<?xml version="1.0" encoding="UTF-8"?>
<beans:beans xmlns="http://www.springframework.org/schema/security"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:beans="http://www.springframework.org/schema/beans"
             xsi:schemaLocation="
			 http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
			 http://www.springframework.org/schema/security http://www.springframework.org/schema/security/spring-security-3.1.xsd">

    <http pattern="/index*" security="none"/>
    <http pattern="/favicon.ico" security="none"/>
    <http pattern="/auth/**" security="none"/>
    <http pattern="/styles/**" security="none"/>
    <http pattern="/js/**" security="none"/>
    <http pattern="/images/**" security="none"/>
    <http pattern="/pers/**" security="none"/>
    <http pattern="/m/**" security="none"/>
	    
    <http auto-config="false" entry-point-ref="loginUrlAuthenticationEntryPoint">

        <custom-filter position="FORM_LOGIN_FILTER" ref="UserAuthenticationFilter"/>
        
        <!--custom-filter position="LOGOUT_FILTER" ref="logoutFilter" /-->

        <!--<session-management session-authentication-strategy-ref="sas" invalid-session-url="/user/home.htm"/>-->

        <intercept-url pattern="/user/*" access="ROLE_REF,ROLE_USER,ROLE_SETT,ROLE_ADMIN,ROLE_TECA,ROLE_TECB,ROLE_TECC,ROLE_TECD,ROLE_OSS,ROLE_ASS"/>
        <intercept-url pattern="/team/*" access="ROLE_REF,ROLE_USER,ROLE_SETT,ROLE_ADMIN,ROLE_TECA,ROLE_TECB,ROLE_TECC,ROLE_TECD,ROLE_OSS,ROLE_ASS"/>
        <intercept-url pattern="/player/*" access="ROLE_REF,ROLE_USER,ROLE_SETT,ROLE_ADMIN,ROLE_TECA,ROLE_TECB,ROLE_TECC,ROLE_TECD,ROLE_OSS,ROLE_ASS"/>

        <logout logout-success-url="/auth/login.htm"/>
        <session-management invalid-session-url="/auth/login.htm">
            <concurrency-control max-sessions="1" error-if-maximum-exceeded="true"/>
        </session-management>
    </http>
	
    <!-- projectname sicsdataanalytics -->
    <authentication-manager alias="authenticationManager">
        <authentication-provider>
            <jdbc-user-service
                data-source-ref="dataSource"
                users-by-username-query="SELECT webusername username, webpassword password, 1 enabled FROM user WHERE user.loginname LIKE '%610' and webusername=?"
                authorities-by-username-query="SELECT webusername username, role.code AS authority FROM user INNER JOIN role ON user.role_id=role.id WHERE groupset_id != 3 and user.loginname LIKE '%610' and user.webusername=?"/>
        </authentication-provider>
    </authentication-manager>
	
	
	
    <!-- projectname AIA 
    <authentication-manager alias="authenticationManager">
            <authentication-provider>
                    <jdbc-user-service
                            data-source-ref="dataSource"
                            users-by-username-query="SELECT webusername username, webpassword password, active as enabled FROM user WHERE groupset_id=3 and webusername=?"
                            authorities-by-username-query="SELECT webusername username, role.code AS authority FROM user INNER JOIN role ON user.role_id=role.id WHERE groupset_id=3 and user.webusername=?"/>
            </authentication-provider>
    </authentication-manager>
    -->
    <!-- Impostazione entrypoint applicazione -->
    <beans:bean id="loginUrlAuthenticationEntryPoint" class="org.springframework.security.web.authentication.LoginUrlAuthenticationEntryPoint">
        <beans:property name="loginFormUrl" value="/auth/login.htm"/>
    </beans:bean>
	
    <beans:bean id="UserAuthenticationFilter" class="sics.filter.UserAuthenticationFilter" >
        <!--<beans:property name="sessionAuthenticationStrategy" ref="sas"/>-->
        <beans:property name="authenticationManager" ref="authenticationManager"/>
        <beans:property name="authenticationFailureHandler" ref="failureHandler"/>
        <beans:property name="authenticationSuccessHandler" ref="successHandler"/>
    </beans:bean>
	
    <!--<beans:bean id="sas" class="org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy"/>-->
    <!--    <beans:bean id="successHandler" class="org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler">
        <beans:property name="defaultTargetUrl" value="/user/home.htm"/>
    </beans:bean>-->
    <beans:bean id="successHandler" class="sics.filter.CustomAuthenticationSuccessHandler">
        <beans:property name="defaultTargetUrl" value="/user/home.htm"/>
    </beans:bean>
    <!--	<beans:bean id="failureHandler" class="org.springframework.security.web.authentication.SimpleUrlAuthenticationFailureHandler">
            <beans:property name="defaultFailureUrl" value="/auth/login.htm?login_error=true"/>
    </beans:bean>-->
    <beans:bean id="failureHandler" class="sics.filter.CustomAuthenticationFailureHandler">
        <beans:property name="defaultFailureUrl" value="/sicsdataanalytics/auth/login.htm"/>
    </beans:bean>
	
    <!--    <beans:bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <beans:property name="basename" value="WEB-INF/classes/pers/sicsdataanalytics/ApplicationResources"/>
        <beans:property name="defaultEncoding" value="UTF-8"/>
    </beans:bean>-->
    
    <!--<beans:bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">-->
    <beans:bean id="messageSource" class="sics.helper.DynamicReloadableResourceBundleMessageSource">
        <beans:property name="basename" value="WEB-INF/classes/pers/sicstv/ApplicationResources"/>
        <beans:property name="defaultEncoding" value="UTF-8"/>
    </beans:bean>

</beans:beans>

