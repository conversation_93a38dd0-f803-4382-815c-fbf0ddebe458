<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:oxm="http://www.springframework.org/schema/oxm"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
	   http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
	   http://www.springframework.org/schema/oxm http://www.springframework.org/schema/oxm/spring-oxm-3.0.xsd
	   http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd">

    <bean class="org.springframework.web.context.support.ServletContextPropertyPlaceholderConfigurer"/>
    <bean id="springApplicationContext" class="sics.helper.SpringApplicationContextHelper"/>
    <bean id="springServletContext" class="sics.helper.SpringServletContextHelper"/>

    <!-- projectname -->
    <bean id="messageSource" class="org.springframework.context.support.ReloadableResourceBundleMessageSource">
        <property name="basename">
            <value>WEB-INF/classes/pers/sicsdataanalytics/ApplicationResources</value>
        </property>
        <property name="defaultEncoding" value="UTF-8"/>
    </bean>

    <!-- database -->
    <bean id="dataSource" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>java:comp/env/jdbc/appDS</value>
        </property>
    </bean>
	
    <!-- Gestione databse mybatis -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="WEB-INF/mapper-config.xml"></property>
    </bean>
	
    <bean id="appDAO" class="sics.dao.AppDaoImpl">
        <property name="sqlSessionFactory" ref="sqlSessionFactory"/>
    </bean>
	
    <!-- DATABASE sics_protezione -->
    <bean id="dataSourceProtezione" class="org.springframework.jndi.JndiObjectFactoryBean">
        <property name="jndiName">
            <value>java:comp/env/jdbc/appDSProtezione</value>
        </property>
    </bean>
	
    <!-- Gestione database mybatis -->
    <bean id="sqlSessionFactoryProtezione" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSourceProtezione"/>
        <property name="configLocation" value="WEB-INF/mapper-config-protezione.xml"></property>
    </bean>
	
    <bean id="appDAOProtezione" class="sics.dao.AppDaoImpl">
        <property name="sqlSessionFactory" ref="sqlSessionFactoryProtezione"/>
    </bean>
	
    <bean id="appRoot" class="java.lang.String">
        <constructor-arg type="java.lang.String" value="sicsdataanalytics"/>
    </bean>
	
    <bean id="localeResolver" class="org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver"/>
</beans>
