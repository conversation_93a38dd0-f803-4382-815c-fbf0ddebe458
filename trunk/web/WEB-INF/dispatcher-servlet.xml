<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:device="http://www.springframework.org/schema/mobile/device"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
	   http://www.springframework.org/schema/mobile/device http://www.springframework.org/schema/mobile/device/spring-mobile-device-1.0.xsd
	   http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd">

    <mvc:annotation-driven/>
	
    <!-- Scannerizza tutti i controller ricercando il metodo che gestisce la request -->
    <context:component-scan base-package="sics.controller"/>
	
    <mvc:interceptors>
        <!-- Changes the locale when a 'locale' request parameter is sent; e.g. /?locale=de -->
        <bean class="org.springframework.web.servlet.i18n.LocaleChangeInterceptor"/>
        <bean class="sics.helper.SpringPreRequestHandler"/>
    </mvc:interceptors>

    <!-- Interceptor utilizzato per mappare tutte le rotte di ogni sessione -->
    <bean id="requestInterceptor" class="sics.helper.SpringPreRequestHandler"/>

    <!-- Gestione eccezione in caso di controller non trovato -->
    <bean id="exceptionResolver" class="sics.helper.ExceptionHandlerHelper">
        <property name="defaultErrorView" value="error/erroreGenerico.jsp"/>
        <property name="exceptionMappings">
            <props>
                <prop key="org.springframework.web.multipart.MaxUploadSizeExceededException">error/erroreUpload.jsp</prop>
            </props>
        </property>
    </bean>

    <!-- View-resolver Trasforma in jsp i ritorni dal controller -->
    <bean class="org.springframework.web.servlet.view.InternalResourceViewResolver" id="viewResolver" p:prefix="/WEB-INF/pages/"/>
    <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
        <property name="maxUploadSize" value="102400000"/>
    </bean>
	
    <!-- Usati con autowired -->
    <bean name="baseService" class="sics.service.BaseService" >
        <property name="appDAO" ref="appDAO"/>
        <property name="appDAOProtezione" ref="appDAOProtezione"/>
    </bean>
    <bean name="userService" class="sics.service.UserService"/>

    <bean id="localeResolver" class="org.springframework.web.servlet.i18n.CookieLocaleResolver">
        <property name="defaultLocale" value="en"/>
    </bean>

</beans>