<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    var matchdaySlider, bornyearSlider, eventAmountSlider, table;
    $(document).ready(function () {
        $("#playerRankingButton").addClass("active");

        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();
            // non faccio niente perch� c'� evento bindato sul campo input
        });

        showChartMessage(1);

        // Define element
        const slider_drag_behaviour = document.getElementById('noui-slider-drag');
        // Create slider
        matchdaySlider = noUiSlider.create(slider_drag_behaviour, {
            start: [1, 100],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 100
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const slider_drag_behaviour_vals = [
            document.getElementById('noui-slider-drag-lower-val'),
            document.getElementById('noui-slider-drag-upper-val')
        ];
        // Show the values
        slider_drag_behaviour.noUiSlider.on('update', function (values, handle) {
            slider_drag_behaviour_vals[handle].innerHTML = values[handle];
        });

        matchdaySlider.on("change", function () {
            let index = filtersIndex.indexOf("matchday");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-matchday", matchdaySlider.get());
            }
            updateChart("matchday");
        });

        // Define element
        const sliderBorn = document.getElementById('noui-slider-drag-born');
        // Create slider
        bornyearSlider = noUiSlider.create(sliderBorn, {
            start: [1900, 2000],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1900,
                'max': 2000
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderBornVals = [
            document.getElementById('noui-slider-drag-born-lower-val'),
            document.getElementById('noui-slider-drag-born-upper-val')
        ];
        // Show the values
        sliderBorn.noUiSlider.on('update', function (values, handle) {
            sliderBornVals[handle].innerHTML = values[handle];
        });

        bornyearSlider.on("change", function () {
            let index = filtersIndex.indexOf("bornyear");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-bornyear", bornyearSlider.get());
            }
            updateChart("bornyear");
        });

        // Define element
        const sliderEventAmount = document.getElementById('noui-slider-drag-events');
        // Create slider
        eventAmountSlider = noUiSlider.create(sliderEventAmount, {
            start: [1, 999],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 999
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderEventAmountVals = [
            document.getElementById('noui-slider-drag-events-lower-val'),
            document.getElementById('noui-slider-drag-events-upper-val')
        ];
        // Show the values
        sliderEventAmount.noUiSlider.on('update', function (values, handle) {
            sliderEventAmountVals[handle].innerHTML = values[handle];
        });

        eventAmountSlider.on("change", function () {
            let index = filtersIndex.indexOf("eventamount");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-eventamount", eventAmountSlider.get());
            }
            updateChart("eventamount");
        });

        checkInitialFilters();
    });

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();

        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");

//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let seasonId = $("#filter-seasonid").val();
            let competitionId = $("#filter-competitionid").val();
            let eventTypeId = getPageEventType();
            if (notEmpty(seasonId) && notEmpty(competitionId) && notEmpty(eventTypeId)) {
                let totalType = $("#filter-totaltype").val();
                if (notEmpty(totalType)) {
                    params.push("totalType;" + totalType);
                }
                params.push("seasonId;" + seasonId);
                params.push("competitionId;" + competitionId.join("|"));
                let groupIds = $("#filter-groupid").val();
                if (notEmpty(groupIds)) {
                    params.push("groupId;" + groupIds.join("|"));
                }
                params.push("eventTypeId;" + eventTypeId);
                if (filterChanged !== "eventTypeId") {
                    let tagTypeId = getPageTagType();
                    if (notEmpty(tagTypeId)) {
                        tagTypeId.sort((a, b) => Number(a) - Number(b));
                        params.push("tagTypeId;" + tagTypeId.join("|"));
                    }
                }
                let isHomeTeam = $("#filter-ishometeam").val();
                if (notEmpty(isHomeTeam)) {
                    params.push("isHomeTeam;" + isHomeTeam);
                }
                let matchday = matchdaySlider.get();
                params.push("matchdayFrom;" + parseInt(matchday[0]));
                params.push("matchdayTo;" + parseInt(matchday[1]));
                let countryId = $("#filter-countryid").val();
                if (notEmpty(countryId)) {
                    params.push("countryId;" + countryId);
                }
                let positionId = $("#filter-positionid").val();
                if (notEmpty(positionId)) {
                    params.push("positionId;" + positionId);
                }
                let positionDetailId = $("#filter-positiondetailid").val();
                if (notEmpty(positionDetailId)) {
                    params.push("positionDetailId;" + positionDetailId);
                }
                let footId = $("#filter-footid").val();
                if (notEmpty(footId)) {
                    params.push("footId;" + footId);
                }
                if (typeof bornyearSlider !== "undefined") {
                    let bornyear = bornyearSlider.get();
                    if (parseInt(bornyear[0]) !== bornyearSlider.options.range.min || parseInt(bornyear[1]) !== bornyearSlider.options.range.max) {
                        params.push("bornyearFrom;" + parseInt(bornyear[0]));
                        params.push("bornyearTo;" + parseInt(bornyear[1]));
                    }
                }
                if (typeof eventAmountSlider !== "undefined") {
                    let eventamount = eventAmountSlider.get();
                    if (parseInt(eventamount[0]) !== eventAmountSlider.options.range.min || parseInt(eventamount[1]) !== eventAmountSlider.options.range.max) {
                        params.push("minEventAmount;" + parseInt(eventamount[0]));
                        params.push("maxEventAmount;" + parseInt(eventamount[1]));
                    }
                }
                let homeModule = $("#filter-homemodule").val();
                if (notEmpty(homeModule)) {
                    params.push("homeModule;" + homeModule.join("|"));
                }
                let awayModule = $("#filter-awaymodule").val();
                if (notEmpty(awayModule)) {
                    params.push("awayModule;" + awayModule.join("|"));
                }
                // filtro per area oppure filtro per il posizionale
                let area = $("#filter-area").val();
                if (notEmpty(area)) {
                    params.push("area;" + area);
                    // se ho almeno un filtro posizionale dico all'utente che non verranno considerati
                    let half = $("#filter-half").val();
                    let zone = $("#filter-zone").val();
                    let channel = $("#filter-channel").val();
                    if (notEmpty(half) || notEmpty(zone) || notEmpty(channel)) {
                        new Noty({
                            text: "<spring:message code="messages.positional.filter.warn"/>",
                            type: "warning",
                            layout: "topCenter"
                        }).show();
                    }
                } else {
                    let half = $("#filter-half").val();
                    if (notEmpty(half)) {
                        params.push("half;" + half);
                    }
                    let zone = $("#filter-zone").val();
                    if (notEmpty(zone)) {
                        params.push("zone;" + zone);
                    }
                    let channel = $("#filter-channel").val();
                    if (notEmpty(channel)) {
                        params.push("channel;" + channel);
                    }
                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);
                showBlockUI();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/player/ranking/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        $.unblockUI();
                        $("#table-container").empty();
                        $("#table-container").append(result);

                        let isValid = $("#ranking-content-valid").attr("isvalid");
                        if (isValid === "false") {
                            $("#chartdiv").removeClass("d-none");
                            $("#table-container").addClass("d-none");
                            $("#page-title").addClass("d-none");

                            showChartMessage(3);
                        } else {
                            if ($("#ranking-content-empty").attr("isempty") === "false") {
                                $("#chartdiv").addClass("d-none");
                                $("#table-container").removeClass("d-none");
                                $("#page-title").removeClass("d-none");
                                // spostato nel ranking-content
                                // $("#page-title").html(getPageTitle());
                            } else {
                                $("#chartdiv").removeClass("d-none");
                                $("#table-container").addClass("d-none");
                                $("#page-title").addClass("d-none");

                                showChartMessage(2);
                            }
                        }

                        // initialize popover
                        const customPopoverElement = $('[data-bs-popup=popover]');
                        if (customPopoverElement) {
                            customPopoverElement.each(function (index, element) {
                                new bootstrap.Popover(element);
                            });
                        }
                        stopChartLoading = false;
                    },
                    error: function () {
                        $.unblockUI();
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <div class="sidebar sidebar-secondary sidebar-expand-lg">
            <!-- Expand button -->
            <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                <i class="ph-caret-right"></i>
            </button>
            <!-- /expand button -->

            <!-- Sidebar content -->
            <div class="sidebar-content" id="filters-containter">

                <!-- Header -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                    <div class="me-1">
                        <i class="ph-arrow-counter-clockwise cursor-pointer" title="<spring:message code="filters.reset"/>" onclick="resetFilters();"></i>
                        <i class="ph-funnel cursor-pointer" title="<spring:message code="filters.show.hide"/>" onclick="managePersonalFilters();"></i>
                    </div>
                    <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex">
                            <i class="ph-arrows-left-right"></i>
                        </button>

                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                            <i class="ph-x"></i>
                        </button>
                    </div>
                </div>
                <!-- /header -->

                <!-- Header Filter Message -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center justify-content-center mb-0 mt-0 py-1 d-none" id="specific-filter-message">
                    <div>
                        <i class="ph-warning text-warning"></i><span class="ms-1 text-warning"><spring:message code="filters.specific.warning"/></span>
                    </div>
                </div>
                <!-- /header filter message -->

                <!-- Sidebar search -->
                <div class="sidebar-section">
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-4 d-none" id="personal-filters-container">
                            <label class="form-label mb-0"><spring:message code="personal.filters"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="personal.filters.placeholder"/>" class="form-control form-control-sm select" id="personal-filter" onchange="loadPersonalFilter();" data-minimum-results-for-search="Infinity" data-width="1%">
                                        <option value=""></option>
                                        <c:forEach var="filter" items="${mFilters}">
                                            <option value="${filter.id}">${filter.name}</option>
                                        </c:forEach>
                                    </select>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" id="personal-filter-update" onclick="updatePersonalFilter();" title="<spring:message code="personal.filter.save.placeholder"/>" disabled>
                                        <i class="ph-pencil"></i>
                                    </button>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" onclick="addPersonalFilter();" title="<spring:message code="personal.filter.add.placeholder"/>">
                                        <i class="ph-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.season"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.season.placeholder"/>" class="form-control form-control-sm select is-filter is-required update-groupid" id="filter-seasonid" onchange="updateFilters('seasonId');" data-minimum-results-for-search="Infinity">
                                    <c:forEach var="season" items="${mSeasons}">
                                        <option value="${season.id}">${season.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required update-groupid" id="filter-competitionid" onchange="updateFilters('competitionId');">
                                </select>
                            </div>
                        </div>
                        <div class="mb-2 d-none" id="filter-groupid-container">
                            <label class="form-label mb-0"><spring:message code="filters.group"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.group.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-initial-load" id="filter-groupid" onchange="updateFilters('groupId');">
                                </select>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.displayed"/></label>
                            <div class="form-control-feedback form-control-feedback-end">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.displayed.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-totaltype" onchange="updateChart('totalType');" data-minimum-results-for-search="Infinity">
                                    <option value="totals" selected><spring:message code="filters.displayed.totals"/></option>
                                    <option value="p90"><spring:message code="filters.displayed.p90"/></option>
                                    <option value="touches"><spring:message code="filters.displayed.touches"/></option>
                                    <option value="average"><spring:message code="filters.displayed.average"/></option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 px-0 py-0" action="#" id="event-filters-container">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="event-container-nav">
                            <li class="nav-item">
                                <a href="#event-filters-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-advanced" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-tactical" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-3">
                            <div class="tab-pane fade show active" id="event-filters-event">
                                <div class="mb-2" id="container-eventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra eventtypeid" id="filter-eventtypeid" onchange="updateFilters('eventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tagtypeid" onchange="updateFilters('tagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-advanced">
                                <div class="mb-2" id="container-advancedeventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-advancedeventtypeid" onchange="updateFilters('advancedeventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-tactical">
                                <div class="mb-2" id="container-tacticaleventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-tacticaleventtypeid" onchange="updateFilters('tacticaleventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tacticaltagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tacticaltagtypeid" onchange="updateFilters('tacticaltagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="animation px-3" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.player.details"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.country"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.country.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-countryid" onchange="updateFilters('countryId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.role"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.role.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-positionid" onchange="updateFilters('positionId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.role.detailed"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.role.detailed.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-positiondetailid" onchange="updateFilters('positionDetailId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.foot"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.foot.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-footid" onchange="updateFilters('footId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.born.year"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-born"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-born-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-born-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.match.details"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.away"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.away.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-ishometeam" onchange="updateFilters('isHomeTeam');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.matchday"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-homemodule" onchange="updateFilters('homeModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.away.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.away.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-awaymodule" onchange="updateFilters('awayModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.positional"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.half"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.half.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-half" onchange="updateChart('half');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.zone"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.zone.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-zone" onchange="updateChart('zone');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.channel"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.channel.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-channel" onchange="updateChart('channel');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.area"/> <i class="ph-info" data-bs-popup="tooltip" data-bs-placement="top" data-bs-html="true" title="<spring:message code="filters.area.tooltip"/>"></i></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.area.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-area" onchange="updateChart('area');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->
            </div>
            <!-- /sidebar content -->
        </div>

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content" id="container">
                    <div class="row">
                        <h6 class="mb-1 text-center" id="page-title"></h6>
                    </div>
                    <div class="row">
                        <div id="chartdiv">
                            <%@ include file="../global/messages.jsp" %>
                        </div>
                        <div id="table-container">

                        </div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
