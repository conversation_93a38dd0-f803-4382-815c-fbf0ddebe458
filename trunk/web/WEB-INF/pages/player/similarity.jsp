<%@ include file="../global/main.jsp" %>

<script src="https://cdn.datatables.net/2.2.2/js/dataTables.js"></script>
<script src="https://cdn.datatables.net/fixedcolumns/5.0.4/js/dataTables.fixedColumns.js"></script>
<script src="https://cdn.datatables.net/fixedcolumns/5.0.4/js/fixedColumns.dataTables.js"></script>

<script type="text/javascript">
    let positionId, positionDetailId, playerId;

    var sliders = [], importances = [], bornyearSlider;
    var valuesForSlider = ['<spring:message code="metric.importance.low"/>', '<spring:message code="metric.importance.medium"/>', '<spring:message code="metric.importance.high"/>'];
    var format = {
        to: function (value) {
            return valuesForSlider[value];
        },
        from: function (value) {
            return valuesForSlider.indexOf(value);
        }
    };

    $(document).ready(function () {
        $("#playerSimilarityButton").addClass("active");
        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();    // non faccio niente perchè c'è evento bindato sul campo input
        });

        $("#navbar-search-player-input").click();

    <c:choose>
        <c:when test="${mPlayerId == null}">
        // bind search input
        $("#navbar-search-player-input").on("input", function () {
            $("#navbar-search-player-text").text("\"" + $(this).val() + "\"");
            if (!$("#navbar-search-player-dropdown").hasClass("show")) {
                $("#navbar-search-player-input").click();
            }
        });
        $("#navbar-search-player-input").on('keyup', function (e) {
            if (e.key === 'Enter' || e.keyCode === 13) {
                searchPlayer();
            }
        });
        </c:when>
        <c:otherwise>
        showChartMessage(1);
        playerId = ${mPlayerId};
        positionId = "${mTeamPlayer.positionId}";
        positionDetailId = "${mTeamPlayer.positionDetailId}";

        // Define element
        const sliderBorn = document.getElementById('noui-slider-drag-born');
        // Create slider
        bornyearSlider = noUiSlider.create(sliderBorn, {
            start: [${mMinAge}, ${mMaxAge}],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': ${mMinAge},
                'max': ${mMaxAge}
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderBornVals = [
            document.getElementById('noui-slider-drag-born-lower-val'),
            document.getElementById('noui-slider-drag-born-upper-val')
        ];
        // Show the values
        sliderBorn.noUiSlider.on('update', function (values, handle) {
            sliderBornVals[handle].innerHTML = values[handle];
        });

        bornyearSlider.on("change", function () {
            let index = filtersIndex.indexOf("bornyear");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-bornyear", bornyearSlider.get());
            }
            updateChart("bornyear");
        });

        let defaultMetrics = [];
        if (positionDetailId === "3") {
            // difensore centrale
            defaultMetrics = [12, 8, 41, 901, 43];
        } else if (positionDetailId === "4" || positionDetailId === "5" || positionDetailId === "15" || positionDetailId === "16") {
            // terzino o quinto
            defaultMetrics = [900, 8, 14, 36, 287, 43, 20];
        } else if (positionDetailId === "6") {
            // mediano
            defaultMetrics = [12, 8, 287, 41, 43, 900];
        } else if (positionDetailId === "10") {
            // trequartista
            defaultMetrics = [8, 20, 2, 275, 1003, 1005, 43];
        } else if (positionDetailId === "11" || positionDetailId === "12") {
            // ala
            defaultMetrics = [8, 14, 36, 43, 2, 1005, 900];
        } else if (positionDetailId === "13" || positionDetailId === "14") {
            // punta
            defaultMetrics = [12, 8, 14, 275, 2, 63, 1003, 6];
        } else if (positionId === "1") {
            // portiere
            defaultMetrics = [59, 289, 60, 12, 8, 276];
        } else if (positionId === "2") {
            // difensore
            defaultMetrics = [12, 8, 41, 14, 36, 287, 43, 900, 20];
        } else if (positionId === "3") {
            // centrocampista
            defaultMetrics = [12, 8, 287, 2, 43, 275, 1003, 900, 1005];
        } else if (positionId === "4") {
            // attaccante
            defaultMetrics = [8, 12, 14, 36, 43, 63, 2, 275, 8, 1003, 1005, 900, 6];
        }

//        let index = filtersIndex.indexOf("statstypeid");
//        if (index >= 0) {
//            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
//            if (index > 25) {
//                index -= 25;
//                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
//            }
//            let value = localStorage.getItem("dataanalytics/610/" + letter + "/filter-statstypeid");
//            if (notEmpty(value)) {
//                defaultMetrics = value.split(",");
//            }
//        }
//
//        index = filtersIndex.indexOf("importance");
//        if (index >= 0) {
//            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
//            if (index > 25) {
//                index -= 25;
//                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
//            }
//            let value = localStorage.getItem("dataanalytics/610/" + letter + "/filter-importance");
//            if (notEmpty(value)) {
//                importances = value.split(",");
//            }
//        }

        stopChartLoading = true;
        defaultMetrics.forEach(function (element) {
            $("#filter-statstypeid").multiselect('select', element);
        });
        redrawSliders(true);
        $("#filter-statstypeid").trigger("change");

            <c:if test="${!mTeamCompetitions.isEmpty()}">
                <c:forEach var="competitionId" items="${mTeamCompetitions}">
        $("#filter-competitionid").multiselect('select', "${competitionId}");
                </c:forEach>
            </c:if>

        stopChartLoading = false;

        $("#filter-statstypeid").on("change", function () {
            redrawSliders(true);
        });

        updateChart();
        </c:otherwise>

    </c:choose>
    });

    function redrawSliders(loadCachedValues) {
        sliders.forEach(function (slider) {
            slider.destroy();
            $("#slider-container-" + slider.typeId).remove();
        });
        sliders = [];

        let index = 0;
        $("#filter-statstypeid").val().forEach(function (element) {
            addSlider(element, loadCachedValues, index);
            index++;
        });

        importances = [];
        sliders.forEach(function (slider) {
            importances.push(slider.get());
        });
    }

    function addSlider(statsTypeId, loadCachedValues, index) {
        let sliderHtml = $("#base-slider").clone();
        sliderHtml.removeClass("d-none");
        sliderHtml.attr('id', function (index, id) {
            return "slider-container-" + statsTypeId;
        });
        sliderHtml.find('.noui-height-helper').attr('id', function (index, id) {
            return "slider-" + statsTypeId;
        });
        sliderHtml.find('.form-label').text($("#filter-statstypeid > option[value='" + statsTypeId + "']").text());
        $("#slider-container").append(sliderHtml);

        // Create slider
        var slider = noUiSlider.create(document.getElementById('slider-' + statsTypeId), {
            start: ["<spring:message code="metric.importance.medium"/>"],
            step: 1,
            behaviour: 'drag',
            connect: [true, false],
            format: format,
            pips: {mode: 'steps', format: format, density: 50},
            range: {
                'min': 0,
                'max': 2
            },
            direction: 'ltr'
        });
        slider.typeId = statsTypeId;

        if (typeof loadCachedValues !== "undefined" && loadCachedValues && typeof index !== "undefined") {
            if (typeof importances[index] !== "undefined") {
                slider.set([importances[index]]);
            }
        }

        slider.on('change', function () {
            // salvo tutti i valori in cache
            importances = [];
            sliders.forEach(function (slider) {
                importances.push(slider.get());
            });
            let index = filtersIndex.indexOf("importance");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-importance", importances);
            }

            updateChart();
        });

        sliders.push(slider);

        // ora che c'è un nuovo slider aggiorno i valori
        importances[index] = slider.get();
    }

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();
        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");
//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let statsTypeIds = $("#filter-statstypeid").val();
            if (playerId && notEmpty(statsTypeIds)) {
                params.push("playerId;" + playerId);
                params.push("statsTypeIds;" + statsTypeIds.join("|"));
                let positionIds = $("#filter-positionid").val();
                params.push("positionIds;" + positionIds.join("|"));
                params.push("importances;" + importances.join("|"));
                let playtime = $("#filter-playtime").val();
                params.push("playtime;" + playtime);

                if (typeof bornyearSlider !== "undefined") {
                    let bornyear = bornyearSlider.get();
                    if (parseInt(bornyear[0]) !== bornyearSlider.options.range.min || parseInt(bornyear[1]) !== bornyearSlider.options.range.max) {
                        params.push("ageFrom;" + parseInt(bornyear[0]));
                        params.push("ageTo;" + parseInt(bornyear[1]));
                    }
                }
                let footId = $("#filter-footid").val();
                if (notEmpty(footId)) {
                    params.push("footId;" + footId);
                }
                let countryIds = $("#filter-countryid").val();
                if (notEmpty(countryIds)) {
                    params.push("countryIds;" + countryIds.join("|"));
                }
                let competitionIds = $("#filter-competitionid").val();
                if (notEmpty(competitionIds)) {
                    params.push("competitionIds;" + competitionIds.join("|"));
                }
                let modality = $("#filter-modality").val();
                if (notEmpty(modality)) {
                    params.push("modality;" + modality);
                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);
                showBlockUI();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/player/similarity/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        $.unblockUI();
                        $("#table-container").empty();
                        $("#table-container").append(result);

                        let isValid = $("#similarity-content-valid").attr("isvalid");
                        if (isValid === "false") {
                            $("#chartdiv").removeClass("d-none");
                            $("#table-container").addClass("d-none");
                            $("#page-title").addClass("d-none");

                            showChartMessage(3);
                        } else {
                            if ($("#similarity-content-empty").attr("isempty") === "false") {
                                $("#chartdiv").addClass("d-none");
                                $("#table-container").removeClass("d-none");
                            } else {
                                $("#chartdiv").removeClass("d-none");
                                $("#table-container").addClass("d-none");

                                showChartMessage(2);
                            }
                        }

                        stopChartLoading = false;
                    },
                    error: function () {
                        $.unblockUI();
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }

    function searchPlayer() {
        $("#navbar-search-player-result").empty();
        let input = $("#navbar-search-player-input").val();
        if (input) {
            event.preventDefault();
            event.stopPropagation();

            $.ajax({
                type: "GET",
                url: "/sicsdataanalytics/user/search.htm",
                cache: false,
                data: encodeURI("input=" + input + "&loadTeams=false&loadPlayersLastTeam=true"),
                success: function (result) {
                    $("#navbar-search-player-result").html(result);
                    highlightSearchPlayerInput();
                },
                error: function () {
                    sweetAlert.fire({
                        title: "ERROR, Oops...",
                        text: "<spring:message code="messages.generic.error"/>",
                        icon: "error",
                        padding: 40
                    });
                }
            });
        }
    }

    function closeSidebars() {
        $("#sidebar-end").addClass("sidebar-collapsed");
    }

    function openSidebars() {
        $("#sidebar-end").removeClass("sidebar-collapsed");
    }

    <c:if test="${mPlayerId == null}">
    // (!) OVERRIDE da pagina main.jsp (!)
    function searchProfile(playerId, isTeam) {
        location.href = location.href + "?playerId=" + playerId;
    }
    </c:if>
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <c:choose>
        <c:when test="${mPlayerId == null}">
            <!-- Page content -->
            <div class="h-75 w-100 d-flex flex-column justify-content-center align-items-center">
                <h3 class="mb-0"><spring:message code="players.similarity.search.player"/></h3>
                <p class="mb-2 text-muted"><spring:message code="players.similarity.search.player.description"/></p>
                <div class="form-control-feedback form-control-feedback-start w-50" data-color-theme="dark">
                    <input type="text" id="navbar-search-player-input" class="form-control rounded-pill" placeholder="<spring:message code="messages.search.player"/>" data-bs-toggle="dropdown" autocomplete="off">
                    <div class="form-control-feedback-icon">
                        <i class="ph-magnifying-glass"></i>
                    </div>
                    <div class="dropdown-menu w-100" data-color-theme="light" id="navbar-search-player-dropdown">
                        <button type="button" class="dropdown-item" onclick="searchPlayer();">
                            <div class="text-center w-32px me-3">
                                <i class="ph-magnifying-glass"></i>
                            </div>
                            <span><spring:message code="messages.search.player"/> <span class="fw-bold" id="navbar-search-player-text">""</span></span>
                        </button>

                        <div id="navbar-search-player-result">

                        </div>
                    </div>
                </div>
            </div>
            <!-- /page content -->
        </c:when>
        <c:otherwise>
            <!-- Page content -->
            <div class="page-content overflow-auto">

                <div class="sidebar sidebar-secondary sidebar-expand-lg">
                    <!-- Expand button -->
                    <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100" onclick="openSidebars();">
                        <i class="ph-caret-right"></i>
                    </button>
                    <!-- /expand button -->

                    <!-- Sidebar content -->
                    <div class="sidebar-content" id="filters-containter">

                        <!-- Header -->
                        <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                            <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                            <div class="ms-auto">
                                <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex" onclick="closeSidebars();">
                                    <i class="ph-arrows-left-right"></i>
                                </button>

                                <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                                    <i class="ph-x"></i>
                                </button>
                            </div>
                        </div>
                        <!-- /header -->

                        <!-- Sidebar search -->
                        <div class="sidebar-section">
                            <form class="sidebar-section-body mb-0 py-1" action="#">
                                <div>
                                    <label class="form-label mb-0"><spring:message code="filters.metrics"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.metrics.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required" id="filter-statstypeid" onchange="updateChart('statsTypeId');">
                                            <c:forEach var="metric" items="${mMetrics}">
                                                <option value="${metric.id}">${metric.getDesc(mUser.tvLanguage)}</option>
                                            </c:forEach>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /sidebar search -->

                        <!-- Sidebar advanced search -->
                        <div class="sidebar-section">
                            <div class="sidebar-section-header border-bottom">
                                <span class="fw-semibold"><spring:message code="metric.importance"/></span>
                            </div>

                            <form class="sidebar-section-body mb-0 py-1" action="#" id="slider-container">
                                <div class="animation mb-5 d-none" data-animation="zoomIn" id="base-slider">
                                    <label class="form-label mb-0 fw-bold"></label>
                                    <div class="px-2">
                                        <div class="noui-height-helper mt-1" id="noui-slider-drag"></div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /sidebar advanced search -->
                    </div>
                    <!-- /sidebar content -->
                </div>

                <!-- Content wrapper -->
                <div class="content-wrapper">

                    <!-- Inner content -->
                    <div class="content-inner">

                        <!-- Content area -->
                        <div class="content" id="container">
                            <div class="row">
                                <div id="chartdiv">
                                    <%@ include file="../global/messages.jsp" %>
                                </div>
                                <div id="table-container">

                                </div>
                            </div>
                        </div>
                        <!-- /footer -->

                    </div>
                    <!-- /inner content -->

                    <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

                </div>
                <!-- /content wrapper -->

                <div class="sidebar sidebar-secondary sidebar-end sidebar-expand-lg" id="sidebar-end">
                    <!-- Expand button -->
                    <!--                    <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                                            <i class="ph-caret-right"></i>
                                        </button>-->
                    <!-- /expand button -->

                    <!-- Sidebar content -->
                    <div class="sidebar-content">
                        <!-- Header -->
                        <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                            <h5 class="mb-0"><spring:message code="filters.view"/></h5>
                        </div>
                        <!-- /header -->

                        <!-- Sidebar search -->
                        <div class="sidebar-section">
                            <form class="sidebar-section-body mb-0 py-1" action="#">
                                <div>
                                    <label class="form-label mb-0"><spring:message code="filters.view.modality"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-modality" onchange="updateChart('modality');" data-minimum-results-for-search="Infinity">
                                            <option value="1" selected><spring:message code="filters.view.modality.graph"/></option>
                                            <option value="2"><spring:message code="filters.view.modality.table"/></option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <!-- /sidebar search -->

                        <!-- Header -->
                        <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                            <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                        </div>
                        <!-- /header -->

                        <!-- Sidebar search -->
                        <div class="sidebar-section">
                            <form class="sidebar-section-body mb-0 py-1" action="#">
                                <div class="mb-2">
                                    <label class="form-label mb-0"><spring:message code="filters.role"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="input-group">
                                            <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                                <i class="ph-arrow-counter-clockwise"></i>
                                            </button>
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.role.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-positionid" onchange="updateChart('positionId');" data-width="1%" data-minimum-results-for-search="Infinity">
                                                <c:forEach var="positionId" items="${mPositionDetailMap.keySet()}">
                                                    <c:choose>
                                                        <c:when test="${mPositionDetailMap.get(positionId).size() > 1}">
                                                            <optgroup label="${mPositions.get(positionId).getDesc(mUser.tvLanguage)}">
                                                                <option value="${positionId}" ${positionId == mTeamPlayer.positionId ? 'selected' : ''}>${mPositions.get(positionId).getDesc(mUser.tvLanguage)}</option>
                                                                <c:forEach var="positionDetail" items="${mPositionDetailMap.get(positionId)}">
                                                                    <option value="${positionId}-${positionDetail.id}">${positionDetail.getDesc(mUser.tvLanguage)}</option>
                                                                </c:forEach>
                                                            </optgroup>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <option value="${positionId}" ${positionId == mTeamPlayer.positionId ? 'selected' : ''}>${mPositions.get(positionId).getDesc(mUser.tvLanguage)}</option>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label mb-0"><spring:message code="filters.playtime"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="input-group">
                                            <select data-container-css-class="select-sm" class="form-control form-control-sm select is-filter" id="filter-playtime" onchange="updateChart('playtime');" data-width="1%" data-minimum-results-for-search="Infinity">
                                                <option value="500" selected><spring:message code="filters.playtime.500"/></option>
                                                <option value="1000"><spring:message code="filters.playtime.1000"/></option>
                                                <option value="1500"><spring:message code="filters.playtime.1500"/></option>
                                                <option value="2000"><spring:message code="filters.playtime.2000"/></option>
                                                <option value="2500"><spring:message code="filters.playtime.2500"/></option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2 animation" data-animation="zoomIn">
                                    <label class="form-label mb-0"><spring:message code="filters.age"/></label>
                                    <div class="px-2">
                                        <div class="noui-height-helper mt-1" id="noui-slider-drag-born"></div>

                                        <div class="clearfix">
                                            <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-born-lower-val"></span></span>
                                            <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-born-upper-val"></span></span>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label mb-0"><spring:message code="filters.foot"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="input-group">
                                            <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                                <i class="ph-arrow-counter-clockwise"></i>
                                            </button>
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.foot.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-footid" onchange="updateChart('footId');" data-width="1%" data-minimum-results-for-search="Infinity">
                                                <option value=""></option>
                                                <c:forEach var="foot" items="${mFoots.values()}">
                                                    <option value="${foot.id}">${foot.getDesc(mUser.tvLanguage)}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-2">
                                    <label class="form-label mb-0"><spring:message code="filters.country"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="input-group">
                                            <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                                <i class="ph-arrow-counter-clockwise"></i>
                                            </button>
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.country.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-countryid" onchange="updateChart('countryId');" data-width="1%">
                                                <c:forEach var="competition" items="${mInternationalCompetitions}">
                                                    <option value="INT-${competition.id}">${competition.getName(mUser.tvLanguage)}</option>
                                                </c:forEach>
                                                <option data-role="divider"></option>
                                                <c:forEach var="country" items="${mCountries}">
                                                    <option value="${country.id}">${country.getName(mUser.tvLanguage)}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="input-group">
                                            <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                                <i class="ph-arrow-counter-clockwise"></i>
                                            </button>
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required" id="filter-competitionid" onchange="updateChart('competitionId');" data-width="1%" data-include-select-all-option="true">
                                                <c:forEach var="competition" items="${mCompetitions}">
                                                    <option value="${competition.id}">${competition.getCompleteName(mUser.tvLanguage)}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div id="export-button-container" class="d-none">
                                    <a type="button" onclick="exportTableData();" class="btn btn-teal w-100 mt-2">
                                        <i class="ph-file-csv me-2"></i>
                                        <spring:message code="messages.export.csv.data"/>
                                    </a>
                                </div>
                            </form>
                        </div>
                        <!-- /sidebar search -->
                    </div>
                    <!-- /sidebar content -->
                </div>

            </div>
            <!-- /page content -->
        </c:otherwise>
    </c:choose>
</body>
