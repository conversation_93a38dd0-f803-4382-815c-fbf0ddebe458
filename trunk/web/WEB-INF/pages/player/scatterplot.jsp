<%@ include file="../global/main.jsp" %>

<!-- D3.js Library for canvas/svg -->
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>
<script src="/sicsdataanalytics/js/sics-canvas-player.js" type="text/javascript"></script>

<script type="text/javascript">
    var stopValueSave = true;
    var eventAmountSlider, eventAmountSliderY;
    var extraLayerContainer;
    $(document).ready(function () {
        $("#playerScatterplotButton").addClass("active");
        eventFilter.maxFilters = 1;
        eventFilter.scatterplot = true;

        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();
            // non faccio niente perch� c'� evento bindato sul campo input
        });

        showChartMessage(1);

        // Define element
        const sliderEventAmount = document.getElementById('noui-slider-drag-events');
        // Create slider
        eventAmountSlider = noUiSlider.create(sliderEventAmount, {
            start: [1, 999],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 999
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderEventAmountVals = [
            document.getElementById('noui-slider-drag-events-lower-val'),
            document.getElementById('noui-slider-drag-events-upper-val')
        ];
        // Show the values
        sliderEventAmount.noUiSlider.on('update', function (values, handle) {
            sliderEventAmountVals[handle].innerHTML = values[handle];
        });

        eventAmountSlider.on("change", function () {
            let index = filtersIndex.indexOf("eventamount");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-eventamount", eventAmountSlider.get());
            }
            updateChart("eventamount");
        });

        // Define element
        const sliderEventAmountY = document.getElementById('noui-slider-drag-events-y');
        // Create slider
        eventAmountSliderY = noUiSlider.create(sliderEventAmountY, {
            start: [1, 999],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 999
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderEventAmountYVals = [
            document.getElementById('noui-slider-drag-events-y-lower-val'),
            document.getElementById('noui-slider-drag-events-y-upper-val')
        ];
        // Show the values
        sliderEventAmountY.noUiSlider.on('update', function (values, handle) {
            sliderEventAmountYVals[handle].innerHTML = values[handle];
        });

        eventAmountSliderY.on("change", function () {
            let index = filtersIndex.indexOf("eventamount-y");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-eventamount-y", eventAmountSliderY.get());
            }
            updateChart("eventamount-y");
        });

        queue.enqueue(() => {
            updateFiltersAllSeasons('seasonId');
        });
        checkInitialFilters();

        queue.enqueue(() => {
            // se non c'� nessun team selezionato seleziono il primo
            if ($("#filter-teamid").find("option:checked").length === 0) {
                stopChartLoading = true;
                $("#filter-teamid").prop("selectedIndex", 1).change();
                stopChartLoading = false;
            }
        });

        // non serve pi� dato che playerid ha la classe "load-all-on-change"
//        queue.enqueue(() => {
//            // se non c'� nessun giocatore selezionato li seleziono tutti
////            if ($("#filter-playerid").find("option:checked").length === 0) {
////                stopChartLoading = true;
////                $("#filter-playerid").find("option").each(function () {
////                    $("#filter-playerid").multiselect('select', $(this).val()).change();
////                });
////                stopChartLoading = false;
////            }
//            stopChartLoading = true;
//            $("#filter-playerid").find("option").each(function () {
//                $("#filter-playerid").multiselect('select', $(this).val());
//            });
//            $("#filter-playerid").trigger("change");
//            stopChartLoading = false;
//        });

        queue.enqueue(() => {
            // lista eventi default / caricati
            let index = filtersIndex.indexOf("eventtypeid-x");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                let value = localStorage.getItem("dataanalytics/610/" + letter + "/scatterplot/filter-eventtypeid-x");
                if (notEmpty(value)) {
                    $("#filter-eventtypeid-x").val(value).change();
                }
            }

            stopValueSave = false;
        });

        queue.enqueue(() => {
            // lista eventi default / caricati
            let index = filtersIndex.indexOf("tagtypeid-x");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                let value = localStorage.getItem("dataanalytics/610/" + letter + "/scatterplot/filter-tagtypeid-x");
                if (notEmpty(value)) {
                    value.split(",").forEach(function (option) {
                        $("#filter-tagtypeid-x").multiselect('select', option).change();
                    });
                }
            }

            stopValueSave = false;
        });

        queue.enqueue(() => {
            // lista eventi default / caricati
            let indexY = filtersIndex.indexOf("eventtypeid-y");
            if (indexY >= 0) {
                let letterY = ((indexY + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                let valueY = localStorage.getItem("dataanalytics/610/" + letterY + "/scatterplot/filter-eventtypeid-y");
                if (notEmpty(valueY)) {
                    $("#filter-eventtypeid-y").val(valueY).change();
                }
            }

            stopValueSave = false;
        });

        queue.enqueue(() => {
            // lista eventi default / caricati
            let indexY = filtersIndex.indexOf("tagtypeid-y");
            if (indexY >= 0) {
                let letterY = ((indexY + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (indexY > 25) {
                    indexY -= 25;
                    letterY = "Z" + ((indexY + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                let valueY = localStorage.getItem("dataanalytics/610/" + letterY + "/scatterplot/filter-tagtypeid-y");
                if (notEmpty(valueY)) {
                    valueY.split(",").forEach(function (option) {
                        $("#filter-tagtypeid-y").multiselect('select', option).change();
                    });
                }
            }

            stopValueSave = false;
        });

        queue.enqueue(() => {
            if (additionalFilterAmount === 0) {
                addAdditionalFilter();
                if ($("#filter-competitionid-1 option[value='" + $("#filter-competitionid").val() + "']").length > 0) {
                    $("#filter-competitionid-1").val($("#filter-competitionid").val()).change();
                }

                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            }
        });

        queue.enqueue(() => {
            // controllo eventi default
            if (eventFilter.currentFilterX === "undefined" || Object.keys(eventFilter.currentFilterX).length === 0) {
                let typeId = null;
                $("#filter-eventtypeid option").each(function (index, element) {
                    if (typeId === null && !$(element).val().includes("-")) {
                        typeId = $(element).val();
                        return false;
                    }
                });

                configureEventFilterModal(typeId);
                saveCurrentEventFilter();
            }
            if (eventFilter.currentFilterY === "undefined" || Object.keys(eventFilter.currentFilterY).length === 0) {
                let typeId = null;
                $("#filter-eventtypeid option").each(function (index, element) {
                    if (typeId === null && !$(element).val().includes("-") && $(element).val() !== eventFilter.currentFilterX[1].typeId) {
                        typeId = $(element).val();
                        return false;
                    }
                });

                configureEventFilterModal(typeId);
                saveCurrentEventFilter();
            }
        });

        // bind eventType-x per filtro custom
        $("#filter-eventtypeid-x").on("change", function () {
            if (!stopValueSave) {
                let elementId = $(this).attr("id");

                let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                if (index >= 0) {
                    let value = $(this).val();
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    if (index > 25) {
                        index -= 25;
                        letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    }
                    if (notEmpty(value)) {
                        localStorage.setItem("dataanalytics/610/" + letter + "/scatterplot/filter-eventtypeid-x", value);
                    } else {
                        localStorage.removeItem("dataanalytics/610/" + letter + "/scatterplot/filter-eventtypeid-x");
                    }
                }
            }
        });

        // bind tagtypeid-x per filtro custom
        $("#filter-tagtypeid-x").on("change", function () {
            if (!stopValueSave) {
                let elementId = $(this).attr("id");

                let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                if (index >= 0) {
                    let value = $(this).val();
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    if (index > 25) {
                        index -= 25;
                        letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    }
                    if (notEmpty(value)) {
                        localStorage.setItem("dataanalytics/610/" + letter + "/scatterplot/filter-tagtypeid-x", value);
                    } else {
                        localStorage.removeItem("dataanalytics/610/" + letter + "/scatterplot/filter-tagtypeid-x");
                    }
                }
            }
        });

        // bind eventType-y per filtro custom
        $("#filter-eventtypeid-y").on("change", function () {
            if (!stopValueSave) {
                let elementId = $(this).attr("id");

                let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                if (index >= 0) {
                    let value = $(this).val();
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    if (index > 25) {
                        index -= 25;
                        letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    }
                    if (notEmpty(value)) {
                        localStorage.setItem("dataanalytics/610/" + letter + "/scatterplot/filter-eventtypeid-y", value);
                    } else {
                        localStorage.removeItem("dataanalytics/610/" + letter + "/scatterplot/filter-eventtypeid-y");
                    }
                }
            }
        });

        // bind tagtypeid-y per filtro custom
        $("#filter-tagtypeid-y").on("change", function () {
            if (!stopValueSave) {
                let elementId = $(this).attr("id");

                let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                if (index >= 0) {
                    let value = $(this).val();
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    if (index > 25) {
                        index -= 25;
                        letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    }
                    if (notEmpty(value)) {
                        localStorage.setItem("dataanalytics/610/" + letter + "/scatterplot/filter-tagtypeid-y", value);
                    } else {
                        localStorage.removeItem("dataanalytics/610/" + letter + "/scatterplot/filter-tagtypeid-y");
                    }
                }
            }
        });
    });

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();

        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");

//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let seasonId = $("#filter-seasonid").val();
            let competitionId = $("#filter-competitionid").val();
            let teamId = $("#filter-teamid").val();
            let playerId = $("#filter-playerid").val();
            let eventTypeIdX = getCurrentFilterParameter(eventFilter.currentFilterX);
            let eventTypeIdY = getCurrentFilterParameter(eventFilter.currentFilterY);
            if (notEmpty(seasonId) && notEmpty(competitionId) && notEmpty(teamId) && notEmpty(playerId) && notEmpty(eventTypeIdX) && notEmpty(eventTypeIdY)) {
                let totalType = $("#filter-totaltype").val();
                if (notEmpty(totalType)) {
                    params.push("totalType;" + totalType);
                }
                params.push("seasonId;" + seasonId.join("/"));
                params.push("competitionId;" + competitionId);
                var canContinue = true;
                var extraElements = $(".additional-filter");
                if (extraElements.length > 0) {
                    for (var i = 0; i < extraElements.length; i++) {
                        let splitted = $(extraElements[i]).attr("id").split("-");
                        let index = splitted[splitted.length - 1];
                        if (notEmpty($("#filter-competitionid-" + index).val())) {
                            canContinue = true;
                            if ($(extraElements[i]).attr("id").includes("competitionid")) {
                                let tmpCompetitionId = $(extraElements[i]).val();
                                if (notEmpty(tmpCompetitionId)) {
                                    params.push("competitionId" + index + ";" + tmpCompetitionId);
                                } else {
                                    canContinue = false;
                                }
                            } else if ($(extraElements[i]).attr("id").includes("teamid")) {
                                let tmpTeamId = $(extraElements[i]).val();
                                if (notEmpty(tmpTeamId)) {
                                    params.push("teamId" + index + ";" + tmpTeamId);
                                }
                            } else {
                                let tmpPlayerId = $(extraElements[i]).val();
                                if (notEmpty(tmpPlayerId)) {
                                    params.push("playerId" + index + ";" + tmpPlayerId.join("|"));
                                }
                            }
                        } else {
                            canContinue = false;
                        }
                    }
                }
                if (!canContinue) {
                    showChartMessage(3);
                    new Noty({
                        text: "<spring:message code="messages.missing.filters"/>",
                        type: "warning",
                        layout: "topCenter"
                    }).show();
                    return;
                }
                if (typeof eventAmountSlider !== "undefined") {
                    let eventamount = eventAmountSlider.get();
                    if (parseInt(eventamount[0]) !== eventAmountSlider.options.range.min || parseInt(eventamount[1]) !== eventAmountSlider.options.range.max) {
                        params.push("minEventAmount;" + parseInt(eventamount[0]));
                        params.push("maxEventAmount;" + parseInt(eventamount[1]));
                    }
                }
                if (typeof eventAmountSliderY !== "undefined") {
                    let eventamount = eventAmountSliderY.get();
                    if (parseInt(eventamount[0]) !== eventAmountSliderY.options.range.min || parseInt(eventamount[1]) !== eventAmountSliderY.options.range.max) {
                        params.push("minEventAmountY;" + parseInt(eventamount[0]));
                        params.push("maxEventAmountY;" + parseInt(eventamount[1]));
                    }
                }
                params.push("teamId;" + teamId);
                params.push("playerId;" + playerId.join("|"));
                params.push("eventTypeIdX;" + eventTypeIdX);
//                let tagTypeIdX = $("#filter-tagtypeid-x").val();
//                if (notEmpty(tagTypeIdX)) {
//                    tagTypeIdX.sort((a, b) => Number(a) - Number(b));
//                    params.push("tagTypeIdX;" + tagTypeIdX.join("|"));
//                }
                params.push("eventTypeIdY;" + eventTypeIdY);
//                let tagTypeIdY = $("#filter-tagtypeid-y").val();
//                if (notEmpty(tagTypeIdY)) {
//                    tagTypeIdY.sort((a, b) => Number(a) - Number(b));
//                    params.push("tagTypeIdY;" + tagTypeIdY.join("|"));
//                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);
                showBlockUI();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/player/scatterplot/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        $.unblockUI();
                        if (notEmpty(result)) {
                            var data = JSON.parse(result);

                            if (jQuery.isEmptyObject(data.data)) {
                                $("#page-title").addClass("d-none");
                                if (params.length === 0) {
                                    showChartMessage(1);
                                } else {
                                    showChartMessage(2);
                                }
                            } else {
                                $(".message-div").addClass("d-none");
                                $("#chartdiv").children("div:not(.message-div)").removeClass("d-none");
                                if (typeof data.mMinPlaytime !== "undefined" && data.mMinPlaytime) {
                                    minPlaytimeFilter = data.mMinPlaytime;
                                }
                                $("#page-title").removeClass("d-none");
                                $("#page-title").html(getPageTitle());

                                if (data.data[0].maxEventAmount !== null) {
                                    updateMaxEventAmount(parseInt(data.data[0].maxEventAmount));
                                    if (data.data[0].maxEventAmountY !== null) {
                                        updateMaxEventAmountY(parseInt(data.data[0].maxEventAmountY));
                                    }
                                    data.data.shift();
                                }

                                let averageX = 0, averageY = 0, yAxisCounter = 0;
                                let minXValue = 0, minYValue = 0, maxXValue = 0, maxYValue = 0;
                                let teamPlayerIds = [];
                                data.data.forEach(function (element) {
                                    $.each(element, function (name, value) {
                                        if (name.startsWith("value")) {
                                            let playerId = name.replace("value", "");
                                            if (teamPlayerIds.indexOf(playerId) === -1) {
                                                teamPlayerIds.push(playerId);
                                            }
                                        }
                                        if (name === "x") {
                                            averageX += parseFloat(value);
                                            if (minXValue === 0 || parseFloat(value) < minXValue) {
                                                minXValue = parseFloat(value);
                                            }
                                            if (maxXValue === 0 || parseFloat(value) > maxXValue) {
                                                maxXValue = parseFloat(value);
                                            }
                                        } else if (name.startsWith("y")) {
                                            averageY += parseFloat(value);
                                            if (minYValue === 0 || parseFloat(value) < minYValue) {
                                                minYValue = parseFloat(value);
                                            }
                                            if (maxYValue === 0 || parseFloat(value) > maxYValue) {
                                                maxYValue = parseFloat(value);
                                            }
                                            yAxisCounter++;
                                        }
                                    });
                                });
                                let bestTeamPlayerValue = 0, bestPlayerId = 0, bestTeamId = 0, bestPlayerLogo = "", bestTeamPlayerXValue = 0, bestTeamPlayerYValue = 0;
                                data.data.forEach(function (element) {
                                    let xValue = ((element.x - minXValue) / (maxXValue - minXValue));
                                    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
                                        xValue = 1 - xValue;
                                    }
                                    $.each(element, function (name, value) {
                                        if (name.startsWith("y")) {
                                            let yValue = ((parseFloat(value) - minYValue) / (maxYValue - minYValue));
                                            if (isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
                                                yValue = 1 - yValue;
                                            }
                                            let total = xValue + yValue;
                                            if (total > bestTeamPlayerValue) {
                                                bestTeamPlayerValue = total;
                                                let teamPlayer = name.replace("y", "");
                                                bestTeamId = teamPlayer.split("-")[0];
                                                bestPlayerId = teamPlayer.split("-")[1];
                                                bestPlayerLogo = element["logo" + teamPlayer].photoSrc;
                                                bestTeamPlayerXValue = element.x;
                                                bestTeamPlayerYValue = value;
                                            }
                                        }
                                    });
                                });
                                averageX = Math.round(averageX / data.data.length * 100) / 100;
                                averageY = Math.round(averageY / yAxisCounter * 100) / 100;

                                if (typeof chart === "undefined") {
                                    getPlayerScatterplotChart(data.data);
                                } else {
                                    // tolgo le altre serie se ci sono
                                    chart.series.clear();
                                    createPlayerScatterplotAxis(chart, data.data);
                                }

                                teamPlayerIds.forEach(function (teamPlayerId) {
                                    addPlayerScatterplotSeries(chart, teamPlayerId, data, "${mUser.tvLanguage}");
                                });

                                root.events.once("frameended", function () {
                                    let minXValue = xAxis._privateSettings.selectionMin;
                                    let maxXValue = xAxis._privateSettings.selectionMax;
                                    let minYValue = yAxis._privateSettings.selectionMin;
                                    let maxYValue = yAxis._privateSettings.selectionMax;
                                    let xStart = ((averageX - minXValue) / (maxXValue - minXValue)) * 100;
                                    let yStart = ((averageY - minYValue) / (maxYValue - minYValue)) * 100;
                                    if (isOpposite(eventFilter.currentFilterX[1].typeId, eventFilter.currentFilterX[1].tagTypes)) {
                                        xStart = 100 - xStart;
                                    }
                                    if (!isOpposite(eventFilter.currentFilterY[1].typeId, eventFilter.currentFilterY[1].tagTypes)) {
                                        yStart = 100 - yStart;
                                    }

                                    // -(90 - (Math.round((Math.atan(chartHeight / chartWidth) * (180 / Math.PI) / 100) * 100)))
                                    var redGradient = am5.LinearGradient.new(root, {
                                        stops: [
                                            {color: am5.color(0xef4444), opacity: 0.5}, // Top-right corner (orange)
                                            {color: am5.color(0xef4444), opacity: 0.05}    // Bottom-left corner (dark blue)
                                        ],
                                        rotation: -45 // Rotates the gradient from top-right to bottom-left
                                    });
                                    var greenGradient = am5.LinearGradient.new(root, {
                                        stops: [
                                            {color: am5.color(0x059669), opacity: 0.05}, // Top-right corner (orange)
                                            {color: am5.color(0x059669), opacity: 0.5}    // Bottom-left corner (dark blue)
                                        ],
                                        rotation: -45 // Rotates the gradient from top-right to bottom-left
                                    });

                                    if (extraLayerContainer) {
                                        extraLayerContainer.dispose();
                                    }
                                    extraLayerContainer = chart.plotContainer.children.push(
                                            am5.Container.new(root, {
                                                x: 0, // Align to the right
                                                y: 0, // Align to the top
                                                width: am5.p100, // Adjust as needed
                                                height: am5.p100
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Rectangle.new(root, {
                                                x: 0,
                                                y: am5.percent(yStart),
                                                width: am5.percent(xStart),
                                                height: am5.percent(100 - yStart),
                                                fillGradient: redGradient
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Rectangle.new(root, {
                                                x: am5.percent(xStart),
                                                y: 0,
                                                width: am5.percent(100 - xStart),
                                                height: am5.percent(yStart),
                                                fillGradient: greenGradient
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Line.new(root, {
                                                stroke: am5.color(0x000000), // Line color
                                                strokeDasharray: [10], // Dotted pattern
                                                strokeWidth: 1,
                                                x: 0, // Start X position
                                                y: am5.percent(yStart), // Start Y position
                                                width: am5.percent(100),
                                                rotation: 0
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Label.new(root, {
                                                text: "<spring:message code="messages.generic.average"/>".toUpperCase(), // Text content
                                                fontSize: 14, // Font size
                                                fontWeight: "bold",
                                                fill: am5.color(0x000000), // Text color
                                                x: 0, // Same X as the line
                                                y: am5.percent(yStart), // Align Y with the line's start position
                                                centerY: am5.p100, // Center vertically
                                                centerX: 0          // Align to the left of the text box
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Line.new(root, {
                                                stroke: am5.color(0x000000), // Line color
                                                strokeDasharray: [10], // Dotted pattern
                                                strokeWidth: 1,
                                                x: am5.percent(xStart), // Start X position
                                                y: 0, // Start Y position
                                                height: am5.percent(100),
                                                rotation: 0
                                            })
                                            );
                                    extraLayerContainer.children.push(
                                            am5.Label.new(root, {
                                                text: "<spring:message code="messages.generic.average"/>".toUpperCase(), // Text content
                                                fontSize: 14, // Font size
                                                fontWeight: "bold",
                                                fill: am5.color(0x000000), // Text color
                                                x: am5.percent(xStart), // Same X as the line
                                                y: am5.p100, // Align Y with the line's start position
                                                centerY: am5.p100, // Center vertically
                                                centerX: 0, // Align to the left of the text box
                                                rotation: -90
                                            })
                                            );

                                    let teamsData = new Map(), playersData = new Map();
                                    ;
                                    data.teams.forEach(function (element) {
                                        let [key, value] = Object.entries(element)[0];
                                        teamsData.set(key, JSON.parse(value));
                                    });
                                    data.players.forEach(function (element) {
                                        let [key, value] = Object.entries(element)[0];
                                        playersData.set(key, JSON.parse(value));
                                    });

                                    var hideBests = $("#filter-hidebest").is(":checked");
                                    if (!hideBests) {
                                        var team = teamsData.get(bestTeamId);
                                        var player = playersData.get(bestPlayerId);
                                        if (team) {
                                            // best team container
                                            var bestTeamContainer = extraLayerContainer.children.push(
                                                    am5.Container.new(root, {
                                                        x: am5.percent(86), // Align to the right
                                                        y: am5.percent(4), // Align to the top
                                                        width: am5.percent(12), // Adjust as needed
                                                        background: am5.RoundedRectangle.new(root, {
                                                            fill: am5.color(0xFFC335),
                                                            fillOpacity: 0.6,
                                                            cornerRadiusTL: 20,
                                                            cornerRadiusTR: 20,
                                                            cornerRadiusBR: 20,
                                                            cornerRadiusBL: 20,
                                                            shadowColor: am5.color(0x000000),
                                                            shadowBlur: 7,
                                                            shadowOffsetX: 1,
                                                            shadowOffsetY: 1
                                                        }),
                                                        layout: root.verticalLayout
                                                    })
                                                    );

                                            // Add text below the image
                                            bestTeamContainer.children.push(
                                                    am5.Label.new(root, {
                                                        text: "<spring:message code="messages.best.player"/>".toUpperCase(),
                                                        fontWeight: "bold",
                                                        fontSize: 11, // Adjust font size
                                                        fill: am5.color(0xffffff), // Adjust text color
                                                        textAlign: "center",
                                                        x: am5.percent(50),
                                                        centerX: am5.percent(50)
                                                    })
                                                    );
                                            // Add an image
                                            bestTeamContainer.children.push(
                                                    am5.Picture.new(root, {
                                                        width: 100, // Adjust as needed
                                                        height: 100, // Adjust as needed
                                                        src: bestPlayerLogo, // URL to your image
                                                        shadowColor: am5.color(0xffffff),
                                                        shadowBlur: 7,
                                                        shadowOffsetX: 1,
                                                        shadowOffsetY: 1,
                                                        x: am5.percent(50),
                                                        centerX: am5.percent(50)
                                                    })
                                                    );
                                            let infoPlayerText = "[bold]" + player.knownName + "[/]\n" + team.name + "\n";
                                            infoPlayerText += "<spring:message code="messages.x.value"/>: [bold]" + bestTeamPlayerXValue + "[/]\n";
                                            infoPlayerText += "<spring:message code="messages.y.value"/>: [bold]" + bestTeamPlayerYValue + "[/]";
                                            // Add text below the image
                                            bestTeamContainer.children.push(
                                                    am5.Label.new(root, {
                                                        text: infoPlayerText,
                                                        fontSize: 11, // Adjust font size
                                                        fill: am5.color(0xffffff), // Adjust text color
                                                        textAlign: "center",
                                                        x: am5.percent(50),
                                                        centerX: am5.percent(50),
                                                        wrap: true, // Wrap text if needed
                                                        maxWidth: am5.percent(90) // Prevent text overflow
                                                    })
                                                    );
                                        }
                                    }
                                });
                            }
                        }
                        stopChartLoading = false;
                    },
                    error: function () {
                        $.unblockUI();
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }

    function setModality(modality) {
        eventFilter.scatterplotModality = modality;
        if (typeof eventFilter.allEvents !== "undefined") {
            loadEventFilterModal();
        } else {
            // c'� qualcosa che non va
            event.preventDefault();
            event.stopPropagation();

            new Noty({
                text: globalMessages.get("error.unexpected"),
                type: "error",
                layout: "topCenter"
            }).show();
        }
    }

//    function customUpdateFilters(object, filterChanged) {
//        Object.keys(object).forEach(function (filterId) {
//            var value = object[filterId];
//            if (filterId === "eventtypeid") {
//                if (notEmpty(value)) {
//                    if (notEmpty(object["advancedeventtypeid"])) {
//                        let splitted = object["advancedeventtypeid"].split(",");
//                        splitted.forEach(function (element) {
//                            value += "," + (globalMessages.get("filters.event.advanced.metrics") + "|" + element);
//                        });
//                    }
//                    if (notEmpty(object["tacticaleventtypeid"])) {
//                        let splitted = object["tacticaleventtypeid"].split(",");
//                        splitted.forEach(function (element) {
//                            value += "," + (globalMessages.get("filters.event.tactical") + "|" + element);
//                        });
//                    }
//                    let filterToUpdate = "eventtypeid-x";
//                    var previousValue = $("#filter-" + filterToUpdate).val();
//
//                    $("#filter-" + filterToUpdate + " option").remove();
//                    $("#filter-" + filterToUpdate + " optgroup").remove();
//                    $("#filter-" + filterToUpdate).append($("<option>"));
//
//                    var options = value.split(",");
//                    options.forEach(function (element) {
//                        var elementParts = element.split("|");
//                        if (elementParts.length === 2) {
//                            $("#filter-" + filterToUpdate).append($("<option>", {
//                                value: elementParts[0],
//                                text: elementParts[1]
//                            }));
//                        } else if (elementParts.length === 3) {
//                            // oggetti raggruppati
//                            let optGroupLabel = elementParts[0];
//                            // creo il group se non esiste
//                            if ($("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").length === 0) {
//                                $("#filter-" + filterToUpdate).append($("<optgroup>", {
//                                    label: optGroupLabel
//                                }));
//                            }
//                            $("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").append($("<option>", {
//                                value: elementParts[1],
//                                text: elementParts[2]
//                            }));
//                        }
//                    });
//                    sortFilterOptions(filterToUpdate);
//
//                    if (keepFilterValue) {
//                        if (notEmpty(previousValue)) {
//                            // se la select ha quel valore allora lo imposto
//                            if ($("#filter-" + filterToUpdate + " option[value='" + previousValue + "']").length > 0) {
//                                if ($.isArray(previousValue)) {
//                                    $("#filter-" + filterToUpdate).val(previousValue).trigger('change');
//                                } else {
//                                    $("#filter-" + filterToUpdate).val(previousValue);
//                                }
//                            }
//                        }
//                    }
//
//                    animate("filter-" + filterToUpdate);
//
//                    filterToUpdate = "eventtypeid-y";
//                    previousValue = $("#filter-" + filterToUpdate).val();
//
//                    $("#filter-" + filterToUpdate + " option").remove();
//                    $("#filter-" + filterToUpdate + " optgroup").remove();
//                    $("#filter-" + filterToUpdate).append($("<option>"));
//
//                    options = value.split(",");
//                    options.forEach(function (element) {
//                        var elementParts = element.split("|");
//                        if (elementParts.length === 2) {
//                            $("#filter-" + filterToUpdate).append($("<option>", {
//                                value: elementParts[0],
//                                text: elementParts[1]
//                            }));
//                        } else if (elementParts.length === 3) {
//                            // oggetti raggruppati
//                            let optGroupLabel = elementParts[0];
//                            // creo il group se non esiste
//                            if ($("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").length === 0) {
//                                $("#filter-" + filterToUpdate).append($("<optgroup>", {
//                                    label: optGroupLabel
//                                }));
//                            }
//                            $("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").append($("<option>", {
//                                value: elementParts[1],
//                                text: elementParts[2]
//                            }));
//                        }
//                    });
//                    sortFilterOptions(filterToUpdate);
//
//                    if (keepFilterValue) {
//                        if (notEmpty(previousValue)) {
//                            // se la select ha quel valore allora lo imposto
//                            if ($("#filter-" + filterToUpdate + " option[value='" + previousValue + "']").length > 0) {
//                                if ($.isArray(previousValue)) {
//                                    $("#filter-" + filterToUpdate).val(previousValue).trigger('change');
//                                } else {
//                                    $("#filter-" + filterToUpdate).val(previousValue);
//                                }
//                            }
//                        }
//                    }
//
//                    animate("filter-" + filterToUpdate);
//                }
//            } else if (filterId === "tagtypeid") {
//                if (filterChanged === "eventTypeIdX" || filterChanged === "eventTypeIdY") {
//                    let filterToUpdate = "tagtypeid-x";
//                    if (filterChanged === "eventTypeIdY") {
//                        filterToUpdate = "tagtypeid-y";
//                    }
//                    var previousValue = $("#filter-" + filterToUpdate).val();
//
//                    $("#filter-" + filterToUpdate + " option").remove();
//                    $("#filter-" + filterToUpdate + " optgroup").remove();
//                    // $("#filter-" + filterToUpdate).append($("<option>"));
//
//                    var options = value.split(",");
//                    options.forEach(function (element) {
//                        var elementParts = element.split("|");
//                        if (elementParts.length === 2) {
//                            $("#filter-" + filterToUpdate).append($("<option>", {
//                                value: elementParts[0],
//                                text: elementParts[1]
//                            }));
//                        } else if (elementParts.length === 3) {
//                            // oggetti raggruppati
//                            let optGroupLabel = elementParts[0];
//                            // creo il group se non esiste
//                            if ($("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").length === 0) {
//                                $("#filter-" + filterToUpdate).append($("<optgroup>", {
//                                    label: optGroupLabel
//                                }));
//                            }
//                            $("#filter-" + filterToUpdate + " optgroup[label='" + optGroupLabel + "']").append($("<option>", {
//                                value: elementParts[1],
//                                text: elementParts[2]
//                            }));
//                        }
//                    });
//                    sortFilterOptions(filterToUpdate);
//                    $("#filter-" + filterToUpdate).multiselect('rebuild');
//
//                    if (keepFilterValue) {
//                        if (notEmpty(previousValue)) {
//                            // se la select ha quel valore allora lo imposto
//                            previousValue.forEach(function (element) {
//                                $("#filter-" + filterToUpdate).multiselect('select', element);
//                            });
//                            $("#filter-" + filterToUpdate).trigger("change");
//                        }
//                    }
//
//                    animate("filter-" + filterToUpdate);
//                }
//            }
//        });
//    }

    var additionalFilterAmount = 0;
    function addAdditionalFilter() {
        additionalFilterAmount++;

        $('#filter-competitionid').select2('destroy');
        $('#filter-teamid').select2('destroy');
        $('#filter-playerid').multiselect('destroy');

        var clonedSeparator = $("#additional-team-separator").clone();
        clonedSeparator.removeClass("d-none");
        clonedSeparator.removeAttr("id");
        clonedSeparator.find("i").attr("index", additionalFilterAmount);
        $("#add-team-button").before(clonedSeparator);

        var clonedCompetitionId = $("#container-competitionid").clone();
        clonedCompetitionId.addClass("mt-2");
        clonedCompetitionId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedCompetitionId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedCompetitionId.find("span").remove();
        clonedCompetitionId.find("select").attr("onchange", clonedCompetitionId.find("select").attr("onchange").replace("updateFilters('competitionId');", "updateFilters('competitionId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedCompetitionId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedCompetitionId);
        $("#filter-competitionid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedTeamId = $("#container-teamid").clone();
        // clonedTeamId.addClass("mt-2");
        clonedTeamId.find("select").removeClass("is-required");
        clonedTeamId.find("select").removeClass("is-required-extra");
        clonedTeamId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedTeamId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedTeamId.find("select").attr("onchange", clonedTeamId.find("select").attr("onchange").replace("updateFilters('teamId');", "updateFilters('teamId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedTeamId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedTeamId);
        $("#filter-teamid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedPlayerId = $("#container-playerid").clone();
        clonedPlayerId.addClass("mt-2");
        clonedPlayerId.find("select").removeClass("is-required");
        clonedPlayerId.find("select").removeClass("is-required-extra");
        clonedPlayerId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedPlayerId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedPlayerId.find("select").attr("onchange", clonedPlayerId.find("select").attr("onchange").replace("updateChart('playerId');", "updateChart('playerId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedPlayerId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedPlayerId);
        $("#filter-playerid-" + additionalFilterAmount).multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        $('#filter-competitionid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-teamid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-playerid').multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        // fix css per search
        $('.multiselect-search').css('padding-left', 'calc(0.875rem * 2 + 1.25rem)');
        updateFiltersColor();
        reloadTooltips();
        bindFiltersSave();

        if (notEmpty($("#filter-competitionid-" + additionalFilterAmount).val())) {
            $("#filter-competitionid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-teamid-" + additionalFilterAmount).val())) {
            $("#filter-teamid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-playerid-" + additionalFilterAmount).val())) {
            $("#filter-playerid-" + additionalFilterAmount).trigger("custom-change");
        }
    }

    function removeAdditionalFilter() {
        if (typeof event.target !== "undefined") {
            let index = $(event.target).attr("index");
            if (notEmpty(index)) {
                $("#container-competitionid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-competitionid-" + index).remove();
                $("#container-teamid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-teamid-" + index).remove();
                $("#container-playerid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-playerid-" + index).remove();
                $(event.target).parent().remove();
                updateChart();
            }
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <div class="sidebar sidebar-secondary sidebar-expand-lg">
            <!-- Expand button -->
            <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                <i class="ph-caret-right"></i>
            </button>
            <!-- /expand button -->

            <!-- Sidebar content -->
            <div class="sidebar-content" id="filters-containter">

                <!-- Header -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                    <div class="me-1">
                        <i class="ph-arrow-counter-clockwise cursor-pointer" title="<spring:message code="filters.reset"/>" onclick="resetFilters();"></i>
                        <i class="ph-funnel cursor-pointer" title="<spring:message code="filters.show.hide"/>" onclick="managePersonalFilters();"></i>
                    </div>
                    <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex">
                            <i class="ph-arrows-left-right"></i>
                        </button>

                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                            <i class="ph-x"></i>
                        </button>
                    </div>
                </div>
                <!-- /header -->

                <!-- Header Filter Message -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center justify-content-center mb-0 mt-0 py-1 d-none" id="specific-filter-message">
                    <div>
                        <i class="ph-warning text-warning"></i><span class="ms-1 text-warning"><spring:message code="filters.specific.warning"/></span>
                    </div>
                </div>
                <!-- /header filter message -->

                <!-- Sidebar search -->
                <div class="sidebar-section">
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-4 d-none" id="personal-filters-container">
                            <label class="form-label mb-0"><spring:message code="personal.filters"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="personal.filters.placeholder"/>" class="form-control form-control-sm select" id="personal-filter" onchange="loadPersonalFilter();" data-minimum-results-for-search="Infinity" data-width="1%">
                                        <option value=""></option>
                                        <c:forEach var="filter" items="${mFilters}">
                                            <option value="${filter.id}">${filter.name}</option>
                                        </c:forEach>
                                    </select>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" id="personal-filter-update" onclick="updatePersonalFilter();" title="<spring:message code="personal.filter.save.placeholder"/>" disabled>
                                        <i class="ph-pencil"></i>
                                    </button>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" onclick="addPersonalFilter();" title="<spring:message code="personal.filter.add.placeholder"/>">
                                        <i class="ph-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-seasonid">
                            <label class="form-label mb-0"><spring:message code="filters.season"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="row">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.season.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required no-search" id="filter-seasonid" onchange="updateChart('seasonid');" data-minimum-results-for-search="Infinity">
                                        <c:forEach var="season" items="${mSeasons}">
                                            <option value="${season.id}">${season.name}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-competitionid">
                            <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-competitionid" onchange="updateFilters('competitionId');">
                                </select>
                            </div>
                        </div>
                        <div class="mb-2" id="container-teamid">
                            <label class="form-label mb-0"><spring:message code="filters.team"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="Teams..." class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-teamid" onchange="updateFilters('teamId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-playerid">
                            <label class="form-label mb-0"><spring:message code="filters.player"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="row">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.player.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required is-required-extra" id="filter-playerid" onchange="updateChart('playerId');" data-width="1%" data-include-select-all-option="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.displayed"/></label>
                            <div class="form-control-feedback form-control-feedback-end">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.displayed.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-totaltype" onchange="updateChart('totalType');" data-minimum-results-for-search="Infinity">
                                    <option value="totals" selected><spring:message code="filters.displayed.totals"/></option>
                                    <option value="p90"><spring:message code="filters.displayed.p90"/></option>
                                    <option value="touches"><spring:message code="filters.displayed.touches"/></option>
                                    <option value="average"><spring:message code="filters.displayed.average"/></option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-3 d-none" id="additional-team-separator">
                            <div class="hr"></div>
                            <i class="ph-trash cursor-pointer mx-1" onclick="removeAdditionalFilter();"></i>
                            <div class="hr"></div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mt-3" id="add-team-button">
                            <div class="bg-info bg-opacity-20 rounded-pill text-info p-2 cursor-pointer" onclick="addAdditionalFilter();">
                                <i class="ph-plus"></i>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                    </div>


                    <div class="sidebar-section-body mb-0 py-1">
                        <button type="button" class="btn btn-info w-100" onclick="setModality(1);" data-bs-toggle="modal" data-bs-target="#modal-event-builder"><spring:message code="filters.metrics.parameters"/></button>
                    </div>
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events-y"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-y-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-y-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                    </form>
                    <form class="sidebar-section-body mb-0 px-0 py-0 multiple-events d-none" action="#" id="event-filters-container">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="event-container-nav">
                            <li class="nav-item">
                                <a href="#event-filters-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-advanced" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-tactical" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-3">
                            <div class="tab-pane fade show active" id="event-filters-event">
                                <div class="mb-2" id="container-eventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required skip-title is-required-extra eventtypeid" id="filter-eventtypeid" onchange="updateChart('eventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-advanced">
                                <div class="mb-2" id="container-advancedeventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-title eventtypeid" id="filter-advancedeventtypeid" onchange="updateChart('advancedeventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-tactical">
                                <div class="mb-2" id="container-tacticaleventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-title eventtypeid" id="filter-tacticaleventtypeid" onchange="updateChart('tacticaleventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="player.scatterplot.options"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <div class="form-check form-switch mb-2">
                                <input type="checkbox" class="form-control is-filter form-check-input" id="filter-hidebest" onchange="updateChart('hidebest');">
                                <label class="form-check-label" for="filter-hidebest"><spring:message code="player.scatterplot.options.hide.best"/></label>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <!--                <div class="sidebar-section">
                                    <div class="sidebar-section-header border-bottom">
                                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                                    </div>

                                    <form class="sidebar-section-body mb-0 py-1" action="#">
                                        <div class="mb-2">
                                            <label class="form-label mb-0"><spring:message code="filters.event.x.axis"/></label>
                                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-required" id="filter-eventtypeid-x" onchange="updateFilters('eventTypeIdX');">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label mb-0"><spring:message code="filters.event.tags.x.axis"/></label>
                                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.x.axis.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect" id="filter-tagtypeid-x" onchange="updateChart('tagTypeId');">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mb-2 animation" data-animation="zoomIn">
                                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                                            <div class="px-2">
                                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events"></div>

                                                <div class="clearfix">
                                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-lower-val"></span></span>
                                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-upper-val"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label mb-0"><spring:message code="filters.event.y.axis"/></label>
                                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-required" id="filter-eventtypeid-y" onchange="updateFilters('eventTypeIdY');">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <label class="form-label mb-0"><spring:message code="filters.event.tags.y.axis"/></label>
                                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.y.axis.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect" id="filter-tagtypeid-y" onchange="updateChart('tagTypeId');">
                                                </select>
                                            </div>
                                        </div>
                                        <div class="animation" data-animation="zoomIn">
                                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                                            <div class="px-2">
                                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events-y"></div>

                                                <div class="clearfix">
                                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-y-lower-val"></span></span>
                                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-y-upper-val"></span></span>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>-->
                <!-- /sidebar advanced search -->
            </div>
            <!-- /sidebar content -->
        </div>

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content" id="container">
                    <div class="row">
                        <h6 class="mb-1 text-center" id="page-title"></h6>
                    </div>
                    <div class="row">
                        <div class="amchart" id="chartdiv">
                            <%@ include file="../global/messages.jsp" %>
                        </div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
