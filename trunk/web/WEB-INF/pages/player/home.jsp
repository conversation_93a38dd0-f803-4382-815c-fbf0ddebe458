<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        $("#headerHomeButton").addClass("active");
    });
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content">
                    <div class="row">
                        <c:if test="${mUser.hasAccess(21)}">
                            <!-- Ranking block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/ranking.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.ranking"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.ranking.description"/></p>
                                        <a href="/sicsdataanalytics/player/ranking.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.ranking.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /ranking block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(22)}">
                            <!-- Trend block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/trend.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.trend"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.trend.description"/></p>
                                        <a href="/sicsdataanalytics/player/trend.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.trend.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /trend block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(23)}">
                            <!-- Overview block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/overview.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.overview"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.overview.description"/></p>
                                        <a href="/sicsdataanalytics/player/overview.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.overview.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /overview block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(24)}">
                            <!-- Positional block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/positional.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.positional"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.positional.description"/></p>
                                        <a href="/sicsdataanalytics/player/positional.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.positional.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /positional block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(25)}">
                            <!-- Scatterplot block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/scatterplot.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.scatterplot"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.scatterplot.description"/></p>
                                        <a href="/sicsdataanalytics/player/scatterplot.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.scatterplot.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /scatterplot block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(26)}">
                            <!-- Radar block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/radar.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.radar"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.radar.description"/></p>
                                        <a href="/sicsdataanalytics/player/radar.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.radar.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /radar block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(27)}">
                            <!-- Distribution block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/distribution.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.distribution"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.distribution.description"/></p>
                                        <a href="/sicsdataanalytics/player/distribution.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.distribution.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /distribution block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(28)}">
                            <!-- Playtime block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/playtime.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.playtime"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.playtime.description"/></p>
                                        <a href="/sicsdataanalytics/player/playtime.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.playtime.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /playtime block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(29)}">
                            <!-- Top Players block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/top.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.top"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.top.description"/></p>
                                        <a href="/sicsdataanalytics/player/top.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.top.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /top players block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(30)}">
                            <!-- Similarity block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/similarity.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.similarity"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.similarity.description"/></p>
                                        <a href="/sicsdataanalytics/player/similarity.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.player.similarity.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /similarity block -->
                        </c:if>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
