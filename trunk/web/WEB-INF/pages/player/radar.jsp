<%@ include file="../global/main.jsp" %>

<!-- D3.js Library for canvas/svg -->
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>
<script src="/sicsdataanalytics/js/sics-canvas-player.js" type="text/javascript"></script>

<style>
    #chartdiv {
        height: 75vh;
    }
</style>

<script type="text/javascript">
    var matchdaySlider;
    // var colorScale = chroma.scale(['#99d98c', '#76c893', '#52b69a', '#34a0a4', '#168aad', '#1a759f', '#1e6091', '#184e77']).classes(100);
    // var colorScale = chroma.scale(['#34a0a4', '#168aad', '#1a759f', '#1e6091', '#184e77']).classes(100);
    var colorScale = chroma.scale(['#ff595e', '#ffca3a', '#8ac926', '#1982c4', '#6a4c93']).classes(10);
    var colorIndex = 0;
    var stopValueSave = true;
    var playerTeamLogo = new Map();
    $(document).ready(function () {
        $("#playerRadarButton").addClass("active");

        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();
            // non faccio niente perch� c'� evento bindato sul campo input
        });

        showChartMessage(1);

        // Define element
        const slider_drag_behaviour = document.getElementById('noui-slider-drag');
        // Create slider
        matchdaySlider = noUiSlider.create(slider_drag_behaviour, {
            start: [1, 100],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 100
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const slider_drag_behaviour_vals = [
            document.getElementById('noui-slider-drag-lower-val'),
            document.getElementById('noui-slider-drag-upper-val')
        ];
        // Show the values
        slider_drag_behaviour.noUiSlider.on('update', function (values, handle) {
            slider_drag_behaviour_vals[handle].innerHTML = values[handle];
        });

        matchdaySlider.on("change", function () {
            let index = filtersIndex.indexOf("matchday");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-matchday", matchdaySlider.get());
            }
            updateChart("matchday");
        });

        checkInitialFilters();
//        queue.enqueue(() => {
//            if ($("#filter-seasonid-1").length === 0) {
//                addAdditionalFilter();
//                // di base imposto la stessa squadra ma nell'anno prima
//                let selectedIndex = $("#filter-seasonid").prop("selectedIndex");
//                if ($("#filter-seasonid-1 option").eq(selectedIndex + 1).length > 0) {
//                    $("#filter-seasonid-1").prop("selectedIndex", (selectedIndex + 1)).change();
//                } else {
//                    $("#filter-seasonid-1").prop("selectedIndex", selectedIndex).change();
//                }
//            }
//        });
//        queue.enqueue(() => {
//            if (empty($("#filter-competitionid-1").val())) {
//                if ($("#filter-competitionid-1 option[value='" + $("#filter-competitionid").val() + "']").length > 0) {
//                    $("#filter-competitionid-1").val($("#filter-competitionid").val()).change();
//                } else {
//                    // nel caso quella competizione non esiste nella stagione scorsa allora seleziono la prima
//                    $("#filter-competitionid-1").prop("selectedIndex", 1).change();
//                }
//            }
//        });
//        queue.enqueue(() => {
//            if (empty($("#filter-teamid-1").val())) {
//                if ($("#filter-teamid-1 option[value='" + $("#filter-teamid").val() + "']").length > 0) {
//                    $("#filter-teamid-1").val($("#filter-teamid").val()).change();
//                } else {
//                    // nel caso quel team non esiste nella stagione scorsa allora seleziono il primo team
//                    $("#filter-teamid-1").prop("selectedIndex", 1).change();
//                }
//            }
//        });
//        queue.enqueue(() => {
//            if (empty($("#filter-playerid-1").val())) {
//                let playerIds = $("#filter-playerid").val();
//                playerIds.forEach(function (playerId) {
//                    $("#filter-playerid-1").multiselect('select', playerId);
//                });
//                if (empty($("#filter-playerid-1").val())) {
//                    $("#filter-playerid-1").multiselect("select", $('#filter-playerid-1 option:first-child').val());
//                }
//                $("#filter-playerid-1").trigger("change");
//            }
//        });
        queue.enqueue(() => {
            // lista eventi default / caricati
            let index = filtersIndex.indexOf("eventtypeid");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                let value = localStorage.getItem("dataanalytics/610/" + letter + "/radar/filter-eventtypeid");
                if (empty(value)) {
                    value = "28,1,20,8,9,100,12,10,11,25,5,102";
                }
                $("#filter-eventtypeid").multiselect('clearSelection');
                value.split(",").forEach(function (value) {
                    $("#filter-eventtypeid").multiselect('select', value);
                });
//                stopValueSave = false;
            }
        });

        // ricarico ora i filtri (dopo aver messo tutti gli eventi)
        queue.enqueue(() => {
            if (typeof eventFilter.currentFilter === "undefined" || Object.keys(eventFilter.currentFilter).length === 0) {
                reloadEventFilterDefaults();
            }
            $("#filter-eventtypeid").trigger("change");
        });
        queue.enqueue(() => {
            // c'� qualcosa che non va in pagina quindi lancio manualmente un caricamento del grafico
            updateChart();
        });

        // bind eventType per filtro custom per radar
        $("#filter-eventtypeid").on("change", function () {
            if (!stopValueSave) {
                let elementId = $(this).attr("id");

                let index = filtersIndex.indexOf(elementId.replace("filter-", ""));
                if (index >= 0) {
                    let value = $(this).val();
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    if (index > 25) {
                        index -= 25;
                        letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    }
                    if (notEmpty(value)) {
                        localStorage.setItem("dataanalytics/610/" + letter + "/radar/filter-eventtypeid", value);
                    } else {
                        localStorage.removeItem("dataanalytics/610/" + letter + "/radar/filter-eventtypeid");
                    }
                }
            }
        });
    });

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();

        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");

//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let playerTeamSeasonIds = [];
            let seasonId = $("#filter-seasonid").val();
            let competitionId = $("#filter-competitionid").val();
            let teamId = $("#filter-teamid").val();
            let playerIds = $("#filter-playerid").val();
            let eventTypeIds = getCurrentFilterParameter();
            if (notEmpty(seasonId) && notEmpty(competitionId) && notEmpty(teamId) && notEmpty(playerIds) && notEmpty(eventTypeIds)) {
                let totalType = $("#filter-totaltype").val();
                if (notEmpty(totalType)) {
                    params.push("totalType;" + totalType);
                }
                params.push("seasonId;" + seasonId);
                params.push("competitionId;" + competitionId);
                params.push("teamId;" + teamId);
                params.push("playerId;" + playerIds.join("|"));
                var extraElements = $(".additional-filter");
                // controllo dopo se bisogna mostrarlo
                // playerTeamSeasonIds.push("0-0-" + competitionId + "-" + seasonId);
                $("#filter-playerid").val().forEach(function (playerId) {
                    playerTeamSeasonIds.push(playerId + "-" + teamId + "-" + competitionId + "-" + seasonId);
                });
                var canContinue = true;
                if (extraElements.length > 0) {
                    for (var i = 0; i < extraElements.length; i++) {
                        let splitted = $(extraElements[i]).attr("id").split("-");
                        let index = splitted[splitted.length - 1];
                        if (notEmpty($("#filter-playerid-" + index).val())) {
                            canContinue = true;
                            if ($(extraElements[i]).attr("id").includes("seasonid")) {
                                let tmpSeasonId = $(extraElements[i]).val();
                                if (notEmpty(tmpSeasonId)) {
                                    params.push("seasonId" + index + ";" + tmpSeasonId);
                                } else {
                                    canContinue = false;
                                }
                            } else if ($(extraElements[i]).attr("id").includes("competitionid")) {
                                let tmpCompetitionId = $(extraElements[i]).val();
                                if (notEmpty(tmpCompetitionId)) {
                                    params.push("competitionId" + index + ";" + tmpCompetitionId);
                                } else {
                                    canContinue = false;
                                }
                            } else if ($(extraElements[i]).attr("id").includes("teamid")) {
                                let tmpTeamId = $(extraElements[i]).val();
                                if (notEmpty(tmpTeamId)) {
                                    params.push("teamId" + index + ";" + tmpTeamId);
                                } else {
                                    canContinue = false;
                                }
                            } else {
                                let tmpPlayerId = $(extraElements[i]).val();
                                if (notEmpty(tmpPlayerId)) {
                                    params.push("playerId" + index + ";" + tmpPlayerId.join("|"));

                                    let tmpSeasonId = $("#filter-seasonid-" + index).val();
                                    let tmpCompetitionId = $("#filter-competitionid-" + index).val();
                                    let tmpTeamId = $("#filter-teamid-" + index).val();
                                    tmpPlayerId.forEach(function (playerId) {
                                        let playerSeasonId = playerId + "-" + tmpTeamId + "-" + tmpCompetitionId + "-" + tmpSeasonId;
                                        if (playerTeamSeasonIds.indexOf(playerSeasonId) === -1) {
                                            playerTeamSeasonIds.push(playerSeasonId);
                                        }
                                    });
                                } else {
                                    canContinue = false;
                                }
                            }
                        } else {
                            canContinue = false;
                        }
                    }
                }
                if (!canContinue) {
                    showChartMessage(3);
                    new Noty({
                        text: "<spring:message code="messages.missing.filters"/>",
                        type: "warning",
                        layout: "topCenter"
                    }).show();
                    return;
                }
                params.push("eventTypeIds;" + eventTypeIds);
                let isHomeTeam = $("#filter-ishometeam").val();
                if (notEmpty(isHomeTeam)) {
                    params.push("isHomeTeam;" + isHomeTeam);
                }
                let matchday = matchdaySlider.get();
                params.push("matchdayFrom;" + parseInt(matchday[0]));
                params.push("matchdayTo;" + parseInt(matchday[1]));
                let homeModule = $("#filter-homemodule").val();
                if (notEmpty(homeModule)) {
                    params.push("homeModule;" + homeModule.join("|"));
                }
                let awayModule = $("#filter-awaymodule").val();
                if (notEmpty(awayModule)) {
                    params.push("awayModule;" + awayModule.join("|"));
                }
                // filtro per area oppure filtro per il posizionale
                let area = $("#filter-area").val();
                if (notEmpty(area)) {
                    params.push("area;" + area);
                    // se ho almeno un filtro posizionale dico all'utente che non verranno considerati
                    let half = $("#filter-half").val();
                    let zone = $("#filter-zone").val();
                    let channel = $("#filter-channel").val();
                    if (notEmpty(half) || notEmpty(zone) || notEmpty(channel)) {
                        new Noty({
                            text: "<spring:message code="messages.positional.filter.warn"/>",
                            type: "warning",
                            layout: "topCenter"
                        }).show();
                    }
                } else {
                    let half = $("#filter-half").val();
                    if (notEmpty(half)) {
                        params.push("half;" + half);
                    }
                    let zone = $("#filter-zone").val();
                    if (notEmpty(zone)) {
                        params.push("zone;" + zone);
                    }
                    let channel = $("#filter-channel").val();
                    if (notEmpty(channel)) {
                        params.push("channel;" + channel);
                    }
                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);
                showBlockUI();

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/player/radar/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        $.unblockUI();
                        if (notEmpty(result)) {
                            var data = JSON.parse(result);
//                            console.log(data);

                            if (jQuery.isEmptyObject(data)) {
                                $("#table-container").addClass("d-none");
                                $("#page-title").addClass("d-none");
                                $("#chartdiv").addClass("col-md-12");
                                $("#chartdiv").removeClass("col-md-5");
                                if (params.length === 0) {
                                    showChartMessage(1);
                                } else {
                                    showChartMessage(2);
                                }
                            } else {
                                data.data = populateMissingValues(data, data.data, playerTeamSeasonIds);
                                if (typeof data.mMinPlaytime !== "undefined" && data.mMinPlaytime) {
                                    minPlaytimeFilter = data.mMinPlaytime;
                                }
                                $(".message-div").addClass("d-none");
                                $("#chartdiv").children("div:not(.message-div)").removeClass("d-none");
                                $("#page-title").removeClass("d-none");
                                $("#page-title").html(getPageTitle());

                                $("#table-container").removeClass("d-none");
                                $("#chartdiv").removeClass("col-md-12");
                                $("#chartdiv").addClass("col-md-5");

                                data.data.forEach(function (element) {
                                    $.each(element, function (name, value) {
//                                        if (name.startsWith("total")) {
//                                            let playerId = name.replace("total", "");
//                                            if (playerSeasonIds.indexOf(playerId) === -1) {
//                                                playerSeasonIds.push(playerId);
//                                            }
//                                        } else if (name.startsWith("teamLogo")) {
//                                            let playerSeason = name.replace("teamLogo", "");
//                                            playerTeamLogo.set(playerSeason, value);
//                                        }
                                        if (name.startsWith("teamLogo")) {
                                            let playerSeason = name.replace("teamLogo", "");
                                            playerTeamLogo.set(playerSeason, value);
                                        }
                                    });
                                });

                                if (typeof chart !== "undefined") {
                                    root.dispose();
                                    chart.dispose();
                                }
                                getPlayerRadarChart(data.data);

                                colorIndex = 0;
                                playerTeamSeasonIds.forEach(function (playerTeamSeason) {
                                    addPlayerRadarSeries(chart, playerTeamSeason, data, "${mUser.tvLanguage}");
                                });

                                createResumeTable(data);
                            }
                        }
                        stopChartLoading = false;
                    },
                    error: function () {
                        $.unblockUI();
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }

    function populateMissingValues(baseData, data, playerTeamSeasonIds) {
        let finalData = [...data];
        let index = 0;
        if (typeof baseData.containsAverage !== "undefined" && baseData.containsAverage) {
            playerTeamSeasonIds.unshift("0-0-" + $("#filter-competitionid").val() + "-" + $("#filter-seasonid").val());
        }

        data.forEach(function (element) {
            let eventId = parseInt(element["eventId"]);
            playerTeamSeasonIds.forEach(function (playerSeason) {
                if (typeof element["total" + playerSeason] === "undefined") {
                    if (oppositeEventType.has(eventId)) {
                        finalData[index]["total" + playerSeason] = 100;
                        finalData[index]["value" + playerSeason] = 0;
                    } else {
                        finalData[index]["total" + playerSeason] = 0;
                        finalData[index]["value" + playerSeason] = 0;
                    }
                }
            });
            index++;
        });

        return finalData;
    }

    function createResumeTable(data) {
        $("#table-container").empty();

        var card = $("<div>", {
            class: "card ms-4"
        });
        var cardBody = $("<div>", {
            class: "card-body overflow-auto"
        });

        var table = $("<table>", {
            class: "table table-hover"
        });

        let containsAverage = false;
        data.data.forEach(function (element) {
            var fields = Object.keys(element);
            fields.forEach(function (field) {
                if (field.startsWith("total0-0-")) {
                    containsAverage = true;
                }
            });
        });
        let competitionsData = new Map();
        data.competitions.forEach(function (element) {
            let [key, value] = Object.entries(element)[0];
            competitionsData.set(key, JSON.parse(value));
        });
        let teamsData = new Map();
        data.teams.forEach(function (element) {
            let [key, value] = Object.entries(element)[0];
            teamsData.set(key, JSON.parse(value));
        });
        let playersData = new Map();
        data.players.forEach(function (element) {
            let [key, value] = Object.entries(element)[0];
            playersData.set(key, JSON.parse(value));
        });

        var tmpIndex = 0;
        let playerIds = [];
        // head
        var thead = $("<thead>");
        var theadRow = $("<tr>");
        theadRow.append($("<th>"));
        if (containsAverage) {
            let pointColor = "<span class='d-inline-block rounded-pill p-1 me-1' style='background-color: " + colorScale(tmpIndex).hex() + "'></span>";
            tmpIndex += 0.2;
            theadRow.append($("<th>", {
                class: "text-center"
            }).html(pointColor + getDescriptionInLanguage(competitionsData.get($("#filter-competitionid").val()), "${mUser.tvLanguage}") + ", " + getDescriptionInLanguage(playersData.get("0"), "${mUser.tvLanguage}") + "<br/>" + $("#filter-seasonid option[value='" + $("#filter-seasonid").val() + "']").text()));
            playerIds.push("0-0-" + $("#filter-competitionid").val() + "-" + $("#filter-seasonid").val());
        }
        $("#filter-playerid").val().forEach(function (element) {
            let pointColor = "<span class='d-inline-block rounded-pill p-1 me-1' style='background-color: " + colorScale(tmpIndex).hex() + "'></span>";
            tmpIndex += 0.2;
            let teamLogo = "<img class='rounded-pill' width='24' height='24' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + playerTeamLogo.get(element + "-" + $("#filter-teamid").val() + "-" + $("#filter-competitionid").val() + "-" + $("#filter-seasonid").val()) + ".png?<%=System.currentTimeMillis()%>' onerror='this.src=\"https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>\"'/> <br/>";
            theadRow.append($("<th>", {
                class: "text-center"
            }).html(teamLogo + pointColor + getDescriptionInLanguage(competitionsData.get($("#filter-competitionid").val()), "${mUser.tvLanguage}") + " | " + getDescriptionInLanguage(teamsData.get($("#filter-teamid").val()), "${mUser.tvLanguage}") + ", " + $("#filter-playerid option[value='" + element + "']").text() + "<br/>" + $("#filter-seasonid option[value='" + $("#filter-seasonid").val() + "']").text()));
            playerIds.push(element + "-" + $("#filter-teamid").val() + "-" + $("#filter-competitionid").val() + "-" + $("#filter-seasonid").val());
        });
        $(".additional-filter").each(function (index, element) {
            let elementId = $(element).attr("id");
            if (elementId.includes("playerid")) {
                let splitted = elementId.split("-");
                let index = splitted[splitted.length - 1];
                if (notEmpty(index)) {
                    $("#filter-playerid-" + index).val().forEach(function (element) {
                        let tmpTeamLogo = "<img class='rounded-pill' width='24' height='24' src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + playerTeamLogo.get(element + "-" + $("#filter-teamid-" + index).val() + "-" + $("#filter-competitionid-" + index).val() + "-" + $("#filter-seasonid-" + index).val()) + ".png?<%=System.currentTimeMillis()%>' onerror='this.src=\"https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>\"'/> <br/>";
                        let tmpPointColor = "<span class='d-inline-block rounded-pill p-1 me-1' style='background-color: " + colorScale(tmpIndex).hex() + "'></span>";
                        tmpIndex += 0.2;
                        theadRow.append($("<th>", {
                            class: "text-center"
                        }).html(tmpTeamLogo + tmpPointColor + getDescriptionInLanguage(competitionsData.get($("#filter-competitionid-" + index).val()), "${mUser.tvLanguage}") + " | " + getDescriptionInLanguage(teamsData.get($("#filter-teamid-" + index).val()), "${mUser.tvLanguage}") + ", " + $("#filter-playerid-" + index + " option[value='" + element + "']").text() + "<br/>" + $("#filter-seasonid-" + index + " option[value='" + $("#filter-seasonid-" + index).val() + "']").text()));
                        playerIds.push(element + "-" + $("#filter-teamid-" + index).val() + "-" + $("#filter-competitionid-" + index).val() + "-" + $("#filter-seasonid-" + index).val());
                    });
                }
            }
        });
        thead.append(theadRow);

        var tbody = $("<tbody>");
        data.data.forEach(function (element) {
            let opposite = oppositeEventType.has(element["eventId"]);
            var tbodyRow = $("<tr>");
            let zoneAbbName = element["zoneAbbName"] || "";
            let column = $("<td>");
            column.html(element["event"].replace("\n", " ").replace(zoneAbbName, ""));
            if (zoneAbbName) {
                column.append($("<span class='text-decoration-underline' title='" + element["zoneName"] + "'>").html(zoneAbbName));
            }
            tbodyRow.append(column);
            playerIds.forEach(function (playerId) {
                var maxValue = 0;
                $.each(element, function (name, value) {
                    if (name.startsWith("value")) {
                        if (!opposite) {
                            if (value > maxValue) {
                                maxValue = value;
                            }
                        } else {
                            if (maxValue === 0 || value < maxValue) {
                                maxValue = value;
                            }
                        }
                    }
                });

                var key = "value" + playerId;
                let value = element[key];
                if (value) {
                    tbodyRow.append($("<td>", {
                        class: "text-center" + (value === maxValue ? " bg-success bg-opacity-50 rounded-pill text-white" : "")
                    }).html(value));
                } else {
                    tbodyRow.append($("<td>", {
                        class: "text-center" + (value === maxValue ? " bg-success bg-opacity-50 rounded-pill text-white" : "")
                    }).html("-"));
                }
            });
            tbody.append(tbodyRow);
        });

        table.append(thead);
        table.append(tbody);
        cardBody.append(table);
        card.append(cardBody);
        $("#table-container").append(card);

        checkParametersAmount($("#table-container").find("table.table.table-hover tbody tr").length);
    }

    var additionalFilterAmount = 0;
    function addAdditionalFilter() {
        additionalFilterAmount++;

        $('#filter-seasonid').select2('destroy');
        $('#filter-competitionid').select2('destroy');
        $('#filter-teamid').select2('destroy');
        $('#filter-playerid').multiselect('destroy');

        var clonedSeparator = $("#additional-team-separator").clone();
        clonedSeparator.removeClass("d-none");
        clonedSeparator.removeAttr("id");
        clonedSeparator.find("i").attr("index", additionalFilterAmount);
        $("#add-team-button").before(clonedSeparator);

        var clonedSeasonId = $("#container-seasonid").clone();
        clonedSeasonId.addClass("mt-2");
        clonedSeasonId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedSeasonId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedSeasonId.find("span").remove();
        clonedSeasonId.find("select").attr("onchange", clonedSeasonId.find("select").attr("onchange").replace("updateFilters('seasonId');", "updateFilters('seasonId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedSeasonId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedSeasonId);
        $("#filter-seasonid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedCompetitionId = $("#container-competitionid").clone();
        clonedCompetitionId.addClass("mt-2");
        clonedCompetitionId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedCompetitionId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedCompetitionId.find("span").remove();
        clonedCompetitionId.find("select").attr("onchange", clonedCompetitionId.find("select").attr("onchange").replace("updateFilters('competitionId');", "updateFilters('competitionId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedCompetitionId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedCompetitionId);
        $("#filter-competitionid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedTeamId = $("#container-teamid").clone();
        clonedTeamId.addClass("mt-2");
        clonedTeamId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedTeamId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedTeamId.find("span").remove();
        clonedTeamId.find("select").attr("onchange", clonedTeamId.find("select").attr("onchange").replace("updateFilters('teamId');", "updateFilters('teamId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedTeamId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedTeamId);
        $("#filter-teamid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedPlayerId = $("#container-playerid").clone();
        clonedPlayerId.addClass("mt-2");
        clonedPlayerId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedPlayerId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedPlayerId.find("select").attr("onchange", clonedPlayerId.find("select").attr("onchange").replace("updateChart('playerId');", "updateChart('playerId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedPlayerId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedPlayerId);
        $("#filter-playerid-" + additionalFilterAmount).multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        $('#filter-seasonid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-competitionid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-teamid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-playerid').multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        updateFiltersColor();
        bindFiltersSave();

        if (notEmpty($("#filter-seasonid-" + additionalFilterAmount).val())) {
            $("#filter-seasonid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-competitionid-" + additionalFilterAmount).val())) {
            $("#filter-competitionid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-teamid-" + additionalFilterAmount).val())) {
            $("#filter-teamid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-playerid-" + additionalFilterAmount).val())) {
            $("#filter-playerid-" + additionalFilterAmount).trigger("custom-change");
        }
    }

    function removeAdditionalFilter() {
        if (typeof event.target !== "undefined") {
            let index = $(event.target).attr("index");
            if (notEmpty(index)) {
                $("#container-seasonid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-seasonid-" + index).remove();
                $("#container-competitionid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-competitionid-" + index).remove();
                $("#container-teamid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-teamid-" + index).remove();
                $("#container-playerid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-playerid-" + index).remove();
                $(event.target).parent().remove();
                updateChart();
            }
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <div class="sidebar sidebar-secondary sidebar-expand-lg">
            <!-- Expand button -->
            <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                <i class="ph-caret-right"></i>
            </button>
            <!-- /expand button -->

            <!-- Sidebar content -->
            <div class="sidebar-content" id="filters-containter">

                <!-- Header -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                    <div class="me-1">
                        <i class="ph-arrow-counter-clockwise cursor-pointer" title="<spring:message code="filters.reset"/>" onclick="resetFilters();"></i>
                        <i class="ph-funnel cursor-pointer" title="<spring:message code="filters.show.hide"/>" onclick="managePersonalFilters();"></i>
                    </div>
                    <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex">
                            <i class="ph-arrows-left-right"></i>
                        </button>

                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                            <i class="ph-x"></i>
                        </button>
                    </div>
                </div>
                <!-- /header -->

                <!-- Header Filter Message -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center justify-content-center mb-0 mt-0 py-1 d-none" id="specific-filter-message">
                    <div>
                        <i class="ph-warning text-warning"></i><span class="ms-1 text-warning"><spring:message code="filters.specific.warning"/></span>
                    </div>
                </div>
                <!-- /header filter message -->

                <!-- Sidebar search -->
                <div class="sidebar-section">
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-4 d-none" id="personal-filters-container">
                            <label class="form-label mb-0"><spring:message code="personal.filters"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="personal.filters.placeholder"/>" class="form-control form-control-sm select" id="personal-filter" onchange="loadPersonalFilter();" data-minimum-results-for-search="Infinity" data-width="1%">
                                        <option value=""></option>
                                        <c:forEach var="filter" items="${mFilters}">
                                            <option value="${filter.id}">${filter.name}</option>
                                        </c:forEach>
                                    </select>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" id="personal-filter-update" onclick="updatePersonalFilter();" title="<spring:message code="personal.filter.save.placeholder"/>" disabled>
                                        <i class="ph-pencil"></i>
                                    </button>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" onclick="addPersonalFilter();" title="<spring:message code="personal.filter.add.placeholder"/>">
                                        <i class="ph-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-seasonid">
                            <label class="form-label mb-0"><spring:message code="filters.season"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.season.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-seasonid" onchange="updateFilters('seasonId');" data-minimum-results-for-search="Infinity">
                                    <c:forEach var="season" items="${mSeasons}">
                                        <option value="${season.id}">${season.name}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </div>
                        <div class="mb-2" id="container-competitionid">
                            <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-competitionid" onchange="updateFilters('competitionId');">
                                </select>
                            </div>
                        </div>
                        <div class="mb-2" id="container-teamid">
                            <label class="form-label mb-0"><spring:message code="filters.team"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.team.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-teamid" onchange="updateFilters('teamId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-playerid">
                            <label class="form-label mb-0"><spring:message code="filters.player"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="row">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.player.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required is-required-extra" id="filter-playerid" onchange="updateChart('playerId');" data-width="1%" data-include-select-all-option="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.displayed"/></label>
                            <div class="form-control-feedback form-control-feedback-end">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.displayed.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-totaltype" onchange="updateChart('totalType');" data-minimum-results-for-search="Infinity">
                                    <option value="totals" selected><spring:message code="filters.displayed.totals"/></option>
                                    <option value="p90"><spring:message code="filters.displayed.p90"/></option>
                                    <option value="touches"><spring:message code="filters.displayed.touches"/></option>
                                    <option value="average"><spring:message code="filters.displayed.average"/></option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-3 d-none" id="additional-team-separator">
                            <div class="hr"></div>
                            <i class="ph-trash cursor-pointer mx-1" onclick="removeAdditionalFilter();"></i>
                            <div class="hr"></div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mt-3" id="add-team-button">
                            <div class="bg-info bg-opacity-20 rounded-pill text-info p-2 cursor-pointer" onclick="addAdditionalFilter();">
                                <i class="ph-plus"></i>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                    </div>

                    <div class="sidebar-section-body mb-0 py-1">
                        <button type="button" class="btn btn-info w-100" data-bs-toggle="modal" data-bs-target="#modal-event-builder"><spring:message code="filters.metrics.parameters"/></button>
                    </div>
                    <form class="sidebar-section-body mb-0 px-0 py-0 multiple-events d-none" action="#" id="event-filters-container">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="event-container-nav">
                            <li class="nav-item">
                                <a href="#event-filters-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-advanced" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-tactical" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-3">
                            <div class="tab-pane fade show active" id="event-filters-event">
                                <div class="mb-2" id="container-eventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required skip-title is-required-extra eventtypeid" id="filter-eventtypeid" onchange="updateFilters('eventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-advanced">
                                <div class="mb-2" id="container-advancedeventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-title eventtypeid" id="filter-advancedeventtypeid" onchange="updateFilters('advancedeventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-tactical">
                                <div class="mb-2" id="container-tacticaleventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter skip-title eventtypeid" id="filter-tacticaleventtypeid" onchange="updateFilters('tacticaleventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.match.details"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.away"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.away.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-ishometeam" onchange="updateFilters('isHomeTeam');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.matchday"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.home.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.home.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-homemodule" onchange="updateFilters('homeModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.away.module"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.away.module.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-awaymodule" onchange="updateFilters('awayModule');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section d-none">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.positional"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.half"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.half.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-half" onchange="updateChart('half');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.zone"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.zone.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-zone" onchange="updateChart('zone');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-label mb-0"><spring:message code="filters.channel"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.channel.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-channel" onchange="updateChart('channel');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.area"/> <i class="ph-info" data-bs-popup="tooltip" data-bs-placement="top" data-bs-html="true" title="<spring:message code="filters.area.tooltip"/>"></i></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <button onclick="resetFilter();" class="input-group-text py-1 px-2" data-bs-popup="tooltip" title="<spring:message code="filters.reset"/>" data-bs-placement="top">
                                        <i class="ph-arrow-counter-clockwise"></i>
                                    </button>
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.area.placeholder"/>" class="form-control form-control-sm select is-filter" id="filter-area" onchange="updateChart('area');" data-width="1%" data-minimum-results-for-search="Infinity">
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->
            </div>
            <!-- /sidebar content -->
        </div>

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content" id="container">
                    <div class="row">
                        <h6 class="mb-1 text-center" id="page-title"></h6>
                    </div>
                    <div class="row">
                        <div id="chartdiv" class="col-md-12">
                            <%@ include file="../global/messages.jsp" %>
                        </div>
                        <div id="table-container" class="col-md-7 d-none"></div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
