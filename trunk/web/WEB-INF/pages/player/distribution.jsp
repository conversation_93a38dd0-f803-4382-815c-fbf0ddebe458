<%@ include file="../global/main.jsp" %>

<!-- D3.js Library for canvas/svg -->
<script src="https://d3js.org/d3.v5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>

<script type="text/javascript">
    var stopValueSave = true;
    var eventAmountSlider, playtimeSlider;
    var textContainer, firstInfoContainer, secondInfoContainer, thirdInfoContainer;
    var isFirstLoad = true;
    $(document).ready(function () {
        $("#playerDistributionButton").addClass("active");

        $('.sidebar-section-body').submit(function (e) {
            e.preventDefault();
            // non faccio niente perch� c'� evento bindato sul campo input
        });

        showChartMessage(1);

        // Define element
        const sliderEventAmount = document.getElementById('noui-slider-drag-events');
        // Create slider
        eventAmountSlider = noUiSlider.create(sliderEventAmount, {
            start: [1, 999],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 999
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderEventAmountVals = [
            document.getElementById('noui-slider-drag-events-lower-val'),
            document.getElementById('noui-slider-drag-events-upper-val')
        ];
        // Show the values
        sliderEventAmount.noUiSlider.on('update', function (values, handle) {
            sliderEventAmountVals[handle].innerHTML = values[handle];
        });

        eventAmountSlider.on("change", function () {
            let index = filtersIndex.indexOf("eventamount");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-eventamount", eventAmountSlider.get());
            }
            updateChart("eventamount");
        });

        // Define element
        const sliderPlaytime = document.getElementById('noui-slider-drag-playtime');
        // Create slider
        playtimeSlider = noUiSlider.create(sliderPlaytime, {
            start: [1, 9999],
            step: 1,
            behaviour: 'drag',
            connect: true,
            range: {
                'min': 1,
                'max': 9999
            },
            direction: 'ltr',
            format: {
                to: (v) => parseFloat(v).toFixed(0),
                from: (v) => parseFloat(v).toFixed(0)
            }
        });
        // Define elements for values
        const sliderPlaytimeVals = [
            document.getElementById('noui-slider-drag-playtime-lower-val'),
            document.getElementById('noui-slider-drag-playtime-upper-val')
        ];
        // Show the values
        sliderPlaytime.noUiSlider.on('update', function (values, handle) {
            sliderPlaytimeVals[handle].innerHTML = values[handle];
        });

        playtimeSlider.on("change", function () {
            let index = filtersIndex.indexOf("playtime");
            if (index >= 0) {
                let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                if (index > 25) {
                    index -= 25;
                    letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                }
                localStorage.setItem("dataanalytics/610/" + letter + "/filter-playtime", playtimeSlider.get());
            }
            updateChart("playtime");
        });

        queue.enqueue(() => {
            updateFilters('seasonId');
        });
        checkInitialFilters();

        queue.enqueue(() => {
            // se non c'� nessun team selezionato seleziono il primo
            if ($("#filter-teamid").find("option:checked").length === 0) {
                stopChartLoading = true;
                $("#filter-teamid").prop("selectedIndex", 1).change();
                stopChartLoading = false;
            }
        });

        queue.enqueue(() => {
            if (additionalFilterAmount === 0) {
                addAdditionalFilter();
                if ($("#filter-competitionid-1 option[value='" + $("#filter-competitionid").val() + "']").length > 0) {
                    $("#filter-competitionid-1").val($("#filter-competitionid").val()).change();
                }

                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            }
        });

        // non serve pi� dato che playerid ha la classe "load-all-on-change"
//        queue.enqueue(() => {
//            // se non c'� nessun giocatore selezionato li seleziono tutti
////            if ($("#filter-playerid").find("option:checked").length === 0) {
////                stopChartLoading = true;
////                $("#filter-playerid").find("option").each(function () {
////                    $("#filter-playerid").multiselect('select', $(this).val()).change();
////                });
////                stopChartLoading = false;
////            }
//            stopChartLoading = true;
//            $("#filter-playerid").find("option").each(function () {
//                $("#filter-playerid").multiselect('select', $(this).val());
//            });
//            $("#filter-playerid").trigger("change");
//            stopChartLoading = false;
//        });
    });

    function isElementVisible(elem) {
        var docViewTop = $("#filters-containter").scrollTop();
        var docViewBottom = docViewTop + $("#filters-containter").height();

        $(elem).removeClass("d-none");
        var elemTop = $(elem).offset().top + 250;
        var elemBottom = elemTop + $(elem).height() - 50;
        $(elem).addClass("d-none");

//        console.log(docViewTop, elemTop, docViewBottom, elemBottom);
        return ((elemBottom <= docViewBottom) && (elemTop >= docViewTop));
    }

    function updateChart(filterChanged) {
        if (stopChartLoading === false) {
            updateFiltersColor();
            var params = [];

            let seasonId = $("#filter-seasonid").val();
            let competitionId = $("#filter-competitionid").val();
            let teamId = $("#filter-teamid").val();
            let playerId = $("#filter-playerid").val();
            let eventTypeId = getPageEventType();
            if (notEmpty(seasonId) && notEmpty(competitionId) && notEmpty(teamId) && notEmpty(playerId) && notEmpty(eventTypeId)) {
                let totalType = $("#filter-totaltype").val();
                if (notEmpty(totalType)) {
                    params.push("totalType;" + totalType);
                }
                params.push("seasonId;" + seasonId);
                params.push("competitionId;" + competitionId);
                var canContinue = true;
                var extraElements = $(".additional-filter");
                if (extraElements.length > 0) {
                    for (var i = 0; i < extraElements.length; i++) {
                        let splitted = $(extraElements[i]).attr("id").split("-");
                        let index = splitted[splitted.length - 1];
                        if (notEmpty($("#filter-competitionid-" + index).val())) {
                            canContinue = true;
                            if ($(extraElements[i]).attr("id").includes("competitionid")) {
                                let tmpCompetitionId = $(extraElements[i]).val();
                                if (notEmpty(tmpCompetitionId)) {
                                    params.push("competitionId" + index + ";" + tmpCompetitionId);
                                } else {
                                    canContinue = false;
                                }
                            } else if ($(extraElements[i]).attr("id").includes("teamid")) {
                                let tmpTeamId = $(extraElements[i]).val();
                                if (notEmpty(tmpTeamId)) {
                                    params.push("teamId" + index + ";" + tmpTeamId);
                                }
                            } else {
                                let tmpPlayerId = $(extraElements[i]).val();
                                if (notEmpty(tmpPlayerId)) {
                                    params.push("playerId" + index + ";" + tmpPlayerId.join("|"));
                                }
                            }
                        } else {
                            canContinue = false;
                        }
                    }
                }
                if (!canContinue) {
                    showChartMessage(3);
                    new Noty({
                        text: "<spring:message code="messages.missing.filters"/>",
                        type: "warning",
                        layout: "topCenter"
                    }).show();
                    return;
                }
                if (typeof eventAmountSlider !== "undefined") {
                    let eventamount = eventAmountSlider.get();
                    if (parseInt(eventamount[0]) !== eventAmountSlider.options.range.min || parseInt(eventamount[1]) !== eventAmountSlider.options.range.max) {
                        params.push("minEventAmount;" + parseInt(eventamount[0]));
                        params.push("maxEventAmount;" + parseInt(eventamount[1]));
                    }
                }
                if (typeof playtimeSlider !== "undefined") {
                    let playtime = playtimeSlider.get();
                    if (parseInt(playtime[0]) !== playtimeSlider.options.range.min || parseInt(playtime[1]) !== playtimeSlider.options.range.max) {
                        params.push("minPlaytime;" + parseInt(playtime[0]));
                        params.push("maxPlaytime;" + parseInt(playtime[1]));
                    }
                }
                params.push("teamId;" + teamId);
                params.push("playerId;" + playerId.join("|"));
                params.push("eventTypeId;" + eventTypeId);
                let tagTypeId = getPageTagType();
                if (notEmpty(tagTypeId)) {
                    tagTypeId.sort((a, b) => Number(a) - Number(b));
                    params.push("tagTypeId;" + tagTypeId.join("|"));
                }

                stopChartLoading = true;
                var data = encodeURI("parameters=" + params.join("-_-"));
                console.log("Reloading chart...", data);
                showChartMessage(1);

                $.ajax({
                    type: "GET",
                    url: "/sicsdataanalytics/player/distribution/getChartData.htm",
                    cache: false,
                    data: data,
                    success: function (result) {
                        if (notEmpty(result)) {
                            var data = JSON.parse(result);
//                            console.log(data);

                            if (jQuery.isEmptyObject(data)) {
                                $("#page-title").addClass("d-none");
                                if (params.length === 0) {
                                    showChartMessage(1);
                                } else {
                                    showChartMessage(2);
                                }
                            } else {
                                $(".message-div").addClass("d-none");
                                $("#chartdiv").children("div:not(.message-div)").removeClass("d-none");
                                $("#page-title").removeClass("d-none");
                                $("#page-title").html(getPageTitle());

                                if (data.data[0].maxEventAmount !== null) {
                                    updateMaxEventAmount(parseInt(data.data[0].maxEventAmount));
                                    if (data.data[0].maxPlaytime !== null) {
                                        updateMaxPlaytime(parseInt(data.data[0].maxPlaytime));
                                        // se non � impostato metto io al 10%
                                        if (parseInt(playtimeSlider.get()[0]) === 1) {
                                            if (data.data[0].minPlaytime !== null) {
                                                let tmpPlaytime = parseInt(data.data[0].minPlaytime);
                                                playtimeSlider.set([tmpPlaytime, playtimeSlider.get()[1]]);
                                                new Noty({
                                                    text: "<spring:message code="player.distribution.playtime.adjusted"/>".replace("X", "<b>" + tmpPlaytime + "</b>"),
                                                    timeout: 3000,
                                                    type: "info",
                                                    layout: "topRight"
                                                }).show();
                                            }
                                        }
                                    }
                                    data.data.shift();
                                }

                                let minValue = 0, maxValue = 0;
                                data.data.forEach(function (element) {
                                    $.each(element, function (name, value) {
                                        if (name === "value") {
                                            if (value > maxValue) {
                                                maxValue = value;
                                            }
                                            if (minValue === 0 || value < minValue) {
                                                minValue = value;
                                            }
                                        }
                                    });
                                });

                                if (typeof chart !== "undefined") {
                                    root.dispose();
                                    chart.dispose();
                                }

                                getPlayerDistributionChart(data.data, minValue, maxValue, data.isOpposite, data.firstBestPlayer, data.secondBestPlayer, data.thirdBestPlayer);
                            }
                        }
                        stopChartLoading = false;
                    },
                    error: function () {
                        stopChartLoading = false;
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }
                });
            } else {
                showChartMessage(3);
                new Noty({
                    text: "<spring:message code="messages.missing.filters"/>",
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        }
    }

    var additionalFilterAmount = 0;
    function addAdditionalFilter() {
        additionalFilterAmount++;

        $('#filter-competitionid').select2('destroy');
        $('#filter-teamid').select2('destroy');
        $('#filter-playerid').multiselect('destroy');

        var clonedSeparator = $("#additional-team-separator").clone();
        clonedSeparator.removeClass("d-none");
        clonedSeparator.removeAttr("id");
        clonedSeparator.find("i").attr("index", additionalFilterAmount);
        $("#add-team-button").before(clonedSeparator);

        var clonedCompetitionId = $("#container-competitionid").clone();
        clonedCompetitionId.addClass("mt-2");
        clonedCompetitionId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedCompetitionId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedCompetitionId.find("span").remove();
        clonedCompetitionId.find("select").attr("onchange", clonedCompetitionId.find("select").attr("onchange").replace("updateFilters('competitionId');", "updateFilters('competitionId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedCompetitionId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedCompetitionId);
        $("#filter-competitionid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedTeamId = $("#container-teamid").clone();
        // clonedTeamId.addClass("mt-2");
        clonedTeamId.find("select").removeClass("is-required");
        clonedTeamId.find("select").removeClass("is-required-extra");
        clonedTeamId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedTeamId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedTeamId.find("select").attr("onchange", clonedTeamId.find("select").attr("onchange").replace("updateFilters('teamId');", "updateFilters('teamId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedTeamId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedTeamId);
        $("#filter-teamid-" + additionalFilterAmount).select2({
            dropdownAutoWidth: true
        });

        var clonedPlayerId = $("#container-playerid").clone();
        clonedPlayerId.addClass("mt-2");
        clonedPlayerId.find("select").removeClass("is-required");
        clonedPlayerId.find("select").removeClass("is-required-extra");
        clonedPlayerId.attr('id', function (index, id) {
            return id + '-' + additionalFilterAmount;
        });
        clonedPlayerId.find('[id]').each(function () {
            var currentId = $(this).attr('id');
            $(this).attr('id', currentId + '-' + additionalFilterAmount);
        });
        clonedPlayerId.find("select").attr("onchange", clonedPlayerId.find("select").attr("onchange").replace("updateChart('playerId');", "updateChart('playerId" + additionalFilterAmount + "', " + additionalFilterAmount + ");"));
        clonedPlayerId.find("select").addClass("additional-filter");
        $("#add-team-button").before(clonedPlayerId);
        $("#filter-playerid-" + additionalFilterAmount).multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        $('#filter-competitionid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-teamid').select2({
            dropdownAutoWidth: true
        });
        $('#filter-playerid').multiselect({
            selectAllText: '<spring:message code="filters.select.all"/>',
            enableFiltering: true,
            includeFilterClearBtn: false,
            enableCaseInsensitiveFiltering: true,
            onDropdownShown: function () {
                if (typeof this.$filter !== "undefined") {
                    $(".multiselect-filter").find("div.form-control-feedback-icon").remove();
                    this.$filter.find('.multiselect-search').focus();
                    this.$filter.find('.multiselect-search').prop('placeholder', '<spring:message code="filters.search"/>');
                }
            }
        });

        // fix css per search
        $('.multiselect-search').css('padding-left', 'calc(0.875rem * 2 + 1.25rem)');
        updateFiltersColor();
        reloadTooltips();
        bindFiltersSave();

        if (notEmpty($("#filter-competitionid-" + additionalFilterAmount).val())) {
            $("#filter-competitionid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-teamid-" + additionalFilterAmount).val())) {
            $("#filter-teamid-" + additionalFilterAmount).trigger("custom-change");
        }
        if (notEmpty($("#filter-playerid-" + additionalFilterAmount).val())) {
            $("#filter-playerid-" + additionalFilterAmount).trigger("custom-change");
        }
    }

    function removeAdditionalFilter() {
        if (typeof event.target !== "undefined") {
            let index = $(event.target).attr("index");
            if (notEmpty(index)) {
                $("#container-competitionid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-competitionid-" + index).remove();
                $("#container-teamid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-teamid-" + index).remove();
                $("#container-playerid-" + index).find("select").val(null).trigger("custom-change");
                $("#container-playerid-" + index).remove();
                $(event.target).parent().remove();
                updateChart();
            }
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <div class="sidebar sidebar-secondary sidebar-expand-lg">
            <!-- Expand button -->
            <button type="button" class="btn btn-sidebar-expand sidebar-control sidebar-secondary-toggle h-100">
                <i class="ph-caret-right"></i>
            </button>
            <!-- /expand button -->

            <!-- Sidebar content -->
            <div class="sidebar-content" id="filters-containter">

                <!-- Header -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center pb-2 mb-0">
                    <div class="me-1">
                        <i class="ph-arrow-counter-clockwise cursor-pointer" title="<spring:message code="filters.reset"/>" onclick="resetFilters();"></i>
                        <i class="ph-funnel cursor-pointer" title="<spring:message code="filters.show.hide"/>" onclick="managePersonalFilters();"></i>
                    </div>
                    <h5 class="mb-0"><spring:message code="filters.title"/></h5>
                    <div class="ms-auto">
                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-control sidebar-secondary-toggle d-none d-lg-inline-flex">
                            <i class="ph-arrows-left-right"></i>
                        </button>

                        <button type="button" class="btn btn-light border-transparent btn-icon rounded-pill btn-sm sidebar-mobile-secondary-toggle d-lg-none">
                            <i class="ph-x"></i>
                        </button>
                    </div>
                </div>
                <!-- /header -->

                <!-- Header Filter Message -->
                <div class="sidebar-section sidebar-section-body d-flex align-items-center justify-content-center mb-0 mt-0 py-1 d-none" id="specific-filter-message">
                    <div>
                        <i class="ph-warning text-warning"></i><span class="ms-1 text-warning"><spring:message code="filters.specific.warning"/></span>
                    </div>
                </div>
                <!-- /header filter message -->

                <!-- Sidebar search -->
                <div class="sidebar-section">
                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-4 d-none" id="personal-filters-container">
                            <label class="form-label mb-0"><spring:message code="personal.filters"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="personal.filters.placeholder"/>" class="form-control form-control-sm select" id="personal-filter" onchange="loadPersonalFilter();" data-minimum-results-for-search="Infinity" data-width="1%">
                                        <option value=""></option>
                                        <c:forEach var="filter" items="${mFilters}">
                                            <option value="${filter.id}">${filter.name}</option>
                                        </c:forEach>
                                    </select>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" id="personal-filter-update" onclick="updatePersonalFilter();" title="<spring:message code="personal.filter.save.placeholder"/>" disabled>
                                        <i class="ph-pencil"></i>
                                    </button>
                                    <button type="button" class="input-group-text py-1 px-1 btn btn-light" onclick="addPersonalFilter();" title="<spring:message code="personal.filter.add.placeholder"/>">
                                        <i class="ph-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-seasonid">
                            <label class="form-label mb-0"><spring:message code="filters.season"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="row">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.season.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-seasonid" onchange="updateFilters('seasonid');" data-minimum-results-for-search="Infinity">
                                        <c:forEach var="season" items="${mSeasons}">
                                            <option value="${season.id}">${season.name}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-competitionid">
                            <label class="form-label mb-0"><spring:message code="filters.competition"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.competition.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-competitionid" onchange="updateFilters('competitionId');">
                                </select>
                            </div>
                        </div>
                        <div class="mb-2" id="container-teamid">
                            <label class="form-label mb-0"><spring:message code="filters.team"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="input-group">
                                    <select data-container-css-class="select-sm" data-placeholder="Teams..." class="form-control form-control-sm select is-filter is-required is-required-extra" id="filter-teamid" onchange="updateFilters('teamId');" data-width="1%">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="mb-2" id="container-playerid">
                            <label class="form-label mb-0"><spring:message code="filters.selected.players"/></label>
                            <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                <div class="row">
                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.selected.players.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter is-required is-required-extra" id="filter-playerid" onchange="updateChart('playerId');" data-width="1%" data-include-select-all-option="true">
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="form-label mb-0"><spring:message code="filters.displayed"/></label>
                            <div class="form-control-feedback form-control-feedback-end">
                                <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.displayed.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-totaltype" onchange="updateChart('totalType');" data-minimum-results-for-search="Infinity">
                                    <option value="totals" selected><spring:message code="filters.displayed.totals"/></option>
                                    <option value="p90"><spring:message code="filters.displayed.p90"/></option>
                                    <option value="touches"><spring:message code="filters.displayed.touches"/></option>
                                    <option value="average"><spring:message code="filters.displayed.average"/></option>
                                </select>
                            </div>
                        </div>
                        <div class="d-flex align-items-center mt-3 d-none" id="additional-team-separator">
                            <div class="hr"></div>
                            <i class="ph-trash cursor-pointer mx-1" onclick="removeAdditionalFilter();"></i>
                            <div class="hr"></div>
                        </div>
                        <div class="d-flex justify-content-center align-items-center mt-3" id="add-team-button">
                            <div class="bg-info bg-opacity-20 rounded-pill text-info p-2 cursor-pointer" onclick="addAdditionalFilter();">
                                <i class="ph-plus"></i>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="filters.event"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 px-0 py-0" action="#" id="event-filters-container">
                        <ul class="nav nav-tabs nav-tabs-underline nav-justified border-bottom-0 mb-2" id="event-container-nav">
                            <li class="nav-item">
                                <a href="#event-filters-event" class="nav-link active" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.events"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-advanced" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.advanced.metrics"/>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a href="#event-filters-tactical" class="nav-link" data-bs-toggle="tab">
                                    <spring:message code="messages.tab.tactical.metrics"/>
                                </a>
                            </li>
                        </ul>

                        <div class="tab-content px-3">
                            <div class="tab-pane fade show active" id="event-filters-event">
                                <div class="mb-2" id="container-eventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter is-required is-required-extra eventtypeid" id="filter-eventtypeid" onchange="updateFilters('eventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tagtypeid" onchange="updateChart('tagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-advanced">
                                <div class="mb-2" id="container-advancedeventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-advancedeventtypeid" onchange="updateFilters('advancedeventTypeId');">
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane fade" id="event-filters-tactical">
                                <div class="mb-2" id="container-tacticaleventtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter eventtypeid" id="filter-tacticaleventtypeid" onchange="updateFilters('tacticaleventTypeId');">
                                        </select>
                                    </div>
                                </div>
                                <div class="mb-2" id="container-tacticaltagtypeid">
                                    <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                    <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                        <div class="row">
                                            <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter tagtypeid" id="filter-tacticaltagtypeid" onchange="updateChart('tacticaltagTypeId');" data-width="1%">
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="px-3 mb-2 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.event.amount"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-events"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-events-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-events-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                        <div class="px-3 animation" data-animation="zoomIn">
                            <label class="form-label mb-0"><spring:message code="filters.playtime"/></label>
                            <div class="px-2">
                                <div class="noui-height-helper mt-1" id="noui-slider-drag-playtime"></div>

                                <div class="clearfix">
                                    <span class="mt-3 float-start"><span class="fw-semibold" id="noui-slider-drag-playtime-lower-val"></span></span>
                                    <span class="mt-3 float-end"><span class="fw-semibold" id="noui-slider-drag-playtime-upper-val"></span></span>
                                </div>
                            </div>
                        </div>
                    </form>
                    <!--                    <form class="sidebar-section-body mb-0 py-1" action="#">
                                            <div class="mb-2">
                                                <label class="form-label mb-0"><spring:message code="filters.event.type"/></label>
                                                <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                    <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.type.placeholder"/>" class="form-control form-control-sm select is-filter is-required" id="filter-eventtypeid" onchange="updateFilters('eventTypeId');">
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="mb-2" id="container-tagtypeid">
                                                <label class="form-label mb-0"><spring:message code="filters.event.tags"/></label>
                                                <div class="form-control-feedback form-control-feedback-end animation" data-animation="zoomIn">
                                                    <div class="row">
                                                        <select data-container-css-class="select-sm" data-placeholder="<spring:message code="filters.event.tags.placeholder"/>" multiple="multiple" class="form-control form-control-sm multiselect is-filter" id="filter-tagtypeid" onchange="updateChart('tagTypeId');" data-width="1%">
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                    
                                        </form>-->
                </div>
                <!-- /sidebar advanced search -->

                <!-- Sidebar advanced search -->
                <div class="sidebar-section">
                    <div class="sidebar-section-header border-bottom">
                        <span class="fw-semibold"><spring:message code="player.distribution.options"/></span>
                    </div>

                    <form class="sidebar-section-body mb-0 py-1" action="#">
                        <div class="mb-2">
                            <div class="form-check form-switch mb-2">
                                <input type="checkbox" class="form-control is-filter form-check-input" id="filter-hidebest" onchange="updateChart('hidebest');">
                                <label class="form-check-label" for="filter-hidebest"><spring:message code="player.distribution.options.hide.best"/></label>
                            </div>
                        </div>
                    </form>
                </div>
                <!-- /sidebar advanced search -->
            </div>
            <!-- /sidebar content -->
        </div>

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content" id="container">
                    <div class="row">
                        <h6 class="mb-1 text-center" id="page-title"></h6>
                    </div>
                    <div class="row">
                        <div class="amchart" id="chartdiv">
                            <%@ include file="../global/messages.jsp" %>
                        </div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
