<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<div class="offcanvas-header border-bottom py-0">
    <h5 class="offcanvas-title py-3"><spring:message code="offcanvas.agency.details"/></h5>
    <button type="button" class="btn btn-light btn-sm btn-icon border-transparent rounded-pill" data-bs-dismiss="offcanvas">
        <i class="ph-x"></i>
    </button>
</div>

<div class="offcanvas-body">
    <c:choose>
        <c:when test="${mAgency != null}">
            <div class="card-body text-center card-img-top">
                <div class="card-img-actions d-inline-block">
                    <img height="120" width="120" class="img-fluid rounded-circle image-shadow-sm" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/agentlogo/${mAgency.photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='/sicsdataanalytics/images/unknown_agency.svg?<%=System.currentTimeMillis()%>'">
                </div>

                <h6 class="mb-0">${mAgency.name}</h6>
                <c:if test="${mAgency.email != null}">
                    <span class="opacity-75">${mAgency.email}</span>
                </c:if>
            </div>
            <hr/>
            <div class="card-body border-top-0">
                <c:if test="${mAgency.countryId != null && mAgency.countryId > 0}">
                    <div class="d-sm-flex flex-sm-wrap mb-3">
                        <div class="fw-semibold"><spring:message code="offcanvas.agency.details.country"/>:</div>
                        <div class="ms-sm-auto mt-1 mt-sm-0">
                            <img height="20" width="20" class="image-shadow-sm me-1" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mAgency.countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'">
                            ${mCountries.get(mAgency.countryId).getName(mUser.tvLanguage)}
                        </div>
                    </div>
                </c:if>
                
                <c:if test="${mAgency.street != null}">
                    <div class="d-sm-flex flex-sm-wrap mb-3">
                        <div class="fw-semibold"><spring:message code="offcanvas.agency.details.address"/>:</div>
                        <div class="ms-sm-auto mt-1 mt-sm-0">
                            ${mAgency.street}
                            <c:if test="${mAgency.location != null}">
                                (${mAgency.location})
                            </c:if>
                        </div>
                    </div>
                </c:if>

                <c:if test="${mAgency.phone != null}">
                    <div class="d-sm-flex flex-sm-wrap mb-3">
                        <div class="fw-semibold"><spring:message code="offcanvas.agency.details.phone"/>:</div>
                        <div class="ms-sm-auto mt-1 mt-sm-0">${mAgency.phone}</div>
                    </div>
                </c:if>

                <c:if test="${mAgency.website != null}">
                    <div class="d-sm-flex flex-sm-wrap mb-3">
                        <div class="fw-semibold"><spring:message code="offcanvas.agency.details.website"/>:</div>
                        <div class="ms-sm-auto mt-1 mt-sm-0">${mAgency.website}</div>
                    </div>
                </c:if>
            </div>
        </c:when>
        <c:otherwise>
            <h5 class="mb-0 text-danger text-center text-uppercase"><spring:message code="offcanvas.agency.details.not.found"/></h5>
        </c:otherwise>
    </c:choose>
</div>