<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        $("#headerHomeButton").addClass("active");
    });

    function showComingSoon() {
        new Noty({
            text: "<spring:message code="menu.coming.soon"/> !",
            type: "info",
            layout: "topCenter"
        }).show();
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content">
                    <div class="row justify-content-center align-items-center h-100">
                        <div class="row col-sm-12 col-md-12 col-lg-6">
                            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                                <div class="card mb-0 h-100 rounded-0 rounded-top-start h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/teams.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.statistics"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.statistics.description"/></p>
                                        <a href="/sicsdataanalytics/team/home.htm" class="btn btn-warning mt-auto"><spring:message code="menu.team.statistics"/></a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                                <div class="card mb-0 h-100 rounded-0 rounded-top-end h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/players.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.player.statistics"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.player.statistics.description"/></p>
                                        <a href="/sicsdataanalytics/player/home.htm" class="btn btn-warning mt-auto"><spring:message code="menu.player.statistics"/></a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                                <button type="button" href="#" onclick="showComingSoon();" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-start" disabled>
                                    <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1 w-100">
                                        <img width="75" height="75" src="/sicsdataanalytics/images/coach.svg"/>
                                        <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                            <spring:message code="menu.coach.statistics"/>
                                        </div>
                                    </div>
                                    <p class="mb-3"><spring:message code="menu.coach.statistics.description"/></p>
                                    <a href="#" class="btn btn-warning mt-auto w-100"><spring:message code="menu.coming.soon"/></a>
                                </button>
                            </div>
                            <div class="col-sm-12 col-md-6 col-lg-6 mb-3">
                                <button type="button" href="#" onclick="showComingSoon();" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-start" disabled>
                                    <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1 w-100">
                                        <img width="75" height="75" src="/sicsdataanalytics/images/referee.svg"/>
                                        <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                            <spring:message code="menu.referee.statistics"/>
                                        </div>
                                    </div>
                                    <p class="mb-3"><spring:message code="menu.referee.statistics.description"/></p>
                                    <a href="#" class="btn btn-warning mt-auto w-100"><spring:message code="menu.coming.soon"/></a>
                                </button>
                            </div>
                        </div>

                        <!--                        <div class="col">
                                                    <a type="button" href="/sicsdataanalytics/team/home.htm" class="btn btn-white w-100 flex-column rounded-0 rounded-top-start py-2 border mb-3">
                                                        <h4 class="mb-0 text-warning"><spring:message code="menu.team.statistics"/></h4>
                                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-4">
                                                            <img width="75" height="75" src="/sicsdataanalytics/images/teams.svg"/>
                                                        </div>
                        <spring:message code="menu.team.statistics.description"/>
                    </a>

                    <button type="button" href="#" onclick="showComingSoon();" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-start py-2" disabled>
                        <h4 class="mb-0 text-warning w-100"><spring:message code="menu.coach.statistics"/><span class="badge bg-warning float-end mt-1"><spring:message code="menu.coming.soon"/></span></h4>
                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                            <img width="75" height="75" src="/sicsdataanalytics/images/coach.svg"/>
                        </div>
                        <spring:message code="menu.coach.statistics.description"/>
                    </button>
                </div>

                <div class="col">
                    <a type="button" href="/sicsdataanalytics/player/home.htm" class="btn btn-white w-100 flex-column rounded-0 rounded-top-end py-2 border mb-3">
                        <h4 class="mb-0 text-warning"><spring:message code="menu.player.statistics"/></h4>
                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-4">
                            <img width="75" height="75" src="/sicsdataanalytics/images/players.svg"/>
                        </div>
                        <spring:message code="menu.player.statistics.description"/>
                    </a>

                    <button type="button" href="#" onclick="showComingSoon();" class="btn btn-light w-100 flex-column rounded-0 rounded-bottom-end py-2" disabled>
                        <h4 class="mb-0 text-warning w-100"><spring:message code="menu.referee.statistics"/><span class="badge bg-warning float-end mt-1"><spring:message code="menu.coming.soon"/></span></h4>
                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                            <img width="75" height="75" src="/sicsdataanalytics/images/referee.svg"/>
                        </div>
                        <spring:message code="menu.referee.statistics.description"/>
                    </button>
                </div>-->
                    </div>
                </div>
            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
