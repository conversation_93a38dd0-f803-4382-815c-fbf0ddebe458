<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script type="text/javascript">
    <c:if test="${!mTeams.isEmpty()}">
        <c:forEach var="team" items="${mTeams}">
    if (!teamsMap.has(${team.id})) {
        teamsMap.set(${team.id}, eval(${team.getJson()}));
    }
        </c:forEach>
    </c:if>
</script>

<div class="dropdown-divider"></div>

<div class="dropdown-menu-scrollable">
    <c:if test="${!mTeams.isEmpty()}">
        <div class="dropdown-header">Teams</div>

        <c:forEach var="team" items="${mTeams}">
            <div class="dropdown-item cursor-pointer" itemid="${team.id}" onclick="searchProfile(${team.id}, true);">
                <div class="me-3">
                    <img src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${team.logo}.png?<%=System.currentTimeMillis()%>" class="w-32px h-32px rounded-pill image-shadow-sm" alt="" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'">
                </div>

                <div class="d-flex flex-column flex-grow-1 is-text">
                    <div class="fw-semibold">${team.getName(mUser.tvLanguage)}</div>
                    <!--<span class="fs-sm text-muted"><EMAIL></span>-->
                </div>

                <div class="d-inline-flex">
                    <a href="#" class="text-body ms-2">
                        <i class="ph-user-circle"></i>
                    </a>
                </div>
            </div>
        </c:forEach>

        <c:if test="${!mPlayers.isEmpty()}">
            <div class="dropdown-divider"></div>
        </c:if>
    </c:if>

    <c:if test="${!mPlayers.isEmpty()}">
        <div class="dropdown-header">Players</div>

        <c:forEach var="player" items="${mPlayers}">
            <div class="dropdown-item cursor-pointer" itemid="${player.id}" onclick="searchProfile(${player.id}, false);">
                <div class="me-3">
                    <img src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${player.photo}.png?<%=System.currentTimeMillis()%>" class="w-32px h-32px rounded-pill image-shadow-sm" alt="" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                </div>

                <c:choose>
                    <c:when test="${mLoadPlayersLastTeam == null || !mLoadPlayersLastTeam}">
                        <div class="d-flex flex-column flex-grow-1">
                            <div class="fw-semibold is-text">${player.knownName}</div>
                            <span class="fs-sm text-muted is-text">${player.firstName} ${player.lastName}</span>
                        </div>
                    </c:when>
                    <c:otherwise>
                        <div class="d-flex flex-row flex-grow-1 align-items-center">
                            <div>
                                <div class="fw-semibold is-text">${player.knownName}</div>
                                <span class="fs-sm text-muted is-text">${player.firstName} ${player.lastName}</span>
                            </div>
                            <c:if test="${mPlayersLastTeam != null && mPlayersLastTeam.containsKey(player.id)}">
                                <div class="vr ms-2 me-2"></div>
                                <div class="me-2">
                                    <img src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mPlayersLastTeam.get(player.id).logo}.png?<%=System.currentTimeMillis()%>" class="w-32px h-32px rounded-pill image-shadow-sm" alt="" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'">
                                </div>
                                <div class="fw-semibold">${mPlayersLastTeam.get(player.id).getName(mUser.tvLanguage)}</div>
                            </c:if>
                        </div>
                    </c:otherwise>
                </c:choose>

                <div class="d-inline-flex">
                    <a href="#" class="text-body ms-2">
                        <i class="ph-user-circle"></i>
                    </a>
                </div>
            </div>
        </c:forEach>
    </c:if>
</div>