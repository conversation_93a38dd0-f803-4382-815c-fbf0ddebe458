<%@ include file="../global/main.jsp" %>
<body>
    <%@ include file="../global/header.jsp" %>
    
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content d-flex justify-content-center align-items-center">

                    <div class="card mb-0">
                        <div class="card-header bg-warning text-white d-flex justify-content-between">
                            <spring:message code="403.header.title"/>
                            <span class="fw-semibold">403</span>
                        </div>

                        <div class="card-body">
                            <h6 class="card-title"><spring:message code='403.title'/></h6>
                            <p class="card-text"><spring:message code='403.description'/></p>
                        </div>
                        
                        <a href="/sicsdataanalytics/user/home.htm" class="btn bg-warning bg-opacity-75 text-white"><spring:message code="messages.go.to.homepage"/></a>
                    </div>

                </div>
                <!-- /content area -->

            </div>
            <!-- /inner content -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
</body>
