<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
%>

<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="/sicsdataanalytics/images/favicon.ico"/>
    <title>Loading Match Studio...</title>

    <!-- CSS -->
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css" rel="stylesheet" type="text/css">

    <!-- JavaScript -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/noty.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/sweet_alert.min.js"></script>

    <style>
        body {
            background: linear-gradient(135deg, #1b2024 0%, #2d3436 100%);
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }

        .loading-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 100vh;
            text-align: center;
            color: white;
        }

        .logo {
            margin-bottom: 40px;
        }

        .logo img {
            max-width: 200px;
            height: auto;
        }

        .loading-content {
            max-width: 500px;
            padding: 0 20px;
        }

        .loading-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .loading-subtitle {
            font-size: 1.2rem;
            margin-bottom: 40px;
            opacity: 0.8;
            line-height: 1.6;
        }

        .spinner-container {
            margin-bottom: 30px;
        }

        .spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(116, 185, 255, 0.3);
            border-top: 4px solid #74b9ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #74b9ff, #0984e3);
            border-radius: 3px;
            width: 0%;
            animation: progress 20s ease-in-out infinite;
        }

        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        .status-text {
            font-size: 1rem;
            opacity: 0.7;
            margin-bottom: 10px;
        }

        .dots {
            display: inline-block;
        }

        .dots::after {
            content: '';
            animation: dots 1.5s steps(4, end) infinite;
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }

        .error-container {
            display: none;
            color: #ff6b6b;
            text-align: center;
        }

        .error-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .error-message {
            font-size: 1rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }

        .retry-button {
            background: linear-gradient(45deg, #74b9ff, #0984e3);
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .retry-button:hover {
            transform: translateY(-2px);
        }

        .estimated-time {
            font-size: 0.9rem;
            opacity: 0.6;
            margin-top: 15px;
        }
    </style>
</head>

<body>
    <div class="loading-container">
        <div class="logo">
            <img src="/sicsdataanalytics/images/logosics.png" alt="SICS Logo">
        </div>

        <div class="loading-content">
            <h1 class="loading-title">Match Studio</h1>
            <p class="loading-subtitle">
                <spring:message code="match.studio.loading.subtitle"/>
            </p>

            <div class="spinner-container">
                <div class="spinner"></div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>

            <div class="status-text">
                <spring:message code="match.studio.loading.status"/><span class="dots"></span>
            </div>

            <div class="estimated-time">
                <spring:message code="match.studio.loading.estimated"/>
            </div>
        </div>

        <div class="error-container" id="error-container">
            <div class="error-title">
                <spring:message code="match.studio.loading.error.title"/>
            </div>
            <div class="error-message" id="error-message">
                <spring:message code="match.studio.loading.error.message"/>
            </div>
            <button class="retry-button" onclick="retryProcessing()">
                <spring:message code="match.studio.loading.retry"/>
            </button>
        </div>
    </div>

    <script>
        let pollInterval;
        let startTime = Date.now();
        let maxRetries = 3;
        let retryCount = 0;

        // Determine if this is playerStudio or matchStudio based on session attributes
        let isPlayerStudio = ${sessionScope.matchStudio_isPlayerStudio != null ? sessionScope.matchStudio_isPlayerStudio : false};
        let processUrl = isPlayerStudio ? '/sicsdataanalytics/auth/playerStudio/process.htm' : '/sicsdataanalytics/auth/matchStudio/process.htm';
        let statusUrl = isPlayerStudio ? '/sicsdataanalytics/auth/playerStudio/status.htm' : '/sicsdataanalytics/auth/matchStudio/status.htm';

        function startProcessing() {
            // Start polling for completion
            pollInterval = setInterval(checkProcessingStatus, 3000); // Check every 3 seconds
            
            // Start the actual processing (only once)
            $.ajax({
                url: processUrl + '?_t=' + Date.now(),
                type: 'GET',
                cache: false,
                timeout: 60000, // 60 second timeout
                success: function(response) {
                    console.log('Processing started successfully');
                    // Processing started, polling will handle the redirect
                },
                error: function(xhr, status, error) {
                    console.error('Processing start error:', error);
                    clearInterval(pollInterval);
                    showError('Failed to start processing. Please try again.');
                }
            });
        }

        function checkProcessingStatus() {
            $.ajax({
                url: statusUrl + '?_t=' + Date.now(),
                type: 'GET',
                cache: false,
                timeout: 10000, // 10 second timeout for status checks
                success: function(response) {
                    try {
                        let result = typeof response === 'string' ? JSON.parse(response) : response;

                        if (result.status === 'completed') {
                            clearInterval(pollInterval);
                            window.location.href = result.redirect;
                        } else if (result.status === 'error') {
                            clearInterval(pollInterval);
                            showError(result.message || 'Processing failed');
                        } else if (result.status === 'processing') {
                            // Still processing, continue polling
                            console.log('Still processing...');
                        }
                    } catch (e) {
                        console.error('Error parsing response:', e);
                        // Continue polling, might be a temporary issue
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Status check error:', error);

                    // If we've been trying for more than 45 seconds, show error
                    if (Date.now() - startTime > 45000) {
                        clearInterval(pollInterval);
                        showError("<spring:message code="match.studio.loading.too.long"/>");
                    }
                }
            });
        }

        function showError(message) {
            document.querySelector('.loading-content').style.display = 'none';
            document.getElementById('error-container').style.display = 'block';
            document.getElementById('error-message').textContent = message;
        }

        function retryProcessing() {
            if (retryCount < maxRetries) {
                retryCount++;
                document.querySelector('.loading-content').style.display = 'block';
                document.getElementById('error-container').style.display = 'none';
                startTime = Date.now();
                startProcessing();
            } else {
                // Max retries reached, redirect to home or show final error
                window.location.href = '/sicsdataanalytics/user/home.htm';
            }
        }

        // Start processing when page loads
        $(document).ready(function() {
            // Override Noty defaults
            Noty.overrideDefaults({
                theme: 'limitless',
                layout: 'topRight',
                type: 'alert',
                timeout: 2500
            });

            startProcessing();
        });
    </script>
</body>
</html>
