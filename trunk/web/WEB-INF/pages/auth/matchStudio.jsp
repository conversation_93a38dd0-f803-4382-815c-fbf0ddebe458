<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%
    response.setHeader("Pragma", "No-cache");
    response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
    response.setDateHeader("Expires", -1);
    response.setHeader("Access-Control-Allow-Origin", "*");
    response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
    response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization");
%>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="icon" href="/sicsdataanalytics/images/favicon.ico"/>
    <title>Match Studio</title>

    <link href="/sicsdataanalytics/css/custom-matchstudio.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/fonts/inter/inter.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/phosphor/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/assets/icons/icomoon/styles.min.css" rel="stylesheet" type="text/css">
    <link href="/sicsdataanalytics/css/themes/b/1/4.0/html/layout_1/full/assets/css/ltr/all.min.css" id="stylesheet" rel="stylesheet" type="text/css">

    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/jquery/jquery.min.js"></script>
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <script src="/sicsdataanalytics/js/amcharts4/amcharts_4_core.js"></script>
    <script src="/sicsdataanalytics/js/amcharts4/amcharts_4_charts.js"></script>

    <script src="/sicsdataanalytics/js/amcharts.js?<%=System.currentTimeMillis()%>"></script>
    <script src="/sicsdataanalytics/js/amcharts5/index.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/xy.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/radar.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/percent.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/themes/Animated.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/themes/Responsive.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/locales/en.js"></script>
    <script src="/sicsdataanalytics/js/amcharts5/plugins/exporting.js"></script>

    <!-- D3.js Library for canvas/svg -->
    <script src="https://d3js.org/d3.v5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-tip/0.9.0/d3-tip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chroma-js/2.4.2/chroma.min.js"></script>
    <script src="/sicsdataanalytics/js/sics-canvas-matchstudio.js?<%=System.currentTimeMillis()%>"></script>

    <script src="https://cdn.jsdelivr.net/npm/html-to-image@1.11.11/dist/html-to-image.min.js"></script>
    <script src="/sicsdataanalytics/js/jsPDF-3.0.1.js"></script>
    <!-- Noty JS files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/noty.min.js"></script>
    <!-- /noty js files -->
    <!-- Sweet Alert Js files -->
    <script src="/sicsdataanalytics/css/themes/b/1/4.0/assets/js/vendor/notifications/sweet_alert.min.js"></script>
    <!-- /sweet alert js files -->
    <!-- BlockUI JS files -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.blockUI/2.70/jquery.blockUI.min.js" type="text/javascript"></script>
    <!-- /blockui js files -->

    <script type="text/javascript">
        var eventTypes = new Map();

        function showBlockUI() {
            $.blockUI({
                message: '<div style="text-align:center;">' +
                    '<i class="icon-spinner4 spinner"></i><br>' +
                    '<span class="d-block mt-2"><spring:message code="match.studio.export.wait"/></span>' +
                    '</div>',
                overlayCSS: {
                    backgroundColor: '#1b2024',
                    opacity: 0.8,
                    cursor: 'wait'
                },
                css: {
                    border: 0,
                    color: '#fff',
                    padding: 0,
                    backgroundColor: 'transparent'
                }
            });
        }

        async function exportReport(name, quality = 'medium') {
            console.time('exportReport');
            const pages = document.querySelectorAll('.page');
            if (pages.length === 0) {
                console.warn("No pages found!");
                return;
            }

            // Optimized quality settings - reduced PNG quality for better performance
            const qualitySettings = {
                'low': {pixelRatio: 0.5, quality: 0.4, format: 'jpeg'},
                'medium': {pixelRatio: 1, quality: 0.6, format: 'jpeg'},
                'high': {pixelRatio: 1.2, quality: 0.75, format: 'jpeg'} // Changed from PNG to JPEG
            };

            const settings = qualitySettings[quality] || qualitySettings['medium'];
            console.log(`Starting export with quality settings:`, settings);

            showBlockUI();

            try {
                console.time('documentProcessing');
                document.body.classList.add('pdf-export');

                // Pre-configure all images globally and optimize base64 images
                const allImages = document.querySelectorAll('.page img');
                const originalCrossOrigins = new Map();
                const base64Optimizations = new Map();

                allImages.forEach(img => {
                    originalCrossOrigins.set(img, img.crossOrigin);
                    img.crossOrigin = 'anonymous';

                    // Optimize base64 images
                    if (img.src && img.src.startsWith('data:image/')) {
                        const originalSrc = img.src;

                        // Check if it's a large base64 image that could benefit from optimization
                        if (originalSrc.length > 25000) { // ~37KB+ base64 images
                            console.log(`Optimizing large base64 image (${Math.round(originalSrc.length/1024)}KB)`);

                            // Store original for restoration
                            base64Optimizations.set(img, originalSrc);

                            // Create optimized version
                            try {
                                const canvas = document.createElement('canvas');
                                const ctx = canvas.getContext('2d');
                                const tempImg = new Image();

                                tempImg.onload = () => {
                                    // Calculate optimized dimensions (max 800px width/height)
                                    const maxDimension = 800;
                                    let { width, height } = tempImg;

                                    if (width > maxDimension || height > maxDimension) {
                                        const ratio = Math.min(maxDimension / width, maxDimension / height);
                                        width *= ratio;
                                        height *= ratio;
                                    }

                                    canvas.width = width;
                                    canvas.height = height;

                                    // Draw and compress
                                    ctx.drawImage(tempImg, 0, 0, width, height);

                                    // Convert to JPEG with compression for better performance
                                    const optimizedSrc = canvas.toDataURL('image/jpeg', 0.7);
                                    img.src = optimizedSrc;

                                    console.log(`Base64 optimized: ${Math.round(originalSrc.length/1024)}KB → ${Math.round(optimizedSrc.length/1024)}KB`);
                                };

                                tempImg.src = originalSrc;
                            } catch (error) {
                                console.warn('Failed to optimize base64 image:', error);
                            }
                        }
                    }
                });

                // Optimized capture options for base64 heavy pages
                const captureOptions = {
                    pixelRatio: settings.pixelRatio,
                    quality: settings.quality,
                    backgroundColor: '#ffffff',
                    skipFonts: false,
                    useCORS: true,
                    allowTaint: false,
                    // Specific optimizations for base64 images
                    cacheBust: false, // Don't add cache-busting parameters
                    ignoreElements: (element) => {
                        // Skip problematic elements that might slow down capture
                        return element.tagName === 'SCRIPT' ||
                            element.tagName === 'NOSCRIPT' ||
                            (element.tagName === 'IMG' && element.src.startsWith('data:image/') && element.src.length > 100000); // Skip very large base64 > ~75KB
                    }
                };

                // Process pages sequentially for better memory management
                console.log(`Processing ${pages.length} pages sequentially`);

                const results = [];

                for (let index = 0; index < pages.length; index++) {
                    const page = pages[index];
                    console.time(`page-` + index);

                    try {
                        console.time(`capture-` + index);

                        // Use toJpeg for all formats for better performance
                        const dataUrl = await htmlToImage.toJpeg(page, captureOptions);

                        console.timeEnd(`capture-` + index);

                        // Use createImageBitmap for faster image processing if available
                        let imgWidth, imgHeight;

                        if ('createImageBitmap' in window) {
                            // Convert data URL to blob for createImageBitmap
                            const response = await fetch(dataUrl);
                            const blob = await response.blob();
                            const bitmap = await createImageBitmap(blob);
                            imgWidth = bitmap.width;
                            imgHeight = bitmap.height;
                            bitmap.close(); // Clean up
                        } else {
                            // Fallback to Image object
                            await new Promise((resolve) => {
                                const img = new Image();
                                img.onload = () => {
                                    imgWidth = img.width;
                                    imgHeight = img.height;
                                    resolve();
                                };
                                img.src = dataUrl;
                            });
                        }

                        const pxToMm = px => px * 25.4 / 96;
                        const imgWidthMm = pxToMm(imgWidth);
                        const imgHeightMm = pxToMm(imgHeight);

                        console.timeEnd(`page-` + index);

                        results.push({
                            index,
                            dataUrl,
                            imgWidthMm,
                            imgHeightMm
                        });
                    } catch (error) {
                        console.error(`Error processing page ${index}:`, error);
                        throw error;
                    }
                }

                // Restore original crossOrigin values and base64 images
                allImages.forEach(img => {
                    const original = originalCrossOrigins.get(img);
                    if (original !== undefined) {
                        img.crossOrigin = original;
                    } else {
                        img.removeAttribute('crossorigin');
                    }

                    // Restore original base64 images
                    const originalBase64 = base64Optimizations.get(img);
                    if (originalBase64) {
                        img.src = originalBase64;
                    }
                });

                console.timeEnd('documentProcessing');
                console.time('pdfCreation');

                // Create PDF with optimized settings
                const pdf = new window.jspdf.jsPDF({
                    orientation: "portrait",
                    unit: "mm",
                    format: "a4",
                    compress: true,
                    putOnlyUsedFonts: true,
                    userUnit: 1.0,
                    // Add precision for better performance
                    precision: 2
                });

                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();

                // Batch add images to PDF to reduce overhead
                results.forEach((result, index) => {
                    const {dataUrl, imgWidthMm, imgHeightMm} = result;

                    const scale = pageWidth / imgWidthMm;
                    const pdfImgWidth = pageWidth;
                    const pdfImgHeight = imgHeightMm * scale;
                    const y = pdfImgHeight < pageHeight ? (pageHeight - pdfImgHeight) / 2 : 0;

                    if (index !== 0) pdf.addPage();

                    // Use JPEG format for all images for consistency and performance
                    pdf.addImage(dataUrl, 'JPEG', 0, y, pdfImgWidth, pdfImgHeight);
                });

                console.timeEnd('pdfCreation');

                pdf.save(name + '.pdf');
                new Noty({text: "PDF saved!", type: "success"}).show();

            } catch (error) {
                console.error("Error exporting report:", error);
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "An error occurred during export",
                    icon: "error",
                    padding: 40
                });
            } finally {
                document.body.classList.remove('pdf-export');
                $.unblockUI();
                console.timeEnd('exportReport');
            }
        }


        /*function exportReport(name) {
            // TODO: metterlo come label (da fare traduzione)
            new Noty({
                text: "PDF generation in progress...",
                timeout: 3000,
                type: "info",
                layout: "topRight"
            }).show();

            setTimeout(function () {
                document.body.classList.add('pdf-export');
                var element = document.getElementById('report');
                html2pdf()
                    .set({
                        margin: 0.5,
                        filename: name +'.pdf',
                        image: { type: 'jpeg', quality: 0.98 },
                        html2canvas: { scale: 2, useCORS: true },
                        jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
                    })
                    .from(element)
                    .save()
                    .then(() => document.body.classList.remove('pdf-export'));
            }, 500);
        }*/

        $(document).ready(function () {
            // opposite event type map
            <c:if test="${mEventTypes != null}">
                <c:forEach var="eventType" items="${mEventTypes.values()}">
                eventTypes.set(${eventType.id}, eval(${eventType.getJson()}));
                </c:forEach>
            </c:if>

            // Override Noty defaults
            Noty.overrideDefaults({
                theme: 'limitless',
                layout: 'topRight',
                type: 'alert',
                timeout: 2500
            });

            <c:if test="${mFirstPageImage != null}">
            var maxPlayerTotals = 0, maxPlayerTotalsTo = 0;
            am4core.ready(function() {
                var chart = am4core.create("heatMapPassesHome", am4charts.XYChart);
                chart.maskBullets = false;

                var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());

                xAxis.dataFields.category = "player";
                yAxis.dataFields.category = "playerTo";

                xAxis.renderer.grid.template.disabled = true;
                xAxis.renderer.minGridDistance = 10;
                xAxis.renderer.inversed = true;

                yAxis.renderer.grid.template.disabled = true;
                yAxis.renderer.inversed = true;
                yAxis.renderer.minGridDistance = 10;

                var series = chart.series.push(new am4charts.ColumnSeries());
                series.dataFields.categoryX = "player";
                series.dataFields.categoryY = "playerTo";
                series.dataFields.value = "value";
                series.sequencedInterpolation = true;
                series.defaultState.transitionDuration = 0;

                var bgColor = am4core.color("green");

                var columnTemplate = series.columns.template;
                columnTemplate.strokeWidth = 1;
                columnTemplate.strokeOpacity = 0.2;
                columnTemplate.stroke = bgColor;
                columnTemplate.tooltipText = "{player}, {playerTo}: {value.workingValue.formatNumber('#.')}";
                columnTemplate.width = am4core.percent(100);
                columnTemplate.height = am4core.percent(100);

                var label = columnTemplate.createChild(am4core.Label);
                label.text = "{value}";
                label.align = "center";
                label.valign = "middle";
                label.fontSize = 12;
                label.fill = am4core.color("black");

                series.heatRules.push({
                    target: columnTemplate,
                    property: "fill",
                    min: am4core.color("#ffffff"),
                    max: am4core.color("#609134"),
                    dataField: "value",
                    minValue: 0,
                    maxValue: ${mHeatMapPassesT1MaxValue}
                });

                series.columns.template.column.adapter.add("fill", function(fill, target) {
                    if (target.dataItem) {
                        if (target.dataItem.dataContext.player === '') {
                            if (target.dataItem.dataContext.value == -999) {
                                return am4core.color("#d6d6d6")
                            } else {
                                if (target.dataItem.dataContext.value == maxPlayerTotalsTo) {
                                    return am4core.color("#609134")
                                } else {
                                    return am4core.color("#d6d6d6")
                                }
                            }
                        } else if (target.dataItem.dataContext.playerTo === '<spring:message code="match.studio.totale"/>') {
                            if (target.dataItem.dataContext.value == maxPlayerTotals) {
                                return am4core.color("#609134")
                            } else {
                                return am4core.color("#d6d6d6")
                            }
                        }
                    }
                    return fill;
                });

                label.adapter.add("text", function(text, target) {
                    if (target.dataItem.dataContext.value > 0) {
                        return target.dataItem.dataContext.value
                    } else if (target.dataItem.dataContext.value == -1) {
                        return "-";
                    } else if (target.dataItem.dataContext.value == -999) {
                        return "<spring:message code="match.studio.totale"/>";
                    } else {
                        return "";
                    }
                });

                var data = ${mHeatMapPassesT1Data};

                // Calcola il totale per ciascun "player" e "playerTo"
                var playerTotals = {};
                var playerToTotals = {};

                data.forEach(function(item) {
                    // Totali per "player"
                    if (!playerTotals[item.player]) {
                        playerTotals[item.player] = 0;
                    }
                    if (item.value >= 0) {
                        playerTotals[item.player] += item.value;
                    }

                    // Totali per "playerTo"
                    if (!playerToTotals[item.playerTo]) {
                        playerToTotals[item.playerTo] = 0;
                    }
                    if (item.value >= 0) {
                        playerToTotals[item.playerTo] += item.value;
                    }
                });

                Object.keys(playerTotals).forEach(function(player) {
                    var el = playerTotals[player];
                    if (el > maxPlayerTotals) {
                        maxPlayerTotals = el;
                    }
                });

                Object.keys(playerToTotals).forEach(function(playerTo) {
                    var el = playerToTotals[playerTo];
                    if (el > maxPlayerTotalsTo) {
                        maxPlayerTotalsTo = el;
                    }
                });

                // Aggiungi i totali come punti dati aggiuntivi
                Object.keys(playerTotals).forEach(function(player) {
                    data.push({ player: player, playerTo: "<spring:message code="match.studio.totale"/>", value: playerTotals[player] });
                });
                data.push({ player: "", playerTo: "<spring:message code="match.studio.totale"/>", value: "-999" });

                Object.keys(playerToTotals).forEach(function(playerTo) {
                    data.unshift({ player: "", playerTo: playerTo, value: playerToTotals[playerTo] });
                });

                chart.data = data;
                chart.events.on("appeared", function () {
                    $("svg").each(function (index, element) {
                        $(element).find("g[opacity='0.3']").remove();
                    });
                });
            }); // end am4core.ready()

            maxPlayerTotals = 0, maxPlayerTotalsTo = 0;
            am4core.ready(function() {
                var chart = am4core.create("heatMapPassesAway", am4charts.XYChart);
                chart.maskBullets = false;

                var xAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                var yAxis = chart.yAxes.push(new am4charts.CategoryAxis());

                xAxis.dataFields.category = "player";
                yAxis.dataFields.category = "playerTo";

                xAxis.renderer.grid.template.disabled = true;
                xAxis.renderer.minGridDistance = 10;
                xAxis.renderer.inversed = true;

                yAxis.renderer.grid.template.disabled = true;
                yAxis.renderer.inversed = true;
                yAxis.renderer.minGridDistance = 10;

                var series = chart.series.push(new am4charts.ColumnSeries());
                series.dataFields.categoryX = "player";
                series.dataFields.categoryY = "playerTo";
                series.dataFields.value = "value";
                series.sequencedInterpolation = true;
                series.defaultState.transitionDuration = 0;

                var bgColor = am4core.color("green");

                var columnTemplate = series.columns.template;
                columnTemplate.strokeWidth = 1;
                columnTemplate.strokeOpacity = 0.2;
                columnTemplate.stroke = bgColor;
                columnTemplate.tooltipText = "{player}, {playerTo}: {value.workingValue.formatNumber('#.')}";
                columnTemplate.width = am4core.percent(100);
                columnTemplate.height = am4core.percent(100);

                var label = columnTemplate.createChild(am4core.Label);
                label.text = "{value}";
                label.align = "center";
                label.valign = "middle";
                label.fontSize = 12;
                label.fill = am4core.color("black");

                series.heatRules.push({
                    target: columnTemplate,
                    property: "fill",
                    min: am4core.color("#ffffff"),
                    max: am4core.color("#609134"),
                    dataField: "value",
                    minValue: 0,
                    maxValue: ${mHeatMapPassesT2MaxValue}
                });

                series.columns.template.column.adapter.add("fill", function(fill, target) {
                    if (target.dataItem) {
                        if (target.dataItem.dataContext.player === '') {
                            if (target.dataItem.dataContext.value == -999) {
                                return am4core.color("#d6d6d6")
                            } else {
                                if (target.dataItem.dataContext.value == maxPlayerTotalsTo) {
                                    return am4core.color("#609134")
                                } else {
                                    return am4core.color("#d6d6d6")
                                }
                            }
                        } else if (target.dataItem.dataContext.playerTo === '<spring:message code="match.studio.totale"/>') {
                            if (target.dataItem.dataContext.value == maxPlayerTotals) {
                                return am4core.color("#609134")
                            } else {
                                return am4core.color("#d6d6d6")
                            }
                        }
                    }
                    return fill;
                });

                label.adapter.add("text", function(text, target) {
                    if (target.dataItem.dataContext.value > 0) {
                        return target.dataItem.dataContext.value
                    } else if (target.dataItem.dataContext.value == -1) {
                        return "-";
                    } else if (target.dataItem.dataContext.value == -999) {
                        return "<spring:message code="match.studio.totale"/>";
                    } else {
                        return "";
                    }
                });

                var data = ${mHeatMapPassesT2Data};

                // Calcola il totale per ciascun "player" e "playerTo"
                var playerTotals = {};
                var playerToTotals = {};

                data.forEach(function(item) {
                    // Totali per "player"
                    if (!playerTotals[item.player]) {
                        playerTotals[item.player] = 0;
                    }
                    if (item.value >= 0) {
                        playerTotals[item.player] += item.value;
                    }

                    // Totali per "playerTo"
                    if (!playerToTotals[item.playerTo]) {
                        playerToTotals[item.playerTo] = 0;
                    }
                    if (item.value >= 0) {
                        playerToTotals[item.playerTo] += item.value;
                    }
                });

                Object.keys(playerTotals).forEach(function(player) {
                    var el = playerTotals[player];
                    if (el > maxPlayerTotals) {
                        maxPlayerTotals = el;
                    }
                });

                Object.keys(playerToTotals).forEach(function(playerTo) {
                    var el = playerToTotals[playerTo];
                    if (el > maxPlayerTotalsTo) {
                        maxPlayerTotalsTo = el;
                    }
                });

                // Aggiungi i totali come punti dati aggiuntivi
                Object.keys(playerTotals).forEach(function(player) {
                    data.push({ player: player, playerTo: "<spring:message code="match.studio.totale"/>", value: playerTotals[player] });
                });
                data.push({ player: "", playerTo: "<spring:message code="match.studio.totale"/>", value: "-999" });

                Object.keys(playerToTotals).forEach(function(playerTo) {
                    data.unshift({ player: "", playerTo: playerTo, value: playerToTotals[playerTo] });
                });

                chart.data = data;
                chart.events.on("appeared", function () {
                    $("svg").each(function (index, element) {
                        $(element).find("g[opacity='0.3']").remove();
                    });
                });
            }); // end am4core.ready()

            <c:if test="${mSkipOI == null || !mSkipOI}">
                am4core.ready(function () {
                    var chart = am4core.create("offensiveIndexColumnChart", am4charts.XYChart);

                    chart.data = [
                        {
                            country: "${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}",
                            visits: ${Math.round(mOITotalT1)},
                            columnColor: "#EB5A10"
                        },
                        {
                            country: "${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}",
                            visits: ${Math.round(mOITotalT2)},
                            columnColor: "#a9a9a9"
                        }
                    ];

                    // Axes
                    var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                    categoryAxis.dataFields.category = "country";
                    categoryAxis.renderer.grid.template.location = 0;
                    categoryAxis.renderer.minGridDistance = 30;

                    var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                    valueAxis.min = 0;
                    var maxVisits = Math.max(${Math.round(mOITotalT1)}, ${Math.round(mOITotalT2)});
                    valueAxis.max = Math.ceil(maxVisits * 1.10); // aggiungi 10%
                    valueAxis.strictMinMax = true;

                    // Series
                    var series = chart.series.push(new am4charts.ColumnSeries());
                    series.dataFields.valueY = "visits";
                    series.dataFields.categoryX = "country";
                    series.name = "Visits";
                    series.columns.template.tooltipText = "{categoryX}: [bold]{valueY}[/]";
                    series.columns.template.fillOpacity = .8;

                    // Colori personalizzati
                    series.columns.template.adapter.add("fill", function (fill, target) {
                        return target.dataItem.dataContext.columnColor;
                    });
                    series.columns.template.adapter.add("stroke", function (stroke, target) {
                        return target.dataItem.dataContext.columnColor;
                    });

                    var columnTemplate = series.columns.template;
                    columnTemplate.strokeWidth = 2;
                    columnTemplate.strokeOpacity = 1;

                    chart.events.on("appeared", function () {
                        $("svg").each(function (index, element) {
                            $(element).find("g[opacity='0.3']").remove();
                        });
                    });
                }); // end am4core.ready()

                am4core.ready(function() {
                    // Chart
                    var chart = am4core.create("offensiveIndexTrendChart", am4charts.XYChart);
                    chart.paddingRight = 30;
                    chart.data = ${mOITrendChartData};

                    // Axes
                    var xAxis = chart.xAxes.push(new am4charts.ValueAxis());
                    xAxis.title.text = "<spring:message code="match.studio.minuti.giocati"/>";
                    xAxis.min = 0;
                    xAxis.max = 95;
                    xAxis.strictMinMax = true;
                    xAxis.renderer.minGridDistance = 25;

                    var yAxis = chart.yAxes.push(new am4charts.ValueAxis());
                    yAxis.title.text = "<spring:message code="match.studio.indice.di.pericolosita"/>";
                    yAxis.min = 0;

                    // home Step Series
                    var homeSeries = chart.series.push(new am4charts.StepLineSeries());
                    homeSeries.name = "${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}";
                    homeSeries.dataFields.valueY = "home";
                    homeSeries.dataFields.valueX = "minute";
                    homeSeries.strokeWidth = 2.5;
                    homeSeries.stroke = am4core.color("#f77c1b"); // Orange
                    homeSeries.fillOpacity = 0;
                    homeSeries.sequencedInterpolation = true;

                    // For home
                    /*var homeBullet = homeSeries.bullets.push(new am4charts.Bullet());
                        var homeImage = homeBullet.createChild(am4core.Image);
                        homeImage.href = "/sicsdataanalytics/images/matchstudio/football-ball-aut.svg";
                        homeImage.width = 20;
                        homeImage.height = 20;
                        homeBullet.adapter.add("visible", function(visible, target) {
                            return target.dataItem.dataContext.homeGoal === true;
                        });*/

                    // away Step Series
                    var awaySeries = chart.series.push(new am4charts.StepLineSeries());
                    awaySeries.name = "${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}";
                    awaySeries.dataFields.valueY = "away";
                    awaySeries.dataFields.valueX = "minute";
                    awaySeries.strokeWidth = 2.5;
                    awaySeries.stroke = am4core.color("#aaaaaa"); // Grey
                    awaySeries.fillOpacity = 0;
                    awaySeries.sequencedInterpolation = true;

                    // For away
                    /*var awayBullet = awaySeries.bullets.push(new am4charts.Bullet());
                        var awayImage = awayBullet.createChild(am4core.Image);
                        awayImage.href = "/sicsdataanalytics/images/matchstudio/football-ball.svg";
                        awayImage.width = 20;
                        awayImage.height = 20;
                        awayBullet.adapter.add("visible", function(visible, target) {
                            return target.dataItem.dataContext.awayGoal === true;
                        });*/

                        // 1. Data for your balls
                        var goalsData = ${mOITrendChartGoalsData};

                    // 2. Add the series (after main step series)
                    var eventsSeries = chart.series.push(new am4charts.LineSeries());
                    eventsSeries.data = goalsData.map(function(ev) {
                        return { minute: ev.minute, value: 0, ballIcon: ev.ballIcon };
                    });
                    eventsSeries.dataFields.valueX = "minute";
                    eventsSeries.dataFields.valueY = "value";
                    eventsSeries.strokeOpacity = 0;

                    // 3. Ball icon
                    var bullet = eventsSeries.bullets.push(new am4charts.Bullet());
                    var image = bullet.createChild(am4core.Image);
                    image.propertyFields.href = "ballIcon";
                    image.width = 15;
                    image.height = 15;
                    image.verticalCenter = "bottom";
                    image.horizontalCenter = "middle";

                    // Add vertical line and label
                    var range = xAxis.axisRanges.create();
                    range.value = ${mOITrendChartBreak};
                    range.grid.stroke = am4core.color("#f77c1b");
                    range.grid.strokeWidth = 2;
                    range.grid.strokeDasharray = "3,3";
                    range.label.text = "<spring:message code="match.studio.intervallo"/>";
                    range.label.rotation = -90;
                    range.label.fill = am4core.color("#f77c1b");
                    range.label.verticalCenter = "bottom";
                    range.label.horizontalCenter = "right";
                    range.label.dy = -175;

                    chart.events.on("appeared", function () {
                        $("svg").each(function (index, element) {
                            $(element).find("g[opacity='0.3']").remove();
                        });
                    });
                }); // end am4core.ready()
            </c:if>
            </c:if>

            <c:forEach var="playerId" items="${mPlayerIds}">
            <c:if test="${mPlayerDataMap.get(playerId).mTab8Data != null && mPlayerDataMap.get(playerId).mTab8Data != ''}">
            var tab3Pitch = {...pitchVertical};
            drawVerticalField("tab3Image${playerId}", tab3Pitch);
            <c:if test="${mPlayerDataMap.get(playerId).mtab3ImageData != null}">
            <c:forEach var="event" items="${mPlayerDataMap.get(playerId).mtab3ImageData}">
            drawEvent(eval(${event.getJson()}), true, tab3Pitch);
            </c:forEach>
            </c:if>

            var tab5Pitch = {...pitchVertical};
            drawVerticalField("tab5Image${playerId}", tab5Pitch);
            <c:if test="${mPlayerDataMap.get(playerId).mTab5ImageData != null}">
                <c:forEach var="event" items="${mPlayerDataMap.get(playerId).mTab5ImageData}">
                drawEvent(eval(${event.getJson()}), true, tab5Pitch);
                </c:forEach>
            </c:if>

            // grafico tocchi
            am4core.ready(function () {
                // Create chart instance
                var chart = am4core.create("chartdiv${playerId}", am4charts.XYChart);
                chart.hiddenState.properties.opacity = 0; // this creates initial fade-in
                chart.paddingBottom = 25;

                // Add data
                chart.data = ${mPlayerDataMap.get(playerId).mTab8Data};

                // Create axes
                var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                categoryAxis.dataFields.category = "year";
                categoryAxis.renderer.minGridDistance = 0;
                // categoryAxis.renderer.grid.template.location = 0.5;
                categoryAxis.renderer.labels.template.fontSize = 8;
                categoryAxis.startLocation = 0.1;
                categoryAxis.endLocation = 0.9;
                categoryAxis.extraMax = 0.1;

                // Create value axis
                var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                valueAxis.baseValue = 0;
                valueAxis.renderer.labels.template.fontSize = 8;
                valueAxis.renderer.minGridDistance = 10;
                valueAxis.extraMax = 0.1;

                // Create series
                var series = chart.series.push(new am4charts.LineSeries());
                series.dataFields.valueY = "value";
                series.dataFields.categoryX = "year";
                series.strokeWidth = 2;
                series.tensionX = 0.77;
                series.stroke = am4core.color("#737373");
                series.fill = series.stroke;
                series.fillOpacity = 0.15;

                var secondSeries = chart.series.push(new am4charts.LineSeries());
                secondSeries.dataFields.valueY = "value2";
                secondSeries.dataFields.categoryX = "year";
                secondSeries.strokeWidth = 2;
                secondSeries.tensionX = 0.77;
                secondSeries.stroke = am4core.color("#f26521");

                // bullet is added because we add tooltip to a bullet for it to change color
                var bullet = series.bullets.push(new am4charts.Bullet());
                bullet.adapter.add("fill", function (fill, target) {
                    if (target.dataItem.valueY < 0) {
                        return am4core.color("#FF0000");
                    }
                    return fill;
                });

                var circleBullet = series.bullets.push(new am4charts.CircleBullet());
                circleBullet.circle.radius = 1;

                var circleBulletSecond = secondSeries.bullets.push(new am4charts.CircleBullet());
                circleBulletSecond.circle.radius = 3;
                circleBulletSecond.circle.fill = am4core.color("#f26521");
                var labelBulletSecond = secondSeries.bullets.push(new am4charts.LabelBullet());
                labelBulletSecond.label.text = "{valueY}";
                labelBulletSecond.label.fontSize = 8;
                labelBulletSecond.label.dy = -10;

                var range = valueAxis.createSeriesRange(series);
                range.value = 0;
                range.endValue = -1000;
                range.contents.stroke = am4core.color("#FF0000");
                range.contents.fill = range.contents.stroke;

                chart.events.on("appeared", function () {
                    $("svg").each(function (index, element) {
                        $(element).find("g[opacity='0.3']").remove();
                    });
                });
            }); // end am4core.ready()
            <c:if test="${mPlayerDataMap.get(playerId).mPositionId != 1}">
            let playerData${playerId} = ${mPlayerDataMap.get(playerId).mTab2DataPlayer};
            let averageData${playerId} = ${mPlayerDataMap.get(playerId).mTab2DataAverage};
            am4core.ready(function () {
                // Create the RadarChart instance in the "chartradardiv" container.
                var chart = am4core.create("chartradardiv${playerId}", am4charts.RadarChart);
                chart.hiddenState.properties.opacity = 0; // for fade-in

                chart.panBehavior = "none";
                chart.mouseWheelBehavior = "none";

                var categoryAxis = chart.xAxes.push(new am4charts.CategoryAxis());
                categoryAxis.dataFields.category = "event";
                categoryAxis.renderer.labels.template.fontSize = 6;
                categoryAxis.renderer.labels.template.wrap = true;
                categoryAxis.renderer.labels.template.maxWidth = 50;

                var valueAxis = chart.yAxes.push(new am4charts.ValueAxis());
                valueAxis.min = 0;
                valueAxis.max = 1;
                valueAxis.extraMax = 0.03; // Add padding above max
                valueAxis.strictMinMax = true;
                valueAxis.renderer.labels.template.disabled = true;

                var series1 = chart.series.push(new am4charts.RadarSeries());
                series1.name = "Player";
                series1.dataFields.valueY = "value";
                series1.dataFields.categoryX = "event";
                series1.strokeWidth = 2;
                series1.fill = am4core.color("#ed6621");
                series1.stroke = am4core.color("#ed6621");
                series1.fillOpacity = 0.2;
                series1.data = playerData${playerId};

                var dsSeries1 = new am4core.DropShadowFilter();
                dsSeries1.blur = 4;
                dsSeries1.dx = 2;
                dsSeries1.dy = 2;
                dsSeries1.opacity = 0.3;
                series1.filters.push(dsSeries1);

                var bullet1 = series1.bullets.push(new am4charts.Bullet());
                var circle1 = bullet1.createChild(am4core.Circle);
                circle1.radius = 3;
                circle1.fill = series1.fill;

                var series2 = chart.series.push(new am4charts.RadarSeries());
                series2.name = "Average";
                series2.dataFields.valueY = "value";
                series2.dataFields.categoryX = "event";
                series2.strokeWidth = 2;
                series2.fill = am4core.color("#999999");
                series2.stroke = am4core.color("#999999");
                series2.fillOpacity = 0.2;
                series2.data = averageData${playerId};

                var bullet2 = series2.bullets.push(new am4charts.Bullet());
                var circle2 = bullet2.createChild(am4core.Circle);
                circle2.radius = 3;
                circle2.fill = series2.fill;

                categoryAxis.data = ${mPlayerDataMap.get(playerId).mTab2DataEvent};

                series1.appear(1000);
                series2.appear(1000);
                chart.appear(1000, 100);

                chart.events.on("appeared", function () {
                    $("svg").each(function (index, element) {
                        $(element).find("g[opacity='0.3']").remove();
                    });

                    valueAxis.zoomToValues(valueAxis.min, valueAxis.max + (valueAxis.extraMax || 0));
                });
            }); // end am4core.ready()
            </c:if>
            </c:if>
            </c:forEach>
        });
    </script>
</head>
<body>
<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">
        <!-- Inner content -->
        <div class="content-inner bg-white">
            <div class="d-flex align-items-center justify-content-center">
                <div id="report">
                    <c:if test="${mFirstPageImage != null}">
                        <div class="page">
                            <div class="mainHeaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div class="dropdown ms-auto hide-on-print">
                                                <button class="btn btn-sm btn-info dropdown-toggle" href="#" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <spring:message code="match.studio.save.pdf"/>
                                                </button>
                                                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                                    <%--<li><a class="dropdown-item" href="#" onclick="exportReport('${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}-${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}-Match-Report', 'low')"><spring:message code="match.studio.export.quality.low"/></a></li>--%>
                                                    <li><a class="dropdown-item" href="#" onclick="exportReport('${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}-${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}-Match-Report', 'medium')"><spring:message code="match.studio.export.quality.medium"/></a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="exportReport('${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}-${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}-Match-Report', 'high')"><spring:message code="match.studio.export.quality.high"/></a></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-8">
                                    <div class="d-flex ms-5 align-items-center">
                                        <img class="first-img" height="40" width="40" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mFixture.homeTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'" crossorigin="anonymous">
                                        <p class="ms-2 mb-0 first-p d-flex align-items-center p-2 px-3 text-bg-black text-white fs-4 fw-bold">${mFixture.homeScore}</p>
                                        <div class="ms-2">
                                            <h4 class="mb-0">${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</h4>
                                            <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(25) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(25).get(null).isEmpty()}">
                                                <p class="mb-0 fs-sm">
                                                    <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(25).get(null)}" varStatus="counter">
                                                        <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                            <c:if test="${counter.count > 1}">,</c:if>${mPlayers.get(event.playerIds.get(0)).knownName} (${event.endMinute}')
                                                        </c:if>
                                                    </c:forEach>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(26) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(26).get('159').isEmpty()}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(26).get('159')}">
                                                            <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                                <c:choose>
                                                                    <c:when test="${event.playerIds.size() == 1}">
                                                                        <c:if test="${playerCounter > 0}">,</c:if>${mPlayers.get(event.playerIds.get(0)).knownName} (${event.endMinute}')
                                                                        <c:set var="playerCounter" value="${playerCounter + 1}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <c:forEach var="playerId" items="${event.playerIds}">
                                                                            <%-- Escludo Portieri --%>
                                                                            <c:if test="${mFixturePlayers.get(playerId).positionId != 1}">
                                                                                <c:if test="${playerCounter > 0}">, </c:if>${mPlayers.get(playerId).knownName} (${event.endMinute}' aut.)
                                                                                <c:set var="playerCounter" value="${playerCounter + 1}"/>
                                                                            </c:if>
                                                                        </c:forEach>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                </p>
                                            </c:if>
                                        </div>
                                    </div>
                                    <div class="d-flex ms-5 mt-2 align-items-center">
                                        <img class="first-img" height="40" width="40" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mFixture.awayTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'" crossorigin="anonymous">
                                        <p class="ms-2 mb-0 first-p d-flex align-items-center p-2 px-3 text-bg-black text-white fs-4 fw-bold">${mFixture.awayScore}</p>
                                        <div class="ms-2">
                                            <h4 class="mb-0">${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</h4>
                                            <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(25) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(25).get(null).isEmpty()}">
                                                <p class="mb-0 fs-sm">
                                                    <c:set var="playerCounter" value="${0}"/>
                                                    <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(25).get(null)}">
                                                        <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                            <c:if test="${playerCounter > 0}">,</c:if>${mPlayers.get(event.playerIds.get(0)).knownName} (${event.endMinute}')
                                                            <c:set var="playerCounter" value="${playerCounter + 1}"/>
                                                        </c:if>
                                                    </c:forEach>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(26) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(26).get('159').isEmpty()}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(26).get('159')}">
                                                            <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                                <c:choose>
                                                                    <c:when test="${event.playerIds.size() == 1}">
                                                                        <c:if test="${playerCounter > 0}">,</c:if>${mPlayers.get(event.playerIds.get(0)).knownName} (${event.endMinute}')
                                                                        <c:set var="playerCounter" value="${playerCounter + 1}"/>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <c:forEach var="playerId" items="${event.playerIds}">
                                                                            <%-- Escludo Portieri --%>
                                                                            <c:if test="${mFixturePlayers.get(playerId).positionId != 1}">
                                                                                <c:if test="${playerCounter > 0}">, </c:if>${mPlayers.get(playerId).knownName} (${event.endMinute}' aut.)
                                                                                <c:set var="playerCounter" value="${playerCounter + 1}"/>
                                                                            </c:if>
                                                                        </c:forEach>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                </p>
                                            </c:if>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <c:if test="${mFixture.referee != null}">
                                        <p class="mb-0"><spring:message code="match.studio.referee"/>: ${mFixture.referee}</p>
                                    </c:if>
                                    <c:if test="${mFixture.firstAssistant != null}">
                                        <p class="mb-0"><spring:message code="match.studio.primo.assistente"/>: ${mFixture.firstAssistant}</p>
                                    </c:if>
                                    <c:if test="${mFixture.secondAssistant != null}">
                                        <p class="mb-0"><spring:message code="match.studio.secondo.assistente"/>: ${mFixture.secondAssistant}</p>
                                    </c:if>
                                    <c:if test="${mFixture.fourthReferee != null}">
                                        <p class="mb-0"><spring:message code="match.studio.quarto.uomo"/>: ${mFixture.fourthReferee}</p>
                                    </c:if>
                                </div>
                            </div>

                            <div class="row mt-3 px-4">
                                <div class="col-6 ps-0">
                                    <table class="table w-100">
                                        <tbody>
                                        <c:set var="lastPositionId" value="${1}"/>
                                        <c:forEach var="fixturePlayer" items="${mHomeTeamPlayers}">
                                            <c:set var="currentPositionId" value="${fixturePlayer.positionId}"/>
                                            <c:set var="extraClass" value="border-0"/>
                                            <c:choose>
                                                <c:when test="${mHomeTeamPlayers.indexOf(fixturePlayer) > 10}">
                                                    <c:if test="${mHomeTeamPlayers.indexOf(fixturePlayer) == 11}">
                                                        <c:set var="extraClass" value="border-top-width-2 border-warning"/>
                                                    </c:if>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${lastPositionId != currentPositionId}">
                                                        <c:set var="extraClass" value=""/>
                                                        <c:set var="lastPositionId" value="${currentPositionId}"/>
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>
                                            <tr>
                                                <td class="py-05 px-0 text-center ${extraClass}">${fixturePlayer.jerseyNumber}</td>
                                                <td class="py-05 px-0 text-center ${extraClass}">${mPositions.get(fixturePlayer.positionId).getDesc(mLanguage).substring(0, 1)}</td>
                                                <td class="py-05 px-0 ${extraClass}">${mPlayers.get(fixturePlayer.playerId).knownName}</td>
                                                <td class="py-05 px-0 ${extraClass}">
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <c:if test="${fixturePlayer.getExitTime() != null}">
                                                            <span>${fixturePlayer.getExitTime()}</span>
                                                            <img width="11" class="ms-05" style="aspect-ratio: 1;" src="/sicsdataanalytics/images/matchstudio/player-out.svg">
                                                        </c:if>
                                                        <c:if test="${fixturePlayer.getJoinTime() != null}">
                                                            <span>${fixturePlayer.getJoinTime()}</span>
                                                            <img width="11" class="ms-05" style="aspect-ratio: 1;" src="/sicsdataanalytics/images/matchstudio/player-in.svg">
                                                        </c:if>
                                                    </div>
                                                </td>
                                                <td class="py-0 px-0 ${extraClass}">
                                                    <div class="d-flex align-items-center">
                                                        <c:set var="imageCounter" value="${0}"/>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(31)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(31).get(null)}">
                                                                <c:if test="${event.tagTypeIds == null || !event.tagTypeIds.contains(172)}">
                                                                    <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/yellow-card.svg">
                                                                    <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(32)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(32).get(null)}">
                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/red-card.svg">
                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(25)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(25).get(null)}">
                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball.svg">
                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(26)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(26).containsKey('159')}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(26).get('159')}">
                                                                <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                                    <c:choose>
                                                                        <c:when test="${event.playerIds.size() == 1}">
                                                                            <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball-aut.svg">
                                                                            <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <%-- Escludo Portieri --%>
                                                                            <c:if test="${fixturePlayer.positionId != 1}">
                                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball-aut.svg">
                                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                            </c:if>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:if>
                                                    </div>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-6 pe-0">
                                    <table class="table w-100">
                                        <tbody>
                                        <c:set var="lastPositionId" value="${1}"/>
                                        <c:forEach var="fixturePlayer" items="${mAwayTeamPlayers}">
                                            <c:set var="currentPositionId" value="${fixturePlayer.positionId}"/>
                                            <c:set var="extraClass" value="border-0"/>
                                            <c:choose>
                                                <c:when test="${mAwayTeamPlayers.indexOf(fixturePlayer) > 10}">
                                                    <c:if test="${mAwayTeamPlayers.indexOf(fixturePlayer) == 11}">
                                                        <c:set var="extraClass" value="border-top-width-2 border-warning"/>
                                                    </c:if>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${lastPositionId != currentPositionId}">
                                                        <c:set var="extraClass" value=""/>
                                                        <c:set var="lastPositionId" value="${currentPositionId}"/>
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>
                                            <tr>
                                                <td class="py-05 px-0 text-center ${extraClass}">${fixturePlayer.jerseyNumber}</td>
                                                <td class="py-05 px-0 text-center ${extraClass}">${mPositions.get(fixturePlayer.positionId).getDesc(mLanguage).substring(0, 1)}</td>
                                                <td class="py-05 px-0 ${extraClass}">${mPlayers.get(fixturePlayer.playerId).knownName}</td>
                                                <td class="py-05 px-0 ${extraClass}">
                                                    <div class="d-flex align-items-center justify-content-center">
                                                        <c:if test="${fixturePlayer.getExitTime() != null}">
                                                            <span>${fixturePlayer.getExitTime()}</span>
                                                            <img width="11" class="ms-05" style="aspect-ratio: 1;" src="/sicsdataanalytics/images/matchstudio/player-out.svg">
                                                        </c:if>
                                                        <c:if test="${fixturePlayer.getJoinTime() != null}">
                                                            <span>${fixturePlayer.getJoinTime()}</span>
                                                            <img width="11" class="ms-05" style="aspect-ratio: 1;" src="/sicsdataanalytics/images/matchstudio/player-in.svg">
                                                        </c:if>
                                                    </div>
                                                </td>
                                                <td class="py-0 px-0 ${extraClass}">
                                                    <div class="d-flex align-items-center">
                                                        <c:set var="imageCounter" value="${0}"/>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(31)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(31).get(null)}">
                                                                <c:if test="${event.tagTypeIds == null || !event.tagTypeIds.contains(172)}">
                                                                    <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/yellow-card.svg">
                                                                    <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(32)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(32).get(null)}">
                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/red-card.svg">
                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(25)}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(25).get(null)}">
                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball.svg">
                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                            </c:forEach>
                                                        </c:if>
                                                        <c:if test="${mGroupedEvents.get(fixturePlayer.teamId).containsKey(fixturePlayer.playerId)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).containsKey(26)
                                                                && mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(26).containsKey('159')}">
                                                            <c:forEach var="event" items="${mGroupedEvents.get(fixturePlayer.teamId).get(fixturePlayer.playerId).get(26).get('159')}">
                                                                <c:if test="${event.playerIds != null && event.playerIds.size() > 0}">
                                                                    <c:choose>
                                                                        <c:when test="${event.playerIds.size() == 1}">
                                                                            <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball-aut.svg">
                                                                            <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                        </c:when>
                                                                        <c:otherwise>
                                                                            <%-- Escludo Portieri --%>
                                                                            <c:if test="${fixturePlayer.positionId != 1}">
                                                                                <img class="${imageCounter > 0 ? 'ms-05' : ''}" width="13" src="/sicsdataanalytics/images/matchstudio/football-ball-aut.svg">
                                                                                <c:set var="imageCounter" value="${imageCounter + 1}"/>
                                                                            </c:if>
                                                                        </c:otherwise>
                                                                    </c:choose>
                                                                </c:if>
                                                            </c:forEach>
                                                        </c:if>
                                                    </div>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="text-center mt-3">
                                <img class="w-65" src="data:image/png;base64,${mFirstPageImage}">
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainHeaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 d-flex">
                                            <div>
                                                <p class="mb-0 ms-5 h4">${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</p>
                                            </div>
                                            <div class="ms-auto me-5">
                                                <p class="mb-0 me-5 h4">${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">
                                    <div class="mt-2 border border-2 border-warning">
                                        <div class="px-5 py-2">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${25}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.reti"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${5}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.tiri.totali"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${5}"/>
                                                    <c:set var="tagType" value="38"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.tiri.in.porta"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${5}"/>
                                                    <c:set var="tagType" value="29"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.tiri.bloccati"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${28}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="firstValueTmp" value="${0}"/>
                                                    <c:set var="secondValueTmp" value="${0}"/>
                                                    <c:set var="totalValueTmp" value="${0}"/>
                                                    <c:set var="eventTypeIdTmp" value="${28}"/>
                                                    <c:set var="tagTypeTmp" value="163"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="firstValueTmp" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="secondValueTmp" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValueTmp + secondValueTmp}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValueTmp / totalValueTmp * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValueTmp == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.assist.vincenti"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue} (${firstValueTmp})</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue} (${secondValueTmp})</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${100}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.tagTypeIds == null || !event.tagTypeIds.contains(2400)}">
                                                                <c:set var="firstValue" value="${firstValue + 1}"/>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.tagTypeIds == null || !event.tagTypeIds.contains(2400)}">
                                                                <c:set var="secondValue" value="${secondValue + 1}"/>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:set var="firstValueTmp" value="${0}"/>
                                                    <c:set var="secondValueTmp" value="${0}"/>
                                                    <c:set var="totalValueTmp" value="${0}"/>
                                                    <c:set var="eventTypeIdTmp" value="${100}"/>
                                                    <c:set var="tagTypeTmp" value="5463"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="firstValueTmp" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="secondValueTmp" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValueTmp + secondValueTmp}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValueTmp / totalValueTmp * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValueTmp == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.palle.laterali.riuscite"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue} (${firstValueTmp})</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue} (${secondValueTmp})</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${12}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.passaggi.chiave"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${8}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="firstValueTmp" value="${0}"/>
                                                    <c:set var="secondValueTmp" value="${0}"/>
                                                    <c:set var="totalValueTmp" value="${0}"/>
                                                    <c:set var="eventTypeIdTmp" value="${8}"/>
                                                    <c:set var="tagTypeTmp" value="5471"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="firstValueTmp" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="secondValueTmp" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValueTmp + secondValueTmp}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValueTmp / totalValueTmp * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValueTmp == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.dribbling.riusciti"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue} (${firstValueTmp})</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue} (${secondValueTmp})</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${3}"/>
                                                    <c:set var="tagType" value="15"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.corner"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100 mb-2">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${3}"/>
                                                    <c:set var="tagType" value="15"/>
                                                    <c:if test="${mFixture.homePossesso != null}">
                                                        <c:set var="firstValue" value="${mFixture.homePossesso}"/>
                                                    </c:if>
                                                    <c:if test="${mFixture.awayPossesso != null}">
                                                        <c:set var="secondValue" value="${mFixture.awayPossesso}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.possesso.palla"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}%</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}%</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-2 border border-2 border-warning">
                                        <div class="px-5 py-2">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${11}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:choose>
                                                                <c:when test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                    <c:forEach var="playerId" items="${event.playerIds}">
                                                                        <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                            && mFixturePlayers.get(playerId).positionId != null
                                                                            && mFixturePlayers.get(playerId).positionId != 1}">
                                                                            <c:set var="firstValue" value="${firstValue + 1}"/>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <c:set var="firstValue" value="${firstValue + 1}"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:choose>
                                                                <c:when test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                    <c:forEach var="playerId" items="${event.playerIds}">
                                                                        <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                            && mFixturePlayers.get(playerId).positionId != null
                                                                            && mFixturePlayers.get(playerId).positionId != 1}">
                                                                            <c:set var="secondValue" value="${secondValue + 1}"/>
                                                                        </c:if>
                                                                    </c:forEach>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <c:set var="secondValue" value="${secondValue + 1}"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.palle.recuperate"/> *</p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${11}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.half != null && event.half == 2}">
                                                                <c:choose>
                                                                    <c:when test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                        <c:forEach var="playerId" items="${event.playerIds}">
                                                                            <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                                && mFixturePlayers.get(playerId).positionId != null
                                                                                && mFixturePlayers.get(playerId).positionId != 1}">
                                                                                <c:set var="firstValue" value="${firstValue + 1}"/>
                                                                            </c:if>
                                                                        </c:forEach>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <c:set var="firstValue" value="${firstValue + 1}"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.half != null && event.half == 2}">
                                                                <c:choose>
                                                                    <c:when test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                        <c:forEach var="playerId" items="${event.playerIds}">
                                                                            <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                                && mFixturePlayers.get(playerId).positionId != null
                                                                                && mFixturePlayers.get(playerId).positionId != 1}">
                                                                                <c:set var="secondValue" value="${secondValue + 1}"/>
                                                                            </c:if>
                                                                        </c:forEach>
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        <c:set var="secondValue" value="${secondValue + 1}"/>
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.palle.recuperate.meta.avversaria"/> *</p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="firstValueTotal" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="secondValueTotal" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${11}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                <c:forEach var="playerId" items="${event.playerIds}">
                                                                    <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                            && mFixturePlayers.get(playerId).positionId != null
                                                                            && mFixturePlayers.get(playerId).positionId != 1}">
                                                                        <c:if test="${event.startPointNormalized != null && event.startPointNormalized.x != -1}">
                                                                            <c:set var="firstValue" value="${firstValue + event.startPointNormalized.x}"/>
                                                                            <c:set var="firstValueTotal" value="${firstValueTotal + 1}"/>
                                                                        </c:if>
                                                                    </c:if>
                                                                </c:forEach>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:forEach var="event" items="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${event.playerIds != null && !event.playerIds.isEmpty()}">
                                                                <c:forEach var="playerId" items="${event.playerIds}">
                                                                    <c:if test="${mFixturePlayers.containsKey(playerId)
                                                                            && mFixturePlayers.get(playerId).positionId != null
                                                                            && mFixturePlayers.get(playerId).positionId != 1}">
                                                                        <c:if test="${event.startPointNormalized != null && event.startPointNormalized.x != -1}">
                                                                            <c:set var="secondValue" value="${secondValue + event.startPointNormalized.x}"/>
                                                                            <c:set var="secondValueTotal" value="${secondValueTotal + 1}"/>
                                                                        </c:if>
                                                                    </c:if>
                                                                </c:forEach>
                                                            </c:if>
                                                        </c:forEach>
                                                    </c:if>
                                                    <c:set var="firstValue" value="${Math.round(firstValue / firstValueTotal)}"/>
                                                    <c:set var="secondValue" value="${Math.round(secondValue / secondValueTotal)}"/>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.altezza.palle.recuperate"/> *</p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${9}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="firstValueTmp" value="${firstValue}"/>
                                                    <c:set var="secondValueTmp" value="${secondValue}"/>
                                                    <c:set var="totalValueTmp" value="${0}"/>
                                                    <c:set var="eventTypeIdTmp" value="${23}"/>
                                                    <c:set var="tagTypeTmp" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="firstValueTmp" value="${firstValueTmp + mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="secondValueTmp" value="${secondValueTmp + mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValueTmp + secondValueTmp}"/>
                                                    <c:set var="firstValueTmp" value="${Math.round(firstValue / firstValueTmp * 100)}"/>
                                                    <c:set var="secondValueTmp" value="${Math.round(secondValue / secondValueTmp * 100)}"/>
                                                    <c:set var="percentage" value="${firstValueTmp}"/>
                                                    <c:if test="${totalValueTmp == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.duelli.vinti.percentuale.vittoria"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}(${firstValueTmp}%)</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}(${secondValueTmp}%)</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${9}"/>
                                                    <c:set var="tagType" value="64"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="firstValueTmp" value="${firstValue}"/>
                                                    <c:set var="secondValueTmp" value="${secondValue}"/>
                                                    <c:set var="totalValueTmp" value="${0}"/>
                                                    <c:set var="eventTypeIdTmp" value="${23}"/>
                                                    <c:set var="tagTypeTmp" value="123"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="firstValueTmp" value="${firstValueTmp + mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeIdTmp)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).containsKey(tagTypeTmp)}">
                                                        <c:set var="secondValueTmp" value="${secondValueTmp + mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeIdTmp).get(tagTypeTmp).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValueTmp + secondValueTmp}"/>
                                                    <c:set var="firstValueTmp" value="${Math.round(firstValue / firstValueTmp * 100)}"/>
                                                    <c:set var="secondValueTmp" value="${Math.round(secondValue / secondValueTmp * 100)}"/>
                                                    <c:set var="percentage" value="${firstValueTmp}"/>
                                                    <c:if test="${totalValueTmp == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.duelli.aerei.vinti.percentuale.vittoria"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}(${firstValueTmp}%)</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}(${secondValueTmp}%)</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100 mb-2">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${7}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.interventi.decisivi"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-2 border border-2 border-warning">
                                        <div class="px-5 py-2">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${19}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.falli.fatti"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${31}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.ammonizioni"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100 mt-2">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${17}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.fuorigioco"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mt-2 border border-2 border-warning">
                                        <div class="px-5 py-2">
                                            <div class="d-flex flex-column align-items-center">
                                                <div class="row w-100">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${16}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.parate"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>

                                                <div class="row w-100 mt-2">
                                                    <c:set var="firstValue" value="${0}"/>
                                                    <c:set var="secondValue" value="${0}"/>
                                                    <c:set var="totalValue" value="${0}"/>
                                                    <c:set var="eventTypeId" value="${15}"/>
                                                    <c:set var="tagType" value="${null}"/>
                                                    <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="firstValue" value="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).containsKey(null)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                        <c:set var="secondValue" value="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}"/>
                                                    </c:if>
                                                    <c:set var="totalValue" value="${firstValue + secondValue}"/>
                                                    <c:set var="percentage" value="${Math.round(firstValue / totalValue * 100 * 100) / 100}"/>
                                                    <c:if test="${totalValue == 0}">
                                                        <c:set var="totalValue" value="${1}"/>
                                                        <c:set var="percentage" value="${50}"/>
                                                    </c:if>
                                                    <p class="mb-0 fw-bold fs-sm text-center"><spring:message code="match.studio.uscite"/></p>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${firstValue}</p>
                                                    </div>
                                                    <div class="col-10">
                                                        <div class="mt-05 progress" style="height: 0.875rem;">
                                                            <div class="progress-bar progress-bar-striped red" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                            <div class="progress-bar progress-bar-striped light-gray" style="width: ${100 - percentage}%" aria-valuenow="${100 - percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                                        </div>
                                                    </div>
                                                    <div class="col-1 fw-bold fs-sm text-center px-0">
                                                        <p class="mb-0">${secondValue}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <p class="mt-2 mb-0 fs-sm text-center">* <spring:message code="match.studio.portieri.esclusi"/></p>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.classifiche"/></span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-5">
                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.assists.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${28}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.palle.laterali.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${100}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">
                                                        <c:set var="eventTypeId" value="${100}"/>
                                                        <c:set var="tagType" value="5463"/>
                                                        <c:set var="tmpValue" value="${0}"/>
                                                        <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                            <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                        </c:if>
                                                            ${playerRanking.counter}(${tmpValue})
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.passaggi.chiave.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${12}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>
                            </div>

                            <div class="row mt-5">
                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.dribbling.riusciti.totali.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${8}"/>
                                        <c:set var="tagType" value="5471"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">
                                                        <c:set var="eventTypeId" value="${8}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:set var="tmpValue" value="${0}"/>
                                                        <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                            <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                        </c:if>
                                                            ${playerRanking.counter}/${tmpValue}
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.palle.recuperate.maiusc"/> *</p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${11}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:set var="shownRows" value="0"/>
                                            <c:forEach var="playerRanking"
                                                       items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}">
                                                <c:if test="${shownRows < 10 && mFixturePlayers.containsKey(playerRanking.id)
                                                        && mFixturePlayers.get(playerRanking.id).positionId != null
                                                        && mFixturePlayers.get(playerRanking.id).positionId != 1}">
                                                    <tr>
                                                        <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                        <td class="py-0 px-1 fs-sm">
                                                            <div class="row">
                                                                <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                        ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                                </div>
                                                                <div class="col-9 p-0 ps-1 fs-xs">
                                                                        ${mPlayers.get(playerRanking.id).knownName}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="py-0 px-1 fs-sm text-center">
                                                                ${playerRanking.counter}
                                                        </td>
                                                    </tr>
                                                    <c:set var="shownRows" value="${shownRows + 1}"/>
                                                </c:if>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.palle.perse.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${10}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4"></div>
                                <div class="col-4 mt-1">
                                    <p class="mb-0 fs-sm text-center">* <spring:message code="match.studio.portieri.esclusi"/></p>
                                </div>
                                <div class="col-4"></div>
                            </div>

                            <div class="row mt-5">
                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.duelli.vinti.totali.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${9}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">
                                                        <c:set var="eventTypeId" value="${23}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:set var="tmpValue" value="${0}"/>
                                                        <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                            <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                        </c:if>
                                                            ${playerRanking.counter}/${playerRanking.counter + tmpValue}
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.falli.fatti.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${19}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:set var="shownRows" value="0"/>
                                            <c:forEach var="playerRanking"
                                                       items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}">
                                                <c:if test="${shownRows < 10 && mFixturePlayers.containsKey(playerRanking.id)
                                                        && mFixturePlayers.get(playerRanking.id).positionId != null
                                                        && mFixturePlayers.get(playerRanking.id).positionId != 1}">
                                                    <tr>
                                                        <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                        <td class="py-0 px-1 fs-sm">
                                                            <div class="row">
                                                                <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                        ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                                </div>
                                                                <div class="col-9 p-0 ps-1 fs-xs">
                                                                        ${mPlayers.get(playerRanking.id).knownName}
                                                                </div>
                                                            </div>
                                                        </td>
                                                        <td class="py-0 px-1 fs-sm text-center">
                                                                ${playerRanking.counter}
                                                        </td>
                                                    </tr>
                                                    <c:set var="shownRows" value="${shownRows + 1}"/>
                                                </c:if>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>

                                <div class="col-4">
                                    <p class="mb-1 fw-bold fs-lg text-center"><spring:message code="match.studio.falli.subiti.maiusc"/></p>
                                    <table class="table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                        </tr>
                                        </thead>
                                        <c:set var="eventTypeId" value="${24}"/>
                                        <c:set var="tagType" value="${null}"/>
                                        <c:if test="${mEventRankings.get(null).containsKey(eventTypeId)
                                                && mEventRankings.get(null).get(eventTypeId).containsKey(tagType)}">
                                            <tbody>
                                            <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                    <td class="py-0 px-1 fs-sm">
                                                        <div class="row">
                                                            <div class="col-3 p-0 fw-bold text-center ${playerRanking.teamId == mFixture.homeTeamId ? 'red' : 'gray'}">
                                                                    ${mTeams.get(playerRanking.teamId).getName(mLanguage).substring(0, 3)}
                                                            </div>
                                                            <div class="col-9 p-0 ps-1 fs-xs">
                                                                    ${mPlayers.get(playerRanking.id).knownName}
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                </tr>
                                            </c:forEach>
                                            </tbody>
                                        </c:if>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.posizioni.medie.giocatori"/></span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <p class="mb-0 text-center fs-2 fw-bold">${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</p>
                                    <div class="mt-2 text-center">
                                        <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam1}">
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-6 pe-1">
                                            <p class="mb-0 text-center fw-bold"><spring:message code="match.studio.primo.tempo"/></p>
                                            <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam11T}">
                                        </div>
                                        <div class="col-6 ps-1">
                                            <p class="mb-0 text-center fw-bold"><spring:message code="match.studio.secondo.tempo"/></p>
                                            <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam12T}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <p class="mb-0 text-center fs-2 fw-bold">${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</p>
                                    <div class="mt-2 text-center">
                                        <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam2}">
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-6 pe-1">
                                            <p class="mb-0 text-center fw-bold"><spring:message code="match.studio.primo.tempo"/></p>
                                            <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam21T}">
                                        </div>
                                        <div class="col-6 ps-1">
                                            <p class="mb-0 text-center fw-bold"><spring:message code="match.studio.secondo.tempo"/></p>
                                            <img class="w-100" src="data:image/png;base64,${mPosizioneMediaTeam22T}">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.distribuzione.passaggi"/></span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <p class="mb-0 text-center fs-2 fw-bold">${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</p>
                                    <div class="mt-2 text-center">
                                        <img class="w-75" src="data:image/png;base64,${mPosizioneMediaTeam1Orizzontale}">
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <div class="w-100" style="width: ${mHeatMapPassesT1Width}px; height: ${mHeatMapPassesT1Height}px" id="heatMapPassesHome"></div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.distribuzione.passaggi"/></span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <p class="mb-0 text-center fs-2 fw-bold">${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</p>
                                    <div class="mt-2 text-center">
                                        <img class="w-75" src="data:image/png;base64,${mPosizioneMediaTeam2Orizzontale}">
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <div class="w-100" style="width: ${mHeatMapPassesT2Width}px; height: ${mHeatMapPassesT2Height}px" id="heatMapPassesAway"></div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.eventi"/> - ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${19}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.falli.fatti.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisFoulsT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${24}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.falli.subiti.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisFoulsConcededT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${10}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.palle.perse.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisPossessionLostT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${11}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.palle.recuperate.maiusc"/> * <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisBallRecoveryT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:set var="shownRows" value="0"/>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${shownRows < 15 && mFixturePlayers.containsKey(playerRanking.id)
                                                                    && mFixturePlayers.get(playerRanking.id).positionId != null
                                                                    && mFixturePlayers.get(playerRanking.id).positionId != 1}">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                </tr>
                                                                <c:set var="shownRows" value="${shownRows + 1}"/>
                                                            </c:if>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="mt-1 mb-0 fs-sm">* <spring:message code="match.studio.portieri.esclusi"/></p>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${9}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <c:set var="secondEventTypeId" value="${23}"/>
                                    <c:set var="secondTagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.duelli.vinti.totali.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()} / ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size() + mGroupedEvents.get(mFixture.homeTeamId).get(null).get(secondEventTypeId).get(secondTagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisDuelsT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(secondEventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).containsKey(secondTagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).get(secondTagType).size()}"/>
                                                                    </c:if>
                                                                        ${playerRanking.counter} / ${playerRanking.counter + tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 fs-sm">Duels Won</span>
                                            </div>
                                        </div>
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 fs-sm">Duels Lost</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${8}"/>
                                    <c:set var="tagType" value="5471"/>
                                    <c:set var="secondEventTypeId" value="${8}"/>
                                    <c:set var="secondTagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.dribbling.riusciti.totali.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()} / ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size() + mGroupedEvents.get(mFixture.homeTeamId).get(null).get(secondEventTypeId).get(secondTagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisDribblingsT1}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(secondEventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).containsKey(secondTagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).get(secondTagType).size()}"/>
                                                                    </c:if>
                                                                        ${playerRanking.counter} / ${tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 fs-sm"><spring:message code="match.studio.dribbling.riusciti"/></span>
                                            </div>
                                        </div>
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 fs-sm"><spring:message code="match.studio.dribbling.non.riusciti"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.eventi"/> - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${19}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.falli.fatti.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisFoulsT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${24}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.falli.subiti.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisFoulsConcededT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${10}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.palle.perse.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisPossessionLostT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${11}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.palle.recuperate.maiusc"/> * <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisBallRecoveryT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:set var="shownRows" value="0"/>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}">
                                                            <c:if test="${shownRows < 15 && mFixturePlayers.containsKey(playerRanking.id)
                                                                    && mFixturePlayers.get(playerRanking.id).positionId != null
                                                                    && mFixturePlayers.get(playerRanking.id).positionId != 1}">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                </tr>
                                                                <c:set var="shownRows" value="${shownRows + 1}"/>
                                                            </c:if>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <p class="mt-1 mb-0 fs-sm">* <spring:message code="match.studio.portieri.esclusi"/></p>
                                </div>
                            </div>

                            <div class="row mt-2">
                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${9}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <c:set var="secondEventTypeId" value="${23}"/>
                                    <c:set var="secondTagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.duelli.vinti.totali.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()} / ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size() + mGroupedEvents.get(mFixture.awayTeamId).get(null).get(secondEventTypeId).get(secondTagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisDuelsT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(secondEventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).containsKey(secondTagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).get(secondTagType).size()}"/>
                                                                    </c:if>
                                                                        ${playerRanking.counter} / ${playerRanking.counter + tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 fs-sm">Duels Won</span>
                                            </div>
                                        </div>
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 fs-sm">Duels Lost</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-6">
                                    <c:set var="eventTypeId" value="${8}"/>
                                    <c:set var="tagType" value="5471"/>
                                    <c:set var="secondEventTypeId" value="${8}"/>
                                    <c:set var="secondTagType" value="${null}"/>
                                    <p class="mb-0 fw-bold">
                                        <spring:message code="match.studio.dribbling.riusciti.totali.maiusc"/> <c:if test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId) != null && !mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).isEmpty()}">(${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()} / ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size() + mGroupedEvents.get(mFixture.awayTeamId).get(null).get(secondEventTypeId).get(secondTagType).size()})</c:if>
                                    </p>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-6">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisDribblingsT2}">
                                            </div>
                                            <div class="col-6 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.tot"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="14">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(secondEventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).containsKey(secondTagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(secondEventTypeId).get(secondTagType).size()}"/>
                                                                    </c:if>
                                                                        ${playerRanking.counter} / ${tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 fs-sm"><spring:message code="match.studio.dribbling.riusciti"/></span>
                                            </div>
                                        </div>
                                        <div class="col-6 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 fs-sm"><spring:message code="match.studio.dribbling.non.riusciti"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.passaggi.chiave"/> - ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <c:set var="eventTypeId" value="${12}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-5">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisKillerPassesT1}">
                                            </div>
                                            <div class="col-7 p-0">
                                                <div class="h-50">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.assists.maiusc"/></th>
                                                        </tr>
                                                        </thead>
                                                        <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                                && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                            <tbody>
                                                            <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">
                                                                        <c:set var="tagType" value="75"/>
                                                                        <c:set var="tmpValue" value="${0}"/>
                                                                        <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                                && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                            <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                        </c:if>
                                                                            ${tmpValue}
                                                                    </td>
                                                                </tr>
                                                            </c:forEach>
                                                            </tbody>
                                                        </c:if>
                                                    </table>
                                                </div>

                                                <div class="h-50">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.ricevuti.maiusc"/></th>
                                                        </tr>
                                                        </thead>
                                                        <c:if test="${!mEventsAnalysisReceivedKillerPassesRankingT1.isEmpty()}">
                                                            <tbody>
                                                            <c:forEach var="playerRanking" items="${mEventsAnalysisReceivedKillerPassesRankingT1}" begin="0" end="9">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                </tr>
                                                            </c:forEach>
                                                            </tbody>
                                                        </c:if>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-5">
                                            <div class="row">
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.passaggi.chiave"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.assists"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <c:set var="eventTypeId" value="${100}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-5">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisSideBallsT1}">
                                            </div>
                                            <div class="col-7 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.riuscite.maiusc"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tagType" value="5463"/>
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                    </c:if>
                                                                        ${tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-5">
                                            <div class="row">
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                        <span class="ms-1 fs-sm">Side Balls</span>
                                                    </div>
                                                </div>
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/cyan-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.palle.laterali.riuscite.2"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <img class="w-100" src="data:image/png;base64,${mEventsAnalysisTimeLineT1}">
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.passaggi.chiave"/> - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <c:set var="eventTypeId" value="${12}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-5">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisKillerPassesT2}">
                                            </div>
                                            <div class="col-7 p-0">
                                                <div class="h-50">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.assists.maiusc"/></th>
                                                        </tr>
                                                        </thead>
                                                        <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                                && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                            <tbody>
                                                            <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">
                                                                        <c:set var="tagType" value="75"/>
                                                                        <c:set var="tmpValue" value="${0}"/>
                                                                        <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                                && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                            <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                        </c:if>
                                                                            ${tmpValue}
                                                                    </td>
                                                                </tr>
                                                            </c:forEach>
                                                            </tbody>
                                                        </c:if>
                                                    </table>
                                                </div>

                                                <div class="h-50">
                                                    <table class="table table-striped table-hover">
                                                        <thead>
                                                        <tr>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.ricevuti.maiusc"/></th>
                                                        </tr>
                                                        </thead>
                                                        <c:if test="${!mEventsAnalysisReceivedKillerPassesRankingT2.isEmpty()}">
                                                            <tbody>
                                                            <c:forEach var="playerRanking" items="${mEventsAnalysisReceivedKillerPassesRankingT2}" begin="0" end="9">
                                                                <tr>
                                                                    <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                    <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                    <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                </tr>
                                                            </c:forEach>
                                                            </tbody>
                                                        </c:if>
                                                    </table>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-5">
                                            <div class="row">
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.passaggi.chiave"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.assists"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <c:set var="eventTypeId" value="${100}"/>
                                    <c:set var="tagType" value="${null}"/>
                                    <div class="mt-1">
                                        <div class="row">
                                            <div class="col-5">
                                                <img class="w-100" src="data:image/png;base64,${mEventsAnalysisSideBallsT2}">
                                            </div>
                                            <div class="col-7 p-0">
                                                <table class="table table-striped table-hover">
                                                    <thead>
                                                    <tr>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                        <th scope="col" class="text-center p-1"><spring:message code="match.studio.riuscite.maiusc"/></th>
                                                    </tr>
                                                    </thead>
                                                    <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                            && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                        <tbody>
                                                        <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                            <tr>
                                                                <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                                <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                                <td class="py-0 px-1 fs-sm text-center">
                                                                    <c:set var="tagType" value="5463"/>
                                                                    <c:set var="tmpValue" value="${0}"/>
                                                                    <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                        <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                    </c:if>
                                                                        ${tmpValue}
                                                                </td>
                                                            </tr>
                                                        </c:forEach>
                                                        </tbody>
                                                    </c:if>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row mt-1 px-3">
                                        <div class="col-5">
                                            <div class="row">
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                        <span class="ms-1 fs-sm">Side Balls</span>
                                                    </div>
                                                </div>
                                                <div class="col-6 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/cyan-dot.svg">
                                                        <span class="ms-1 fs-sm"><spring:message code="match.studio.palle.laterali.riuscite.2"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <img class="w-100" src="data:image/png;base64,${mEventsAnalysisTimeLineT2}">
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.tiri"/> - ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.dettagli.maiusc"/></h3>

                                    <div class="row">
                                        <div class="col-6 px-4">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.riepilogo.eventi"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.totali"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.in.porta"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="38"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.bloccati"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="29"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.fuori"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="39"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.da.area"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="40"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.da.fuori.area"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="41"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.reti"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${25}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.percentuale.realizzativa"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.homeTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${Math.round(mFixture.homeScore / mGroupedEvents.get(mFixture.homeTeamId).get(null).get(eventTypeId).get(tagType).size() * 100)}%
                                                            </c:when>
                                                            <c:otherwise>0%</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-6 px-4">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.metriche.avanzate.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected <spring:message code="match.studio.reti"/> (xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1003}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected <spring:message code="match.studio.reti"/> on Target (xGoT)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1004}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected Assist (xA)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1005}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected Assist Shot (xAs)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1006}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Shot Quality (xGoT - xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1004}"/>
                                                        <c:set var="secondStatsTypeId" value="${1003}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)
                                                                        && mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(secondStatsTypeId)}">
                                                                ${Math.round(((mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000) - (mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(secondStatsTypeId) / 1000)) * 1000) / 1000}
                                                            </c:when>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(secondStatsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(secondStatsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Against xGoT (AxGoT)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1012}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Open Play Expected Goal (op_xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1016}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.posizioni.maiusc"/></h3>

                                    <div class="row">
                                        <div class="col-6 px-4">
                                            <h3 class="mb-0 text-center"><spring:message code="match.studio.tiri.maiusc"/></h3>
                                            <img class="w-100" src="data:image/png;base64,${mShotAnalysisT1Shots}">
                                            <div class="mt-1 row">
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.in.porta"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.fuori"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/gray-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.bloccati"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <table class="mt-1 table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.dentro.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.fuori.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1">xG/xGoT</th>
                                                </tr>
                                                </thead>
                                                <c:set var="eventTypeId" value="${5}"/>
                                                <c:set var="tagType" value="${null}"/>
                                                <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                        && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                    <tbody>
                                                    <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                        <tr>
                                                            <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                            <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                            <td class="py-0 px-1 fs-sm text-center">
                                                                <c:set var="eventTypeId" value="${5}"/>
                                                                <c:set var="tagType" value="38"/>
                                                                <c:set var="tmpValue" value="${0}"/>
                                                                <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                        && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                    <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                </c:if>
                                                                    ${tmpValue}
                                                            </td>
                                                            <td class="py-0 px-1 fs-sm text-center">
                                                                <c:set var="eventTypeId" value="${5}"/>
                                                                <c:set var="tagType" value="39"/>
                                                                <c:set var="tmpValue" value="${0}"/>
                                                                <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                        && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                    <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                </c:if>
                                                                    ${tmpValue}
                                                            </td>
                                                            <td class="py-0 px-1 fs-xs text-center">
                                                                <c:set var="statsTypeId" value="${1003}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                                /
                                                                <c:set var="statsTypeId" value="${1004}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                    </tbody>
                                                </c:if>
                                            </table>
                                        </div>

                                        <div class="col-6 px-4">
                                            <h3 class="mb-0 text-center"><spring:message code="match.studio.assists.maiusc"/></h3>
                                            <img class="w-100" src="data:image/png;base64,${mShotAnalysisT1Assists}">
                                            <div class="mt-1 row">
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.assists"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <table class="mt-1 table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.assists.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1">xA/xAs</th>
                                                </tr>
                                                </thead>
                                                <c:set var="eventTypeId" value="${28}"/>
                                                <c:set var="tagType" value="${null}"/>
                                                <c:if test="${mEventRankings.get(mFixture.homeTeamId).containsKey(eventTypeId)
                                                        && mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).containsKey(tagType)}">
                                                    <tbody>
                                                    <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.homeTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                        <tr>
                                                            <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                            <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                            <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            <td class="py-0 px-1 fs-xs text-center">
                                                                <c:set var="statsTypeId" value="${1005}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                                /
                                                                <c:set var="statsTypeId" value="${1006}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.homeTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                    </tbody>
                                                </c:if>
                                            </table>
                                        </div>
                                    </div>

                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.timeline.maiusc"/></h3>

                                    <img class="w-100" src="data:image/png;base64,${mShotAnalysisT1Timeline}">
                                    <div class="mt-1 row">
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.in.porta"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.fuori"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/gray-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.bloccati"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.analisi.tiri"/> - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col">
                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.dettagli.maiusc"/></h3>

                                    <div class="row">
                                        <div class="col-6 px-4">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.riepilogo.eventi"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.totali"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.in.porta"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="38"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.bloccati"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="29"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.fuori"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="39"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.da.area"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="40"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.tiri.da.fuori.area"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="41"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.reti"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${25}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size()}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm"><spring:message code="match.studio.percentuale.realizzativa"/></td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="eventTypeId" value="${5}"/>
                                                        <c:set var="tagType" value="${null}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedEvents.get(mFixture.awayTeamId).get(null).containsKey(eventTypeId)
                                                                            && mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).containsKey(tagType)}">
                                                                ${Math.round(mFixture.awayScore / mGroupedEvents.get(mFixture.awayTeamId).get(null).get(eventTypeId).get(tagType).size() * 100)}%
                                                            </c:when>
                                                            <c:otherwise>0%</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="col-6 px-4">
                                            <table class="table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.metriche.avanzate.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.totale.maiusc"/></th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected <spring:message code="match.studio.reti"/> (xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1003}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected <spring:message code="match.studio.reti"/> on Target (xGoT)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1004}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected Assist (xA)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1005}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Expected Assist Shot (xAs)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1006}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Shot Quality (xGoT - xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1004}"/>
                                                        <c:set var="secondStatsTypeId" value="${1003}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)
                                                                        && mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(secondStatsTypeId)}">
                                                                ${Math.round(((mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000) - (mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(secondStatsTypeId) / 1000)) * 1000) / 1000}
                                                            </c:when>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(secondStatsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(secondStatsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Against xGoT (AxGoT)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1012}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="py-0 px-1 fs-sm">Open Play Expected Goal (op_xG)</td>
                                                    <td class="py-0 px-1 fs-xs text-center">
                                                        <c:set var="statsTypeId" value="${1016}"/>
                                                        <c:choose>
                                                            <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).containsKey(statsTypeId)}">
                                                                ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(null).get(statsTypeId) / 1000}
                                                            </c:when>
                                                            <c:otherwise>0</c:otherwise>
                                                        </c:choose>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.posizioni.maiusc"/></h3>

                                    <div class="row">
                                        <div class="col-6 px-4">
                                            <h3 class="mb-0 text-center"><spring:message code="match.studio.tiri.maiusc"/></h3>
                                            <img class="w-100" src="data:image/png;base64,${mShotAnalysisT2Shots}">
                                            <div class="mt-1 row">
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.in.porta"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.fuori"/></span>
                                                    </div>
                                                </div>
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/gray-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.tiri.bloccati"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <table class="mt-1 table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.dentro.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.fuori.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1">xG/xGoT</th>
                                                </tr>
                                                </thead>
                                                <c:set var="eventTypeId" value="${5}"/>
                                                <c:set var="tagType" value="${null}"/>
                                                <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                        && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                    <tbody>
                                                    <c:forEach var="playerRanking" items="${mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                        <tr>
                                                            <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                            <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                            <td class="py-0 px-1 fs-sm text-center">
                                                                <c:set var="eventTypeId" value="${5}"/>
                                                                <c:set var="tagType" value="38"/>
                                                                <c:set var="tmpValue" value="${0}"/>
                                                                <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                        && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                    <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                </c:if>
                                                                    ${tmpValue}
                                                            </td>
                                                            <td class="py-0 px-1 fs-sm text-center">
                                                                <c:set var="eventTypeId" value="${5}"/>
                                                                <c:set var="tagType" value="39"/>
                                                                <c:set var="tmpValue" value="${0}"/>
                                                                <c:if test="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).containsKey(eventTypeId)
                                                                        && mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).containsKey(tagType)}">
                                                                    <c:set var="tmpValue" value="${mGroupedEvents.get(playerRanking.teamId).get(playerRanking.id).get(eventTypeId).get(tagType).size()}"/>
                                                                </c:if>
                                                                    ${tmpValue}
                                                            </td>
                                                            <td class="py-0 px-1 fs-xs text-center">
                                                                <c:set var="statsTypeId" value="${1003}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                                /
                                                                <c:set var="statsTypeId" value="${1004}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                    </tbody>
                                                </c:if>
                                            </table>
                                        </div>

                                        <div class="col-6 px-4">
                                            <h3 class="mb-0 text-center"><spring:message code="match.studio.assists.maiusc"/></h3>
                                            <img class="w-100" src="data:image/png;base64,${mShotAnalysisT2Assists}">
                                            <div class="mt-1 row">
                                                <div class="col-4 px-1">
                                                    <div class="d-flex justify-content-center align-items-center">
                                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                        <span class="ms-1 text-small"><spring:message code="match.studio.assists"/></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <table class="mt-1 table table-striped table-hover">
                                                <thead>
                                                <tr>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.numero"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.giocatore.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1"><spring:message code="match.studio.assists.maiusc"/></th>
                                                    <th scope="col" class="text-center p-1">xA/xAs</th>
                                                </tr>
                                                </thead>
                                                <c:set var="eventTypeId" value="${28}"/>
                                                <c:set var="tagType" value="${null}"/>
                                                <c:if test="${mEventRankings.get(mFixture.awayTeamId).containsKey(eventTypeId)
                                                        && mEventRankings.get(mFixture.awayTeamId).get(eventTypeId).containsKey(tagType)}">
                                                    <tbody>
                                                    <c:forEach var="playerRanking" items="${mEventRankings.get(null).get(eventTypeId).get(tagType)}" begin="0" end="9">
                                                        <tr>
                                                            <td class="py-0 px-1 fs-sm text-center">${mFixturePlayers.get(playerRanking.id).jerseyNumber}</td>
                                                            <td class="py-0 px-1 fs-xs">${mPlayers.get(playerRanking.id).knownName}</td>
                                                            <td class="py-0 px-1 fs-sm text-center">${playerRanking.counter}</td>
                                                            <td class="py-0 px-1 fs-xs text-center">
                                                                <c:set var="statsTypeId" value="${1005}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                                /
                                                                <c:set var="statsTypeId" value="${1006}"/>
                                                                <c:choose>
                                                                    <c:when test="${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).containsKey(statsTypeId)}">
                                                                        ${mGroupedAdvancedMetrics.get(mFixture.awayTeamId).get(playerRanking.id).get(statsTypeId) / 1000}
                                                                    </c:when>
                                                                    <c:otherwise>0</c:otherwise>
                                                                </c:choose>
                                                            </td>
                                                        </tr>
                                                    </c:forEach>
                                                    </tbody>
                                                </c:if>
                                            </table>
                                        </div>
                                    </div>

                                    <h3 class="mb-0 text-center"><spring:message code="match.studio.timeline.maiusc"/></h3>

                                    <img class="w-100" src="data:image/png;base64,${mShotAnalysisT2Timeline}">
                                    <div class="mt-1 row">
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.in.porta"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/red-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.fuori"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/gray-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.tiri.bloccati"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <c:if test="${mSkipOI == null || !mSkipOI}">
                        <div class="page">
                            <div class="mainheaderQuad">
                                <div class="row px-2">
                                    <div class="col-1 px-0 red">
                                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                                        </div>
                                    </div>
                                    <div class="col-11 px-0">
                                        <div class="d-flex align-items-center black">
                                            <div class="ps-5 text-white fw-bold">
                                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                                <br/>
                                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                                            </div>
                                            <div class="pe-5 ms-auto">
                                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                                            </div>
                                        </div>
                                        <div class="mt-2 ms-2 d-flex">
                                            <span class="fw-bold fs-lg"><spring:message code="match.studio.indice.di.pericolosita"/></span>
                                        </div>
                                        <div class="mt-1 d-flex">
                                            <div style="width: 50%; height: 5px;" class="black"></div>
                                            <div style="width: 10%; height: 5px;" class="red"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-4">
                                    <h5 class="mb-0 text-center">${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}: ${Math.round(mOITotalT1)}</h5>
                                    <table class="mt-1 table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.riepilogo.eventi"/></th>
                                            <th scope="col" class="text-center p-1">*<spring:message code="match.studio.su.azione.sigla"/></th>
                                            <th scope="col" class="text-center p-1">*<spring:message code="match.studio.su.palla.inattiva.sigla"/></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <c:set var="eventTypeId" value="${0}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${10}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${1}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${11}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${8}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${15}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${6}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${13}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${9}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${16}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${2}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${19}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${3}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${20}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${4}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${21}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${5}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${12}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.homeTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.homeTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-0 px-1 fs-xs"></td>
                                            <td class="py-0 px-1 h5 text-center" colspan="2"><spring:message code="match.studio.punti"/></td>
                                        </tr>
                                        <tr>
                                            <td class="py-0 px-1 fs-xs"><spring:message code="match.studio.indice.di.pericolosita"/></td>
                                            <td class="py-0 px-1 fs-xs text-center">${Math.round(mOITotalOnOpenPlayT1)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">${Math.round(mOITotalOnSetPiecesT1)}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="col-4">
                                    <div id="offensiveIndexColumnChart"></div>
                                    <div class="mt-1 row">
                                        <div class="col-6">
                                            <h3 class="mb-0 text-center">${mFixture.homeScore}</h3>
                                        </div>
                                        <div class="col-6">
                                            <h3 class="mb-0 text-center">${mFixture.awayScore}</h3>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <p class="mb-0 fs-xs">*<spring:message code="match.studio.su.azione.sigla"/> = <spring:message code="match.studio.su.azione"/></p>
                                        </div>
                                        <div class="col-6">
                                            <p class="mb-0 fs-xs">*<spring:message code="match.studio.su.palla.inattiva.sigla"/> = <spring:message code="match.studio.su.palla.inattiva"/></p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <h5 class="mb-0 text-center">${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}: ${Math.round(mOITotalT2)}</h5>
                                    <table class="mt-1 table table-striped table-hover">
                                        <thead>
                                        <tr>
                                            <th scope="col" class="text-center p-1"><spring:message code="match.studio.riepilogo.eventi"/></th>
                                            <th scope="col" class="text-center p-1">*<spring:message code="match.studio.su.azione.sigla"/></th>
                                            <th scope="col" class="text-center p-1">*<spring:message code="match.studio.su.palla.inattiva.sigla"/></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <tr>
                                            <c:set var="eventTypeId" value="${0}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${10}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${1}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${11}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${8}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${15}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${6}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${13}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${9}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${16}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${2}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${19}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${3}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${20}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${4}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${21}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <c:set var="eventTypeId" value="${5}"/>
                                            <td class="py-0 px-1 fs-xs">${mOIGroupedEventTypes.get(eventTypeId).getDesc(mLanguage)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                            <c:set var="eventTypeId" value="${12}"/>
                                            <td class="py-0 px-1 fs-xs text-center">
                                                <c:choose>
                                                    <c:when test="${mOIGroupedEvents.get(mFixture.awayTeamId).containsKey(eventTypeId)}">
                                                        ${mOIGroupedEvents.get(mFixture.awayTeamId).get(eventTypeId).value}
                                                    </c:when>
                                                    <c:otherwise>-</c:otherwise>
                                                </c:choose>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td class="py-0 px-1 fs-xs"></td>
                                            <td class="py-0 px-1 h5 text-center" colspan="2"><spring:message code="match.studio.punti"/></td>
                                        </tr>
                                        <tr>
                                            <td class="py-0 px-1 fs-xs"><spring:message code="match.studio.indice.di.pericolosita"/></td>
                                            <td class="py-0 px-1 fs-xs text-center">${Math.round(mOITotalOnOpenPlayT2)}</td>
                                            <td class="py-0 px-1 fs-xs text-center">${Math.round(mOITotalOnSetPiecesT2)}</td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <div class="mt-3 row">
                                <div id="offensiveIndexTrendChart"></div>
                                </div>
                                <div class="row">
                                    <div class="col-6 px-5">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <img width="13" src="/sicsdataanalytics/images/matchstudio/football-ball-aut.svg">
                                            <span class="ms-1"><spring:message code="match.studio.reti"/> - ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)}</span>
                                    </div>
                                </div>
                                <div class="col-6 px-5">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <img width="13" src="/sicsdataanalytics/images/matchstudio/football-ball.svg">
                                        <span class="ms-1"><spring:message code="match.studio.reti"/> - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        </c:if>
                    </c:if>

                    <%@ include file="matchStudioPlayers.jsp" %>
                </div>
            </div>
        </div>
        <!-- /inner content -->
    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
</body>