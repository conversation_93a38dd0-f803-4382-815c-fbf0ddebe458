<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<c:forEach var="playerId" items="${mPlayerIds}" varStatus="counter">
    <c:if test="${mPlayerDataMap.get(playerId).mFixturePlayer.playTime > 0}">
        <div class="page">
            <div class="mainheaderQuad">
                <div class="row px-2">
                    <div class="col-1 px-0 red">
                        <div class="d-flex align-items-center justify-content-center h-100 py-1">
                            <p class="mb-0 fw-bold fs-sm vertical-text">Match.Studio</p>
                        </div>
                    </div>
                    <div class="col-11 px-0">
                        <div class="d-flex align-items-center black">
                            <div class="ps-5 text-white fw-bold">
                                    ${mTeams.get(mFixture.homeTeamId).getName(mLanguage)} - ${mTeams.get(mFixture.awayTeamId).getName(mLanguage)}
                                <br/>
                                    ${mCompetition.getName(mLanguage)} | <spring:message code="match.studio.giornata"/> ${mFixture.matchDay} | ${mFixture.gameDateString}
                            </div>
                            <div class="pe-5 ms-auto">
                                <img height="20" width="80" src="/sicsdataanalytics/images/logosics.png">
                            </div>
                        </div>
                        <div class="ms-2 d-flex align-items-center">
                            <img height="40" width="40" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mPlayerDataMap.get(playerId).mFixturePlayer.teamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'" crossorigin="anonymous">
                            <div>
                                <span class="fw-bold fs-lg">${mPlayerDataMap.get(playerId).mFixturePlayer.jerseyNumber} - ${mPlayerDataMap.get(playerId).mPlayer.knownName} (${mPositions.get(mPlayerDataMap.get(playerId).mFixturePlayer.positionId).getDesc(mLanguage).substring(0, 1)}) - ${Math.round(mPlayerDataMap.get(playerId).mFixturePlayer.playTime / 60.0)}'</span>
                                <br/>
                                <span>${mTeams.get(mPlayerDataMap.get(playerId).mFixturePlayer.teamId).getName(mLanguage)}</span>
                            </div>
                            <c:if test="${counter.count == 1 && mFirstPageImage == null}">
                                <div class="dropdown ms-auto hide-on-print">
                                    <button class="btn btn-sm btn-info dropdown-toggle" href="#" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                        <spring:message code="match.studio.save.pdf"/>
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('${mPlayerDataMap.get(playerId).mPlayer.knownName}-Player-Report', 'low')"><spring:message code="match.studio.export.quality.low"/></a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('${mPlayerDataMap.get(playerId).mPlayer.knownName}-Player-Report', 'medium')"><spring:message code="match.studio.export.quality.medium"/></a></li>
                                        <li><a class="dropdown-item" href="#" onclick="exportReport('${mPlayerDataMap.get(playerId).mPlayer.knownName}-Player-Report', 'high')"><spring:message code="match.studio.export.quality.high"/></a></li>
                                    </ul>
                                </div>
                            </c:if>
                        </div>
                        <div class="d-flex">
                            <div style="width: 50%; height: 5px;" class="black"></div>
                            <div style="width: 10%; height: 5px;" class="red"></div>
                        </div>
                    </div>
                </div>
            </div>

            <c:if test="${mPlayerDataMap.get(playerId).mPositionId == 1}">
                <div class="row mt-2">
                    <div class="col-6">
                        <table class="table table-hover table-striped w-100">
                            <thead>
                            <tr>
                                <th class="p-05 w-50"></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab1Table}">
                                <tr>
                                    <td class="p-05 table-column fw-bold">${row.text}</td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(0)}
                                    </td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(1)}
                                    </td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(2)}
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-6">
                        <div class="d-flex">
                            <div class="col-8">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab2Table}">
                                        <tr>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab2ThirdImage}">
                                <div class="row mt-1 px-3">
                                    <div class="col-4 px-1">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                            <span class="ms-1 text-small"><spring:message code="match.studio.parata"/></span>
                                        </div>
                                    </div>
                                    <div class="col-4 px-1">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/close-cross24.png">
                                            <span class="ms-1 text-small"><spring:message code="match.studio.fuori.porta"/></span>
                                        </div>
                                    </div>
                                    <div class="col-4 px-1">
                                        <div class="d-flex justify-content-center align-items-center">
                                            <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/ball24.png">
                                            <span class="ms-1 text-small"><spring:message code="match.studio.gol"/></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 ps-1">
                                <p class="mb-0 fs-xs fw-bold text-center text-uppercase"><spring:message code="match.studio.primo.tempo"/></p>
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab2FirstImage}">
                                <p class="mt-4 mb-0 fs-xs fw-bold text-center text-uppercase"><spring:message code="match.studio.secondo.tempo"/></p>
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab2SecondImage}">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.passaggi.e.lanci"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-16px"></th>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab3Table}">
                                        <tr>
                                            <td class="p-05 table-column text-center table-column-black">${row.number}</td>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-5 mt-1">
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab3Image}">
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.rinvii"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="d-flex flex-column col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-16px"></th>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab4Table}">
                                        <tr>
                                            <td class="p-05 table-column text-center table-column-black">${row.number}</td>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                                <div class="mt-auto">
                                    <div class="row">
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.mano"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.dal.fondo"/></span>
                                            </div>
                                        </div>
                                        <div class="col-4 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/cyan-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.piede"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-5 mt-1">
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab4Image}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.palle.laterali"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col">
                                <div class="d-flex flex-column justify-content-center align-items-center">
                                    <p class="mb-0 fs-xs fw-bold text-center text-uppercase"><spring:message code="match.studio.primo.tempo"/></p>
                                    <img class="w-50" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab5FirstImage}">
                                    <p class="mb-0 fs-xs fw-bold text-center text-uppercase"><spring:message code="match.studio.secondo.tempo"/></p>
                                    <img class="w-50" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab5SecondImage}">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.passaggi.fatti"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-16px"></th>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab6Table}">
                                        <tr>
                                            <td class="p-05 table-column text-center table-column-black">${row.number}</td>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-5 mt-1">
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab6Image}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.heatmap.tocchi"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="d-flex col mt-1 justify-content-center">
                                <img class="w-75" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab7Image}">
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.confronto.tocchi.giocatore.media.squadra"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col mt-1">
                                <div class="w-100" style="height: 200px" id="chartdiv${playerId}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <c:if test="${mPlayerDataMap.get(playerId).mPositionId != 1}">
                <div class="row mt-2">
                    <div class="col-6">
                        <table class="table table-hover table-striped w-100">
                            <thead>
                            <tr>
                                <th class="p-05 w-50"></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab1Table}">
                                <tr>
                                    <td class="p-05 table-column fw-bold">${row.text}</td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(0)}
                                    </td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(1)}
                                    </td>
                                    <td class="p-05 table-column text-center">
                                            ${row.getValuesText(2)}
                                    </td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                    <div class="col-6">
                        <div class="col mt-1">
                            <div class="w-100" style="height: 200px" id="chartradardiv${playerId}"></div>
                        </div>

                        <div class="mt-auto">
                            <div class="row">
                                <div class="col-6 px-1">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/dark-orange-dot.svg">
                                        <span class="ms-1 text-small text-uppercase"><spring:message code="match.studio.giocatore"/></span>
                                    </div>
                                </div>
                                <div class="col-6 px-1">
                                    <div class="d-flex justify-content-center align-items-center">
                                        <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/gray-dot.svg">
                                        <span class="ms-1 text-small text-uppercase"><spring:message code="match.studio.media.squadra"/></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.passaggi.fatti"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-16px"></th>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab3Table}">
                                        <tr>
                                            <td class="p-05 table-column text-center table-column-black">${row.number}</td>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-5 mt-1">
                                <div id="tab3Image${playerId}" class="w-100">

                                </div>
                                <%--<img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab3Image}">--%>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.passaggi.ricevuti"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="d-flex flex-column col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-16px"></th>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab4Table}">
                                        <tr>
                                            <td class="p-05 table-column text-center table-column-black">${row.number}</td>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-5 mt-1">
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab4Image}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.duelli.e.dribbling"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="d-flex flex-column col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab5Table}">
                                        <tr>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                                <div class="mt-auto">
                                    <div class="row">
                                        <div class="col-3 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/yellow-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.duelli.vinti"/></span>
                                            </div>
                                        </div>
                                        <div class="col-3 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/orange-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.duelli.persi"/></span>
                                            </div>
                                        </div>
                                        <div class="col-3 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/blue-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.dribbling.vinti"/></span>
                                            </div>
                                        </div>
                                        <div class="col-3 px-1">
                                            <div class="d-flex justify-content-center align-items-center">
                                                <img width="12" height="12" src="/sicsdataanalytics/images/matchstudio/cyan-dot.svg">
                                                <span class="ms-1 text-small"><spring:message code="match.studio.dribbling.persi"/></span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-5 mt-1">
                                <div id="tab5Image${playerId}" class="w-100">

                                </div>
                                <%--<img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab5Image}">--%>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.tocchi.palla"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col-7 mt-1">
                                <table class="table table-hover table-striped w-100">
                                    <thead>
                                    <tr>
                                        <th class="p-05 w-50"></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.totale"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.1t"/></th>
                                        <th class="table-th border p-05 text-center fw-bold"><spring:message code="match.studio.2t"/></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <c:forEach var="row" items="${mPlayerDataMap.get(playerId).mTab6Table}">
                                        <tr>
                                            <td class="p-05 table-column fw-bold">${row.text}</td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(0)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(1)}
                                            </td>
                                            <td class="p-05 table-column text-center">
                                                    ${row.getValuesText(2)}
                                            </td>
                                        </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-5 mt-1">
                                <img class="w-100" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab6Image}">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-1">
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.heatmap.tocchi"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="d-flex col mt-1 justify-content-center">
                                <img class="w-75" src="data:image/png;base64,${mPlayerDataMap.get(playerId).mTab7Image}">
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <p class="fw-bold mb-0"><spring:message code="match.studio.confronto.tocchi.giocatore.media.squadra"/></p>
                        <div class="d-flex w-100">
                            <div class="subtitle-black"></div>
                            <div class="subtitle-red"></div>
                        </div>

                        <div class="row">
                            <div class="col mt-1">
                                <div class="w-100" style="height: 200px" id="chartdiv${playerId}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </c:if>
</c:forEach>