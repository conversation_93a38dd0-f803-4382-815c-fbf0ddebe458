<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        if (${not empty usr} && ${not empty psw}) {
            $("#sumbitButton").click();
        }
    });

    function recoverPassword() {
        event.preventDefault();

        let email = $("#email").val() || "";
        if (notEmpty(email)) {
            $.ajax({
                type: "GET",
                url: "/sicsdataanalytics/auth/lostpwd.htm",
                cache: false,
                data: encodeURI("formUsername=" + email),
                success: function (result) {
                    if (result === "ok") {
                        sweetAlert.fire({
                            title: "<spring:message code="messages.recover.password"/>",
                            text: "<spring:message code="messages.password.resetted"/>",
                            icon: "success",
                            padding: 40
                        });
                    } else {
                        sweetAlert.fire({
                            title: "ERROR, Oops...",
                            text: "<spring:message code="messages.generic.error"/>",
                            icon: "error",
                            padding: 40
                        });
                    }

                    setTimeout(function () {
                        location.href = "/sicsdataanalytics/auth/login.htm";
                    }, 2000);
                },
                error: function () {
                    sweetAlert.fire({
                        title: "ERROR, Oops...",
                        text: "<spring:message code="messages.generic.error"/>",
                        icon: "error",
                        padding: 40
                    });
                }
            });
        }
    }
</script>

<body class="bg-dark">
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content d-flex justify-content-center align-items-center">

                    <!-- Password recovery form -->
                    <form class="login-form" action="#">
                        <div class="card mb-0">
                            <div class="card-body">
                                <div class="text-center mb-3">
                                    <div class="d-inline-flex bg-primary bg-opacity-10 text-primary lh-1 rounded-pill p-3 mb-3 mt-1">
                                        <i class="ph-arrows-counter-clockwise ph-2x"></i>
                                    </div>
                                    <h5 class="mb-0"><spring:message code="messages.recover.password.title"/></h5>
                                    <span class="d-block text-muted"><spring:message code="messages.recover.password.subtitle"/></span>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label"><spring:message code="messages.recover.password.email"/></label>
                                    <div class="form-control-feedback form-control-feedback-start">
                                        <input type="email" class="form-control" placeholder="<EMAIL>" id="email">
                                        <div class="form-control-feedback-icon">
                                            <i class="ph-at text-muted"></i>
                                        </div>
                                    </div>
                                </div>

                                <button type="submit" class="btn btn-primary w-100" onclick="recoverPassword();">
                                    <i class="ph-arrow-counter-clockwise me-2"></i>
                                    <spring:message code="messages.recover.password"/>
                                </button>
                            </div>
                        </div>
                    </form>
                    <!-- /password recovery form -->

                </div>
                <!-- /content area -->

            </div>
            <!-- /inner content -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
</body>
