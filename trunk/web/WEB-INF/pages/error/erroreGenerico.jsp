<%@ page contentType="text/html; charset=utf-8" language="java" errorPage="" %>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
        <%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="SKYPE_TOOLBAR" content="SKYPE_TOOLBAR_PARSER_COMPATIBLE" />
        <title><spring:message code="theme.title"/></title>
    </head>
    <body>
        <div id="container">
            <div id="topheader">
                <img class="logo" style='width:150px;' src="../<spring:message code='theme.logo'/>" border="0" alt=""/>
                <span class="titlelogin"><spring:message code="theme.title2"/></span>
            </div>
            <div id="matchFilter"><h1>Errore</h1></div>
            <p>&nbsp;</p>
            <p>  ${exception.toString()} </p>
        </div>
    </body>
</html>
