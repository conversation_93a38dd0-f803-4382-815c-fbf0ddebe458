<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script type="text/javascript">
    $(document).ready(function () {
    <c:if test="${mMinPlaytime != null}">
        minPlaytimeFilter = ${mMinPlaytime};
    </c:if>
        $("#page-title").html(getPageTitle());
        if (typeof table !== "undefined") {
            table.destroy();
        }
        table = $('#ranking-table').DataTable({
            dom: 't',
            paging: false,
            stateSave: true,
            order: [3, "desc"]
        });

        updateMaxEventAmount(${mMaxTotal});
    });

    function redirectToOverview(teamId, playerId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
        }

        index = filtersIndex.indexOf("playerid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-playerid", playerId);

            location.href = "/sicsdataanalytics/player/overview.htm";
        }
    }

    function redirectToTeamOverview(teamId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

            location.href = "/sicsdataanalytics/team/overview.htm";
        }
    }
</script>
<div class="row">
    <span class="d-none" id="ranking-content-empty" isempty="${mRows.isEmpty()}"></span>
    <span class="d-none" id="ranking-content-valid" isvalid="${mValid}"></span>
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="ranking-table">
                    <thead>
                        <tr>
                            <th><spring:message code="player.ranking.player"/></th>
                            <th class="text-center"><spring:message code="player.ranking.team"/></th>
                            <th class="text-center"><spring:message code="player.ranking.role"/></th>
                            <th class="text-center" style="width: 35vw;"></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="row" items="${mRows}">
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator-container status-indicator-container-image">
                                            <img width="36" height="36" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${row.teamId}, ${row.playerId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(row.playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                            <img width="15" height="10" class="status-indicator" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(row.countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'" title="${mCountries.get(row.countryId).getName(mUser.tvLanguage)}">
                                        </div>
                                        <div class="ms-1">
                                            ${mPlayers.get(row.playerId).knownName}
                                            <div class="text-muted">${row.bornYear != null ? row.bornYear.toString().substring(2) : ''}', ${row.footId != null ? mFoots.get(Integer.valueOf(row.footId)).getDesc(mUser.tvLanguage) : ''}</div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <img width="36" height="36" class="rounded-pill cursor-pointer" onclick="redirectToTeamOverview(${row.teamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(row.teamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                    <span class="ms-1">${mTeams.get(row.teamId).getName(mUser.tvLanguage)}</span>
                                </td>
                                <td>
                                    ${row.positionId != null ? mPositions.get(Integer.valueOf(row.positionId)).getDesc(mUser.tvLanguage) : ''}
                                    <div class="text-muted">${row.positionDetailId != null ? mPositionDetails.get(Integer.valueOf(row.positionDetailId)).getDesc(mUser.tvLanguage) : ''}</div>
                                </td>
                                <td class="p-1" style="width: 35vw;">
                                    <div class="progress">
                                        <c:choose>
                                            <c:when test="${mDisplayType.equals('P90')}">
                                                <c:set var="percentage" value="${Math.round(1.0 * row.totalP90 / mMax * 100)}"/>
                                                <c:set var="value" value="${row.totalP90}"/>
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('TOUCHES')}">
                                                <c:set var="percentage" value="${Math.round(1.0 * row.total100Touches / mMax * 100)}"/>
                                                <c:set var="value" value="${row.total100Touches}"/>
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('AVERAGE')}">
                                                <c:set var="percentage" value="${Math.round(1.0 * row.totalAverage / mMax * 100)}"/>
                                                <c:set var="value" value="${row.totalAverage}"/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="percentage" value="${Math.round(1.0 * row.total / mMax * 100)}"/>
                                                <c:set var="value" value="${row.total}"/>
                                            </c:otherwise>
                                        </c:choose>

                                        <div class="progress-bar progress-bar-striped bg-info rounded-end" total="${row.total}" total90="${row.totalP90}" totalTouches="${row.total100Touches}" totalAverage="${row.totalAverage}" style="width: ${percentage}%" aria-valuenow="${coloredPerc}" aria-valuemin="${mMin}" aria-valuemax="${mMax}">${value}</div>
                                    </div>
                                </td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>