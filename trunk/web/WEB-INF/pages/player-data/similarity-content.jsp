<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<style>
    .first-col {
        width: 10vw!important;
        min-width: 10vw!important;
        max-width: 10vw!important;
    }

    .col {
        width: 12vw;
        min-width: 12vw;
        max-width: 12vw;
    }

    table > thead.hide {
        display: none;
    }

    /*th:nth-child(2), td:nth-child(2) {
        --bg-opacity: 0.2;
        background-color: rgba(var(--success-rgb),var(--bg-opacity))!important;
    }*/
</style>

<script type="text/javascript">
    var colorScale;
    var similarityTable;
    $(document).ready(function () {
        colorScale = chroma.scale(['#039158', '#e63235']).domain([0, 1]);
    <c:if test="${mModality == '1'}">
        <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
        let chartData${counter.count} = [];

            <c:forEach var="statsTypeId" items="${mStatsTypeIds}">
                <c:if test="${statsTypeId != 904}">
                    <c:set var="basePlayerValue" value="${0}"/>
                    <c:if test="${mPlayerRow.adjustedStatsTypeMap != null && mPlayerRow.adjustedStatsTypeMap.containsKey(statsTypeId)}">
                        <c:set var="basePlayerValue" value="${Math.round(mPlayerRow.adjustedStatsTypeMap.get(statsTypeId) * 100) / 100}"/>
                    </c:if>
                    <c:set var="playerValue" value="${0}"/>
                    <c:if test="${similarPlayer.adjustedStatsTypeMap != null && similarPlayer.adjustedStatsTypeMap.containsKey(statsTypeId)}">
                        <c:set var="playerValue" value="${Math.round(similarPlayer.adjustedStatsTypeMap.get(statsTypeId) * 100) / 100}"/>
                    </c:if>
        chartData${counter.count}.push({event: '${mSimilarityMetrics.get(statsTypeId).getDesc(mUser.tvLanguage)}', total_0: ${basePlayerValue}, total_1: ${playerValue}});
                </c:if>
            </c:forEach>

        let variables${counter.count} = getPlayerSimilarityRadarChart("radar-${counter.count}", chartData${counter.count});
        addPlayerSimilarityRadarSeries(variables${counter.count}[0], variables${counter.count}[1], variables${counter.count}[2], variables${counter.count}[3], chartData${counter.count}, 0, "it");
            <c:if test="${counter.count != 1}">
        addPlayerSimilarityRadarSeries(variables${counter.count}[0], variables${counter.count}[1], variables${counter.count}[2], variables${counter.count}[3], chartData${counter.count}, 1, "it");
            </c:if>
        </c:forEach>
    </c:if>

    <c:choose>
        <c:when test="${mModality == '1'}">
        similarityTable = $('#similarity-table').DataTable({
            dom: 't',
            ordering: false,
            fixedColumns: {
                start: 2
            },
            paging: false,
            scrollCollapse: false,
            scrollX: true
        });
        $("#export-button-container").addClass("d-none");
        </c:when>
        <c:otherwise>
        similarityTable = $('#similarity-table').DataTable({
            dom: 't',
            ordering: false,
            paging: false
        });
        $("#export-button-container").removeClass("d-none");
        </c:otherwise>
    </c:choose>
    });

    <c:if test="${mModality == '2'}">
    function exportTableData() {
        var csv = '';

        // Get header row dynamically
        var headers = [];
        $('#similarity-table thead th').each(function () {
            headers.push($(this).text()); // Add header cell text
        });
        csv += headers.join(',') + '\n'; // Add header to CSV

        // Get table rows data dynamically
        similarityTable.rows().every(function () {
            var data = this.data();
            csv += data.join(',') + '\n'; // Add data row to CSV
        });

        // Create a download link for the CSV file
        var hiddenElement = document.createElement('a');
        hiddenElement.href = 'data:text/csv;charset=utf-8,' + encodeURI(csv);
        hiddenElement.target = '_blank';
        hiddenElement.download = 'SDA-Similarity.csv';
        hiddenElement.click();

        // Clean up by removing the hidden element after download
        hiddenElement.remove();
    }
    </c:if>
</script>

<div class="row">
    <span class="d-none" id="similarity-content-empty" isempty="${mSimilarPlayers.isEmpty()}"></span>
    <span class="d-none" id="similarity-content-valid" isvalid="${mValid}"></span>
    <div class="card mb-0 p-0">
        <div class="card-body overflow-auto p-0">
            <div class="table-responsive">
                <c:choose>
                    <c:when test="${mModality == '1'}">
                        <table class="table table-xs table-hover fs-sm" id="similarity-table">
                            <thead class="hide">
                                <tr>
                                    <th></th>
                                        <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                        <th></th>
                                        </c:forEach>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold col first-col"><spring:message code="player.similarity.player"/></td>
                                    <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                        <td class="${counter.count > 11 ? 'd-none' : ''} col px-0" index="${counter.count}">
                                            <div class="d-flex flex-column align-items-center">
                                                <p class="mb-0 fw-semibold cursor-pointer text-decoration-underline fs-base d-flex justify-content-center align-items-center" title="${mPlayers.get(similarPlayer.playerId).firstName} ${mPlayers.get(similarPlayer.playerId).lastName}"  onclick="searchProfile(${similarPlayer.playerId}, false);">
                                                    ${mPlayers.get(similarPlayer.playerId).knownName} (${mSimilarPlayersData.get(similarPlayer.playerId).getBornYearString()})
                                                    <img height="20" width="20" class="image-shadow-sm ms-2" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mPlayers.get(similarPlayer.playerId).countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'" title="${mCountries.get(mPlayers.get(similarPlayer.playerId).countryId).getName(mUser.tvLanguage)}">
                                                </p>
                                                <img height="60" width="60" class="image-shadow mt-2" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(similarPlayer.playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                                <p class="mb-0 mt-2">
                                                    <img height="20" width="20" class="image-shadow-sm" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mSimilarPlayersData.get(similarPlayer.playerId).lastTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'">
                                                    ${mTeams.get(mSimilarPlayersData.get(similarPlayer.playerId).lastTeamId).getName(mUser.tvLanguage)}
                                                </p>
                                                <p class="mb-0 mt-1 text-uppercase">
                                                    <c:if test="${mSimilarPlayersData.get(similarPlayer.playerId).footId != null}">
                                                        <c:if test="${mSimilarPlayersData.get(similarPlayer.playerId).footId == 1}">
                                                            <img height="20" width="20" class="image-shadow-xs" src="/sicsdataanalytics/images/foot-right.png">
                                                        </c:if>
                                                        <c:if test="${mSimilarPlayersData.get(similarPlayer.playerId).footId == 2}">
                                                            <img height="20" width="20" class="image-shadow-xs" src="/sicsdataanalytics/images/foot-left.png">
                                                        </c:if>
                                                        <c:if test="${mSimilarPlayersData.get(similarPlayer.playerId).footId == 3}">
                                                            <img height="20" width="20" class="image-shadow-xs" src="/sicsdataanalytics/images/foot.png">
                                                        </c:if>
                                                    </c:if>
                                                    ${mPositions.get(mSimilarPlayersData.get(similarPlayer.playerId).positionId).getDesc(mUser.tvLanguage)}
                                                </p>
                                                <c:if test="${mSimilarPlayersData.get(similarPlayer.playerId).positionDetailId != null}">
                                                    <p class="mb-0 mt-1 fs-xs text-uppercase">
                                                        ${mPositionDetails.get(mSimilarPlayersData.get(similarPlayer.playerId).positionDetailId).getDesc(mUser.tvLanguage)}
                                                    </p>
                                                </c:if>
                                            </div>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td class="fw-bold col first-col"><spring:message code="player.similarity.radar"/></td>
                                    <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                        <td class="${counter.count > 11 ? 'd-none' : ''} col p-0" index="${counter.count}">
                                            <div id="radar-${counter.count}" style="min-height: 20vh;">

                                            </div>
                                        </td>
                                    </c:forEach>
                                </tr>
                                <tr>
                                    <td class="fw-bold col first-col"><spring:message code="player.similarity.similarity.perc"/></td>
                                    <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                        <c:choose>
                                            <c:when test="${similarPlayer.similarityPercentage != null}">
                                                <c:set var="textColor" value=""/>
                                                <c:choose>
                                                    <c:when test="${similarPlayer.similarityPercentage >= 75}">
                                                        <c:set var="textColor" value="bg-success bg-opacity-25 text-success"/>
                                                    </c:when>
                                                    <c:when test="${similarPlayer.similarityPercentage >= 50}">
                                                        <c:set var="textColor" value="bg-warning bg-opacity-25 text-warning"/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:set var="textColor" value="bg-danger bg-opacity-25 text-danger"/>
                                                    </c:otherwise>
                                                </c:choose>
                                                <td class="fw-bold text-center ${counter.count > 11 ? 'd-none' : ''} col ${textColor}" index="${counter.count}">
                                                    ${Math.round(similarPlayer.similarityPercentage)} %
                                                </td>
                                            </c:when>
                                            <c:otherwise>
                                                <td class="text-center ${counter.count > 11 ? 'd-none' : ''} col" index="${counter.count}"></td>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:forEach>
                                </tr>
                                <c:forEach var="statsTypeId" items="${mStatsTypeIds}">
                                    <tr>
                                        <td class="col first-col">${mSimilarityMetrics.get(statsTypeId).getDesc(mUser.tvLanguage)}</td>
                                        <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                            <c:choose>
                                                <c:when test="${similarPlayer.statsTypeMap != null && similarPlayer.statsTypeMap.containsKey(statsTypeId)}">
                                                    <td class="text-center ${counter.count > 11 ? 'd-none' : ''} col" index="${counter.count}">${similarPlayer.statsTypeMap.get(statsTypeId)}</td>
                                                </c:when>
                                                <c:otherwise>
                                                    <c:choose>
                                                        <c:when test="${statsTypeId == 904}">
                                                            <td class="text-center ${counter.count > 11 ? 'd-none' : ''} col" index="${counter.count}"><spring:message code="player.similarity.position.checked"/></td>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <td class="text-center ${counter.count > 11 ? 'd-none' : ''} col" index="${counter.count}">-</td>
                                                        </c:otherwise>
                                                    </c:choose>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:forEach>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </c:when>
                    <c:otherwise>
                        <table class="table table-xs table-hover fs-sm" id="similarity-table">
                            <thead>
                                <tr>
                                    <th class="fw-bold"><spring:message code="player.similarity.player"/></th>
                                    <th class="fw-bold"><spring:message code="player.similarity.similarity.perc"/></th>
                                        <c:forEach var="statsTypeId" items="${mStatsTypeIds}">
                                        <th>${mSimilarityMetrics.get(statsTypeId).getDesc(mUser.tvLanguage)}</th>
                                        </c:forEach>
                                </tr>
                            </thead>
                            <tbody>
                                <c:forEach var="similarPlayer" items="${mSimilarPlayers}" varStatus="counter">
                                    <tr class="${counter.count == 1 ? 'bg-success bg-opacity-20' : ''}">
                                        <td class="fw-semibold cursor-pointer text-decoration-underline" title="${mPlayers.get(similarPlayer.playerId).firstName} ${mPlayers.get(similarPlayer.playerId).lastName}" onclick="searchProfile(${similarPlayer.playerId}, false);">
                                            ${mPlayers.get(similarPlayer.playerId).knownName} (${mSimilarPlayersData.get(similarPlayer.playerId).getBornYearString()})
                                        </td>
                                        <c:choose>
                                            <c:when test="${similarPlayer.similarityPercentage != null}">
                                                <c:set var="textColor" value=""/>
                                                <c:choose>
                                                    <c:when test="${similarPlayer.similarityPercentage >= 75}">
                                                        <c:set var="textColor" value="bg-success bg-opacity-25 text-success"/>
                                                    </c:when>
                                                    <c:when test="${similarPlayer.similarityPercentage >= 50}">
                                                        <c:set var="textColor" value="bg-warning bg-opacity-25 text-warning"/>
                                                    </c:when>
                                                    <c:otherwise>
                                                        <c:set var="textColor" value="bg-danger bg-opacity-25 text-danger"/>
                                                    </c:otherwise>
                                                </c:choose>
                                                <td class="fw-bold text-center ${textColor}" index="${counter.count}">
                                                    ${Math.round(similarPlayer.similarityPercentage)} %
                                                </td>
                                            </c:when>
                                            <c:otherwise>
                                                <td class="text-center" index="${counter.count}"></td>
                                            </c:otherwise>
                                        </c:choose>
                                        <c:forEach var="statsTypeId" items="${mStatsTypeIds}">
                                            <c:choose>
                                                <c:when test="${similarPlayer.statsTypeMap != null && similarPlayer.statsTypeMap.containsKey(statsTypeId)}">
                                                    <td class="text-center" index="${counter.count}">${similarPlayer.statsTypeMap.get(statsTypeId)}</td>
                                                </c:when>
                                                <c:otherwise>
                                                    <td class="text-center" index="${counter.count}">-</td>
                                                </c:otherwise>
                                            </c:choose>
                                        </c:forEach>
                                    </tr>
                                </c:forEach>
                            </tbody>
                        </table>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </div>
</div>