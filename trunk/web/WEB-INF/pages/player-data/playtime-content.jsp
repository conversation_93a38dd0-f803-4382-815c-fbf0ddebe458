<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script type="text/javascript">
    $(document).ready(function () {
        if (typeof table !== "undefined") {
            table.destroy();
        }
        table = $('#playtime-table').DataTable({
            dom: 't',
            paging: false,
            stateSave: true,
            order: [0, "desc"]
        });

        function moveLastRow() {
            var lastRow = $('#playtime-table tbody tr.always-last');
            if (lastRow.length) {
                $('#playtime-table tbody').append(lastRow); // Move it to the end
            }
        }

        // Move the last row every time the table is sorted
        table.on('order.dt search.dt draw.dt', function () {
            moveLastRow();
        });

        // Ensure the last row is moved on initialization
        moveLastRow();
    });

    function redirectToOverview(teamId, playerId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
        }

        index = filtersIndex.indexOf("playerid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-playerid", playerId);

            location.href = "/sicsdataanalytics/player/overview.htm";
        }
    }
</script>
<div class="row">
    <span class="d-none" id="playtime-content-empty" isempty="${mRows.isEmpty()}"></span>
    <span class="d-none" id="playtime-content-valid" isvalid="${mValid}"></span>
    <div class="card mb-0">
        <div class="card-body overflow-auto pt-0">
            <div class="table-responsive">
                <table class="table table-hover" id="playtime-table">
                    <thead>
                        <tr>
                            <th style="min-width: 2vw; width: 2vw;"></th>
                            <th style="min-width: 10vw; width: 10vw;"></th>
                            <th style="min-width: 3vw; width: 3vw;"></th>
                            <th style="min-width: 10vw; width: 10vw;"></th>
                                <c:forEach var="fixtureId" items="${mFixtureIds}">
                                <th></th>
                                </c:forEach>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="playerId" items="${mSortedPlayerIds}">
                            <tr>
                                <td class="text-center p-1 fs-sm">
                                    ${mPositions.get(mTeamPlayers.get(mSeasonId).get(mTeamId).get(playerId).positionId).code}
                                </td>
                                <td class="p-1 fs-sm">
                                    <div class="d-flex align-items-center">
                                        <div class="status-indicator-container status-indicator-container-image me-1">
                                            <img width="16" height="16" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${mTeamId}, ${playerId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                            <img width="7" height="7" class="status-indicator" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mPlayers.get(playerId).countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'" title="${mCountries.get(mPlayers.get(playerId).countryId).getName(mUser.tvLanguage)}">
                                        </div>
                                        ${mPlayers.get(playerId).knownName}
                                    </div>
                                </td>
                                <td class="text-center p-1 fs-sm">
                                    ${mRows.get(playerId).size()}
                                </td>
                                <c:set var="total" value="${0}"/>
                                <c:set var="maxTotal" value="${0}"/>
                                <c:forEach var="fixtureId" items="${mFixtureIds}">
                                    <c:set var="fixturePlayer" value="${mRows.get(playerId).get(fixtureId)}"/>
                                    <c:if test="${fixturePlayer != null}">
                                        <c:set var="total" value="${total + Math.round(1.0 * fixturePlayer.playTime / 60)}"/>
                                        <c:if test="${Math.round(1.0 * fixturePlayer.maxPlaytime / 60) > maxTotal}">
                                            <c:set var="maxTotal" value="${Math.round(1.0 * fixturePlayer.maxPlaytime / 60)}"/>
                                        </c:if>
                                    </c:if>
                                </c:forEach>
                                <td class="text-center p-0 fs-sm" data-order="${total}">
                                    <div class="d-flex">
                                        <div class="col-md-9">
                                            <div class="progress">
                                                <c:set var="percentage" value="${Math.round(1.0 * total / maxTotal * 100)}"/>
                                                <div class="progress-bar progress-bar-striped bg-${wrapper.color} rounded-end fw-bold" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="${maxTotal}"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            ${total}'
                                        </div>
                                    </div>
                                </td>
                                <c:forEach var="fixtureId" items="${mFixtureIds}">
                                    <c:set var="fixturePlayer" value="${mRows.get(playerId).get(fixtureId)}"/>
                                    <c:choose>
                                        <c:when test="${fixturePlayer == null}">
                                            <td class="p-1"></td>
                                        </c:when>
                                        <c:otherwise>
                                            <c:set var="background" value=""/>
                                            <c:choose>
                                                <c:when test="${fixturePlayer.playerOut != null && fixturePlayer.playerOut}">
                                                    <c:set var="background" value="bg-success bg-opacity-50 text-white"/>
                                                </c:when>
                                                <c:when test="${fixturePlayer.starter != null && fixturePlayer.starter}">
                                                    <c:set var="background" value="bg-success bg-opacity-75 text-white"/>
                                                </c:when>
                                                <c:when test="${fixturePlayer.playerIn != null && fixturePlayer.playerIn}">
                                                    <c:set var="background" value="bg-yellow bg-opacity-50"/>
                                                </c:when>
                                                <c:when test="${fixturePlayer.redCard != null && fixturePlayer.redCard}">
                                                    <c:set var="background" value="bg-danger bg-opacity-50 text-white"/>
                                                </c:when>
                                            </c:choose>

                                            <td class="text-center p-1 fs-xs ${background}">${Math.round(1.0 * fixturePlayer.playTime / 60)}</td>
                                        </c:otherwise>
                                    </c:choose>
                                </c:forEach>
                            </tr>
                        </c:forEach>
                        <tr class="always-last">
                            <td colspan="4">
                                <div class="d-flex align-items-center">
                                    <span class="d-inline-block rounded-pill p-2 me-2 bg-success bg-opacity-75"></span>
                                    <p class="mb-0"><spring:message code="player.playtime.starter"/></p>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="d-inline-block rounded-pill p-2 me-2 bg-success bg-opacity-50"></span>
                                    <p class="mb-0"><spring:message code="player.playtime.player.out"/></p>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="d-inline-block rounded-pill p-2 me-2 bg-yellow bg-opacity-50"></span>
                                    <p class="mb-0"><spring:message code="player.playtime.player.in"/></p>
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="d-inline-block rounded-pill p-2 me-2 bg-danger bg-opacity-50"></span>
                                    <p class="mb-0"><spring:message code="player.playtime.red.card"/></p>
                                </div>
                            </td>
                            <td class="d-none"></td>
                            <td class="d-none"></td>
                            <td class="d-none"></td>
                            <c:forEach var="fixtureId" items="${mFixtureIds}">
                                <c:set var="fixture" value="${mFixtureMap.get(fixtureId)}"/>
                                <c:set var="isHomeTeam" value="${fixture.homeTeamId == mTeamId}"/>
                                <c:set var="color" value=""/>
                                <c:choose>
                                    <c:when test="${isHomeTeam}">
                                        <c:choose>
                                            <c:when test="${fixture.homeScore > fixture.awayScore}">
                                                <c:set var="color" value="text-success"/>
                                            </c:when>
                                            <c:when test="${fixture.homeScore == fixture.awayScore}">
                                                <c:set var="color" value=""/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="color" value="text-danger"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${fixture.homeScore > fixture.awayScore}">
                                                <c:set var="color" value="text-danger"/>
                                            </c:when>
                                            <c:when test="${fixture.homeScore == fixture.awayScore}">
                                                <c:set var="color" value=""/>
                                            </c:when>
                                            <c:otherwise>
                                                <c:set var="color" value="text-success"/>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                                <td class="text-center p-0 ${color}" style="writing-mode: vertical-lr; transform: rotate(180deg);">
                                    <c:choose>
                                        <c:when test="${isHomeTeam}">
                                            ${mTeams.get(fixture.awayTeamId).getName(mUser.tvLanguage)} (${fixture.homeScore}-${fixture.awayScore})
                                        </c:when>
                                        <c:otherwise>
                                            ${mTeams.get(fixture.homeTeamId).getName(mUser.tvLanguage)} (${fixture.homeScore}-${fixture.awayScore})
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                            </c:forEach>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>