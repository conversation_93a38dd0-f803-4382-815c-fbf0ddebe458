<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<script type="text/javascript">
    var colorScale;
    $(document).ready(function () {
    <c:if test="${mMinPlaytime != null}">
        minPlaytimeFilter = ${mMinPlaytime};
    </c:if>
        $("#page-title").html(getPageTitle());
        updateMaxEventAmount(${mMaxTotal});

        updateProgressColor();
    });

    function updateProgressColor() {
        let maxValue = 0;
        $(".progress-bar.progress-bar-striped").each(function (index, element) {
            let value = parseFloat($(element).text());
            if (value) {
                if (value > maxValue) {
                    maxValue = value;
                }
            }
        });

        colorScale = chroma.scale(['#e63235', '#fc9228', '#86d44b', '#039158']).domain([0, maxValue]);
        $(".progress-bar.progress-bar-striped").each(function (index, element) {
            let value = parseFloat($(element).text());
            if (value) {
                $(element).css("background-color", colorScale(value).hex());
            }
        });
    }

    function redirectToOverview(teamId, playerId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
        }

        index = filtersIndex.indexOf("playerid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-playerid", playerId);

            location.href = "/sicsdataanalytics/player/overview.htm";
        }
    }

    function redirectToTeamOverview(teamId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

            location.href = "/sicsdataanalytics/team/overview.htm";
        }
    }
</script>
<div class="row">
    <span class="d-none" id="top-content-empty" isempty="${mRows.isEmpty()}"></span>
    <span class="d-none" id="top-content-valid" isvalid="${mValid}"></span>
    <div class="card mb-0">
        <div class="card-body overflow-auto pt-0">
            <div class="table-responsive">
                <table class="table table-hover" id="top-table">
                    <thead>
                        <tr>
                            <th style="width: 15vw;"></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="teamId" items="${mRows.keySet()}">
                            <tr>
                                <td class="p-1">
                                    <img width="24" height="24" class="rounded-pill cursor-pointer me-1" onclick="redirectToTeamOverview(${teamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(teamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                    ${mTeams.get(teamId).getName(mUser.tvLanguage)}
                                </td>
                                <c:forEach var="row" items="${mRows.get(teamId)}">
                                    <td class="p-1">
                                        <c:if test="${row.playerId != null}">
                                            <div class="d-flex align-items-center">
                                                <div class="status-indicator-container status-indicator-container-image me-1">
                                                    <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${teamId}, ${row.playerId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(row.playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                                    <img width="10" height="10" class="status-indicator" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mPlayers.get(row.playerId).countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'" title="${mCountries.get(mPlayers.get(row.playerId).countryId).getName(mUser.tvLanguage)}">
                                                </div>

                                                <div class="w-50">
                                                    ${mPlayers.get(row.playerId).knownName}
                                                </div>

                                                <div class="progress w-50">
                                                    <c:choose>
                                                        <c:when test="${mDisplayType.equals('P90')}">
                                                            <c:set var="percentage" value="${Math.round(row.totalP90 / mMaxValue * 100)}"/>
                                                        </c:when>
                                                        <c:when test="${mDisplayType.equals('TOUCHES')}">
                                                            <c:set var="percentage" value="${Math.round(row.total100Touches / mMaxValue * 100)}"/>
                                                        </c:when>
                                                        <c:when test="${mDisplayType.equals('AVERAGE')}">
                                                            <c:set var="percentage" value="${Math.round(row.totalAverage / mMaxValue * 100)}"/>
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:choose>
                                                                <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                                    <c:set var="percentage" value="${Math.round(row.total / mMaxValue * 100)}"/>
                                                                </c:when>
                                                                <c:otherwise>
                                                                    <c:set var="percentage" value="${Math.round((1.0 * row.total) / mMaxValue * 100)}"/>
                                                                </c:otherwise>
                                                            </c:choose>
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <div class="progress-bar progress-bar-striped rounded-end text-white" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="${mMaxValue}">
                                                        <c:choose>
                                                            <c:when test="${mDisplayType.equals('P90')}">
                                                                ${row.totalP90}
                                                            </c:when>
                                                            <c:when test="${mDisplayType.equals('TOUCHES')}">
                                                                ${row.total100Touches}
                                                            </c:when>
                                                            <c:when test="${mDisplayType.equals('AVERAGE')}">
                                                                ${row.totalAverage}
                                                            </c:when>
                                                            <c:otherwise>
                                                                <c:choose>
                                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                                        ${row.total}
                                                                    </c:when>
                                                                    <c:otherwise>
                                                                        ${Math.round(1.0 * row.total)}
                                                                    </c:otherwise>
                                                                </c:choose>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </div>
                                            </div>
                                        </c:if>
                                    </td>
                                </c:forEach>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>