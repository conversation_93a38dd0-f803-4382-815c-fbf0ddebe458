<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<script type="text/javascript">
    $(document).ready(function () {
        checkParametersAmount(${mTotalRows});
        let offensiveChartData = [], defensiveChartData = [], matchChartData = [];

    <c:forEach var="row" items="${mRows}">
        <c:set var="percentage" value="${row.progressPercentage}"/>
        <c:if test="${row.value == -1}">
            <c:set var="percentage" value="${0}"/>
        </c:if>
        <c:set var="color" value="${row.getProgressClassRBG()}"/>
        <c:set var="category" value="${row.eventTypeName}"/>
        <c:if test="${row.tagTypeName != null}">
            <c:set var="category" value="${category}\n${row.tagTypeName}"/>
        </c:if>
        <c:if test="${row.zoneAbbName != null}">
            <c:set var="category" value="${category}\n${row.zoneAbbName}"/>
        </c:if>
        <c:if test="${percentage < 10}">
            <c:set var="percentage" value="${10}"/>
        </c:if>
        offensiveChartData.push({category: "${category}", value: ${percentage}, color: "${color}"});
    </c:forEach>
    <c:forEach var="row" items="${mSufferedRows}">
        <c:set var="percentage" value="${row.progressPercentage}"/>
        <c:if test="${row.value == -1}">
            <c:set var="percentage" value="${0}"/>
        </c:if>
        <c:set var="color" value="${row.getProgressClassRBG()}"/>
        <c:set var="category" value="${row.eventTypeName}"/>
        <c:if test="${row.tagTypeName != null}">
            <c:set var="category" value="${category}\n${row.tagTypeName}"/>
        </c:if>
        <c:if test="${row.zoneAbbName != null}">
            <c:set var="category" value="${category}\n${row.zoneAbbName}"/>
        </c:if>
        <c:if test="${percentage < 10}">
            <c:set var="percentage" value="${10}"/>
        </c:if>
        defensiveChartData.push({category: "${category}", value: ${percentage}, color: "${color}"});
    </c:forEach>

        if (offensiveChartData.length > 0) {
            getPlayerOverviewPolarChart("offensive-chartdiv", offensiveChartData);
        }
        if (defensiveChartData.length > 0) {
            getPlayerOverviewPolarChart("defensive-chartdiv", defensiveChartData);
        }

    <c:set var="from" value="${0}"/>
    <c:set var="to" value="${mStarterStats.starterCompleted}"/>
        matchChartData.push({category: "", from: ${from}, to: ${to}, total: ${mStarterStats.starterCompleted}, name: "<spring:message code="player.overview.completed"/>", columnSettings: {fill: am5.color(0x0ca948)}});
    <c:set var="from" value="${from + mStarterStats.starterCompleted}"/>
    <c:set var="to" value="${to + mStarterStats.starterNotCompleted}"/>
        matchChartData.push({category: "", from: ${from}, to: ${to}, total: ${mStarterStats.starterNotCompleted}, name: "<spring:message code="player.overview.out"/>", columnSettings: {fill: am5.color(0x93da49)}});
    <c:set var="from" value="${from + mStarterStats.starterNotCompleted}"/>
    <c:set var="to" value="${to + mStarterStats.substituted}"/>
        matchChartData.push({category: "", from: ${from}, to: ${to}, total: ${mStarterStats.substituted}, name: "<spring:message code="player.overview.in"/>", columnSettings: {fill: am5.color(0xffd100)}});
    <c:set var="from" value="${from + mStarterStats.substituted}"/>
    <c:set var="to" value="${to + mStarterStats.redCards}"/>
        matchChartData.push({category: "", from: ${from}, to: ${to}, total: ${mStarterStats.redCards}, name: "<spring:message code="player.overview.red"/>", columnSettings: {fill: am5.color(0xcd213b)}});
        getPlayerOverviewMatchResumeChart(matchChartData, ${to});

        getPlayerOverviewPlaytimeAverageChart(${Math.round((mPlaytimeStats.average - mPlaytimeStats.min) / (mPlaytimeStats.max - mPlaytimeStats.min) * 10)}, ${mPlaytimeStats.average}, ${mPlaytimeStats.max}, ${mPlaytimeStats.min});

        drawField('moduleContainer', pitch);
        pitch.pointsDrawed = 0;
    <c:if test="${!mPlayerPositions.isEmpty()}">
        <c:forEach var="point" items="${mPlayerPositions.keySet()}">
        drawPoint(${point.x}, ${point.y}, ${mPlayerPositions.get(point)}, ${mPositionTotal});
        </c:forEach>
    </c:if>
    <c:if test="${mPlayerPositions.isEmpty()}">
        drawPlayerRole("${mPositions.get(mPlayerData.positionId).code}");
    </c:if>
    <c:if test="${mMinPlaytime != null}">
        minPlaytimeFilter = ${mMinPlaytime};
    </c:if>
    });

    function redirectToOverview(teamId) {
        let index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);

            location.reload();
        }
    }

    function changeSeasonCompetitionTeam(seasonId, competitionId, teamId) {
        let index = filtersIndex.indexOf("seasonid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-seasonid", seasonId);
        }

        index = filtersIndex.indexOf("competitionid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-competitionid", competitionId);
        }

        index = filtersIndex.indexOf("teamid");
        if (index >= 0) {
            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            if (index > 25) {
                index -= 25;
                letter = "Z" + ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
            }
            localStorage.setItem("dataanalytics/610/" + letter + "/filter-teamid", teamId);
        }
        location.reload();
    }

    function openSimilarPlayer(playerId) {
        if (playerId) {
            location.href = "/sicsdataanalytics/player/similarity.htm?playerId=" + playerId;
        }
    }
</script>
<div class="row">
    <span class="d-none" id="overview-content-empty" isempty="${mRows.isEmpty() && mSufferedRows.isEmpty()}"></span>
    <span class="d-none" id="overview-content-valid" isvalid="${mValid}"></span>

    <div class="col-12">
        <div class="card mb-2">
            <div class="card-body pt-2 pb-2">
                <div class="row min-vh-30">
                    <div class="col-2 d-flex flex-column align-items-center justify-content-center">
                        <img height="120" width="120" class="image-shadow" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                        <h6 class="mt-1 mb-0">${mPlayerName} <small class="fs-xs fw-normal">(#${mPlayerData.jerseyNumber})</small></h6>
                        <div class="mt-1 col-12 d-flex align-items-center justify-content-center">
                            <img height="20" width="20" class="image-shadow-sm" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'">
                            <span class="ms-1 text-uppercase">
                                ${mTeams.get(mTeamId).getName(mUser.tvLanguage)}
                                (${mCountries.get(mTeams.get(mTeamId).countryId).getName(mUser.tvLanguage)})
                            </span>
                        </div>
                        <small class="mt-1">${mCompetitions.get(mCompetitionId).getName(mUser.tvLanguage)}</small>
                        <c:if test="${mPlayerData.lastTeamId != null && mTeamId != mPlayerData.lastTeamId}">
                            <small class="fw-bold">(<spring:message code="player.overview.transferred"/>)</small>
                        </c:if>
                        <c:if test="${mPlayer.marketValue != null && mPlayer.marketValue > 0}">
                            <div class="border mt-2 px-3 py-2 rounded text-center">
                                <p class="mb-0" title="${mPlayer.marketValue}"><spring:message code="player.market.value"/>: <span class="fw-bold">${mPlayer.getMarketValueString(mUser.tvLanguage)}</span></p>
                                    <c:if test="${mPlayerData.contractExpires != null}">
                                    <p class="mb-0"><spring:message code="player.contract.expiration"/>: <span class="fw-semibold">${mPlayerData.getContractExpiresString()}</span></p>
                                    </c:if>
                            </div>
                        </c:if>
                    </div>
                    <div class="col-8">
                        <div class="row h-100">
                            <div class="col-4">
                                <h6 class="mb-2 text-center"><spring:message code="player.overview.player.informations"/></h6>
                                <div class="row mb-1">
                                    <div class="col-4">
                                        <spring:message code="player.overview.full.name"/>
                                    </div>
                                    <div class="col-8 fw-bold">
                                        ${mPlayer.firstName} ${mPlayer.lastName}
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-4">
                                        <spring:message code="player.overview.citizenship"/>
                                    </div>
                                    <div class="col-8">
                                        <img height="20" width="20" class="image-shadow-sm me-1" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/${mCountries.get(mPlayer.countryId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/country/unknown.png?<%=System.currentTimeMillis()%>'">
                                        ${mCountries.get(mPlayer.countryId).getName(mUser.tvLanguage)}
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-4">
                                        <spring:message code="player.overview.age"/>
                                    </div>
                                    <div class="col-8 fw-bold">
                                        ${mPlayerData.getAge()}
                                        <small class="fw-normal">(${mPlayerData.getBornDateString()})</small>
                                    </div>
                                </div>
                                <c:if test="${mPlayerData.height != null && mPlayerData.height > 0}">
                                    <div class="row">
                                        <div class="col-4">
                                            <spring:message code="player.overview.height"/>
                                        </div>
                                        <div class="col-8 fw-bold">
                                            ${mPlayerData.height} <small class="fw-normal">cm</small>
                                        </div>
                                    </div>
                                </c:if>
                                <hr class="m-2 mx-0"/>
                                <div class="row mb-1">
                                    <div class="col-4">
                                        <spring:message code="player.overview.foot"/>
                                    </div>
                                    <div class="col-8">
                                        <c:if test="${mPlayerData.footId == null}">
                                            <img height="20" width="20" class="image-shadow-xs me-1" src="/sicsdataanalytics/images/foot-none.png">
                                            N/A
                                        </c:if>
                                        <c:if test="${mPlayerData.footId != null}">
                                            <c:if test="${mPlayerData.footId == 1}">
                                                <img height="20" width="20" class="image-shadow-xs me-1" src="/sicsdataanalytics/images/foot-right.png">
                                            </c:if>
                                            <c:if test="${mPlayerData.footId == 2}">
                                                <img height="20" width="20" class="image-shadow-xs me-1" src="/sicsdataanalytics/images/foot-left.png">
                                            </c:if>
                                            <c:if test="${mPlayerData.footId == 3}">
                                                <img height="20" width="20" class="image-shadow-xs me-1" src="/sicsdataanalytics/images/foot.png">
                                            </c:if>
                                            ${mFoots.get(mPlayerData.footId).getDesc(mUser.tvLanguage)}
                                        </c:if>
                                    </div>
                                </div>
                                <div class="row mb-1">
                                    <div class="col-4">
                                        <spring:message code="player.overview.position"/>
                                    </div>
                                    <div class="col-8 fw-bold">
                                        ${mPositions.get(mPlayerData.positionId).getDesc(mUser.tvLanguage)}
                                        <c:if test="${mPlayerData.positionDetailId != null}">
                                            <small class="fw-normal">(${mPositionDetails.get(mPlayerData.positionDetailId).getDesc(mUser.tvLanguage)})</small>
                                        </c:if>
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-4">
                                        <spring:message code="player.overview.last.team"/>
                                    </div>
                                    <div class="col-8 fw-bold">
                                        <img height="20" width="20" class="image-shadow-sm me-1" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(mPlayerData.lastTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'">
                                        <small class="fw-normal">${mTeams.get(mPlayerData.lastTeamId).getName(mUser.tvLanguage)}</small>
                                    </div>
                                </div>
                                <c:if test="${mPlayer.playerAgencyId != null}">
                                    <div class="row mb-2">
                                        <div class="col-4">
                                            <spring:message code="player.overview.agent"/>
                                        </div>
                                        <div class="col-8 fw-bold">
                                            <img height="20" width="20" class="image-shadow-sm me-1" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/agentlogo/${mAgencies.get(mPlayer.playerAgencyId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='/sicsdataanalytics/images/unknown_agency.svg?<%=System.currentTimeMillis()%>'">
                                            <small class="fw-normal text-decoration-underline cursor-pointer" title="<spring:message code="messages.view.details"/>" onclick="showAgencyDetails(${mPlayer.playerAgencyId});">${mAgencies.get(mPlayer.playerAgencyId).name}</small>
                                        </div>
                                    </div>
                                </c:if>
                                <div class="row mb-0">
                                    <div class="col-12">
                                        <p class="mb-0 text-center link-primary cursor-pointer" onclick="openSimilarPlayer(${mPlayer.id});"><spring:message code="players.overview.check.similar"/></p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4 d-flex flex-column flex-grow-1">
                                <h6 class="mb-0 text-center"><spring:message code="player.overview.passes.distribution"/></h6>
                                <div class="d-flex flex-column flex-grow-1 align-items-center justify-content-center">
                                    <div class="h-50 row align-items-center justify-content-center mb-3">
                                        <div class="col-12">
                                            <h6 class="mb-0 text-center"><spring:message code="player.overview.passes.made"/></h6>
                                        </div>
                                        <div class="col-3 d-flex flex-column align-items-center justify-content-center">
                                            <img height="60" width="60" class="image-shadow" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(mPlayerMostPasses.playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                            <p class="mb-0 fs-sm text-center" title="<spring:message code="player.overview.passes.p90"/>">(${Math.round((mPlayerMostPasses.eventAmount / mPlaytimeStats.totalMinutes * 90) * 100) / 100})</p>
                                        </div>
                                        <div class="col-9">
                                            <p class="mb-0 fw-bold">${mPlayers.get(mPlayerMostPasses.playerId).knownName}</p>
                                            <p class="mb-0">
                                                ${mPositions.get(mPlayerMostPasses.positionId).getDesc(mUser.tvLanguage)}
                                                <c:if test="${mPlayerMostPasses.positionDetailId != null}">
                                                    <small>(${mPositionDetails.get(mPlayerMostPasses.positionDetailId).getDesc(mUser.tvLanguage)})</small>
                                                </c:if>
                                            </p>
                                        </div>
                                    </div>
                                    <div class="h-50 row align-items-center justify-content-center">
                                        <div class="col-12">
                                            <h6 class="mb-0 text-center"><spring:message code="player.overview.passes.received"/></h6>
                                        </div>
                                        <div class="col-3 d-flex flex-column align-items-center justify-content-center">
                                            <img height="60" width="60" class="image-shadow" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayers.get(mPlayerMostReceivedPasses.playerId).photo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'">
                                            <p class="mb-0 fs-sm text-center" title="<spring:message code="player.overview.passes.p90"/>">(${Math.round((mPlayerMostReceivedPasses.eventAmount / mPlaytimeStats.totalMinutes * 90) * 100) / 100})</p>
                                        </div>
                                        <div class="col-9">
                                            <p class="mb-0 fw-bold">${mPlayers.get(mPlayerMostReceivedPasses.playerId).knownName}</p>
                                            <p class="mb-0">
                                                ${mPositions.get(mPlayerMostReceivedPasses.positionId).getDesc(mUser.tvLanguage)}
                                                <c:if test="${mPlayerMostReceivedPasses.positionDetailId != null}">
                                                    <small>(${mPositionDetails.get(mPlayerMostReceivedPasses.positionDetailId).getDesc(mUser.tvLanguage)})</small>
                                                </c:if>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="h-50">
                                    <h6 class="mb-0 text-center"><spring:message code="player.overview.matches.resume"/> <small class="fw-normal">(${mStarterStats.totals})</small></h6>
                                    <div id="matchResumeMatchContainer" style="height: 65%">

                                    </div>
                                </div>
                                <div class="h-50">
                                    <h6 class="mb-0 text-center"><spring:message code="player.overview.average.minutes"/> <small class="fw-normal text-decoration-underline" title="<spring:message code="player.overview.total.minutes"/>">(${Math.round(mPlaytimeStats.totalMinutes * 100.0) / 100})</small></h6>
                                    <div id="ageAverageChartContainer" style="height: 80%">

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-2 d-flex flex-column align-items-center justify-content-center" id="moduleContainer">
                        <h6 class="mb-0 text-center"><spring:message code="player.overview.main.positions"/></h6>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card mb-2">
            <div class="card-body">
                <c:if test="${!mRows.isEmpty() || !mSufferedRows.isEmpty()}">
                    <div class="row">
                        <c:if test="${!mRows.isEmpty()}">
                            <div class="col-6">
                                <h6 class="mb-0 text-center"><spring:message code="team.overview.offensive.metrics"/></h6>
                                <p class="mb-0 text-center text-muted fs-sm">(<spring:message code="player.overview.percentage.values"/>)</p>
                                <div id="offensive-chartdiv" class="vh-40">

                                </div>
                            </div>
                        </c:if>
                        <c:if test="${!mSufferedRows.isEmpty()}">
                            <div class="col-6">
                                <h6 class="mb-0 text-center"><spring:message code="team.overview.defensive.metrics"/></h6>
                                <p class="mb-0 text-center text-muted fs-sm">(<spring:message code="player.overview.percentage.values"/>)</p>
                                <div id="defensive-chartdiv" class="vh-40">

                                </div>
                            </div>
                        </c:if>
                    </div>
                </c:if>
                <c:choose>
                    <c:when test="${mMinPlaytime != null}">
                        <p class="mb-0 w-100 fw-semibold fs-sm text-warning text-center"><spring:message code="messages.playtime.filter.detailed" arguments="${mMinPlaytime}"/></p>
                    </c:when>
                    <c:otherwise>
                        <p class="mb-0 w-100 fw-semibold fs-sm text-warning text-center"><spring:message code="messages.playtime.filter"/></p>
                    </c:otherwise>
                </c:choose>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="card mb-0">
            <div class="card-body pt-0">
                <div class="table-responsive">
                    <table class="table table-hover" id="overview-table">
                        <thead>
                            <tr>
                                <th><spring:message code="player.overview.kpi"/></th>
                                <th class="text-center"><spring:message code="player.overview.ranking"/></th>
                                <th class="text-center"><spring:message code="player.overview.value"/></th>
                                <th class="text-center py-0" style="width: 35vw;">
                                    <div class="text-center d-flex align-items-center justify-content-center">
                                        <img class="me-1 rounded-pill" width="32" height="32" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                        <h6 class="mb-0">${mPlayerName.toUpperCase()} <spring:message code="player.overview.overview"/></h6>
                                    </div>
                                </th>
                                <th class="text-center"><spring:message code="player.overview.min.max"/></th>
                                <th><spring:message code="player.overview.competition.average"/></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="7" class="text-center p-1">
                                    <h6 class="mb-0"><spring:message code="player.overview.offensive.metrics"/> (${mOffensiveIndexAverage}�)</h6>
                                </td>
                            </tr>
                            <c:forEach var="row" items="${mRows}">
                                <tr>
                                    <td class="p-1">
                                        ${row.eventTypeName}
                                        <c:if test="${row.tagTypeName != null}">
                                            <span class="fs-sm">${row.tagTypeName}</span>
                                        </c:if>
                                        <c:if test="${row.zoneAbbName != null}">
                                            <span class="fs-sm text-decoration-underline" title="${row.zoneName}">${row.zoneAbbName}</span>
                                        </c:if>
                                    </td>
                                    <td class="text-center p-1">
                                        ${row.index}�
                                    </td>
                                    <td class="text-center p-1">
                                        <c:choose>
                                            <c:when test="${row.value == -1}">
                                                -
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('TOTALS')}">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.value}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.value)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:when>
                                            <c:otherwise>
                                                ${row.value}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="p-1">
                                        <div class="progress">
                                            <c:set var="coloredPerc" value="${row.progressPercentage}"/>
                                            <c:set var="coloredPercText" value="${row.progressPercentage}%"/>
                                            <c:set var="averagePerc" value="${Math.round((row.average - row.minValue) / (row.maxValue - row.minValue) * 100)}"/>
                                            <c:if test="${row.isOpposite != null && row.isOpposite}">
                                                <c:set var="averagePerc" value="${100 - averagePerc}"/>
                                            </c:if>
                                            <c:set var="averagePerc" value="${averagePerc - row.progressPercentage}"/>
                                            <c:set var="averagePerc" value="${averagePerc}%"/>

                                            <c:if test="${row.value != -1}">
                                                <div class="progress-bar progress-bar-striped bg-${row.getProgressClass()} rounded-end" style="width: ${coloredPercText}" aria-valuenow="${coloredPerc}" aria-valuemin="${row.minValue}" aria-valuemax="${row.maxValue}"></div>
                                                <div style="margin-left: ${averagePerc}; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                                <!--<img class="rounded-pill" width="20" height="20" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'"/>-->
                                            </c:if>
                                        </div>
                                    </td>
                                    <c:choose>
                                        <c:when test="${mDisplayType.equals('TOTALS')}">
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValuePlayer} / ${row.maxValuePlayer}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValuePlayer} / ${row.minValuePlayer}</c:if>" data-bs-original-title="<spring:message code="player.overview.worst.best"/>" data-bs-placement="top">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.minValue} / ${row.maxValue}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.minValue)} / ${Math.round(row.maxValue)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:when>
                                        <c:otherwise>
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValuePlayer} / ${row.maxValuePlayer}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValuePlayer} / ${row.minValuePlayer}</c:if>" data-bs-original-title="<spring:message code="player.overview.worst.best"/>" data-bs-placement="top">
                                                ${row.minValue} / ${row.maxValue}
                                            </td>
                                        </c:otherwise>
                                    </c:choose>
                                    <td class="text-center p-1">
                                        ${row.average}
                                    </td>
                                </tr>
                            </c:forEach>
                            <tr>
                                <td colspan="7" class="text-center p-1">
                                    <h6 class="mb-0"><spring:message code="player.overview.defensive.metrics"/> (${mDefensiveIndexAverage}�)</h6>
                                </td>
                            </tr>
                            <c:forEach var="row" items="${mSufferedRows}">
                                <tr>
                                    <td class="p-1">
                                        ${row.eventTypeName}
                                        <c:if test="${row.tagTypeName != null}">
                                            <span class="fs-sm">${row.tagTypeName}</span>
                                        </c:if>
                                        <c:if test="${row.zoneAbbName != null}">
                                            <span class="fs-sm text-decoration-underline" title="${row.zoneName}">${row.zoneAbbName}</span>
                                        </c:if>
                                    </td>
                                    <td class="text-center p-1">
                                        ${row.index}�
                                    </td>
                                    <td class="text-center p-1">
                                        <c:choose>
                                            <c:when test="${row.value == -1}">
                                                -
                                            </c:when>
                                            <c:when test="${mDisplayType.equals('TOTALS')}">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.value}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.value)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </c:when>
                                            <c:otherwise>
                                                ${row.value}
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td class="p-1">
                                        <div class="progress">
                                            <c:set var="coloredPerc" value="${row.progressPercentage}"/>
                                            <c:set var="coloredPercText" value="${row.progressPercentage}%"/>
                                            <c:set var="averagePerc" value="${Math.round((row.average - row.minValue) / (row.maxValue - row.minValue) * 100)}"/>
                                            <c:if test="${row.isOpposite != null && row.isOpposite}">
                                                <c:set var="averagePerc" value="${100 - averagePerc}"/>
                                            </c:if>
                                            <c:set var="averagePerc" value="${averagePerc - row.progressPercentage}"/>
                                            <c:set var="averagePerc" value="${averagePerc}%"/>

                                            <c:if test="${row.value != -1}">
                                                <div class="progress-bar progress-bar-striped bg-${row.getProgressClass()} rounded-end" style="width: ${coloredPercText}" aria-valuenow="${coloredPerc}" aria-valuemin="${row.minValue}" aria-valuemax="${row.maxValue}"></div>
                                                <div style="margin-left: ${averagePerc}; width: 1px; transform: translateX(-50%); border-left: 1px dashed #000;"></div>
                                                <!--<img class="rounded-pill" width="20" height="20" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/${mPlayerPhoto}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?<%=System.currentTimeMillis()%>'"/>-->
                                            </c:if>
                                        </div>
                                    </td>
                                    <c:choose>
                                        <c:when test="${mDisplayType.equals('TOTALS')}">
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValuePlayer} / ${row.maxValuePlayer}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValuePlayer} / ${row.minValuePlayer}</c:if>" data-bs-original-title="<spring:message code="player.overview.worst.best"/>" data-bs-placement="top">
                                                <c:choose>
                                                    <c:when test="${mAdvancedMetrics.containsKey(row.eventTypeId)}">
                                                        ${row.minValue} / ${row.maxValue}
                                                    </c:when>
                                                    <c:otherwise>
                                                        ${Math.round(row.minValue)} / ${Math.round(row.maxValue)}
                                                    </c:otherwise>
                                                </c:choose>
                                            </td>
                                        </c:when>
                                        <c:otherwise>
                                            <td class="text-center text-decoration-underline p-1" data-bs-popup="popover" data-bs-trigger="hover" data-bs-content="<c:if test="${row.isOpposite == null || !row.isOpposite}">${row.minValuePlayer} / ${row.maxValuePlayer}</c:if><c:if test="${row.isOpposite != null && row.isOpposite}">${row.maxValuePlayer} / ${row.minValuePlayer}</c:if>" data-bs-original-title="<spring:message code="player.overview.worst.best"/>" data-bs-placement="top">
                                                ${row.minValue} / ${row.maxValue}
                                            </td>
                                        </c:otherwise>
                                    </c:choose>
                                    <td class="text-center p-1">
                                        ${row.average}
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-12">
        <div class="row mt-2">
            <c:if test="${!mPlayerLastFixtures.isEmpty()}">
                <div class="col-6">
                    <div class="card mb-0 h-100">
                        <div class="card-body">
                            <h6 class="mb-0 text-center"><spring:message code="player.overview.last.player.matches"/></h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-center"><spring:message code="player.overview.match"/></th>
                                            <th class="text-center"><spring:message code="player.overview.date"/></th>
                                            <th class="text-center"><spring:message code="player.overview.minutes.played"/></th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="row" items="${mPlayerLastFixtures}">
                                            <c:set var="extraClass" value="bg-yellow text-dark-yellow"/>
                                            <c:if test="${row.homeTeamId == mTeamId}">
                                                <c:if test="${row.homeScore > row.awayScore}">
                                                    <c:set var="extraClass" value="bg-success text-dark-success"/>
                                                </c:if>
                                                <c:if test="${row.homeScore < row.awayScore}">
                                                    <c:set var="extraClass" value="bg-danger text-dark-danger"/>
                                                </c:if>
                                            </c:if>
                                            <c:if test="${row.awayTeamId == mTeamId}">
                                                <c:if test="${row.homeScore > row.awayScore}">
                                                    <c:set var="extraClass" value="bg-danger text-dark-danger"/>
                                                </c:if>
                                                <c:if test="${row.homeScore < row.awayScore}">
                                                    <c:set var="extraClass" value="bg-success text-dark-success"/>
                                                </c:if>
                                            </c:if>
                                            <tr class="fs-sm">
                                                <td class="text-center px-1">
                                                    <table class="w-100">
                                                        <tbody>
                                                            <tr>
                                                                <td class="text-end fs-sm" style="width: 35%">
                                                                    ${mTeams.get(row.homeTeamId).getName(mUser.tvLanguage)}
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${row.homeTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(row.homeTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <span class="text-muted fs-sm">${row.homeScore}-${row.awayScore}</span>
                                                                </td>
                                                                <td class="text-center" style="width: 10%">
                                                                    <img width="24" height="24" class="rounded-pill cursor-pointer" onclick="redirectToOverview(${row.awayTeamId});" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${mTeams.get(row.awayTeamId).logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?<%=System.currentTimeMillis()%>'"/>
                                                                </td>
                                                                <td class="fs-sm" style="width: 35%">
                                                                    ${mTeams.get(row.awayTeamId).getName(mUser.tvLanguage)}
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </td>
                                                <td class="text-center px-1">
                                                    ${row.getGameDateString()}
                                                </td>
                                                <td class="text-center px-1">
                                                    ${row.minutesPlayed}
                                                </td>
                                                <td>
                                                    <span class="d-inline-block p-1 fw-bold rounded-2 bg-opacity-75 ${extraClass}">
                                                        <c:if test="${row.homeScore == row.awayScore}">
                                                            <spring:message code="player.overview.d"/>
                                                        </c:if>
                                                        <c:if test="${row.homeTeamId == mTeamId}">
                                                            <c:if test="${row.homeScore > row.awayScore}">
                                                                <spring:message code="player.overview.w"/>
                                                            </c:if>
                                                            <c:if test="${row.homeScore < row.awayScore}">
                                                                <spring:message code="player.overview.l"/>
                                                            </c:if>
                                                        </c:if>
                                                        <c:if test="${row.awayTeamId == mTeamId}">
                                                            <c:if test="${row.homeScore > row.awayScore}">
                                                                <spring:message code="player.overview.l"/>
                                                            </c:if>
                                                            <c:if test="${row.homeScore < row.awayScore}">
                                                                <spring:message code="player.overview.w"/>
                                                            </c:if>
                                                        </c:if>
                                                    </span>
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
            <c:if test="${!mPlayerCareer.isEmpty()}">
                <div class="col-6 ps-0">
                    <div class="card mb-0 h-100">
                        <div class="card-body">
                            <h6 class="mb-0 text-center"><spring:message code="player.overview.player.career"/></h6>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th><spring:message code="player.overview.season"/></th>
                                            <th class="text-center"><spring:message code="player.overview.competition"/></th>
                                            <th class="text-center"><spring:message code="player.overview.team"/></th>
                                            <th class="text-center"><spring:message code="player.overview.matches.played"/></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <c:forEach var="row" items="${mPlayerCareer}">
                                            <c:set var="onClick" value=""/>
                                            <c:if test="${row.clickable}">
                                                <c:set var="onClick" value="changeSeasonCompetitionTeam(${row.seasonId}, ${row.competitionId}, ${row.teamId});"/>
                                            </c:if>
                                            <tr class="${row.clickable ? '' : 'opacity-50'}">
                                                <td class="px-1">
                                                    ${row.season.name}
                                                </td>
                                                <td class="p-1">
                                                    <img class="rounded-pill ${row.clickable ? 'cursor-pointer' : ''}" onclick="${onClick}" width="32" height="32" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/${row.competition.logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/competitionslogo/unknown.png?<%=System.currentTimeMillis()%>'"/>
                                                    ${row.competition.getName(mUser.tvLanguage)}
                                                </td>
                                                <td class="p-1">
                                                    <img class="rounded-pill ${row.clickable ? 'cursor-pointer' : ''}" onclick="${onClick}" width="32" height="32" src="https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/${row.team.logo}.png?<%=System.currentTimeMillis()%>" onerror="this.src='https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknown.png?<%=System.currentTimeMillis()%>'"/>
                                                    ${row.team.getName(mUser.tvLanguage)}
                                                </td>
                                                <td class="p-1 text-center">
                                                    ${row.fixtureAmount}
                                                </td>
                                            </tr>
                                        </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</div>