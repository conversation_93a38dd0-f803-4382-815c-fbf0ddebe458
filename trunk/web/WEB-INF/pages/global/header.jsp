<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!-- Main navbar -->
<div class="navbar navbar-dark navbar-expand-lg navbar-static">
    <div class="container-fluid">
        <div class="d-flex d-lg-none me-2">
            <button type="button" class="navbar-toggler sidebar-mobile-secondary-toggle rounded-pill">
                <i class="ph-arrow-left"></i>
            </button>
        </div>

        <div class="navbar-brand flex-1 flex-lg-0 p-0">
            <a href="/sicsdataanalytics/user/home.htm" class="d-inline-flex align-items-center">
                <img src="/sicsdataanalytics/images/logo_new_white_text.svg" class="d-none d-sm-inline-block h-32px ms-3" alt="">
            </a>
        </div>

        <div class="navbar-collapse justify-content-center flex-lg-1 order-2 order-lg-1 collapse" id="navbar-search">
            <div class="navbar-search flex-fill position-relative mt-2 mt-lg-0 mx-lg-3">
                <div class="form-control-feedback form-control-feedback-start flex-grow-1" data-color-theme="dark">
                    <input type="text" id="navbar-search-input" class="form-control bg-transparent rounded-pill" placeholder="<spring:message code="messages.search"/>" data-bs-toggle="dropdown" autocomplete="off">
                    <div class="form-control-feedback-icon">
                        <i class="ph-magnifying-glass"></i>
                    </div>
                    <div class="dropdown-menu w-100" data-color-theme="light" id="navbar-search-dropdown">
                        <button type="button" class="dropdown-item" onclick="search();">
                            <div class="text-center w-32px me-3">
                                <i class="ph-magnifying-glass"></i>
                            </div>
                            <span><spring:message code="messages.search"/> <span class="fw-bold" id="navbar-search-text">""</span> <spring:message code="messages.search.everywhere"/></span>
                        </button>

                        <div id="navbar-search-result">

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <ul class="nav flex-row justify-content-end align-items-center order-1 order-lg-2">
            <li class="nav-item nav-item-dropdown-lg dropdown">
                <img height="30" width="30" src="/sicsdataanalytics/images/flags/${mUser.tvLanguage}.svg" class="cursor-pointer" data-bs-toggle="dropdown">

                <div class="dropdown-menu dropdown-menu-end p-2">
                    <h5 class="text-center mb-0"><spring:message code="language.menu"/></h5>
                    <p class="text-center text-muted fs-sm mb-1"><spring:message code="language.menu.description"/></p>
                    <c:if test="${!mUser.tvLanguage.equals('it')}">
                        <img height="40" width="40" src="/sicsdataanalytics/images/flags/it.svg" class="cursor-pointer" title="<spring:message code="language.it.desc"/>" onclick="changeLanguage('it', '<spring:message code="error.unexpected"/>')">
                    </c:if>
                    <c:if test="${!mUser.tvLanguage.equals('en')}">
                        <img height="40" width="40" src="/sicsdataanalytics/images/flags/en.svg" class="cursor-pointer ms-1" title="<spring:message code="language.en.desc"/>" onclick="changeLanguage('en', '<spring:message code="error.unexpected"/>')">
                    </c:if>
                </div>
            </li>
            <div class="vr ms-3"></div>
            <li class="nav-item nav-item-dropdown-lg dropdown ms-lg-2">
                <a href="https://server.sics.it/download/glossary/SICS-Glossary%20for%20Customers%202025.pdf" target="_blank" class="navbar-nav-link align-items-center rounded-pill p-1">
                    <i class="ph-info"></i>
                    <span class="d-none d-lg-inline-block mx-lg-2"><spring:message code="messages.glossary"/></span>
                </a>
            </li>
            <li class="nav-item nav-item-dropdown-lg dropdown ms-lg-2">
                <a href="#" class="navbar-nav-link align-items-center rounded-pill p-1" data-bs-toggle="dropdown">
                    <i class="ph-user-circle"></i>
                    <span class="d-none d-lg-inline-block mx-lg-2">${mUser.firstName}</span>
                </a>

                <div class="dropdown-menu dropdown-menu-end">
                    <div class="py-1 px-3">
                        <div class="d-flex align-items-center my-1">
                            <div>
                                <div class="fw-semibold">${mUser.firstName} ${mUser.lastName}</div>
                                <span class="text-muted fs-sm">${mUser.email}</span>
                                <span class="text-muted fs-sm"><spring:message code="messages.license.expiration.date"/> ${mUser.getExpirationDateString()}</span>
                            </div>
                        </div>
                    </div>
                    <div class="dropdown-submenu dropdown-submenu-start dropup">
                        <a href="#" class="dropdown-item"><spring:message code="menu.user.profile"/></a>
                        <div class="dropdown-menu">
                            <a href="#" onclick="changePassword();" class="dropdown-item">
                                <i class="ph-password me-2"></i>
                                <spring:message code="messages.change.password"/>
                            </a>
                            <a href="#" data-bs-toggle="modal" data-bs-target="#modal-preferred-team" class="dropdown-item">
                                <i class="ph-star me-2"></i>
                                <spring:message code="preferred.team"/>
                            </a>
                            <c:if test="${mUser.groupsetId == 1 || mUser.groupsetId == 2 || mUser.groupsetId == 412}">
                                <a href="/sicsdataanalytics/admin/home.htm" class="dropdown-item">
                                    <i class="ph-activity me-2"></i>
                                    Manager
                                </a>
                            </c:if>
                        </div>
                    </div>
                    <div class="dropdown-divider"></div>
                    <a href="/sicsdataanalytics/auth/logout.htm" class="dropdown-item">
                        <i class="ph-sign-out me-2"></i>
                        <spring:message code="menu.user.logout"/>
                    </a>
                </div>
            </li>
        </ul>
    </div>
</div>
<!-- /main navbar -->

<!-- Navigation -->
<div class="navbar navbar-sm shadow">
    <div class="container-fluid">
        <div class="d-flex align-items-center flex-fill overflow-auto overflow-lg-visible scrollbar-hidden">
            <ul class="nav gap-1 flex-nowrap flex-lg-wrap align-items-center">
                <li class="nav-item">
                    <a id="headerHomeButton" href="/sicsdataanalytics/user/home.htm" class="navbar-nav-link rounded">
                        <i class="ph-house me-2"></i>
                        Home
                    </a>
                </li>
                <c:if test="${mPageType != null && mPageType.equals('team')}">
                    <c:if test="${mUser.hasAccess(1)}">
                        <li class="nav-item team-item">
                            <a id="rankingButton" href="/sicsdataanalytics/team/ranking.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/ranking.svg"/>
                                <spring:message code="menu.team.ranking.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(2)}">
                        <li class="nav-item team-item">
                            <a id="trendButton" href="/sicsdataanalytics/team/trend.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/trend.svg"/>
                                <spring:message code="menu.team.trend.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(3)}">
                        <li class="nav-item team-item">
                            <a id="overviewButton" href="/sicsdataanalytics/team/overview.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/overview.svg"/>
                                <spring:message code="menu.team.overview.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(4)}">
                        <li class="nav-item team-item">
                            <a id="positionalButton" href="/sicsdataanalytics/team/positional.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/positional.svg"/>
                                <spring:message code="menu.team.positional.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(5)}">
                        <li class="nav-item team-item">
                            <a id="scatterplotButton" href="/sicsdataanalytics/team/scatterplot.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/scatterplot.svg"/>
                                <spring:message code="menu.team.scatterplot.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(6)}">
                        <li class="nav-item team-item">
                            <a id="radarButton" href="/sicsdataanalytics/team/radar.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/radar.svg"/>
                                <spring:message code="menu.team.radar.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(7)}">
                        <li class="nav-item team-item">
                            <a id="matchesButton" href="/sicsdataanalytics/team/matches.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/matches.svg"/>
                                <spring:message code="menu.team.matches.button"/>
                            </a>
                        </li>
                    </c:if>
                </c:if>
                <c:if test="${mPageType != null && mPageType.equals('player')}">
                    <c:if test="${mUser.hasAccess(21)}">
                        <li class="nav-item player-item">
                            <a id="playerRankingButton" href="/sicsdataanalytics/player/ranking.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/ranking.svg"/>
                                <spring:message code="menu.player.ranking.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(22)}">
                        <li class="nav-item player-item">
                            <a id="playerTrendButton" href="/sicsdataanalytics/player/trend.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/trend.svg"/>
                                <spring:message code="menu.player.trend.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(23)}">
                        <li class="nav-item player-item">
                            <a id="playerOverviewButton" href="/sicsdataanalytics/player/overview.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/overview.svg"/>
                                <spring:message code="menu.player.overview.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(24)}">
                        <li class="nav-item player-item">
                            <a id="playerPositionalButton" href="/sicsdataanalytics/player/positional.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/positional.svg"/>
                                <spring:message code="menu.player.positional.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(25)}">
                        <li class="nav-item player-item">
                            <a id="playerScatterplotButton" href="/sicsdataanalytics/player/scatterplot.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/scatterplot.svg"/>
                                <spring:message code="menu.player.scatterplot.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(26)}">
                        <li class="nav-item player-item">
                            <a id="playerDistributionButton" href="/sicsdataanalytics/player/distribution.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/distribution.svg"/>
                                <spring:message code="menu.player.distribution.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(27)}">
                        <li class="nav-item player-item">
                            <a id="playerRadarButton" href="/sicsdataanalytics/player/radar.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/radar.svg"/>
                                <spring:message code="menu.player.radar.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(28)}">
                        <li class="nav-item player-item">
                            <a id="playerPlaytimeButton" href="/sicsdataanalytics/player/playtime.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/playtime.svg"/>
                                <spring:message code="menu.player.playtime.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(29)}">
                        <li class="nav-item player-item">
                            <a id="playerTopButton" href="/sicsdataanalytics/player/top.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/top.svg"/>
                                <spring:message code="menu.player.top.button"/>
                            </a>
                        </li>
                    </c:if>
                    <c:if test="${mUser.hasAccess(30)}">
                        <li class="nav-item player-item">
                            <a id="playerSimilarityButton" href="/sicsdataanalytics/player/similarity.htm" class="navbar-nav-link rounded">
                                <img class="me-1" width="20" height="20" src="/sicsdataanalytics/images/similarity.svg"/>
                                <spring:message code="menu.player.similarity.button"/>
                            </a>
                        </li>
                    </c:if>
                </c:if>
            </ul>

            <c:if test="${mPageType != null && mIsHomePage == null}">
                <div class="d-flex align-items-center my-auto ms-auto" id="screenshot-container">
                <c:if test="${mPageType.equals('team') && true == false}">
                    <!-- Pulsante Download XValue Report -->
                    <div class="w-100 w-sm-auto me-3">
                        <c:set var="tooltipText" value="<span class='fs-sm'>"/>
                        <c:set var="tooltipText">
                            ${tooltipText}<spring:message code="soccerment.team.report.title"/></span>
                        </c:set>
                        <c:choose>
                            <c:when test="${mUser.soccermentTeamReportExpirationDate != null}">
                                <c:set var="tooltipText">
                                    ${tooltipText}<hr class='m-1'/><span class='fs-sm'><spring:message code='soccerment.team.expiration.date'/> ${mUser.getSoccermentTeamReportExpirationDateString()}</span>
                                </c:set>
                                <c:set var="extraClass" value="has-access"/>
                            </c:when>
                            <c:otherwise>
                                <c:set var="tooltipText">
                                    ${tooltipText}<hr class='m-1'/><span class='fs-sm'><spring:message code='soccerment.team.no.access'/></span>
                                </c:set>
                            </c:otherwise>
                        </c:choose>
                        <c:choose>
                            <c:when test="${mCheckSoccerment == true}">
                                <button type="button" class="btn-flat-info border-transparent py-0 ${extraClass} rounded" onclick="/*downloadXvalueReport();*/" id="soccerment-team-report-button"
                                        data-bs-popup="white-tooltip" data-bs-placement="bottom" data-bs-html="true"
                                        base-title="${tooltipText}"
                                        title="${tooltipText}">
                                    <img src="/sicsdataanalytics/images/xvalue_logo_inverted.svg" alt="Xvalue Report" class="me-1" style="height: 35px;">
                                </button>
                            </c:when>
                            <c:otherwise>
                                <c:set var="tooltipText">
                                    ${tooltipText}<br/><br/><em class='fs-sm text-warning'><spring:message code='soccerment.team.only.profile.page'/></em>
                                </c:set>
                                <button type="button" class="btn-flat-info border-transparent py-0 ${extraClass} rounded" id="soccerment-team-report-button"
                                        data-bs-popup="white-tooltip" data-bs-placement="bottom" data-bs-html="true"
                                        base-title="${tooltipText}"
                                        title="${tooltipText}">
                                    <img src="/sicsdataanalytics/images/xvalue_logo_inverted.svg" alt="Xvalue Report" class="me-1" style="height: 35px;">
                                </button>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </c:if>

                    <div class="w-100 w-sm-auto">
                        <div class="dropdown">
                            <a type="button" class="d-flex align-items-center text-body lh-1 dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="screenshot-dropdown">
                                <img src="/sicsdataanalytics/images/screenshot.svg" class="w-24px h-24px me-1" alt="">
                                <div class="me-auto me-lg-1">
                                    <div class="fw-semibold">Screenshot</div>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="screenshot-dropdown">
                                <li><a class="dropdown-item" href="#" onclick="takeInstantScreenshot();"><i class="ph-camera me-2"></i>Instant Screenshot</a></li>
                                <li><a class="dropdown-item" href="#" onclick="openScreenshotCollectionModal();"><i class="ph-image me-2"></i>Add to Collection</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </c:if>
        </div>
    </div>
</div>
<!-- /navigation -->