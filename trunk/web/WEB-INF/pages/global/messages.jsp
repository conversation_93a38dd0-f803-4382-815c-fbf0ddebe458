<%@ taglib prefix="spring" uri="http://www.springframework.org/tags"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!-- Type 1 Message -->
<div id="chart-message-1" class="content d-flex justify-content-center align-items-center d-none message-div vh-80">
    <!--    <div class="flex-fill">
            <div class="text-center mb-4">
                <h1 class="display-3 fw-semibold lh-1 mb-3">Not Enough Filters</h1>
                <h6 class="w-md-25 mx-md-auto">There are some missing parameters. <br> Season, Competition and Event are required.</h6>
            </div>
        </div>-->
    <div class="d-flex align-items-center justify-content-center">
        <img src="/sicsdataanalytics/images/animated/loading.gif" width="240" style="mix-blend-mode: multiply"/>
    </div>
</div>
<!-- /type 1 message -->

<!-- Type 2 Message -->
<div id="chart-message-2" class="content d-flex justify-content-center align-items-center d-none message-div">
    <div class="flex-fill">
        <div class="text-center mb-4">
            <h1 class="display-3 fw-semibold lh-1 mb-3"><spring:message code="messages.2.title"/></h1>
            <h6 class="w-md-25 mx-md-auto"><spring:message code="messages.2.content"/></h6>
        </div>
    </div>
</div>
<!-- /type 2 message -->

<!-- Type 3 Message -->
<div id="chart-message-3" class="content d-flex justify-content-center align-items-center d-none message-div">
    <div class="flex-fill">
        <div class="text-center mb-4">
            <h1 class="display-3 fw-semibold lh-1 mb-3"><spring:message code="messages.3.title"/></h1>
            <h6 class="w-md-25 mx-md-auto"><spring:message code="messages.3.content"/></h6>
        </div>
    </div>
</div>
<!-- /type 3 message -->