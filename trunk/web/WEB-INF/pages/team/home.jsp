<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    $(document).ready(function () {
        $("#headerHomeButton").addClass("active");
    });
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content">
                    <div class="row">
                        <c:if test="${mUser.hasAccess(1)}">
                            <!-- Ranking block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/ranking.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.ranking"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.ranking.description"/></p>
                                        <a href="/sicsdataanalytics/team/ranking.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.ranking.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /ranking block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(2)}">
                            <!-- Trend block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/trend.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.trend"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.trend.description"/></p>
                                        <a href="/sicsdataanalytics/team/trend.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.trend.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /trend block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(3)}">
                            <!-- Overview block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/overview.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.overview"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.overview.description"/></p>
                                        <a href="/sicsdataanalytics/team/overview.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.overview.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /overview block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(4)}">
                            <!-- Positional block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/positional.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.positional"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.positional.description"/></p>
                                        <a href="/sicsdataanalytics/team/positional.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.positional.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /positional block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(5)}">
                            <!-- Scatterplot block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/scatterplot.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.scatterplot"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.scatterplot.description"/></p>
                                        <a href="/sicsdataanalytics/team/scatterplot.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.scatterplot.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /scatterplot block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(6)}">
                            <!-- Radar block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/radar.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.radar"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.radar.description"/></p>
                                        <a href="/sicsdataanalytics/team/radar.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.radar.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /radar block -->
                        </c:if>
                        <c:if test="${mUser.hasAccess(7)}">
                            <!-- Matches block -->
                            <div class="col-sm-12 col-md-6 col-lg-3 mb-3">
                                <div class="card mb-0 h-100">
                                    <div class="card-body text-center d-flex flex-column flex-grow-1">
                                        <div class="d-inline-flex bg-warning bg-opacity-10 text-success rounded-pill p-3 mb-3 mt-1">
                                            <img width="75" height="75" src="/sicsdataanalytics/images/matches.svg"/>
                                            <div class="d-flex align-items-center justify-content-center flex-fill text-center text-black h5 mb-0">
                                                <spring:message code="menu.team.matches"/>
                                            </div>
                                        </div>
                                        <p class="mb-3"><spring:message code="menu.team.matches.description"/></p>
                                        <a href="/sicsdataanalytics/team/matches.htm" class="btn btn-warning mb-1 mt-auto"><spring:message code="menu.team.matches.button"/></a>
                                    </div>
                                </div>
                            </div>
                            <!-- /matches block -->
                        </c:if>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
