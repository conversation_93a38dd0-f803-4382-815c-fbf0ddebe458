<%@ include file="../global/main.jsp" %>
<script type="text/javascript">
    let userSessions = new Map();
    let userSessionTable, userSessionDetailsTable;

    $(document).ready(function () {
        // $("#headerHomeButton").addClass("active");

        $("#filter-agentid").select2({
            placeholder: "Select an agent",
            allowClear: true
        });

        // Setting datatable defaults
        $.extend($.fn.dataTable.defaults, {
            autoWidth: false,
            dom: '<"datatable-header"fl><"datatable-scroll"t><"datatable-footer"ip>',
            language: {
                search: '<span class="me-3">Filter:</span> <div class="form-control-feedback form-control-feedback-end flex-fill">_INPUT_<div class="form-control-feedback-icon"><i class="ph-magnifying-glass opacity-50"></i></div></div>',
                searchPlaceholder: 'Type to filter...',
                lengthMenu: '<span class="me-3">Show:</span> _MENU_',
                paginate: {'first': 'First', 'last': 'Last', 'next': document.dir === "rtl" ? '&larr;' : '&rarr;', 'previous': document.dir === "rtl" ? '&rarr;' : '&larr;'}
            }
        });

        // Single row selection
        const singleSelect = $('.datatable-selection-single').DataTable({
            paging: true,
            lengthMenu: [
                [10, 15, 25, 50, -1],
                [10, 15, 25, 50, 'All']
            ],
            order: [5, "desc"],
            columnDefs: [
                {
                    type: "date-euro",
                    targets: [5],
                    render: function (data, type, row) {
                        // Se il dato � vuoto, restituisci una data molto grande per i valori "pi� alti"
                        if (data === '') {
                            return type === 'sort' ? '99/99/9999' : data;
                        }
                        return data;
                    }
                }
            ],
            stateSave: true
        });
        $('.datatable-selection-single tbody').on('click', 'tr', function () {
            if ($(this).hasClass('table-success')) {
                $(this).removeClass('table-success');
            } else {
                singleSelect.$('tr.table-success').removeClass('table-success');
                $(this).addClass('table-success');
            }
        });

        userSessionTable = $('#user-session-table').DataTable({
            dom: "t",
            paging: true,
            order: [0, "desc"],
            columnDefs: [
                {
                    type: "date-euro",
                    targets: [0],
                    render: function (data, type, row) {
                        // Se il dato � vuoto, restituisci una data molto grande per i valori "pi� alti"
                        if (data === '') {
                            return type === 'sort' ? '99/99/9999' : data;
                        }
                        return data;
                    }
                }
            ]
        });
        $('.datatable-selection-single-session tbody').on('click', 'tr', function () {
            if ($(this).hasClass('table-success')) {
                $(this).removeClass('table-success');
            } else {
                userSessionTable.$('tr.table-success').removeClass('table-success');
                $(this).addClass('table-success');
            }
        });

        userSessionDetailsTable = $('#user-session-details-table').DataTable({
            dom: "t",
            paging: true,
            order: [1, "desc"]
        });

    <c:forEach var="row" items="${mUsersLogs.values()}">
        userSessions.set(${row.user.id}, eval(${row.getJson()}));
    </c:forEach>
    });

    function loadUserSessions(userId) {
        userSessionTable.clear().draw();
        userSessionDetailsTable.clear().draw();
        if (userSessions.has(userId)) {
            let wrapper = userSessions.get(userId);
            if (wrapper.sessions) {
                let rows = [];
                wrapper.sessions.forEach(function (row) {
                    let columns = [];
                    columns.push(row["sessionCreation"]);
                    columns.push(row["sessionDuration"]);
                    columns.push(row["totalCharts"]);
                    rows.push(columns);
                });
                userSessionTable.rows.add(rows).draw();
                userSessionTable.$('tr').each(function (index, element) {
                    $(element).addClass("cursor-pointer");
                    $(element).attr("onclick", "loadUserSessionDetails(" + userId + "," + index + ");");
                });
            }
        }
    }

    function loadUserSessionDetails(userId, index) {
        userSessionDetailsTable.clear().draw();
        if (userSessions.has(userId)) {
            let wrapper = userSessions.get(userId);
            if (wrapper.sessions) {
                let rows = [];
                if (typeof wrapper.sessions[index] !== "undefined") {
                    if (typeof wrapper.sessions[index].pageCounterMap !== "undefined") {
                        Object.keys(wrapper.sessions[index].pageCounterMap).forEach(function (key) {
                            let columns = [];
                            columns.push(key);
                            columns.push(wrapper.sessions[index].pageCounterMap[key]);
                            rows.push(columns);
                        });
                    }
                }
                userSessionDetailsTable.rows.add(rows).draw();
            }
        }
    }

    function changeAgentId() {
        let agentId = $("#filter-agentid").val();
        if (agentId) {
            location.href = "/sicsdataanalytics/admin/home.htm?agentId=" + agentId;
        } else {
            location.href = "/sicsdataanalytics/admin/home.htm";
        }
    }
</script>

<body>
    <%@ include file="../global/header.jsp" %>

    <!-- Page content -->
    <div class="page-content overflow-auto">

        <!-- Content wrapper -->
        <div class="content-wrapper">

            <!-- Inner content -->
            <div class="content-inner">

                <!-- Content area -->
                <div class="content">
                    <div class="row">
                        <div class="col-6">
                            <div class="card">
                                <div class="card-header">
                                    <div class="d-flex">
                                        <div>
                                            <h3 class="mb-0">USER LIST</h3>
                                            <p class="mb-0 text-muted fs-sm">Data loaded from the past 6 months</p>
                                        </div>
                                        <c:if test="${mUser.groupsetId == 1 || mUser.groupsetId == 2}">
                                            <div class="ms-auto d-flex align-items-center">
                                                <label class="form-label mb-0 me-2">Agent: </label>
                                                <div class="form-control-feedback form-control-feedback-end">
                                                    <select data-container-css-class="select-sm" data-placeholder="Select an agent" class="form-control form-control-sm select" id="filter-agentid" onchange="changeAgentId();" data-minimum-results-for-search="Infinity">
                                                        <option selected></option>
                                                        <c:forEach var="agent" items="${mAgents}">
                                                            <option value="${agent.id}" ${mAgentId != null && agent.id == mAgentId ? 'selected' : ''}>${agent.firstName} ${agent.lastName}</option>
                                                        </c:forEach>
                                                    </select>
                                                </div>
                                            </div>
                                        </c:if>
                                    </div>
                                </div>
                                <div class="card-body pt-0">
                                    <table class="table table-hover w-100 datatable-selection-single">
                                        <thead>
                                            <tr>
                                                <th>User</th>
                                                <th>Groupset</th>
                                                <th>Expiration</th>
                                                <th>Logins</th>
                                                <th>Charts</th>
                                                <th>Last Login</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <c:forEach var="row" items="${mUsersLogs.values()}">
                                                <tr class="cursor-pointer" userid="${row.user.id}" onclick="loadUserSessions(${row.user.id});">
                                                    <td class="py-1">
                                                        ${row.user.firstName} ${row.user.lastName}
                                                        <div class="text-muted fs-sm">
                                                            #${row.user.id}
                                                            <c:if test="${row.user.oraganizationName != null}">
                                                                - ${row.user.oraganizationName}
                                                            </c:if>
                                                        </div>
                                                    </td>
                                                    <td class="py-1">${row.groupsetName}</td>
                                                    <td class="py-1">${row.user.getExpirationDateString()}</td>
                                                    <td class="py-1">${row.totalLogins}</td>
                                                    <td class="py-1">${row.totalCharts}</td>
                                                    <td class="py-1">${row.getLastLoginDateString()}</td>
                                                </tr>
                                            </c:forEach>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="mb-0">USER SESSIONS</h3>
                                    <p class="mb-0 text-muted fs-sm">Click a row in the first table to see all the user sessions</p>
                                </div>
                                <div class="card-body">
                                    <table class="table table-hover w-100 datatable-selection-single-session" id="user-session-table">
                                        <thead>
                                            <tr>
                                                <th>When</th>
                                                <th>Duration</th>
                                                <th>Charts</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <div class="col-3">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="mb-0">USER SESSION DETAILS</h3>
                                    <p class="mb-0 text-muted fs-sm">Click a row in the second table to check the session's activity</p>
                                </div>
                                <div class="card-body">
                                    <table class="table table-hover w-100 datatable-selection-single-session" id="user-session-details-table">
                                        <thead>
                                            <tr>
                                                <th>Page</th>
                                                <th>Times</th>
                                            </tr>
                                        </thead>
                                        <tbody>

                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- /footer -->

            </div>
            <!-- /inner content -->

            <div class="btn-to-top"><button class="btn btn-secondary btn-icon rounded-pill" type="button"><i class="ph-arrow-up"></i></button></div>

        </div>
        <!-- /content wrapper -->

    </div>
    <!-- /page content -->
</body>
