<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">

<configuration>
    <typeAliases>
        <typeAlias type="sics.domain.User"              alias="User"/>
        <typeAlias type="sics.domain.UserPermission"    alias="UserPermission"/>
        <typeAlias type="sics.domain.LogData"           alias="LogData"/>
        <typeAlias type="sics.domain.Competition"       alias="Competition"/>
        <typeAlias type="sics.domain.Team"              alias="Team"/>
        <typeAlias type="sics.domain.Season"            alias="Season"/>
        <typeAlias type="sics.domain.Settings"          alias="Settings"/>
        <typeAlias type="sics.domain.EventType"         alias="EventType"/>
        <typeAlias type="sics.domain.TagType"           alias="TagType"/>
        <typeAlias type="sics.domain.Fixture"           alias="Fixture"/>
        <typeAlias type="sics.domain.Player"            alias="Player"/>
        <typeAlias type="sics.domain.Event"             alias="Event"/>
        <typeAlias type="sics.domain.FixtureDetails"    alias="FixtureDetails"/>
        <typeAlias type="sics.domain.Country"           alias="Country"/>
        <typeAlias type="sics.domain.Foot"              alias="Foot"/>
        <typeAlias type="sics.domain.Position"          alias="Position"/>
        <typeAlias type="sics.domain.FixturePlayer"     alias="FixturePlayer"/>
        <typeAlias type="sics.domain.TeamPlayer"        alias="TeamPlayer"/>
        <typeAlias type="sics.domain.AdvancedMetric"    alias="AdvancedMetric"/>
        <typeAlias type="sics.domain.Filter"            alias="Filter"/>
        <typeAlias type="sics.domain.TeamCareerItem"    alias="TeamCareerItem"/>
        <typeAlias type="sics.domain.TeamData"          alias="TeamData"/>
        <typeAlias type="sics.domain.TeamPlayerData"    alias="TeamPlayerData"/>
        <typeAlias type="sics.domain.PlayerData"        alias="PlayerData"/>
        <typeAlias type="sics.domain.PlayerCareerItem"  alias="PlayerCareerItem"/>
        <typeAlias type="sics.domain.Groupset"          alias="Groupset"/>
        <typeAlias type="sics.domain.TeamPlayerLast"    alias="TeamPlayerLast"/>
        <typeAlias type="sics.domain.PlayerAgency"      alias="PlayerAgency"/>
        <typeAlias type="sics.domain.Group"             alias="Group"/>
        <typeAlias type="sics.domain.AdvancedMetricValue" alias="AdvancedMetricValue"/>
        <typeAlias type="sics.domain.IndexEvent"        alias="IndexEvent"/>
        <typeAlias type="sics.domain.IndexEventType"    alias="IndexEventType"/>
        <typeAlias type="sics.domain.IndexTrend"        alias="IndexTrend"/>
    </typeAliases>

    <mappers>
        <mapper resource="sics/dao/mapper/User.xml"/>
        <mapper resource="sics/dao/mapper/LogData.xml"/>
        <mapper resource="sics/dao/mapper/Competition.xml"/>
        <mapper resource="sics/dao/mapper/Team.xml"/>
        <mapper resource="sics/dao/mapper/Season.xml"/>
        <mapper resource="sics/dao/mapper/Settings.xml"/>
        <mapper resource="sics/dao/mapper/EventType.xml"/>
        <mapper resource="sics/dao/mapper/TagType.xml"/>
        <mapper resource="sics/dao/mapper/Fixture.xml"/>
        <mapper resource="sics/dao/mapper/Player.xml"/>
        <mapper resource="sics/dao/mapper/Event.xml"/>
        <mapper resource="sics/dao/mapper/Country.xml"/>
        <mapper resource="sics/dao/mapper/Foot.xml"/>
        <mapper resource="sics/dao/mapper/Position.xml"/>
        <mapper resource="sics/dao/mapper/FixturePlayer.xml"/>
        <mapper resource="sics/dao/mapper/TeamPlayer.xml"/>
        <mapper resource="sics/dao/mapper/StatsType.xml"/>
        <mapper resource="sics/dao/mapper/Filter.xml"/>
        <mapper resource="sics/dao/mapper/Groupset.xml"/>
        <mapper resource="sics/dao/mapper/PlayerAgency.xml"/>
        <mapper resource="sics/dao/mapper/Group.xml"/>
        <mapper resource="sics/dao/mapper/Index.xml"/>
    </mappers>
</configuration>
