class Queue {
    constructor() {
        this.items = [];
        this.isProcessing = false;
        this.canContinue = true;
    }

    enqueue(item) {
        this.items.push(item);
        if (this.items.length === 1) {
            this.processQueue();
        }
    }

    async processQueue() {
        if (this.isProcessing || !this.canContinue)
            return;

        if (this.items.length === 0) {
            $("#filters-containter").unblock();
            if (typeof updateChart === "function") {
                stopChartLoading = false;
                console.warn("Empty queue, updating chart...");
                updateChart();
            }
            return;
        }

        if ($('#filters-containter').data()["blockUI.isBlocked"] !== 1) {
            blockElement($("#filters-containter"));
        }
        this.isProcessing = true;
        const currentItem = this.items.shift();
//        console.warn("Executing...", currentItem);

        await currentItem();
        this.isProcessing = false;

//        console.error(this.items.length, this.canContinue);
        if (this.canContinue) {
            this.processQueue();
        }
    }
}

var keepFilterValue = true;
var stopChartLoading = false;
const queue = new Queue();

var defaultPEvents = ['34-416', '12', '14-182', '704', '16', '15', '26'], defaultPAdvanced = [], defaultPTactical = [];
var defaultDEvents = ['28', '34-416', '12', '100', '8', '19', '9', '23', '11', '704', '22'], defaultDAdvanced = [], defaultDTactical = [];
var defaultCEvents = ['28', '34-416', '12', '5', '8', '19', '9', '23', '11', '704', '22'], defaultCAdvanced = [], defaultCTactical = [];
var defaultAEvents = ['28', '8', '12', '5', '25', '19', '9', '23', '11'], defaultAAdvanced = ['1015', '1005'], defaultATactical = [];
var defaultEvents = ['8', '100', '12', '5', '25', '23', '9', '101', '11', '6', '26'], defaultAdvanced = ['1002', '1015', '1010', '1008', '1011'], defaultTactical = [];

var eventFilter = {maxFilters: 15};

function animate(elementId) {
    if ($("#" + elementId).length > 0) {
        var element = document.getElementById(elementId).closest("div.animation");
        const animation = element.getAttribute('data-animation');

        element.classList.add('animated', animation);
        element.addEventListener('animationend', function () {
            element.classList.remove('animated', animation);
        });
    }
}

function updateFilters(filterChanged, extraIndex) {
    if (notEmpty(filterChanged)) {
        queue.canContinue = false;
        var continueToLoadFilters = true;

        let additionalFilter = "";
        if (typeof extraIndex !== "undefined") {
            additionalFilter = "-" + extraIndex;
        }

        var params = [];
        let seasonId = $("#filter-seasonid").val();
        // gestione pagina radar, multi stagione
        if (typeof filterChanged !== "undefined" && filterChanged.startsWith("seasonId") && filterChanged !== "seasonId") {
            seasonId = $("#filter-seasonid-" + filterChanged.replace("seasonId", "")).val();
            continueToLoadFilters = false;
        }
        if (notEmpty(seasonId)) {
            if ($.isArray(seasonId)) {
                // se multi stagione seleziono solo la prima per i filtri
                seasonId = seasonId[0];
            }
            params.push("seasonId;" + seasonId);
        }
        let competitionId = $("#filter-competitionid").val();
        if (notEmpty(competitionId) /*&& typeof extraIndex === "undefined"*/) {
            params.push("competitionId;" + competitionId);
        }
        // al cambio di competizione devo ricaricare tutto
        if (typeof filterChanged !== "undefined" && filterChanged === "competitionId") {
            continueToLoadFilters = false;
        }
        let teamIds = $("#filter-teamid").val();
        if (typeof filterChanged === "undefined" || filterChanged !== "competitionId") {
            if (continueToLoadFilters && notEmpty(teamIds) && typeof extraIndex === "undefined") {
                if (typeof teamIds === "string") {
                    params.push("teamId;" + teamIds);
                } else {
                    params.push("teamId;" + teamIds.join("|"));
                }
            }
        }
        // non sono sicuro di questo "filterChanged !== "teamId" || !isPlayerPage()"
        // da capire meglio...
        if (typeof filterChanged === "undefined" || filterChanged !== "teamId") {
            let playerIds = $("#filter-playerid").val();
            if (continueToLoadFilters && notEmpty(playerIds) && typeof extraIndex === "undefined") {
                if (typeof playerIds === "string") {
                    params.push("playerId;" + playerIds);
                } else {
                    params.push("playerId;" + playerIds.join("|"));
                }
            }
        }
        if (typeof filterChanged === "undefined" || (filterChanged !== "seasonId" && filterChanged !== "competitionId")) {
            var eventTypeId = getPageEventType();
            // gestione pagina ranking, multi evento
            if (typeof filterChanged !== "undefined" && filterChanged.startsWith("eventTypeId") && filterChanged !== "eventTypeId") {
                eventTypeId = $("#filter-eventtypeid-" + filterChanged.replace("eventTypeId", "")).val();
            }
            if (notEmpty(eventTypeId)) {
                if ($.isArray(eventTypeId)) {
                    // se voglio che gli eventi vengano comunque filtrati metto il filtro
                    // filter-eventtypeid e in questo caso significa che voglio il multiple
                    // ma allora bisogna passare eventTypeIds alla getFilters altrimenti non va
                    params.push("eventTypeIds;" + eventTypeId.join("|"));
                } else {
                    params.push("eventTypeId;" + eventTypeId);
                }
            }
            let eventTypeIds = $("#filter-eventtypeids").val();
            if (notEmpty(eventTypeIds)) {
                params.push("eventTypeIds;" + eventTypeIds.join("|"));
            }
            if (typeof filterChanged !== "undefined" && filterChanged === "eventTypeIdX") {
                eventTypeId = $("#filter-eventtypeid-x").val();
                if (notEmpty(eventTypeId)) {
                    params.push("eventTypeId;" + eventTypeId);
                }
            } else if (typeof filterChanged !== "undefined" && filterChanged === "eventTypeIdY") {
                eventTypeId = $("#filter-eventtypeid-y").val();
                if (notEmpty(eventTypeId)) {
                    params.push("eventTypeId;" + eventTypeId);
                }
            }
            let tagTypeId = getPageTagType();
            if (notEmpty(tagTypeId)) {
                params.push("tagTypeId;" + tagTypeId.join("|"));
            }
        }
//        let fixtureId = $("#filter-fixtureid").val();
//        if (continueToLoadFilters && notEmpty(fixtureId)) {
//            params.push("fixtureId;" + fixtureId);
//        }
        let isHomeTeam = $("#filter-ishometeam").val();
        if (continueToLoadFilters && notEmpty(isHomeTeam)) {
            params.push("isHomeTeam;" + isHomeTeam);
        }
//        if (typeof matchdaySlider !== "undefined") {
//            let matchday = matchdaySlider.get();
//            if (continueToLoadFilters) {
//                if (parseInt(matchday[0]) !== matchdaySlider.options.range.min || parseInt(matchday[1]) !== matchdaySlider.options.range.max) {
//                    params.push("matchdayFrom;" + parseInt(matchday[0]));
//                    params.push("matchdayTo;" + parseInt(matchday[1]));
//                }
//            }
//        }
        let countryId = $("#filter-countryid").val();
        if (continueToLoadFilters && notEmpty(countryId)) {
            params.push("countryId;" + countryId);
        }
        let positionId = $("#filter-positionid").val();
        if (continueToLoadFilters && notEmpty(positionId)) {
            params.push("positionId;" + positionId);
        }
        let positionDetailId = $("#filter-positiondetailid").val();
        if (continueToLoadFilters && notEmpty(positionDetailId)) {
            params.push("positionDetailId;" + positionDetailId);
        }
        let footId = $("#filter-footid").val();
        if (continueToLoadFilters && notEmpty(footId)) {
            params.push("footId;" + footId);
        }
        if (typeof bornyearSlider !== "undefined") {
            let bornyear = bornyearSlider.get();
            if (continueToLoadFilters) {
                if (parseInt(bornyear[0]) !== bornyearSlider.options.range.min || parseInt(bornyear[1]) !== bornyearSlider.options.range.max) {
                    params.push("bornyearFrom;" + parseInt(bornyear[0]));
                    params.push("bornyearTo;" + parseInt(bornyear[1]));
                }
            }
        }
        let homeModule = $("#filter-homemodule").val();
        if (continueToLoadFilters && notEmpty(homeModule)) {
            params.push("homeModule;" + homeModule);
        }
        let awayModule = $("#filter-awaymodule").val();
        if (continueToLoadFilters && notEmpty(awayModule)) {
            params.push("awayModule;" + awayModule);
        }
        let groupId = $("#filter-groupid").val();
        if (continueToLoadFilters && notEmpty(groupId)) {
            params.push("groupId;" + groupId.join("|"));
        }

        // gestione filtri extra
        var extraElements = $(".additional-filter");
        if (extraElements.length > 0 && typeof extraIndex !== "undefined") {
            for (var i = 0; i < extraElements.length; i++) {
                let splitted = $(extraElements[i]).attr("id").split("-");
                let index = splitted[splitted.length - 1];
                let value = $(extraElements[i]).val();
                if (notEmpty(value)) {
                    let onChangeText = $(extraElements[i]).attr("onchange");
                    if (onChangeText.startsWith("updateFilters") && onChangeText.includes(", " + index + ")") && onChangeText.includes(";")) {
                        // cerco di essere sicuro del contenuto splittando a ; e prendendo il primo pezzo
                        let onChangeSplitted = onChangeText.split(";");
                        if (onChangeSplitted.length > 0) {
                            let onChangeValid = onChangeSplitted[0];
                            let extraFilterId = onChangeValid.replace("updateFilters('", "").replace("updateFiltersAllSeasons('", "").replace("', " + index + ")", "").replace(index, "");
                            if (!extraFilterId.includes("'") && !extraFilterId.includes(")")) {
                                // controlli di sicurezza
                                if (extraFilterId.includes("eventTypeId") || extraFilterId.includes("tagTypeId")) {
                                    let prefix = getPageEventPrefix();
                                    extraFilterId = extraFilterId.replace(prefix, "");
                                }
                                if (!$.isArray(value)) {
                                    //params.push(extraFilterId + "-" + index + ";" + value);
                                    params.push(extraFilterId + ";" + value);
                                } else {
                                    // da verificare ma per ora prendo solo il primo
                                    params.push(extraFilterId + ";" + value[0]);
                                }
                            }
                        }
                    }
                }
            }
        }

        // gestione caricamento di tutti i tag disponibili
        // se nella pagina eventTypeId ha l'attributo multiple allora devo caricarli
        if ($("#filter-eventtypeid").length > 0 && typeof $("#filter-eventtypeid").attr("multiple") !== "undefined") {
            params.push("multiEventTypeId;true");
        }

        // gestione caricamento fixture
        // dato che fa una query su database lo faccio solo se serve -> quando il filtro è presente in pagina
        if ($("#filter-fixtureid").length > 0) {
            params.push("loadFixtures;true");
        }

        if (isPlayerPage()) {
            params.push("type;player");
        } else {
            params.push("type;team");
        }

        if (loadAllEventTypes) {
            params.push("loadAllEventTypes;true");
        }

        var data = encodeURI("parameters=" + params.join("-_-") + "&filterChanged=" + filterChanged);
        console.log("Reloading filters...", data, "triggered by field " + filterChanged);

        var needToUnlock = false;
        if ($('#filters-containter').data()["blockUI.isBlocked"] !== 1) {
            blockElement($("#filters-containter"));
            needToUnlock = true;
        }
        $.ajax({
            type: "GET",
            url: "/sicsdataanalytics/user/getFiltersV2.htm",
            cache: false,
            data: data,
            success: function (result) {
                if (notEmpty(result)) {
                    var object = JSON.parse(result);
//                    console.warn("filters", object, filterChanged);
                    Object.keys(object).forEach(function (filterId) {
                        // non aggiorno mai me stesso
                        if (filterChanged.toLowerCase() !== filterId) {
                            var value = object[filterId];
                            if ($("#filter-" + filterId).length > 0 || filterId === "matchday" || filterId === "bornyear") {  // solo filtri che sono in pagina
                                if (filterId === "matchday" || filterId === "bornyear") {
                                    if (filterId === "matchday") {
                                        if (typeof matchdaySlider !== "undefined") {
                                            let matchday = matchdaySlider.get();
                                            var updateMaxValue = false, updateMinValue = false;
                                            let minValue = parseInt(value.split(",")[0]), maxValue = parseInt(value.split(",")[1]);
                                            if (parseInt(matchday[0]) !== minValue) {
                                                updateMinValue = true;
                                            }
                                            if (parseInt(matchday[1]) !== maxValue) {
                                                updateMaxValue = true;
                                            }
                                            // console.log(updateMinValue, updateMaxValue, matchday, value);

                                            if (matchdaySlider.options.range.max === 100 || maxValue > matchdaySlider.options.range.max) {
                                                matchdaySlider.updateOptions({
                                                    range: {
                                                        'min': matchdaySlider.options.range.min,
                                                        'max': maxValue
                                                    }
                                                });
                                            }

                                            if (updateMaxValue && updateMinValue) {
                                                matchdaySlider.set([minValue, maxValue]);
                                            } else if (updateMinValue) {
                                                matchdaySlider.set([minValue, matchdaySlider.get()[1]]);
                                            } else if (updateMaxValue) {
                                                matchdaySlider.set([matchdaySlider.get()[0], maxValue]);
                                            }

                                            // se ho 1 solo matchday fixo visualizzazione slider
                                            if (matchdaySlider.options.range.min === matchdaySlider.options.range.max) {
                                                matchdaySlider.set([matchdaySlider.options.range.min - 1, matchdaySlider.options.range.max]);
                                            }

                                            animate("noui-slider-drag");
                                        }
                                    } else if (filterId === "bornyear") {
                                        if (typeof bornyearSlider !== "undefined") {
                                            let bornyear = bornyearSlider.get();
                                            var updateMaxValue = false, updateMinValue = false;
                                            if (parseInt(bornyear[0]) === bornyearSlider.options.range.min) {
                                                updateMinValue = true;
                                            }
                                            if (parseInt(bornyear[1]) === bornyearSlider.options.range.max) {
                                                updateMaxValue = true;
                                            }
                                            // console.log(updateMinValue, updateMaxValue, bornyear, value);

                                            bornyearSlider.updateOptions({
                                                range: {
                                                    'min': parseInt(value.split(",")[0]),
                                                    'max': parseInt(value.split(",")[1])
                                                }
                                            });

                                            if (updateMaxValue && updateMinValue) {
                                                bornyearSlider.set([bornyearSlider.options.range.min, bornyearSlider.options.range.max]);
                                            } else if (updateMinValue) {
                                                bornyearSlider.set([bornyearSlider.options.range.min, bornyearSlider.get()[1]]);
                                            } else if (updateMaxValue) {
                                                bornyearSlider.set([bornyearSlider.get()[0], bornyearSlider.options.range.min]);
                                            }

                                            // se ho 1 solo bornyear fixo visualizzazione slider
                                            if (bornyearSlider.options.range.min === bornyearSlider.options.range.max) {
                                                bornyearSlider.set([bornyearSlider.options.range.min - 1, bornyearSlider.options.range.max]);
                                            }

                                            animate("noui-slider-drag-born");
                                        }
                                    }
                                } else {
                                    if (filterId === "tagtypeid") {
                                        if (filterChanged.includes("eventTypeId") && filterChanged !== "eventTypeId") {
                                            filterId = filterChanged.replace("eventTypeId", "").replace((extraIndex || ""), "") + filterId;
                                            console.warn("new filterId", filterId);
                                        }
                                    }
                                    if (typeof extraIndex !== "undefined") {
                                        filterId = filterId + "-" + extraIndex;
                                        if ($("#filter-" + filterId).length === 0) {
                                            return;
                                        }
                                        console.warn("new filterId", filterId);
                                    }

                                    var previousValue = $("#filter-" + filterId).val();

                                    $("#filter-" + filterId + " option").remove();
                                    $("#filter-" + filterId + " optgroup").remove();
                                    if ($("#filter-" + filterId).attr("multiple") !== "multiple") {
                                        $("#filter-" + filterId).append($("<option>"));
                                    }

//                                    console.warn(filterId, value, filterChanged);

                                    var options = value.split(",");
                                    if (value.length === 0) {
                                        if ($("#filter-" + filterId).parent().parent().parent().hasClass("tab-pane")) {
                                            $("[href='#" + $("#filter-" + filterId).parent().parent().parent().attr("id") + "']").addClass("disabled");
                                        }
                                    } else {
                                        if ($("#filter-" + filterId).parent().parent().parent().hasClass("tab-pane")) {
                                            $("[href='#" + $("#filter-" + filterId).parent().parent().parent().attr("id") + "']").removeClass("disabled");
                                        }
                                    }
                                    options.forEach(function (element) {
                                        var elementParts = element.split("|");
                                        var attributes = "";
                                        if (element.includes(";")) {
                                            attributes = element.split(";")[0].split("|");
                                            elementParts = element.split(";")[1].split("|");
                                        }
                                        if (elementParts.length === 2) {
                                            let option = $("<option>", {
                                                value: elementParts[0],
                                                text: elementParts[1]
                                            });
                                            if (attributes.length > 0) {
                                                attributes.forEach(function (attribute) {
                                                    let attributeParts = attribute.split("=");
                                                    option.attr(attributeParts[0], attributeParts[1]);
                                                });
                                            }

                                            $("#filter-" + filterId).append(option);
                                        } else if (elementParts.length === 3) {
                                            // oggetti raggruppati
                                            let optGroupLabel = elementParts[0];
                                            // creo il group se non esiste
                                            if ($("#filter-" + filterId + " optgroup[label='" + optGroupLabel + "']").length === 0) {
                                                $("#filter-" + filterId).append($("<optgroup>", {
                                                    label: optGroupLabel
                                                }));
                                            }
                                            $("#filter-" + filterId + " optgroup[label='" + optGroupLabel + "']").append($("<option>", {
                                                value: elementParts[1],
                                                text: elementParts[2]
                                            }));
                                        }
                                    });
                                    // faccio il sort in Java per le competizioni
                                    if (!filterId.includes("competitionid")) {
                                        sortFilterOptions(filterId);
                                    }
                                    if ($("#filter-" + filterId).attr("multiple") === "multiple") {
                                        $("#filter-" + filterId).multiselect('rebuild');
                                    }

                                    if ($("#filter-" + filterId).hasClass("load-all-on-change") && $("#filter-" + filterId).attr("multiple") === "multiple") {
                                        // se la select ha la classe "load-all-on-change" imposto tutti i valori
                                        $("#filter-" + filterId).find("option").each(function () {
                                            $("#filter-" + filterId).multiselect('select', $(this).val());
                                        });
                                        $("#filter-" + filterId).trigger("change");
                                    } else {
                                        if (keepFilterValue) {
                                            // console.log(filterId, previousValue, notEmpty(previousValue));
                                            if (notEmpty(previousValue)) {
                                                // se la select ha quel valore allora lo imposto
                                                if ($("#filter-" + filterId).attr("multiple") === "multiple") {
                                                    previousValue.forEach(function (element) {
                                                        $("#filter-" + filterId).multiselect('select', element);
                                                    });
                                                    // $("#filter-" + filterId).trigger("change");
                                                } else if ($("#filter-" + filterId + " option[value='" + previousValue + "']").length > 0) {
                                                    if ($.isArray(previousValue)) {
                                                        $("#filter-" + filterId).val(previousValue);
                                                    } else {
                                                        $("#filter-" + filterId).val(previousValue);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    animate("filter-" + filterId);
                                }
                            }
                        }
                    });

                    if (typeof customUpdateFilters === "function") {
                        customUpdateFilters(object, filterChanged);
                    }
                    updateEventFilter();

                    manageUpdateChart();
//                    if (notEmpty($("#filter-seasonid").val()) && notEmpty($("#filter-competitionid").val()) && ($("#filter-eventtypeid").length === 0 || notEmpty($("#filter-eventtypeid").val()))) {
//                        updateChart(filterChanged);
//                    }
                }
                if (needToUnlock) {
                    $("#filters-containter").unblock();
                }
                updateFiltersColor();
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            },
            error: function () {
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
                if (needToUnlock) {
                    $("#filters-containter").unblock();
                }
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "Something went wrong!",
                    icon: "error",
                    padding: 40
                });
            }
        });
    }
}

function updateFiltersAllSeasons(filterChanged, extraIndex) {
    if (notEmpty(filterChanged)) {
        queue.canContinue = false;

        var additionalFilter = "";
        var params = [];
        var competitionIdFilter = "competitionid";
        if (typeof filterChanged !== "undefined" && filterChanged.startsWith("competitionId") && filterChanged !== "competitionId") {
            additionalFilter = "-" + filterChanged.replace("competitionId", "");
            competitionIdFilter = "competitionid" + additionalFilter;
        }
        let competitionId = $("#filter-" + competitionIdFilter).val();
        if (notEmpty(competitionId)) {
            params.push("competitionId;" + competitionId);
        }
        let teamIds = $("#filter-teamid" + additionalFilter).length > 0 ? $("#filter-teamid" + additionalFilter).val() : $("#filter-teamid").val();
        if (notEmpty(teamIds) && filterChanged !== "competitionId") {
            if (typeof teamIds === "string") {
                params.push("teamId;" + teamIds);
            } else {
                params.push("teamId;" + teamIds.join("|"));
            }
        }
        if (isPlayerPage()) {
            params.push("type;player");
        } else {
            params.push("type;team");
        }

        var data = encodeURI("parameters=" + params.join("-_-") + "&filterChanged=" + filterChanged);
        console.log("Reloading filters...", data, "triggered by field " + filterChanged);

        var needToUnlock = false;
        if ($('#filters-containter').data()["blockUI.isBlocked"] !== 1) {
            blockElement($("#filters-containter"));
            needToUnlock = true;
        }
        $.ajax({
            type: "GET",
            url: "/sicsdataanalytics/user/getFiltersAllSeasons.htm",
            cache: false,
            data: data,
            success: function (result) {
                if (notEmpty(result)) {
                    var object = JSON.parse(result);
//                    console.warn(object, filterChanged);
                    Object.keys(object).forEach(function (filterId) {
                        // non aggiorno mai me stesso
                        if (filterChanged.toLowerCase() !== filterId) {
                            var value = object[filterId];
                            if (notEmpty(value)) {
                                filterId += additionalFilter; // nel caso sto aggiornando "competitionId1" aggiorno teamId1, playerId1...
                                if ($("#filter-" + filterId).length > 0) { // solo filtri che sono in pagina
                                    var previousValue = $("#filter-" + filterId).val();

                                    $("#filter-" + filterId + " option").remove();
                                    $("#filter-" + filterId + " optgroup").remove();
                                    if ($("#filter-" + filterId).attr("multiple") !== "multiple") {
                                        $("#filter-" + filterId).append($("<option>"));
                                    }

//                                    console.warn(filterId, value, filterChanged);

                                    var options = value.split(",");
                                    options.forEach(function (element) {
                                        var elementParts = element.split("|");
                                        if (elementParts.length === 2) {
                                            $("#filter-" + filterId).append($("<option>", {
                                                value: elementParts[0],
                                                text: elementParts[1]
                                            }));
                                        } else if (elementParts.length === 3) {
                                            // oggetti raggruppati
                                            let optGroupLabel = elementParts[0];
                                            // creo il group se non esiste
                                            if ($("#filter-" + filterId + " optgroup[label='" + optGroupLabel + "']").length === 0) {
                                                $("#filter-" + filterId).append($("<optgroup>", {
                                                    label: optGroupLabel
                                                }));
                                            }
                                            $("#filter-" + filterId + " optgroup[label='" + optGroupLabel + "']").append($("<option>", {
                                                value: elementParts[1],
                                                text: elementParts[2]
                                            }));
                                        }
                                    });
                                    // faccio il sort in Java per le competizioni
                                    if (!filterId.includes("competitionid")) {
                                        sortFilterOptions(filterId);
                                    }
                                    if ($("#filter-" + filterId).attr("multiple") === "multiple") {
                                        $("#filter-" + filterId).multiselect('rebuild');
                                    }

                                    if ($("#filter-" + filterId).hasClass("load-all-on-change") && $("#filter-" + filterId).attr("multiple") === "multiple") {
                                        // se la select ha la classe "load-all-on-change" imposto tutti i valori
                                        $("#filter-" + filterId).find("option").each(function () {
                                            $("#filter-" + filterId).multiselect('select', $(this).val());
                                        });
                                        $("#filter-" + filterId).trigger("change");
                                    } else {
                                        if (keepFilterValue) {
                                            // console.log(filterId, previousValue, notEmpty(previousValue));
                                            if (notEmpty(previousValue)) {
                                                // se la select ha quel valore allora lo imposto
                                                if ($("#filter-" + filterId + " option[value='" + previousValue + "']").length > 0) {
                                                    if ($.isArray(previousValue)) {
                                                        $("#filter-" + filterId).val(previousValue).trigger('change');
                                                    } else {
                                                        $("#filter-" + filterId).val(previousValue);
                                                    }
                                                }
                                            }
                                        }
                                    }

                                    animate("filter-" + filterId);
                                }
                            }
                        }
                    });

                    if (typeof customUpdateFilters === "function") {
                        customUpdateFilters(object);
                    }

                    manageUpdateChart();
//                    if (notEmpty($("#filter-seasonid").val()) && notEmpty($("#filter-competitionid").val()) && ($("#filter-eventtypeid").length === 0 || notEmpty($("#filter-eventtypeid").val()))) {
//                        updateChart(filterChanged);
//                    }
                }
                if (needToUnlock) {
                    $("#filters-containter").unblock();
                }
                updateFiltersColor();
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            },
            error: function () {
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
                if (needToUnlock) {
                    $("#filters-containter").unblock();
                }
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: "Something went wrong!",
                    icon: "error",
                    padding: 40
                });
            }
        });
    }
}

function updateMaxEventAmount(maxValue) {
    if (maxValue === eventAmountSlider.options.range.max) {
        return;
    }
    // console.log(updateMinValue, updateMaxValue, matchday, value);

    eventAmountSlider.updateOptions({
        range: {
            'min': eventAmountSlider.options.range.min,
            'max': maxValue
        }
    });

    eventAmountSlider.set([eventAmountSlider.get()[0], maxValue]);

    // se ho 1 solo valore fixo visualizzazione slider
    if (eventAmountSlider.options.range.min === eventAmountSlider.options.range.max) {
        eventAmountSlider.set([eventAmountSlider.options.range.min - 1, eventAmountSlider.options.range.max]);
    }

    animate("noui-slider-drag-events");
}

function updateMaxEventAmountY(maxValue) {
    if (maxValue === eventAmountSliderY.options.range.max) {
        return;
    }
    // console.log(updateMinValue, updateMaxValue, matchday, value);

    eventAmountSliderY.updateOptions({
        range: {
            'min': eventAmountSliderY.options.range.min,
            'max': maxValue
        }
    });

    eventAmountSliderY.set([eventAmountSliderY.get()[0], maxValue]);

    // se ho 1 solo valore fixo visualizzazione slider
    if (eventAmountSliderY.options.range.min === eventAmountSliderY.options.range.max) {
        eventAmountSliderY.set([eventAmountSliderY.options.range.min - 1, eventAmountSliderY.options.range.max]);
    }

    animate("noui-slider-drag-events-y");
}

function updateMaxPlaytime(maxValue) {
    if (maxValue === playtimeSlider.options.range.max) {
        return;
    }
    // console.log(updateMinValue, updateMaxValue, matchday, value);

    playtimeSlider.updateOptions({
        range: {
            'min': playtimeSlider.options.range.min,
            'max': maxValue
        }
    });

    playtimeSlider.set([playtimeSlider.get()[0], maxValue]);

    // se ho 1 solo valore fixo visualizzazione slider
    if (playtimeSlider.options.range.min === playtimeSlider.options.range.max) {
        playtimeSlider.set([playtimeSlider.options.range.min - 1, playtimeSlider.options.range.max]);
    }

    animate("noui-slider-drag-playtime");
    if (typeof updateChart === "function") {
        stopChartLoading = false;
        updateChart();
    }
}

function manageUpdateChart() {
    if (!queue.isProcessing && queue.items.length === 0 && !stopChartLoading) {
        if (typeof updateChart === "function") {
            updateChart();
        }
    }
}

function sortFilterOptions(filterId) {
    var options = $("#filter-" + filterId + " option");
    var groupedOptions = {};
    var ungroupedOptions = [];
    let finalKeys = [];

    // Separate options into groups and ungrouped options
    options.each(function (_, o) {
        var optgroupLabel = $(o).parent('optgroup').attr('label') || 'No Group';
        if (optgroupLabel === 'No Group') {
            finalKeys.push($(o).text());
            let tmpOption = {
                t: $(o).text(),
                v: o.value,
                attributes: {}
            };
            $(o).each(function () {
                // 'this' refers to the DOM element
                var attributes = this.attributes; // Access the attributes property

                // Iterate through all attributes
                $.each(attributes, function (_, attr) {
                    if (attr.name !== "value" && attr.name !== "data-select2-id") {
                        tmpOption.attributes[attr.name] = attr.value;
                    }
                });
            });

            ungroupedOptions.push(tmpOption);
        } else {
            if (!groupedOptions[optgroupLabel]) {
                groupedOptions[optgroupLabel] = [];
            }
            let tmpOption = {
                t: $(o).text(),
                v: o.value,
                attributes: {}
            };
            $(o).each(function () {
                // 'this' refers to the DOM element
                var attributes = this.attributes; // Access the attributes property

                // Iterate through all attributes
                $.each(attributes, function (_, attr) {
                    if (attr.name !== "value" && attr.name !== "data-select2-id") {
                        tmpOption.attributes[attr.name] = attr.value;
                    }
                });
            });
            groupedOptions[optgroupLabel].push(tmpOption);
        }
    });

    // Sort ungrouped options
    ungroupedOptions.sort(function (o1, o2) {
        return o1.t.localeCompare(o2.t);
    });

    // Sort the options within each group
    for (var group in groupedOptions) {
        groupedOptions[group].sort(function (o1, o2) {
            return o1.t.localeCompare(o2.t);
        });
    }

    // Create a new array to hold the sorted options
    var sortedOptions = [];

    // Sort groups by their label and add grouped options
    var groupKeys = Object.keys(groupedOptions);
    groupKeys.sort(function (o1, o2) {
        let o1Exists = false, o2Exists = false;
        groupedOptions[o1].forEach(function (tag) {
            if (tag.t === o1) {
                o1Exists = true;
            }
        });
        groupedOptions[o2].forEach(function (tag) {
            if (tag.t === o2) {
                o2Exists = true;
            }
        });
        if (o1Exists && o2Exists) {
            return o1.localeCompare(o2);
        } else {
            return o1;
        }
    });

    if (groupKeys.length > 0) {
        finalKeys = finalKeys.concat(groupKeys);
        finalKeys.sort();
        finalKeys.forEach(function (key) {
            if (groupKeys.indexOf(key) > -1) {
                if (key !== globalMessages.get("filters.event.advanced.metrics") && key !== globalMessages.get("filters.event.tactical")) {
                    // opt group
                    sortedOptions.push('<optgroup label="' + key + '">');
                    groupedOptions[key].forEach(function (option) {
                        if (key === option.t) {
                            sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
                        }
                    });
                    groupedOptions[key].forEach(function (option) {
                        if (key !== option.t) {
                            sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
                        }
                    });
                    sortedOptions.push('</optgroup>');
                }
            } else {
                // singolo
                ungroupedOptions.forEach(function (option) {
                    if (key === option.t) {
                        sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
                    }
                });
            }
        });

        // advanced metrics and tacticals
        if (finalKeys.indexOf(globalMessages.get("filters.event.advanced.metrics")) > -1) {
            let key = finalKeys[finalKeys.indexOf(globalMessages.get("filters.event.advanced.metrics"))];
            sortedOptions.push('<optgroup label="' + key + '">');
            groupedOptions[key].forEach(function (option) {
                sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
            });
            sortedOptions.push('</optgroup>');
        }
        if (finalKeys.indexOf(globalMessages.get("filters.event.tactical")) > -1) {
            let key = finalKeys[finalKeys.indexOf(globalMessages.get("filters.event.tactical"))];
            sortedOptions.push('<optgroup label="' + key + '">');
            groupedOptions[key].forEach(function (option) {
                sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
            });
            sortedOptions.push('</optgroup>');
        }
    } else {
        // Add ungrouped options first
        ungroupedOptions.forEach(function (option) {
            sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
        });

        for (var i = 0; i < groupKeys.length; i++) {
            var group = groupKeys[i];
            sortedOptions.push('<optgroup label="' + group + '">');
            groupedOptions[group].forEach(function (option) {
                sortedOptions.push('<option value="' + option.v + '"' + getOptionAttributes(option) + '>' + option.t + '</option>');
            });
            sortedOptions.push('</optgroup>');
        }
    }

    // Clear existing options and append sorted ones
    $("#filter-" + filterId).empty().append(sortedOptions.join(''));
}

function getOptionAttributes(option) {
    let attributes = [];
    if (!jQuery.isEmptyObject(option.attributes)) {
        Object.keys(option.attributes).forEach(function (name) {
            attributes.push(name + '="' + option.attributes[name] + '"');
        });
    }

    return attributes.join(" ");
}

function updateFiltersColor() {
    $("#filters-containter select.form-control.select.is-filter").each(function (index, element) {
        if (notEmpty($(element).val())) {
            if ($(element).parent().find("span.select2-selection.select2-selection--single").hasClass("border-danger")) {
                $(element).parent().find("span.select2-selection.select2-selection--single").removeClass("border-danger border-bottom-width-3 animated pulse");
            }
            $(element).parent().find("span.select2-selection.select2-selection--single").addClass("border-success border-bottom-width-3");
        } else {
            $(element).parent().find("span.select2-selection.select2-selection--single").removeClass("border-success border-bottom-width-3");
            if ($(element).hasClass("is-required")) {
                $(element).parent().find("span.select2-selection.select2-selection--single").addClass("border-danger border-bottom-width-3 animated pulse");
            }
        }
    });

    $("#filters-containter select.form-control.multiselect.is-filter").each(function (index, element) {
        if (notEmpty($(element).val())) {
            if ($(element).parent().find("button.multiselect.dropdown-toggle.btn").hasClass("border-danger")) {
                $(element).parent().find("button.multiselect.dropdown-toggle.btn").removeClass("border-danger border-bottom-width-3 animated pulse");
            }
            $(element).parent().find("button.multiselect.dropdown-toggle.btn").addClass("border-success border-bottom-width-3");
        } else {
            $(element).parent().find("button.multiselect.dropdown-toggle.btn").removeClass("border-success border-bottom-width-3");
            if ($(element).hasClass("is-required")) {
                $(element).parent().find("button.multiselect.dropdown-toggle.btn").addClass("border-danger border-bottom-width-3 animated pulse");
            }
        }
    });
}

function resetFilter(element) {
    if (typeof element === "undefined") {
        if (typeof event.target === "object") {
            if ($(event.target).is("button")) {
                bootstrap.Tooltip.getInstance($(event.target)).hide();
            }

            var parent = $(event.target).parent();
            if ($(parent).is("button")) {
                bootstrap.Tooltip.getInstance($(parent)).hide();
                parent = $(parent).parent();
            }
            var select = $(parent).find("select");
            element = $(select).attr("id");
        } else {
            console.warn("Unable to reset filter. typeof target is", typeof event.target);
        }
    }

    if (element) {
        var select = $("#" + element);
        $(select).val(null).trigger("change");

        if ($(select).attr("multiple") === "multiple") {
            $(select).multiselect('clearSelection');
        }
    }
}

function resetFilters() {
    sweetAlert.fire({
        title: globalMessages.get("action.confirmation"),
        html: globalMessages.get("filters.reset.confirm"),
        icon: 'warning',
        showCancelButton: true,
        confirmButtonText: globalMessages.get("action.yes"),
        cancelButtonText: globalMessages.get("action.no"),
        buttonsStyling: false,
        customClass: {
            confirmButton: 'btn btn-success',
            cancelButton: 'btn btn-danger'
        }
    }).then(function (result) {
        if (result.value) {
            let prefix = "dataanalytics/610";
            var keysToRemove = [];
            for (var i = 0; i < localStorage.length; i++) {
                let key = localStorage.key(i);
                if (key.startsWith(prefix) && (key.includes("/filter-") || key.includes("/event-filter") || key.startsWith(prefix + "/player/") || key.startsWith(prefix + "/team/"))) {
                    let splitted = key.split("/");
                    if ((!key.includes("-seasonid") && !key.includes("-competitionid") && !key.includes("-teamid") && !key.includes("-playerid")) || splitted.length > 4) {
                        keysToRemove.push(key);
                    }
                }
            }
            keysToRemove.forEach(function (key) {
                localStorage.removeItem(key);
            });
            location.reload();
        }
    });
}

function managePersonalFilters() {
    if (typeof event.target !== "undefined") {
        $(event.target).toggleClass("text-success");
    }
    $("#personal-filters-container").toggleClass("d-none");
}

function addPersonalFilter() {
    let filters = getPageFilters();
    if (filters) {
        let valid = true;
        filters.forEach(function (filter) {
            let parts = filter.split("||");
            if (parts.length === 2) {
                // limite di 512 caratteri per la colonna filter_value di da_filter_detail
                if (parts[1].length > 512) {
                    valid = false;
                }
            } else {
                valid = false;
            }
        });
        if (!valid) {
            new Noty({
                text: globalMessages.get("personal.filter.too.long"),
                type: "danger",
                layout: "topCenter"
            }).show();
        }

        bootbox.prompt({
            title: globalMessages.get("personal.filter.name"),
            value: globalMessages.get("personal.filter.name.default"),
            buttons: {
                confirm: {
                    label: globalMessages.get("personal.filter.save"),
                    className: 'btn-primary'
                },
                cancel: {
                    label: globalMessages.get("personal.filter.cancel"),
                    className: 'btn-link'
                }
            },
            callback: function (result) {
                let page = getPageName();
                var data = encodeURI("name=" + result + "&page=" + page + "&filters=" + filters.join("-_-"));
                if (result !== null) {
                    $.ajax({
                        type: "POST",
                        url: "/sicsdataanalytics/user/saveFilter.htm",
                        cache: false,
                        data: data,
                        success: function (msg) {
                            if (msg === "ok") {
                                new Noty({
                                    text: globalMessages.get("personal.filter.save.confirm"),
                                    type: "success",
                                    layout: "topCenter"
                                }).show();

                                // aspetto un pò prima di ricaricare la pagina
                                setTimeout(function () {
                                    location.reload();
                                }, 1000);
                            } else {
                                new Noty({
                                    text: globalMessages.get("error.unexpected"),
                                    type: "error",
                                    layout: "topCenter"
                                }).show();
                            }
                        }
                    });
                }
            }
        });
    } else {
        new Noty({
            text: globalMessages.get("error.unexpected"),
            type: "warning",
            layout: "topCenter"
        }).show();
    }
}

function updatePersonalFilter() {
    let id = parseInt($("#personal-filter").val());
    if (id) {
        if (personalFilters.has(id)) {
            let filters = getPageFilters();
            if (filters) {
                let valid = true;
                filters.forEach(function (filter) {
                    let parts = filter.split("||");
                    if (parts.length === 2) {
                        // limite di 512 caratteri per la colonna filter_value di da_filter_detail
                        if (parts[1].length > 512) {
                            valid = false;
                        }
                    } else {
                        valid = false;
                    }
                });
                if (!valid) {
                    new Noty({
                        text: globalMessages.get("personal.filter.too.long"),
                        type: "danger",
                        layout: "topCenter"
                    }).show();
                }

                bootbox.dialog({
                    title: globalMessages.get("personal.filter.name"),
                    message: "<input type='text' class='form-control' id='filter-name' value='" + personalFilters.get(id).name + "'>",
                    buttons: {
                        delete: {
                            label: globalMessages.get("personal.filter.delete"),
                            className: 'btn-danger',
                            callback: function () {
                                deletePersonalFilter();
                            }
                        },
                        cancel: {
                            label: globalMessages.get("personal.filter.cancel"),
                            className: 'btn-light'
                        },
                        confirm: {
                            label: globalMessages.get("personal.filter.update"),
                            className: 'btn-primary',
                            callback: function () {
                                let name = $("#filter-name").val();
                                if (name) {
                                    let page = getPageName();
                                    var data = encodeURI("id=" + id + "&name=" + $("#filter-name").val() + "&page=" + page + "&filters=" + filters.join("-_-"));
                                    $.ajax({
                                        type: "POST",
                                        url: "/sicsdataanalytics/user/saveFilter.htm",
                                        cache: false,
                                        data: data,
                                        success: function (msg) {
                                            if (msg === "ok") {
                                                new Noty({
                                                    text: globalMessages.get("personal.filter.update.confirm"),
                                                    type: "success",
                                                    layout: "topCenter"
                                                }).show();

                                                // aspetto un pò prima di ricaricare la pagina
                                                setTimeout(function () {
                                                    location.reload();
                                                }, 1000);
                                            } else {
                                                new Noty({
                                                    text: globalMessages.get("error.unexpected"),
                                                    type: "error",
                                                    layout: "topCenter"
                                                }).show();
                                            }
                                        }
                                    });
                                }
                            }
                        }
                    }
                });
            } else {
                new Noty({
                    text: globalMessages.get("error.unexpected"),
                    type: "warning",
                    layout: "topCenter"
                }).show();
            }
        } else {
            new Noty({
                text: globalMessages.get("error.unexpected"),
                type: "warning",
                layout: "topCenter"
            }).show();
        }
    } else {
        new Noty({
            text: globalMessages.get("error.unexpected"),
            type: "warning",
            layout: "topCenter"
        }).show();
    }
}

function deletePersonalFilter() {
    let id = parseInt($("#personal-filter").val());
    if (id) {
        if (personalFilters.has(id)) {
            sweetAlert.fire({
                title: globalMessages.get("action.confirmation"),
                html: globalMessages.get("personal.filter.delete.question"),
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: globalMessages.get("action.yes"),
                cancelButtonText: globalMessages.get("action.no"),
                buttonsStyling: false,
                customClass: {
                    confirmButton: 'btn btn-success',
                    cancelButton: 'btn btn-danger'
                }
            }).then(function (result) {
                if (result.value) {
                    var data = encodeURI("id=" + id);
                    $.ajax({
                        type: "POST",
                        url: "/sicsdataanalytics/user/deleteFilter.htm",
                        cache: false,
                        data: data,
                        success: function (msg) {
                            if (msg === "ok") {
                                new Noty({
                                    text: globalMessages.get("personal.filter.delete.confirm"),
                                    type: "success",
                                    layout: "topCenter"
                                }).show();

                                // aspetto un pò prima di ricaricare la pagina
                                setTimeout(function () {
                                    location.reload();
                                }, 1000);
                            } else {
                                new Noty({
                                    text: globalMessages.get("error.unexpected"),
                                    type: "error",
                                    layout: "topCenter"
                                }).show();
                            }
                        }
                    });
                }
            });
        } else {
            new Noty({
                text: globalMessages.get("error.unexpected"),
                type: "warning",
                layout: "topCenter"
            }).show();
        }
    } else {
        new Noty({
            text: globalMessages.get("error.unexpected"),
            type: "warning",
            layout: "topCenter"
        }).show();
    }
}

function loadPersonalFilter() {
    $("#personal-filter-update").attr("disabled", "disabled");
    let id = parseInt($("#personal-filter").val());
    if (id) {
        if (personalFilters.has(id)) {
            // localStorage.setItem("dataanalytics/610/filter", id);
            let filtersToRemove = getPageFilters();
            if (filtersToRemove) {
                filtersToRemove.forEach(function (filter) {
                    let parts = filter.split("||");
                    if (parts.length === 2) {
                        localStorage.removeItem(parts[0]);
                    }
                });
            }

            let filtersToLoad = personalFilters.get(id).filters;
            if (filtersToLoad) {
                Object.keys(filtersToLoad).forEach(function (filter) {
                    var value = filtersToLoad[filter];
                    localStorage.setItem(filter, value);
                });
            }

            $("#personal-filter-update").removeAttr("disabled");
            new Noty({
                text: globalMessages.get("personal.filter.loaded"),
                type: "success",
                layout: "topCenter"
            }).show();
            checkInitialFilters();
            delete eventFilter.currentFilter;
            updateEventFilter();
        } else {
            new Noty({
                text: globalMessages.get("error.unexpected"),
                type: "warning",
                layout: "topCenter"
            }).show();
        }
    } else {
        new Noty({
            text: globalMessages.get("error.unexpected"),
            type: "warning",
            layout: "topCenter"
        }).show();
    }
}

var filtersLoaded = true, filtersAllLoaded = true;
function checkInitialFilters() {
    // Michele ha deciso che se non ci sono filtri selezionati devo sempre comunque mostrare un grafico...
    // carico quindi un filtro alla volta fino ad avere il grafico
    let prefix = "dataanalytics/610/";

//    blockElement($("#filters-containter"));
//    stopChartLoading = true;
    filtersAllLoaded = false;
    let seasonId = localStorage.getItem(prefix + "B/filter-seasonid");
    if (seasonId === null) {
        // non ci sono filtri, imposto sempre la prima scelta fino a che non vedo il grafico
        // sicuramente devo impostare la competizione
        if ($("#filter-seasonid").attr("multiple") === "multiple") {
            $("#filter-seasonid").multiselect("select", $('#filter-seasonid option:first-child').val()).change();
        } else {
            $("#filter-seasonid").prop("selectedIndex", 0).change();
        }
        // filtersLoaded = true;   // non so se il change sopra triggera la getFilters quindi per sicurezza rilascio
    } else {
        // carico tutto quello che posso
        console.group("%c Loading filters from storage...", 'color: green; font-size: 2em;');
        stopChartLoading = true;
        var validKeys = [];
        for (var i = 0; i < localStorage.length; i++) {
            let key = localStorage.key(i);
            if (key.startsWith(prefix) && key.includes("/filter-")) {
                validKeys.push(key);
            }
        }

        let clonedLocalStorage = $.extend({}, localStorage);
        validKeys = validKeys.sort();
        validKeys.forEach(function (key) {
            queue.enqueue(() => {
                // queue.canContinue = true;
                if (key.startsWith(prefix) && key.includes("/filter-")) {
                    let splitted = key.split("/");
                    if (splitted.length === 4 || splitted.length === 7) {
                        let filterId = splitted[splitted.length - 1];
                        var filterValue = clonedLocalStorage[key];

                        if (splitted.length === 7) {
                            // evento extra
                            if (typeof addAdditionalFilter === "function" && typeof additionalFilterAmount !== "undefined") {
                                // controllo se sono nella pagina giusta
                                let page = splitted[splitted.length - 3];
                                if (page === location.pathname.split("/").pop().replace(".htm", "")) {
                                    let filterIndex = splitted[splitted.length - 2];
                                    if (parseInt(filterIndex) !== NaN) {
                                        if (additionalFilterAmount < parseInt(filterIndex)) {
                                            if ($("#" + filterId.replace("-" + filterIndex, "")).length > 0) {
                                                // controllo se ho tutti i valori altrimenti non creo
                                                let allFiltersFound = true;
                                                $("#filters-containter").find(".is-required-extra").each(function (index, element) {
                                                    if (allFiltersFound) {
                                                        let tmpFilterId = $(element).attr("id");
                                                        let index = filtersIndex.indexOf(tmpFilterId.replace("filter-", ""));
                                                        if (index >= 0) {
                                                            let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                                                            let keyToFound = key.replace(splitted[splitted.length - 1], "");
                                                            keyToFound = keyToFound.replace("/" + splitted[splitted.length - 4] + "/", "/" + letter + "/");
                                                            keyToFound += tmpFilterId + "-" + filterIndex;
                                                            allFiltersFound = typeof clonedLocalStorage[keyToFound] !== "undefined";
                                                        }
                                                    }
                                                });

                                                if (allFiltersFound) {
                                                    // aggiungo solo se non è già visibile
                                                    addAdditionalFilter();
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    // skippo questo filtro dato che non devo impostarlo
                                    return true;
                                }
                            } else {
                                // skippo questo filtro dato che non posso impostarlo
                                return true;
                            }
                        }

                        if (filterId === "filter-matchday") {
                            if (typeof matchdaySlider !== "undefined") {
                                matchdaySlider.set(filterValue.split(","));
                            }
                        } else if (filterId === "filter-bornyear") {
                            if (typeof bornyearSlider !== "undefined") {
                                bornyearSlider.set(filterValue.split(","));
                            }
                        } else if (notEmpty(filterValue)) {
                            if ($("#" + filterId).length > 0 && !$("#" + filterId).hasClass("skip-initial-load")) {
                                queue.canContinue = false;
                                console.log("Loading", filterId, "with value", filterValue);
                                if (filterValue === "true" && $("#" + filterId).attr("type") === "checkbox") {
                                    $("#" + filterId).attr("checked", "checked");
                                } else {
                                    if (filterValue.includes(",")) {
                                        if ($("#" + filterId).attr("multiple") === "multiple") {
                                            // array
                                            filterValue.split(",").forEach(function (value) {
                                                $("#" + filterId).multiselect('select', value);
                                            });
                                            $("#" + filterId).trigger("change");
                                        } else {
                                            // passo da multi a singolo -> prendo il primo valore
                                            $("#" + filterId).val(filterValue.split(",")[0]).change();
                                        }
                                    } else {
                                        if ($("#" + filterId).attr("multiple") === "multiple") {
                                            $("#" + filterId).multiselect('select', filterValue).change();
                                        } else {
                                            $("#" + filterId).val(filterValue).change();
                                        }
                                    }
                                }

                                if (!$("#" + filterId).attr("onchange") || !$("#" + filterId).attr("onchange").includes("updateFilters")) {
                                    queue.canContinue = true;
                                    if (queue.items.length > 0) {
                                        queue.processQueue();
                                    }
                                }
                            }
                        }
                    }
                }
            });
        });

        queue.enqueue(() => {
            // queue.canContinue = true;
            console.groupEnd();
        });
    }

    queue.enqueue(() => {
        // queue.canContinue = true;
        console.group("%c Checking initial values...", 'color: green; font-size: 1.5em;');
    });

    queue.enqueue(() => {
        // queue.canContinue = true;
        if ($("#filter-competitionid").length > 0 && empty($("#filter-competitionid").val()) && !$("#filter-competitionid").hasClass("skip-initial-load")) {
            queue.canContinue = false;
            console.log("Setting first competition...");
            if ($("#filter-competitionid").attr("multiple") === "multiple") {
                $("#filter-competitionid").multiselect('select', $("#filter-competitionid option:first").val()).change();
            } else {
                $("#filter-competitionid").prop("selectedIndex", 1).change();
            }

            if (!$("#filter-competitionid").attr("onchange").includes("updateFilters")) {
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            }
            // filtersLoaded = true;   // non so se il change sopra triggera la getFilters quindi per sicurezza rilascio
        }
    });

    queue.enqueue(() => {
        // queue.canContinue = true;
        // ora carico il primo team se presente
        if ($("#filter-teamid").length > 0 && empty($("#filter-teamid").val()) && !$("#filter-teamid").hasClass("skip-initial-load")) {
            queue.canContinue = false;
            console.log("Setting first team...");
            if ($("#filter-teamid").attr("multiple") === "multiple") {
                $("#filter-teamid").multiselect('select', $("#filter-teamid option:first").val()).change();
            } else {
                $("#filter-teamid").prop("selectedIndex", 1).change();
            }

            if (!$("#filter-teamid").attr("onchange").includes("updateFilters")) {
                queue.canContinue = true;
                if (queue.items.length > 0) {
                    queue.processQueue();
                }
            }
            // filtersLoaded = true;   // non so se il change sopra triggera la getFilters quindi per sicurezza rilascio
        }
    });

    queue.enqueue(() => {
        // queue.canContinue = true;
        // ora carico l'evento se presente
        if ($("#filter-eventtypeid").length > 0) {
            if (empty(getPageEventType()) && !$("#filter-eventtypeid").hasClass("skip-initial-load")) {
                queue.canContinue = false;

                let loadFirstEvent = true;
                let index = filtersIndex.indexOf("eventtypeid");
                if (index >= 0) {
                    let letter = ((index + 1) + 9).toString(36).toUpperCase(); // lettera dell'alfabeto, usata poi per fare il sort
                    let value = localStorage.getItem("dataanalytics/610/" + letter + "/filter-eventtypeid");
                    if (notEmpty(value)) {
                        console.log("Setting default event(s)...");
                        if (value.includes(",")) {
                            if ($("#filter-eventtypeid").attr("multiple") === "multiple") {
                                // array
                                value.split(",").forEach(function (value) {
                                    $("#filter-eventtypeid").multiselect('select', value);
                                });
                                $("#filter-eventtypeid").trigger("change");
                            } else {
                                // passo da multi a singolo -> prendo il primo valore
                                $("#filter-eventtypeid").val(value.split(",")[0]).change();
                            }
                        } else {
                            if ($("#filter-eventtypeid").attr("multiple") === "multiple") {
                                $("#filter-eventtypeid").multiselect('select', value).change();
                            } else {
                                $("#filter-eventtypeid").val(value).change();
                            }
                        }
                        loadFirstEvent = false;
                    }
                }

                if (loadFirstEvent) {
                    console.log("Setting first event...");
                    if ($("#filter-eventtypeid").attr("multiple") === "multiple") {
                        $("#filter-eventtypeid").multiselect('select', $("#filter-eventtypeid option:first").val()).change();
                    } else {
                        $("#filter-eventtypeid").prop("selectedIndex", 1).change();
                    }

                    if (!$("#filter-eventtypeid").attr("onchange").includes("updateFilters")) {
                        queue.canContinue = true;
                        if (queue.items.length > 0) {
                            queue.processQueue();
                        }
                    }
                }

                if (!$("#filter-eventtypeid").attr("onchange").includes("updateFilters")) {
                    queue.canContinue = true;
                    if (queue.items.length > 0) {
                        queue.processQueue();
                    }
                }
                // filtersLoaded = true;   // non so se il change sopra triggera la getFilters quindi per sicurezza rilascio
            }
        } else {
            if ($("#filter-eventtypeid-x").length > 0) {
                // NON USO IL .change() ALTRIMENTI CARICA 2 VOLTE IL GRAFICO
                queue.enqueue(() => {
                    if (empty($("#filter-eventtypeid-x").val()) && !$("#filter-eventtypeid-x").hasClass("skip-initial-load")) {
                        queue.canContinue = false;
                        console.log("Setting first event(x)...");
                        $("#filter-eventtypeid-x").prop("selectedIndex", 1).change();
                    }

                    if (notEmpty($("#filter-eventtypeid-y").val())) {
                        queue.canContinue = true;
                        if (queue.items.length > 0) {
                            queue.processQueue();
                        }
                    }
                });
                queue.enqueue(() => {
                    if (empty($("#filter-eventtypeid-y").val()) && !$("#filter-eventtypeid-y").hasClass("skip-initial-load")) {
                        queue.canContinue = false;
                        console.log("Setting first event(y)...");
                        $("#filter-eventtypeid-y").prop("selectedIndex", 2).change();
                    }

                    queue.canContinue = true;
                    if (queue.items.length > 0) {
                        queue.processQueue();
                    }
                });
            }
        }
    });

    queue.enqueue(() => {
        // queue.canContinue = true;
        // ora carico il giocatore se presente
        if ($("#filter-playerid").length > 0 && isPlayerPage()) {
            if (empty($("#filter-playerid").val()) && !$("#filter-playerid").hasClass("skip-initial-load")) {
                queue.canContinue = false;
                console.log("Setting first player...");
                if ($("#filter-playerid").attr("multiple") === "multiple") {
                    $("#filter-playerid").multiselect('select', $("#filter-playerid option:first").val()).change();
                } else {
                    $("#filter-playerid").prop("selectedIndex", 1).change();
                }

                if (!$("#filter-playerid").attr("onchange").includes("updateFilters")) {
                    queue.canContinue = true;
                    if (queue.items.length > 0) {
                        queue.processQueue();
                    }
                }
                // filtersLoaded = true;   // non so se il change sopra triggera la getFilters quindi per sicurezza rilascio
            }
        }
    });

    queue.enqueue(() => {
        console.groupEnd();
    });
}

function reloadEventFilterDefaults() {
    let eventTypeIds = $("#filter-eventtypeid").val();
    let advancedTypeIds = $("#filter-advanced-eventtypeid").val();
    let tacticalTypeIds = $("#filter-tactical-eventtypeid").val();

    eventFilter.currentFilter = {};

    if (eventTypeIds && $.isArray(eventTypeIds)) {
        eventTypeIds.forEach(function (typeId) {
            // niente tag
            if (!typeId.includes("-")) {
                let element = {};
                element.typeId = parseInt(typeId);
                element.type = "1";
                let tags = [];
                eventTypeIds.forEach(function (tmpTypeId) {
                    if (tmpTypeId.includes("-") && tmpTypeId.startsWith(typeId)) {
                        tags.push(tmpTypeId);
                    }
                });
                if (tags.length > 0) {
                    element.tagTypes = tags;
                }
                let nextIndex = Object.keys(eventFilter.currentFilter).length + 1;
                if (nextIndex <= eventFilter.maxFilters) {
                    eventFilter.currentFilter[nextIndex] = element;
                }
            }
        });
    }
    if (advancedTypeIds && $.isArray(advancedTypeIds)) {
        advancedTypeIds.forEach(function (typeId) {
            // niente tag
            if (!typeId.includes("-")) {
                let element = {};
                element.typeId = parseInt(typeId);
                element.type = "2";
                let tags = [];
                advancedTypeIds.forEach(function (tmpTypeId) {
                    if (tmpTypeId.includes("-") && tmpTypeId.startsWith(typeId)) {
                        tags.push(tmpTypeId);
                    }
                });
                if (tags.length > 0) {
                    element.tagTypes = tags;
                }
                let nextIndex = Object.keys(eventFilter.currentFilter).length + 1;
                if (nextIndex <= eventFilter.maxFilters) {
                    eventFilter.currentFilter[nextIndex] = element;
                }
            }
        });
    }
    if (tacticalTypeIds && $.isArray(tacticalTypeIds)) {
        tacticalTypeIds.forEach(function (typeId) {
            // niente tag
            if (!typeId.includes("-")) {
                let element = {};
                element.typeId = parseInt(typeId);
                element.type = "3";
                let tags = [];
                tacticalTypeIds.forEach(function (tmpTypeId) {
                    if (tmpTypeId.includes("-") && tmpTypeId.startsWith(typeId)) {
                        tags.push(tmpTypeId);
                    }
                });
                if (tags.length > 0) {
                    element.tagTypes = tags;
                }
                let nextIndex = Object.keys(eventFilter.currentFilter).length + 1;
                if (nextIndex <= eventFilter.maxFilters) {
                    eventFilter.currentFilter[nextIndex] = element;
                }
            }
        });
    }

    updateCurrentFilterCache();
}

function updateEventFilter() {
    // eventi
    if (typeof customUpdateFilters !== "function") {
        eventFilter.events = {};
        eventFilter.advanced = {};
        eventFilter.tactical = {};
        if (!$("#filter-eventtypeid").closest("div.tab-pane").hasClass("disabled")) {
            $("#filter-eventtypeid option").each(function (index, element) {
                let value = $(element).attr("value");
                if (value) {
                    if (value.includes("-")) {
                        // tag
                        let splitted = value.split("-");
                        if (splitted.length === 2) {
                            let currentTags = eventFilter.events[splitted[0]];
                            currentTags.push(splitted[1]);
                        }
                    } else {
                        // evento
                        eventFilter.events[value] = [];
                    }
                }
            });
        }
        if (!$("#filter-advancedeventtypeid").closest("div.tab-pane").hasClass("disabled")) {
            $("#filter-advancedeventtypeid option").each(function (index, element) {
                let value = $(element).attr("value");
                if (value) {
                    if (value.includes("-")) {
                        // tag
                        let splitted = value.split("-");
                        if (splitted.length === 2) {
                            let currentTags = eventFilter.advanced[splitted[0]];
                            currentTags.push(splitted[1]);
                        }
                    } else {
                        // evento
                        eventFilter.advanced[value] = [];
                    }
                }
            });
        }
        if (!$("#filter-tacticaleventtypeid").closest("div.tab-pane").hasClass("disabled")) {
            $("#filter-tacticaleventtypeid option").each(function (index, element) {
                let value = $(element).attr("value");
                if (value) {
                    if (value.includes("-")) {
                        // tag
                        let splitted = value.split("-");
                        if (splitted.length === 2) {
                            let currentTags = eventFilter.tactical[splitted[0]];
                            currentTags.push(splitted[1]);
                        }
                    } else {
                        // evento
                        eventFilter.tactical[value] = [];
                    }
                }
            });
        }
    }

    eventFilter.allEvents = {...eventFilter.events, ...eventFilter.advanced, ...eventFilter.tactical};

    if (typeof eventFilter.currentFilter === "undefined") {
        eventFilter.currentFilter = {};
        let storedName = getEventFilterCacheName();
        if (typeof localStorage[storedName] !== "undefined") {
            eventFilter.currentFilter = JSON.parse(localStorage[storedName]);
        }
    }
    if (typeof eventFilter.scatterplot !== "undefined" && (typeof eventFilter.currentFilterX === "undefined" || typeof eventFilter.currentFilterY === "undefined")) {
        eventFilter.currentFilterX = {};
        let storedName = getEventFilterCacheName() + "-x";
        if (typeof localStorage[storedName] !== "undefined") {
            eventFilter.currentFilterX = JSON.parse(localStorage[storedName]);
        }
        eventFilter.currentFilterY = {};
        storedName = getEventFilterCacheName() + "-y";
        if (typeof localStorage[storedName] !== "undefined") {
            eventFilter.currentFilterY = JSON.parse(localStorage[storedName]);
        }
    }
}

function loadEventFilterModal() {
    if (typeof eventFilter.allEvents !== "undefined") {
        if (d3.select("#pitchFasce").empty()) {
            drawPositionalField("modal-event-position", pitchFasce);
            if (typeof handleResize === "function") {
                handleResize();
            }
        }

        // tolgo tutto quello che c'era
        $("#event-builder-event-list").find("a:not(#base-event-item)").remove();
        $("#event-builder-advanced-list").find("a:not(#base-event-item)").remove();
        $("#event-builder-tactical-list").find("a:not(#base-event-item)").remove();
        $("#modal-event-container-nav").find("a[href='#modal-event-filters-event']").removeClass("disabled");
        $("#modal-event-container-nav").find("a[href='#modal-event-filters-advanced']").removeClass("disabled");
        $("#modal-event-container-nav").find("a[href='#modal-event-filters-tactical']").removeClass("disabled");

        $("#event-builder-tag-list").addClass("d-none");
        $("#empty-event-builder-tag-list").removeClass("d-none");
        $("#modal-event-position-container").addClass("d-none");
        resetPositionalFilter();

        Object.keys(eventFilter.allEvents).forEach(function (typeId) {
            let tags = eventFilter.allEvents[typeId];
            let hasTags = (tags.length > 0), hasPositional = false;

            let listItem = $("#base-event-item").clone();
            listItem.removeClass("d-none");
            listItem.removeAttr("id");
            listItem.attr("typeid", typeId);
            listItem.attr("onclick", "configureEventFilterModal(" + typeId + ");");

            if (typeof eventFilter.events[typeId] !== "undefined") {
                hasPositional = true;
                listItem.find("span.event-text").text($("#filter-eventtypeid option[value='" + typeId + "']").text());
                listItem.attr("type", 1);
            } else if (typeof eventFilter.advanced[typeId] !== "undefined") {
                listItem.find("span.event-text").text($("#filter-advancedeventtypeid option[value='" + typeId + "']").text());
                listItem.attr("type", 2);
            } else if (typeof eventFilter.tactical[typeId] !== "undefined") {
                listItem.find("span.event-text").text($("#filter-tacticaleventtypeid option[value='" + typeId + "']").text());
                listItem.attr("type", 3);
            }
            if (!hasTags) {
                listItem.find("#event-item-tags").remove();
            } else {
                listItem.find("div.icon-container").addClass("has-tags");
            }
            if (!hasPositional) {
                listItem.find("#event-item-positions").remove();
            } else {
                listItem.find("div.icon-container").addClass("has-positions");
            }
            // tolgo gli id altrimenti potrebbe creare problemi
            listItem.find("#event-item-tags").removeAttr("id");
            listItem.find("#event-item-positions").removeAttr("id");
            // controllo se serve tenere il container delle icone
            if (listItem.find("div.icon-container").find("i").length === 0) {
                listItem.find("div.icon-container").remove();
            }

            if (typeof eventFilter.events[typeId] !== "undefined") {
                $("#event-builder-event-list").append(listItem);
            } else if (typeof eventFilter.advanced[typeId] !== "undefined") {
                $("#event-builder-advanced-list").append(listItem);
            } else if (typeof eventFilter.tactical[typeId] !== "undefined") {
                $("#event-builder-tactical-list").append(listItem);
            }
        });

        if ($("#event-builder-event-list").find("a").length === 0) {
            $("#modal-event-container-nav").find("a[href='#modal-event-filters-event']").addClass("disabled");
        } else {
            // sort per nome
            let $eventList = $("#event-builder-event-list");
            let sortedItems = $eventList.children("a").sort(function (a, b) {
                let textA = $(a).find(".event-text").text().trim().toLowerCase();
                let textB = $(b).find(".event-text").text().trim().toLowerCase();
                return textA.localeCompare(textB);
            });
            $eventList.append(sortedItems);
        }
        if ($("#event-builder-advanced-list").find("a").length === 0) {
            $("#modal-event-container-nav").find("a[href='#modal-event-filters-advanced']").addClass("disabled");
        } else {
            // sort per nome
            let $eventList = $("#event-builder-advanced-list");
            let sortedItems = $eventList.children("a").sort(function (a, b) {
                let textA = $(a).find(".event-text").text().trim().toLowerCase();
                let textB = $(b).find(".event-text").text().trim().toLowerCase();
                return textA.localeCompare(textB);
            });
            $eventList.append(sortedItems);
        }
        if ($("#event-builder-tactical-list").find("a").length === 0) {
            $("#modal-event-container-nav").find("a[href='#modal-event-filters-tactical']").addClass("disabled");
        } else {
            // sort per nome
            let $eventList = $("#event-builder-tactical-list");
            let sortedItems = $eventList.children("a").sort(function (a, b) {
                let textA = $(a).find(".event-text").text().trim().toLowerCase();
                let textB = $(b).find(".event-text").text().trim().toLowerCase();
                return textA.localeCompare(textB);
            });
            $eventList.append(sortedItems);
        }

        // click automatico sul primo evento visibile
        if (typeof eventFilter.lastTypeId !== "undefined" && $("#modal-event-tabs").find("div.active.show").find("a.list-group-item-action[typeid='" + eventFilter.lastTypeId + "']").length > 0) {
            $("#modal-event-tabs").find("div.active.show").find("a.list-group-item-action[typeid='" + eventFilter.lastTypeId + "']").click();
        } else {
            $("#modal-event-tabs").find("div.active.show").find("a.list-group-item-action").first().click();
        }

        if (typeof eventFilter.scatterplot === "undefined") {
            $("#modal-current-event").addClass("show").addClass("active");
            $("#modal-current-event-scatterplot").removeClass("show").removeClass("active");
        } else {
            $("#modal-current-event").removeClass("show").removeClass("active");
            $("#modal-current-event-scatterplot").addClass("show").addClass("active");
        }
        loadCurrentEventFilter();
    }
}

function configureEventFilterModal(typeId) {
    if (typeId) {
        resetPositionalFilter();
        $("#modal-event-tabs").find("a.bg-info.bg-opacity-20").each(function (index, element) {
            $(element).removeClass("bg-info");
            $(element).removeClass("bg-opacity-20");
        });
        let element = $("#modal-event-tabs").find("a[typeid='" + typeId + "']");
        $(element).addClass("bg-info");
        $(element).addClass("bg-opacity-20");
        let type = $(element).attr("type");
        if (type) {
            $("#event-builder-tag-list").find("label").remove();

            let hasIcons = $(element).find("div.icon-container").length > 0;
            if (hasIcons) {
                let hasTags = $(element).find("div.icon-container").hasClass("has-tags");
                let hasPositions = $(element).find("div.icon-container").hasClass("has-positions");

                if (hasTags) {
                    if (type === "1") {
                        // tag di evento
                        eventFilter.events[typeId].forEach(function (tagTypeId) {
                            let listItem = $("#base-tag-item").clone();
                            listItem.removeClass("d-none");
                            listItem.removeAttr("id");
                            listItem.find("span.event-text").text($("#filter-eventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                            listItem.find("input[type='checkbox']").attr("typeid", tagTypeId);

                            $("#event-builder-tag-list").append(listItem);
                        });
                    } else if (type === "2") {
                        // tag di metrica avanzata (in teoria mai)
                        eventFilter.advanced[typeId].forEach(function (tagTypeId) {
                            let listItem = $("#base-tag-item").clone();
                            listItem.removeClass("d-none");
                            listItem.removeAttr("id");
                            listItem.find("span.event-text").text($("#filter-advancedeventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                            listItem.find("input[type='checkbox']").attr("typeid", tagTypeId);

                            $("#event-builder-tag-list").append(listItem);
                        });
                    } else if (type === "3") {
                        // tag di metrica tattica
                        eventFilter.tactical[typeId].forEach(function (tagTypeId) {
                            let listItem = $("#base-tag-item").clone();
                            listItem.removeClass("d-none");
                            listItem.removeAttr("id");
                            listItem.find("span.event-text").text($("#filter-tacticaleventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                            listItem.find("input[type='checkbox']").attr("typeid", tagTypeId);

                            $("#event-builder-tag-list").append(listItem);
                        });
                    }

                    // sort per nome
                    let $tagList = $("#event-builder-tag-list");
                    let sortedItems = $tagList.children("label").sort(function (a, b) {
                        let textA = $(a).find(".event-text").text().trim().toLowerCase();
                        let textB = $(b).find(".event-text").text().trim().toLowerCase();
                        return textA.localeCompare(textB);
                    });
                    $tagList.append(sortedItems);

                    $("#event-builder-tag-list").removeClass("d-none");
                    $("#empty-event-builder-tag-list").addClass("d-none");
                } else {
                    $("#event-builder-tag-list").addClass("d-none");
                    $("#empty-event-builder-tag-list").removeClass("d-none");
                }
                if (hasPositions) {
                    $("#modal-event-position-container").removeClass("d-none");
                    $("#empty-modal-event-position").addClass("d-none");
                } else {
                    $("#modal-event-position-container").addClass("d-none");
                    $("#empty-modal-event-position").removeClass("d-none");
                }
            } else {
                $("#event-builder-tag-list").addClass("d-none");
                $("#empty-event-builder-tag-list").removeClass("d-none");
                $("#modal-event-position-container").addClass("d-none");
                $("#empty-modal-event-position").removeClass("d-none");
            }
        }

        eventFilter.currentTypeId = typeId;
        $("#modal-add-parameter-container").removeClass("d-none");
    }
}

function saveCurrentEventFilter() {
    if (typeof eventFilter.currentTypeId !== "undefined") {
        let elementToSave = getCurrentEventFilter();
        if (typeof eventFilter.scatterplot !== "undefined") {
            if (Object.keys(eventFilter.currentFilterX).length === 0) {
                elementToSave = eventFilter.currentFilterX;
            } else {
                elementToSave = eventFilter.currentFilterY;
            }
        }
        let nextIndex = Object.keys(elementToSave).length + 1;
        if (nextIndex <= eventFilter.maxFilters) {
            let tags = [];
            let zoneId = eventFilter.currentZoneId;
            let type = $("#modal-event-container-nav").find("a.nav-link.active").attr("type");
            if (!$("#event-builder-tag-list").hasClass("d-none")) {
                $("#event-builder-tag-list input:checked").each(function (index, element) {
                    tags.push($(element).attr("typeid"));
                });
            }

            elementToSave[nextIndex] = {};
            elementToSave[nextIndex].typeId = eventFilter.currentTypeId;
            elementToSave[nextIndex].type = type;
            if (tags.length > 0) {
                elementToSave[nextIndex].tagTypes = tags;
            }
            if (typeof zoneId !== "undefined" && zoneId !== null) {
                elementToSave[nextIndex].zoneId = zoneId;
            }

            new Noty({
                text: globalMessages.get("event.filter.modal.parameter.added"),
                type: "success",
                layout: "topCenter"
            }).show();
            eventFilter.lastTypeId = eventFilter.currentTypeId;
            delete eventFilter.currentTypeId;
            delete eventFilter.currentZoneId;

            updateCurrentFilterCache();
            loadEventFilterModal();
        } else {
            new Noty({
                text: globalMessages.get("event.filter.max.filters.reached").replace("(X)", "(" + eventFilter.maxFilters + ")"),
                type: "warning",
                layout: "topCenter"
            }).show();
        }
    }
}

function loadCurrentEventFilter() {
    if (typeof eventFilter.scatterplot === "undefined") {
        $("#modal-current-parameters-table tbody tr").remove();

        loadCurrentEventElement(getCurrentEventFilter());
    } else {
        $("#modal-current-parameters-table-scatterplot-x tbody tr").remove();
        $("#modal-current-parameters-table-scatterplot-y tbody tr").remove();

        eventFilter.scatterplotModality = 1;
        loadCurrentEventElement(eventFilter.currentFilterX);
        eventFilter.scatterplotModality = 2;
        loadCurrentEventElement(eventFilter.currentFilterY);
    }
}

function loadCurrentEventElement(elementToLoad) {
    if (typeof elementToLoad !== "undefined" && Object.keys(elementToLoad).length > 0) {
        Object.keys(elementToLoad).forEach(function (index) {
            let element = elementToLoad[index];
            if (element) {
                let typeId = element.typeId;
                let eventName;
                if (element.type === "2") {
                    eventName = $("#filter-advancedeventtypeid option[value='" + typeId + "']").text();
                } else if (element.type === "3") {
                    eventName = $("#filter-tacticaleventtypeid option[value='" + typeId + "']").text();
                } else {
                    eventName = $("#filter-eventtypeid option[value='" + typeId + "']").text();
                }
                let tagTypes = [];
                let zoneId = element.zoneId;
                if (typeof element.tagTypes !== "undefined") {
                    element.tagTypes.forEach(function (tagTypeId) {
                        if (element.type === "2") {
                            tagTypes.push($("#filter-advancedeventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                        } else if (element.type === "3") {
                            tagTypes.push($("#filter-tacticaleventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                        } else {
                            tagTypes.push($("#filter-eventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text());
                        }
                    });
                }

                // eventName = $("#filter-advancedeventtypeid option[value='" + typeId + "-" + tagTypeId + "']").text();

                let tableRow = $("<tr>");
                let firstCol = $("<td>");
                firstCol.addClass("py-1");
                let firstColDiv = $("<div>");
                firstColDiv.addClass("d-flex");
                firstColDiv.addClass("align-items-center");
                let firstColDivContainer = $("<div>");
                firstColDivContainer.append('<div class="text-body">' + eventName + '</div>');
                if (tagTypes.length > 0) {
                    firstColDivContainer.append('<div class="text-muted fs-sm">' + tagTypes.join(", ") + '</div>');
                }
                firstColDiv.append(firstColDivContainer);
                firstCol.append(firstColDiv);

                let secondCol = $("<td>");
                secondCol.addClass("py-1");
                if (typeof zoneId !== "undefined" && zoneId !== null) {
                    let code = getZoneMessageTag(zoneId);
                    if (code) {
                        secondCol.append('<span class="text-muted text-decoration-underline" title="' + globalMessages.get(code) + '">' + globalMessages.get(code + ".abb") + '</span>');
                    }
                }

                let thirdCol = $("<td>");
                thirdCol.addClass("py-1");
                if (typeof eventFilter.scatterplot === "undefined") {
                    thirdCol.append('<i class="ph-x text-danger cursor-pointer" onclick="removeEventFilter(' + index + ');"></i>');
                } else {
                    thirdCol.append('<i class="ph-x text-danger cursor-pointer" onclick="removeEventFilter(' + index + ', ' + eventFilter.scatterplotModality + ');"></i>');
                }

                tableRow.append(firstCol);
                tableRow.append(secondCol);
                tableRow.append(thirdCol);

                if (typeof eventFilter.scatterplot === "undefined") {
                    $("#modal-current-parameters-table tbody").append(tableRow);
                } else {
                    if (eventFilter.scatterplotModality === 1) {
                        $("#modal-current-parameters-table-scatterplot-x tbody").append(tableRow);
                    } else {
                        $("#modal-current-parameters-table-scatterplot-y tbody").append(tableRow);
                    }
                }
            }
        });
    }
}

function removeEventFilter(index, scatterplotModality) {
    let element = getCurrentEventFilter();
    if (typeof scatterplotModality !== "undefined") {
        if (scatterplotModality === 1) {
            element = eventFilter.currentFilterX;
        } else {
            element = eventFilter.currentFilterY;
        }
    }

    delete element[index];
    adjustCurrentFilterIndexes();
    updateCurrentFilterCache();
    loadCurrentEventFilter();
}

function adjustCurrentFilterIndexes() {
    let element = getCurrentEventFilter();
    if (typeof element !== "undefined") {
        let adjustedFilter = {};
        let index = 1;
        Object.keys(element).forEach(function (tmpIndex) {
            adjustedFilter[index] = element[tmpIndex];
            index++;
        });

        eventFilter[getCurrentEventFilterName()] = adjustedFilter;
    }
}

function getCurrentFilterParameter(elementToLoad) {
    let params = [];
    if (typeof elementToLoad === "undefined") {
        elementToLoad = eventFilter.currentFilter;
    }
    if (typeof elementToLoad !== "undefined") {
        let amount = 1;
        Object.keys(elementToLoad).forEach(function (index) {
            if (amount <= eventFilter.maxFilters) {
                let element = elementToLoad[index];
                let param = element.typeId;
                if (typeof element.tagTypes !== "undefined") {
                    param += "$" + element.tagTypes.join("-");
                }
                if (typeof element.zoneId !== "undefined") {
                    param += "@" + element.zoneId;
                }
                params.push(param);
            }
            amount++;
        });
    }

    return params.join("|");
}

function updateCurrentFilterCache() {
    // aggiorno cache
    let element = getCurrentEventFilter();
    let storedName = getEventFilterCacheName();
    if (typeof eventFilter.scatterplotModality !== "undefined") {
        if (eventFilter.scatterplotModality === 1) {
            storedName += "-x";
            localStorage[storedName] = JSON.stringify(element);

            eventFilter.scatterplotModality = 2;
            element = getCurrentEventFilter();
            storedName = getEventFilterCacheName() + "-y";
            localStorage[storedName] = JSON.stringify(element);
            eventFilter.scatterplotModality = 1;
        } else if (eventFilter.scatterplotModality === 2) {
            storedName += "-y";
            localStorage[storedName] = JSON.stringify(element);

            eventFilter.scatterplotModality = 1;
            element = getCurrentEventFilter();
            storedName = getEventFilterCacheName() + "-x";
            localStorage[storedName] = JSON.stringify(element);
            eventFilter.scatterplotModality = 2;
        }
    } else {
        localStorage[storedName] = JSON.stringify(element);
    }
}

function getCurrentEventFilter() {
    if (typeof eventFilter.scatterplotModality !== "undefined") {
        if (eventFilter.scatterplotModality === 1) {
            // Asse X
            if (typeof eventFilter.currentFilterX !== "undefined") {
                return eventFilter.currentFilterX;
            }
        } else if (eventFilter.scatterplotModality === 2) {
            // Asse Y
            if (typeof eventFilter.currentFilterY !== "undefined") {
                return eventFilter.currentFilterY;
            }
        }
    }

    return eventFilter.currentFilter;
}

function getCurrentEventFilterName() {
    if (typeof eventFilter.scatterplotModality !== "undefined") {
        if (eventFilter.scatterplotModality === 1) {
            // Asse X
            if (typeof eventFilter.currentFilterX !== "undefined") {
                return "currentFilterX";
            }
        } else if (eventFilter.scatterplotModality === 2) {
            // Asse Y
            if (typeof eventFilter.currentFilterY !== "undefined") {
                return "currentFilterY";
            }
        }
    }

    return "currentFilter";
}

function checkParametersAmount(amount) {
    let element = getCurrentEventFilter();
    if (amount < Object.keys(element).length) {
        new Noty({
            text: globalMessages.get("messages.warning.missed.parameters"),
            type: "warning",
            layout: "topCenter"
        }).show();
    }
}

function getEventFilterCacheName() {
    let pageName = getPageName().replaceAll("-", "/");
    let prefix = "dataanalytics/610";
    let filterName = "event-filter";

    return prefix + "/" + pageName + "/" + filterName;
}

function getPageEventPrefix() {
    if ($("#event-filters-advanced").hasClass("show") && $("#event-filters-advanced").hasClass("active")) {
        return "advanced";
    } else if ($("#event-filters-tactical").hasClass("show") && $("#event-filters-tactical").hasClass("active")) {
        return "tactical";
    }
    return "";
}

function getPageEventType() {
    if ($("#event-filters-container").hasClass("multiple-events")) {
        // devo sommare i vari select
        let eventTypes = [];
        eventTypes = eventTypes.concat($("#filter-eventtypeid").val() || []);
        eventTypes = eventTypes.concat($("#filter-advancedeventtypeid").val() || []);
        eventTypes = eventTypes.concat($("#filter-tacticaleventtypeid").val() || []);
        return eventTypes;
    } else {
        // prendo quello visibile
        return $("#event-filters-container").find("div.tab-pane.fade.show.active").find(".eventtypeid").first().val();
    }
}

function getPageTagType() {
    if ($("#event-filters-container").hasClass("multiple-events")) {
        // devo sommare i vari select
        let eventTypes = [];
        eventTypes.concat($("#filter-tagtypeid").val());
        eventTypes.concat($("#filter-tacticaltagtypeid").val());
    } else {
        // prendo quello visibile
        return $("#event-filters-container").find("div.tab-pane.fade.show.active").find(".tagtypeid").first().val();
    }
}

function getPageExtraEventAmount() {
    let prefix = getPageEventPrefix();
    prefix = prefix || "event";

    return $("#event-filters-" + prefix).find("select.eventtypeid.additional-filter").length;
}

//function waitForFilters() {
//    return new Promise((resolve) => {
//        const interval = setInterval(() => {
//            if (filtersLoaded) {
//                clearInterval(interval);
//                resolve(); // Resolve the promise when the variable is true
//            }
//        }, 10); // Check every 250 milliseconds
//    });
//}

/*
 * EXTRA FUNCTIONS
 */
function getFiltersText() {
    var filters = [];

    $("#filters-containter").find("label").each(function (index, element) {
        let filterName = $(element).text();
        let selectElements = $(element).parent().find("select");
        if (selectElements && selectElements.length === 1) {
            let selectElement = selectElements[0];
            let selectValue = $(selectElement).val();
            if (selectValue) {
                let selectValueText = $(selectElement).find("option[value='" + selectValue + "']").text();
                if (selectValueText) {
                    selectValueText = selectValueText.replace("\n", "");
                    filters.push(filterName + " " + selectValueText);
                }
            }
        }
    });
}

function getPageFilters() {
    let prefix = "dataanalytics/610";
    let validFilters = [];
    let pageName = getPageName().replaceAll("-", "/");
    for (var i = 0; i < localStorage.length; i++) {
        let key = localStorage.key(i);
        if (key.startsWith(prefix) && (key.includes("/filter-") || key.includes("/event-filter"))) {
            let slashAmount = key.split("/").length;
            if (slashAmount === 4 || key.includes("/" + pageName + "/")) {
                validFilters.push(key + "||" + localStorage.getItem(key));
            }
        }
    }

    return validFilters;
}

function getPageName() {
    let name = isPlayerPage() ? 'player-' : 'team-';
    name += document.location.pathname.match(/[^\/]+$/)[0].replace(".htm", "");
    return name;
}

function isPlayerPage() {
    return window.location.pathname.includes("/player/");
}

function getZoneMessageTag(zoneId) {
    let code = "";
    if (zoneId) {
        if (zoneId === 1) {
            code = "positional.defensive.half";
        } else if (zoneId === 2) {
            code = "positional.offensive.half";
        } else if (zoneId === 3) {
            code = "positional.zone.one";
        } else if (zoneId === 4) {
            code = "positional.zone.two";
        } else if (zoneId === 5) {
            code = "positional.zone.three";
        } else if (zoneId === 6) {
            code = "positional.channel.one";
        } else if (zoneId === 7) {
            code = "positional.channel.two";
        } else if (zoneId === 8) {
            code = "positional.channel.three";
        } else if (zoneId === 9) {
            code = "positional.channel.four";
        } else if (zoneId === 10) {
            code = "positional.channel.five";
        } else if (zoneId === 11) {
            code = "positional.zone.one.channel.one";
        } else if (zoneId === 12) {
            code = "positional.zone.two.channel.one";
        } else if (zoneId === 13) {
            code = "positional.zone.three.channel.one";
        } else if (zoneId === 14) {
            code = "positional.zone.one.channel.two";
        } else if (zoneId === 15) {
            code = "positional.zone.two.channel.two";
        } else if (zoneId === 16) {
            code = "positional.zone.three.channel.two";
        } else if (zoneId === 17) {
            code = "positional.zone.one.channel.three";
        } else if (zoneId === 18) {
            code = "positional.zone.two.channel.three";
        } else if (zoneId === 19) {
            code = "positional.zone.three.channel.three";
        } else if (zoneId === 20) {
            code = "positional.zone.one.channel.four";
        } else if (zoneId === 21) {
            code = "positional.zone.two.channel.four";
        } else if (zoneId === 22) {
            code = "positional.zone.three.channel.four";
        } else if (zoneId === 23) {
            code = "positional.zone.one.channel.five";
        } else if (zoneId === 24) {
            code = "positional.zone.two.channel.five";
        } else if (zoneId === 25) {
            code = "positional.zone.three.channel.five";
        } else if (zoneId === 26) {
            code = "positional.penalty.area";
        } else if (zoneId === 27) {
            code = "positional.small.area";
        } else if (zoneId === 28) {
            code = "positional.opponent.penalty.area";
        } else if (zoneId === 29) {
            code = "positional.opponent.small.area";
        }
    }

    return code;
}