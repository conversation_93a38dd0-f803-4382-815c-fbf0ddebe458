{"version": 3, "file": "venn.js", "mappings": "0NAQO,MAAMA,UAAyBC,EAAA,EAC3B,iBAAAC,GACTC,MAAMD,oBACN,MAAME,EAAIC,KAAKC,KAAKC,KAAKF,MAEzBD,EAAE,QAAQI,OAAO,CAChBC,gBAAiB,aACjBC,gBAAiB,UACjBC,OAAQC,EAAA,EAASC,IAAIR,KAAKS,MAAO,CAAC,GAClCC,MAAO,KACPC,OAAQ,OAITZ,EAAE,QAAS,CAAC,SAASI,OAAO,CAC3BS,KAAM,aACNC,cAAc,EACdC,QAAS,KACTC,QAAS,MAGX,E,6FC3BM,SAASC,EAAMC,GAA2B,IAAtB,IAAIlB,EAAI,IAAImB,MAAMD,GAAaE,EAAI,EAAGA,EAAIF,IAAKE,EAAKpB,EAAEoB,GAAK,EAAK,OAAOpB,CAAG,CAC9F,SAASqB,EAAOH,EAAEI,GAAK,OAAOL,EAAMC,GAAGK,KAAI,WAAa,OAAON,EAAMK,EAAI,GAAI,CAE7E,SAASE,EAAIC,EAAGC,GAEnB,IADA,IAAIC,EAAM,EACDP,EAAI,EAAGA,EAAIK,EAAEG,SAAUR,EAC5BO,GAAOF,EAAEL,GAAKM,EAAEN,GAEpB,OAAOO,CACX,CAEO,SAASE,EAAMJ,GAClB,OAAOK,KAAKC,KAAKP,EAAIC,EAAGA,GAC5B,CAEO,SAASO,EAAML,EAAKM,EAAOC,GAC9B,IAAK,IAAId,EAAI,EAAGA,EAAIa,EAAML,SAAUR,EAChCO,EAAIP,GAAKa,EAAMb,GAAKc,CAE5B,CAEO,SAASC,EAAYR,EAAKS,EAAIC,EAAIC,EAAIC,GACzC,IAAK,IAAIC,EAAI,EAAGA,EAAIb,EAAIC,SAAUY,EAC9Bb,EAAIa,GAAKJ,EAAKC,EAAGG,GAAKF,EAAKC,EAAGC,EAEtC,CCxBO,SAASC,EAAWC,EAAGC,EAAIC,GAG9B,IASIC,EATAC,GAFJF,EAAaA,GAAc,CAAC,GAEGE,eAA6B,IAAZH,EAAGf,OAC/CmB,EAAeH,EAAWG,cAAgB,KAC1CC,EAAYJ,EAAWI,WAAa,KACpCC,EAAgBL,EAAWK,eAAiB,KAC5CC,EAAeN,EAAWK,eAAiB,KAC3CE,OAA0BC,IAAnBR,EAAWO,IAAqBP,EAAWO,IAAM,EACxDE,OAA0BD,IAAnBR,EAAWS,IAAqBT,EAAWS,IAAM,EACxDC,OAA0BF,IAAnBR,EAAWU,IAAqBV,EAAWU,KAAO,GACzDC,OAA8BH,IAArBR,EAAWW,MAAuBX,EAAWW,MAAQ,GAI9DC,EAAIb,EAAGf,OACP6B,EAAU,IAAItC,MAAMqC,EAAI,GAC5BC,EAAQ,GAAKd,EACbc,EAAQ,GAAGC,GAAKhB,EAAEC,GAClBc,EAAQ,GAAGE,GAAK,EAChB,IAAK,IAAIvC,EAAI,EAAGA,EAAIoC,IAAKpC,EAAG,CACxB,IAAIwC,EAAQjB,EAAGkB,QACfD,EAAMxC,GAAKwC,EAAMxC,GAAKwC,EAAMxC,GAAK2B,EAAeC,EAChDS,EAAQrC,EAAE,GAAKwC,EACfH,EAAQrC,EAAE,GAAGsC,GAAKhB,EAAEkB,GACpBH,EAAQrC,EAAE,GAAGuC,GAAKvC,EAAE,CACxB,CAEA,SAAS0C,EAAc7B,GACnB,IAAK,IAAIb,EAAI,EAAGA,EAAIa,EAAML,OAAQR,IAC9BqC,EAAQD,GAAGpC,GAAKa,EAAMb,GAE1BqC,EAAQD,GAAGE,GAAKzB,EAAMyB,EAC1B,CASA,IAPA,IAAIK,EAAY,SAAStC,EAAGC,GAAK,OAAOD,EAAEiC,GAAKhC,EAAEgC,EAAI,EAEjDM,EAAWrB,EAAGkB,QACdI,EAAYtB,EAAGkB,QACfK,EAAavB,EAAGkB,QAChBM,EAAWxB,EAAGkB,QAETO,EAAY,EAAGA,EAAYtB,IAAiBsB,EAAW,CAG5D,GAFAX,EAAQY,KAAKN,GAETnB,EAAW0B,QAAS,CAGpB,IAAIC,EAAgBd,EAAQlC,KAAI,SAAUL,GACtC,IAAIsD,EAAQtD,EAAE2C,QAGd,OAFAW,EAAMd,GAAKxC,EAAEwC,GACbc,EAAMb,GAAKzC,EAAEyC,GACNa,CACX,IACAD,EAAcF,MAAK,SAAS5C,EAAEC,GAAK,OAAOD,EAAEkC,GAAKjC,EAAEiC,EAAI,IAEvDf,EAAW0B,QAAQG,KAAK,CAACvD,EAAGuC,EAAQ,GAAGI,QACdH,GAAID,EAAQ,GAAGC,GACfD,QAASc,GACtC,CAGA,IADA1B,EAAU,EACLzB,EAAI,EAAGA,EAAIoC,IAAKpC,EACjByB,EAAUf,KAAK4C,IAAI7B,EAASf,KAAK6C,IAAIlB,EAAQ,GAAGrC,GAAKqC,EAAQ,GAAGrC,KAGpE,GAAKU,KAAK6C,IAAIlB,EAAQ,GAAGC,GAAKD,EAAQD,GAAGE,IAAMT,GAC1CJ,EAAUK,EACX,MAIJ,IAAK9B,EAAI,EAAGA,EAAIoC,IAAKpC,EAAG,CACpB4C,EAAS5C,GAAK,EACd,IAAK,IAAIoB,EAAI,EAAGA,EAAIgB,IAAKhB,EACrBwB,EAAS5C,IAAMqC,EAAQjB,GAAGpB,GAE9B4C,EAAS5C,IAAMoC,CACnB,CAIA,IAAIoB,EAAQnB,EAAQD,GAKpB,GAJArB,EAAY8B,EAAW,EAAEd,EAAKa,GAAWb,EAAKyB,GAC9CX,EAAUP,GAAKhB,EAAEuB,GAGbA,EAAUP,GAAKD,EAAQ,GAAGC,GAC1BvB,EAAYgC,EAAU,EAAEd,EAAKW,GAAWX,EAAKuB,GAC7CT,EAAST,GAAKhB,EAAEyB,GACZA,EAAST,GAAKO,EAAUP,GACxBI,EAAcK,GAEdL,EAAcG,QAMjB,GAAIA,EAAUP,IAAMD,EAAQD,EAAE,GAAGE,GAAI,CACtC,IAAImB,GAAe,EAsBnB,GApBIZ,EAAUP,GAAKkB,EAAMlB,IAErBvB,EAAY+B,EAAY,EAAEZ,EAAKU,GAAWV,EAAKsB,GAC/CV,EAAWR,GAAKhB,EAAEwB,GACdA,EAAWR,GAAKkB,EAAMlB,GACtBI,EAAcI,GAEdW,GAAe,IAInB1C,EAAY+B,EAAY,EAAEZ,EAAMH,EAAKa,EAAUV,EAAIH,EAAKyB,GACxDV,EAAWR,GAAKhB,EAAEwB,GACdA,EAAWR,GAAKO,EAAUP,GAC1BI,EAAcI,GAEdW,GAAe,GAInBA,EAAc,CAEd,GAAItB,GAAS,EAAG,MAGhB,IAAKnC,EAAI,EAAGA,EAAIqC,EAAQ7B,SAAUR,EAC9Be,EAAYsB,EAAQrC,GAAI,EAAImC,EAAOE,EAAQ,GAAIF,EAAOE,EAAQrC,IAC9DqC,EAAQrC,GAAGsC,GAAKhB,EAAEe,EAAQrC,GAElC,CACJ,MACI0C,EAAcG,EAEtB,CAGA,OADAR,EAAQY,KAAKN,GACN,CAACL,GAAKD,EAAQ,GAAGC,GAChBxC,EAAIuC,EAAQ,GACxB,CCrIO,SAASqB,EAAgBpC,EAAGqC,EAAIC,EAASC,EAAMxD,EAAGyD,EAAIC,GACzD,IAAIC,EAAOJ,EAAQtB,GAAI2B,EAAY7D,EAAIwD,EAAQM,QAASP,GACpDQ,EAAMH,EAAMI,EAAUJ,EACtBK,EAAWJ,EACXK,EAAK,EAMT,SAASC,EAAKC,EAAMC,EAAQC,GACxB,IAAK,IAAI1B,EAAY,EAAGA,EAAY,KAAMA,EAMtC,GALA3C,GAAKmE,EAAOC,GAAQ,EACpB1D,EAAY8C,EAAK/D,EAAG,EAAK8D,EAAQ9D,EAAGO,EAAGsD,GACvCQ,EAAMN,EAAKvB,GAAKhB,EAAEuC,EAAK/D,EAAG+D,EAAKK,SAC/BG,EAAWjE,EAAIyD,EAAKK,QAASP,GAExBQ,EAAOH,EAAOF,EAAKzD,EAAI4D,GACvBE,GAAOO,EACRD,EAASpE,MAEL,CACJ,GAAIK,KAAK6C,IAAIc,KAAcN,EAAKE,EAC5B,OAAO5D,EAGPgE,GAAYI,EAASD,IAAQ,IAC7BC,EAASD,GAGbA,EAAOnE,EACPqE,EAASP,CACb,CAGJ,OAAO,CACX,CA9BA9D,EAAIA,GAAK,EACTyD,EAAKA,GAAM,KACXC,EAAKA,GAAM,GA8BX,IAAK,IAAIf,EAAY,EAAGA,EAAY,KAAMA,EAAW,CAIjD,GAHAjC,EAAY8C,EAAK/D,EAAG,EAAK8D,EAAQ9D,EAAGO,EAAGsD,GACvCQ,EAAMN,EAAKvB,GAAKhB,EAAEuC,EAAK/D,EAAG+D,EAAKK,SAC/BG,EAAWjE,EAAIyD,EAAKK,QAASP,GACxBQ,EAAOH,EAAOF,EAAKzD,EAAI4D,GACvBjB,GAAcmB,GAAOC,EACtB,OAAOG,EAAKD,EAAIjE,EAAG+D,GAGvB,GAAI1D,KAAK6C,IAAIc,KAAcN,EAAKE,EAC5B,OAAO5D,EAGX,GAAIgE,GAAY,EACZ,OAAOE,EAAKlE,EAAGiE,EAAIH,GAGvBC,EAAUD,EACVG,EAAKjE,EACLA,GAAK,CACT,CAEA,OAAOA,CACX,CCpEO,SAASsE,EAAkBrD,EAAGsD,EAASC,GAG1C,IAGIlB,EAAImB,EAEJpD,EALAkC,EAAU,CAAC9D,EAAG8E,EAAQnC,QAASH,GAAI,EAAG4B,QAASU,EAAQnC,SACvDoB,EAAO,CAAC/D,EAAG8E,EAAQnC,QAASH,GAAI,EAAG4B,QAASU,EAAQnC,SACpDsC,EAAKH,EAAQnC,QAEbpC,EAAI,EAIRqB,GADAmD,EAASA,GAAU,CAAC,GACGnD,eAAkC,GAAjBkD,EAAQpE,OAEhDoD,EAAQtB,GAAKhB,EAAEsC,EAAQ9D,EAAG8D,EAAQM,SAElCtD,EADA+C,EAAKC,EAAQM,QAAQzB,QACXmB,EAAQM,SAAS,GAE3B,IAAK,IAAIlE,EAAI,EAAGA,EAAI0B,IAAiB1B,EAAG,CAWpC,GAVAK,EAAIqD,EAAgBpC,EAAGqC,EAAIC,EAASC,EAAMxD,GAGtCwE,EAAO3B,SACP2B,EAAO3B,QAAQG,KAAK,CAACvD,EAAG8D,EAAQ9D,EAAE2C,QACbH,GAAIsB,EAAQtB,GACZ4B,QAASN,EAAQM,QAAQzB,QACzBuC,MAAO3E,IAG3BA,EAKE,CAEHU,EAAYgE,EAAI,EAAGlB,EAAKK,SAAU,EAAGN,EAAQM,SAE7C,IAAIe,EAAU7E,EAAIwD,EAAQM,QAASN,EAAQM,SAG3CnD,EAAY4C,EAFCjD,KAAK4C,IAAI,EAAGlD,EAAI2E,EAAIlB,EAAKK,SAAWe,GAEzBtB,GAAK,EAAGE,EAAKK,SAErCY,EAAOlB,EACPA,EAAUC,EACVA,EAAOiB,CACX,MAdIlE,EAAM+C,EAAIC,EAAQM,SAAU,GAgBhC,GAAIzD,EAAMmD,EAAQM,UAAY,KAC1B,KAER,CASA,OAPIW,EAAO3B,SACP2B,EAAO3B,QAAQG,KAAK,CAACvD,EAAG8D,EAAQ9D,EAAE2C,QACbH,GAAIsB,EAAQtB,GACZ4B,QAASN,EAAQM,QAAQzB,QACzBuC,MAAO3E,IAGzBuD,CACX,CC/DA,IAAIsB,EAAQ,MAIL,SAASC,EAAiBC,EAASC,GAEtC,IAO6CrF,EAPzCsF,EAsIR,SAA+BF,GAE3B,IADA,IAAI7E,EAAM,GACDP,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAClC,IAAK,IAAIoB,EAAIpB,EAAI,EAAGoB,EAAIgE,EAAQ5E,SAAUY,EAGtC,IAFA,IAAImE,EAAYC,EAAyBJ,EAAQpF,GACHoF,EAAQhE,IAC7CqE,EAAI,EAAGA,EAAIF,EAAU/E,SAAUiF,EAAG,CACvC,IAAIC,EAAIH,EAAUE,GAClBC,EAAEC,YAAc,CAAC3F,EAAEoB,GACnBb,EAAI8C,KAAKqC,EACb,CAGR,OAAOnF,CACX,CApJ6BqF,CAAsBR,GAG3CS,EAAcP,EAAmBQ,QAAO,SAAUJ,GAClD,OAwHD,SAA4BlD,EAAO4C,GACtC,IAAK,IAAIpF,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAClC,GAAI+F,EAASvD,EAAO4C,EAAQpF,IAAMoF,EAAQpF,GAAGgG,OAASd,EAClD,OAAO,EAGf,OAAO,CACX,CA/Hee,CAAmBP,EAAGN,EACjC,IAEIc,EAAU,EAAGC,EAAc,EAAGC,EAAO,GAIzC,GAAIP,EAAYrF,OAAS,EAAG,CAGxB,IAAI6F,EAASC,EAAUT,GACvB,IAAK7F,EAAI,EAAGA,EAAI6F,EAAYrF,SAAUR,EAAI,CACtC,IAAI0F,EAAIG,EAAY7F,GACpB0F,EAAEa,MAAQ7F,KAAK8F,MAAMd,EAAE5F,EAAIuG,EAAOvG,EAAG4F,EAAExF,EAAImG,EAAOnG,EACtD,CACA2F,EAAY5C,MAAK,SAAS5C,EAAEC,GAAK,OAAOA,EAAEiG,MAAQlG,EAAEkG,KAAM,IAI1D,IAAIE,EAAKZ,EAAYA,EAAYrF,OAAS,GAC1C,IAAKR,EAAI,EAAGA,EAAI6F,EAAYrF,SAAUR,EAAG,CACrC,IAAI0G,EAAKb,EAAY7F,GAGrBmG,IAAgBM,EAAG3G,EAAI4G,EAAG5G,IAAM4G,EAAGxG,EAAIuG,EAAGvG,GAO1C,IAJA,IAAIyG,EAAW,CAAC7G,GAAK4G,EAAG5G,EAAI2G,EAAG3G,GAAK,EACpBI,GAAKwG,EAAGxG,EAAIuG,EAAGvG,GAAK,GAChC0G,EAAM,KAEDxF,EAAI,EAAGA,EAAIsF,EAAGf,YAAYnF,SAAUY,EACzC,GAAIqF,EAAGd,YAAYkB,QAAQH,EAAGf,YAAYvE,KAAO,EAAG,CAGhD,IAAI0F,EAAS1B,EAAQsB,EAAGf,YAAYvE,IAChC2F,EAAKrG,KAAK8F,MAAME,EAAG5G,EAAIgH,EAAOhH,EAAG4G,EAAGxG,EAAI4G,EAAO5G,GAC/C8G,EAAKtG,KAAK8F,MAAMC,EAAG3G,EAAIgH,EAAOhH,EAAG2G,EAAGvG,EAAI4G,EAAO5G,GAE/C+G,EAAaD,EAAKD,EAClBE,EAAY,IACZA,GAAa,EAAEvG,KAAKwG,IAKxB,IAAI7G,EAAI2G,EAAKC,EAAU,EACnB1H,EAAQwG,EAASY,EAAU,CACvB7G,EAAIgH,EAAOhH,EAAIgH,EAAOd,OAAStF,KAAKyG,IAAI9G,GACxCH,EAAI4G,EAAO5G,EAAI4G,EAAOd,OAAStF,KAAK0G,IAAI/G,KAK5Cd,EAAwB,EAAhBuH,EAAOd,SACfzG,EAAwB,EAAhBuH,EAAOd,SAIN,OAARY,GAAkBA,EAAIrH,MAAQA,KAC/BqH,EAAM,CAAEE,OAASA,EACTvH,MAAQA,EACRmH,GAAKA,EACLD,GAAKA,GAErB,CAGQ,OAARG,IACAR,EAAK/C,KAAKuD,GACVV,GAAWmB,EAAWT,EAAIE,OAAOd,OAAQY,EAAIrH,OAC7CkH,EAAKC,EAEb,CACJ,KAAO,CAGH,IAAIY,EAAWlC,EAAQ,GACvB,IAAKpF,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAC1BoF,EAAQpF,GAAGgG,OAASsB,EAAStB,SAC7BsB,EAAWlC,EAAQpF,IAM3B,IAAIuH,GAAW,EACf,IAAKvH,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAC9B,GAAI+F,EAASX,EAAQpF,GAAIsH,GAAY5G,KAAK6C,IAAI+D,EAAStB,OAASZ,EAAQpF,GAAGgG,QAAS,CAChFuB,GAAW,EACX,KACJ,CAGAA,EACArB,EAAUC,EAAc,GAGxBD,EAAUoB,EAAStB,OAASsB,EAAStB,OAAStF,KAAKwG,GACnDd,EAAK/C,KAAK,CAACyD,OAASQ,EACTZ,GAAI,CAAE5G,EAAGwH,EAASxH,EAAUI,EAAIoH,EAASpH,EAAIoH,EAAStB,QACtDS,GAAI,CAAE3G,EAAGwH,EAASxH,EAAIoF,EAAOhF,EAAIoH,EAASpH,EAAIoH,EAAStB,QACvDzG,MAA0B,EAAlB+H,EAAStB,SAEpC,CAYA,OAVAG,GAAe,EACXd,IACAA,EAAMmC,KAAOtB,EAAUC,EACvBd,EAAMa,QAAUA,EAChBb,EAAMc,YAAcA,EACpBd,EAAMe,KAAOA,EACbf,EAAMQ,YAAcA,EACpBR,EAAMC,mBAAqBA,GAGxBY,EAAUC,CACrB,CA8BO,SAASkB,EAAWzI,EAAGW,GAC1B,OAAOX,EAAIA,EAAI8B,KAAK+G,KAAK,EAAIlI,EAAMX,IAAMA,EAAIW,GAASmB,KAAKC,KAAKpB,GAAS,EAAIX,EAAIW,GACrF,CAGO,SAASwG,EAASW,EAAID,GACzB,OAAO/F,KAAKC,MAAM+F,EAAG5G,EAAI2G,EAAG3G,IAAM4G,EAAG5G,EAAI2G,EAAG3G,IAC1B4G,EAAGxG,EAAIuG,EAAGvG,IAAMwG,EAAGxG,EAAIuG,EAAGvG,GAChD,CAMO,SAASwH,EAAcC,EAAIC,EAAIC,GAElC,GAAIA,GAAKF,EAAKC,EACV,OAAO,EAIX,GAAIC,GAAKnH,KAAK6C,IAAIoE,EAAKC,GACnB,OAAOlH,KAAKwG,GAAKxG,KAAKoH,IAAIH,EAAIC,GAAMlH,KAAKoH,IAAIH,EAAIC,GAGrD,IACI1G,EAAK0G,GAAMC,EAAIA,EAAIF,EAAKA,EAAKC,EAAKA,IAAO,EAAIC,GACjD,OAAOR,EAAWM,EAFTA,GAAME,EAAIA,EAAID,EAAKA,EAAKD,EAAKA,IAAO,EAAIE,IAErBR,EAAWO,EAAI1G,EAC/C,CAMO,SAASsE,EAAyBkB,EAAID,GACzC,IAAIoB,EAAI9B,EAASW,EAAID,GACjBkB,EAAKjB,EAAGV,OACR4B,EAAKnB,EAAGT,OAGZ,GAAK6B,GAAMF,EAAKC,GAASC,GAAKnH,KAAK6C,IAAIoE,EAAKC,GACxC,MAAO,GAGX,IAAIvH,GAAKsH,EAAKA,EAAKC,EAAKA,EAAKC,EAAIA,IAAM,EAAIA,GACvCE,EAAIrH,KAAKC,KAAKgH,EAAKA,EAAKtH,EAAIA,GAC5BkB,EAAKmF,EAAG5G,EAAIO,GAAKoG,EAAG3G,EAAI4G,EAAG5G,GAAK+H,EAChCG,EAAKtB,EAAGxG,EAAIG,GAAKoG,EAAGvG,EAAIwG,EAAGxG,GAAK2H,EAChCI,IAAOxB,EAAGvG,EAAIwG,EAAGxG,IAAM6H,EAAIF,GAC3BK,IAAOzB,EAAG3G,EAAI4G,EAAG5G,IAAMiI,EAAIF,GAE/B,MAAO,CAAC,CAAC/H,EAAGyB,EAAK0G,EAAI/H,EAAI8H,EAAKE,GACtB,CAACpI,EAAGyB,EAAK0G,EAAI/H,EAAI8H,EAAKE,GAClC,CAGO,SAAS5B,EAAU6B,GAEtB,IADA,IAAI9B,EAAS,CAACvG,EAAG,EAAGI,EAAG,GACdF,EAAG,EAAGA,EAAImI,EAAO3H,SAAUR,EAChCqG,EAAOvG,GAAKqI,EAAOnI,GAAGF,EACtBuG,EAAOnG,GAAKiI,EAAOnI,GAAGE,EAI1B,OAFAmG,EAAOvG,GAAKqI,EAAO3H,OACnB6F,EAAOnG,GAAKiI,EAAO3H,OACZ6F,CACX,CCnKA,IAAI,EAAQ,MAIL,SAAS+B,EAA0BT,EAAIC,EAAIS,GAE9C,OAAI3H,KAAKoH,IAAIH,EAAIC,GAAMlH,KAAKoH,IAAIH,EAAGC,GAAMlH,KAAKwG,IAAMmB,EAAU,EACnD3H,KAAK6C,IAAIoE,EAAKC,GChEtB,SAAgBtG,EAAGjB,EAAGC,EAAGkB,GAE5B,IAAIE,GADJF,EAAaA,GAAc,CAAC,GACGE,eAAiB,IAC5C4G,EAAY9G,EAAW8G,WAAa,MACpCC,EAAKjH,EAAEjB,GACPmI,EAAKlH,EAAEhB,GACPmI,EAAQnI,EAAID,EAEhB,GAAIkI,EAAKC,EAAK,EACV,KAAM,iDAGV,GAAW,IAAPD,EAAU,OAAOlI,EACrB,GAAW,IAAPmI,EAAU,OAAOlI,EAErB,IAAK,IAAIN,EAAI,EAAGA,EAAI0B,IAAiB1B,EAAG,CAEpC,IAAI0I,EAAMrI,GADVoI,GAAS,GAELE,EAAOrH,EAAEoH,GAMb,GAJIC,EAAOJ,GAAM,IACblI,EAAIqI,GAGHhI,KAAK6C,IAAIkF,GAASH,GAAwB,IAATK,EAClC,OAAOD,CAEf,CACA,OAAOrI,EAAIoI,CACf,CDsCWG,EAAO,SAAS7C,GACnB,OAAO2B,EAAcC,EAAIC,EAAI7B,GAAYsC,CAC7C,GAAG,EAAGV,EAAKC,EACf,CA2GO,SAASiB,EAAkBC,EAAOjE,GACrC,IAAID,EAiFD,SAAsBkE,EAAOjE,GAIhC,IAHA,IAEoCkE,EAFhCC,EAAOnE,GAAUA,EAAOoE,aAAepE,EAAOoE,aAAe,EAE7D7D,EAAU,CAAC,EAAG8D,EAAc,CAAC,EACxBlJ,EAAI,EAAGA,EAAI8I,EAAMtI,SAAUR,EAAG,CACnC,IAAIwH,EAAOsB,EAAM9I,GACO,GAApBwH,EAAK2B,KAAK3I,SACVuI,EAAMvB,EAAK2B,KAAK,GAChB/D,EAAQ2D,GAAO,CAACjJ,EAAG,KAAMI,EAAG,KACZkJ,MAAOhE,EAAQ5E,OACf6I,KAAM7B,EAAK6B,KACXrD,OAAQtF,KAAKC,KAAK6G,EAAK6B,KAAO3I,KAAKwG,KACnDgC,EAAYH,GAAO,GAE3B,CAIA,IAHAD,EAAQA,EAAMhD,QAAO,SAASzF,GAAK,OAAwB,GAAjBA,EAAE8I,KAAK3I,MAAa,IAGzDR,EAAI,EAAGA,EAAI8I,EAAMtI,SAAUR,EAAG,CAC/B,IAAI4D,EAAUkF,EAAM9I,GAChBsJ,EAAS1F,EAAQ2F,eAAe,UAAY3F,EAAQ0F,OAAS,EAC7DE,EAAO5F,EAAQuF,KAAK,GAAIM,EAAQ7F,EAAQuF,KAAK,GAG7CvF,EAAQyF,KAAO,GAAS3I,KAAKoH,IAAI1C,EAAQoE,GAAMH,KACdjE,EAAQqE,GAAOJ,QAChDC,EAAS,GAGbJ,EAAYM,GAAMnG,KAAM,CAAC0F,IAAIU,EAAOJ,KAAKzF,EAAQyF,KAAMC,OAAOA,IAC9DJ,EAAYO,GAAOpG,KAAK,CAAC0F,IAAIS,EAAOH,KAAKzF,EAAQyF,KAAMC,OAAOA,GAClE,CAGA,IAAII,EAAiB,GACrB,IAAKX,KAAOG,EACR,GAAIA,EAAYK,eAAeR,GAAM,CACjC,IAAIM,EAAO,EACX,IAAKrJ,EAAI,EAAGA,EAAIkJ,EAAYH,GAAKvI,SAAUR,EACvCqJ,GAAQH,EAAYH,GAAK/I,GAAGqJ,KAAOH,EAAYH,GAAK/I,GAAGsJ,OAG3DI,EAAerG,KAAK,CAAC0F,IAAKA,EAAKM,KAAKA,GACxC,CAIJ,SAAS1G,EAAUtC,EAAEC,GACjB,OAAOA,EAAE+I,KAAOhJ,EAAEgJ,IACtB,CACAK,EAAezG,KAAKN,GAGpB,IAAIgH,EAAa,CAAC,EAClB,SAASC,EAAaC,GAClB,OAAOA,EAAQd,OAAOY,CAC1B,CAGA,SAASG,EAAYtH,EAAOuH,GACxB3E,EAAQ2E,GAAOjK,EAAI0C,EAAM1C,EACzBsF,EAAQ2E,GAAO7J,EAAIsC,EAAMtC,EACzByJ,EAAWI,IAAS,CACxB,CAQA,IALAD,EAAY,CAAChK,EAAG,EAAGI,EAAG,GAAIwJ,EAAe,GAAGX,KAKvC/I,EAAI,EAAGA,EAAI0J,EAAelJ,SAAUR,EAAG,CACxC,IAAIgK,EAAWN,EAAe1J,GAAG+I,IAC7BV,EAAUa,EAAYc,GAAUlE,OAAO8D,GAI3C,GAHAb,EAAM3D,EAAQ4E,GACd3B,EAAQpF,KAAKN,GAEU,IAAnB0F,EAAQ7H,OAER,KAAM,8CAIV,IADA,IAAI2H,EAAS,GACJ/G,EAAI,EAAGA,EAAIiH,EAAQ7H,SAAUY,EAAG,CAErC,IAAIsF,EAAKtB,EAAQiD,EAAQjH,GAAG2H,KACxBkB,EAAK7B,EAA0BW,EAAI/C,OAAQU,EAAGV,OACfqC,EAAQjH,GAAGiI,MAG9ClB,EAAO9E,KAAK,CAACvD,EAAI4G,EAAG5G,EAAImK,EAAI/J,EAAIwG,EAAGxG,IACnCiI,EAAO9E,KAAK,CAACvD,EAAI4G,EAAG5G,EAAImK,EAAI/J,EAAIwG,EAAGxG,IACnCiI,EAAO9E,KAAK,CAACnD,EAAIwG,EAAGxG,EAAI+J,EAAInK,EAAI4G,EAAG5G,IACnCqI,EAAO9E,KAAK,CAACnD,EAAIwG,EAAGxG,EAAI+J,EAAInK,EAAI4G,EAAG5G,IAInC,IAAK,IAAI2F,EAAIrE,EAAI,EAAGqE,EAAI4C,EAAQ7H,SAAUiF,EAStC,IARA,IAAIgB,EAAKrB,EAAQiD,EAAQ5C,GAAGsD,KACxBmB,EAAK9B,EAA0BW,EAAI/C,OAAQS,EAAGT,OACfqC,EAAQ5C,GAAG4D,MAE1Cc,EAAc3E,EACd,CAAE1F,EAAG4G,EAAG5G,EAAGI,EAAGwG,EAAGxG,EAAG8F,OAAQiE,GAC5B,CAAEnK,EAAG2G,EAAG3G,EAAGI,EAAGuG,EAAGvG,EAAG8F,OAAQkE,IAEvBE,EAAI,EAAGA,EAAID,EAAY3J,SAAU4J,EACtCjC,EAAO9E,KAAK8G,EAAYC,GAGpC,CAIA,IAAIC,EAAW,KAAMC,EAAYnC,EAAO,GACxC,IAAK/G,EAAI,EAAGA,EAAI+G,EAAO3H,SAAUY,EAAG,CAChCgE,EAAQ4E,GAAUlK,EAAIqI,EAAO/G,GAAGtB,EAChCsF,EAAQ4E,GAAU9J,EAAIiI,EAAO/G,GAAGlB,EAChC,IAAIqK,EAAYvB,EAAK5D,EAAS0D,GAC1ByB,EAAYF,IACZA,EAAWE,EACXD,EAAYnC,EAAO/G,GAE3B,CAEA0I,EAAYQ,EAAWN,EAC3B,CAEA,OAAO5E,CACX,CAlNkBoF,CAAa1B,EAAOjE,GAC9BmE,EAAOnE,EAAOoE,cAAgB,EAMlC,GAAIH,EAAMtI,QAAU,EAAG,CACnB,IAAIiK,EAYL,SAA8B3B,EAAOjE,GAExC,IAG4B7E,EAHxB0K,GADJ7F,EAASA,GAAU,CAAC,GACE6F,UAAY,GAG9BvB,EAAO,GAAIwB,EAAS,CAAC,EACzB,IAAK3K,EAAI,EAAGA,EAAI8I,EAAMtI,SAAUR,EAAI,CAChC,IAAIwH,EAAOsB,EAAM9I,GACO,GAApBwH,EAAK2B,KAAK3I,SACVmK,EAAOnD,EAAK2B,KAAK,IAAMA,EAAK3I,OAC5B2I,EAAK9F,KAAKmE,GAElB,CAEA,IAAIoD,EAvGD,SAA6B9B,EAAOK,EAAMwB,GAE7C,IAAIE,EAAY5K,EAAOkJ,EAAK3I,OAAQ2I,EAAK3I,QACrCsK,EAAc7K,EAAOkJ,EAAK3I,OAAQ2I,EAAK3I,QA0B3C,OAtBAsI,EAAMhD,QAAO,SAAShG,GAAK,OAAwB,GAAjBA,EAAEqJ,KAAK3I,MAAa,IACjDL,KAAI,SAASyD,GACd,IAAI4F,EAAOmB,EAAO/G,EAAQuF,KAAK,IAC3BM,EAAQkB,EAAO/G,EAAQuF,KAAK,IAG5BpD,EAAWqC,EAFN1H,KAAKC,KAAKwI,EAAKK,GAAMH,KAAO3I,KAAKwG,IACjCxG,KAAKC,KAAKwI,EAAKM,GAAOJ,KAAO3I,KAAKwG,IACMtD,EAAQyF,MAEzDwB,EAAUrB,GAAMC,GAASoB,EAAUpB,GAAOD,GAAQzD,EAIlD,IAAIjF,EAAI,EACJ8C,EAAQyF,KAAO,OAAS3I,KAAKoH,IAAIqB,EAAKK,GAAMH,KACXF,EAAKM,GAAOJ,MAC7CvI,EAAI,EACG8C,EAAQyF,MAAQ,QACvBvI,GAAK,GAETgK,EAAYtB,GAAMC,GAASqB,EAAYrB,GAAOD,GAAQ1I,CAC1D,IAEO,CAAC+J,UAAWA,EAAWC,YAAaA,EAC/C,CAyEmBC,CAAoBjC,EAAOK,EAAMwB,GAC5CE,EAAYD,EAASC,UACrBC,EAAcF,EAASE,YAIvBE,EAAOvK,EAAMoK,EAAU1K,IAAIM,IAASoK,EAAgB,OACxDA,EAAYA,EAAU1K,KAAI,SAAU8K,GAChC,OAAOA,EAAI9K,KAAI,SAAUU,GAAS,OAAOA,EAAQmK,CAAM,GAAG,IAE9D,IAIIE,EAAMtH,EAJNuH,EAAM,SAASrL,EAAGoE,GAClB,OAjFR,SAAgCpE,EAAGoE,EAAS2G,EAAWC,GACnD,IAAc9K,EAAVgJ,EAAO,EACX,IAAKhJ,EAAI,EAAGA,EAAIkE,EAAQ1D,SAAUR,EAC9BkE,EAAQlE,GAAK,EAGjB,IAAKA,EAAI,EAAGA,EAAI6K,EAAUrK,SAAUR,EAEhC,IADA,IAAIoL,EAAKtL,EAAE,EAAIE,GAAIqL,EAAKvL,EAAE,EAAIE,EAAI,GACzBoB,EAAIpB,EAAI,EAAGoB,EAAIyJ,EAAUrK,SAAUY,EAAG,CAC3C,IAAIkK,EAAKxL,EAAE,EAAIsB,GAAImK,EAAKzL,EAAE,EAAIsB,EAAI,GAC9BoK,EAAMX,EAAU7K,GAAGoB,GACnBqK,EAAaX,EAAY9K,GAAGoB,GAE5BsK,GAAmBJ,EAAKF,IAAOE,EAAKF,IAAOG,EAAKF,IAAOE,EAAKF,GAC5DtF,EAAWrF,KAAKC,KAAK+K,GACrBjD,EAAQiD,EAAkBF,EAAMA,EAE9BC,EAAa,GAAO1F,GAAYyF,GAChCC,EAAa,GAAO1F,GAAYyF,IAItCxC,GAAQ,EAAIP,EAAQA,EAEpBvE,EAAQ,EAAElE,IAAU,EAAIyI,GAAS2C,EAAKE,GACtCpH,EAAQ,EAAElE,EAAI,IAAM,EAAIyI,GAAS4C,EAAKE,GAEtCrH,EAAQ,EAAE9C,IAAU,EAAIqH,GAAS6C,EAAKF,GACtClH,EAAQ,EAAE9C,EAAI,IAAM,EAAIqH,GAAS8C,EAAKF,GAC1C,CAEJ,OAAOrC,CACX,CAiDe2C,CAAuB7L,EAAGoE,EAAS2G,EAAWC,EACzD,EAGA,IAAK9K,EAAI,EAAGA,EAAI0K,IAAY1K,EAGxB4D,EAAUe,EAAkBwG,EAFdtL,EAAuB,EAAjBgL,EAAUrK,QAAUL,IAAIO,KAAKkL,QAEP/G,KACrCqG,GAAStH,EAAQtB,GAAK4I,EAAK5I,MAC5B4I,EAAOtH,GAGf,IAAIiI,EAAYX,EAAKpL,EAGjBsF,EAAU,CAAC,EACf,IAAKpF,EAAI,EAAGA,EAAImJ,EAAK3I,SAAUR,EAAG,CAC9B,IAAI+I,EAAMI,EAAKnJ,GACfoF,EAAQ2D,EAAII,KAAK,IAAM,CACnBrJ,EAAG+L,EAAU,EAAE7L,GAAKgL,EACpB9K,EAAG2L,EAAU,EAAE7L,EAAI,GAAKgL,EACxBhF,OAAStF,KAAKC,KAAKoI,EAAIM,KAAO3I,KAAKwG,IAE3C,CAEA,GAAIrC,EAAO3B,QACP,IAAKlD,EAAI,EAAGA,EAAI6E,EAAO3B,QAAQ1C,SAAUR,EACrCY,EAAMiE,EAAO3B,QAAQlD,GAAGF,EAAGkL,GAGnC,OAAO5F,CACX,CApE2B0G,CAAqBhD,EAAOjE,GACzBmE,EAAKyB,EAAa3B,GAGlB,KAFLE,EAAKpE,EAASkE,KAG3BlE,EAAU6F,EAElB,CACA,OAAO7F,CACX,CAsMO,SAAS,EAAauE,EAAM4C,GAO/B,IANA,IAAIC,EAAS,EAMJhM,EAAI,EAAGA,EAAI+L,EAASvL,SAAUR,EAAG,CACtC,IAAwBqI,EAApBb,EAAOuE,EAAS/L,GACpB,GAAwB,GAApBwH,EAAK2B,KAAK3I,OAAd,CAEO,GAAwB,GAApBgH,EAAK2B,KAAK3I,OAAa,CAC9B,IAAIgJ,EAAOL,EAAK3B,EAAK2B,KAAK,IACtBM,EAAQN,EAAK3B,EAAK2B,KAAK,IAC3Bd,EAAUX,EAAc8B,EAAKxD,OAAQyD,EAAMzD,OACnBD,EAASyD,EAAMC,GAC3C,MACIpB,EAAUlD,EAA4BqC,EAAK2B,KAbhChJ,KAAI,SAASH,GAAK,OAAOmJ,EAAKnJ,EAAI,KAiBjDgM,IADaxE,EAAK+B,eAAe,UAAY/B,EAAK8B,OAAS,IACvCjB,EAAUb,EAAK6B,OAAShB,EAAUb,EAAK6B,KAH3D,CAIJ,CAEA,OAAO2C,CACX,CAGA,SAASC,EAAiB7G,EAAS8G,EAAaC,GAO5C,IAAInM,EAEJ,GARyB,OAArBmM,EACA/G,EAAQnC,MAAK,SAAU5C,EAAGC,GAAK,OAAOA,EAAE0F,OAAS3F,EAAE2F,MAAQ,IAE3DZ,EAAQnC,KAAKkJ,GAKb/G,EAAQ5E,OAAS,EAAG,CACpB,IAAI4L,EAAWhH,EAAQ,GAAGtF,EACtBuM,EAAWjH,EAAQ,GAAGlF,EAE1B,IAAKF,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAC9BoF,EAAQpF,GAAGF,GAAKsM,EAChBhH,EAAQpF,GAAGE,GAAKmM,CAExB,CAcA,GAZsB,GAAlBjH,EAAQ5E,QAGGuF,EAASX,EAAQ,GAAIA,EAAQ,IAC7B1E,KAAK6C,IAAI6B,EAAQ,GAAGY,OAASZ,EAAQ,GAAGY,UAC/CZ,EAAQ,GAAGtF,EAAIsF,EAAQ,GAAGtF,EAAIsF,EAAQ,GAAGY,OAASZ,EAAQ,GAAGY,OAAS,MACtEZ,EAAQ,GAAGlF,EAAIkF,EAAQ,GAAGlF,GAM9BkF,EAAQ5E,OAAS,EAAG,CACpB,IAE4BV,EAAGI,EAF3BoM,EAAW5L,KAAK8F,MAAMpB,EAAQ,GAAGtF,EAAGsF,EAAQ,GAAGlF,GAAKgM,EACpDpL,EAAIJ,KAAK0G,IAAIkF,GACbC,EAAI7L,KAAKyG,IAAImF,GAEjB,IAAKtM,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAC9BF,EAAIsF,EAAQpF,GAAGF,EACfI,EAAIkF,EAAQpF,GAAGE,EACfkF,EAAQpF,GAAGF,EAAIgB,EAAIhB,EAAIyM,EAAIrM,EAC3BkF,EAAQpF,GAAGE,EAAIqM,EAAIzM,EAAIgB,EAAIZ,CAEnC,CAIA,GAAIkF,EAAQ5E,OAAS,EAAG,CAEpB,IADA,IAAI+F,EAAQ7F,KAAK8F,MAAMpB,EAAQ,GAAGtF,EAAGsF,EAAQ,GAAGlF,GAAKgM,EAC9C3F,EAAQ,GAAKA,GAAS,EAAG7F,KAAKwG,GACrC,KAAOX,EAAQ,EAAE7F,KAAKwG,IAAMX,GAAS,EAAG7F,KAAKwG,GAC7C,GAAIX,EAAQ7F,KAAKwG,GAAI,CACjB,IAAIsF,EAAQpH,EAAQ,GAAGlF,GAAK,MAAQkF,EAAQ,GAAGtF,GAC/C,IAAKE,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAAG,CACjC,IAAI6H,GAAKzC,EAAQpF,GAAGF,EAAI0M,EAAQpH,EAAQpF,GAAGE,IAAM,EAAIsM,EAAMA,GAC3DpH,EAAQpF,GAAGF,EAAI,EAAI+H,EAAIzC,EAAQpF,GAAGF,EAClCsF,EAAQpF,GAAGE,EAAI,EAAI2H,EAAI2E,EAAQpH,EAAQpF,GAAGE,CAC9C,CACJ,CACJ,CACJ,CAoDA,SAASuM,EAAerH,GACpB,IAAIsH,EAAS,SAAS7E,GAKlB,MAAO,CAACvE,IAJC5C,KAAK4C,IAAIqJ,MAAM,KAAMvH,EAAQjF,KACd,SAASW,GAAK,OAAOA,EAAE+G,GAAK/G,EAAEkF,MAAQ,KAG9C8B,IAFPpH,KAAKoH,IAAI6E,MAAM,KAAMvH,EAAQjF,KACd,SAASW,GAAK,OAAOA,EAAE+G,GAAK/G,EAAEkF,MAAO,KAEjE,EAEA,MAAO,CAAC4G,OAAQF,EAAO,KAAMG,OAAQH,EAAO,KAChD,CE1LA,SAASI,EAAalJ,EAASmJ,EAAUC,GACrC,IAAkEhN,EAAGiN,EAAjEC,EAASH,EAAS,GAAG/G,OAASD,EAASgH,EAAS,GAAInJ,GACxD,IAAK5D,EAAI,EAAGA,EAAI+M,EAASvM,SAAUR,GAC/BiN,EAAIF,EAAS/M,GAAGgG,OAASD,EAASgH,EAAS/M,GAAI4D,KACtCsJ,IACLA,EAASD,GAIjB,IAAKjN,EAAI,EAAGA,EAAIgN,EAASxM,SAAUR,GAC/BiN,EAAIlH,EAASiH,EAAShN,GAAI4D,GAAWoJ,EAAShN,GAAGgG,SACxCkH,IACLA,EAASD,GAGjB,OAAOC,CACX,CAKO,SAASC,EAAkBJ,EAAUC,GAGxC,IAAiBhN,EAAbmI,EAAS,GACb,IAAKnI,EAAI,EAAGA,EAAI+M,EAASvM,SAAUR,EAAG,CAClC,IAAIc,EAAIiM,EAAS/M,GACjBmI,EAAO9E,KAAK,CAACvD,EAAGgB,EAAEhB,EAAGI,EAAGY,EAAEZ,IAC1BiI,EAAO9E,KAAK,CAACvD,EAAGgB,EAAEhB,EAAIgB,EAAEkF,OAAO,EAAG9F,EAAGY,EAAEZ,IACvCiI,EAAO9E,KAAK,CAACvD,EAAGgB,EAAEhB,EAAIgB,EAAEkF,OAAO,EAAG9F,EAAGY,EAAEZ,IACvCiI,EAAO9E,KAAK,CAACvD,EAAGgB,EAAEhB,EAAGI,EAAGY,EAAEZ,EAAIY,EAAEkF,OAAO,IACvCmC,EAAO9E,KAAK,CAACvD,EAAGgB,EAAEhB,EAAGI,EAAGY,EAAEZ,EAAIY,EAAEkF,OAAO,GAC3C,CACA,IAAIpB,EAAUuD,EAAO,GAAI+E,EAASJ,EAAa3E,EAAO,GAAI4E,EAAUC,GACpE,IAAKhN,EAAI,EAAGA,EAAImI,EAAO3H,SAAUR,EAAG,CAChC,IAAIiN,EAAIH,EAAa3E,EAAOnI,GAAI+M,EAAUC,GACtCC,GAAKC,IACLtI,EAAUuD,EAAOnI,GACjBkN,EAASD,EAEjB,CAGA,IAAIG,EAAW/L,GACH,SAASqE,GAAK,OAAQ,EAAIoH,EAAa,CAAChN,EAAG4F,EAAE,GAAIxF,EAAGwF,EAAE,IAAKqH,EAAUC,EAAW,GAChF,CAACpI,EAAQ9E,EAAG8E,EAAQ1E,GACpB,CAACwB,cAAc,IAAKG,cAAc,QAAQ/B,EAClDS,EAAM,CAACT,EAAGsN,EAAS,GAAIlN,EAAGkN,EAAS,IAInCC,GAAQ,EACZ,IAAKrN,EAAI,EAAGA,EAAI+M,EAASvM,SAAUR,EAC/B,GAAI+F,EAASxF,EAAKwM,EAAS/M,IAAM+M,EAAS/M,GAAGgG,OAAQ,CACjDqH,GAAQ,EACR,KACJ,CAGJ,IAAKrN,EAAI,EAAGA,EAAIgN,EAASxM,SAAUR,EAC/B,GAAI+F,EAASxF,EAAKyM,EAAShN,IAAMgN,EAAShN,GAAGgG,OAAQ,CACjDqH,GAAQ,EACR,KACJ,CAGJ,IAAKA,EACD,GAAuB,GAAnBN,EAASvM,OACTD,EAAM,CAACT,EAAGiN,EAAS,GAAGjN,EAAGI,EAAG6M,EAAS,GAAG7M,OACrC,CACH,IAAIoN,EAAY,CAAC,EACjBnI,EAAiB4H,EAAUO,GAGvB/M,EAD0B,IAA1B+M,EAAUlH,KAAK5F,OACT,CAAC,EAAK,EAAG,GAAM,IAAM+G,UAAS,GAEJ,GAAzB+F,EAAUlH,KAAK5F,OAChB,CAAC,EAAK8M,EAAUlH,KAAK,GAAGU,OAAOhH,EAC9B,EAAKwN,EAAUlH,KAAK,GAAGU,OAAO5G,GAE9B8M,EAASxM,OAEV2M,EAAkBJ,EAAU,IAO5BzG,EAAUgH,EAAUlH,KAAKjG,KAAI,SAAUE,GAAK,OAAOA,EAAEqG,EAAI,IAEvE,CAGJ,OAAOnG,CACX,CA2BO,SAASgN,EAAmBnI,EAAS0D,GAExC,IADA,IAAIvI,EAAM,CAAC,EAAGiN,EAxBlB,SAA+BpI,GAC3B,IAAI7E,EAAM,CAAC,EAAGkN,EAAY,GAC1B,IAAK,IAAIC,KAAYtI,EACjBqI,EAAUpK,KAAKqK,GACfnN,EAAImN,GAAY,GAEpB,IAAK,IAAI1N,EAAK,EAAGA,EAAIyN,EAAUjN,OAAQR,IAEnC,IADA,IAAIK,EAAI+E,EAAQqI,EAAUzN,IACjBoB,EAAIpB,EAAI,EAAGoB,EAAIqM,EAAUjN,SAAUY,EAAG,CAC3C,IAAId,EAAI8E,EAAQqI,EAAUrM,IACtByG,EAAI9B,EAAS1F,EAAGC,GAEhBuH,EAAIvH,EAAE0F,QAAU3F,EAAE2F,OAAS,MAC3BzF,EAAIkN,EAAUrM,IAAIiC,KAAKoK,EAAUzN,IAE1B6H,EAAIxH,EAAE2F,QAAU1F,EAAE0F,OAAS,OAClCzF,EAAIkN,EAAUzN,IAAIqD,KAAKoK,EAAUrM,GAEzC,CAEJ,OAAOb,CACX,CAG+BoN,CAAsBvI,GACxCpF,EAAI,EAAGA,EAAI8I,EAAMtI,SAAUR,EAAG,CAEnC,IADA,IAAIwH,EAAOsB,EAAM9I,GAAGmJ,KAAMyE,EAAU,CAAC,EAAGC,EAAU,CAAC,EAC1CzM,EAAI,EAAGA,EAAIoG,EAAKhH,SAAUY,EAAG,CAClCwM,EAAQpG,EAAKpG,KAAM,EAKnB,IAJA,IAAI2K,EAAWyB,EAAWhG,EAAKpG,IAItBqE,EAAI,EAAGA,EAAIsG,EAASvL,SAAUiF,EACnCoI,EAAQ9B,EAAStG,KAAM,CAE/B,CAEA,IAAIsH,EAAW,GAAIC,EAAW,GAC9B,IAAK,IAAIc,KAAS1I,EACV0I,KAASF,EACTb,EAAS1J,KAAK+B,EAAQ0I,IACbA,KAASD,GAClBb,EAAS3J,KAAK+B,EAAQ0I,IAG9B,IAAIC,EAASZ,EAAkBJ,EAAUC,GACzCzM,EAAIiH,GAAQuG,EACRA,EAAOxG,UAAauB,EAAM9I,GAAGqJ,KAAO,GACpC2E,QAAQC,IAAI,iBAAmBzG,EAAO,6BAE9C,CACA,OAAQjH,CACZ,C,QCvZO,MAAM2N,UAAaC,EAAA,EAA1B,c,oBASC,oC,gDAA0B,KAO1B,8C,gDAAkCtP,KAAKuP,SAAS/K,KAAKgL,EAAA,EAAUhP,IAAIR,KAAKS,MAAO,CAAC,MAOhF,8C,gDAAkCT,KAAKuP,SAAS/K,KAAKgL,EAAA,EAAUhP,IAAIR,KAAKS,MAAO,CAAC,MAQhF,4C,gDAAgCT,KAAKyP,gBAAgBF,SAAS/K,KAAKkL,EAAA,EAASlP,IAAIR,KAAKS,MAAO,CAAEkP,SAAU,WAAYC,YAAY,OAEhI,uC,yDAcA,qC,gDAAiD5P,KAAK6P,gBAmDtD,qC,gDAA8C7P,KAAK8P,eA8WpD,CA7aW,SAAAC,GACT/P,KAAKgQ,eAAexL,KAAK7E,EAAiBa,IAAIR,KAAKS,QACnDT,KAAKiQ,OAAOzL,KAAK,gBAAiB,WAAY,QAC9C1E,MAAMiQ,WACP,CAcO,SAAAG,CAAUC,GAChB,MAAMvM,EAAQ5D,KAAKyP,gBAAgBF,SAAS/K,KAAKxE,KAAKoQ,OAAOC,QA2B7D,OA1BAzM,EAAM0M,OAAOC,GAAG,eAAgBC,IAC/BxQ,KAAKyQ,SAAWD,EAAEE,OAClB1Q,KAAK2Q,cAAc,IAGpB/M,EAAM0M,OAAOC,GAAG,cAAc,KAC7BvQ,KAAKyQ,cAAWtN,EAChBnD,KAAK4Q,cAAcC,MAAM,IAG1BjN,EAAM2M,GAAG,QAAQ,KAChBvQ,KAAK8Q,mBAAmBX,EAAS,IAGlCvM,EAAM2M,GAAG,eAAe,KACvBvQ,KAAK8Q,mBAAmBX,EAAS,IAGlCvM,EAAM2M,GAAG,UAAU,KAClBvQ,KAAK8Q,mBAAmBX,EAAS,IAGlCvM,EAAMmN,aAAaZ,GACnBA,EAASjG,IAAI,QAAStG,GACtB5D,KAAKoQ,OAAO5L,KAAKZ,GAEVA,CACR,CAEU,YAAA+M,GACT,GAAI3Q,KAAKyQ,SAAU,CAClB,MAAMG,EAAgB5Q,KAAK4Q,cAC3BA,EAAc1G,IAAI,UAAWlK,KAAKyQ,SAASO,IAAI,YAC/CJ,EAAcK,OACdL,EAAcM,S,CAEhB,CAYO,SAAAC,CAAUhB,GAChB,MAAMiB,EAAQpR,KAAKqR,gBAAgB9B,SAAS/K,KAAKxE,KAAKsR,OAAOjB,QAI7D,OAHAe,EAAML,aAAaZ,GACnBA,EAASjG,IAAI,QAASkH,GACtBpR,KAAKsR,OAAO9M,KAAK4M,GACVA,CACR,CAGU,WAAAvB,GACT,OAAO,IAAI0B,EAAA,EACVC,EAAA,GAAShR,IAAI,CAAC,IACd,IAAMkP,EAAA,EAAS+B,KAAKzR,KAAKS,MAAO,CAC/BiR,UAAW,YAAiB1R,KAAKoQ,OAAOuB,SAASX,IAAI,YAAa,IAAK,CAAC,OAAQ,YAC9E,CAAChR,KAAKoQ,OAAOuB,YAElB,CAEU,WAAA7B,GACT,OAAO,IAAIyB,EAAA,EACVC,EAAA,GAAShR,IAAI,CAAC,IACd,IAAMoR,EAAA,EAAMH,KAAKzR,KAAKS,MAAO,CAC5BiR,UAAW,YAAiB1R,KAAKsR,OAAOK,SAASX,IAAI,YAAa,IAAK,CAAC,OAAQ,YAC9E,CAAChR,KAAKsR,OAAOK,YAElB,CAGU,eAAAE,CAAgB1B,GAIzB,GAHArQ,MAAM+R,gBAAgB1B,GAGM,MAAxBA,EAASa,IAAI,QAAiB,CACjC,IAAI1Q,EAASN,KAAKgR,IAAI,UAClB1Q,GACH6P,EAAS2B,OAAO,OAAQxR,EAAO0E,O,CAIjC,GAAmC,MAA/BmL,EAASa,IAAI,eAAwB,CACxC,IAAIe,EAAW/R,KAAKgR,IAAI,YACpBe,GACH5B,EAAS2B,OAAO,cAAeC,EAAS/M,O,CAI1ChF,KAAKkQ,UAAUC,GACfnQ,KAAKmR,UAAUhB,EAChB,CAEO,gBAAA6B,GAGN,GAFAlS,MAAMkS,mBAEFhS,KAAKiS,cAAgBjS,KAAKkS,WAAY,CACzC,MAAM5H,EAAc,GAGpB,OAAYtK,KAAKmS,WAAYhC,IAC5B,MAAMjG,EAAW,CAAC,EACZkI,EAAgBjC,EAASa,IAAI,iBAElC9G,EAAII,KADD8H,GAIQ,CAACjC,EAASa,IAAI,aAE1B9G,EAAIM,KAAO2F,EAASa,IAAI,gBAGxB1G,EAAK9F,KAAK0F,GAGV,MAAMkH,EAAQjB,EAASa,IAAI,SACrBpN,EAAQuM,EAASa,IAAI,SAG3B,IAAIqB,GAAU,EACe,GAAzBlC,EAASa,IAAI,WAChBqB,GAAU,EAENzO,GACHA,EAAMzD,OAAO,CACZc,EAAGjB,KAAKU,QAAU,EAClBW,EAAGrB,KAAKW,SAAW,KAIlByQ,GACHA,EAAMkB,WAAW,UAAWD,E,IAK9B,MAAME,EAAUjI,EAAKkI,WAIrB,GAFAxS,KAAKyS,MAAQF,EAETjI,EAAK3I,OAAS,EAAG,CACpB,IAAI+Q,EHzSD,SAAczI,EAAOtH,IACxBA,EAAaA,GAAc,CAAC,GACjBE,cAAgBF,EAAWE,eAAiB,IACvD,IAAI8P,EAAgBhQ,EAAWgQ,eAAiB3I,EAC5CG,EAAOxH,EAAWyH,cAAgB,EAGtCH,EAgEJ,SAAyBA,GACrBA,EAAQA,EAAMrG,QAGd,IAA0BzC,EAAGoB,EAAGf,EAAGC,EAA/BmR,EAAM,GAAIC,EAAQ,CAAC,EACvB,IAAK1R,EAAI,EAAGA,EAAI8I,EAAMtI,SAAUR,EAAG,CAC/B,IAAIwH,EAAOsB,EAAM9I,GACO,GAApBwH,EAAK2B,KAAK3I,OACViR,EAAIpO,KAAKmE,EAAK2B,KAAK,IACQ,GAApB3B,EAAK2B,KAAK3I,SAGjBkR,EAAM,CAFNrR,EAAImH,EAAK2B,KAAK,GACd7I,EAAIkH,EAAK2B,KAAK,MACE,EAChBuI,EAAM,CAACpR,EAAGD,KAAM,EAExB,CAGA,IAFAoR,EAAIxO,MAAK,SAAS5C,EAAGC,GAAK,OAAOD,EAAIC,CAAG,IAEnCN,EAAI,EAAGA,EAAIyR,EAAIjR,SAAUR,EAE1B,IADAK,EAAIoR,EAAIzR,GACHoB,EAAIpB,EAAI,EAAGoB,EAAIqQ,EAAIjR,SAAUY,EAExB,CAACf,EADPC,EAAImR,EAAIrQ,MACQsQ,GACZ5I,EAAMzF,KAAK,CAAC,KAAQ,CAAChD,EAAGC,GACZ,KAAQ,IAIhC,OAAOwI,CACX,CA7FY6I,CAAgB7I,GAGxB,IAG+BgF,EAH3B1I,EAAUoM,EAAc1I,EAAOtH,GAG/BoD,EAAU,GAAI+F,EAAS,GAC3B,IAAKmD,KAAS1I,EACNA,EAAQmE,eAAeuE,KACvBlJ,EAAQvB,KAAK+B,EAAQ0I,GAAOhO,GAC5B8E,EAAQvB,KAAK+B,EAAQ0I,GAAO5N,GAC5ByK,EAAOtH,KAAKyK,IAyBpB,IApBA,IACIV,EAAW/L,GACX,SAASuQ,GAGL,IADA,IAAIhO,EAAU,CAAC,EACN5D,EAAI,EAAGA,EAAI2K,EAAOnK,SAAUR,EAAG,CACpC,IAAI8N,EAAQnD,EAAO3K,GACnB4D,EAAQkK,GAAS,CAAChO,EAAG8R,EAAO,EAAI5R,GACdE,EAAG0R,EAAO,EAAI5R,EAAI,GAClBgG,OAASZ,EAAQ0I,GAAO9H,OAG9C,CACA,OAAOgD,EAAKpF,EAASkF,EACzB,GACAlE,EACApD,GAGAqK,EAAYuB,EAAStN,EAChBE,EAAI,EAAGA,EAAI2K,EAAOnK,SAAUR,EACjC8N,EAAQnD,EAAO3K,GACfoF,EAAQ0I,GAAOhO,EAAI+L,EAAU,EAAI7L,GACjCoF,EAAQ0I,GAAO5N,EAAI2L,EAAU,EAAI7L,EAAI,GAGzC,OAAOoF,CACX,CGuPmB,CAAU+D,GACzBoI,EHiPG,SAA2BnE,EAAUlB,EAAaC,GACjC,OAAhBD,IACAA,EAAcxL,KAAKwG,GAAG,GAK1B,IAAkBlH,EAAG8N,EAAjB1I,EAAU,GACd,IAAK0I,KAASV,EACV,GAAIA,EAAS7D,eAAeuE,GAAQ,CAChC,IAAI+D,EAAWzE,EAASU,GACxB1I,EAAQ/B,KAAK,CAACvD,EAAG+R,EAAS/R,EACZI,EAAG2R,EAAS3R,EACZ8F,OAAQ6L,EAAS7L,OACjB8H,MAAOA,GACzB,CAIJ,IAAIgE,EAjFD,SAAyB1M,GAK5B,SAAS2M,EAAKjL,GAIV,OAHIA,EAAOkL,SAAWlL,IAClBA,EAAOkL,OAASD,EAAKjL,EAAOkL,SAEzBlL,EAAOkL,MAClB,CARA5M,EAAQjF,KAAI,SAAS2G,GAAUA,EAAOkL,OAASlL,CAAQ,IAgBvD,IAAK,IAAI9G,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAClC,IAAK,IAAIoB,EAAIpB,EAAI,EAAGoB,EAAIgE,EAAQ5E,SAAUY,EAAG,CACzC,IAAI6Q,EAAc7M,EAAQpF,GAAGgG,OAASZ,EAAQhE,GAAG4E,OAC7CD,EAASX,EAAQpF,GAAIoF,EAAQhE,IAAM,MAAQ6Q,IATxCnS,EAUGsF,EAAQhE,GAVRlB,EAUYkF,EAAQpF,GAT9BkS,WAAQH,EAAKjS,GAAIqS,EAAQJ,EAAK7R,GAClCgS,EAAMF,OAASG,EAUf,CAZJ,IAAerS,EAAGI,EACVgS,EAAiBC,EAeErE,EAAvBsE,EAAmB,CAAC,EACxB,IAAKpS,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,GAC9B8N,EAAQiE,EAAK3M,EAAQpF,IAAIgS,OAAOlE,SACjBsE,IACXA,EAAiBtE,GAAS,IAE9BsE,EAAiBtE,GAAOzK,KAAK+B,EAAQpF,IAIzCoF,EAAQjF,KAAI,SAAS2G,UAAiBA,EAAOkL,MAAQ,IAGrD,IAAIzR,EAAM,GACV,IAAKuN,KAASsE,EACNA,EAAiB7I,eAAeuE,IAChCvN,EAAI8C,KAAK+O,EAAiBtE,IAGlC,OAAOvN,CACX,CAiCmB8R,CAAgBjN,GAG/B,IAAKpF,EAAI,EAAGA,EAAI8R,EAAStR,SAAUR,EAAG,CAClCiM,EAAiB6F,EAAS9R,GAAIkM,EAAaC,GAC3C,IAAImG,EAAS7F,EAAeqF,EAAS9R,IACrC8R,EAAS9R,GAAGqJ,MAAQiJ,EAAO1F,OAAOtJ,IAAMgP,EAAO1F,OAAO9E,MAAQwK,EAAOzF,OAAOvJ,IAAMgP,EAAOzF,OAAO/E,KAChGgK,EAAS9R,GAAGsS,OAASA,CACzB,CACAR,EAAS7O,MAAK,SAAS5C,EAAGC,GAAK,OAAOA,EAAE+I,KAAOhJ,EAAEgJ,IAAM,IAIvD,IAAIkJ,GADJnN,EAAU0M,EAAS,IACQQ,OAEvBE,GAAWD,EAAa3F,OAAOtJ,IAAMiP,EAAa3F,OAAO9E,KAAK,GAElE,SAAS2K,EAAWC,EAASjJ,EAAOkJ,GAChC,GAAKD,EAAL,CAEA,IAA6BE,EAASC,EAASC,EAA3CR,EAASI,EAAQJ,OAEjB7I,EACAmJ,EAAUL,EAAa3F,OAAOtJ,IAAOgP,EAAO1F,OAAO9E,IAAM0K,GAEzDI,EAAUL,EAAa3F,OAAOtJ,IAAOgP,EAAO1F,OAAOtJ,KACnDwP,GAAaR,EAAO1F,OAAOtJ,IAAMgP,EAAO1F,OAAO9E,KAAO,GACzCyK,EAAa3F,OAAOtJ,IAAMiP,EAAa3F,OAAO9E,KAAO,GAClD,IAAG8K,GAAWE,IAG9BH,EACAE,EAAUN,EAAa1F,OAAOvJ,IAAOgP,EAAOzF,OAAO/E,IAAM0K,GAEzDK,EAAUN,EAAa1F,OAAOvJ,IAAOgP,EAAOzF,OAAOvJ,KACnDwP,GAAaR,EAAOzF,OAAOvJ,IAAMgP,EAAOzF,OAAO/E,KAAO,GACzCyK,EAAa1F,OAAOvJ,IAAMiP,EAAa1F,OAAO/E,KAAO,GAClD,IAAG+K,GAAWC,IAGlC,IAAK,IAAI1R,EAAI,EAAGA,EAAIsR,EAAQlS,SAAUY,EAClCsR,EAAQtR,GAAGtB,GAAK8S,EAChBF,EAAQtR,GAAGlB,GAAK2S,EAChBzN,EAAQ/B,KAAKqP,EAAQtR,GAzBL,CA2BxB,CAGA,IADA,IAAI2I,EAAQ,EACLA,EAAQ+H,EAAStR,QACpBiS,EAAWX,EAAS/H,IAAQ,GAAM,GAClC0I,EAAWX,EAAS/H,EAAM,IAAI,GAAO,GACrC0I,EAAWX,EAAS/H,EAAM,IAAI,GAAM,GACpCA,GAAS,EAITwI,EAAe9F,EAAerH,GAIlC,IAAI7E,EAAM,CAAC,EACX,IAAKP,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAC9BO,EAAI6E,EAAQpF,GAAG8N,OAAS1I,EAAQpF,GAEpC,OAAOO,CACX,CGrUe,CAAuBgR,EAAU,KAAM,MAClDA,EHyUG,SAAuBnE,EAAU7N,EAAOC,EAAQuT,GACnD,IAAI3N,EAAU,GAAIuF,EAAS,GAC3B,IAAK,IAAImD,KAASV,EACVA,EAAS7D,eAAeuE,KACxBnD,EAAOtH,KAAKyK,GACZ1I,EAAQ/B,KAAK+J,EAASU,KAI9BvO,GAAS,EACTC,GAAU,EAEV,IAAI8S,EAAS7F,EAAerH,GACxBwH,EAAS0F,EAAO1F,OAChBC,EAASyF,EAAOzF,OAEpB,GAAKD,EAAOtJ,KAAOsJ,EAAO9E,KACrB+E,EAAOvJ,KAAOuJ,EAAO/E,IAEtB,OADAkG,QAAQC,IAAI,4CACLb,EAYX,IATA,IAAI4F,EAAWzT,GAAUqN,EAAOtJ,IAAMsJ,EAAO9E,KACzCmL,EAAWzT,GAAUqN,EAAOvJ,IAAMuJ,EAAO/E,KACzCoL,EAAUxS,KAAKoH,IAAImL,EAAUD,GAG7BJ,GAAWrT,GAAUqN,EAAOtJ,IAAMsJ,EAAO9E,KAAOoL,GAAW,EAC3DL,GAAWrT,GAAUqN,EAAOvJ,IAAMuJ,EAAO/E,KAAOoL,GAAW,EAE3DC,EAAS,CAAC,EACLnT,EAAI,EAAGA,EAAIoF,EAAQ5E,SAAUR,EAAG,CACrC,IAAI8G,EAAS1B,EAAQpF,GACrBmT,EAAOxI,EAAO3K,IAAM,CAChBgG,OAAQkN,EAAUpM,EAAOd,OACzBlG,EG5WuE,EH4W1D8S,GAAW9L,EAAOhH,EAAI8M,EAAO9E,KAAOoL,EACjDhT,EG7WuE,EH6W1D2S,GAAW/L,EAAO5G,EAAI2M,EAAO/E,KAAOoL,EAEzD,CAEA,OAAOC,CACX,CGlXe,CAAmB5B,EAAU1S,KAAKuU,aAAcvU,KAAKwU,eAEhE,MAAMjO,EAAe,CAAC,EACtB,IAAK,IAAIkO,KAAQ/B,EAAU,CAC1B,IAAIgC,EAAOhC,EAAS+B,GAChB1U,EAAI2U,EAAKvN,OAEb,MAAMgJ,EAAWnQ,KAAK2U,sBAAsBF,GAC5C,GAAItE,EAAU,CACb,MAAMvM,EAAQuM,EAASa,IAAI,SACrB4D,EAAQzE,EAASa,IAAI,QAC3BpN,EAAMiR,YAAY,OAAQD,GAE1B,MAAME,EAAc3E,EAASa,IAAI,eACjCpN,EAAMiR,YAAY,cAAeC,GAEjClR,EAAMiR,YAAY,SAAUD,GAE5B5U,KAAK8Q,mBAAmBX,GAExBvM,EAAMsG,IAAI,UAAW,IAAMwK,EAAKzT,EAAI,IAAMyT,EAAKrT,EAAI,OAAStB,EAAI,SAAWA,EAAI,IAAMA,EAAI,UAAgB,EAAJA,EAAQ,QAAUA,EAAI,IAAMA,EAAI,WAAiB,EAAJA,EAAQ,MAC1JwG,EAAQkO,GAAQC,C,EAKlB,IAAIK,EAAe,EAAwBxO,EAAS+D,GAGpD,OAAYtK,KAAKmS,WAAYhC,IAC5B,IAAIsE,EAAOtE,EAASa,IAAI,YACpBxJ,EAASuN,EAAQN,GACrB,MAAMrC,EAAgBjC,EAASa,IAAI,iBACnC,GAAIoB,IACHqC,EAAOrC,EAAcI,WACrBhL,EAASuN,EAAQN,GACbjN,GAAQ,CACX,IAAI0C,EAAMkI,EACN4C,EAAK,GAET,IAAK,IAAItH,EAAI,EAAGA,EAAIxD,EAAIvI,OAAQ+L,IAC/BsH,EAAGxQ,KAAK+B,EAAQ2D,EAAIwD,KAErB,IAAIuH,EDwOJ,SAA8B1O,GACjC,IAAIC,EAAQ,CAAC,EACbF,EAAiBC,EAASC,GAC1B,IAAIe,EAAOf,EAAMe,KAEjB,GAAoB,IAAhBA,EAAK5F,OACL,MAAO,QAEJ,GAAmB,GAAf4F,EAAK5F,OAAa,CACzB,IAAIsG,EAASV,EAAK,GAAGU,OACrB,OA7BD,SAAoBhH,EAAGI,EAAGtB,GAC7B,IAAI2B,EAAM,GAKV,OAJAA,EAAI8C,KAAK,MAAOvD,EAAGI,GACnBK,EAAI8C,KAAK,OAAQzE,EAAG,GACpB2B,EAAI8C,KAAK,MAAOzE,EAAGA,EAAG,EAAG,EAAG,EAAM,EAAHA,EAAM,GACrC2B,EAAI8C,KAAK,MAAOzE,EAAGA,EAAG,EAAG,EAAG,EAAM,GAAHA,EAAM,GAC9B2B,EAAIwT,KAAK,IACpB,CAsBeC,CAAWlN,EAAOhH,EAAGgH,EAAO5G,EAAG4G,EAAOd,OAEjD,CAGI,IADA,IAAIzF,EAAM,CAAC,MAAO6F,EAAK,GAAGK,GAAG3G,EAAGsG,EAAK,GAAGK,GAAGvG,GAClCF,EAAI,EAAGA,EAAIoG,EAAK5F,SAAUR,EAAG,CAClC,IAAI4G,EAAMR,EAAKpG,GAAIpB,EAAIgI,EAAIE,OAAOd,OAAQiO,EAAOrN,EAAIrH,MAAQX,EAC7D2B,EAAI8C,KAAK,MAAOzE,EAAGA,EAAG,EAAGqV,EAAO,EAAI,EAAG,EAC9BrN,EAAIF,GAAG5G,EAAG8G,EAAIF,GAAGxG,EAC9B,CACA,OAAOK,EAAIwT,KAAK,IAExB,CC9P8B,CAA0BF,GAC7CpR,EAAQuM,EAASa,IAAI,SAEzB,MAAM4D,EAAQzE,EAASa,IAAI,QAC3BpN,EAAMiR,YAAY,OAAQD,GAC1BhR,EAAMiR,YAAY,SAAUD,GAE5B,MAAME,EAAc3E,EAASa,IAAI,eACjCpN,EAAMiR,YAAY,cAAeC,GAEjClR,EAAMzD,OAAO,CAAEkV,QAASJ,G,CAItBzN,GACS2I,EAASa,IAAI,SACnB7Q,OAAO,CAAEc,EAAGuG,EAAOvG,EAAGI,EAAGmG,EAAOnG,IAGvCrB,KAAKsV,kBAAkBnF,EAAS,G,CAIlCnQ,KAAK2Q,c,CAEP,CAQO,qBAAAgE,CAAsBjR,GAC5B,OAAO,OAAY1D,KAAKmS,WAAYhC,GAC5BA,EAASa,IAAI,aAAetN,GAErC,CAUa,YAAA6R,CAAapF,EAA+CqF,G,uHACxE,MAAMC,EAAW,CAAC,EAAMF,aAAY,UAACpF,EAAUqF,IAC1C,WAAeA,KACnBA,EAAWxV,KAAKgR,IAAI,yBAA0B,IAG/C,MAAM0E,EAAS1V,KAAKgR,IAAI,wBAExB,IAAIhP,EAAQmO,EAASa,IAAI,SAEzB,MAAM2E,EAAYxF,EAASyF,QAAQ,CAAEC,IAAK,eAAgBC,GAAI9T,EAAOwT,SAAUA,EAAUE,OAAQA,IAC7FC,GACHF,EAASjR,KAAKmR,EAAUI,eAGzB,MAAM3E,EAAQjB,EAASa,IAAI,SACvBI,GACHqE,EAASjR,KAAK4M,EAAMH,KAAKuE,IAG1B,MAAM5R,EAAQuM,EAASa,IAAI,SACvBpN,GACH6R,EAASjR,KAAKZ,EAAMqN,KAAKuE,IAI1B,MAAMpD,EAAgBjC,EAASa,IAAI,iBAUnC,GATIoB,GACH,OAAYA,GAAgB4D,IAC3B,MAAMC,EAAKjW,KAAK2U,sBAAsBqB,GAClCC,GAAMA,EAAGC,YACZlW,KAAKuV,aAAaU,EAAIT,E,KAKpBpD,EAAe,CACnB,MAAM+D,EAAWhG,EAASa,IAAI,YAE9B,OAAYhR,KAAKmS,WAAY8D,IAC5B,MAAM7D,EAAgB6D,EAAGjF,IAAI,iBAC7B,GAAIiF,GAAM9F,GAAYiC,EAAe,CACpC,IAAIgE,GAAa,EACjB,OAAYhE,GAAgB4D,IAC3B,MAAMK,EAAMrW,KAAK2U,sBAAsBqB,GACnCK,GAAOA,EAAIH,aACdE,GAAa,E,IAIXA,IAAkD,GAApChE,EAAcpK,QAAQmO,IACnCF,EAAGC,YACNlW,KAAKuV,aAAaU,EAAIT,E,WAOrBc,QAAQC,IAAId,EACnB,G,CASa,YAAAe,CAAarG,EAA+CqF,G,uHACxE,MAAMC,EAAW,CAAC,EAAMe,aAAY,UAACrG,EAAUqF,IACzCiB,EAAczW,KAAK0W,OAAOC,OAAO,SAAU,CAAC,GAE7C,WAAenB,KACnBA,EAAWiB,EAAYzF,IAAI,yBAA0BhR,KAAKgR,IAAI,yBAA0B,KAGzF,MAAM0E,EAASe,EAAYzF,IAAI,uBAAwBhR,KAAKgR,IAAI,yBAE1D2E,EAAYxF,EAASyF,QAAQ,CAAEC,IAAK,eAAgBC,GAAI,EAAGN,SAAUA,EAAUE,OAAQA,IACzFC,GACHF,EAASjR,KAAKmR,EAAUI,eAGzB,MAAM3E,EAAQjB,EAASa,IAAI,SACvBI,GACHqE,EAASjR,KAAK4M,EAAMP,KAAK2E,IAG1B,MAAM5R,EAAQuM,EAASa,IAAI,SACvBpN,IACH6R,EAASjR,KAAKZ,EAAMiN,KAAK2E,IACzB5R,EAAMgT,eAGFzG,EAASa,IAAI,kBACjB,OAAYhR,KAAKmS,WAAY8D,IAC5B,MAAM7D,EAAgB6D,EAAGjF,IAAI,iBACzBiF,GAAM9F,GAAYiC,IACmC,GAApDA,EAAcpK,QAAQmI,EAASa,IAAI,cACtChR,KAAKwW,aAAaP,EAAIT,E,UAMpBc,QAAQC,IAAId,EACnB,G,CAKO,eAAAoB,CAAgB1G,GACtBrQ,MAAM+W,gBAAgB1G,GACtB,IAAIiB,EAAQjB,EAASa,IAAI,SACrBI,IACHpR,KAAKsR,OAAOwF,YAAY1F,GACxBA,EAAM2F,WAGP,IAAInT,EAAQuM,EAASa,IAAI,SACrBpN,IACH5D,KAAKoQ,OAAO0G,YAAYlT,GACxBA,EAAMmT,UAER,CAKO,kBAAAjG,CAAmBX,GACzB,MAAMvM,EAAQuM,EAASa,IAAI,SAE3B,GAAIpN,EAAO,CACV,MAAMoT,EAAiB7G,EAASa,IAAI,kBACpC,GAAIgG,EAAgB,CACnB,MAAMC,EAAkBD,EAAehG,IAAI,mBACtCb,EAAS+F,YACb,OAAY,KAAiBgB,IAC5BD,EAAgB/M,IAAIgN,EAAStT,EAAMoN,IAAIkG,GAAS,G,EAKrD,CAQO,aAAAC,CAAchH,GACpB,MAAMvM,EAAQuM,EAASa,IAAI,SACvBpN,IAAUA,EAAMsS,YACnBtS,EAAMwT,OAER,CAQO,eAAAC,CAAgBlH,GACtB,MAAMvM,EAAQuM,EAASa,IAAI,SACvBpN,GACHA,EAAM0T,SAER,EA7cA,qC,gDAAkC,SAClC,sC,gDAA0ChI,EAAA,EAAOiI,WAAWC,OAAO,CAACnI,EAAKoI,a,oECzGnE,MAAMC,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/venn/VennDefaultTheme.ts", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/fmin/blas1.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/fmin/nelderMead.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/fmin/linesearch.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/fmin/conjugateGradient.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/circleintersection.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/layout.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/fmin/bisect.js", "webpack://@amcharts/amcharts5/./dist/es2015/.internal/charts/venn/vennjs/diagram.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/venn/Venn.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/venn.js"], "sourcesContent": ["import { Theme } from \"../../core/Theme\";\r\nimport { p50, p100 } from \"../../core/util/Percent\";\r\nimport { ColorSet } from \"../../core/util/ColorSet\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class VennDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\t\tconst r = this.rule.bind(this);\r\n\r\n\t\tr(\"Venn\").setAll({\r\n\t\t\tlegendLabelText: \"{category}\",\r\n\t\t\tlegendValueText: \"{value}\",\r\n\t\t\tcolors: ColorSet.new(this._root, {}),\r\n\t\t\twidth: p100,\r\n\t\t\theight: p100\r\n\t\t});\r\n\r\n\r\n\t\tr(\"Label\", [\"venn\"]).setAll({\r\n\t\t\ttext: \"{category}\",\r\n\t\t\tpopulateText: true,\r\n\t\t\tcenterX: p50,\r\n\t\t\tcenterY: p50\r\n\t\t});\r\n\r\n\t}\r\n}\r\n", "// need some basic operations on vectors, rather than adding a dependency,\n// just define here\nexport function zeros(x) { var r = new Array(x); for (var i = 0; i < x; ++i) { r[i] = 0; } return r; }\nexport function zerosM(x,y) { return zeros(x).map(function() { return zeros(y); }); }\n\nexport function dot(a, b) {\n    var ret = 0;\n    for (var i = 0; i < a.length; ++i) {\n        ret += a[i] * b[i];\n    }\n    return ret;\n}\n\nexport function norm2(a)  {\n    return Math.sqrt(dot(a, a));\n}\n\nexport function scale(ret, value, c) {\n    for (var i = 0; i < value.length; ++i) {\n        ret[i] = value[i] * c;\n    }\n}\n\nexport function weightedSum(ret, w1, v1, w2, v2) {\n    for (var j = 0; j < ret.length; ++j) {\n        ret[j] = w1 * v1[j] + w2 * v2[j];\n    }\n}\n", "import {dot, norm2, weightedSum} from \"./blas1\";\n\n/** minimizes a function using the downhill simplex method */\nexport function nelderMead(f, x0, parameters) {\n    parameters = parameters || {};\n\n    var maxIterations = parameters.maxIterations || x0.length * 200,\n        nonZeroDelta = parameters.nonZeroDelta || 1.05,\n        zeroDelta = parameters.zeroDelta || 0.001,\n        minErrorDelta = parameters.minErrorDelta || 1e-6,\n        minTolerance = parameters.minErrorDelta || 1e-5,\n        rho = (parameters.rho !== undefined) ? parameters.rho : 1,\n        chi = (parameters.chi !== undefined) ? parameters.chi : 2,\n        psi = (parameters.psi !== undefined) ? parameters.psi : -0.5,\n        sigma = (parameters.sigma !== undefined) ? parameters.sigma : 0.5,\n        maxDiff;\n\n    // initialize simplex.\n    var N = x0.length,\n        simplex = new Array(N + 1);\n    simplex[0] = x0;\n    simplex[0].fx = f(x0);\n    simplex[0].id = 0;\n    for (var i = 0; i < N; ++i) {\n        var point = x0.slice();\n        point[i] = point[i] ? point[i] * nonZeroDelta : zeroDelta;\n        simplex[i+1] = point;\n        simplex[i+1].fx = f(point);\n        simplex[i+1].id = i+1;\n    }\n\n    function updateSimplex(value) {\n        for (var i = 0; i < value.length; i++) {\n            simplex[N][i] = value[i];\n        }\n        simplex[N].fx = value.fx;\n    }\n\n    var sortOrder = function(a, b) { return a.fx - b.fx; };\n\n    var centroid = x0.slice(),\n        reflected = x0.slice(),\n        contracted = x0.slice(),\n        expanded = x0.slice();\n\n    for (var iteration = 0; iteration < maxIterations; ++iteration) {\n        simplex.sort(sortOrder);\n\n        if (parameters.history) {\n            // copy the simplex (since later iterations will mutate) and\n            // sort it to have a consistent order between iterations\n            var sortedSimplex = simplex.map(function (x) {\n                var state = x.slice();\n                state.fx = x.fx;\n                state.id = x.id;\n                return state;\n            });\n            sortedSimplex.sort(function(a,b) { return a.id - b.id; });\n\n            parameters.history.push({x: simplex[0].slice(),\n                                     fx: simplex[0].fx,\n                                     simplex: sortedSimplex});\n        }\n\n        maxDiff = 0;\n        for (i = 0; i < N; ++i) {\n            maxDiff = Math.max(maxDiff, Math.abs(simplex[0][i] - simplex[1][i]));\n        }\n\n        if ((Math.abs(simplex[0].fx - simplex[N].fx) < minErrorDelta) &&\n            (maxDiff < minTolerance)) {\n            break;\n        }\n\n        // compute the centroid of all but the worst point in the simplex\n        for (i = 0; i < N; ++i) {\n            centroid[i] = 0;\n            for (var j = 0; j < N; ++j) {\n                centroid[i] += simplex[j][i];\n            }\n            centroid[i] /= N;\n        }\n\n        // reflect the worst point past the centroid  and compute loss at reflected\n        // point\n        var worst = simplex[N];\n        weightedSum(reflected, 1+rho, centroid, -rho, worst);\n        reflected.fx = f(reflected);\n\n        // if the reflected point is the best seen, then possibly expand\n        if (reflected.fx < simplex[0].fx) {\n            weightedSum(expanded, 1+chi, centroid, -chi, worst);\n            expanded.fx = f(expanded);\n            if (expanded.fx < reflected.fx) {\n                updateSimplex(expanded);\n            }  else {\n                updateSimplex(reflected);\n            }\n        }\n\n        // if the reflected point is worse than the second worst, we need to\n        // contract\n        else if (reflected.fx >= simplex[N-1].fx) {\n            var shouldReduce = false;\n\n            if (reflected.fx > worst.fx) {\n                // do an inside contraction\n                weightedSum(contracted, 1+psi, centroid, -psi, worst);\n                contracted.fx = f(contracted);\n                if (contracted.fx < worst.fx) {\n                    updateSimplex(contracted);\n                } else {\n                    shouldReduce = true;\n                }\n            } else {\n                // do an outside contraction\n                weightedSum(contracted, 1-psi * rho, centroid, psi*rho, worst);\n                contracted.fx = f(contracted);\n                if (contracted.fx < reflected.fx) {\n                    updateSimplex(contracted);\n                } else {\n                    shouldReduce = true;\n                }\n            }\n\n            if (shouldReduce) {\n                // if we don't contract here, we're done\n                if (sigma >= 1) break;\n\n                // do a reduction\n                for (i = 1; i < simplex.length; ++i) {\n                    weightedSum(simplex[i], 1 - sigma, simplex[0], sigma, simplex[i]);\n                    simplex[i].fx = f(simplex[i]);\n                }\n            }\n        } else {\n            updateSimplex(reflected);\n        }\n    }\n\n    simplex.sort(sortOrder);\n    return {fx : simplex[0].fx,\n            x : simplex[0]};\n}\n", "import {dot, weightedSum} from \"./blas1\";\n\n\n/// searches along line 'pk' for a point that satifies the wolfe conditions\n/// See 'Numerical Optimization' by <PERSON><PERSON><PERSON> and <PERSON> p59-60\n/// f : objective function\n/// pk : search direction\n/// current: object containing current gradient/loss\n/// next: output: contains next gradient/loss\n/// returns a: step size taken\nexport function wolfeLineSearch(f, pk, current, next, a, c1, c2) {\n    var phi0 = current.fx, phiPrime0 = dot(current.fxprime, pk),\n        phi = phi0, phi_old = phi0,\n        phiPrime = phiPrime0,\n        a0 = 0;\n\n    a = a || 1;\n    c1 = c1 || 1e-6;\n    c2 = c2 || 0.1;\n\n    function zoom(a_lo, a_high, phi_lo) {\n        for (var iteration = 0; iteration < 16; ++iteration) {\n            a = (a_lo + a_high)/2;\n            weightedSum(next.x, 1.0, current.x, a, pk);\n            phi = next.fx = f(next.x, next.fxprime);\n            phiPrime = dot(next.fxprime, pk);\n\n            if ((phi > (phi0 + c1 * a * phiPrime0)) ||\n                (phi >= phi_lo)) {\n                a_high = a;\n\n            } else  {\n                if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n                    return a;\n                }\n\n                if (phiPrime * (a_high - a_lo) >=0) {\n                    a_high = a_lo;\n                }\n\n                a_lo = a;\n                phi_lo = phi;\n            }\n        }\n\n        return 0;\n    }\n\n    for (var iteration = 0; iteration < 10; ++iteration) {\n        weightedSum(next.x, 1.0, current.x, a, pk);\n        phi = next.fx = f(next.x, next.fxprime);\n        phiPrime = dot(next.fxprime, pk);\n        if ((phi > (phi0 + c1 * a * phiPrime0)) ||\n            (iteration && (phi >= phi_old))) {\n            return zoom(a0, a, phi_old);\n        }\n\n        if (Math.abs(phiPrime) <= -c2 * phiPrime0) {\n            return a;\n        }\n\n        if (phiPrime >= 0 ) {\n            return zoom(a, a0, phi);\n        }\n\n        phi_old = phi;\n        a0 = a;\n        a *= 2;\n    }\n\n    return a;\n}\n", "import {dot, norm2, scale, weightedSum} from \"./blas1\";\nimport {wolfeLineSearch} from \"./linesearch\";\n\nexport function conjugateGradient(f, initial, params) {\n    // allocate all memory up front here, keep out of the loop for perfomance\n    // reasons\n    var current = {x: initial.slice(), fx: 0, fxprime: initial.slice()},\n        next = {x: initial.slice(), fx: 0, fxprime: initial.slice()},\n        yk = initial.slice(),\n        pk, temp,\n        a = 1,\n        maxIterations;\n\n    params = params || {};\n    maxIterations = params.maxIterations || initial.length * 20;\n\n    current.fx = f(current.x, current.fxprime);\n    pk = current.fxprime.slice();\n    scale(pk, current.fxprime,-1);\n\n    for (var i = 0; i < maxIterations; ++i) {\n        a = wolfeLineSearch(f, pk, current, next, a);\n\n        // todo: history in wrong spot?\n        if (params.history) {\n            params.history.push({x: current.x.slice(),\n                                 fx: current.fx,\n                                 fxprime: current.fxprime.slice(),\n                                 alpha: a});\n        }\n\n        if (!a) {\n            // faiiled to find point that satifies wolfe conditions.\n            // reset direction for next iteration\n            scale(pk, current.fxprime, -1);\n\n        } else {\n            // update direction using Polak–Ribiere CG method\n            weightedSum(yk, 1, next.fxprime, -1, current.fxprime);\n\n            var delta_k = dot(current.fxprime, current.fxprime),\n                beta_k = Math.max(0, dot(yk, next.fxprime) / delta_k);\n\n            weightedSum(pk, beta_k, pk, -1, next.fxprime);\n\n            temp = current;\n            current = next;\n            next = temp;\n        }\n\n        if (norm2(current.fxprime) <= 1e-5) {\n            break;\n        }\n    }\n\n    if (params.history) {\n        params.history.push({x: current.x.slice(),\n                             fx: current.fx,\n                             fxprime: current.fxprime.slice(),\n                             alpha: a});\n    }\n\n    return current;\n}\n", "var SMALL = 1e-10;\n\n/** Returns the intersection area of a bunch of circles (where each circle\n is an object having an x,y and radius property) */\nexport function intersectionArea(circles, stats) {\n    // get all the intersection points of the circles\n    var intersectionPoints = getIntersectionPoints(circles);\n\n    // filter out points that aren't included in all the circles\n    var innerPoints = intersectionPoints.filter(function (p) {\n        return containedInCircles(p, circles);\n    });\n\n    var arcArea = 0, polygonArea = 0, arcs = [], i;\n\n    // if we have intersection points that are within all the circles,\n    // then figure out the area contained by them\n    if (innerPoints.length > 1) {\n        // sort the points by angle from the center of the polygon, which lets\n        // us just iterate over points to get the edges\n        var center = getCenter(innerPoints);\n        for (i = 0; i < innerPoints.length; ++i ) {\n            var p = innerPoints[i];\n            p.angle = Math.atan2(p.x - center.x, p.y - center.y);\n        }\n        innerPoints.sort(function(a,b) { return b.angle - a.angle;});\n\n        // iterate over all points, get arc between the points\n        // and update the areas\n        var p2 = innerPoints[innerPoints.length - 1];\n        for (i = 0; i < innerPoints.length; ++i) {\n            var p1 = innerPoints[i];\n\n            // polygon area updates easily ...\n            polygonArea += (p2.x + p1.x) * (p1.y - p2.y);\n\n            // updating the arc area is a little more involved\n            var midPoint = {x : (p1.x + p2.x) / 2,\n                            y : (p1.y + p2.y) / 2},\n                arc = null;\n\n            for (var j = 0; j < p1.parentIndex.length; ++j) {\n                if (p2.parentIndex.indexOf(p1.parentIndex[j]) > -1) {\n                    // figure out the angle halfway between the two points\n                    // on the current circle\n                    var circle = circles[p1.parentIndex[j]],\n                        a1 = Math.atan2(p1.x - circle.x, p1.y - circle.y),\n                        a2 = Math.atan2(p2.x - circle.x, p2.y - circle.y);\n\n                    var angleDiff = (a2 - a1);\n                    if (angleDiff < 0) {\n                        angleDiff += 2*Math.PI;\n                    }\n\n                    // and use that angle to figure out the width of the\n                    // arc\n                    var a = a2 - angleDiff/2,\n                        width = distance(midPoint, {\n                            x : circle.x + circle.radius * Math.sin(a),\n                            y : circle.y + circle.radius * Math.cos(a)\n                        });\n\n                    // clamp the width to the largest is can actually be\n                    // (sometimes slightly overflows because of FP errors)\n                    if (width > circle.radius * 2) {\n                        width = circle.radius * 2;\n                    }\n\n                    // pick the circle whose arc has the smallest width\n                    if ((arc === null) || (arc.width > width)) {\n                        arc = { circle : circle,\n                                width : width,\n                                p1 : p1,\n                                p2 : p2};\n                    }\n                }\n            }\n\n            if (arc !== null) {\n                arcs.push(arc);\n                arcArea += circleArea(arc.circle.radius, arc.width);\n                p2 = p1;\n            }\n        }\n    } else {\n        // no intersection points, is either disjoint - or is completely\n        // overlapped. figure out which by examining the smallest circle\n        var smallest = circles[0];\n        for (i = 1; i < circles.length; ++i) {\n            if (circles[i].radius < smallest.radius) {\n                smallest = circles[i];\n            }\n        }\n\n        // make sure the smallest circle is completely contained in all\n        // the other circles\n        var disjoint = false;\n        for (i = 0; i < circles.length; ++i) {\n            if (distance(circles[i], smallest) > Math.abs(smallest.radius - circles[i].radius)) {\n                disjoint = true;\n                break;\n            }\n        }\n\n        if (disjoint) {\n            arcArea = polygonArea = 0;\n\n        } else {\n            arcArea = smallest.radius * smallest.radius * Math.PI;\n            arcs.push({circle : smallest,\n                       p1: { x: smallest.x,        y : smallest.y + smallest.radius},\n                       p2: { x: smallest.x - SMALL, y : smallest.y + smallest.radius},\n                       width : smallest.radius * 2 });\n        }\n    }\n\n    polygonArea /= 2;\n    if (stats) {\n        stats.area = arcArea + polygonArea;\n        stats.arcArea = arcArea;\n        stats.polygonArea = polygonArea;\n        stats.arcs = arcs;\n        stats.innerPoints = innerPoints;\n        stats.intersectionPoints = intersectionPoints;\n    }\n\n    return arcArea + polygonArea;\n}\n\n/** returns whether a point is contained by all of a list of circles */\nexport function containedInCircles(point, circles) {\n    for (var i = 0; i < circles.length; ++i) {\n        if (distance(point, circles[i]) > circles[i].radius + SMALL) {\n            return false;\n        }\n    }\n    return true;\n}\n\n/** Gets all intersection points between a bunch of circles */\nfunction getIntersectionPoints(circles) {\n    var ret = [];\n    for (var i = 0; i < circles.length; ++i) {\n        for (var j = i + 1; j < circles.length; ++j) {\n            var intersect = circleCircleIntersection(circles[i],\n                                                          circles[j]);\n            for (var k = 0; k < intersect.length; ++k) {\n                var p = intersect[k];\n                p.parentIndex = [i,j];\n                ret.push(p);\n            }\n        }\n    }\n    return ret;\n}\n\n/** Circular segment area calculation. See http://mathworld.wolfram.com/CircularSegment.html */\nexport function circleArea(r, width) {\n    return r * r * Math.acos(1 - width/r) - (r - width) * Math.sqrt(width * (2 * r - width));\n}\n\n/** euclidean distance between two points */\nexport function distance(p1, p2) {\n    return Math.sqrt((p1.x - p2.x) * (p1.x - p2.x) +\n                     (p1.y - p2.y) * (p1.y - p2.y));\n}\n\n\n/** Returns the overlap area of two circles of radius r1 and r2 - that\nhave their centers separated by distance d. Simpler faster\ncircle intersection for only two circles */\nexport function circleOverlap(r1, r2, d) {\n    // no overlap\n    if (d >= r1 + r2) {\n        return 0;\n    }\n\n    // completely overlapped\n    if (d <= Math.abs(r1 - r2)) {\n        return Math.PI * Math.min(r1, r2) * Math.min(r1, r2);\n    }\n\n    var w1 = r1 - (d * d - r2 * r2 + r1 * r1) / (2 * d),\n        w2 = r2 - (d * d - r1 * r1 + r2 * r2) / (2 * d);\n    return circleArea(r1, w1) + circleArea(r2, w2);\n}\n\n/** Given two circles (containing a x/y/radius attributes),\nreturns the intersecting points if possible.\nnote: doesn't handle cases where there are infinitely many\nintersection points (circles are equivalent):, or only one intersection point*/\nexport function circleCircleIntersection(p1, p2) {\n    var d = distance(p1, p2),\n        r1 = p1.radius,\n        r2 = p2.radius;\n\n    // if to far away, or self contained - can't be done\n    if ((d >= (r1 + r2)) || (d <= Math.abs(r1 - r2))) {\n        return [];\n    }\n\n    var a = (r1 * r1 - r2 * r2 + d * d) / (2 * d),\n        h = Math.sqrt(r1 * r1 - a * a),\n        x0 = p1.x + a * (p2.x - p1.x) / d,\n        y0 = p1.y + a * (p2.y - p1.y) / d,\n        rx = -(p2.y - p1.y) * (h / d),\n        ry = -(p2.x - p1.x) * (h / d);\n\n    return [{x: x0 + rx, y : y0 - ry },\n            {x: x0 - rx, y : y0 + ry }];\n}\n\n/** Returns the center of a bunch of points */\nexport function getCenter(points) {\n    var center = {x: 0, y: 0};\n    for (var i =0; i < points.length; ++i ) {\n        center.x += points[i].x;\n        center.y += points[i].y;\n    }\n    center.x /= points.length;\n    center.y /= points.length;\n    return center;\n}\n", "import {nelderMead, bisect, conjugateGradient, zeros, zerosM, norm2,\n        scale} from './fmin/index.js';\nimport {intersectionArea, circleOverlap, circleCircleIntersection, distance} from './circleintersection';\n\n/** given a list of set objects, and their corresponding overlaps.\nupdates the (x, y, radius) attribute on each set such that their positions\nroughly correspond to the desired overlaps */\nexport function venn(areas, parameters) {\n    parameters = parameters || {};\n    parameters.maxIterations = parameters.maxIterations || 500;\n    var initialLayout = parameters.initialLayout || bestInitialLayout;\n    var loss = parameters.lossFunction || lossFunction;\n\n    // add in missing pairwise areas as having 0 size\n    areas = addMissingAreas(areas);\n\n    // initial layout is done greedily\n    var circles = initialLayout(areas, parameters);\n\n    // transform x/y coordinates to a vector to optimize\n    var initial = [], setids = [], setid;\n    for (setid in circles) {\n        if (circles.hasOwnProperty(setid)) {\n            initial.push(circles[setid].x);\n            initial.push(circles[setid].y);\n            setids.push(setid);\n        }\n    }\n\n    // optimize initial layout from our loss function\n    var totalFunctionCalls = 0;\n    var solution = nelderMead(\n        function(values) {\n            totalFunctionCalls += 1;\n            var current = {};\n            for (var i = 0; i < setids.length; ++i) {\n                var setid = setids[i];\n                current[setid] = {x: values[2 * i],\n                                  y: values[2 * i + 1],\n                                  radius : circles[setid].radius,\n                                 // size : circles[setid].size\n                                 };\n            }\n            return loss(current, areas);\n        },\n        initial,\n        parameters);\n\n    // transform solution vector back to x/y points\n    var positions = solution.x;\n    for (var i = 0; i < setids.length; ++i) {\n        setid = setids[i];\n        circles[setid].x = positions[2 * i];\n        circles[setid].y = positions[2 * i + 1];\n    }\n\n    return circles;\n}\n\nvar SMALL = 1e-10;\n\n/** Returns the distance necessary for two circles of radius r1 + r2 to\nhave the overlap area 'overlap' */\nexport function distanceFromIntersectArea(r1, r2, overlap) {\n    // handle complete overlapped circles\n    if (Math.min(r1, r2) * Math.min(r1,r2) * Math.PI <= overlap + SMALL) {\n        return Math.abs(r1 - r2);\n    }\n\n    return bisect(function(distance) {\n        return circleOverlap(r1, r2, distance) - overlap;\n    }, 0, r1 + r2);\n}\n\n/** Missing pair-wise intersection area data can cause problems:\n treating as an unknown means that sets will be laid out overlapping,\n which isn't what people expect. To reflect that we want disjoint sets\n here, set the overlap to 0 for all missing pairwise set intersections */\nfunction addMissingAreas(areas) {\n    areas = areas.slice();\n\n    // two circle intersections that aren't defined\n    var ids = [], pairs = {}, i, j, a, b;\n    for (i = 0; i < areas.length; ++i) {\n        var area = areas[i];\n        if (area.sets.length == 1) {\n            ids.push(area.sets[0]);\n        } else if (area.sets.length == 2) {\n            a = area.sets[0];\n            b = area.sets[1];\n            pairs[[a, b]] = true;\n            pairs[[b, a]] = true;\n        }\n    }\n    ids.sort(function(a, b) { return a > b; });\n\n    for (i = 0; i < ids.length; ++i) {\n        a = ids[i];\n        for (j = i + 1; j < ids.length; ++j) {\n            b = ids[j];\n            if (!([a, b] in pairs)) {\n                areas.push({'sets': [a, b],\n                            'size': 0});\n            }\n        }\n    }\n    return areas;\n}\n\n/// Returns two matrices, one of the euclidean distances between the sets\n/// and the other indicating if there are subset or disjoint set relationships\nexport function getDistanceMatrices(areas, sets, setids) {\n    // initialize an empty distance matrix between all the points\n    var distances = zerosM(sets.length, sets.length),\n        constraints = zerosM(sets.length, sets.length);\n\n    // compute required distances between all the sets such that\n    // the areas match\n    areas.filter(function(x) { return x.sets.length == 2; })\n        .map(function(current) {\n        var left = setids[current.sets[0]],\n            right = setids[current.sets[1]],\n            r1 = Math.sqrt(sets[left].size / Math.PI),\n            r2 = Math.sqrt(sets[right].size / Math.PI),\n            distance = distanceFromIntersectArea(r1, r2, current.size);\n\n        distances[left][right] = distances[right][left] = distance;\n\n        // also update constraints to indicate if its a subset or disjoint\n        // relationship\n        var c = 0;\n        if (current.size + 1e-10 >= Math.min(sets[left].size,\n                                             sets[right].size)) {\n            c = 1;\n        } else if (current.size <= 1e-10) {\n            c = -1;\n        }\n        constraints[left][right] = constraints[right][left] = c;\n    });\n\n    return {distances: distances, constraints: constraints};\n}\n\n/// computes the gradient and loss simulatenously for our constrained MDS optimizer\nfunction constrainedMDSGradient(x, fxprime, distances, constraints) {\n    var loss = 0, i;\n    for (i = 0; i < fxprime.length; ++i) {\n        fxprime[i] = 0;\n    }\n\n    for (i = 0; i < distances.length; ++i) {\n        var xi = x[2 * i], yi = x[2 * i + 1];\n        for (var j = i + 1; j < distances.length; ++j) {\n            var xj = x[2 * j], yj = x[2 * j + 1],\n                dij = distances[i][j],\n                constraint = constraints[i][j];\n\n            var squaredDistance = (xj - xi) * (xj - xi) + (yj - yi) * (yj - yi),\n                distance = Math.sqrt(squaredDistance),\n                delta = squaredDistance - dij * dij;\n\n            if (((constraint > 0) && (distance <= dij)) ||\n                ((constraint < 0) && (distance >= dij))) {\n                continue;\n            }\n\n            loss += 2 * delta * delta;\n\n            fxprime[2*i]     += 4 * delta * (xi - xj);\n            fxprime[2*i + 1] += 4 * delta * (yi - yj);\n\n            fxprime[2*j]     += 4 * delta * (xj - xi);\n            fxprime[2*j + 1] += 4 * delta * (yj - yi);\n        }\n    }\n    return loss;\n}\n\n/// takes the best working variant of either constrained MDS or greedy\nexport function bestInitialLayout(areas, params) {\n    var initial = greedyLayout(areas, params);\n    var loss = params.lossFunction || lossFunction;\n\n    // greedylayout is sufficient for all 2/3 circle cases. try out\n    // constrained MDS for higher order problems, take its output\n    // if it outperforms. (greedy is aesthetically better on 2/3 circles\n    // since it axis aligns)\n    if (areas.length >= 8) {\n        var constrained  = constrainedMDSLayout(areas, params),\n            constrainedLoss = loss(constrained, areas),\n            greedyLoss = loss(initial, areas);\n\n        if (constrainedLoss + 1e-8 < greedyLoss) {\n            initial = constrained;\n        }\n    }\n    return initial;\n}\n\n/// use the constrained MDS variant to generate an initial layout\nexport function constrainedMDSLayout(areas, params) {\n    params = params || {};\n    var restarts = params.restarts || 10;\n\n    // bidirectionally map sets to a rowid  (so we can create a matrix)\n    var sets = [], setids = {}, i;\n    for (i = 0; i < areas.length; ++i ) {\n        var area = areas[i];\n        if (area.sets.length == 1) {\n            setids[area.sets[0]] = sets.length;\n            sets.push(area);\n        }\n    }\n\n    var matrices = getDistanceMatrices(areas, sets, setids),\n        distances = matrices.distances,\n        constraints = matrices.constraints;\n\n    // keep distances bounded, things get messed up otherwise.\n    // TODO: proper preconditioner?\n    var norm = norm2(distances.map(norm2))/(distances.length);\n    distances = distances.map(function (row) {\n        return row.map(function (value) { return value / norm; });});\n\n    var obj = function(x, fxprime) {\n        return constrainedMDSGradient(x, fxprime, distances, constraints);\n    };\n\n    var best, current;\n    for (i = 0; i < restarts; ++i) {\n        var initial = zeros(distances.length*2).map(Math.random);\n\n        current = conjugateGradient(obj, initial, params);\n        if (!best || (current.fx < best.fx)) {\n            best = current;\n        }\n    }\n    var positions = best.x;\n\n    // translate rows back to (x,y,radius) coordinates\n    var circles = {};\n    for (i = 0; i < sets.length; ++i) {\n        var set = sets[i];\n        circles[set.sets[0]] = {\n            x: positions[2*i] * norm,\n            y: positions[2*i + 1] * norm,\n            radius:  Math.sqrt(set.size / Math.PI)\n        };\n    }\n\n    if (params.history) {\n        for (i = 0; i < params.history.length; ++i) {\n            scale(params.history[i].x, norm);\n        }\n    }\n    return circles;\n}\n\n/** Lays out a Venn diagram greedily, going from most overlapped sets to\nleast overlapped, attempting to position each new set such that the\noverlapping areas to already positioned sets are basically right */\nexport function greedyLayout(areas, params) {\n    var loss = params && params.lossFunction ? params.lossFunction : lossFunction;\n    // define a circle for each set\n    var circles = {}, setOverlaps = {}, set;\n    for (var i = 0; i < areas.length; ++i) {\n        var area = areas[i];\n        if (area.sets.length == 1) {\n            set = area.sets[0];\n            circles[set] = {x: 1e10, y: 1e10,\n                            rowid: circles.length,\n                            size: area.size,\n                            radius: Math.sqrt(area.size / Math.PI)};\n            setOverlaps[set] = [];\n        }\n    }\n    areas = areas.filter(function(a) { return a.sets.length == 2; });\n\n    // map each set to a list of all the other sets that overlap it\n    for (i = 0; i < areas.length; ++i) {\n        var current = areas[i];\n        var weight = current.hasOwnProperty('weight') ? current.weight : 1.0;\n        var left = current.sets[0], right = current.sets[1];\n\n        // completely overlapped circles shouldn't be positioned early here\n        if (current.size + SMALL >= Math.min(circles[left].size,\n                                             circles[right].size)) {\n            weight = 0;\n        }\n\n        setOverlaps[left].push ({set:right, size:current.size, weight:weight});\n        setOverlaps[right].push({set:left,  size:current.size, weight:weight});\n    }\n\n    // get list of most overlapped sets\n    var mostOverlapped = [];\n    for (set in setOverlaps) {\n        if (setOverlaps.hasOwnProperty(set)) {\n            var size = 0;\n            for (i = 0; i < setOverlaps[set].length; ++i) {\n                size += setOverlaps[set][i].size * setOverlaps[set][i].weight;\n            }\n\n            mostOverlapped.push({set: set, size:size});\n        }\n    }\n\n    // sort by size desc\n    function sortOrder(a,b) {\n        return b.size - a.size;\n    }\n    mostOverlapped.sort(sortOrder);\n\n    // keep track of what sets have been laid out\n    var positioned = {};\n    function isPositioned(element) {\n        return element.set in positioned;\n    }\n\n    // adds a point to the output\n    function positionSet(point, index) {\n        circles[index].x = point.x;\n        circles[index].y = point.y;\n        positioned[index] = true;\n    }\n\n    // add most overlapped set at (0,0)\n    positionSet({x: 0, y: 0}, mostOverlapped[0].set);\n\n    // get distances between all points. TODO, necessary?\n    // answer: probably not\n    // var distances = venn.getDistanceMatrices(circles, areas).distances;\n    for (i = 1; i < mostOverlapped.length; ++i) {\n        var setIndex = mostOverlapped[i].set,\n            overlap = setOverlaps[setIndex].filter(isPositioned);\n        set = circles[setIndex];\n        overlap.sort(sortOrder);\n\n        if (overlap.length === 0) {\n            // this shouldn't happen anymore with addMissingAreas\n            throw \"ERROR: missing pairwise overlap information\";\n        }\n\n        var points = [];\n        for (var j = 0; j < overlap.length; ++j) {\n            // get appropriate distance from most overlapped already added set\n            var p1 = circles[overlap[j].set],\n                d1 = distanceFromIntersectArea(set.radius, p1.radius,\n                                               overlap[j].size);\n\n            // sample positions at 90 degrees for maximum aesthetics\n            points.push({x : p1.x + d1, y : p1.y});\n            points.push({x : p1.x - d1, y : p1.y});\n            points.push({y : p1.y + d1, x : p1.x});\n            points.push({y : p1.y - d1, x : p1.x});\n\n            // if we have at least 2 overlaps, then figure out where the\n            // set should be positioned analytically and try those too\n            for (var k = j + 1; k < overlap.length; ++k) {\n                var p2 = circles[overlap[k].set],\n                    d2 = distanceFromIntersectArea(set.radius, p2.radius,\n                                                   overlap[k].size);\n\n                var extraPoints = circleCircleIntersection(\n                    { x: p1.x, y: p1.y, radius: d1},\n                    { x: p2.x, y: p2.y, radius: d2});\n\n                for (var l = 0; l < extraPoints.length; ++l) {\n                    points.push(extraPoints[l]);\n                }\n            }\n        }\n\n        // we have some candidate positions for the set, examine loss\n        // at each position to figure out where to put it at\n        var bestLoss = 1e50, bestPoint = points[0];\n        for (j = 0; j < points.length; ++j) {\n            circles[setIndex].x = points[j].x;\n            circles[setIndex].y = points[j].y;\n            var localLoss = loss(circles, areas);\n            if (localLoss < bestLoss) {\n                bestLoss = localLoss;\n                bestPoint = points[j];\n            }\n        }\n\n        positionSet(bestPoint, setIndex);\n    }\n\n    return circles;\n}\n\n/** Given a bunch of sets, and the desired overlaps between these sets - computes\nthe distance from the actual overlaps to the desired overlaps. Note that\nthis method ignores overlaps of more than 2 circles */\nexport function lossFunction(sets, overlaps) {\n    var output = 0;\n\n    function getCircles(indices) {\n        return indices.map(function(i) { return sets[i]; });\n    }\n\n    for (var i = 0; i < overlaps.length; ++i) {\n        var area = overlaps[i], overlap;\n        if (area.sets.length == 1) {\n            continue;\n        } else if (area.sets.length == 2) {\n            var left = sets[area.sets[0]],\n                right = sets[area.sets[1]];\n            overlap = circleOverlap(left.radius, right.radius,\n                                    distance(left, right));\n        } else {\n            overlap = intersectionArea(getCircles(area.sets));\n        }\n\n        var weight = area.hasOwnProperty('weight') ? area.weight : 1.0;\n        output += weight * (overlap - area.size) * (overlap - area.size);\n    }\n\n    return output;\n}\n\n// orientates a bunch of circles to point in orientation\nfunction orientateCircles(circles, orientation, orientationOrder) {\n    if (orientationOrder === null) {\n        circles.sort(function (a, b) { return b.radius - a.radius; });\n    } else {\n        circles.sort(orientationOrder);\n    }\n\n    var i;\n    // shift circles so largest circle is at (0, 0)\n    if (circles.length > 0) {\n        var largestX = circles[0].x,\n            largestY = circles[0].y;\n\n        for (i = 0; i < circles.length; ++i) {\n            circles[i].x -= largestX;\n            circles[i].y -= largestY;\n        }\n    }\n\n    if (circles.length == 2) {\n        // if the second circle is a subset of the first, arrange so that\n        // it is off to one side. hack for https://github.com/benfred/venn.js/issues/120\n        var dist = distance(circles[0], circles[1]);\n        if (dist < Math.abs(circles[1].radius - circles[0].radius)) {\n            circles[1].x = circles[0].x + circles[0].radius - circles[1].radius - 1e-10;\n            circles[1].y = circles[0].y;\n        }\n    }\n\n    // rotate circles so that second largest is at an angle of 'orientation'\n    // from largest\n    if (circles.length > 1) {\n        var rotation = Math.atan2(circles[1].x, circles[1].y) - orientation,\n            c = Math.cos(rotation),\n            s = Math.sin(rotation), x, y;\n\n        for (i = 0; i < circles.length; ++i) {\n            x = circles[i].x;\n            y = circles[i].y;\n            circles[i].x = c * x - s * y;\n            circles[i].y = s * x + c * y;\n        }\n    }\n\n    // mirror solution if third solution is above plane specified by\n    // first two circles\n    if (circles.length > 2) {\n        var angle = Math.atan2(circles[2].x, circles[2].y) - orientation;\n        while (angle < 0) { angle += 2* Math.PI; }\n        while (angle > 2*Math.PI) { angle -= 2* Math.PI; }\n        if (angle > Math.PI) {\n            var slope = circles[1].y / (1e-10 + circles[1].x);\n            for (i = 0; i < circles.length; ++i) {\n                var d = (circles[i].x + slope * circles[i].y) / (1 + slope*slope);\n                circles[i].x = 2 * d - circles[i].x;\n                circles[i].y = 2 * d * slope - circles[i].y;\n            }\n        }\n    }\n}\n\nexport function disjointCluster(circles) {\n    // union-find clustering to get disjoint sets\n    circles.map(function(circle) { circle.parent = circle; });\n\n    // path compression step in union find\n    function find(circle) {\n        if (circle.parent !== circle) {\n            circle.parent = find(circle.parent);\n        }\n        return circle.parent;\n    }\n\n    function union(x, y) {\n        var xRoot = find(x), yRoot = find(y);\n        xRoot.parent = yRoot;\n    }\n\n    // get the union of all overlapping sets\n    for (var i = 0; i < circles.length; ++i) {\n        for (var j = i + 1; j < circles.length; ++j) {\n            var maxDistance = circles[i].radius + circles[j].radius;\n            if (distance(circles[i], circles[j]) + 1e-10 < maxDistance) {\n                union(circles[j], circles[i]);\n            }\n        }\n    }\n\n    // find all the disjoint clusters and group them together\n    var disjointClusters = {}, setid;\n    for (i = 0; i < circles.length; ++i) {\n        setid = find(circles[i]).parent.setid;\n        if (!(setid in disjointClusters)) {\n            disjointClusters[setid] = [];\n        }\n        disjointClusters[setid].push(circles[i]);\n    }\n\n    // cleanup bookkeeping\n    circles.map(function(circle) { delete circle.parent; });\n\n    // return in more usable form\n    var ret = [];\n    for (setid in disjointClusters) {\n        if (disjointClusters.hasOwnProperty(setid)) {\n            ret.push(disjointClusters[setid]);\n        }\n    }\n    return ret;\n}\n\nfunction getBoundingBox(circles) {\n    var minMax = function(d) {\n        var hi = Math.max.apply(null, circles.map(\n                                function(c) { return c[d] + c.radius; } )),\n            lo = Math.min.apply(null, circles.map(\n                                function(c) { return c[d] - c.radius;} ));\n        return {max:hi, min:lo};\n    };\n\n    return {xRange: minMax('x'), yRange: minMax('y')};\n}\n\nexport function normalizeSolution(solution, orientation, orientationOrder) {\n    if (orientation === null){\n        orientation = Math.PI/2;\n    }\n\n    // work with a list instead of a dictionary, and take a copy so we\n    // don't mutate input\n    var circles = [], i, setid;\n    for (setid in solution) {\n        if (solution.hasOwnProperty(setid)) {\n            var previous = solution[setid];\n            circles.push({x: previous.x,\n                          y: previous.y,\n                          radius: previous.radius,\n                          setid: setid});\n        }\n    }\n\n    // get all the disjoint clusters\n    var clusters = disjointCluster(circles);\n\n    // orientate all disjoint sets, get sizes\n    for (i = 0; i < clusters.length; ++i) {\n        orientateCircles(clusters[i], orientation, orientationOrder);\n        var bounds = getBoundingBox(clusters[i]);\n        clusters[i].size = (bounds.xRange.max - bounds.xRange.min) * (bounds.yRange.max - bounds.yRange.min);\n        clusters[i].bounds = bounds;\n    }\n    clusters.sort(function(a, b) { return b.size - a.size; });\n\n    // orientate the largest at 0,0, and get the bounds\n    circles = clusters[0];\n    var returnBounds = circles.bounds;\n\n    var spacing = (returnBounds.xRange.max - returnBounds.xRange.min)/50;\n\n    function addCluster(cluster, right, bottom) {\n        if (!cluster) return;\n\n        var bounds = cluster.bounds, xOffset, yOffset, centreing;\n\n        if (right) {\n            xOffset = returnBounds.xRange.max  - bounds.xRange.min + spacing;\n        } else {\n            xOffset = returnBounds.xRange.max  - bounds.xRange.max;\n            centreing = (bounds.xRange.max - bounds.xRange.min) / 2 -\n                        (returnBounds.xRange.max - returnBounds.xRange.min) / 2;\n            if (centreing < 0) xOffset += centreing;\n        }\n\n        if (bottom) {\n            yOffset = returnBounds.yRange.max  - bounds.yRange.min + spacing;\n        } else {\n            yOffset = returnBounds.yRange.max  - bounds.yRange.max;\n            centreing = (bounds.yRange.max - bounds.yRange.min) / 2 -\n                        (returnBounds.yRange.max - returnBounds.yRange.min) / 2;\n            if (centreing < 0) yOffset += centreing;\n        }\n\n        for (var j = 0; j < cluster.length; ++j) {\n            cluster[j].x += xOffset;\n            cluster[j].y += yOffset;\n            circles.push(cluster[j]);\n        }\n    }\n\n    var index = 1;\n    while (index < clusters.length) {\n        addCluster(clusters[index], true, false);\n        addCluster(clusters[index+1], false, true);\n        addCluster(clusters[index+2], true, true);\n        index += 3;\n\n        // have one cluster (in top left). lay out next three relative\n        // to it in a grid\n        returnBounds = getBoundingBox(circles);\n    }\n\n    // convert back to solution form\n    var ret = {};\n    for (i = 0; i < circles.length; ++i) {\n        ret[circles[i].setid] = circles[i];\n    }\n    return ret;\n}\n\n/** Scales a solution from venn.venn or venn.greedyLayout such that it fits in\na rectangle of width/height - with padding around the borders. also\ncenters the diagram in the available space at the same time */\nexport function scaleSolution(solution, width, height, padding) {\n    var circles = [], setids = [];\n    for (var setid in solution) {\n        if (solution.hasOwnProperty(setid)) {\n            setids.push(setid);\n            circles.push(solution[setid]);\n        }\n    }\n\n    width -= 2*padding;\n    height -= 2*padding;\n\n    var bounds = getBoundingBox(circles),\n        xRange = bounds.xRange,\n        yRange = bounds.yRange;\n\n    if ((xRange.max == xRange.min) ||\n        (yRange.max == yRange.min)) {\n        console.log(\"not scaling solution: zero size detected\");\n        return solution;\n    }\n\n    var xScaling = width  / (xRange.max - xRange.min),\n        yScaling = height / (yRange.max - yRange.min),\n        scaling = Math.min(yScaling, xScaling),\n\n        // while we're at it, center the diagram too\n        xOffset = (width -  (xRange.max - xRange.min) * scaling) / 2,\n        yOffset = (height - (yRange.max - yRange.min) * scaling) / 2;\n\n    var scaled = {};\n    for (var i = 0; i < circles.length; ++i) {\n        var circle = circles[i];\n        scaled[setids[i]] = {\n            radius: scaling * circle.radius,\n            x: padding + xOffset + (circle.x - xRange.min) * scaling,\n            y: padding + yOffset + (circle.y - yRange.min) * scaling,\n        };\n    }\n\n    return scaled;\n}\n", "/** finds the zeros of a function, given two starting points (which must\n * have opposite signs */\nexport function bisect(f, a, b, parameters) {\n    parameters = parameters || {};\n    var maxIterations = parameters.maxIterations || 100,\n        tolerance = parameters.tolerance || 1e-10,\n        fA = f(a),\n        fB = f(b),\n        delta = b - a;\n\n    if (fA * fB > 0) {\n        throw \"Initial bisect points must have opposite signs\";\n    }\n\n    if (fA === 0) return a;\n    if (fB === 0) return b;\n\n    for (var i = 0; i < maxIterations; ++i) {\n        delta /= 2;\n        var mid = a + delta,\n            fMid = f(mid);\n\n        if (fMid * fA >= 0) {\n            a = mid;\n        }\n\n        if ((Math.abs(delta) < tolerance) || (fMid === 0)) {\n            return mid;\n        }\n    }\n    return a + delta;\n}\n", "import {select, selectAll} from \"d3-selection\";\nimport {transition} from \"d3-transition\";\n\nimport {venn, lossFunction, normalizeSolution, scaleSolution} from \"./layout\";\nimport {intersectionArea, distance, getCenter} from \"./circleintersection\";\nimport {nelderMead} from \"./fmin/index.js\";\n\n/*global console:true*/\n\nexport function VennDiagram() {\n    var width = 600,\n        height = 350,\n        padding = 15,\n        duration = 1000,\n        orientation = Math.PI / 2,\n        normalize = true,\n        wrap = true,\n        styled = true,\n        fontSize = null,\n        orientationOrder = null,\n\n        // mimic the behaviour of d3.scale.category10 from the previous\n        // version of d3\n        colourMap = {},\n\n        // so this is the same as d3.schemeCategory10, which is only defined in d3 4.0\n        // since we can support older versions of d3 as long as we don't force this,\n        // I'm hackily redefining below. TODO: remove this and change to d3.schemeCategory10\n        colourScheme = [\"#1f77b4\", \"#ff7f0e\", \"#2ca02c\", \"#d62728\", \"#9467bd\", \"#8c564b\", \"#e377c2\", \"#7f7f7f\", \"#bcbd22\", \"#17becf\"],\n        colourIndex = 0,\n        colours = function(key) {\n            if (key in colourMap) {\n                return colourMap[key];\n            }\n            var ret = colourMap[key] = colourScheme[colourIndex];\n            colourIndex += 1;\n            if (colourIndex >= colourScheme.length) {\n                colourIndex = 0;\n            }\n            return ret;\n        },\n        layoutFunction = venn,\n        loss = lossFunction;\n\n\n    function chart(selection) {\n        var data = selection.datum();\n\n        // handle 0-sized sets by removing from input\n        var toremove = {};\n        data.forEach(function(datum) {\n            if ((datum.size == 0) && datum.sets.length == 1) {\n                toremove[datum.sets[0]] = 1;\n            }\n        });\n        data = data.filter(function(datum) {\n            return !datum.sets.some(function(set) { return set in toremove; });\n        });\n\n        var circles = {};\n        var textCentres = {};\n\n        if (data.length > 0) {\n            var solution = layoutFunction(data, {lossFunction: loss});\n\n            if (normalize) {\n                solution = normalizeSolution(solution,\n                                            orientation,\n                                            orientationOrder);\n            }\n\n            circles = scaleSolution(solution, width, height, padding);\n            textCentres = computeTextCentres(circles, data);\n        }\n\n        // Figure out the current label for each set. These can change\n        // and D3 won't necessarily update (fixes https://github.com/benfred/venn.js/issues/103)\n        var labels = {};\n        data.forEach(function(datum) {\n            if (datum.label) {\n                labels[datum.sets] = datum.label;\n            }\n        });\n\n        function label(d) {\n            if (d.sets in labels) {\n                return labels[d.sets];\n            }\n            if (d.sets.length == 1) {\n                return '' + d.sets[0];\n            }\n        }\n\n        // create svg if not already existing\n        selection.selectAll(\"svg\").data([circles]).enter().append(\"svg\");\n\n        var svg = selection.select(\"svg\")\n            .attr(\"width\", width)\n            .attr(\"height\", height);\n\n        // to properly transition intersection areas, we need the\n        // previous circles locations. load from elements\n        var previous = {}, hasPrevious = false;\n        svg.selectAll(\".venn-area path\").each(function (d) {\n            var path = select(this).attr(\"d\");\n            if ((d.sets.length == 1) && path) {\n                hasPrevious = true;\n                previous[d.sets[0]] = circleFromPath(path);\n            }\n        });\n\n        // interpolate intersection area paths between previous and\n        // current paths\n        var pathTween = function(d) {\n            return function(t) {\n                var c = d.sets.map(function(set) {\n                    var start = previous[set], end = circles[set];\n                    if (!start) {\n                        start = {x : width/2, y : height/2, radius : 1};\n                    }\n                    if (!end) {\n                        end = {x : width/2, y : height/2, radius : 1};\n                    }\n                    return {'x' : start.x * (1 - t) + end.x * t,\n                            'y' : start.y * (1 - t) + end.y * t,\n                            'radius' : start.radius * (1 - t) + end.radius * t};\n                });\n                return intersectionAreaPath(c);\n            };\n        };\n\n        // update data, joining on the set ids\n        var nodes = svg.selectAll(\".venn-area\")\n            .data(data, function(d) { return d.sets; });\n\n        // create new nodes\n        var enter = nodes.enter()\n            .append('g')\n            .attr(\"class\", function(d) {\n                return \"venn-area venn-\" +\n                    (d.sets.length == 1 ? \"circle\" : \"intersection\");\n            })\n            .attr(\"data-venn-sets\", function(d) {\n                return d.sets.join(\"_\");\n            });\n\n        var enterPath = enter.append(\"path\"),\n            enterText = enter.append(\"text\")\n            .attr(\"class\", \"label\")\n            .text(function (d) { return label(d); } )\n            .attr(\"text-anchor\", \"middle\")\n            .attr(\"dy\", \".35em\")\n            .attr(\"x\", width/2)\n            .attr(\"y\", height/2);\n\n\n        // apply minimal style if wanted\n        if (styled) {\n            enterPath.style(\"fill-opacity\", \"0\")\n                .filter(function (d) { return d.sets.length == 1; } )\n                .style(\"fill\", function(d) { return colours(d.sets); })\n                .style(\"fill-opacity\", \".25\");\n\n            enterText\n                .style(\"fill\", function(d) { return d.sets.length == 1 ? colours(d.sets) : \"#444\"; });\n        }\n\n        // update existing, using pathTween if necessary\n        var update = selection;\n        if (hasPrevious) {\n            update = selection.transition(\"venn\").duration(duration);\n            update.selectAll(\"path\")\n                .attrTween(\"d\", pathTween);\n        } else {\n            update.selectAll(\"path\")\n                .attr(\"d\", function(d) {\n                    return intersectionAreaPath(d.sets.map(function (set) { return circles[set]; }));\n                });\n        }\n\n        var updateText = update.selectAll(\"text\")\n            .filter(function (d) { return d.sets in textCentres; })\n            .text(function (d) { return label(d); } )\n            .attr(\"x\", function(d) { return Math.floor(textCentres[d.sets].x);})\n            .attr(\"y\", function(d) { return Math.floor(textCentres[d.sets].y);});\n\n        if (wrap) {\n            if (hasPrevious) {\n                // d3 4.0 uses 'on' for events on transitions,\n                // but d3 3.0 used 'each' instead. switch appropiately\n                if ('on' in updateText) {\n                    updateText.on(\"end\", wrapText(circles, label));\n                } else {\n                    updateText.each(\"end\", wrapText(circles, label));\n                }\n            } else {\n                updateText.each(wrapText(circles, label));\n            }\n        }\n\n        // remove old\n        var exit = nodes.exit().transition('venn').duration(duration).remove();\n        exit.selectAll(\"path\")\n            .attrTween(\"d\", pathTween);\n\n        var exitText = exit.selectAll(\"text\")\n            .attr(\"x\", width/2)\n            .attr(\"y\", height/2);\n\n        // if we've been passed a fontSize explicitly, use it to\n        // transition\n        if (fontSize !== null) {\n            enterText.style(\"font-size\", \"0px\");\n            updateText.style(\"font-size\", fontSize);\n            exitText.style(\"font-size\", \"0px\");\n        }\n\n\n        return {'circles': circles,\n                'textCentres': textCentres,\n                'nodes': nodes,\n                'enter': enter,\n                'update': update,\n                'exit': exit};\n    }\n\n    chart.wrap = function(_) {\n        if (!arguments.length) return wrap;\n        wrap = _;\n        return chart;\n    };\n\n    chart.width = function(_) {\n        if (!arguments.length) return width;\n        width = _;\n        return chart;\n    };\n\n    chart.height = function(_) {\n        if (!arguments.length) return height;\n        height = _;\n        return chart;\n    };\n\n    chart.padding = function(_) {\n        if (!arguments.length) return padding;\n        padding = _;\n        return chart;\n    };\n\n    chart.colours = function(_) {\n        if (!arguments.length) return colours;\n        colours = _;\n        return chart;\n    };\n\n    chart.fontSize = function(_) {\n        if (!arguments.length) return fontSize;\n        fontSize = _;\n        return chart;\n    };\n\n    chart.duration = function(_) {\n        if (!arguments.length) return duration;\n        duration = _;\n        return chart;\n    };\n\n    chart.layoutFunction = function(_) {\n        if (!arguments.length) return layoutFunction;\n        layoutFunction = _;\n        return chart;\n    };\n\n    chart.normalize = function(_) {\n        if (!arguments.length) return normalize;\n        normalize = _;\n        return chart;\n    };\n\n    chart.styled = function(_) {\n        if (!arguments.length) return styled;\n        styled = _;\n        return chart;\n    };\n\n    chart.orientation = function(_) {\n        if (!arguments.length) return orientation;\n        orientation = _;\n        return chart;\n    };\n\n    chart.orientationOrder = function(_) {\n        if (!arguments.length) return orientationOrder;\n        orientationOrder = _;\n        return chart;\n    };\n\n    chart.lossFunction = function(_) {\n      if (!arguments.length) return loss;\n      loss = _;\n      return chart;\n    };\n\n    return chart;\n}\n// sometimes text doesn't fit inside the circle, if thats the case lets wrap\n// the text here such that it fits\n// todo: looks like this might be merged into d3 (\n// https://github.com/mbostock/d3/issues/1642),\n// also worth checking out is\n// http://engineering.findthebest.com/wrapping-axis-labels-in-d3-js/\n// this seems to be one of those things that should be easy but isn't\nexport function wrapText(circles, labeller) {\n    return function() {\n        var text = select(this),\n            data = text.datum(),\n            width = circles[data.sets[0]].radius || 50,\n            label = labeller(data) || '';\n\n            var words = label.split(/\\s+/).reverse(),\n            maxLines = 3,\n            minChars = (label.length + words.length) / maxLines,\n            word = words.pop(),\n            line = [word],\n            joined,\n            lineNumber = 0,\n            lineHeight = 1.1, // ems\n            tspan = text.text(null).append(\"tspan\").text(word);\n\n        while (true) {\n            word = words.pop();\n            if (!word) break;\n            line.push(word);\n            joined = line.join(\" \");\n            tspan.text(joined);\n            if (joined.length > minChars && tspan.node().getComputedTextLength() > width) {\n                line.pop();\n                tspan.text(line.join(\" \"));\n                line = [word];\n                tspan = text.append(\"tspan\").text(word);\n                lineNumber++;\n            }\n        }\n\n        var initial = 0.35 - lineNumber * lineHeight / 2,\n            x = text.attr(\"x\"),\n            y = text.attr(\"y\");\n\n        text.selectAll(\"tspan\")\n            .attr(\"x\", x)\n            .attr(\"y\", y)\n            .attr(\"dy\", function(d, i) {\n                 return (initial + i * lineHeight) + \"em\";\n            });\n    };\n}\n\nfunction circleMargin(current, interior, exterior) {\n    var margin = interior[0].radius - distance(interior[0], current), i, m;\n    for (i = 1; i < interior.length; ++i) {\n        m = interior[i].radius - distance(interior[i], current);\n        if (m <= margin) {\n            margin = m;\n        }\n    }\n\n    for (i = 0; i < exterior.length; ++i) {\n        m = distance(exterior[i], current) - exterior[i].radius;\n        if (m <= margin) {\n            margin = m;\n        }\n    }\n    return margin;\n}\n\n// compute the center of some circles by maximizing the margin of\n// the center point relative to the circles (interior) after subtracting\n// nearby circles (exterior)\nexport function computeTextCentre(interior, exterior) {\n    // get an initial estimate by sampling around the interior circles\n    // and taking the point with the biggest margin\n    var points = [], i;\n    for (i = 0; i < interior.length; ++i) {\n        var c = interior[i];\n        points.push({x: c.x, y: c.y});\n        points.push({x: c.x + c.radius/2, y: c.y});\n        points.push({x: c.x - c.radius/2, y: c.y});\n        points.push({x: c.x, y: c.y + c.radius/2});\n        points.push({x: c.x, y: c.y - c.radius/2});\n    }\n    var initial = points[0], margin = circleMargin(points[0], interior, exterior);\n    for (i = 1; i < points.length; ++i) {\n        var m = circleMargin(points[i], interior, exterior);\n        if (m >= margin) {\n            initial = points[i];\n            margin = m;\n        }\n    }\n\n    // maximize the margin numerically\n    var solution = nelderMead(\n                function(p) { return -1 * circleMargin({x: p[0], y: p[1]}, interior, exterior); },\n                [initial.x, initial.y],\n                {maxIterations:500, minErrorDelta:1e-10}).x;\n    var ret = {x: solution[0], y: solution[1]};\n\n    // check solution, fallback as needed (happens if fully overlapped\n    // etc)\n    var valid = true;\n    for (i = 0; i < interior.length; ++i) {\n        if (distance(ret, interior[i]) > interior[i].radius) {\n            valid = false;\n            break;\n        }\n    }\n\n    for (i = 0; i < exterior.length; ++i) {\n        if (distance(ret, exterior[i]) < exterior[i].radius) {\n            valid = false;\n            break;\n        }\n    }\n\n    if (!valid) {\n        if (interior.length == 1) {\n            ret = {x: interior[0].x, y: interior[0].y};\n        } else {\n            var areaStats = {};\n            intersectionArea(interior, areaStats);\n\n            if (areaStats.arcs.length === 0) {\n                ret = {'x': 0, 'y': -1000, disjoint:true};\n\n            } else if (areaStats.arcs.length == 1) {\n                ret = {'x': areaStats.arcs[0].circle.x,\n                       'y': areaStats.arcs[0].circle.y};\n\n            } else if (exterior.length) {\n                // try again without other circles\n                ret = computeTextCentre(interior, []);\n\n            } else {\n                // take average of all the points in the intersection\n                // polygon. this should basically never happen\n                // and has some issues:\n                // https://github.com/benfred/venn.js/issues/48#issuecomment-146069777\n                ret = getCenter(areaStats.arcs.map(function (a) { return a.p1; }));\n            }\n        }\n    }\n\n    return ret;\n}\n\n// given a dictionary of {setid : circle}, returns\n// a dictionary of setid to list of circles that completely overlap it\nfunction getOverlappingCircles(circles) {\n    var ret = {}, circleids = [];\n    for (var circleid in circles) {\n        circleids.push(circleid);\n        ret[circleid] = [];\n    }\n    for (var i  = 0; i < circleids.length; i++) {\n        var a = circles[circleids[i]];\n        for (var j = i + 1; j < circleids.length; ++j) {\n            var b = circles[circleids[j]],\n                d = distance(a, b);\n\n            if (d + b.radius <= a.radius + 1e-10) {\n                ret[circleids[j]].push(circleids[i]);\n\n            } else if (d + a.radius <= b.radius + 1e-10) {\n                ret[circleids[i]].push(circleids[j]);\n            }\n        }\n    }\n    return ret;\n}\n\nexport function computeTextCentres(circles, areas) {\n    var ret = {}, overlapped = getOverlappingCircles(circles);\n    for (var i = 0; i < areas.length; ++i) {\n        var area = areas[i].sets, areaids = {}, exclude = {};\n        for (var j = 0; j < area.length; ++j) {\n            areaids[area[j]] = true;\n            var overlaps = overlapped[area[j]];\n            // keep track of any circles that overlap this area,\n            // and don't consider for purposes of computing the text\n            // centre\n            for (var k = 0; k < overlaps.length; ++k) {\n                exclude[overlaps[k]] = true;\n            }\n        }\n\n        var interior = [], exterior = [];\n        for (var setid in circles) {\n            if (setid in areaids) {\n                interior.push(circles[setid]);\n            } else if (!(setid in exclude)) {\n                exterior.push(circles[setid]);\n            }\n        }\n        var centre = computeTextCentre(interior, exterior);\n        ret[area] = centre;\n        if (centre.disjoint && (areas[i].size > 0)) {\n            console.log(\"WARNING: area \" + area + \" not represented on screen\");\n        }\n    }\n    return  ret;\n}\n\n// sorts all areas in the venn diagram, so that\n// a particular area is on top (relativeTo) - and\n// all other areas are so that the smallest areas are on top\nexport function sortAreas(div, relativeTo) {\n\n    // figure out sets that are completly overlapped by relativeTo\n    var overlaps = getOverlappingCircles(div.selectAll(\"svg\").datum());\n    var exclude = {};\n    for (var i = 0; i < relativeTo.sets.length; ++i) {\n        var check = relativeTo.sets[i];\n        for (var setid in overlaps) {\n            var overlap = overlaps[setid];\n            for (var j = 0; j < overlap.length; ++j) {\n                if (overlap[j] == check) {\n                    exclude[setid] = true;\n                    break;\n                }\n            }\n        }\n    }\n\n    // checks that all sets are in exclude;\n    function shouldExclude(sets) {\n        for (var i = 0; i < sets.length; ++i) {\n            if (!(sets[i] in exclude)) {\n                return false;\n            }\n        }\n        return true;\n    }\n\n    // need to sort div's so that Z order is correct\n    div.selectAll(\"g\").sort(function (a, b) {\n        // highest order set intersections first\n        if (a.sets.length != b.sets.length) {\n            return a.sets.length - b.sets.length;\n        }\n\n        if (a == relativeTo) {\n            return shouldExclude(b.sets) ? -1 : 1;\n        }\n        if (b == relativeTo) {\n            return shouldExclude(a.sets) ? 1 : -1;\n        }\n\n        // finally by size\n        return b.size - a.size;\n    });\n}\n\nexport function circlePath(x, y, r) {\n    var ret = [];\n    ret.push(\"\\nM\", x, y);\n    ret.push(\"\\nm\", -r, 0);\n    ret.push(\"\\na\", r, r, 0, 1, 0, r *2, 0);\n    ret.push(\"\\na\", r, r, 0, 1, 0,-r *2, 0);\n    return ret.join(\" \");\n}\n\n// inverse of the circlePath function, returns a circle object from an svg path\nexport function circleFromPath(path) {\n    var tokens = path.split(' ');\n    return {'x' : parseFloat(tokens[1]),\n            'y' : parseFloat(tokens[2]),\n            'radius' : -parseFloat(tokens[4])\n            };\n}\n\n/** returns a svg path of the intersection area of a bunch of circles */\nexport function intersectionAreaPath(circles) {\n    var stats = {};\n    intersectionArea(circles, stats);\n    var arcs = stats.arcs;\n\n    if (arcs.length === 0) {\n        return \"M 0 0\";\n\n    } else if (arcs.length == 1) {\n        var circle = arcs[0].circle;\n        return circlePath(circle.x, circle.y, circle.radius);\n\n    } else {\n        // draw path around arcs\n        var ret = [\"\\nM\", arcs[0].p2.x, arcs[0].p2.y];\n        for (var i = 0; i < arcs.length; ++i) {\n            var arc = arcs[i], r = arc.circle.radius, wide = arc.width > r;\n            ret.push(\"\\nA\", r, r, 0, wide ? 1 : 0, 1,\n                     arc.p1.x, arc.p1.y);\n        }\n        return ret.join(\" \");\n    }\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { ILegendDataItem } from \"../../core/render/Legend\";\nimport type { Color } from \"../../core/util/Color\";\nimport type { ColorSet } from \"../../core/util/ColorSet\";\nimport type { Pattern } from \"../../core/render/patterns/Pattern\";\nimport type { PatternSet } from \"../../core/util/PatternSet\";\n\nimport { VennDefaultTheme } from \"./VennDefaultTheme\";\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate } from \"../../core/render/Series\";\nimport { Template } from \"../../core/util/Template\";\nimport { Graphics, visualSettings } from \"../../core/render/Graphics\";\nimport { Container } from \"../../core/render/Container\";\nimport { Label } from \"../../core/render/Label\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as venn from \"./vennjs/index.js\";\n\nexport interface IVennDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Array of categories that this data item is an intersection for.\n\t */\n\tintersections: Array<string>;\n\n\t/**\n\t * Category.\n\t */\n\tcategory: string;\n\n\t/**\n\t * Slice visaul element.\n\t */\n\tslice: Graphics;\n\n\t/**\n\t * Slice label.\n\t */\n\tlabel: Label;\n\n\t/**\n\t * A related legend data item.\n\t */\n\tlegendDataItem: DataItem<ILegendDataItem>;\n\n\t/**\n\t * Fill color used for the slice and related elements, e.g. legend marker.\n\t */\n\tfill: Color;\n\n\t/**\n\t * Fill pattern used for the slice and related elements, e.g. legend marker.\n\t * \n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/} for more info\n\t * @since 5.10.0\n\t */\n\tfillPattern: Pattern;\n\n}\n\nexport interface IVennSettings extends ISeriesSettings {\n\n\t/**\n\t * A field in data that holds array of categories that overlap.\n\t */\n\tintersectionsField?: string;\n\n\t/**\n\t * A [[ColorSet]] to use when asigning colors for slices.\n\t */\n\tcolors?: ColorSet;\n\n\t/**\n\t * A [[PatternSet]] to use when asigning patterns for slices.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/#Pattern_sets} for more info\n\t * @since 5.10.0\n\t */\n\tpatterns?: PatternSet;\n\n\t/**\n\t * A field in data that holds category names.\n\t */\n\tcategoryField?: string;\n\n\t/**\n\t * A field that holds color for slice fill.\n\t */\n\tfillField?: string;\n\n}\n\nexport interface IVennPrivate extends ISeriesPrivate {\n}\n\n/**\n * Creates a Venn diagram.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/venn/} for more info\n * @important\n */\nexport class Venn extends Series {\n\n\tpublic static className: string = \"Venn\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([Venn.className]);\n\n\tdeclare public _settings: IVennSettings;\n\tdeclare public _privateSettings: IVennPrivate;\n\tdeclare public _dataItemSettings: IVennDataItem;\n\n\tprotected _sets: string = \"\";\n\n\t/**\n\t * A [[Container]] that holds all slices (circles and intersections).\n\t *\n\t * @default Container.new()\n\t */\n\tpublic readonly slicesContainer = this.children.push(Container.new(this._root, {}));\n\n\t/**\n\t * A [[Container]] that holds all labels.\n\t *\n\t * @default Container.new()\n\t */\n\tpublic readonly labelsContainer = this.children.push(Container.new(this._root, {}));\n\n\t/**\n\t * A [[Graphics]] element that is used to show the shape of the hovered slice\n\t * or intersection.\n\t *\n\t * @default Graphics.new()\n\t */\n\tpublic readonly hoverGraphics = this.slicesContainer.children.push(Graphics.new(this._root, { position: \"absolute\", isMeasured: false }))\n\n\tprotected _hovered?: Graphics;\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(VennDefaultTheme.new(this._root));\n\t\tthis.fields.push(\"intersections\", \"category\", \"fill\");\n\t\tsuper._afterNew();\n\t}\n\n\n\t/**\n\t * A [[ListTemplate]] of all slices in series.\n\t *\n\t * `slices.template` can also be used to configure slices.\n\t */\n\tpublic readonly slices: ListTemplate<Graphics> = this._makeSlices();\n\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeSlice(dataItem: DataItem<this[\"_dataItemSettings\"]>): Graphics {\n\t\tconst slice = this.slicesContainer.children.push(this.slices.make());\n\t\tslice.events.on(\"pointerover\", (e) => {\n\t\t\tthis._hovered = e.target;\n\t\t\tthis._updateHover();\n\t\t})\n\n\t\tslice.events.on(\"pointerout\", () => {\n\t\t\tthis._hovered = undefined;\n\t\t\tthis.hoverGraphics.hide();\n\t\t})\n\n\t\tslice.on(\"fill\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\n\n\t\tslice.on(\"fillPattern\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\n\n\t\tslice.on(\"stroke\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\n\n\t\tslice._setDataItem(dataItem);\n\t\tdataItem.set(\"slice\", slice);\n\t\tthis.slices.push(slice);\n\n\t\treturn slice;\n\t}\n\n\tprotected _updateHover() {\n\t\tif (this._hovered) {\n\t\t\tconst hoverGraphics = this.hoverGraphics;\n\t\t\thoverGraphics.set(\"svgPath\", this._hovered.get(\"svgPath\"));\n\t\t\thoverGraphics.show();\n\t\t\thoverGraphics.toFront();\n\t\t}\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all slice labels in series.\n\t *\n\t * `labels.template` can also be used to configure slice labels.\n\t */\n\tpublic readonly labels: ListTemplate<Label> = this._makeLabels();\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLabel(dataItem: DataItem<this[\"_dataItemSettings\"]>): Label {\n\t\tconst label = this.labelsContainer.children.push(this.labels.make());\n\t\tlabel._setDataItem(dataItem);\n\t\tdataItem.set(\"label\", label);\n\t\tthis.labels.push(label);\n\t\treturn label;\n\t}\n\n\n\tprotected _makeSlices(): ListTemplate<Graphics> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Graphics._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.slices.template.get(\"themeTags\", []), [\"venn\", \"series\"])\n\t\t\t}, [this.slices.template]),\n\t\t);\n\t}\n\n\tprotected _makeLabels(): ListTemplate<Label> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Label._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [\"venn\", \"series\"])\n\t\t\t}, [this.labels.template]),\n\t\t);\n\t}\n\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\n\t\tif (dataItem.get(\"fill\") == null) {\n\t\t\tlet colors = this.get(\"colors\");\n\t\t\tif (colors) {\n\t\t\t\tdataItem.setRaw(\"fill\", colors.next());\n\t\t\t}\n\t\t}\n\n\t\tif (dataItem.get(\"fillPattern\") == null) {\n\t\t\tlet patterns = this.get(\"patterns\");\n\t\t\tif (patterns) {\n\t\t\t\tdataItem.setRaw(\"fillPattern\", patterns.next());\n\t\t\t}\n\t\t}\n\n\t\tthis.makeSlice(dataItem);\n\t\tthis.makeLabel(dataItem);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this._valuesDirty || this._sizeDirty) {\n\t\t\tconst sets: any[] = [];\n\n\t\t\t// prepare data for venn\n\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\tconst set: any = {};\n\t\t\t\tconst intersections = dataItem.get(\"intersections\");\n\t\t\t\tif (intersections) {\n\t\t\t\t\tset.sets = intersections;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tset.sets = [dataItem.get(\"category\")];\n\t\t\t\t}\n\t\t\t\tset.size = dataItem.get(\"valueWorking\");\n\n\t\t\t\t//if (set.size > 0) { // not good\n\t\t\t\tsets.push(set);\n\t\t\t\t//}\n\n\t\t\t\tconst label = dataItem.get(\"label\");\n\t\t\t\tconst slice = dataItem.get(\"slice\");\n\n\n\t\t\t\tlet visible = true;\n\t\t\t\tif (dataItem.get(\"value\") == 0) {\n\t\t\t\t\tvisible = false;\n\n\t\t\t\t\tif (slice) {\n\t\t\t\t\t\tslice.setAll({\n\t\t\t\t\t\t\tx: this.width() / 2,\n\t\t\t\t\t\t\ty: this.height() / 2\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (label) {\n\t\t\t\t\tlabel.setPrivate(\"visible\", visible);\n\t\t\t\t}\n\t\t\t})\n\n\n\t\t\tconst newSets = sets.toString();\n\n\t\t\tthis._sets = newSets;\n\n\t\t\tif (sets.length > 0) {\n\t\t\t\tlet vennData = venn.venn(sets);\n\t\t\t\tvennData = venn.normalizeSolution(vennData, null, null);\n\t\t\t\tvennData = venn.scaleSolution(vennData, this.innerWidth(), this.innerHeight(), 0);\n\n\t\t\t\tconst circles: any = {};\n\t\t\t\tfor (let name in vennData) {\n\t\t\t\t\tlet item = vennData[name];\n\t\t\t\t\tlet r = item.radius;\n\n\t\t\t\t\tconst dataItem = this.getDataItemByCategory(name);\n\t\t\t\t\tif (dataItem) {\n\t\t\t\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\t\t\t\tconst color = dataItem.get(\"fill\");\n\t\t\t\t\t\tslice._setDefault(\"fill\", color);\n\n\t\t\t\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\t\t\t\t\t\tslice._setDefault(\"fillPattern\", fillPattern);\n\n\t\t\t\t\t\tslice._setDefault(\"stroke\", color);\n\n\t\t\t\t\t\tthis.updateLegendMarker(dataItem);\n\n\t\t\t\t\t\tslice.set(\"svgPath\", \"M\" + item.x + \",\" + item.y + \" m -\" + r + \", 0 a \" + r + \",\" + r + \" 0 1,1 \" + r * 2 + \",0 a \" + r + \",\" + r + \" 0 1,1 -\" + r * 2 + \",0\");\n\t\t\t\t\t\tcircles[name] = item;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\n\t\t\t\tlet centers: any = venn.computeTextCentres(circles, sets);\n\n\n\t\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\t\tlet name = dataItem.get(\"category\");\n\t\t\t\t\tlet center = centers[name];\n\t\t\t\t\tconst intersections = dataItem.get(\"intersections\");\n\t\t\t\t\tif (intersections) {\n\t\t\t\t\t\tname = intersections.toString();\n\t\t\t\t\t\tcenter = centers[name];\n\t\t\t\t\t\tif (center) {\n\t\t\t\t\t\t\tlet set = intersections;\n\t\t\t\t\t\t\tlet cc = [];\n\n\t\t\t\t\t\t\tfor (let s = 0; s < set.length; s++) {\n\t\t\t\t\t\t\t\tcc.push(circles[set[s]]);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlet intersectionPath = venn.intersectionAreaPath(cc)\n\t\t\t\t\t\t\tlet slice = dataItem.get(\"slice\");\n\n\t\t\t\t\t\t\tconst color = dataItem.get(\"fill\");\n\t\t\t\t\t\t\tslice._setDefault(\"fill\", color);\n\t\t\t\t\t\t\tslice._setDefault(\"stroke\", color);\n\n\t\t\t\t\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\t\t\t\t\t\t\tslice._setDefault(\"fillPattern\", fillPattern);\n\n\t\t\t\t\t\t\tslice.setAll({ svgPath: intersectionPath });\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tif (center) {\n\t\t\t\t\t\tlet label = dataItem.get(\"label\");\n\t\t\t\t\t\tlabel.setAll({ x: center.x, y: center.y });\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.updateLegendValue(dataItem);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tthis._updateHover();\n\t\t}\n\t}\n\n\t/**\n\t * Looks up and returns a data item by its category.\n\t *\n\t * @param   category  Category\n\t * @return      Data item\n\t */\n\tpublic getDataItemByCategory(id: string): DataItem<this[\"_dataItemSettings\"]> | undefined {\n\t\treturn $array.find(this.dataItems, (dataItem: any) => {\n\t\t\treturn dataItem.get(\"category\") == id;\n\t\t})\n\t}\n\n\n\t/**\n\t * Shows series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = this.get(\"stateAnimationDuration\", 0);\n\t\t}\n\n\t\tconst easing = this.get(\"stateAnimationEasing\");\n\n\t\tlet value = dataItem.get(\"value\");\n\n\t\tconst animation = dataItem.animate({ key: \"valueWorking\", to: value, duration: duration, easing: easing });\n\t\tif (animation) {\n\t\t\tpromises.push(animation.waitForStop());\n\t\t}\n\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tpromises.push(label.show(duration));\n\t\t}\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tpromises.push(slice.show(duration));\n\t\t}\n\n\n\t\tconst intersections = dataItem.get(\"intersections\");\n\t\tif (intersections) {\n\t\t\t$array.each(intersections, (cat) => {\n\t\t\t\tconst di = this.getDataItemByCategory(cat);\n\t\t\t\tif (di && di.isHidden()) {\n\t\t\t\t\tthis.showDataItem(di, duration);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tif (!intersections) {\n\t\t\tconst category = dataItem.get(\"category\");\n\n\t\t\t$array.each(this.dataItems, (di) => {\n\t\t\t\tconst intersections = di.get(\"intersections\");\n\t\t\t\tif (di != dataItem && intersections) {\n\t\t\t\t\tlet allVisible = true;\n\t\t\t\t\t$array.each(intersections, (cat) => {\n\t\t\t\t\t\tconst dii = this.getDataItemByCategory(cat);\n\t\t\t\t\t\tif (dii && dii.isHidden()) {\n\t\t\t\t\t\t\tallVisible = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tif (allVisible && intersections.indexOf(category) != -1) {\n\t\t\t\t\t\tif (di.isHidden()) {\n\t\t\t\t\t\t\tthis.showDataItem(di, duration);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Hides series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\t\tconst hiddenState = this.states.create(\"hidden\", {})\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = hiddenState.get(\"stateAnimationDuration\", this.get(\"stateAnimationDuration\", 0));\n\t\t}\n\n\t\tconst easing = hiddenState.get(\"stateAnimationEasing\", this.get(\"stateAnimationEasing\"));\n\n\t\tconst animation = dataItem.animate({ key: \"valueWorking\", to: 0, duration: duration, easing: easing });\n\t\tif (animation) {\n\t\t\tpromises.push(animation.waitForStop());\n\t\t}\n\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tpromises.push(label.hide(duration));\n\t\t}\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tpromises.push(slice.hide(duration));\n\t\t\tslice.hideTooltip();\n\t\t}\n\n\t\tif (!dataItem.get(\"intersections\")) {\n\t\t\t$array.each(this.dataItems, (di) => {\n\t\t\t\tconst intersections = di.get(\"intersections\");\n\t\t\t\tif (di != dataItem && intersections) {\n\t\t\t\t\tif (intersections.indexOf(dataItem.get(\"category\")) != -1) {\n\t\t\t\t\t\tthis.hideDataItem(di, duration);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tthis.labels.removeValue(label);\n\t\t\tlabel.dispose();\n\t\t}\n\n\t\tlet slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tthis.slices.removeValue(slice);\n\t\t\tslice.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateLegendMarker(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\n\t\tif (slice) {\n\t\t\tconst legendDataItem = dataItem.get(\"legendDataItem\");\n\t\t\tif (legendDataItem) {\n\t\t\t\tconst markerRectangle = legendDataItem.get(\"markerRectangle\");\n\t\t\t\tif (!dataItem.isHidden()) {\n\t\t\t\t\t$array.each(visualSettings, (setting: any) => {\n\t\t\t\t\t\tmarkerRectangle.set(setting, slice.get(setting));\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Triggers hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic hoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice && !slice.isHidden()) {\n\t\t\tslice.hover();\n\t\t}\n\t}\n\n\t/**\n\t * Triggers un-hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic unhoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tslice.unhover();\n\t\t}\n\t}\n}\n", "import * as m from \"./../../dist/es2015/venn.js\";\nexport const am5venn = m;"], "names": ["VennDefaultTheme", "Theme", "setupDefaultRules", "super", "r", "this", "rule", "bind", "setAll", "legendLabelText", "legendValueText", "colors", "ColorSet", "new", "_root", "width", "height", "text", "populateText", "centerX", "centerY", "zeros", "x", "Array", "i", "zerosM", "y", "map", "dot", "a", "b", "ret", "length", "norm2", "Math", "sqrt", "scale", "value", "c", "weightedSum", "w1", "v1", "w2", "v2", "j", "nelderMead", "f", "x0", "parameters", "maxDiff", "maxIterations", "nonZeroDelta", "zeroDelta", "minE<PERSON><PERSON><PERSON><PERSON><PERSON>", "minTolerance", "rho", "undefined", "chi", "psi", "sigma", "N", "simplex", "fx", "id", "point", "slice", "updateSimplex", "sortOrder", "centroid", "reflected", "contracted", "expanded", "iteration", "sort", "history", "sortedSimplex", "state", "push", "max", "abs", "worst", "shouldReduce", "wolfeLineSearch", "pk", "current", "next", "c1", "c2", "phi0", "phiPrime0", "fxprime", "phi", "phi_old", "phiPrime", "a0", "zoom", "a_lo", "a_high", "phi_lo", "conjugateGradient", "initial", "params", "temp", "yk", "alpha", "delta_k", "SMALL", "intersectionArea", "circles", "stats", "intersectionPoints", "intersect", "circleCircleIntersection", "k", "p", "parentIndex", "getIntersectionPoints", "innerPoints", "filter", "distance", "radius", "containedInCircles", "arcArea", "polygonArea", "arcs", "center", "getCenter", "angle", "atan2", "p2", "p1", "midPoint", "arc", "indexOf", "circle", "a1", "a2", "angleDiff", "PI", "sin", "cos", "circleArea", "smallest", "disjoint", "area", "acos", "circleOverlap", "r1", "r2", "d", "min", "h", "y0", "rx", "ry", "points", "distanceFromIntersectArea", "overlap", "tolerance", "fA", "fB", "delta", "mid", "fMid", "bisect", "bestInitialLayout", "areas", "set", "loss", "lossFunction", "setOverlaps", "sets", "rowid", "size", "weight", "hasOwnProperty", "left", "right", "mostOverlapped", "positioned", "isPositioned", "element", "positionSet", "index", "setIndex", "d1", "d2", "extraPoints", "l", "bestLoss", "bestPoint", "localLoss", "greedyLayout", "constrained", "restarts", "setids", "matrices", "distances", "constraints", "getDistanceMatrices", "norm", "row", "best", "obj", "xi", "yi", "xj", "yj", "dij", "constraint", "squaredDistance", "constrainedMDSGradient", "random", "positions", "constrainedMDSLayout", "overlaps", "output", "orientateCircles", "orientation", "orientationOrder", "largestX", "largestY", "rotation", "s", "slope", "getBoundingBox", "minMax", "apply", "xRange", "y<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "interior", "exterior", "m", "margin", "computeTextCentre", "solution", "valid", "areaStats", "computeTextCentres", "overlapped", "circleids", "circleid", "getOverlappingCircles", "areaids", "exclude", "setid", "centre", "console", "log", "<PERSON><PERSON>n", "Series", "children", "Container", "slicesContainer", "Graphics", "position", "isMeasured", "_makeSlices", "_make<PERSON><PERSON><PERSON>", "_afterNew", "_defaultThemes", "fields", "makeSlice", "dataItem", "slices", "make", "events", "on", "e", "_hovered", "target", "_updateHover", "hoverGraphics", "hide", "updateLegendMarker", "_setDataItem", "get", "show", "toFront", "<PERSON><PERSON><PERSON><PERSON>", "label", "labelsContainer", "labels", "List", "Template", "_new", "themeTags", "template", "Label", "processDataItem", "setRaw", "patterns", "_prepare<PERSON><PERSON><PERSON><PERSON>", "_valuesDirty", "_sizeDirty", "dataItems", "intersections", "visible", "setPrivate", "newSets", "toString", "_sets", "vennData", "initialLayout", "ids", "pairs", "addMissingAreas", "values", "previous", "clusters", "find", "parent", "maxDistance", "xRoot", "yRoot", "disjointClusters", "disjointCluster", "bounds", "returnBounds", "spacing", "addCluster", "cluster", "bottom", "xOffset", "yOffset", "centreing", "padding", "xScaling", "yScaling", "scaling", "scaled", "innerWidth", "innerHeight", "name", "item", "getDataItemByCategory", "color", "_setDefault", "fillPattern", "centers", "cc", "intersectionPath", "join", "circlePath", "wide", "svgPath", "updateLegendValue", "showDataItem", "duration", "promises", "easing", "animation", "animate", "key", "to", "waitForStop", "cat", "di", "isHidden", "category", "allVisible", "dii", "Promise", "all", "hideDataItem", "hiddenState", "states", "create", "hideTooltip", "disposeDataItem", "removeValue", "dispose", "legendDataItem", "markerRectangle", "setting", "hoverDataItem", "hover", "unhoverDataItem", "unhover", "classNames", "concat", "className", "am5venn"], "sourceRoot": ""}