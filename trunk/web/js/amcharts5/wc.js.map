{"version": 3, "file": "wc.js", "mappings": "+NAUO,MAAMA,UAA8BC,EAAA,EAChC,iBAAAC,GACTC,MAAMD,oBAEN,MAAME,EAAKC,KAAKC,MAAMC,gBAChBC,EAAIH,KAAKI,KAAKC,KAAKL,MAEzBG,EAAE,aAAaG,OAAO,CACrBC,MAAO,KACPC,OAAQ,KACRC,aAAa,QAAQ,GACrBC,aAAa,QAAQ,IACrBC,aAAc,GACdC,OAAQ,CAAC,GAAI,IACbC,cAAe,EACfC,KAAM,GACNC,WAAY,EACZC,SAAS,EACTC,gBAAiB,MAAU,WAG5B,CACC,MAAMb,EAAOD,EAAE,QAAS,CAAC,cAEzBC,EAAKE,OAAO,CACXY,KAAM,aACNC,QAAS,KACTC,QAAS,KACTC,SAAU,WACVC,WAAY,KACZC,cAAc,IAGfnB,EAAKoB,MAASC,IACbA,EAAOC,IAAI,aAAcC,EAAA,EAAUC,IAAI5B,KAAKC,MAAO,CAClD4B,KAAM9B,EAAG+B,IAAI,cACbC,YAAa,IACX,C,CAIN,E,sGCqHM,MAAMC,UAAkBC,EAAA,EAA/B,c,oBASC,4C,gDAAkC,IAClC,yC,yDAEA,8C,gDAA4BjC,KAAKkC,SAASC,KAAKC,EAAA,EAAUR,IAAI5B,KAAKC,MAAO,CAAEoC,MAAO,GAAIC,QAAS,SAE/F,yC,gDAA6C,KAC7C,oC,gDAA0B,IAE1B,uC,iDAAqB,IAErB,sC,gDAAmC,KAEnC,2C,yDAuBA,qC,gDAA8CtC,KAAKuC,eAwdpD,CA7eW,SAAAC,GACTxC,KAAKyC,eAAeN,KAAKxC,EAAsBiC,IAAI5B,KAAKC,QAExDD,KAAK0C,OAAOP,KAAK,WAAY,QAC7BnC,KAAK2C,YAAY,aAAc,SAC/B3C,KAAK2C,YAAY,gBAAiB,YAClC3C,KAAK2C,YAAY,YAAa,QAE9B7C,MAAM0C,YAENxC,KAAKC,MAAM2C,OAAOC,GAAG,cAAc,KAClC7C,KAAK0B,IAAI,WAAY1B,KAAK8C,cAAgB9C,KAAK+C,UAAUC,OAAO,GAElE,CAaO,SAAAC,CAAUC,GAChB,MAAMC,EAAQnD,KAAKkC,SAASC,KAAKnC,KAAKoD,OAAOC,QAC7CF,EAAMG,aAAaJ,GACnB,MAAMrB,EAAOqB,EAASpB,IAAI,QACd,MAARD,GACHsB,EAAMzB,IAAI,OAAQG,GAEnBsB,EAAMzB,IAAI,KAAM,QAEhBwB,EAASxB,IAAI,QAASyB,GACtBnD,KAAKoD,OAAOjB,KAAKgB,GAEjB,MAAMI,EAAavD,KAAKwD,gBAAgBtB,SAASC,KAAKnC,KAAKoD,OAAOC,QAMlE,OALAE,EAAWD,aAAaJ,GACxBK,EAAWjD,OAAO,CAAEuB,KAAM4B,EAAA,GAAMC,QAAQ,GAAWC,WAAY,QAC/DT,EAASxB,IAAI,aAAc6B,GAC3BvD,KAAKoD,OAAOjB,KAAKoB,GAEVJ,CACR,CAEU,WAAAZ,GACT,OAAO,IAAIqB,EAAA,EACVC,EAAA,GAASjC,IAAI,CAAC,IACd,IAAMkC,EAAA,EAAMC,KAAK/D,KAAKC,MAAO,CAC5B+D,UAAW,YAAiBhE,KAAKoD,OAAOa,SAASnC,IAAI,YAAa,IAAK,CAAC,YAAa,YACnF,CAAC9B,KAAKoD,OAAOa,YAElB,CAGU,eAAAC,CAAgBhB,GAIzB,GAHApD,MAAMoE,gBAAgBhB,GAGM,MAAxBA,EAASpB,IAAI,QAAiB,CACjC,IAAIqC,EAASnE,KAAK8B,IAAI,UAClBqC,GACHjB,EAASkB,OAAO,OAAQD,EAAOE,O,CAIjCrE,KAAKiD,UAAUC,EAChB,CAEO,gBAAAoB,GACNxE,MAAMwE,mBAEFtE,KAAKuE,QAAQ,UAChBvE,KAAKwE,KAAKlE,OAAON,KAAKyE,UAAUzE,KAAK8B,IAAI,UACzC9B,KAAK0E,OAAa,MAAI,EAExB,CAEO,eAAAC,GACN7E,MAAM6E,kBAEN,MAAMC,EAAa5E,KAAKC,MAAM4E,UAAUD,WAClCE,EAAOC,KAAKC,MAAMhF,KAAKC,MAAMM,QAAUqE,GAE7C,IAAI9D,EAA6B,EAAtBd,KAAK8B,IAAI,OAAQ,GAE5B,GAAI9B,KAAKiF,cAAgBjF,KAAKkF,YAAclF,KAAKmF,eAAe,oBAAqB,CACpF,MAAMC,EAAmBpF,KAAKqF,WAAW,mBAAoB,GAEvDC,EAAItF,KAAKuF,aACTC,EAAIxF,KAAKyF,cAETC,EAAUX,KAAKY,IAAIL,EAAGE,GACtBI,EAASb,KAAKc,IAAIP,EAAGE,GAE3BxF,KAAK8F,QAAUC,MAAMhB,KAAKiB,KAAKhG,KAAKC,MAAMM,QAAUP,KAAKC,MAAMO,SAAWoE,EAAaA,IAAa/C,KAAK,GAErG6D,EAAU,MACb5E,GAAc,GAGfd,KAAKwD,gBAAgByC,SAASC,QAC9BlG,KAAKmG,WAAa,GAElB,IAAK,IAAIC,EAAI,EAAGA,EAAIpG,KAAKqG,MAAOD,IAAK,CAEpC,MAAME,EAAUxF,GAAQd,KAAKqG,MAAQD,GAC/BG,EAAS,eAAmBjB,EAAI,EAAGE,EAAI,EAAGF,EAAGE,EAAG,EAAGc,EAAUd,EAAII,EAAQU,EAAUhB,EAAIM,EAAQ,EAAG,GAGxG,IAAK,IAAIQ,EAAIG,EAAOvD,OAAS,EAAGoD,GAAK,EAAGA,IAAK,CAC5C,IAAII,EAAQD,EAAOH,IAEfI,EAAMC,EAAI,GAAKD,EAAMC,EAAInB,GAAKkB,EAAME,EAAI,GAAKF,EAAME,EAAIlB,IAC1De,EAAOI,OAAOP,EAAG,E,CAInBpG,KAAKmG,WAAWhE,KAAKoE,E,CAGtB,IAAIK,EAAM,EACNC,EAAS,EACTC,EAAY,EACZC,EAAWC,IACXC,EAAQ,EACZ,OAAYjH,KAAKkH,YAAahE,IAC7B,MAAMiE,EAAejE,EAASpB,IAAI,eAAgB,GAClD8E,GAAOO,EACPN,GAAU9B,KAAKqC,IAAID,EAAa,IAGjCnH,KAAKkH,WAAWG,MAAK,CAACC,EAAGC,KACxB,IAAIC,EAASF,EAAExF,IAAI,QAAS,GACxB2F,EAASF,EAAEzF,IAAI,QAAS,GAE5B,OAAI0F,EAASC,GACJ,EAELD,EAASC,EACL,EAED,CAAC,IAIT,OAAYzH,KAAKkH,YAAahE,IAE7B,MAAMwE,EAAQxE,EAASpB,IAAI,eAAgB,GAEvC4F,GAASb,IACZD,EAAM1D,EAASpB,IAAI,QAAS,IAGzB4F,EAAQZ,IACXA,EAAYY,GAGTA,EAAQX,IACXA,EAAWW,GAGZT,GAAO,IAGRjH,KAAK2H,cAAc,WAAYZ,GAC/B/G,KAAK2H,cAAc,YAAab,GAChC9G,KAAK2H,cAAc,WAAYf,GAC/B5G,KAAK2H,cAAc,eAAgBf,EAAMK,GACzCjH,KAAK2H,cAAc,mBAAoBd,GAEvC,MAAMe,EAAc7C,KAAKY,IAAIL,EAAGE,GAC1B/E,EAAc,kBAAuBT,KAAK8B,IAAI,cAAe,IAAK8F,GAAexC,EACjF1E,EAAc,kBAAuBV,KAAK8B,IAAI,cAAe,KAAM8F,GAAexC,EAElFxE,EAASZ,KAAK8B,IAAI,SAAU,CAAC,IAEnC,OAAY9B,KAAKkH,YAAahE,IAC7B,MAAMwE,EAAQxE,EAASpB,IAAI,eAAgB,GACrCyB,EAAaL,EAASpB,IAAI,cAChC,IAAI+F,EAAWpH,GAAeC,EAAcD,IAAgBiH,EAAQX,IAAaD,EAAYC,GACzF,QAAYc,KACfA,EAAWnH,GAGZ,MAAMgB,EAAM1B,KAAKqG,MAAQ,EAAItB,KAAK+C,OAAOD,EAAWpH,IAAgBC,EAAcD,IAAgBT,KAAKqG,MAAQ,IAE/GnD,EAASkB,OAAO,MAAO1C,GACvBwB,EAASkB,OAAO,WAAYyD,GAE5B,IAAIE,EAAQnH,EAAOmE,KAAK+C,MAAM/C,KAAKiD,SAAYpH,EAAa,SAE5DsC,EAASkB,OAAO,QAAS2D,GAEzBxE,EAAWjD,OAAO,CAAEuH,SAAUA,EAAUI,SAAUF,EAAOtB,GAAI,KAAQ,IAGtEzG,KAAKkI,UAAW,EAChBlI,KAAK8C,cAAgB,EAErB9C,KAAKC,MAAM2C,OAAOuF,KAAK,cAAc,KACpCnI,KAAKoI,YAAW,KACfpI,KAAKkI,UAAW,EAChBlI,KAAKqI,cAAc,WAAW,GAC5B,GAAG,G,CAIR,MAAMC,EAActI,KAAKuI,aACzB,GAAID,EAAa,CAChB,MAAME,EAAWxI,KAAKwD,gBAAgByC,SAASwC,WAA2BD,QACpE9B,EAAI3B,KAAKC,MAAMsD,EAAYI,KAC3BjC,EAAI1B,KAAKC,MAAMsD,EAAYK,MAC3BrD,EAAIP,KAAKC,MAAMsD,EAAYM,MAAQN,EAAYK,MAC/CnD,EAAIT,KAAKC,MAAMsD,EAAYO,OAASP,EAAYI,KAEhDI,EAAYN,EAAQO,aAAatC,EAAGC,EAAGpB,EAAGE,GAAGhB,KAC7CwE,EAAShJ,KAAK8F,QAEpB,IAAImD,EAAI,EACR,IAAK,IAAI9I,EAAIuG,EAAGvG,EAAIuG,EAAIlB,EAAGrF,IAC1B,IAAK,IAAI+I,EAAIzC,EAAGyC,EAAIzC,EAAInB,EAAG4D,IAAK,CAC/B,IAAI9C,GAAMjG,EAAI,GAAK2E,GAAQA,EAAOoE,GACd,GAAhBJ,EAAUG,KACbD,EAAO5C,GAAK,GAEb6C,GAAK,C,CAGPjJ,KAAKuI,kBAAeY,C,CAGjBnJ,KAAKkI,UAAYlI,KAAKuE,QAAQ,aACjCvE,KAAKoJ,cAEP,CAEU,YAAAA,GAGT,GAFApJ,KAAKuI,kBAAeY,EAEhBnJ,KAAK8C,cAAgB9C,KAAK+C,UAAUC,OAAQ,CAC/C,MAAME,EAAWlD,KAAK+C,UAAU/C,KAAK8C,eAC/BK,EAAQD,EAASpB,IAAI,SACrByB,EAAaL,EAASpB,IAAI,cAE1B8C,EAAa5E,KAAKC,MAAM4E,UAAUD,WAExC,IAAIyE,EAAK9F,EAAWhD,QAChB+I,EAAK/F,EAAW/C,SAEpB,MAAMgI,EAAWjF,EAAW0C,SAASwC,WAA2BD,QAC1D9G,EAAMwB,EAASpB,IAAI,OAEnByE,EAASvG,KAAKmG,WAAWzE,GAEzB4D,EAAItF,KAAKuF,aACTC,EAAIxF,KAAKyF,cAETX,EAAOC,KAAKC,MAAMhF,KAAKC,MAAMM,QAAUqE,GAEvC6B,EAAIzG,KAAKyG,IACTC,EAAI1G,KAAK0G,IAET9F,EAASZ,KAAK8B,IAAI,SAAU,CAAC,IAC/BwD,EAAIE,GACH6D,GAAM/D,EAAI,GACb,OAAY1E,GAASmH,IACP,GAATA,GAAuC,GAAzB7E,EAASpB,IAAI,WAC9BoB,EAASkB,OAAO,QAAS,GACzBb,EAAW7B,IAAI,WAAY,IAC1B2H,EAAIC,GAAM,CAACA,EAAID,G,IAMhB7D,EAAIF,GACH+D,GAAM/D,EAAI,GACb,OAAY1E,GAASmH,IACG,IAAnBhD,KAAKqC,IAAIW,IAAyC,GAAzB7E,EAASpB,IAAI,WACzCoB,EAASkB,OAAO,QAAS2D,GACzBxE,EAAW7B,IAAI,WAAYqG,IAC1BsB,EAAIC,GAAM,CAACA,EAAID,G,IAMpB,MAAME,EAAKxE,KAAKiB,KAAKqD,EAAKzE,GACpB4E,EAAKzE,KAAKiB,KAAKsD,EAAK1E,GAE1B,GAAI4D,GAAWa,EAAK,GAAKC,EAAK,EAAG,CAChC,IAAIG,EAAS1E,KAAKC,MAAMD,KAAKiD,SAAWzB,EAAOvD,OAAShD,KAAK8B,IAAI,aAAc,IAE3E4H,GAAa,EAEjB,KAAOA,GAAY,CAElB,IAAIC,EAAIpD,EAAOkD,GACf,GAAIE,EAAG,CAGN,GAFAD,GAAa,EAET1J,KAAK8C,cAAgB,EAAG,CAC3B,IAAI8G,EAAK7E,KAAKC,OAAO2E,EAAElD,EAAIA,GAAK7B,EAAa2E,EAAK,GAC9CM,EAAK9E,KAAKC,OAAO2E,EAAEjD,EAAIA,GAAK9B,EAAa4E,EAAK,GAClDE,EAAa1J,KAAK8J,UAAUF,EAAIC,EAAIN,EAAIC,EAAI1E,E,CAG7C,GAAI6E,EAAElD,EAAI4C,EAAK,EAAI,GAAKM,EAAElD,EAAI4C,EAAK,EAAI/D,GAAKqE,EAAEjD,EAAI4C,EAAK,EAAI,GAAKK,EAAEjD,EAAI4C,EAAK,EAAI9D,EAC9EiE,IACAC,GAAa,OAGb,GAAKA,EAyBJD,GAAU,MAzBM,CAChB,MAAM1B,EAAQ7E,EAASpB,IAAI,QAAS,GAC9B+F,EAAW3E,EAASpB,IAAI,WAAY,IACnB,QAAnBqB,EAAMrB,IAAI,MACbqB,EAAM4G,QAAQ,CAAEC,IAAK,IAAKC,GAAIN,EAAElD,EAAGyD,SAAUlK,KAAK8B,IAAI,oBAAqB,GAAIqI,OAAQnK,KAAK8B,IAAI,qBAChGqB,EAAM4G,QAAQ,CAAEC,IAAK,IAAKC,GAAIN,EAAEjD,EAAGwD,SAAUlK,KAAK8B,IAAI,oBAAqB,GAAIqI,OAAQnK,KAAK8B,IAAI,qBAChGqB,EAAM4G,QAAQ,CAAEC,IAAK,WAAYC,GAAIlC,EAAOmC,SAAUlK,KAAK8B,IAAI,oBAAqB,GAAIqI,OAAQnK,KAAK8B,IAAI,qBACzGqB,EAAM4G,QAAQ,CAAEC,IAAK,WAAYC,GAAIpC,EAAUqC,SAAUlK,KAAK8B,IAAI,oBAAqB,GAAIqI,OAAQnK,KAAK8B,IAAI,uBAG5GqB,EAAM7C,OAAO,CAAEmG,EAAGkD,EAAElD,EAAGC,EAAGiD,EAAEjD,EAAGuB,SAAUF,EAAOF,SAAUA,IAC1D1E,EAAMiH,UAGP7G,EAAWjD,OAAO,CAAEmG,EAAGkD,EAAElD,EAAGC,EAAGiD,EAAEjD,IAEjC,IAAK,IAAIN,EAAIG,EAAOvD,OAAS,EAAGoD,GAAK,EAAGA,IAAK,CAC5C,IAAII,EAAQD,EAAOH,GACfI,EAAMC,GAAKkD,EAAElD,EAAI4C,EAAK,GAAK7C,EAAMC,GAAKkD,EAAElD,EAAI4C,EAAK,GAAK7C,EAAME,GAAKiD,EAAEjD,EAAI4C,EAAK,GAAK9C,EAAME,GAAKiD,EAAEjD,EAAI4C,EAAK,GAC1G/C,EAAOI,OAAOP,EAAG,E,CAGnBpG,KAAKuI,aAAe,CAAEI,MAAOgB,EAAElD,EAAIA,EAAI4C,EAAK,GAAKzE,EAAYgE,OAAQe,EAAElD,EAAIA,EAAI4C,EAAK,GAAKzE,EAAY8D,KAAMiB,EAAEjD,EAAIA,EAAI4C,EAAK,GAAK1E,EAAYiE,QAASc,EAAEjD,EAAIA,EAAI4C,EAAK,GAAK1E,E,MAOtK,CACJ,GAAI5E,KAAK8B,IAAI,WAIZ,YAHA9B,KAAKoI,YAAW,KACfpI,KAAKqK,WAAW,mBAA6D,GAAzCrK,KAAKqF,WAAW,mBAAoB,GAAS,GAC/E,IAGJlC,EAAMzB,IAAI,KAAM,QAChBgI,GAAa,C,GAKhB1J,KAAK8C,e,CAEP,CAIO,eAAAwH,CAAgBpH,GACtBpD,MAAMwK,gBAAgBpH,GACtB,MAAMC,EAAQD,EAASpB,IAAI,SACvBqB,IACHnD,KAAKoD,OAAOmH,YAAYpH,GACxBA,EAAMqH,WAGP,MAAMjH,EAAaL,EAASpB,IAAI,cAC5ByB,IACHvD,KAAKoD,OAAOmH,YAAYhH,GACxBA,EAAWiH,UAEb,CAOU,SAAA/F,CAAUgG,GACnB,IAAIC,EAAoD,GAExD,GAAID,EAAO,CACV,MAAME,EAAQ,q2BACRC,EAAM,IAAIC,OAAO,KAAOF,EAAQ,OAAUA,EAAQ,MAAQA,EAAQ,SAAWA,EAAQ,MAAO,MAClG,IAKIG,EALAC,EAAMN,EAAMO,MAAMJ,GACtB,IAAKG,EACJ,MAAO,GAIR,KACCD,EAAOC,EAAIE,MAENH,GAHO,CAOZ,IAAII,EACJ,IAAK,IAAI9E,EAAI,EAAGA,EAAIsE,EAAM1H,OAAQoD,IACjC,GAAIsE,EAAMtE,GAAG+E,SAASC,eAAiBN,EAAKM,cAAe,CAC1DF,EAAOR,EAAMtE,GACb,K,CAGE8E,GACHA,EAAKxD,QACA1H,KAAKqL,cAAcP,KACvBI,EAAKC,SAAWL,IAIjBJ,EAAMvI,KAAK,CACVgJ,SAAUL,EACVpD,MAAO,G,CAKV,IAAI/G,EAAeX,KAAK8B,IAAI,gBAE5B,MAAMwJ,EAAWtL,KAAK8B,IAAI,WAAY,GAChCjB,EAAgBb,KAAK8B,IAAI,gBAAiB,GAEhD,GAAIwJ,EAAW,GAAKzK,EAAgB,GAAMF,GAAgBA,EAAaqC,OAAS,EAC/E,IAAK,IAAIoD,EAAIsE,EAAM1H,OAAS,EAAGoD,GAAK,EAAGA,IAAK,CAC3C,IAAId,EAAIoF,EAAMtE,GACV0E,EAAOxF,EAAE6F,SAET7F,EAAEoC,MAAQ4D,GACbZ,EAAM/D,OAAOP,EAAG,GAEb0E,EAAK9H,OAASnC,GACjB6J,EAAM/D,OAAOP,EAAG,GAEbzF,IAAgD,IAAhCA,EAAa4K,QAAQT,IACxCJ,EAAM/D,OAAOP,EAAG,E,CAKnBsE,EAAMrD,MAAK,SAAUC,EAAGC,GACvB,OAAID,EAAEI,OAASH,EAAEG,MACT,EAECJ,EAAEI,MAAQH,EAAEG,OACZ,EAGD,CAET,IAEA,MAAM8D,EAAWxL,KAAK8B,IAAI,WAAYkF,KAClC0D,EAAM1H,OAASwI,IAClBd,EAAQA,EAAMe,MAAM,EAAGD,G,CAIzB,OAAOd,CACR,CAQO,aAAAW,CAAcP,GACpB,IAAIY,EAAQZ,EAAKM,cACjB,OAAON,EAAK,IAAMY,EAAM,IACpBZ,EAAKa,OAAO,IAAMD,EAAMC,OAAO,IAC/Bb,GAAQY,CACb,CAEU,SAAA5B,CAAUrD,EAAWC,EAAWpB,EAAWE,EAAWV,GAC/D,MAAMkE,EAAShJ,KAAK8F,QACpB,GAAIkD,EACH,IAAK,IAAI7I,EAAIuG,EAAGvG,EAAIuG,EAAIlB,EAAGrF,GAAK,EAC/B,IAAK,IAAI+I,EAAIzC,EAAGyC,EAAIzC,EAAInB,EAAG4D,GAAK,EAE/B,GAAiB,GAAbF,GADM7I,EAAI,GAAK2E,GAAQA,EAAOoE,IAEjC,OAAO,EAKX,OAAO,CACR,EAjgBA,qC,gDAAkC,cAClC,sC,gDAA0CjH,EAAA,EAAO2J,WAAWC,OAAO,CAAC7J,EAAU8J,a,iEC1KxE,MAAMC,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/wordcloud/WordCloudDefaultTheme.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/wordcloud/WordCloud.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/wc.js"], "sourcesContent": ["import { Theme } from \"../../core/Theme\";\r\nimport { percent, p100, p50 } from \"../../core/util/Percent\";\r\nimport { Rectangle } from \"../../core/render/Rectangle\";\r\n\r\nimport * as $ease from \"../../core/util/Ease\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class WordCloudDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\r\n\t\tconst ic = this._root.interfaceColors;\r\n\t\tconst r = this.rule.bind(this);\r\n\r\n\t\tr(\"WordCloud\").setAll({\r\n\t\t\twidth: p100,\r\n\t\t\theight: p100,\r\n\t\t\tminFontSize: percent(2),\r\n\t\t\tmaxFontSize: percent(15),\r\n\t\t\texcludeWords: [],\r\n\t\t\tangles: [0, -90],\r\n\t\t\tminWordLength: 1,\r\n\t\t\tstep: 15,\r\n\t\t\trandomness: 0,\r\n\t\t\tautoFit: true,\r\n\t\t\tanimationEasing: $ease.out($ease.cubic)\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"wordcloud\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tposition: \"absolute\",\r\n\t\t\t\tlineHeight: p100,\r\n\t\t\t\tpopulateText: true\r\n\t\t\t});\r\n\r\n\t\t\trule.setup = (target) => {\r\n\t\t\t\ttarget.set(\"background\", Rectangle.new(this._root, {\r\n\t\t\t\t\tfill: ic.get(\"background\"),\r\n\t\t\t\t\tfillOpacity: 0\r\n\t\t\t\t}));\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n}\r\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { CanvasLayer } from \"../../core/render/backend/CanvasRenderer\";\nimport type { ColorSet } from \"../../core/util/ColorSet\";\nimport type { Percent } from \"../../core/util/Percent\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport type { IDisposer } from \"../../core/util/Disposer\";\nimport type { Time } from \"../../core/util/Animation\";\n\nimport { WordCloudDefaultTheme } from \"./WordCloudDefaultTheme\";\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate } from \"../../core/render/Series\";\nimport { Template } from \"../../core/util/Template\";\nimport { Label } from \"../../core/render/Label\";\nimport { Container } from \"../../core/render/Container\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { Color } from \"../../core/util/Color\";\nimport type { IBounds } from \"../../core/util/IBounds\";\n\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $array from \"../../core/util/Array\";\nimport * as $math from \"../../core/util/Math\";\nimport * as $type from \"../../core/util/Type\";\n\nexport interface IWordCloudDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Category.\n\t */\n\tcategory: string;\n\n\t/**\n\t * Label.\n\t */\n\tlabel: Label;\n\n\t/**\n\t * Label.\n\t */\n\tghostLabel: Label;\n\n\t/**\n\t * Fill color used for the slice and related elements, e.g. legend marker.\n\t */\n\tfill: Color;\n\n\t/**\n\t * @ignore\n\t */\n\tset: number;\n\n\t/**\n\t * @ignore\n\t */\n\tangle: number;\n\n\t/**\n\t * @ignore\n\t */\n\tfontSize: number;\n}\n\nexport interface IWordCloudSettings extends ISeriesSettings {\n\n\t/**\n\t * Duration of word animation when chart resizes.\n\t */\n\tanimationDuration?: number;\n\n\t/**\n\t * An easing function to use for word animations.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/animations/#Easing_functions} for more info\n\t * @default am5.ease.out($ease.cubic)\n\t */\n\tanimationEasing?: (t: Time) => Time;\n\n\t/**\n\t * @default false\n\t */\n\tautoFit?: boolean;\n\n\t/**\n\t * Progress of current word layout animation. (0-1)\n\t * \n\t * @readonly\n\t */\n\tprogress?: number;\n\n\t/**\n\t * A [[ColorSet]] to use when asigning colors for slices.\n\t */\n\tcolors?: ColorSet;\n\n\t/**\n\t * A field in data that holds category names.\n\t */\n\tcategoryField?: string;\n\n\t/**\n\t * A field that holds color for label fill.\n\t */\n\tfillField?: string;\n\n\t/**\n\t * Source text from which words are extracted.\n\t */\n\ttext?: string;\n\n\t/**\n\t * Absolute or relative font size for the smallest words.\n\t */\n\tminFontSize?: number | Percent;\n\n\t/**\n\t * Absolute or relative font size for the biggest words.\n\t */\n\tmaxFontSize?: number | Percent;\n\n\t/**\n\t * Minimum occurances for a word to be included into cloud.\n\t */\n\tminValue?: number;\n\n\t/**\n\t * Maximum number of words to show.\n\t */\n\tmaxCount?: number;\n\n\t/**\n\t * Array of words  exclude from cloud.\n\t */\n\texcludeWords?: Array<string>;\n\n\t/**\n\t * Randomness of word placement (0-1).\n\t */\n\trandomness?: number;\n\n\t/**\n\t * Minimum number of characters for a word to be included in the cloud.\n\t */\n\tminWordLength?: number;\n\n\t/**\n\t * An array of possible rotation angles for words.\n\t */\n\tangles?: number[];\n\n\t/**\n\t * Step for next word placement.\n\t */\n\tstep?: number;\n}\n\nexport interface IWordCloudPrivate extends ISeriesPrivate {\n\n\t/**\n\t * Indicates whether size of the font was adjusted for better fit.\n\t */\n\tadjustedFontSize: number;\n\n}\n\n/**\n * Creates a [[WordlCloud]] series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/word-cloud/} for more info\n * @important\n */\nexport class WordCloud extends Series {\n\n\tpublic static className: string = \"WordCloud\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([WordCloud.className]);\n\n\tdeclare public _settings: IWordCloudSettings;\n\tdeclare public _privateSettings: IWordCloudPrivate;\n\tdeclare public _dataItemSettings: IWordCloudDataItem;\n\n\tprotected _currentIndex: number = 0;\n\tprotected _timeoutDP?: IDisposer;\n\n\tprotected _ghostContainer = this.children.push(Container.new(this._root, { layer: 99, opacity: 0.01 }))\n\n\tprotected _pointSets: Array<Array<IPoint>> = [];\n\tprotected _sets: number = 3;\n\n\tprotected _process = false;\n\n\tprotected _buffer: Array<number> = [];\n\n\tprotected _boundsToAdd?: IBounds;\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(WordCloudDefaultTheme.new(this._root));\n\n\t\tthis.fields.push(\"category\", \"fill\");\n\t\tthis._setDefault(\"valueField\", \"value\");\n\t\tthis._setDefault(\"categoryField\", \"category\");\n\t\tthis._setDefault(\"fillField\", \"fill\");\n\n\t\tsuper._afterNew();\n\n\t\tthis._root.events.on(\"frameended\", () => {\n\t\t\tthis.set(\"progress\", this._currentIndex / this.dataItems.length);\n\t\t})\n\t}\n\n\n\t/**\n\t * A [[ListTemplate]] of all labels in series.\n\t *\n\t * `labels.template` can also be used to configure labels.\n\t */\n\tpublic readonly labels: ListTemplate<Label> = this._makeLabels();\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLabel(dataItem: DataItem<this[\"_dataItemSettings\"]>): Label {\n\t\tconst label = this.children.push(this.labels.make());\n\t\tlabel._setDataItem(dataItem);\n\t\tconst fill = dataItem.get(\"fill\");\n\t\tif (fill != null) {\n\t\t\tlabel.set(\"fill\", fill);\n\t\t}\n\t\tlabel.set(\"x\", -999999); // do not change!\n\n\t\tdataItem.set(\"label\", label);\n\t\tthis.labels.push(label);\n\n\t\tconst ghostLabel = this._ghostContainer.children.push(this.labels.make());\n\t\tghostLabel._setDataItem(dataItem);\n\t\tghostLabel.setAll({ fill: Color.fromHex(0x000000), fontWeight: \"900\" });\n\t\tdataItem.set(\"ghostLabel\", ghostLabel);\n\t\tthis.labels.push(ghostLabel);\n\n\t\treturn label;\n\t}\n\n\tprotected _makeLabels(): ListTemplate<Label> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Label._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [\"wordcloud\", \"series\"])\n\t\t\t}, [this.labels.template]),\n\t\t);\n\t}\n\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\n\t\tif (dataItem.get(\"fill\") == null) {\n\t\t\tlet colors = this.get(\"colors\");\n\t\t\tif (colors) {\n\t\t\t\tdataItem.setRaw(\"fill\", colors.next());\n\t\t\t}\n\t\t}\n\n\t\tthis.makeLabel(dataItem);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"text\")) {\n\t\t\tthis.data.setAll(this._getWords(this.get(\"text\")));\n\t\t\tthis._dirty[\"text\"] = false;\n\t\t}\n\t}\n\n\tpublic _updateChildren() {\n\t\tsuper._updateChildren();\n\n\t\tconst resolution = this._root._renderer.resolution;\n\t\tconst cols = Math.round(this._root.width() * resolution);\n\t\t//const rows = Math.round(this._root.height() * resolution);\n\t\tlet step = this.get(\"step\", 1) * 2;\n\n\t\tif (this._valuesDirty || this._sizeDirty || this.isPrivateDirty(\"adjustedFontSize\")) {\n\t\t\tconst adjustedFontSize = this.getPrivate(\"adjustedFontSize\", 1);\n\n\t\t\tconst w = this.innerWidth();\n\t\t\tconst h = this.innerHeight();\n\n\t\t\tconst smaller = Math.min(w, h);\n\t\t\tconst bigger = Math.max(w, h);\n\n\t\t\tthis._buffer = Array(Math.ceil(this._root.width() * this._root.height() * resolution * resolution)).fill(0);\n\n\t\t\tif (smaller < 800) {\n\t\t\t\tstep = step / 2;\n\t\t\t}\n\n\t\t\tthis._ghostContainer._display.clear();\n\t\t\tthis._pointSets = [];\n\n\t\t\tfor (let i = 0; i < this._sets; i++) {\n\t\t\t\t// bigger step at the beginning\n\t\t\t\tconst setStep = step * (this._sets - i);\n\t\t\t\tconst points = $math.spiralPoints(w / 2, h / 2, w, h, 0, setStep * h / bigger, setStep * w / bigger, 0, 0)\n\n\t\t\t\t// generated more points and remove those out of bounds\n\t\t\t\tfor (let i = points.length - 1; i >= 0; i--) {\n\t\t\t\t\tlet point = points[i];\n\n\t\t\t\t\tif (point.x < 0 || point.x > w || point.y < 0 || point.y > h) {\n\t\t\t\t\t\tpoints.splice(i, 1);\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis._pointSets.push(points);\n\t\t\t}\n\n\t\t\tlet sum = 0;\n\t\t\tlet absSum = 0;\n\t\t\tlet valueHigh = 0;\n\t\t\tlet valueLow = Infinity;\n\t\t\tlet count = 0;\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tconst valueWorking = dataItem.get(\"valueWorking\", 0);\n\t\t\t\tsum += valueWorking;\n\t\t\t\tabsSum += Math.abs(valueWorking);\n\t\t\t});\n\n\t\t\tthis._dataItems.sort((a, b) => {\n\t\t\t\tlet aValue = a.get(\"value\", 0);\n\t\t\t\tlet bValue = b.get(\"value\", 0);\n\n\t\t\t\tif (aValue > bValue) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\tif (aValue < bValue) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\treturn 0;\n\n\t\t\t})\n\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\n\t\t\t\tconst value = dataItem.get(\"valueWorking\", 0);\n\n\t\t\t\tif (value >= absSum) {\n\t\t\t\t\tsum = dataItem.get(\"value\", 0);\n\t\t\t\t}\n\n\t\t\t\tif (value > valueHigh) {\n\t\t\t\t\tvalueHigh = value;\n\t\t\t\t}\n\n\t\t\t\tif (value < valueLow) {\n\t\t\t\t\tvalueLow = value;\n\t\t\t\t}\n\n\t\t\t\tcount++;\n\t\t\t});\n\n\t\t\tthis.setPrivateRaw(\"valueLow\", valueLow);\n\t\t\tthis.setPrivateRaw(\"valueHigh\", valueHigh);\n\t\t\tthis.setPrivateRaw(\"valueSum\", sum);\n\t\t\tthis.setPrivateRaw(\"valueAverage\", sum / count);\n\t\t\tthis.setPrivateRaw(\"valueAbsoluteSum\", absSum);\n\n\t\t\tconst smallerSize = Math.min(w, h);\n\t\t\tconst minFontSize = $utils.relativeToValue(this.get(\"minFontSize\", 10), smallerSize) * adjustedFontSize;\n\t\t\tconst maxFontSize = $utils.relativeToValue(this.get(\"maxFontSize\", 100), smallerSize) * adjustedFontSize;\n\n\t\t\tconst angles = this.get(\"angles\", [0]);\n\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tconst value = dataItem.get(\"valueWorking\", 0);\n\t\t\t\tconst ghostLabel = dataItem.get(\"ghostLabel\");\n\t\t\t\tlet fontSize = minFontSize + (maxFontSize - minFontSize) * (value - valueLow) / (valueHigh - valueLow);\n\t\t\t\tif ($type.isNaN(fontSize)) {\n\t\t\t\t\tfontSize = maxFontSize;\n\t\t\t\t}\n\n\t\t\t\tconst set = this._sets - 1 - Math.floor((fontSize - minFontSize) / (maxFontSize - minFontSize) * (this._sets - 1));\n\n\t\t\t\tdataItem.setRaw(\"set\", set);\n\t\t\t\tdataItem.setRaw(\"fontSize\", fontSize);\n\n\t\t\t\tlet angle = angles[Math.floor(Math.random() * (angles.length))];\n\n\t\t\t\tdataItem.setRaw(\"angle\", angle);\n\n\t\t\t\tghostLabel.setAll({ fontSize: fontSize, rotation: angle, x: -10000 });\n\t\t\t})\n\n\t\t\tthis._process = false;\n\t\t\tthis._currentIndex = 0;\n\n\t\t\tthis._root.events.once(\"frameended\", () => {\n\t\t\t\tthis.setTimeout(() => {\n\t\t\t\t\tthis._process = true;\n\t\t\t\t\tthis._markDirtyKey(\"progress\");\n\t\t\t\t}, 50)\n\t\t\t})\n\t\t}\n\n\t\tconst boundsToAdd = this._boundsToAdd;\n\t\tif (boundsToAdd) {\n\t\t\tconst context = (this._ghostContainer._display.getLayer() as CanvasLayer).context;\n\t\t\tconst y = Math.round(boundsToAdd.top);\n\t\t\tconst x = Math.round(boundsToAdd.left);\n\t\t\tconst w = Math.round(boundsToAdd.right - boundsToAdd.left);\n\t\t\tconst h = Math.round(boundsToAdd.bottom - boundsToAdd.top);\n\n\t\t\tconst imageData = context.getImageData(x, y, w, h).data;\n\t\t\tconst buffer = this._buffer;\n\n\t\t\tlet n = 3;\n\t\t\tfor (let r = y; r < y + h; r++) {\n\t\t\t\tfor (let c = x; c < x + w; c++) {\n\t\t\t\t\tlet i = ((r + 1) * cols - (cols - c));\n\t\t\t\t\tif (imageData[n] != 0) {\n\t\t\t\t\t\tbuffer[i] = 1;\n\t\t\t\t\t}\n\t\t\t\t\tn += 4;\n\t\t\t\t}\n\t\t\t}\n\t\t\tthis._boundsToAdd = undefined;\n\t\t}\n\n\t\tif (this._process && this.isDirty(\"progress\")) {\n\t\t\tthis._processItem();\n\t\t}\n\t}\n\n\tprotected _processItem() {\n\t\tthis._boundsToAdd = undefined;\n\n\t\tif (this._currentIndex < this.dataItems.length) {\n\t\t\tconst dataItem = this.dataItems[this._currentIndex];\n\t\t\tconst label = dataItem.get(\"label\");\n\t\t\tconst ghostLabel = dataItem.get(\"ghostLabel\");\n\n\t\t\tconst resolution = this._root._renderer.resolution;\n\n\t\t\tlet lw = ghostLabel.width();\n\t\t\tlet lh = ghostLabel.height();\n\n\t\t\tconst context = (ghostLabel._display.getLayer() as CanvasLayer).context;\n\t\t\tconst set = dataItem.get(\"set\");\n\n\t\t\tconst points = this._pointSets[set];\n\n\t\t\tconst w = this.innerWidth();\n\t\t\tconst h = this.innerHeight();\n\n\t\t\tconst cols = Math.round(this._root.width() * resolution);\n\n\t\t\tconst x = this.x();\n\t\t\tconst y = this.y();\n\n\t\t\tconst angles = this.get(\"angles\", [0]);\n\t\t\tif (w > h) {\n\t\t\t\tif (lw >= w / 2) {\n\t\t\t\t\t$array.each(angles, (angle) => {\n\t\t\t\t\t\tif (angle == 0 && dataItem.get(\"angle\") != 0) {\n\t\t\t\t\t\t\tdataItem.setRaw(\"angle\", 0);\n\t\t\t\t\t\t\tghostLabel.set(\"rotation\", 0);\n\t\t\t\t\t\t\t[lw, lh] = [lh, lw];\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (h > w) {\n\t\t\t\tif (lw >= w / 2) {\n\t\t\t\t\t$array.each(angles, (angle) => {\n\t\t\t\t\t\tif (Math.abs(angle) == 90 && dataItem.get(\"angle\") == 0) {\n\t\t\t\t\t\t\tdataItem.setRaw(\"angle\", angle);\n\t\t\t\t\t\t\tghostLabel.set(\"rotation\", angle);\n\t\t\t\t\t\t\t[lw, lh] = [lh, lw];\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst rw = Math.ceil(lw * resolution);\n\t\t\tconst rh = Math.ceil(lh * resolution);\n\n\t\t\tif (context && lw > 0 && lh > 0) {\n\t\t\t\tlet pIndex = Math.round(Math.random() * points.length * this.get(\"randomness\", 0));\n\n\t\t\t\tlet intersects = true;\n\n\t\t\t\twhile (intersects) {\n\n\t\t\t\t\tlet p = points[pIndex];\n\t\t\t\t\tif (p) {\n\t\t\t\t\t\tintersects = false;\n\n\t\t\t\t\t\tif (this._currentIndex > 0) {\n\t\t\t\t\t\t\tlet cx = Math.round((p.x + x) * resolution - rw / 2);\n\t\t\t\t\t\t\tlet cy = Math.round((p.y + y) * resolution - rh / 2);\n\t\t\t\t\t\t\tintersects = this._hasColor(cx, cy, rw, rh, cols);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (p.x - lw / 2 < 0 || p.x + lw / 2 > w || p.y - lh / 2 < 0 || p.y + lh / 2 > h) {\n\t\t\t\t\t\t\tpIndex++;\n\t\t\t\t\t\t\tintersects = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tif (!intersects) {\n\t\t\t\t\t\t\t\tconst angle = dataItem.get(\"angle\", 0);\n\t\t\t\t\t\t\t\tconst fontSize = dataItem.get(\"fontSize\", 0);\n\t\t\t\t\t\t\t\tif (label.get(\"x\") != -999999) {\n\t\t\t\t\t\t\t\t\tlabel.animate({ key: \"x\", to: p.x, duration: this.get(\"animationDuration\", 0), easing: this.get(\"animationEasing\") })\n\t\t\t\t\t\t\t\t\tlabel.animate({ key: \"y\", to: p.y, duration: this.get(\"animationDuration\", 0), easing: this.get(\"animationEasing\") })\n\t\t\t\t\t\t\t\t\tlabel.animate({ key: \"rotation\", to: angle, duration: this.get(\"animationDuration\", 0), easing: this.get(\"animationEasing\") })\n\t\t\t\t\t\t\t\t\tlabel.animate({ key: \"fontSize\", to: fontSize, duration: this.get(\"animationDuration\", 0), easing: this.get(\"animationEasing\") })\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t\tlabel.setAll({ x: p.x, y: p.y, rotation: angle, fontSize: fontSize });\n\t\t\t\t\t\t\t\t\tlabel.appear();\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tghostLabel.setAll({ x: p.x, y: p.y });\n\n\t\t\t\t\t\t\t\tfor (let i = points.length - 1; i >= 0; i--) {\n\t\t\t\t\t\t\t\t\tlet point = points[i]\n\t\t\t\t\t\t\t\t\tif (point.x >= p.x - lw / 2 && point.x <= p.x + lw / 2 && point.y >= p.y - lh / 2 && point.y <= p.y + lh / 2) {\n\t\t\t\t\t\t\t\t\t\tpoints.splice(i, 1);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis._boundsToAdd = { left: (p.x + x - lw / 2) * resolution, right: (p.x + x + lw / 2) * resolution, top: (p.y + y - lh / 2) * resolution, bottom: (p.y + y + lh / 2) * resolution };\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tpIndex += 2;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (this.get(\"autoFit\")) {\n\t\t\t\t\t\t\tthis.setTimeout(() => {\n\t\t\t\t\t\t\t\tthis.setPrivate(\"adjustedFontSize\", this.getPrivate(\"adjustedFontSize\", 1) * 0.9);\n\t\t\t\t\t\t\t}, 50);\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tlabel.set(\"x\", -999999);\n\t\t\t\t\t\tintersects = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._currentIndex++;\n\t\t}\n\t}\n\t/**\n* @ignore\n*/\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tthis.labels.removeValue(label);\n\t\t\tlabel.dispose();\n\t\t}\n\n\t\tconst ghostLabel = dataItem.get(\"ghostLabel\");\n\t\tif (ghostLabel) {\n\t\t\tthis.labels.removeValue(ghostLabel);\n\t\t\tghostLabel.dispose();\n\t\t}\n\t}\n\t/**\n* Extracts words and number of their appearances from a text.\n*\n* @ignore\n* @param  input  Source text\n*/\n\tprotected _getWords(input?: string): Array<{ category: string, value: number }> {\n\t\tlet words: Array<{ category: string, value: number }> = [];\n\n\t\tif (input) {\n\t\t\tconst chars = \"\\u0041-\\u005A\\u0061-\\u007A\\u00AA\\u00B5\\u00BA\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376-\\u0377\\u037A-\\u037D\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u0523\\u0531-\\u0556\\u0559\\u0561-\\u0587\\u05D0-\\u05EA\\u05F0-\\u05F2\\u0621-\\u064A\\u066E-\\u066F\\u0671-\\u06D3\\u06D5\\u06E5-\\u06E6\\u06EE-\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4-\\u07F5\\u07FA\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0972\\u097B-\\u097F\\u0985-\\u098C\\u098F-\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC-\\u09DD\\u09DF-\\u09E1\\u09F0-\\u09F1\\u0A05-\\u0A0A\\u0A0F-\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32-\\u0A33\\u0A35-\\u0A36\\u0A38-\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2-\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0-\\u0AE1\\u0B05-\\u0B0C\\u0B0F-\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32-\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C-\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99-\\u0B9A\\u0B9C\\u0B9E-\\u0B9F\\u0BA3-\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C33\\u0C35-\\u0C39\\u0C3D\\u0C58-\\u0C59\\u0C60-\\u0C61\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDE\\u0CE0-\\u0CE1\\u0D05-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D28\\u0D2A-\\u0D39\\u0D3D\\u0D60-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32-\\u0E33\\u0E40-\\u0E46\\u0E81-\\u0E82\\u0E84\\u0E87-\\u0E88\\u0E8A\\u0E8D\\u0E94-\\u0E97\\u0E99-\\u0E9F\\u0EA1-\\u0EA3\\u0EA5\\u0EA7\\u0EAA-\\u0EAB\\u0EAD-\\u0EB0\\u0EB2-\\u0EB3\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDD\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8B\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065-\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10D0-\\u10FA\\u10FC\\u1100-\\u1159\\u115F-\\u11A2\\u11A8-\\u11F9\\u1200-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F4\\u1401-\\u166C\\u166F-\\u1676\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F0\\u1700-\\u170C\\u170E-\\u1711\\u1720-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1877\\u1880-\\u18A8\\u18AA\\u1900-\\u191C\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19A9\\u19C1-\\u19C7\\u1A00-\\u1A16\\u1B05-\\u1B33\\u1B45-\\u1B4B\\u1B83-\\u1BA0\\u1BAE-\\u1BAF\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u2094\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2119-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u212D\\u212F-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2C2E\\u2C30-\\u2C5E\\u2C60-\\u2C6F\\u2C71-\\u2C7D\\u2C80-\\u2CE4\\u2D00-\\u2D25\\u2D30-\\u2D65\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2E2F\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312D\\u3131-\\u318E\\u31A0-\\u31B7\\u31F0-\\u31FF\\u3400\\u4DB5\\u4E00\\u9FC3\\uA000-\\uA48C\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A-\\uA62B\\uA640-\\uA65F\\uA662-\\uA66E\\uA67F-\\uA697\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA78C\\uA7FB-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA90A-\\uA925\\uA930-\\uA946\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAC00-\\uD7A3\\uF900-\\uFA2D\\uFA30-\\uFA6A\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40-\\uFB41\\uFB43-\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDFB\\uFE70-\\uFE74\\uFE76-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC0-9@+\";\n\t\t\tconst reg = new RegExp(\"([\" + chars + \"]+[\\-\" + chars + \"]*[\" + chars + \"]+)|([\" + chars + \"]+)\", \"ig\");\n\t\t\tlet res = input.match(reg);\n\t\t\tif (!res) {\n\t\t\t\treturn [];\n\t\t\t}\n\n\t\t\tlet word;\n\t\t\twhile (true) {\n\t\t\t\tword = res.pop();\n\n\t\t\t\tif (!word) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tlet item;\n\t\t\t\tfor (let i = 0; i < words.length; i++) {\n\t\t\t\t\tif (words[i].category.toLowerCase() == word.toLowerCase()) {\n\t\t\t\t\t\titem = words[i];\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tif (item) {\n\t\t\t\t\titem.value++;\n\t\t\t\t\tif (!this.isCapitalized(word)) {\n\t\t\t\t\t\titem.category = word;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\twords.push({\n\t\t\t\t\t\tcategory: word,\n\t\t\t\t\t\tvalue: 1\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tlet excludeWords = this.get(\"excludeWords\");\n\n\t\t\tconst minValue = this.get(\"minValue\", 1);\n\t\t\tconst minWordLength = this.get(\"minWordLength\", 1);\n\n\t\t\tif (minValue > 1 || minWordLength > 1 || (excludeWords && excludeWords.length > 0)) {\n\t\t\t\tfor (let i = words.length - 1; i >= 0; i--) {\n\t\t\t\t\tlet w = words[i];\n\t\t\t\t\tlet word = w.category;\n\n\t\t\t\t\tif (w.value < minValue) {\n\t\t\t\t\t\twords.splice(i, 1);\n\t\t\t\t\t}\n\t\t\t\t\tif (word.length < minWordLength) {\n\t\t\t\t\t\twords.splice(i, 1);\n\t\t\t\t\t}\n\t\t\t\t\tif (excludeWords && excludeWords.indexOf(word) !== -1) {\n\t\t\t\t\t\twords.splice(i, 1);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\twords.sort(function (a, b) {\n\t\t\t\tif (a.value == b.value) {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t\telse if (a.value > b.value) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tconst maxCount = this.get(\"maxCount\", Infinity);\n\t\t\tif (words.length > maxCount) {\n\t\t\t\twords = words.slice(0, maxCount);\n\t\t\t}\n\t\t}\n\n\t\treturn words;\n\t}\n\t/**\n* Checks if word is capitalized (starts with an uppercase) or not.\n*\n* @ignore\n* @param   word  Word\n* @return        Capitalized?\n*/\n\tpublic isCapitalized(word: string): boolean {\n\t\tlet lword = word.toLowerCase();\n\t\treturn word[0] != lword[0]\n\t\t\t&& word.substr(1) == lword.substr(1)\n\t\t\t&& word != lword;\n\t}\n\n\tprotected _hasColor(x: number, y: number, w: number, h: number, cols: number): boolean {\n\t\tconst buffer = this._buffer;\n\t\tif (buffer) {\n\t\t\tfor (let r = y; r < y + h; r += 4) {\n\t\t\t\tfor (let c = x; c < x + w; c += 4) {\n\t\t\t\t\tlet i = ((r + 1) * cols - (cols - c));\n\t\t\t\t\tif (buffer[i] != 0) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn false\n\t}\n}\n", "import * as m from \"./../../dist/es2015/wc.js\";\nexport const am5wc = m;"], "names": ["WordCloudDefaultTheme", "Theme", "setupDefaultRules", "super", "ic", "this", "_root", "interfaceColors", "r", "rule", "bind", "setAll", "width", "height", "minFontSize", "maxFontSize", "excludeWords", "angles", "min<PERSON><PERSON><PERSON><PERSON><PERSON>", "step", "randomness", "autoFit", "animationEasing", "text", "centerX", "centerY", "position", "lineHeight", "populateText", "setup", "target", "set", "Rectangle", "new", "fill", "get", "fillOpacity", "WordCloud", "Series", "children", "push", "Container", "layer", "opacity", "_make<PERSON><PERSON><PERSON>", "_afterNew", "_defaultThemes", "fields", "_setDefault", "events", "on", "_currentIndex", "dataItems", "length", "<PERSON><PERSON><PERSON><PERSON>", "dataItem", "label", "labels", "make", "_setDataItem", "<PERSON><PERSON><PERSON><PERSON>", "_ghost<PERSON><PERSON>r", "Color", "fromHex", "fontWeight", "List", "Template", "Label", "_new", "themeTags", "template", "processDataItem", "colors", "setRaw", "next", "_prepare<PERSON><PERSON><PERSON><PERSON>", "isDirty", "data", "_getWords", "_dirty", "_update<PERSON><PERSON><PERSON>n", "resolution", "_renderer", "cols", "Math", "round", "_valuesDirty", "_sizeDirty", "isPrivateDirty", "adjustedFontSize", "getPrivate", "w", "innerWidth", "h", "innerHeight", "smaller", "min", "bigger", "max", "_buffer", "Array", "ceil", "_display", "clear", "_pointSets", "i", "_sets", "setStep", "points", "point", "x", "y", "splice", "sum", "absSum", "valueHigh", "valueLow", "Infinity", "count", "_dataItems", "valueWorking", "abs", "sort", "a", "b", "aValue", "bValue", "value", "setPrivateRaw", "smallerSize", "fontSize", "floor", "angle", "random", "rotation", "_process", "once", "setTimeout", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "boundsToAdd", "_boundsToAdd", "context", "<PERSON><PERSON><PERSON><PERSON>", "top", "left", "right", "bottom", "imageData", "getImageData", "buffer", "n", "c", "undefined", "_processItem", "lw", "lh", "rw", "rh", "pIndex", "intersects", "p", "cx", "cy", "_hasColor", "animate", "key", "to", "duration", "easing", "appear", "setPrivate", "disposeDataItem", "removeValue", "dispose", "input", "words", "chars", "reg", "RegExp", "word", "res", "match", "pop", "item", "category", "toLowerCase", "isCapitalized", "minValue", "indexOf", "maxCount", "slice", "lword", "substr", "classNames", "concat", "className", "am5wc"], "sourceRoot": ""}