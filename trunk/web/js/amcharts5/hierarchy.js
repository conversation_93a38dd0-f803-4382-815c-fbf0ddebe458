(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[3583],{8447:function(t,e,n){"use strict";n.r(e),n.d(e,{BreadcrumbBar:function(){return p},DefaultTheme:function(){return l},ForceDirected:function(){return rt},Hierarchy:function(){return C},HierarchyLink:function(){return z},HierarchyNode:function(){return b},LinkedHierarchy:function(){return R},LinkedHierarchyNode:function(){return M},Pack:function(){return ot},Partition:function(){return ut},Sunburst:function(){return gt},Tree:function(){return _t},Treemap:function(){return Mt},VoronoiTreemap:function(){return St}});var i=n(3409),r=n(6245),a=n(2754),o=n(3783),s=n(9395);class l extends i.Q{setupDefaultRules(){super.setupDefaultRules();const t=this._root.interfaceColors,e=this._root.gridLayout,n=this.rule.bind(this);n("Hierarchy").setAll({legendLabelText:"{category}",legendValueText:"{sum.formatNumber('#.#')}",width:r.AQ,height:r.AQ,colors:a.U.new(this._root,{step:2}),downDepth:1,initialDepth:5,singleBranchOnly:!0,maskContent:!0,animationEasing:s.out(s.cubic)}),n("HierarchyNode").setAll({toggleKey:"disabled",setStateOnChildren:!0,position:"absolute",isMeasured:!1,cursorOverStyle:"pointer",tooltipText:"{category}: {sum}"}),n("HierarchyNode",["last"]).setAll({cursorOverStyle:"default"});{const e=n("Label",["hierarchy","node"]);e.setAll({centerX:r.CI,centerY:r.CI,position:"absolute",paddingBottom:1,paddingTop:1,paddingRight:4,paddingLeft:4,text:"{category}",populateText:!0,oversizedBehavior:"fit",minScale:.3}),(0,o.v)(e,"fill",t,"alternativeText")}{const e=n("HierarchyLink");e.setAll({isMeasured:!1,position:"absolute",strokeWidth:1,strokeOpacity:1,strength:.9,distance:1.1}),(0,o.v)(e,"stroke",t,"grid")}n("Circle",["linkedhierarchy","shape"]).setAll({position:"absolute",fillOpacity:1,strokeOpacity:0,radius:15,tooltipY:0}),n("Circle",["linkedhierarchy","shape","outer"]).setAll({position:"absolute",opacity:1,fillOpacity:0,strokeDasharray:0,strokeOpacity:1,radius:15,scale:1.1,interactive:!1}),n("Circle",["linkedhierarchy","shape","outer"]).states.create("disabled",{opacity:1,scale:1.1,strokeDasharray:2}),n("Circle",["linkedhierarchy","shape","outer"]).states.create("hoverDisabled",{scale:1.2,opacity:1,strokeDasharray:2}),n("Circle",["linkedhierarchy","shape","outer"]).states.create("hover",{scale:1.05,strokeDasharray:0}),n("Circle",["linkedhierarchy","shape","outer"]).states.create("hidden",{opacity:0,strokeDasharray:0}),n("BreadcrumbBar").setAll({paddingLeft:8,layout:e});{const e=n("Label",["breadcrumb"]);e.setAll({paddingRight:4,paddingLeft:4,cursorOverStyle:"pointer",populateText:!0,text:"{category}:"}),(0,o.v)(e,"fill",t,"primaryButton")}{const e=n("Label",["breadcrumb"]).states.create("hover",{});(0,o.v)(e,"fill",t,"primaryButtonHover")}{const e=n("Label",["breadcrumb"]).states.create("down",{stateAnimationDuration:0});(0,o.v)(e,"fill",t,"primaryButtonDown")}{const e=n("Label",["breadcrumb","last"]);e.setAll({populateText:!0,text:"{category}",fontWeight:"bold",cursorOverStyle:"default"}),(0,o.v)(e,"fill",t,"primaryButton")}{const e=n("RoundedRectangle",["breadcrumb","label","background"]);e.setAll({fillOpacity:0}),(0,o.v)(e,"fill",t,"background")}n("Partition").setAll({downDepth:1,upDepth:0,initialDepth:5}),n("HierarchyNode",["partition"]).setAll({setStateOnChildren:!1}),n("HierarchyNode",["partition"]).states.create("hidden",{opacity:1,visible:!0});{const e=n("Label",["partition","node"]);e.setAll({x:r.CI,y:r.CI,centerY:r.CI,centerX:r.CI,paddingBottom:1,paddingTop:1,paddingLeft:1,paddingRight:1,rotation:90,populateText:!0,text:"{category}",oversizedBehavior:"fit",minScale:.4}),(0,o.v)(e,"fill",t,"alternativeText")}n("Label",["horizontal","partition","node"]).setAll({rotation:0});{const e=n("RoundedRectangle",["partition","node","shape"]);e.setAll({strokeOpacity:1,strokeWidth:1,cornerRadiusBR:0,cornerRadiusTR:0,cornerRadiusBL:0,cornerRadiusTL:0}),(0,o.v)(e,"stroke",t,"background")}n("RoundedRectangle",["partition","node","shape","last"]).setAll({fillOpacity:.75}),n("Sunburst").setAll({singleBranchOnly:!0}),n("HierarchyNode",["sunburst"]).setAll({setStateOnChildren:!1}),n("HierarchyNode",["sunburst"]).states.create("hidden",{opacity:0,visible:!1});{const e=n("Slice",["sunburst","node","shape"]);e.setAll({strokeOpacity:1,strokeWidth:1,cornerRadius:0}),(0,o.v)(e,"stroke",t,"background")}n("Slice",["sunburst","node","shape","last"]).setAll({fillOpacity:.75});{const e=n("RadialLabel",["sunburst","node"]);e.setAll({x:0,y:0,textType:"radial",paddingBottom:1,paddingTop:1,paddingLeft:1,paddingRight:1,centerX:r.CI,populateText:!0,text:"{category}",oversizedBehavior:"fit",minScale:.4,baseRadius:r.CI,rotation:0}),(0,o.v)(e,"fill",t,"alternativeText")}n("ForceDirected").setAll({minRadius:(0,r.aQ)(1),maxRadius:(0,r.aQ)(8),initialFrames:500,centerStrength:.8,manyBodyStrength:-14,velocityDecay:.5,linkWithStrength:.5,showOnFrame:10,singleBranchOnly:!1,upDepth:1/0,downDepth:1,initialDepth:5,topDepth:0}),n("Tree").setAll({orientation:"vertical",paddingLeft:20,paddingRight:20,paddingTop:20,paddingBottom:20,singleBranchOnly:!1,upDepth:1/0,downDepth:1,initialDepth:5,topDepth:0}),n("Pack").setAll({paddingLeft:20,paddingTop:20,paddingBottom:20,paddingRight:20,nodePadding:0});{const e=n("Label",["pack","node"]);e.setAll({centerY:r.CI,centerX:r.CI,paddingBottom:1,paddingTop:1,paddingLeft:1,paddingRight:1,populateText:!0,text:"{category}",oversizedBehavior:"fit",minScale:.4}),(0,o.v)(e,"fill",t,"alternativeText")}{const e=n("Circle",["pack","node","shape"]);e.setAll({strokeOpacity:.5,fillOpacity:.8,strokeWidth:1}),(0,o.v)(e,"stroke",t,"background")}n("LinkedHierarchyNode").setAll({draggable:!0}),n("LinkedHierarchyNode").states.create("hidden",{scale:0,opacity:0,visible:!1}),n("Treemap").setAll({upDepth:0,layoutAlgorithm:"squarify"});{const e=n("Label",["treemap","node"]);e.setAll({x:r.CI,y:r.CI,centerY:r.CI,centerX:r.CI,paddingBottom:1,paddingTop:1,paddingLeft:1,paddingRight:1,populateText:!0,text:"{category}",oversizedBehavior:"fit",minScale:.4}),(0,o.v)(e,"fill",t,"alternativeText")}n("HierarchyNode",["treemap","node"]).setAll({tooltipY:(0,r.aQ)(40),isMeasured:!1,position:"absolute"});{const e=n("RoundedRectangle",["treemap","node","shape"]);e.setAll({strokeOpacity:1,strokeWidth:1,cornerRadiusBR:0,cornerRadiusTR:0,cornerRadiusBL:0,cornerRadiusTL:0,fillOpacity:1}),(0,o.v)(e,"stroke",t,"background")}n("VoronoiTreemap").setAll({type:"polygon",minWeightRatio:.005,convergenceRatio:.005,maxIterationCount:100,singleBranchOnly:!0});{const e=n("Graphics",["voronoitreemap","node","shape"]);e.setAll({strokeOpacity:1,strokeWidth:1,fillOpacity:1}),(0,o.v)(e,"stroke",t,"background")}n("Polygon",["hierarchy","node","shape","depth1"]).setAll({strokeWidth:3});{const e=n("Label",["voronoitreemap"]);e.setAll({centerY:r.CI,centerX:r.CI,paddingBottom:1,paddingTop:1,paddingLeft:1,paddingRight:1,populateText:!0,text:"{category}",oversizedBehavior:"fit",minScale:.4,layer:30}),(0,o.v)(e,"fill",t,"alternativeText")}}}var h=n(8777),u=n(962),c=n(3497),d=n(5769),f=n(7144),g=n(7652);class p extends h.W{constructor(){super(...arguments),Object.defineProperty(this,"labels",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>u._._new(this._root,{themeTags:g.mergeTags(this.labels.template.get("themeTags",[]),["label"]),background:c.c.new(this._root,{themeTags:["background"]})},[this.labels.template])))}),Object.defineProperty(this,"_disposer",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}makeLabel(t){const e=this.labels.make();return e._setDataItem(t),e.states.create("hover",{}),e.states.create("down",{}),e.events.on("click",(()=>{const e=this.get("series");e&&e.selectDataItem(t)})),this.labels.push(e),e}_afterNew(){this._defaultThemes.push(l.new(this._root)),this._settings.themeTags=g.mergeTags(this._settings.themeTags,["breadcrumb"]),super._afterNew()}_changed(){if(super._changed(),this.isDirty("series")){const t=this.get("series"),e=this._prevSettings.series;t!=e?this._disposer=t.events.on("dataitemselected",(t=>{this._handleDataItem(t.dataItem)})):e&&this._disposer&&this._disposer.dispose(),this._handleDataItem(t.get("selectedDataItem"))}}_handleDataItem(t){if(this.set("minHeight",this.height()),this.children.clear(),this.labels.clear(),t){let e=t;for(;e;){let n=this.makeLabel(e);e==t&&n.addTag("last"),this.children.moveValue(n,0),e=e.get("parent")}}}}Object.defineProperty(p,"className",{enumerable:!0,configurable:!0,writable:!0,value:"BreadcrumbBar"}),Object.defineProperty(p,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:h.W.classNames.concat([p.className])});var v=n(7582),m=n(3399),y=n(9361);class b extends h.W{constructor(){super(...arguments),Object.defineProperty(this,"series",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_clickDisposer",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_afterNew(){super._afterNew(),this.states.create("disabled",{}),this.states.create("hover",{}),this.states.create("hoverDisabled",{}),this.on("disabled",(()=>{const t=this.dataItem;if(!t.get("children"))return void this.set("disabled",!0);const e=this.get("disabled"),n=this.series;t&&n&&t.get("disabled")!=e&&(e?n.disableDataItem(t):n.enableDataItem(t,n.get("downDepth",1),0))}))}_changed(){super._changed(),this.isDirty("toggleKey")&&("disabled"==this.get("toggleKey")?this._clickDisposer=this.events.on("click",(()=>{if(!this._isDragging){let t=this.series;t&&t.selectDataItem(this.dataItem)}})):this._clickDisposer&&this._clickDisposer.dispose())}}Object.defineProperty(b,"className",{enumerable:!0,configurable:!0,writable:!0,value:"HierarchyNode"}),Object.defineProperty(b,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:h.W.classNames.concat([b.className])});var w=n(5071),x=n(5040);function _(t){var e=0,n=t.children,i=n&&n.length;if(i)for(;--i>=0;)e+=n[i].value;else e=1;t.value=e}function k(t,e){t instanceof Map?(t=[void 0,t],void 0===e&&(e=N)):void 0===e&&(e=D);for(var n,i,r,a,o,s=new I(t),l=[s];n=l.pop();)if((r=e(n.data))&&(o=(r=Array.from(r)).length))for(n.children=r,a=o-1;a>=0;--a)l.push(i=r[a]=new I(r[a])),i.parent=n,i.depth=n.depth+1;return s.eachBefore(A)}function D(t){return t.children}function N(t){return Array.isArray(t)?t[1]:null}function P(t){void 0!==t.data.value&&(t.value=t.data.value),t.data=t.data.data}function A(t){var e=0;do{t.height=e}while((t=t.parent)&&t.height<++e)}function I(t){this.data=t,this.depth=this.height=0,this.parent=null}I.prototype=k.prototype={constructor:I,count:function(){return this.eachAfter(_)},each:function(t,e){let n=-1;for(const i of this)t.call(e,i,++n,this);return this},eachAfter:function(t,e){for(var n,i,r,a=this,o=[a],s=[],l=-1;a=o.pop();)if(s.push(a),n=a.children)for(i=0,r=n.length;i<r;++i)o.push(n[i]);for(;a=s.pop();)t.call(e,a,++l,this);return this},eachBefore:function(t,e){for(var n,i,r=this,a=[r],o=-1;r=a.pop();)if(t.call(e,r,++o,this),n=r.children)for(i=n.length-1;i>=0;--i)a.push(n[i]);return this},find:function(t,e){let n=-1;for(const i of this)if(t.call(e,i,++n,this))return i},sum:function(t){return this.eachAfter((function(e){for(var n=+t(e.data)||0,i=e.children,r=i&&i.length;--r>=0;)n+=i[r].value;e.value=n}))},sort:function(t){return this.eachBefore((function(e){e.children&&e.children.sort(t)}))},path:function(t){for(var e=this,n=function(t,e){if(t===e)return t;var n=t.ancestors(),i=e.ancestors(),r=null;for(t=n.pop(),e=i.pop();t===e;)r=t,t=n.pop(),e=i.pop();return r}(e,t),i=[e];e!==n;)e=e.parent,i.push(e);for(var r=i.length;t!==n;)i.splice(r,0,t),t=t.parent;return i},ancestors:function(){for(var t=this,e=[t];t=t.parent;)e.push(t);return e},descendants:function(){return Array.from(this)},leaves:function(){var t=[];return this.eachBefore((function(e){e.children||t.push(e)})),t},links:function(){var t=this,e=[];return t.each((function(n){n!==t&&e.push({source:n.parent,target:n})})),e},copy:function(){return k(this).eachBefore(P)},[Symbol.iterator]:function*(){var t,e,n,i,r=this,a=[r];do{for(t=a.reverse(),a=[];r=t.pop();)if(yield r,e=r.children)for(n=0,i=e.length;n<i;++n)a.push(e[n])}while(a.length)}};class C extends m.F{constructor(){super(...arguments),Object.defineProperty(this,"nodesContainer",{enumerable:!0,configurable:!0,writable:!0,value:this.children.push(h.W.new(this._root,{isMeasured:!1}))}),Object.defineProperty(this,"_rootNode",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_treeData",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_index",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"hierarchy"}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>b.new(this._root,{themeTags:g.mergeTags(this.nodes.template.get("themeTags",[]),[this._tag,"hierarchy","node"])},this.nodes.template)))}),Object.defineProperty(this,"labels",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>u._.new(this._root,{themeTags:g.mergeTags(this.labels.template.get("themeTags",[]),[this._tag])},this.labels.template)))}),Object.defineProperty(this,"_currentDownDepth",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}makeNode(t){const e=t.get("childData"),n=this.nodes.make();n.series=this,n._setDataItem(t),this.nodes.push(n),t.setRaw("node",n);const i=this.labels.make();i._setDataItem(t),t.setRaw("label",i),this.labels.push(i),e&&0!=e.length||n.addTag("last");const r=t.get("depth");return n.addTag("depth"+r),this.nodesContainer.children.push(n),n.children.push(i),n}_afterNew(){this._defaultThemes.push(l.new(this._root)),this.fields.push("category","childData","disabled","fill"),this.children.push(this.bulletsContainer),super._afterNew()}_prepareChildren(){if(super._prepareChildren(),this._valuesDirty){this._treeData={};const t=this.dataItems[0];if(t&&(this._makeHierarchyData(this._treeData,t),this._index=0,this._rootNode=k(this._treeData),this._rootNode)){this._rootNode.sum((t=>t.value));const t=this.get("sort");"descending"==t?this._rootNode.sort(((t,e)=>e.value-t.value)):"ascending"==t&&this._rootNode.sort(((t,e)=>t.value-e.value)),this.setPrivateRaw("valueLow",1/0),this.setPrivateRaw("valueHigh",-1/0),this.setPrivateRaw("maxDepth",0),this._updateValues(this._rootNode)}}if((this._valuesDirty||this._sizeDirty)&&this._updateVisuals(),this._sizeDirty){const t=this.get("selectedDataItem");t&&this._zoom(t)}}_changed(){super._changed(),this.isDirty("selectedDataItem")&&this._selectDataItem(this.get("selectedDataItem"))}_updateVisuals(){this._rootNode&&this._updateNodes(this._rootNode)}_updateNodes(t){const e=t.data.dataItem;if(e){this._updateNode(e),this.bullets.length>0&&!e.bullets&&this._makeBullets(e);const n=t.children;n&&w.each(n,(t=>{this._updateNodes(t)}))}}_updateNode(t){}getDataItemById(t){return this._getDataItemById(this.dataItems,t)}_getDataItemById(t,e){let n;return w.each(t,(t=>{t.get("id")==e&&(n=t);const i=t.get("children");if(i){let t=this._getDataItemById(i,e);t&&(n=t)}})),n}_handleBullets(t){w.each(t,(t=>{const e=t.bullets;e&&(w.each(e,(t=>{t.dispose()})),t.bullets=void 0);const n=t.get("children");n&&this._handleBullets(n)})),this._updateVisuals()}_onDataClear(){super._onDataClear();const t=this.get("colors");t&&t.reset();const e=this.get("patterns");e&&e.reset()}processDataItem(t){super.processDataItem(t);const e=t.get("childData"),n=this.get("colors"),i=this.get("patterns"),r=this.get("topDepth",0);t.get("parent")||(t.setRaw("depth",0),n&&0==r&&null==t.get("fill")&&(t.setRaw("fill",n.next()),i&&t.setRaw("fillPattern",i.next())));let a=t.get("depth");const o=this.get("initialDepth",1);if(this.makeNode(t),this._processDataItem(t),e){const r=[];t.setRaw("children",r),w.each(e,(e=>{const o=new y.z(this,e,this._makeDataItem(e));r.push(o),o.setRaw("parent",t),o.setRaw("depth",a+1),1==this.dataItems.length&&0==a?(n&&null==o.get("fill")&&o.setRaw("fill",n.next()),i&&null==o.get("fillPattern")&&o.setRaw("fillPattern",i.next())):(null==o.get("fill")&&o.setRaw("fill",t.get("fill")),null==o.get("fillPattern")&&o.setRaw("fillPattern",t.get("fillPattern"))),this.processDataItem(o)}))}const s=t.get("children");s&&0!=s.length||t.get("node").setAll({toggleKey:void 0}),null==t.get("disabled")&&a>=r+o&&this.disableDataItem(t,0)}addChildData(t,e){const n=t.dataContext,i=this.get("childDataField");let r=n[i];r?r.push(...e):(r=e,n[i]=r);let a=t.get("children");a||(a=[],t.set("children",a));const o=t.get("node");o&&o.set("toggleKey","disabled");let s=t.get("depth");w.each(r,(e=>{let n=!1;if(w.eachContinue(a,(t=>t.dataContext!=e||(n=!0,!1))),!n){const n=new y.z(this,e,this._makeDataItem(e));a.push(n),n.setRaw("parent",t),n.setRaw("depth",s+1),null==n.get("fill")&&n.setRaw("fill",t.get("fill")),this.processDataItem(n)}}))}_processDataItem(t){}_updateValues(t){const e=t.data.dataItem;if(t.depth>this.getPrivate("maxDepth")&&this.setPrivateRaw("maxDepth",t.depth),e){e.setRaw("d3HierarchyNode",t),t.index=this._index,this._index++,this.root.events.once("frameended",(()=>{e.get("node").set("disabled",e.get("disabled"))}));let n=t.data.value,i=t.value;if(null!=n&&(i=n,t.value=i),x.isNumber(i)){e.setRaw("sum",i),e.setRaw("valuePercentTotal",i/this.dataItems[0].get("sum")*100);let t=100;const n=e.get("parent");n&&(t=i/n.get("sum")*100),e.get("label").text.markDirtyText(),e.setRaw("valuePercent",t);const r=this.getPrivate("valueLow");null!=r&&r>i&&this.setPrivateRaw("valueLow",i);const a=this.getPrivate("valueHigh");null!=a&&a<i&&this.setPrivateRaw("valueHigh",i)}this.updateLegendValue(e)}const n=t.children;n&&w.each(n,(t=>{this._updateValues(t)}))}_makeHierarchyData(t,e){t.dataItem=e;const n=e.get("children");if(n){const i=[];t.children=i,w.each(n,(t=>{const e={};i.push(e),this._makeHierarchyData(e,t)}));const r=e.get("valueWorking");x.isNumber(r)&&(t.value=r)}else{const n=e.get("valueWorking");x.isNumber(n)&&(t.value=n)}}disposeDataItem(t){super.disposeDataItem(t);const e=t.get("node");e&&(this.nodes.removeValue(e),e.dispose());const n=t.get("label");n&&(this.labels.removeValue(n),n.dispose());const i=t.get("children");i&&w.each(i,(t=>{this.disposeDataItem(t)}))}hideDataItem(t,e){const n=Object.create(null,{hideDataItem:{get:()=>super.hideDataItem}});return(0,v.mG)(this,void 0,void 0,(function*(){const i=[n.hideDataItem.call(this,t,e)],r=this.states.create("hidden",{});if(!x.isNumber(e)){const t="stateAnimationDuration";e=r.get(t,this.get(t,0))}const a="stateAnimationEasing",o=r.get(a,this.get(a)),s=t.get("children");s&&0!=s.length||!x.isNumber(t.get("value"))||i.push(t.animate({key:"valueWorking",to:0,duration:e,easing:o}).waitForStop());const l=t.get("node");l.hide(),l.hideTooltip(),s&&w.each(s,(t=>{i.push(this.hideDataItem(t))})),yield Promise.all(i)}))}showDataItem(t,e){const n=Object.create(null,{showDataItem:{get:()=>super.showDataItem}});return(0,v.mG)(this,void 0,void 0,(function*(){const i=[n.showDataItem.call(this,t,e)];x.isNumber(e)||(e=this.get("stateAnimationDuration",0));const r=this.get("stateAnimationEasing"),a=t.get("children");a&&0!=a.length||!x.isNumber(t.get("value"))||i.push(t.animate({key:"valueWorking",to:t.get("value"),duration:e,easing:r}).waitForStop()),t.get("node").show(),a&&w.each(a,(t=>{i.push(this.showDataItem(t))})),yield Promise.all(i)}))}enableDataItem(t,e,n,i){null==n&&(n=0),null==e&&(e=1),t.set("disabled",!1),t.get("node").set("disabled",!1),t.isHidden()||t.get("node").show(i);const r=this.get("topDepth",0);if(t.get("depth")<r&&t.get("node").hide(0),0==n){const e=this.get("upDepth",1/0);let n=t,i=0;for(;void 0!==n;)i>e&&n.get("node").hide(),n=n.get("parent"),i++}let a=t.get("children");a&&(n<e-1?w.each(a,(t=>{const r=this.get("disabledField");r?1!=t.dataContext[r]?this.enableDataItem(t,e,n+1,i):this.disableDataItem(t):this.enableDataItem(t,e,n+1,i)})):w.each(a,(t=>{t.isHidden()||(t.get("node").show(i),t.get("node").states.applyAnimate("disabled"),t.set("disabled",!0),this.disableDataItem(t))})))}disableDataItem(t,e){t.set("disabled",!0);let n=t.get("children");n&&w.each(n,(t=>{this.disableDataItem(t,e),t.get("node").hide(e)}))}_selectDataItem(t,e,n){if(t){if(!n){const e="dataitemselected";this.events.dispatch(e,{type:e,target:this,dataItem:t})}let i=this.getPrivate("maxDepth",1);const r=this.get("topDepth",0);null==e&&(e=Math.min(this.get("downDepth",1),i-t.get("depth")));const a=t.get("d3HierarchyNode");let o=a.depth;this.inited||(e=Math.min(this.get("initialDepth",1),i-r),e=Math.max(0,e)),this._currentDownDepth=e,o+e>i&&(e=i-o),o<r&&(e+=r-o,o=r);const s=t.get("children");s&&s.length>0?(e>0?this.enableDataItem(t,e):(t.get("node").show(),w.each(s,(t=>{t.get("node").hide()}))),a.depth<r&&t.get("node").hide(0),this.get("singleBranchOnly")&&this._handleSingle(t)):this.enableDataItem(t,e),this._root.events.once("frameended",(()=>{this._zoom(t)}))}}_zoom(t){}_handleSingle(t){const e=t.get("parent");if(e){const n=e.get("children");n&&w.each(n,(e=>{e!=t&&this.disableDataItem(e)}))}}selectDataItem(t){const e=t.get("parent"),n=this.getPrivate("maxDepth",1);if(this.get("selectedDataItem")==t)if(e)this.set("selectedDataItem",e);else{let e=Math.min(this.get("downDepth",1),n-t.get("depth"));this._currentDownDepth==e&&(e=Math.min(this.get("initialDepth",1),n-this.get("topDepth",0))),this._selectDataItem(t,e)}else this.set("selectedDataItem",t)}_makeBullet(t,e,n){const i=super._makeBullet(t,e,n);if(i){const e=i.get("sprite"),n=t.get("node");e&&(n.children.push(e),n.on("width",(()=>{this._positionBullet(i)})),n.on("height",(()=>{this._positionBullet(i)})))}return i}_positionBullet(t){const e=t.get("sprite");if(e){const n=e.dataItem,i=t.get("locationX",.5),r=t.get("locationY",.5),a=n.get("node");e.set("x",a.width()*i),e.set("y",a.height()*r)}}hoverDataItem(t){const e=t.get("node");e&&!e.isHidden()&&e.hover()}unhoverDataItem(t){const e=t.get("node");e&&e.unhover()}}Object.defineProperty(C,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Hierarchy"}),Object.defineProperty(C,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:m.F.classNames.concat([C.className])});class M extends b{_afterNew(){super._afterNew(),this.states.create("disabled",{}),this.states.create("hover",{}),this.states.create("hoverDisabled",{})}_updateLinks(t){const e=this.dataItem;if(e){let n=e.get("links");w.each(n,(e=>{let n=e.get("source"),i=e.get("target");n&&i&&(n.get("node").isHidden()||i.get("node").isHidden()?e.hide(t):e.show(t))}))}}_prepareChildren(){super._prepareChildren(),this.isDirty("disabled")&&this._updateLinks()}_onHide(t){super._onHide(t),this._updateLinks(t)}_onShow(t){super._onShow(t),this._updateLinks(t)}}Object.defineProperty(M,"className",{enumerable:!0,configurable:!0,writable:!0,value:"LinkedHierarchyNode"}),Object.defineProperty(M,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:b.classNames.concat([M.className])});var O=n(1479);class z extends O.T{constructor(){super(...arguments),Object.defineProperty(this,"bullets",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"series",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_handleBullets(t){w.each(this.bullets,(t=>{t.dispose()})),t.each((t=>{const e=t(this._root,this.get("source"),this.get("target"));if(e){this.bullets.push(e);const t=e.get("sprite");if(this.addDisposer(e.on("locationX",(()=>{this._clear=!0,this.markDirty()}))),t){const e=this.series;e&&e.linksContainer.children.push(t)}}}))}_changed(){var t,e;if(super._changed(),this._clear){let n=this.get("source"),i=this.get("target");if(n&&i){const r=n.get("node"),a=i.get("node"),o=r.x(),s=r.y(),l=a.x(),h=a.y();this._display.moveTo(o,s),this._display.lineTo(l,h);const u=null===(t=r.dataItem)||void 0===t?void 0:t.get("outerCircle").get("radius",0),c=null===(e=a.dataItem)||void 0===e?void 0:e.get("outerCircle").get("radius",0),d=Math.hypot(l-o,h-s),f=d-u-c;w.each(this.bullets,(t=>{const e=t.get("sprite");if(e){const n=t.get("locationX",.5);e.set("x",o+u/d*(l-o)+f/d*(l-o)*n),e.set("y",s+u/d*(h-s)+f/d*(h-s)*n),t.get("autoRotate")&&e.set("rotation",180*Math.atan2(h-s,l-o)/Math.PI+t.get("autoRotateAngle",0))}}))}}}hide(t){return w.each(this.bullets,(e=>{if(e){const n=e.get("sprite");n&&n.hide(t)}})),super.hide()}show(t){return w.each(this.bullets,(e=>{if(e){const n=e.get("sprite");n&&n.show(t)}})),super.show()}_beforeChanged(){if(super._beforeChanged(),this.isDirty("source")){const t=this.get("source");t&&t.get("node").events.on("positionchanged",(()=>{this._markDirtyKey("stroke")}))}if(this.isDirty("target")){const t=this.get("target");t&&t.get("node").events.on("positionchanged",(()=>{this._markDirtyKey("stroke")}))}}dispose(){super.dispose(),w.each(this.bullets,(t=>{t.dispose()})),this.bullets=[]}}Object.defineProperty(z,"className",{enumerable:!0,configurable:!0,writable:!0,value:"HierarchyLink"}),Object.defineProperty(z,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:O.T.classNames.concat([z.className])});var T=n(8035);class R extends C{constructor(){super(...arguments),Object.defineProperty(this,"linkBullets",{enumerable:!0,configurable:!0,writable:!0,value:new f.aV}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>M._new(this._root,{themeTags:g.mergeTags(this.nodes.template.get("themeTags",[]),[this._tag,"linkedhierarchy","hierarchy","node"]),x:this.width()/2,y:this.height()/2},[this.nodes.template])))}),Object.defineProperty(this,"circles",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>T.C._new(this._root,{themeTags:g.mergeTags(this.circles.template.get("themeTags",[]),[this._tag,"shape"])},[this.circles.template])))}),Object.defineProperty(this,"outerCircles",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>T.C._new(this._root,{themeTags:g.mergeTags(this.outerCircles.template.get("themeTags",[]),[this._tag,"outer","shape"])},[this.outerCircles.template])))}),Object.defineProperty(this,"links",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>z._new(this._root,{themeTags:g.mergeTags(this.links.template.get("themeTags",[]),[this._tag,"linkedhierarchy","hierarchy","link"])},[this.links.template])))}),Object.defineProperty(this,"linksContainer",{enumerable:!0,configurable:!0,writable:!0,value:this.children.moveValue(h.W.new(this._root,{}),0)})}_afterNew(){this.fields.push("linkWith","x","y"),this._disposers.push(this.linkBullets.events.onAll((()=>{this.links.each((t=>{t._handleBullets(this.linkBullets)}))}))),super._afterNew()}makeNode(t){const e=super.makeNode(t),n=e.children.moveValue(this.circles.make(),0);this.circles.push(n),e.setPrivate("tooltipTarget",n),t.setRaw("circle",n);const i=e.children.moveValue(this.outerCircles.make(),0);this.outerCircles.push(i),t.setRaw("outerCircle",i);const r=t.get("label");n.on("radius",(()=>{const t=2*n.get("radius",this.width());r.setAll({maxWidth:t,maxHeight:t}),i.set("radius",t/2),this._handleRadiusChange()}));const a=2*n.get("radius",this.width());return r.setAll({maxWidth:a,maxHeight:a}),n._setDataItem(t),i._setDataItem(t),e}_handleRadiusChange(){}processDataItem(t){t.setRaw("childLinks",[]),t.setRaw("links",[]),super.processDataItem(t)}_processDataItem(t){super._processDataItem(t);const e=t.get("parent");if(e&&e.get("depth")>=this.get("topDepth")){const n=this.linkDataItems(e,t);t.setRaw("parentLink",n)}const n=t.get("node");this.updateLinkWith(this.dataItems),n._updateLinks(0)}updateLinkWith(t){w.each(t,(t=>{const e=t.get("linkWith");e&&w.each(e,(e=>{const n=this._getDataItemById(this.dataItems,e);n&&this.linkDataItems(t,n)}));const n=t.get("children");n&&this.updateLinkWith(n)}))}_getPoint(t){return{x:t.x,y:t.y}}_animatePositions(t){const e=t.get("node"),n=t.get("d3HierarchyNode"),i=this._getPoint(n),r=this.get("animationDuration",0),a=this.get("animationEasing");e.animate({key:"x",to:i.x,duration:r,easing:a}),e.animate({key:"y",to:i.y,duration:r,easing:a})}_updateNode(t){super._updateNode(t),this._animatePositions(t);const e=t.get("d3HierarchyNode").children;e&&w.each(e,(t=>{this._updateNodes(t)}));const n=t.get("fill"),i=t.get("fillPattern"),r=t.get("circle"),a=t.get("children");r&&(r._setDefault("fill",n),r._setDefault("fillPattern",i),r._setDefault("stroke",n));const o=t.get("outerCircle");o&&(o._setDefault("fill",n),o._setDefault("stroke",n),a&&0!=a.length?o.setPrivate("visible",!0):o.setPrivate("visible",!1))}linkDataItems(t,e,n){let i;const r=t.get("links");r&&w.each(r,(t=>{t.get("target")==e&&(i=t)}));const a=e.get("links");return a&&w.each(a,(e=>{e.get("target")==t&&(i=e)})),i||(i=this.links.make(),i.series=this,this.links.push(i),this.linksContainer.children.push(i),i.set("source",t),i.set("target",e),i._setDataItem(t),i._handleBullets(this.linkBullets),i.set("stroke",t.get("fill")),null!=n&&i.set("strength",n),t.get("childLinks").push(i),w.move(t.get("links"),i),w.move(e.get("links"),i),this._processLink(i,t,e)),i}unlinkDataItems(t,e){let n;const i=t.get("links");i&&w.each(i,(t=>{t&&t.get("target")==e&&(n=t,w.remove(i,n))}));const r=e.get("links");r&&w.each(r,(e=>{e&&e.get("target")==t&&(n=e,w.remove(r,n))})),n&&this._disposeLink(n),this._handleUnlink()}_handleUnlink(){}_disposeLink(t){this.links.removeValue(t),t.dispose()}areLinked(t,e){const n=t.get("links");let i=!1;n&&w.each(n,(t=>{t.get("target")==e&&(i=!0)}));const r=e.get("links");return r&&w.each(r,(e=>{e.get("target")==t&&(i=!0)})),i}_processLink(t,e,n){}disposeDataItem(t){super.disposeDataItem(t);const e=t.get("links");e&&w.each(e,(t=>{this._disposeLink(t)}))}selectDataItem(t){const e=t.get("parent");if(t.get("disabled")){if(e){this.setRaw("selectedDataItem",e);const n="dataitemselected";this.events.dispatch(n,{type:n,target:this,dataItem:e}),this.disableDataItem(t)}}else this.set("selectedDataItem",t)}}Object.defineProperty(R,"className",{enumerable:!0,configurable:!0,writable:!0,value:"LinkedHierarchy"}),Object.defineProperty(R,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:C.classNames.concat([R.className])});var S=n(4138),j=n(7738);const L=4294967296;function H(t){return t.x}function B(t){return t.y}var W=Math.PI*(3-Math.sqrt(5));function F(t){var e,n=1,i=.001,r=1-Math.pow(i,1/300),a=0,o=.6,s=new Map,l=(0,j.HT)(c),h=(0,S.Z)("tick","end"),u=function(){let t=1;return()=>(t=(1664525*t+1013904223)%L)/L}();function c(){d(),h.call("tick",e),n<i&&(l.stop(),h.call("end",e))}function d(i){var l,h,u=t.length;void 0===i&&(i=1);for(var c=0;c<i;++c)for(n+=(a-n)*r,s.forEach((function(t){t(n)})),l=0;l<u;++l)null==(h=t[l]).fx?h.x+=h.vx*=o:(h.x=h.fx,h.vx=0),null==h.fy?h.y+=h.vy*=o:(h.y=h.fy,h.vy=0);return e}function f(){for(var e,n=0,i=t.length;n<i;++n){if((e=t[n]).index=n,null!=e.fx&&(e.x=e.fx),null!=e.fy&&(e.y=e.fy),isNaN(e.x)||isNaN(e.y)){var r=10*Math.sqrt(.5+n),a=n*W;e.x=r*Math.cos(a),e.y=r*Math.sin(a)}(isNaN(e.vx)||isNaN(e.vy))&&(e.vx=e.vy=0)}}function g(e){return e.initialize&&e.initialize(t,u),e}return null==t&&(t=[]),f(),e={tick:d,restart:function(){return l.restart(c),e},stop:function(){return l.stop(),e},nodes:function(n){return arguments.length?(t=n,f(),s.forEach(g),e):t},alpha:function(t){return arguments.length?(n=+t,e):n},alphaMin:function(t){return arguments.length?(i=+t,e):i},alphaDecay:function(t){return arguments.length?(r=+t,e):+r},alphaTarget:function(t){return arguments.length?(a=+t,e):a},velocityDecay:function(t){return arguments.length?(o=1-t,e):1-o},randomSource:function(t){return arguments.length?(u=t,s.forEach(g),e):u},force:function(t,n){return arguments.length>1?(null==n?s.delete(t):s.set(t,g(n)),e):s.get(t)},find:function(e,n,i){var r,a,o,s,l,h=0,u=t.length;for(null==i?i=1/0:i*=i,h=0;h<u;++h)(o=(r=e-(s=t[h]).x)*r+(a=n-s.y)*a)<i&&(l=s,i=o);return l},on:function(t,n){return arguments.length>1?(h.on(t,n),e):h.on(t)}}}function V(t,e,n,i){if(isNaN(e)||isNaN(n))return t;var r,a,o,s,l,h,u,c,d,f=t._root,g={data:i},p=t._x0,v=t._y0,m=t._x1,y=t._y1;if(!f)return t._root=g,t;for(;f.length;)if((h=e>=(a=(p+m)/2))?p=a:m=a,(u=n>=(o=(v+y)/2))?v=o:y=o,r=f,!(f=f[c=u<<1|h]))return r[c]=g,t;if(s=+t._x.call(null,f.data),l=+t._y.call(null,f.data),e===s&&n===l)return g.next=f,r?r[c]=g:t._root=g,t;do{r=r?r[c]=new Array(4):t._root=new Array(4),(h=e>=(a=(p+m)/2))?p=a:m=a,(u=n>=(o=(v+y)/2))?v=o:y=o}while((c=u<<1|h)==(d=(l>=o)<<1|s>=a));return r[d]=f,r[c]=g,t}function E(t,e,n,i,r){this.node=t,this.x0=e,this.y0=n,this.x1=i,this.y1=r}function q(t){return t[0]}function Y(t){return t[1]}function X(t,e,n){var i=new G(null==e?q:e,null==n?Y:n,NaN,NaN,NaN,NaN);return null==t?i:i.addAll(t)}function G(t,e,n,i,r,a){this._x=t,this._y=e,this._x0=n,this._y0=i,this._x1=r,this._y1=a,this._root=void 0}function Z(t){for(var e={data:t.data},n=e;t=t.next;)n=n.next={data:t.data};return e}var K=X.prototype=G.prototype;function Q(t){return function(){return t}}function U(t){return 1e-6*(t()-.5)}function J(t){return t.x+t.vx}function $(t){return t.y+t.vy}function tt(t){var e,n,i,r=1,a=1;function o(){for(var t,o,l,h,u,c,d,f=e.length,g=0;g<a;++g)for(o=X(e,J,$).visitAfter(s),t=0;t<f;++t)l=e[t],c=n[l.index],d=c*c,h=l.x+l.vx,u=l.y+l.vy,o.visit(p);function p(t,e,n,a,o){var s=t.data,f=t.r,g=c+f;if(!s)return e>h+g||a<h-g||n>u+g||o<u-g;if(s.index>l.index){var p=h-s.x-s.vx,v=u-s.y-s.vy,m=p*p+v*v;m<g*g&&(0===p&&(m+=(p=U(i))*p),0===v&&(m+=(v=U(i))*v),m=(g-(m=Math.sqrt(m)))/m*r,l.vx+=(p*=m)*(g=(f*=f)/(d+f)),l.vy+=(v*=m)*g,s.vx-=p*(g=1-g),s.vy-=v*g)}}}function s(t){if(t.data)return t.r=n[t.data.index];for(var e=t.r=0;e<4;++e)t[e]&&t[e].r>t.r&&(t.r=t[e].r)}function l(){if(e){var i,r,a=e.length;for(n=new Array(a),i=0;i<a;++i)r=e[i],n[r.index]=+t(r,i,e)}}return"function"!=typeof t&&(t=Q(null==t?1:+t)),o.initialize=function(t,n){e=t,i=n,l()},o.iterations=function(t){return arguments.length?(a=+t,o):a},o.strength=function(t){return arguments.length?(r=+t,o):r},o.radius=function(e){return arguments.length?(t="function"==typeof e?e:Q(+e),l(),o):t},o}function et(t){return t.index}function nt(t,e){var n=t.get(e);if(!n)throw new Error("node not found: "+e);return n}function it(t){var e,n,i,r,a,o,s=et,l=function(t){return 1/Math.min(r[t.source.index],r[t.target.index])},h=Q(30),u=1;function c(i){for(var r=0,s=t.length;r<u;++r)for(var l,h,c,d,f,g,p,v=0;v<s;++v)h=(l=t[v]).source,d=(c=l.target).x+c.vx-h.x-h.vx||U(o),f=c.y+c.vy-h.y-h.vy||U(o),d*=g=((g=Math.sqrt(d*d+f*f))-n[v])/g*i*e[v],f*=g,c.vx-=d*(p=a[v]),c.vy-=f*p,h.vx+=d*(p=1-p),h.vy+=f*p}function d(){if(i){var o,l,h=i.length,u=t.length,c=new Map(i.map(((t,e)=>[s(t,e,i),t])));for(o=0,r=new Array(h);o<u;++o)(l=t[o]).index=o,"object"!=typeof l.source&&(l.source=nt(c,l.source)),"object"!=typeof l.target&&(l.target=nt(c,l.target)),r[l.source.index]=(r[l.source.index]||0)+1,r[l.target.index]=(r[l.target.index]||0)+1;for(o=0,a=new Array(u);o<u;++o)l=t[o],a[o]=r[l.source.index]/(r[l.source.index]+r[l.target.index]);e=new Array(u),f(),n=new Array(u),g()}}function f(){if(i)for(var n=0,r=t.length;n<r;++n)e[n]=+l(t[n],n,t)}function g(){if(i)for(var e=0,r=t.length;e<r;++e)n[e]=+h(t[e],e,t)}return null==t&&(t=[]),c.initialize=function(t,e){i=t,o=e,d()},c.links=function(e){return arguments.length?(t=e,d(),c):t},c.id=function(t){return arguments.length?(s=t,c):s},c.iterations=function(t){return arguments.length?(u=+t,c):u},c.strength=function(t){return arguments.length?(l="function"==typeof t?t:Q(+t),f(),c):l},c.distance=function(t){return arguments.length?(h="function"==typeof t?t:Q(+t),g(),c):h},c}K.copy=function(){var t,e,n=new G(this._x,this._y,this._x0,this._y0,this._x1,this._y1),i=this._root;if(!i)return n;if(!i.length)return n._root=Z(i),n;for(t=[{source:i,target:n._root=new Array(4)}];i=t.pop();)for(var r=0;r<4;++r)(e=i.source[r])&&(e.length?t.push({source:e,target:i.target[r]=new Array(4)}):i.target[r]=Z(e));return n},K.add=function(t){const e=+this._x.call(null,t),n=+this._y.call(null,t);return V(this.cover(e,n),e,n,t)},K.addAll=function(t){var e,n,i,r,a=t.length,o=new Array(a),s=new Array(a),l=1/0,h=1/0,u=-1/0,c=-1/0;for(n=0;n<a;++n)isNaN(i=+this._x.call(null,e=t[n]))||isNaN(r=+this._y.call(null,e))||(o[n]=i,s[n]=r,i<l&&(l=i),i>u&&(u=i),r<h&&(h=r),r>c&&(c=r));if(l>u||h>c)return this;for(this.cover(l,h).cover(u,c),n=0;n<a;++n)V(this,o[n],s[n],t[n]);return this},K.cover=function(t,e){if(isNaN(t=+t)||isNaN(e=+e))return this;var n=this._x0,i=this._y0,r=this._x1,a=this._y1;if(isNaN(n))r=(n=Math.floor(t))+1,a=(i=Math.floor(e))+1;else{for(var o,s,l=r-n||1,h=this._root;n>t||t>=r||i>e||e>=a;)switch(s=(e<i)<<1|t<n,(o=new Array(4))[s]=h,h=o,l*=2,s){case 0:r=n+l,a=i+l;break;case 1:n=r-l,a=i+l;break;case 2:r=n+l,i=a-l;break;case 3:n=r-l,i=a-l}this._root&&this._root.length&&(this._root=h)}return this._x0=n,this._y0=i,this._x1=r,this._y1=a,this},K.data=function(){var t=[];return this.visit((function(e){if(!e.length)do{t.push(e.data)}while(e=e.next)})),t},K.extent=function(t){return arguments.length?this.cover(+t[0][0],+t[0][1]).cover(+t[1][0],+t[1][1]):isNaN(this._x0)?void 0:[[this._x0,this._y0],[this._x1,this._y1]]},K.find=function(t,e,n){var i,r,a,o,s,l,h,u=this._x0,c=this._y0,d=this._x1,f=this._y1,g=[],p=this._root;for(p&&g.push(new E(p,u,c,d,f)),null==n?n=1/0:(u=t-n,c=e-n,d=t+n,f=e+n,n*=n);l=g.pop();)if(!(!(p=l.node)||(r=l.x0)>d||(a=l.y0)>f||(o=l.x1)<u||(s=l.y1)<c))if(p.length){var v=(r+o)/2,m=(a+s)/2;g.push(new E(p[3],v,m,o,s),new E(p[2],r,m,v,s),new E(p[1],v,a,o,m),new E(p[0],r,a,v,m)),(h=(e>=m)<<1|t>=v)&&(l=g[g.length-1],g[g.length-1]=g[g.length-1-h],g[g.length-1-h]=l)}else{var y=t-+this._x.call(null,p.data),b=e-+this._y.call(null,p.data),w=y*y+b*b;if(w<n){var x=Math.sqrt(n=w);u=t-x,c=e-x,d=t+x,f=e+x,i=p.data}}return i},K.remove=function(t){if(isNaN(a=+this._x.call(null,t))||isNaN(o=+this._y.call(null,t)))return this;var e,n,i,r,a,o,s,l,h,u,c,d,f=this._root,g=this._x0,p=this._y0,v=this._x1,m=this._y1;if(!f)return this;if(f.length)for(;;){if((h=a>=(s=(g+v)/2))?g=s:v=s,(u=o>=(l=(p+m)/2))?p=l:m=l,e=f,!(f=f[c=u<<1|h]))return this;if(!f.length)break;(e[c+1&3]||e[c+2&3]||e[c+3&3])&&(n=e,d=c)}for(;f.data!==t;)if(i=f,!(f=f.next))return this;return(r=f.next)&&delete f.next,i?(r?i.next=r:delete i.next,this):e?(r?e[c]=r:delete e[c],(f=e[0]||e[1]||e[2]||e[3])&&f===(e[3]||e[2]||e[1]||e[0])&&!f.length&&(n?n[d]=f:this._root=f),this):(this._root=r,this)},K.removeAll=function(t){for(var e=0,n=t.length;e<n;++e)this.remove(t[e]);return this},K.root=function(){return this._root},K.size=function(){var t=0;return this.visit((function(e){if(!e.length)do{++t}while(e=e.next)})),t},K.visit=function(t){var e,n,i,r,a,o,s=[],l=this._root;for(l&&s.push(new E(l,this._x0,this._y0,this._x1,this._y1));e=s.pop();)if(!t(l=e.node,i=e.x0,r=e.y0,a=e.x1,o=e.y1)&&l.length){var h=(i+a)/2,u=(r+o)/2;(n=l[3])&&s.push(new E(n,h,u,a,o)),(n=l[2])&&s.push(new E(n,i,u,h,o)),(n=l[1])&&s.push(new E(n,h,r,a,u)),(n=l[0])&&s.push(new E(n,i,r,h,u))}return this},K.visitAfter=function(t){var e,n=[],i=[];for(this._root&&n.push(new E(this._root,this._x0,this._y0,this._x1,this._y1));e=n.pop();){var r=e.node;if(r.length){var a,o=e.x0,s=e.y0,l=e.x1,h=e.y1,u=(o+l)/2,c=(s+h)/2;(a=r[0])&&n.push(new E(a,o,s,u,c)),(a=r[1])&&n.push(new E(a,u,s,l,c)),(a=r[2])&&n.push(new E(a,o,c,u,h)),(a=r[3])&&n.push(new E(a,u,c,l,h))}i.push(e)}for(;e=i.pop();)t(e.node,e.x0,e.y0,e.x1,e.y1);return this},K.x=function(t){return arguments.length?(this._x=t,this):this._x},K.y=function(t){return arguments.length?(this._y=t,this):this._y};class rt extends R{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"forcedirected"}),Object.defineProperty(this,"d3forceSimulation",{enumerable:!0,configurable:!0,writable:!0,value:F()}),Object.defineProperty(this,"collisionForce",{enumerable:!0,configurable:!0,writable:!0,value:tt(20)}),Object.defineProperty(this,"linkForce",{enumerable:!0,configurable:!0,writable:!0,value:it()}),Object.defineProperty(this,"_nodes",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_links",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_tick",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_nodesDirty",{enumerable:!0,configurable:!0,writable:!0,value:!1})}_afterNew(){super._afterNew(),this.d3forceSimulation.on("tick",(()=>{this._tick++,this.updateNodePositions()}))}_prepareChildren(){super._prepareChildren(),this.isDirty("showOnFrame")&&this.get("showOnFrame")>this._tick&&(this.nodesContainer.setPrivate("visible",!1),this.linksContainer.setPrivate("visible",!1));const t=this.d3forceSimulation;this.isDirty("velocityDecay")&&t.velocityDecay(this.get("velocityDecay",0)),this.isDirty("initialFrames")&&t.alphaDecay(1-Math.pow(.001,1/this.get("initialFrames",500)))}restartSimulation(t){const e=this.d3forceSimulation;e.alpha()<.25&&(e.alpha(t),e.restart())}_handleRadiusChange(){this._updateForces()}processDataItem(t){const e={index:this._index,x:this.innerWidth()/2,y:this.innerHeight()/2,dataItem:t},n=this._nodes.push(e)-1;e.index=n,this.d3forceSimulation.nodes(this._nodes),t.set("d3ForceNode",e),super.processDataItem(t);const i=t.get("node");i.set("x",-1e4),i.on("scale",(()=>{this._updateForces()})),i.events.on("dragged",(()=>{e.fx=i.x(),e.fy=i.y(),this._updateForces()})),i.events.on("dragstop",(()=>{null==t.get("x")&&(e.fx=void 0),null==t.get("y")&&(e.fy=void 0)}))}_updateValues(t){super._updateValues(t),this._nodesDirty=!0;const e=this.d3forceSimulation;e.force("collision",this.collisionForce),e.nodes(this._nodes),this.linkForce=it(this._links),e.force("link",this.linkForce)}_updateVisuals(){super._updateVisuals(),this.restartSimulation(.3)}_updateChildren(){super._updateChildren();const t=this.d3forceSimulation;if(this._sizeDirty){let e=Math.max(50,this.innerWidth()),n=Math.max(50,this.innerHeight()),i=this.get("paddingTop",0),r=this.get("paddingLeft",0),a=this.get("centerStrength",1);t.force("x",function(t){var e,n,i,r=Q(.1);function a(t){for(var r,a=0,o=e.length;a<o;++a)(r=e[a]).vx+=(i[a]-r.x)*n[a]*t}function o(){if(e){var a,o=e.length;for(n=new Array(o),i=new Array(o),a=0;a<o;++a)n[a]=isNaN(i[a]=+t(e[a],a,e))?0:+r(e[a],a,e)}}return"function"!=typeof t&&(t=Q(null==t?0:+t)),a.initialize=function(t){e=t,o()},a.strength=function(t){return arguments.length?(r="function"==typeof t?t:Q(+t),o(),a):r},a.x=function(e){return arguments.length?(t="function"==typeof e?e:Q(+e),o(),a):t},a}().x(e/2+r).strength(100*a/e)),t.force("y",function(t){var e,n,i,r=Q(.1);function a(t){for(var r,a=0,o=e.length;a<o;++a)(r=e[a]).vy+=(i[a]-r.y)*n[a]*t}function o(){if(e){var a,o=e.length;for(n=new Array(o),i=new Array(o),a=0;a<o;++a)n[a]=isNaN(i[a]=+t(e[a],a,e))?0:+r(e[a],a,e)}}return"function"!=typeof t&&(t=Q(null==t?0:+t)),a.initialize=function(t){e=t,o()},a.strength=function(t){return arguments.length?(r="function"==typeof t?t:Q(+t),o(),a):r},a.y=function(e){return arguments.length?(t="function"==typeof e?e:Q(+e),o(),a):t},a}().y(n/2+i).strength(100*a/n))}this._nodesDirty&&this._updateForces()}_updateForces(){this.d3forceSimulation.force("manybody",function(){var t,e,n,i,r,a=Q(-30),o=1,s=1/0,l=.81;function h(n){var r,a=t.length,o=X(t,H,B).visitAfter(c);for(i=n,r=0;r<a;++r)e=t[r],o.visit(d)}function u(){if(t){var e,n,i=t.length;for(r=new Array(i),e=0;e<i;++e)n=t[e],r[n.index]=+a(n,e,t)}}function c(t){var e,n,i,a,o,s=0,l=0;if(t.length){for(i=a=o=0;o<4;++o)(e=t[o])&&(n=Math.abs(e.value))&&(s+=e.value,l+=n,i+=n*e.x,a+=n*e.y);t.x=i/l,t.y=a/l}else{(e=t).x=e.data.x,e.y=e.data.y;do{s+=r[e.data.index]}while(e=e.next)}t.value=s}function d(t,a,h,u){if(!t.value)return!0;var c=t.x-e.x,d=t.y-e.y,f=u-a,g=c*c+d*d;if(f*f/l<g)return g<s&&(0===c&&(g+=(c=U(n))*c),0===d&&(g+=(d=U(n))*d),g<o&&(g=Math.sqrt(o*g)),e.vx+=c*t.value*i/g,e.vy+=d*t.value*i/g),!0;if(!(t.length||g>=s)){(t.data!==e||t.next)&&(0===c&&(g+=(c=U(n))*c),0===d&&(g+=(d=U(n))*d),g<o&&(g=Math.sqrt(o*g)));do{t.data!==e&&(f=r[t.data.index]*i/g,e.vx+=c*f,e.vy+=d*f)}while(t=t.next)}}return h.initialize=function(e,i){t=e,n=i,u()},h.strength=function(t){return arguments.length?(a="function"==typeof t?t:Q(+t),u(),h):a},h.distanceMin=function(t){return arguments.length?(o=t*t,h):Math.sqrt(o)},h.distanceMax=function(t){return arguments.length?(s=t*t,h):Math.sqrt(s)},h.theta=function(t){return arguments.length?(l=t*t,h):Math.sqrt(l)},h}().strength((t=>{let e=t.dataItem,n=e.get("node"),i=e.get("circle"),r=this.get("manyBodyStrength",-15);return i?i.get("radius",1)*n.get("scale",1)*r:0}))),this.collisionForce.radius((t=>{let e=t.dataItem,n=e.get("node"),i=e.get("circle"),r=e.get("outerCircle");if(i&&r){let t=i.get("radius",1);return r.isHidden()||(t*=r.get("scale",1.1)),t*=n.get("scale",1),t+this.get("nodePadding",0)}})),this.restartSimulation(.3)}_animatePositions(t){}_clearDirty(){super._clearDirty(),this._nodesDirty=!1}updateNodePositions(){const t=this.linkForce;t&&(t.distance((t=>this.getDistance(t))),t.strength((t=>this.getStrength(t)))),this._tick==this.get("showOnFrame")&&(this.nodesContainer.setPrivate("visible",!0),this.linksContainer.setPrivate("visible",!0));let e=this.d3forceSimulation.nodes();w.each(e,(t=>{const e=t.dataItem.get("node");e.set("x",t.x),e.set("y",t.y)}))}updateLinkWith(t){w.each(t,(t=>{const e=t.get("linkWith");e&&w.each(e,(e=>{const n=this._getDataItemById(this.dataItems,e);n&&this.linkDataItems(t,n,this.get("linkWithStrength"))}));const n=t.get("children");n&&this.updateLinkWith(n)}))}getDistance(t){let e=t.sourceDataItem,n=t.targetDataItem,i=0;if(e&&n){const r=n.get("node");if(r.isHidden())return 0;let a=t.link;a&&(i=a.get("distance",1));const o=e.get("node");return r.isHidden()&&(i=1),i*(e.get("circle").get("radius",1)*o.get("scale",1)+n.get("circle").get("radius",1)*r.get("scale",1))}return i}getStrength(t){let e=0,n=t.link;return n&&(e=n.get("strength",1)),e*=t.targetDataItem.get("node").get("scale"),e}_updateNode(t){super._updateNode(t),this._updateRadius(t);const e=t.get("x"),n=t.get("y"),i=t.get("d3ForceNode");i.fx=null!=e?g.relativeToValue(e,this.innerWidth()):void 0,null!=n?i.fy=g.relativeToValue(n,this.innerHeight()):i.fx=void 0}_updateRadius(t){let e=(this.innerWidth()+this.innerHeight())/2,n=g.relativeToValue(this.get("minRadius",1),e),i=g.relativeToValue(this.get("maxRadius",5),e),r=t.get("sum"),a=i;const o=this.getPrivate("valueLow",0),s=this.getPrivate("valueHigh",0);s>0&&(a=n+(r-o)/(s-o)*(i-n)),x.isNumber(a)||(a=n);const l=this.get("animationDuration",0),h=this.get("animationEasing");t.get("circle").animate({key:"radius",to:a,duration:l,easing:h})}_processLink(t,e,n){const i={link:t,source:e.get("d3ForceNode").index,target:n.get("d3ForceNode").index,sourceDataItem:e,targetDataItem:n};this._links.push(i),t.setPrivate("d3Link",i),this.linkForce=it(this._links),this.d3forceSimulation.force("link",this.linkForce),this.restartSimulation(.5)}_disposeLink(t){super._disposeLink(t),w.remove(this._links,t.getPrivate("d3Link"))}_handleUnlink(){this.restartSimulation(.5)}_onDataClear(){super._onDataClear(),this._nodes=[],this._links=[]}}Object.defineProperty(rt,"className",{enumerable:!0,configurable:!0,writable:!0,value:"ForceDirected"}),Object.defineProperty(rt,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:R.classNames.concat([rt.className])});var at=n(2156);class ot extends C{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"pack"}),Object.defineProperty(this,"_packLayout",{enumerable:!0,configurable:!0,writable:!0,value:at.Z()}),Object.defineProperty(this,"_packData",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"circles",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>T.C._new(this._root,{themeTags:g.mergeTags(this.circles.template.get("themeTags",[]),[this._tag,"shape"])},[this.circles.template])))})}_afterNew(){super._afterNew(),this.setPrivate("scaleR",1)}_prepareChildren(){super._prepareChildren(),this.isPrivateDirty("scaleR")&&this._rootNode&&this._updateNodesScale(this._rootNode)}_updateVisuals(){if(this._rootNode){const t=this._packLayout;t.size([this.innerWidth(),this.innerHeight()]),t(this._rootNode),t.padding(this.get("nodePadding",0)),this._updateNodes(this._rootNode)}}_updateNode(t){super._updateNode(t);const e=t.get("node"),n=t.get("circle"),i=t.get("d3HierarchyNode"),r=this.getPrivate("scaleR",1),a=i.x*r,o=i.y*r,s=i.r*r,l=this.get("animationDuration",0),h=this.get("animationEasing");if(e.animate({key:"x",to:a,duration:l,easing:h}),e.animate({key:"y",to:o,duration:l,easing:h}),n){const e=t.get("fill"),i=t.get("fillPattern");n.animate({key:"radius",to:s,duration:l,easing:h}),n._setDefault("fill",e),n._setDefault("fillPattern",i),n._setDefault("stroke",e)}}_updateNodesScale(t){const e=t.data.dataItem;if(e){const n=e.get("node"),i=e.get("circle"),r=this.getPrivate("scaleR",1),a=t.x*r,o=t.y*r,s=t.r*r;n.setAll({x:a,y:o}),i.set("radius",s);const l=t.children;l&&w.each(l,(t=>{this._updateNodesScale(t)}))}}makeNode(t){const e=super.makeNode(t),n=e.children.moveValue(this.circles.make(),0);e.setPrivate("tooltipTarget",n),this.circles.push(n),t.setRaw("circle",n);const i=t.get("label");return n.on("radius",(()=>{const t=2*n.get("radius",this.width());i.setAll({maxWidth:t,maxHeight:t})})),e}_zoom(t){const e=t.get("d3HierarchyNode");let n=e.x,i=e.y,r=e.r,a=Math.min(this.innerWidth(),this.innerHeight())/(2*r);const o=this.get("animationEasing");let s=this.get("animationDuration",0);this.inited||(s=0),this.animatePrivate({key:"scaleR",to:a,duration:s,easing:o});const l=this.nodesContainer;l.animate({key:"x",from:l.x(),to:this.width()/2-n*a,duration:s,easing:o}),l.animate({key:"y",from:l.y(),to:this.height()/2-i*a,duration:s,easing:o})}}function st(t){t.x0=Math.round(t.x0),t.y0=Math.round(t.y0),t.x1=Math.round(t.x1),t.y1=Math.round(t.y1)}function lt(t,e,n,i,r){for(var a,o=t.children,s=-1,l=o.length,h=t.value&&(i-e)/t.value;++s<l;)(a=o[s]).y0=n,a.y1=r,a.x0=e,a.x1=e+=a.value*h}function ht(){var t=1,e=1,n=0,i=!1;function r(r){var a=r.height+1;return r.x0=r.y0=n,r.x1=t,r.y1=e/a,r.eachBefore(function(t,e){return function(i){i.children&&lt(i,i.x0,t*(i.depth+1)/e,i.x1,t*(i.depth+2)/e);var r=i.x0,a=i.y0,o=i.x1-n,s=i.y1-n;o<r&&(r=o=(r+o)/2),s<a&&(a=s=(a+s)/2),i.x0=r,i.y0=a,i.x1=o,i.y1=s}}(e,a)),i&&r.eachBefore(st),r}return r.round=function(t){return arguments.length?(i=!!t,r):i},r.size=function(n){return arguments.length?(t=+n[0],e=+n[1],r):[t,e]},r.padding=function(t){return arguments.length?(n=+t,r):n},r}Object.defineProperty(ot,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Pack"}),Object.defineProperty(ot,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:C.classNames.concat([ot.className])});class ut extends C{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"partition"}),Object.defineProperty(this,"rectangles",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>c.c._new(this._root,{themeTags:g.mergeTags(this.rectangles.template.get("themeTags",[]),[this._tag,"shape"])},[this.rectangles.template])))}),Object.defineProperty(this,"_partitionLayout",{enumerable:!0,configurable:!0,writable:!0,value:ht()})}_afterNew(){this._settings.themeTags=g.mergeTags(this._settings.themeTags,["partition",this._settings.orientation||"vertical"]),super._afterNew(),this.setPrivate("scaleX",1),this.setPrivate("scaleY",1)}_prepareChildren(){super._prepareChildren(),this.isDirty("nodePadding")&&this._rootNode&&this._updateNodes(this._rootNode),(this.isPrivateDirty("scaleX")||this.isPrivateDirty("scaleY"))&&this._rootNode&&this._updateNodesScale(this._rootNode),this.isDirty("orientation")&&this._updateVisuals()}_updateVisuals(){if(this._rootNode){const t=this._partitionLayout;let e=this.innerWidth(),n=this.innerHeight();"horizontal"==this.get("orientation")&&([e,n]=[n,e]),t.size([e,n]);const i=this.get("nodePadding");x.isNumber(i)&&t.padding(i),t(this._rootNode),this._updateNodes(this._rootNode)}}_updateNode(t){super._updateNode(t);const e=t.get("node"),n=t.get("rectangle"),i=t.get("d3HierarchyNode"),r=this.getPrivate("scaleX",1),a=this.getPrivate("scaleY",1);let o,s,l,h;"horizontal"==this.get("orientation")?(o=i.y0*r,s=i.y1*r,l=i.x0*a,h=i.x1*a):(o=i.x0*r,s=i.x1*r,l=i.y0*a,h=i.y1*a);let u=s-o,c=h-l;const d=this.get("animationDuration",0),f=this.get("animationEasing");if(e.animate({key:"x",to:o,duration:d,easing:f}),e.animate({key:"y",to:l,duration:d,easing:f}),e.animate({key:"width",to:u,duration:d,easing:f}),e.animate({key:"height",to:c,duration:d,easing:f}),n){const e=t.get("fill"),i=t.get("fillPattern");n.animate({key:"width",to:u,duration:d,easing:f}),n.animate({key:"height",to:c,duration:d,easing:f}),n._setDefault("fill",e),n._setDefault("fillPattern",i),n._setDefault("stroke",e)}}_updateNodesScale(t){const e=t.data.dataItem;if(e){const n=e.get("node"),i=e.get("rectangle"),r=this.getPrivate("scaleX",1),a=this.getPrivate("scaleY",1);let o,s,l,h;"horizontal"==this.get("orientation")?(o=t.y0*r,s=t.y1*r,l=t.x0*a,h=t.x1*a):(o=t.x0*r,s=t.x1*r,l=t.y0*a,h=t.y1*a);const u=s-o,c=h-l;n.setAll({x:o,y:l,width:u,height:c}),i.setAll({width:u,height:c});const d=t.children;d&&w.each(d,(t=>{this._updateNodesScale(t)}))}}makeNode(t){const e=super.makeNode(t);return this._makeNode(t,e),e}_makeNode(t,e){const n=e.children.moveValue(this.rectangles.make(),0);e.setPrivate("tooltipTarget",n),t.setRaw("rectangle",n),n._setDataItem(t);const i=t.get("label");n.on("width",(()=>{i.set("maxWidth",n.width())})),n.on("height",(()=>{i.set("maxHeight",n.height())}))}_zoom(t){let e=0,n=0,i=0,r=0;const a=this.get("upDepth",0)+1,o=this.get("topDepth",0),s=this.innerWidth(),l=this.innerHeight(),h=this.getPrivate("maxDepth",1),u=l/(h+1),c=s/(h+1),d=Math.min(this.get("initialDepth",1),h);let f=this._currentDownDepth;if(null==f&&(f=this.get("downDepth",1)),t){const h=t.get("d3HierarchyNode");let g=h.depth;"horizontal"==this.get("orientation")?(e=h.y0,n=h.y1,i=h.x0,r=h.x1,e=n-c*a,n=e+c*(f+1),g<o&&(i=0,r=l,e=c*o,n=e+c*d)):(e=h.x0,n=h.x1,i=h.y0,r=h.y1,i=r-u*a,r=i+u*(f+1),g<o&&(e=0,n=s,i=u*o,r=i+u*d))}let g=s/(n-e),p=l/(r-i);const v=this.get("animationEasing");let m=this.get("animationDuration",0);this.inited||(m=0),this.animatePrivate({key:"scaleX",to:g,duration:m,easing:v}),this.animatePrivate({key:"scaleY",to:p,duration:m,easing:v}),this.animate({key:"_d",from:0,to:1,duration:m,easing:v}),this.nodesContainer.animate({key:"x",to:-e*g,duration:m,easing:v}),this.nodesContainer.animate({key:"y",to:-i*p,duration:m,easing:v})}}Object.defineProperty(ut,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Partition"}),Object.defineProperty(ut,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:C.classNames.concat([ut.className])});var ct=n(5863),dt=n(815),ft=n(751);class gt extends ut{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"sunburst"}),Object.defineProperty(this,"_partitionLayout",{enumerable:!0,configurable:!0,writable:!0,value:ht()}),Object.defineProperty(this,"slices",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>ct.p._new(this._root,{themeTags:g.mergeTags(this.slices.template.get("themeTags",[]),[this._tag,"hierarchy","node","shape"])},[this.slices.template])))}),Object.defineProperty(this,"labels",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>dt.x._new(this._root,{themeTags:g.mergeTags(this.labels.template.get("themeTags",[]),[this._tag])},[this.labels.template])))})}_afterNew(){super._afterNew(),this.nodesContainer.setAll({x:r.CI,y:r.CI}),this.setPrivateRaw("dx",0),this.setPrivateRaw("dr",0)}_prepareChildren(){super._prepareChildren(),(this.isPrivateDirty("dr")||this.isPrivateDirty("dx"))&&this._rootNode&&this._updateNodesScale(this._rootNode)}_updateVisuals(){if(this._rootNode){const t=this._partitionLayout;let e=ft.getArcBounds(0,0,this.get("startAngle",0),this.get("endAngle",360),1),n=this.innerWidth(),i=this.innerHeight();const a=n/(e.right-e.left),o=i/(e.bottom-e.top);let s=Math.min(a,o),l=g.relativeToValue(this.get("radius",r.AQ),s),h=g.relativeToValue(this.get("innerRadius",0),l);h<0&&(h=l+h),s=l-h,this.setPrivateRaw("innerRadius",h),this.setPrivateRaw("hierarchySize",s),t.size([s,s]),this.nodesContainer.setAll({dy:-l*(e.bottom+e.top)/2,dx:-l*(e.right+e.left)/2});const u=this.get("nodePadding");x.isNumber(u)&&t.padding(u),t(this._rootNode),this._updateNodes(this._rootNode)}}_updateNode(t){super._updateNode(t);const e=t.get("d3HierarchyNode");t.get("node").setAll({x:0,y:0});const n=this.get("animationDuration",0),i=this.get("animationEasing"),r=this.getPrivate("scaleX",1),a=this.getPrivate("scaleY",1),o=this.getPrivate("dr",0),s=this.getPrivate("dx",0),l=e.x0*r+s,h=e.x1*r+s,u=e.y0*a,c=e.y1*a,d=this.getPrivate("innerRadius"),f=this.getPrivate("hierarchySize",0),g=t.get("slice");if(g){const e=this.get("startAngle",-90),r=this.get("endAngle",270),a=e+l/f*(r-e),s=e+h/f*(r-e)-a;let p=d+u,v=d+c;p-=o,v-=o,v=Math.max(0,v),p=Math.max(0,p),g.animate({key:"radius",to:v,duration:n,easing:i}),g.animate({key:"innerRadius",to:p,duration:n,easing:i}),g.animate({key:"startAngle",to:a,duration:n,easing:i}),g.animate({key:"arc",to:s,duration:n,easing:i});const m=t.get("fill"),y=t.get("fillPattern");g._setDefault("fill",m),g._setDefault("fillPattern",y),g._setDefault("stroke",m)}}_updateNodesScale(t){const e=t.data.dataItem;if(e){const n=this.getPrivate("scaleX",1),i=this.getPrivate("scaleY",1),r=this.getPrivate("dr",0),a=this.getPrivate("dx",0),o=t.x0*n+a,s=t.x1*n+a,l=t.y0*i,h=t.y1*i,u=this.getPrivate("innerRadius"),c=this.getPrivate("hierarchySize",0),d=e.get("slice");if(d){const t=this.get("startAngle",-90),e=this.get("endAngle",270),n=t+o/c*(e-t),i=t+s/c*(e-t)-n;let a=u+l,f=u+h;a-=r,f-=r,f=Math.max(0,f),a=Math.max(0,a),d.setAll({radius:f,innerRadius:a,startAngle:n,arc:i})}const f=t.children;f&&w.each(f,(t=>{this._updateNodesScale(t)}))}}_makeNode(t,e){const n=e.children.moveValue(this.slices.make(),0);e.setPrivate("tooltipTarget",n),t.setRaw("slice",n),n._setDataItem(t),n.on("arc",(()=>{this._updateLabel(t)})),n.on("innerRadius",(()=>{this._updateLabel(t)})),n.on("radius",(()=>{this._updateLabel(t)}))}_updateLabel(t){const e=t.get("slice"),n=t.get("label");if(e&&n){let t=e.get("innerRadius",0),i=e.get("radius",0),r=e.get("startAngle",0),a=Math.abs(e.get("arc",0)),o=r+a/2,s=n.get("textType"),l=i-t,h=i*a*ft.RADIANS;0==t&&a>=360&&"radial"==s&&(i=1,o=0,l*=2,h=l),Math.round(a)>=360&&"radial"==s&&(o=0),"circular"==s&&(l=a*ft.RADIANS*(t+(i-t)/2)-10),n.setAll({labelAngle:o}),n.setPrivate("radius",i),n.setPrivate("innerRadius",t),n.setAll({maxHeight:h,maxWidth:l})}}_zoom(t){let e=0,n=0,i=this.getPrivate("hierarchySize",0);const r=t.get("d3HierarchyNode");let a=this.get("upDepth",0),o=this.get("topDepth",0),s=r.depth,l=this.getPrivate("maxDepth",1),h=this._currentDownDepth;null==h&&(h=this.get("downDepth",1));const u=i/(l+1);s<o&&(s=o),s-a<0&&(a=s),e=r.x0,n=r.x1;let c=h+a+1;c>l-o+1&&(c=l-o+1);let d=i/(n-e),f=i/(u*c),g=Math.max(s-a,o)*u*f;const p=this.get("animationEasing");let v=this.get("animationDuration",0);this.inited||(v=0);let m=-e*d;this.animatePrivate({key:"scaleX",to:d,duration:v,easing:p}),this.animatePrivate({key:"scaleY",to:f,duration:v,easing:p}),this.animatePrivate({key:"dr",to:g,duration:v,easing:p}),this.animatePrivate({key:"dx",to:m,duration:v,easing:p})}_handleSingle(t){const e=t.get("parent");if(e){const n=e.get("children");n&&w.each(n,(e=>{e!=t&&(this.disableDataItem(e),e.get("node").hide())})),this._handleSingle(e)}}_positionBullet(t){const e=t.get("sprite");if(e){const n=e.dataItem,i=t.get("locationX",.5),r=t.get("locationY",.5),a=n.get("slice"),o=a.get("arc",0),s=a.get("startAngle",0)+a.get("arc",0)*i,l=a.get("innerRadius",0),h=l+(a.get("radius",0)-l)*r;let u=ft.cos(s)*h,c=ft.sin(s)*h;360===ft.round(o,5)&&0===ft.round(l,2)&&(u=0,c=0),e.set("x",u),e.set("y",c)}}_makeBullet(t,e,n){const i=super._makeBullet(t,e,n);if(i){const e=i.get("sprite"),n=t.get("slice");return e&&n&&(n.on("arc",(()=>{this._positionBullet(i)})),n.on("radius",(()=>{this._positionBullet(i)}))),i}}}function pt(t,e){return t.parent===e.parent?1:2}function vt(t){var e=t.children;return e?e[0]:t.t}function mt(t){var e=t.children;return e?e[e.length-1]:t.t}function yt(t,e,n){var i=n/(e.i-t.i);e.c-=i,e.s+=n,t.c+=i,e.z+=n,e.m+=n}function bt(t,e,n){return t.a.parent===e.parent?t.a:n}function wt(t,e){this._=t,this.parent=null,this.children=null,this.A=null,this.a=this,this.z=0,this.m=0,this.c=0,this.s=0,this.t=null,this.i=e}function xt(){var t=pt,e=1,n=1,i=null;function r(r){var l=function(t){for(var e,n,i,r,a,o=new wt(t,0),s=[o];e=s.pop();)if(i=e._.children)for(e.children=new Array(a=i.length),r=a-1;r>=0;--r)s.push(n=e.children[r]=new wt(i[r],r)),n.parent=e;return(o.parent=new wt(null,0)).children=[o],o}(r);if(l.eachAfter(a),l.parent.m=-l.z,l.eachBefore(o),i)r.eachBefore(s);else{var h=r,u=r,c=r;r.eachBefore((function(t){t.x<h.x&&(h=t),t.x>u.x&&(u=t),t.depth>c.depth&&(c=t)}));var d=h===u?1:t(h,u)/2,f=d-h.x,g=e/(u.x+d+f),p=n/(c.depth||1);r.eachBefore((function(t){t.x=(t.x+f)*g,t.y=t.depth*p}))}return r}function a(e){var n=e.children,i=e.parent.children,r=e.i?i[e.i-1]:null;if(n){!function(t){for(var e,n=0,i=0,r=t.children,a=r.length;--a>=0;)(e=r[a]).z+=n,e.m+=n,n+=e.s+(i+=e.c)}(e);var a=(n[0].z+n[n.length-1].z)/2;r?(e.z=r.z+t(e._,r._),e.m=e.z-a):e.z=a}else r&&(e.z=r.z+t(e._,r._));e.parent.A=function(e,n,i){if(n){for(var r,a=e,o=e,s=n,l=a.parent.children[0],h=a.m,u=o.m,c=s.m,d=l.m;s=mt(s),a=vt(a),s&&a;)l=vt(l),(o=mt(o)).a=e,(r=s.z+c-a.z-h+t(s._,a._))>0&&(yt(bt(s,e,i),e,r),h+=r,u+=r),c+=s.m,h+=a.m,d+=l.m,u+=o.m;s&&!mt(o)&&(o.t=s,o.m+=c-u),a&&!vt(l)&&(l.t=a,l.m+=h-d,i=e)}return i}(e,r,e.parent.A||i[0])}function o(t){t._.x=t.z+t.parent.m,t.m+=t.parent.m}function s(t){t.x*=e,t.y=t.depth*n}return r.separation=function(e){return arguments.length?(t=e,r):t},r.size=function(t){return arguments.length?(i=!1,e=+t[0],n=+t[1],r):i?null:[e,n]},r.nodeSize=function(t){return arguments.length?(i=!0,e=+t[0],n=+t[1],r):i?[e,n]:null},r}Object.defineProperty(gt,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Sunburst"}),Object.defineProperty(gt,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:ut.classNames.concat([gt.className])}),wt.prototype=Object.create(I.prototype);class _t extends R{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"tree"}),Object.defineProperty(this,"_hierarchyLayout",{enumerable:!0,configurable:!0,writable:!0,value:xt()}),Object.defineProperty(this,"_packData",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_prepareChildren(){super._prepareChildren(),(this.isDirty("orientation")||this.isDirty("inversed"))&&this._updateVisuals()}_updateVisuals(){if(this._rootNode){const t=this._hierarchyLayout;"vertical"==this.get("orientation")?t.size([this.innerWidth(),this.innerHeight()]):t.size([this.innerHeight(),this.innerWidth()]),t(this._rootNode)}super._updateVisuals()}_getPoint(t){const e=this.get("inversed");return"vertical"==this.get("orientation")?e?{x:t.x,y:this.innerHeight()-t.y}:{x:t.x,y:t.y}:e?{x:this.innerWidth()-t.y,y:t.x}:{x:t.y,y:t.x}}}function kt(t,e,n,i,r){for(var a,o=t.children,s=-1,l=o.length,h=t.value&&(r-n)/t.value;++s<l;)(a=o[s]).x0=e,a.x1=i,a.y0=n,a.y1=n+=a.value*h}Object.defineProperty(_t,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Tree"}),Object.defineProperty(_t,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:R.classNames.concat([_t.className])});var Dt=function t(e){function n(t,n,i,r,a){!function(t,e,n,i,r,a){for(var o,s,l,h,u,c,d,f,g,p,v,m=[],y=e.children,b=0,w=0,x=y.length,_=e.value;b<x;){l=r-n,h=a-i;do{u=y[w++].value}while(!u&&w<x);for(c=d=u,v=u*u*(p=Math.max(h/l,l/h)/(_*t)),g=Math.max(d/v,v/c);w<x;++w){if(u+=s=y[w].value,s<c&&(c=s),s>d&&(d=s),v=u*u*p,(f=Math.max(d/v,v/c))>g){u-=s;break}g=f}m.push(o={value:u,dice:l<h,children:y.slice(b,w)}),o.dice?lt(o,n,i,r,_?i+=h*u/_:a):kt(o,n,i,_?n+=l*u/_:r,a),_-=u,b=w}}(e,t,n,i,r,a)}return n.ratio=function(e){return t((e=+e)>1?e:1)},n}((1+Math.sqrt(5))/2),Nt=n(8433),Pt=n(7639);function At(){var t=Dt,e=!1,n=1,i=1,r=[0],a=Pt.G,o=Pt.G,s=Pt.G,l=Pt.G,h=Pt.G;function u(t){return t.x0=t.y0=0,t.x1=n,t.y1=i,t.eachBefore(c),r=[0],e&&t.eachBefore(st),t}function c(e){var n=r[e.depth],i=e.x0+n,u=e.y0+n,c=e.x1-n,d=e.y1-n;c<i&&(i=c=(i+c)/2),d<u&&(u=d=(u+d)/2),e.x0=i,e.y0=u,e.x1=c,e.y1=d,e.children&&(n=r[e.depth+1]=a(e)/2,i+=h(e)-n,u+=o(e)-n,(c-=s(e)-n)<i&&(i=c=(i+c)/2),(d-=l(e)-n)<u&&(u=d=(u+d)/2),t(e,i,u,c,d))}return u.round=function(t){return arguments.length?(e=!!t,u):e},u.size=function(t){return arguments.length?(n=+t[0],i=+t[1],u):[n,i]},u.tile=function(e){return arguments.length?(t=(0,Nt.C)(e),u):t},u.padding=function(t){return arguments.length?u.paddingInner(t).paddingOuter(t):u.paddingInner()},u.paddingInner=function(t){return arguments.length?(a="function"==typeof t?t:(0,Pt.Z)(+t),u):a},u.paddingOuter=function(t){return arguments.length?u.paddingTop(t).paddingRight(t).paddingBottom(t).paddingLeft(t):u.paddingTop()},u.paddingTop=function(t){return arguments.length?(o="function"==typeof t?t:(0,Pt.Z)(+t),u):o},u.paddingRight=function(t){return arguments.length?(s="function"==typeof t?t:(0,Pt.Z)(+t),u):s},u.paddingBottom=function(t){return arguments.length?(l="function"==typeof t?t:(0,Pt.Z)(+t),u):l},u.paddingLeft=function(t){return arguments.length?(h="function"==typeof t?t:(0,Pt.Z)(+t),u):h},u}function It(t,e,n,i,r){var a,o,s=t.children,l=s.length,h=new Array(l+1);for(h[0]=o=a=0;a<l;++a)h[a+1]=o+=s[a].value;!function t(e,n,i,r,a,o,l){if(e>=n-1){var u=s[e];return u.x0=r,u.y0=a,u.x1=o,void(u.y1=l)}for(var c=h[e],d=i/2+c,f=e+1,g=n-1;f<g;){var p=f+g>>>1;h[p]<d?f=p+1:g=p}d-h[f-1]<h[f]-d&&e+1<f&&--f;var v=h[f]-c,m=i-v;if(o-r>l-a){var y=i?(r*m+o*v)/i:o;t(e,f,v,r,a,y,l),t(f,n,m,y,a,o,l)}else{var b=i?(a*m+l*v)/i:l;t(e,f,v,r,a,o,b),t(f,n,m,r,b,o,l)}}(0,l,t.value,e,n,i,r)}function Ct(t,e,n,i,r){(1&t.depth?kt:lt)(t,e,n,i,r)}class Mt extends C{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"treemap"}),Object.defineProperty(this,"rectangleTemplate",{enumerable:!0,configurable:!0,writable:!0,value:d.YS.new({})}),Object.defineProperty(this,"_treemapLayout",{enumerable:!0,configurable:!0,writable:!0,value:At().tile(Dt)}),Object.defineProperty(this,"rectangles",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>c.c._new(this._root,{themeTags:g.mergeTags(this.rectangles.template.get("themeTags",[]),[this._tag,"shape"])},[this.rectangles.template])))})}_afterNew(){super._afterNew(),this.setPrivate("scaleX",1),this.setPrivate("scaleY",1),this.nodes.template.setPrivate("trustBounds",!0)}_prepareChildren(){if(super._prepareChildren(),this.isDirty("layoutAlgorithm")){let t;switch(this.get("layoutAlgorithm")){case"squarify":t=Dt;break;case"binary":t=It;break;case"slice":t=kt;break;case"dice":t=lt;break;case"sliceDice":t=Ct}if(t){this._treemapLayout=At().tile(t),this._updateVisuals();const e=this.get("selectedDataItem");e&&this._zoom(e)}}(this.isDirty("nodePaddingRight")||this.isDirty("nodePaddingLeft")||this.isDirty("nodePaddingTop")||this.isDirty("nodePaddingBottom")||this.isDirty("nodePaddingOuter")||this.isDirty("nodePaddingInner"))&&this._rootNode&&this._updateNodes(this._rootNode),(this.isPrivateDirty("scaleX")||this.isPrivateDirty("scaleY"))&&this._rootNode&&this._updateNodesScale(this._rootNode)}_updateVisuals(){if(this._rootNode){const t=this._treemapLayout;t.size([this.innerWidth(),this.innerHeight()]);const e=this.get("nodePaddingLeft"),n=this.get("nodePaddingRight"),i=this.get("nodePaddingTop"),r=this.get("nodePaddingBottom"),a=this.get("nodePaddingInner"),o=this.get("nodePaddingOuter");x.isNumber(e)&&t.paddingLeft(e),x.isNumber(n)&&t.paddingRight(n),x.isNumber(i)&&t.paddingTop(i),x.isNumber(r)&&t.paddingBottom(r),x.isNumber(a)&&t.paddingInner(a),x.isNumber(o)&&t.paddingOuter(o),t(this._rootNode),this._updateNodes(this._rootNode)}}_updateNode(t){super._updateNode(t);const e=t.get("node"),n=t.get("rectangle"),i=t.get("d3HierarchyNode"),r=this.getPrivate("scaleX",1),a=this.getPrivate("scaleY",1),o=i.x0*r,s=i.x1*r,l=i.y0*a,h=s-o,u=i.y1*a-l,c=this.get("animationDuration",0),d=this.get("animationEasing");if(e.animate({key:"x",to:o,duration:c,easing:d}),e.animate({key:"y",to:l,duration:c,easing:d}),e.animate({key:"width",to:h,duration:c,easing:d}),e.animate({key:"height",to:u,duration:c,easing:d}),n){const e=t.get("fill"),i=t.get("fillPattern");n.animate({key:"width",to:h,duration:c,easing:d}),n.animate({key:"height",to:u,duration:c,easing:d}),n._setDefault("fill",e),n._setDefault("fillPattern",i),n._setDefault("stroke",e)}}_updateNodesScale(t){const e=t.data.dataItem;if(e){const n=e.get("node"),i=e.get("rectangle"),r=this.getPrivate("scaleX",1),a=this.getPrivate("scaleY",1),o=t.x0*r,s=t.x1*r,l=t.y0*a,h=s-o,u=t.y1*a-l;n.setAll({x:o,y:l,width:h,height:u}),i.setAll({width:h,height:u});const c=t.children;c&&w.each(c,(t=>{this._updateNodesScale(t)}))}}makeNode(t){const e=super.makeNode(t),n=e.children.moveValue(this.rectangles.make(),0);e.setPrivate("tooltipTarget",n),t.setRaw("rectangle",n);const i=t.get("label");return n.on("width",(()=>{i.setPrivate("maxWidth",n.width())})),n.on("height",(()=>{i.setPrivate("maxHeight",n.height())})),e}_zoom(t){if(this.width()>0&&this.height()>0){const e=t.get("d3HierarchyNode"),n=this.get("nodePaddingOuter",0);let i=e.x0+n,r=e.x1-n,a=e.y0+n,o=e.y1-n,s=(this.innerWidth()-2*n)/(r-i),l=(this.innerHeight()-2*n)/(o-a);const h=this.get("animationEasing");let u=this.get("animationDuration",0);this.inited||(u=0),this.animatePrivate({key:"scaleX",to:s,duration:u,easing:h}),this.animatePrivate({key:"scaleY",to:l,duration:u,easing:h}),this.nodesContainer.animate({key:"x",to:n-i*s,duration:u,easing:h}),this.nodesContainer.animate({key:"y",to:n-a*l,duration:u,easing:h})}}_selectDataItem(t,e,n){if(super._selectDataItem(t,e,n),t){let e=this.get("downDepth",1)+t.get("depth");this.inited||(e=this.get("initialDepth",1));const n=this._getVisibleNodes(t,e);this.nodes.each((t=>{-1==n.indexOf(t.dataItem)?t.setPrivate("focusable",!1):t.removePrivate("focusable")}))}this._root._invalidateTabindexes()}_getVisibleNodes(t,e){const n=t.get("children");let i=[];return n&&w.each(n,(t=>{t.get("depth")!=e&&t.get("children")?i=i.concat(this._getVisibleNodes(t,e)):i.push(t)})),i}}Object.defineProperty(Mt,"className",{enumerable:!0,configurable:!0,writable:!0,value:"Treemap"}),Object.defineProperty(Mt,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:C.classNames.concat([Mt.className])});var Ot=n(9384),zt=n(5720),Tt=n.n(zt),Rt=n(7770);class St extends C{constructor(){super(...arguments),Object.defineProperty(this,"_tag",{enumerable:!0,configurable:!0,writable:!0,value:"voronoitreemap"}),Object.defineProperty(this,"polygons",{enumerable:!0,configurable:!0,writable:!0,value:new f.o(d.YS.new({}),(()=>Rt.m._new(this._root,{themeTags:g.mergeTags(this.polygons.template.get("themeTags",[]),[this._tag,"shape"])},[this.polygons.template])))}),Object.defineProperty(this,"voronoi",{enumerable:!0,configurable:!0,writable:!0,value:(0,Ot.voronoiTreemap)()})}_afterNew(){this.nodesContainer.setAll({x:r.CI,y:r.CI,centerX:r.CI,centerY:r.CI}),this.nodes.template.setPrivate("trustBounds",!0),super._afterNew()}_prepareChildren(){super._prepareChildren();const t=this.innerWidth()/2,e=this.innerHeight()/2;let n=this._rootNode;const i=this.get("selectedDataItem");i&&(n=i.get("d3HierarchyNode")),this.voronoi.convergenceRatio(this.get("convergenceRatio",.005)),this.voronoi.maxIterationCount(this.get("maxIterationCount",100)),this.voronoi.minWeightRatio(this.get("minWeightRatio",.005)),this.isDirty("type")&&"polygon"==this.get("type")&&(this.voronoi.clip(this.getCirclePolygon(1)),this._updateVisuals()),this._sizeDirty&&"rectangle"==this.get("type")&&(this.voronoi.prng(Tt()("X")),this.voronoi.clip([[-t,-e],[-t,e],[t,e],[t,-e]])(n),this._updateVisuals()),(this._valuesDirty||this.isDirty("selectedDataItem"))&&n&&(this.voronoi.prng(Tt()("X")),this.voronoi(n),this._updateVisuals())}_updateNode(t){const e=t.get("d3HierarchyNode").polygon,n=t.get("polygon");if(e&&n){let i=[],r=1;"polygon"==this.get("type")&&(r=Math.min(this.innerWidth(),this.innerHeight())/2);let a=1/0,o=-1/0;for(let t=0,n=e.length;t<n;t++){const n=e[t];let s=n[0]*r,l=n[1]*r;i.push([s,l]),a=Math.min(a,s),o=Math.max(o,s)}n.set("coordinates",i);const s=t.get("fill"),l=t.get("fillPattern");n._setDefault("fill",s),n._setDefault("fillPattern",l);const h=t.get("label");if(h){const t=e.site;t&&h.setAll({x:t.x*r,y:t.y*r,maxWidth:Math.abs(o-a)})}}}_handleSingle(t){const e=t.get("parent");if(e){const n=e.get("children");n&&w.each(n,(e=>{e!=t&&(this.disableDataItem(e),e.get("node").hide())})),this._handleSingle(e)}}makeNode(t){const e=super.makeNode(t);return this._makeNode(t,e),e}_makeNode(t,e){const n=e.children.moveValue(this.polygons.make(),0);e.setPrivate("tooltipTarget",n),t.setRaw("polygon",n),n._setDataItem(t)}getCirclePolygon(t){const e=this.get("cornerCount",120),n=2*Math.PI/e,i=[];for(let r=0;r<e;r++){let e=r*n;i.push([t*Math.cos(e),t*Math.sin(e)])}return i}}Object.defineProperty(St,"className",{enumerable:!0,configurable:!0,writable:!0,value:"VoronoiTreemap"}),Object.defineProperty(St,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:C.classNames.concat([St.className])})},7832:function(t,e,n){"use strict";function i(t){for(var e,n=-1,i=t.length,r=t[i-1],a=0;++n<i;)e=r,r=t[n],a+=e[1]*r[0]-e[0]*r[1];return a/2}function r(t){for(var e,n,i=-1,r=t.length,a=0,o=0,s=t[r-1],l=0;++i<r;)e=s,s=t[i],l+=n=e[0]*s[1]-s[0]*e[1],a+=(e[0]+s[0])*n,o+=(e[1]+s[1])*n;return[a/(l*=3),o/l]}function a(t,e){return t[0]-e[0]||t[1]-e[1]}function o(t){const e=t.length,n=[0,1];let i,r=2;for(i=2;i<e;++i){for(;r>1&&(a=t[n[r-2]],o=t[n[r-1]],s=t[i],(o[0]-a[0])*(s[1]-a[1])-(o[1]-a[1])*(s[0]-a[0])<=0);)--r;n[r++]=i}var a,o,s;return n.slice(0,r)}function s(t){if((n=t.length)<3)return null;var e,n,i=new Array(n),r=new Array(n);for(e=0;e<n;++e)i[e]=[+t[e][0],+t[e][1],e];for(i.sort(a),e=0;e<n;++e)r[e]=[i[e][0],-i[e][1]];var s=o(i),l=o(r),h=l[0]===s[0],u=l[l.length-1]===s[s.length-1],c=[];for(e=s.length-1;e>=0;--e)c.push(t[i[s[e]][2]]);for(e=+h;e<l.length-u;++e)c.push(t[i[l[e]][2]]);return c}function l(t,e){for(var n,i,r=t.length,a=t[r-1],o=e[0],s=e[1],l=a[0],h=a[1],u=!1,c=0;c<r;++c)n=(a=t[c])[0],(i=a[1])>s!=h>s&&o<(l-n)*(s-i)/(h-i)+n&&(u=!u),l=n,h=i;return u}function h(t){for(var e,n,i=-1,r=t.length,a=t[r-1],o=a[0],s=a[1],l=0;++i<r;)e=o,n=s,e-=o=(a=t[i])[0],n-=s=a[1],l+=Math.hypot(e,n);return l}n.r(e),n.d(e,{polygonArea:function(){return i},polygonCentroid:function(){return r},polygonContains:function(){return l},polygonHull:function(){return s},polygonLength:function(){return h}})},6159:function(t,e,n){!function(t,e,n,i,r){"use strict";function a(){this.growthChangesLength=o,this.totalAvailableArea=NaN,this.lastAreaError=NaN,this.lastGrowth=NaN,this.growthChanges=[],this.growthChangeWeights=s(this.growthChangesLength),this.growthChangeWeightsSum=l(this.growthChangeWeights)}var o=10;function s(t){for(var e=3,n=[],i=0;i<t;i++)n.push(e),(e-=1)<1&&(e=1);return n}function l(t){for(var e=0,n=0;n<t.length;n++)e+=t[n];return e}function h(){var t,n,i,r,a,o,s,l;return function(h,u,c,d){var f,g,p=!1;for(t!==d.clip()&&(t=d.clip(),n=d.extent(),p=!0),p&&(i=n[0][0],r=n[1][0],a=n[0][1],o=n[1][1],s=r-i,l=o-a),f=i+s*d.prng()(),g=a+l*d.prng()();!e.polygonContains(t,[f,g]);)f=i+s*d.prng()(),g=a+l*d.prng()();return[f,g]}}function u(){var t,n,i,r,a;return function(o,s,l,h){var u=!1;return t!==h.clip()&&(t=h.clip(),u|=!0),n!==l&&(n=l,u|=!0),u&&(i=n.length,r=e.polygonArea(t),a=r/i/2),a}}function c(t){this.message=t,this.stack=(new Error).stack}a.prototype.reset=function(){return this.lastAreaError=NaN,this.lastGrowth=NaN,this.growthChanges=[],this.growthChangesLength=o,this.growthChangeWeights=s(this.growthChangesLength),this.growthChangeWeightsSum=l(this.growthChangeWeights),this.totalAvailableArea=NaN,this},a.prototype.clear=function(){return this.lastAreaError=NaN,this.lastGrowth=NaN,this.growthChanges=[],this},a.prototype.length=function(t){return arguments.length?(parseInt(t)>0?(this.growthChangesLength=Math.floor(parseInt(t)),this.growthChangeWeights=s(this.growthChangesLength),this.growthChangeWeightsSum=l(this.growthChangeWeights)):console.warn("FlickeringMitigation.length() accepts only positive integers; unable to handle "+t),this):this.growthChangesLength},a.prototype.totalArea=function(t){return arguments.length?(parseFloat(t)>0?this.totalAvailableArea=parseFloat(t):console.warn("FlickeringMitigation.totalArea() accepts only positive numbers; unable to handle "+t),this):this.totalAvailableArea},a.prototype.add=function(t){var e,n;return e=this.lastAreaError,this.lastAreaError=t,isNaN(e)||(n=this.lastGrowth,this.lastGrowth=this.lastAreaError>=e?1:-1),isNaN(n)||this.growthChanges.unshift(this.lastGrowth!=n),this.growthChanges.length>this.growthChangesLength&&this.growthChanges.pop(),this},a.prototype.ratio=function(){var t=0;if(this.growthChanges.length<this.growthChangesLength)return 0;if(this.lastAreaError>this.totalAvailableArea/10)return 0;for(var e=0;e<this.growthChangesLength;e++)this.growthChanges[e]&&(t+=this.growthChangeWeights[e]);return t/this.growthChangeWeightsSum},c.prototype.name="d3VoronoiMapError",c.prototype=new Error,t.voronoiMapSimulation=function(t){var o,s,l,d,f,g,p,v,m=Math.random,y=h(),b=u(),w=(h(),1e-10),x=function(t){return t.weight},_=.01,k=50,D=.01,N=m,P=y,A=b,I=r.weightedVoronoi(),C=new a,M=!0,O=n.timer(H),z=i.dispatch("tick","end");const T=1,R=1e3;var S;function j(t){return Math.pow(t,2)}function L(t,e){return j(e.x-t.x)+j(e.y-t.y)}function H(){B(),z.call("tick",v),p&&(O.stop(),z.call("end",v))}function B(){p||(M&&W(),f=function(t,n){var i;if(function(t,n){var i,r,a,s,l,h,u=[];i=1-.5*n;for(var c=0;c<o;c++)a=(r=t[c]).site.originalObject,l=(s=e.polygonCentroid(r))[0]-a.x,h=s[1]-a.y,l*=i,h*=i,a.x+=l,a.y+=h,u.push(a);S(u)}(t,n),i=t.map((function(t){return t.site.originalObject})),(t=I(i)).length<o)throw new c("at least 1 site has no area, which is not supposed to arise");if(function(t,n){var i,r,a,s,l,h,u=[],c=.1;i=c*n;for(var d=0;d<o;d++)a=(r=t[d]).site.originalObject,s=e.polygonArea(r),l=a.targetedArea/s,l=Math.max(l,1-c+i),l=Math.min(l,1+c-i),h=a.weight*l,h=Math.max(h,w),a.weight=h,u.push(a);S(u)}(t,n),i=t.map((function(t){return t.site.originalObject})),(t=I(i)).length<o)throw new c("at least 1 site has no area, which is not supposed to arise");return t}(f,C.ratio()),d++,g=function(t){for(var n,i,r,a=0,s=0;s<o;s++)i=(n=t[s]).site.originalObject,r=e.polygonArea(n),a+=Math.abs(i.targetedArea-r);return a}(f),C.add(g),p=g<l||d>=k)}function W(){(function(){switch(T){case 0:S=F;break;case 1:S=V;break;default:console.error("unknown 'handleOverweighted' variant; using variant #1"),S=F}})(),o=t.length,s=Math.abs(e.polygonArea(I.clip())),l=_*s,C.clear().totalArea(s),d=0,f=function(t,n){var i,r=t.reduce((function(t,e){return Math.max(t,x(e))}),-1/0)*D;return i=function(t,n){var i,r=t.reduce((function(t,e){return t+e.weight}),0);return t.map((function(t,a,o){return i=t.initialPosition,e.polygonContains(I.clip(),i)||(i=y(t,a,o,n)),{index:t.index,targetedArea:s*t.weight/r,data:t,x:i[0],y:i[1],weight:t.initialWeight}}))}(t.map((function(t,e,i){return{index:e,weight:Math.max(x(t),r),initialPosition:P(t,e,i,n),initialWeight:A(t,e,i,n),originalData:t}})),n),S(i),I(i)}(t,v),p=!1,M=!1}function F(t){var e,n,i,r,a,s,l,h=0;do{if(h>R)throw new c("handleOverweighted0 is looping too much");e=!1;for(var u=0;u<o;u++){n=t[u];for(var d=u+1;d<o;d++)if(i=t[d],n.weight>i.weight?(r=n,a=i):(r=i,a=n),(s=L(n,i))<r.weight-a.weight){l=s+a.weight/2,l=Math.max(l,w),r.weight=l,e=!0,h++;break}if(e)break}}while(e)}function V(t){var e,n,i,r,a,s,l,h=0;do{if(h>R)throw new c("handleOverweighted1 is looping too much");e=!1;for(var u=0;u<o;u++){n=t[u];for(var d=u+1;d<o;d++)if(i=t[d],n.weight>i.weight?(r=n,a=i):(r=i,a=n),(s=L(n,i))<r.weight-a.weight){l=r.weight-a.weight-s,a.weight+=l+w,e=!0,h++;break}if(e)break}}while(e)}return v={tick:B,restart:function(){return O.restart(H),v},stop:function(){return O.stop(),v},weight:function(t){return arguments.length?(x=t,M=!0,v):x},convergenceRatio:function(t){return arguments.length?(_=t,M=!0,v):_},maxIterationCount:function(t){return arguments.length?(k=t,v):k},minWeightRatio:function(t){return arguments.length?(D=t,M=!0,v):D},clip:function(t){return arguments.length?(I.clip(t),M=!0,v):I.clip()},extent:function(t){return arguments.length?(I.extent(t),M=!0,v):I.extent()},size:function(t){return arguments.length?(I.size(t),M=!0,v):I.size()},prng:function(t){return arguments.length?(N=t,M=!0,v):N},initialPosition:function(t){return arguments.length?(P=t,M=!0,v):P},initialWeight:function(t){return arguments.length?(A=t,M=!0,v):A},state:function(){return M&&W(),{ended:p,iterationCount:d,convergenceRatio:g/s,polygons:f}},on:function(t,e){return 1===arguments.length?z.on(t):(z.on(t,e),v)}},v},t.voronoiMapInitialPositionRandom=h,t.voronoiMapInitialPositionPie=function(){var t,n,i,r,a,o,s=0;function l(l,u,c,d){var f=!1;return t!==d.clip()&&(t=d.clip(),f|=!0),n!==c&&(n=c,f|=!0),f&&(r=e.polygonCentroid(t),a=function(t,e){for(var n,i=1/0,r=0,a=e[e.length-1],o=e[r];r<e.length;)(n=h(t,a,o))<i&&(i=n),a=o,o=e[++r];return i}(r,t)/2,i=n.length,o=2*Math.PI/i),[r[0]+Math.cos(s+u*o)*a+.001*(d.prng()()-.5),r[1]+Math.sin(s+u*o)*a+.001*(d.prng()()-.5)]}function h(t,e,n){var i,r,a=t[0],o=t[1],s=e[0],l=e[1],h=n[0],u=n[1],c=h-s,d=u-l,f=c*c+d*d,g=-1;0!=f&&(g=((a-s)*c+(o-l)*d)/f),g<0?(i=s,r=l):g>1?(i=h,r=u):(i=s+g*c,r=l+g*d);var p=a-i,v=o-r;return Math.sqrt(p*p+v*v)}return l.startAngle=function(t){return arguments.length?(s=t,l):s},l},t.d3VoronoiMapError=c,Object.defineProperty(t,"__esModule",{value:!0})}(e,n(7832),n(3677),n(9594),n(9062))},9594:function(t,e,n){"use strict";n.r(e),n.d(e,{dispatch:function(){return l}});var i={value:()=>{}};function r(){for(var t,e=0,n=arguments.length,i={};e<n;++e){if(!(t=arguments[e]+"")||t in i||/[\s.]/.test(t))throw new Error("illegal type: "+t);i[t]=[]}return new a(i)}function a(t){this._=t}function o(t,e){for(var n,i=0,r=t.length;i<r;++i)if((n=t[i]).name===e)return n.value}function s(t,e,n){for(var r=0,a=t.length;r<a;++r)if(t[r].name===e){t[r]=i,t=t.slice(0,r).concat(t.slice(r+1));break}return null!=n&&t.push({name:e,value:n}),t}a.prototype=r.prototype={constructor:a,on:function(t,e){var n,i,r=this._,a=(i=r,(t+"").trim().split(/^|\s+/).map((function(t){var e="",n=t.indexOf(".");if(n>=0&&(e=t.slice(n+1),t=t.slice(0,n)),t&&!i.hasOwnProperty(t))throw new Error("unknown type: "+t);return{type:t,name:e}}))),l=-1,h=a.length;if(!(arguments.length<2)){if(null!=e&&"function"!=typeof e)throw new Error("invalid callback: "+e);for(;++l<h;)if(n=(t=a[l]).type)r[n]=s(r[n],t.name,e);else if(null==e)for(n in r)r[n]=s(r[n],t.name,null);return this}for(;++l<h;)if((n=(t=a[l]).type)&&(n=o(r[n],t.name)))return n},copy:function(){var t={},e=this._;for(var n in e)t[n]=e[n].slice();return new a(t)},call:function(t,e){if((n=arguments.length-2)>0)for(var n,i,r=new Array(n),a=0;a<n;++a)r[a]=arguments[a+2];if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(a=0,n=(i=this._[t]).length;a<n;++a)i[a].value.apply(e,r)},apply:function(t,e,n){if(!this._.hasOwnProperty(t))throw new Error("unknown type: "+t);for(var i=this._[t],r=0,a=i.length;r<a;++r)i[r].value.apply(e,n)}};var l=r},3677:function(t,e,n){"use strict";n.r(e),n.d(e,{interval:function(){return k},now:function(){return g},timeout:function(){return _},timer:function(){return m},timerFlush:function(){return y}});var i,r,a=0,o=0,s=0,l=1e3,h=0,u=0,c=0,d="object"==typeof performance&&performance.now?performance:Date,f="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(t){setTimeout(t,17)};function g(){return u||(f(p),u=d.now()+c)}function p(){u=0}function v(){this._call=this._time=this._next=null}function m(t,e,n){var i=new v;return i.restart(t,e,n),i}function y(){g(),++a;for(var t,e=i;e;)(t=u-e._time)>=0&&e._call.call(null,t),e=e._next;--a}function b(){u=(h=d.now())+c,a=o=0;try{y()}finally{a=0,function(){for(var t,e,n=i,a=1/0;n;)n._call?(a>n._time&&(a=n._time),t=n,n=n._next):(e=n._next,n._next=null,n=t?t._next=e:i=e);r=t,x(a)}(),u=0}}function w(){var t=d.now(),e=t-h;e>l&&(c-=e,h=t)}function x(t){a||(o&&(o=clearTimeout(o)),t-u>24?(t<1/0&&(o=setTimeout(b,t-d.now()-c)),s&&(s=clearInterval(s))):(s||(h=d.now(),s=setInterval(w,l)),a=1,f(b)))}function _(t,e,n){var i=new v;return e=null==e?0:+e,i.restart((n=>{i.stop(),t(n+e)}),e,n),i}function k(t,e,n){var i=new v,r=e;return null==e?(i.restart(t,e,n),i):(i._restart=i.restart,i.restart=function(t,e,n){e=+e,n=null==n?g():+n,i._restart((function a(o){o+=r,i._restart(a,r+=e,n),t(o)}),e,n)},i.restart(t,e,n),i)}v.prototype=m.prototype={constructor:v,restart:function(t,e,n){if("function"!=typeof t)throw new TypeError("callback is not a function");n=(null==n?g():+n)+(null==e?0:+e),this._next||r===this||(r?r._next=this:i=this,r=this),this._call=t,this._time=n,x()},stop:function(){this._call&&(this._call=null,this._time=1/0,x())}}},9384:function(t,e,n){!function(t,e){"use strict";t.voronoiTreemap=function(){var t=Math.random,n=[[0,0],[0,1],[1,1],[1,0]],i=[[0,0],[1,1]],r=[1,1],a=.01,o=50,s=.01,l=t,h=e.voronoiMapSimulation([{weight:1},{weight:1}]).stop();function u(t){c(n,t)}function c(t,n){var i;if(n.polygon=t,0!=n.height){for(var r=(i=e.voronoiMapSimulation(n.children).clip(t).weight((function(t){return t.value})).convergenceRatio(a).maxIterationCount(o).minWeightRatio(s).prng(l).stop()).state();!r.ended;)i.tick(),r=i.state();r.polygons.forEach((function(t){c(t,t.site.originalObject.data.originalData)}))}}return u.convergenceRatio=function(t){return arguments.length?(a=t,u):a},u.maxIterationCount=function(t){return arguments.length?(o=t,u):o},u.minWeightRatio=function(t){return arguments.length?(s=t,u):s},u.clip=function(t){return arguments.length?(h.clip(t),n=h.clip(),i=h.extent(),r=h.size(),u):n},u.extent=function(t){return arguments.length?(h.extent(t),n=h.clip(),i=h.extent(),r=h.size(),u):i},u.size=function(t){return arguments.length?(h.size(t),n=h.clip(),i=h.extent(),r=h.size(),u):r},u.prng=function(t){return arguments.length?(l=t,u):l},u},Object.defineProperty(t,"__esModule",{value:!0})}(e,n(6159))},9062:function(t,e,n){!function(t,e,n){"use strict";var i=1e-10;function r(t){return t<=i&&t>=-i}function a(t,e){return t.x*e.x+t.y*e.y+t.z*e.z}function o(t,e){return r(t.x*e.y-t.y*e.x)&&r(t.y*e.z-t.z*e.y)&&r(t.z*e.x-t.x*e.z)}function s(t,e){return[e[0]-t[0],e[1]-t[1]]}function l(t,e){return t[0]*e[1]-t[1]*e[0]}function h(t,e){this.face=t,this.vert=e,this.nextf=null,this.prevf=null,this.nextv=null,this.prevv=null}function u(t){this.forFace=t,this.head=null}function c(t,e,n,r,a,o){this.x=t,this.y=e,this.weight=i,this.index=0,this.conflicts=new u(!1),this.neighbours=null,this.nonClippedPolygon=null,this.polygon=null,this.originalObject=null,this.isDummy=!1,void 0!==a&&(this.originalObject=a),null!=o&&(this.isDummy=o),null!=r&&(this.weight=r),this.z=null!=n?n:this.projectZ(this.x,this.y,this.weight)}function d(t){var e=t.verts[0],n=t.verts[1],i=t.verts[2];this.a=e.y*(n.z-i.z)+n.y*(i.z-e.z)+i.y*(e.z-n.z),this.b=e.z*(n.x-i.x)+n.z*(i.x-e.x)+i.z*(e.x-n.x),this.c=e.x*(n.y-i.y)+n.x*(i.y-e.y)+i.x*(e.y-n.y),this.d=-1*(e.x*(n.y*i.z-i.y*n.z)+n.x*(i.y*e.z-e.y*i.z)+i.x*(e.y*n.z-n.y*e.z))}function f(t,e){this.x=t,this.y=e}function g(t,e,n){this.x=t,this.y=e,this.z=n}function p(t,e,n){this.next=null,this.prev=null,this.twin=null,this.orig=t,this.dest=e,this.iFace=n}function v(t){this.message=t,this.stack=(new Error).stack}function m(t,e,n,i){this.conflicts=new u(!0),this.verts=[t,e,n],this.marked=!1;var r=t.subtract(e).crossproduct(e.subtract(n));this.normal=new g(-r.x,-r.y,-r.z),this.normal.normalize(),this.createEdges(),this.dualPoint=null,null!=i&&this.orient(i)}function y(){this.points=[],this.facets=[],this.created=[],this.horizon=[],this.visible=[],this.current=0}function b(t,e){for(var n,i,r,a,o,s,l,h=_(e),u=-1,c=t.length-_(t),d=t[c-1];++u<c;){for(n=e.slice(),e.length=0,a=t[u],o=n[(r=n.length-h)-1],i=-1;++i<r;)w(s=n[i],d,a)?(w(o,d,a)||(l=x(o,s,d,a),isFinite(l[0])&&e.push(l)),e.push(s)):w(o,d,a)&&(l=x(o,s,d,a),isFinite(l[0])&&e.push(l)),o=s;h&&e.push(e[0]),d=a}return e}function w(t,e,n){return(n[0]-e[0])*(t[1]-e[1])<(n[1]-e[1])*(t[0]-e[0])}function x(t,e,n,i){var r=t[0],a=n[0],o=e[0]-r,s=i[0]-a,l=t[1],h=n[1],u=e[1]-l,c=i[1]-h,d=(s*(l-h)-c*(r-a))/(c*o-s*u);return[r+d*o,l+d*u]}function _(t){var e=t[0],n=t[t.length-1];return!(e[0]-n[0]||e[1]-n[1])}function k(t){var e=[],n=t,i=t.dest.originalObject,r=[];do{var a=(n=n.twin.prev).orig.originalObject;a.isDummy||r.push(a);var o=n.iFace;o.isVisibleFromBelow()&&e.push(o)}while(n!==t);return i.neighbours=r,e}u.prototype.add=function(t){null===this.head?this.head=t:this.forFace?(this.head.prevv=t,t.nextv=this.head,this.head=t):(this.head.prevf=t,t.nextf=this.head,this.head=t)},u.prototype.isEmpty=function(){return null===this.head},u.prototype.fill=function(t){if(!this.forFace){var e=this.head;do{t.push(e.face),e.face.marked=!0,e=e.nextf}while(null!==e)}},u.prototype.removeAll=function(){if(this.forFace){var t=this.head;do{null===t.prevf?null===t.nextf?t.vert.conflicts.head=null:(t.nextf.prevf=null,t.vert.conflicts.head=t.nextf):(null!=t.nextf&&(t.nextf.prevf=t.prevf),t.prevf.nextf=t.nextf),null!=(t=t.nextv)&&(t.prevv=null)}while(null!=t)}else{t=this.head;do{null==t.prevv?null==t.nextv?t.face.conflicts.head=null:(t.nextv.prevv=null,t.face.conflicts.head=t.nextv):(null!=t.nextv&&(t.nextv.prevv=t.prevv),t.prevv.nextv=t.nextv),null!=(t=t.nextf)&&(t.prevf=null)}while(null!=t)}},u.prototype.getVertices=function(){for(var t=[],e=this.head;null!==e;)t.push(e.vert),e=e.nextv;return t},c.prototype.projectZ=function(t,e,n){return t*t+e*e-n},c.prototype.setWeight=function(t){this.weight=t,this.z=this.projectZ(this.x,this.y,this.weight)},c.prototype.subtract=function(t){return new c(t.x-this.x,t.y-this.y,t.z-this.z)},c.prototype.crossproduct=function(t){return new c(this.y*t.z-this.z*t.y,this.z*t.x-this.x*t.z,this.x*t.y-this.y*t.x)},c.prototype.equals=function(t){return this.x===t.x&&this.y===t.y&&this.z===t.z},d.prototype.getNormZPlane=function(){return[this.a/this.c*-1,this.b/this.c*-1,this.d/this.c*-1]},d.prototype.getDualPointMappedToPlane=function(){var t=this.getNormZPlane();return new f(t[0]/2,t[1]/2)},g.prototype.negate=function(){this.x*=-1,this.y*=-1,this.z*=-1},g.prototype.normalize=function(){var t=Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z);t>0&&(this.x/=t,this.y/=t,this.z/=t)},p.prototype.isHorizon=function(){return null!==this.twin&&!this.iFace.marked&&this.twin.iFace.marked},p.prototype.findHorizon=function(t){if(this.isHorizon()){if(t.length>0&&this===t[0])return;t.push(this),this.next.findHorizon(t)}else null!==this.twin&&this.twin.next.findHorizon(t)},p.prototype.isEqual=function(t,e){return this.orig.equals(t)&&this.dest.equals(e)||this.orig.equals(e)&&this.dest.equals(t)},v.prototype.name="d3WeightedVoronoiError",v.prototype=new Error,m.prototype.getDualPoint=function(){if(null==this.dualPoint){var t=new d(this);this.dualPoint=t.getDualPointMappedToPlane()}return this.dualPoint},m.prototype.isVisibleFromBelow=function(){return this.normal.z<-1.4259414393190911e-9},m.prototype.createEdges=function(){this.edges=[],this.edges[0]=new p(this.verts[0],this.verts[1],this),this.edges[1]=new p(this.verts[1],this.verts[2],this),this.edges[2]=new p(this.verts[2],this.verts[0],this),this.edges[0].next=this.edges[1],this.edges[0].prev=this.edges[2],this.edges[1].next=this.edges[2],this.edges[1].prev=this.edges[0],this.edges[2].next=this.edges[0],this.edges[2].prev=this.edges[1]},m.prototype.orient=function(t){if(!(a(this.normal,t)<a(this.normal,this.verts[0]))){var e=this.verts[1];this.verts[1]=this.verts[2],this.verts[2]=e,this.normal.negate(),this.createEdges()}},m.prototype.getEdge=function(t,e){for(var n=0;n<3;n++)if(this.edges[n].isEqual(t,e))return this.edges[n];return null},m.prototype.link=function(t,e,n){if(t instanceof m){if(null===(i=t.getEdge(e,n)))throw new v("when linking, twin is null");if(null===(r=this.getEdge(e,n)))throw new v("when linking, twin is null");i.twin=r,r.twin=i}else{var i=t,r=this.getEdge(i.orig,i.dest);i.twin=r,r.twin=i}},m.prototype.conflict=function(t){return a(this.normal,t)>a(this.normal,this.verts[0])+i},m.prototype.getHorizon=function(){for(var t=0;t<3;t++)if(null!==this.edges[t].twin&&this.edges[t].twin.isHorizon())return this.edges[t];return null},m.prototype.removeConflict=function(){this.conflicts.removeAll()},y.prototype.init=function(t,e){this.points=[];for(var n=0;n<e.length;n++)this.points[n]=new c(e[n].x,e[n].y,e[n].z,null,e[n],!1);this.points=this.points.concat(t)},y.prototype.permutate=function(){for(var t=this.points.length-1;t>0;t--){var e=Math.floor(Math.random()*t),n=this.points[e];n.index=t;var i=this.points[t];i.index=e,this.points.splice(e,1,i),this.points.splice(t,1,n)}},y.prototype.prep=function(){if(this.points.length<=3)throw new v("Less than 4 points");for(var t=0;t<this.points.length;t++)this.points[t].index=t;var e,n,i,s,l,h,u,c,d;for(e=this.points[0],n=this.points[1],i=s=null,t=2;t<this.points.length;t++)if(!o(e,this.points[t])||!o(n,this.points[t])){(i=this.points[t]).index=2,this.points[2].index=t,this.points.splice(t,1,this.points[2]),this.points.splice(2,1,i);break}if(null===i)throw new v("Not enough non-planar Points (v2 is null)");for(c=new m(e,n,i),t=3;t<this.points.length;t++)if(!r(a(c.normal,c.verts[0])-a(c.normal,this.points[t]))){(s=this.points[t]).index=3,this.points[3].index=t,this.points.splice(t,1,this.points[3]),this.points.splice(3,1,s);break}if(null===s)throw new v("Not enough non-planar Points (v3 is null)");for(c.orient(s),l=new m(e,i,s,n),h=new m(e,n,s,i),u=new m(n,i,s,e),this.addFacet(c),this.addFacet(l),this.addFacet(h),this.addFacet(u),c.link(l,e,i),c.link(h,e,n),c.link(u,n,i),l.link(h,e,s),l.link(u,i,s),h.link(u,s,n),this.current=4,t=this.current;t<this.points.length;t++)d=this.points[t],c.conflict(d)&&this.addConflict(c,d),l.conflict(d)&&this.addConflict(l,d),h.conflict(d)&&this.addConflict(h,d),u.conflict(d)&&this.addConflict(u,d)},y.prototype.addConflicts=function(t,e,n){var i,r,a,o=t.conflicts.getVertices(),s=e.conflicts.getVertices(),l=[];for(h=a=0;h<o.length||a<s.length;)h<o.length&&a<s.length?(i=o[h],r=s[a],i.index===r.index?(l.push(i),h++,a++):i.index>r.index?(l.push(i),h++):(l.push(r),a++)):h<o.length?l.push(o[h++]):l.push(s[a++]);for(var h=l.length-1;h>=0;h--)i=l[h],n.conflict(i)&&this.addConflict(n,i)},y.prototype.addConflict=function(t,e){var n=new h(t,e);t.conflicts.add(n),e.conflicts.add(n)},y.prototype.removeConflict=function(t){t.removeConflict();var e=t.index;if(t.index=-1,e!==this.facets.length-1){if(!(e>=this.facets.length||e<0)){var n=this.facets.splice(this.facets.length-1,1);n[0].index=e,this.facets.splice(e,1,n[0])}}else this.facets.splice(this.facets.length-1,1)},y.prototype.addFacet=function(t){t.index=this.facets.length,this.facets.push(t)},y.prototype.compute=function(){for(this.prep();this.current<this.points.length;){var t=this.points[this.current];if(t.conflicts.isEmpty())this.current++;else{var e;this.created=[],this.horizon=[],this.visible=[],t.conflicts.fill(this.visible);for(var n=0;n<this.visible.length;n++)if(null!==(e=this.visible[n].getHorizon())){e.findHorizon(this.horizon);break}for(var i=null,r=null,a=0;a<this.horizon.length;a++){var o=this.horizon[a],s=new m(t,o.orig,o.dest,o.twin.next.dest);s.conflicts=new u(!0),this.addFacet(s),this.created.push(s),this.addConflicts(o.iFace,o.twin.iFace,s),s.link(o),null!==i&&s.link(i,t,o.orig),i=s,null===r&&(r=s)}if(null!==r&&null!==i&&i.link(r,t,this.horizon[0].orig),0!=this.created.length){for(var l=0;l<this.visible.length;l++)this.removeConflict(this.visible[l]);this.current++,this.created=[]}}}return this.facets},y.prototype.clear=function(){this.points=[],this.facets=[],this.created=[],this.horizon=[],this.visible=[],this.current=0},t.weightedVoronoi=function(){var t=function(t){return t.x},r=function(t){return t.y},a=function(t){return t.weight},o=[[0,0],[0,1],[1,1],[1,0]],h=[[0,0],[1,1]],u=[1,1];function d(e){return function(t,e,r){var a=new y;a.clear(),a.init(e,t);for(var o=a.compute(t),s=[],l=[],h=o.length,u=0;u<h;u++){var c=o[u];if(c.isVisibleFromBelow())for(var d=0;d<3;d++){var f=c.edges[d],g=f.dest,p=g.originalObject;if(!l[g.index]){if(l[g.index]=!0,p.isDummy)continue;for(var v=k(f),m=[],w=null,x=null,_=1,D=1,N=0;N<v.length;N++){var P=v[N].getDualPoint(),A=P.x,I=P.y;null!==w&&((_=w-A)<0&&(_=-_),(D=x-I)<0&&(D=-D)),(_>i||D>i)&&(m.push([A,I]),w=A,x=I)}if(p.nonClippedPolygon=m.reverse(),!p.isDummy&&n.polygonLength(p.nonClippedPolygon)>0){var C=b(r,p.nonClippedPolygon);p.polygon=C,C.site=p,C.length>0&&s.push(C)}}}}return s}(e.map((function(e){return new c(t(e),r(e),null,a(e),e,!1)})),function(){var t,e,n,r,a,o,s,l,u,d,f=[],g=[];s=(t=h[0][0])-(a=(e=h[1][0])-t),l=e+a,u=(n=h[0][1])-(o=(r=h[1][1])-n),d=r+o,f[0]=[s,u],f[1]=[s,d],f[2]=[l,d],f[3]=[l,u];for(var p=0;p<4;p++)g.push(new c(f[p][0],f[p][1],null,i,new c(f[p][0],f[p][1],null,i,null,!0),!0));return g}(),o)}return d.x=function(e){return arguments.length?(t=e,d):t},d.y=function(t){return arguments.length?(r=t,d):r},d.weight=function(t){return arguments.length?(a=t,d):a},d.clip=function(t){var i,r,a;return arguments.length?(r=e.extent(t.map((function(t){return t[0]}))),a=e.extent(t.map((function(t){return t[1]}))),i=function(t){var e,n,i,r,a,o,h;if(i=t[t.length-2],r=t[t.length-1],a=t[0],n=l(s(i,r),o=s(r,a)),e=Math.sign(n),i=r,n=l(o,o=s(r=a,a=t[1])),Math.sign(n)===e){for(h=2;h<t.length-1;h++)if(i=r,n=l(o,o=s(r=a,a=t[h])),Math.sign(n)!==e)return;return e}}(t),o=void 0===i?n.polygonHull(t):1===i?t.reverse():t,h=[[r[0],a[0]],[r[1],a[1]]],u=[r[1]-r[0],a[1]-a[0]],d):o},d.extent=function(t){return arguments.length?(o=[t[0],[t[0][0],t[1][1]],t[1],[t[1][0],t[0][1]]],h=t,u=[t[1][0]-t[0][0],t[1][1]-t[0][1]],d):h},d.size=function(t){return arguments.length?(o=[[0,0],[0,t[1]],[t[0],t[1]],[t[0],0]],h=[[0,0],t],u=t,d):u},d},t.d3WeightedVoronoiError=v,Object.defineProperty(t,"__esModule",{value:!0})}(e,n(6087),n(7832))},6087:function(t,e,n){"use strict";function i(t,e){return t<e?-1:t>e?1:t>=e?0:NaN}function r(t){let e=t,n=t;function r(t,e,i,r){for(null==i&&(i=0),null==r&&(r=t.length);i<r;){const a=i+r>>>1;n(t[a],e)<0?i=a+1:r=a}return i}return 1===t.length&&(e=(e,n)=>t(e)-n,n=function(t){return(e,n)=>i(t(e),n)}(t)),{left:r,center:function(t,n,i,a){null==i&&(i=0),null==a&&(a=t.length);const o=r(t,n,i,a-1);return o>i&&e(t[o-1],n)>-e(t[o],n)?o-1:o},right:function(t,e,i,r){for(null==i&&(i=0),null==r&&(r=t.length);i<r;){const a=i+r>>>1;n(t[a],e)>0?r=a:i=a+1}return i}}}function a(t){return null===t?NaN:+t}n.r(e),n.d(e,{Adder:function(){return x},InternMap:function(){return D},InternSet:function(){return N},ascending:function(){return i},bin:function(){return $},bisect:function(){return u},bisectCenter:function(){return h},bisectLeft:function(){return l},bisectRight:function(){return s},bisector:function(){return r},count:function(){return c},cross:function(){return p},cumsum:function(){return v},descending:function(){return m},deviation:function(){return b},difference:function(){return Tt},disjoint:function(){return Rt},every:function(){return At},extent:function(){return w},fcumsum:function(){return k},filter:function(){return Ct},fsum:function(){return _},greatest:function(){return yt},greatestIndex:function(){return bt},group:function(){return O},groupSort:function(){return F},groups:function(){return z},histogram:function(){return $},index:function(){return S},indexes:function(){return j},intersection:function(){return jt},least:function(){return vt},leastIndex:function(){return mt},map:function(){return Mt},max:function(){return tt},maxIndex:function(){return lt},mean:function(){return ht},median:function(){return ut},merge:function(){return ct},min:function(){return et},minIndex:function(){return dt},nice:function(){return U},pairs:function(){return ft},permute:function(){return B},quantile:function(){return rt},quantileSorted:function(){return at},quickselect:function(){return nt},range:function(){return pt},reduce:function(){return Ot},reverse:function(){return zt},rollup:function(){return T},rollups:function(){return R},scan:function(){return wt},shuffle:function(){return xt},shuffler:function(){return _t},some:function(){return It},sort:function(){return W},subset:function(){return Ht},sum:function(){return kt},superset:function(){return Lt},thresholdFreedmanDiaconis:function(){return ot},thresholdScott:function(){return st},thresholdSturges:function(){return J},tickIncrement:function(){return K},tickStep:function(){return Q},ticks:function(){return Z},transpose:function(){return Dt},union:function(){return Bt},variance:function(){return y},zip:function(){return Pt}});const o=r(i),s=o.right,l=o.left,h=r(a).center;var u=s;function c(t,e){let n=0;if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&++n;else{let i=-1;for(let r of t)null!=(r=e(r,++i,t))&&(r=+r)>=r&&++n}return n}function d(t){return 0|t.length}function f(t){return!(t>0)}function g(t){return"object"!=typeof t||"length"in t?t:Array.from(t)}function p(...t){const e="function"==typeof t[t.length-1]&&function(t){return e=>t(...e)}(t.pop()),n=(t=t.map(g)).map(d),i=t.length-1,r=new Array(i+1).fill(0),a=[];if(i<0||n.some(f))return a;for(;;){a.push(r.map(((e,n)=>t[n][e])));let o=i;for(;++r[o]===n[o];){if(0===o)return e?a.map(e):a;r[o--]=0}}}function v(t,e){var n=0,i=0;return Float64Array.from(t,void 0===e?t=>n+=+t||0:r=>n+=+e(r,i++,t)||0)}function m(t,e){return e<t?-1:e>t?1:e>=t?0:NaN}function y(t,e){let n,i=0,r=0,a=0;if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(n=e-r,r+=n/++i,a+=n*(e-r));else{let o=-1;for(let s of t)null!=(s=e(s,++o,t))&&(s=+s)>=s&&(n=s-r,r+=n/++i,a+=n*(s-r))}if(i>1)return a/(i-1)}function b(t,e){const n=y(t,e);return n?Math.sqrt(n):n}function w(t,e){let n,i;if(void 0===e)for(const e of t)null!=e&&(void 0===n?e>=e&&(n=i=e):(n>e&&(n=e),i<e&&(i=e)));else{let r=-1;for(let a of t)null!=(a=e(a,++r,t))&&(void 0===n?a>=a&&(n=i=a):(n>a&&(n=a),i<a&&(i=a)))}return[n,i]}class x{constructor(){this._partials=new Float64Array(32),this._n=0}add(t){const e=this._partials;let n=0;for(let i=0;i<this._n&&i<32;i++){const r=e[i],a=t+r,o=Math.abs(t)<Math.abs(r)?t-(a-r):r-(a-t);o&&(e[n++]=o),t=a}return e[n]=t,this._n=n+1,this}valueOf(){const t=this._partials;let e,n,i,r=this._n,a=0;if(r>0){for(a=t[--r];r>0&&(e=a,n=t[--r],a=e+n,i=n-(a-e),!i););r>0&&(i<0&&t[r-1]<0||i>0&&t[r-1]>0)&&(n=2*i,e=a+n,n==e-a&&(a=e))}return a}}function _(t,e){const n=new x;if(void 0===e)for(let e of t)(e=+e)&&n.add(e);else{let i=-1;for(let r of t)(r=+e(r,++i,t))&&n.add(r)}return+n}function k(t,e){const n=new x;let i=-1;return Float64Array.from(t,void 0===e?t=>n.add(+t||0):r=>n.add(+e(r,++i,t)||0))}class D extends Map{constructor(t,e=C){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const[e,n]of t)this.set(e,n)}get(t){return super.get(P(this,t))}has(t){return super.has(P(this,t))}set(t,e){return super.set(A(this,t),e)}delete(t){return super.delete(I(this,t))}}class N extends Set{constructor(t,e=C){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(const e of t)this.add(e)}has(t){return super.has(P(this,t))}add(t){return super.add(A(this,t))}delete(t){return super.delete(I(this,t))}}function P({_intern:t,_key:e},n){const i=e(n);return t.has(i)?t.get(i):n}function A({_intern:t,_key:e},n){const i=e(n);return t.has(i)?t.get(i):(t.set(i,n),n)}function I({_intern:t,_key:e},n){const i=e(n);return t.has(i)&&(n=t.get(n),t.delete(i)),n}function C(t){return null!==t&&"object"==typeof t?t.valueOf():t}function M(t){return t}function O(t,...e){return H(t,M,M,e)}function z(t,...e){return H(t,Array.from,M,e)}function T(t,e,...n){return H(t,M,e,n)}function R(t,e,...n){return H(t,Array.from,e,n)}function S(t,...e){return H(t,M,L,e)}function j(t,...e){return H(t,Array.from,L,e)}function L(t){if(1!==t.length)throw new Error("duplicate key");return t[0]}function H(t,e,n,i){return function t(r,a){if(a>=i.length)return n(r);const o=new D,s=i[a++];let l=-1;for(const t of r){const e=s(t,++l,r),n=o.get(e);n?n.push(t):o.set(e,[t])}for(const[e,n]of o)o.set(e,t(n,a));return e(o)}(t,0)}function B(t,e){return Array.from(e,(e=>t[e]))}function W(t,...e){if("function"!=typeof t[Symbol.iterator])throw new TypeError("values is not iterable");t=Array.from(t);let[n=i]=e;if(1===n.length||e.length>1){const r=Uint32Array.from(t,((t,e)=>e));return e.length>1?(e=e.map((e=>t.map(e))),r.sort(((t,n)=>{for(const r of e){const e=i(r[t],r[n]);if(e)return e}}))):(n=t.map(n),r.sort(((t,e)=>i(n[t],n[e])))),B(t,r)}return t.sort(n)}function F(t,e,n){return(1===e.length?W(T(t,e,n),(([t,e],[n,r])=>i(e,r)||i(t,n))):W(O(t,n),(([t,n],[r,a])=>e(n,a)||i(t,r)))).map((([t])=>t))}var V=Array.prototype,E=V.slice;function q(t){return function(){return t}}V.map;var Y=Math.sqrt(50),X=Math.sqrt(10),G=Math.sqrt(2);function Z(t,e,n){var i,r,a,o,s=-1;if(n=+n,(t=+t)==(e=+e)&&n>0)return[t];if((i=e<t)&&(r=t,t=e,e=r),0===(o=K(t,e,n))||!isFinite(o))return[];if(o>0){let n=Math.round(t/o),i=Math.round(e/o);for(n*o<t&&++n,i*o>e&&--i,a=new Array(r=i-n+1);++s<r;)a[s]=(n+s)*o}else{o=-o;let n=Math.round(t*o),i=Math.round(e*o);for(n/o<t&&++n,i/o>e&&--i,a=new Array(r=i-n+1);++s<r;)a[s]=(n+s)/o}return i&&a.reverse(),a}function K(t,e,n){var i=(e-t)/Math.max(0,n),r=Math.floor(Math.log(i)/Math.LN10),a=i/Math.pow(10,r);return r>=0?(a>=Y?10:a>=X?5:a>=G?2:1)*Math.pow(10,r):-Math.pow(10,-r)/(a>=Y?10:a>=X?5:a>=G?2:1)}function Q(t,e,n){var i=Math.abs(e-t)/Math.max(0,n),r=Math.pow(10,Math.floor(Math.log(i)/Math.LN10)),a=i/r;return a>=Y?r*=10:a>=X?r*=5:a>=G&&(r*=2),e<t?-r:r}function U(t,e,n){let i;for(;;){const r=K(t,e,n);if(r===i||0===r||!isFinite(r))return[t,e];r>0?(t=Math.floor(t/r)*r,e=Math.ceil(e/r)*r):r<0&&(t=Math.ceil(t*r)/r,e=Math.floor(e*r)/r),i=r}}function J(t){return Math.ceil(Math.log(c(t))/Math.LN2)+1}function $(){var t=M,e=w,n=J;function i(i){Array.isArray(i)||(i=Array.from(i));var r,a,o=i.length,s=new Array(o);for(r=0;r<o;++r)s[r]=t(i[r],r,i);var l=e(s),h=l[0],c=l[1],d=n(s,h,c);if(!Array.isArray(d)){const t=c,n=+d;if(e===w&&([h,c]=U(h,c,n)),(d=Z(h,c,n))[d.length-1]>=c)if(t>=c&&e===w){const t=K(h,c,n);isFinite(t)&&(t>0?c=(Math.floor(c/t)+1)*t:t<0&&(c=(Math.ceil(c*-t)+1)/-t))}else d.pop()}for(var f=d.length;d[0]<=h;)d.shift(),--f;for(;d[f-1]>c;)d.pop(),--f;var g,p=new Array(f+1);for(r=0;r<=f;++r)(g=p[r]=[]).x0=r>0?d[r-1]:h,g.x1=r<f?d[r]:c;for(r=0;r<o;++r)h<=(a=s[r])&&a<=c&&p[u(d,a,0,f)].push(i[r]);return p}return i.value=function(e){return arguments.length?(t="function"==typeof e?e:q(e),i):t},i.domain=function(t){return arguments.length?(e="function"==typeof t?t:q([t[0],t[1]]),i):e},i.thresholds=function(t){return arguments.length?(n="function"==typeof t?t:Array.isArray(t)?q(E.call(t)):q(t),i):n},i}function tt(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n<e||void 0===n&&e>=e)&&(n=e);else{let i=-1;for(let r of t)null!=(r=e(r,++i,t))&&(n<r||void 0===n&&r>=r)&&(n=r)}return n}function et(t,e){let n;if(void 0===e)for(const e of t)null!=e&&(n>e||void 0===n&&e>=e)&&(n=e);else{let i=-1;for(let r of t)null!=(r=e(r,++i,t))&&(n>r||void 0===n&&r>=r)&&(n=r)}return n}function nt(t,e,n=0,r=t.length-1,a=i){for(;r>n;){if(r-n>600){const i=r-n+1,o=e-n+1,s=Math.log(i),l=.5*Math.exp(2*s/3),h=.5*Math.sqrt(s*l*(i-l)/i)*(o-i/2<0?-1:1);nt(t,e,Math.max(n,Math.floor(e-o*l/i+h)),Math.min(r,Math.floor(e+(i-o)*l/i+h)),a)}const i=t[e];let o=n,s=r;for(it(t,n,e),a(t[r],i)>0&&it(t,n,r);o<s;){for(it(t,o,s),++o,--s;a(t[o],i)<0;)++o;for(;a(t[s],i)>0;)--s}0===a(t[n],i)?it(t,n,s):(++s,it(t,s,r)),s<=e&&(n=s+1),e<=s&&(r=s-1)}return t}function it(t,e,n){const i=t[e];t[e]=t[n],t[n]=i}function rt(t,e,n){if(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(i=+i)>=i&&(yield i)}}(t,n)),i=t.length){if((e=+e)<=0||i<2)return et(t);if(e>=1)return tt(t);var i,r=(i-1)*e,a=Math.floor(r),o=tt(nt(t,a).subarray(0,a+1));return o+(et(t.subarray(a+1))-o)*(r-a)}}function at(t,e,n=a){if(i=t.length){if((e=+e)<=0||i<2)return+n(t[0],0,t);if(e>=1)return+n(t[i-1],i-1,t);var i,r=(i-1)*e,o=Math.floor(r),s=+n(t[o],o,t);return s+(+n(t[o+1],o+1,t)-s)*(r-o)}}function ot(t,e,n){return Math.ceil((n-e)/(2*(rt(t,.75)-rt(t,.25))*Math.pow(c(t),-1/3)))}function st(t,e,n){return Math.ceil((n-e)/(3.5*b(t)*Math.pow(c(t),-1/3)))}function lt(t,e){let n,i=-1,r=-1;if(void 0===e)for(const e of t)++r,null!=e&&(n<e||void 0===n&&e>=e)&&(n=e,i=r);else for(let a of t)null!=(a=e(a,++r,t))&&(n<a||void 0===n&&a>=a)&&(n=a,i=r);return i}function ht(t,e){let n=0,i=0;if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(++n,i+=e);else{let r=-1;for(let a of t)null!=(a=e(a,++r,t))&&(a=+a)>=a&&(++n,i+=a)}if(n)return i/n}function ut(t,e){return rt(t,.5,e)}function ct(t){return Array.from(function*(t){for(const e of t)yield*e}(t))}function dt(t,e){let n,i=-1,r=-1;if(void 0===e)for(const e of t)++r,null!=e&&(n>e||void 0===n&&e>=e)&&(n=e,i=r);else for(let a of t)null!=(a=e(a,++r,t))&&(n>a||void 0===n&&a>=a)&&(n=a,i=r);return i}function ft(t,e=gt){const n=[];let i,r=!1;for(const a of t)r&&n.push(e(i,a)),i=a,r=!0;return n}function gt(t,e){return[t,e]}function pt(t,e,n){t=+t,e=+e,n=(r=arguments.length)<2?(e=t,t=0,1):r<3?1:+n;for(var i=-1,r=0|Math.max(0,Math.ceil((e-t)/n)),a=new Array(r);++i<r;)a[i]=t+i*n;return a}function vt(t,e=i){let n,r=!1;if(1===e.length){let a;for(const o of t){const t=e(o);(r?i(t,a)<0:0===i(t,t))&&(n=o,a=t,r=!0)}}else for(const i of t)(r?e(i,n)<0:0===e(i,i))&&(n=i,r=!0);return n}function mt(t,e=i){if(1===e.length)return dt(t,e);let n,r=-1,a=-1;for(const i of t)++a,(r<0?0===e(i,i):e(i,n)<0)&&(n=i,r=a);return r}function yt(t,e=i){let n,r=!1;if(1===e.length){let a;for(const o of t){const t=e(o);(r?i(t,a)>0:0===i(t,t))&&(n=o,a=t,r=!0)}}else for(const i of t)(r?e(i,n)>0:0===e(i,i))&&(n=i,r=!0);return n}function bt(t,e=i){if(1===e.length)return lt(t,e);let n,r=-1,a=-1;for(const i of t)++a,(r<0?0===e(i,i):e(i,n)>0)&&(n=i,r=a);return r}function wt(t,e){const n=mt(t,e);return n<0?void 0:n}var xt=_t(Math.random);function _t(t){return function(e,n=0,i=e.length){let r=i-(n=+n);for(;r;){const i=t()*r--|0,a=e[r+n];e[r+n]=e[i+n],e[i+n]=a}return e}}function kt(t,e){let n=0;if(void 0===e)for(let e of t)(e=+e)&&(n+=e);else{let i=-1;for(let r of t)(r=+e(r,++i,t))&&(n+=r)}return n}function Dt(t){if(!(r=t.length))return[];for(var e=-1,n=et(t,Nt),i=new Array(n);++e<n;)for(var r,a=-1,o=i[e]=new Array(r);++a<r;)o[a]=t[a][e];return i}function Nt(t){return t.length}function Pt(){return Dt(arguments)}function At(t,e){if("function"!=typeof e)throw new TypeError("test is not a function");let n=-1;for(const i of t)if(!e(i,++n,t))return!1;return!0}function It(t,e){if("function"!=typeof e)throw new TypeError("test is not a function");let n=-1;for(const i of t)if(e(i,++n,t))return!0;return!1}function Ct(t,e){if("function"!=typeof e)throw new TypeError("test is not a function");const n=[];let i=-1;for(const r of t)e(r,++i,t)&&n.push(r);return n}function Mt(t,e){if("function"!=typeof t[Symbol.iterator])throw new TypeError("values is not iterable");if("function"!=typeof e)throw new TypeError("mapper is not a function");return Array.from(t,((n,i)=>e(n,i,t)))}function Ot(t,e,n){if("function"!=typeof e)throw new TypeError("reducer is not a function");const i=t[Symbol.iterator]();let r,a,o=-1;if(arguments.length<3){if(({done:r,value:n}=i.next()),r)return;++o}for(;({done:r,value:a}=i.next()),!r;)n=e(n,a,++o,t);return n}function zt(t){if("function"!=typeof t[Symbol.iterator])throw new TypeError("values is not iterable");return Array.from(t).reverse()}function Tt(t,...e){t=new Set(t);for(const n of e)for(const e of n)t.delete(e);return t}function Rt(t,e){const n=e[Symbol.iterator](),i=new Set;for(const e of t){if(i.has(e))return!1;let t,r;for(;({value:t,done:r}=n.next())&&!r;){if(Object.is(e,t))return!1;i.add(t)}}return!0}function St(t){return t instanceof Set?t:new Set(t)}function jt(t,...e){t=new Set(t),e=e.map(St);t:for(const n of t)for(const i of e)if(!i.has(n)){t.delete(n);continue t}return t}function Lt(t,e){const n=t[Symbol.iterator](),i=new Set;for(const t of e){if(i.has(t))continue;let e,r;for(;({value:e,done:r}=n.next());){if(r)return!1;if(i.add(e),Object.is(t,e))break}}return!0}function Ht(t,e){return Lt(e,t)}function Bt(...t){const e=new Set;for(const n of t)for(const t of n)e.add(t);return e}},5720:function(t,e,n){var i=n(5971),r=n(7234),a=n(5019),o=n(4467),s=n(2309),l=n(5743),h=n(6543);h.alea=i,h.xor128=r,h.xorwow=a,h.xorshift7=o,h.xor4096=s,h.tychei=l,t.exports=h},5971:function(t,e,n){var i;!function(t,r,a){function o(t){var e,n=this,i=(e=4022871197,function(t){t=String(t);for(var n=0;n<t.length;n++){var i=.02519603282416938*(e+=t.charCodeAt(n));i-=e=i>>>0,e=(i*=e)>>>0,e+=4294967296*(i-=e)}return 2.3283064365386963e-10*(e>>>0)});n.next=function(){var t=2091639*n.s0+2.3283064365386963e-10*n.c;return n.s0=n.s1,n.s1=n.s2,n.s2=t-(n.c=0|t)},n.c=1,n.s0=i(" "),n.s1=i(" "),n.s2=i(" "),n.s0-=i(t),n.s0<0&&(n.s0+=1),n.s1-=i(t),n.s1<0&&(n.s1+=1),n.s2-=i(t),n.s2<0&&(n.s2+=1),i=null}function s(t,e){return e.c=t.c,e.s0=t.s0,e.s1=t.s1,e.s2=t.s2,e}function l(t,e){var n=new o(t),i=e&&e.state,r=n.next;return r.int32=function(){return 4294967296*n.next()|0},r.double=function(){return r()+11102230246251565e-32*(2097152*r()|0)},r.quick=r,i&&("object"==typeof i&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.alea=l}(0,t=n.nmd(t),n.amdD)},5743:function(t,e,n){var i;!function(t,r,a){function o(t){var e=this,n="";e.next=function(){var t=e.b,n=e.c,i=e.d,r=e.a;return t=t<<25^t>>>7^n,n=n-i|0,i=i<<24^i>>>8^r,r=r-t|0,e.b=t=t<<20^t>>>12^n,e.c=n=n-i|0,e.d=i<<16^n>>>16^r,e.a=r-t|0},e.a=0,e.b=0,e.c=-1640531527,e.d=1367130551,t===Math.floor(t)?(e.a=t/4294967296|0,e.b=0|t):n+=t;for(var i=0;i<n.length+20;i++)e.b^=0|n.charCodeAt(i),e.next()}function s(t,e){return e.a=t.a,e.b=t.b,e.c=t.c,e.d=t.d,e}function l(t,e){var n=new o(t),i=e&&e.state,r=function(){return(n.next()>>>0)/4294967296};return r.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},r.int32=n.next,r.quick=r,i&&("object"==typeof i&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.tychei=l}(0,t=n.nmd(t),n.amdD)},7234:function(t,e,n){var i;!function(t,r,a){function o(t){var e=this,n="";e.x=0,e.y=0,e.z=0,e.w=0,e.next=function(){var t=e.x^e.x<<11;return e.x=e.y,e.y=e.z,e.z=e.w,e.w^=e.w>>>19^t^t>>>8},t===(0|t)?e.x=t:n+=t;for(var i=0;i<n.length+64;i++)e.x^=0|n.charCodeAt(i),e.next()}function s(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e}function l(t,e){var n=new o(t),i=e&&e.state,r=function(){return(n.next()>>>0)/4294967296};return r.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},r.int32=n.next,r.quick=r,i&&("object"==typeof i&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.xor128=l}(0,t=n.nmd(t),n.amdD)},2309:function(t,e,n){var i;!function(t,r,a){function o(t){var e=this;e.next=function(){var t,n,i=e.w,r=e.X,a=e.i;return e.w=i=i+1640531527|0,n=r[a+34&127],t=r[a=a+1&127],n^=n<<13,t^=t<<17,n^=n>>>15,t^=t>>>12,n=r[a]=n^t,e.i=a,n+(i^i>>>16)|0},function(t,e){var n,i,r,a,o,s=[],l=128;for(e===(0|e)?(i=e,e=null):(e+="\0",i=0,l=Math.max(l,e.length)),r=0,a=-32;a<l;++a)e&&(i^=e.charCodeAt((a+32)%e.length)),0===a&&(o=i),i^=i<<10,i^=i>>>15,i^=i<<4,i^=i>>>13,a>=0&&(o=o+1640531527|0,r=0==(n=s[127&a]^=i+o)?r+1:0);for(r>=128&&(s[127&(e&&e.length||0)]=-1),r=127,a=512;a>0;--a)i=s[r+34&127],n=s[r=r+1&127],i^=i<<13,n^=n<<17,i^=i>>>15,n^=n>>>12,s[r]=i^n;t.w=o,t.X=s,t.i=r}(e,t)}function s(t,e){return e.i=t.i,e.w=t.w,e.X=t.X.slice(),e}function l(t,e){null==t&&(t=+new Date);var n=new o(t),i=e&&e.state,r=function(){return(n.next()>>>0)/4294967296};return r.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},r.int32=n.next,r.quick=r,i&&(i.X&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.xor4096=l}(0,t=n.nmd(t),n.amdD)},4467:function(t,e,n){var i;!function(t,r,a){function o(t){var e=this;e.next=function(){var t,n,i=e.x,r=e.i;return t=i[r],n=(t^=t>>>7)^t<<24,n^=(t=i[r+1&7])^t>>>10,n^=(t=i[r+3&7])^t>>>3,n^=(t=i[r+4&7])^t<<7,t=i[r+7&7],n^=(t^=t<<13)^t<<9,i[r]=n,e.i=r+1&7,n},function(t,e){var n,i=[];if(e===(0|e))i[0]=e;else for(e=""+e,n=0;n<e.length;++n)i[7&n]=i[7&n]<<15^e.charCodeAt(n)+i[n+1&7]<<13;for(;i.length<8;)i.push(0);for(n=0;n<8&&0===i[n];++n);for(8==n?i[7]=-1:i[n],t.x=i,t.i=0,n=256;n>0;--n)t.next()}(e,t)}function s(t,e){return e.x=t.x.slice(),e.i=t.i,e}function l(t,e){null==t&&(t=+new Date);var n=new o(t),i=e&&e.state,r=function(){return(n.next()>>>0)/4294967296};return r.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},r.int32=n.next,r.quick=r,i&&(i.x&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.xorshift7=l}(0,t=n.nmd(t),n.amdD)},5019:function(t,e,n){var i;!function(t,r,a){function o(t){var e=this,n="";e.next=function(){var t=e.x^e.x>>>2;return e.x=e.y,e.y=e.z,e.z=e.w,e.w=e.v,(e.d=e.d+362437|0)+(e.v=e.v^e.v<<4^t^t<<1)|0},e.x=0,e.y=0,e.z=0,e.w=0,e.v=0,t===(0|t)?e.x=t:n+=t;for(var i=0;i<n.length+64;i++)e.x^=0|n.charCodeAt(i),i==n.length&&(e.d=e.x<<10^e.x>>>4),e.next()}function s(t,e){return e.x=t.x,e.y=t.y,e.z=t.z,e.w=t.w,e.v=t.v,e.d=t.d,e}function l(t,e){var n=new o(t),i=e&&e.state,r=function(){return(n.next()>>>0)/4294967296};return r.double=function(){do{var t=((n.next()>>>11)+(n.next()>>>0)/4294967296)/(1<<21)}while(0===t);return t},r.int32=n.next,r.quick=r,i&&("object"==typeof i&&s(i,n),r.state=function(){return s(n,{})}),r}r&&r.exports?r.exports=l:n.amdD&&n.amdO?void 0===(i=function(){return l}.call(e,n,e,r))||(r.exports=i):this.xorwow=l}(0,t=n.nmd(t),n.amdD)},6543:function(t,e,n){var i;!function(r,a,o){var s,l=256,h=o.pow(l,6),u=o.pow(2,52),c=2*u,d=l-1;function f(t,e,n){var i=[],d=m(v((e=1==e?{entropy:!0}:e||{}).entropy?[t,y(a)]:null==t?function(){try{var t;return s&&(t=s.randomBytes)?t=t(l):(t=new Uint8Array(l),(r.crypto||r.msCrypto).getRandomValues(t)),y(t)}catch(t){var e=r.navigator,n=e&&e.plugins;return[+new Date,r,n,r.screen,y(a)]}}():t,3),i),f=new g(i),b=function(){for(var t=f.g(6),e=h,n=0;t<u;)t=(t+n)*l,e*=l,n=f.g(1);for(;t>=c;)t/=2,e/=2,n>>>=1;return(t+n)/e};return b.int32=function(){return 0|f.g(4)},b.quick=function(){return f.g(4)/4294967296},b.double=b,m(y(f.S),a),(e.pass||n||function(t,e,n,i){return i&&(i.S&&p(i,f),t.state=function(){return p(f,{})}),n?(o.random=t,e):t})(b,d,"global"in e?e.global:this==o,e.state)}function g(t){var e,n=t.length,i=this,r=0,a=i.i=i.j=0,o=i.S=[];for(n||(t=[n++]);r<l;)o[r]=r++;for(r=0;r<l;r++)o[r]=o[a=d&a+t[r%n]+(e=o[r])],o[a]=e;(i.g=function(t){for(var e,n=0,r=i.i,a=i.j,o=i.S;t--;)e=o[r=d&r+1],n=n*l+o[d&(o[r]=o[a=d&a+e])+(o[a]=e)];return i.i=r,i.j=a,n})(l)}function p(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function v(t,e){var n,i=[],r=typeof t;if(e&&"object"==r)for(n in t)try{i.push(v(t[n],e-1))}catch(t){}return i.length?i:"string"==r?t:t+"\0"}function m(t,e){for(var n,i=t+"",r=0;r<i.length;)e[d&r]=d&(n^=19*e[d&r])+i.charCodeAt(r++);return y(e)}function y(t){return String.fromCharCode.apply(0,t)}if(m(o.random(),a),t.exports){t.exports=f;try{s=n(6439)}catch(t){}}else void 0===(i=function(){return f}.call(e,n,e,t))||(t.exports=i)}("undefined"!=typeof self?self:this,[],Math)},1484:function(t,e,n){"use strict";n.r(e),n.d(e,{am5hierarchy:function(){return i}});const i=n(8447)},6439:function(){}},function(t){var e=(1484,t(t.s=1484)),n=window;for(var i in e)n[i]=e[i];e.__esModule&&Object.defineProperty(n,"__esModule",{value:!0})}]);
//# sourceMappingURL=hierarchy.js.map