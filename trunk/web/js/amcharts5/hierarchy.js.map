{"version": 3, "file": "hierarchy.js", "mappings": "4nBAWO,MAAMA,UAA8BC,EAAA,EAChC,iBAAAC,GACTC,MAAMD,oBAEN,MAAME,EAAKC,KAAKC,MAAMC,gBAChBC,EAAaH,KAAKC,MAAME,WACxBC,EAAIJ,KAAKK,KAAKC,KAAKN,MAQzBI,EAAE,aAAaG,OAAO,CACrBC,gBAAiB,aACjBC,gBAAiB,4BACjBC,MAAO,KACPC,OAAQ,KACRC,OAAQC,EAAA,EAASC,IAAId,KAAKC,MAAO,CAAEc,KAAM,IACzCC,UAAW,EACXC,aAAc,EACdC,kBAAkB,EAClBC,aAAa,EACbC,gBAAiB,MAAU,WAG5BhB,EAAE,iBAAiBG,OAAO,CACzBc,UAAW,WACXC,oBAAoB,EACpBC,SAAU,WACVC,YAAY,EACZC,gBAAiB,UACjBC,YAAa,sBAGdtB,EAAE,gBAAiB,CAAC,SAASG,OAAO,CACnCkB,gBAAiB,YAGlB,CACC,MAAMpB,EAAOD,EAAE,QAAS,CAAC,YAAa,SAEtCC,EAAKE,OAAO,CACXoB,QAAS,KACTC,QAAS,KACTL,SAAU,WACVM,cAAe,EACfC,WAAY,EACZC,aAAc,EACdC,YAAa,EACbC,KAAM,aACNC,cAAc,EACdC,kBAAmB,MACnBC,SAAU,MAGX,OAAS/B,EAAM,OAAQN,EAAI,kB,CAG5B,CACC,MAAMM,EAAOD,EAAE,iBAEfC,EAAKE,OAAO,CACXiB,YAAY,EACZD,SAAU,WACVc,YAAa,EACbC,cAAe,EACfC,SAAU,GACVC,SAAU,OAGX,OAASnC,EAAM,SAAUN,EAAI,O,CAG9BK,EAAE,SAAU,CAAC,kBAAmB,UAAUG,OAAO,CAChDgB,SAAU,WACVkB,YAAa,EACbH,cAAe,EACfI,OAAQ,GACRC,SAAU,IAGXvC,EAAE,SAAU,CAAC,kBAAmB,QAAS,UAAUG,OAAO,CACzDgB,SAAU,WACVqB,QAAS,EACTH,YAAa,EACbI,gBAAiB,EACjBP,cAAe,EACfI,OAAQ,GACRI,MAAO,IACPC,aAAa,IAGd3C,EAAE,SAAU,CAAC,kBAAmB,QAAS,UAAU4C,OAAOC,OAAO,WAAY,CAAEL,QAAS,EAAGE,MAAO,IAAKD,gBAAiB,IACxHzC,EAAE,SAAU,CAAC,kBAAmB,QAAS,UAAU4C,OAAOC,OAAO,gBAAiB,CAAEH,MAAO,IAAKF,QAAS,EAAGC,gBAAiB,IAC7HzC,EAAE,SAAU,CAAC,kBAAmB,QAAS,UAAU4C,OAAOC,OAAO,QAAS,CAAEH,MAAO,KAAMD,gBAAiB,IAC1GzC,EAAE,SAAU,CAAC,kBAAmB,QAAS,UAAU4C,OAAOC,OAAO,SAAU,CAAEL,QAAS,EAAGC,gBAAiB,IAS1GzC,EAAE,iBAAiBG,OAAO,CACzByB,YAAa,EACbkB,OAAQ/C,IAGT,CACC,MAAME,EAAOD,EAAE,QAAS,CAAC,eAEzBC,EAAKE,OAAO,CACXwB,aAAc,EACdC,YAAa,EACbP,gBAAiB,UACjBS,cAAc,EACdD,KAAM,iBAGP,OAAS5B,EAAM,OAAQN,EAAI,gB,CAG5B,CACC,MAAMM,EAAOD,EAAE,QAAS,CAAC,eAAe4C,OAAOC,OAAO,QAAS,CAAC,IAChE,OAAS5C,EAAM,OAAQN,EAAI,qB,CAG5B,CACC,MAAMM,EAAOD,EAAE,QAAS,CAAC,eAAe4C,OAAOC,OAAO,OAAQ,CAAEE,uBAAwB,KACxF,OAAS9C,EAAM,OAAQN,EAAI,oB,CAG5B,CACC,MAAMM,EAAOD,EAAE,QAAS,CAAC,aAAc,SAEvCC,EAAKE,OAAO,CACX2B,cAAc,EACdD,KAAM,aACNmB,WAAY,OACZ3B,gBAAiB,aAGlB,OAASpB,EAAM,OAAQN,EAAI,gB,CAG5B,CACC,MAAMM,EAAOD,EAAE,mBAAoB,CAAC,aAAc,QAAS,eAE3DC,EAAKE,OAAO,CACXkC,YAAa,KAGd,OAASpC,EAAM,OAAQN,EAAI,a,CAU5BK,EAAE,aAAaG,OAAO,CACrBS,UAAW,EACXqC,QAAS,EACTpC,aAAc,IAGfb,EAAE,gBAAiB,CAAC,cAAcG,OAAO,CACxCe,oBAAoB,IAGrBlB,EAAE,gBAAiB,CAAC,cAAc4C,OAAOC,OAAO,SAAU,CACzDL,QAAS,EACTU,SAAS,IAGV,CACC,MAAMjD,EAAOD,EAAE,QAAS,CAAC,YAAa,SAEtCC,EAAKE,OAAO,CACXgD,EAAG,KACHC,EAAG,KACH5B,QAAS,KACTD,QAAS,KACTE,cAAe,EACfC,WAAY,EACZE,YAAa,EACbD,aAAc,EACd0B,SAAU,GACVvB,cAAc,EACdD,KAAM,aACNE,kBAAmB,MACnBC,SAAU,MAGX,OAAS/B,EAAM,OAAQN,EAAI,kB,CAG5BK,EAAE,QAAS,CAAC,aAAc,YAAa,SAASG,OAAO,CACtDkD,SAAU,IAGX,CACC,MAAMpD,EAAOD,EAAE,mBAAoB,CAAC,YAAa,OAAQ,UAEzDC,EAAKE,OAAO,CACX+B,cAAe,EACfD,YAAa,EACbqB,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,KAGjB,OAASxD,EAAM,SAAUN,EAAI,a,CAG9BK,EAAE,mBAAoB,CAAC,YAAa,OAAQ,QAAS,SAASG,OAAO,CACpEkC,YAAa,MAUdrC,EAAE,YAAYG,OAAO,CACpBW,kBAAkB,IAGnBd,EAAE,gBAAiB,CAAC,aAAaG,OAAO,CACvCe,oBAAoB,IAGrBlB,EAAE,gBAAiB,CAAC,aAAa4C,OAAOC,OAAO,SAAU,CACxDL,QAAS,EACTU,SAAS,IAGV,CACC,MAAMjD,EAAOD,EAAE,QAAS,CAAC,WAAY,OAAQ,UAE7CC,EAAKE,OAAO,CACX+B,cAAe,EACfD,YAAa,EACbyB,aAAc,KAGf,OAASzD,EAAM,SAAUN,EAAI,a,CAG9BK,EAAE,QAAS,CAAC,WAAY,OAAQ,QAAS,SAASG,OAAO,CACxDkC,YAAa,MAGd,CACC,MAAMpC,EAAOD,EAAE,cAAe,CAAC,WAAY,SAE3CC,EAAKE,OAAO,CACXgD,EAAG,EACHC,EAAG,EACHO,SAAU,SACVlC,cAAe,EACfC,WAAY,EACZE,YAAa,EACbD,aAAc,EACdJ,QAAS,KACTO,cAAc,EACdD,KAAM,aACNE,kBAAmB,MACnBC,SAAU,GACV4B,WAAY,KACZP,SAAU,KAGX,OAASpD,EAAM,OAAQN,EAAI,kB,CAU5BK,EAAE,iBAAiBG,OAAO,CACzB0D,WAAW,QAAQ,GACnBC,WAAW,QAAQ,GACnBC,cAAe,IACfC,eAAgB,GAChBC,kBAAmB,GACnBC,cAAe,GACfC,iBAAkB,GAClBC,YAAa,GACbtD,kBAAkB,EAClBmC,QAASoB,IACTzD,UAAW,EACXC,aAAc,EACdyD,SAAU,IAUXtE,EAAE,QAAQG,OAAO,CAChBoE,YAAa,WACb3C,YAAa,GACbD,aAAc,GACdD,WAAY,GACZD,cAAe,GACfX,kBAAkB,EAClBmC,QAASoB,IACTzD,UAAW,EACXC,aAAc,EACdyD,SAAU,IAUXtE,EAAE,QAAQG,OAAO,CAChByB,YAAa,GACbF,WAAY,GACZD,cAAe,GACfE,aAAc,GACd6C,YAAa,IAGd,CACC,MAAMvE,EAAOD,EAAE,QAAS,CAAC,OAAQ,SAEjCC,EAAKE,OAAO,CACXqB,QAAS,KACTD,QAAS,KACTE,cAAe,EACfC,WAAY,EACZE,YAAa,EACbD,aAAc,EACdG,cAAc,EACdD,KAAM,aACNE,kBAAmB,MACnBC,SAAU,MAGX,OAAS/B,EAAM,OAAQN,EAAI,kB,CAG5B,CACC,MAAMM,EAAOD,EAAE,SAAU,CAAC,OAAQ,OAAQ,UAE1CC,EAAKE,OAAO,CACX+B,cAAe,GACfG,YAAa,GACbJ,YAAa,KAGd,OAAShC,EAAM,SAAUN,EAAI,a,CAI9BK,EAAE,uBAAuBG,OAAO,CAC/BsE,WAAW,IAGZzE,EAAE,uBAAuB4C,OAAOC,OAAO,SAAU,CAAEH,MAAO,EAAGF,QAAS,EAAGU,SAAS,IASlFlD,EAAE,WAAWG,OAAO,CACnB8C,QAAS,EACTyB,gBAAiB,aAGlB,CACC,MAAMzE,EAAOD,EAAE,QAAS,CAAC,UAAW,SAEpCC,EAAKE,OAAO,CACXgD,EAAG,KACHC,EAAG,KACH5B,QAAS,KACTD,QAAS,KACTE,cAAe,EACfC,WAAY,EACZE,YAAa,EACbD,aAAc,EACdG,cAAc,EACdD,KAAM,aACNE,kBAAmB,MACnBC,SAAU,MAGX,OAAS/B,EAAM,OAAQN,EAAI,kB,CAG5BK,EAAE,gBAAiB,CAAC,UAAW,SAASG,OAAO,CAC9CoC,UAAU,QAAQ,IAClBnB,YAAY,EACZD,SAAU,aAGX,CACC,MAAMlB,EAAOD,EAAE,mBAAoB,CAAC,UAAW,OAAQ,UAEvDC,EAAKE,OAAO,CACX+B,cAAe,EACfD,YAAa,EACbqB,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,EAChBC,eAAgB,EAChBpB,YAAa,KAGd,OAASpC,EAAM,SAAUN,EAAI,a,CAW7BK,EAAE,kBAAkBG,OAAO,CAC1BwE,KAAM,UACNC,eAAgB,KAChBC,iBAAkB,KAClBC,kBAAmB,IACnBhE,kBAAkB,IAIpB,CACC,MAAMb,EAAOD,EAAE,WAAY,CAAC,iBAAkB,OAAQ,UAEtDC,EAAKE,OAAO,CACX+B,cAAe,EACfD,YAAa,EACbI,YAAa,KAGd,OAASpC,EAAM,SAAUN,EAAI,a,CAI7BK,EAAE,UAAW,CAAC,YAAa,OAAQ,QAAS,WAAWG,OAAO,CAC7D8B,YAAa,IAIf,CACC,MAAMhC,EAAOD,EAAE,QAAS,CAAC,mBAEzBC,EAAKE,OAAO,CACXqB,QAAS,KACTD,QAAS,KACTE,cAAe,EACfC,WAAY,EACZE,YAAa,EACbD,aAAc,EACdG,cAAc,EACdD,KAAM,aACNE,kBAAmB,MACnBC,SAAU,GACV+C,MAAO,MAGR,OAAS9E,EAAM,OAAQN,EAAI,kB,CAG7B,E,+DCndM,MAAMqF,UAAsBC,EAAA,EAAnC,c,oBA6BC,qC,gDAA8C,IAAIC,EAAA,EACjDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAM0E,EAAA,EAAMC,KAAKzF,KAAKC,MAAO,CAC5ByF,UAAW,YAAiB1F,KAAK2F,OAAOC,SAASC,IAAI,YAAa,IAAK,CAAC,UACxEC,WAAYC,EAAA,EAAiBjF,IAAId,KAAKC,MAAO,CAC5CyF,UAAW,CAAC,iBAEX,CAAC1F,KAAK2F,OAAOC,eAUjB,wC,wDA+CD,CAxFQ,SAAAI,CAAUC,GAChB,MAAMC,EAAQlG,KAAK2F,OAAOQ,OAa1B,OAZAD,EAAME,aAAaH,GACnBC,EAAMlD,OAAOC,OAAO,QAAS,CAAC,GAC9BiD,EAAMlD,OAAOC,OAAO,OAAQ,CAAC,GAC7BiD,EAAMG,OAAOC,GAAG,SAAS,KACxB,MAAMC,EAASvG,KAAK6F,IAAI,UACpBU,GACHA,EAAOC,eAAeP,E,IAIxBjG,KAAK2F,OAAOc,KAAKP,GAEVA,CACR,CA4BU,SAAAQ,GACT1G,KAAK2G,eAAeF,KAAK9G,EAAsBmB,IAAId,KAAKC,QACxDD,KAAK4G,UAAUlB,UAAY,YAAiB1F,KAAK4G,UAAUlB,UAAW,CAAC,eAEvE5F,MAAM4G,WACP,CAEO,QAAAG,GAEN,GADA/G,MAAM+G,WACF7G,KAAK8G,QAAQ,UAAW,CAC3B,MAAMP,EAASvG,KAAK6F,IAAI,UAClBkB,EAAW/G,KAAKgH,cAAcT,OAChCA,GAAUQ,EACb/G,KAAKiH,UAAYV,EAAOF,OAAOC,GAAG,oBAAqBY,IACtDlH,KAAKmH,gBAAgBD,EAAMjB,SAAS,IAG7Bc,GACJ/G,KAAKiH,WACRjH,KAAKiH,UAAUG,UAIjBpH,KAAKmH,gBAAgBZ,EAAOV,IAAI,oB,CAElC,CAEU,eAAAsB,CAAgBlB,GAKzB,GAJAjG,KAAKqH,IAAI,YAAarH,KAAKW,UAC3BX,KAAKsH,SAASC,QACdvH,KAAK2F,OAAO4B,QAERtB,EAAU,CACb,IAAIuB,EAASvB,EAEb,KAAOuB,GAAQ,CACd,IAAItB,EAAQlG,KAAKgG,UAAUwB,GACvBA,GAAUvB,GACbC,EAAMuB,OAAO,QAEdzH,KAAKsH,SAASI,UAAUxB,EAAO,GAC/BsB,EAASA,EAAO3B,IAAI,S,EAGvB,EArDA,qC,gDAAkC,kBAClC,sC,gDAA0CR,EAAA,EAAUsC,WAAWC,OAAO,CAACxC,EAAcyC,c,kCC3D/E,MAAMC,UAAsBzC,EAAA,EAAnC,c,oBAKC,qC,yDAUA,6C,wDAuDD,CArDW,SAAAqB,GACT5G,MAAM4G,YAEN1G,KAAKgD,OAAOC,OAAO,WAAY,CAAC,GAChCjD,KAAKgD,OAAOC,OAAO,QAAS,CAAC,GAC7BjD,KAAKgD,OAAOC,OAAO,gBAAiB,CAAC,GAErCjD,KAAKsG,GAAG,YAAY,KACnB,MAAML,EAAWjG,KAAKiG,SACtB,IAAKA,EAASJ,IAAI,YAEjB,YADA7F,KAAKqH,IAAI,YAAY,GAItB,MAAMU,EAAW/H,KAAK6F,IAAI,YACpBU,EAASvG,KAAKuG,OAEhBN,GAAYM,GACXN,EAASJ,IAAI,aAAekC,IAC3BA,EACHxB,EAAOyB,gBAAgB/B,GAGvBM,EAAO0B,eAAehC,EAAUM,EAAOV,IAAI,YAAa,GAAI,G,GAKjE,CAEO,QAAAgB,GACN/G,MAAM+G,WAEF7G,KAAK8G,QAAQ,eAGC,YAFC9G,KAAK6F,IAAI,aAG1B7F,KAAKkI,eAAiBlI,KAAKqG,OAAOC,GAAG,SAAS,KAC7C,IAAKtG,KAAKmI,YAAa,CACtB,IAAI5B,EAASvG,KAAKuG,OACdA,GACHA,EAAOC,eAAexG,KAAKiG,S,KAM1BjG,KAAKkI,gBACRlI,KAAKkI,eAAed,UAIxB,EA3DA,qC,gDAAkC,kBAClC,sC,gDAA0C/B,EAAA,EAAUsC,WAAWC,OAAO,CAACE,EAAcD,c,wBC1BtF,SAASO,EAAMC,GACb,IAAIC,EAAM,EACNhB,EAAWe,EAAKf,SAChBiB,EAAIjB,GAAYA,EAASkB,OAC7B,GAAKD,EACA,OAASA,GAAK,GAAGD,GAAOhB,EAASiB,GAAGE,WADjCH,EAAM,EAEdD,EAAKI,MAAQH,CACf,CCOe,SAASI,EAAUC,EAAMrB,GAClCqB,aAAgBC,KAClBD,EAAO,MAACE,EAAWF,QACFE,IAAbvB,IAAwBA,EAAWwB,SACjBD,IAAbvB,IACTA,EAAWyB,GAWb,IARA,IACIV,EAEAW,EACAC,EACAV,EACAW,EANAC,EAAO,IAAIC,EAAKT,GAEhBU,EAAQ,CAACF,GAMNd,EAAOgB,EAAMC,OAClB,IAAKL,EAAS3B,EAASe,EAAKM,SAAWO,GAAKD,EAASM,MAAMC,KAAKP,IAAST,QAEvE,IADAH,EAAKf,SAAW2B,EACXV,EAAIW,EAAI,EAAGX,GAAK,IAAKA,EACxBc,EAAM5C,KAAKuC,EAAQC,EAAOV,GAAK,IAAIa,EAAKH,EAAOV,KAC/CS,EAAMxB,OAASa,EACfW,EAAMS,MAAQpB,EAAKoB,MAAQ,EAKjC,OAAON,EAAKO,WAAWC,EACzB,CAMA,SAASZ,EAAea,GACtB,OAAOA,EAAEtC,QACX,CAEA,SAASwB,EAAYc,GACnB,OAAOL,MAAMM,QAAQD,GAAKA,EAAE,GAAK,IACnC,CAEA,SAASE,EAASzB,QACQQ,IAApBR,EAAKM,KAAKF,QAAqBJ,EAAKI,MAAQJ,EAAKM,KAAKF,OAC1DJ,EAAKM,KAAON,EAAKM,KAAKA,IACxB,CAEO,SAASgB,EAActB,GAC5B,IAAI1H,EAAS,EACb,GAAG0H,EAAK1H,OAASA,SACT0H,EAAOA,EAAKb,SAAYa,EAAK1H,SAAWA,EAClD,CAEO,SAASyI,EAAKT,GACnB3I,KAAK2I,KAAOA,EACZ3I,KAAKyJ,MACLzJ,KAAKW,OAAS,EACdX,KAAKwH,OAAS,IAChB,CAEA4B,EAAKW,UAAYrB,EAAUqB,UAAY,CACrCC,YAAaZ,EACbhB,MDnEa,WACb,OAAOpI,KAAKiK,UAAU7B,EACxB,ECkEE8B,KC7Ea,SAASC,EAAUC,GAChC,IAAIC,GAAS,EACb,IAAK,MAAMhC,KAAQrI,KACjBmK,EAASG,KAAKF,EAAM/B,IAAQgC,EAAOrK,MAErC,OAAOA,IACT,EDwEEiK,UE9Ea,SAASE,EAAUC,GAEhC,IADA,IAA4C9C,EAAUiB,EAAGW,EAArDb,EAAOrI,KAAMqJ,EAAQ,CAAChB,GAAOkC,EAAO,GAAoBF,GAAS,EAC9DhC,EAAOgB,EAAMC,OAElB,GADAiB,EAAK9D,KAAK4B,GACNf,EAAWe,EAAKf,SAClB,IAAKiB,EAAI,EAAGW,EAAI5B,EAASkB,OAAQD,EAAIW,IAAKX,EACxCc,EAAM5C,KAAKa,EAASiB,IAI1B,KAAOF,EAAOkC,EAAKjB,OACjBa,EAASG,KAAKF,EAAM/B,IAAQgC,EAAOrK,MAErC,OAAOA,IACT,EFiEE0J,WG/Ea,SAASS,EAAUC,GAEhC,IADA,IAAiC9C,EAAUiB,EAAvCF,EAAOrI,KAAMqJ,EAAQ,CAAChB,GAAoBgC,GAAS,EAChDhC,EAAOgB,EAAMC,OAElB,GADAa,EAASG,KAAKF,EAAM/B,IAAQgC,EAAOrK,MAC/BsH,EAAWe,EAAKf,SAClB,IAAKiB,EAAIjB,EAASkB,OAAS,EAAGD,GAAK,IAAKA,EACtCc,EAAM5C,KAAKa,EAASiB,IAI1B,OAAOvI,IACT,EHqEEwK,KIhFa,SAASL,EAAUC,GAChC,IAAIC,GAAS,EACb,IAAK,MAAMhC,KAAQrI,KACjB,GAAImK,EAASG,KAAKF,EAAM/B,IAAQgC,EAAOrK,MACrC,OAAOqI,CAGb,EJ0EEC,IKjFa,SAASG,GACtB,OAAOzI,KAAKiK,WAAU,SAAS5B,GAI7B,IAHA,IAAIC,GAAOG,EAAMJ,EAAKM,OAAS,EAC3BrB,EAAWe,EAAKf,SAChBiB,EAAIjB,GAAYA,EAASkB,SACpBD,GAAK,GAAGD,GAAOhB,EAASiB,GAAGE,MACpCJ,EAAKI,MAAQH,CACf,GACF,EL0EEmC,KMlFa,SAASC,GACtB,OAAO1K,KAAK0J,YAAW,SAASrB,GAC1BA,EAAKf,UACPe,EAAKf,SAASmD,KAAKC,EAEvB,GACF,EN6EEC,KOnFa,SAASC,GAItB,IAHA,IAAIC,EAAQ7K,KACR8K,EAcN,SAA6BC,EAAGC,GAC9B,GAAID,IAAMC,EAAG,OAAOD,EACpB,IAAIE,EAASF,EAAEG,YACXC,EAASH,EAAEE,YACXE,EAAI,KAGR,IAFAL,EAAIE,EAAO3B,MACX0B,EAAIG,EAAO7B,MACJyB,IAAMC,GACXI,EAAIL,EACJA,EAAIE,EAAO3B,MACX0B,EAAIG,EAAO7B,MAEb,OAAO8B,CACT,CA3BiBC,CAAoBR,EAAOD,GACtCvB,EAAQ,CAACwB,GACNA,IAAUC,GACfD,EAAQA,EAAMrD,OACd6B,EAAM5C,KAAKoE,GAGb,IADA,IAAIS,EAAIjC,EAAMb,OACPoC,IAAQE,GACbzB,EAAMkC,OAAOD,EAAG,EAAGV,GACnBA,EAAMA,EAAIpD,OAEZ,OAAO6B,CACT,EPsEE6B,UQpFa,WAEb,IADA,IAAI7C,EAAOrI,KAAMqJ,EAAQ,CAAChB,GACnBA,EAAOA,EAAKb,QACjB6B,EAAM5C,KAAK4B,GAEb,OAAOgB,CACT,ER+EEmC,YSrFa,WACb,OAAOjC,MAAMC,KAAKxJ,KACpB,EToFEyL,OUtFa,WACb,IAAIA,EAAS,GAMb,OALAzL,KAAK0J,YAAW,SAASrB,GAClBA,EAAKf,UACRmE,EAAOhF,KAAK4B,EAEhB,IACOoD,CACT,EV+EEC,MWvFa,WACb,IAAIvC,EAAOnJ,KAAM0L,EAAQ,GAMzB,OALAvC,EAAKe,MAAK,SAAS7B,GACbA,IAASc,GACXuC,EAAMjF,KAAK,CAACkF,OAAQtD,EAAKb,OAAQoE,OAAQvD,GAE7C,IACOqD,CACT,EXgFEG,KA5CF,WACE,OAAOnD,EAAU1I,MAAM0J,WAAWI,EACpC,EA2CE,CAACgC,OAAOC,UYzFK,YACb,IAAiBC,EAAwB1E,EAAUiB,EAAGW,EAAlDb,EAAOrI,KAAeuK,EAAO,CAAClC,GAClC,GAEE,IADA2D,EAAUzB,EAAK0B,UAAW1B,EAAO,GAC1BlC,EAAO2D,EAAQ1C,OAEpB,SADMjB,EACFf,EAAWe,EAAKf,SAClB,IAAKiB,EAAI,EAAGW,EAAI5B,EAASkB,OAAQD,EAAIW,IAAKX,EACxCgC,EAAK9D,KAAKa,EAASiB,UAIlBgC,EAAK/B,OAChB,GC4OO,MAAe0D,UAAkBC,EAAA,EAAxC,c,oBAcC,6C,gDAAiCnM,KAAKsH,SAASb,KAAKpB,EAAA,EAAUvE,IAAId,KAAKC,MAAO,CAAEuB,YAAY,OAE5F,wC,yDAEA,wC,yDAEA,qC,gDAA2B,IAE3B,mC,gDAAyB,cAOzB,oC,gDAAqD,IAAI8D,EAAA,EACxDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMgH,EAAchH,IAAId,KAAKC,MAAO,CACnCyF,UAAW,YAAiB1F,KAAKqJ,MAAMzD,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,YAAa,UAC7FpM,KAAKqJ,MAAMzD,cAuCf,qC,gDAA8C,IAAIN,EAAA,EACjDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAM0E,EAAA,EAAM1E,IAAId,KAAKC,MAAO,CAC3ByF,UAAW,YAAiB1F,KAAK2F,OAAOC,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,QAC3EpM,KAAK2F,OAAOC,cAGhB,gD,wDA6tBD,CArwBQ,QAAAyG,CAASpG,GAEf,MAAMqG,EAAYrG,EAASJ,IAAI,aAEzBwC,EAAOrI,KAAKqJ,MAAMlD,OACxBkC,EAAK9B,OAASvG,KACdqI,EAAKjC,aAAaH,GAClBjG,KAAKqJ,MAAM5C,KAAK4B,GAChBpC,EAASsG,OAAO,OAAQlE,GAExB,MAAMnC,EAAQlG,KAAK2F,OAAOQ,OAC1BD,EAAME,aAAaH,GACnBA,EAASsG,OAAO,QAASrG,GACzBlG,KAAK2F,OAAOc,KAAKP,GAEZoG,GAAiC,GAApBA,EAAU9D,QAC3BH,EAAKZ,OAAO,QAGb,MAAMgC,EAAQxD,EAASJ,IAAI,SAM3B,OALAwC,EAAKZ,OAAO,QAAUgC,GAEtBzJ,KAAKwM,eAAelF,SAASb,KAAK4B,GAClCA,EAAKf,SAASb,KAAKP,GAEZmC,CACR,CAgBU,SAAA3B,GACT1G,KAAK2G,eAAeF,KAAK9G,EAAsBmB,IAAId,KAAKC,QACxDD,KAAKyM,OAAOhG,KAAK,WAAY,YAAa,WAAY,QACtDzG,KAAKsH,SAASb,KAAKzG,KAAK0M,kBAExB5M,MAAM4G,WACP,CAEO,gBAAAiG,GAGN,GAFA7M,MAAM6M,mBAEF3M,KAAK4M,aAAc,CACtB5M,KAAK6M,UAAY,CAAC,EAElB,MAAMC,EAAQ9M,KAAK+M,UAAU,GAC7B,GAAID,IACH9M,KAAKgN,mBAAmBhN,KAAK6M,UAAWC,GACxC9M,KAAKiN,OAAS,EACdjN,KAAKkN,UAAa,EAAsBlN,KAAK6M,WACzC7M,KAAKkN,WAAW,CACnBlN,KAAKkN,UAAU5E,KAAKsB,GACZA,EAAEnB,QAEV,MAAMgC,EAAOzK,KAAK6F,IAAI,QACV,cAAR4E,EACHzK,KAAKkN,UAAUzC,MAAK,CAACM,EAAGC,IAAOA,EAAEvC,MAAiBsC,EAAEtC,QAEpC,aAARgC,GACRzK,KAAKkN,UAAUzC,MAAK,CAACM,EAAGC,IAAOD,EAAEtC,MAAiBuC,EAAEvC,QAErDzI,KAAKmN,cAAc,WAAY1I,KAC/BzE,KAAKmN,cAAc,aAAa,KAChCnN,KAAKmN,cAAc,WAAY,GAC/BnN,KAAKoN,cAAcpN,KAAKkN,U,EAS3B,IAJIlN,KAAK4M,cAAgB5M,KAAKqN,aAC7BrN,KAAKsN,iBAGFtN,KAAKqN,WAAY,CACpB,MAAMpH,EAAWjG,KAAK6F,IAAI,oBACtBI,GACHjG,KAAKuN,MAAMtH,E,CAGd,CAEO,QAAAY,GACN/G,MAAM+G,WACF7G,KAAK8G,QAAQ,qBAChB9G,KAAKwN,gBAAgBxN,KAAK6F,IAAI,oBAEhC,CAEU,cAAAyH,GACLtN,KAAKkN,WACRlN,KAAKyN,aAAazN,KAAKkN,UAEzB,CAEU,YAAAO,CAAaC,GACtB,MAAMzH,EAAWyH,EAAc/E,KAAK1C,SAEpC,GAAIA,EAAU,CACbjG,KAAK2N,YAAY1H,GAEbjG,KAAK4N,QAAQpF,OAAS,IAAMvC,EAAS2H,SACxC5N,KAAK6N,aAAa5H,GAGnB,MAAM6H,EAAoBJ,EAAcpG,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAKyN,aAAaM,EAAe,G,CAIrC,CAEU,WAAAJ,CAAYK,GAEtB,CAQO,eAAAC,CAAgBC,GACtB,OAAOlO,KAAKmO,iBAAiBnO,KAAK+M,UAAWmB,EAC9C,CAEO,gBAAAC,CAAiBpB,EAAuDmB,GAE9E,IAAIE,EAiBJ,OAfA,OAAYrB,GAAY9G,IAEnBA,EAASJ,IAAI,OAASqI,IACzBE,EAAKnI,GAGN,MAAMqB,EAAWrB,EAASJ,IAAI,YAC9B,GAAIyB,EAAU,CACb,IAAI+G,EAAgBrO,KAAKmO,iBAAiB7G,EAAU4G,GAChDG,IACHD,EAAKC,E,KAKDD,CACR,CAEU,cAAAE,CAAevB,GACxB,OAAYA,GAAY9G,IACvB,MAAM2H,EAAU3H,EAAS2H,QACrBA,IACH,OAAYA,GAAUW,IACrBA,EAAOnH,SAAS,IAEjBnB,EAAS2H,aAAU/E,GAGpB,MAAMvB,EAAWrB,EAASJ,IAAI,YAE1ByB,GACHtH,KAAKsO,eAAehH,E,IAItBtH,KAAKsN,gBACN,CAEU,YAAAkB,GACT1O,MAAM0O,eACN,MAAM5N,EAASZ,KAAK6F,IAAI,UACpBjF,GACHA,EAAO6N,QAGR,MAAMC,EAAW1O,KAAK6F,IAAI,YACtB6I,GACHA,EAASD,OAEX,CAEU,eAAAE,CAAgB1I,GACzBnG,MAAM6O,gBAAgB1I,GAEtB,MAAMqG,EAAYrG,EAASJ,IAAI,aACzBjF,EAASZ,KAAK6F,IAAI,UAClB6I,EAAW1O,KAAK6F,IAAI,YACpBnB,EAAW1E,KAAK6F,IAAI,WAAY,GAEjCI,EAASJ,IAAI,YACjBI,EAASsG,OAAO,QAAS,GACrB3L,GAAsB,GAAZ8D,GAAyC,MAAxBuB,EAASJ,IAAI,UAC3CI,EAASsG,OAAO,OAAQ3L,EAAO2J,QAE3BmE,GACHzI,EAASsG,OAAO,cAAemC,EAASnE,UAK3C,IAAId,EAAQxD,EAASJ,IAAI,SACzB,MAAM5E,EAAejB,KAAK6F,IAAI,eAAgB,GAK9C,GAHA7F,KAAKqM,SAASpG,GACdjG,KAAK4O,iBAAiB3I,GAElBqG,EAAW,CACd,MAAMhF,EAAuD,GAC7DrB,EAASsG,OAAO,WAAYjF,GAE5B,OAAYgF,GAAYtD,IACvB,MAAMqF,EAAgB,IAAI,IAASrO,KAAMgJ,EAAOhJ,KAAK6O,cAAc7F,IAEnE1B,EAASb,KAAK4H,GAEdA,EAAc9B,OAAO,SAAUtG,GAC/BoI,EAAc9B,OAAO,QAAS9C,EAAQ,GAET,GAAzBzJ,KAAK+M,UAAUvE,QAAwB,GAATiB,GAC7B7I,GAAuC,MAA7ByN,EAAcxI,IAAI,SAC/BwI,EAAc9B,OAAO,OAAQ3L,EAAO2J,QAEjCmE,GAAgD,MAApCL,EAAcxI,IAAI,gBACjCwI,EAAc9B,OAAO,cAAemC,EAASnE,UAIb,MAA7B8D,EAAcxI,IAAI,SACrBwI,EAAc9B,OAAO,OAAQtG,EAASJ,IAAI,SAEH,MAApCwI,EAAcxI,IAAI,gBACrBwI,EAAc9B,OAAO,cAAetG,EAASJ,IAAI,iBAInD7F,KAAK2O,gBAAgBN,EAAc,G,CAIrC,MAAM/G,EAAWrB,EAASJ,IAAI,YACzByB,GAA+B,GAAnBA,EAASkB,QACZvC,EAASJ,IAAI,QACrBtF,OAAO,CAAEc,eAAWwH,IAGM,MAA5B5C,EAASJ,IAAI,aACZ4D,GAAS/E,EAAWzD,GACvBjB,KAAKgI,gBAAgB/B,EAAU,EAGlC,CAQO,YAAA6I,CAAa7I,EAA+C0C,GAClE,MAAMoG,EAAc9I,EAAS8I,YACvBC,EAAiBhP,KAAK6F,IAAI,kBAEhC,IAAIyG,EAAYyC,EAAYC,GACvB1C,EAKJA,EAAU7F,QAAQkC,IAJlB2D,EAAY3D,EACZoG,EAAYC,GAAkB1C,GAM/B,IAAIhF,EAAWrB,EAASJ,IAAI,YACvByB,IACJA,EAAW,GACXrB,EAASoB,IAAI,WAAYC,IAG1B,MAAMe,EAAOpC,EAASJ,IAAI,QAEtBwC,GACHA,EAAKhB,IAAI,YAAa,YAGvB,IAAIoC,EAAQxD,EAASJ,IAAI,SAEzB,OAAYyG,GAAYtD,IACvB,IAAIiG,GAAQ,EASZ,GARA,eAAoB3H,GAAWrB,GAC1BA,EAAS8I,aAAe/F,IAC3BiG,GAAQ,GACD,MAKJA,EAAO,CACX,MAAMZ,EAAgB,IAAI,IAASrO,KAAMgJ,EAAOhJ,KAAK6O,cAAc7F,IAEnE1B,EAASb,KAAK4H,GAEdA,EAAc9B,OAAO,SAAUtG,GAC/BoI,EAAc9B,OAAO,QAAS9C,EAAQ,GAEL,MAA7B4E,EAAcxI,IAAI,SACrBwI,EAAc9B,OAAO,OAAQtG,EAASJ,IAAI,SAG3C7F,KAAK2O,gBAAgBN,E,IAGxB,CAEU,gBAAAO,CAAiBZ,GAE3B,CAEU,aAAAZ,CAAc8B,GACvB,MAAMjJ,EAAWiJ,EAAgBvG,KAAK1C,SAMtC,GAJIiJ,EAAgBzF,MAAQzJ,KAAKmP,WAAW,aAC3CnP,KAAKmN,cAAc,WAAY+B,EAAgBzF,OAG5CxD,EAAU,CACbA,EAASsG,OAAO,kBAAmB2C,GAElCA,EAAwB7E,MAAQrK,KAAKiN,OAEtCjN,KAAKiN,SAELjN,KAAKmJ,KAAK9C,OAAO+I,KAAK,cAAc,KACnCnJ,EAASJ,IAAI,QAAQwB,IAAI,WAAYpB,EAASJ,IAAI,YAAY,IAG/D,IAAIwJ,EAAYH,EAAgBvG,KAAKF,MACjCA,EAAQyG,EAAgBzG,MAO5B,GALiB,MAAb4G,IACH5G,EAAQ4G,EACPH,EAA+B,MAAIzG,GAGjC,WAAeA,GAAQ,CAC1BxC,EAASsG,OAAO,MAAO9D,GACvBxC,EAASsG,OAAO,oBAAqB9D,EAAQzI,KAAK+M,UAAU,GAAGlH,IAAI,OAAS,KAE5E,IAAIyJ,EAAe,IACnB,MAAM9H,EAASvB,EAASJ,IAAI,UACxB2B,IACH8H,EAAe7G,EAAQjB,EAAO3B,IAAI,OAAS,KAE5CI,EAASJ,IAAI,SAAS5D,KAAKsN,gBAC3BtJ,EAASsG,OAAO,eAAgB+C,GAEhC,MAAME,EAAWxP,KAAKmP,WAAW,YACjBtG,MAAZ2G,GAAyBA,EAAW/G,GACvCzI,KAAKmN,cAAc,WAAY1E,GAGhC,MAAMgH,EAAYzP,KAAKmP,WAAW,aACjBtG,MAAb4G,GAA0BA,EAAYhH,GACzCzI,KAAKmN,cAAc,YAAa1E,E,CAIlCzI,KAAK0P,kBAAkBzJ,E,CAGxB,MAAM6H,EAAoBoB,EAAgB5H,SACtCwG,GACH,OAAYA,GAAoB6B,IAC/B3P,KAAKoN,cAAcuC,EAAiB,GAGvC,CAEU,kBAAA3C,CAAmBrE,EAA4B1C,GACxD0C,EAAK1C,SAAWA,EAEhB,MAAMqB,EAAWrB,EAASJ,IAAI,YAC9B,GAAIyB,EAAU,CACb,MAAMsI,EAAiD,GACvDjH,EAAKrB,SAAWsI,EAChB,OAAYtI,GAAW+G,IACtB,MAAM/B,EAAY,CAAC,EACnBsD,EAAkBnJ,KAAK6F,GACvBtM,KAAKgN,mBAAmBV,EAAW+B,EAAc,IAGlD,MAAM5F,EAAQxC,EAASJ,IAAI,gBACvB,WAAe4C,KAClBE,EAAKF,MAAQA,E,KAGV,CACJ,MAAMA,EAAQxC,EAASJ,IAAI,gBACvB,WAAe4C,KAClBE,EAAKF,MAAQA,E,CAGhB,CAKO,eAAAoH,CAAgB5J,GACtBnG,MAAM+P,gBAAgB5J,GACtB,MAAMoC,EAAOpC,EAASJ,IAAI,QACtBwC,IACHrI,KAAKqJ,MAAMyG,YAAYzH,GACvBA,EAAKjB,WAEN,MAAMlB,EAAQD,EAASJ,IAAI,SACvBK,IACHlG,KAAK2F,OAAOmK,YAAY5J,GACxBA,EAAMkB,WAGP,MAAME,EAAWrB,EAASJ,IAAI,YAC1ByB,GACH,OAAYA,GAAW0B,IACtBhJ,KAAK6P,gBAAgB7G,EAAM,GAG9B,CASa,YAAA+G,CAAa9J,EAA+C+J,G,uHACxE,MAAMC,EAAW,CAAC,EAAMF,aAAY,UAAC9J,EAAU+J,IAEzCE,EAAclQ,KAAKgD,OAAOC,OAAO,SAAU,CAAC,GAElD,IAAK,WAAe+M,GAAW,CAC9B,MAAM7M,EAAyB,yBAC/B6M,EAAWE,EAAYrK,IAAI1C,EAAwBnD,KAAK6F,IAAI1C,EAAwB,G,CAGrF,MAAMgN,EAAuB,uBACvBC,EAASF,EAAYrK,IAAIsK,EAAsBnQ,KAAK6F,IAAIsK,IAExD7I,EAAWrB,EAASJ,IAAI,YAExByB,GAA+B,GAAnBA,EAASkB,SAAgB,WAAevC,EAASJ,IAAI,WACtEoK,EAASxJ,KAAKR,EAASoK,QAAQ,CAAEC,IAAK,eAAuBC,GAAI,EAAGP,SAAUA,EAAUI,OAAQA,IAAUI,eAG3G,MAAMnI,EAAOpC,EAASJ,IAAI,QAC1BwC,EAAKoI,OACLpI,EAAKqI,cAEDpJ,GACH,OAAYA,GAAW+G,IACtB4B,EAASxJ,KAAKzG,KAAK+P,aAAa1B,GAAe,UAI3CsC,QAAQC,IAAIX,EACnB,G,CASa,YAAAY,CAAa5K,EAA+C+J,G,uHACxE,MAAMC,EAAW,CAAC,EAAMY,aAAY,UAAC5K,EAAU+J,IAE1C,WAAeA,KACnBA,EAAWhQ,KAAK6F,IAAI,yBAA0B,IAG/C,MAAMuK,EAASpQ,KAAK6F,IAAI,wBAElByB,EAAWrB,EAASJ,IAAI,YAExByB,GAA+B,GAAnBA,EAASkB,SAAgB,WAAevC,EAASJ,IAAI,WACtEoK,EAASxJ,KAAKR,EAASoK,QAAQ,CAAEC,IAAK,eAAuBC,GAAItK,EAASJ,IAAI,SAAUmK,SAAUA,EAAUI,OAAQA,IAAUI,eAGlHvK,EAASJ,IAAI,QACrBiL,OAEDxJ,GACH,OAAYA,GAAW+G,IACtB4B,EAASxJ,KAAKzG,KAAK6Q,aAAaxC,GAAe,UAI3CsC,QAAQC,IAAIX,EACnB,G,CAQO,cAAAhI,CAAehC,EAA+C8K,EAAmBtH,EAAgBuG,GAC1F,MAATvG,IACHA,EAAQ,GAGO,MAAZsH,IACHA,EAAW,GAGZ9K,EAASoB,IAAI,YAAY,GACzBpB,EAASJ,IAAI,QAAQwB,IAAI,YAAY,GAEhCpB,EAAS+K,YACb/K,EAASJ,IAAI,QAAQiL,KAAKd,GAK3B,MAAMtL,EAAW1E,KAAK6F,IAAI,WAAY,GAKtC,GAJII,EAASJ,IAAI,SAAWnB,GAC3BuB,EAASJ,IAAI,QAAQ4K,KAAK,GAGd,GAAThH,EAAY,CACf,MAAMpG,EAAUrD,KAAK6F,IAAI,UAAWpB,KACpC,IAAI+C,EAASvB,EACTmC,EAAQ,EAEZ,UAAkBS,IAAXrB,GACFY,EAAQ/E,GACXmE,EAAO3B,IAAI,QAAQ4K,OAEpBjJ,EAASA,EAAO3B,IAAI,UACpBuC,G,CAIF,IAAId,EAAWrB,EAASJ,IAAI,YACxByB,IACCmC,EAAQsH,EAAW,EACtB,OAAYzJ,GAAW0B,IACtB,MAAMiI,EAAgBjR,KAAK6F,IAAI,iBAC3BoL,EAE+B,GADdjI,EAAM+F,YACVkC,GACfjR,KAAKiI,eAAee,EAAO+H,EAAUtH,EAAkB,EAAGuG,GAG1DhQ,KAAKgI,gBAAgBgB,GAItBhJ,KAAKiI,eAAee,EAAO+H,EAAUtH,EAAkB,EAAGuG,E,IAK5D,OAAY1I,GAAW0B,IACjBA,EAAMgI,aACVhI,EAAMnD,IAAI,QAAQiL,KAAKd,GACvBhH,EAAMnD,IAAI,QAAQ7C,OAAOkO,aAAa,YACtClI,EAAM3B,IAAI,YAAY,GAEtBrH,KAAKgI,gBAAgBgB,G,IAK1B,CAQO,eAAAhB,CAAgB/B,EAA+C+J,GACrE/J,EAASoB,IAAI,YAAY,GACzB,IAAIC,EAAWrB,EAASJ,IAAI,YACxByB,GACH,OAAYA,GAAW0B,IACtBhJ,KAAKgI,gBAAgBgB,EAAOgH,GAC5BhH,EAAMnD,IAAI,QAAQ4K,KAAKT,EAAS,GAGnC,CAEU,eAAAxC,CAAgBvH,EAAgDjF,EAAoBmQ,GAC7F,GAAIlL,EAAU,CACb,IAAKkL,EAAc,CAClB,MAAMpM,EAAO,mBACb/E,KAAKqG,OAAO+K,SAASrM,EAAM,CAAEA,KAAMA,EAAM6G,OAAQ5L,KAAMiG,SAAUA,G,CAGlE,IAAI8K,EAAW/Q,KAAKmP,WAAW,WAAY,GAC3C,MAAMzK,EAAW1E,KAAK6F,IAAI,WAAY,GAErB,MAAb7E,IACHA,EAAYqQ,KAAKC,IAAItR,KAAK6F,IAAI,YAAa,GAAIkL,EAAW9K,EAASJ,IAAI,WAGxE,MAAM6H,EAAgBzH,EAASJ,IAAI,mBACnC,IAAI0L,EAAe7D,EAAcjE,MAC5BzJ,KAAKwR,SACTxQ,EAAYqQ,KAAKC,IAAItR,KAAK6F,IAAI,eAAgB,GAAIkL,EAAWrM,GAC7D1D,EAAYqQ,KAAKI,IAAI,EAAGzQ,IAGzBhB,KAAK0R,kBAAoB1Q,EAErBuQ,EAAevQ,EAAY+P,IAC9B/P,EAAY+P,EAAWQ,GAGpBA,EAAe7M,IAClB1D,GAAa0D,EAAW6M,EACxBA,EAAe7M,GAGhB,MAAM4C,EAAWrB,EAASJ,IAAI,YAE1ByB,GAAYA,EAASkB,OAAS,GAC7BxH,EAAY,EACfhB,KAAKiI,eAAehC,EAAUjF,IAG9BiF,EAASJ,IAAI,QAAQiL,OACrB,OAAYxJ,GAAW0B,IACtBA,EAAMnD,IAAI,QAAQ4K,MAAM,KAItB/C,EAAcjE,MAAQ/E,GACzBuB,EAASJ,IAAI,QAAQ4K,KAAK,GAGvBzQ,KAAK6F,IAAI,qBACZ7F,KAAK2R,cAAc1L,IAIpBjG,KAAKiI,eAAehC,EAAUjF,GAG/BhB,KAAKC,MAAMoG,OAAO+I,KAAK,cAAc,KACpCpP,KAAKuN,MAAMtH,EAAS,G,CAGvB,CAEU,KAAAsH,CAAMS,GAChB,CAEU,aAAA2D,CAAc1L,GACvB,MAAMuB,EAASvB,EAASJ,IAAI,UAC5B,GAAI2B,EAAQ,CACX,MAAMF,EAAWE,EAAO3B,IAAI,YACxByB,GACH,OAAYA,GAAW0B,IAClBA,GAAS/C,GACZjG,KAAKgI,gBAAgBgB,E,IAK1B,CAOO,cAAAxC,CAAeP,GACrB,MAAMuB,EAASvB,EAASJ,IAAI,UACtBkL,EAAW/Q,KAAKmP,WAAW,WAAY,GAE7C,GAAInP,KAAK6F,IAAI,qBAAuBI,EACnC,GAAIuB,EACHxH,KAAKqH,IAAI,mBAAoBG,OAEzB,CACJ,IAAIiC,EAAQ4H,KAAKC,IAAItR,KAAK6F,IAAI,YAAa,GAAIkL,EAAW9K,EAASJ,IAAI,UAEnE7F,KAAK0R,mBAAqBjI,IAC7BA,EAAQ4H,KAAKC,IAAItR,KAAK6F,IAAI,eAAgB,GAAIkL,EAAW/Q,KAAK6F,IAAI,WAAY,KAG/E7F,KAAKwN,gBAAgBvH,EAAUwD,E,MAIhCzJ,KAAKqH,IAAI,mBAAoBpB,EAE/B,CAGU,WAAA2L,CAAY3L,EAA+C4L,EAAmHxH,GACvL,MAAMkE,EAASzO,MAAM8R,YAAY3L,EAAU4L,EAAgBxH,GAC3D,GAAIkE,EAAQ,CACX,MAAMuD,EAASvD,EAAO1I,IAAI,UACpBwC,EAAOpC,EAASJ,IAAI,QAEtBiM,IACHzJ,EAAKf,SAASb,KAAKqL,GACnBzJ,EAAK/B,GAAG,SAAS,KAChBtG,KAAK+R,gBAAgBxD,EAAO,IAE7BlG,EAAK/B,GAAG,UAAU,KACjBtG,KAAK+R,gBAAgBxD,EAAO,I,CAI/B,OAAOA,CACR,CAEO,eAAAwD,CAAgBxD,GAEtB,MAAMuD,EAASvD,EAAO1I,IAAI,UAC1B,GAAIiM,EAAQ,CACX,MAAM7L,EAAW6L,EAAO7L,SAElB+L,EAAYzD,EAAO1I,IAAI,YAAa,IACpCoM,EAAY1D,EAAO1I,IAAI,YAAa,IAEpCwC,EAAOpC,EAASJ,IAAI,QAE1BiM,EAAOzK,IAAI,IAAKgB,EAAK3H,QAAUsR,GAC/BF,EAAOzK,IAAI,IAAKgB,EAAK1H,SAAWsR,E,CAElC,CAQO,aAAAC,CAAcjM,GACpB,MAAMoC,EAAOpC,EAASJ,IAAI,QACtBwC,IAASA,EAAK2I,YACjB3I,EAAK8J,OAEP,CAQO,eAAAC,CAAgBnM,GACtB,MAAMoC,EAAOpC,EAASJ,IAAI,QACtBwC,GACHA,EAAKgK,SAEP,EA1yBA,qC,gDAAkC,cAClC,sC,gDAA0ClG,EAAA,EAAOxE,WAAWC,OAAO,CAACsE,EAAUrE,cC3OxE,MAAMyK,UAA4BxK,EAe9B,SAAApB,GACT5G,MAAM4G,YAEN1G,KAAKgD,OAAOC,OAAO,WAAY,CAAC,GAChCjD,KAAKgD,OAAOC,OAAO,QAAS,CAAC,GAC7BjD,KAAKgD,OAAOC,OAAO,gBAAiB,CAAC,EACtC,CAGO,YAAAsP,CAAavC,GACnB,MAAM/J,EAAWjG,KAAKiG,SACtB,GAAIA,EAAU,CACb,IAAIyF,EAASzF,EAAgDJ,IAAI,SAEjE,OAAY6F,GAAQ8G,IACnB,IAAI7G,EAAS6G,EAAK3M,IAAI,UAClB+F,EAAS4G,EAAK3M,IAAI,UAElB8F,GAAUC,IACTD,EAAO9F,IAAI,QAAQmL,YAAcpF,EAAO/F,IAAI,QAAQmL,WACvDwB,EAAK/B,KAAKT,GAGVwC,EAAK1B,KAAKd,G,IAKf,CAEO,gBAAArD,GACN7M,MAAM6M,mBAEF3M,KAAK8G,QAAQ,aAChB9G,KAAKuS,cAEP,CAEU,OAAAE,CAAQzC,GACjBlQ,MAAM2S,QAAQzC,GACdhQ,KAAKuS,aAAavC,EACnB,CAEU,OAAA0C,CAAQ1C,GACjBlQ,MAAM4S,QAAQ1C,GACdhQ,KAAKuS,aAAavC,EACnB,EAnDA,qC,gDAAkC,wBAClC,sC,gDAA0ClI,EAAcH,WAAWC,OAAO,CAAC0K,EAAoBzK,c,cCczF,MAAM8K,UAAsBC,EAAA,EAAnC,c,oBAOC,sC,gDAAgC,KAEhC,qC,wDA+HD,CA7HQ,cAAAtE,CAAeV,GACrB,OAAY5N,KAAK4N,SAAUW,IAC1BA,EAAOnH,SAAS,IAGjBwG,EAAQ1D,MAAMqE,IACb,MAAMsE,EAAYtE,EAAOvO,KAAKC,MAAOD,KAAK6F,IAAI,UAAY7F,KAAK6F,IAAI,WACnE,GAAIgN,EAAW,CACd7S,KAAK4N,QAAQnH,KAAKoM,GAElB,MAAMf,EAASe,EAAUhN,IAAI,UAO7B,GALA7F,KAAK8S,YAAYD,EAAUvM,GAAG,aAAa,KAC1CtG,KAAK+S,QAAS,EACd/S,KAAKgT,WAAW,KAGdlB,EAAO,CACT,MAAMvL,EAASvG,KAAKuG,OACjBA,GACFA,EAAO0M,eAAe3L,SAASb,KAAKqL,E,KAKzC,CAEO,QAAAjL,G,QAEN,GADA/G,MAAM+G,WACF7G,KAAK+S,OAAQ,CAChB,IAAIpH,EAAS3L,KAAK6F,IAAI,UAClB+F,EAAS5L,KAAK6F,IAAI,UACtB,GAAI8F,GAAUC,EAAQ,CACrB,MAAMsH,EAAavH,EAAO9F,IAAI,QACxBsN,EAAavH,EAAO/F,IAAI,QAExBuN,EAAKF,EAAW3P,IAChB8P,EAAKH,EAAW1P,IAEhB8P,EAAKH,EAAW5P,IAChBgQ,EAAKJ,EAAW3P,IAEtBxD,KAAKwT,SAASC,OAAOL,EAAIC,GACzBrT,KAAKwT,SAASE,OAAOJ,EAAIC,GAEzB,MAAMI,EAAkC,QAAnB,EAAAT,EAAWjN,gBAAQ,eAAEJ,IAAI,eAAsBA,IAAI,SAAU,GAC5E+N,EAAkC,QAAnB,EAAAT,EAAWlN,gBAAQ,eAAEJ,IAAI,eAAsBA,IAAI,SAAU,GAE5ErD,EAAW6O,KAAKwC,MAAMP,EAAKF,EAAIG,EAAKF,GACpCS,EAAetR,EAAWmR,EAAeC,EAE/C,OAAY5T,KAAK4N,SAAUW,IAC1B,MAAMuD,EAASvD,EAAO1I,IAAI,UAC1B,GAAGiM,EAAO,CACT,MAAMiC,EAAWxF,EAAO1I,IAAI,YAAa,IAKzCiM,EAAOzK,IAAI,IAAK+L,EAAKO,EAAenR,GAAY8Q,EAAKF,GAAMU,EAAetR,GAAY8Q,EAAKF,GAAMW,GACjGjC,EAAOzK,IAAI,IAAKgM,EAAKM,EAAenR,GAAY+Q,EAAKF,GAAMS,EAAetR,GAAY+Q,EAAKF,GAAMU,GAE9FxF,EAAO1I,IAAI,eACbiM,EAAOzK,IAAI,WAA2C,IAA/BgK,KAAK2C,MAAMT,EAAKF,EAAIC,EAAKF,GAAY/B,KAAK4C,GAAK1F,EAAO1I,IAAI,kBAAmB,G,MAM1G,CAEO,IAAA4K,CAAKT,GASX,OARA,OAAYhQ,KAAK4N,SAAUW,IAC1B,GAAGA,EAAO,CACT,MAAMuD,EAASvD,EAAO1I,IAAI,UACvBiM,GACFA,EAAOrB,KAAKT,E,KAIRlQ,MAAM2Q,MACd,CAEO,IAAAK,CAAKd,GASX,OARA,OAAYhQ,KAAK4N,SAAUW,IAC1B,GAAGA,EAAO,CACT,MAAMuD,EAASvD,EAAO1I,IAAI,UACvBiM,GACFA,EAAOhB,KAAKd,E,KAIRlQ,MAAMgR,MACd,CAEO,cAAAoD,GAGN,GAFApU,MAAMoU,iBAEFlU,KAAK8G,QAAQ,UAAW,CAC3B,MAAM6E,EAAS3L,KAAK6F,IAAI,UACpB8F,GACgBA,EAAO9F,IAAI,QACnBQ,OAAOC,GAAG,mBAAmB,KACvCtG,KAAKmU,cAAc,SAAS,G,CAI/B,GAAInU,KAAK8G,QAAQ,UAAW,CAC3B,MAAM8E,EAAS5L,KAAK6F,IAAI,UACpB+F,GACgBA,EAAO/F,IAAI,QACnBQ,OAAOC,GAAG,mBAAmB,KACvCtG,KAAKmU,cAAc,SAAS,G,CAIhC,CAEO,OAAA/M,GACNtH,MAAMsH,UACN,OAAYpH,KAAK4N,SAAUW,IAC1BA,EAAOnH,SAAS,IAEjBpH,KAAK4N,QAAU,EAChB,EAnIA,qC,gDAAkC,kBAClC,sC,gDAA0CgF,EAAA,EAASjL,WAAWC,OAAO,CAAC+K,EAAc9K,c,cCqD9E,MAAeuM,UAAwBlI,EAA9C,c,oBAUC,0C,gDAA4H,IAAI5G,EAAA,KAmBhI,oC,gDAA2D,IAAIA,EAAA,EAC9DC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMwR,EAAoB7M,KAAKzF,KAAKC,MAAO,CAC1CyF,UAAW,YAAiB1F,KAAKqJ,MAAMzD,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,kBAAmB,YAAa,SAClH7I,EAAGvD,KAAKU,QAAU,EAClB8C,EAAGxD,KAAKW,SAAW,GACjB,CAACX,KAAKqJ,MAAMzD,eAQhB,sC,gDAAgD,IAAIN,EAAA,EACnDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMuT,EAAAC,EAAO7O,KAAKzF,KAAKC,MAAO,CAC7ByF,UAAW,YAAiB1F,KAAKuU,QAAQ3O,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,WAClF,CAACpM,KAAKuU,QAAQ3O,eAQlB,2C,gDAAqD,IAAIN,EAAA,EACxDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMuT,EAAAC,EAAO7O,KAAKzF,KAAKC,MAAO,CAC7ByF,UAAW,YAAiB1F,KAAKwU,aAAa5O,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,QAAS,WAChG,CAACpM,KAAKwU,aAAa5O,eAQvB,oC,gDAAqD,IAAIN,EAAA,EACxDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAM6R,EAAclN,KAAKzF,KAAKC,MAAO,CACpCyF,UAAW,YAAiB1F,KAAK0L,MAAM9F,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,kBAAmB,YAAa,UAChH,CAACpM,KAAK0L,MAAM9F,eAQhB,6C,gDAAiC5F,KAAKsH,SAASI,UAAUrC,EAAA,EAAUvE,IAAId,KAAKC,MAAO,CAAC,GAAI,IAqTzF,CAxXW,SAAAyG,GACT1G,KAAKyM,OAAOhG,KAAK,WAAY,IAAK,KAElCzG,KAAKyU,WAAWhO,KAAKzG,KAAK0U,YAAYrO,OAAOsO,OAAM,KAClD3U,KAAK0L,MAAMxB,MAAMsI,IAChBA,EAAKlE,eAAetO,KAAK0U,YAAY,GACpC,KAGH5U,MAAM4G,WACP,CA8DO,QAAA2F,CAASpG,GACf,MAAMoC,EAAOvI,MAAMuM,SAASpG,GAEtB2O,EAASvM,EAAKf,SAASI,UAAU1H,KAAKuU,QAAQpO,OAAQ,GAC5DnG,KAAKuU,QAAQ9N,KAAKmO,GAClBvM,EAAKwM,WAAW,gBAAiBD,GACjC3O,EAASsG,OAAO,SAAUqI,GAE1B,MAAME,EAAczM,EAAKf,SAASI,UAAU1H,KAAKwU,aAAarO,OAAQ,GACtEnG,KAAKwU,aAAa/N,KAAKqO,GACvB7O,EAASsG,OAAO,cAAeuI,GAE/B,MAAM5O,EAAQD,EAASJ,IAAI,SAE3B+O,EAAOtO,GAAG,UAAU,KACnB,MAAMsD,EAAyC,EAArCgL,EAAO/O,IAAI,SAAU7F,KAAKU,SACpCwF,EAAM3F,OAAO,CAAEwU,SAAUnL,EAAGoL,UAAWpL,IACvCkL,EAAYzN,IAAI,SAAUuC,EAAI,GAE9B5J,KAAKiV,qBAAqB,IAG3B,MAAMrL,EAAyC,EAArCgL,EAAO/O,IAAI,SAAU7F,KAAKU,SAMpC,OALAwF,EAAM3F,OAAO,CAAEwU,SAAUnL,EAAGoL,UAAWpL,IAEvCgL,EAAOxO,aAAaH,GACpB6O,EAAY1O,aAAaH,GAElBoC,CACR,CAEO,mBAAA4M,GAEP,CAEU,eAAAtG,CAAgB1I,GACzBA,EAASsG,OAAO,aAAc,IAC9BtG,EAASsG,OAAO,QAAS,IACzBzM,MAAM6O,gBAAgB1I,EACvB,CAEU,gBAAA2I,CAAiB3I,GAC1BnG,MAAM8O,iBAAiB3I,GAEvB,MAAMiP,EAAiBjP,EAASJ,IAAI,UACpC,GAAIqP,GAAkBA,EAAerP,IAAI,UAAY7F,KAAK6F,IAAI,YAAa,CAC1E,MAAM2M,EAAOxS,KAAKmV,cAAcD,EAAgBjP,GAChDA,EAASsG,OAAO,aAAciG,E,CAG/B,MAAMnK,EAAOpC,EAASJ,IAAI,QAC1B7F,KAAKoV,eAAepV,KAAK+M,WACzB1E,EAAKkK,aAAa,EACnB,CAKO,cAAA6C,CAAerI,GACrB,OAAYA,GAAY9G,IACvB,MAAMoP,EAAWpP,EAASJ,IAAI,YAC1BwP,GACH,OAAYA,GAAWnH,IACtB,MAAMoH,EAAmBtV,KAAKmO,iBAAiBnO,KAAK+M,UAAWmB,GAE3DoH,GACHtV,KAAKmV,cAAclP,EAAUqP,E,IAKhC,MAAMhO,EAAWrB,EAASJ,IAAI,YAC1ByB,GACHtH,KAAKoV,eAAe9N,E,GAGvB,CAEU,SAAAiO,CAAU7H,GACnB,MAAO,CAAEnK,EAAGmK,EAAcnK,EAAGC,EAAGkK,EAAclK,EAC/C,CAEU,iBAAAgS,CAAkBvP,GAC3B,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB6H,EAAgBzH,EAASJ,IAAI,mBAE7B4P,EAAQzV,KAAKuV,UAAU7H,GAEvBsC,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAExBwC,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAIkF,EAAMlS,EAAGyM,SAAUA,EAAUI,OAAQA,IAClE/H,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAIkF,EAAMjS,EAAGwM,SAAUA,EAAUI,OAAQA,GACnE,CAEU,WAAAzC,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAElBjG,KAAKwV,kBAAkBvP,GAEvB,MAEM6H,EAFgB7H,EAASJ,IAAI,mBAEKyB,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAKyN,aAAaM,EAAe,IAInC,MAAM2H,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAC3B+O,EAAS3O,EAASJ,IAAI,UACtByB,EAAWrB,EAASJ,IAAI,YAE1B+O,IACHA,EAAOgB,YAAY,OAAQF,GAC3Bd,EAAOgB,YAAY,cAAeD,GAClCf,EAAOgB,YAAY,SAAUF,IAG9B,MAAMZ,EAAc7O,EAASJ,IAAI,eAC7BiP,IACHA,EAAYc,YAAY,OAAQF,GAChCZ,EAAYc,YAAY,SAAUF,GAE7BpO,GAA+B,GAAnBA,EAASkB,OAIzBsM,EAAYD,WAAW,WAAW,GAHlCC,EAAYD,WAAW,WAAW,GAMrC,CAUO,aAAAM,CAAcxJ,EAA6CC,EAA6CrJ,GAE9G,IAAIiQ,EAEJ,MAAMqD,EAAclK,EAAO9F,IAAI,SAE3BgQ,GACH,OAAYA,GAAcC,IACrBA,EAAIjQ,IAAI,WAAa+F,IACxB4G,EAAOsD,E,IAKV,MAAMC,EAAcnK,EAAO/F,IAAI,SAiC/B,OA/BIkQ,GACH,OAAYA,GAAcD,IACrBA,EAAIjQ,IAAI,WAAa8F,IACxB6G,EAAOsD,E,IAKLtD,IACJA,EAAOxS,KAAK0L,MAAMvF,OAClBqM,EAAKjM,OAASvG,KACdA,KAAK0L,MAAMjF,KAAK+L,GAChBxS,KAAKiT,eAAe3L,SAASb,KAAK+L,GAClCA,EAAKnL,IAAI,SAAUsE,GACnB6G,EAAKnL,IAAI,SAAUuE,GACnB4G,EAAKpM,aAAauF,GAElB6G,EAAKlE,eAAetO,KAAK0U,aACzBlC,EAAKnL,IAAI,SAAUsE,EAAO9F,IAAI,SACd,MAAZtD,GACHiQ,EAAKnL,IAAI,WAAY9E,GAGtBoJ,EAAO9F,IAAI,cAAcY,KAAK+L,GAE9B,OAAY7G,EAAO9F,IAAI,SAAU2M,GACjC,OAAY5G,EAAO/F,IAAI,SAAU2M,GAEjCxS,KAAKgW,aAAaxD,EAAM7G,EAAQC,IAG1B4G,CACR,CASO,eAAAyD,CAAgBtK,EAA6CC,GAEnE,IAAI4G,EAEJ,MAAMqD,EAAclK,EAAO9F,IAAI,SAE3BgQ,GACH,OAAYA,GAAcC,IACrBA,GAAOA,EAAIjQ,IAAI,WAAa+F,IAC/B4G,EAAOsD,EACP,SAAcD,EAAarD,G,IAK9B,MAAMuD,EAAcnK,EAAO/F,IAAI,SAE3BkQ,GACH,OAAYA,GAAcD,IACrBA,GAAOA,EAAIjQ,IAAI,WAAa8F,IAC/B6G,EAAOsD,EACP,SAAcC,EAAavD,G,IAK1BA,GACHxS,KAAKkW,aAAa1D,GAGnBxS,KAAKmW,eACN,CAEU,aAAAA,GAEV,CAEU,YAAAD,CAAa1D,GACtBxS,KAAK0L,MAAMoE,YAAY0C,GACvBA,EAAKpL,SACN,CAKO,SAAAgP,CAAUzK,EAA6CC,GAC7D,MAAMiK,EAAclK,EAAO9F,IAAI,SAC/B,IAAIwQ,GAAS,EACTR,GACH,OAAYA,GAAcC,IACrBA,EAAIjQ,IAAI,WAAa+F,IACxByK,GAAS,E,IAKZ,MAAMN,EAAcnK,EAAO/F,IAAI,SAU/B,OARIkQ,GACH,OAAYA,GAAcD,IACrBA,EAAIjQ,IAAI,WAAa8F,IACxB0K,GAAS,E,IAKLA,CACR,CAEU,YAAAL,CAAaM,EAAsBC,EAA8CC,GAE3F,CAKO,eAAA3G,CAAgB5J,GACtBnG,MAAM+P,gBAAgB5J,GACtB,MAAMyF,EAAQzF,EAASJ,IAAI,SACvB6F,GACH,OAAYA,GAAQ8G,IACnBxS,KAAKkW,aAAa1D,EAAK,GAG1B,CAMO,cAAAhM,CAAeP,GACrB,MAAMuB,EAASvB,EAASJ,IAAI,UAE5B,GAAKI,EAASJ,IAAI,aAIjB,GAAI2B,EAAQ,CACXxH,KAAKuM,OAAO,mBAAoB/E,GAChC,MAAMzC,EAAO,mBACb/E,KAAKqG,OAAO+K,SAASrM,EAAM,CAAEA,KAAMA,EAAM6G,OAAQ5L,KAAMiG,SAAUuB,IACjExH,KAAKgI,gBAAgB/B,E,OAPtBjG,KAAKqH,IAAI,mBAAoBpB,EAU/B,EAjYA,qC,gDAAkC,oBAClC,sC,gDAA0CiG,EAAUvE,WAAWC,OAAO,CAACwM,EAAgBvM,c,wBCrGxF,MAEM4O,EAAI,WCCH,SAASlT,EAAEqG,GAChB,OAAOA,EAAErG,CACX,CAEO,SAASC,EAAEoG,GAChB,OAAOA,EAAEpG,CACX,CAEA,IACIkT,EAAerF,KAAK4C,IAAM,EAAI5C,KAAKsF,KAAK,IAE7B,WAAStN,GACtB,IAAIuN,EACAC,EAAQ,EACRC,EAAW,KACXC,EAAa,EAAI1F,KAAK2F,IAAIF,EAAU,EAAI,KACxCG,EAAc,EACd3S,EAAgB,GAChB4S,EAAS,IAAItO,IACbuO,GAAU,EAAAC,EAAA,IAAMrW,GAChBmG,GAAQ,EAAAkK,EAAA,GAAS,OAAQ,OACzBiG,EDpBS,WACb,IAAIC,EAAI,EACR,MAAO,KAAOA,GANN,QAMeA,EALf,YAKwBb,GAAKA,CACvC,CCiBec,GAIb,SAASxW,IACPyW,IACAtQ,EAAMoD,KAAK,OAAQsM,GACfC,EAAQC,IACVK,EAAQM,OACRvQ,EAAMoD,KAAK,MAAOsM,GAEtB,CAEA,SAASY,EAAKE,GACZ,IAAInP,EAAqBF,EAAlBa,EAAIG,EAAMb,YAEEK,IAAf6O,IAA0BA,EAAa,GAE3C,IAAK,IAAIpM,EAAI,EAAGA,EAAIoM,IAAcpM,EAOhC,IANAuL,IAAUI,EAAcJ,GAASE,EAEjCG,EAAOS,SAAQ,SAASC,GACtBA,EAAMf,EACR,IAEKtO,EAAI,EAAGA,EAAIW,IAAKX,EAEJ,OADfF,EAAOgB,EAAMd,IACJsP,GAAYxP,EAAK9E,GAAK8E,EAAKyP,IAAMxT,GACrC+D,EAAK9E,EAAI8E,EAAKwP,GAAIxP,EAAKyP,GAAK,GAClB,MAAXzP,EAAK0P,GAAY1P,EAAK7E,GAAK6E,EAAK2P,IAAM1T,GACrC+D,EAAK7E,EAAI6E,EAAK0P,GAAI1P,EAAK2P,GAAK,GAIrC,OAAOpB,CACT,CAEA,SAASqB,IACP,IAAK,IAA6B5P,EAAzBE,EAAI,EAAGW,EAAIG,EAAMb,OAAcD,EAAIW,IAAKX,EAAG,CAIlD,IAHAF,EAAOgB,EAAMd,IAAS8B,MAAQ9B,EACf,MAAXF,EAAKwP,KAAYxP,EAAK9E,EAAI8E,EAAKwP,IACpB,MAAXxP,EAAK0P,KAAY1P,EAAK7E,EAAI6E,EAAK0P,IAC/BG,MAAM7P,EAAK9E,IAAM2U,MAAM7P,EAAK7E,GAAI,CAClC,IAAId,EAxDQ,GAwDiB2O,KAAKsF,KAAK,GAAMpO,GAAI4P,EAAQ5P,EAAImO,EAC7DrO,EAAK9E,EAAIb,EAAS2O,KAAK+G,IAAID,GAC3B9P,EAAK7E,EAAId,EAAS2O,KAAKgH,IAAIF,EAC7B,EACID,MAAM7P,EAAKyP,KAAOI,MAAM7P,EAAK2P,OAC/B3P,EAAKyP,GAAKzP,EAAK2P,GAAK,EAExB,CACF,CAEA,SAASM,EAAgBV,GAEvB,OADIA,EAAMW,YAAYX,EAAMW,WAAWlP,EAAOgO,GACvCO,CACT,CAIA,OA1Da,MAATvO,IAAeA,EAAQ,IAwD3B4O,IAEOrB,EAAa,CAClBY,KAAMA,EAENgB,QAAS,WACP,OAAOrB,EAAQqB,QAAQzX,GAAO6V,CAChC,EAEAa,KAAM,WACJ,OAAON,EAAQM,OAAQb,CACzB,EAEAvN,MAAO,SAASoP,GACd,OAAOC,UAAUlQ,QAAUa,EAAQoP,EAAGR,IAAmBf,EAAOS,QAAQW,GAAkB1B,GAAcvN,CAC1G,EAEAwN,MAAO,SAAS4B,GACd,OAAOC,UAAUlQ,QAAUqO,GAAS4B,EAAG7B,GAAcC,CACvD,EAEAC,SAAU,SAAS2B,GACjB,OAAOC,UAAUlQ,QAAUsO,GAAY2B,EAAG7B,GAAcE,CAC1D,EAEAC,WAAY,SAAS0B,GACnB,OAAOC,UAAUlQ,QAAUuO,GAAc0B,EAAG7B,IAAeG,CAC7D,EAEAE,YAAa,SAASwB,GACpB,OAAOC,UAAUlQ,QAAUyO,GAAewB,EAAG7B,GAAcK,CAC7D,EAEA3S,cAAe,SAASmU,GACtB,OAAOC,UAAUlQ,QAAUlE,EAAgB,EAAImU,EAAG7B,GAAc,EAAItS,CACtE,EAEAqU,aAAc,SAASF,GACrB,OAAOC,UAAUlQ,QAAU6O,EAASoB,EAAGvB,EAAOS,QAAQW,GAAkB1B,GAAcS,CACxF,EAEAO,MAAO,SAASgB,EAAMH,GACpB,OAAOC,UAAUlQ,OAAS,GAAW,MAALiQ,EAAYvB,EAAO2B,OAAOD,GAAQ1B,EAAO7P,IAAIuR,EAAMN,EAAgBG,IAAM7B,GAAcM,EAAOrR,IAAI+S,EACpI,EAEApO,KAAM,SAASjH,EAAGC,EAAGd,GACnB,IAEIoW,EACAC,EACAC,EACA3Q,EACA4Q,EANA1Q,EAAI,EACJW,EAAIG,EAAMb,OAUd,IAHc,MAAV9F,EAAgBA,EAAS+B,IACxB/B,GAAUA,EAEV6F,EAAI,EAAGA,EAAIW,IAAKX,GAInByQ,GAFAF,EAAKvV,GADL8E,EAAOgB,EAAMd,IACChF,GAEJuV,GADVC,EAAKvV,EAAI6E,EAAK7E,GACMuV,GACXrW,IAAQuW,EAAU5Q,EAAM3F,EAASsW,GAG5C,OAAOC,CACT,EAEA3S,GAAI,SAASsS,EAAMH,GACjB,OAAOC,UAAUlQ,OAAS,GAAKtB,EAAMZ,GAAGsS,EAAMH,GAAI7B,GAAc1P,EAAMZ,GAAGsS,EAC3E,EAEJ,CCrJA,SAAS,EAAIM,EAAM3V,EAAGC,EAAGoG,GACvB,GAAIsO,MAAM3U,IAAM2U,MAAM1U,GAAI,OAAO0V,EAEjC,IAAI1R,EAOA2R,EACAC,EACAC,EACAC,EACAC,EACAC,EACAjR,EACAkR,EAbApR,EAAO6Q,EAAKjZ,MACZyZ,EAAO,CAAC/Q,KAAMiB,GACdwJ,EAAK8F,EAAKS,IACVtG,EAAK6F,EAAKU,IACVtG,EAAK4F,EAAKW,IACVtG,EAAK2F,EAAKY,IAWd,IAAKzR,EAAM,OAAO6Q,EAAKjZ,MAAQyZ,EAAMR,EAGrC,KAAO7Q,EAAKG,QAGV,IAFI+Q,EAAQhW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,GACtDK,EAAShW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,EACvD5R,EAASa,IAAQA,EAAOA,EAAKE,EAAIiR,GAAU,EAAID,IAAS,OAAO/R,EAAOe,GAAKmR,EAAMR,EAMvF,GAFAG,GAAMH,EAAKa,GAAGzP,KAAK,KAAMjC,EAAKM,MAC9B2Q,GAAMJ,EAAKc,GAAG1P,KAAK,KAAMjC,EAAKM,MAC1BpF,IAAM8V,GAAM7V,IAAM8V,EAAI,OAAOI,EAAKnP,KAAOlC,EAAMb,EAASA,EAAOe,GAAKmR,EAAOR,EAAKjZ,MAAQyZ,EAAMR,EAGlG,GACE1R,EAASA,EAASA,EAAOe,GAAK,IAAIgB,MAAM,GAAK2P,EAAKjZ,MAAQ,IAAIsJ,MAAM,IAChEgQ,EAAQhW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,GACtDK,EAAShW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,SACnD7Q,EAAIiR,GAAU,EAAID,KAAYE,GAAKH,GAAMF,IAAO,EAAKC,GAAMF,IACrE,OAAO3R,EAAOiS,GAAKpR,EAAMb,EAAOe,GAAKmR,EAAMR,CAC7C,CC/Ce,WAAS7Q,EAAM+K,EAAIC,EAAIC,EAAIC,GACxCvT,KAAKqI,KAAOA,EACZrI,KAAKoT,GAAKA,EACVpT,KAAKqT,GAAKA,EACVrT,KAAKsT,GAAKA,EACVtT,KAAKuT,GAAKA,CACZ,CCNO,SAAS0G,EAASrQ,GACvB,OAAOA,EAAE,EACX,CCFO,SAASsQ,EAAStQ,GACvB,OAAOA,EAAE,EACX,CCWe,SAASuQ,EAAS9Q,EAAO9F,EAAGC,GACzC,IAAI0V,EAAO,IAAIkB,EAAc,MAAL7W,EAAY0W,EAAW1W,EAAQ,MAALC,EAAY0W,EAAW1W,EAAG6W,IAAKA,IAAKA,IAAKA,KAC3F,OAAgB,MAAThR,EAAgB6P,EAAOA,EAAKoB,OAAOjR,EAC5C,CAEA,SAAS+Q,EAAS7W,EAAGC,EAAG4P,EAAIC,EAAIC,EAAIC,GAClCvT,KAAK+Z,GAAKxW,EACVvD,KAAKga,GAAKxW,EACVxD,KAAK2Z,IAAMvG,EACXpT,KAAK4Z,IAAMvG,EACXrT,KAAK6Z,IAAMvG,EACXtT,KAAK8Z,IAAMvG,EACXvT,KAAKC,WAAQ4I,CACf,CAEA,SAAS0R,EAAUb,GAEjB,IADA,IAAI7N,EAAO,CAAClD,KAAM+Q,EAAK/Q,MAAO4B,EAAOsB,EAC9B6N,EAAOA,EAAKnP,MAAMA,EAAOA,EAAKA,KAAO,CAAC5B,KAAM+Q,EAAK/Q,MACxD,OAAOkD,CACT,CAEA,IAAI2O,EAAYL,EAASpQ,UAAYqQ,EAASrQ,UClC/B,WAASxG,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCJe,WAAS8T,GACtB,OAA0B,MAAlBA,IAAW,GACrB,CCEA,SAAS,EAAEzN,GACT,OAAOA,EAAErG,EAAIqG,EAAEkO,EACjB,CAEA,SAAS,EAAElO,GACT,OAAOA,EAAEpG,EAAIoG,EAAEoO,EACjB,CAEe,YAAStV,GACtB,IAAI2G,EACAoR,EACApD,EACA9U,EAAW,EACXmV,EAAa,EAIjB,SAASE,IASP,IARA,IAAIrP,EACA2Q,EACA7Q,EACAqS,EACAC,EACAC,EACAC,EANG3R,EAAIG,EAAMb,OAQR8C,EAAI,EAAGA,EAAIoM,IAAcpM,EAEhC,IADA4N,EAAOiB,EAAS9Q,EAAO,EAAG,GAAGyR,WAAWC,GACnCxS,EAAI,EAAGA,EAAIW,IAAKX,EACnBF,EAAOgB,EAAMd,GACbqS,EAAKH,EAAMpS,EAAKgC,OAAQwQ,EAAMD,EAAKA,EACnCF,EAAKrS,EAAK9E,EAAI8E,EAAKyP,GACnB6C,EAAKtS,EAAK7E,EAAI6E,EAAK2P,GACnBkB,EAAK8B,MAAMC,GAIf,SAASA,EAAMC,EAAM9H,EAAIC,EAAIC,EAAIC,GAC/B,IAAI5K,EAAOuS,EAAKvS,KAAMwS,EAAKD,EAAK9a,EAAGA,EAAIwa,EAAKO,EAC5C,IAAIxS,EAiBJ,OAAOyK,EAAKsH,EAAKta,GAAKkT,EAAKoH,EAAKta,GAAKiT,EAAKsH,EAAKva,GAAKmT,EAAKoH,EAAKva,EAhB5D,GAAIuI,EAAK0B,MAAQhC,EAAKgC,MAAO,CAC3B,IAAI9G,EAAImX,EAAK/R,EAAKpF,EAAIoF,EAAKmP,GACvBtU,EAAImX,EAAKhS,EAAKnF,EAAImF,EAAKqP,GACvBoD,EAAI7X,EAAIA,EAAIC,EAAIA,EAChB4X,EAAIhb,EAAIA,IACA,IAANmD,IAA6B6X,IAApB7X,EAAI8X,EAAOhE,IAAkB9T,GAChC,IAANC,IAA6B4X,IAApB5X,EAAI6X,EAAOhE,IAAkB7T,GAC1C4X,GAAKhb,GAAKgb,EAAI/J,KAAKsF,KAAKyE,KAAOA,EAAI7Y,EACnC8F,EAAKyP,KAAOvU,GAAK6X,IAAMhb,GAAK+a,GAAMA,IAAON,EAAMM,IAC/C9S,EAAK2P,KAAOxU,GAAK4X,GAAKhb,EACtBuI,EAAKmP,IAAMvU,GAAKnD,EAAI,EAAIA,GACxBuI,EAAKqP,IAAMxU,EAAIpD,EAEnB,CAIJ,CACF,CAEA,SAAS2a,EAAQG,GACf,GAAIA,EAAKvS,KAAM,OAAOuS,EAAK9a,EAAIqa,EAAMS,EAAKvS,KAAK0B,OAC/C,IAAK,IAAI9B,EAAI2S,EAAK9a,EAAI,EAAGmI,EAAI,IAAKA,EAC5B2S,EAAK3S,IAAM2S,EAAK3S,GAAGnI,EAAI8a,EAAK9a,IAC9B8a,EAAK9a,EAAI8a,EAAK3S,GAAGnI,EAGvB,CAEA,SAASmY,IACP,GAAKlP,EAAL,CACA,IAAId,EAAqBF,EAAlBa,EAAIG,EAAMb,OAEjB,IADAiS,EAAQ,IAAIlR,MAAML,GACbX,EAAI,EAAGA,EAAIW,IAAKX,EAAGF,EAAOgB,EAAMd,GAAIkS,EAAMpS,EAAKgC,QAAU3H,EAAO2F,EAAME,EAAGc,EAH5D,CAIpB,CAoBA,MA/EsB,mBAAX3G,IAAuBA,EAAS4Y,EAAmB,MAAV5Y,EAAiB,GAAKA,IA6D1EkV,EAAMW,WAAa,SAASgD,EAAQC,GAClCnS,EAAQkS,EACRlE,EAASmE,EACTjD,GACF,EAEAX,EAAMF,WAAa,SAASe,GAC1B,OAAOC,UAAUlQ,QAAUkP,GAAce,EAAGb,GAASF,CACvD,EAEAE,EAAMrV,SAAW,SAASkW,GACxB,OAAOC,UAAUlQ,QAAUjG,GAAYkW,EAAGb,GAASrV,CACrD,EAEAqV,EAAMlV,OAAS,SAAS+V,GACtB,OAAOC,UAAUlQ,QAAU9F,EAAsB,mBAAN+V,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASlV,CACzG,EAEOkV,CACT,CChGA,SAASvN,GAAMT,GACb,OAAOA,EAAES,KACX,CAEA,SAAS,GAAKoR,EAAUC,GACtB,IAAIrT,EAAOoT,EAAS5V,IAAI6V,GACxB,IAAKrT,EAAM,MAAM,IAAIsT,MAAM,mBAAqBD,GAChD,OAAOrT,CACT,CAEe,YAASqD,GACtB,IAEIkQ,EAEAC,EACAxS,EACAjB,EACA0T,EACAzE,EARAnJ,EAAK7D,GACL9H,EAYJ,SAAyBiQ,GACvB,OAAO,EAAInB,KAAKC,IAAIlJ,EAAMoK,EAAK7G,OAAOtB,OAAQjC,EAAMoK,EAAK5G,OAAOvB,OAClE,EAZI7H,EAAW8Y,EAAS,IAMpB5D,EAAa,EAQjB,SAASE,EAAMf,GACb,IAAK,IAAIvL,EAAI,EAAGpC,EAAIwC,EAAMlD,OAAQ8C,EAAIoM,IAAcpM,EAClD,IAAK,IAAWkH,EAAM7G,EAAQC,EAAQrI,EAAGC,EAAG4X,EAAGpQ,EAAtCzC,EAAI,EAAqCA,EAAIW,IAAKX,EACxCoD,GAAjB6G,EAAO9G,EAAMnD,IAAkBoD,OAC/BpI,GADuCqI,EAAS4G,EAAK5G,QAC1CrI,EAAIqI,EAAOkM,GAAKnM,EAAOpI,EAAIoI,EAAOmM,IAAMuD,EAAOhE,GAC1D7T,EAAIoI,EAAOpI,EAAIoI,EAAOoM,GAAKrM,EAAOnI,EAAImI,EAAOqM,IAAMqD,EAAOhE,GAG1D9T,GADA6X,IADAA,EAAI/J,KAAKsF,KAAKpT,EAAIA,EAAIC,EAAIA,IACjBqY,EAAUtT,IAAM6S,EAAIvE,EAAQ+E,EAAUrT,GACvC/E,GAAK4X,EACbxP,EAAOkM,IAAMvU,GAAKyH,EAAI8Q,EAAKvT,IAC3BqD,EAAOoM,IAAMxU,EAAIwH,EACjBW,EAAOmM,IAAMvU,GAAKyH,EAAI,EAAIA,GAC1BW,EAAOqM,IAAMxU,EAAIwH,CAGvB,CAEA,SAASuN,IACP,GAAKlP,EAAL,CAEA,IAAId,EAIAiK,EAHAtJ,EAAIG,EAAMb,OACViO,EAAI/K,EAAMlD,OACViT,EAAW,IAAI7S,IAAIS,EAAM0S,KAAI,CAACnS,EAAGrB,IAAM,CAAC2F,EAAGtE,EAAGrB,EAAGc,GAAQO,MAG7D,IAAKrB,EAAI,EAAGH,EAAQ,IAAImB,MAAML,GAAIX,EAAIkO,IAAKlO,GACzCiK,EAAO9G,EAAMnD,IAAS8B,MAAQ9B,EACH,iBAAhBiK,EAAK7G,SAAqB6G,EAAK7G,OAAS,GAAK8P,EAAUjJ,EAAK7G,SAC5C,iBAAhB6G,EAAK5G,SAAqB4G,EAAK5G,OAAS,GAAK6P,EAAUjJ,EAAK5G,SACvExD,EAAMoK,EAAK7G,OAAOtB,QAAUjC,EAAMoK,EAAK7G,OAAOtB,QAAU,GAAK,EAC7DjC,EAAMoK,EAAK5G,OAAOvB,QAAUjC,EAAMoK,EAAK5G,OAAOvB,QAAU,GAAK,EAG/D,IAAK9B,EAAI,EAAGuT,EAAO,IAAIvS,MAAMkN,GAAIlO,EAAIkO,IAAKlO,EACxCiK,EAAO9G,EAAMnD,GAAIuT,EAAKvT,GAAKH,EAAMoK,EAAK7G,OAAOtB,QAAUjC,EAAMoK,EAAK7G,OAAOtB,OAASjC,EAAMoK,EAAK5G,OAAOvB,QAGtGuR,EAAY,IAAIrS,MAAMkN,GAAIuF,IAC1BH,EAAY,IAAItS,MAAMkN,GAAIwF,GArBR,CAsBpB,CAEA,SAASD,IACP,GAAK3S,EAEL,IAAK,IAAId,EAAI,EAAGW,EAAIwC,EAAMlD,OAAQD,EAAIW,IAAKX,EACzCqT,EAAUrT,IAAMhG,EAASmJ,EAAMnD,GAAIA,EAAGmD,EAE1C,CAEA,SAASuQ,IACP,GAAK5S,EAEL,IAAK,IAAId,EAAI,EAAGW,EAAIwC,EAAMlD,OAAQD,EAAIW,IAAKX,EACzCsT,EAAUtT,IAAM/F,EAASkJ,EAAMnD,GAAIA,EAAGmD,EAE1C,CA4BA,OA1Fa,MAATA,IAAeA,EAAQ,IAgE3BkM,EAAMW,WAAa,SAASgD,EAAQC,GAClCnS,EAAQkS,EACRlE,EAASmE,EACTjD,GACF,EAEAX,EAAMlM,MAAQ,SAAS+M,GACrB,OAAOC,UAAUlQ,QAAUkD,EAAQ+M,EAAGF,IAAcX,GAASlM,CAC/D,EAEAkM,EAAM1J,GAAK,SAASuK,GAClB,OAAOC,UAAUlQ,QAAU0F,EAAKuK,EAAGb,GAAS1J,CAC9C,EAEA0J,EAAMF,WAAa,SAASe,GAC1B,OAAOC,UAAUlQ,QAAUkP,GAAce,EAAGb,GAASF,CACvD,EAEAE,EAAMrV,SAAW,SAASkW,GACxB,OAAOC,UAAUlQ,QAAUjG,EAAwB,mBAANkW,EAAmBA,EAAI6C,GAAU7C,GAAIuD,IAAsBpE,GAASrV,CACnH,EAEAqV,EAAMpV,SAAW,SAASiW,GACxB,OAAOC,UAAUlQ,QAAUhG,EAAwB,mBAANiW,EAAmBA,EAAI6C,GAAU7C,GAAIwD,IAAsBrE,GAASpV,CACnH,EAEOoV,CACT,CJhFA4C,EAAU3O,KAAO,WACf,IAEIxC,EACAL,EAHA6C,EAAO,IAAIuO,EAASpa,KAAK+Z,GAAI/Z,KAAKga,GAAIha,KAAK2Z,IAAK3Z,KAAK4Z,IAAK5Z,KAAK6Z,IAAK7Z,KAAK8Z,KACzEzR,EAAOrI,KAAKC,MAIhB,IAAKoI,EAAM,OAAOwD,EAElB,IAAKxD,EAAKG,OAAQ,OAAOqD,EAAK5L,MAAQsa,EAAUlS,GAAOwD,EAGvD,IADAxC,EAAQ,CAAC,CAACsC,OAAQtD,EAAMuD,OAAQC,EAAK5L,MAAQ,IAAIsJ,MAAM,KAChDlB,EAAOgB,EAAMC,OAClB,IAAK,IAAIf,EAAI,EAAGA,EAAI,IAAKA,GACnBS,EAAQX,EAAKsD,OAAOpD,MAClBS,EAAMR,OAAQa,EAAM5C,KAAK,CAACkF,OAAQ3C,EAAO4C,OAAQvD,EAAKuD,OAAOrD,GAAK,IAAIgB,MAAM,KAC3ElB,EAAKuD,OAAOrD,GAAKgS,EAAUvR,IAKtC,OAAO6C,CACT,EAEA2O,EAAU0B,IJ3DK,SAAStS,GACtB,MAAMrG,GAAKvD,KAAK+Z,GAAGzP,KAAK,KAAMV,GAC1BpG,GAAKxD,KAAKga,GAAG1P,KAAK,KAAMV,GAC5B,OAAO,EAAI5J,KAAKmc,MAAM5Y,EAAGC,GAAID,EAAGC,EAAGoG,EACrC,EIwDA4Q,EAAUF,OJXH,SAAgB3R,GACrB,IAAIiB,EAAGrB,EACHhF,EACAC,EAFM0F,EAAIP,EAAKH,OAGf4T,EAAK,IAAI7S,MAAML,GACfmT,EAAK,IAAI9S,MAAML,GACfkK,EAAK3O,IACL4O,EAAK5O,IACL6O,GAAK,IACLC,GAAK,IAGT,IAAKhL,EAAI,EAAGA,EAAIW,IAAKX,EACf2P,MAAM3U,GAAKvD,KAAK+Z,GAAGzP,KAAK,KAAMV,EAAIjB,EAAKJ,MAAQ2P,MAAM1U,GAAKxD,KAAKga,GAAG1P,KAAK,KAAMV,MACjFwS,EAAG7T,GAAKhF,EACR8Y,EAAG9T,GAAK/E,EACJD,EAAI6P,IAAIA,EAAK7P,GACbA,EAAI+P,IAAIA,EAAK/P,GACbC,EAAI6P,IAAIA,EAAK7P,GACbA,EAAI+P,IAAIA,EAAK/P,IAInB,GAAI4P,EAAKE,GAAMD,EAAKE,EAAI,OAAOvT,KAM/B,IAHAA,KAAKmc,MAAM/I,EAAIC,GAAI8I,MAAM7I,EAAIC,GAGxBhL,EAAI,EAAGA,EAAIW,IAAKX,EACnB,EAAIvI,KAAMoc,EAAG7T,GAAI8T,EAAG9T,GAAII,EAAKJ,IAG/B,OAAOvI,IACT,EItBAwa,EAAU2B,MK7DK,SAAS5Y,EAAGC,GACzB,GAAI0U,MAAM3U,GAAKA,IAAM2U,MAAM1U,GAAKA,GAAI,OAAOxD,KAE3C,IAAIoT,EAAKpT,KAAK2Z,IACVtG,EAAKrT,KAAK4Z,IACVtG,EAAKtT,KAAK6Z,IACVtG,EAAKvT,KAAK8Z,IAKd,GAAI5B,MAAM9E,GACRE,GAAMF,EAAK/B,KAAKiL,MAAM/Y,IAAM,EAC5BgQ,GAAMF,EAAKhC,KAAKiL,MAAM9Y,IAAM,MAIzB,CAMH,IALA,IAEIgE,EACAe,EAHAgU,EAAIjJ,EAAKF,GAAM,EACf/K,EAAOrI,KAAKC,MAITmT,EAAK7P,GAAKA,GAAK+P,GAAMD,EAAK7P,GAAKA,GAAK+P,GAGzC,OAFAhL,GAAK/E,EAAI6P,IAAO,EAAK9P,EAAI6P,GACzB5L,EAAS,IAAI+B,MAAM,IAAWhB,GAAKF,EAAMA,EAAOb,EAAQ+U,GAAK,EACrDhU,GACN,KAAK,EAAG+K,EAAKF,EAAKmJ,EAAGhJ,EAAKF,EAAKkJ,EAAG,MAClC,KAAK,EAAGnJ,EAAKE,EAAKiJ,EAAGhJ,EAAKF,EAAKkJ,EAAG,MAClC,KAAK,EAAGjJ,EAAKF,EAAKmJ,EAAGlJ,EAAKE,EAAKgJ,EAAG,MAClC,KAAK,EAAGnJ,EAAKE,EAAKiJ,EAAGlJ,EAAKE,EAAKgJ,EAI/Bvc,KAAKC,OAASD,KAAKC,MAAMuI,SAAQxI,KAAKC,MAAQoI,EACpD,CAMA,OAJArI,KAAK2Z,IAAMvG,EACXpT,KAAK4Z,IAAMvG,EACXrT,KAAK6Z,IAAMvG,EACXtT,KAAK8Z,IAAMvG,EACJvT,IACT,ELoBAwa,EAAU7R,KM9DK,WACb,IAAIA,EAAO,GAIX,OAHA3I,KAAKgb,OAAM,SAAS3S,GAClB,IAAKA,EAAKG,OAAQ,GAAGG,EAAKlC,KAAK4B,EAAKM,YAAcN,EAAOA,EAAKkC,KAChE,IACO5B,CACT,ENyDA6R,EAAUgC,OO/DK,SAAS/D,GACtB,OAAOC,UAAUlQ,OACXxI,KAAKmc,OAAO1D,EAAE,GAAG,IAAKA,EAAE,GAAG,IAAI0D,OAAO1D,EAAE,GAAG,IAAKA,EAAE,GAAG,IACrDP,MAAMlY,KAAK2Z,UAAO9Q,EAAY,CAAC,CAAC7I,KAAK2Z,IAAK3Z,KAAK4Z,KAAM,CAAC5Z,KAAK6Z,IAAK7Z,KAAK8Z,KAC7E,EP4DAU,EAAUhQ,KQ9DK,SAASjH,EAAGC,EAAGd,GAC5B,IAAIiG,EAGA2K,EACAC,EACAkJ,EACAC,EAKAC,EACApU,EAXA6K,EAAKpT,KAAK2Z,IACVtG,EAAKrT,KAAK4Z,IAKVgD,EAAK5c,KAAK6Z,IACVgD,EAAK7c,KAAK8Z,IACVgD,EAAQ,GACRzU,EAAOrI,KAAKC,MAYhB,IARIoI,GAAMyU,EAAMrW,KAAK,IAAI,EAAK4B,EAAM+K,EAAIC,EAAIuJ,EAAIC,IAClC,MAAVna,EAAgBA,EAAS+B,KAE3B2O,EAAK7P,EAAIb,EAAQ2Q,EAAK7P,EAAId,EAC1Bka,EAAKrZ,EAAIb,EAAQma,EAAKrZ,EAAId,EAC1BA,GAAUA,GAGLia,EAAIG,EAAMxT,OAGf,OAAMjB,EAAOsU,EAAEtU,QACPiL,EAAKqJ,EAAEvJ,IAAMwJ,IACbrJ,EAAKoJ,EAAEtJ,IAAMwJ,IACbJ,EAAKE,EAAErJ,IAAMF,IACbsJ,EAAKC,EAAEpJ,IAAMF,GAGrB,GAAIhL,EAAKG,OAAQ,CACf,IAAI2Q,GAAM7F,EAAKmJ,GAAM,EACjBrD,GAAM7F,EAAKmJ,GAAM,EAErBI,EAAMrW,KACJ,IAAI,EAAK4B,EAAK,GAAI8Q,EAAIC,EAAIqD,EAAIC,GAC9B,IAAI,EAAKrU,EAAK,GAAIiL,EAAI8F,EAAID,EAAIuD,GAC9B,IAAI,EAAKrU,EAAK,GAAI8Q,EAAI5F,EAAIkJ,EAAIrD,GAC9B,IAAI,EAAK/Q,EAAK,GAAIiL,EAAIC,EAAI4F,EAAIC,KAI5B7Q,GAAK/E,GAAK4V,IAAO,EAAK7V,GAAK4V,KAC7BwD,EAAIG,EAAMA,EAAMtU,OAAS,GACzBsU,EAAMA,EAAMtU,OAAS,GAAKsU,EAAMA,EAAMtU,OAAS,EAAID,GACnDuU,EAAMA,EAAMtU,OAAS,EAAID,GAAKoU,EAElC,KAGK,CACH,IAAI7D,EAAKvV,GAAKvD,KAAK+Z,GAAGzP,KAAK,KAAMjC,EAAKM,MAClCoQ,EAAKvV,GAAKxD,KAAKga,GAAG1P,KAAK,KAAMjC,EAAKM,MAClCqQ,EAAKF,EAAKA,EAAKC,EAAKA,EACxB,GAAIC,EAAKtW,EAAQ,CACf,IAAIkH,EAAIyH,KAAKsF,KAAKjU,EAASsW,GAC3B5F,EAAK7P,EAAIqG,EAAGyJ,EAAK7P,EAAIoG,EACrBgT,EAAKrZ,EAAIqG,EAAGiT,EAAKrZ,EAAIoG,EACrBjB,EAAON,EAAKM,IACd,CACF,CAGF,OAAOA,CACT,ERJA6R,EAAUuC,OSjEK,SAASnT,GACtB,GAAIsO,MAAM3U,GAAKvD,KAAK+Z,GAAGzP,KAAK,KAAMV,KAAOsO,MAAM1U,GAAKxD,KAAKga,GAAG1P,KAAK,KAAMV,IAAK,OAAO5J,KAEnF,IAAIwH,EAEAwV,EACAjW,EACAwD,EAKAhH,EACAC,EACA2V,EACAC,EACAG,EACAC,EACAjR,EACAkR,EAfApR,EAAOrI,KAAKC,MAIZmT,EAAKpT,KAAK2Z,IACVtG,EAAKrT,KAAK4Z,IACVtG,EAAKtT,KAAK6Z,IACVtG,EAAKvT,KAAK8Z,IAWd,IAAKzR,EAAM,OAAOrI,KAIlB,GAAIqI,EAAKG,OAAQ,OAAa,CAG5B,IAFI+Q,EAAQhW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,GACtDK,EAAShW,IAAM4V,GAAM/F,EAAKE,GAAM,IAAIF,EAAK+F,EAAS7F,EAAK6F,EACrD5R,EAASa,IAAMA,EAAOA,EAAKE,EAAIiR,GAAU,EAAID,IAAS,OAAOvZ,KACnE,IAAKqI,EAAKG,OAAQ,OACdhB,EAAQe,EAAI,EAAK,IAAMf,EAAQe,EAAI,EAAK,IAAMf,EAAQe,EAAI,EAAK,MAAIyU,EAAWxV,EAAQiS,EAAIlR,EAChG,CAGA,KAAOF,EAAKM,OAASiB,MAAS7C,EAAWsB,IAAMA,EAAOA,EAAKkC,MAAO,OAAOvK,KAIzE,OAHIuK,EAAOlC,EAAKkC,cAAalC,EAAKkC,KAG9BxD,GAAkBwD,EAAOxD,EAASwD,KAAOA,SAAcxD,EAASwD,KAAOvK,MAGtEwH,GAGL+C,EAAO/C,EAAOe,GAAKgC,SAAc/C,EAAOe,IAGnCF,EAAOb,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAMA,EAAO,KACnDa,KAAUb,EAAO,IAAMA,EAAO,IAAMA,EAAO,IAAMA,EAAO,MACvDa,EAAKG,SACPwU,EAAUA,EAASvD,GAAKpR,EACvBrI,KAAKC,MAAQoI,GAGbrI,OAbaA,KAAKC,MAAQsK,EAAMvK,KAczC,ETUAwa,EAAUyC,USRH,SAAmBtU,GACxB,IAAK,IAAIJ,EAAI,EAAGW,EAAIP,EAAKH,OAAQD,EAAIW,IAAKX,EAAGvI,KAAK+c,OAAOpU,EAAKJ,IAC9D,OAAOvI,IACT,ETMAwa,EAAUrR,KUnEK,WACb,OAAOnJ,KAAKC,KACd,EVkEAua,EAAU0C,KWpEK,WACb,IAAIA,EAAO,EAIX,OAHAld,KAAKgb,OAAM,SAAS3S,GAClB,IAAKA,EAAKG,OAAQ,KAAK0U,QAAa7U,EAAOA,EAAKkC,KAClD,IACO2S,CACT,EX+DA1C,EAAUQ,MYnEK,SAAS7Q,GACtB,IAAgBwS,EAAsB3T,EAAOoK,EAAIC,EAAIC,EAAIC,EAArDuJ,EAAQ,GAAOzU,EAAOrI,KAAKC,MAE/B,IADIoI,GAAMyU,EAAMrW,KAAK,IAAI,EAAK4B,EAAMrI,KAAK2Z,IAAK3Z,KAAK4Z,IAAK5Z,KAAK6Z,IAAK7Z,KAAK8Z,MAChE6C,EAAIG,EAAMxT,OACf,IAAKa,EAAS9B,EAAOsU,EAAEtU,KAAM+K,EAAKuJ,EAAEvJ,GAAIC,EAAKsJ,EAAEtJ,GAAIC,EAAKqJ,EAAErJ,GAAIC,EAAKoJ,EAAEpJ,KAAOlL,EAAKG,OAAQ,CACvF,IAAI2Q,GAAM/F,EAAKE,GAAM,EAAG8F,GAAM/F,EAAKE,GAAM,GACrCvK,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOmQ,EAAIC,EAAI9F,EAAIC,KACxDvK,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOoK,EAAIgG,EAAID,EAAI5F,KACxDvK,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOmQ,EAAI9F,EAAIC,EAAI8F,KACxDpQ,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOoK,EAAIC,EAAI8F,EAAIC,GAC9D,CAEF,OAAOpZ,IACT,EZuDAwa,EAAUM,WapEK,SAAS3Q,GACtB,IAA2BwS,EAAvBG,EAAQ,GAAIvS,EAAO,GAEvB,IADIvK,KAAKC,OAAO6c,EAAMrW,KAAK,IAAI,EAAKzG,KAAKC,MAAOD,KAAK2Z,IAAK3Z,KAAK4Z,IAAK5Z,KAAK6Z,IAAK7Z,KAAK8Z,MAC5E6C,EAAIG,EAAMxT,OAAO,CACtB,IAAIjB,EAAOsU,EAAEtU,KACb,GAAIA,EAAKG,OAAQ,CACf,IAAIQ,EAAOoK,EAAKuJ,EAAEvJ,GAAIC,EAAKsJ,EAAEtJ,GAAIC,EAAKqJ,EAAErJ,GAAIC,EAAKoJ,EAAEpJ,GAAI4F,GAAM/F,EAAKE,GAAM,EAAG8F,GAAM/F,EAAKE,GAAM,GACxFvK,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOoK,EAAIC,EAAI8F,EAAIC,KACxDpQ,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOmQ,EAAI9F,EAAIC,EAAI8F,KACxDpQ,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOoK,EAAIgG,EAAID,EAAI5F,KACxDvK,EAAQX,EAAK,KAAIyU,EAAMrW,KAAK,IAAI,EAAKuC,EAAOmQ,EAAIC,EAAI9F,EAAIC,GAC9D,CACAhJ,EAAK9D,KAAKkW,EACZ,CACA,KAAOA,EAAIpS,EAAKjB,OACda,EAASwS,EAAEtU,KAAMsU,EAAEvJ,GAAIuJ,EAAEtJ,GAAIsJ,EAAErJ,GAAIqJ,EAAEpJ,IAEvC,OAAOvT,IACT,EbmDAwa,EAAUjX,EFnEK,SAASkV,GACtB,OAAOC,UAAUlQ,QAAUxI,KAAK+Z,GAAKtB,EAAGzY,MAAQA,KAAK+Z,EACvD,EEkEAS,EAAUhX,EDpEK,SAASiV,GACtB,OAAOC,UAAUlQ,QAAUxI,KAAKga,GAAKvB,EAAGzY,MAAQA,KAAKga,EACvD,Ee4JO,MAAMmD,WAAsB/I,EAAnC,c,oBAEC,mC,gDAAyB,kBAKzB,gD,gDAAsH,MAKtH,6C,gDAAoF,GAAqB,MAKzG,wC,gDAA6H,OAU7H,qC,gDAA+B,KAC/B,qC,gDAA+B,KAW/B,oC,gDAA0B,IAC1B,0C,iDAAiC,GA4UlC,CAtVW,SAAA1N,GACT5G,MAAM4G,YAEN1G,KAAKod,kBAAkB9W,GAAG,QAAQ,KACjCtG,KAAKqd,QACLrd,KAAKsd,qBAAqB,GAE5B,CAKO,gBAAA3Q,GACN7M,MAAM6M,mBACF3M,KAAK8G,QAAQ,gBACI9G,KAAK6F,IAAI,eACX7F,KAAKqd,QACtBrd,KAAKwM,eAAeqI,WAAW,WAAW,GAC1C7U,KAAKiT,eAAe4B,WAAW,WAAW,IAI5C,MAAMuI,EAAoBpd,KAAKod,kBAE3Bpd,KAAK8G,QAAQ,kBAChBsW,EAAkB9Y,cAActE,KAAK6F,IAAI,gBAAiB,IAGvD7F,KAAK8G,QAAQ,kBAChBsW,EAAkBrG,WAAW,EAAI1F,KAAK2F,IAAI,KAAO,EAAIhX,KAAK6F,IAAI,gBAAiB,MAEjF,CAKO,iBAAA0X,CAAkB1G,GACxB,MAAMuG,EAAoBpd,KAAKod,kBAC3BA,EAAkBvG,QAAU,MAC/BuG,EAAkBvG,MAAMA,GACxBuG,EAAkB5E,UAEpB,CAEO,mBAAAvD,GACNjV,KAAKwd,eACN,CAEU,eAAA7O,CAAgB1I,GACzB,MAAMwX,EAAmB,CAAEpT,MAAOrK,KAAKiN,OAAQ1J,EAAGvD,KAAK0d,aAAe,EAAGla,EAAGxD,KAAK2d,cAAgB,EAAG1X,SAAUA,GACxGoE,EAAQrK,KAAKub,OAAO9U,KAAKgX,GAAe,EAC9CA,EAAYpT,MAAQA,EAEpBrK,KAAKod,kBAAkB/T,MAAMrJ,KAAKub,QAElCtV,EAASoB,IAAI,cAAeoW,GAC5B3d,MAAM6O,gBAAgB1I,GAEtB,MAAMoC,EAAOpC,EAASJ,IAAI,QAC1BwC,EAAKhB,IAAI,KAAM,KACfgB,EAAK/B,GAAG,SAAS,KAChBtG,KAAKwd,eAAe,IAGrBnV,EAAKhC,OAAOC,GAAG,WAAW,KACzBmX,EAAY5F,GAAKxP,EAAK9E,IACtBka,EAAY1F,GAAK1P,EAAK7E,IACtBxD,KAAKwd,eAAe,IAGrBnV,EAAKhC,OAAOC,GAAG,YAAY,KACD,MAArBL,EAASJ,IAAI,OAChB4X,EAAY5F,QAAKhP,GAEO,MAArB5C,EAASJ,IAAI,OAChB4X,EAAY1F,QAAKlP,E,GAGpB,CAEU,aAAAuE,CAAc8B,GACvBpP,MAAMsN,cAAc8B,GAEpBlP,KAAK4d,aAAc,EAEnB,MAAMR,EAAoBpd,KAAKod,kBAC/BA,EAAkBxF,MAAM,YAAa5X,KAAK6d,gBAC1CT,EAAkB/T,MAAMrJ,KAAKub,QAE7Bvb,KAAK8d,UAAY,GAAkB9d,KAAK+d,QACxCX,EAAkBxF,MAAM,OAAQ5X,KAAK8d,UACtC,CAEU,cAAAxQ,GACTxN,MAAMwN,iBACNtN,KAAKud,kBAAkB,GACxB,CAEO,eAAAS,GACNle,MAAMke,kBAEN,MAAMZ,EAAoBpd,KAAKod,kBAE/B,GAAIpd,KAAKqN,WAAY,CACpB,IAAI4Q,EAAI5M,KAAKI,IAAI,GAAIzR,KAAK0d,cACtBQ,EAAI7M,KAAKI,IAAI,GAAIzR,KAAK2d,eACtBQ,EAAKne,KAAK6F,IAAI,aAAc,GAC5BuY,EAAKpe,KAAK6F,IAAI,cAAe,GAE7BzB,EAAiBpE,KAAK6F,IAAI,iBAAkB,GAEhDuX,EAAkBxF,MAAM,IC7SZ,SAASrU,GACtB,IACI8F,EACAuS,EACAQ,EAHA7Z,EAAW+Y,EAAS,IAOxB,SAAS1D,EAAMf,GACb,IAAK,IAA6BxO,EAAzBE,EAAI,EAAGW,EAAIG,EAAMb,OAAcD,EAAIW,IAAKX,GAC/CF,EAAOgB,EAAMd,IAASuP,KAAOsE,EAAG7T,GAAKF,EAAK9E,GAAKqY,EAAUrT,GAAKsO,CAElE,CAEA,SAAS0B,IACP,GAAKlP,EAAL,CACA,IAAId,EAAGW,EAAIG,EAAMb,OAGjB,IAFAoT,EAAY,IAAIrS,MAAML,GACtBkT,EAAK,IAAI7S,MAAML,GACVX,EAAI,EAAGA,EAAIW,IAAKX,EACnBqT,EAAUrT,GAAK2P,MAAMkE,EAAG7T,IAAMhF,EAAE8F,EAAMd,GAAIA,EAAGc,IAAU,GAAK9G,EAAS8G,EAAMd,GAAIA,EAAGc,EALlE,CAOpB,CAeA,MA/BiB,mBAAN9F,IAAkBA,EAAI+X,EAAc,MAAL/X,EAAY,GAAKA,IAkB3DqU,EAAMW,WAAa,SAASE,GAC1BpP,EAAQoP,EACRF,GACF,EAEAX,EAAMrV,SAAW,SAASkW,GACxB,OAAOC,UAAUlQ,QAAUjG,EAAwB,mBAANkW,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASrV,CAC3G,EAEAqV,EAAMrU,EAAI,SAASkV,GACjB,OAAOC,UAAUlQ,QAAUjF,EAAiB,mBAANkV,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASrU,CACpG,EAEOqU,CACT,CDuQgC,GAAiBrU,EAAE0a,EAAI,EAAIG,GAAI7b,SAA0B,IAAjB6B,EAAuB6Z,IAC5Fb,EAAkBxF,MAAM,IE9SZ,SAASpU,GACtB,IACI6F,EACAuS,EACAS,EAHA9Z,EAAW+Y,EAAS,IAOxB,SAAS1D,EAAMf,GACb,IAAK,IAA6BxO,EAAzBE,EAAI,EAAGW,EAAIG,EAAMb,OAAcD,EAAIW,IAAKX,GAC/CF,EAAOgB,EAAMd,IAASyP,KAAOqE,EAAG9T,GAAKF,EAAK7E,GAAKoY,EAAUrT,GAAKsO,CAElE,CAEA,SAAS0B,IACP,GAAKlP,EAAL,CACA,IAAId,EAAGW,EAAIG,EAAMb,OAGjB,IAFAoT,EAAY,IAAIrS,MAAML,GACtBmT,EAAK,IAAI9S,MAAML,GACVX,EAAI,EAAGA,EAAIW,IAAKX,EACnBqT,EAAUrT,GAAK2P,MAAMmE,EAAG9T,IAAM/E,EAAE6F,EAAMd,GAAIA,EAAGc,IAAU,GAAK9G,EAAS8G,EAAMd,GAAIA,EAAGc,EALlE,CAOpB,CAeA,MA/BiB,mBAAN7F,IAAkBA,EAAI8X,EAAc,MAAL9X,EAAY,GAAKA,IAkB3DoU,EAAMW,WAAa,SAASE,GAC1BpP,EAAQoP,EACRF,GACF,EAEAX,EAAMrV,SAAW,SAASkW,GACxB,OAAOC,UAAUlQ,QAAUjG,EAAwB,mBAANkW,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASrV,CAC3G,EAEAqV,EAAMpU,EAAI,SAASiV,GACjB,OAAOC,UAAUlQ,QAAUhF,EAAiB,mBAANiV,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASpU,CACpG,EAEOoU,CACT,CFwQgC,GAAiBpU,EAAE0a,EAAI,EAAIC,GAAI5b,SAA0B,IAAjB6B,EAAuB8Z,G,CAGzFle,KAAK4d,aACR5d,KAAKwd,eAEP,CAEO,aAAAA,GACoBxd,KAAKod,kBACbxF,MAAM,WGrTX,WACb,IAAIvO,EACAhB,EACAgP,EACAR,EAEA+E,EADArZ,EAAW+Y,GAAU,IAErB+C,EAAe,EACfC,EAAe7Z,IACf8Z,EAAS,IAEb,SAAS3G,EAAMa,GACb,IAAIlQ,EAAGW,EAAIG,EAAMb,OAAQ0Q,EAAOiB,EAAS9Q,EAAO9F,EAAGC,GAAGsX,WAAW0D,GACjE,IAAK3H,EAAQ4B,EAAGlQ,EAAI,EAAGA,EAAIW,IAAKX,EAAGF,EAAOgB,EAAMd,GAAI2Q,EAAK8B,MAAMC,EACjE,CAEA,SAAS1C,IACP,GAAKlP,EAAL,CACA,IAAId,EAAqBF,EAAlBa,EAAIG,EAAMb,OAEjB,IADAoT,EAAY,IAAIrS,MAAML,GACjBX,EAAI,EAAGA,EAAIW,IAAKX,EAAGF,EAAOgB,EAAMd,GAAIqT,EAAUvT,EAAKgC,QAAU9H,EAAS8F,EAAME,EAAGc,EAHlE,CAIpB,CAEA,SAASmV,EAAWtD,GAClB,IAAkByB,EAAGvR,EAAe7H,EAAGC,EAAG+E,EAAtChG,EAAW,EAASkc,EAAS,EAGjC,GAAIvD,EAAK1S,OAAQ,CACf,IAAKjF,EAAIC,EAAI+E,EAAI,EAAGA,EAAI,IAAKA,GACtBoU,EAAIzB,EAAK3S,MAAQ6C,EAAIiG,KAAKqN,IAAI/B,EAAElU,UACnClG,GAAYoa,EAAElU,MAAOgW,GAAUrT,EAAG7H,GAAK6H,EAAIuR,EAAEpZ,EAAGC,GAAK4H,EAAIuR,EAAEnZ,GAG/D0X,EAAK3X,EAAIA,EAAIkb,EACbvD,EAAK1X,EAAIA,EAAIib,CACf,KAGK,EACH9B,EAAIzB,GACF3X,EAAIoZ,EAAEhU,KAAKpF,EACboZ,EAAEnZ,EAAImZ,EAAEhU,KAAKnF,EACb,GAAGjB,GAAYqZ,EAAUe,EAAEhU,KAAK0B,aACzBsS,EAAIA,EAAEpS,KACf,CAEA2Q,EAAKzS,MAAQlG,CACf,CAEA,SAAS0Y,EAAMC,EAAM5H,EAAImF,EAAGgE,GAC1B,IAAKvB,EAAKzS,MAAO,OAAO,EAExB,IAAIlF,EAAI2X,EAAK3X,EAAI8E,EAAK9E,EAClBC,EAAI0X,EAAK1X,EAAI6E,EAAK7E,EAClBya,EAAIxB,EAAKnJ,EACT8H,EAAI7X,EAAIA,EAAIC,EAAIA,EAIpB,GAAIya,EAAIA,EAAIM,EAASnD,EAQnB,OAPIA,EAAIkD,IACI,IAAN/a,IAA6B6X,IAApB7X,EAAI8X,EAAOhE,IAAkB9T,GAChC,IAANC,IAA6B4X,IAApB5X,EAAI6X,EAAOhE,IAAkB7T,GACtC4X,EAAIiD,IAAcjD,EAAI/J,KAAKsF,KAAK0H,EAAejD,IACnD/S,EAAKyP,IAAMvU,EAAI2X,EAAKzS,MAAQoO,EAAQuE,EACpC/S,EAAK2P,IAAMxU,EAAI0X,EAAKzS,MAAQoO,EAAQuE,IAE/B,EAIJ,KAAIF,EAAK1S,QAAU4S,GAAKkD,GAAxB,EAGDpD,EAAKvS,OAASN,GAAQ6S,EAAK3Q,QACnB,IAANhH,IAA6B6X,IAApB7X,EAAI8X,EAAOhE,IAAkB9T,GAChC,IAANC,IAA6B4X,IAApB5X,EAAI6X,EAAOhE,IAAkB7T,GACtC4X,EAAIiD,IAAcjD,EAAI/J,KAAKsF,KAAK0H,EAAejD,KAGrD,GAAOF,EAAKvS,OAASN,IACnB4V,EAAIrC,EAAUV,EAAKvS,KAAK0B,OAASwM,EAAQuE,EACzC/S,EAAKyP,IAAMvU,EAAI0a,EACf5V,EAAK2P,IAAMxU,EAAIya,SACR/C,EAAOA,EAAK3Q,KAb4B,CAcnD,CAwBA,OAtBAqN,EAAMW,WAAa,SAASgD,EAAQC,GAClCnS,EAAQkS,EACRlE,EAASmE,EACTjD,GACF,EAEAX,EAAMrV,SAAW,SAASkW,GACxB,OAAOC,UAAUlQ,QAAUjG,EAAwB,mBAANkW,EAAmBA,EAAI6C,GAAU7C,GAAIF,IAAcX,GAASrV,CAC3G,EAEAqV,EAAM+G,YAAc,SAASlG,GAC3B,OAAOC,UAAUlQ,QAAU6V,EAAe5F,EAAIA,EAAGb,GAASvG,KAAKsF,KAAK0H,EACtE,EAEAzG,EAAMgH,YAAc,SAASnG,GAC3B,OAAOC,UAAUlQ,QAAU8V,EAAe7F,EAAIA,EAAGb,GAASvG,KAAKsF,KAAK2H,EACtE,EAEA1G,EAAMiH,MAAQ,SAASpG,GACrB,OAAOC,UAAUlQ,QAAU+V,EAAS9F,EAAIA,EAAGb,GAASvG,KAAKsF,KAAK4H,EAChE,EAEO3G,CACT,CHuMsC,GAAwBrV,UAAUuc,IACrE,IAAI7Y,EAAY6Y,EAAe7Y,SAC3BoC,EAAOpC,EAASJ,IAAI,QACpB+O,EAAS3O,EAASJ,IAAI,UACtBxB,EAAmBrE,KAAK6F,IAAI,oBAAqB,IAErD,OAAI+O,EACIA,EAAO/O,IAAI,SAAU,GAAKwC,EAAKxC,IAAI,QAAS,GAAKxB,EAElD,CAAC,KAGTrE,KAAK6d,eAAenb,QAAQoc,IAC3B,IAAI7Y,EAAY6Y,EAAe7Y,SAC3BoC,EAAOpC,EAASJ,IAAI,QACpB+O,EAAS3O,EAASJ,IAAI,UACtBiP,EAAc7O,EAASJ,IAAI,eAC/B,GAAI+O,GAAUE,EAAa,CAC1B,IAAIpS,EAASkS,EAAO/O,IAAI,SAAU,GAQlC,OANKiP,EAAY9D,aAChBtO,GAAkBoS,EAAYjP,IAAI,QAAS,MAG5CnD,GAAU2F,EAAKxC,IAAI,QAAS,GAErBnD,EAAS1C,KAAK6F,IAAI,cAAe,E,KAG1C7F,KAAKud,kBAAkB,GACxB,CAEU,iBAAA/H,CAAkBxH,GAE5B,CAEO,WAAA+Q,GACNjf,MAAMif,cACN/e,KAAK4d,aAAc,CACpB,CAKO,mBAAAN,GACN,MAAMQ,EAAY9d,KAAK8d,UACnBA,IACHA,EAAUtb,UAAUwc,GACZhf,KAAKif,YAAYD,KAEzBlB,EAAUvb,UAAUyc,GACZhf,KAAKkf,YAAYF,MAGtBhf,KAAKqd,OAASrd,KAAK6F,IAAI,iBAC1B7F,KAAKwM,eAAeqI,WAAW,WAAW,GAC1C7U,KAAKiT,eAAe4B,WAAW,WAAW,IAG3C,IAAIsK,EAAUnf,KAAKod,kBAAkB/T,QAErC,OAAY8V,GAAUC,IACrB,MACM/W,EADW+W,EAAOnZ,SACFJ,IAAI,QAE1BwC,EAAKhB,IAAI,IAAK+X,EAAO7b,GACrB8E,EAAKhB,IAAI,IAAK+X,EAAO5b,EAAE,GAGzB,CAKO,cAAA4R,CAAerI,GACrB,OAAYA,GAAY9G,IACvB,MAAMoP,EAAWpP,EAASJ,IAAI,YAC1BwP,GACH,OAAYA,GAAWnH,IACtB,MAAMoH,EAAmBtV,KAAKmO,iBAAiBnO,KAAK+M,UAAWmB,GAE3DoH,GACHtV,KAAKmV,cAAclP,EAAUqP,EAAkBtV,KAAK6F,IAAI,oB,IAK3D,MAAMyB,EAAWrB,EAASJ,IAAI,YAC1ByB,GACHtH,KAAKoV,eAAe9N,E,GAGvB,CAKU,WAAA2X,CAAYD,GACrB,IAAIK,EAA2FL,EAAUK,eACrGC,EAA2FN,EAAUM,eAErG9c,EAAW,EAEf,GAAI6c,GAAkBC,EAAgB,CAErC,MAAMnM,EAAamM,EAAezZ,IAAI,QACtC,GAAIsN,EAAWnC,WACd,OAAO,EAGR,IAAIwB,EAAOwM,EAAUxM,KACjBA,IACHhQ,EAAWgQ,EAAK3M,IAAI,WAAY,IAGjC,MAAMqN,EAAamM,EAAexZ,IAAI,QAMtC,OAJIsN,EAAWnC,aACdxO,EAAW,GAGJA,GAAY6c,EAAexZ,IAAI,UAAUA,IAAI,SAAU,GAAKqN,EAAWrN,IAAI,QAAS,GAAKyZ,EAAezZ,IAAI,UAAUA,IAAI,SAAU,GAAKsN,EAAWtN,IAAI,QAAS,G,CAE1K,OAAOrD,CACR,CAKU,WAAA0c,CAAYF,GACrB,IAAIzc,EAAW,EAEXiQ,EAAOwM,EAAUxM,KAQrB,OAPIA,IACHjQ,EAAWiQ,EAAK3M,IAAI,WAAY,IAIjCtD,GADuByc,EAAUM,eACNzZ,IAAI,QAAQA,IAAI,SAEpCtD,CACR,CAEU,WAAAoL,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAClBjG,KAAKuf,cAActZ,GAEnB,MAAM1C,EAAI0C,EAASJ,IAAI,KACjBrC,EAAIyC,EAASJ,IAAI,KAEjBuZ,EAASnZ,EAASJ,IAAI,eAG1BuZ,EAAevH,GADR,MAALtU,EACkB,kBAAuBA,EAAGvD,KAAK0d,mBAG/B7U,EAGb,MAALrF,EACF4b,EAAerH,GAAK,kBAAuBvU,EAAGxD,KAAK2d,eAGnDyB,EAAevH,QAAKhP,CAEvB,CAEU,aAAA0W,CAActZ,GACvB,IAAIiX,GAAQld,KAAK0d,aAAe1d,KAAK2d,eAAiB,EAElD1Z,EAAY,kBAAuBjE,KAAK6F,IAAI,YAAa,GAAIqX,GAC7DhZ,EAAY,kBAAuBlE,KAAK6F,IAAI,YAAa,GAAIqX,GAE7DsC,EAAevZ,EAASJ,IAAI,OAE5BnD,EAASwB,EAEb,MAAMoN,EAAMtR,KAAKmP,WAAW,WAAY,GAClCsC,EAAMzR,KAAKmP,WAAW,YAAa,GAErCsC,EAAM,IACT/O,EAASuB,GAAaub,EAAelO,IAAQG,EAAMH,IAAQpN,EAAYD,IAGnE,WAAevB,KACnBA,EAASuB,GAGV,MAAM+L,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAExBI,EAASJ,IAAI,UAAUwK,QAAQ,CAAEC,IAAK,SAAUC,GAAI7N,EAAQsN,SAAUA,EAAUI,OAAQA,GACzF,CAEU,YAAA4F,CAAaxD,EAAqB7G,EAA6CC,GACxF,MAAM6T,EAAS,CAAEjN,KAAMA,EAAM7G,OAAQA,EAAO9F,IAAI,eAAewE,MAAOuB,OAAQA,EAAO/F,IAAI,eAAewE,MAAOgV,eAAgB1T,EAAQ2T,eAAgB1T,GACvJ5L,KAAK+d,OAAOtX,KAAKgZ,GACjBjN,EAAKqC,WAAW,SAAU4K,GAE1Bzf,KAAK8d,UAAY,GAAkB9d,KAAK+d,QACxC/d,KAAKod,kBAAkBxF,MAAM,OAAQ5X,KAAK8d,WAC1C9d,KAAKud,kBAAkB,GACxB,CAEU,YAAArH,CAAa1D,GACtB1S,MAAMoW,aAAa1D,GACnB,SAAcxS,KAAK+d,OAAQvL,EAAKrD,WAAW,UAC5C,CAEU,aAAAgH,GACTnW,KAAKud,kBAAkB,GACxB,CAEU,YAAA/O,GAET1O,MAAM0O,eAENxO,KAAKub,OAAS,GACdvb,KAAK+d,OAAS,EACf,EAhWA,sC,gDAAkC,kBAClC,uC,gDAA0C3J,EAAgBzM,WAAWC,OAAO,CAACuV,GAActV,c,eI3GrF,MAAM6X,WAAaxT,EAA1B,c,oBAMC,mC,gDAAyB,SAKzB,0C,gDAAqB,SAErB,wC,yDAOA,sC,gDAAgD,IAAI5G,EAAA,EACnDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMuT,EAAAC,EAAO7O,KAAKzF,KAAKC,MAAO,CAC7ByF,UAAW,YAAiB1F,KAAKuU,QAAQ3O,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,WAClF,CAACpM,KAAKuU,QAAQ3O,cA6HnB,CA1HW,SAAAc,GACT5G,MAAM4G,YACN1G,KAAK6U,WAAW,SAAU,EAC3B,CAEO,gBAAAlI,GACN7M,MAAM6M,mBAEF3M,KAAK2f,eAAe,WACnB3f,KAAKkN,WACRlN,KAAK4f,kBAAkB5f,KAAKkN,UAG/B,CAEU,cAAAI,GACT,GAAItN,KAAKkN,UAAW,CACnB,MAAM2S,EAAa7f,KAAK8f,YACxBD,EAAW3C,KAAK,CAACld,KAAK0d,aAAc1d,KAAK2d,gBACzCkC,EAAW7f,KAAKkN,WAChB2S,EAAWE,QAAQ/f,KAAK6F,IAAI,cAAe,IAC3C7F,KAAKyN,aAAazN,KAAKkN,U,CAEzB,CAEU,WAAAS,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAElB,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB+O,EAAS3O,EAASJ,IAAI,UACtB6H,EAAgBzH,EAASJ,IAAI,mBAE7Bma,EAAShgB,KAAKmP,WAAW,SAAU,GAEnC5L,EAAImK,EAAcnK,EAAIyc,EACtBxc,EAAIkK,EAAclK,EAAIwc,EACtBtd,EAASgL,EAActN,EAAI4f,EAE3BhQ,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAKxB,GAHAwC,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAIhN,EAAGyM,SAAUA,EAAUI,OAAQA,IAC5D/H,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAI/M,EAAGwM,SAAUA,EAAUI,OAAQA,IAExDwE,EAAQ,CACX,MAAMc,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAEjC+O,EAAOvE,QAAQ,CAAEC,IAAK,SAAUC,GAAI7N,EAAQsN,SAAUA,EAAUI,OAAQA,IACxEwE,EAAOgB,YAAY,OAAQF,GAC3Bd,EAAOgB,YAAY,cAAeD,GAClCf,EAAOgB,YAAY,SAAUF,E,CAE/B,CAEU,iBAAAkK,CAAkBlS,GAC3B,MAAMzH,EAAWyH,EAAc/E,KAAK1C,SACpC,GAAIA,EAAU,CACb,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB+O,EAAS3O,EAASJ,IAAI,UAEtBma,EAAShgB,KAAKmP,WAAW,SAAU,GAEnC5L,EAAImK,EAAcnK,EAAIyc,EACtBxc,EAAIkK,EAAclK,EAAIwc,EACtBtd,EAASgL,EAActN,EAAI4f,EAEjC3X,EAAK9H,OAAO,CAAEgD,EAAGA,EAAGC,EAAGA,IACvBoR,EAAOvN,IAAI,SAAU3E,GAErB,MAAMoL,EAAoBJ,EAAcpG,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAK4f,kBAAkB7R,EAAe,G,CAI1C,CAKO,QAAA1B,CAASpG,GACf,MAAMoC,EAAOvI,MAAMuM,SAASpG,GAEtB2O,EAASvM,EAAKf,SAASI,UAAU1H,KAAKuU,QAAQpO,OAAQ,GAC5DkC,EAAKwM,WAAW,gBAAiBD,GACjC5U,KAAKuU,QAAQ9N,KAAKmO,GAClB3O,EAASsG,OAAO,SAAUqI,GAE1B,MAAM1O,EAAQD,EAASJ,IAAI,SAO3B,OALA+O,EAAOtO,GAAG,UAAU,KACnB,MAAMsD,EAAyC,EAArCgL,EAAO/O,IAAI,SAAU7F,KAAKU,SACpCwF,EAAM3F,OAAO,CAAEwU,SAAUnL,EAAGoL,UAAWpL,GAAI,IAGrCvB,CACR,CAEO,KAAAkF,CAAMtH,GACZ,MAAMyH,EAAgBzH,EAASJ,IAAI,mBAEnC,IAAItC,EAAImK,EAAcnK,EAClBC,EAAIkK,EAAclK,EAClBpD,EAAIsN,EAActN,EAElB4f,EAAS3O,KAAKC,IAAItR,KAAK0d,aAAc1d,KAAK2d,gBAAsB,EAAJvd,GAEhE,MAAMgQ,EAASpQ,KAAK6F,IAAI,mBACxB,IAAImK,EAAWhQ,KAAK6F,IAAI,oBAAqB,GAExC7F,KAAKwR,SACTxB,EAAW,GAGZhQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAIyP,EAAQhQ,SAAUA,EAAUI,OAAQA,IAE7E,MAAM5D,EAAiBxM,KAAKwM,eAC5BA,EAAe6D,QAAQ,CAAEC,IAAK,IAAK9G,KAAMgD,EAAejJ,IAAKgN,GAAIvQ,KAAKU,QAAU,EAAI6C,EAAIyc,EAAQhQ,SAAUA,EAAUI,OAAQA,IAC5H5D,EAAe6D,QAAQ,CAAEC,IAAK,IAAK9G,KAAMgD,EAAehJ,IAAK+M,GAAIvQ,KAAKW,SAAW,EAAI6C,EAAIwc,EAAQhQ,SAAUA,EAAUI,OAAQA,GAC9H,EC/Nc,YAAS/H,GACtBA,EAAK+K,GAAK/B,KAAK6O,MAAM7X,EAAK+K,IAC1B/K,EAAKgL,GAAKhC,KAAK6O,MAAM7X,EAAKgL,IAC1BhL,EAAKiL,GAAKjC,KAAK6O,MAAM7X,EAAKiL,IAC1BjL,EAAKkL,GAAKlC,KAAK6O,MAAM7X,EAAKkL,GAC5B,CCLe,YAAS/L,EAAQ4L,EAAIC,EAAIC,EAAIC,GAO1C,IANA,IACIlL,EADAgB,EAAQ7B,EAAOF,SAEfiB,GAAK,EACLW,EAAIG,EAAMb,OACV8C,EAAI9D,EAAOiB,QAAU6K,EAAKF,GAAM5L,EAAOiB,QAElCF,EAAIW,IACXb,EAAOgB,EAAMd,IAAS8K,GAAKA,EAAIhL,EAAKkL,GAAKA,EACzClL,EAAK+K,GAAKA,EAAI/K,EAAKiL,GAAKF,GAAM/K,EAAKI,MAAQ6C,CAE/C,CCRe,cACb,IAAIwN,EAAK,EACLC,EAAK,EACLgH,EAAU,EACVG,GAAQ,EAEZ,SAASC,EAAUhX,GACjB,IAAID,EAAIC,EAAKxI,OAAS,EAOtB,OANAwI,EAAKiK,GACLjK,EAAKkK,GAAK0M,EACV5W,EAAKmK,GAAKwF,EACV3P,EAAKoK,GAAKwF,EAAK7P,EACfC,EAAKO,WAKP,SAAsBqP,EAAI7P,GACxB,OAAO,SAASb,GACVA,EAAKf,UACP,GAAYe,EAAMA,EAAK+K,GAAI2F,GAAM1Q,EAAKoB,MAAQ,GAAKP,EAAGb,EAAKiL,GAAIyF,GAAM1Q,EAAKoB,MAAQ,GAAKP,GAEzF,IAAIkK,EAAK/K,EAAK+K,GACVC,EAAKhL,EAAKgL,GACVC,EAAKjL,EAAKiL,GAAKyM,EACfxM,EAAKlL,EAAKkL,GAAKwM,EACfzM,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GAC/BC,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnClL,EAAK+K,GAAKA,EACV/K,EAAKgL,GAAKA,EACVhL,EAAKiL,GAAKA,EACVjL,EAAKkL,GAAKA,CACZ,CACF,CArBkB6M,CAAarH,EAAI7P,IAC7BgX,GAAO/W,EAAKO,WAAW,IACpBP,CACT,CAgCA,OAZAgX,EAAUD,MAAQ,SAAS3c,GACzB,OAAOmV,UAAUlQ,QAAU0X,IAAU3c,EAAG4c,GAAaD,CACvD,EAEAC,EAAUjD,KAAO,SAAS3Z,GACxB,OAAOmV,UAAUlQ,QAAUsQ,GAAMvV,EAAE,GAAIwV,GAAMxV,EAAE,GAAI4c,GAAa,CAACrH,EAAIC,EACvE,EAEAoH,EAAUJ,QAAU,SAASxc,GAC3B,OAAOmV,UAAUlQ,QAAUuX,GAAWxc,EAAG4c,GAAaJ,CACxD,EAEOI,CACT,CHgCC,sC,gDAAkC,SAClC,uC,gDAA0CjU,EAAUvE,WAAWC,OAAO,CAAC8X,GAAK7X,cIGtE,MAAMwY,WAAkBnU,EAA/B,c,oBAMC,mC,gDAAyB,cAUzB,yC,gDAA6D,IAAI5G,EAAA,EAChEC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMiF,EAAA,EAAiBN,KAAKzF,KAAKC,MAAO,CACvCyF,UAAW,YAAiB1F,KAAKsgB,WAAW1a,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,WACrF,CAACpM,KAAKsgB,WAAW1a,eAGrB,+C,gDAA0B,MAuP3B,CAnPW,SAAAc,GACT1G,KAAK4G,UAAUlB,UAAY,YAAiB1F,KAAK4G,UAAUlB,UAAW,CAAC,YAAa1F,KAAK4G,UAAUjC,aAAe,aAClH7E,MAAM4G,YACN1G,KAAK6U,WAAW,SAAU,GAC1B7U,KAAK6U,WAAW,SAAU,EAC3B,CAEO,gBAAAlI,GACN7M,MAAM6M,mBAEF3M,KAAK8G,QAAQ,gBACZ9G,KAAKkN,WACRlN,KAAKyN,aAAazN,KAAKkN,YAIrBlN,KAAK2f,eAAe,WAAa3f,KAAK2f,eAAe,YACpD3f,KAAKkN,WACRlN,KAAK4f,kBAAkB5f,KAAKkN,WAI1BlN,KAAK8G,QAAQ,gBAChB9G,KAAKsN,gBAEP,CAEU,cAAAA,GACT,GAAItN,KAAKkN,UAAW,CACnB,MAAMqT,EAAkBvgB,KAAKwgB,iBAC7B,IAAIvC,EAAIje,KAAK0d,aACTQ,EAAIle,KAAK2d,cAEkB,cAA3B3d,KAAK6F,IAAI,kBACXoY,EAAGC,GAAK,CAACA,EAAGD,IAGdsC,EAAgBrD,KAAK,CAACe,EAAGC,IAEzB,MAAMtZ,EAAc5E,KAAK6F,IAAI,eAEzB,WAAejB,IAClB2b,EAAgBR,QAAQnb,GAIzB2b,EAAgBvgB,KAAKkN,WACrBlN,KAAKyN,aAAazN,KAAKkN,U,CAEzB,CAEU,WAAAS,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAElB,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB4a,EAAYxa,EAASJ,IAAI,aACzB6H,EAAgBzH,EAASJ,IAAI,mBAE7B6a,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GAEzC,IAAIiE,EAAIE,EAAID,EAAIE,EAEe,cAA3BvT,KAAK6F,IAAI,gBACZuN,EAAK1F,EAAc2F,GAAKqN,EACxBpN,EAAK5F,EAAc6F,GAAKmN,EACxBrN,EAAK3F,EAAc0F,GAAKuN,EACxBpN,EAAK7F,EAAc4F,GAAKqN,IAGxBvN,EAAK1F,EAAc0F,GAAKsN,EACxBpN,EAAK5F,EAAc4F,GAAKoN,EACxBrN,EAAK3F,EAAc2F,GAAKsN,EACxBpN,EAAK7F,EAAc6F,GAAKoN,GAEzB,IAAI1C,EAAI3K,EAAKF,EACT8K,EAAI3K,EAAKF,EAEb,MAAMrD,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAOxB,GALAwC,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAI6C,EAAIpD,SAAUA,EAAUI,OAAQA,IAC7D/H,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAI8C,EAAIrD,SAAUA,EAAUI,OAAQA,IAC7D/H,EAAKgI,QAAQ,CAAEC,IAAK,QAASC,GAAI0N,EAAGjO,SAAUA,EAAUI,OAAQA,IAChE/H,EAAKgI,QAAQ,CAAEC,IAAK,SAAUC,GAAI2N,EAAGlO,SAAUA,EAAUI,OAAQA,IAE7DqQ,EAAW,CACd,MAAM/K,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAEjC4a,EAAUpQ,QAAQ,CAAEC,IAAK,QAASC,GAAI0N,EAAGjO,SAAUA,EAAUI,OAAQA,IACrEqQ,EAAUpQ,QAAQ,CAAEC,IAAK,SAAUC,GAAI2N,EAAGlO,SAAUA,EAAUI,OAAQA,IACtEqQ,EAAU7K,YAAY,OAAQF,GAC9B+K,EAAU7K,YAAY,cAAeD,GACrC8K,EAAU7K,YAAY,SAAUF,E,CAElC,CAGU,iBAAAkK,CAAkBlS,GAC3B,MAAMzH,EAAWyH,EAAc/E,KAAK1C,SACpC,GAAIA,EAAU,CACb,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB4a,EAAYxa,EAASJ,IAAI,aAEzB6a,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GAEzC,IAAIiE,EAAIE,EAAID,EAAIE,EAEe,cAA3BvT,KAAK6F,IAAI,gBACZuN,EAAK1F,EAAc2F,GAAKqN,EACxBpN,EAAK5F,EAAc6F,GAAKmN,EACxBrN,EAAK3F,EAAc0F,GAAKuN,EACxBpN,EAAK7F,EAAc4F,GAAKqN,IAGxBvN,EAAK1F,EAAc0F,GAAKsN,EACxBpN,EAAK5F,EAAc4F,GAAKoN,EACxBrN,EAAK3F,EAAc2F,GAAKsN,EACxBpN,EAAK7F,EAAc6F,GAAKoN,GAGzB,MAAM1C,EAAI3K,EAAKF,EACT8K,EAAI3K,EAAKF,EAEfhL,EAAK9H,OAAO,CAAEgD,EAAG6P,EAAI5P,EAAG6P,EAAI3S,MAAOud,EAAGtd,OAAQud,IAC9CuC,EAAUlgB,OAAO,CAAEG,MAAOud,EAAGtd,OAAQud,IAErC,MAAMpQ,EAAoBJ,EAAcpG,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAK4f,kBAAkB7R,EAAe,G,CAI1C,CAKO,QAAA1B,CAASpG,GACf,MAAMoC,EAAOvI,MAAMuM,SAASpG,GAE5B,OADAjG,KAAK4gB,UAAU3a,EAAUoC,GAClBA,CACR,CAEU,SAAAuY,CAAU3a,EAA+CoC,GAClE,MAAMoY,EAAYpY,EAAKf,SAASI,UAAU1H,KAAKsgB,WAAWna,OAAQ,GAClEkC,EAAKwM,WAAW,gBAAiB4L,GACjCxa,EAASsG,OAAO,YAAakU,GAC7BA,EAAUra,aAAaH,GAEvB,MAAMC,EAAQD,EAASJ,IAAI,SAE3B4a,EAAUna,GAAG,SAAS,KACrBJ,EAAMmB,IAAI,WAAYoZ,EAAU/f,QAAQ,IAGzC+f,EAAUna,GAAG,UAAU,KACtBJ,EAAMmB,IAAI,YAAaoZ,EAAU9f,SAAS,GAE5C,CAEU,KAAA4M,CAAMtH,GACf,IAAImN,EAAK,EACLE,EAAK,EACLD,EAAK,EACLE,EAAK,EAET,MAAMlQ,EAAUrD,KAAK6F,IAAI,UAAW,GAAK,EACnCnB,EAAW1E,KAAK6F,IAAI,WAAY,GAEhCnF,EAAQV,KAAK0d,aACb/c,EAASX,KAAK2d,cAEd5M,EAAW/Q,KAAKmP,WAAW,WAAY,GACvC0R,EAAclgB,GAAUoQ,EAAW,GACnC+P,EAAapgB,GAASqQ,EAAW,GACjC9P,EAAeoQ,KAAKC,IAAItR,KAAK6F,IAAI,eAAgB,GAAIkL,GAE3D,IAAI/P,EAAYhB,KAAK0R,kBAKrB,GAJiB,MAAb1Q,IACHA,EAAYhB,KAAK6F,IAAI,YAAa,IAG/BI,EAAU,CACb,MAAMyH,EAAgBzH,EAASJ,IAAI,mBACnC,IAAI0L,EAAe7D,EAAcjE,MAEF,cAA3BzJ,KAAK6F,IAAI,gBACZuN,EAAK1F,EAAc2F,GACnBC,EAAK5F,EAAc6F,GAEnBF,EAAK3F,EAAc0F,GACnBG,EAAK7F,EAAc4F,GAEnBF,EAAKE,EAAKwN,EAAazd,EACvBiQ,EAAKF,EAAK0N,GAAc9f,EAAY,GAEhCuQ,EAAe7M,IAClB2O,EAAK,EACLE,EAAK5S,EACLyS,EAAK0N,EAAapc,EAClB4O,EAAKF,EAAK0N,EAAa7f,KAIxBmS,EAAK1F,EAAc0F,GACnBE,EAAK5F,EAAc4F,GAEnBD,EAAK3F,EAAc2F,GACnBE,EAAK7F,EAAc6F,GAEnBF,EAAKE,EAAKsN,EAAcxd,EACxBkQ,EAAKF,EAAKwN,GAAe7f,EAAY,GAEjCuQ,EAAe7M,IAClB0O,EAAK,EACLE,EAAK5S,EACL2S,EAAKwN,EAAcnc,EACnB6O,EAAKF,EAAKwN,EAAc5f,G,CAK3B,IAAIyf,EAAShgB,GAAS4S,EAAKF,GACvBuN,EAAShgB,GAAU4S,EAAKF,GAE5B,MAAMjD,EAASpQ,KAAK6F,IAAI,mBACxB,IAAImK,EAAWhQ,KAAK6F,IAAI,oBAAqB,GAExC7F,KAAKwR,SACTxB,EAAW,GAGZhQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAImQ,EAAQ1Q,SAAUA,EAAUI,OAAQA,IAC7EpQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAIoQ,EAAQ3Q,SAAUA,EAAUI,OAAQA,IAE7EpQ,KAAKqQ,QAAQ,CAACC,IAAI,KAAM9G,KAAK,EAAG+G,GAAG,EAAGP,SAAUA,EAAUI,OAAQA,IAClEpQ,KAAKwM,eAAe6D,QAAQ,CAAEC,IAAK,IAAKC,IAAK6C,EAAKsN,EAAQ1Q,SAAUA,EAAUI,OAAQA,IACtFpQ,KAAKwM,eAAe6D,QAAQ,CAAEC,IAAK,IAAKC,IAAK8C,EAAKsN,EAAQ3Q,SAAUA,EAAUI,OAAQA,GACvF,EArQA,sC,gDAAkC,cAClC,uC,gDAA0ClE,EAAUvE,WAAWC,OAAO,CAACyY,GAAUxY,c,mCCqB3E,MAAMkZ,WAAiBV,GAA9B,c,oBAMC,mC,gDAAyB,aAKzB,+C,gDAA0B,OAS1B,qC,gDAA8C,IAAI/a,EAAA,EACjDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMkgB,GAAA,EAAMvb,KAAKzF,KAAKC,MAAO,CAC5ByF,UAAW,YAAiB1F,KAAKihB,OAAOrb,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,YAAa,OAAQ,WACtG,CAACpM,KAAKihB,OAAOrb,eAQjB,qC,gDAAoD,IAAIN,EAAA,EACvDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMogB,GAAA,EAAYzb,KAAKzF,KAAKC,MAAO,CAClCyF,UAAW,YAAiB1F,KAAK2F,OAAOC,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,QAC3E,CAACpM,KAAK2F,OAAOC,cAyVlB,CAtVW,SAAAc,GACT5G,MAAM4G,YACN1G,KAAKwM,eAAejM,OAAO,CAAEgD,EAAG,KAAKC,EAAG,OACxCxD,KAAKmN,cAAc,KAAM,GACzBnN,KAAKmN,cAAc,KAAM,EAC1B,CAEO,gBAAAR,GACN7M,MAAM6M,oBAEF3M,KAAK2f,eAAe,OAAS3f,KAAK2f,eAAe,QAChD3f,KAAKkN,WACRlN,KAAK4f,kBAAkB5f,KAAKkN,UAG/B,CAEU,cAAAI,GACT,GAAItN,KAAKkN,UAAW,CACnB,MAAMqT,EAAkBvgB,KAAKwgB,iBAE7B,IAAIW,EAAS,gBAAmB,EAAG,EAAGnhB,KAAK6F,IAAI,aAAc,GAAI7F,KAAK6F,IAAI,WAAY,KAAM,GAExFoY,EAAIje,KAAK0d,aACTQ,EAAIle,KAAK2d,cAEb,MAAMyD,EAAKnD,GAAKkD,EAAO5H,MAAQ4H,EAAOE,MAChCC,EAAKpD,GAAKiD,EAAO3H,OAAS2H,EAAOI,KAEvC,IAAIjK,EAAIjG,KAAKC,IAAI8P,EAAIE,GAEjBlhB,EAAI,kBAAuBJ,KAAK6F,IAAI,SAAU,MAAOyR,GACrDkK,EAAK,kBAAuBxhB,KAAK6F,IAAI,cAAe,GAAIzF,GAExDohB,EAAK,IACRA,EAAKphB,EAAIohB,GAGVlK,EAAIlX,EAAIohB,EAERxhB,KAAKmN,cAAc,cAAeqU,GAClCxhB,KAAKmN,cAAc,gBAAiBmK,GAEpCiJ,EAAgBrD,KAAK,CAAC5F,EAAGA,IAEzBtX,KAAKwM,eAAejM,OAAO,CAC1BwY,IAAK3Y,GAAK+gB,EAAO3H,OAAS2H,EAAOI,KAAO,EAAGzI,IAAK1Y,GAAK+gB,EAAO5H,MAAQ4H,EAAOE,MAAQ,IAGpF,MAAMzc,EAAc5E,KAAK6F,IAAI,eAEzB,WAAejB,IAClB2b,EAAgBR,QAAQnb,GAIzB2b,EAAgBvgB,KAAKkN,WACrBlN,KAAKyN,aAAazN,KAAKkN,U,CAEzB,CAEU,WAAAS,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAElB,MAAMyH,EAAgBzH,EAASJ,IAAI,mBACtBI,EAASJ,IAAI,QAErBtF,OAAO,CAAEgD,EAAG,EAAGC,EAAG,IACvB,MAAMwM,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAElB6a,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GACnCsS,EAAKzhB,KAAKmP,WAAW,KAAM,GAC3B2J,EAAK9Y,KAAKmP,WAAW,KAAM,GAE3BiE,EAAK1F,EAAc0F,GAAKsN,EAAS5H,EACjCxF,EAAK5F,EAAc4F,GAAKoN,EAAS5H,EACjCzF,EAAK3F,EAAc2F,GAAKsN,EACxBpN,EAAK7F,EAAc6F,GAAKoN,EAExBa,EAAKxhB,KAAKmP,WAAW,eACrBuS,EAAK1hB,KAAKmP,WAAW,gBAAiB,GAEtCwS,EAAQ1b,EAASJ,IAAI,SAC3B,GAAI8b,EAAO,CACV,MAAMC,EAAa5hB,KAAK6F,IAAI,cAAe,IACrCgc,EAAW7hB,KAAK6F,IAAI,WAAY,KAEhCic,EAAkBF,EAAaxO,EAAKsO,GAAMG,EAAWD,GACrDG,EAAMH,EAAatO,EAAKoO,GAAMG,EAAWD,GAAcE,EAE7D,IAAIE,EAAmBR,EAAKnO,EACxB4O,EAAcT,EAAKjO,EAEvByO,GAAoBP,EACpBQ,GAAeR,EAEfQ,EAAc5Q,KAAKI,IAAI,EAAGwQ,GAC1BD,EAAmB3Q,KAAKI,IAAI,EAAGuQ,GAE/BL,EAAMtR,QAAQ,CAAEC,IAAK,SAAUC,GAAI0R,EAAajS,SAAUA,EAAUI,OAAQA,IAC5EuR,EAAMtR,QAAQ,CAAEC,IAAK,cAAeC,GAAIyR,EAAkBhS,SAAUA,EAAUI,OAAQA,IACtFuR,EAAMtR,QAAQ,CAAEC,IAAK,aAAcC,GAAIuR,EAAiB9R,SAAUA,EAAUI,OAAQA,IACpFuR,EAAMtR,QAAQ,CAAEC,IAAK,MAAOC,GAAIwR,EAAK/R,SAAUA,EAAUI,OAAQA,IAEjE,MAAMsF,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAEjC8b,EAAM/L,YAAY,OAAQF,GAC1BiM,EAAM/L,YAAY,cAAeD,GACjCgM,EAAM/L,YAAY,SAAUF,E,CAE9B,CAGU,iBAAAkK,CAAkBlS,GAC3B,MAAMzH,EAAWyH,EAAc/E,KAAK1C,SACpC,GAAIA,EAAU,CACb,MAAMya,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GACnCsS,EAAKzhB,KAAKmP,WAAW,KAAM,GAC3B2J,EAAK9Y,KAAKmP,WAAW,KAAM,GAE3BiE,EAAK1F,EAAc0F,GAAKsN,EAAS5H,EACjCxF,EAAK5F,EAAc4F,GAAKoN,EAAS5H,EACjCzF,EAAK3F,EAAc2F,GAAKsN,EACxBpN,EAAK7F,EAAc6F,GAAKoN,EAExBa,EAAKxhB,KAAKmP,WAAW,eACrBuS,EAAK1hB,KAAKmP,WAAW,gBAAiB,GAEtCwS,EAAQ1b,EAASJ,IAAI,SAC3B,GAAI8b,EAAO,CACV,MAAMC,EAAa5hB,KAAK6F,IAAI,cAAe,IACrCgc,EAAW7hB,KAAK6F,IAAI,WAAY,KAEhCic,EAAkBF,EAAaxO,EAAKsO,GAAMG,EAAWD,GACrDG,EAAMH,EAAatO,EAAKoO,GAAMG,EAAWD,GAAcE,EAE7D,IAAIE,EAAmBR,EAAKnO,EACxB4O,EAAcT,EAAKjO,EAEvByO,GAAoBP,EACpBQ,GAAeR,EAEfQ,EAAc5Q,KAAKI,IAAI,EAAGwQ,GAC1BD,EAAmB3Q,KAAKI,IAAI,EAAGuQ,GAE/BL,EAAMphB,OAAO,CAAEmC,OAAQuf,EAAaC,YAAaF,EAAkBJ,WAAYE,EAAiBC,IAAKA,G,CAGtG,MAAMjU,EAAoBJ,EAAcpG,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAK4f,kBAAkB7R,EAAe,G,CAI1C,CAEU,SAAA6S,CAAU3a,EAA+CoC,GAClE,MAAMsZ,EAAQtZ,EAAKf,SAASI,UAAU1H,KAAKihB,OAAO9a,OAAQ,GAC1DkC,EAAKwM,WAAW,gBAAiB8M,GACjC1b,EAASsG,OAAO,QAASoV,GAEzBA,EAAMvb,aAAaH,GAEnB0b,EAAMrb,GAAG,OAAO,KACftG,KAAKmiB,aAAalc,EAAS,IAE5B0b,EAAMrb,GAAG,eAAe,KACvBtG,KAAKmiB,aAAalc,EAAS,IAE5B0b,EAAMrb,GAAG,UAAU,KAClBtG,KAAKmiB,aAAalc,EAAS,GAE7B,CAEU,YAAAkc,CAAalc,GACtB,MAAM0b,EAAQ1b,EAASJ,IAAI,SACrBK,EAAQD,EAASJ,IAAI,SAE3B,GAAI8b,GAASzb,EAAO,CACnB,IAAIgc,EAAcP,EAAM9b,IAAI,cAAe,GACvCnD,EAASif,EAAM9b,IAAI,SAAU,GAC7BsS,EAAQwJ,EAAM9b,IAAI,aAAc,GAChCkc,EAAM1Q,KAAKqN,IAAIiD,EAAM9b,IAAI,MAAO,IAChCuc,EAAajK,EAAQ4J,EAAM,EAC3Bhe,EAAWmC,EAAML,IAAI,YAErBkP,EAAWrS,EAASwf,EACpBlN,EAAYtS,EAASqf,EAAM,WAEZ,GAAfG,GAAoBH,GAAO,KAAmB,UAAZhe,IACrCrB,EAAS,EACT0f,EAAa,EACbrN,GAAY,EACZC,EAAYD,GAGT1D,KAAK6O,MAAM6B,IAAQ,KAAmB,UAAZhe,IAC7Bqe,EAAa,GAGE,YAAZre,IACHgR,EAAWgN,EAAM,YAAiBG,GAAexf,EAASwf,GAAe,GAAK,IAI/Ehc,EAAM3F,OAAO,CAAE6hB,WAAYA,IAC3Blc,EAAM2O,WAAW,SAAUnS,GAC3BwD,EAAM2O,WAAW,cAAeqN,GAEhChc,EAAM3F,OAAO,CACZyU,UAAWA,EACXD,SAAUA,G,CAGb,CAEU,KAAAxH,CAAMtH,GACf,IAAImN,EAAK,EACLE,EAAK,EACLoO,EAAK1hB,KAAKmP,WAAW,gBAAiB,GAE1C,MAAMzB,EAAgBzH,EAASJ,IAAI,mBAEnC,IAAIxC,EAAUrD,KAAK6F,IAAI,UAAW,GAC9BnB,EAAW1E,KAAK6F,IAAI,WAAY,GAChC0L,EAAe7D,EAAcjE,MAC7BsH,EAAW/Q,KAAKmP,WAAW,WAAY,GACvCnO,EAAYhB,KAAK0R,kBAEJ,MAAb1Q,IACHA,EAAYhB,KAAK6F,IAAI,YAAa,IAGnC,MAAMgb,EAAca,GAAM3Q,EAAW,GAEjCQ,EAAe7M,IAClB6M,EAAe7M,GAGZ6M,EAAelO,EAAU,IAC5BA,EAAUkO,GAGX6B,EAAK1F,EAAc0F,GACnBE,EAAK5F,EAAc4F,GAEnB,IAAI+O,EAAcrhB,EAAYqC,EAAU,EAEpCgf,EAAatR,EAAWrM,EAAW,IACtC2d,EAAatR,EAAWrM,EAAW,GAGpC,IAAIgc,EAASgB,GAAMpO,EAAKF,GACpBuN,EAASe,GAAMb,EAAcwB,GAE7BZ,EAAKpQ,KAAKI,IAAIF,EAAelO,EAASqB,GAAYmc,EAAcF,EAEpE,MAAMvQ,EAASpQ,KAAK6F,IAAI,mBACxB,IAAImK,EAAWhQ,KAAK6F,IAAI,oBAAqB,GAExC7F,KAAKwR,SACTxB,EAAW,GAGZ,IAAI8I,GAAM1F,EAAKsN,EAEf1gB,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAImQ,EAAQ1Q,SAAUA,EAAUI,OAAQA,IAC7EpQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAIoQ,EAAQ3Q,SAAUA,EAAUI,OAAQA,IAC7EpQ,KAAKigB,eAAe,CAAE3P,IAAK,KAAMC,GAAIkR,EAAIzR,SAAUA,EAAUI,OAAQA,IACrEpQ,KAAKigB,eAAe,CAAE3P,IAAK,KAAMC,GAAIuI,EAAI9I,SAAUA,EAAUI,OAAQA,GACtE,CAGU,aAAAuB,CAAc1L,GACvB,MAAMuB,EAASvB,EAASJ,IAAI,UAC5B,GAAI2B,EAAQ,CACX,MAAMF,EAAWE,EAAO3B,IAAI,YACxByB,GACH,OAAYA,GAAW0B,IAClBA,GAAS/C,IACZjG,KAAKgI,gBAAgBgB,GACrBA,EAAMnD,IAAI,QAAQ4K,O,IAIrBzQ,KAAK2R,cAAcnK,E,CAErB,CAEO,eAAAuK,CAAgBxD,GAEtB,MAAMuD,EAASvD,EAAO1I,IAAI,UAC1B,GAAIiM,EAAQ,CACX,MAAM7L,EAAW6L,EAAO7L,SAElB+L,EAAYzD,EAAO1I,IAAI,YAAa,IACpCoM,EAAY1D,EAAO1I,IAAI,YAAa,IAEpC8b,EAAQ1b,EAASJ,IAAI,SACrBkc,EAAMJ,EAAM9b,IAAI,MAAO,GACvBsS,EAAQwJ,EAAM9b,IAAI,aAAc,GAAK8b,EAAM9b,IAAI,MAAO,GAAKmM,EAC3DkQ,EAAcP,EAAM9b,IAAI,cAAe,GACvCnD,EAASwf,GAAeP,EAAM9b,IAAI,SAAU,GAAKqc,GAAejQ,EAEtE,IAAI1O,EAAI,OAAU4U,GAASzV,EACvBc,EAAI,OAAU2U,GAASzV,EAEC,MAAxB,SAAYqf,EAAK,IAA8C,IAAhC,SAAYG,EAAa,KAC3D3e,EAAI,EACJC,EAAI,GAGLsO,EAAOzK,IAAI,IAAK9D,GAChBuO,EAAOzK,IAAI,IAAK7D,E,CAElB,CAEU,WAAAoO,CAAY3L,EAA+C4L,EAAmHxH,GACvL,MAAMkE,EAASzO,MAAM8R,YAAY3L,EAAU4L,EAAgBxH,GAE3D,GAAIkE,EAAQ,CACX,MAAMuD,EAASvD,EAAO1I,IAAI,UACpB8b,EAAQ1b,EAASJ,IAAI,SAY3B,OAVIiM,GAAU6P,IACbA,EAAMrb,GAAG,OAAO,KACftG,KAAK+R,gBAAgBxD,EAAO,IAG7BoT,EAAMrb,GAAG,UAAU,KAClBtG,KAAK+R,gBAAgBxD,EAAO,KAIvBA,C,CAET,EC/eD,SAAS+T,GAAkBvX,EAAGC,GAC5B,OAAOD,EAAEvD,SAAWwD,EAAExD,OAAS,EAAI,CACrC,CAUA,SAAS+a,GAASC,GAChB,IAAIlb,EAAWkb,EAAElb,SACjB,OAAOA,EAAWA,EAAS,GAAKkb,EAAEC,CACpC,CAGA,SAASC,GAAUF,GACjB,IAAIlb,EAAWkb,EAAElb,SACjB,OAAOA,EAAWA,EAASA,EAASkB,OAAS,GAAKga,EAAEC,CACtD,CAIA,SAASE,GAAYC,EAAIC,EAAIC,GAC3B,IAAIC,EAASD,GAASD,EAAGta,EAAIqa,EAAGra,GAChCsa,EAAGzX,GAAK2X,EACRF,EAAGvL,GAAKwL,EACRF,EAAGxX,GAAK2X,EACRF,EAAGtG,GAAKuG,EACRD,EAAGpM,GAAKqM,CACV,CAqBA,SAASE,GAAaC,EAAKT,EAAG1X,GAC5B,OAAOmY,EAAIlY,EAAEvD,SAAWgb,EAAEhb,OAASyb,EAAIlY,EAAID,CAC7C,CAEA,SAASoY,GAAS7a,EAAME,GACtBvI,KAAKyY,EAAIpQ,EACTrI,KAAKwH,OAAS,KACdxH,KAAKsH,SAAW,KAChBtH,KAAKmjB,EAAI,KACTnjB,KAAK+K,EAAI/K,KACTA,KAAKuc,EAAI,EACTvc,KAAKyW,EAAI,EACTzW,KAAKoL,EAAI,EACTpL,KAAKsX,EAAI,EACTtX,KAAKyiB,EAAI,KACTziB,KAAKuI,EAAIA,CACX,CA4Be,cACb,IAAI6a,EAAad,GACbxJ,EAAK,EACLC,EAAK,EACLsK,EAAW,KAEf,SAASnK,EAAK/P,GACZ,IAAIsZ,EA/BR,SAAkBtZ,GAShB,IARA,IACId,EAEAW,EACA1B,EACAiB,EACAW,EANAgQ,EAAO,IAAIgK,GAAS/Z,EAAM,GAE1BE,EAAQ,CAAC6P,GAMN7Q,EAAOgB,EAAMC,OAClB,GAAIhC,EAAWe,EAAKoQ,EAAEnR,SAEpB,IADAe,EAAKf,SAAW,IAAIiC,MAAML,EAAI5B,EAASkB,QAClCD,EAAIW,EAAI,EAAGX,GAAK,IAAKA,EACxBc,EAAM5C,KAAKuC,EAAQX,EAAKf,SAASiB,GAAK,IAAI2a,GAAS5b,EAASiB,GAAIA,IAChES,EAAMxB,OAASa,EAMrB,OADC6Q,EAAK1R,OAAS,IAAI0b,GAAS,KAAM,IAAI5b,SAAW,CAAC4R,GAC3CA,CACT,CAUYoK,CAASna,GAOjB,GAJAsZ,EAAExY,UAAUsZ,GAAYd,EAAEjb,OAAOiP,GAAKgM,EAAElG,EACxCkG,EAAE/Y,WAAW8Z,GAGTH,EAAUla,EAAKO,WAAW+Z,OAIzB,CACH,IAAIpC,EAAOlY,EACPoQ,EAAQpQ,EACRqQ,EAASrQ,EACbA,EAAKO,YAAW,SAASrB,GACnBA,EAAK9E,EAAI8d,EAAK9d,IAAG8d,EAAOhZ,GACxBA,EAAK9E,EAAIgW,EAAMhW,IAAGgW,EAAQlR,GAC1BA,EAAKoB,MAAQ+P,EAAO/P,QAAO+P,EAASnR,EAC1C,IACA,IAAIiP,EAAI+J,IAAS9H,EAAQ,EAAI6J,EAAW/B,EAAM9H,GAAS,EACnDmK,EAAKpM,EAAI+J,EAAK9d,EACdogB,EAAK7K,GAAMS,EAAMhW,EAAI+T,EAAIoM,GACzBE,EAAK7K,GAAMS,EAAO/P,OAAS,GAC/BN,EAAKO,YAAW,SAASrB,GACvBA,EAAK9E,GAAK8E,EAAK9E,EAAImgB,GAAMC,EACzBtb,EAAK7E,EAAI6E,EAAKoB,MAAQma,CACxB,GACF,CAEA,OAAOza,CACT,CAMA,SAASoa,EAAUf,GACjB,IAAIlb,EAAWkb,EAAElb,SACbuc,EAAWrB,EAAEhb,OAAOF,SACpB2W,EAAIuE,EAAEja,EAAIsb,EAASrB,EAAEja,EAAI,GAAK,KAClC,GAAIjB,EAAU,EA5GlB,SAAuBkb,GAMrB,IALA,IAIIvE,EAJA6E,EAAQ,EACRC,EAAS,EACTzb,EAAWkb,EAAElb,SACbiB,EAAIjB,EAASkB,SAERD,GAAK,IACZ0V,EAAI3W,EAASiB,IACXgU,GAAKuG,EACP7E,EAAExH,GAAKqM,EACPA,GAAS7E,EAAE3G,GAAKyL,GAAU9E,EAAE7S,EAEhC,CAiGM0Y,CAActB,GACd,IAAIuB,GAAYzc,EAAS,GAAGiV,EAAIjV,EAASA,EAASkB,OAAS,GAAG+T,GAAK,EAC/D0B,GACFuE,EAAEjG,EAAI0B,EAAE1B,EAAI6G,EAAWZ,EAAE/J,EAAGwF,EAAExF,GAC9B+J,EAAE/L,EAAI+L,EAAEjG,EAAIwH,GAEZvB,EAAEjG,EAAIwH,CAEV,MAAW9F,IACTuE,EAAEjG,EAAI0B,EAAE1B,EAAI6G,EAAWZ,EAAE/J,EAAGwF,EAAExF,IAEhC+J,EAAEhb,OAAO2b,EAoBX,SAAmBX,EAAGvE,EAAGnT,GACvB,GAAImT,EAAG,CAUL,IATA,IAQI6E,EARAkB,EAAMxB,EACNyB,EAAMzB,EACNS,EAAMhF,EACNiG,EAAMF,EAAIxc,OAAOF,SAAS,GAC1B6c,EAAMH,EAAIvN,EACV2N,EAAMH,EAAIxN,EACV4N,EAAMpB,EAAIxM,EACV6N,EAAMJ,EAAIzN,EAEPwM,EAAMP,GAAUO,GAAMe,EAAMzB,GAASyB,GAAMf,GAAOe,GACvDE,EAAM3B,GAAS2B,IACfD,EAAMvB,GAAUuB,IACZlZ,EAAIyX,GACRM,EAAQG,EAAI1G,EAAI8H,EAAML,EAAIzH,EAAI4H,EAAMf,EAAWH,EAAIxK,EAAGuL,EAAIvL,IAC9C,IACVkK,GAAYK,GAAaC,EAAKT,EAAG1X,GAAW0X,EAAGM,GAC/CqB,GAAOrB,EACPsB,GAAOtB,GAETuB,GAAOpB,EAAIxM,EACX0N,GAAOH,EAAIvN,EACX6N,GAAOJ,EAAIzN,EACX2N,GAAOH,EAAIxN,EAETwM,IAAQP,GAAUuB,KACpBA,EAAIxB,EAAIQ,EACRgB,EAAIxN,GAAK4N,EAAMD,GAEbJ,IAAQzB,GAAS2B,KACnBA,EAAIzB,EAAIuB,EACRE,EAAIzN,GAAK0N,EAAMG,EACfxZ,EAAW0X,EAEf,CACA,OAAO1X,CACT,CAzDeyZ,CAAU/B,EAAGvE,EAAGuE,EAAEhb,OAAO2b,GAAKU,EAAS,GACtD,CAGA,SAASL,EAAWhB,GAClBA,EAAE/J,EAAElV,EAAIif,EAAEjG,EAAIiG,EAAEhb,OAAOiP,EACvB+L,EAAE/L,GAAK+L,EAAEhb,OAAOiP,CAClB,CAoDA,SAASgN,EAASpb,GAChBA,EAAK9E,GAAKuV,EACVzQ,EAAK7E,EAAI6E,EAAKoB,MAAQsP,CACxB,CAcA,OAZAG,EAAKkK,WAAa,SAAS7f,GACzB,OAAOmV,UAAUlQ,QAAU4a,EAAa7f,EAAG2V,GAAQkK,CACrD,EAEAlK,EAAKgE,KAAO,SAAS3Z,GACnB,OAAOmV,UAAUlQ,QAAU6a,GAAW,EAAOvK,GAAMvV,EAAE,GAAIwV,GAAMxV,EAAE,GAAI2V,GAASmK,EAAW,KAAO,CAACvK,EAAIC,EACvG,EAEAG,EAAKmK,SAAW,SAAS9f,GACvB,OAAOmV,UAAUlQ,QAAU6a,GAAW,EAAMvK,GAAMvV,EAAE,GAAIwV,GAAMxV,EAAE,GAAI2V,GAASmK,EAAW,CAACvK,EAAIC,GAAM,IACrG,EAEOG,CACT,CD/GC,sC,gDAAkC,aAClC,uC,gDAA0CmH,GAAU1Y,WAAWC,OAAO,CAACmZ,GAASlZ,cCrDjFqb,GAASnZ,UAAYya,OAAOvhB,OAAOmG,EAAKW,WCHjC,MAAM0a,WAAarQ,EAA1B,c,oBAMC,mC,gDAAyB,SAKzB,+C,gDAA0B,OAE1B,wC,wDA+CD,CA7CQ,gBAAAzH,GACN7M,MAAM6M,oBAEF3M,KAAK8G,QAAQ,gBAAkB9G,KAAK8G,QAAQ,cAC/C9G,KAAKsN,gBAEP,CAEU,cAAAA,GACT,GAAItN,KAAKkN,UAAW,CACnB,MAAMhK,EAASlD,KAAK0kB,iBAEW,YAA3B1kB,KAAK6F,IAAI,eACZ3C,EAAOga,KAAK,CAACld,KAAK0d,aAAc1d,KAAK2d,gBAGrCza,EAAOga,KAAK,CAACld,KAAK2d,cAAe3d,KAAK0d,eAGvCxa,EAAOlD,KAAKkN,U,CAGbpN,MAAMwN,gBACP,CAEU,SAAAiI,CAAU7H,GACnB,MAAMiX,EAAW3kB,KAAK6F,IAAI,YAC1B,MAA+B,YAA3B7F,KAAK6F,IAAI,eACR8e,EACI,CAAEphB,EAAGmK,EAAcnK,EAAGC,EAAGxD,KAAK2d,cAAgBjQ,EAAclK,GAG5D,CAAED,EAAGmK,EAAcnK,EAAGC,EAAGkK,EAAclK,GAI3CmhB,EACI,CAAEphB,EAAGvD,KAAK0d,aAAehQ,EAAclK,EAAGA,EAAGkK,EAAcnK,GAG3D,CAAEA,EAAGmK,EAAclK,EAAGA,EAAGkK,EAAcnK,EAGjD,EChIc,YAASiE,EAAQ4L,EAAIC,EAAIC,EAAIC,GAO1C,IANA,IACIlL,EADAgB,EAAQ7B,EAAOF,SAEfiB,GAAK,EACLW,EAAIG,EAAMb,OACV8C,EAAI9D,EAAOiB,QAAU8K,EAAKF,GAAM7L,EAAOiB,QAElCF,EAAIW,IACXb,EAAOgB,EAAMd,IAAS6K,GAAKA,EAAI/K,EAAKiL,GAAKA,EACzCjL,EAAKgL,GAAKA,EAAIhL,EAAKkL,GAAKF,GAAMhL,EAAKI,MAAQ6C,CAE/C,CDmEC,sC,gDAAkC,SAClC,uC,gDAA0C8I,EAAgBzM,WAAWC,OAAO,CAAC6c,GAAK5c,cEzBnF,OAAe,SAAU+c,EAAOC,GAE9B,SAASC,EAAStd,EAAQ4L,EAAIC,EAAIC,EAAIC,IAnDjC,SAAuBsR,EAAOrd,EAAQ4L,EAAIC,EAAIC,EAAIC,GAkBvD,IAjBA,IAEIwR,EACAC,EAIAlM,EAAIC,EAEJkM,EACAC,EACAC,EACAC,EACAC,EACAxO,EACAyO,EAfAC,EAAO,GACPlc,EAAQ7B,EAAOF,SAGfke,EAAK,EACLC,EAAK,EACLvc,EAAIG,EAAMb,OAEVC,EAAQjB,EAAOiB,MASZ+c,EAAKtc,GAAG,CACb4P,EAAKxF,EAAKF,EAAI2F,EAAKxF,EAAKF,EAGxB,GAAG4R,EAAW5b,EAAMoc,KAAMhd,aAAewc,GAAYQ,EAAKvc,GAO1D,IANAgc,EAAWC,EAAWF,EAEtBK,EAAOL,EAAWA,GADlBpO,EAAQxF,KAAKI,IAAIsH,EAAKD,EAAIA,EAAKC,IAAOtQ,EAAQoc,IAE9CQ,EAAWhU,KAAKI,IAAI0T,EAAWG,EAAMA,EAAOJ,GAGrCO,EAAKvc,IAAKuc,EAAI,CAMnB,GALAR,GAAYD,EAAY3b,EAAMoc,GAAIhd,MAC9Buc,EAAYE,IAAUA,EAAWF,GACjCA,EAAYG,IAAUA,EAAWH,GACrCM,EAAOL,EAAWA,EAAWpO,GAC7BuO,EAAW/T,KAAKI,IAAI0T,EAAWG,EAAMA,EAAOJ,IAC7BG,EAAU,CAAEJ,GAAYD,EAAW,KAAO,CACzDK,EAAWD,CACb,CAGAG,EAAK9e,KAAKse,EAAM,CAACtc,MAAOwc,EAAUS,KAAM5M,EAAKC,EAAIzR,SAAU+B,EAAMsY,MAAM6D,EAAIC,KACvEV,EAAIW,KAAM,GAAYX,EAAK3R,EAAIC,EAAIC,EAAI7K,EAAQ4K,GAAM0F,EAAKkM,EAAWxc,EAAQ8K,GAC5E,GAAawR,EAAK3R,EAAIC,EAAI5K,EAAQ2K,GAAM0F,EAAKmM,EAAWxc,EAAQ6K,EAAIC,GACzE9K,GAASwc,EAAUO,EAAKC,CAC1B,CAGF,CAKIE,CAAcd,EAAOrd,EAAQ4L,EAAIC,EAAIC,EAAIC,EAC3C,CAMA,OAJAuR,EAASD,MAAQ,SAASthB,GACxB,OAAOqhB,GAAQrhB,GAAKA,GAAK,EAAIA,EAAI,EACnC,EAEOuhB,CACR,CAXD,EAnDkB,EAAIzT,KAAKsF,KAAK,IAAM,G,sBCEvB,cACb,IAAIiP,EAAOd,GACP5E,GAAQ,EACRpH,EAAK,EACLC,EAAK,EACL8M,EAAe,CAAC,GAChBC,EAAe,KACfhkB,EAAa,KACbC,EAAe,KACfF,EAAgB,KAChBG,EAAc,KAElB,SAAS+jB,EAAQ5c,GAQf,OAPAA,EAAKiK,GACLjK,EAAKkK,GAAK,EACVlK,EAAKmK,GAAKwF,EACV3P,EAAKoK,GAAKwF,EACV5P,EAAKO,WAAW0W,GAChByF,EAAe,CAAC,GACZ3F,GAAO/W,EAAKO,WAAW,IACpBP,CACT,CAEA,SAASiX,EAAa/X,GACpB,IAAI2d,EAAIH,EAAaxd,EAAKoB,OACtB2J,EAAK/K,EAAK+K,GAAK4S,EACf3S,EAAKhL,EAAKgL,GAAK2S,EACf1S,EAAKjL,EAAKiL,GAAK0S,EACfzS,EAAKlL,EAAKkL,GAAKyS,EACf1S,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GAC/BC,EAAKF,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnClL,EAAK+K,GAAKA,EACV/K,EAAKgL,GAAKA,EACVhL,EAAKiL,GAAKA,EACVjL,EAAKkL,GAAKA,EACNlL,EAAKf,WACP0e,EAAIH,EAAaxd,EAAKoB,MAAQ,GAAKqc,EAAazd,GAAQ,EACxD+K,GAAMpR,EAAYqG,GAAQ2d,EAC1B3S,GAAMvR,EAAWuG,GAAQ2d,GACzB1S,GAAMvR,EAAasG,GAAQ2d,GAElB5S,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,IADnCC,GAAM1R,EAAcwG,GAAQ2d,GAEnB3S,IAAIA,EAAKE,GAAMF,EAAKE,GAAM,GACnCqS,EAAKvd,EAAM+K,EAAIC,EAAIC,EAAIC,GAE3B,CA0CA,OAxCAwS,EAAQ7F,MAAQ,SAAS3c,GACvB,OAAOmV,UAAUlQ,QAAU0X,IAAU3c,EAAGwiB,GAAW7F,CACrD,EAEA6F,EAAQ7I,KAAO,SAAS3Z,GACtB,OAAOmV,UAAUlQ,QAAUsQ,GAAMvV,EAAE,GAAIwV,GAAMxV,EAAE,GAAIwiB,GAAW,CAACjN,EAAIC,EACrE,EAEAgN,EAAQH,KAAO,SAASriB,GACtB,OAAOmV,UAAUlQ,QAAUod,GAAO,QAASriB,GAAIwiB,GAAWH,CAC5D,EAEAG,EAAQhG,QAAU,SAASxc,GACzB,OAAOmV,UAAUlQ,OAASud,EAAQD,aAAaviB,GAAG0iB,aAAa1iB,GAAKwiB,EAAQD,cAC9E,EAEAC,EAAQD,aAAe,SAASviB,GAC9B,OAAOmV,UAAUlQ,QAAUsd,EAA4B,mBAANviB,EAAmBA,GAAI,SAAUA,GAAIwiB,GAAWD,CACnG,EAEAC,EAAQE,aAAe,SAAS1iB,GAC9B,OAAOmV,UAAUlQ,OAASud,EAAQjkB,WAAWyB,GAAGxB,aAAawB,GAAG1B,cAAc0B,GAAGvB,YAAYuB,GAAKwiB,EAAQjkB,YAC5G,EAEAikB,EAAQjkB,WAAa,SAASyB,GAC5B,OAAOmV,UAAUlQ,QAAU1G,EAA0B,mBAANyB,EAAmBA,GAAI,SAAUA,GAAIwiB,GAAWjkB,CACjG,EAEAikB,EAAQhkB,aAAe,SAASwB,GAC9B,OAAOmV,UAAUlQ,QAAUzG,EAA4B,mBAANwB,EAAmBA,GAAI,SAAUA,GAAIwiB,GAAWhkB,CACnG,EAEAgkB,EAAQlkB,cAAgB,SAAS0B,GAC/B,OAAOmV,UAAUlQ,QAAU3G,EAA6B,mBAAN0B,EAAmBA,GAAI,SAAUA,GAAIwiB,GAAWlkB,CACpG,EAEAkkB,EAAQ/jB,YAAc,SAASuB,GAC7B,OAAOmV,UAAUlQ,QAAUxG,EAA2B,mBAANuB,EAAmBA,GAAI,SAAUA,GAAIwiB,GAAW/jB,CAClG,EAEO+jB,CACT,CC7Fe,YAASve,EAAQ4L,EAAIC,EAAIC,EAAIC,GAC1C,IACIhL,EACAD,EAFAe,EAAQ7B,EAAOF,SACZ4B,EAAIG,EAAMb,OACR0d,EAAO,IAAI3c,MAAML,EAAI,GAE9B,IAAKgd,EAAK,GAAK5d,EAAMC,EAAI,EAAGA,EAAIW,IAAKX,EACnC2d,EAAK3d,EAAI,GAAKD,GAAOe,EAAMd,GAAGE,OAKhC,SAAS0X,EAAU5X,EAAGkR,EAAGhR,EAAO2K,EAAIC,EAAIC,EAAIC,GAC1C,GAAIhL,GAAKkR,EAAI,EAAG,CACd,IAAIpR,EAAOgB,EAAMd,GAGjB,OAFAF,EAAK+K,GAAKA,EAAI/K,EAAKgL,GAAKA,EACxBhL,EAAKiL,GAAKA,OAAIjL,EAAKkL,GAAKA,EAE1B,CAOA,IALA,IAAI4S,EAAcD,EAAK3d,GACnB6d,EAAe3d,EAAQ,EAAK0d,EAC5B7a,EAAI/C,EAAI,EACR8d,EAAK5M,EAAI,EAENnO,EAAI+a,GAAI,CACb,IAAIC,EAAMhb,EAAI+a,IAAO,EACjBH,EAAKI,GAAOF,EAAa9a,EAAIgb,EAAM,EAClCD,EAAKC,CACZ,CAEKF,EAAcF,EAAK5a,EAAI,GAAO4a,EAAK5a,GAAK8a,GAAgB7d,EAAI,EAAI+C,KAAKA,EAE1E,IAAIib,EAAYL,EAAK5a,GAAK6a,EACtBK,EAAa/d,EAAQ8d,EAEzB,GAAKjT,EAAKF,EAAOG,EAAKF,EAAK,CACzB,IAAIoT,EAAKhe,GAAS2K,EAAKoT,EAAalT,EAAKiT,GAAa9d,EAAQ6K,EAC9D6M,EAAU5X,EAAG+C,EAAGib,EAAWnT,EAAIC,EAAIoT,EAAIlT,GACvC4M,EAAU7U,EAAGmO,EAAG+M,EAAYC,EAAIpT,EAAIC,EAAIC,EAC1C,KAAO,CACL,IAAImT,EAAKje,GAAS4K,EAAKmT,EAAajT,EAAKgT,GAAa9d,EAAQ8K,EAC9D4M,EAAU5X,EAAG+C,EAAGib,EAAWnT,EAAIC,EAAIC,EAAIoT,GACvCvG,EAAU7U,EAAGmO,EAAG+M,EAAYpT,EAAIsT,EAAIpT,EAAIC,EAC1C,CACF,CAnCA4M,CAAU,EAAGjX,EAAG1B,EAAOiB,MAAO2K,EAAIC,EAAIC,EAAIC,EAoC5C,CC1Ce,YAAS/L,EAAQ4L,EAAIC,EAAIC,EAAIC,IAC1B,EAAf/L,EAAOiC,MAAYkY,GAAQ+D,IAAMle,EAAQ4L,EAAIC,EAAIC,EAAIC,EACxD,CCkGO,MAAMoT,WAAgBza,EAA7B,c,oBAMC,mC,gDAAyB,YAKzB,gD,gDAAgE3G,EAAA,GAASzE,IAAI,CAAC,KAE9E,6C,gDAAwB,KAAsB8kB,KAAK,MASnD,yC,gDAA6D,IAAItgB,EAAA,EAChEC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAMiF,EAAA,EAAiBN,KAAKzF,KAAKC,MAAO,CACvCyF,UAAW,YAAiB1F,KAAKsgB,WAAW1a,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,WACrF,CAACpM,KAAKsgB,WAAW1a,cA6PtB,CA1PW,SAAAc,GACT5G,MAAM4G,YACN1G,KAAK6U,WAAW,SAAU,GAC1B7U,KAAK6U,WAAW,SAAU,GAE1B7U,KAAKqJ,MAAMzD,SAASiP,WAAW,eAAe,EAC/C,CAEO,gBAAAlI,GAGN,GAFA7M,MAAM6M,mBAEF3M,KAAK8G,QAAQ,mBAAoB,CACpC,IAAI8f,EAEJ,OAAQ5mB,KAAK6F,IAAI,oBAChB,IAAK,WACJ+gB,EAAY,GACZ,MACD,IAAK,SACJA,EAAY,GACZ,MACD,IAAK,QACJA,EAAY,GACZ,MACD,IAAK,OACJA,EAAY,GACZ,MACD,IAAK,YACJA,EAAY,GAGd,GAAIA,EAAW,CACd5mB,KAAK6mB,eAAiB,KAAsBjB,KAAKgB,GACjD5mB,KAAKsN,iBAEL,MAAMwZ,EAAmB9mB,KAAK6F,IAAI,oBAC/BihB,GACF9mB,KAAKuN,MAAMuZ,E,GAKV9mB,KAAK8G,QAAQ,qBAAuB9G,KAAK8G,QAAQ,oBAAsB9G,KAAK8G,QAAQ,mBAAqB9G,KAAK8G,QAAQ,sBAAwB9G,KAAK8G,QAAQ,qBAAuB9G,KAAK8G,QAAQ,sBAC9L9G,KAAKkN,WACRlN,KAAKyN,aAAazN,KAAKkN,YAIrBlN,KAAK2f,eAAe,WAAa3f,KAAK2f,eAAe,YACpD3f,KAAKkN,WACRlN,KAAK4f,kBAAkB5f,KAAKkN,UAG/B,CAEU,cAAAI,GACT,GAAItN,KAAKkN,UAAW,CACnB,MAAM6Z,EAAgB/mB,KAAK6mB,eAC3BE,EAAc7J,KAAK,CAACld,KAAK0d,aAAc1d,KAAK2d,gBAE5C,MAAM3b,EAAchC,KAAK6F,IAAI,mBACvB9D,EAAe/B,KAAK6F,IAAI,oBACxB/D,EAAa9B,KAAK6F,IAAI,kBACtBhE,EAAgB7B,KAAK6F,IAAI,qBACzBigB,EAAe9lB,KAAK6F,IAAI,oBACxBogB,EAAejmB,KAAK6F,IAAI,oBAC1B,WAAe7D,IAClB+kB,EAAc/kB,YAAYA,GAEvB,WAAeD,IAClBglB,EAAchlB,aAAaA,GAExB,WAAeD,IAClBilB,EAAcjlB,WAAWA,GAEtB,WAAeD,IAClBklB,EAAcllB,cAAcA,GAEzB,WAAeikB,IAClBiB,EAAcjB,aAAaA,GAExB,WAAeG,IAClBc,EAAcd,aAAaA,GAG5Bc,EAAc/mB,KAAKkN,WACnBlN,KAAKyN,aAAazN,KAAKkN,U,CAEzB,CAEU,WAAAS,CAAY1H,GACrBnG,MAAM6N,YAAY1H,GAElB,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB4a,EAAYxa,EAASJ,IAAI,aACzB6H,EAAgBzH,EAASJ,IAAI,mBAE7B6a,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GAEnCiE,EAAK1F,EAAc0F,GAAKsN,EACxBpN,EAAK5F,EAAc4F,GAAKoN,EACxBrN,EAAK3F,EAAc2F,GAAKsN,EAGxB1C,EAAI3K,EAAKF,EACT8K,EAHKxQ,EAAc6F,GAAKoN,EAGftN,EAETrD,EAAWhQ,KAAK6F,IAAI,oBAAqB,GACzCuK,EAASpQ,KAAK6F,IAAI,mBAQxB,GANAwC,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAI6C,EAAIpD,SAAUA,EAAUI,OAAQA,IAC7D/H,EAAKgI,QAAQ,CAAEC,IAAK,IAAKC,GAAI8C,EAAIrD,SAAUA,EAAUI,OAAQA,IAE7D/H,EAAKgI,QAAQ,CAAEC,IAAK,QAASC,GAAI0N,EAAGjO,SAAUA,EAAUI,OAAQA,IAChE/H,EAAKgI,QAAQ,CAAEC,IAAK,SAAUC,GAAI2N,EAAGlO,SAAUA,EAAUI,OAAQA,IAE7DqQ,EAAW,CACd,MAAM/K,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAEjC4a,EAAUpQ,QAAQ,CAAEC,IAAK,QAASC,GAAI0N,EAAGjO,SAAUA,EAAUI,OAAQA,IACrEqQ,EAAUpQ,QAAQ,CAAEC,IAAK,SAAUC,GAAI2N,EAAGlO,SAAUA,EAAUI,OAAQA,IACtEqQ,EAAU7K,YAAY,OAAQF,GAC9B+K,EAAU7K,YAAY,cAAeD,GACrC8K,EAAU7K,YAAY,SAAUF,E,CAElC,CAGU,iBAAAkK,CAAkBlS,GAC3B,MAAMzH,EAAWyH,EAAc/E,KAAK1C,SACpC,GAAIA,EAAU,CACb,MAAMoC,EAAOpC,EAASJ,IAAI,QACpB4a,EAAYxa,EAASJ,IAAI,aAEzB6a,EAAS1gB,KAAKmP,WAAW,SAAU,GACnCwR,EAAS3gB,KAAKmP,WAAW,SAAU,GAEnCiE,EAAK1F,EAAc0F,GAAKsN,EACxBpN,EAAK5F,EAAc4F,GAAKoN,EACxBrN,EAAK3F,EAAc2F,GAAKsN,EAGxB1C,EAAI3K,EAAKF,EACT8K,EAHKxQ,EAAc6F,GAAKoN,EAGftN,EAEfhL,EAAK9H,OAAO,CAAEgD,EAAG6P,EAAI5P,EAAG6P,EAAI3S,MAAOud,EAAGtd,OAAQud,IAC9CuC,EAAUlgB,OAAO,CAAEG,MAAOud,EAAGtd,OAAQud,IAErC,MAAMpQ,EAAoBJ,EAAcpG,SACpCwG,GACH,OAAYA,GAAoBC,IAC/B/N,KAAK4f,kBAAkB7R,EAAe,G,CAI1C,CAKO,QAAA1B,CAASpG,GACf,MAAMoC,EAAOvI,MAAMuM,SAASpG,GAEtBwa,EAAYpY,EAAKf,SAASI,UAAU1H,KAAKsgB,WAAWna,OAAQ,GAElEkC,EAAKwM,WAAW,gBAAiB4L,GACjCxa,EAASsG,OAAO,YAAakU,GAE7B,MAAMva,EAAQD,EAASJ,IAAI,SAU3B,OARA4a,EAAUna,GAAG,SAAS,KACrBJ,EAAM2O,WAAW,WAAY4L,EAAU/f,QAAQ,IAGhD+f,EAAUna,GAAG,UAAU,KACtBJ,EAAM2O,WAAW,YAAa4L,EAAU9f,SAAS,IAG3C0H,CACR,CAEO,KAAAkF,CAAMtH,GACZ,GAAIjG,KAAKU,QAAU,GAAKV,KAAKW,SAAW,EAAG,CAC1C,MAAM+M,EAAgBzH,EAASJ,IAAI,mBAE7BmhB,EAAmBhnB,KAAK6F,IAAI,mBAAoB,GAEtD,IAAIuN,EAAK1F,EAAc0F,GAAK4T,EACxB1T,EAAK5F,EAAc4F,GAAK0T,EAExB3T,EAAK3F,EAAc2F,GAAK2T,EACxBzT,EAAK7F,EAAc6F,GAAKyT,EAExBtG,GAAU1gB,KAAK0d,aAAkC,EAAnBsJ,IAAyB1T,EAAKF,GAC5DuN,GAAU3gB,KAAK2d,cAAmC,EAAnBqJ,IAAyBzT,EAAKF,GAEjE,MAAMjD,EAASpQ,KAAK6F,IAAI,mBACxB,IAAImK,EAAWhQ,KAAK6F,IAAI,oBAAqB,GAExC7F,KAAKwR,SACTxB,EAAW,GAGZhQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAImQ,EAAQ1Q,SAAUA,EAAUI,OAAQA,IAC7EpQ,KAAKigB,eAAe,CAAE3P,IAAK,SAAUC,GAAIoQ,EAAQ3Q,SAAUA,EAAUI,OAAQA,IAE7EpQ,KAAKwM,eAAe6D,QAAQ,CAAEC,IAAK,IAAKC,GAAIyW,EAAmB5T,EAAKsN,EAAQ1Q,SAAUA,EAAUI,OAAQA,IACxGpQ,KAAKwM,eAAe6D,QAAQ,CAAEC,IAAK,IAAKC,GAAIyW,EAAmB3T,EAAKsN,EAAQ3Q,SAAUA,EAAUI,OAAQA,G,CAE1G,CAEU,eAAA5C,CAAgBvH,EAAgDjF,EAAoBmQ,GAG7F,GAFArR,MAAM0N,gBAAgBvH,EAAUjF,EAAWmQ,GAEvClL,EAAU,CACb,IAAI8K,EAAW/Q,KAAK6F,IAAI,YAAa,GAAKI,EAASJ,IAAI,SAClD7F,KAAKwR,SACTT,EAAW/Q,KAAK6F,IAAI,eAAgB,IAErC,MAAMohB,EAAejnB,KAAKknB,iBAAiBjhB,EAAU8K,GACrD/Q,KAAKqJ,MAAMa,MAAM7B,KACmE,GAA/E4e,EAAaE,QAAQ9e,EAAKpC,UAC7BoC,EAAKwM,WAAW,aAAa,GAG7BxM,EAAK+e,cAAc,Y,IAItBpnB,KAAKC,MAAMonB,uBACZ,CAEU,gBAAAH,CAAiBjhB,EAA+C8K,GACzE,MAAMzJ,EAAWrB,EAASJ,IAAI,YAC9B,IAAIyhB,EAA+D,GAWnE,OAVGhgB,GACF,OAAYA,GAAW0B,IAClBA,EAAMnD,IAAI,UAAYkL,GAAa/H,EAAMnD,IAAI,YAIhDyhB,EAAmBA,EAAiB1f,OAAO5H,KAAKknB,iBAAiBle,EAAO+H,IAHxEuW,EAAiB7gB,KAAKuC,E,IAOlBse,CACR,EA7QA,sC,gDAAkC,YAClC,uC,gDAA0Cpb,EAAUvE,WAAWC,OAAO,CAAC+e,GAAQ9e,c,gDCEzE,MAAM0f,WAAuBrb,EAApC,c,oBAMC,mC,gDAAyB,mBAUzB,uC,gDAAkD,IAAI5G,EAAA,EACrDC,EAAA,GAASzE,IAAI,CAAC,IACd,IAAM0mB,GAAA,EAAQ/hB,KAAKzF,KAAKC,MAAO,CAC9ByF,UAAW,YAAiB1F,KAAKynB,SAAS7hB,SAASC,IAAI,YAAa,IAAK,CAAC7F,KAAKoM,KAAM,WACnF,CAACpM,KAAKynB,SAAS7hB,eAGnB,sC,iDAAiB,KAAA8hB,mBAsJlB,CApJW,SAAAhhB,GAET1G,KAAKwM,eAAejM,OAAO,CAC1BgD,EAAG,KACHC,EAAG,KACH7B,QAAS,KACTC,QAAS,OAGV5B,KAAKqJ,MAAMzD,SAASiP,WAAW,eAAe,GAE9C/U,MAAM4G,WACP,CAEO,gBAAAiG,GACN7M,MAAM6M,mBAEN,MAAMjM,EAAQV,KAAK0d,aAAe,EAC5B/c,EAASX,KAAK2d,cAAgB,EAEpC,IAAItV,EAAOrI,KAAKkN,UAChB,MAAM4Z,EAAmB9mB,KAAK6F,IAAI,oBAE9BihB,IACHze,EAAOye,EAAiBjhB,IAAI,oBAG7B7F,KAAK2nB,QAAQ1iB,iBAAkBjF,KAAK6F,IAAI,mBAAoB,OAC5D7F,KAAK2nB,QAAQziB,kBAAmBlF,KAAK6F,IAAI,oBAAqB,MAC9D7F,KAAK2nB,QAAQ3iB,eAAgBhF,KAAK6F,IAAI,iBAAkB,OAEpD7F,KAAK8G,QAAQ,SACQ,WAApB9G,KAAK6F,IAAI,UACZ7F,KAAK2nB,QAAQC,KAAK5nB,KAAK6nB,iBAAiB,IACxC7nB,KAAKsN,kBAIHtN,KAAKqN,YACgB,aAApBrN,KAAK6F,IAAI,UACZ7F,KAAK2nB,QAAQG,KAAK,KAAW,MAC7B9nB,KAAK2nB,QAAQC,KAAK,CAAC,EAAElnB,GAAQC,GAAS,EAAED,EAAOC,GAAS,CAACD,EAAOC,GAAS,CAACD,GAAQC,IAAlFX,CAA4FqI,GAC5FrI,KAAKsN,mBAIFtN,KAAK4M,cAAgB5M,KAAK8G,QAAQ,sBAAwBuB,IAC9DrI,KAAK2nB,QAAQG,KAAK,KAAW,MAC7B9nB,KAAK2nB,QAAQtf,GACbrI,KAAKsN,iBAEP,CAEU,WAAAK,CAAY1H,GACrB,MAAM8hB,EAAe9hB,EAASJ,IAAI,mBAA2BmiB,QACvDA,EAAU/hB,EAASJ,IAAI,WAE7B,GAAIkiB,GAAUC,EAAS,CAEtB,IAAIC,EAAmB,GAEnBre,EAAI,EACgB,WAApB5J,KAAK6F,IAAI,UACZ+D,EAAIyH,KAAKC,IAAItR,KAAK0d,aAAc1d,KAAK2d,eAAiB,GAGvD,IAAIuK,EAAOzjB,IACP0jB,GAAO,IAEX,IAAK,IAAI5f,EAAI,EAAG6f,EAAML,EAAOvf,OAAQD,EAAI6f,EAAK7f,IAAK,CAClD,MAAMkN,EAAuBsS,EAAOxf,GACpC,IAAIhF,EAAIkS,EAAM,GAAK7L,EACfpG,EAAIiS,EAAM,GAAK7L,EAEnBqe,EAAYxhB,KAAK,CAAClD,EAAGC,IAErB0kB,EAAO7W,KAAKC,IAAI4W,EAAM3kB,GACtB4kB,EAAO9W,KAAKI,IAAI0W,EAAM5kB,E,CAGvBykB,EAAQ3gB,IAAI,cAAe4gB,GAE3B,MAAMvS,EAAOzP,EAASJ,IAAI,QACpB8P,EAAc1P,EAASJ,IAAI,eAEjCmiB,EAAQpS,YAAY,OAAQF,GAC5BsS,EAAQpS,YAAY,cAAeD,GAEnC,MAAMzP,EAAQD,EAASJ,IAAI,SAC3B,GAAIK,EAAO,CACV,MAAMmiB,EAAON,EAAOM,KAEhBA,GACHniB,EAAM3F,OAAO,CACZgD,EAAG8kB,EAAK9kB,EAAIqG,EACZpG,EAAG6kB,EAAK7kB,EAAIoG,EACZmL,SAAU1D,KAAKqN,IAAIyJ,EAAOD,I,EAK/B,CAGU,aAAAvW,CAAc1L,GACvB,MAAMuB,EAASvB,EAASJ,IAAI,UAC5B,GAAI2B,EAAQ,CACX,MAAMF,EAAWE,EAAO3B,IAAI,YACxByB,GACH,OAAYA,GAAW0B,IAClBA,GAAS/C,IACZjG,KAAKgI,gBAAgBgB,GACrBA,EAAMnD,IAAI,QAAQ4K,O,IAIrBzQ,KAAK2R,cAAcnK,E,CAErB,CAKO,QAAA6E,CAASpG,GACf,MAAMoC,EAAOvI,MAAMuM,SAASpG,GAE5B,OADAjG,KAAK4gB,UAAU3a,EAAUoC,GAClBA,CACR,CAEU,SAAAuY,CAAU3a,EAA+CoC,GAClE,MAAM2f,EAAU3f,EAAKf,SAASI,UAAU1H,KAAKynB,SAASthB,OAAQ,GAC9DkC,EAAKwM,WAAW,gBAAiBmT,GACjC/hB,EAASsG,OAAO,UAAWyb,GAC3BA,EAAQ5hB,aAAaH,EACtB,CAEU,gBAAA4hB,CAAiBnlB,GAC1B,MAAM4lB,EAAStoB,KAAK6F,IAAI,cAAe,KACjC0iB,EAAmB,EAAVlX,KAAK4C,GAASqU,EACvBN,EAAgC,GAEtC,IAAK,IAAIzf,EAAI,EAAGA,EAAI+f,EAAQ/f,IAAK,CAChC,IAAI4P,EAAQ5P,EAAIggB,EAChBP,EAAQvhB,KAAK,CAAC/D,EAAS2O,KAAK+G,IAAID,GAAQzV,EAAS2O,KAAKgH,IAAIF,I,CAG3D,OAAO6P,CACR,EApKA,sC,gDAAkC,mBAClC,uC,gDAA0C9b,EAAUvE,WAAWC,OAAO,CAAC2f,GAAe1f,a,oCC3HxE,WAASmgB,GAOtB,IANA,IAEIjd,EAFAxC,GAAK,EACLW,EAAI8e,EAAQxf,OAEZwC,EAAIgd,EAAQ9e,EAAI,GAChBsf,EAAO,IAEFjgB,EAAIW,GACX6B,EAAIC,EACJA,EAAIgd,EAAQzf,GACZigB,GAAQzd,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,GAGjC,OAAOwd,EAAO,CAChB,CCde,WAASR,GAUtB,IATA,IAIIjd,EAEAK,EANA7C,GAAK,EACLW,EAAI8e,EAAQxf,OACZjF,EAAI,EACJC,EAAI,EAEJwH,EAAIgd,EAAQ9e,EAAI,GAEhBoC,EAAI,IAEC/C,EAAIW,GACX6B,EAAIC,EACJA,EAAIgd,EAAQzf,GACZ+C,GAAKF,EAAIL,EAAE,GAAKC,EAAE,GAAKA,EAAE,GAAKD,EAAE,GAChCxH,IAAMwH,EAAE,GAAKC,EAAE,IAAMI,EACrB5H,IAAMuH,EAAE,GAAKC,EAAE,IAAMI,EAGvB,MAAe,CAAC7H,GAAT+H,GAAK,GAAW9H,EAAI8H,EAC7B,CCjBA,SAASmd,EAAmB1d,EAAGC,GAC7B,OAAOD,EAAE,GAAKC,EAAE,IAAMD,EAAE,GAAKC,EAAE,EACjC,CAKA,SAAS0d,EAAwBJ,GAC/B,MAAMpf,EAAIof,EAAO9f,OACbmgB,EAAU,CAAC,EAAG,GAClB,IAAcpgB,EAAV2U,EAAO,EAEX,IAAK3U,EAAI,EAAGA,EAAIW,IAAKX,EAAG,CACtB,KAAO2U,EAAO,ICXMnS,EDWKud,EAAOK,EAAQzL,EAAO,ICXxBlS,EDW6Bsd,EAAOK,EAAQzL,EAAO,ICXhD9R,EDWqDkd,EAAO/f,ICVhFyC,EAAE,GAAKD,EAAE,KAAOK,EAAE,GAAKL,EAAE,KAAOC,EAAE,GAAKD,EAAE,KAAOK,EAAE,GAAKL,EAAE,KDU8B,MAAKmS,EAClGyL,EAAQzL,KAAU3U,CACpB,CCba,IAASwC,EAAGC,EAAGI,EDe5B,OAAOud,EAAQhH,MAAM,EAAGzE,EAC1B,CAEe,WAASoL,GACtB,IAAKpf,EAAIof,EAAO9f,QAAU,EAAG,OAAO,KAEpC,IAAID,EACAW,EACA0f,EAAe,IAAIrf,MAAML,GACzB2f,EAAgB,IAAItf,MAAML,GAE9B,IAAKX,EAAI,EAAGA,EAAIW,IAAKX,EAAGqgB,EAAargB,GAAK,EAAE+f,EAAO/f,GAAG,IAAK+f,EAAO/f,GAAG,GAAIA,GAEzE,IADAqgB,EAAane,KAAKge,GACblgB,EAAI,EAAGA,EAAIW,IAAKX,EAAGsgB,EAActgB,GAAK,CAACqgB,EAAargB,GAAG,IAAKqgB,EAAargB,GAAG,IAEjF,IAAIugB,EAAeJ,EAAwBE,GACvCG,EAAeL,EAAwBG,GAGvCG,EAAWD,EAAa,KAAOD,EAAa,GAC5CG,EAAYF,EAAaA,EAAavgB,OAAS,KAAOsgB,EAAaA,EAAatgB,OAAS,GACzF0gB,EAAO,GAIX,IAAK3gB,EAAIugB,EAAatgB,OAAS,EAAGD,GAAK,IAAKA,EAAG2gB,EAAKziB,KAAK6hB,EAAOM,EAAaE,EAAavgB,IAAI,KAC9F,IAAKA,GAAKygB,EAAUzgB,EAAIwgB,EAAavgB,OAASygB,IAAa1gB,EAAG2gB,EAAKziB,KAAK6hB,EAAOM,EAAaG,EAAaxgB,IAAI,KAE7G,OAAO2gB,CACT,CEhDe,WAASlB,EAASvS,GAQ/B,IAPA,IAIInC,EAAIC,EAJJrK,EAAI8e,EAAQxf,OACZwd,EAAIgC,EAAQ9e,EAAI,GAChB3F,EAAIkS,EAAM,GAAIjS,EAAIiS,EAAM,GACxBrC,EAAK4S,EAAE,GAAI3S,EAAK2S,EAAE,GAElBmD,GAAS,EAEJ5gB,EAAI,EAAGA,EAAIW,IAAKX,EACP+K,GAAhB0S,EAAIgC,EAAQzf,IAAW,IAAIgL,EAAKyS,EAAE,IACvBxiB,GAAQ6P,EAAK7P,GAAQD,GAAK6P,EAAKE,IAAO9P,EAAI+P,IAAOF,EAAKE,GAAMD,IAAK6V,GAAUA,GACtF/V,EAAKE,EAAID,EAAKE,EAGhB,OAAO4V,CACT,CCfe,WAASnB,GAUtB,IATA,IAGIoB,EACAC,EAJA9gB,GAAK,EACLW,EAAI8e,EAAQxf,OACZwC,EAAIgd,EAAQ9e,EAAI,GAGhBogB,EAAKte,EAAE,GACPue,EAAKve,EAAE,GACPwe,EAAY,IAEPjhB,EAAIW,GACXkgB,EAAKE,EACLD,EAAKE,EAILH,GAFAE,GADAte,EAAIgd,EAAQzf,IACL,GAGP8gB,GAFAE,EAAKve,EAAE,GAGPwe,GAAanY,KAAKwC,MAAMuV,EAAIC,GAG9B,OAAOG,CACT,C,sNClBQ,SAAUC,EAAQC,EAAUC,EAAQC,EAAWC,GAAqB,aAE1E,SAASC,IAEP9pB,KAAK+pB,oBAAsBC,EAC3BhqB,KAAKiqB,mBAAqB5P,IAG1Bra,KAAKkqB,cAAgB7P,IACrBra,KAAKmqB,WAAa9P,IAClBra,KAAKoqB,cAAgB,GACrBpqB,KAAKqqB,oBAAsBC,EAA4BtqB,KAAK+pB,qBAC5D/pB,KAAKuqB,uBAAyBC,EAA8BxqB,KAAKqqB,oBAEnE,CAEA,IAAIL,EAAiB,GAMrB,SAASM,EAA4B9hB,GAQnC,IAPA,IAIIiiB,EAJgB,EAKhBJ,EAAsB,GAEjB9hB,EAAE,EAAGA,EAAEC,EAAQD,IACtB8hB,EAAoB5jB,KAAKgkB,IACzBA,GARoB,GACN,IAQiBA,EARjB,GAUhB,OAAOJ,CACT,CAEA,SAASG,EAA+BH,GAEtC,IADA,IAAIE,EAAyB,EACpBhiB,EAAE,EAAGA,EAAE8hB,EAAoB7hB,OAAQD,IAC1CgiB,GAA0BF,EAAoB9hB,GAEhD,OAAOgiB,CACT,CA6FA,SAASG,IAGP,IAAIC,EACFnO,EACA0L,EAAMC,EACNyC,EAAMC,EACN/R,EAAIC,EA2CN,OApCA,SAAiBnP,EAAGrB,EAAGuiB,EAAKC,GAC1B,IACIxnB,EAAGC,EADHwnB,GAAwB,EAe5B,IAZIL,IAAoBI,EAAqBnD,SAC3C+C,EAAkBI,EAAqBnD,OACvCpL,EAASuO,EAAqBvO,SAC9BwO,GAAwB,GAGtBA,IAkBJ9C,EAAO1L,EAAO,GAAG,GACjB2L,EAAO3L,EAAO,GAAG,GACjBoO,EAAOpO,EAAO,GAAG,GACjBqO,EAAOrO,EAAO,GAAG,GACjB1D,EAAKqP,EAAOD,EACZnP,EAAK8R,EAAOD,GAnBZrnB,EAAI2kB,EAAOpP,EAAKiS,EAAqBjD,MAArBiD,GAChBvnB,EAAIonB,EAAO7R,EAAKgS,EAAqBjD,MAArBiD,IACRrB,EAAUuB,gBAAgBN,EAAiB,CAACpnB,EAAGC,KACrDD,EAAI2kB,EAAOpP,EAAKiS,EAAqBjD,MAArBiD,GAChBvnB,EAAIonB,EAAO7R,EAAKgS,EAAqBjD,MAArBiD,GAElB,MAAO,CAACxnB,EAAGC,EACb,CAgBF,CAyHA,SAAS0nB,IAEP,IAAIP,EACFQ,EACAC,EACAC,EACAC,EAkCF,OA5BA,SAA0B1hB,EAAGrB,EAAGuiB,EAAKC,GACnC,IAAIC,GAAwB,EAc5B,OAbIL,IAAoBI,EAAqBnD,SAC3C+C,EAAkBI,EAAqBnD,OACvCoD,IAAyB,GAEvBG,IAAcL,IAChBK,EAAYL,EACZE,IAAyB,GAGvBA,IAYJI,EAAYD,EAAU3iB,OACtB6iB,EAAY3B,EAAU6B,YAAYZ,GAClCW,EAAkBD,EAAYD,EAAY,GAVnCE,CACT,CAaF,CAKA,SAASE,EAAkBC,GACzBzrB,KAAKyrB,QAAUA,EACfzrB,KAAK0rB,OAAQ,IAAI/P,OAAQ+P,KAC3B,CApTA5B,EAAqB/f,UAAU0E,MAAQ,WASrC,OARAzO,KAAKkqB,cAAgB7P,IACrBra,KAAKmqB,WAAa9P,IAClBra,KAAKoqB,cAAgB,GACrBpqB,KAAK+pB,oBAAsBC,EAC3BhqB,KAAKqqB,oBAAsBC,EAA4BtqB,KAAK+pB,qBAC5D/pB,KAAKuqB,uBAAyBC,EAA8BxqB,KAAKqqB,qBACjErqB,KAAKiqB,mBAAqB5P,IAEnBra,IACT,EAEA8pB,EAAqB/f,UAAUxC,MAAQ,WAKrC,OAJAvH,KAAKkqB,cAAgB7P,IACrBra,KAAKmqB,WAAa9P,IAClBra,KAAKoqB,cAAgB,GAEdpqB,IACT,EAEA8pB,EAAqB/f,UAAUvB,OAAS,SAAUiQ,GAChD,OAAKC,UAAUlQ,QAEXmjB,SAASlT,GAAG,GACdzY,KAAK+pB,oBAAsB1Y,KAAKiL,MAAMqP,SAASlT,IAC/CzY,KAAKqqB,oBAAsBC,EAA4BtqB,KAAK+pB,qBAC5D/pB,KAAKuqB,uBAAyBC,EAA8BxqB,KAAKqqB,sBAEjEuB,QAAQC,KAAK,kFAAkFpT,GAE1FzY,MATyBA,KAAK+pB,mBAUvC,EAEAD,EAAqB/f,UAAUshB,UAAY,SAAU5S,GACnD,OAAKC,UAAUlQ,QAEXsjB,WAAWrT,GAAG,EAChBzY,KAAKiqB,mBAAqB6B,WAAWrT,GAErCmT,QAAQC,KAAK,oFAAoFpT,GAE5FzY,MAPyBA,KAAKiqB,kBAQvC,EAEAH,EAAqB/f,UAAUmS,IAAM,SAAU6P,GAC7C,IAAIC,EAAuBC,EAe3B,OAbAD,EAAwBhsB,KAAKkqB,cAC7BlqB,KAAKkqB,cAAgB6B,EAChB7T,MAAM8T,KACTC,EAAqBjsB,KAAKmqB,WAC1BnqB,KAAKmqB,WAAuBnqB,KAAKkqB,eAAe8B,EAlF/B,GAAK,GAoFnB9T,MAAM+T,IACTjsB,KAAKoqB,cAAc8B,QAAQlsB,KAAKmqB,YAAY8B,GAG1CjsB,KAAKoqB,cAAc5hB,OAAOxI,KAAK+pB,qBACjC/pB,KAAKoqB,cAAc9gB,MAEdtJ,IACT,EAEA8pB,EAAqB/f,UAAU8a,MAAQ,WACrC,IAAIsH,EAAsB,EAG1B,GAAInsB,KAAKoqB,cAAc5hB,OAASxI,KAAK+pB,oBAAuB,OAAO,EACnE,GAAI/pB,KAAKkqB,cAAgBlqB,KAAKiqB,mBAAmB,GAAM,OAAO,EAE9D,IAAI,IAAI1hB,EAAE,EAAGA,EAAEvI,KAAK+pB,oBAAqBxhB,IACnCvI,KAAKoqB,cAAc7hB,KACrB4jB,GAAuBnsB,KAAKqqB,oBAAoB9hB,IAYpD,OARQ4jB,EAAoBnsB,KAAKuqB,sBASnC,EAiOAiB,EAAkBzhB,UAAU6O,KAAO,oBACnC4S,EAAkBzhB,UAAY,IAAI4R,MA+elC8N,EAAQsB,qBA7eR,SAA8BpiB,GAE5B,IAyBEyiB,EACAC,EACAe,EACAC,EACA5E,EACAsE,EAEAO,EAGE1V,EAhCA2V,EAAelb,KAAKgG,OACpBmV,EAA2B9B,IAC3B+B,EAAyBvB,IAEzBwB,GAD0BhC,IAChB,OAIVjM,EAAS,SAAU7U,GACrB,OAAOA,EAAE6U,MACX,EACIxZ,EAd4B,IAe5BC,EAd8B,GAe9BF,EAd2B,IAe3B8iB,EAAOyE,EACPI,EAAkBH,EAClBI,EAAgBH,EAGhBI,EAAkBhD,EAAkBgD,kBACtCC,EAAuB,IAAIhD,EAC3BiD,GAAmB,EAYnB5V,EAAUwS,EAAQvS,MAAMrW,GACxBmG,EAAQ0iB,EAAWxY,SAAS,OAAQ,OAItC,MAAM4b,EAA8B,EAC9BC,EAA0C,IAChD,IAAIC,EAIJ,SAASC,EAAIvjB,GACX,OAAOyH,KAAK2F,IAAIpN,EAAG,EACrB,CAEA,SAASwjB,EAAgBC,EAAIC,GAC3B,OAAOH,EAAIG,EAAG/pB,EAAI8pB,EAAG9pB,GAAK4pB,EAAIG,EAAG9pB,EAAI6pB,EAAG7pB,EAC1C,CAkJA,SAASzC,IACPyW,IACAtQ,EAAMoD,KAAK,OAAQsM,GACf0V,IACFnV,EAAQM,OACRvQ,EAAMoD,KAAK,MAAOsM,GAEtB,CAIA,SAASY,IACF8U,IACCS,GACFQ,IAEF9F,EA8EJ,SAAeA,EAAU+F,GACvB,IAAIC,EAOJ,GAgBF,SAAwBhG,EAAU+F,GAChC,IAE0B5jB,EAAGoe,EAAS0F,EAAUC,EAAU7U,EAAIC,EAF1D6U,EAAe,GAKnBhkB,EAAI,EAJoB,GAGqB4jB,EAE7C,IAAK,IAAIjlB,EAAI,EAAGA,EAAI6iB,EAAW7iB,IAE7BmlB,GADA1F,EAAUP,EAASlf,IACA8f,KAAKwF,eAGxB/U,GAFA6U,EAAWjE,EAAUoE,gBAAgB9F,IAEvB,GAAK0F,EAASnqB,EAC5BwV,EAAK4U,EAAS,GAAKD,EAASlqB,EAG5BsV,GAAMlP,EACNmP,GAAMnP,EAGN8jB,EAASnqB,GAAKuV,EACd4U,EAASlqB,GAAKuV,EAEd6U,EAAannB,KAAKinB,GAGpBR,EAAmBU,EACrB,CAhDEG,CAAetG,EAAU+F,GACzBC,EAAmBhG,EAAS1L,KAAI,SAAUiK,GACxC,OAAOA,EAAEqC,KAAKwF,cAChB,KACApG,EAAWoF,EAAgBY,IACdjlB,OAAS4iB,EACpB,MAAM,IAAII,EAAkB,+DAQ9B,GAoCF,SAAsB/D,EAAU+F,GAC9B,IAEIV,EAAsB9E,EAAS0F,EAAUM,EAAaC,EAAYC,EAFlEN,EAAe,GACjBO,EAAsB,GAGxBrB,EAAuBqB,EAAsBX,EAC7C,IAAK,IAAIjlB,EAAI,EAAGA,EAAI6iB,EAAW7iB,IAE7BmlB,GADA1F,EAAUP,EAASlf,IACA8f,KAAKwF,eACxBG,EAActE,EAAU6B,YAAYvD,GACpCiG,EAAaP,EAASU,aAAeJ,EAGrCC,EAAa5c,KAAKI,IAAIwc,EAAY,EAAIE,EAAsBrB,GAC5DmB,EAAa5c,KAAKC,IAAI2c,EAAY,EAAIE,EAAsBrB,GAG5DoB,EAAgBR,EAASjP,OAASwP,EAClCC,EAAgB7c,KAAKI,IAAIyc,EAAexB,GAExCgB,EAASjP,OAASyP,EAElBN,EAAannB,KAAKinB,GAGpBR,EAAmBU,EACrB,CAnEES,CAAa5G,EAAU+F,GACvBC,EAAmBhG,EAAS1L,KAAI,SAAUiK,GACxC,OAAOA,EAAEqC,KAAKwF,cAChB,KACApG,EAAWoF,EAAgBY,IACdjlB,OAAS4iB,EACpB,MAAM,IAAII,EAAkB,+DAG9B,OAAO/D,CACT,CApGe6G,CAAM7G,EAAUqF,EAAqBjI,SAChDwH,IACAN,EAoPJ,SAA0BtE,GAIxB,IAFA,IACIO,EAAS0F,EAAUM,EADnBO,EAAe,EAEVhmB,EAAI,EAAGA,EAAI6iB,EAAW7iB,IAE7BmlB,GADA1F,EAAUP,EAASlf,IACA8f,KAAKwF,eACxBG,EAActE,EAAU6B,YAAYvD,GACpCuG,GAAgBld,KAAKqN,IAAIgP,EAASU,aAAeJ,GAEnD,OAAOO,CACT,CA/PgBC,CAAiB/G,GAC7BqF,EAAqB5Q,IAAI6P,GAEzBO,EADYP,EAAYK,GACHC,GAAkBnnB,EAG3C,CAGA,SAASqoB,KAwPT,WACE,OAAQP,GACN,KAAK,EACHE,EAAqBuB,EACrB,MACF,KAAK,EACHvB,EAAqBwB,EACrB,MACF,QACE9C,QAAQ+C,MAAM,0DACdzB,EAAqBuB,EAE3B,EAlQEG,GAGAxD,EAAYziB,EAAKH,OACjB6iB,EAAYha,KAAKqN,IAAIgL,EAAU6B,YAAYsB,EAAgBjF,SAC3DwE,EAAoBnnB,EAAmBomB,EACvCyB,EAAqBvlB,QAAQ8jB,UAAUA,GAEvCgB,EAAiB,EAEjB5E,EAKF,SAAoB9e,EAAMiO,GACxB,IAIaiY,EADXC,EAHcnmB,EAAKomB,QAAO,SAAUtd,EAAK7H,GACvC,OAAOyH,KAAKI,IAAIA,EAAKgN,EAAO7U,GAC9B,IAAG,KAC4B5E,EAmBjC,OAFA6pB,EAKF,SAAyBG,EAAYpY,GACnC,IAGI+V,EAHAsC,EAAcD,EAAWD,QAAO,SAAUG,EAAKC,GACjD,OAAQD,EAAOC,EAAG1Q,MACpB,GAAG,GAGH,OAAOuQ,EAAWjT,KAAI,SAAUoT,EAAI5mB,EAAG6mB,GAOrC,OANAzC,EAAkBwC,EAAGxC,gBAEhBjD,EAAUuB,gBAAgB4B,EAAgBjF,OAAQ+E,KACrDA,EAAkBH,EAAyB2C,EAAI5mB,EAAG6mB,EAAKxY,IAGlD,CACLvM,MAAO8kB,EAAG9kB,MACV+jB,aAAe/C,EAAY8D,EAAG1Q,OAAUwQ,EACxCtmB,KAAMwmB,EACN5rB,EAAGopB,EAAgB,GACnBnpB,EAAGmpB,EAAgB,GACnBlO,OAAQ0Q,EAAGvC,cAEf,GACF,CA3BcyC,CAbF1mB,EAAKoT,KAAI,SAAUnS,EAAGrB,EAAGuiB,GACjC,MAAO,CACLzgB,MAAO9B,EACPkW,OAAQpN,KAAKI,IAAIgN,EAAO7U,GAAIklB,GAC5BnC,gBAAiBA,EAAgB/iB,EAAGrB,EAAGuiB,EAAKlU,GAC5CgW,cAAeA,EAAchjB,EAAGrB,EAAGuiB,EAAKlU,GACxC0Y,aAAc1lB,EAElB,IAKqCgN,GACrCsW,EAAmB2B,GACZhC,EAAgBgC,EACzB,CA7BatW,CAAW5P,EAAMiO,GAC5B0V,GAAQ,EACRS,GAAmB,CACrB,CAsIA,SAAS0B,EAAoBI,GAC3B,IACIU,EAAYC,EAAKC,EAAKC,EAAWC,EAAUC,EAAM1B,EADjD2B,EAAW,EAEf,EAAG,CACD,GAAIA,EAAW5C,EACb,MAAM,IAAIzB,EAAkB,2CAE9B+D,GAAa,EACb,IAAK,IAAIhnB,EAAI,EAAGA,EAAI6iB,EAAW7iB,IAAK,CAClCinB,EAAMX,EAAUtmB,GAChB,IAAK,IAAIkR,EAAIlR,EAAI,EAAGkR,EAAI2R,EAAW3R,IAUjC,GATAgW,EAAMZ,EAAUpV,GACZ+V,EAAI/Q,OAASgR,EAAIhR,QACnBiR,EAAYF,EACZG,EAAWF,IAEXC,EAAYD,EACZE,EAAWH,IAEbI,EAAOxC,EAAgBoC,EAAKC,IACjBC,EAAUjR,OAASkR,EAASlR,OAAQ,CAG7CyP,EAAgB0B,EAAOD,EAASlR,OAAS,EACzCyP,EAAgB7c,KAAKI,IAAIyc,EAAexB,GACxCgD,EAAUjR,OAASyP,EACnBqB,GAAa,EACbM,IACA,KACF,CAEF,GAAIN,EACF,KAEJ,CACF,OAASA,EAOX,CAGA,SAASb,EAAoBG,GAC3B,IACIU,EAAYC,EAAKC,EAAKC,EAAWC,EAAUC,EAAME,EADjDD,EAAW,EAEf,EAAG,CACD,GAAIA,EAAW5C,EACb,MAAM,IAAIzB,EAAkB,2CAE9B+D,GAAa,EACb,IAAK,IAAIhnB,EAAI,EAAGA,EAAI6iB,EAAW7iB,IAAK,CAClCinB,EAAMX,EAAUtmB,GAChB,IAAK,IAAIkR,EAAIlR,EAAI,EAAGkR,EAAI2R,EAAW3R,IAUjC,GATAgW,EAAMZ,EAAUpV,GACZ+V,EAAI/Q,OAASgR,EAAIhR,QACnBiR,EAAYF,EACZG,EAAWF,IAEXC,EAAYD,EACZE,EAAWH,IAEbI,EAAOxC,EAAgBoC,EAAKC,IACjBC,EAAUjR,OAASkR,EAASlR,OAAQ,CAC7CqR,EAAaJ,EAAUjR,OAASkR,EAASlR,OAASmR,EAClDD,EAASlR,QAAUqR,EAAapD,EAChC6C,GAAa,EACbM,IACA,KACF,CAEF,GAAIN,EACF,KAEJ,CACF,OAASA,EAOX,CA6BA,OA5aA3Y,EAAa,CACXY,KAAMA,EAENgB,QAAS,WAEP,OADArB,EAAQqB,QAAQzX,GACT6V,CACT,EAEAa,KAAM,WAEJ,OADAN,EAAQM,OACDb,CACT,EAEA6H,OAAQ,SAAUhG,GAChB,OAAKC,UAAUlQ,QAIfiW,EAAShG,EACTsU,GAAmB,EACZnW,GALE6H,CAMX,EAEAxZ,iBAAkB,SAAUwT,GAC1B,OAAKC,UAAUlQ,QAIfvD,EAAmBwT,EACnBsU,GAAmB,EACZnW,GALE3R,CAMX,EAEAC,kBAAmB,SAAUuT,GAC3B,OAAKC,UAAUlQ,QAIftD,EAAoBuT,EACb7B,GAJE1R,CAKX,EAEAF,eAAgB,SAAUyT,GACxB,OAAKC,UAAUlQ,QAIfxD,EAAiByT,EACjBsU,GAAmB,EACZnW,GALE5R,CAMX,EAEA4iB,KAAM,SAAUnP,GACd,OAAKC,UAAUlQ,QAIfqkB,EAAgBjF,KAAKnP,GACrBsU,GAAmB,EACZnW,GALEiW,EAAgBjF,MAM3B,EAEApL,OAAQ,SAAU/D,GAChB,OAAKC,UAAUlQ,QAIfqkB,EAAgBrQ,OAAO/D,GACvBsU,GAAmB,EACZnW,GALEiW,EAAgBrQ,QAM3B,EAEAU,KAAM,SAAUzE,GACd,OAAKC,UAAUlQ,QAIfqkB,EAAgB3P,KAAKzE,GACrBsU,GAAmB,EACZnW,GALEiW,EAAgB3P,MAM3B,EAEA4K,KAAM,SAAUrP,GACd,OAAKC,UAAUlQ,QAIfsf,EAAOrP,EACPsU,GAAmB,EACZnW,GALEkR,CAMX,EAEA6E,gBAAiB,SAAUlU,GACzB,OAAKC,UAAUlQ,QAIfmkB,EAAkBlU,EAClBsU,GAAmB,EACZnW,GALE+V,CAMX,EAEAC,cAAe,SAAUnU,GACvB,OAAKC,UAAUlQ,QAIfokB,EAAgBnU,EAChBsU,GAAmB,EACZnW,GALEgW,CAMX,EAEAmD,MAAO,WAIL,OAHIhD,GACFQ,IAEK,CACLjB,MAAOA,EACPD,eAAgBA,EAChBpnB,iBAAkB8mB,EAAYV,EAC9B5D,SAAUA,EAEd,EAEAnhB,GAAI,SAAUsS,EAAMH,GAClB,OAAyB,IAArBC,UAAUlQ,OACLtB,EAAMZ,GAAGsS,IAGlB1R,EAAMZ,GAAGsS,EAAMH,GACR7B,EACT,GAySKA,CACT,EAGA6S,EAAQuG,gCAAkCtF,EAC1CjB,EAAQwG,6BA5pBR,WAEE,IACItF,EACFQ,EACA+E,EACAC,EACAC,EACAC,EANEzO,EAAa,EAajB,SAAS0O,EAAK1mB,EAAGrB,EAAGuiB,EAAKC,GACvB,IAAIC,GAAwB,EAiB5B,OAfIL,IAAoBI,EAAqBnD,SAC3C+C,EAAkBI,EAAqBnD,OACvCoD,IAAyB,GAEvBG,IAAcL,IAChBK,EAAYL,EACZE,IAAyB,GAGvBA,IA0BJmF,EAA0BzG,EAAUoE,gBAAgBnD,GACpDyF,EAKF,SAAiCG,EAAQ5F,GAOvC,IANA,IAII6F,EAJAC,EAAmBhsB,IACrBisB,EAAY,EACZC,EAAchG,EAAgBA,EAAgBniB,OAAS,GACvDooB,EAAcjG,EAAgB+F,GAGzBA,EAAY/F,EAAgBniB,SACjCgoB,EAAsBK,EAAUN,EAAQI,EAAaC,IAC3BH,IACxBA,EAAmBD,GAGrBG,EAAcC,EACdA,EAAcjG,IAFd+F,GAKF,OAAOD,CACT,CAvBuBK,CAAwBX,EAAyBxF,GAAmB,EACzFuF,EAAkB/E,EAAU3iB,OAC5B6nB,EAAmB,EAAIhf,KAAK4C,GAAKic,GAvB1B,CACLC,EAAwB,GAAK9e,KAAK+G,IAAIwJ,EAAarZ,EAAI8nB,GAAoBD,EAA6D,MAAvCrF,EAAqBjD,MAArBiD,GAAgC,IACjIoF,EAAwB,GAAK9e,KAAKgH,IAAIuJ,EAAarZ,EAAI8nB,GAAoBD,EAA6D,MAAvCrF,EAAqBjD,MAArBiD,GAAgC,IAErI,CA2CA,SAAS8F,EAAUN,EAAQI,EAAaC,GACtC,IAiBIG,EAAIC,EAjBJztB,EAAIgtB,EAAO,GACb/sB,EAAI+sB,EAAO,GACXjd,EAAKqd,EAAY,GACjBpd,EAAKod,EAAY,GACjBlU,EAAKmU,EAAY,GACjBlU,EAAKkU,EAAY,GAGjBtc,EAAImI,EAAKnJ,EACT2d,EAAIvU,EAAKnJ,EAEP2d,EAAS5c,EAAIA,EAAI2c,EAAIA,EACrBE,GAAS,EAEC,GAAVD,IACFC,IATM5tB,EAAI+P,GAIEgB,GAHR9Q,EAAI+P,GAGY0d,GAKNC,GAIZC,EAAQ,GACVJ,EAAKzd,EACL0d,EAAKzd,GACI4d,EAAQ,GACjBJ,EAAKtU,EACLuU,EAAKtU,IAELqU,EAAKzd,EAAK6d,EAAQ7c,EAClB0c,EAAKzd,EAAK4d,EAAQF,GAGpB,IAAInY,EAAKvV,EAAIwtB,EACThY,EAAKvV,EAAIwtB,EACb,OAAO3f,KAAKsF,KAAKmC,EAAKA,EAAKC,EAAKA,EAClC,CAEA,OA7EAuX,EAAK1O,WAAa,SAAUnJ,GAC1B,OAAKC,UAAUlQ,QAIfoZ,EAAanJ,EACN6X,GAJE1O,CAKX,EAsEO0O,CACT,EAwiBA7G,EAAQ+B,kBAAoBA,EAE5BhH,OAAO4M,eAAe3H,EAAS,aAAc,CAAEhhB,OAAO,GAExD,CAl2BiE4oB,CAAQ5H,EAAS,EAAQ,MAAe,EAAQ,MAAa,EAAQ,MAAgB,EAAQ,M,kFCD9J,IAAI6H,EAAO,CAAC7oB,MAAO,QAEnB,SAAS2I,IACP,IAAK,IAAyCqR,EAArCla,EAAI,EAAGW,EAAIwP,UAAUlQ,OAAQiQ,EAAI,CAAC,EAAMlQ,EAAIW,IAAKX,EAAG,CAC3D,KAAMka,EAAI/J,UAAUnQ,GAAK,KAAQka,KAAKhK,GAAM,QAAQ8Y,KAAK9O,GAAI,MAAM,IAAI9G,MAAM,iBAAmB8G,GAChGhK,EAAEgK,GAAK,EACT,CACA,OAAO,IAAI+O,EAAS/Y,EACtB,CAEA,SAAS+Y,EAAS/Y,GAChBzY,KAAKyY,EAAIA,CACX,CAoDA,SAAS5S,EAAId,EAAM6T,GACjB,IAAK,IAA4BxN,EAAxB7C,EAAI,EAAGW,EAAInE,EAAKyD,OAAWD,EAAIW,IAAKX,EAC3C,IAAK6C,EAAIrG,EAAKwD,IAAIqQ,OAASA,EACzB,OAAOxN,EAAE3C,KAGf,CAEA,SAASpB,EAAItC,EAAM6T,EAAMzO,GACvB,IAAK,IAAI5B,EAAI,EAAGW,EAAInE,EAAKyD,OAAQD,EAAIW,IAAKX,EACxC,GAAIxD,EAAKwD,GAAGqQ,OAASA,EAAM,CACzB7T,EAAKwD,GAAK+oB,EAAMvsB,EAAOA,EAAK4c,MAAM,EAAGpZ,GAAGX,OAAO7C,EAAK4c,MAAMpZ,EAAI,IAC9D,KACF,CAGF,OADgB,MAAZ4B,GAAkBpF,EAAK0B,KAAK,CAACmS,KAAMA,EAAMnQ,MAAO0B,IAC7CpF,CACT,CA1DAysB,EAASznB,UAAYqH,EAASrH,UAAY,CACxCC,YAAawnB,EACblrB,GAAI,SAASmrB,EAAUtnB,GACrB,IAEIsY,EAd2BiP,EAY3BjZ,EAAIzY,KAAKyY,EACTkZ,GAb2BD,EAaOjZ,GAAfgZ,EAAW,IAZnBG,OAAOC,MAAM,SAAS9V,KAAI,SAAS0G,GAClD,IAAI7J,EAAO,GAAIrQ,EAAIka,EAAE0E,QAAQ,KAE7B,GADI5e,GAAK,IAAGqQ,EAAO6J,EAAEd,MAAMpZ,EAAI,GAAIka,EAAIA,EAAEd,MAAM,EAAGpZ,IAC9Cka,IAAMiP,EAAMI,eAAerP,GAAI,MAAM,IAAI9G,MAAM,iBAAmB8G,GACtE,MAAO,CAAC1d,KAAM0d,EAAG7J,KAAMA,EACzB,KASMrQ,GAAK,EACLW,EAAIyoB,EAAEnpB,OAGV,KAAIkQ,UAAUlQ,OAAS,GAAvB,CAOA,GAAgB,MAAZ2B,GAAwC,mBAAbA,EAAyB,MAAM,IAAIwR,MAAM,qBAAuBxR,GAC/F,OAAS5B,EAAIW,GACX,GAAIuZ,GAAKgP,EAAWE,EAAEppB,IAAIxD,KAAM0T,EAAEgK,GAAKpb,EAAIoR,EAAEgK,GAAIgP,EAAS7Y,KAAMzO,QAC3D,GAAgB,MAAZA,EAAkB,IAAKsY,KAAKhK,EAAGA,EAAEgK,GAAKpb,EAAIoR,EAAEgK,GAAIgP,EAAS7Y,KAAM,MAG1E,OAAO5Y,IAVP,CAFE,OAASuI,EAAIW,OAAQuZ,GAAKgP,EAAWE,EAAEppB,IAAIxD,QAAU0d,EAAI5c,EAAI4S,EAAEgK,GAAIgP,EAAS7Y,OAAQ,OAAO6J,CAa/F,EACA5W,KAAM,WACJ,IAAIA,EAAO,CAAC,EAAG4M,EAAIzY,KAAKyY,EACxB,IAAK,IAAIgK,KAAKhK,EAAG5M,EAAK4W,GAAKhK,EAAEgK,GAAGd,QAChC,OAAO,IAAI6P,EAAS3lB,EACtB,EACAvB,KAAM,SAASvF,EAAMqF,GACnB,IAAKlB,EAAIwP,UAAUlQ,OAAS,GAAK,EAAG,IAAK,IAAgCU,EAAGuZ,EAA/BsP,EAAO,IAAIxoB,MAAML,GAAIX,EAAI,EAASA,EAAIW,IAAKX,EAAGwpB,EAAKxpB,GAAKmQ,UAAUnQ,EAAI,GACnH,IAAKvI,KAAKyY,EAAEqZ,eAAe/sB,GAAO,MAAM,IAAI4W,MAAM,iBAAmB5W,GACrE,IAAuBwD,EAAI,EAAGW,GAAzBuZ,EAAIziB,KAAKyY,EAAE1T,IAAoByD,OAAQD,EAAIW,IAAKX,EAAGka,EAAEla,GAAGE,MAAMwS,MAAM7Q,EAAM2nB,EACjF,EACA9W,MAAO,SAASlW,EAAMqF,EAAM2nB,GAC1B,IAAK/xB,KAAKyY,EAAEqZ,eAAe/sB,GAAO,MAAM,IAAI4W,MAAM,iBAAmB5W,GACrE,IAAK,IAAI0d,EAAIziB,KAAKyY,EAAE1T,GAAOwD,EAAI,EAAGW,EAAIuZ,EAAEja,OAAQD,EAAIW,IAAKX,EAAGka,EAAEla,GAAGE,MAAMwS,MAAM7Q,EAAM2nB,EACrF,GAsBF,O,mMCnFA,IAIIC,EACAC,EALA,EAAQ,EACRC,EAAU,EACVC,EAAW,EACXC,EAAY,IAGZC,EAAY,EACZC,EAAW,EACXC,EAAY,EACZC,EAA+B,iBAAhBC,aAA4BA,YAAYC,IAAMD,YAAcE,KAC3EC,EAA6B,iBAAXC,QAAuBA,OAAOC,sBAAwBD,OAAOC,sBAAsBxyB,KAAKuyB,QAAU,SAASE,GAAKC,WAAWD,EAAG,GAAK,EAElJ,SAASL,IACd,OAAOJ,IAAaM,EAASK,GAAWX,EAAWE,EAAME,MAAQH,EACnE,CAEA,SAASU,IACPX,EAAW,CACb,CAEO,SAASY,IACdlzB,KAAKmzB,MACLnzB,KAAKozB,MACLpzB,KAAKqzB,MAAQ,IACf,CAyBO,SAASjc,EAAMjN,EAAUmpB,EAAOC,GACrC,IAAI9Q,EAAI,IAAIyQ,EAEZ,OADAzQ,EAAEjK,QAAQrO,EAAUmpB,EAAOC,GACpB9Q,CACT,CAEO,SAAS+Q,IACdd,MACE,EAEF,IADA,IAAkBe,EAAdhR,EAAIuP,EACDvP,IACAgR,EAAInB,EAAW7P,EAAE2Q,QAAU,GAAG3Q,EAAE0Q,MAAM7oB,KAAK,KAAMmpB,GACtDhR,EAAIA,EAAE4Q,QAEN,CACJ,CAEA,SAASK,IACPpB,GAAYD,EAAYG,EAAME,OAASH,EACvC,EAAQL,EAAU,EAClB,IACEsB,GACF,CAAE,QACA,EAAQ,EAWZ,WAEE,IADA,IAAIG,EAAmBC,EAAfC,EAAK7B,EAAcuB,EAAO9uB,IAC3BovB,GACDA,EAAGV,OACDI,EAAOM,EAAGT,QAAOG,EAAOM,EAAGT,OAC/BO,EAAKE,EAAIA,EAAKA,EAAGR,QAEjBO,EAAKC,EAAGR,MAAOQ,EAAGR,MAAQ,KAC1BQ,EAAKF,EAAKA,EAAGN,MAAQO,EAAK5B,EAAW4B,GAGzC3B,EAAW0B,EACXG,EAAMP,EACR,CAvBIQ,GACAzB,EAAW,CACb,CACF,CAEA,SAAS0B,IACP,IAAItB,EAAMF,EAAME,MAAOY,EAAQZ,EAAML,EACjCiB,EAAQlB,IAAWG,GAAae,EAAOjB,EAAYK,EACzD,CAiBA,SAASoB,EAAMP,GACT,IACArB,IAASA,EAAU+B,aAAa/B,IACxBqB,EAAOjB,EACP,IACNiB,EAAO9uB,MAAUytB,EAAUc,WAAWU,EAAMH,EAAOf,EAAME,MAAQH,IACjEJ,IAAUA,EAAW+B,cAAc/B,MAElCA,IAAUE,EAAYG,EAAME,MAAOP,EAAWgC,YAAYH,EAAM5B,IACrE,EAAQ,EAAGQ,EAASc,IAExB,CC3Ge,WAASvpB,EAAUmpB,EAAOC,GACvC,IAAI9Q,EAAI,IAAIyQ,EAMZ,OALAI,EAAiB,MAATA,EAAgB,GAAKA,EAC7B7Q,EAAEjK,SAAQ4b,IACR3R,EAAEhL,OACFtN,EAASiqB,EAAUd,EAAM,GACxBA,EAAOC,GACH9Q,CACT,CCRe,WAAStY,EAAUmpB,EAAOC,GACvC,IAAI9Q,EAAI,IAAIyQ,EAAOmB,EAAQf,EAC3B,OAAa,MAATA,GAAsB7Q,EAAEjK,QAAQrO,EAAUmpB,EAAOC,GAAO9Q,IAC5DA,EAAE6R,SAAW7R,EAAEjK,QACfiK,EAAEjK,QAAU,SAASrO,EAAUmpB,EAAOC,GACpCD,GAASA,EAAOC,EAAe,MAARA,EAAeb,KAASa,EAC/C9Q,EAAE6R,UAAS,SAAS9c,EAAK4c,GACvBA,GAAWC,EACX5R,EAAE6R,SAAS9c,EAAM6c,GAASf,EAAOC,GACjCppB,EAASiqB,EACX,GAAGd,EAAOC,EACZ,EACA9Q,EAAEjK,QAAQrO,EAAUmpB,EAAOC,GACpB9Q,EACT,CFUAyQ,EAAMnpB,UAAYqN,EAAMrN,UAAY,CAClCC,YAAakpB,EACb1a,QAAS,SAASrO,EAAUmpB,EAAOC,GACjC,GAAwB,mBAAbppB,EAAyB,MAAM,IAAIoqB,UAAU,8BACxDhB,GAAgB,MAARA,EAAeb,KAASa,IAAkB,MAATD,EAAgB,GAAKA,GACzDtzB,KAAKqzB,OAASpB,IAAajyB,OAC1BiyB,EAAUA,EAASoB,MAAQrzB,KAC1BgyB,EAAWhyB,KAChBiyB,EAAWjyB,MAEbA,KAAKmzB,MAAQhpB,EACbnK,KAAKozB,MAAQG,EACbO,GACF,EACArc,KAAM,WACAzX,KAAKmzB,QACPnzB,KAAKmzB,MAAQ,KACbnzB,KAAKozB,MAAQ3uB,IACbqvB,IAEJ,E,wBG1CM,SAAUrK,EAAQ+K,GAAgB,aAwKxC/K,EAAQ/B,eAtKR,WAEE,IAGI6E,EAAelb,KAAKgG,OAIpBuQ,EAAO,CACT,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,IAEFpL,EAAS,CACX,CAAC,EAAG,GACJ,CAAC,EAAG,IAEFU,EAAO,CAAC,EAAG,GACXjY,EAlB4B,IAmB5BC,EAlB8B,GAmB9BF,EAlB2B,IAmB3B8iB,EAAOyE,EAWPkI,EAA0CD,EAAazJ,qBAR9B,CAC3B,CACEtM,OAAQ,GAEV,CACEA,OAAQ,KAG4FhH,OAOxG,SAASid,EAAgBC,GACvBC,EAAQhN,EAAM+M,EAChB,CAoFA,SAASC,EAAQjK,EAAiBtiB,GAChC,IAAIuO,EAKJ,GAFAvO,EAAK2f,QAAU2C,EAEI,GAAftiB,EAAK1H,OAAa,CAgBpB,IAHA,IAAIovB,GAXJnZ,EAAa4d,EAAazJ,qBAAqB1iB,EAAKf,UACjDsgB,KAAK+C,GACLlM,QAAO,SAAU7U,GAChB,OAAOA,EAAEnB,KACX,IACCxD,iBAAiBA,GACjBC,kBAAkBA,GAClBF,eAAeA,GACf8iB,KAAKA,GACLrQ,QAEoBsY,SAGfA,EAAMzD,OACZ1V,EAAWY,OACXuY,EAAQnZ,EAAWmZ,QAKrBA,EAAMtI,SAAS9P,SAAQ,SAAUkd,GAC/BD,EAAQC,EAAIA,EAAGxM,KAAKwF,eAAellB,KAAK2mB,aAC1C,GAEF,CACF,CAEA,OAtHAoF,EAAgBzvB,iBAAmB,SAAUwT,GAC3C,OAAKC,UAAUlQ,QAIfvD,EAAmBwT,EACZic,GAJEzvB,CAKX,EAEAyvB,EAAgBxvB,kBAAoB,SAAUuT,GAC5C,OAAKC,UAAUlQ,QAIftD,EAAoBuT,EACbic,GAJExvB,CAKX,EAEAwvB,EAAgB1vB,eAAiB,SAAUyT,GACzC,OAAKC,UAAUlQ,QAIfxD,EAAiByT,EACVic,GAJE1vB,CAKX,EAEA0vB,EAAgB9M,KAAO,SAAUnP,GAC/B,OAAKC,UAAUlQ,QAKfisB,EAAwC7M,KAAKnP,GAE7CmP,EAAO6M,EAAwC7M,OAC/CpL,EAASiY,EAAwCjY,SACjDU,EAAOuX,EAAwCvX,OACxCwX,GATE9M,CAUX,EAEA8M,EAAgBlY,OAAS,SAAU/D,GACjC,OAAKC,UAAUlQ,QAKfisB,EAAwCjY,OAAO/D,GAE/CmP,EAAO6M,EAAwC7M,OAC/CpL,EAASiY,EAAwCjY,SACjDU,EAAOuX,EAAwCvX,OACxCwX,GATElY,CAUX,EAEAkY,EAAgBxX,KAAO,SAAUzE,GAC/B,OAAKC,UAAUlQ,QAKfisB,EAAwCvX,KAAKzE,GAE7CmP,EAAO6M,EAAwC7M,OAC/CpL,EAASiY,EAAwCjY,SACjDU,EAAOuX,EAAwCvX,OACxCwX,GATExX,CAUX,EAEAwX,EAAgB5M,KAAO,SAAUrP,GAC/B,OAAKC,UAAUlQ,QAIfsf,EAAOrP,EACAic,GAJE5M,CAKX,EA0CO4M,CACT,EAIAlQ,OAAO4M,eAAe3H,EAAS,aAAc,CAAEhhB,OAAO,GAExD,CA/KiE4oB,CAAQ5H,EAAS,EAAQ,M,wBCGlF,SAAUA,EAAQqL,EAAQpL,GAAa,aAE7C,IAAIgD,EAAU,MAEd,SAASqI,EAAa7rB,GACpB,OAAOA,GAAKwjB,GAAWxjB,IAAMwjB,CAC/B,CAIA,SAASsI,EAAIC,EAAIC,GACf,OAAOD,EAAG1xB,EAAI2xB,EAAG3xB,EAAI0xB,EAAGzxB,EAAI0xB,EAAG1xB,EAAIyxB,EAAG1Y,EAAI2Y,EAAG3Y,CAC/C,CAKA,SAAS4Y,EAAgBF,EAAIC,GAC3B,OACEH,EAAaE,EAAG1xB,EAAI2xB,EAAG1xB,EAAIyxB,EAAGzxB,EAAI0xB,EAAG3xB,IACrCwxB,EAAaE,EAAGzxB,EAAI0xB,EAAG3Y,EAAI0Y,EAAG1Y,EAAI2Y,EAAG1xB,IACrCuxB,EAAaE,EAAG1Y,EAAI2Y,EAAG3xB,EAAI0xB,EAAG1xB,EAAI2xB,EAAG3Y,EAEzC,CA+CA,SAAS6Y,EAAK5rB,EAAM+G,GAClB,MAAO,CAACA,EAAG,GAAK/G,EAAK,GAAI+G,EAAG,GAAK/G,EAAK,GACxC,CAEA,SAAS6rB,EAAsBJ,EAAIC,GACjC,OAAOD,EAAG,GAAKC,EAAG,GAAKD,EAAG,GAAKC,EAAG,EACpC,CAIA,SAASI,EAAkBC,EAAMC,GAC/Bx1B,KAAKu1B,KAAOA,EACZv1B,KAAKw1B,KAAOA,EACZx1B,KAAKy1B,MAAQ,KACbz1B,KAAK01B,MAAQ,KACb11B,KAAK21B,MAAQ,KACb31B,KAAK41B,MAAQ,IACf,CAGA,SAASC,EAAcC,GACrB91B,KAAK81B,QAAUA,EACf91B,KAAK+1B,KAAO,IACd,CA6FA,SAASC,EAAQzyB,EAAGC,EAAG+Y,EAAGkC,EAAQwX,EAAMC,GACtCl2B,KAAKuD,EAAIA,EACTvD,KAAKwD,EAAIA,EACTxD,KAAKye,OAASiO,EACd1sB,KAAKqK,MAAQ,EACbrK,KAAKm2B,UAAY,IAAIN,GAAa,GAClC71B,KAAKo2B,WAAa,KAClBp2B,KAAKq2B,kBAAoB,KACzBr2B,KAAKgoB,QAAU,KACfhoB,KAAK6tB,eAAiB,KACtB7tB,KAAKk2B,SAAU,OAEFrtB,IAATotB,IACFj2B,KAAK6tB,eAAiBoI,GAETptB,MAAXqtB,IACFl2B,KAAKk2B,QAAUA,GAEH,MAAVzX,IACFze,KAAKye,OAASA,GAGdze,KAAKuc,EADE,MAALA,EACOA,EAEAvc,KAAKs2B,SAASt2B,KAAKuD,EAAGvD,KAAKwD,EAAGxD,KAAKye,OAEhD,CA0BA,SAAS8X,EAAShB,GAChB,IAAIiB,EAAKjB,EAAKkB,MAAM,GAChBC,EAAKnB,EAAKkB,MAAM,GAChBE,EAAKpB,EAAKkB,MAAM,GACpBz2B,KAAK+K,EAAIyrB,EAAGhzB,GAAKkzB,EAAGna,EAAEoa,EAAGpa,GAAKma,EAAGlzB,GAAKmzB,EAAGpa,EAAEia,EAAGja,GAAKoa,EAAGnzB,GAAKgzB,EAAGja,EAAEma,EAAGna,GACnEvc,KAAKgL,EAAIwrB,EAAGja,GAAKma,EAAGnzB,EAAEozB,EAAGpzB,GAAKmzB,EAAGna,GAAKoa,EAAGpzB,EAAEizB,EAAGjzB,GAAKozB,EAAGpa,GAAKia,EAAGjzB,EAAEmzB,EAAGnzB,GACnEvD,KAAKoL,EAAIorB,EAAGjzB,GAAKmzB,EAAGlzB,EAAEmzB,EAAGnzB,GAAKkzB,EAAGnzB,GAAKozB,EAAGnzB,EAAEgzB,EAAGhzB,GAAKmzB,EAAGpzB,GAAKizB,EAAGhzB,EAAEkzB,EAAGlzB,GACnExD,KAAK4J,GAAK,GAAK4sB,EAAGjzB,GAAKmzB,EAAGlzB,EAAEmzB,EAAGpa,EAAIoa,EAAGnzB,EAAEkzB,EAAGna,GAAKma,EAAGnzB,GAAKozB,EAAGnzB,EAAEgzB,EAAGja,EAAIia,EAAGhzB,EAAEmzB,EAAGpa,GAAKoa,EAAGpzB,GAAKizB,EAAGhzB,EAAEkzB,EAAGna,EAAIma,EAAGlzB,EAAEgzB,EAAGja,GAC/G,CAkBA,SAASqa,EAASrzB,EAAGC,GACnBxD,KAAKuD,EAAIA,EACTvD,KAAKwD,EAAIA,CACX,CAKA,SAASqzB,EAAQtzB,EAAGC,EAAG+Y,GACrBvc,KAAKuD,EAAIA,EACTvD,KAAKwD,EAAIA,EACTxD,KAAKuc,EAAIA,CACX,CAqBA,SAASua,EAAOb,EAAMc,EAAMxB,GAC1Bv1B,KAAKuK,KAAO,KACZvK,KAAKg3B,KAAO,KACZh3B,KAAKi3B,KAAO,KACZj3B,KAAKi2B,KAAOA,EACZj2B,KAAK+2B,KAAOA,EACZ/2B,KAAKk3B,MAAQ3B,CACf,CA8BA,SAAS4B,EAAuB1L,GAC9BzrB,KAAKyrB,QAAUA,EACfzrB,KAAK0rB,OAAQ,IAAI/P,OAAQ+P,KAC3B,CAMA,SAAS0L,EAAKrsB,EAAGC,EAAGI,EAAGisB,GACrBr3B,KAAKm2B,UAAY,IAAIN,GAAa,GAClC71B,KAAKy2B,MAAQ,CAAC1rB,EAAGC,EAAGI,GACpBpL,KAAKs3B,QAAS,EACd,IAAI7U,EAAI1X,EAAEwsB,SAASvsB,GAAGwsB,aAAaxsB,EAAEusB,SAASnsB,IAE9CpL,KAAKy3B,OAAS,IAAIZ,GAAQpU,EAAElf,GAAIkf,EAAEjf,GAAIif,EAAElG,GACxCvc,KAAKy3B,OAAOC,YACZ13B,KAAK23B,cACL33B,KAAK43B,UAAY,KAEH/uB,MAAVwuB,GACFr3B,KAAKq3B,OAAOA,EAEhB,CAwFA,SAASQ,IACP73B,KAAKsoB,OAAS,GACdtoB,KAAK83B,OAAS,GACd93B,KAAK+3B,QAAU,GACf/3B,KAAKg4B,QAAU,GACfh4B,KAAKsD,QAAU,GACftD,KAAKgM,QAAU,CACjB,CA4OA,SAASisB,EAAYrQ,EAAMsQ,GAoBzB,IAZA,IAAIC,EAIF1e,EACAhD,EAEAzL,EACAI,EACAxB,EACAwuB,EATAC,EAASC,EAAcJ,GACvB3vB,GAAK,EACLW,EAAI0e,EAAKpf,OAAS8vB,EAAc1Q,GAGhC7c,EAAI6c,EAAK1e,EAAI,KAMNX,EAAIW,GAAG,CAMd,IALAivB,EAAQD,EAAQvW,QAChBuW,EAAQ1vB,OAAS,EACjBwC,EAAI4c,EAAKrf,GACT6C,EAAI+sB,GAAO1hB,EAAI0hB,EAAM3vB,OAAS6vB,GAAU,GACxC5e,GAAK,IACIA,EAAIhD,GAEP8hB,EADJ3uB,EAAIuuB,EAAM1e,GACW1O,EAAGC,IACjButB,EAAcntB,EAAGL,EAAGC,KACvBotB,EAAeI,EAAiBptB,EAAGxB,EAAGmB,EAAGC,GACrCytB,SAASL,EAAa,KACxBF,EAAQzxB,KAAK2xB,IAGjBF,EAAQzxB,KAAKmD,IACJ2uB,EAAcntB,EAAGL,EAAGC,KAC7BotB,EAAeI,EAAiBptB,EAAGxB,EAAGmB,EAAGC,GACrCytB,SAASL,EAAa,KACxBF,EAAQzxB,KAAK2xB,IAGjBhtB,EAAIxB,EAEFyuB,GAAQH,EAAQzxB,KAAKyxB,EAAQ,IACjCntB,EAAIC,CACN,CAEA,OAAOktB,CACT,CAEA,SAASK,EAAcvS,EAAGjb,EAAGC,GAC3B,OAAQA,EAAE,GAAKD,EAAE,KAAOib,EAAE,GAAKjb,EAAE,KAAOC,EAAE,GAAKD,EAAE,KAAOib,EAAE,GAAKjb,EAAE,GACnE,CAIA,SAASytB,EAAiBptB,EAAGxB,EAAGmB,EAAGC,GACjC,IAAIsI,EAAKlI,EAAE,GACTwR,EAAK7R,EAAE,GACP2tB,EAAM9uB,EAAE,GAAK0J,EACbqlB,EAAM3tB,EAAE,GAAK4R,EACbrJ,EAAKnI,EAAE,GACPyR,EAAK9R,EAAE,GACP6tB,EAAMhvB,EAAE,GAAK2J,EACbslB,EAAM7tB,EAAE,GAAK6R,EACbic,GAAMH,GAAOplB,EAAKsJ,GAAMgc,GAAOvlB,EAAKsJ,KAAQic,EAAMH,EAAMC,EAAMC,GAChE,MAAO,CAACtlB,EAAKwlB,EAAKJ,EAAKnlB,EAAKulB,EAAKF,EACnC,CAGA,SAASN,EAAcrQ,GACrB,IAAIld,EAAIkd,EAAY,GAClBjd,EAAIid,EAAYA,EAAYzf,OAAS,GACvC,QAASuC,EAAE,GAAKC,EAAE,IAAMD,EAAE,GAAKC,EAAE,GACnC,CAGA,SAAS+tB,EAAqBC,GAC5B,IAAIC,EAAQ,GACRlyB,EAAWiyB,EAEX3Q,EADQ2Q,EAAKjC,KACAlJ,eACbuI,EAAa,GACjB,EAAG,CAED,IAAI8C,GADJnyB,EAAWA,EAASkwB,KAAKD,MACCf,KAAKpI,eAC1BqL,EAAWhD,SACdE,EAAW3vB,KAAKyyB,GAElB,IAAIhC,EAAQnwB,EAASmwB,MACjBA,EAAMiC,sBACRF,EAAMxyB,KAAKywB,EAEf,OAASnwB,IAAaiyB,GAEtB,OADA3Q,EAAK+N,WAAaA,EACX6C,CACT,CAjrBApD,EAAa9rB,UAAUmS,IAAM,SAASkd,GAClB,OAAdp5B,KAAK+1B,KACP/1B,KAAK+1B,KAAOqD,EAERp5B,KAAK81B,SACP91B,KAAK+1B,KAAKH,MAAQwD,EAClBA,EAAIzD,MAAQ31B,KAAK+1B,KACjB/1B,KAAK+1B,KAAOqD,IAEZp5B,KAAK+1B,KAAKL,MAAQ0D,EAClBA,EAAI3D,MAAQz1B,KAAK+1B,KACjB/1B,KAAK+1B,KAAOqD,EAGlB,EAEAvD,EAAa9rB,UAAUsvB,QAAU,WAC/B,OAAqB,OAAdr5B,KAAK+1B,IACd,EAGAF,EAAa9rB,UAAU2L,KAAO,SAASpS,GACrC,IAAItD,KAAK81B,QAAT,CAGA,IAAIwD,EAAOt5B,KAAK+1B,KAChB,GACEzyB,EAAQmD,KAAK6yB,EAAK/D,MAClB+D,EAAK/D,KAAK+B,QAAS,EACnBgC,EAAOA,EAAK7D,YACI,OAAT6D,EANT,CAOF,EAEAzD,EAAa9rB,UAAUkT,UAAY,WACjC,GAAIjd,KAAK81B,QAAS,CAChB,IAAIwD,EAAOt5B,KAAK+1B,KAChB,GACqB,OAAfuD,EAAK5D,MACY,OAAf4D,EAAK7D,MACP6D,EAAK9D,KAAKW,UAAUJ,KAAO,MAE3BuD,EAAK7D,MAAMC,MAAQ,KACnB4D,EAAK9D,KAAKW,UAAUJ,KAAOuD,EAAK7D,QAGhB,MAAd6D,EAAK7D,QACP6D,EAAK7D,MAAMC,MAAQ4D,EAAK5D,OAE1B4D,EAAK5D,MAAMD,MAAQ6D,EAAK7D,OAGd,OADZ6D,EAAOA,EAAK3D,SAEV2D,EAAK1D,MAAQ,YAEA,MAAR0D,EACX,KAAO,CACDA,EAAOt5B,KAAK+1B,KAChB,GACoB,MAAduD,EAAK1D,MACW,MAAd0D,EAAK3D,MACP2D,EAAK/D,KAAKY,UAAUJ,KAAO,MAE3BuD,EAAK3D,MAAMC,MAAQ,KACnB0D,EAAK/D,KAAKY,UAAUJ,KAAOuD,EAAK3D,QAGhB,MAAd2D,EAAK3D,QACP2D,EAAK3D,MAAMC,MAAQ0D,EAAK1D,OAE1B0D,EAAK1D,MAAMD,MAAQ2D,EAAK3D,OAGd,OADZ2D,EAAOA,EAAK7D,SAEV6D,EAAK5D,MAAQ,YACA,MAAR4D,EACX,CACF,EAGAzD,EAAa9rB,UAAUwvB,YAAc,WAGnC,IAFA,IAAIC,EAAO,GACTF,EAAOt5B,KAAK+1B,KACE,OAATuD,GACLE,EAAK/yB,KAAK6yB,EAAK9D,MACf8D,EAAOA,EAAK3D,MAEd,OAAO6D,CACT,EA+BAxD,EAAOjsB,UAAUusB,SAAW,SAAS/yB,EAAGC,EAAGib,GACzC,OAASlb,EAAEA,EAAMC,EAAEA,EAAKib,CAC1B,EAEAuX,EAAOjsB,UAAU0vB,UAAY,SAAShb,GACpCze,KAAKye,OAASA,EACdze,KAAKuc,EAAIvc,KAAKs2B,SAASt2B,KAAKuD,EAAGvD,KAAKwD,EAAGxD,KAAKye,OAC9C,EAEAuX,EAAOjsB,UAAUwtB,SAAW,SAAS/U,GACnC,OAAO,IAAIwT,EAAOxT,EAAEjf,EAAIvD,KAAKuD,EAAGif,EAAEhf,EAAIxD,KAAKwD,EAAGgf,EAAEjG,EAAIvc,KAAKuc,EAC3D,EAEAyZ,EAAOjsB,UAAUytB,aAAe,SAAShV,GACvC,OAAO,IAAIwT,EAAQh2B,KAAKwD,EAAIgf,EAAEjG,EAAMvc,KAAKuc,EAAIiG,EAAEhf,EAAKxD,KAAKuc,EAAIiG,EAAEjf,EAAMvD,KAAKuD,EAAIif,EAAEjG,EAAKvc,KAAKuD,EAAIif,EAAEhf,EAAMxD,KAAKwD,EAAIgf,EAAEjf,EACnH,EAEAyyB,EAAOjsB,UAAU2vB,OAAS,SAASlX,GACjC,OAAQxiB,KAAKuD,IAAMif,EAAEjf,GAAKvD,KAAKwD,IAAMgf,EAAEhf,GAAKxD,KAAKuc,IAAMiG,EAAEjG,CAC3D,EAeAga,EAAQxsB,UAAU4vB,cAAgB,WAChC,MAAO,CACC35B,KAAK+K,EAAI/K,KAAKoL,GAAnB,EACKpL,KAAKgL,EAAIhL,KAAKoL,GAAnB,EACKpL,KAAK4J,EAAI5J,KAAKoL,GAAnB,EAEL,EAGAmrB,EAAQxsB,UAAU6vB,0BAA4B,WAC5C,IAAIC,EAAS75B,KAAK25B,gBAElB,OADgB,IAAI/C,EAAQiD,EAAO,GAAG,EAAGA,EAAO,GAAG,EAErD,EAiBAhD,EAAO9sB,UAAU+vB,OAAS,WACxB95B,KAAKuD,IAAM,EACXvD,KAAKwD,IAAM,EACXxD,KAAKuc,IAAM,CACb,EAGAsa,EAAO9sB,UAAU2tB,UAAY,WAC3B,IAAIqC,EAAS1oB,KAAKsF,KAAM3W,KAAKuD,EAAIvD,KAAKuD,EAAMvD,KAAKwD,EAAIxD,KAAKwD,EAAMxD,KAAKuc,EAAIvc,KAAKuc,GAC1Ewd,EAAS,IACX/5B,KAAKuD,GAAKw2B,EACV/5B,KAAKwD,GAAKu2B,EACV/5B,KAAKuc,GAAKwd,EAEd,EAcAjD,EAAM/sB,UAAUiwB,UAAY,WAC1B,OAAqB,OAAdh6B,KAAKi3B,OAAkBj3B,KAAKk3B,MAAMI,QAAUt3B,KAAKi3B,KAAKC,MAAMI,MACrE,EAGAR,EAAM/sB,UAAUkwB,YAAc,SAASjC,GACrC,GAAIh4B,KAAKg6B,YAAa,CACpB,GAAIhC,EAAQxvB,OAAS,GAAKxI,OAASg4B,EAAQ,GACzC,OAEAA,EAAQvxB,KAAKzG,MACbA,KAAKuK,KAAK0vB,YAAYjC,EAE1B,MACoB,OAAdh4B,KAAKi3B,MACPj3B,KAAKi3B,KAAK1sB,KAAK0vB,YAAYjC,EAGjC,EAGAlB,EAAM/sB,UAAUmwB,QAAU,SAASC,EAAQpD,GACzC,OAAS/2B,KAAKi2B,KAAKyD,OAAOS,IAAWn6B,KAAK+2B,KAAK2C,OAAO3C,IAAW/2B,KAAKi2B,KAAKyD,OAAO3C,IAAS/2B,KAAK+2B,KAAK2C,OAAOS,EAC9G,EAUAhD,EAAuBptB,UAAU6O,KAAO,yBACxCue,EAAuBptB,UAAY,IAAI4R,MAoBvCyb,EAAKrtB,UAAUqwB,aAAe,WAC5B,GAAsB,MAAlBp6B,KAAK43B,UAAmB,CAC1B,IAAIyC,EAAU,IAAI9D,EAAQv2B,MAC1BA,KAAK43B,UAAYyC,EAAQT,2BAC3B,CACA,OAAO55B,KAAK43B,SACd,EAEAR,EAAKrtB,UAAUovB,mBAAqB,WAClC,OAAOn5B,KAAKy3B,OAAOlb,GAAK,qBAC1B,EAEA6a,EAAKrtB,UAAU4tB,YAAc,WAC3B33B,KAAKs6B,MAAQ,GACbt6B,KAAKs6B,MAAM,GAAK,IAAIxD,EAAM92B,KAAKy2B,MAAM,GAAIz2B,KAAKy2B,MAAM,GAAIz2B,MACxDA,KAAKs6B,MAAM,GAAK,IAAIxD,EAAM92B,KAAKy2B,MAAM,GAAIz2B,KAAKy2B,MAAM,GAAIz2B,MACxDA,KAAKs6B,MAAM,GAAK,IAAIxD,EAAM92B,KAAKy2B,MAAM,GAAIz2B,KAAKy2B,MAAM,GAAIz2B,MACxDA,KAAKs6B,MAAM,GAAG/vB,KAAOvK,KAAKs6B,MAAM,GAChCt6B,KAAKs6B,MAAM,GAAGtD,KAAOh3B,KAAKs6B,MAAM,GAChCt6B,KAAKs6B,MAAM,GAAG/vB,KAAOvK,KAAKs6B,MAAM,GAChCt6B,KAAKs6B,MAAM,GAAGtD,KAAOh3B,KAAKs6B,MAAM,GAChCt6B,KAAKs6B,MAAM,GAAG/vB,KAAOvK,KAAKs6B,MAAM,GAChCt6B,KAAKs6B,MAAM,GAAGtD,KAAOh3B,KAAKs6B,MAAM,EAClC,EAGAlD,EAAKrtB,UAAUstB,OAAS,SAAUA,GAChC,KAAMrC,EAAIh1B,KAAKy3B,OAAQJ,GAAUrC,EAAIh1B,KAAKy3B,OAAQz3B,KAAKy2B,MAAM,KAAM,CACjE,IAAI8D,EAAOv6B,KAAKy2B,MAAM,GACtBz2B,KAAKy2B,MAAM,GAAKz2B,KAAKy2B,MAAM,GAC3Bz2B,KAAKy2B,MAAM,GAAK8D,EAChBv6B,KAAKy3B,OAAOqC,SACZ95B,KAAK23B,aACP,CACF,EAGAP,EAAKrtB,UAAUywB,QAAU,SAAUvF,EAAIC,GACrC,IAAK,IAAI3sB,EAAI,EAAGA,EAAI,EAAGA,IACrB,GAAIvI,KAAKs6B,MAAM/xB,GAAG2xB,QAAQjF,EAAIC,GAC5B,OAAOl1B,KAAKs6B,MAAM/xB,GAGtB,OAAO,IACT,EAGA6uB,EAAKrtB,UAAUyI,KAAO,SAAU+iB,EAAMN,EAAIC,GACxC,GAAIK,aAAgB6B,EAAM,CAExB,GAAa,QADTH,EAAO1B,EAAKiF,QAAQvF,EAAIC,IAE1B,MAAM,IAAIiC,EAAuB,8BAGnC,GAAa,QADT6B,EAAOh5B,KAAKw6B,QAAQvF,EAAIC,IAE1B,MAAM,IAAIiC,EAAuB,8BAEnCF,EAAKA,KAAO+B,EACZA,EAAK/B,KAAOA,CACd,KAAO,CACL,IAAIA,EAAO1B,EACPyD,EAAOh5B,KAAKw6B,QAAQvD,EAAKhB,KAAMgB,EAAKF,MACxCE,EAAKA,KAAO+B,EACZA,EAAK/B,KAAOA,CACd,CACF,EAGAG,EAAKrtB,UAAU0wB,SAAW,SAAUjY,GAClC,OAAOwS,EAAIh1B,KAAKy3B,OAAQjV,GAAKwS,EAAIh1B,KAAKy3B,OAAQz3B,KAAKy2B,MAAM,IAAM/J,CACjE,EAEA0K,EAAKrtB,UAAU2wB,WAAa,WAC1B,IAAK,IAAInyB,EAAI,EAAGA,EAAI,EAAGA,IACrB,GAA2B,OAAvBvI,KAAKs6B,MAAM/xB,GAAG0uB,MAAiBj3B,KAAKs6B,MAAM/xB,GAAG0uB,KAAK+C,YACpD,OAAOh6B,KAAKs6B,MAAM/xB,GAGtB,OAAO,IACT,EAEA6uB,EAAKrtB,UAAU4wB,eAAiB,WAC9B36B,KAAKm2B,UAAUlZ,WACjB,EAYA4a,EAAW9tB,UAAU6wB,KAAO,SAAUC,EAAeC,GACnD96B,KAAKsoB,OAAS,GACd,IAAK,IAAI/f,EAAI,EAAGA,EAAIuyB,EAAMtyB,OAAQD,IAChCvI,KAAKsoB,OAAO/f,GAAK,IAAIytB,EAAO8E,EAAMvyB,GAAGhF,EAAGu3B,EAAMvyB,GAAG/E,EAAGs3B,EAAMvyB,GAAGgU,EAAG,KAAMue,EAAMvyB,IAAI,GAElFvI,KAAKsoB,OAAStoB,KAAKsoB,OAAO1gB,OAAOizB,EACnC,EAEAhD,EAAW9tB,UAAUgxB,UAAY,WAE/B,IADA,IACSxyB,EADOvI,KAAKsoB,OAAO9f,OACH,EAAGD,EAAI,EAAGA,IAAK,CACtC,IAAIyyB,EAAK3pB,KAAKiL,MAAMjL,KAAKgG,SAAW9O,GAChCgyB,EAAOv6B,KAAKsoB,OAAO0S,GACvBT,EAAKlwB,MAAQ9B,EACb,IAAI0yB,EAAcj7B,KAAKsoB,OAAO/f,GAC9B0yB,EAAY5wB,MAAQ2wB,EACpBh7B,KAAKsoB,OAAO/c,OAAOyvB,EAAI,EAAGC,GAC1Bj7B,KAAKsoB,OAAO/c,OAAOhD,EAAG,EAAGgyB,EAC3B,CACF,EAEC1C,EAAW9tB,UAAUmxB,KAAO,WAC3B,GAAIl7B,KAAKsoB,OAAO9f,QAAU,EACxB,MAAM,IAAI2uB,EAAuB,sBAEnC,IAAK,IAAI5uB,EAAI,EAAGA,EAAIvI,KAAKsoB,OAAO9f,OAAQD,IACtCvI,KAAKsoB,OAAO/f,GAAG8B,MAAQ9B,EAGzB,IAAI0sB,EAAIC,EAAIiG,EAAIC,EACZC,EAAIC,EAAIC,EAAIC,EAsDZhZ,EAhDJ,IALAyS,EAAKj1B,KAAKsoB,OAAO,GACjB4M,EAAKl1B,KAAKsoB,OAAO,GACjB6S,EAAKC,EAAK,KAGD7yB,EAAI,EAAGA,EAAIvI,KAAKsoB,OAAO9f,OAAQD,IACtC,IAAM4sB,EAAgBF,EAAIj1B,KAAKsoB,OAAO/f,MAAO4sB,EAAgBD,EAAIl1B,KAAKsoB,OAAO/f,IAAM,EACjF4yB,EAAKn7B,KAAKsoB,OAAO/f,IACd8B,MAAQ,EACXrK,KAAKsoB,OAAO,GAAGje,MAAQ9B,EACvBvI,KAAKsoB,OAAO/c,OAAOhD,EAAG,EAAGvI,KAAKsoB,OAAO,IACrCtoB,KAAKsoB,OAAO/c,OAAO,EAAG,EAAG4vB,GACzB,KACF,CAEF,GAAW,OAAPA,EACF,MAAM,IAAIhE,EAAuB,6CAMnC,IAFAqE,EAAK,IAAIpE,EAAKnC,EAAIC,EAAIiG,GAEb5yB,EAAI,EAAGA,EAAIvI,KAAKsoB,OAAO9f,OAAQD,IACtC,IAAKwsB,EAAaC,EAAIwG,EAAG/D,OAAQ+D,EAAG/E,MAAM,IAAMzB,EAAIwG,EAAG/D,OAAQz3B,KAAKsoB,OAAO/f,KAAM,EAC/E6yB,EAAKp7B,KAAKsoB,OAAO/f,IACd8B,MAAQ,EACXrK,KAAKsoB,OAAO,GAAGje,MAAQ9B,EACvBvI,KAAKsoB,OAAO/c,OAAOhD,EAAG,EAAGvI,KAAKsoB,OAAO,IACrCtoB,KAAKsoB,OAAO/c,OAAO,EAAG,EAAG6vB,GACzB,KACF,CAEF,GAAW,OAAPA,EACF,MAAM,IAAIjE,EAAuB,6CAqBnC,IAlBAqE,EAAGnE,OAAO+D,GACVC,EAAK,IAAIjE,EAAKnC,EAAIkG,EAAIC,EAAIlG,GAC1BoG,EAAK,IAAIlE,EAAKnC,EAAIC,EAAIkG,EAAID,GAC1BI,EAAK,IAAInE,EAAKlC,EAAIiG,EAAIC,EAAInG,GAC1Bj1B,KAAKy7B,SAASD,GACdx7B,KAAKy7B,SAASJ,GACdr7B,KAAKy7B,SAASH,GACdt7B,KAAKy7B,SAASF,GAEdC,EAAGhpB,KAAK6oB,EAAIpG,EAAIkG,GAChBK,EAAGhpB,KAAK8oB,EAAIrG,EAAIC,GAChBsG,EAAGhpB,KAAK+oB,EAAIrG,EAAIiG,GAChBE,EAAG7oB,KAAK8oB,EAAIrG,EAAImG,GAChBC,EAAG7oB,KAAK+oB,EAAIJ,EAAIC,GAChBE,EAAG9oB,KAAK+oB,EAAIH,EAAIlG,GAChBl1B,KAAKgM,QAAU,EAGNzD,EAAIvI,KAAKgM,QAASzD,EAAIvI,KAAKsoB,OAAO9f,OAAQD,IACjDia,EAAIxiB,KAAKsoB,OAAO/f,GACZizB,EAAGf,SAASjY,IACdxiB,KAAK07B,YAAYF,EAAIhZ,GAEnB6Y,EAAGZ,SAASjY,IACdxiB,KAAK07B,YAAYL,EAAI7Y,GAEnB8Y,EAAGb,SAASjY,IACdxiB,KAAK07B,YAAYJ,EAAI9Y,GAEnB+Y,EAAGd,SAASjY,IACdxiB,KAAK07B,YAAYH,EAAI/Y,EAG3B,EAEGqV,EAAW9tB,UAAU4xB,aAAe,SAAUC,EAAMC,EAAMC,GACzD,IAGI5G,EAAIiG,EACD/f,EAJH2gB,EAAKH,EAAKzF,UAAUoD,cACpByC,EAAKH,EAAK1F,UAAUoD,cACpB0C,EAAM,GAKV,IAFA1zB,EAAI6S,EAAI,EAED7S,EAAIwzB,EAAGvzB,QAAU4S,EAAI4gB,EAAGxzB,QACzBD,EAAIwzB,EAAGvzB,QAAU4S,EAAI4gB,EAAGxzB,QAC1B0sB,EAAK6G,EAAGxzB,GACR4yB,EAAKa,EAAG5gB,GAEJ8Z,EAAG7qB,QAAU8wB,EAAG9wB,OAClB4xB,EAAIx1B,KAAKyuB,GACT3sB,IACA6S,KACS8Z,EAAG7qB,MAAQ8wB,EAAG9wB,OACvB4xB,EAAIx1B,KAAKyuB,GACT3sB,MAEA0zB,EAAIx1B,KAAK00B,GACT/f,MAEO7S,EAAIwzB,EAAGvzB,OAChByzB,EAAIx1B,KAAKs1B,EAAGxzB,MAEZ0zB,EAAIx1B,KAAKu1B,EAAG5gB,MAIhB,IAAK,IAAI7S,EAAI0zB,EAAIzzB,OAAS,EAAGD,GAAK,EAAGA,IACnC2sB,EAAK+G,EAAI1zB,GACLuzB,EAAGrB,SAASvF,IAAKl1B,KAAK07B,YAAYI,EAAI5G,EAE9C,EAGF2C,EAAW9tB,UAAU2xB,YAAc,SAAUnG,EAAMC,GACjD,IAAI/B,EAAI,IAAI6B,EAAiBC,EAAMC,GACnCD,EAAKY,UAAUja,IAAIuX,GACnB+B,EAAKW,UAAUja,IAAIuX,EACrB,EAGAoE,EAAW9tB,UAAU4wB,eAAiB,SAAU5H,GAC9CA,EAAE4H,iBACF,IAAItwB,EAAQ0oB,EAAE1oB,MAEd,GADA0oB,EAAE1oB,OAAS,EACPA,IAAUrK,KAAK83B,OAAOtvB,OAAS,GAInC,KAAI6B,GAASrK,KAAK83B,OAAOtvB,QAAU6B,EAAQ,GAA3C,CACA,IAAI6xB,EAAOl8B,KAAK83B,OAAOvsB,OAAOvL,KAAK83B,OAAOtvB,OAAS,EAAG,GACtD0zB,EAAK,GAAG7xB,MAAQA,EAChBrK,KAAK83B,OAAOvsB,OAAOlB,EAAO,EAAG6xB,EAAK,GAHkB,OAHlDl8B,KAAK83B,OAAOvsB,OAAOvL,KAAK83B,OAAOtvB,OAAS,EAAG,EAO/C,EAGAqvB,EAAW9tB,UAAU0xB,SAAW,SAAUlG,GACxCA,EAAKlrB,MAAQrK,KAAK83B,OAAOtvB,OACzBxI,KAAK83B,OAAOrxB,KAAK8uB,EACnB,EAEAsC,EAAW9tB,UAAUoyB,QAAU,WAE7B,IADAn8B,KAAKk7B,OACEl7B,KAAKgM,QAAUhM,KAAKsoB,OAAO9f,QAAQ,CACxC,IAAI+B,EAAOvK,KAAKsoB,OAAOtoB,KAAKgM,SAC5B,GAAIzB,EAAK4rB,UAAUkD,UAEjBr5B,KAAKgM,cAFP,CAWA,IAAIynB,EANJzzB,KAAK+3B,QAAU,GACf/3B,KAAKg4B,QAAU,GACfh4B,KAAKsD,QAAU,GAEfiH,EAAK4rB,UAAUzgB,KAAK1V,KAAKsD,SAGzB,IAAK,IAAI84B,EAAK,EAAGA,EAAKp8B,KAAKsD,QAAQkF,OAAQ4zB,IAEzC,GAAU,QADV3I,EAAIzzB,KAAKsD,QAAQ84B,GAAI1B,cACL,CACdjH,EAAEwG,YAAYj6B,KAAKg4B,SACnB,KACF,CAKF,IAHA,IAAIkE,EAAO,KACTpvB,EAAQ,KAEDuvB,EAAM,EAAGA,EAAMr8B,KAAKg4B,QAAQxvB,OAAQ6zB,IAAO,CAClD,IAAIC,EAAKt8B,KAAKg4B,QAAQqE,GAClBP,EAAK,IAAI1E,EAAK7sB,EAAM+xB,EAAGrG,KAAMqG,EAAGvF,KAAMuF,EAAGrF,KAAK1sB,KAAKwsB,MACvD+E,EAAG3F,UAAY,IAAIN,GAAa,GAEhC71B,KAAKy7B,SAASK,GACd97B,KAAK+3B,QAAQtxB,KAAKq1B,GAElB97B,KAAK27B,aAAaW,EAAGpF,MAAOoF,EAAGrF,KAAKC,MAAO4E,GAE3CA,EAAGtpB,KAAK8pB,GACK,OAATJ,GAAeJ,EAAGtpB,KAAK0pB,EAAM3xB,EAAM+xB,EAAGrG,MAC1CiG,EAAOJ,EACO,OAAVhvB,IAAgBA,EAAQgvB,EAC9B,CAKA,GAHc,OAAVhvB,GAA2B,OAATovB,GACpBA,EAAK1pB,KAAK1F,EAAOvC,EAAMvK,KAAKg4B,QAAQ,GAAG/B,MAEd,GAAvBj2B,KAAK+3B,QAAQvvB,OAAa,CAE5B,IAAK,IAAIuqB,EAAI,EAAGA,EAAI/yB,KAAKsD,QAAQkF,OAAQuqB,IACvC/yB,KAAK26B,eAAe36B,KAAKsD,QAAQyvB,IAEnC/yB,KAAKgM,UACLhM,KAAK+3B,QAAU,EACjB,CA5CA,CA6CF,CACA,OAAO/3B,KAAK83B,MACd,EAEAD,EAAW9tB,UAAUxC,MAAQ,WAC3BvH,KAAKsoB,OAAS,GACdtoB,KAAK83B,OAAS,GACd93B,KAAK+3B,QAAU,GACf/3B,KAAKg4B,QAAU,GACfh4B,KAAKsD,QAAU,GACftD,KAAKgM,QAAU,CACjB,EAmWAyd,EAAQoD,gBAtLR,WAEE,IAAItpB,EAAI,SAAUqG,GAChB,OAAOA,EAAErG,CACX,EACIC,EAAI,SAAUoG,GAChB,OAAOA,EAAEpG,CACX,EACIib,EAAS,SAAU7U,GACrB,OAAOA,EAAE6U,MACX,EACImJ,EAAO,CACT,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,GACJ,CAAC,EAAG,IAEFpL,EAAS,CACX,CAAC,EAAG,GACJ,CAAC,EAAG,IAEFU,EAAO,CAAC,EAAG,GAMf,SAASqf,EAAiB5zB,GASxB,OAzGJ,SAAwCmyB,EAAOD,EAAelQ,GAC5D,IAAI6R,EAAa,IAAI3E,EACrB2E,EAAWj1B,QACXi1B,EAAW5B,KAAKC,EAAeC,GAO/B,IALA,IAAIhD,EAAS0E,EAAWL,QAAQrB,GAC5BrT,EAAW,GACXgV,EAAkB,GAClBC,EAAa5E,EAAOtvB,OAEfD,EAAI,EAAGA,EAAIm0B,EAAYn0B,IAAK,CACnC,IAAIo0B,EAAQ7E,EAAOvvB,GACnB,GAAIo0B,EAAMxD,qBACR,IAAK,IAAI1F,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAE1B,IAAIuF,EAAO2D,EAAMrC,MAAM7G,GACnBmJ,EAAa5D,EAAKjC,KAClB1O,EAAOuU,EAAW/O,eAEtB,IAAK4O,EAAgBG,EAAWvyB,OAAQ,CAEtC,GADAoyB,EAAgBG,EAAWvyB,QAAS,EAChCge,EAAK6N,QAEP,SASF,IANA,IAAI+C,EAAQF,EAAqBC,GAC7B6D,EAAY,GACZC,EAAQ,KACRC,EAAQ,KACRjkB,EAAK,EACLC,EAAK,EACAU,EAAI,EAAGA,EAAIwf,EAAMzwB,OAAQiR,IAAK,CACrC,IAAIhE,EAAQwjB,EAAMxf,GAAG2gB,eACjB9mB,EAAKmC,EAAMlS,EACXgQ,EAAKkC,EAAMjS,EACD,OAAVs5B,KACFhkB,EAAKgkB,EAAQxpB,GAEJ,IACPwF,GAAMA,IAFRC,EAAKgkB,EAAQxpB,GAIJ,IACPwF,GAAMA,KAGND,EAAK4T,GAAW3T,EAAK2T,KACvBmQ,EAAUp2B,KAAK,CAAC6M,EAAIC,IACpBupB,EAAQxpB,EACRypB,EAAQxpB,EAEZ,CAGA,GADA8U,EAAKgO,kBAAoBwG,EAAU5wB,WAC9Boc,EAAK6N,SAAWxM,EAAUsT,cAAc3U,EAAKgO,mBAAqB,EAAG,CACxE,IAAI4G,EAAchF,EAAYtN,EAAiBtC,EAAKgO,mBACpDhO,EAAKL,QAAUiV,EACfA,EAAY5U,KAAOA,EACf4U,EAAYz0B,OAAS,GACvBif,EAAShhB,KAAKw2B,EAElB,CACF,CACF,CAEJ,CACA,OAAOxV,CACT,CAsCWyV,CALSv0B,EAAKoT,KAAI,SAAUnS,GACjC,OAAO,IAAIosB,EAAOzyB,EAAEqG,GAAIpG,EAAEoG,GAAI,KAAM6U,EAAO7U,GAAIA,GAAG,EACpD,IAiGF,WACE,IAAIse,EACFC,EACAyC,EACAC,EACAnqB,EACAC,EACAyS,EACAE,EACAD,EACAE,EACA4pB,EAAe,GACftC,EAAgB,GAQlBznB,GANA8U,EAAO1L,EAAO,GAAG,KAIjB9b,GAHAynB,EAAO3L,EAAO,GAAG,IAGF0L,GAGf5U,EAAK6U,EAAOznB,EACZ2S,GANAuX,EAAOpO,EAAO,GAAG,KAGjB7b,GAFAkqB,EAAOrO,EAAO,GAAG,IAEDoO,GAIhBrX,EAAKsX,EAAOlqB,EAKZw8B,EAAa,GAAK,CAAC/pB,EAAIC,GACvB8pB,EAAa,GAAK,CAAC/pB,EAAIG,GACvB4pB,EAAa,GAAK,CAAC7pB,EAAIC,GACvB4pB,EAAa,GAAK,CAAC7pB,EAAID,GAEvB,IAAK,IAAI9K,EAAI,EAAGA,EAAI,EAAGA,IACrBsyB,EAAcp0B,KACZ,IAAIuvB,EACFmH,EAAa50B,GAAG,GAChB40B,EAAa50B,GAAG,GAChB,KACAmkB,EACA,IAAIsJ,EAAOmH,EAAa50B,GAAG,GAAI40B,EAAa50B,GAAG,GAAI,KAAMmkB,EAAS,MAAM,IACxE,IAKN,OAAOmO,CACT,CA7IsDA,GAAiBjT,EACvE,CA8IA,OA5IA2U,EAAiBh5B,EAAI,SAAUkV,GAC7B,OAAKC,UAAUlQ,QAIfjF,EAAIkV,EACG8jB,GAJEh5B,CAKX,EAEAg5B,EAAiB/4B,EAAI,SAAUiV,GAC7B,OAAKC,UAAUlQ,QAIfhF,EAAIiV,EACG8jB,GAJE/4B,CAKX,EAEA+4B,EAAiB9d,OAAS,SAAUhG,GAClC,OAAKC,UAAUlQ,QAIfiW,EAAShG,EACF8jB,GAJE9d,CAKX,EAEA8d,EAAiB3U,KAAO,SAAUnP,GAChC,IAAI2kB,EAAWC,EAASC,EAExB,OAAK5kB,UAAUlQ,QAIf60B,EAAUvI,EAAQtY,OAChB/D,EAAEsD,KAAI,SAAU3Q,GACd,OAAOA,EAAE,EACX,KAEFkyB,EAAUxI,EAAQtY,OAChB/D,EAAEsD,KAAI,SAAU3Q,GACd,OAAOA,EAAE,EACX,KAEFgyB,EAl5BJ,SAA0BpV,GACxB,IAAIuV,EAAM/F,EAAcgG,EAAIhH,EAAIE,EAAQxB,EAAI3sB,EAoB5C,GAjBAi1B,EAAKxV,EAAQA,EAAQxf,OAAS,GAC9BguB,EAAKxO,EAAQA,EAAQxf,OAAS,GAC9BkuB,EAAK1O,EAAQ,GAGbwP,EAAenC,EAFVD,EAAKoI,EAAIhH,GACdtB,EAAKE,EAAKoB,EAAIE,IAGd6G,EAAOlsB,KAAKksB,KAAK/F,GAGjBgG,EAAKhH,EAKLgB,EAAenC,EAFVH,EACLA,EAAKE,EAHLoB,EAAKE,EACLA,EAAK1O,EAAQ,KAKT3W,KAAKksB,KAAK/F,KAAkB+F,EAAhC,CAKA,IAAKh1B,EAAI,EAAGA,EAAIyf,EAAQxf,OAAS,EAAGD,IAQlC,GAPAi1B,EAAKhH,EAKLgB,EAAenC,EAFVH,EACLA,EAAKE,EAHLoB,EAAKE,EACLA,EAAK1O,EAAQzf,KAKT8I,KAAKksB,KAAK/F,KAAkB+F,EAC9B,OAIJ,OAAOA,CAhBP,CAiBF,CA02BgBE,CAAiBhlB,GAE3BmP,OADgB/e,IAAdu0B,EACK1T,EAAUgU,YAAYjlB,GACN,IAAd2kB,EACF3kB,EAAExM,UAEFwM,EAET+D,EAAS,CACP,CAAC6gB,EAAQ,GAAIC,EAAQ,IACrB,CAACD,EAAQ,GAAIC,EAAQ,KAEvBpgB,EAAO,CAACmgB,EAAQ,GAAKA,EAAQ,GAAIC,EAAQ,GAAKA,EAAQ,IAC/Cf,GA1BE3U,CA2BX,EAEA2U,EAAiB/f,OAAS,SAAU/D,GAClC,OAAKC,UAAUlQ,QAIfof,EAAO,CAACnP,EAAE,GAAI,CAACA,EAAE,GAAG,GAAIA,EAAE,GAAG,IAAKA,EAAE,GAAI,CAACA,EAAE,GAAG,GAAIA,EAAE,GAAG,KACvD+D,EAAS/D,EACTyE,EAAO,CAACzE,EAAE,GAAG,GAAKA,EAAE,GAAG,GAAIA,EAAE,GAAG,GAAKA,EAAE,GAAG,IACnC8jB,GANE/f,CAOX,EAEA+f,EAAiBrf,KAAO,SAAUzE,GAChC,OAAKC,UAAUlQ,QAIfof,EAAO,CACL,CAAC,EAAG,GACJ,CAAC,EAAGnP,EAAE,IACN,CAACA,EAAE,GAAIA,EAAE,IACT,CAACA,EAAE,GAAI,IAET+D,EAAS,CAAC,CAAC,EAAG,GAAI/D,GAClByE,EAAOzE,EACA8jB,GAXErf,CAYX,EAuDOqf,CACT,EAGA9S,EAAQ0N,uBAAyBA,EAEjC3S,OAAO4M,eAAe3H,EAAS,aAAc,CAAEhhB,OAAO,GAExD,CAzhCiE4oB,CAAQ5H,EAAS,EAAQ,MAAa,EAAQ,M,oCCDhG,WAAS1e,EAAGC,GACzB,OAAOD,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAID,GAAKC,EAAI,EAAIqP,GAC/C,CCAe,WAAS0Y,GACtB,IAAI4K,EAAQ5K,EACRroB,EAAUqoB,EAOd,SAAS1R,EAAKtW,EAAGxH,EAAGq6B,EAAIvX,GAGtB,IAFU,MAANuX,IAAYA,EAAK,GACX,MAANvX,IAAYA,EAAKtb,EAAEvC,QAChBo1B,EAAKvX,GAAI,CACd,MAAMC,EAAOsX,EAAKvX,IAAQ,EACtB3b,EAAQK,EAAEub,GAAM/iB,GAAK,EAAGq6B,EAAKtX,EAAM,EAClCD,EAAKC,CACZ,CACA,OAAOsX,CACT,CAoBA,OAlCiB,IAAb7K,EAAEvqB,SACJm1B,EAAQ,CAAC/zB,EAAGrG,IAAMwvB,EAAEnpB,GAAKrG,EACzBmH,EAmCJ,SAA6BqoB,GAC3B,MAAO,CAACnpB,EAAGrG,IAAMs6B,EAAU9K,EAAEnpB,GAAIrG,EACnC,CArCcu6B,CAAoB/K,IAgCzB,CAAC1R,OAAM0c,OAPd,SAAgBhzB,EAAGxH,EAAGq6B,EAAIvX,GACd,MAANuX,IAAYA,EAAK,GACX,MAANvX,IAAYA,EAAKtb,EAAEvC,QACvB,MAAMD,EAAI8Y,EAAKtW,EAAGxH,EAAGq6B,EAAIvX,EAAK,GAC9B,OAAO9d,EAAIq1B,GAAMD,EAAM5yB,EAAExC,EAAI,GAAIhF,IAAMo6B,EAAM5yB,EAAExC,GAAIhF,GAAKgF,EAAI,EAAIA,CAClE,EAEsBgR,MAlBtB,SAAexO,EAAGxH,EAAGq6B,EAAIvX,GAGvB,IAFU,MAANuX,IAAYA,EAAK,GACX,MAANvX,IAAYA,EAAKtb,EAAEvC,QAChBo1B,EAAKvX,GAAI,CACd,MAAMC,EAAOsX,EAAKvX,IAAQ,EACtB3b,EAAQK,EAAEub,GAAM/iB,GAAK,EAAG8iB,EAAKC,EAC5BsX,EAAKtX,EAAM,CAClB,CACA,OAAOsX,CACT,EAUF,CCzCe,WAASr6B,GACtB,OAAa,OAANA,EAAa8W,KAAO9W,CAC7B,C,kkECEA,MAAMy6B,EAAkBC,EAASJ,GACpBK,EAAcF,EAAgBzkB,MAC9B4kB,EAAaH,EAAgB3c,KAC7B+c,EAAeH,EAASI,GAAQN,OAC7C,QCRe,SAAS31B,EAAMk2B,EAAQC,GACpC,IAAIn2B,EAAQ,EACZ,QAAgBS,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,EACH,MAAT71B,IAAkBA,GAASA,IAAUA,KACrCL,MAGD,CACL,IAAIiC,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAAqB71B,GAASA,IAAUA,KACzEL,CAGR,CACA,OAAOA,CACT,CCjBA,SAAS,EAAOo2B,GACd,OAAsB,EAAfA,EAAMh2B,MACf,CAEA,SAASi2B,EAAMj2B,GACb,QAASA,EAAS,EACpB,CAEA,SAASk2B,EAASJ,GAChB,MAAyB,iBAAXA,GAAuB,WAAYA,EAASA,EAAS/0B,MAAMC,KAAK80B,EAChF,CAMe,SAASK,KAASL,GAC/B,MAAMvP,EAA8C,mBAA9BuP,EAAOA,EAAO91B,OAAS,IAL/C,SAAiBumB,GACf,OAAOuP,GAAUvP,KAAUuP,EAC7B,CAGoEM,CAAQN,EAAOh1B,OAE3Eu1B,GADNP,EAASA,EAAOviB,IAAI2iB,IACG3iB,IAAI,GACrBtC,EAAI6kB,EAAO91B,OAAS,EACpB6B,EAAQ,IAAId,MAAMkQ,EAAI,GAAG/D,KAAK,GAC9BopB,EAAU,GAChB,GAAIrlB,EAAI,GAAKolB,EAAQE,KAAKN,GAAQ,OAAOK,EACzC,OAAa,CACXA,EAAQr4B,KAAK4D,EAAM0R,KAAI,CAACtC,EAAGlR,IAAM+1B,EAAO/1B,GAAGkR,MAC3C,IAAIlR,EAAIkR,EACR,OAASpP,EAAM9B,KAAOs2B,EAAQt2B,IAAI,CAChC,GAAU,IAANA,EAAS,OAAOwmB,EAAS+P,EAAQ/iB,IAAIgT,GAAU+P,EACnDz0B,EAAM9B,KAAO,CACf,CACF,CACF,CChCe,SAASy2B,EAAOV,EAAQC,GACrC,IAAIj2B,EAAM,EAAG+B,EAAQ,EACrB,OAAO40B,aAAaz1B,KAAK80B,OAAoBz1B,IAAZ01B,EAC7B/b,GAAMla,IAAQka,GAAK,EACnBA,GAAMla,IAAQi2B,EAAQ/b,EAAGnY,IAASi0B,IAAW,EACnD,CCLe,WAASvzB,EAAGC,GACzB,OAAOA,EAAID,GAAK,EAAIC,EAAID,EAAI,EAAIC,GAAKD,EAAI,EAAIsP,GAC/C,CCFe,SAAS6kB,EAASZ,EAAQC,GACvC,IACIZ,EADAv1B,EAAQ,EAER+2B,EAAO,EACP72B,EAAM,EACV,QAAgBO,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,EACH,MAAT71B,IAAkBA,GAASA,IAAUA,IACvCk1B,EAAQl1B,EAAQ02B,EAChBA,GAAQxB,IAAUv1B,EAClBE,GAAOq1B,GAASl1B,EAAQ02B,QAGvB,CACL,IAAI90B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAAqB71B,GAASA,IAAUA,IAC3Ek1B,EAAQl1B,EAAQ02B,EAChBA,GAAQxB,IAAUv1B,EAClBE,GAAOq1B,GAASl1B,EAAQ02B,GAG9B,CACA,GAAI/2B,EAAQ,EAAG,OAAOE,GAAOF,EAAQ,EACvC,CCtBe,SAASg3B,EAAUd,EAAQC,GACxC,MAAM/b,EAAI0c,EAASZ,EAAQC,GAC3B,OAAO/b,EAAInR,KAAKsF,KAAK6L,GAAKA,CAC5B,CCLe,WAAS8b,EAAQC,GAC9B,IAAIjtB,EACAG,EACJ,QAAgB5I,IAAZ01B,EACF,IAAK,MAAM91B,KAAS61B,EACL,MAAT71B,SACUI,IAARyI,EACE7I,GAASA,IAAO6I,EAAMG,EAAMhJ,IAE5B6I,EAAM7I,IAAO6I,EAAM7I,GACnBgJ,EAAMhJ,IAAOgJ,EAAMhJ,SAIxB,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,WACvBz1B,IAARyI,EACE7I,GAASA,IAAO6I,EAAMG,EAAMhJ,IAE5B6I,EAAM7I,IAAO6I,EAAM7I,GACnBgJ,EAAMhJ,IAAOgJ,EAAMhJ,IAI/B,CACA,MAAO,CAAC6I,EAAKG,EACf,CC3BO,MAAM4tB,EACX,WAAAr1B,GACEhK,KAAKs/B,UAAY,IAAIL,aAAa,IAClCj/B,KAAKu/B,GAAK,CACZ,CACA,GAAArjB,CAAI3Y,GACF,MAAMyiB,EAAIhmB,KAAKs/B,UACf,IAAI/2B,EAAI,EACR,IAAK,IAAIkR,EAAI,EAAGA,EAAIzZ,KAAKu/B,IAAM9lB,EAAI,GAAIA,IAAK,CAC1C,MAAMjW,EAAIwiB,EAAEvM,GACV4M,EAAK9iB,EAAIC,EACTo6B,EAAKvsB,KAAKqN,IAAInb,GAAK8N,KAAKqN,IAAIlb,GAAKD,GAAK8iB,EAAK7iB,GAAKA,GAAK6iB,EAAK9iB,GACxDq6B,IAAI5X,EAAEzd,KAAOq1B,GACjBr6B,EAAI8iB,CACN,CAGA,OAFAL,EAAEzd,GAAKhF,EACPvD,KAAKu/B,GAAKh3B,EAAI,EACPvI,IACT,CACA,OAAAw/B,GACE,MAAMxZ,EAAIhmB,KAAKs/B,UACf,IAAiB/7B,EAAGC,EAAGo6B,EAAnB10B,EAAIlJ,KAAKu/B,GAAclZ,EAAK,EAChC,GAAInd,EAAI,EAAG,CAET,IADAmd,EAAKL,IAAI9c,GACFA,EAAI,IACT3F,EAAI8iB,EACJ7iB,EAAIwiB,IAAI9c,GACRmd,EAAK9iB,EAAIC,EACTo6B,EAAKp6B,GAAK6iB,EAAK9iB,IACXq6B,KAEF10B,EAAI,IAAO00B,EAAK,GAAK5X,EAAE9c,EAAI,GAAK,GAAO00B,EAAK,GAAK5X,EAAE9c,EAAI,GAAK,KAC9D1F,EAAS,EAALo6B,EACJr6B,EAAI8iB,EAAK7iB,EACLA,GAAKD,EAAI8iB,IAAIA,EAAK9iB,GAE1B,CACA,OAAO8iB,CACT,EAGK,SAASoZ,EAAKnB,EAAQC,GAC3B,MAAMmB,EAAQ,IAAIL,EAClB,QAAgBx2B,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,GACZ71B,GAASA,IACXi3B,EAAMxjB,IAAIzT,OAGT,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,GACZ71B,GAAS81B,EAAQ91B,IAAS4B,EAAOi0B,KACnCoB,EAAMxjB,IAAIzT,EAGhB,CACA,OAAQi3B,CACV,CAEO,SAASC,EAAQrB,EAAQC,GAC9B,MAAMmB,EAAQ,IAAIL,EAClB,IAAIh1B,GAAS,EACb,OAAO40B,aAAaz1B,KAAK80B,OAAoBz1B,IAAZ01B,EAC3B/b,GAAKkd,EAAMxjB,KAAKsG,GAAK,GACrBA,GAAKkd,EAAMxjB,KAAKqiB,EAAQ/b,IAAKnY,EAAOi0B,IAAW,GAEvD,CCpEO,MAAMsB,UAAkBh3B,IAC7B,WAAAoB,CAAY61B,EAASvvB,EAAMwvB,GAGzB,GAFAhgC,QACA0kB,OAAOub,iBAAiB//B,KAAM,CAACggC,QAAS,CAACv3B,MAAO,IAAIG,KAAQq3B,KAAM,CAACx3B,MAAO6H,KAC3D,MAAXuvB,EAAiB,IAAK,MAAOvvB,EAAK7H,KAAUo3B,EAAS7/B,KAAKqH,IAAIiJ,EAAK7H,EACzE,CACA,GAAA5C,CAAIyK,GACF,OAAOxQ,MAAM+F,IAAIq6B,EAAWlgC,KAAMsQ,GACpC,CACA,GAAA6vB,CAAI7vB,GACF,OAAOxQ,MAAMqgC,IAAID,EAAWlgC,KAAMsQ,GACpC,CACA,GAAAjJ,CAAIiJ,EAAK7H,GACP,OAAO3I,MAAMuH,IAAI+4B,EAAWpgC,KAAMsQ,GAAM7H,EAC1C,CACA,OAAO6H,GACL,OAAOxQ,MAAM+Y,OAAOwnB,EAAcrgC,KAAMsQ,GAC1C,EAGK,MAAMgwB,UAAkBC,IAC7B,WAAAv2B,CAAYs0B,EAAQhuB,EAAMwvB,GAGxB,GAFAhgC,QACA0kB,OAAOub,iBAAiB//B,KAAM,CAACggC,QAAS,CAACv3B,MAAO,IAAIG,KAAQq3B,KAAM,CAACx3B,MAAO6H,KAC5D,MAAVguB,EAAgB,IAAK,MAAM71B,KAAS61B,EAAQt+B,KAAKkc,IAAIzT,EAC3D,CACA,GAAA03B,CAAI13B,GACF,OAAO3I,MAAMqgC,IAAID,EAAWlgC,KAAMyI,GACpC,CACA,GAAAyT,CAAIzT,GACF,OAAO3I,MAAMoc,IAAIkkB,EAAWpgC,KAAMyI,GACpC,CACA,OAAOA,GACL,OAAO3I,MAAM+Y,OAAOwnB,EAAcrgC,KAAMyI,GAC1C,EAGF,SAASy3B,GAAW,QAACF,EAAO,KAAEC,GAAOx3B,GACnC,MAAM6H,EAAM2vB,EAAKx3B,GACjB,OAAOu3B,EAAQG,IAAI7vB,GAAO0vB,EAAQn6B,IAAIyK,GAAO7H,CAC/C,CAEA,SAAS23B,GAAW,QAACJ,EAAO,KAAEC,GAAOx3B,GACnC,MAAM6H,EAAM2vB,EAAKx3B,GACjB,OAAIu3B,EAAQG,IAAI7vB,GAAa0vB,EAAQn6B,IAAIyK,IACzC0vB,EAAQ34B,IAAIiJ,EAAK7H,GACVA,EACT,CAEA,SAAS43B,GAAc,QAACL,EAAO,KAAEC,GAAOx3B,GACtC,MAAM6H,EAAM2vB,EAAKx3B,GAKjB,OAJIu3B,EAAQG,IAAI7vB,KACd7H,EAAQu3B,EAAQn6B,IAAI4C,GACpBu3B,EAAQnnB,OAAOvI,IAEV7H,CACT,CAEA,SAASq3B,EAAMr3B,GACb,OAAiB,OAAVA,GAAmC,iBAAVA,EAAqBA,EAAM+2B,UAAY/2B,CACzE,CC5De,WAASlF,GACtB,OAAOA,CACT,CCCe,SAASi9B,EAAMlC,KAAWmC,GACvC,OAAOC,EAAKpC,EAAQqC,EAAUA,EAAUF,EAC1C,CAEO,SAASG,EAAOtC,KAAWmC,GAChC,OAAOC,EAAKpC,EAAQ/0B,MAAMC,KAAMm3B,EAAUF,EAC5C,CAEO,SAASI,EAAOvC,EAAQvP,KAAW0R,GACxC,OAAOC,EAAKpC,EAAQqC,EAAU5R,EAAQ0R,EACxC,CAEO,SAASK,EAAQxC,EAAQvP,KAAW0R,GACzC,OAAOC,EAAKpC,EAAQ/0B,MAAMC,KAAMulB,EAAQ0R,EAC1C,CAEO,SAASp2B,EAAMi0B,KAAWmC,GAC/B,OAAOC,EAAKpC,EAAQqC,EAAUI,EAAQN,EACxC,CAEO,SAAS9X,EAAQ2V,KAAWmC,GACjC,OAAOC,EAAKpC,EAAQ/0B,MAAMC,KAAMu3B,EAAQN,EAC1C,CAEA,SAASM,EAAOzC,GACd,GAAsB,IAAlBA,EAAO91B,OAAc,MAAM,IAAImT,MAAM,iBACzC,OAAO2iB,EAAO,EAChB,CAEA,SAASoC,EAAKpC,EAAQviB,EAAKgT,EAAQ0R,GACjC,OAAO,SAAUO,EAAQ1C,EAAQ/1B,GAC/B,GAAIA,GAAKk4B,EAAKj4B,OAAQ,OAAOumB,EAAOuP,GACpC,MAAMsC,EAAS,IAAIhB,EACbE,EAAQW,EAAKl4B,KACnB,IAAI8B,GAAS,EACb,IAAK,MAAM5B,KAAS61B,EAAQ,CAC1B,MAAMhuB,EAAMwvB,EAAMr3B,IAAS4B,EAAOi0B,GAC5BkC,EAAQI,EAAO/6B,IAAIyK,GACrBkwB,EAAOA,EAAM/5B,KAAKgC,GACjBm4B,EAAOv5B,IAAIiJ,EAAK,CAAC7H,GACxB,CACA,IAAK,MAAO6H,EAAKguB,KAAWsC,EAC1BA,EAAOv5B,IAAIiJ,EAAK0wB,EAAQ1C,EAAQ/1B,IAElC,OAAOwT,EAAI6kB,EACZ,CAfM,CAeJtC,EAAQ,EACb,CCjDe,WAAS3yB,EAAQ80B,GAC9B,OAAOl3B,MAAMC,KAAKi3B,GAAMnwB,GAAO3E,EAAO2E,IACxC,CCCe,SAAS7F,EAAK6zB,KAAW2C,GACtC,GAAuC,mBAA5B3C,EAAOxyB,OAAOC,UAA0B,MAAM,IAAIwoB,UAAU,0BACvE+J,EAAS/0B,MAAMC,KAAK80B,GACpB,IAAKvL,EAAI8K,GAAaoD,EACtB,GAAiB,IAAblO,EAAEvqB,QAAgBy4B,EAAEz4B,OAAS,EAAG,CAClC,MAAM6B,EAAQ62B,YAAY13B,KAAK80B,GAAQ,CAAC10B,EAAGrB,IAAMA,IAajD,OAZI04B,EAAEz4B,OAAS,GACby4B,EAAIA,EAAEllB,KAAIgX,GAAKuL,EAAOviB,IAAIgX,KAC1B1oB,EAAMI,MAAK,CAAClC,EAAGkR,KACb,IAAK,MAAMsZ,KAAKkO,EAAG,CACjB,MAAM71B,EAAIyyB,EAAU9K,EAAExqB,GAAIwqB,EAAEtZ,IAC5B,GAAIrO,EAAG,OAAOA,CAChB,OAGF2nB,EAAIuL,EAAOviB,IAAIgX,GACf1oB,EAAMI,MAAK,CAAClC,EAAGkR,IAAMokB,EAAU9K,EAAExqB,GAAIwqB,EAAEtZ,OAElC0nB,EAAQ7C,EAAQj0B,EACzB,CACA,OAAOi0B,EAAO7zB,KAAKsoB,EACrB,CCpBe,SAASqO,EAAU9C,EAAQvP,EAAQze,GAChD,OAA0B,IAAlBye,EAAOvmB,OACXiC,EAAKo2B,EAAOvC,EAAQvP,EAAQze,IAAM,EAAG+wB,EAAIC,IAAMC,EAAIC,KAAQ3D,EAAUyD,EAAIE,IAAO3D,EAAUwD,EAAIE,KAC9F92B,EAAK+1B,EAAMlC,EAAQhuB,IAAM,EAAG+wB,EAAIC,IAAMC,EAAIC,KAAQzS,EAAOuS,EAAIE,IAAO3D,EAAUwD,EAAIE,MACnFxlB,KAAI,EAAEzL,KAASA,GACpB,CCTA,IAAIkuB,EAAQj1B,MAAMQ,UAEP4X,EAAQ6c,EAAM7c,MCFV,WAASpe,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CDDiBi7B,EAAMziB,IEHvB,IAAI0lB,EAAMpwB,KAAKsF,KAAK,IAChB+qB,EAAKrwB,KAAKsF,KAAK,IACfgrB,EAAKtwB,KAAKsF,KAAK,GAEJ,WAAS9L,EAAO4M,EAAMrP,GACnC,IAAI6D,EAEA/C,EACA04B,EACA7gC,EAHAwH,GAAK,EAMT,GAD8BH,GAASA,GAAzByC,GAASA,KAAvB4M,GAAQA,IACcrP,EAAQ,EAAG,MAAO,CAACyC,GAEzC,IADIoB,EAAUwL,EAAO5M,KAAO3B,EAAI2B,EAAOA,EAAQ4M,EAAMA,EAAOvO,GACT,KAA9CnI,EAAO8gC,EAAch3B,EAAO4M,EAAMrP,MAAkBqwB,SAAS13B,GAAO,MAAO,GAEhF,GAAIA,EAAO,EAAG,CACZ,IAAI+gC,EAAKzwB,KAAK6O,MAAMrV,EAAQ9J,GAAOghC,EAAK1wB,KAAK6O,MAAMzI,EAAO1W,GAI1D,IAHI+gC,EAAK/gC,EAAO8J,KAASi3B,EACrBC,EAAKhhC,EAAO0W,KAAQsqB,EACxBH,EAAQ,IAAIr4B,MAAML,EAAI64B,EAAKD,EAAK,KACvBv5B,EAAIW,GAAG04B,EAAMr5B,IAAMu5B,EAAKv5B,GAAKxH,CACxC,KAAO,CACLA,GAAQA,EACR,IAAI+gC,EAAKzwB,KAAK6O,MAAMrV,EAAQ9J,GAAOghC,EAAK1wB,KAAK6O,MAAMzI,EAAO1W,GAI1D,IAHI+gC,EAAK/gC,EAAO8J,KAASi3B,EACrBC,EAAKhhC,EAAO0W,KAAQsqB,EACxBH,EAAQ,IAAIr4B,MAAML,EAAI64B,EAAKD,EAAK,KACvBv5B,EAAIW,GAAG04B,EAAMr5B,IAAMu5B,EAAKv5B,GAAKxH,CACxC,CAIA,OAFIkL,GAAS21B,EAAM31B,UAEZ21B,CACT,CAEO,SAASC,EAAch3B,EAAO4M,EAAMrP,GACzC,IAAIrH,GAAQ0W,EAAO5M,GAASwG,KAAKI,IAAI,EAAGrJ,GACpC45B,EAAQ3wB,KAAKiL,MAAMjL,KAAK4wB,IAAIlhC,GAAQsQ,KAAK6wB,MACzCvT,EAAQ5tB,EAAOsQ,KAAK2F,IAAI,GAAIgrB,GAChC,OAAOA,GAAS,GACTrT,GAAS8S,EAAM,GAAK9S,GAAS+S,EAAK,EAAI/S,GAASgT,EAAK,EAAI,GAAKtwB,KAAK2F,IAAI,GAAIgrB,IAC1E3wB,KAAK2F,IAAI,IAAKgrB,IAAUrT,GAAS8S,EAAM,GAAK9S,GAAS+S,EAAK,EAAI/S,GAASgT,EAAK,EAAI,EACzF,CAEO,SAASQ,EAASt3B,EAAO4M,EAAMrP,GACpC,IAAIg6B,EAAQ/wB,KAAKqN,IAAIjH,EAAO5M,GAASwG,KAAKI,IAAI,EAAGrJ,GAC7Ci6B,EAAQhxB,KAAK2F,IAAI,GAAI3F,KAAKiL,MAAMjL,KAAK4wB,IAAIG,GAAS/wB,KAAK6wB,OACvDvT,EAAQyT,EAAQC,EAIpB,OAHI1T,GAAS8S,EAAKY,GAAS,GAClB1T,GAAS+S,EAAIW,GAAS,EACtB1T,GAASgT,IAAIU,GAAS,GACxB5qB,EAAO5M,GAASw3B,EAAQA,CACjC,CCnDe,SAASC,EAAKz3B,EAAO4M,EAAMrP,GACxC,IAAIm6B,EACJ,OAAa,CACX,MAAMxhC,EAAO8gC,EAAch3B,EAAO4M,EAAMrP,GACxC,GAAIrH,IAASwhC,GAAoB,IAATxhC,IAAe03B,SAAS13B,GAC9C,MAAO,CAAC8J,EAAO4M,GACN1W,EAAO,GAChB8J,EAAQwG,KAAKiL,MAAMzR,EAAQ9J,GAAQA,EACnC0W,EAAOpG,KAAKmxB,KAAK/qB,EAAO1W,GAAQA,GACvBA,EAAO,IAChB8J,EAAQwG,KAAKmxB,KAAK33B,EAAQ9J,GAAQA,EAClC0W,EAAOpG,KAAKiL,MAAM7E,EAAO1W,GAAQA,GAEnCwhC,EAAUxhC,CACZ,CACF,CCfe,WAASu9B,GACtB,OAAOjtB,KAAKmxB,KAAKnxB,KAAK4wB,IAAI75B,EAAMk2B,IAAWjtB,KAAKoxB,KAAO,CACzD,CCKe,aACb,IAAIh6B,EAAQk4B,EACR+B,EAASlmB,EACTmmB,EAAYC,EAEhB,SAASC,EAAUl6B,GACZY,MAAMM,QAAQlB,KAAOA,EAAOY,MAAMC,KAAKb,IAE5C,IAAIJ,EAEAhF,EADA2F,EAAIP,EAAKH,OAET81B,EAAS,IAAI/0B,MAAML,GAEvB,IAAKX,EAAI,EAAGA,EAAIW,IAAKX,EACnB+1B,EAAO/1B,GAAKE,EAAME,EAAKJ,GAAIA,EAAGI,GAGhC,IAAIyT,EAAKsmB,EAAOpE,GACZlrB,EAAKgJ,EAAG,GACR9I,EAAK8I,EAAG,GACR0mB,EAAKH,EAAUrE,EAAQlrB,EAAIE,GAI/B,IAAK/J,MAAMM,QAAQi5B,GAAK,CACtB,MAAMrxB,EAAM6B,EAAIyvB,GAAMD,EAWtB,GAVIJ,IAAWlmB,KAASpJ,EAAIE,GAAMgvB,EAAKlvB,EAAIE,EAAIyvB,KAC/CD,EAAKlB,EAAMxuB,EAAIE,EAAIyvB,IASZD,EAAGt6B,OAAS,IAAM8K,EACvB,GAAI7B,GAAO6B,GAAMovB,IAAWlmB,EAAQ,CAClC,MAAMzb,EAAO8gC,EAAczuB,EAAIE,EAAIyvB,GAC/BtK,SAAS13B,KACPA,EAAO,EACTuS,GAAMjC,KAAKiL,MAAMhJ,EAAKvS,GAAQ,GAAKA,EAC1BA,EAAO,IAChBuS,GAAMjC,KAAKmxB,KAAKlvB,GAAMvS,GAAQ,IAAMA,GAG1C,MACE+hC,EAAGx5B,KAGT,CAIA,IADA,IAAImN,EAAIqsB,EAAGt6B,OACJs6B,EAAG,IAAM1vB,GAAI0vB,EAAGhgB,UAAWrM,EAClC,KAAOqsB,EAAGrsB,EAAI,GAAKnD,GAAIwvB,EAAGx5B,QAASmN,EAEnC,IACIusB,EADAC,EAAO,IAAI15B,MAAMkN,EAAI,GAIzB,IAAKlO,EAAI,EAAGA,GAAKkO,IAAKlO,GACpBy6B,EAAMC,EAAK16B,GAAK,IACZ6K,GAAK7K,EAAI,EAAIu6B,EAAGv6B,EAAI,GAAK6K,EAC7B4vB,EAAI1vB,GAAK/K,EAAIkO,EAAIqsB,EAAGv6B,GAAK+K,EAI3B,IAAK/K,EAAI,EAAGA,EAAIW,IAAKX,EAEf6K,IADJ7P,EAAI+6B,EAAO/1B,KACIhF,GAAK+P,GAClB2vB,EAAKC,EAAOJ,EAAIv/B,EAAG,EAAGkT,IAAIhQ,KAAKkC,EAAKJ,IAIxC,OAAO06B,CACT,CAcA,OAZAJ,EAAUp6B,MAAQ,SAASgQ,GACzB,OAAOC,UAAUlQ,QAAUC,EAAqB,mBAANgQ,EAAmBA,EAAI6C,EAAS7C,GAAIoqB,GAAap6B,CAC7F,EAEAo6B,EAAUH,OAAS,SAASjqB,GAC1B,OAAOC,UAAUlQ,QAAUk6B,EAAsB,mBAANjqB,EAAmBA,EAAI6C,EAAS,CAAC7C,EAAE,GAAIA,EAAE,KAAMoqB,GAAaH,CACzG,EAEAG,EAAUM,WAAa,SAAS1qB,GAC9B,OAAOC,UAAUlQ,QAAUm6B,EAAyB,mBAANlqB,EAAmBA,EAAIlP,MAAMM,QAAQ4O,GAAK6C,EAASqG,EAAMrX,KAAKmO,IAAM6C,EAAS7C,GAAIoqB,GAAaF,CAC9I,EAEOE,CACT,CCpGe,SAASpxB,GAAI6sB,EAAQC,GAClC,IAAI9sB,EACJ,QAAgB5I,IAAZ01B,EACF,IAAK,MAAM91B,KAAS61B,EACL,MAAT71B,IACIgJ,EAAMhJ,QAAkBI,IAAR4I,GAAqBhJ,GAASA,KACpDgJ,EAAMhJ,OAGL,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAC7B7sB,EAAMhJ,QAAkBI,IAAR4I,GAAqBhJ,GAASA,KACpDgJ,EAAMhJ,EAGZ,CACA,OAAOgJ,CACT,CCnBe,SAASH,GAAIgtB,EAAQC,GAClC,IAAIjtB,EACJ,QAAgBzI,IAAZ01B,EACF,IAAK,MAAM91B,KAAS61B,EACL,MAAT71B,IACI6I,EAAM7I,QAAkBI,IAARyI,GAAqB7I,GAASA,KACpD6I,EAAM7I,OAGL,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAC7BhtB,EAAM7I,QAAkBI,IAARyI,GAAqB7I,GAASA,KACpD6I,EAAM7I,EAGZ,CACA,OAAO6I,CACT,CCfe,SAAS8xB,GAAY5E,EAAOlzB,EAAG+V,EAAO,EAAG9H,EAAQilB,EAAMh2B,OAAS,EAAGkC,EAAUmzB,GAC1F,KAAOtkB,EAAQ8H,GAAM,CACnB,GAAI9H,EAAQ8H,EAAO,IAAK,CACtB,MAAMnY,EAAIqQ,EAAQ8H,EAAO,EACnB5K,EAAInL,EAAI+V,EAAO,EACf9E,EAAIlL,KAAK4wB,IAAI/4B,GACboO,EAAI,GAAMjG,KAAKgyB,IAAI,EAAI9mB,EAAI,GAC3B+mB,EAAK,GAAMjyB,KAAKsF,KAAK4F,EAAIjF,GAAKpO,EAAIoO,GAAKpO,IAAMuN,EAAIvN,EAAI,EAAI,GAAK,EAAI,GAGxEk6B,GAAY5E,EAAOlzB,EAFH+F,KAAKI,IAAI4P,EAAMhQ,KAAKiL,MAAMhR,EAAImL,EAAIa,EAAIpO,EAAIo6B,IACzCjyB,KAAKC,IAAIiI,EAAOlI,KAAKiL,MAAMhR,GAAKpC,EAAIuN,GAAKa,EAAIpO,EAAIo6B,IACzB54B,EAC3C,CAEA,MAAM+X,EAAI+b,EAAMlzB,GAChB,IAAI/C,EAAI8Y,EACJ5H,EAAIF,EAKR,IAHAgqB,GAAK/E,EAAOnd,EAAM/V,GACdZ,EAAQ8zB,EAAMjlB,GAAQkJ,GAAK,GAAG8gB,GAAK/E,EAAOnd,EAAM9H,GAE7ChR,EAAIkR,GAAG,CAEZ,IADA8pB,GAAK/E,EAAOj2B,EAAGkR,KAAMlR,IAAKkR,EACnB/O,EAAQ8zB,EAAMj2B,GAAIka,GAAK,KAAKla,EACnC,KAAOmC,EAAQ8zB,EAAM/kB,GAAIgJ,GAAK,KAAKhJ,CACrC,CAEgC,IAA5B/O,EAAQ8zB,EAAMnd,GAAOoB,GAAU8gB,GAAK/E,EAAOnd,EAAM5H,MAC9CA,EAAG8pB,GAAK/E,EAAO/kB,EAAGF,IAErBE,GAAKnO,IAAG+V,EAAO5H,EAAI,GACnBnO,GAAKmO,IAAGF,EAAQE,EAAI,EAC1B,CACA,OAAO+kB,CACT,CAEA,SAAS+E,GAAK/E,EAAOj2B,EAAGkR,GACtB,MAAMgJ,EAAI+b,EAAMj2B,GAChBi2B,EAAMj2B,GAAKi2B,EAAM/kB,GACjB+kB,EAAM/kB,GAAKgJ,CACb,CCtCe,SAAS+gB,GAASlF,EAAQtY,EAAGuY,GAE1C,GADAD,EAASW,aAAaz1B,KzBFjB,UAAkB80B,EAAQC,GAC/B,QAAgB11B,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,EACH,MAAT71B,IAAkBA,GAASA,IAAUA,UACjCA,OAGL,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAAqB71B,GAASA,IAAUA,UACrEA,EAGZ,CACF,CyBb6Bg7B,CAAQnF,EAAQC,IACrCr1B,EAAIo1B,EAAO91B,OAAjB,CACA,IAAKwd,GAAKA,IAAM,GAAK9c,EAAI,EAAG,OAAOoI,GAAIgtB,GACvC,GAAItY,GAAK,EAAG,OAAOvU,GAAI6sB,GACvB,IAAIp1B,EACAX,GAAKW,EAAI,GAAK8c,EACdR,EAAKnU,KAAKiL,MAAM/T,GAChBm7B,EAASjyB,GAAI2xB,GAAY9E,EAAQ9Y,GAAIme,SAAS,EAAGne,EAAK,IAE1D,OAAOke,GADMpyB,GAAIgtB,EAAOqF,SAASne,EAAK,IACZke,IAAWn7B,EAAIid,EART,CASlC,CAEO,SAASoe,GAAetF,EAAQtY,EAAGuY,EAAUF,GAClD,GAAMn1B,EAAIo1B,EAAO91B,OAAjB,CACA,IAAKwd,GAAKA,IAAM,GAAK9c,EAAI,EAAG,OAAQq1B,EAAQD,EAAO,GAAI,EAAGA,GAC1D,GAAItY,GAAK,EAAG,OAAQuY,EAAQD,EAAOp1B,EAAI,GAAIA,EAAI,EAAGo1B,GAClD,IAAIp1B,EACAX,GAAKW,EAAI,GAAK8c,EACdR,EAAKnU,KAAKiL,MAAM/T,GAChBm7B,GAAUnF,EAAQD,EAAO9Y,GAAKA,EAAI8Y,GAEtC,OAAOoF,IADOnF,EAAQD,EAAO9Y,EAAK,GAAIA,EAAK,EAAG8Y,GACpBoF,IAAWn7B,EAAIid,EART,CASlC,CCzBe,YAAS8Y,EAAQhtB,EAAKG,GACnC,OAAOJ,KAAKmxB,MAAM/wB,EAAMH,IAAQ,GAAKkyB,GAASlF,EAAQ,KAAQkF,GAASlF,EAAQ,MAASjtB,KAAK2F,IAAI5O,EAAMk2B,IAAU,EAAI,IACvH,CCFe,YAASA,EAAQhtB,EAAKG,GACnC,OAAOJ,KAAKmxB,MAAM/wB,EAAMH,IAAQ,IAAM8tB,EAAUd,GAAUjtB,KAAK2F,IAAI5O,EAAMk2B,IAAU,EAAI,IACzF,CCLe,SAASuF,GAASvF,EAAQC,GACvC,IAAI9sB,EACAoyB,GAAY,EACZx5B,GAAS,EACb,QAAgBxB,IAAZ01B,EACF,IAAK,MAAM91B,KAAS61B,IAChBj0B,EACW,MAAT5B,IACIgJ,EAAMhJ,QAAkBI,IAAR4I,GAAqBhJ,GAASA,KACpDgJ,EAAMhJ,EAAOo7B,EAAWx5B,QAI5B,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAC7B7sB,EAAMhJ,QAAkBI,IAAR4I,GAAqBhJ,GAASA,KACpDgJ,EAAMhJ,EAAOo7B,EAAWx5B,GAI9B,OAAOw5B,CACT,CCrBe,SAAS1E,GAAKb,EAAQC,GACnC,IAAIn2B,EAAQ,EACRE,EAAM,EACV,QAAgBO,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,EACH,MAAT71B,IAAkBA,GAASA,IAAUA,MACrCL,EAAOE,GAAOG,OAGf,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAAqB71B,GAASA,IAAUA,MACzEL,EAAOE,GAAOG,EAGtB,CACA,GAAIL,EAAO,OAAOE,EAAMF,CAC1B,CChBe,YAASk2B,EAAQC,GAC9B,OAAOiF,GAASlF,EAAQ,GAAKC,EAC/B,CCEe,SAASuF,GAAMC,GAC5B,OAAOx6B,MAAMC,KAPf,UAAkBu6B,GAChB,IAAK,MAAMvF,KAASuF,QACXvF,CAEX,CAGoBwF,CAAQD,GAC5B,CCRe,SAASE,GAAS3F,EAAQC,GACvC,IAAIjtB,EACA2yB,GAAY,EACZ55B,GAAS,EACb,QAAgBxB,IAAZ01B,EACF,IAAK,MAAM91B,KAAS61B,IAChBj0B,EACW,MAAT5B,IACI6I,EAAM7I,QAAkBI,IAARyI,GAAqB7I,GAASA,KACpD6I,EAAM7I,EAAOw7B,EAAW55B,QAI5B,IAAK,IAAI5B,KAAS61B,EACiC,OAA5C71B,EAAQ81B,EAAQ91B,IAAS4B,EAAOi0B,MAC7BhtB,EAAM7I,QAAkBI,IAARyI,GAAqB7I,GAASA,KACpD6I,EAAM7I,EAAOw7B,EAAW55B,GAI9B,OAAO45B,CACT,CCrBe,SAASC,GAAM5F,EAAQ6F,EAASC,IAC7C,MAAMF,EAAQ,GACd,IAAIn9B,EACA+F,GAAQ,EACZ,IAAK,MAAMrE,KAAS61B,EACdxxB,GAAOo3B,EAAMz9B,KAAK09B,EAAOp9B,EAAU0B,IACvC1B,EAAW0B,EACXqE,GAAQ,EAEV,OAAOo3B,CACT,CAEO,SAASE,GAAKr5B,EAAGC,GACtB,MAAO,CAACD,EAAGC,EACb,CCde,YAASH,EAAO4M,EAAM1W,GACnC8J,GAASA,EAAO4M,GAAQA,EAAM1W,GAAQmI,EAAIwP,UAAUlQ,QAAU,GAAKiP,EAAO5M,EAAOA,EAAQ,EAAG,GAAK3B,EAAI,EAAI,GAAKnI,EAM9G,IAJA,IAAIwH,GAAK,EACLW,EAAoD,EAAhDmI,KAAKI,IAAI,EAAGJ,KAAKmxB,MAAM/qB,EAAO5M,GAAS9J,IAC3CsjC,EAAQ,IAAI96B,MAAML,KAEbX,EAAIW,GACXm7B,EAAM97B,GAAKsC,EAAQtC,EAAIxH,EAGzB,OAAOsjC,CACT,CCVe,SAASC,GAAMhG,EAAQ5zB,EAAUmzB,GAC9C,IAAIvsB,EACAizB,GAAU,EACd,GAAuB,IAAnB75B,EAAQlC,OAAc,CACxB,IAAI0c,EACJ,IAAK,MAAMsf,KAAWlG,EAAQ,CAC5B,MAAM71B,EAAQiC,EAAQ85B,IAClBD,EACE1G,EAAUp1B,EAAOyc,GAAY,EACD,IAA5B2Y,EAAUp1B,EAAOA,MACrB6I,EAAMkzB,EACNtf,EAAWzc,EACX87B,GAAU,EAEd,CACF,MACE,IAAK,MAAM97B,KAAS61B,GACdiG,EACE75B,EAAQjC,EAAO6I,GAAO,EACI,IAA1B5G,EAAQjC,EAAOA,MACnB6I,EAAM7I,EACN87B,GAAU,GAIhB,OAAOjzB,CACT,CCzBe,SAASmzB,GAAWnG,EAAQ5zB,EAAUmzB,GACnD,GAAuB,IAAnBnzB,EAAQlC,OAAc,OAAOy7B,GAAS3F,EAAQ5zB,GAClD,IAAIwa,EACA5T,GAAO,EACPjH,GAAS,EACb,IAAK,MAAM5B,KAAS61B,IAChBj0B,GACEiH,EAAM,EACsB,IAA1B5G,EAAQjC,EAAOA,GACfiC,EAAQjC,EAAOyc,GAAY,KAC/BA,EAAWzc,EACX6I,EAAMjH,GAGV,OAAOiH,CACT,CChBe,SAASozB,GAASpG,EAAQ5zB,EAAUmzB,GACjD,IAAIpsB,EACA8yB,GAAU,EACd,GAAuB,IAAnB75B,EAAQlC,OAAc,CACxB,IAAI2c,EACJ,IAAK,MAAMqf,KAAWlG,EAAQ,CAC5B,MAAM71B,EAAQiC,EAAQ85B,IAClBD,EACE1G,EAAUp1B,EAAO0c,GAAY,EACD,IAA5B0Y,EAAUp1B,EAAOA,MACrBgJ,EAAM+yB,EACNrf,EAAW1c,EACX87B,GAAU,EAEd,CACF,MACE,IAAK,MAAM97B,KAAS61B,GACdiG,EACE75B,EAAQjC,EAAOgJ,GAAO,EACI,IAA1B/G,EAAQjC,EAAOA,MACnBgJ,EAAMhJ,EACN87B,GAAU,GAIhB,OAAO9yB,CACT,CCzBe,SAASkzB,GAAcrG,EAAQ5zB,EAAUmzB,GACtD,GAAuB,IAAnBnzB,EAAQlC,OAAc,OAAOq7B,GAASvF,EAAQ5zB,GAClD,IAAIya,EACA1T,GAAO,EACPpH,GAAS,EACb,IAAK,MAAM5B,KAAS61B,IAChBj0B,GACEoH,EAAM,EACsB,IAA1B/G,EAAQjC,EAAOA,GACfiC,EAAQjC,EAAO0c,GAAY,KAC/BA,EAAW1c,EACXgJ,EAAMpH,GAGV,OAAOoH,CACT,CChBe,SAASmzB,GAAKtG,EAAQ5zB,GACnC,MAAML,EAAQo6B,GAAWnG,EAAQ5zB,GACjC,OAAOL,EAAQ,OAAIxB,EAAYwB,CACjC,CCLA,OAAew6B,GAASxzB,KAAKgG,QAEtB,SAASwtB,GAASxtB,GACvB,OAAO,SAAiBmnB,EAAOhZ,EAAK,EAAGC,EAAK+Y,EAAMh2B,QAChD,IAAIiO,EAAIgP,GAAMD,GAAMA,GACpB,KAAO/O,GAAG,CACR,MAAMlO,EAAI8O,IAAWZ,IAAM,EAAGgM,EAAI+b,EAAM/nB,EAAI+O,GAC5CgZ,EAAM/nB,EAAI+O,GAAMgZ,EAAMj2B,EAAIid,GAC1BgZ,EAAMj2B,EAAIid,GAAM/C,CAClB,CACA,OAAO+b,CACT,CACF,CCZe,SAASl2B,GAAIg2B,EAAQC,GAClC,IAAIj2B,EAAM,EACV,QAAgBO,IAAZ01B,EACF,IAAK,IAAI91B,KAAS61B,GACZ71B,GAASA,KACXH,GAAOG,OAGN,CACL,IAAI4B,GAAS,EACb,IAAK,IAAI5B,KAAS61B,GACZ71B,GAAS81B,EAAQ91B,IAAS4B,EAAOi0B,MACnCh2B,GAAOG,EAGb,CACA,OAAOH,CACT,CCfe,YAASw8B,GACtB,KAAM57B,EAAI47B,EAAOt8B,QAAS,MAAO,GACjC,IAAK,IAAID,GAAK,EAAGkO,EAAInF,GAAIwzB,EAAQ,IAASC,EAAY,IAAIx7B,MAAMkN,KAAMlO,EAAIkO,GACxE,IAAK,IAAYvN,EAARuQ,GAAK,EAAMsL,EAAMggB,EAAUx8B,GAAK,IAAIgB,MAAML,KAAMuQ,EAAIvQ,GAC3D6b,EAAItL,GAAKqrB,EAAOrrB,GAAGlR,GAGvB,OAAOw8B,CACT,CAEA,SAAS,GAAOn7B,GACd,OAAOA,EAAEpB,MACX,CCZe,cACb,OAAOu8B,GAAUrsB,UACnB,CCJe,SAASssB,GAAM1G,EAAQ/M,GACpC,GAAoB,mBAATA,EAAqB,MAAM,IAAIgD,UAAU,0BACpD,IAAIlqB,GAAS,EACb,IAAK,MAAM5B,KAAS61B,EAClB,IAAK/M,EAAK9oB,IAAS4B,EAAOi0B,GACxB,OAAO,EAGX,OAAO,CACT,CCTe,SAASS,GAAKT,EAAQ/M,GACnC,GAAoB,mBAATA,EAAqB,MAAM,IAAIgD,UAAU,0BACpD,IAAIlqB,GAAS,EACb,IAAK,MAAM5B,KAAS61B,EAClB,GAAI/M,EAAK9oB,IAAS4B,EAAOi0B,GACvB,OAAO,EAGX,OAAO,CACT,CCTe,SAAS2G,GAAO3G,EAAQ/M,GACrC,GAAoB,mBAATA,EAAqB,MAAM,IAAIgD,UAAU,0BACpD,MAAMiK,EAAQ,GACd,IAAIn0B,GAAS,EACb,IAAK,MAAM5B,KAAS61B,EACd/M,EAAK9oB,IAAS4B,EAAOi0B,IACvBE,EAAM/3B,KAAKgC,GAGf,OAAO+1B,CACT,CCVe,SAAS,GAAIF,EAAQ4G,GAClC,GAAuC,mBAA5B5G,EAAOxyB,OAAOC,UAA0B,MAAM,IAAIwoB,UAAU,0BACvE,GAAsB,mBAAX2Q,EAAuB,MAAM,IAAI3Q,UAAU,4BACtD,OAAOhrB,MAAMC,KAAK80B,GAAQ,CAAC71B,EAAO4B,IAAU66B,EAAOz8B,EAAO4B,EAAOi0B,IACnE,CCJe,SAASvP,GAAOuP,EAAQM,EAASn2B,GAC9C,GAAuB,mBAAZm2B,EAAwB,MAAM,IAAIrK,UAAU,6BACvD,MAAMxoB,EAAWuyB,EAAOxyB,OAAOC,YAC/B,IAAIo5B,EAAM56B,EAAMF,GAAS,EACzB,GAAIqO,UAAUlQ,OAAS,EAAG,CAExB,KADE28B,OAAM18B,SAASsD,EAASxB,QACtB46B,EAAM,SACR96B,CACJ,CACA,OAAS86B,OAAM18B,MAAO8B,GAAQwB,EAASxB,SAAU46B,GAC/C18B,EAAQm2B,EAAQn2B,EAAO8B,IAAQF,EAAOi0B,GAExC,OAAO71B,CACT,CCbe,SAASwD,GAAQqyB,GAC9B,GAAuC,mBAA5BA,EAAOxyB,OAAOC,UAA0B,MAAM,IAAIwoB,UAAU,0BACvE,OAAOhrB,MAAMC,KAAK80B,GAAQryB,SAC5B,CCHe,SAASm5B,GAAW9G,KAAW+G,GAC5C/G,EAAS,IAAIiC,IAAIjC,GACjB,IAAK,MAAMgH,KAASD,EAClB,IAAK,MAAM58B,KAAS68B,EAClBhH,EAAOzlB,OAAOpQ,GAGlB,OAAO61B,CACT,CCRe,SAASiH,GAASjH,EAAQgH,GACvC,MAAMv5B,EAAWu5B,EAAMx5B,OAAOC,YAAa1E,EAAM,IAAIk5B,IACrD,IAAK,MAAM/d,KAAK8b,EAAQ,CACtB,GAAIj3B,EAAI84B,IAAI3d,GAAI,OAAO,EACvB,IAAI/Z,EAAO08B,EACX,OAAS18B,QAAO08B,QAAQp5B,EAASxB,UAC3B46B,GADoC,CAExC,GAAI3gB,OAAOghB,GAAGhjB,EAAG/Z,GAAQ,OAAO,EAChCpB,EAAI6U,IAAIzT,EACV,CACF,CACA,OAAO,CACT,CCZe,SAASpB,GAAIi3B,GAC1B,OAAOA,aAAkBiC,IAAMjC,EAAS,IAAIiC,IAAIjC,EAClD,CCAe,SAASlG,GAAakG,KAAW+G,GAC9C/G,EAAS,IAAIiC,IAAIjC,GACjB+G,EAASA,EAAOtpB,IAAI1U,IACpBo+B,EAAK,IAAK,MAAMh9B,KAAS61B,EACvB,IAAK,MAAMgH,KAASD,EAClB,IAAKC,EAAMnF,IAAI13B,GAAQ,CACrB61B,EAAOzlB,OAAOpQ,GACd,SAASg9B,CACX,CAGJ,OAAOnH,CACT,CCde,SAASoH,GAASpH,EAAQgH,GACvC,MAAMv5B,EAAWuyB,EAAOxyB,OAAOC,YAAa1E,EAAM,IAAIk5B,IACtD,IAAK,MAAMoF,KAAKL,EAAO,CACrB,GAAIj+B,EAAI84B,IAAIwF,GAAI,SAChB,IAAIl9B,EAAO08B,EACX,OAAS18B,QAAO08B,QAAQp5B,EAASxB,SAAS,CACxC,GAAI46B,EAAM,OAAO,EAEjB,GADA99B,EAAI6U,IAAIzT,GACJ+b,OAAOghB,GAAGG,EAAGl9B,GAAQ,KAC3B,CACF,CACA,OAAO,CACT,CCVe,SAASm9B,GAAOtH,EAAQgH,GACrC,OAAOI,GAASJ,EAAOhH,EACzB,CCJe,SAASuH,MAASR,GAC/B,MAAMh+B,EAAM,IAAIk5B,IAChB,IAAK,MAAM+E,KAASD,EAClB,IAAK,MAAMM,KAAKL,EACdj+B,EAAI6U,IAAIypB,GAGZ,OAAOt+B,CACT,C,uBCIA,IAAIy+B,EAAO,EAAQ,MAKfC,EAAS,EAAQ,MAKjBC,EAAS,EAAQ,MAQjBC,EAAY,EAAQ,MASpBC,EAAU,EAAQ,MAOlBC,EAAS,EAAQ,MAIjBC,EAAK,EAAQ,MAEjBA,EAAGN,KAAOA,EACVM,EAAGL,OAASA,EACZK,EAAGJ,OAASA,EACZI,EAAGH,UAAYA,EACfG,EAAGF,QAAUA,EACbE,EAAGD,OAASA,EAEZE,EAAO5c,QAAU2c,C,uBC3DjB,OA2BA,SAAUE,EAAQD,EAAQE,GAE1B,SAASC,EAAKC,GACZ,IAgDIv9B,EAhDAw9B,EAAK1mC,KAAM2mC,GAgDXz9B,EAAI,WAEG,SAASP,GAClBA,EAAOi+B,OAAOj+B,GACd,IAAK,IAAIJ,EAAI,EAAGA,EAAII,EAAKH,OAAQD,IAAK,CAEpC,IAAI2V,EAAI,oBADRhV,GAAKP,EAAKk+B,WAAWt+B,IAGrB2V,GADAhV,EAAIgV,IAAM,EAGVhV,GADAgV,GAAKhV,KACK,EAEVA,GAAS,YADTgV,GAAKhV,EAEP,CACA,OAAmB,wBAAXA,IAAM,EAChB,GA7DAw9B,EAAGn8B,KAAO,WACR,IAAIkY,EAAI,QAAUikB,EAAGrZ,GAAY,uBAAPqZ,EAAGt7B,EAG7B,OAFAs7B,EAAGrZ,GAAKqZ,EAAGpZ,GACXoZ,EAAGpZ,GAAKoZ,EAAGI,GACJJ,EAAGI,GAAKrkB,GAAKikB,EAAGt7B,EAAQ,EAAJqX,EAC7B,EAGAikB,EAAGt7B,EAAI,EACPs7B,EAAGrZ,GAAKsZ,EAAK,KACbD,EAAGpZ,GAAKqZ,EAAK,KACbD,EAAGI,GAAKH,EAAK,KACbD,EAAGrZ,IAAMsZ,EAAKF,GACVC,EAAGrZ,GAAK,IAAKqZ,EAAGrZ,IAAM,GAC1BqZ,EAAGpZ,IAAMqZ,EAAKF,GACVC,EAAGpZ,GAAK,IAAKoZ,EAAGpZ,IAAM,GAC1BoZ,EAAGI,IAAMH,EAAKF,GACVC,EAAGI,GAAK,IAAKJ,EAAGI,IAAM,GAC1BH,EAAO,IACT,CAEA,SAAS96B,EAAKknB,EAAGtQ,GAKf,OAJAA,EAAErX,EAAI2nB,EAAE3nB,EACRqX,EAAE4K,GAAK0F,EAAE1F,GACT5K,EAAE6K,GAAKyF,EAAEzF,GACT7K,EAAEqkB,GAAK/T,EAAE+T,GACFrkB,CACT,CAEA,SAASskB,EAAKN,EAAMO,GAClB,IAAIC,EAAK,IAAIT,EAAKC,GACd1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAOmf,EAAG18B,KAUd,OATAud,EAAKof,MAAQ,WAAa,OAAoB,WAAZD,EAAG18B,OAAwB,CAAG,EAChEud,EAAKqf,OAAS,WACZ,OAAOrf,IAAmC,uBAAhB,QAATA,IAAoB,EACvC,EACAA,EAAKsf,MAAQtf,EACTiI,IACmB,iBAAX,GAAqBlkB,EAAKkkB,EAAOkX,GAC3Cnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAwBIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAK8lC,KAAOiB,CAGb,CAhFD,CAiFE/mC,E,WAEA,O,uBC9GF,OAIA,SAAUsmC,EAAQD,EAAQE,GAE1B,SAASc,EAAOZ,GACd,IAAIC,EAAK1mC,KAAMsnC,EAAU,GAGzBZ,EAAGn8B,KAAO,WACR,IAAIS,EAAI07B,EAAG17B,EAAGI,EAAIs7B,EAAGt7B,EAAGxB,EAAI88B,EAAG98B,EAAGmB,EAAI27B,EAAG37B,EAQzC,OAPAC,EAAKA,GAAK,GAAOA,IAAM,EAAKI,EAC5BA,EAAKA,EAAIxB,EAAK,EACdA,EAAKA,GAAK,GAAOA,IAAM,EAAKmB,EAC5BA,EAAKA,EAAIC,EAAK,EACd07B,EAAG17B,EAAIA,EAAKA,GAAK,GAAOA,IAAM,GAAMI,EACpCs7B,EAAGt7B,EAAIA,EAAKA,EAAIxB,EAAK,EACrB88B,EAAG98B,EAAKA,GAAK,GAAOwB,IAAM,GAAML,EACzB27B,EAAG37B,EAAKA,EAAIC,EAAK,CAC1B,EAkBA07B,EAAG37B,EAAI,EACP27B,EAAG17B,EAAI,EACP07B,EAAGt7B,GAAI,WACPs7B,EAAG98B,EAAI,WAEH68B,IAASp1B,KAAKiL,MAAMmqB,IAEtBC,EAAG37B,EAAK07B,EAAO,WAAe,EAC9BC,EAAG17B,EAAW,EAAPy7B,GAGPa,GAAWb,EAIb,IAAK,IAAIn7B,EAAI,EAAGA,EAAIg8B,EAAQ9+B,OAAS,GAAI8C,IACvCo7B,EAAG17B,GAA6B,EAAxBs8B,EAAQT,WAAWv7B,GAC3Bo7B,EAAGn8B,MAEP,CAEA,SAASsB,EAAKknB,EAAGtQ,GAKf,OAJAA,EAAE1X,EAAIgoB,EAAEhoB,EACR0X,EAAEzX,EAAI+nB,EAAE/nB,EACRyX,EAAErX,EAAI2nB,EAAE3nB,EACRqX,EAAE7Y,EAAImpB,EAAEnpB,EACD6Y,CACT,CAEA,SAASskB,EAAKN,EAAMO,GAClB,IAAIC,EAAK,IAAII,EAAOZ,GAChB1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAO,WAAa,OAAQmf,EAAG18B,SAAW,GAAK,UAAa,EAehE,OAdAud,EAAKqf,OAAS,WACZ,GACE,IAEII,IAFMN,EAAG18B,SAAW,KACb08B,EAAG18B,SAAW,GAAK,aACF,GAAK,UACf,IAAXg9B,GACT,OAAOA,CACT,EACAzf,EAAKof,MAAQD,EAAG18B,KAChBud,EAAKsf,MAAQtf,EACTiI,IACmB,iBAAX,GAAqBlkB,EAAKkkB,EAAOkX,GAC3Cnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAEIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAKmmC,OAASY,CAGf,CA5FD,CA6FE/mC,E,WAEA,O,uBCnGF,OAGA,SAAUsmC,EAAQD,EAAQE,GAE1B,SAASc,EAAOZ,GACd,IAAIC,EAAK1mC,KAAMsnC,EAAU,GAEzBZ,EAAGnjC,EAAI,EACPmjC,EAAGljC,EAAI,EACPkjC,EAAGnqB,EAAI,EACPmqB,EAAGzoB,EAAI,EAGPyoB,EAAGn8B,KAAO,WACR,IAAIkY,EAAIikB,EAAGnjC,EAAKmjC,EAAGnjC,GAAK,GAIxB,OAHAmjC,EAAGnjC,EAAImjC,EAAGljC,EACVkjC,EAAGljC,EAAIkjC,EAAGnqB,EACVmqB,EAAGnqB,EAAImqB,EAAGzoB,EACHyoB,EAAGzoB,GAAMyoB,EAAGzoB,IAAM,GAAMwE,EAAKA,IAAM,CAC5C,EAEIgkB,KAAiB,EAAPA,GAEZC,EAAGnjC,EAAIkjC,EAGPa,GAAWb,EAIb,IAAK,IAAIn7B,EAAI,EAAGA,EAAIg8B,EAAQ9+B,OAAS,GAAI8C,IACvCo7B,EAAGnjC,GAA6B,EAAxB+jC,EAAQT,WAAWv7B,GAC3Bo7B,EAAGn8B,MAEP,CAEA,SAASsB,EAAKknB,EAAGtQ,GAKf,OAJAA,EAAElf,EAAIwvB,EAAExvB,EACRkf,EAAEjf,EAAIuvB,EAAEvvB,EACRif,EAAElG,EAAIwW,EAAExW,EACRkG,EAAExE,EAAI8U,EAAE9U,EACDwE,CACT,CAEA,SAASskB,EAAKN,EAAMO,GAClB,IAAIC,EAAK,IAAII,EAAOZ,GAChB1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAO,WAAa,OAAQmf,EAAG18B,SAAW,GAAK,UAAa,EAehE,OAdAud,EAAKqf,OAAS,WACZ,GACE,IAEII,IAFMN,EAAG18B,SAAW,KACb08B,EAAG18B,SAAW,GAAK,aACF,GAAK,UACf,IAAXg9B,GACT,OAAOA,CACT,EACAzf,EAAKof,MAAQD,EAAG18B,KAChBud,EAAKsf,MAAQtf,EACTiI,IACmB,iBAAX,GAAqBlkB,EAAKkkB,EAAOkX,GAC3Cnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAEIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAK+lC,OAASgB,CAGf,CAvED,CAwEE/mC,E,WAEA,O,uBC7EF,OAyBA,SAAUsmC,EAAQD,EAAQE,GAE1B,SAASc,EAAOZ,GACd,IAAIC,EAAK1mC,KAGT0mC,EAAGn8B,KAAO,WACR,IACwBkY,EAAGD,EADvBvE,EAAIyoB,EAAGzoB,EACPupB,EAAId,EAAGc,EAAGj/B,EAAIm+B,EAAGn+B,EAcrB,OAZAm+B,EAAGzoB,EAAIA,EAAKA,EAAI,WAAc,EAE9BuE,EAAIglB,EAAGj/B,EAAI,GAAM,KACjBka,EAAI+kB,EAAEj/B,EAAMA,EAAI,EAAK,KACrBia,GAAKA,GAAK,GACVC,GAAKA,GAAK,GACVD,GAAKA,IAAM,GACXC,GAAKA,IAAM,GAEXD,EAAIglB,EAAEj/B,GAAKia,EAAIC,EACfikB,EAAGn+B,EAAIA,EAECia,GAAKvE,EAAKA,IAAM,IAAQ,CAClC,EAEA,SAAcyoB,EAAID,GAChB,IAAIhkB,EAAGD,EAAGja,EAAGkR,EAAGwE,EAAGupB,EAAI,GAAIC,EAAQ,IAYnC,IAXIhB,KAAiB,EAAPA,IAEZjkB,EAAIikB,EACJA,EAAO,OAGPA,GAAc,KACdjkB,EAAI,EACJilB,EAAQp2B,KAAKI,IAAIg2B,EAAOhB,EAAKj+B,SAG1BD,EAAI,EAAGkR,GAAK,GAAIA,EAAIguB,IAAShuB,EAE5BgtB,IAAMjkB,GAAKikB,EAAKI,YAAYptB,EAAI,IAAMgtB,EAAKj+B,SAErC,IAANiR,IAASwE,EAAIuE,GACjBA,GAAKA,GAAK,GACVA,GAAKA,IAAM,GACXA,GAAKA,GAAK,EACVA,GAAKA,IAAM,GACP/I,GAAK,IACPwE,EAAKA,EAAI,WAAc,EAEvB1V,EAAK,IADLka,EAAK+kB,EAAM,IAAJ/tB,IAAa+I,EAAIvE,GACT1V,EAAI,EAAI,GAW3B,IAPIA,GAAK,MACPi/B,EAA+B,KAA5Bf,GAAQA,EAAKj+B,QAAU,KAAa,GAKzCD,EAAI,IACCkR,EAAI,IAASA,EAAI,IAAKA,EACzB+I,EAAIglB,EAAGj/B,EAAI,GAAM,KACjBka,EAAI+kB,EAAEj/B,EAAMA,EAAI,EAAK,KACrBia,GAAKA,GAAK,GACVC,GAAKA,GAAK,GACVD,GAAKA,IAAM,GACXC,GAAKA,IAAM,GACX+kB,EAAEj/B,GAAKia,EAAIC,EAGbikB,EAAGzoB,EAAIA,EACPyoB,EAAGc,EAAIA,EACPd,EAAGn+B,EAAIA,CACT,CAEAqyB,CAAK8L,EAAID,EACX,CAEA,SAAS56B,EAAKknB,EAAGtQ,GAIf,OAHAA,EAAEla,EAAIwqB,EAAExqB,EACRka,EAAExE,EAAI8U,EAAE9U,EACRwE,EAAE+kB,EAAIzU,EAAEyU,EAAE7lB,QACHc,CACT,CAEA,SAASskB,EAAKN,EAAMO,GACN,MAARP,IAAcA,GAAQ,IAAK9T,MAC/B,IAAIsU,EAAK,IAAII,EAAOZ,GAChB1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAO,WAAa,OAAQmf,EAAG18B,SAAW,GAAK,UAAa,EAehE,OAdAud,EAAKqf,OAAS,WACZ,GACE,IAEII,IAFMN,EAAG18B,SAAW,KACb08B,EAAG18B,SAAW,GAAK,aACF,GAAK,UACf,IAAXg9B,GACT,OAAOA,CACT,EACAzf,EAAKof,MAAQD,EAAG18B,KAChBud,EAAKsf,MAAQtf,EACTiI,IACEA,EAAMyX,GAAG37B,EAAKkkB,EAAOkX,GACzBnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAEIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAKkmC,QAAUa,CAGhB,CApHD,CAqHE/mC,E,WAEA,O,uBChJF,OAKA,SAAUsmC,EAAQD,EAAQE,GAE1B,SAASc,EAAOZ,GACd,IAAIC,EAAK1mC,KAGT0mC,EAAGn8B,KAAO,WAER,IAAwBkY,EAAGD,EAAvBglB,EAAId,EAAGnjC,EAAGgF,EAAIm+B,EAAGn+B,EAQrB,OAPAka,EAAI+kB,EAAEj/B,GAAoBia,GAAhBC,GAAMA,IAAM,GAAaA,GAAK,GACpBD,IAApBC,EAAI+kB,EAAGj/B,EAAI,EAAK,IAAcka,IAAM,GAChBD,IAApBC,EAAI+kB,EAAGj/B,EAAI,EAAK,IAAcka,IAAM,EAChBD,IAApBC,EAAI+kB,EAAGj/B,EAAI,EAAK,IAAcka,GAAK,EACnCA,EAAI+kB,EAAGj/B,EAAI,EAAK,GAAuBia,IAAnBC,GAASA,GAAK,IAAeA,GAAK,EACtD+kB,EAAEj/B,GAAKia,EACPkkB,EAAGn+B,EAAKA,EAAI,EAAK,EACVia,CACT,EAEA,SAAckkB,EAAID,GAChB,IAAIhtB,EAAM+tB,EAAI,GAEd,GAAIf,KAAiB,EAAPA,GAERe,EAAE,GAAKf,OAIX,IADAA,EAAO,GAAKA,EACPhtB,EAAI,EAAGA,EAAIgtB,EAAKj+B,SAAUiR,EAC7B+tB,EAAM,EAAJ/tB,GAAU+tB,EAAM,EAAJ/tB,IAAU,GACnBgtB,EAAKI,WAAWptB,GAAK+tB,EAAG/tB,EAAI,EAAK,IAAM,GAIhD,KAAO+tB,EAAEh/B,OAAS,GAAGg/B,EAAE/gC,KAAK,GAC5B,IAAKgT,EAAI,EAAGA,EAAI,GAAc,IAAT+tB,EAAE/tB,KAAYA,GAOnC,IANS,GAALA,EAAY+tB,EAAE,IAAM,EAAYA,EAAE/tB,GAEtCitB,EAAGnjC,EAAIikC,EACPd,EAAGn+B,EAAI,EAGFkR,EAAI,IAAKA,EAAI,IAAKA,EACrBitB,EAAGn8B,MAEP,CAEAqwB,CAAK8L,EAAID,EACX,CAEA,SAAS56B,EAAKknB,EAAGtQ,GAGf,OAFAA,EAAElf,EAAIwvB,EAAExvB,EAAEoe,QACVc,EAAEla,EAAIwqB,EAAExqB,EACDka,CACT,CAEA,SAASskB,EAAKN,EAAMO,GACN,MAARP,IAAcA,GAAQ,IAAK9T,MAC/B,IAAIsU,EAAK,IAAII,EAAOZ,GAChB1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAO,WAAa,OAAQmf,EAAG18B,SAAW,GAAK,UAAa,EAehE,OAdAud,EAAKqf,OAAS,WACZ,GACE,IAEII,IAFMN,EAAG18B,SAAW,KACb08B,EAAG18B,SAAW,GAAK,aACF,GAAK,UACf,IAAXg9B,GACT,OAAOA,CACT,EACAzf,EAAKof,MAAQD,EAAG18B,KAChBud,EAAKsf,MAAQtf,EACTiI,IACEA,EAAMxsB,GAAGsI,EAAKkkB,EAAOkX,GACzBnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAEIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAKimC,UAAYc,CAGlB,CAtFD,CAuFE/mC,E,WAEA,O,uBC9FF,OAGA,SAAUsmC,EAAQD,EAAQE,GAE1B,SAASc,EAAOZ,GACd,IAAIC,EAAK1mC,KAAMsnC,EAAU,GAGzBZ,EAAGn8B,KAAO,WACR,IAAIkY,EAAKikB,EAAGnjC,EAAKmjC,EAAGnjC,IAAM,EAE1B,OADAmjC,EAAGnjC,EAAImjC,EAAGljC,EAAGkjC,EAAGljC,EAAIkjC,EAAGnqB,EAAGmqB,EAAGnqB,EAAImqB,EAAGzoB,EAAGyoB,EAAGzoB,EAAIyoB,EAAGlkB,GACzCkkB,EAAG98B,EAAK88B,EAAG98B,EAAI,OAAS,IAC5B88B,EAAGlkB,EAAKkkB,EAAGlkB,EAAKkkB,EAAGlkB,GAAK,EAAOC,EAAKA,GAAK,GAAO,CACtD,EAEAikB,EAAGnjC,EAAI,EACPmjC,EAAGljC,EAAI,EACPkjC,EAAGnqB,EAAI,EACPmqB,EAAGzoB,EAAI,EACPyoB,EAAGlkB,EAAI,EAEHikB,KAAiB,EAAPA,GAEZC,EAAGnjC,EAAIkjC,EAGPa,GAAWb,EAIb,IAAK,IAAIn7B,EAAI,EAAGA,EAAIg8B,EAAQ9+B,OAAS,GAAI8C,IACvCo7B,EAAGnjC,GAA6B,EAAxB+jC,EAAQT,WAAWv7B,GACvBA,GAAKg8B,EAAQ9+B,SACfk+B,EAAG98B,EAAI88B,EAAGnjC,GAAK,GAAKmjC,EAAGnjC,IAAM,GAE/BmjC,EAAGn8B,MAEP,CAEA,SAASsB,EAAKknB,EAAGtQ,GAOf,OANAA,EAAElf,EAAIwvB,EAAExvB,EACRkf,EAAEjf,EAAIuvB,EAAEvvB,EACRif,EAAElG,EAAIwW,EAAExW,EACRkG,EAAExE,EAAI8U,EAAE9U,EACRwE,EAAED,EAAIuQ,EAAEvQ,EACRC,EAAE7Y,EAAImpB,EAAEnpB,EACD6Y,CACT,CAEA,SAASskB,EAAKN,EAAMO,GAClB,IAAIC,EAAK,IAAII,EAAOZ,GAChB1W,EAAQiX,GAAQA,EAAKjX,MACrBjI,EAAO,WAAa,OAAQmf,EAAG18B,SAAW,GAAK,UAAa,EAehE,OAdAud,EAAKqf,OAAS,WACZ,GACE,IAEII,IAFMN,EAAG18B,SAAW,KACb08B,EAAG18B,SAAW,GAAK,aACF,GAAK,UACf,IAAXg9B,GACT,OAAOA,CACT,EACAzf,EAAKof,MAAQD,EAAG18B,KAChBud,EAAKsf,MAAQtf,EACTiI,IACmB,iBAAX,GAAqBlkB,EAAKkkB,EAAOkX,GAC3Cnf,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo7B,EAAI,CAAC,EAAI,GAE1Cnf,CACT,CAEIue,GAAUA,EAAO5c,QACnB4c,EAAO5c,QAAUsd,EACR,QAAU,YACe,KAAlC,aAAoB,OAAOA,CAAO,gCAElC/mC,KAAKgmC,OAASe,CAGf,CA5ED,CA6EE/mC,E,WAEA,O,uBClFF,OAwBA,SAAWsmC,EAAQoB,EAAMC,GAKzB,IAQIC,EARAlnC,EAAQ,IAIRmnC,EAAaF,EAAK3wB,IAAItW,EAHb,GAITonC,EAAeH,EAAK3wB,IAAI,EAHf,IAIT+wB,EAA0B,EAAfD,EACXE,EAAOtnC,EAAQ,EAOnB,SAASunC,EAAWxB,EAAMyB,EAAS/9B,GACjC,IAAImG,EAAM,GAIN63B,EAAYC,EAAOpE,GAHvBkE,EAAsB,GAAXA,EAAmB,CAAEG,SAAS,GAAUH,GAAW,CAAC,GAIrDG,QAAU,CAAC5B,EAAM6B,EAASZ,IACzB,MAARjB,EA8IL,WACE,IACE,IAAIhB,EAQJ,OAPImC,IAAenC,EAAMmC,EAAWW,aAElC9C,EAAMA,EAAI/kC,IAEV+kC,EAAM,IAAI+C,WAAW9nC,IACpB4lC,EAAOmC,QAAUnC,EAAOoC,UAAUC,gBAAgBlD,IAE9C6C,EAAS7C,EAClB,CAAE,MAAOhS,GACP,IAAImV,EAAUtC,EAAOuC,UACjBC,EAAUF,GAAWA,EAAQE,QACjC,MAAO,EAAE,IAAInW,KAAM2T,EAAQwC,EAASxC,EAAOyC,OAAQT,EAASZ,GAC9D,CACF,CA9JqBsB,GAAavC,EAAM,GAAIn2B,GAGtC24B,EAAO,IAAIC,EAAK54B,GAIhBwX,EAAO,WAIT,IAHA,IAAI5e,EAAI+/B,EAAKE,EA5BJ,GA6BLv/B,EAAIi+B,EACJtkC,EAAI,EACD2F,EAAI4+B,GACT5+B,GAAKA,EAAI3F,GAAK7C,EACdkJ,GAAKlJ,EACL6C,EAAI0lC,EAAKE,EAAE,GAEb,KAAOjgC,GAAK6+B,GACV7+B,GAAK,EACLU,GAAK,EACLrG,KAAO,EAET,OAAQ2F,EAAI3F,GAAKqG,CACnB,EAUA,OARAke,EAAKof,MAAQ,WAAa,OAAmB,EAAZ+B,EAAKE,EAAE,EAAQ,EAChDrhB,EAAKsf,MAAQ,WAAa,OAAO6B,EAAKE,EAAE,GAAK,UAAa,EAC1DrhB,EAAKqf,OAASrf,EAGdsgB,EAAOE,EAASW,EAAKG,GAAI1B,IAGjBQ,EAAQmB,MAAQl/B,GACpB,SAAS2d,EAAM2e,EAAM6C,EAAcvZ,GAUjC,OATIA,IAEEA,EAAMqZ,GAAKv9B,EAAKkkB,EAAOkZ,GAE3BnhB,EAAKiI,MAAQ,WAAa,OAAOlkB,EAAKo9B,EAAM,CAAC,EAAI,GAK/CK,GAAgB3B,EAAY,OAAI7f,EAAa2e,GAIrC3e,CACd,GACJA,EACAqgB,EACA,WAAYD,EAAUA,EAAQ5B,OAAUtmC,MAAQ2nC,EAChDO,EAAQnY,MACV,CAYA,SAASmZ,EAAK54B,GACZ,IAAImS,EAAG8mB,EAASj5B,EAAI9H,OAChBk+B,EAAK1mC,KAAMuI,EAAI,EAAGkR,EAAIitB,EAAGn+B,EAAIm+B,EAAGjtB,EAAI,EAAGnC,EAAIovB,EAAG0C,EAAI,GAMtD,IAHKG,IAAUj5B,EAAM,CAACi5B,MAGfhhC,EAAI7H,GACT4W,EAAE/O,GAAKA,IAET,IAAKA,EAAI,EAAGA,EAAI7H,EAAO6H,IACrB+O,EAAE/O,GAAK+O,EAAEmC,EAAIuuB,EAAQvuB,EAAInJ,EAAI/H,EAAIghC,IAAW9mB,EAAInL,EAAE/O,KAClD+O,EAAEmC,GAAKgJ,GAIRikB,EAAGyC,EAAI,SAAS/gC,GAIf,IAFA,IAAIqa,EAAGriB,EAAI,EACPmI,EAAIm+B,EAAGn+B,EAAGkR,EAAIitB,EAAGjtB,EAAGnC,EAAIovB,EAAG0C,EACxBhhC,KACLqa,EAAInL,EAAE/O,EAAIy/B,EAAQz/B,EAAI,GACtBnI,EAAIA,EAAIM,EAAQ4W,EAAE0wB,GAAS1wB,EAAE/O,GAAK+O,EAAEmC,EAAIuuB,EAAQvuB,EAAIgJ,KAAQnL,EAAEmC,GAAKgJ,IAGrE,OADAikB,EAAGn+B,EAAIA,EAAGm+B,EAAGjtB,EAAIA,EACVrZ,CAIT,GAAGM,EACL,CAMA,SAASmL,EAAKknB,EAAGtQ,GAIf,OAHAA,EAAEla,EAAIwqB,EAAExqB,EACRka,EAAEhJ,EAAIsZ,EAAEtZ,EACRgJ,EAAE2mB,EAAIrW,EAAEqW,EAAEznB,QACHc,CACT,CAMA,SAASuhB,EAAQwF,EAAK//B,GACpB,IAAqCggC,EAAjClC,EAAS,GAAImC,SAAcF,EAC/B,GAAI//B,GAAgB,UAAPigC,EACX,IAAKD,KAAQD,EACX,IAAMjC,EAAO9gC,KAAKu9B,EAAQwF,EAAIC,GAAOhgC,EAAQ,GAAK,CAAE,MAAOgqB,GAAI,CAGnE,OAAQ8T,EAAO/+B,OAAS++B,EAAgB,UAAPmC,EAAkBF,EAAMA,EAAM,IACjE,CAOA,SAASpB,EAAO3B,EAAMn2B,GAEpB,IADA,IAA4Bq5B,EAAxBC,EAAanD,EAAO,GAAWhtB,EAAI,EAChCA,EAAImwB,EAAWphC,QACpB8H,EAAI03B,EAAOvuB,GACTuuB,GAAS2B,GAAyB,GAAhBr5B,EAAI03B,EAAOvuB,IAAWmwB,EAAW/C,WAAWptB,KAElE,OAAO6uB,EAASh4B,EAClB,CA6BA,SAASg4B,EAASv9B,GAChB,OAAO67B,OAAOiD,aAAa5uB,MAAM,EAAGlQ,EACtC,CAeA,GANAq9B,EAAOT,EAAKtwB,SAAUqwB,GAMarB,EAAO5c,QAAS,CACjD4c,EAAO5c,QAAUwe,EAEjB,IACEL,EAAa,EAAQ,KACvB,CAAE,MAAOkC,GAAK,CAChB,WAC0C,KAAxC,aAAoB,OAAO7B,CAAa,+BAQzC,CA9ND,CAiOmB,oBAAT8B,KAAwBA,KAAO/pC,KACvC,GACAqR,K,sFC1PK,MAAM24B,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/HierarchyDefaultTheme.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/BreadcrumbBar.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/HierarchyNode.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/count.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/index.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/each.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/eachAfter.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/eachBefore.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/find.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/sum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/sort.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/path.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/ancestors.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/descendants.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/leaves.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/links.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/hierarchy/iterator.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Hierarchy.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/LinkedHierarchyNode.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/HierarchyLink.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/LinkedHierarchy.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/lcg.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/simulation.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/add.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/quad.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/x.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/y.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/quadtree.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/constant.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/jiggle.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/collide.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/link.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/cover.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/data.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/extent.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/find.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/remove.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/root.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/size.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/visit.js", "webpack://@amcharts/amcharts5/./node_modules/d3-quadtree/src/visitAfter.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/ForceDirected.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/x.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/y.js", "webpack://@amcharts/amcharts5/./node_modules/d3-force/src/manyBody.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Pack.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/round.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/dice.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/partition.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Partition.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Sunburst.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/tree.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Tree.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/slice.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/squarify.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/index.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/binary.js", "webpack://@amcharts/amcharts5/./node_modules/d3-hierarchy/src/treemap/sliceDice.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/Treemap.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/hierarchy/VoronoiTreemap.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/area.js", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/centroid.js", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/hull.js", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/cross.js", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/contains.js", "webpack://@amcharts/amcharts5/./node_modules/d3-polygon/src/length.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-map/build/d3-voronoi-map.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-map/node_modules/d3-dispatch/src/dispatch.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-map/node_modules/d3-timer/src/timer.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-map/node_modules/d3-timer/src/timeout.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-map/node_modules/d3-timer/src/interval.js", "webpack://@amcharts/amcharts5/./node_modules/d3-voronoi-treemap/build/d3-voronoi-treemap.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/build/d3-weighted-voronoi.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/ascending.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/bisector.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/number.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/bisect.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/count.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/cross.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/cumsum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/descending.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/variance.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/deviation.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/extent.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/fsum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/internmap/src/index.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/identity.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/group.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/permute.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/sort.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/groupSort.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/array.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/constant.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/ticks.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/nice.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/threshold/sturges.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/bin.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/max.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/min.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/quickselect.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/quantile.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/threshold/freedmanDiaconis.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/threshold/scott.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/maxIndex.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/mean.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/median.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/merge.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/minIndex.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/pairs.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/range.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/least.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/leastIndex.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/greatest.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/greatestIndex.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/scan.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/shuffle.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/sum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/transpose.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/zip.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/every.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/some.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/filter.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/map.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/reduce.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/reverse.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/difference.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/disjoint.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/set.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/intersection.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/superset.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/subset.js", "webpack://@amcharts/amcharts5/./node_modules/d3-weighted-voronoi/node_modules/d3-array/src/union.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/index.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/alea.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/tychei.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/xor128.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/xor4096.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/xorshift7.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/lib/xorwow.js", "webpack://@amcharts/amcharts5/./node_modules/seedrandom/seedrandom.js", "webpack://@amcharts/amcharts5/./tmp/webpack/hierarchy.js"], "sourcesContent": ["import { Theme } from \"../../core/Theme\";\r\nimport { percent, p100, p50 } from \"../../core/util/Percent\";\r\nimport { ColorSet } from \"../../core/util/ColorSet\";\r\nimport { setColor } from \"../../themes/DefaultTheme\";\r\n\r\nimport * as $ease from \"../../core/util/Ease\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class HierarchyDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\r\n\t\tconst ic = this._root.interfaceColors;\r\n\t\tconst gridLayout = this._root.gridLayout;\r\n\t\tconst r = this.rule.bind(this);\r\n\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/hierarchy\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"Hierarchy\").setAll({\r\n\t\t\tlegendLabelText: \"{category}\",\r\n\t\t\tlegendValueText: \"{sum.formatNumber('#.#')}\",\r\n\t\t\twidth: p100,\r\n\t\t\theight: p100,\r\n\t\t\tcolors: ColorSet.new(this._root, { step: 2 }),\r\n\t\t\tdownDepth: 1,\r\n\t\t\tinitialDepth: 5,\r\n\t\t\tsingleBranchOnly: true,\r\n\t\t\tmaskContent: true,\r\n\t\t\tanimationEasing: $ease.out($ease.cubic)\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\").setAll({\r\n\t\t\ttoggleKey: \"disabled\",\r\n\t\t\tsetStateOnChildren: true,\r\n\t\t\tposition: \"absolute\",\r\n\t\t\tisMeasured: false,\r\n\t\t\tcursorOverStyle: \"pointer\",\r\n\t\t\ttooltipText: \"{category}: {sum}\"\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\", [\"last\"]).setAll({\r\n\t\t\tcursorOverStyle: \"default\"\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"hierarchy\", \"node\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tposition: \"absolute\",\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingRight: 4,\r\n\t\t\t\tpaddingLeft: 4,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.3\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"HierarchyLink\");\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tisMeasured: false,\r\n\t\t\t\tposition: \"absolute\",\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t\tstrokeOpacity: 1,\r\n\t\t\t\tstrength: 0.9,\r\n\t\t\t\tdistance: 1.1\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"grid\");\r\n\t\t}\r\n\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\"]).setAll({\r\n\t\t\tposition: \"absolute\",\r\n\t\t\tfillOpacity: 1,\r\n\t\t\tstrokeOpacity: 0,\r\n\t\t\tradius: 15,\r\n\t\t\ttooltipY: 0\r\n\t\t});\r\n\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\", \"outer\"]).setAll({\r\n\t\t\tposition: \"absolute\",\r\n\t\t\topacity: 1,\r\n\t\t\tfillOpacity: 0,\r\n\t\t\tstrokeDasharray: 0,\r\n\t\t\tstrokeOpacity: 1,\r\n\t\t\tradius: 15,\r\n\t\t\tscale: 1.1,\r\n\t\t\tinteractive: false\r\n\t\t})\r\n\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\", \"outer\"]).states.create(\"disabled\", { opacity: 1, scale: 1.1, strokeDasharray: 2 });\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\", \"outer\"]).states.create(\"hoverDisabled\", { scale: 1.2, opacity: 1, strokeDasharray: 2 });\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\", \"outer\"]).states.create(\"hover\", { scale: 1.05, strokeDasharray: 0 });\r\n\t\tr(\"Circle\", [\"linkedhierarchy\", \"shape\", \"outer\"]).states.create(\"hidden\", { opacity: 0, strokeDasharray: 0 });\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: BreadcrumbBar\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"BreadcrumbBar\").setAll({\r\n\t\t\tpaddingLeft: 8,\r\n\t\t\tlayout: gridLayout\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"breadcrumb\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tpaddingRight: 4,\r\n\t\t\t\tpaddingLeft: 4,\r\n\t\t\t\tcursorOverStyle: \"pointer\",\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}:\",\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"primaryButton\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"breadcrumb\"]).states.create(\"hover\", {});\r\n\t\t\tsetColor(rule, \"fill\", ic, \"primaryButtonHover\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"breadcrumb\"]).states.create(\"down\", { stateAnimationDuration: 0 });\r\n\t\t\tsetColor(rule, \"fill\", ic, \"primaryButtonDown\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"breadcrumb\", \"last\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\tfontWeight: \"bold\",\r\n\t\t\t\tcursorOverStyle: \"default\"\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"primaryButton\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"RoundedRectangle\", [\"breadcrumb\", \"label\", \"background\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tfillOpacity: 0,\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"background\");\r\n\t\t}\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Partition\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Partition\").setAll({\r\n\t\t\tdownDepth: 1,\r\n\t\t\tupDepth: 0,\r\n\t\t\tinitialDepth: 5\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\", [\"partition\"]).setAll({\r\n\t\t\tsetStateOnChildren: false\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\", [\"partition\"]).states.create(\"hidden\", {\r\n\t\t\topacity: 1,\r\n\t\t\tvisible: true\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"partition\", \"node\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tx: p50,\r\n\t\t\t\ty: p50,\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingLeft: 1,\r\n\t\t\t\tpaddingRight: 1,\r\n\t\t\t\trotation: 90,\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.4\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\t\tr(\"Label\", [\"horizontal\", \"partition\", \"node\"]).setAll({\r\n\t\t\trotation: 0\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"RoundedRectangle\", [\"partition\", \"node\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 1,\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t\tcornerRadiusBR: 0,\r\n\t\t\t\tcornerRadiusTR: 0,\r\n\t\t\t\tcornerRadiusBL: 0,\r\n\t\t\t\tcornerRadiusTL: 0\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\t\tr(\"RoundedRectangle\", [\"partition\", \"node\", \"shape\", \"last\"]).setAll({\r\n\t\t\tfillOpacity: 0.75\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Sunburst\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Sunburst\").setAll({\r\n\t\t\tsingleBranchOnly: true\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\", [\"sunburst\"]).setAll({\r\n\t\t\tsetStateOnChildren: false\r\n\t\t});\r\n\r\n\t\tr(\"HierarchyNode\", [\"sunburst\"]).states.create(\"hidden\", {\r\n\t\t\topacity: 0,\r\n\t\t\tvisible: false\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Slice\", [\"sunburst\", \"node\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 1,\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t\tcornerRadius: 0\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\t\tr(\"Slice\", [\"sunburst\", \"node\", \"shape\", \"last\"]).setAll({\r\n\t\t\tfillOpacity: 0.75\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"RadialLabel\", [\"sunburst\", \"node\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tx: 0,\r\n\t\t\t\ty: 0,\r\n\t\t\t\ttextType: \"radial\",\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingLeft: 1,\r\n\t\t\t\tpaddingRight: 1,\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.4,\r\n\t\t\t\tbaseRadius: p50,\r\n\t\t\t\trotation: 0\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: ForceDirected\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"ForceDirected\").setAll({\r\n\t\t\tminRadius: percent(1),\r\n\t\t\tmaxRadius: percent(8),\r\n\t\t\tinitialFrames: 500,\r\n\t\t\tcenterStrength: 0.8,\r\n\t\t\tmanyBodyStrength: -14,\r\n\t\t\tvelocityDecay: 0.5,\r\n\t\t\tlinkWithStrength: 0.5,\r\n\t\t\tshowOnFrame: 10,\r\n\t\t\tsingleBranchOnly: false,\r\n\t\t\tupDepth: Infinity,\r\n\t\t\tdownDepth: 1,\r\n\t\t\tinitialDepth: 5,\r\n\t\t\ttopDepth: 0\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Tree\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Tree\").setAll({\r\n\t\t\torientation: \"vertical\",\r\n\t\t\tpaddingLeft: 20,\r\n\t\t\tpaddingRight: 20,\r\n\t\t\tpaddingTop: 20,\r\n\t\t\tpaddingBottom: 20,\r\n\t\t\tsingleBranchOnly: false,\r\n\t\t\tupDepth: Infinity,\r\n\t\t\tdownDepth: 1,\r\n\t\t\tinitialDepth: 5,\r\n\t\t\ttopDepth: 0\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Pack\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Pack\").setAll({\r\n\t\t\tpaddingLeft: 20,\r\n\t\t\tpaddingTop: 20,\r\n\t\t\tpaddingBottom: 20,\r\n\t\t\tpaddingRight: 20,\r\n\t\t\tnodePadding: 0\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"pack\", \"node\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingLeft: 1,\r\n\t\t\t\tpaddingRight: 1,\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.4\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Circle\", [\"pack\", \"node\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 0.5,\r\n\t\t\t\tfillOpacity: 0.8,\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\r\n\t\tr(\"LinkedHierarchyNode\").setAll({\r\n\t\t\tdraggable: true\r\n\t\t});\r\n\r\n\t\tr(\"LinkedHierarchyNode\").states.create(\"hidden\", { scale: 0, opacity: 0, visible: false });\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Treemap\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"Treemap\").setAll({\r\n\t\t\tupDepth: 0,\r\n\t\t\tlayoutAlgorithm: \"squarify\"\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"treemap\", \"node\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tx: p50,\r\n\t\t\t\ty: p50,\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingLeft: 1,\r\n\t\t\t\tpaddingRight: 1,\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.4\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\t\tr(\"HierarchyNode\", [\"treemap\", \"node\"]).setAll({\r\n\t\t\ttooltipY: percent(40),\r\n\t\t\tisMeasured: false,\r\n\t\t\tposition: \"absolute\"\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"RoundedRectangle\", [\"treemap\", \"node\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 1,\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t\tcornerRadiusBR: 0,\r\n\t\t\t\tcornerRadiusTR: 0,\r\n\t\t\t\tcornerRadiusBL: 0,\r\n\t\t\t\tcornerRadiusTL: 0,\r\n\t\t\t\tfillOpacity: 1\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/hierarchy: Voronoi Treemap\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\t{\r\n\t\t\tr(\"VoronoiTreemap\").setAll({\r\n\t\t\t\ttype: \"polygon\",\r\n\t\t\t\tminWeightRatio: 0.005,\r\n\t\t\t\tconvergenceRatio: 0.005,\r\n\t\t\t\tmaxIterationCount: 100,\r\n\t\t\t\tsingleBranchOnly: true\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Graphics\", [\"voronoitreemap\", \"node\", \"shape\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tstrokeOpacity: 1,\r\n\t\t\t\tstrokeWidth: 1,\r\n\t\t\t\tfillOpacity: 1\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tr(\"Polygon\", [\"hierarchy\", \"node\", \"shape\", \"depth1\"]).setAll({\r\n\t\t\t\tstrokeWidth: 3\r\n\t\t\t});\r\n\t\t}\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Label\", [\"voronoitreemap\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tcenterY: p50,\r\n\t\t\t\tcenterX: p50,\r\n\t\t\t\tpaddingBottom: 1,\r\n\t\t\t\tpaddingTop: 1,\r\n\t\t\t\tpaddingLeft: 1,\r\n\t\t\t\tpaddingRight: 1,\r\n\t\t\t\tpopulateText: true,\r\n\t\t\t\ttext: \"{category}\",\r\n\t\t\t\toversizedBehavior: \"fit\",\r\n\t\t\t\tminScale: 0.4,\r\n\t\t\t\tlayer: 30\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeText\");\r\n\t\t}\r\n\r\n\t}\r\n}\r\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { IDisposer } from \"../../core/util/Disposer\";\nimport type { Hierarchy, IHierarchyDataItem } from \"./Hierarchy\";\n\nimport { HierarchyDefaultTheme } from \"./HierarchyDefaultTheme\";\nimport { Container, IContainerPrivate, IContainerSettings, IContainerEvents } from \"../../core/render/Container\";\nimport { Label } from \"../../core/render/Label\";\nimport { RoundedRectangle } from \"../../core/render/RoundedRectangle\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IBreadcrumbBarSettings extends IContainerSettings {\n\n\t/**\n\t * A hierarchy series bar will use to build current selection path.\n\t */\n\tseries: Hierarchy;\n\n}\n\nexport interface IBreadcrumbBarPrivate extends IContainerPrivate {\n}\n\nexport interface IBreadcrumbBarEvents extends IContainerEvents {\n}\n\n/**\n * Creates a breadcrumb navigation control.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/breadcrumbs/} for more info\n * @important\n */\nexport class BreadcrumbBar extends Container {\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLabel(dataItem: DataItem<IHierarchyDataItem>): Label {\n\t\tconst label = this.labels.make();\n\t\tlabel._setDataItem(dataItem);\n\t\tlabel.states.create(\"hover\", {});\n\t\tlabel.states.create(\"down\", {});\n\t\tlabel.events.on(\"click\", () => {\n\t\t\tconst series = this.get(\"series\");\n\t\t\tif (series) {\n\t\t\t\tseries.selectDataItem(dataItem);\n\t\t\t}\n\t\t});\n\n\t\tthis.labels.push(label);\n\n\t\treturn label;\n\t}\n\n\t/**\n\t * A list of labels in the bar.\n\t *\n\t * `labels.template` can be used to configure label apperance and behavior.\n\t *\n\t * @default new ListTemplate<Label>\n\t */\n\tpublic readonly labels: ListTemplate<Label> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Label._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [\"label\"]),\n\t\t\tbackground: RoundedRectangle.new(this._root, {\n\t\t\t\tthemeTags: [\"background\"]\n\t\t\t})\n\t\t}, [this.labels.template])\n\t);\n\n\tpublic static className: string = \"BreadcrumbBar\";\n\tpublic static classNames: Array<string> = Container.classNames.concat([BreadcrumbBar.className]);\n\n\tdeclare public _settings: IBreadcrumbBarSettings;\n\tdeclare public _privateSettings: IBreadcrumbBarPrivate;\n\tdeclare public _events: IBreadcrumbBarEvents;\n\n\tprotected _disposer: IDisposer | undefined;\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(HierarchyDefaultTheme.new(this._root));\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"breadcrumb\"]);\n\n\t\tsuper._afterNew();\n\t}\n\n\tpublic _changed() {\n\t\tsuper._changed();\n\t\tif (this.isDirty(\"series\")) {\n\t\t\tconst series = this.get(\"series\");\n\t\t\tconst previous = this._prevSettings.series;\n\t\t\tif (series != previous) {\n\t\t\t\tthis._disposer = series.events.on(\"dataitemselected\", (event) => {\n\t\t\t\t\tthis._handleDataItem(event.dataItem)\n\t\t\t\t})\n\t\t\t}\n\t\t\telse if (previous) {\n\t\t\t\tif (this._disposer) {\n\t\t\t\t\tthis._disposer.dispose();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._handleDataItem(series.get(\"selectedDataItem\"));\n\t\t}\n\t}\n\n\tprotected _handleDataItem(dataItem: DataItem<IHierarchyDataItem> | undefined) {\n\t\tthis.set(\"minHeight\", this.height());\n\t\tthis.children.clear();\n\t\tthis.labels.clear();\n\n\t\tif (dataItem) {\n\t\t\tlet parent = dataItem;\n\n\t\t\twhile (parent) {\n\t\t\t\tlet label = this.makeLabel(parent);\n\t\t\t\tif (parent == dataItem) {\n\t\t\t\t\tlabel.addTag(\"last\");\n\t\t\t\t}\n\t\t\t\tthis.children.moveValue(label, 0);\n\t\t\t\tparent = parent.get(\"parent\");\n\t\t\t}\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { IDisposer } from \"../../core/util/Disposer\";\nimport type { Hierarchy, IHierarchyDataItem } from \"./Hierarchy\";\n\nimport { Container, IContainerPrivate, IContainerSettings } from \"../../core/render/Container\";\n\nexport interface IHierarchyNodeSettings extends IContainerSettings {\n}\n\nexport interface IHierarchyNodePrivate extends IContainerPrivate {\n}\n\n/**\n * Base class for hierarchy nodes.\n */\nexport class HierarchyNode extends Container {\n\n\t/**\n\t * Related series.\n\t */\n\tpublic series: Hierarchy | undefined;\n\n\tdeclare public _settings: IHierarchyNodeSettings;\n\tdeclare public _privateSettings: IHierarchyNodePrivate;\n\n\tpublic static className: string = \"HierarchyNode\";\n\tpublic static classNames: Array<string> = Container.classNames.concat([HierarchyNode.className]);\n\n\tdeclare protected _dataItem: DataItem<IHierarchyDataItem> | undefined;\n\n\tprotected _clickDisposer: IDisposer | undefined;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\n\t\tthis.states.create(\"disabled\", {});\n\t\tthis.states.create(\"hover\", {});\n\t\tthis.states.create(\"hoverDisabled\", {});\n\n\t\tthis.on(\"disabled\", () => {\n\t\t\tconst dataItem = this.dataItem as DataItem<IHierarchyDataItem>;\n\t\t\tif (!dataItem.get(\"children\")) {\n\t\t\t\tthis.set(\"disabled\", true);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst disabled = this.get(\"disabled\");\n\t\t\tconst series = this.series;\n\n\t\t\tif (dataItem && series) {\n\t\t\t\tif (dataItem.get(\"disabled\") != disabled) {\n\t\t\t\t\tif (disabled) {\n\t\t\t\t\t\tseries.disableDataItem(dataItem);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tseries.enableDataItem(dataItem, series.get(\"downDepth\", 1), 0);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic _changed() {\n\t\tsuper._changed();\n\n\t\tif (this.isDirty(\"toggleKey\")) {\n\t\t\tconst toggleKey = this.get(\"toggleKey\");\n\n\t\t\tif (toggleKey == \"disabled\") {\n\t\t\t\tthis._clickDisposer = this.events.on(\"click\", () => {\n\t\t\t\t\tif (!this._isDragging) {\n\t\t\t\t\t\tlet series = this.series;\n\t\t\t\t\t\tif (series) {\n\t\t\t\t\t\t\tseries.selectDataItem(this.dataItem as DataItem<IHierarchyDataItem>);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (this._clickDisposer) {\n\t\t\t\t\tthis._clickDisposer.dispose();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n", "function count(node) {\n  var sum = 0,\n      children = node.children,\n      i = children && children.length;\n  if (!i) sum = 1;\n  else while (--i >= 0) sum += children[i].value;\n  node.value = sum;\n}\n\nexport default function() {\n  return this.eachAfter(count);\n}\n", "import node_count from \"./count.js\";\nimport node_each from \"./each.js\";\nimport node_eachBefore from \"./eachBefore.js\";\nimport node_eachAfter from \"./eachAfter.js\";\nimport node_find from \"./find.js\";\nimport node_sum from \"./sum.js\";\nimport node_sort from \"./sort.js\";\nimport node_path from \"./path.js\";\nimport node_ancestors from \"./ancestors.js\";\nimport node_descendants from \"./descendants.js\";\nimport node_leaves from \"./leaves.js\";\nimport node_links from \"./links.js\";\nimport node_iterator from \"./iterator.js\";\n\nexport default function hierarchy(data, children) {\n  if (data instanceof Map) {\n    data = [undefined, data];\n    if (children === undefined) children = mapChildren;\n  } else if (children === undefined) {\n    children = objectChildren;\n  }\n\n  var root = new Node(data),\n      node,\n      nodes = [root],\n      child,\n      childs,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if ((childs = children(node.data)) && (n = (childs = Array.from(childs)).length)) {\n      node.children = childs;\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = childs[i] = new Node(childs[i]));\n        child.parent = node;\n        child.depth = node.depth + 1;\n      }\n    }\n  }\n\n  return root.eachBefore(computeHeight);\n}\n\nfunction node_copy() {\n  return hierarchy(this).eachBefore(copyData);\n}\n\nfunction objectChildren(d) {\n  return d.children;\n}\n\nfunction mapChildren(d) {\n  return Array.isArray(d) ? d[1] : null;\n}\n\nfunction copyData(node) {\n  if (node.data.value !== undefined) node.value = node.data.value;\n  node.data = node.data.data;\n}\n\nexport function computeHeight(node) {\n  var height = 0;\n  do node.height = height;\n  while ((node = node.parent) && (node.height < ++height));\n}\n\nexport function Node(data) {\n  this.data = data;\n  this.depth =\n  this.height = 0;\n  this.parent = null;\n}\n\nNode.prototype = hierarchy.prototype = {\n  constructor: Node,\n  count: node_count,\n  each: node_each,\n  eachAfter: node_eachAfter,\n  eachBefore: node_eachBefore,\n  find: node_find,\n  sum: node_sum,\n  sort: node_sort,\n  path: node_path,\n  ancestors: node_ancestors,\n  descendants: node_descendants,\n  leaves: node_leaves,\n  links: node_links,\n  copy: node_copy,\n  [Symbol.iterator]: node_iterator\n};\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], next = [], children, i, n, index = -1;\n  while (node = nodes.pop()) {\n    next.push(node);\n    if (children = node.children) {\n      for (i = 0, n = children.length; i < n; ++i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  while (node = next.pop()) {\n    callback.call(that, node, ++index, this);\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  var node = this, nodes = [node], children, i, index = -1;\n  while (node = nodes.pop()) {\n    callback.call(that, node, ++index, this);\n    if (children = node.children) {\n      for (i = children.length - 1; i >= 0; --i) {\n        nodes.push(children[i]);\n      }\n    }\n  }\n  return this;\n}\n", "export default function(callback, that) {\n  let index = -1;\n  for (const node of this) {\n    if (callback.call(that, node, ++index, this)) {\n      return node;\n    }\n  }\n}\n", "export default function(value) {\n  return this.eachAfter(function(node) {\n    var sum = +value(node.data) || 0,\n        children = node.children,\n        i = children && children.length;\n    while (--i >= 0) sum += children[i].value;\n    node.value = sum;\n  });\n}\n", "export default function(compare) {\n  return this.eachBefore(function(node) {\n    if (node.children) {\n      node.children.sort(compare);\n    }\n  });\n}\n", "export default function(end) {\n  var start = this,\n      ancestor = leastCommonAncestor(start, end),\n      nodes = [start];\n  while (start !== ancestor) {\n    start = start.parent;\n    nodes.push(start);\n  }\n  var k = nodes.length;\n  while (end !== ancestor) {\n    nodes.splice(k, 0, end);\n    end = end.parent;\n  }\n  return nodes;\n}\n\nfunction leastCommonAncestor(a, b) {\n  if (a === b) return a;\n  var aNodes = a.ancestors(),\n      bNodes = b.ancestors(),\n      c = null;\n  a = aNodes.pop();\n  b = bNodes.pop();\n  while (a === b) {\n    c = a;\n    a = aNodes.pop();\n    b = bNodes.pop();\n  }\n  return c;\n}\n", "export default function() {\n  var node = this, nodes = [node];\n  while (node = node.parent) {\n    nodes.push(node);\n  }\n  return nodes;\n}\n", "export default function() {\n  return Array.from(this);\n}\n", "export default function() {\n  var leaves = [];\n  this.eachBefore(function(node) {\n    if (!node.children) {\n      leaves.push(node);\n    }\n  });\n  return leaves;\n}\n", "export default function() {\n  var root = this, links = [];\n  root.each(function(node) {\n    if (node !== root) { // Don’t include the root’s parent, if any.\n      links.push({source: node.parent, target: node});\n    }\n  });\n  return links;\n}\n", "export default function*() {\n  var node = this, current, next = [node], children, i, n;\n  do {\n    current = next.reverse(), next = [];\n    while (node = current.pop()) {\n      yield node;\n      if (children = node.children) {\n        for (i = 0, n = children.length; i < n; ++i) {\n          next.push(children[i]);\n        }\n      }\n    }\n  } while (next.length);\n}\n", "import type { Color } from \"../../core/util/Color\";\nimport type { ColorSet } from \"../../core/util/ColorSet\";\nimport type { Bullet } from \"../../core/render/Bullet\";\nimport type { Root } from \"../../core/Root\";\nimport type { Easing } from \"../../core/util/Ease\";\nimport type { PatternSet } from \"../../core/util/PatternSet\";\nimport type { Pattern } from \"../../core/render/patterns/Pattern\";\n\nimport { HierarchyDefaultTheme } from \"./HierarchyDefaultTheme\";\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate, ISeriesEvents } from \"../../core/render/Series\";\nimport { DataItem } from \"../../core/render/Component\";\nimport { HierarchyNode } from \"./HierarchyNode\";\nimport { Container } from \"../../core/render/Container\";\nimport { Label } from \"../../core/render/Label\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as d3hierarchy from \"d3-hierarchy\";\n\n/**\n * @ignore\n */\nexport interface IHierarchyDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: IHierarchyDataObject[],\n\tdataItem?: DataItem<IHierarchyDataItem>\n\tcustomValue?: boolean;\n};\n\nexport interface IHierarchyDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Value of the node as set in data.\n\t */\n\tvalue: number;\n\n\t/**\n\t * @ignore\n\t */\n\tvalueWorking: number;\n\n\t/**\n\t * Percent value of the node, based on total sum of all nodes in upper level.\n\t */\n\tvaluePercentTotal: number;\n\n\t/**\n\t * Percent value of the node, based on the value of its direct parent.\n\t *\n\t * @since 5.2.21\n\t */\n\tvaluePercent: number;\n\n\t/**\n\t * Sum of child values.\n\t */\n\tsum: number;\n\n\t/**\n\t * Category.\n\t */\n\tcategory: string;\n\n\t/**\n\t * List of child node data items.\n\t */\n\tchildren: Array<DataItem<IHierarchyDataItem>>;\n\n\t/**\n\t * Raw data of the node's children.\n\t */\n\tchildData: Array<any>\n\n\t/**\n\t * Data item of parent node.\n\t */\n\tparent: DataItem<IHierarchyDataItem>;\n\n\t/**\n\t * Node's depth within the hierarchy.\n\t */\n\tdepth: number;\n\n\t/**\n\t * A reference to the related [[HierarchyNode]].\n\t */\n\tnode: HierarchyNode;\n\n\t/**\n\t * A reference to node's [[Label]].\n\t */\n\tlabel: Label;\n\n\t/**\n\t * Node's auto-assigned color.\n\t */\n\tfill: Color;\n\n\t/**\n\t * Node's auto-assigned pattern.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/} for more info\n\t * @since 5.10.0\n\t */\n\tfillPattern: Pattern;\n\n\t/**\n\t * Indicates if node is currently disabled.\n\t */\n\tdisabled: boolean;\n\n\t/**\n\t * @ignore\n\t */\n\td3HierarchyNode: d3hierarchy.HierarchyNode<IHierarchyDataObject>;\n\n}\n\nexport interface IHierarchySettings extends ISeriesSettings {\n\t/**\n\t * How to sort nodes by their value.\n\t *\n\t * @default \"none\"\n\t */\n\tsort?: \"ascending\" | \"descending\" | \"none\"\n\n\n\t/**\n\t * A field in data that holds numeric value for the node.\n\t */\n\tvalueField?: string;\n\n\t/**\n\t * A field in data that holds string-based identificator for node.\n\t */\n\tcategoryField?: string;\n\n\t/**\n\t * A field in data that holds an array of child node data.\n\t */\n\tchildDataField?: string;\n\n\t/**\n\t * A field in data that holds boolean value indicating if node is\n\t * disabled (collapsed).\n\t */\n\tdisabledField?: string;\n\n\t/**\n\t * A field in data that holds color used for fills for various elements, such\n\t * as nodes.\n\t */\n\tfillField?: string;\n\n\t/**\n\t * A [[ColorSet]] to use when asigning colors for nodes.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Node_colors} for more info\n\t */\n\tcolors?: ColorSet;\n\n\t/**\n\t * A [[PatternSet]] to use when asigning patterns for nodes.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/#Pattern_sets} for more info\n\t * @since 5.10.0\n\t */\n\tpatterns?: PatternSet;\n\n\t/**\n\t * Number of child levels to open when clicking on a node.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Drill_down} for more info\n\t */\n\tdownDepth?: number;\n\n\t/**\n\t * Number of levels parent levels to show from currently selected node.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Drill_down} for more info\n\t */\n\tupDepth?: number;\n\n\t/**\n\t * Number of levels to show on chart's first load.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Tree_depth} for more info\n\t */\n\tinitialDepth?: number;\n\n\t/**\n\t * If set, will show nodes starting from set level.\n\t *\n\t * It could be used to eliminate top level branches, that do not need to be\n\t * shown.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Tree_depth} for more info\n\t */\n\ttopDepth?: number;\n\n\t/**\n\t * If set to `true` will make all other branches collapse when some branch is\n\t * expanded.\n\t */\n\tsingleBranchOnly?: boolean;\n\n\t/**\n\t * A data item for currently selected node.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/#Pre_selected_branch} for more info\n\t */\n\tselectedDataItem?: DataItem<IHierarchyDataItem>;\n\n\t/**\n\t * Duration for all drill animations in milliseconds.\n\t */\n\tanimationDuration?: number;\n\n\t/**\n\t * An easing function to use for drill animations.\n\t */\n\tanimationEasing?: Easing;\n\n}\n\nexport interface IHierarchyPrivate extends ISeriesPrivate {\n\n\t/**\n\t * Level count in series.\n\t */\n\tmaxDepth: number;\n\n}\n\nexport interface IHierarchyEvents extends ISeriesEvents {\n\tdataitemselected: {\n\t\tdataItem?: DataItem<IHierarchyDataItem>\n\t};\n}\n\n/**\n * A base class for all hierarchy charts.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/} for more info\n */\nexport abstract class Hierarchy extends Series {\n\tpublic static className: string = \"Hierarchy\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([Hierarchy.className]);\n\n\tdeclare public _settings: IHierarchySettings;\n\tdeclare public _privateSettings: IHierarchyPrivate;\n\tdeclare public _dataItemSettings: IHierarchyDataItem;\n\tdeclare public _events: IHierarchyEvents;\n\n\t/**\n\t * A [[Container]] that nodes are placed in.\n\t *\n\t * @default Container.new()\n\t */\n\tpublic readonly nodesContainer = this.children.push(Container.new(this._root, { isMeasured: false }));\n\n\tpublic _rootNode: d3hierarchy.HierarchyNode<IHierarchyDataObject> | undefined;\n\n\tpublic _treeData: IHierarchyDataObject | undefined;\n\n\tprotected _index: number = 0;\n\n\tprotected _tag: string = \"hierarchy\";\n\n\t/**\n\t * A list of nodes in a [[Hierarchy]] chart.\n\t *\n\t * @default new ListTemplate<HierarchyNode>\n\t */\n\tpublic readonly nodes: ListTemplate<HierarchyNode> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => HierarchyNode.new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.nodes.template.get(\"themeTags\", []), [this._tag, \"hierarchy\", \"node\"])\n\t\t}, this.nodes.template)\n\t);\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): HierarchyNode {\n\n\t\tconst childData = dataItem.get(\"childData\");\n\n\t\tconst node = this.nodes.make();\n\t\tnode.series = this;\n\t\tnode._setDataItem(dataItem);\n\t\tthis.nodes.push(node);\n\t\tdataItem.setRaw(\"node\", node);\n\n\t\tconst label = this.labels.make();\n\t\tlabel._setDataItem(dataItem);\n\t\tdataItem.setRaw(\"label\", label);\n\t\tthis.labels.push(label);\n\n\t\tif (!childData || childData.length == 0) {\n\t\t\tnode.addTag(\"last\");\n\t\t}\n\n\t\tconst depth = dataItem.get(\"depth\");\n\t\tnode.addTag(\"depth\" + depth);\n\n\t\tthis.nodesContainer.children.push(node);\n\t\tnode.children.push(label);\n\n\t\treturn node;\n\t}\n\n\t/**\n\t * A list of label elements in a [[Hierarchy]] chart.\n\t *\n\t * @default new ListTemplate<Label>\n\t */\n\tpublic readonly labels: ListTemplate<Label> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Label.new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [this._tag])\n\t\t}, this.labels.template)\n\t);\n\n\tpublic _currentDownDepth: number | undefined;\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(HierarchyDefaultTheme.new(this._root));\n\t\tthis.fields.push(\"category\", \"childData\", \"disabled\", \"fill\");\n\t\tthis.children.push(this.bulletsContainer);\n\n\t\tsuper._afterNew();\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this._valuesDirty) {\n\t\t\tthis._treeData = {};\n\n\t\t\tconst first = this.dataItems[0];\n\t\t\tif (first) {\n\t\t\t\tthis._makeHierarchyData(this._treeData, first);\n\t\t\t\tthis._index = 0;\n\t\t\t\tthis._rootNode = (d3hierarchy.hierarchy(this._treeData) as any);\n\t\t\t\tif (this._rootNode) {\n\t\t\t\t\tthis._rootNode.sum((d) => {\n\t\t\t\t\t\treturn d.value as any;\n\t\t\t\t\t});\n\t\t\t\t\tconst sort = this.get(\"sort\")\n\t\t\t\t\tif (sort == \"descending\") {\n\t\t\t\t\t\tthis._rootNode.sort((a, b) => (b.value as any) - (a.value as any));\n\t\t\t\t\t}\n\t\t\t\t\telse if (sort == \"ascending\") {\n\t\t\t\t\t\tthis._rootNode.sort((a, b) => (a.value as any) - (b.value as any));\n\t\t\t\t\t}\n\t\t\t\t\tthis.setPrivateRaw(\"valueLow\", Infinity);\n\t\t\t\t\tthis.setPrivateRaw(\"valueHigh\", -Infinity);\n\t\t\t\t\tthis.setPrivateRaw(\"maxDepth\", 0);\n\t\t\t\t\tthis._updateValues(this._rootNode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this._valuesDirty || this._sizeDirty) {\n\t\t\tthis._updateVisuals();\n\t\t}\n\n\t\tif (this._sizeDirty) {\n\t\t\tconst dataItem = this.get(\"selectedDataItem\");\n\t\t\tif (dataItem) {\n\t\t\t\tthis._zoom(dataItem);\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic _changed() {\n\t\tsuper._changed();\n\t\tif (this.isDirty(\"selectedDataItem\")) {\n\t\t\tthis._selectDataItem(this.get(\"selectedDataItem\"));\n\t\t}\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tthis._updateNodes(this._rootNode);\n\t\t}\n\t}\n\n\tprotected _updateNodes(hierarchyNode: d3hierarchy.HierarchyNode<IHierarchyDataObject>) {\n\t\tconst dataItem = hierarchyNode.data.dataItem;\n\n\t\tif (dataItem) {\n\t\t\tthis._updateNode(dataItem);\n\n\t\t\tif (this.bullets.length > 0 && !dataItem.bullets) {\n\t\t\t\tthis._makeBullets(dataItem);\n\t\t\t}\n\n\t\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\t\tif (hierarchyChildren) {\n\t\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\t\tthis._updateNodes(hierarchyChild)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _updateNode(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t}\n\n\t/**\n\t * Looks up and returns a data item by its ID.\n\t *\n\t * @param   id  ID\n\t * @return      Data item\n\t */\n\tpublic getDataItemById(id: string): DataItem<this[\"_dataItemSettings\"]> | undefined {\n\t\treturn this._getDataItemById(this.dataItems, id);\n\t}\n\n\tpublic _getDataItemById(dataItems: Array<DataItem<this[\"_dataItemSettings\"]>>, id: string): DataItem<this[\"_dataItemSettings\"]> | undefined {\n\n\t\tlet di: DataItem<this[\"_dataItemSettings\"]> | undefined;\n\n\t\t$array.each(dataItems, (dataItem: any) => {\n\n\t\t\tif (dataItem.get(\"id\") == id) {\n\t\t\t\tdi = dataItem;\n\t\t\t}\n\n\t\t\tconst children = dataItem.get(\"children\");\n\t\t\tif (children) {\n\t\t\t\tlet childDataItem = this._getDataItemById(children, id);\n\t\t\t\tif (childDataItem) {\n\t\t\t\t\tdi = childDataItem\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\treturn di;\n\t}\n\n\tprotected _handleBullets(dataItems: Array<DataItem<this[\"_dataItemSettings\"]>>) {\n\t\t$array.each(dataItems, (dataItem) => {\n\t\t\tconst bullets = dataItem.bullets;\n\t\t\tif (bullets) {\n\t\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\t\tbullet.dispose();\n\t\t\t\t})\n\t\t\t\tdataItem.bullets = undefined;\n\t\t\t}\n\n\t\t\tconst children = dataItem.get(\"children\");\n\n\t\t\tif (children) {\n\t\t\t\tthis._handleBullets(children);\n\t\t\t}\n\t\t})\n\n\t\tthis._updateVisuals();\n\t}\n\n\tprotected _onDataClear() {\n\t\tsuper._onDataClear();\n\t\tconst colors = this.get(\"colors\");\n\t\tif (colors) {\n\t\t\tcolors.reset();\n\t\t}\n\n\t\tconst patterns = this.get(\"patterns\");\n\t\tif (patterns) {\n\t\t\tpatterns.reset();\n\t\t}\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tconst childData = dataItem.get(\"childData\");\n\t\tconst colors = this.get(\"colors\");\n\t\tconst patterns = this.get(\"patterns\");\n\t\tconst topDepth = this.get(\"topDepth\", 0);\n\n\t\tif (!dataItem.get(\"parent\")) {\n\t\t\tdataItem.setRaw(\"depth\", 0);\n\t\t\tif (colors && topDepth == 0 && dataItem.get(\"fill\") == null) {\n\t\t\t\tdataItem.setRaw(\"fill\", colors.next());\n\n\t\t\t\tif (patterns) {\n\t\t\t\t\tdataItem.setRaw(\"fillPattern\", patterns.next());\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tlet depth = dataItem.get(\"depth\");\n\t\tconst initialDepth = this.get(\"initialDepth\", 1);\n\n\t\tthis.makeNode(dataItem);\n\t\tthis._processDataItem(dataItem);\n\n\t\tif (childData) {\n\t\t\tconst children: Array<DataItem<this[\"_dataItemSettings\"]>> = [];\n\t\t\tdataItem.setRaw(\"children\", children);\n\n\t\t\t$array.each(childData, (child) => {\n\t\t\t\tconst childDataItem = new DataItem(this, child, this._makeDataItem(child));\n\n\t\t\t\tchildren.push(childDataItem);\n\n\t\t\t\tchildDataItem.setRaw(\"parent\", dataItem);\n\t\t\t\tchildDataItem.setRaw(\"depth\", depth + 1);\n\n\t\t\t\tif (this.dataItems.length == 1 && depth == 0) {\n\t\t\t\t\tif (colors && childDataItem.get(\"fill\") == null) {\n\t\t\t\t\t\tchildDataItem.setRaw(\"fill\", colors.next());\n\t\t\t\t\t}\n\t\t\t\t\tif (patterns && childDataItem.get(\"fillPattern\") == null) {\n\t\t\t\t\t\tchildDataItem.setRaw(\"fillPattern\", patterns.next());\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif (childDataItem.get(\"fill\") == null) {\n\t\t\t\t\t\tchildDataItem.setRaw(\"fill\", dataItem.get(\"fill\"));\n\t\t\t\t\t}\n\t\t\t\t\tif (childDataItem.get(\"fillPattern\") == null) {\n\t\t\t\t\t\tchildDataItem.setRaw(\"fillPattern\", dataItem.get(\"fillPattern\"));\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.processDataItem(childDataItem);\n\t\t\t})\n\t\t}\n\n\t\tconst children = dataItem.get(\"children\");\n\t\tif (!children || children.length == 0) {\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tnode.setAll({ toggleKey: undefined });\n\t\t}\n\n\t\tif (dataItem.get(\"disabled\") == null) {\n\t\t\tif (depth >= topDepth + initialDepth) {\n\t\t\t\tthis.disableDataItem(dataItem, 0);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Adds children data to the target data item.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/hierarchy-api/#Dynamically_adding_child_nodes} for more info\n\t * @since 5.4.5\n\t */\n\tpublic addChildData(dataItem: DataItem<this[\"_dataItemSettings\"]>, data: Array<any>) {\n\t\tconst dataContext = dataItem.dataContext as any;\n\t\tconst childDataField = this.get(\"childDataField\");\n\n\t\tlet childData = dataContext[childDataField] as any;\n\t\tif (!childData) {\n\t\t\tchildData = data;\n\t\t\tdataContext[childDataField] = childData;\n\t\t}\n\t\telse {\n\t\t\tchildData.push(...data);\n\t\t}\n\n\t\tlet children = dataItem.get(\"children\");\n\t\tif (!children) {\n\t\t\tchildren = [];\n\t\t\tdataItem.set(\"children\", children);\n\t\t}\n\n\t\tconst node = dataItem.get(\"node\");\n\n\t\tif (node) {\n\t\t\tnode.set(\"toggleKey\", \"disabled\");\n\t\t}\n\n\t\tlet depth = dataItem.get(\"depth\");\n\n\t\t$array.each(childData, (child) => {\n\t\t\tlet found = false;\n\t\t\t$array.eachContinue(children, (dataItem) => {\n\t\t\t\tif (dataItem.dataContext == child) {\n\t\t\t\t\tfound = true;\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t\treturn true;\n\t\t\t})\n\n\t\t\tif (!found) {\n\t\t\t\tconst childDataItem = new DataItem(this, child, this._makeDataItem(child));\n\n\t\t\t\tchildren.push(childDataItem);\n\n\t\t\t\tchildDataItem.setRaw(\"parent\", dataItem);\n\t\t\t\tchildDataItem.setRaw(\"depth\", depth + 1);\n\n\t\t\t\tif (childDataItem.get(\"fill\") == null) {\n\t\t\t\t\tchildDataItem.setRaw(\"fill\", dataItem.get(\"fill\"));\n\t\t\t\t}\n\n\t\t\t\tthis.processDataItem(childDataItem);\n\t\t\t}\n\t\t})\n\t}\n\n\tprotected _processDataItem(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t}\n\n\tprotected _updateValues(d3HierarchyNode: d3hierarchy.HierarchyNode<IHierarchyDataObject>) {\n\t\tconst dataItem = d3HierarchyNode.data.dataItem;\n\n\t\tif (d3HierarchyNode.depth > this.getPrivate(\"maxDepth\")) {\n\t\t\tthis.setPrivateRaw(\"maxDepth\", d3HierarchyNode.depth);\n\t\t}\n\n\t\tif (dataItem) {\n\t\t\tdataItem.setRaw(\"d3HierarchyNode\", d3HierarchyNode);\n\n\t\t\t(d3HierarchyNode as any).index = this._index;\n\n\t\t\tthis._index++;\n\n\t\t\tthis.root.events.once(\"frameended\", () => {\n\t\t\t\tdataItem.get(\"node\").set(\"disabled\", dataItem.get(\"disabled\"));\n\t\t\t})\n\n\t\t\tlet dataValue = d3HierarchyNode.data.value;\n\t\t\tlet value = d3HierarchyNode.value\n\n\t\t\tif (dataValue != null) {\n\t\t\t\tvalue = dataValue;\n\t\t\t\t(d3HierarchyNode as any)[\"value\"] = value;\n\t\t\t}\n\n\t\t\tif ($type.isNumber(value)) {\n\t\t\t\tdataItem.setRaw(\"sum\", value);\n\t\t\t\tdataItem.setRaw(\"valuePercentTotal\", value / this.dataItems[0].get(\"sum\") * 100);\n\n\t\t\t\tlet valuePercent = 100;\n\t\t\t\tconst parent = dataItem.get(\"parent\");\n\t\t\t\tif (parent) {\n\t\t\t\t\tvaluePercent = value / parent.get(\"sum\") * 100;\n\t\t\t\t}\n\t\t\t\tdataItem.get(\"label\").text.markDirtyText();\n\t\t\t\tdataItem.setRaw(\"valuePercent\", valuePercent);\n\n\t\t\t\tconst valueLow = this.getPrivate(\"valueLow\");\n\t\t\t\tif (valueLow != undefined && valueLow > value) {\n\t\t\t\t\tthis.setPrivateRaw(\"valueLow\", value);\n\t\t\t\t}\n\n\t\t\t\tconst valueHigh = this.getPrivate(\"valueHigh\");\n\t\t\t\tif (valueHigh != undefined && valueHigh < value) {\n\t\t\t\t\tthis.setPrivateRaw(\"valueHigh\", value);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis.updateLegendValue(dataItem);\n\t\t}\n\n\t\tconst hierarchyChildren = d3HierarchyNode.children;\n\t\tif (hierarchyChildren) {\n\t\t\t$array.each(hierarchyChildren, (d3HierarchyChild) => {\n\t\t\t\tthis._updateValues(d3HierarchyChild);\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _makeHierarchyData(data: IHierarchyDataObject, dataItem: DataItem<IHierarchyDataItem>) {\n\t\tdata.dataItem = dataItem;\n\n\t\tconst children = dataItem.get(\"children\");\n\t\tif (children) {\n\t\t\tconst childrenDataArray: Array<IHierarchyDataObject> = [];\n\t\t\tdata.children = childrenDataArray;\n\t\t\t$array.each(children, (childDataItem) => {\n\t\t\t\tconst childData = {};\n\t\t\t\tchildrenDataArray.push(childData);\n\t\t\t\tthis._makeHierarchyData(childData, childDataItem);\n\t\t\t})\n\n\t\t\tconst value = dataItem.get(\"valueWorking\");\n\t\t\tif ($type.isNumber(value)) {\n\t\t\t\tdata.value = value;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tconst value = dataItem.get(\"valueWorking\");\n\t\t\tif ($type.isNumber(value)) {\n\t\t\t\tdata.value = value;\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tconst node = dataItem.get(\"node\");\n\t\tif (node) {\n\t\t\tthis.nodes.removeValue(node);\n\t\t\tnode.dispose();\n\t\t}\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tthis.labels.removeValue(label);\n\t\t\tlabel.dispose();\n\t\t}\n\n\t\tconst children = dataItem.get(\"children\");\n\t\tif (children) {\n\t\t\t$array.each(children, (child) => {\n\t\t\t\tthis.disposeDataItem(child);\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * Hides hierarchy's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\n\t\tconst hiddenState = this.states.create(\"hidden\", {})\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tconst stateAnimationDuration = \"stateAnimationDuration\"\n\t\t\tduration = hiddenState.get(stateAnimationDuration, this.get(stateAnimationDuration, 0));\n\t\t}\n\n\t\tconst stateAnimationEasing = \"stateAnimationEasing\";\n\t\tconst easing = hiddenState.get(stateAnimationEasing, this.get(stateAnimationEasing));\n\n\t\tconst children = dataItem.get(\"children\");\n\n\t\tif ((!children || children.length == 0) && $type.isNumber(dataItem.get(\"value\"))) {\n\t\t\tpromises.push(dataItem.animate({ key: \"valueWorking\" as any, to: 0, duration: duration, easing: easing }).waitForStop());\n\t\t}\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tnode.hide();\n\t\tnode.hideTooltip();\n\n\t\tif (children) {\n\t\t\t$array.each(children, (childDataItem) => {\n\t\t\t\tpromises.push(this.hideDataItem(childDataItem));\n\t\t\t})\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Shows hierarchy's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = this.get(\"stateAnimationDuration\", 0);\n\t\t}\n\n\t\tconst easing = this.get(\"stateAnimationEasing\");\n\n\t\tconst children = dataItem.get(\"children\");\n\n\t\tif ((!children || children.length == 0) && $type.isNumber(dataItem.get(\"value\"))) {\n\t\t\tpromises.push(dataItem.animate({ key: \"valueWorking\" as any, to: dataItem.get(\"value\"), duration: duration, easing: easing }).waitForStop());\n\t\t}\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tnode.show();\n\n\t\tif (children) {\n\t\t\t$array.each(children, (childDataItem) => {\n\t\t\t\tpromises.push(this.showDataItem(childDataItem));\n\t\t\t})\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Enables a disabled data item.\n\t *\n\t * @param  dataItem  Target data item\n\t * @param  duration  Animation duration in milliseconds\n\t */\n\tpublic enableDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, maxDepth?: number, depth?: number, duration?: number) {\n\t\tif (depth == null) {\n\t\t\tdepth = 0;\n\t\t}\n\n\t\tif (maxDepth == null) {\n\t\t\tmaxDepth = 1;\n\t\t}\n\n\t\tdataItem.set(\"disabled\", false);\n\t\tdataItem.get(\"node\").set(\"disabled\", false);\n\n\t\tif (!dataItem.isHidden()) {\n\t\t\tdataItem.get(\"node\").show(duration);\n\t\t}\n\n\t\t\n\n\t\tconst topDepth = this.get(\"topDepth\", 0);\n\t\tif (dataItem.get(\"depth\") < topDepth) {\n\t\t\tdataItem.get(\"node\").hide(0);\n\t\t}\n\n\t\tif (depth == 0) {\n\t\t\tconst upDepth = this.get(\"upDepth\", Infinity);\n\t\t\tlet parent = dataItem;\n\t\t\tlet count = 0;\n\n\t\t\twhile (parent !== undefined) {\n\t\t\t\tif (count > upDepth) {\n\t\t\t\t\tparent.get(\"node\").hide();\n\t\t\t\t}\n\t\t\t\tparent = parent.get(\"parent\");\n\t\t\t\tcount++;\n\t\t\t}\n\t\t}\n\n\t\tlet children = dataItem.get(\"children\");\n\t\tif (children) {\n\t\t\tif (depth < maxDepth - 1) {\t\t\t\t\n\t\t\t\t$array.each(children, (child) => {\n\t\t\t\t\tconst disabledField = this.get(\"disabledField\");\n\t\t\t\t\tif (disabledField) {\n\t\t\t\t\t\tconst dataContext = child.dataContext as any;\n\t\t\t\t\t\tif (dataContext[disabledField] != true) {\n\t\t\t\t\t\t\tthis.enableDataItem(child, maxDepth, depth as number + 1, duration);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tthis.disableDataItem(child);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis.enableDataItem(child, maxDepth, depth as number + 1, duration);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\telse {\n\t\t\t\t$array.each(children, (child) => {\n\t\t\t\t\tif (!child.isHidden()) {\n\t\t\t\t\t\tchild.get(\"node\").show(duration);\n\t\t\t\t\t\tchild.get(\"node\").states.applyAnimate(\"disabled\");\n\t\t\t\t\t\tchild.set(\"disabled\", true);\n\n\t\t\t\t\t\tthis.disableDataItem(child);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Disables a data item.\n\t *\n\t * @param  dataItem  Target data item\n\t * @param  duration  Animation duration in milliseconds\n\t */\n\tpublic disableDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number) {\n\t\tdataItem.set(\"disabled\", true);\n\t\tlet children = dataItem.get(\"children\");\n\t\tif (children) {\n\t\t\t$array.each(children, (child) => {\n\t\t\t\tthis.disableDataItem(child, duration);\n\t\t\t\tchild.get(\"node\").hide(duration);\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _selectDataItem(dataItem?: DataItem<this[\"_dataItemSettings\"]>, downDepth?: number, skipDisptach?: boolean) {\n\t\tif (dataItem) {\n\t\t\tif (!skipDisptach) {\n\t\t\t\tconst type = \"dataitemselected\";\n\t\t\t\tthis.events.dispatch(type, { type: type, target: this, dataItem: dataItem });\n\t\t\t}\n\n\t\t\tlet maxDepth = this.getPrivate(\"maxDepth\", 1);\n\t\t\tconst topDepth = this.get(\"topDepth\", 0);\n\n\t\t\tif (downDepth == null) {\n\t\t\t\tdownDepth = Math.min(this.get(\"downDepth\", 1), maxDepth - dataItem.get(\"depth\"));\n\t\t\t}\n\n\t\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\t\t\t\n\t\t\tlet currentDepth = hierarchyNode.depth;\n\t\t\tif (!this.inited) {\n\t\t\t\tdownDepth = Math.min(this.get(\"initialDepth\", 1), maxDepth - topDepth);\n\t\t\t\tdownDepth = Math.max(0, downDepth);\n\t\t\t}\n\n\t\t\tthis._currentDownDepth = downDepth;\n\n\t\t\tif (currentDepth + downDepth > maxDepth) {\n\t\t\t\tdownDepth = maxDepth - currentDepth;\n\t\t\t}\n\n\t\t\tif (currentDepth < topDepth) {\n\t\t\t\tdownDepth += topDepth - currentDepth;\n\t\t\t\tcurrentDepth = topDepth;\n\t\t\t}\n\n\t\t\tconst children = dataItem.get(\"children\");\n\n\t\t\tif (children && children.length > 0) {\n\t\t\t\tif (downDepth > 0) {\n\t\t\t\t\tthis.enableDataItem(dataItem, downDepth);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tdataItem.get(\"node\").show();\n\t\t\t\t\t$array.each(children, (child) => {\n\t\t\t\t\t\tchild.get(\"node\").hide();\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tif (hierarchyNode.depth < topDepth) {\n\t\t\t\t\tdataItem.get(\"node\").hide(0);\n\t\t\t\t}\n\n\t\t\t\tif (this.get(\"singleBranchOnly\")) {\n\t\t\t\t\tthis._handleSingle(dataItem);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.enableDataItem(dataItem, downDepth);\n\t\t\t}\n\n\t\t\tthis._root.events.once(\"frameended\", () => {\n\t\t\t\tthis._zoom(dataItem);\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _zoom(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t}\n\n\tprotected _handleSingle(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst parent = dataItem.get(\"parent\");\n\t\tif (parent) {\n\t\t\tconst children = parent.get(\"children\");\n\t\t\tif (children) {\n\t\t\t\t$array.each(children, (child) => {\n\t\t\t\t\tif (child != dataItem) {\n\t\t\t\t\t\tthis.disableDataItem(child);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Selects specific data item.\n\t *\n\t * @param  dataItem  Target data item\n\t */\n\tpublic selectDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst parent = dataItem.get(\"parent\");\n\t\tconst maxDepth = this.getPrivate(\"maxDepth\", 1);\n\n\t\tif (this.get(\"selectedDataItem\") == dataItem) {\n\t\t\tif (parent) {\n\t\t\t\tthis.set(\"selectedDataItem\", parent);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlet depth = Math.min(this.get(\"downDepth\", 1), maxDepth - dataItem.get(\"depth\"));\n\n\t\t\t\tif (this._currentDownDepth == depth) {\n\t\t\t\t\tdepth = Math.min(this.get(\"initialDepth\", 1), maxDepth - this.get(\"topDepth\", 0));\n\t\t\t\t}\n\n\t\t\t\tthis._selectDataItem(dataItem, depth);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tthis.set(\"selectedDataItem\", dataItem);\n\t\t}\n\t}\n\n\n\tprotected _makeBullet(dataItem: DataItem<this[\"_dataItemSettings\"]>, bulletFunction: (root: Root, series: Series, dataItem: DataItem<this[\"_dataItemSettings\"]>) => Bullet | undefined, index?: number) {\n\t\tconst bullet = super._makeBullet(dataItem, bulletFunction, index);\n\t\tif (bullet) {\n\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\tconst node = dataItem.get(\"node\");\n\n\t\t\tif (sprite) {\n\t\t\t\tnode.children.push(sprite);\n\t\t\t\tnode.on(\"width\", () => {\n\t\t\t\t\tthis._positionBullet(bullet);\n\t\t\t\t})\n\t\t\t\tnode.on(\"height\", () => {\n\t\t\t\t\tthis._positionBullet(bullet);\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\treturn bullet;\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\n\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\n\t\t\tconst node = dataItem.get(\"node\");\n\n\t\t\tsprite.set(\"x\", node.width() * locationX);\n\t\t\tsprite.set(\"y\", node.height() * locationY);\n\t\t}\n\t}\n\n\t/**\n\t * Triggers hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic hoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst node = dataItem.get(\"node\");\n\t\tif (node && !node.isHidden()) {\n\t\t\tnode.hover();\n\t\t}\n\t}\n\n\t/**\n\t * Triggers un-hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic unhoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst node = dataItem.get(\"node\");\n\t\tif (node) {\n\t\t\tnode.unhover();\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { LinkedHierarchy, ILinkedHierarchyDataItem } from \"./LinkedHierarchy\";\n\nimport { HierarchyNode, IHierarchyNodePrivate, IHierarchyNodeSettings } from \"./HierarchyNode\";\n\nimport * as $array from \"../../core/util/Array\";\n\nexport interface ILinkedHierarchyNodeSettings extends IHierarchyNodeSettings {\n}\n\nexport interface ILinkedHierarchyNodePrivate extends IHierarchyNodePrivate {\n}\n\n/**\n * A node class for [[LinkedHierarchy]].\n */\nexport class LinkedHierarchyNode extends HierarchyNode {\n\n\t/**\n\t * A series node belongs to.\n\t */\n\tdeclare public series: LinkedHierarchy | undefined;\n\n\tdeclare public _settings: ILinkedHierarchyNodeSettings;\n\tdeclare public _privateSettings: ILinkedHierarchyNodePrivate;\n\n\tpublic static className: string = \"LinkedHierarchyNode\";\n\tpublic static classNames: Array<string> = HierarchyNode.classNames.concat([LinkedHierarchyNode.className]);\n\n\tdeclare protected _dataItem: DataItem<ILinkedHierarchyDataItem> | undefined;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\n\t\tthis.states.create(\"disabled\", {});\n\t\tthis.states.create(\"hover\", {});\n\t\tthis.states.create(\"hoverDisabled\", {});\n\t}\n\n\n\tpublic _updateLinks(duration?: number) {\n\t\tconst dataItem = this.dataItem;\n\t\tif (dataItem) {\n\t\t\tlet links = (dataItem as DataItem<ILinkedHierarchyDataItem>).get(\"links\");\n\n\t\t\t$array.each(links, (link) => {\n\t\t\t\tlet source = link.get(\"source\")\n\t\t\t\tlet target = link.get(\"target\")\n\n\t\t\t\tif (source && target) {\n\t\t\t\t\tif (source.get(\"node\").isHidden() || target.get(\"node\").isHidden()) {\n\t\t\t\t\t\tlink.hide(duration);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tlink.show(duration);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"disabled\")) {\n\t\t\tthis._updateLinks();\n\t\t}\n\t}\n\n\tprotected _onHide(duration?: number) {\n\t\tsuper._onHide(duration);\n\t\tthis._updateLinks(duration);\n\t}\n\n\tprotected _onShow(duration?: number) {\n\t\tsuper._onShow(duration);\n\t\tthis._updateLinks(duration);\n\t}\n}\n", "import type { IHierarchyDataItem } from \"./Hierarchy\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nimport { Graphics, IGraphicsSettings, IGraphicsPrivate } from \"../../core/render/Graphics\";\nimport * as $array from \"../../core/util/Array\";\nimport type { Root } from \"../../core/Root\";\nimport type { List } from \"../../core/util/List\";\nimport type { LinkedHierarchy } from \"./LinkedHierarchy\";\n\nexport interface IHierarchyLinkSettings extends IGraphicsSettings {\n\n\t/**\n\t * Source node data item.\n\t */\n\tsource?: DataItem<IHierarchyDataItem>;\n\n\t/**\n\t * Target node data item.\n\t */\n\ttarget?: DataItem<IHierarchyDataItem>;\n\n\t/**\n\t * Strength of the link.\n\t */\n\tstrength?: number;\n\n\t/**\n\t * Distance in pixels.\n\t */\n\tdistance?: number;\n\n}\n\nexport interface IHierarchyLinkPrivate extends IGraphicsPrivate {\n\td3Link: any;\n}\n\n/**\n * Draws a link between nodes in a hierarchy series.\n */\nexport class HierarchyLink extends Graphics {\n\tdeclare public _settings: IHierarchyLinkSettings;\n\tdeclare public _privateSettings: IHierarchyLinkPrivate;\n\n\tpublic static className: string = \"HierarchyLink\";\n\tpublic static classNames: Array<string> = Graphics.classNames.concat([HierarchyLink.className]);\n\n\tpublic bullets: Array<Bullet> = [];\n\n\tpublic series?: LinkedHierarchy;\n\n\tpublic _handleBullets(bullets:List<<D extends DataItem<IHierarchyDataItem>>(root: Root, source: D, target:D) => Bullet | undefined>) {\n\t\t$array.each(this.bullets, (bullet) => {\n\t\t\tbullet.dispose();\n\t\t})\n\t\t\n\t\tbullets.each((bullet)=>{\n\t\t\tconst newBullet = bullet(this._root, this.get(\"source\")!, this.get(\"target\")!);\n\t\t\tif (newBullet) {\n\t\t\t\tthis.bullets.push(newBullet);\n\n\t\t\t\tconst sprite = newBullet.get(\"sprite\");\n\n\t\t\t\tthis.addDisposer(newBullet.on(\"locationX\", () => {\n\t\t\t\t\tthis._clear = true;\n\t\t\t\t\tthis.markDirty();\n\t\t\t\t}))\n\n\t\t\t\tif(sprite){\n\t\t\t\t\tconst series = this.series;\n\t\t\t\t\tif(series){\n\t\t\t\t\t\tseries.linksContainer.children.push(sprite);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\t\t\t\n\t\t})\t\n\t}\n\n\tpublic _changed() {\n\t\tsuper._changed();\n\t\tif (this._clear) {\n\t\t\tlet source = this.get(\"source\");\n\t\t\tlet target = this.get(\"target\");\n\t\t\tif (source && target) {\n\t\t\t\tconst sourceNode = source.get(\"node\");\n\t\t\t\tconst targetNode = target.get(\"node\");\n\n\t\t\t\tconst x0 = sourceNode.x();\n\t\t\t\tconst y0 = sourceNode.y();\n\n\t\t\t\tconst x1 = targetNode.x();\n\t\t\t\tconst y1 = targetNode.y();\n\n\t\t\t\tthis._display.moveTo(x0, y0);\n\t\t\t\tthis._display.lineTo(x1, y1);\n\n\t\t\t\tconst sourceRadius = sourceNode.dataItem?.get(\"outerCircle\" as any).get(\"radius\", 0);\n\t\t\t\tconst targetRadius = targetNode.dataItem?.get(\"outerCircle\" as any).get(\"radius\", 0);\n\n\t\t\t\tconst distance = Math.hypot(x1 - x0, y1 - y0);\n\t\t\t\tconst trueDistance = distance - sourceRadius - targetRadius;\n\n\t\t\t\t$array.each(this.bullets, (bullet) => {\n\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\t\tif(sprite){\n\t\t\t\t\t\tconst location = bullet.get(\"locationX\", 0.5);\n\n\t\t\t\t\t\t// const tx = trueDistance / distance * (x1 - x0);\n\t\t\t\t\t\t// const ty = trueDistance / distance * (y1 - y0);\n\n\t\t\t\t\t\tsprite.set(\"x\", x0 + sourceRadius / distance * (x1 - x0) + trueDistance / distance * (x1 - x0) * location);\n\t\t\t\t\t\tsprite.set(\"y\", y0 + sourceRadius / distance * (y1 - y0) + trueDistance / distance * (y1 - y0) * location);\n\n\t\t\t\t\t\tif(bullet.get(\"autoRotate\")){\n\t\t\t\t\t\t\tsprite.set(\"rotation\", Math.atan2(y1 - y0, x1 - x0) * 180 / Math.PI + bullet.get(\"autoRotateAngle\", 0));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic hide(duration?: number){\n\t\t$array.each(this.bullets, (bullet) => {\n\t\t\tif(bullet){\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif(sprite){\n\t\t\t\t\tsprite.hide(duration);\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn super.hide();\t\n\t}\n\n\tpublic show(duration?: number){\n\t\t$array.each(this.bullets, (bullet) => {\n\t\t\tif(bullet){\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif(sprite){\n\t\t\t\t\tsprite.show(duration);\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn super.show();\t\n\t}\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this.isDirty(\"source\")) {\n\t\t\tconst source = this.get(\"source\");\n\t\t\tif (source) {\n\t\t\t\tconst sourceNode = source.get(\"node\");\n\t\t\t\tsourceNode.events.on(\"positionchanged\", () => {\n\t\t\t\t\tthis._markDirtyKey(\"stroke\");\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t\tif (this.isDirty(\"target\")) {\n\t\t\tconst target = this.get(\"target\");\n\t\t\tif (target) {\n\t\t\t\tconst targetNode = target.get(\"node\");\n\t\t\t\ttargetNode.events.on(\"positionchanged\", () => {\n\t\t\t\t\tthis._markDirtyKey(\"stroke\");\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic dispose(){\n\t\tsuper.dispose();\n\t\t$array.each(this.bullets, (bullet) => {\n\t\t\tbullet.dispose();\n\t\t})\n\t\tthis.bullets = [];\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Bullet } from \"../../core/render/Bullet\";\nimport type * as d3hierarchy from \"d3-hierarchy\";\n\nimport { Hierarchy, IHierarchySettings, IHierarchyDataItem, IHierarchyPrivate, IHierarchyEvents } from \"./Hierarchy\";\nimport { Container } from \"../../core/render/Container\";\nimport { LinkedHierarchyNode } from \"./LinkedHierarchyNode\";\nimport { HierarchyLink } from \"./HierarchyLink\";\nimport { Template } from \"../../core/util/Template\";\nimport { Circle } from \"../../core/render/Circle\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport type { IPoint } from \"../../core/util/IPoint\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $utils from \"../../core/util/Utils\";\nimport { List } from \"../../core/util/List\";\nimport type { Root } from \"../../core/Root\";\n\n/**\n * @ignore\n */\nexport interface ILinkedHierarchyDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: ILinkedHierarchyDataObject[],\n\tdataItem?: DataItem<ILinkedHierarchyDataItem>\n};\n\nexport interface ILinkedHierarchyDataItem extends IHierarchyDataItem {\n\n\t/**\n\t * An array of child data items.\n\t */\n\tchildren: Array<DataItem<ILinkedHierarchyDataItem>>;\n\n\t/**\n\t * A data item of a parent node.\n\t */\n\tparent: DataItem<ILinkedHierarchyDataItem>;\n\n\t/**\n\t * A related node.\n\t */\n\tnode: LinkedHierarchyNode;\n\n\t/**\n\t * [[Circle]] element of the related node.\n\t */\n\tcircle: Circle;\n\n\t/**\n\t * [[Circle]] element of the related node, representing outer circle.\n\t */\n\touterCircle: Circle;\n\n\t/**\n\t * A [[HierarchyLink]] leading to parent node.\n\t */\n\tparentLink: HierarchyLink;\n\n\t/**\n\t * An [[HierarchyLink]] leading to parent node.\n\t */\n\tlinks: Array<HierarchyLink>;\n\n\t/**\n\t * An array of [[HierarchyLink]] objects leading to child nodes.\n\t */\n\tchildLinks: Array<HierarchyLink>;\n\n\t/**\n\t * An array of IDs of directly linked nodes.\n\t */\n\tlinkWith: Array<string>;\n\n\t/**\n\t * @ignore\n\t */\n\td3HierarchyNode: d3hierarchy.HierarchyPointNode<ILinkedHierarchyDataObject>;\n}\n\nexport interface ILinkedHierarchySettings extends IHierarchySettings {\n\n\t/**\n\t * A field in data which holds IDs of nodes to link with.\n\t */\n\tlinkWithField?: string;\n\n}\n\nexport interface ILinkedHierarchyPrivate extends IHierarchyPrivate {\n}\n\nexport interface ILinkedHierarchyEvents extends IHierarchyEvents {\n}\n\n/**\n * A base class for linked hierarchy series.\n */\nexport abstract class LinkedHierarchy extends Hierarchy {\n\n\tpublic static className: string = \"LinkedHierarchy\";\n\tpublic static classNames: Array<string> = Hierarchy.classNames.concat([LinkedHierarchy.className]);\n\n\tdeclare public _settings: ILinkedHierarchySettings;\n\tdeclare public _privateSettings: ILinkedHierarchyPrivate;\n\tdeclare public _dataItemSettings: ILinkedHierarchyDataItem;\n\tdeclare public _events: ILinkedHierarchyEvents;\n\n\tpublic linkBullets: List<<D extends DataItem<IHierarchyDataItem>>(root: Root, source: D, target:D) => Bullet | undefined> = new List();\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"linkWith\", \"x\", \"y\");\n\n\t\tthis._disposers.push(this.linkBullets.events.onAll(() => {\n\t\t\tthis.links.each((link) => {\n\t\t\t\tlink._handleBullets(this.linkBullets);\n\t\t\t})\n\t\t}))\n\n\t\tsuper._afterNew();\n\t}\n\n\t/**\n\t * A list of nodes in a [[LinkedHierarchy]] chart.\n\t *\n\t * @default new ListTemplate<LinkedHierarchyNode>\n\t */\n\tpublic readonly nodes: ListTemplate<LinkedHierarchyNode> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => LinkedHierarchyNode._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.nodes.template.get(\"themeTags\", []), [this._tag, \"linkedhierarchy\", \"hierarchy\", \"node\"]),\n\t\t\tx: this.width() / 2,\n\t\t\ty: this.height() / 2\n\t\t}, [this.nodes.template])\n\t);\n\n\t/**\n\t * A list of node circle elements in a [[LinkedHierarchy]] chart.\n\t *\n\t * @default new ListTemplate<Circle>\n\t */\n\tpublic readonly circles: ListTemplate<Circle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Circle._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.circles.template.get(\"themeTags\", []), [this._tag, \"shape\"])\n\t\t}, [this.circles.template])\n\t);\n\n\t/**\n\t * A list of node outer circle elements in a [[LinkedHierarchy]] chart.\n\t *\n\t * @default new ListTemplate<Circle>\n\t */\n\tpublic readonly outerCircles: ListTemplate<Circle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Circle._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.outerCircles.template.get(\"themeTags\", []), [this._tag, \"outer\", \"shape\"])\n\t\t}, [this.outerCircles.template])\n\t);\n\n\t/**\n\t * A list of link elements in a [[LinkedHierarchy]] chart.\n\t *\n\t * @default new ListTemplate<HierarchyLink>\n\t */\n\tpublic readonly links: ListTemplate<HierarchyLink> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => HierarchyLink._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.links.template.get(\"themeTags\", []), [this._tag, \"linkedhierarchy\", \"hierarchy\", \"link\"])\n\t\t}, [this.links.template])\n\t);\n\n\t/**\n\t * A [[Container]] that link elements are placed in.\n\t *\n\t * @default Container.new()\n\t */\n\tpublic readonly linksContainer = this.children.moveValue(Container.new(this._root, {}), 0);\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): LinkedHierarchyNode {\n\t\tconst node = super.makeNode(dataItem) as LinkedHierarchyNode;\n\n\t\tconst circle = node.children.moveValue(this.circles.make(), 0);\n\t\tthis.circles.push(circle);\n\t\tnode.setPrivate(\"tooltipTarget\", circle);\n\t\tdataItem.setRaw(\"circle\", circle);\n\n\t\tconst outerCircle = node.children.moveValue(this.outerCircles.make(), 0);\n\t\tthis.outerCircles.push(outerCircle);\n\t\tdataItem.setRaw(\"outerCircle\", outerCircle);\n\n\t\tconst label = dataItem.get(\"label\");\n\n\t\tcircle.on(\"radius\", () => {\n\t\t\tconst d = circle.get(\"radius\", this.width()) * 2;\n\t\t\tlabel.setAll({ maxWidth: d, maxHeight: d })\n\t\t\touterCircle.set(\"radius\", d / 2);\n\n\t\t\tthis._handleRadiusChange();\n\t\t})\n\n\t\tconst d = circle.get(\"radius\", this.width()) * 2;\n\t\tlabel.setAll({ maxWidth: d, maxHeight: d });\n\t\t\n\t\tcircle._setDataItem(dataItem);\n\t\touterCircle._setDataItem(dataItem);\n\n\t\treturn node;\n\t}\n\n\tpublic _handleRadiusChange() {\n\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tdataItem.setRaw(\"childLinks\", []);\n\t\tdataItem.setRaw(\"links\", []);\n\t\tsuper.processDataItem(dataItem);\n\t}\n\n\tprotected _processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._processDataItem(dataItem);\n\n\t\tconst parentDataItem = dataItem.get(\"parent\");\n\t\tif (parentDataItem && parentDataItem.get(\"depth\") >= this.get(\"topDepth\")) {\n\t\t\tconst link = this.linkDataItems(parentDataItem, dataItem);\n\t\t\tdataItem.setRaw(\"parentLink\", link);\n\t\t}\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tthis.updateLinkWith(this.dataItems);\n\t\tnode._updateLinks(0);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateLinkWith(dataItems: Array<DataItem<this[\"_dataItemSettings\"]>>) {\n\t\t$array.each(dataItems, (dataItem) => {\n\t\t\tconst linkWith = dataItem.get(\"linkWith\");\n\t\t\tif (linkWith) {\n\t\t\t\t$array.each(linkWith, (id) => {\n\t\t\t\t\tconst linkWithDataItem = this._getDataItemById(this.dataItems, id);\n\n\t\t\t\t\tif (linkWithDataItem) {\n\t\t\t\t\t\tthis.linkDataItems(dataItem, linkWithDataItem);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tconst children = dataItem.get(\"children\");\n\t\t\tif (children) {\n\t\t\t\tthis.updateLinkWith(children);\n\t\t\t}\n\t\t})\n\t}\n\n\tprotected _getPoint(hierarchyNode: this[\"_dataItemSettings\"][\"d3HierarchyNode\"]): IPoint {\n\t\treturn { x: hierarchyNode.x, y: hierarchyNode.y };\n\t}\n\n\tprotected _animatePositions(dataItem: DataItem<this[\"_dataItemSettings\"]>){\n\t\tconst node = dataItem.get(\"node\");\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tconst point = this._getPoint(hierarchyNode);\n\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tnode.animate({ key: \"x\", to: point.x, duration: duration, easing: easing });\n\t\tnode.animate({ key: \"y\", to: point.y, duration: duration, easing: easing });\t\t\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\n\t\tthis._animatePositions(dataItem);\n\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\tif (hierarchyChildren) {\n\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\tthis._updateNodes(hierarchyChild)\n\t\t\t})\n\t\t}\n\n\t\tconst fill = dataItem.get(\"fill\");\n\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\t\tconst circle = dataItem.get(\"circle\");\n\t\tconst children = dataItem.get(\"children\");\n\t\t\n\t\tif (circle) {\n\t\t\tcircle._setDefault(\"fill\", fill);\n\t\t\tcircle._setDefault(\"fillPattern\", fillPattern);\n\t\t\tcircle._setDefault(\"stroke\", fill);\n\t\t}\n\n\t\tconst outerCircle = dataItem.get(\"outerCircle\");\n\t\tif (outerCircle) {\n\t\t\touterCircle._setDefault(\"fill\", fill);\n\t\t\touterCircle._setDefault(\"stroke\", fill);\n\n\t\t\tif (!children || children.length == 0) {\n\t\t\t\touterCircle.setPrivate(\"visible\", false);\n\t\t\t}\n\t\t\telse{\n\t\t\t\touterCircle.setPrivate(\"visible\", true);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Link two data items with a link element.\n\t *\n\t * @param   source    Source node data item\n\t * @param   target    Target node data item\n\t * @param   strength  Link strength\n\t * @return            Link element\n\t */\n\tpublic linkDataItems(source: DataItem<this[\"_dataItemSettings\"]>, target: DataItem<this[\"_dataItemSettings\"]>, strength?: number): HierarchyLink {\n\n\t\tlet link!: HierarchyLink;\n\n\t\tconst sourceLinks = source.get(\"links\");\n\n\t\tif (sourceLinks) {\n\t\t\t$array.each(sourceLinks, (lnk) => {\n\t\t\t\tif (lnk.get(\"target\") == target) {\n\t\t\t\t\tlink = lnk;\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tconst targetLinks = target.get(\"links\");\n\n\t\tif (targetLinks) {\n\t\t\t$array.each(targetLinks, (lnk) => {\n\t\t\t\tif (lnk.get(\"target\") == source) {\n\t\t\t\t\tlink = lnk;\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tif (!link) {\n\t\t\tlink = this.links.make();\n\t\t\tlink.series = this;\n\t\t\tthis.links.push(link);\n\t\t\tthis.linksContainer.children.push(link);\n\t\t\tlink.set(\"source\", source);\n\t\t\tlink.set(\"target\", target);\n\t\t\tlink._setDataItem(source);\n\n\t\t\tlink._handleBullets(this.linkBullets);\n\t\t\tlink.set(\"stroke\", source.get(\"fill\"));\n\t\t\tif (strength != null) {\n\t\t\t\tlink.set(\"strength\", strength)\n\t\t\t}\n\n\t\t\tsource.get(\"childLinks\").push(link);\n\n\t\t\t$array.move(source.get(\"links\"), link);\n\t\t\t$array.move(target.get(\"links\"), link);\n\n\t\t\tthis._processLink(link, source, target);\n\t\t}\n\n\t\treturn link;\n\t}\n\n\n\t/**\n\t * Unlink two linked data items.\n\t *\n\t * @param   source  Source node data item\n\t * @param   target  Target node data item\n\t */\n\tpublic unlinkDataItems(source: DataItem<this[\"_dataItemSettings\"]>, target: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t\tlet link!: HierarchyLink;\n\n\t\tconst sourceLinks = source.get(\"links\");\n\n\t\tif (sourceLinks) {\n\t\t\t$array.each(sourceLinks, (lnk) => {\n\t\t\t\tif (lnk && lnk.get(\"target\") == target) {\n\t\t\t\t\tlink = lnk;\n\t\t\t\t\t$array.remove(sourceLinks, link);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tconst targetLinks = target.get(\"links\");\n\n\t\tif (targetLinks) {\n\t\t\t$array.each(targetLinks, (lnk) => {\n\t\t\t\tif (lnk && lnk.get(\"target\") == source) {\n\t\t\t\t\tlink = lnk;\n\t\t\t\t\t$array.remove(targetLinks, link);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tif (link) {\n\t\t\tthis._disposeLink(link);\n\t\t}\n\n\t\tthis._handleUnlink();\n\t}\n\n\tprotected _handleUnlink() {\n\n\t}\n\n\tprotected _disposeLink(link: HierarchyLink) {\n\t\tthis.links.removeValue(link);\n\t\tlink.dispose();\n\t}\n\n\t/**\n\t * Returns `true` if two nodes are linked with each other.\n\t */\n\tpublic areLinked(source: DataItem<this[\"_dataItemSettings\"]>, target: DataItem<this[\"_dataItemSettings\"]>): boolean {\n\t\tconst sourceLinks = source.get(\"links\");\n\t\tlet linked = false;\n\t\tif (sourceLinks) {\n\t\t\t$array.each(sourceLinks, (lnk) => {\n\t\t\t\tif (lnk.get(\"target\") == target) {\n\t\t\t\t\tlinked = true;\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tconst targetLinks = target.get(\"links\");\n\n\t\tif (targetLinks) {\n\t\t\t$array.each(targetLinks, (lnk) => {\n\t\t\t\tif (lnk.get(\"target\") == source) {\n\t\t\t\t\tlinked = true;\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\treturn linked;\n\t}\n\n\tprotected _processLink(_link: HierarchyLink, _source: DataItem<this[\"_dataItemSettings\"]>, _target: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tconst links = dataItem.get(\"links\");\n\t\tif (links) {\n\t\t\t$array.each(links, (link) => {\n\t\t\t\tthis._disposeLink(link)\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * Select a data item.\n\t * @param  dataItem  Data item\n\t */\n\tpublic selectDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst parent = dataItem.get(\"parent\");\n\n\t\tif (!dataItem.get(\"disabled\")) {\n\t\t\tthis.set(\"selectedDataItem\", dataItem);\n\t\t}\n\t\telse {\n\t\t\tif (parent) {\n\t\t\t\tthis.setRaw(\"selectedDataItem\", parent);\n\t\t\t\tconst type = \"dataitemselected\";\n\t\t\t\tthis.events.dispatch(type, { type: type, target: this, dataItem: parent });\n\t\t\t\tthis.disableDataItem(dataItem);\n\t\t\t}\n\t\t}\n\t}\n}\n", "// https://en.wikipedia.org/wiki/Linear_congruential_generator#Parameters_in_common_use\nconst a = 1664525;\nconst c = 1013904223;\nconst m = 4294967296; // 2^32\n\nexport default function() {\n  let s = 1;\n  return () => (s = (a * s + c) % m) / m;\n}\n", "import {dispatch} from \"d3-dispatch\";\nimport {timer} from \"d3-timer\";\nimport lcg from \"./lcg.js\";\n\nexport function x(d) {\n  return d.x;\n}\n\nexport function y(d) {\n  return d.y;\n}\n\nvar initialRadius = 10,\n    initialAngle = Math.PI * (3 - Math.sqrt(5));\n\nexport default function(nodes) {\n  var simulation,\n      alpha = 1,\n      alphaMin = 0.001,\n      alphaDecay = 1 - Math.pow(alphaMin, 1 / 300),\n      alphaTarget = 0,\n      velocityDecay = 0.6,\n      forces = new Map(),\n      stepper = timer(step),\n      event = dispatch(\"tick\", \"end\"),\n      random = lcg();\n\n  if (nodes == null) nodes = [];\n\n  function step() {\n    tick();\n    event.call(\"tick\", simulation);\n    if (alpha < alphaMin) {\n      stepper.stop();\n      event.call(\"end\", simulation);\n    }\n  }\n\n  function tick(iterations) {\n    var i, n = nodes.length, node;\n\n    if (iterations === undefined) iterations = 1;\n\n    for (var k = 0; k < iterations; ++k) {\n      alpha += (alphaTarget - alpha) * alphaDecay;\n\n      forces.forEach(function(force) {\n        force(alpha);\n      });\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        if (node.fx == null) node.x += node.vx *= velocityDecay;\n        else node.x = node.fx, node.vx = 0;\n        if (node.fy == null) node.y += node.vy *= velocityDecay;\n        else node.y = node.fy, node.vy = 0;\n      }\n    }\n\n    return simulation;\n  }\n\n  function initializeNodes() {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.index = i;\n      if (node.fx != null) node.x = node.fx;\n      if (node.fy != null) node.y = node.fy;\n      if (isNaN(node.x) || isNaN(node.y)) {\n        var radius = initialRadius * Math.sqrt(0.5 + i), angle = i * initialAngle;\n        node.x = radius * Math.cos(angle);\n        node.y = radius * Math.sin(angle);\n      }\n      if (isNaN(node.vx) || isNaN(node.vy)) {\n        node.vx = node.vy = 0;\n      }\n    }\n  }\n\n  function initializeForce(force) {\n    if (force.initialize) force.initialize(nodes, random);\n    return force;\n  }\n\n  initializeNodes();\n\n  return simulation = {\n    tick: tick,\n\n    restart: function() {\n      return stepper.restart(step), simulation;\n    },\n\n    stop: function() {\n      return stepper.stop(), simulation;\n    },\n\n    nodes: function(_) {\n      return arguments.length ? (nodes = _, initializeNodes(), forces.forEach(initializeForce), simulation) : nodes;\n    },\n\n    alpha: function(_) {\n      return arguments.length ? (alpha = +_, simulation) : alpha;\n    },\n\n    alphaMin: function(_) {\n      return arguments.length ? (alphaMin = +_, simulation) : alphaMin;\n    },\n\n    alphaDecay: function(_) {\n      return arguments.length ? (alphaDecay = +_, simulation) : +alphaDecay;\n    },\n\n    alphaTarget: function(_) {\n      return arguments.length ? (alphaTarget = +_, simulation) : alphaTarget;\n    },\n\n    velocityDecay: function(_) {\n      return arguments.length ? (velocityDecay = 1 - _, simulation) : 1 - velocityDecay;\n    },\n\n    randomSource: function(_) {\n      return arguments.length ? (random = _, forces.forEach(initializeForce), simulation) : random;\n    },\n\n    force: function(name, _) {\n      return arguments.length > 1 ? ((_ == null ? forces.delete(name) : forces.set(name, initializeForce(_))), simulation) : forces.get(name);\n    },\n\n    find: function(x, y, radius) {\n      var i = 0,\n          n = nodes.length,\n          dx,\n          dy,\n          d2,\n          node,\n          closest;\n\n      if (radius == null) radius = Infinity;\n      else radius *= radius;\n\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        dx = x - node.x;\n        dy = y - node.y;\n        d2 = dx * dx + dy * dy;\n        if (d2 < radius) closest = node, radius = d2;\n      }\n\n      return closest;\n    },\n\n    on: function(name, _) {\n      return arguments.length > 1 ? (event.on(name, _), simulation) : event.on(name);\n    }\n  };\n}\n", "export default function(d) {\n  const x = +this._x.call(null, d),\n      y = +this._y.call(null, d);\n  return add(this.cover(x, y), x, y, d);\n}\n\nfunction add(tree, x, y, d) {\n  if (isNaN(x) || isNaN(y)) return tree; // ignore invalid points\n\n  var parent,\n      node = tree._root,\n      leaf = {data: d},\n      x0 = tree._x0,\n      y0 = tree._y0,\n      x1 = tree._x1,\n      y1 = tree._y1,\n      xm,\n      ym,\n      xp,\n      yp,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return tree._root = leaf, tree;\n\n  // Find the existing leaf for the new point, or add it.\n  while (node.length) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (parent = node, !(node = node[i = bottom << 1 | right])) return parent[i] = leaf, tree;\n  }\n\n  // Is the new point is exactly coincident with the existing point?\n  xp = +tree._x.call(null, node.data);\n  yp = +tree._y.call(null, node.data);\n  if (x === xp && y === yp) return leaf.next = node, parent ? parent[i] = leaf : tree._root = leaf, tree;\n\n  // Otherwise, split the leaf node until the old and new point are separated.\n  do {\n    parent = parent ? parent[i] = new Array(4) : tree._root = new Array(4);\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n  } while ((i = bottom << 1 | right) === (j = (yp >= ym) << 1 | (xp >= xm)));\n  return parent[j] = node, parent[i] = leaf, tree;\n}\n\nexport function addAll(data) {\n  var d, i, n = data.length,\n      x,\n      y,\n      xz = new Array(n),\n      yz = new Array(n),\n      x0 = Infinity,\n      y0 = Infinity,\n      x1 = -Infinity,\n      y1 = -Infinity;\n\n  // Compute the points and their extent.\n  for (i = 0; i < n; ++i) {\n    if (isNaN(x = +this._x.call(null, d = data[i])) || isNaN(y = +this._y.call(null, d))) continue;\n    xz[i] = x;\n    yz[i] = y;\n    if (x < x0) x0 = x;\n    if (x > x1) x1 = x;\n    if (y < y0) y0 = y;\n    if (y > y1) y1 = y;\n  }\n\n  // If there were no (valid) points, abort.\n  if (x0 > x1 || y0 > y1) return this;\n\n  // Expand the tree to cover the new points.\n  this.cover(x0, y0).cover(x1, y1);\n\n  // Add the new points.\n  for (i = 0; i < n; ++i) {\n    add(this, xz[i], yz[i], data[i]);\n  }\n\n  return this;\n}\n", "export default function(node, x0, y0, x1, y1) {\n  this.node = node;\n  this.x0 = x0;\n  this.y0 = y0;\n  this.x1 = x1;\n  this.y1 = y1;\n}\n", "export function defaultX(d) {\n  return d[0];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._x = _, this) : this._x;\n}\n", "export function defaultY(d) {\n  return d[1];\n}\n\nexport default function(_) {\n  return arguments.length ? (this._y = _, this) : this._y;\n}\n", "import tree_add, {addAll as tree_addAll} from \"./add.js\";\nimport tree_cover from \"./cover.js\";\nimport tree_data from \"./data.js\";\nimport tree_extent from \"./extent.js\";\nimport tree_find from \"./find.js\";\nimport tree_remove, {removeAll as tree_removeAll} from \"./remove.js\";\nimport tree_root from \"./root.js\";\nimport tree_size from \"./size.js\";\nimport tree_visit from \"./visit.js\";\nimport tree_visitAfter from \"./visitAfter.js\";\nimport tree_x, {defaultX} from \"./x.js\";\nimport tree_y, {defaultY} from \"./y.js\";\n\nexport default function quadtree(nodes, x, y) {\n  var tree = new Quadtree(x == null ? defaultX : x, y == null ? defaultY : y, NaN, NaN, NaN, NaN);\n  return nodes == null ? tree : tree.addAll(nodes);\n}\n\nfunction Quadtree(x, y, x0, y0, x1, y1) {\n  this._x = x;\n  this._y = y;\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  this._root = undefined;\n}\n\nfunction leaf_copy(leaf) {\n  var copy = {data: leaf.data}, next = copy;\n  while (leaf = leaf.next) next = next.next = {data: leaf.data};\n  return copy;\n}\n\nvar treeProto = quadtree.prototype = Quadtree.prototype;\n\ntreeProto.copy = function() {\n  var copy = new Quadtree(this._x, this._y, this._x0, this._y0, this._x1, this._y1),\n      node = this._root,\n      nodes,\n      child;\n\n  if (!node) return copy;\n\n  if (!node.length) return copy._root = leaf_copy(node), copy;\n\n  nodes = [{source: node, target: copy._root = new Array(4)}];\n  while (node = nodes.pop()) {\n    for (var i = 0; i < 4; ++i) {\n      if (child = node.source[i]) {\n        if (child.length) nodes.push({source: child, target: node.target[i] = new Array(4)});\n        else node.target[i] = leaf_copy(child);\n      }\n    }\n  }\n\n  return copy;\n};\n\ntreeProto.add = tree_add;\ntreeProto.addAll = tree_addAll;\ntreeProto.cover = tree_cover;\ntreeProto.data = tree_data;\ntreeProto.extent = tree_extent;\ntreeProto.find = tree_find;\ntreeProto.remove = tree_remove;\ntreeProto.removeAll = tree_removeAll;\ntreeProto.root = tree_root;\ntreeProto.size = tree_size;\ntreeProto.visit = tree_visit;\ntreeProto.visitAfter = tree_visitAfter;\ntreeProto.x = tree_x;\ntreeProto.y = tree_y;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "export default function(random) {\n  return (random() - 0.5) * 1e-6;\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction x(d) {\n  return d.x + d.vx;\n}\n\nfunction y(d) {\n  return d.y + d.vy;\n}\n\nexport default function(radius) {\n  var nodes,\n      radii,\n      random,\n      strength = 1,\n      iterations = 1;\n\n  if (typeof radius !== \"function\") radius = constant(radius == null ? 1 : +radius);\n\n  function force() {\n    var i, n = nodes.length,\n        tree,\n        node,\n        xi,\n        yi,\n        ri,\n        ri2;\n\n    for (var k = 0; k < iterations; ++k) {\n      tree = quadtree(nodes, x, y).visitAfter(prepare);\n      for (i = 0; i < n; ++i) {\n        node = nodes[i];\n        ri = radii[node.index], ri2 = ri * ri;\n        xi = node.x + node.vx;\n        yi = node.y + node.vy;\n        tree.visit(apply);\n      }\n    }\n\n    function apply(quad, x0, y0, x1, y1) {\n      var data = quad.data, rj = quad.r, r = ri + rj;\n      if (data) {\n        if (data.index > node.index) {\n          var x = xi - data.x - data.vx,\n              y = yi - data.y - data.vy,\n              l = x * x + y * y;\n          if (l < r * r) {\n            if (x === 0) x = jiggle(random), l += x * x;\n            if (y === 0) y = jiggle(random), l += y * y;\n            l = (r - (l = Math.sqrt(l))) / l * strength;\n            node.vx += (x *= l) * (r = (rj *= rj) / (ri2 + rj));\n            node.vy += (y *= l) * r;\n            data.vx -= x * (r = 1 - r);\n            data.vy -= y * r;\n          }\n        }\n        return;\n      }\n      return x0 > xi + r || x1 < xi - r || y0 > yi + r || y1 < yi - r;\n    }\n  }\n\n  function prepare(quad) {\n    if (quad.data) return quad.r = radii[quad.data.index];\n    for (var i = quad.r = 0; i < 4; ++i) {\n      if (quad[i] && quad[i].r > quad.r) {\n        quad.r = quad[i].r;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    radii = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], radii[node.index] = +radius(node, i, nodes);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = +_, force) : strength;\n  };\n\n  force.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : radius;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\n\nfunction index(d) {\n  return d.index;\n}\n\nfunction find(nodeById, nodeId) {\n  var node = nodeById.get(nodeId);\n  if (!node) throw new Error(\"node not found: \" + nodeId);\n  return node;\n}\n\nexport default function(links) {\n  var id = index,\n      strength = defaultStrength,\n      strengths,\n      distance = constant(30),\n      distances,\n      nodes,\n      count,\n      bias,\n      random,\n      iterations = 1;\n\n  if (links == null) links = [];\n\n  function defaultStrength(link) {\n    return 1 / Math.min(count[link.source.index], count[link.target.index]);\n  }\n\n  function force(alpha) {\n    for (var k = 0, n = links.length; k < iterations; ++k) {\n      for (var i = 0, link, source, target, x, y, l, b; i < n; ++i) {\n        link = links[i], source = link.source, target = link.target;\n        x = target.x + target.vx - source.x - source.vx || jiggle(random);\n        y = target.y + target.vy - source.y - source.vy || jiggle(random);\n        l = Math.sqrt(x * x + y * y);\n        l = (l - distances[i]) / l * alpha * strengths[i];\n        x *= l, y *= l;\n        target.vx -= x * (b = bias[i]);\n        target.vy -= y * b;\n        source.vx += x * (b = 1 - b);\n        source.vy += y * b;\n      }\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n\n    var i,\n        n = nodes.length,\n        m = links.length,\n        nodeById = new Map(nodes.map((d, i) => [id(d, i, nodes), d])),\n        link;\n\n    for (i = 0, count = new Array(n); i < m; ++i) {\n      link = links[i], link.index = i;\n      if (typeof link.source !== \"object\") link.source = find(nodeById, link.source);\n      if (typeof link.target !== \"object\") link.target = find(nodeById, link.target);\n      count[link.source.index] = (count[link.source.index] || 0) + 1;\n      count[link.target.index] = (count[link.target.index] || 0) + 1;\n    }\n\n    for (i = 0, bias = new Array(m); i < m; ++i) {\n      link = links[i], bias[i] = count[link.source.index] / (count[link.source.index] + count[link.target.index]);\n    }\n\n    strengths = new Array(m), initializeStrength();\n    distances = new Array(m), initializeDistance();\n  }\n\n  function initializeStrength() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      strengths[i] = +strength(links[i], i, links);\n    }\n  }\n\n  function initializeDistance() {\n    if (!nodes) return;\n\n    for (var i = 0, n = links.length; i < n; ++i) {\n      distances[i] = +distance(links[i], i, links);\n    }\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.links = function(_) {\n    return arguments.length ? (links = _, initialize(), force) : links;\n  };\n\n  force.id = function(_) {\n    return arguments.length ? (id = _, force) : id;\n  };\n\n  force.iterations = function(_) {\n    return arguments.length ? (iterations = +_, force) : iterations;\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initializeStrength(), force) : strength;\n  };\n\n  force.distance = function(_) {\n    return arguments.length ? (distance = typeof _ === \"function\" ? _ : constant(+_), initializeDistance(), force) : distance;\n  };\n\n  return force;\n}\n", "export default function(x, y) {\n  if (isNaN(x = +x) || isNaN(y = +y)) return this; // ignore invalid points\n\n  var x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1;\n\n  // If the quadtree has no extent, initialize them.\n  // Integer extent are necessary so that if we later double the extent,\n  // the existing quadrant boundaries don’t change due to floating point error!\n  if (isNaN(x0)) {\n    x1 = (x0 = Math.floor(x)) + 1;\n    y1 = (y0 = Math.floor(y)) + 1;\n  }\n\n  // Otherwise, double repeatedly to cover.\n  else {\n    var z = x1 - x0 || 1,\n        node = this._root,\n        parent,\n        i;\n\n    while (x0 > x || x >= x1 || y0 > y || y >= y1) {\n      i = (y < y0) << 1 | (x < x0);\n      parent = new Array(4), parent[i] = node, node = parent, z *= 2;\n      switch (i) {\n        case 0: x1 = x0 + z, y1 = y0 + z; break;\n        case 1: x0 = x1 - z, y1 = y0 + z; break;\n        case 2: x1 = x0 + z, y0 = y1 - z; break;\n        case 3: x0 = x1 - z, y0 = y1 - z; break;\n      }\n    }\n\n    if (this._root && this._root.length) this._root = node;\n  }\n\n  this._x0 = x0;\n  this._y0 = y0;\n  this._x1 = x1;\n  this._y1 = y1;\n  return this;\n}\n", "export default function() {\n  var data = [];\n  this.visit(function(node) {\n    if (!node.length) do data.push(node.data); while (node = node.next)\n  });\n  return data;\n}\n", "export default function(_) {\n  return arguments.length\n      ? this.cover(+_[0][0], +_[0][1]).cover(+_[1][0], +_[1][1])\n      : isNaN(this._x0) ? undefined : [[this._x0, this._y0], [this._x1, this._y1]];\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(x, y, radius) {\n  var data,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1,\n      y1,\n      x2,\n      y2,\n      x3 = this._x1,\n      y3 = this._y1,\n      quads = [],\n      node = this._root,\n      q,\n      i;\n\n  if (node) quads.push(new Quad(node, x0, y0, x3, y3));\n  if (radius == null) radius = Infinity;\n  else {\n    x0 = x - radius, y0 = y - radius;\n    x3 = x + radius, y3 = y + radius;\n    radius *= radius;\n  }\n\n  while (q = quads.pop()) {\n\n    // Stop searching if this quadrant can’t contain a closer node.\n    if (!(node = q.node)\n        || (x1 = q.x0) > x3\n        || (y1 = q.y0) > y3\n        || (x2 = q.x1) < x0\n        || (y2 = q.y1) < y0) continue;\n\n    // Bisect the current quadrant.\n    if (node.length) {\n      var xm = (x1 + x2) / 2,\n          ym = (y1 + y2) / 2;\n\n      quads.push(\n        new Quad(node[3], xm, ym, x2, y2),\n        new Quad(node[2], x1, ym, xm, y2),\n        new Quad(node[1], xm, y1, x2, ym),\n        new Quad(node[0], x1, y1, xm, ym)\n      );\n\n      // Visit the closest quadrant first.\n      if (i = (y >= ym) << 1 | (x >= xm)) {\n        q = quads[quads.length - 1];\n        quads[quads.length - 1] = quads[quads.length - 1 - i];\n        quads[quads.length - 1 - i] = q;\n      }\n    }\n\n    // Visit this point. (Visiting coincident points isn’t necessary!)\n    else {\n      var dx = x - +this._x.call(null, node.data),\n          dy = y - +this._y.call(null, node.data),\n          d2 = dx * dx + dy * dy;\n      if (d2 < radius) {\n        var d = Math.sqrt(radius = d2);\n        x0 = x - d, y0 = y - d;\n        x3 = x + d, y3 = y + d;\n        data = node.data;\n      }\n    }\n  }\n\n  return data;\n}\n", "export default function(d) {\n  if (isNaN(x = +this._x.call(null, d)) || isNaN(y = +this._y.call(null, d))) return this; // ignore invalid points\n\n  var parent,\n      node = this._root,\n      retainer,\n      previous,\n      next,\n      x0 = this._x0,\n      y0 = this._y0,\n      x1 = this._x1,\n      y1 = this._y1,\n      x,\n      y,\n      xm,\n      ym,\n      right,\n      bottom,\n      i,\n      j;\n\n  // If the tree is empty, initialize the root as a leaf.\n  if (!node) return this;\n\n  // Find the leaf node for the point.\n  // While descending, also retain the deepest parent with a non-removed sibling.\n  if (node.length) while (true) {\n    if (right = x >= (xm = (x0 + x1) / 2)) x0 = xm; else x1 = xm;\n    if (bottom = y >= (ym = (y0 + y1) / 2)) y0 = ym; else y1 = ym;\n    if (!(parent = node, node = node[i = bottom << 1 | right])) return this;\n    if (!node.length) break;\n    if (parent[(i + 1) & 3] || parent[(i + 2) & 3] || parent[(i + 3) & 3]) retainer = parent, j = i;\n  }\n\n  // Find the point to remove.\n  while (node.data !== d) if (!(previous = node, node = node.next)) return this;\n  if (next = node.next) delete node.next;\n\n  // If there are multiple coincident points, remove just the point.\n  if (previous) return (next ? previous.next = next : delete previous.next), this;\n\n  // If this is the root point, remove it.\n  if (!parent) return this._root = next, this;\n\n  // Remove this leaf.\n  next ? parent[i] = next : delete parent[i];\n\n  // If the parent now contains exactly one leaf, collapse superfluous parents.\n  if ((node = parent[0] || parent[1] || parent[2] || parent[3])\n      && node === (parent[3] || parent[2] || parent[1] || parent[0])\n      && !node.length) {\n    if (retainer) retainer[j] = node;\n    else this._root = node;\n  }\n\n  return this;\n}\n\nexport function removeAll(data) {\n  for (var i = 0, n = data.length; i < n; ++i) this.remove(data[i]);\n  return this;\n}\n", "export default function() {\n  return this._root;\n}\n", "export default function() {\n  var size = 0;\n  this.visit(function(node) {\n    if (!node.length) do ++size; while (node = node.next)\n  });\n  return size;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], q, node = this._root, child, x0, y0, x1, y1;\n  if (node) quads.push(new Quad(node, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    if (!callback(node = q.node, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1) && node.length) {\n      var xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n    }\n  }\n  return this;\n}\n", "import Quad from \"./quad.js\";\n\nexport default function(callback) {\n  var quads = [], next = [], q;\n  if (this._root) quads.push(new Quad(this._root, this._x0, this._y0, this._x1, this._y1));\n  while (q = quads.pop()) {\n    var node = q.node;\n    if (node.length) {\n      var child, x0 = q.x0, y0 = q.y0, x1 = q.x1, y1 = q.y1, xm = (x0 + x1) / 2, ym = (y0 + y1) / 2;\n      if (child = node[0]) quads.push(new Quad(child, x0, y0, xm, ym));\n      if (child = node[1]) quads.push(new Quad(child, xm, y0, x1, ym));\n      if (child = node[2]) quads.push(new Quad(child, x0, ym, xm, y1));\n      if (child = node[3]) quads.push(new Quad(child, xm, ym, x1, y1));\n    }\n    next.push(q);\n  }\n  while (q = next.pop()) {\n    callback(q.node, q.x0, q.y0, q.x1, q.y1);\n  }\n  return this;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Percent } from \"../../core/util/Percent\";\nimport type { LinkedHierarchyNode } from \"./LinkedHierarchyNode\";\nimport type { HierarchyLink } from \"./HierarchyLink\";\nimport type * as d3Hierarchy from \"d3-hierarchy\";\n\nimport { LinkedHierarchy, ILinkedHierarchySettings, ILinkedHierarchyDataItem, ILinkedHierarchyPrivate, ILinkedHierarchyEvents } from \"./LinkedHierarchy\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $type from \"../../core/util/Type\";\nimport * as d3Force from \"d3-force\";\n\n/**\n * @ignore\n */\nexport interface IForceDirectedDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: IForceDirectedDataObject[],\n\tdataItem?: DataItem<IForceDirectedDataItem>\n};\n\nexport interface IForceDirectedDataItem extends ILinkedHierarchyDataItem {\n\n\t/**\n\t * An array of data items of child nodes.\n\t */\n\tchildren: Array<DataItem<IForceDirectedDataItem>>;\n\n\t/**\n\t * Data item of a parent node.\n\t */\n\tparent: DataItem<IForceDirectedDataItem>;\n\n\t/**\n\t * @ignore\n\t */\n\td3ForceNode: d3Force.SimulationNodeDatum;\n\n\t/**\n\t * X coordinate.\n\t */\n\tx: number | undefined;\n\n\t/**\n\t * Y coordinate.\n\t */\n\ty: number | undefined;\n}\n\nexport interface IForceDirectedSettings extends ILinkedHierarchySettings {\n\n\t/**\n\t * Minimum gap in pixels between the nodes.\n\t */\n\tnodePadding?: number;\n\n\t/**\n\t * A force that attracts (or pushes back) all nodes to the center of the\n\t * chart.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default 0.5\n\t */\n\tcenterStrength?: number;\n\n\t/**\n\t * A force that attracts (or pushes back) all nodes to each other.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default -15\n\t */\n\tmanyBodyStrength?: number;\n\n\t/**\n\t * A force that attracts (or pushes back) nodes that are linked together\n\t * via `linkWithField`.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default 0.5\n\t */\n\tlinkWithStrength?: number | undefined;\n\n\t/**\n\t * Resistance acting agains node speed.\n\t *\n\t * The greater the value, the more \"sluggish\" the nodes will be.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default 0.5\n\t */\n\tvelocityDecay?: number;\n\n\t/**\n\t * Length of how long initial force simulation would run in frames.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default 500\n\t */\n\tinitialFrames?: number;\n\n\t/**\n\t * If set to a number will wait X number of frames before revealing\n\t * the tree.\n\t *\n\t * Can be used to hide initial animations where nodes settle into their\n\t * places.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Layout_and_force_simulation} for more info\n\t * @default 10\n\t */\n\tshowOnFrame?: number;\n\n\t/**\n\t * Smallest possible radius for a node circle.\n\t *\n\t * Can be a fixed pixel value or percent relative to chart size.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Sizing_nodes} for more info\n\t * @default 1%\n\t */\n\tminRadius?: number | Percent;\n\n\t/**\n\t * Biggest possible radius for a node circle.\n\t *\n\t * Can be a fixed pixel value or percent relative to chart size.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Sizing_nodes} for more info\n\t * @default 8%\n\t */\n\tmaxRadius?: number | Percent;\n\n\t/**\n\t * Field in data that holds X coordinate of the node.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Fixed_nodes} for more info\n\t */\n\txField?: string;\n\n\t/**\n\t * Field in data that holds X coordinate of the node.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/#Fixed_nodes} for more info\n\t */\n\tyField?: string;\n\n}\n\nexport interface IForceDirectedPrivate extends ILinkedHierarchyPrivate {\n}\n\nexport interface IForceDirectedEvents extends ILinkedHierarchyEvents {\n}\n\n/**\n * Creates a force-directed tree.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/force-directed/} for more info\n * @important\n */\nexport class ForceDirected extends LinkedHierarchy {\n\n\tprotected _tag: string = \"forcedirected\";\n\n\t/**\n\t * @ignore\n\t */\n\tpublic readonly d3forceSimulation: d3Force.Simulation<{}, d3Force.SimulationLinkDatum<d3Force.SimulationNodeDatum>> = d3Force.forceSimulation();\n\n\t/**\n\t * @ignore\n\t */\n\tpublic readonly collisionForce: d3Force.ForceCollide<d3Force.SimulationNodeDatum> = d3Force.forceCollide(20);\n\n\t/**\n\t * @ignore\n\t */\n\tpublic linkForce: d3Force.ForceLink<d3Force.SimulationNodeDatum, d3Force.SimulationLinkDatum<d3Force.SimulationNodeDatum>> = d3Force.forceLink();\n\n\tpublic static className: string = \"ForceDirected\";\n\tpublic static classNames: Array<string> = LinkedHierarchy.classNames.concat([ForceDirected.className]);\n\n\tdeclare public _settings: IForceDirectedSettings;\n\tdeclare public _privateSettings: IForceDirectedPrivate;\n\tdeclare public _dataItemSettings: IForceDirectedDataItem;\n\tdeclare public _events: IForceDirectedEvents;\n\n\tprotected _nodes: Array<any> = [];\n\tprotected _links: Array<any> = [];\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\n\t\tthis.d3forceSimulation.on(\"tick\", () => {\n\t\t\tthis._tick++;\n\t\t\tthis.updateNodePositions();\n\t\t});\n\t}\n\n\tprotected _tick: number = 0;\n\tprotected _nodesDirty: boolean = false;\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\t\tif (this.isDirty(\"showOnFrame\")) {\n\t\t\tconst showOnFrame = this.get(\"showOnFrame\");\n\t\t\tif (showOnFrame > this._tick) {\n\t\t\t\tthis.nodesContainer.setPrivate(\"visible\", false);\n\t\t\t\tthis.linksContainer.setPrivate(\"visible\", false);\n\t\t\t}\n\t\t}\n\n\t\tconst d3forceSimulation = this.d3forceSimulation;\n\n\t\tif (this.isDirty(\"velocityDecay\")) {\n\t\t\td3forceSimulation.velocityDecay(this.get(\"velocityDecay\", 0));\n\t\t}\n\n\t\tif (this.isDirty(\"initialFrames\")) {\n\t\t\td3forceSimulation.alphaDecay(1 - Math.pow(0.001, 1 / this.get(\"initialFrames\", 500)));\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic restartSimulation(alpha: number): void {\n\t\tconst d3forceSimulation = this.d3forceSimulation;\n\t\tif (d3forceSimulation.alpha() < .25) {\n\t\t\td3forceSimulation.alpha(alpha);\n\t\t\td3forceSimulation.restart();\n\t\t}\n\t}\n\n\tpublic _handleRadiusChange() {\n\t\tthis._updateForces();\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst d3ForceNode: any = { index: this._index, x: this.innerWidth() / 2, y: this.innerHeight() / 2, dataItem: dataItem };\n\t\tconst index = this._nodes.push(d3ForceNode) - 1;\n\t\td3ForceNode.index = index;\n\n\t\tthis.d3forceSimulation.nodes(this._nodes);\n\n\t\tdataItem.set(\"d3ForceNode\", d3ForceNode);\n\t\tsuper.processDataItem(dataItem);\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tnode.set(\"x\", -10000);\n\t\tnode.on(\"scale\", () => {\n\t\t\tthis._updateForces();\n\t\t})\n\n\t\tnode.events.on(\"dragged\", () => {\n\t\t\td3ForceNode.fx = node.x();\n\t\t\td3ForceNode.fy = node.y();\n\t\t\tthis._updateForces();\n\t\t})\n\n\t\tnode.events.on(\"dragstop\", () => {\n\t\t\tif (dataItem.get(\"x\") == null) {\n\t\t\t\td3ForceNode.fx = undefined;\n\t\t\t}\n\t\t\tif (dataItem.get(\"y\") == null) {\n\t\t\t\td3ForceNode.fy = undefined;\n\t\t\t}\n\t\t})\n\t}\n\n\tprotected _updateValues(d3HierarchyNode: d3Hierarchy.HierarchyNode<IForceDirectedDataObject>) {\n\t\tsuper._updateValues(d3HierarchyNode);\n\n\t\tthis._nodesDirty = true;\n\n\t\tconst d3forceSimulation = this.d3forceSimulation;\n\t\td3forceSimulation.force(\"collision\", this.collisionForce);\n\t\td3forceSimulation.nodes(this._nodes);\n\n\t\tthis.linkForce = d3Force.forceLink(this._links);\n\t\td3forceSimulation.force(\"link\", this.linkForce);\n\t}\n\n\tprotected _updateVisuals() {\n\t\tsuper._updateVisuals();\n\t\tthis.restartSimulation(.3);\n\t}\n\n\tpublic _updateChildren() {\n\t\tsuper._updateChildren();\n\n\t\tconst d3forceSimulation = this.d3forceSimulation;\n\n\t\tif (this._sizeDirty) {\n\t\t\tlet w = Math.max(50, this.innerWidth());\n\t\t\tlet h = Math.max(50, this.innerHeight());\n\t\t\tlet pt = this.get(\"paddingTop\", 0);\n\t\t\tlet pl = this.get(\"paddingLeft\", 0);\n\n\t\t\tlet centerStrength = this.get(\"centerStrength\", 1);\n\n\t\t\td3forceSimulation.force(\"x\", d3Force.forceX().x(w / 2 + pl).strength(centerStrength * 100 / w));\n\t\t\td3forceSimulation.force(\"y\", d3Force.forceY().y(h / 2 + pt).strength(centerStrength * 100 / h));\n\t\t}\n\n\t\tif (this._nodesDirty) {\n\t\t\tthis._updateForces();\n\t\t}\n\t}\n\n\tpublic _updateForces() {\n\t\tconst d3forceSimulation = this.d3forceSimulation;\n\t\td3forceSimulation.force(\"manybody\", d3Force.forceManyBody().strength((d3node) => {\n\t\t\tlet dataItem = (d3node as any).dataItem;\n\t\t\tlet node = dataItem.get(\"node\") as LinkedHierarchyNode;\n\t\t\tlet circle = dataItem.get(\"circle\");\n\t\t\tlet manyBodyStrength = this.get(\"manyBodyStrength\", -15);\n\n\t\t\tif (circle) {\n\t\t\t\treturn circle.get(\"radius\", 1) * node.get(\"scale\", 1) * manyBodyStrength;\n\t\t\t}\n\t\t\treturn 0;\n\t\t}));\n\n\t\tthis.collisionForce.radius((d3node) => {\n\t\t\tlet dataItem = (d3node as any).dataItem;\n\t\t\tlet node = dataItem.get(\"node\") as LinkedHierarchyNode;\n\t\t\tlet circle = dataItem.get(\"circle\");\n\t\t\tlet outerCircle = dataItem.get(\"outerCircle\");\n\t\t\tif (circle && outerCircle) {\n\t\t\t\tlet radius = circle.get(\"radius\", 1);\n\n\t\t\t\tif (!outerCircle.isHidden()) {\n\t\t\t\t\tradius = radius * outerCircle.get(\"scale\", 1.1);\n\t\t\t\t}\n\n\t\t\t\tradius *= node.get(\"scale\", 1);\n\n\t\t\t\treturn radius + this.get(\"nodePadding\", 0);\n\t\t\t}\n\t\t})\n\t\tthis.restartSimulation(0.3);\n\t}\n\n\tprotected _animatePositions(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\t// void, do not remove\n\t}\n\n\tpublic _clearDirty() {\n\t\tsuper._clearDirty();\n\t\tthis._nodesDirty = false;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateNodePositions() {\n\t\tconst linkForce = this.linkForce;\n\t\tif (linkForce) {\n\t\t\tlinkForce.distance((linkDatum) => {\n\t\t\t\treturn this.getDistance(linkDatum)\n\t\t\t});\n\t\t\tlinkForce.strength((linkDatum) => {\n\t\t\t\treturn this.getStrength(linkDatum)\n\t\t\t});\n\t\t}\n\t\tif (this._tick == this.get(\"showOnFrame\")) {\n\t\t\tthis.nodesContainer.setPrivate(\"visible\", true);\n\t\t\tthis.linksContainer.setPrivate(\"visible\", true);\n\t\t}\n\n\t\tlet d3Nodes = this.d3forceSimulation.nodes();\n\n\t\t$array.each(d3Nodes, (d3Node: any) => {\n\t\t\tconst dataItem = d3Node.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tconst node = dataItem.get(\"node\");\n\n\t\t\tnode.set(\"x\", d3Node.x);\n\t\t\tnode.set(\"y\", d3Node.y);\n\t\t})\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateLinkWith(dataItems: Array<DataItem<this[\"_dataItemSettings\"]>>) {\n\t\t$array.each(dataItems, (dataItem) => {\n\t\t\tconst linkWith = dataItem.get(\"linkWith\");\n\t\t\tif (linkWith) {\n\t\t\t\t$array.each(linkWith, (id) => {\n\t\t\t\t\tconst linkWithDataItem = this._getDataItemById(this.dataItems, id);\n\n\t\t\t\t\tif (linkWithDataItem) {\n\t\t\t\t\t\tthis.linkDataItems(dataItem, linkWithDataItem, this.get(\"linkWithStrength\"));\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tconst children = dataItem.get(\"children\");\n\t\t\tif (children) {\n\t\t\t\tthis.updateLinkWith(children);\n\t\t\t}\n\t\t})\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected getDistance(linkDatum: any) {\n\t\tlet sourceDataItem: DataItem<this[\"_dataItemSettings\"]> = <DataItem<this[\"_dataItemSettings\"]>>linkDatum.sourceDataItem;\n\t\tlet targetDataItem: DataItem<this[\"_dataItemSettings\"]> = <DataItem<this[\"_dataItemSettings\"]>>linkDatum.targetDataItem;\n\n\t\tlet distance = 0;\n\n\t\tif (sourceDataItem && targetDataItem) {\n\n\t\t\tconst targetNode = targetDataItem.get(\"node\");\n\t\t\tif (targetNode.isHidden()) {\n\t\t\t\treturn 0;\n\t\t\t}\n\n\t\t\tlet link = linkDatum.link;\n\t\t\tif (link) {\n\t\t\t\tdistance = link.get(\"distance\", 1);\n\t\t\t}\n\n\t\t\tconst sourceNode = sourceDataItem.get(\"node\");\n\n\t\t\tif (targetNode.isHidden()) {\n\t\t\t\tdistance = 1;\n\t\t\t}\n\n\t\t\treturn (distance * (sourceDataItem.get(\"circle\").get(\"radius\", 1) * sourceNode.get(\"scale\", 1) + targetDataItem.get(\"circle\").get(\"radius\", 1) * targetNode.get(\"scale\", 1)));\n\t\t}\n\t\treturn distance;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected getStrength(linkDatum: any) {\n\t\tlet strength = 0;\n\n\t\tlet link = linkDatum.link;\n\t\tif (link) {\n\t\t\tstrength = link.get(\"strength\", 1);\n\t\t}\n\n\t\tconst targetDataItem = linkDatum.targetDataItem;\n\t\tstrength *= targetDataItem.get(\"node\").get(\"scale\");\n\n\t\treturn strength;\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\t\tthis._updateRadius(dataItem);\n\n\t\tconst x = dataItem.get(\"x\");\n\t\tconst y = dataItem.get(\"y\");\n\n\t\tconst d3Node = dataItem.get(\"d3ForceNode\");\n\n\t\tif (x != null) {\n\t\t\t(d3Node as any).fx = $utils.relativeToValue(x, this.innerWidth());\n\t\t}\n\t\telse {\n\t\t\t(d3Node as any).fx = undefined;\n\t\t}\n\n\t\tif (y != null) {\n\t\t\t(d3Node as any).fy = $utils.relativeToValue(y, this.innerHeight());\n\t\t}\n\t\telse {\n\t\t\t(d3Node as any).fx = undefined;\n\t\t}\n\t}\n\n\tprotected _updateRadius(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tlet size = (this.innerWidth() + this.innerHeight()) / 2;\n\n\t\tlet minRadius = $utils.relativeToValue(this.get(\"minRadius\", 1), size);\n\t\tlet maxRadius = $utils.relativeToValue(this.get(\"maxRadius\", 5), size);\n\n\t\tlet valueWorking = dataItem.get(\"sum\");\n\n\t\tlet radius = maxRadius;\n\n\t\tconst min = this.getPrivate(\"valueLow\", 0);\n\t\tconst max = this.getPrivate(\"valueHigh\", 0);\n\n\t\tif (max > 0) {\n\t\t\tradius = minRadius + (valueWorking - min) / (max - min) * (maxRadius - minRadius);\n\t\t}\n\n\t\tif (!$type.isNumber(radius)) {\n\t\t\tradius = minRadius;\n\t\t}\n\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tdataItem.get(\"circle\").animate({ key: \"radius\", to: radius, duration: duration, easing: easing });\n\t}\n\n\tprotected _processLink(link: HierarchyLink, source: DataItem<this[\"_dataItemSettings\"]>, target: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst d3Link = { link: link, source: source.get(\"d3ForceNode\").index, target: target.get(\"d3ForceNode\").index, sourceDataItem: source, targetDataItem: target };\n\t\tthis._links.push(d3Link);\n\t\tlink.setPrivate(\"d3Link\", d3Link);\n\n\t\tthis.linkForce = d3Force.forceLink(this._links);\n\t\tthis.d3forceSimulation.force(\"link\", this.linkForce);\n\t\tthis.restartSimulation(0.5);\n\t}\n\n\tprotected _disposeLink(link: HierarchyLink) {\n\t\tsuper._disposeLink(link);\n\t\t$array.remove(this._links, link.getPrivate(\"d3Link\"));\n\t}\n\n\tprotected _handleUnlink() {\n\t\tthis.restartSimulation(0.5);\n\t}\n\n\tprotected _onDataClear() {\n\n\t\tsuper._onDataClear();\n\n\t\tthis._nodes = [];\n\t\tthis._links = [];\n\t}\n}\n", "import constant from \"./constant.js\";\n\nexport default function(x) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      xz;\n\n  if (typeof x !== \"function\") x = constant(x == null ? 0 : +x);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vx += (xz[i] - node.x) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    xz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(xz[i] = +x(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.x = function(_) {\n    return arguments.length ? (x = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : x;\n  };\n\n  return force;\n}\n", "import constant from \"./constant.js\";\n\nexport default function(y) {\n  var strength = constant(0.1),\n      nodes,\n      strengths,\n      yz;\n\n  if (typeof y !== \"function\") y = constant(y == null ? 0 : +y);\n\n  function force(alpha) {\n    for (var i = 0, n = nodes.length, node; i < n; ++i) {\n      node = nodes[i], node.vy += (yz[i] - node.y) * strengths[i] * alpha;\n    }\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length;\n    strengths = new Array(n);\n    yz = new Array(n);\n    for (i = 0; i < n; ++i) {\n      strengths[i] = isNaN(yz[i] = +y(nodes[i], i, nodes)) ? 0 : +strength(nodes[i], i, nodes);\n    }\n  }\n\n  force.initialize = function(_) {\n    nodes = _;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.y = function(_) {\n    return arguments.length ? (y = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : y;\n  };\n\n  return force;\n}\n", "import {quadtree} from \"d3-quadtree\";\nimport constant from \"./constant.js\";\nimport jiggle from \"./jiggle.js\";\nimport {x, y} from \"./simulation.js\";\n\nexport default function() {\n  var nodes,\n      node,\n      random,\n      alpha,\n      strength = constant(-30),\n      strengths,\n      distanceMin2 = 1,\n      distanceMax2 = Infinity,\n      theta2 = 0.81;\n\n  function force(_) {\n    var i, n = nodes.length, tree = quadtree(nodes, x, y).visitAfter(accumulate);\n    for (alpha = _, i = 0; i < n; ++i) node = nodes[i], tree.visit(apply);\n  }\n\n  function initialize() {\n    if (!nodes) return;\n    var i, n = nodes.length, node;\n    strengths = new Array(n);\n    for (i = 0; i < n; ++i) node = nodes[i], strengths[node.index] = +strength(node, i, nodes);\n  }\n\n  function accumulate(quad) {\n    var strength = 0, q, c, weight = 0, x, y, i;\n\n    // For internal nodes, accumulate forces from child quadrants.\n    if (quad.length) {\n      for (x = y = i = 0; i < 4; ++i) {\n        if ((q = quad[i]) && (c = Math.abs(q.value))) {\n          strength += q.value, weight += c, x += c * q.x, y += c * q.y;\n        }\n      }\n      quad.x = x / weight;\n      quad.y = y / weight;\n    }\n\n    // For leaf nodes, accumulate forces from coincident quadrants.\n    else {\n      q = quad;\n      q.x = q.data.x;\n      q.y = q.data.y;\n      do strength += strengths[q.data.index];\n      while (q = q.next);\n    }\n\n    quad.value = strength;\n  }\n\n  function apply(quad, x1, _, x2) {\n    if (!quad.value) return true;\n\n    var x = quad.x - node.x,\n        y = quad.y - node.y,\n        w = x2 - x1,\n        l = x * x + y * y;\n\n    // Apply the Barnes-Hut approximation if possible.\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (w * w / theta2 < l) {\n      if (l < distanceMax2) {\n        if (x === 0) x = jiggle(random), l += x * x;\n        if (y === 0) y = jiggle(random), l += y * y;\n        if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n        node.vx += x * quad.value * alpha / l;\n        node.vy += y * quad.value * alpha / l;\n      }\n      return true;\n    }\n\n    // Otherwise, process points directly.\n    else if (quad.length || l >= distanceMax2) return;\n\n    // Limit forces for very close nodes; randomize direction if coincident.\n    if (quad.data !== node || quad.next) {\n      if (x === 0) x = jiggle(random), l += x * x;\n      if (y === 0) y = jiggle(random), l += y * y;\n      if (l < distanceMin2) l = Math.sqrt(distanceMin2 * l);\n    }\n\n    do if (quad.data !== node) {\n      w = strengths[quad.data.index] * alpha / l;\n      node.vx += x * w;\n      node.vy += y * w;\n    } while (quad = quad.next);\n  }\n\n  force.initialize = function(_nodes, _random) {\n    nodes = _nodes;\n    random = _random;\n    initialize();\n  };\n\n  force.strength = function(_) {\n    return arguments.length ? (strength = typeof _ === \"function\" ? _ : constant(+_), initialize(), force) : strength;\n  };\n\n  force.distanceMin = function(_) {\n    return arguments.length ? (distanceMin2 = _ * _, force) : Math.sqrt(distanceMin2);\n  };\n\n  force.distanceMax = function(_) {\n    return arguments.length ? (distanceMax2 = _ * _, force) : Math.sqrt(distanceMax2);\n  };\n\n  force.theta = function(_) {\n    return arguments.length ? (theta2 = _ * _, force) : Math.sqrt(theta2);\n  };\n\n  return force;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { HierarchyNode } from \"./HierarchyNode\";\n\nimport { Hierarchy, IHierarchyPrivate, IHierarchySettings, IHierarchyDataItem, IHierarchyEvents } from \"./Hierarchy\";\nimport { Circle } from \"../../core/render/Circle\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as d3hierarchy from \"d3-hierarchy\";\nimport * as $utils from \"../../core/util/Utils\";\n\n/**\n * @ignore\n */\nexport interface IPackDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: IPackDataObject[],\n\tdataItem?: DataItem<IPackDataItem>\n};\n\nexport interface IPackDataItem extends IHierarchyDataItem {\n\n\t/**\n\t * An array of data items of node's children.\n\t */\n\tchildren: Array<DataItem<IPackDataItem>>;\n\n\t/**\n\t * A data item of node's parent.\n\t */\n\tparent: DataItem<IPackDataItem>;\n\n\t/**\n\t * @ignore\n\t */\n\td3HierarchyNode: d3hierarchy.HierarchyCircularNode<IPackDataObject>;\n\n\t/**\n\t * A [[Circle]] element of the node.\n\t */\n\tcircle: Circle;\n\n}\n\nexport interface IPackSettings extends IHierarchySettings {\n\n\t/**\n\t * Gap between nodes, in pixels.\n\t *\n\t * @since 5.2.6\n\t */\n\tnodePadding?:number\n\n}\n\nexport interface IPackPrivate extends IHierarchyPrivate {\n\n\t/**\n\t * @ignore\n\t */\n\tscaleR?: number;\n\n}\n\nexport interface IPackEvents extends IHierarchyEvents {\n}\n\n/**\n * Builds a pack diagram.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/sunburst/} for more info\n * @important\n */\nexport class Pack extends Hierarchy {\n\n\tdeclare public _settings: IPackSettings;\n\tdeclare public _privateSettings: IPackPrivate;\n\tdeclare public _dataItemSettings: IPackDataItem;\n\n\tprotected _tag: string = \"pack\";\n\n\tpublic static className: string = \"Pack\";\n\tpublic static classNames: Array<string> = Hierarchy.classNames.concat([Pack.className]);\n\n\tpublic _packLayout = d3hierarchy.pack();\n\tdeclare public _rootNode: d3hierarchy.HierarchyCircularNode<IPackDataObject> | undefined;\n\tpublic _packData: IPackDataObject | undefined;\n\n\t/**\n\t * A list of node circle elements in a [[Pack]] chart.\n\t *\n\t * @default new ListTemplate<Circle>\n\t */\n\tpublic readonly circles: ListTemplate<Circle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Circle._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.circles.template.get(\"themeTags\", []), [this._tag, \"shape\"])\n\t\t}, [this.circles.template])\n\t);\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.setPrivate(\"scaleR\", 1);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isPrivateDirty(\"scaleR\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodesScale(this._rootNode);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tconst packLayout = this._packLayout;\n\t\t\tpackLayout.size([this.innerWidth(), this.innerHeight()]);\n\t\t\tpackLayout(this._rootNode);\n\t\t\tpackLayout.padding(this.get(\"nodePadding\", 0));\n\t\t\tthis._updateNodes(this._rootNode);\n\t\t}\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tconst circle = dataItem.get(\"circle\");\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tconst scaleR = this.getPrivate(\"scaleR\", 1);\n\n\t\tconst x = hierarchyNode.x * scaleR;\n\t\tconst y = hierarchyNode.y * scaleR;\n\t\tconst radius = hierarchyNode.r * scaleR;\n\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tnode.animate({ key: \"x\", to: x, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"y\", to: y, duration: duration, easing: easing })\n\n\t\tif (circle) {\n\t\t\tconst fill = dataItem.get(\"fill\");\n\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\n\t\t\tcircle.animate({ key: \"radius\", to: radius, duration: duration, easing: easing })\n\t\t\tcircle._setDefault(\"fill\", fill);\n\t\t\tcircle._setDefault(\"fillPattern\", fillPattern);\n\t\t\tcircle._setDefault(\"stroke\", fill);\n\t\t}\n\t}\n\n\tprotected _updateNodesScale(hierarchyNode: d3hierarchy.HierarchyCircularNode<IPackDataObject>) {\n\t\tconst dataItem = hierarchyNode.data.dataItem;\n\t\tif (dataItem) {\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tconst circle = dataItem.get(\"circle\");\n\n\t\t\tconst scaleR = this.getPrivate(\"scaleR\", 1);\n\n\t\t\tconst x = hierarchyNode.x * scaleR;\n\t\t\tconst y = hierarchyNode.y * scaleR;\n\t\t\tconst radius = hierarchyNode.r * scaleR;\n\n\t\t\tnode.setAll({ x: x, y: y })\n\t\t\tcircle.set(\"radius\", radius);\n\n\t\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\t\tif (hierarchyChildren) {\n\t\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\t\tthis._updateNodesScale(hierarchyChild)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): HierarchyNode {\n\t\tconst node = super.makeNode(dataItem);\n\n\t\tconst circle = node.children.moveValue(this.circles.make(), 0);\n\t\tnode.setPrivate(\"tooltipTarget\", circle);\n\t\tthis.circles.push(circle);\n\t\tdataItem.setRaw(\"circle\", circle);\n\n\t\tconst label = dataItem.get(\"label\");\n\n\t\tcircle.on(\"radius\", () => {\n\t\t\tconst d = circle.get(\"radius\", this.width()) * 2;\n\t\t\tlabel.setAll({ maxWidth: d, maxHeight: d });\n\t\t})\n\n\t\treturn node;\n\t}\n\n\tpublic _zoom(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tlet x = hierarchyNode.x;\n\t\tlet y = hierarchyNode.y;\n\t\tlet r = hierarchyNode.r;\n\n\t\tlet scaleR = Math.min(this.innerWidth(), this.innerHeight()) / (r * 2);\n\n\t\tconst easing = this.get(\"animationEasing\");\n\t\tlet duration = this.get(\"animationDuration\", 0);\n\n\t\tif (!this.inited) {\n\t\t\tduration = 0;\n\t\t}\n\n\t\tthis.animatePrivate({ key: \"scaleR\", to: scaleR, duration: duration, easing: easing });\n\n\t\tconst nodesContainer = this.nodesContainer;\n\t\tnodesContainer.animate({ key: \"x\", from: nodesContainer.x(), to: this.width() / 2 - x * scaleR, duration: duration, easing: easing });\n\t\tnodesContainer.animate({ key: \"y\", from: nodesContainer.y(), to: this.height() / 2 - y * scaleR, duration: duration, easing: easing });\n\t}\n}\n", "export default function(node) {\n  node.x0 = Math.round(node.x0);\n  node.y0 = Math.round(node.y0);\n  node.x1 = Math.round(node.x1);\n  node.y1 = Math.round(node.y1);\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (x1 - x0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.y0 = y0, node.y1 = y1;\n    node.x0 = x0, node.x1 = x0 += node.value * k;\n  }\n}\n", "import roundNode from \"./treemap/round.js\";\nimport treemapDice from \"./treemap/dice.js\";\n\nexport default function() {\n  var dx = 1,\n      dy = 1,\n      padding = 0,\n      round = false;\n\n  function partition(root) {\n    var n = root.height + 1;\n    root.x0 =\n    root.y0 = padding;\n    root.x1 = dx;\n    root.y1 = dy / n;\n    root.eachBefore(positionNode(dy, n));\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(dy, n) {\n    return function(node) {\n      if (node.children) {\n        treemapDice(node, node.x0, dy * (node.depth + 1) / n, node.x1, dy * (node.depth + 2) / n);\n      }\n      var x0 = node.x0,\n          y0 = node.y0,\n          x1 = node.x1 - padding,\n          y1 = node.y1 - padding;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      node.x0 = x0;\n      node.y0 = y0;\n      node.x1 = x1;\n      node.y1 = y1;\n    };\n  }\n\n  partition.round = function(x) {\n    return arguments.length ? (round = !!x, partition) : round;\n  };\n\n  partition.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], partition) : [dx, dy];\n  };\n\n  partition.padding = function(x) {\n    return arguments.length ? (padding = +x, partition) : padding;\n  };\n\n  return partition;\n}\n", "import type { HierarchyNode } from \"./HierarchyNode\";\nimport type { DataItem } from \"../../core/render/Component\";\n\nimport { Hierarchy, IHierarchyPrivate, IHierarchySettings, IHierarchyDataItem, IHierarchyDataObject } from \"./Hierarchy\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { RoundedRectangle } from \"../../core/render/RoundedRectangle\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as d3hierarchy from \"d3-hierarchy\";\n\n/**\n * @ignore\n */\nexport interface IPartitionDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: IPartitionDataObject[],\n\tdataItem?: DataItem<IPartitionDataItem>\n};\n\nexport interface IPartitionDataItem extends IHierarchyDataItem {\n\n\t/**\n\t * Data items of child nodes.\n\t */\n\tchildren: Array<DataItem<IPartitionDataItem>>;\n\n\t/**\n\t * Data it of a parent node.\n\t */\n\tparent: DataItem<IPartitionDataItem>;\n\n\t/**\n\t * @ignore\n\t */\n\td3HierarchyNode: d3hierarchy.HierarchyRectangularNode<IHierarchyDataObject>;\n\n\t/**\n\t * A [[RoundedRectangle]] element of a node.\n\t */\n\trectangle: RoundedRectangle;\n\n}\n\nexport interface IPartitionSettings extends IHierarchySettings {\n\n\t/**\n\t * Gap between nodes in pixels.\n\t *\n\t * @default 0\n\t */\n\tnodePadding?: number;\n\n\t/**\n\t * Orientation of the diagram.\n\t *\n\t * @default \"vertical\"\n\t */\n\torientation?: \"horizontal\" | \"vertical\";\n\n\t/**\n\t * @ignore\n\t */\n\t_d?:number;\n}\n\nexport interface IPartitionPrivate extends IHierarchyPrivate {\n\n\t/**\n\t * Current horizontal scale.\n\t */\n\tscaleX?: number;\n\n\t/**\n\t * Current vertical scale.\n\t */\n\tscaleY?: number;\t\n}\n\n/**\n * Partition series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/partition/} for more info\n */\nexport class Partition extends Hierarchy {\n\n\tdeclare public _settings: IPartitionSettings;\n\tdeclare public _privateSettings: IPartitionPrivate;\n\tdeclare public _dataItemSettings: IPartitionDataItem;\n\n\tprotected _tag: string = \"partition\";\n\n\tpublic static className: string = \"Partition\";\n\tpublic static classNames: Array<string> = Hierarchy.classNames.concat([Partition.className]);\n\n\t/**\n\t * A list of node rectangle elements in a [[Partition]] chart.\n\t *\n\t * @default new ListTemplate<RoundedRectangle>\n\t */\n\tpublic readonly rectangles: ListTemplate<RoundedRectangle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => RoundedRectangle._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.rectangles.template.get(\"themeTags\", []), [this._tag, \"shape\"])\n\t\t}, [this.rectangles.template])\n\t);\n\n\tpublic _partitionLayout = d3hierarchy.partition();\n\n\tdeclare public _rootNode: d3hierarchy.HierarchyRectangularNode<IPartitionDataObject> | undefined;\n\n\tprotected _afterNew() {\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"partition\", this._settings.orientation || \"vertical\"]);\n\t\tsuper._afterNew();\n\t\tthis.setPrivate(\"scaleX\", 1);\n\t\tthis.setPrivate(\"scaleY\", 1);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"nodePadding\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodes(this._rootNode);\n\t\t\t}\n\t\t}\n\n\t\tif (this.isPrivateDirty(\"scaleX\") || this.isPrivateDirty(\"scaleY\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodesScale(this._rootNode);\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"orientation\")) {\n\t\t\tthis._updateVisuals();\n\t\t}\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tconst partitionLayout = this._partitionLayout;\n\t\t\tlet w = this.innerWidth();\n\t\t\tlet h = this.innerHeight();\n\n\t\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\t\t[w, h] = [h, w];\n\t\t\t}\n\n\t\t\tpartitionLayout.size([w, h]);\n\n\t\t\tconst nodePadding = this.get(\"nodePadding\");\n\n\t\t\tif ($type.isNumber(nodePadding)) {\n\t\t\t\tpartitionLayout.padding(nodePadding);\n\t\t\t}\n\n\n\t\t\tpartitionLayout(this._rootNode);\n\t\t\tthis._updateNodes(this._rootNode);\n\t\t}\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tconst rectangle = dataItem.get(\"rectangle\");\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\n\t\tlet x0, x1, y0, y1: number;\n\n\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\tx0 = hierarchyNode.y0 * scaleX;\n\t\t\tx1 = hierarchyNode.y1 * scaleX;\n\t\t\ty0 = hierarchyNode.x0 * scaleY;\n\t\t\ty1 = hierarchyNode.x1 * scaleY;\n\t\t}\n\t\telse {\n\t\t\tx0 = hierarchyNode.x0 * scaleX;\n\t\t\tx1 = hierarchyNode.x1 * scaleX;\n\t\t\ty0 = hierarchyNode.y0 * scaleY;\n\t\t\ty1 = hierarchyNode.y1 * scaleY;\n\t\t}\n\t\tlet w = x1 - x0;\n\t\tlet h = y1 - y0;\n\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tnode.animate({ key: \"x\", to: x0, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"y\", to: y0, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"width\", to: w, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"height\", to: h, duration: duration, easing: easing })\n\n\t\tif (rectangle) {\n\t\t\tconst fill = dataItem.get(\"fill\");\n\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\t\t\t\n\t\t\trectangle.animate({ key: \"width\", to: w, duration: duration, easing: easing })\n\t\t\trectangle.animate({ key: \"height\", to: h, duration: duration, easing: easing })\n\t\t\trectangle._setDefault(\"fill\", fill);\n\t\t\trectangle._setDefault(\"fillPattern\", fillPattern);\n\t\t\trectangle._setDefault(\"stroke\", fill);\t\t\t\n\t\t}\n\t}\n\n\n\tprotected _updateNodesScale(hierarchyNode: d3hierarchy.HierarchyRectangularNode<IPartitionDataObject>) {\n\t\tconst dataItem = hierarchyNode.data.dataItem;\n\t\tif (dataItem) {\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tconst rectangle = dataItem.get(\"rectangle\");\n\n\t\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\n\t\t\tlet x0, x1, y0, y1: number;\n\n\t\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\t\tx0 = hierarchyNode.y0 * scaleX;\n\t\t\t\tx1 = hierarchyNode.y1 * scaleX;\n\t\t\t\ty0 = hierarchyNode.x0 * scaleY;\n\t\t\t\ty1 = hierarchyNode.x1 * scaleY;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tx0 = hierarchyNode.x0 * scaleX;\n\t\t\t\tx1 = hierarchyNode.x1 * scaleX;\n\t\t\t\ty0 = hierarchyNode.y0 * scaleY;\n\t\t\t\ty1 = hierarchyNode.y1 * scaleY;\n\t\t\t}\n\n\t\t\tconst w = x1 - x0;\n\t\t\tconst h = y1 - y0;\n\n\t\t\tnode.setAll({ x: x0, y: y0, width: w, height: h });\n\t\t\trectangle.setAll({ width: w, height: h });\n\n\t\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\t\tif (hierarchyChildren) {\n\t\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\t\tthis._updateNodesScale(hierarchyChild)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): HierarchyNode {\n\t\tconst node = super.makeNode(dataItem);\n\t\tthis._makeNode(dataItem, node);\n\t\treturn node;\n\t}\n\n\tprotected _makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>, node: HierarchyNode) {\n\t\tconst rectangle = node.children.moveValue(this.rectangles.make(), 0);\n\t\tnode.setPrivate(\"tooltipTarget\", rectangle);\n\t\tdataItem.setRaw(\"rectangle\", rectangle);\n\t\trectangle._setDataItem(dataItem);\n\n\t\tconst label = dataItem.get(\"label\");\n\n\t\trectangle.on(\"width\", () => {\n\t\t\tlabel.set(\"maxWidth\", rectangle.width());\n\t\t})\n\n\t\trectangle.on(\"height\", () => {\n\t\t\tlabel.set(\"maxHeight\", rectangle.height());\n\t\t})\n\t}\n\n\tprotected _zoom(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tlet x0 = 0;\n\t\tlet x1 = 0;\n\t\tlet y0 = 0;\n\t\tlet y1 = 0;\n\n\t\tconst upDepth = this.get(\"upDepth\", 0) + 1;\n\t\tconst topDepth = this.get(\"topDepth\", 0);\n\n\t\tconst width = this.innerWidth();\n\t\tconst height = this.innerHeight();\n\n\t\tconst maxDepth = this.getPrivate(\"maxDepth\", 1);\n\t\tconst levelHeight = height / (maxDepth + 1);\n\t\tconst levelWidth = width / (maxDepth + 1);\n\t\tconst initialDepth = Math.min(this.get(\"initialDepth\", 1), maxDepth)// - topDepth);\n\n\t\tlet downDepth = this._currentDownDepth;\n\t\tif (downDepth == null) {\n\t\t\tdownDepth = this.get(\"downDepth\", 1);\n\t\t}\n\n\t\tif (dataItem) {\n\t\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\t\t\tlet currentDepth = hierarchyNode.depth;\n\n\t\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\t\tx0 = hierarchyNode.y0;\n\t\t\t\tx1 = hierarchyNode.y1;\n\n\t\t\t\ty0 = hierarchyNode.x0;\n\t\t\t\ty1 = hierarchyNode.x1;\n\n\t\t\t\tx0 = x1 - levelWidth * upDepth;\n\t\t\t\tx1 = x0 + levelWidth * (downDepth + 1);\n\n\t\t\t\tif (currentDepth < topDepth) {\n\t\t\t\t\ty0 = 0;\n\t\t\t\t\ty1 = height;\n\t\t\t\t\tx0 = levelWidth * topDepth;\n\t\t\t\t\tx1 = x0 + levelWidth * initialDepth;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tx0 = hierarchyNode.x0;\n\t\t\t\tx1 = hierarchyNode.x1;\n\n\t\t\t\ty0 = hierarchyNode.y0;\n\t\t\t\ty1 = hierarchyNode.y1;\n\n\t\t\t\ty0 = y1 - levelHeight * upDepth;\n\t\t\t\ty1 = y0 + levelHeight * (downDepth + 1);\n\n\t\t\t\tif (currentDepth < topDepth) {\n\t\t\t\t\tx0 = 0;\n\t\t\t\t\tx1 = width;\n\t\t\t\t\ty0 = levelHeight * topDepth;\n\t\t\t\t\ty1 = y0 + levelHeight * initialDepth;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tlet scaleX = width / (x1 - x0);\n\t\tlet scaleY = height / (y1 - y0);\n\n\t\tconst easing = this.get(\"animationEasing\");\n\t\tlet duration = this.get(\"animationDuration\", 0);\n\n\t\tif (!this.inited) {\n\t\t\tduration = 0;\n\t\t}\n\n\t\tthis.animatePrivate({ key: \"scaleX\", to: scaleX, duration: duration, easing: easing });\n\t\tthis.animatePrivate({ key: \"scaleY\", to: scaleY, duration: duration, easing: easing });\t\t\n\n\t\tthis.animate({key:\"_d\", from:0, to:1, duration: duration, easing: easing })\n\t\tthis.nodesContainer.animate({ key: \"x\", to: -x0 * scaleX, duration: duration, easing: easing });\n\t\tthis.nodesContainer.animate({ key: \"y\", to: -y0 * scaleY, duration: duration, easing: easing });\n\t}\n}\n", "import type { Root } from \"../../core/Root\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { HierarchyNode } from \"./HierarchyNode\";\nimport type { Bullet } from \"../../core/render/Bullet\";\nimport type { Series } from \"../../core/render/Series\";\n\nimport { Partition, IPartitionPrivate, IPartitionSettings, IPartitionDataItem } from \"./Partition\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { Slice } from \"../../core/render/Slice\";\nimport { RadialLabel } from \"../../core/render/RadialLabel\";\nimport { Percent, p100, p50 } from \"../../core/util/Percent\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as d3hierarchy from \"d3-hierarchy\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $math from \"../../core/util/Math\";\n\n/**\n * @ignore\n */\nexport interface ISunburstDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: ISunburstDataObject[],\n\tdataItem?: DataItem<ISunburstDataItem>\n};\n\nexport interface ISunburstDataItem extends IPartitionDataItem {\n\n\t/**\n\t * Data items of child nodes.\n\t */\n\tchildren: Array<DataItem<ISunburstDataItem>>;\n\n\t/**\n\t * Data item of a parent node.\n\t */\n\tparent: DataItem<ISunburstDataItem>;\n\n\t/**\n\t * @ignore\n\t */\n\td3PartitionNode: d3hierarchy.HierarchyRectangularNode<ISunburstDataObject>;\n\n\t/**\n\t * A [[Slice]] element of the node.\n\t */\n\tslice: Slice;\n\n}\n\nexport interface ISunburstSettings extends IPartitionSettings {\n\n\t/**\n\t * Start angle of the series.\n\t *\n\t * @default -90\n\t */\n\tstartAngle?: number;\n\n\t/**\n\t * End angle of the series.\n\t *\n\t * @default 270\n\t */\n\tendAngle?: number;\n\n\t/**\n\t * Inner radius of the suburst pie.\n\t *\n\t * Setting to negative number will mean pixels from outer radius.\n\t *\n\t * @default 0\n\t */\n\tinnerRadius?: number | Percent;\n\n\t/**\n\t * Outer radius of the sunburst pie.\n\t *\n\t * @default 100%\n\t */\n\tradius?: number | Percent;\n\n}\n\nexport interface ISunburstPrivate extends IPartitionPrivate {\n\n\t/**\n\t * @ignore\n\t */\n\tdr: number;\n\n\t/**\n\t * @ignore\n\t */\n\tdx: number;\n\n\t/**\n\t * @ignore\n\t */\n\tinnerRadius: number;\n\n\t/**\n\t * @ignore\n\t */\n\thierarchySize?: number;\n\n}\n\n/**\n * Builds a sunburst diagram.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/sunburst/} for more info\n * @important\n */\nexport class Sunburst extends Partition {\n\n\tdeclare public _settings: ISunburstSettings;\n\tdeclare public _privateSettings: ISunburstPrivate;\n\tdeclare public _dataItemSettings: ISunburstDataItem;\n\n\tprotected _tag: string = \"sunburst\";\n\n\tpublic static className: string = \"Sunburst\";\n\tpublic static classNames: Array<string> = Partition.classNames.concat([Sunburst.className]);\n\n\tpublic _partitionLayout = d3hierarchy.partition();\n\n\tdeclare public _rootNode: d3hierarchy.HierarchyRectangularNode<ISunburstDataObject> | undefined;\n\n\t/**\n\t * A list of node slice elements in a [[Sunburst]] chart.\n\t *\n\t * @default new ListTemplate<Slice>\n\t */\n\tpublic readonly slices: ListTemplate<Slice> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => Slice._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.slices.template.get(\"themeTags\", []), [this._tag, \"hierarchy\", \"node\", \"shape\"])\n\t\t}, [this.slices.template])\n\t);\n\n\t/**\n\t * A list of label elements in a [[Hierarchy]] chart.\n\t *\n\t * @default new ListTemplate<RadialLabel>\n\t */\n\tpublic readonly labels: ListTemplate<RadialLabel> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => RadialLabel._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [this._tag])\n\t\t}, [this.labels.template])\n\t);\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.nodesContainer.setAll({ x: p50, y: p50 });\n\t\tthis.setPrivateRaw(\"dx\", 0);\n\t\tthis.setPrivateRaw(\"dr\", 0);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isPrivateDirty(\"dr\") || this.isPrivateDirty(\"dx\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodesScale(this._rootNode);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tconst partitionLayout = this._partitionLayout;\n\n\t\t\tlet bounds = $math.getArcBounds(0, 0, this.get(\"startAngle\", 0), this.get(\"endAngle\", 360), 1);\n\n\t\t\tlet w = this.innerWidth();\n\t\t\tlet h = this.innerHeight();\n\n\t\t\tconst wr = w / (bounds.right - bounds.left);\n\t\t\tconst hr = h / (bounds.bottom - bounds.top);\n\n\t\t\tlet s = Math.min(wr, hr);\n\n\t\t\tlet r = $utils.relativeToValue(this.get(\"radius\", p100), s);\n\t\t\tlet ir = $utils.relativeToValue(this.get(\"innerRadius\", 0), r);\n\n\t\t\tif (ir < 0) {\n\t\t\t\tir = r + ir;\n\t\t\t}\n\n\t\t\ts = r - ir;\n\n\t\t\tthis.setPrivateRaw(\"innerRadius\", ir);\n\t\t\tthis.setPrivateRaw(\"hierarchySize\", s);\n\n\t\t\tpartitionLayout.size([s, s]);\n\n\t\t\tthis.nodesContainer.setAll({\n\t\t\t\tdy: -r * (bounds.bottom + bounds.top) / 2, dx: -r * (bounds.right + bounds.left) / 2\n\t\t\t})\n\n\t\t\tconst nodePadding = this.get(\"nodePadding\");\n\n\t\t\tif ($type.isNumber(nodePadding)) {\n\t\t\t\tpartitionLayout.padding(nodePadding);\n\t\t\t}\n\n\n\t\t\tpartitionLayout(this._rootNode);\n\t\t\tthis._updateNodes(this._rootNode);\n\t\t}\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\t\tconst node = dataItem.get(\"node\");\n\n\t\tnode.setAll({ x: 0, y: 0 });\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\t\tconst dr = this.getPrivate(\"dr\", 0);\n\t\tconst dx = this.getPrivate(\"dx\", 0);\n\n\t\tconst x0 = hierarchyNode.x0 * scaleX + dx;\n\t\tconst x1 = hierarchyNode.x1 * scaleX + dx;\n\t\tconst y0 = hierarchyNode.y0 * scaleY;\n\t\tconst y1 = hierarchyNode.y1 * scaleY;\n\n\t\tconst ir = this.getPrivate(\"innerRadius\");\n\t\tconst hs = this.getPrivate(\"hierarchySize\", 0);\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tconst startAngle = this.get(\"startAngle\", -90);\n\t\t\tconst endAngle = this.get(\"endAngle\", 270);\n\n\t\t\tconst sliceStartAngle = startAngle + x0 / hs * (endAngle - startAngle);\n\t\t\tconst arc = startAngle + x1 / hs * (endAngle - startAngle) - sliceStartAngle;\n\n\t\t\tlet sliceInnerRadius = ir + y0;\n\t\t\tlet sliceRadius = ir + y1;\n\n\t\t\tsliceInnerRadius -= dr;\n\t\t\tsliceRadius -= dr;\n\n\t\t\tsliceRadius = Math.max(0, sliceRadius);\n\t\t\tsliceInnerRadius = Math.max(0, sliceInnerRadius);\n\n\t\t\tslice.animate({ key: \"radius\", to: sliceRadius, duration: duration, easing: easing })\n\t\t\tslice.animate({ key: \"innerRadius\", to: sliceInnerRadius, duration: duration, easing: easing })\n\t\t\tslice.animate({ key: \"startAngle\", to: sliceStartAngle, duration: duration, easing: easing })\n\t\t\tslice.animate({ key: \"arc\", to: arc, duration: duration, easing: easing })\n\n\t\t\tconst fill = dataItem.get(\"fill\");\n\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\n\t\t\tslice._setDefault(\"fill\", fill);\n\t\t\tslice._setDefault(\"fillPattern\", fillPattern);\n\t\t\tslice._setDefault(\"stroke\", fill);\n\t\t}\n\t}\n\n\n\tprotected _updateNodesScale(hierarchyNode: d3hierarchy.HierarchyRectangularNode<ISunburstDataObject>) {\n\t\tconst dataItem = hierarchyNode.data.dataItem;\n\t\tif (dataItem) {\n\t\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\t\t\tconst dr = this.getPrivate(\"dr\", 0);\n\t\t\tconst dx = this.getPrivate(\"dx\", 0);\n\n\t\t\tconst x0 = hierarchyNode.x0 * scaleX + dx;\n\t\t\tconst x1 = hierarchyNode.x1 * scaleX + dx;\n\t\t\tconst y0 = hierarchyNode.y0 * scaleY;\n\t\t\tconst y1 = hierarchyNode.y1 * scaleY;\n\n\t\t\tconst ir = this.getPrivate(\"innerRadius\");\n\t\t\tconst hs = this.getPrivate(\"hierarchySize\", 0);\n\n\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\tif (slice) {\n\t\t\t\tconst startAngle = this.get(\"startAngle\", -90);\n\t\t\t\tconst endAngle = this.get(\"endAngle\", 270);\n\n\t\t\t\tconst sliceStartAngle = startAngle + x0 / hs * (endAngle - startAngle);\n\t\t\t\tconst arc = startAngle + x1 / hs * (endAngle - startAngle) - sliceStartAngle;\n\n\t\t\t\tlet sliceInnerRadius = ir + y0;\n\t\t\t\tlet sliceRadius = ir + y1;\n\n\t\t\t\tsliceInnerRadius -= dr;\n\t\t\t\tsliceRadius -= dr;\n\n\t\t\t\tsliceRadius = Math.max(0, sliceRadius);\n\t\t\t\tsliceInnerRadius = Math.max(0, sliceInnerRadius);\n\n\t\t\t\tslice.setAll({ radius: sliceRadius, innerRadius: sliceInnerRadius, startAngle: sliceStartAngle, arc: arc });\n\t\t\t}\n\n\t\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\t\tif (hierarchyChildren) {\n\t\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\t\tthis._updateNodesScale(hierarchyChild)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>, node: HierarchyNode) {\n\t\tconst slice = node.children.moveValue(this.slices.make(), 0);\n\t\tnode.setPrivate(\"tooltipTarget\", slice);\n\t\tdataItem.setRaw(\"slice\", slice);\n\n\t\tslice._setDataItem(dataItem);\n\n\t\tslice.on(\"arc\", () => {\n\t\t\tthis._updateLabel(dataItem);\n\t\t})\n\t\tslice.on(\"innerRadius\", () => {\n\t\t\tthis._updateLabel(dataItem);\n\t\t})\n\t\tslice.on(\"radius\", () => {\n\t\t\tthis._updateLabel(dataItem);\n\t\t})\n\t}\n\n\tprotected _updateLabel(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tconst label = dataItem.get(\"label\") as RadialLabel;\n\n\t\tif (slice && label) {\n\t\t\tlet innerRadius = slice.get(\"innerRadius\", 0);\n\t\t\tlet radius = slice.get(\"radius\", 0);\n\t\t\tlet angle = slice.get(\"startAngle\", 0);\n\t\t\tlet arc = Math.abs(slice.get(\"arc\", 0));\n\t\t\tlet labelAngle = angle + arc / 2;\n\t\t\tlet textType = label.get(\"textType\");\n\n\t\t\tlet maxWidth = radius - innerRadius;\n\t\t\tlet maxHeight = radius * arc * $math.RADIANS;\n\n\t\t\tif (innerRadius == 0 && arc >= 360 && textType == \"radial\") {\n\t\t\t\tradius = 1;\n\t\t\t\tlabelAngle = 0;\n\t\t\t\tmaxWidth *= 2;\n\t\t\t\tmaxHeight = maxWidth;\t\t\t\t\n\t\t\t}\n\n\t\t\tif (Math.round(arc) >= 360 && textType == \"radial\") {\n\t\t\t\tlabelAngle = 0;\n\t\t\t}\t\t\t\n\n\t\t\tif (textType == \"circular\") {\n\t\t\t\tmaxWidth = arc * $math.RADIANS * (innerRadius + (radius - innerRadius) / 2) - 10;\n\t\t\t}\n\n\n\t\t\tlabel.setAll({ labelAngle: labelAngle });\n\t\t\tlabel.setPrivate(\"radius\", radius);\n\t\t\tlabel.setPrivate(\"innerRadius\", innerRadius);\n\n\t\t\tlabel.setAll({\n\t\t\t\tmaxHeight: maxHeight,\n\t\t\t\tmaxWidth: maxWidth\n\t\t\t});\n\t\t}\n\t}\n\n\tprotected _zoom(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tlet x0 = 0;\n\t\tlet x1 = 0;\n\t\tlet hs = this.getPrivate(\"hierarchySize\", 0);\n\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tlet upDepth = this.get(\"upDepth\", 0);\n\t\tlet topDepth = this.get(\"topDepth\", 0);\n\t\tlet currentDepth = hierarchyNode.depth;\n\t\tlet maxDepth = this.getPrivate(\"maxDepth\", 1);\n\t\tlet downDepth = this._currentDownDepth;\n\n\t\tif (downDepth == null) {\n\t\t\tdownDepth = this.get(\"downDepth\", 1);\n\t\t}\n\n\t\tconst levelHeight = hs / (maxDepth + 1);\n\n\t\tif (currentDepth < topDepth) {\n\t\t\tcurrentDepth = topDepth;\n\t\t}\n\n\t\tif (currentDepth - upDepth < 0) {\n\t\t\tupDepth = currentDepth;\n\t\t}\n\n\t\tx0 = hierarchyNode.x0;\n\t\tx1 = hierarchyNode.x1;\n\n\t\tlet scaleDepth = (downDepth + upDepth + 1);\n\n\t\tif (scaleDepth > maxDepth - topDepth + 1) {\n\t\t\tscaleDepth = maxDepth - topDepth + 1;\n\t\t}\n\n\t\tlet scaleX = hs / (x1 - x0);\n\t\tlet scaleY = hs / (levelHeight * scaleDepth);\n\n\t\tlet dr = Math.max(currentDepth - upDepth, topDepth) * levelHeight * scaleY;\n\n\t\tconst easing = this.get(\"animationEasing\");\n\t\tlet duration = this.get(\"animationDuration\", 0);\n\n\t\tif (!this.inited) {\n\t\t\tduration = 0;\n\t\t}\n\n\t\tlet dx = -x0 * scaleX\n\n\t\tthis.animatePrivate({ key: \"scaleX\", to: scaleX, duration: duration, easing: easing });\n\t\tthis.animatePrivate({ key: \"scaleY\", to: scaleY, duration: duration, easing: easing });\n\t\tthis.animatePrivate({ key: \"dr\", to: dr, duration: duration, easing: easing });\n\t\tthis.animatePrivate({ key: \"dx\", to: dx, duration: duration, easing: easing });\n\t}\n\n\n\tprotected _handleSingle(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst parent = dataItem.get(\"parent\");\n\t\tif (parent) {\n\t\t\tconst children = parent.get(\"children\");\n\t\t\tif (children) {\n\t\t\t\t$array.each(children, (child) => {\n\t\t\t\t\tif (child != dataItem) {\n\t\t\t\t\t\tthis.disableDataItem(child);\n\t\t\t\t\t\tchild.get(\"node\").hide();\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t\tthis._handleSingle(parent);\n\t\t}\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\n\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\n\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\tconst arc = slice.get(\"arc\", 0);\n\t\t\tconst angle = slice.get(\"startAngle\", 0) + slice.get(\"arc\", 0) * locationX;\n\t\t\tconst innerRadius = slice.get(\"innerRadius\", 0);\n\t\t\tconst radius = innerRadius + (slice.get(\"radius\", 0) - innerRadius) * locationY;\n\n\t\t\tlet x = $math.cos(angle) * radius;\n\t\t\tlet y = $math.sin(angle) * radius;\n\n\t\t\tif ($math.round(arc, 5) === 360 && $math.round(innerRadius, 2) === 0) {\n\t\t\t\tx = 0;\n\t\t\t\ty = 0;\n\t\t\t}\n\n\t\t\tsprite.set(\"x\", x);\n\t\t\tsprite.set(\"y\", y);\n\t\t}\n\t}\n\n\tprotected _makeBullet(dataItem: DataItem<this[\"_dataItemSettings\"]>, bulletFunction: (root: Root, series: Series, dataItem: DataItem<this[\"_dataItemSettings\"]>) => Bullet | undefined, index?: number) {\n\t\tconst bullet = super._makeBullet(dataItem, bulletFunction, index);\n\n\t\tif (bullet) {\n\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\tconst slice = dataItem.get(\"slice\");\n\n\t\t\tif (sprite && slice) {\n\t\t\t\tslice.on(\"arc\", () => {\n\t\t\t\t\tthis._positionBullet(bullet);\n\t\t\t\t})\n\n\t\t\t\tslice.on(\"radius\", () => {\n\t\t\t\t\tthis._positionBullet(bullet);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\treturn bullet;\n\t\t}\n\t}\n}\n", "import {Node} from \"./hierarchy/index.js\";\n\nfunction defaultSeparation(a, b) {\n  return a.parent === b.parent ? 1 : 2;\n}\n\n// function radialSeparation(a, b) {\n//   return (a.parent === b.parent ? 1 : 2) / a.depth;\n// }\n\n// This function is used to traverse the left contour of a subtree (or\n// subforest). It returns the successor of v on this contour. This successor is\n// either given by the leftmost child of v or by the thread of v. The function\n// returns null if and only if v is on the highest level of its subtree.\nfunction nextLeft(v) {\n  var children = v.children;\n  return children ? children[0] : v.t;\n}\n\n// This function works analogously to nextLeft.\nfunction nextRight(v) {\n  var children = v.children;\n  return children ? children[children.length - 1] : v.t;\n}\n\n// Shifts the current subtree rooted at w+. This is done by increasing\n// prelim(w+) and mod(w+) by shift.\nfunction moveSubtree(wm, wp, shift) {\n  var change = shift / (wp.i - wm.i);\n  wp.c -= change;\n  wp.s += shift;\n  wm.c += change;\n  wp.z += shift;\n  wp.m += shift;\n}\n\n// All other shifts, applied to the smaller subtrees between w- and w+, are\n// performed by this function. To prepare the shifts, we have to adjust\n// change(w+), shift(w+), and change(w-).\nfunction executeShifts(v) {\n  var shift = 0,\n      change = 0,\n      children = v.children,\n      i = children.length,\n      w;\n  while (--i >= 0) {\n    w = children[i];\n    w.z += shift;\n    w.m += shift;\n    shift += w.s + (change += w.c);\n  }\n}\n\n// If vi-’s ancestor is a sibling of v, returns vi-’s ancestor. Otherwise,\n// returns the specified (default) ancestor.\nfunction nextAncestor(vim, v, ancestor) {\n  return vim.a.parent === v.parent ? vim.a : ancestor;\n}\n\nfunction TreeNode(node, i) {\n  this._ = node;\n  this.parent = null;\n  this.children = null;\n  this.A = null; // default ancestor\n  this.a = this; // ancestor\n  this.z = 0; // prelim\n  this.m = 0; // mod\n  this.c = 0; // change\n  this.s = 0; // shift\n  this.t = null; // thread\n  this.i = i; // number\n}\n\nTreeNode.prototype = Object.create(Node.prototype);\n\nfunction treeRoot(root) {\n  var tree = new TreeNode(root, 0),\n      node,\n      nodes = [tree],\n      child,\n      children,\n      i,\n      n;\n\n  while (node = nodes.pop()) {\n    if (children = node._.children) {\n      node.children = new Array(n = children.length);\n      for (i = n - 1; i >= 0; --i) {\n        nodes.push(child = node.children[i] = new TreeNode(children[i], i));\n        child.parent = node;\n      }\n    }\n  }\n\n  (tree.parent = new TreeNode(null, 0)).children = [tree];\n  return tree;\n}\n\n// Node-link tree diagram using the Reingold-Tilford \"tidy\" algorithm\nexport default function() {\n  var separation = defaultSeparation,\n      dx = 1,\n      dy = 1,\n      nodeSize = null;\n\n  function tree(root) {\n    var t = treeRoot(root);\n\n    // Compute the layout using Buchheim et al.’s algorithm.\n    t.eachAfter(firstWalk), t.parent.m = -t.z;\n    t.eachBefore(secondWalk);\n\n    // If a fixed node size is specified, scale x and y.\n    if (nodeSize) root.eachBefore(sizeNode);\n\n    // If a fixed tree size is specified, scale x and y based on the extent.\n    // Compute the left-most, right-most, and depth-most nodes for extents.\n    else {\n      var left = root,\n          right = root,\n          bottom = root;\n      root.eachBefore(function(node) {\n        if (node.x < left.x) left = node;\n        if (node.x > right.x) right = node;\n        if (node.depth > bottom.depth) bottom = node;\n      });\n      var s = left === right ? 1 : separation(left, right) / 2,\n          tx = s - left.x,\n          kx = dx / (right.x + s + tx),\n          ky = dy / (bottom.depth || 1);\n      root.eachBefore(function(node) {\n        node.x = (node.x + tx) * kx;\n        node.y = node.depth * ky;\n      });\n    }\n\n    return root;\n  }\n\n  // Computes a preliminary x-coordinate for v. Before that, FIRST WALK is\n  // applied recursively to the children of v, as well as the function\n  // APPORTION. After spacing out the children by calling EXECUTE SHIFTS, the\n  // node v is placed to the midpoint of its outermost children.\n  function firstWalk(v) {\n    var children = v.children,\n        siblings = v.parent.children,\n        w = v.i ? siblings[v.i - 1] : null;\n    if (children) {\n      executeShifts(v);\n      var midpoint = (children[0].z + children[children.length - 1].z) / 2;\n      if (w) {\n        v.z = w.z + separation(v._, w._);\n        v.m = v.z - midpoint;\n      } else {\n        v.z = midpoint;\n      }\n    } else if (w) {\n      v.z = w.z + separation(v._, w._);\n    }\n    v.parent.A = apportion(v, w, v.parent.A || siblings[0]);\n  }\n\n  // Computes all real x-coordinates by summing up the modifiers recursively.\n  function secondWalk(v) {\n    v._.x = v.z + v.parent.m;\n    v.m += v.parent.m;\n  }\n\n  // The core of the algorithm. Here, a new subtree is combined with the\n  // previous subtrees. Threads are used to traverse the inside and outside\n  // contours of the left and right subtree up to the highest common level. The\n  // vertices used for the traversals are vi+, vi-, vo-, and vo+, where the\n  // superscript o means outside and i means inside, the subscript - means left\n  // subtree and + means right subtree. For summing up the modifiers along the\n  // contour, we use respective variables si+, si-, so-, and so+. Whenever two\n  // nodes of the inside contours conflict, we compute the left one of the\n  // greatest uncommon ancestors using the function ANCESTOR and call MOVE\n  // SUBTREE to shift the subtree and prepare the shifts of smaller subtrees.\n  // Finally, we add a new thread (if necessary).\n  function apportion(v, w, ancestor) {\n    if (w) {\n      var vip = v,\n          vop = v,\n          vim = w,\n          vom = vip.parent.children[0],\n          sip = vip.m,\n          sop = vop.m,\n          sim = vim.m,\n          som = vom.m,\n          shift;\n      while (vim = nextRight(vim), vip = nextLeft(vip), vim && vip) {\n        vom = nextLeft(vom);\n        vop = nextRight(vop);\n        vop.a = v;\n        shift = vim.z + sim - vip.z - sip + separation(vim._, vip._);\n        if (shift > 0) {\n          moveSubtree(nextAncestor(vim, v, ancestor), v, shift);\n          sip += shift;\n          sop += shift;\n        }\n        sim += vim.m;\n        sip += vip.m;\n        som += vom.m;\n        sop += vop.m;\n      }\n      if (vim && !nextRight(vop)) {\n        vop.t = vim;\n        vop.m += sim - sop;\n      }\n      if (vip && !nextLeft(vom)) {\n        vom.t = vip;\n        vom.m += sip - som;\n        ancestor = v;\n      }\n    }\n    return ancestor;\n  }\n\n  function sizeNode(node) {\n    node.x *= dx;\n    node.y = node.depth * dy;\n  }\n\n  tree.separation = function(x) {\n    return arguments.length ? (separation = x, tree) : separation;\n  };\n\n  tree.size = function(x) {\n    return arguments.length ? (nodeSize = false, dx = +x[0], dy = +x[1], tree) : (nodeSize ? null : [dx, dy]);\n  };\n\n  tree.nodeSize = function(x) {\n    return arguments.length ? (nodeSize = true, dx = +x[0], dy = +x[1], tree) : (nodeSize ? [dx, dy] : null);\n  };\n\n  return tree;\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { IPoint } from \"../../core/util/IPoint\";\n\nimport { LinkedHierarchy, ILinkedHierarchyPrivate, ILinkedHierarchySettings, ILinkedHierarchyDataItem, ILinkedHierarchyEvents } from \"./LinkedHierarchy\";\n\nimport * as d3hierarchy from \"d3-hierarchy\";\n\nexport interface ITreeDataObject {\n\tname?: string,\n\tvalue?: number,\n\tchildren?: ITreeDataObject[],\n\tdataItem?: DataItem<ITreeDataItem>\n};\n\nexport interface ITreeDataItem extends ILinkedHierarchyDataItem {\n\n\t/**\n\t * An array of children data items.\n\t */\n\tchildren: Array<DataItem<ITreeDataItem>>;\n\n\t/**\n\t * Parent data item.\n\t * @type {DataItem<ITreeDataItem>}\n\t */\n\tparent: DataItem<ITreeDataItem>;\n\n}\n\nexport interface ITreeSettings extends ILinkedHierarchySettings {\n\n\t/**\n\t * Orientation of the diagram.\n\t *\n\t * @default \"vertical\"\n\t */\n\torientation?: \"horizontal\" | \"vertical\";\n\n\t/**\n\t * If set to `true`, will flip the tree direction.\n\t *\n\t * @default false\n\t * @since 5.2.4\n\t */\n\tinversed?: boolean;\n\n}\n\nexport interface ITreePrivate extends ILinkedHierarchyPrivate {\n\n\t/**\n\t * Current horizontal scale.\n\t */\n\tscaleX?: number;\n\n\t/**\n\t * Current vertical scale.\n\t */\n\tscaleY?: number;\n}\n\nexport interface ITreeEvents extends ILinkedHierarchyEvents {\n}\n\n/**\n * Displays a tree diagram.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/tree/} for more info\n * @important\n */\nexport class Tree extends LinkedHierarchy {\n\n\tdeclare public _settings: ITreeSettings;\n\tdeclare public _privateSettings: ITreePrivate;\n\tdeclare public _dataItemSettings: ITreeDataItem;\n\n\tprotected _tag: string = \"tree\";\n\n\tpublic static className: string = \"Tree\";\n\tpublic static classNames: Array<string> = LinkedHierarchy.classNames.concat([Tree.className]);\n\n\tpublic _hierarchyLayout = d3hierarchy.tree();\n\tdeclare public _rootNode: d3hierarchy.HierarchyCircularNode<ITreeDataObject> | undefined;\n\tpublic _packData: ITreeDataObject | undefined;\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"orientation\") || this.isDirty(\"inversed\")) {\n\t\t\tthis._updateVisuals();\n\t\t}\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tconst layout = this._hierarchyLayout;\n\n\t\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\t\tlayout.size([this.innerWidth(), this.innerHeight()]);\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlayout.size([this.innerHeight(), this.innerWidth()]);\n\t\t\t}\n\n\t\t\tlayout(this._rootNode);\n\t\t}\n\n\t\tsuper._updateVisuals();\n\t}\n\n\tprotected _getPoint(hierarchyNode: this[\"_dataItemSettings\"][\"d3HierarchyNode\"]): IPoint {\n\t\tconst inversed = this.get(\"inversed\");\n\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\tif (inversed) {\n\t\t\t\treturn { x: hierarchyNode.x, y: this.innerHeight() - hierarchyNode.y };\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn { x: hierarchyNode.x, y: hierarchyNode.y };\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tif (inversed) {\n\t\t\t\treturn { x: this.innerWidth() - hierarchyNode.y, y: hierarchyNode.x };\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn { x: hierarchyNode.y, y: hierarchyNode.x };\n\t\t\t}\n\t\t}\n\t}\n\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      node,\n      i = -1,\n      n = nodes.length,\n      k = parent.value && (y1 - y0) / parent.value;\n\n  while (++i < n) {\n    node = nodes[i], node.x0 = x0, node.x1 = x1;\n    node.y0 = y0, node.y1 = y0 += node.value * k;\n  }\n}\n", "import treemapDice from \"./dice.js\";\nimport treemapSlice from \"./slice.js\";\n\nexport var phi = (1 + Math.sqrt(5)) / 2;\n\nexport function squarifyRatio(ratio, parent, x0, y0, x1, y1) {\n  var rows = [],\n      nodes = parent.children,\n      row,\n      nodeValue,\n      i0 = 0,\n      i1 = 0,\n      n = nodes.length,\n      dx, dy,\n      value = parent.value,\n      sumValue,\n      minValue,\n      maxValue,\n      newRatio,\n      minRatio,\n      alpha,\n      beta;\n\n  while (i0 < n) {\n    dx = x1 - x0, dy = y1 - y0;\n\n    // Find the next non-empty node.\n    do sumValue = nodes[i1++].value; while (!sumValue && i1 < n);\n    minValue = maxValue = sumValue;\n    alpha = Math.max(dy / dx, dx / dy) / (value * ratio);\n    beta = sumValue * sumValue * alpha;\n    minRatio = Math.max(maxValue / beta, beta / minValue);\n\n    // Keep adding nodes while the aspect ratio maintains or improves.\n    for (; i1 < n; ++i1) {\n      sumValue += nodeValue = nodes[i1].value;\n      if (nodeValue < minValue) minValue = nodeValue;\n      if (nodeValue > maxValue) maxValue = nodeValue;\n      beta = sumValue * sumValue * alpha;\n      newRatio = Math.max(maxValue / beta, beta / minValue);\n      if (newRatio > minRatio) { sumValue -= nodeValue; break; }\n      minRatio = newRatio;\n    }\n\n    // Position and record the row orientation.\n    rows.push(row = {value: sumValue, dice: dx < dy, children: nodes.slice(i0, i1)});\n    if (row.dice) treemapDice(row, x0, y0, x1, value ? y0 += dy * sumValue / value : y1);\n    else treemapSlice(row, x0, y0, value ? x0 += dx * sumValue / value : x1, y1);\n    value -= sumValue, i0 = i1;\n  }\n\n  return rows;\n}\n\nexport default (function custom(ratio) {\n\n  function squarify(parent, x0, y0, x1, y1) {\n    squarifyRatio(ratio, parent, x0, y0, x1, y1);\n  }\n\n  squarify.ratio = function(x) {\n    return custom((x = +x) > 1 ? x : 1);\n  };\n\n  return squarify;\n})(phi);\n", "import roundNode from \"./round.js\";\nimport squarify from \"./squarify.js\";\nimport {required} from \"../accessors.js\";\nimport constant, {constantZero} from \"../constant.js\";\n\nexport default function() {\n  var tile = squarify,\n      round = false,\n      dx = 1,\n      dy = 1,\n      paddingStack = [0],\n      paddingInner = constantZero,\n      paddingTop = constantZero,\n      paddingRight = constantZero,\n      paddingBottom = constantZero,\n      paddingLeft = constantZero;\n\n  function treemap(root) {\n    root.x0 =\n    root.y0 = 0;\n    root.x1 = dx;\n    root.y1 = dy;\n    root.eachBefore(positionNode);\n    paddingStack = [0];\n    if (round) root.eachBefore(roundNode);\n    return root;\n  }\n\n  function positionNode(node) {\n    var p = paddingStack[node.depth],\n        x0 = node.x0 + p,\n        y0 = node.y0 + p,\n        x1 = node.x1 - p,\n        y1 = node.y1 - p;\n    if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n    if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n    node.x0 = x0;\n    node.y0 = y0;\n    node.x1 = x1;\n    node.y1 = y1;\n    if (node.children) {\n      p = paddingStack[node.depth + 1] = paddingInner(node) / 2;\n      x0 += paddingLeft(node) - p;\n      y0 += paddingTop(node) - p;\n      x1 -= paddingRight(node) - p;\n      y1 -= paddingBottom(node) - p;\n      if (x1 < x0) x0 = x1 = (x0 + x1) / 2;\n      if (y1 < y0) y0 = y1 = (y0 + y1) / 2;\n      tile(node, x0, y0, x1, y1);\n    }\n  }\n\n  treemap.round = function(x) {\n    return arguments.length ? (round = !!x, treemap) : round;\n  };\n\n  treemap.size = function(x) {\n    return arguments.length ? (dx = +x[0], dy = +x[1], treemap) : [dx, dy];\n  };\n\n  treemap.tile = function(x) {\n    return arguments.length ? (tile = required(x), treemap) : tile;\n  };\n\n  treemap.padding = function(x) {\n    return arguments.length ? treemap.paddingInner(x).paddingOuter(x) : treemap.paddingInner();\n  };\n\n  treemap.paddingInner = function(x) {\n    return arguments.length ? (paddingInner = typeof x === \"function\" ? x : constant(+x), treemap) : paddingInner;\n  };\n\n  treemap.paddingOuter = function(x) {\n    return arguments.length ? treemap.paddingTop(x).paddingRight(x).paddingBottom(x).paddingLeft(x) : treemap.paddingTop();\n  };\n\n  treemap.paddingTop = function(x) {\n    return arguments.length ? (paddingTop = typeof x === \"function\" ? x : constant(+x), treemap) : paddingTop;\n  };\n\n  treemap.paddingRight = function(x) {\n    return arguments.length ? (paddingRight = typeof x === \"function\" ? x : constant(+x), treemap) : paddingRight;\n  };\n\n  treemap.paddingBottom = function(x) {\n    return arguments.length ? (paddingBottom = typeof x === \"function\" ? x : constant(+x), treemap) : paddingBottom;\n  };\n\n  treemap.paddingLeft = function(x) {\n    return arguments.length ? (paddingLeft = typeof x === \"function\" ? x : constant(+x), treemap) : paddingLeft;\n  };\n\n  return treemap;\n}\n", "export default function(parent, x0, y0, x1, y1) {\n  var nodes = parent.children,\n      i, n = nodes.length,\n      sum, sums = new Array(n + 1);\n\n  for (sums[0] = sum = i = 0; i < n; ++i) {\n    sums[i + 1] = sum += nodes[i].value;\n  }\n\n  partition(0, n, parent.value, x0, y0, x1, y1);\n\n  function partition(i, j, value, x0, y0, x1, y1) {\n    if (i >= j - 1) {\n      var node = nodes[i];\n      node.x0 = x0, node.y0 = y0;\n      node.x1 = x1, node.y1 = y1;\n      return;\n    }\n\n    var valueOffset = sums[i],\n        valueTarget = (value / 2) + valueOffset,\n        k = i + 1,\n        hi = j - 1;\n\n    while (k < hi) {\n      var mid = k + hi >>> 1;\n      if (sums[mid] < valueTarget) k = mid + 1;\n      else hi = mid;\n    }\n\n    if ((valueTarget - sums[k - 1]) < (sums[k] - valueTarget) && i + 1 < k) --k;\n\n    var valueLeft = sums[k] - valueOffset,\n        valueRight = value - valueLeft;\n\n    if ((x1 - x0) > (y1 - y0)) {\n      var xk = value ? (x0 * valueRight + x1 * valueLeft) / value : x1;\n      partition(i, k, valueLeft, x0, y0, xk, y1);\n      partition(k, j, valueRight, xk, y0, x1, y1);\n    } else {\n      var yk = value ? (y0 * valueRight + y1 * valueLeft) / value : y1;\n      partition(i, k, valueLeft, x0, y0, x1, yk);\n      partition(k, j, valueRight, x0, yk, x1, y1);\n    }\n  }\n}\n", "import dice from \"./dice.js\";\nimport slice from \"./slice.js\";\n\nexport default function(parent, x0, y0, x1, y1) {\n  (parent.depth & 1 ? slice : dice)(parent, x0, y0, x1, y1);\n}\n", "import type { HierarchyNode } from \"./HierarchyNode\";\nimport type { DataItem } from \"../../core/render/Component\";\n\nimport { Hierarchy, IHierarchyPrivate, IHierarchySettings, IHierarchyDataItem, IHierarchyDataObject } from \"./Hierarchy\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { RoundedRectangle } from \"../../core/render/RoundedRectangle\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as d3hierarchy from \"d3-hierarchy\";\n\nexport interface ITreemapDataObject { name?: string, value?: number, children?: ITreemapDataObject[], dataItem?: DataItem<ITreemapDataItem> };\n\nexport interface ITreemapDataItem extends IHierarchyDataItem {\n\tchildren: Array<DataItem<ITreemapDataItem>>;\n\tparent: DataItem<ITreemapDataItem>;\n\td3HierarchyNode: d3hierarchy.HierarchyRectangularNode<IHierarchyDataObject>;\n\trectangle: RoundedRectangle;\n}\n\nexport interface ITreemapSettings extends IHierarchySettings {\n\n\t/**\n\t * Gap between nodes. In pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingInner?: number;\n\n\t/**\n\t * Gap between nodes and outer edge of the chart. In pixels.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingOuter?: number;\n\n\t/**\n\t * Gap between nodes and top edge.\n\t *\n\t * Will be ignored if `nodePaddingOuter` is set.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingTop?: number;\n\n\t/**\n\t * Gap between nodes and bottomedge.\n\t *\n\t * Will be ignored if `nodePaddingOuter` is set.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingBottom?: number;\n\n\t/**\n\t * Gap between nodes and left edge.\n\t *\n\t * Will be ignored if `nodePaddingOuter` is set.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingLeft?: number;\n\n\t/**\n\t * Gap between nodes and bottom edge.\n\t *\n\t * Will be ignored if `nodePaddingOuter` is set.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/#Margins}\n\t */\n\tnodePaddingRight?: number;\n\n\t/**\n\t * An algorithm to use when laying out node rectangles.\n\t *\n\t * @see {@link }\n\t * @default \"squarify\"\n\t */\n\tlayoutAlgorithm?: \"binary\" | \"squarify\" | \"slice\" | \"dice\" | \"sliceDice\";\n\n}\n\nexport interface ITreemapPrivate extends IHierarchyPrivate {\n\n\t/**\n\t * Current horizontal scale.\n\t */\n\tscaleX?: number;\n\n\t/**\n\t * Current vertical scale.\n\t */\n\tscaleY?: number;\n\n}\n\n/**\n * Treemap series.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/treemap/} for more info\n */\nexport class Treemap extends Hierarchy {\n\n\tdeclare public _settings: ITreemapSettings;\n\tdeclare public _privateSettings: ITreemapPrivate;\n\tdeclare public _dataItemSettings: ITreemapDataItem;\n\n\tprotected _tag: string = \"treemap\";\n\n\tpublic static className: string = \"Treemap\";\n\tpublic static classNames: Array<string> = Hierarchy.classNames.concat([Treemap.className]);\n\n\tpublic readonly rectangleTemplate: Template<RoundedRectangle> = Template.new({});\n\n\tpublic _treemapLayout = d3hierarchy.treemap().tile(d3hierarchy.treemapSquarify);\n\n\tdeclare public _rootNode: d3hierarchy.HierarchyRectangularNode<ITreemapDataObject> | undefined;\n\n\t/**\n\t * A list of node rectangle elements in a [[Treemap]] chart.\n\t *\n\t * @default new ListTemplate<RoundedRectangle>\n\t */\n\tpublic readonly rectangles: ListTemplate<RoundedRectangle> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => RoundedRectangle._new(this._root, {\n\t\t\tthemeTags: $utils.mergeTags(this.rectangles.template.get(\"themeTags\", []), [this._tag, \"shape\"])\n\t\t}, [this.rectangles.template])\n\t);\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.setPrivate(\"scaleX\", 1);\n\t\tthis.setPrivate(\"scaleY\", 1);\n\n\t\tthis.nodes.template.setPrivate(\"trustBounds\", true);\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"layoutAlgorithm\")) {\n\t\t\tlet algorithm;\n\n\t\t\tswitch (this.get(\"layoutAlgorithm\")) {\n\t\t\t\tcase \"squarify\":\n\t\t\t\t\talgorithm = d3hierarchy.treemapSquarify;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"binary\":\n\t\t\t\t\talgorithm = d3hierarchy.treemapBinary;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"slice\":\n\t\t\t\t\talgorithm = d3hierarchy.treemapSlice;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"dice\":\n\t\t\t\t\talgorithm = d3hierarchy.treemapDice;\n\t\t\t\t\tbreak;\n\t\t\t\tcase \"sliceDice\":\n\t\t\t\t\talgorithm = d3hierarchy.treemapSliceDice;\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t\tif (algorithm) {\n\t\t\t\tthis._treemapLayout = d3hierarchy.treemap().tile(algorithm);\n\t\t\t\tthis._updateVisuals();\n\n\t\t\t\tconst selectedDataItem = this.get(\"selectedDataItem\") as DataItem<this[\"_dataItemSettings\"]>;\t\t\t\t\n\t\t\t\tif(selectedDataItem){\n\t\t\t\t\tthis._zoom(selectedDataItem);\n\t\t\t\t}\t\t\t\t\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"nodePaddingRight\") || this.isDirty(\"nodePaddingLeft\") || this.isDirty(\"nodePaddingTop\") || this.isDirty(\"nodePaddingBottom\") || this.isDirty(\"nodePaddingOuter\") || this.isDirty(\"nodePaddingInner\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodes(this._rootNode);\n\t\t\t}\n\t\t}\n\n\t\tif (this.isPrivateDirty(\"scaleX\") || this.isPrivateDirty(\"scaleY\")) {\n\t\t\tif (this._rootNode) {\n\t\t\t\tthis._updateNodesScale(this._rootNode);\n\t\t\t}\n\t\t}\t\t\n\t}\n\n\tprotected _updateVisuals() {\n\t\tif (this._rootNode) {\n\t\t\tconst treemapLayout = this._treemapLayout;\n\t\t\ttreemapLayout.size([this.innerWidth(), this.innerHeight()]);\n\n\t\t\tconst paddingLeft = this.get(\"nodePaddingLeft\");\n\t\t\tconst paddingRight = this.get(\"nodePaddingRight\");\n\t\t\tconst paddingTop = this.get(\"nodePaddingTop\");\n\t\t\tconst paddingBottom = this.get(\"nodePaddingBottom\");\n\t\t\tconst paddingInner = this.get(\"nodePaddingInner\");\n\t\t\tconst paddingOuter = this.get(\"nodePaddingOuter\");\n\t\t\tif ($type.isNumber(paddingLeft)) {\n\t\t\t\ttreemapLayout.paddingLeft(paddingLeft);\n\t\t\t}\n\t\t\tif ($type.isNumber(paddingRight)) {\n\t\t\t\ttreemapLayout.paddingRight(paddingRight);\n\t\t\t}\n\t\t\tif ($type.isNumber(paddingTop)) {\n\t\t\t\ttreemapLayout.paddingTop(paddingTop);\n\t\t\t}\n\t\t\tif ($type.isNumber(paddingBottom)) {\n\t\t\t\ttreemapLayout.paddingBottom(paddingBottom);\n\t\t\t}\n\t\t\tif ($type.isNumber(paddingInner)) {\n\t\t\t\ttreemapLayout.paddingInner(paddingInner);\n\t\t\t}\n\t\t\tif ($type.isNumber(paddingOuter)) {\n\t\t\t\ttreemapLayout.paddingOuter(paddingOuter);\n\t\t\t}\n\n\t\t\ttreemapLayout(this._rootNode);\n\t\t\tthis._updateNodes(this._rootNode);\n\t\t}\n\t}\n\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._updateNode(dataItem);\n\n\t\tconst node = dataItem.get(\"node\");\n\t\tconst rectangle = dataItem.get(\"rectangle\");\n\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\n\t\tconst x0 = hierarchyNode.x0 * scaleX;\n\t\tconst x1 = hierarchyNode.x1 * scaleX;\n\t\tconst y0 = hierarchyNode.y0 * scaleY;\n\t\tconst y1 = hierarchyNode.y1 * scaleY;\n\n\t\tconst w = x1 - x0;\n\t\tconst h = y1 - y0;\n\n\t\tconst duration = this.get(\"animationDuration\", 0);\n\t\tconst easing = this.get(\"animationEasing\");\n\n\t\tnode.animate({ key: \"x\", to: x0, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"y\", to: y0, duration: duration, easing: easing })\n\n\t\tnode.animate({ key: \"width\", to: w, duration: duration, easing: easing })\n\t\tnode.animate({ key: \"height\", to: h, duration: duration, easing: easing })\n\n\t\tif (rectangle) {\n\t\t\tconst fill = dataItem.get(\"fill\");\n\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\n\t\t\trectangle.animate({ key: \"width\", to: w, duration: duration, easing: easing })\n\t\t\trectangle.animate({ key: \"height\", to: h, duration: duration, easing: easing })\n\t\t\trectangle._setDefault(\"fill\", fill);\n\t\t\trectangle._setDefault(\"fillPattern\", fillPattern);\n\t\t\trectangle._setDefault(\"stroke\", fill);\n\t\t}\n\t}\n\n\n\tprotected _updateNodesScale(hierarchyNode: d3hierarchy.HierarchyRectangularNode<ITreemapDataObject>) {\n\t\tconst dataItem = hierarchyNode.data.dataItem;\n\t\tif (dataItem) {\n\t\t\tconst node = dataItem.get(\"node\");\n\t\t\tconst rectangle = dataItem.get(\"rectangle\");\n\n\t\t\tconst scaleX = this.getPrivate(\"scaleX\", 1);\n\t\t\tconst scaleY = this.getPrivate(\"scaleY\", 1);\n\n\t\t\tconst x0 = hierarchyNode.x0 * scaleX;\n\t\t\tconst x1 = hierarchyNode.x1 * scaleX;\n\t\t\tconst y0 = hierarchyNode.y0 * scaleY;\n\t\t\tconst y1 = hierarchyNode.y1 * scaleY;\n\n\t\t\tconst w = x1 - x0;\n\t\t\tconst h = y1 - y0;\n\n\t\t\tnode.setAll({ x: x0, y: y0, width: w, height: h });\n\t\t\trectangle.setAll({ width: w, height: h });\n\n\t\t\tconst hierarchyChildren = hierarchyNode.children;\n\t\t\tif (hierarchyChildren) {\n\t\t\t\t$array.each(hierarchyChildren, (hierarchyChild) => {\n\t\t\t\t\tthis._updateNodesScale(hierarchyChild)\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): HierarchyNode {\n\t\tconst node = super.makeNode(dataItem);\n\n\t\tconst rectangle = node.children.moveValue(this.rectangles.make(), 0);\n\n\t\tnode.setPrivate(\"tooltipTarget\", rectangle);\n\t\tdataItem.setRaw(\"rectangle\", rectangle);\n\n\t\tconst label = dataItem.get(\"label\");\n\n\t\trectangle.on(\"width\", () => {\n\t\t\tlabel.setPrivate(\"maxWidth\", rectangle.width());\n\t\t})\n\n\t\trectangle.on(\"height\", () => {\n\t\t\tlabel.setPrivate(\"maxHeight\", rectangle.height());\n\t\t})\n\n\t\treturn node;\n\t}\n\n\tpublic _zoom(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tif (this.width() > 0 && this.height() > 0) {\n\t\t\tconst hierarchyNode = dataItem.get(\"d3HierarchyNode\");\n\n\t\t\tconst nodePaddingOuter = this.get(\"nodePaddingOuter\", 0);\n\n\t\t\tlet x0 = hierarchyNode.x0 + nodePaddingOuter;\n\t\t\tlet x1 = hierarchyNode.x1 - nodePaddingOuter;\n\n\t\t\tlet y0 = hierarchyNode.y0 + nodePaddingOuter;\n\t\t\tlet y1 = hierarchyNode.y1 - nodePaddingOuter;\n\n\t\t\tlet scaleX = (this.innerWidth() - nodePaddingOuter * 2) / (x1 - x0);\n\t\t\tlet scaleY = (this.innerHeight() - nodePaddingOuter * 2) / (y1 - y0);\n\n\t\t\tconst easing = this.get(\"animationEasing\");\n\t\t\tlet duration = this.get(\"animationDuration\", 0);\n\n\t\t\tif (!this.inited) {\n\t\t\t\tduration = 0;\n\t\t\t}\t\t\t\n\t\t\t\n\t\t\tthis.animatePrivate({ key: \"scaleX\", to: scaleX, duration: duration, easing: easing });\n\t\t\tthis.animatePrivate({ key: \"scaleY\", to: scaleY, duration: duration, easing: easing });\n\n\t\t\tthis.nodesContainer.animate({ key: \"x\", to: nodePaddingOuter - x0 * scaleX, duration: duration, easing: easing });\n\t\t\tthis.nodesContainer.animate({ key: \"y\", to: nodePaddingOuter - y0 * scaleY, duration: duration, easing: easing });\n\t\t}\n\t}\n\n\tprotected _selectDataItem(dataItem?: DataItem<this[\"_dataItemSettings\"]>, downDepth?: number, skipDisptach?: boolean) {\n\t\tsuper._selectDataItem(dataItem, downDepth, skipDisptach);\n\n\t\tif (dataItem) {\n\t\t\tlet maxDepth = this.get(\"downDepth\", 1) + dataItem.get(\"depth\");\n\t\t\tif (!this.inited) {\n\t\t\t\tmaxDepth = this.get(\"initialDepth\", 1);\n\t\t\t}\n\t\t\tconst visibleNodes = this._getVisibleNodes(dataItem, maxDepth);\n\t\t\tthis.nodes.each((node) => {\n\t\t\t\tif (visibleNodes.indexOf(node.dataItem as DataItem<this[\"_dataItemSettings\"]>) == -1) {\n\t\t\t\t\tnode.setPrivate(\"focusable\", false);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tnode.removePrivate(\"focusable\");\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\tthis._root._invalidateTabindexes();\n\t}\n\n\tprotected _getVisibleNodes(dataItem: DataItem<this[\"_dataItemSettings\"]>, maxDepth: number) {\n\t\tconst children = dataItem.get(\"children\");\n\t\tlet includedChildren: Array<DataItem<this[\"_dataItemSettings\"]>> = [];\n\t\tif(children){\n\t\t\t$array.each(children, (child) => {\n\t\t\t\tif (child.get(\"depth\") == maxDepth || !child.get(\"children\")) {\n\t\t\t\t\tincludedChildren.push(child);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tincludedChildren = includedChildren.concat(this._getVisibleNodes(child, maxDepth));\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\t\treturn includedChildren;\n\t}\n\n}\n", "import { voronoiTreemap } from 'd3-voronoi-treemap';\r\nimport seedrandom from \"seedrandom\";\r\n\r\nimport type { HierarchyNode } from \"../hierarchy/HierarchyNode\";\r\n\r\nimport type { DataItem } from \"../../core/render/Component\";\r\nimport { Hierarchy, IHierarchyPrivate, IHierarchySettings, IHierarchyDataItem } from \"../hierarchy/Hierarchy\";\r\nimport { Template } from \"../../core/util/Template\";\r\nimport { ListTemplate } from \"../../core/util/List\";\r\nimport { Polygon } from \"../../core/render/Polygon\";\r\nimport * as $utils from \"../../core/util/Utils\";\r\nimport * as $array from \"../../core/util/Array\";\r\nimport { p50 } from \"../../core/util/Percent\";\r\n\r\n\r\nexport interface IVoronoiTreemapDataObject {\r\n\tname?: string,\r\n\tvalue?: number,\r\n\tchildren?: IVoronoiTreemapDataObject[],\r\n\tdataItem?: DataItem<IVoronoiTreemapDataItem>\r\n};\r\n\r\nexport interface IVoronoiTreemapDataItem extends IHierarchyDataItem {\r\n\t/**\r\n\t * Data items of child nodes.\r\n\t */\r\n\tchildren: Array<DataItem<IVoronoiTreemapDataItem>>;\r\n\r\n\t/**\r\n\t * Data it of a parent node.\r\n\t */\r\n\tparent: DataItem<IVoronoiTreemapDataItem>;\r\n\r\n\t/**\r\n\t * A [[Polygon]] element of a node.\r\n\t */\r\n\tpolygon: Polygon;\r\n\r\n}\r\n\r\nexport interface IVoronoiTreemapSettings extends IHierarchySettings {\r\n\r\n\t/**\r\n\t * Type of the diagram's shape.\r\n\t *\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/voronoi-treemap/#Diagram_type} for more info\r\n\t * @default \"polygon\"\r\n\t */\r\n\ttype?: \"rectangle\" | \"polygon\"\r\n\r\n\t/**\r\n\t * Number of corners when type is `\"polygon\"`.\r\n\t * \r\n\t * `120` means the polygoon will look like a circle.\r\n\t *\r\n\t * NOTE: this setting is ignored if `type=\"rectangle\"`.\r\n\t * \r\n\t * @default 120\r\n\t */\r\n\tcornerCount?: number;\r\n\r\n\t/**\r\n\t * Minimum weight ratio which allows computing the minimum allowed\r\n\t * weight (`= [maximum weight] * minWeightRatio`).\r\n\t * \r\n\t * Setting very small `minWeigtRatio` might result flickering.\r\n\t *\r\n\t * NOTE: the nodes that have smaller weight will be scaled up and will not\r\n\t * represent their true value correctly.\r\n\t * \r\n\t * @default 0.005\r\n\t */\r\n\tminWeightRatio?: number;\r\n\r\n\t/**\r\n\t * The convergence ratio in Voronoi treemaps measures how well the treemap\r\n\t * layout represents the hierarchical structure of the underlying data.\r\n\t * \r\n\t * It is calculated as the ratio of the summed area of the smallest enclosing\r\n\t * rectangle for each cell to the total area of the treemap. A lower\r\n\t * convergence ratio indicates a better representation of the hierarchy,\r\n\t * meaning that the cells are closely packed and the treemap effectively\r\n\t * captures the nested relationships between the data elements.\r\n\t * \r\n\t * @default 0.005\r\n\t */\r\n\tconvergenceRatio?: number;\r\n\r\n\t/**\r\n\t * Maximum allowed number of iterations when computing the layout.\r\n\t * \r\n\t * Computation is stopped when it number of iterations is reached, even if\r\n\t * the `convergenceRatio` is not yet reached.\r\n\t *\r\n\t * Bigger number means finer results, but slower performance.\r\n\t * \r\n\t * @default 100\r\n\t */\r\n\tmaxIterationCount?: number;\r\n\r\n}\r\n\r\nexport interface IVoronoiTreemapPrivate extends IHierarchyPrivate {\r\n}\r\n\r\n/**\r\n * A Weighted Voronoi Treemap series.\r\n * \r\n * NOTE: Try to avoid a big number of data items with very big value\r\n * differences. Better group small items into \"Other\" item.\r\n *\r\n * @see {@link https://www.amcharts.com/docs/v5/charts/hierarchy/voronoi-treemap/} for more info\r\n * @since 5.4.0\r\n */\r\nexport class VoronoiTreemap extends Hierarchy {\r\n\r\n\tdeclare public _settings: IVoronoiTreemapSettings;\r\n\tdeclare public _privateSettings: IVoronoiTreemapPrivate;\r\n\tdeclare public _dataItemSettings: IVoronoiTreemapDataItem;\r\n\r\n\tprotected _tag: string = \"voronoitreemap\";\r\n\r\n\tpublic static className: string = \"VoronoiTreemap\";\r\n\tpublic static classNames: Array<string> = Hierarchy.classNames.concat([VoronoiTreemap.className]);\r\n\r\n\t/**\r\n\t * A list of node graphics elements in a [[VoronoiTreemap]] chart.\r\n\t *\r\n\t * @default new ListTemplate<RoundedRectangle>\r\n\t */\r\n\tpublic readonly polygons: ListTemplate<Polygon> = new ListTemplate(\r\n\t\tTemplate.new({}),\r\n\t\t() => Polygon._new(this._root, {\r\n\t\t\tthemeTags: $utils.mergeTags(this.polygons.template.get(\"themeTags\", []), [this._tag, \"shape\"])\r\n\t\t}, [this.polygons.template])\r\n\t);\r\n\r\n\tpublic voronoi = voronoiTreemap();\r\n\r\n\tprotected _afterNew() {\r\n\r\n\t\tthis.nodesContainer.setAll({\r\n\t\t\tx: p50,\r\n\t\t\ty: p50,\r\n\t\t\tcenterX: p50,\r\n\t\t\tcenterY: p50\r\n\t\t})\r\n\r\n\t\tthis.nodes.template.setPrivate(\"trustBounds\", true);\r\n\r\n\t\tsuper._afterNew();\r\n\t}\r\n\r\n\tpublic _prepareChildren() {\r\n\t\tsuper._prepareChildren();\r\n\r\n\t\tconst width = this.innerWidth() / 2;\r\n\t\tconst height = this.innerHeight() / 2;\r\n\r\n\t\tlet node = this._rootNode;\r\n\t\tconst selectedDataItem = this.get(\"selectedDataItem\") as DataItem<IVoronoiTreemapDataItem>;\r\n\r\n\t\tif (selectedDataItem) {\r\n\t\t\tnode = selectedDataItem.get(\"d3HierarchyNode\")\r\n\t\t}\r\n\r\n\t\tthis.voronoi.convergenceRatio((this.get(\"convergenceRatio\", 0.005)));\r\n\t\tthis.voronoi.maxIterationCount((this.get(\"maxIterationCount\", 100)));\r\n\t\tthis.voronoi.minWeightRatio((this.get(\"minWeightRatio\", 0.005)));\r\n\r\n\t\tif (this.isDirty(\"type\")) {\r\n\t\t\tif (this.get(\"type\") == \"polygon\") {\r\n\t\t\t\tthis.voronoi.clip(this.getCirclePolygon(1));\r\n\t\t\t\tthis._updateVisuals();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif (this._sizeDirty) {\r\n\t\t\tif (this.get(\"type\") == \"rectangle\") {\r\n\t\t\t\tthis.voronoi.prng(seedrandom(\"X\"));\r\n\t\t\t\tthis.voronoi.clip([[-width, -height], [-width, height], [width, height], [width, -height]])(node);\r\n\t\t\t\tthis._updateVisuals();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tif ((this._valuesDirty || this.isDirty(\"selectedDataItem\")) && node) {\r\n\t\t\tthis.voronoi.prng(seedrandom(\"X\"));\r\n\t\t\tthis.voronoi(node);\r\n\t\t\tthis._updateVisuals();\r\n\t\t}\r\n\t}\r\n\r\n\tprotected _updateNode(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\r\n\t\tconst coords: any = (dataItem.get(\"d3HierarchyNode\") as any).polygon;\r\n\t\tconst polygon = dataItem.get(\"polygon\");\r\n\r\n\t\tif (coords && polygon) {\r\n\r\n\t\t\tlet coordinates: any = [];\r\n\r\n\t\t\tlet d = 1;\r\n\t\t\tif (this.get(\"type\") == \"polygon\") {\r\n\t\t\t\td = Math.min(this.innerWidth(), this.innerHeight()) / 2;\r\n\t\t\t}\r\n\r\n\t\t\tlet minX = Infinity;\r\n\t\t\tlet maxX = -Infinity;\r\n\r\n\t\t\tfor (let i = 0, len = coords.length; i < len; i++) {\r\n\t\t\t\tconst point: Array<number> = coords[i] as any;\r\n\t\t\t\tlet x = point[0] * d;\r\n\t\t\t\tlet y = point[1] * d;\r\n\r\n\t\t\t\tcoordinates.push([x, y]);\r\n\r\n\t\t\t\tminX = Math.min(minX, x);\r\n\t\t\t\tmaxX = Math.max(maxX, x);\r\n\t\t\t}\r\n\r\n\t\t\tpolygon.set(\"coordinates\", coordinates);\r\n\r\n\t\t\tconst fill = dataItem.get(\"fill\");\r\n\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\r\n\t\t\t\r\n\t\t\tpolygon._setDefault(\"fill\", fill);\r\n\t\t\tpolygon._setDefault(\"fillPattern\", fillPattern);\r\n\r\n\t\t\tconst label = dataItem.get(\"label\");\r\n\t\t\tif (label) {\r\n\t\t\t\tconst site = coords.site;\r\n\r\n\t\t\t\tif (site) {\r\n\t\t\t\t\tlabel.setAll({\r\n\t\t\t\t\t\tx: site.x * d,\r\n\t\t\t\t\t\ty: site.y * d,\r\n\t\t\t\t\t\tmaxWidth: Math.abs(maxX - minX)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\r\n\tprotected _handleSingle(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\r\n\t\tconst parent = dataItem.get(\"parent\");\r\n\t\tif (parent) {\r\n\t\t\tconst children = parent.get(\"children\");\r\n\t\t\tif (children) {\r\n\t\t\t\t$array.each(children, (child) => {\r\n\t\t\t\t\tif (child != dataItem) {\r\n\t\t\t\t\t\tthis.disableDataItem(child);\r\n\t\t\t\t\t\tchild.get(\"node\").hide();\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis._handleSingle(parent);\r\n\t\t}\r\n\t}\r\n\r\n\t/**\r\n\t * @ignore\r\n\t */\r\n\tpublic makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>): HierarchyNode {\r\n\t\tconst node = super.makeNode(dataItem);\r\n\t\tthis._makeNode(dataItem, node);\r\n\t\treturn node;\r\n\t}\r\n\r\n\tprotected _makeNode(dataItem: DataItem<this[\"_dataItemSettings\"]>, node: HierarchyNode) {\r\n\t\tconst polygon = node.children.moveValue(this.polygons.make(), 0);\r\n\t\tnode.setPrivate(\"tooltipTarget\", polygon);\r\n\t\tdataItem.setRaw(\"polygon\", polygon);\r\n\t\tpolygon._setDataItem(dataItem);\r\n\t}\r\n\r\n\tprotected getCirclePolygon(radius: number) {\r\n\t\tconst points = this.get(\"cornerCount\", 120);\r\n\t\tconst dAngle = Math.PI * 2 / points;\r\n\t\tconst polygon: Array<Array<number>> = [];\r\n\r\n\t\tfor (let i = 0; i < points; i++) {\r\n\t\t\tlet angle = i * dAngle;\r\n\t\t\tpolygon.push([radius * Math.cos(angle), radius * Math.sin(angle)])\r\n\t\t}\r\n\r\n\t\treturn polygon;\r\n\t}\r\n}", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      a,\n      b = polygon[n - 1],\n      area = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    area += a[1] * b[0] - a[0] * b[1];\n  }\n\n  return area / 2;\n}\n", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      x = 0,\n      y = 0,\n      a,\n      b = polygon[n - 1],\n      c,\n      k = 0;\n\n  while (++i < n) {\n    a = b;\n    b = polygon[i];\n    k += c = a[0] * b[1] - b[0] * a[1];\n    x += (a[0] + b[0]) * c;\n    y += (a[1] + b[1]) * c;\n  }\n\n  return k *= 3, [x / k, y / k];\n}\n", "import cross from \"./cross.js\";\n\nfunction lexicographicOrder(a, b) {\n  return a[0] - b[0] || a[1] - b[1];\n}\n\n// Computes the upper convex hull per the monotone chain algorithm.\n// Assumes points.length >= 3, is sorted by x, unique in y.\n// Returns an array of indices into points in left-to-right order.\nfunction computeUpperHullIndexes(points) {\n  const n = points.length,\n      indexes = [0, 1];\n  let size = 2, i;\n\n  for (i = 2; i < n; ++i) {\n    while (size > 1 && cross(points[indexes[size - 2]], points[indexes[size - 1]], points[i]) <= 0) --size;\n    indexes[size++] = i;\n  }\n\n  return indexes.slice(0, size); // remove popped points\n}\n\nexport default function(points) {\n  if ((n = points.length) < 3) return null;\n\n  var i,\n      n,\n      sortedPoints = new Array(n),\n      flippedPoints = new Array(n);\n\n  for (i = 0; i < n; ++i) sortedPoints[i] = [+points[i][0], +points[i][1], i];\n  sortedPoints.sort(lexicographicOrder);\n  for (i = 0; i < n; ++i) flippedPoints[i] = [sortedPoints[i][0], -sortedPoints[i][1]];\n\n  var upperIndexes = computeUpperHullIndexes(sortedPoints),\n      lowerIndexes = computeUpperHullIndexes(flippedPoints);\n\n  // Construct the hull polygon, removing possible duplicate endpoints.\n  var skipLeft = lowerIndexes[0] === upperIndexes[0],\n      skipRight = lowerIndexes[lowerIndexes.length - 1] === upperIndexes[upperIndexes.length - 1],\n      hull = [];\n\n  // Add upper hull in right-to-l order.\n  // Then add lower hull in left-to-right order.\n  for (i = upperIndexes.length - 1; i >= 0; --i) hull.push(points[sortedPoints[upperIndexes[i]][2]]);\n  for (i = +skipLeft; i < lowerIndexes.length - skipRight; ++i) hull.push(points[sortedPoints[lowerIndexes[i]][2]]);\n\n  return hull;\n}\n", "// Returns the 2D cross product of AB and AC vectors, i.e., the z-component of\n// the 3D cross product in a quadrant I Cartesian coordinate system (+x is\n// right, +y is up). Returns a positive value if ABC is counter-clockwise,\n// negative if clockwise, and zero if the points are collinear.\nexport default function(a, b, c) {\n  return (b[0] - a[0]) * (c[1] - a[1]) - (b[1] - a[1]) * (c[0] - a[0]);\n}\n", "export default function(polygon, point) {\n  var n = polygon.length,\n      p = polygon[n - 1],\n      x = point[0], y = point[1],\n      x0 = p[0], y0 = p[1],\n      x1, y1,\n      inside = false;\n\n  for (var i = 0; i < n; ++i) {\n    p = polygon[i], x1 = p[0], y1 = p[1];\n    if (((y1 > y) !== (y0 > y)) && (x < (x0 - x1) * (y - y1) / (y0 - y1) + x1)) inside = !inside;\n    x0 = x1, y0 = y1;\n  }\n\n  return inside;\n}\n", "export default function(polygon) {\n  var i = -1,\n      n = polygon.length,\n      b = polygon[n - 1],\n      xa,\n      ya,\n      xb = b[0],\n      yb = b[1],\n      perimeter = 0;\n\n  while (++i < n) {\n    xa = xb;\n    ya = yb;\n    b = polygon[i];\n    xb = b[0];\n    yb = b[1];\n    xa -= xb;\n    ya -= yb;\n    perimeter += Math.hypot(xa, ya);\n  }\n\n  return perimeter;\n}\n", "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-polygon'), require('d3-timer'), require('d3-dispatch'), require('d3-weighted-voronoi')) :\n  typeof define === 'function' && define.amd ? define(['exports', 'd3-polygon', 'd3-timer', 'd3-dispatch', 'd3-weighted-voronoi'], factory) :\n  (factory((global.d3 = global.d3 || {}),global.d3,global.d3,global.d3,global.d3));\n}(this, function (exports,d3Polygon,d3Timer,d3Dispatch,d3WeightedVoronoi) { 'use strict';\n\n  function FlickeringMitigation () {\n    /////// Inputs ///////\n    this.growthChangesLength = DEFAULT_LENGTH;\n    this.totalAvailableArea = NaN;\n\n    //begin: internals\n    this.lastAreaError = NaN;\n    this.lastGrowth = NaN;\n    this.growthChanges = [];\n    this.growthChangeWeights = generateGrowthChangeWeights(this.growthChangesLength); //used to make recent changes weighter than older changes\n    this.growthChangeWeightsSum = computeGrowthChangeWeightsSum(this.growthChangeWeights);\n    //end: internals\n  }\n\n  var DEFAULT_LENGTH = 10;\n\n  function direction(h0, h1) {\n    return (h0 >= h1)? 1 : -1;\n  }\n\n  function generateGrowthChangeWeights(length) {\n    var initialWeight = 3;   // a magic number\n    var weightDecrement = 1; // a magic number\n    var minWeight = 1;\n\n    var weightedCount = initialWeight;\n    var growthChangeWeights = [];\n\n    for (var i=0; i<length; i++) {\n      growthChangeWeights.push(weightedCount);\n      weightedCount -= weightDecrement;\n      if (weightedCount<minWeight) { weightedCount = minWeight; }\n    }\n    return growthChangeWeights;\n  }\n\n  function computeGrowthChangeWeightsSum (growthChangeWeights) {\n    var growthChangeWeightsSum = 0;\n    for (var i=0; i<growthChangeWeights.length; i++) {\n      growthChangeWeightsSum += growthChangeWeights[i];\n    }\n    return growthChangeWeightsSum;\n  }\n\n  ///////////////////////\n  ///////// API /////////\n  ///////////////////////\n\n  FlickeringMitigation.prototype.reset = function () {\n    this.lastAreaError = NaN;\n    this.lastGrowth = NaN;\n    this.growthChanges = [];\n    this.growthChangesLength = DEFAULT_LENGTH;\n    this.growthChangeWeights = generateGrowthChangeWeights(this.growthChangesLength);\n    this.growthChangeWeightsSum = computeGrowthChangeWeightsSum(this.growthChangeWeights);\n    this.totalAvailableArea = NaN;\n\n    return this;\n  };\n\n  FlickeringMitigation.prototype.clear = function () {\n    this.lastAreaError = NaN;\n    this.lastGrowth = NaN;\n    this.growthChanges = [];\n\n    return this;\n  };\n\n  FlickeringMitigation.prototype.length = function (_) {\n    if (!arguments.length) { return this.growthChangesLength; }\n\n    if (parseInt(_)>0) {\n      this.growthChangesLength = Math.floor(parseInt(_));\n      this.growthChangeWeights = generateGrowthChangeWeights(this.growthChangesLength);\n      this.growthChangeWeightsSum = computeGrowthChangeWeightsSum(this.growthChangeWeights);\n    } else {\n      console.warn(\"FlickeringMitigation.length() accepts only positive integers; unable to handle \"+_);\n    }\n    return this;\n  };\n\n  FlickeringMitigation.prototype.totalArea = function (_) {\n    if (!arguments.length) { return this.totalAvailableArea; }\n\n    if (parseFloat(_)>0) {\n      this.totalAvailableArea = parseFloat(_);\n    } else {\n      console.warn(\"FlickeringMitigation.totalArea() accepts only positive numbers; unable to handle \"+_);\n    }\n    return this;\n  };\n\n  FlickeringMitigation.prototype.add = function (areaError) {\n    var secondToLastAreaError, secondToLastGrowth;\n\n    secondToLastAreaError = this.lastAreaError;\n    this.lastAreaError = areaError;\n    if (!isNaN(secondToLastAreaError)) {\n      secondToLastGrowth = this.lastGrowth;\n      this.lastGrowth = direction(this.lastAreaError, secondToLastAreaError);\n    }\n    if (!isNaN(secondToLastGrowth)) {\n      this.growthChanges.unshift(this.lastGrowth!=secondToLastGrowth);\n    }\n\n    if (this.growthChanges.length>this.growthChangesLength) {\n      this.growthChanges.pop();\n    }\n    return this;\n  };\n\n  FlickeringMitigation.prototype.ratio = function () {\n    var weightedChangeCount = 0;\n    var ratio;\n\n    if (this.growthChanges.length < this.growthChangesLength) { return 0; }\n    if (this.lastAreaError > this.totalAvailableArea/10) { return 0; }\n\n    for(var i=0; i<this.growthChangesLength; i++) {\n      if (this.growthChanges[i]) {\n        weightedChangeCount += this.growthChangeWeights[i];\n      }\n    }\n\n    ratio = weightedChangeCount/this.growthChangeWeightsSum;\n\n    /*\n    if (ratio>0) {\n      console.log(\"flickering mitigation ratio: \"+Math.floor(ratio*1000)/1000);\n    }\n    */\n\n    return ratio;\n  };\n\n  function randomInitialPosition () {\n\n    //begin: internals\n    var clippingPolygon,\n      extent,\n      minX, maxX,\n      minY, maxY,\n      dx, dy;\n    //end: internals\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n\n    function _random(d, i, arr, voronoiMapSimulation) {\n      var shouldUpdateInternals = false;\n      var x, y;\n\n      if (clippingPolygon !== voronoiMapSimulation.clip()) {\n        clippingPolygon = voronoiMapSimulation.clip();\n        extent = voronoiMapSimulation.extent();\n        shouldUpdateInternals = true;\n      }\n\n      if (shouldUpdateInternals) {\n        updateInternals();\n      }\n\n      x = minX + dx * voronoiMapSimulation.prng()();\n      y = minY + dy * voronoiMapSimulation.prng()();\n      while (!d3Polygon.polygonContains(clippingPolygon, [x, y])) {\n        x = minX + dx * voronoiMapSimulation.prng()();\n        y = minY + dy * voronoiMapSimulation.prng()();\n      }\n      return [x, y];\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    function updateInternals() {\n      minX = extent[0][0];\n      maxX = extent[1][0];\n      minY = extent[0][1];\n      maxY = extent[1][1];\n      dx = maxX - minX;\n      dy = maxY - minY;\n    };\n\n    return _random;\n  };\n\n  function pie () {\n    //begin: internals\n    var startAngle = 0;\n    var clippingPolygon,\n      dataArray,\n      dataArrayLength,\n      clippingPolygonCentroid,\n      halfIncircleRadius,\n      angleBetweenData;\n    //end: internals\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n\n    function _pie(d, i, arr, voronoiMapSimulation) {\n      var shouldUpdateInternals = false;\n\n      if (clippingPolygon !== voronoiMapSimulation.clip()) {\n        clippingPolygon = voronoiMapSimulation.clip();\n        shouldUpdateInternals |= true;\n      }\n      if (dataArray !== arr) {\n        dataArray = arr;\n        shouldUpdateInternals |= true;\n      }\n\n      if (shouldUpdateInternals) {\n        updateInternals();\n      }\n\n      // add some randomness to prevent colinear/cocircular points\n      // substract -0.5 so that the average jitter is still zero\n      return [\n        clippingPolygonCentroid[0] + Math.cos(startAngle + i * angleBetweenData) * halfIncircleRadius + (voronoiMapSimulation.prng()() - 0.5) * 1E-3,\n        clippingPolygonCentroid[1] + Math.sin(startAngle + i * angleBetweenData) * halfIncircleRadius + (voronoiMapSimulation.prng()() - 0.5) * 1E-3\n      ];\n    };\n\n    _pie.startAngle = function (_) {\n      if (!arguments.length) {\n        return startAngle;\n      }\n\n      startAngle = _;\n      return _pie;\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    function updateInternals() {\n      clippingPolygonCentroid = d3Polygon.polygonCentroid(clippingPolygon);\n      halfIncircleRadius = computeMinDistFromEdges(clippingPolygonCentroid, clippingPolygon) / 2;\n      dataArrayLength = dataArray.length;\n      angleBetweenData = 2 * Math.PI / dataArrayLength;\n    };\n\n    function computeMinDistFromEdges(vertex, clippingPolygon) {\n      var minDistFromEdges = Infinity,\n        edgeIndex = 0,\n        edgeVertex0 = clippingPolygon[clippingPolygon.length - 1],\n        edgeVertex1 = clippingPolygon[edgeIndex];\n      var distFromCurrentEdge;\n\n      while (edgeIndex < clippingPolygon.length) {\n        distFromCurrentEdge = vDistance(vertex, edgeVertex0, edgeVertex1);\n        if (distFromCurrentEdge < minDistFromEdges) {\n          minDistFromEdges = distFromCurrentEdge;\n        }\n        edgeIndex++;\n        edgeVertex0 = edgeVertex1;\n        edgeVertex1 = clippingPolygon[edgeIndex];\n      }\n\n      return minDistFromEdges;\n    }\n\n    //from https://stackoverflow.com/questions/849211/shortest-distance-between-a-point-and-a-line-segment\n    function vDistance(vertex, edgeVertex0, edgeVertex1) {\n      var x = vertex[0],\n        y = vertex[1],\n        x1 = edgeVertex0[0],\n        y1 = edgeVertex0[1],\n        x2 = edgeVertex1[0],\n        y2 = edgeVertex1[1];\n      var A = x - x1,\n        B = y - y1,\n        C = x2 - x1,\n        D = y2 - y1;\n      var dot = A * C + B * D;\n      var len_sq = C * C + D * D;\n      var param = -1;\n\n      if (len_sq != 0) //in case of 0 length line\n        param = dot / len_sq;\n\n      var xx, yy;\n\n      if (param < 0) { // this should not arise as clippingpolygon is convex\n        xx = x1;\n        yy = y1;\n      } else if (param > 1) { // this should not arise as clippingpolygon is convex\n        xx = x2;\n        yy = y2;\n      } else {\n        xx = x1 + param * C;\n        yy = y1 + param * D;\n      }\n\n      var dx = x - xx;\n      var dy = y - yy;\n      return Math.sqrt(dx * dx + dy * dy);\n    }\n\n    return _pie;\n  }\n\n  function halfAverageAreaInitialWeight () {\n    //begin: internals\n    var clippingPolygon,\n      dataArray,\n      siteCount,\n      totalArea,\n      halfAverageArea;\n    //end: internals\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n    function _halfAverageArea(d, i, arr, voronoiMapSimulation) {\n      var shouldUpdateInternals = false;\n      if (clippingPolygon !== voronoiMapSimulation.clip()) {\n        clippingPolygon = voronoiMapSimulation.clip();\n        shouldUpdateInternals |= true;\n      }\n      if (dataArray !== arr) {\n        dataArray = arr;\n        shouldUpdateInternals |= true;\n      }\n\n      if (shouldUpdateInternals) {\n        updateInternals();\n      }\n\n      return halfAverageArea;\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    function updateInternals() {\n      siteCount = dataArray.length;\n      totalArea = d3Polygon.polygonArea(clippingPolygon);\n      halfAverageArea = totalArea / siteCount / 2; // half of the average area of the the clipping polygon\n    }\n\n    return _halfAverageArea;\n  };\n\n  // from https://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript\n  // (above link provided by https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)\n\n  function d3VoronoiMapError(message) {\n    this.message = message;\n    this.stack = new Error().stack;\n  }\n\n  d3VoronoiMapError.prototype.name = 'd3VoronoiMapError';\n  d3VoronoiMapError.prototype = new Error();\n\n  function voronoiMapSimulation(data) {\n    //begin: constants\n    var DEFAULT_CONVERGENCE_RATIO = 0.01;\n    var DEFAULT_MAX_ITERATION_COUNT = 50;\n    var DEFAULT_MIN_WEIGHT_RATIO = 0.01;\n    var DEFAULT_PRNG = Math.random;\n    var DEFAULT_INITIAL_POSITION = randomInitialPosition();\n    var DEFAULT_INITIAL_WEIGHT = halfAverageAreaInitialWeight();\n    var RANDOM_INITIAL_POSITION = randomInitialPosition();\n    var epsilon = 1e-10;\n    //end: constants\n\n    /////// Inputs ///////\n    var weight = function (d) {\n      return d.weight;\n    }; // accessor to the weight\n    var convergenceRatio = DEFAULT_CONVERGENCE_RATIO; // targeted allowed error ratio; default 0.01 stops computation when cell areas error <= 1% clipping polygon's area\n    var maxIterationCount = DEFAULT_MAX_ITERATION_COUNT; // maximum allowed iteration; stops computation even if convergence is not reached; use a large amount for a sole converge-based computation stop\n    var minWeightRatio = DEFAULT_MIN_WEIGHT_RATIO; // used to compute the minimum allowed weight; default 0.01 means 1% of max weight; handle near-zero weights, and leaves enought space for cell hovering\n    var prng = DEFAULT_PRNG; // pseudorandom number generator\n    var initialPosition = DEFAULT_INITIAL_POSITION; // accessor to the initial position; defaults to a random position inside the clipping polygon\n    var initialWeight = DEFAULT_INITIAL_WEIGHT; // accessor to the initial weight; defaults to the average area of the clipping polygon\n\n    //begin: internals\n    var weightedVoronoi = d3WeightedVoronoi.weightedVoronoi(),\n      flickeringMitigation = new FlickeringMitigation(),\n      shouldInitialize = true, // should initialize due to changes via APIs\n      siteCount, // number of sites\n      totalArea, // area of the clipping polygon\n      areaErrorTreshold, // targeted allowed area error (= totalArea * convergenceRatio); below this treshold, map is considered obtained and computation stops\n      iterationCount, // current iteration\n      polygons, // current computed polygons\n      areaError, // current area error\n      converged, // true if (areaError < areaErrorTreshold)\n      ended; // stores if computation is ended, either if computation has converged or if it has reached the maximum allowed iteration\n    //end: internals\n    //being: internals/simulation\n    var simulation,\n      stepper = d3Timer.timer(step),\n      event = d3Dispatch.dispatch('tick', 'end');\n    //end: internals/simulation\n\n    //begin: algorithm conf.\n    const HANDLE_OVERWEIGHTED_VARIANT = 1; // this option still exists 'cause for further experiments\n    const HANLDE_OVERWEIGHTED_MAX_ITERATION_COUNT = 1000; // max number of tries to handle overweigthed sites\n    var handleOverweighted;\n    //end: algorithm conf.\n\n    //begin: utils\n    function sqr(d) {\n      return Math.pow(d, 2);\n    }\n\n    function squaredDistance(s0, s1) {\n      return sqr(s1.x - s0.x) + sqr(s1.y - s0.y);\n    }\n    //end: utils\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n\n    simulation = {\n      tick: tick,\n\n      restart: function () {\n        stepper.restart(step);\n        return simulation;\n      },\n\n      stop: function () {\n        stepper.stop();\n        return simulation;\n      },\n\n      weight: function (_) {\n        if (!arguments.length) {\n          return weight;\n        }\n\n        weight = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      convergenceRatio: function (_) {\n        if (!arguments.length) {\n          return convergenceRatio;\n        }\n\n        convergenceRatio = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      maxIterationCount: function (_) {\n        if (!arguments.length) {\n          return maxIterationCount;\n        }\n\n        maxIterationCount = _;\n        return simulation;\n      },\n\n      minWeightRatio: function (_) {\n        if (!arguments.length) {\n          return minWeightRatio;\n        }\n\n        minWeightRatio = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      clip: function (_) {\n        if (!arguments.length) {\n          return weightedVoronoi.clip();\n        }\n\n        weightedVoronoi.clip(_);\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      extent: function (_) {\n        if (!arguments.length) {\n          return weightedVoronoi.extent();\n        }\n\n        weightedVoronoi.extent(_);\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      size: function (_) {\n        if (!arguments.length) {\n          return weightedVoronoi.size();\n        }\n\n        weightedVoronoi.size(_);\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      prng: function (_) {\n        if (!arguments.length) {\n          return prng;\n        }\n\n        prng = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      initialPosition: function (_) {\n        if (!arguments.length) {\n          return initialPosition;\n        }\n\n        initialPosition = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      initialWeight: function (_) {\n        if (!arguments.length) {\n          return initialWeight;\n        }\n\n        initialWeight = _;\n        shouldInitialize = true;\n        return simulation;\n      },\n\n      state: function () {\n        if (shouldInitialize) {\n          initializeSimulation();\n        }\n        return {\n          ended: ended,\n          iterationCount: iterationCount,\n          convergenceRatio: areaError / totalArea,\n          polygons: polygons,\n        };\n      },\n\n      on: function (name, _) {\n        if (arguments.length === 1) {\n          return event.on(name);\n        }\n\n        event.on(name, _);\n        return simulation;\n      },\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    //begin: simulation's main loop\n    function step() {\n      tick();\n      event.call('tick', simulation);\n      if (ended) {\n        stepper.stop();\n        event.call('end', simulation);\n      }\n    }\n    //end: simulation's main loop\n\n    //begin: algorithm used at each iteration\n    function tick() {\n      if (!ended) {\n        if (shouldInitialize) {\n          initializeSimulation();\n        }\n        polygons = adapt(polygons, flickeringMitigation.ratio());\n        iterationCount++;\n        areaError = computeAreaError(polygons);\n        flickeringMitigation.add(areaError);\n        converged = areaError < areaErrorTreshold;\n        ended = converged || iterationCount >= maxIterationCount;\n        // console.log(\"error %: \"+Math.round(areaError*100*1000/totalArea)/1000);\n      }\n    }\n    //end: algorithm used at each iteration\n\n    function initializeSimulation() {\n      //begin: handle algorithm's variants\n      setHandleOverweighted();\n      //end: handle algorithm's variants\n\n      siteCount = data.length;\n      totalArea = Math.abs(d3Polygon.polygonArea(weightedVoronoi.clip()));\n      areaErrorTreshold = convergenceRatio * totalArea;\n      flickeringMitigation.clear().totalArea(totalArea);\n\n      iterationCount = 0;\n      converged = false;\n      polygons = initialize(data, simulation);\n      ended = false;\n      shouldInitialize = false;\n    }\n\n    function initialize(data, simulation) {\n      var maxWeight = data.reduce(function (max, d) {\n          return Math.max(max, weight(d));\n        }, -Infinity),\n        minAllowedWeight = maxWeight * minWeightRatio;\n      var weights, mapPoints;\n\n      //begin: extract weights\n      weights = data.map(function (d, i, arr) {\n        return {\n          index: i,\n          weight: Math.max(weight(d), minAllowedWeight),\n          initialPosition: initialPosition(d, i, arr, simulation),\n          initialWeight: initialWeight(d, i, arr, simulation),\n          originalData: d,\n        };\n      });\n      //end: extract weights\n\n      // create map-related points\n      // (with targetedArea, initial position and initialWeight)\n      mapPoints = createMapPoints(weights, simulation);\n      handleOverweighted(mapPoints);\n      return weightedVoronoi(mapPoints);\n    }\n\n    function createMapPoints(basePoints, simulation) {\n      var totalWeight = basePoints.reduce(function (acc, bp) {\n        return (acc += bp.weight);\n      }, 0);\n      var initialPosition;\n\n      return basePoints.map(function (bp, i, bps) {\n        initialPosition = bp.initialPosition;\n\n        if (!d3Polygon.polygonContains(weightedVoronoi.clip(), initialPosition)) {\n          initialPosition = DEFAULT_INITIAL_POSITION(bp, i, bps, simulation);\n        }\n\n        return {\n          index: bp.index,\n          targetedArea: (totalArea * bp.weight) / totalWeight,\n          data: bp,\n          x: initialPosition[0],\n          y: initialPosition[1],\n          weight: bp.initialWeight, // ArlindNocaj/Voronoi-Treemap-Library uses an epsilonesque initial weight; using heavier initial weights allows faster weight adjustements, hence faster stabilization\n        };\n      });\n    }\n\n    function adapt(polygons, flickeringMitigationRatio) {\n      var adaptedMapPoints;\n\n      adaptPositions(polygons, flickeringMitigationRatio);\n      adaptedMapPoints = polygons.map(function (p) {\n        return p.site.originalObject;\n      });\n      polygons = weightedVoronoi(adaptedMapPoints);\n      if (polygons.length < siteCount) {\n        throw new d3VoronoiMapError('at least 1 site has no area, which is not supposed to arise');\n      }\n\n      adaptWeights(polygons, flickeringMitigationRatio);\n      adaptedMapPoints = polygons.map(function (p) {\n        return p.site.originalObject;\n      });\n      polygons = weightedVoronoi(adaptedMapPoints);\n      if (polygons.length < siteCount) {\n        throw new d3VoronoiMapError('at least 1 site has no area, which is not supposed to arise');\n      }\n\n      return polygons;\n    }\n\n    function adaptPositions(polygons, flickeringMitigationRatio) {\n      var newMapPoints = [],\n        flickeringInfluence = 0.5;\n      var flickeringMitigation, d, polygon, mapPoint, centroid, dx, dy;\n\n      flickeringMitigation = flickeringInfluence * flickeringMitigationRatio;\n      d = 1 - flickeringMitigation; // in [0.5, 1]\n      for (var i = 0; i < siteCount; i++) {\n        polygon = polygons[i];\n        mapPoint = polygon.site.originalObject;\n        centroid = d3Polygon.polygonCentroid(polygon);\n\n        dx = centroid[0] - mapPoint.x;\n        dy = centroid[1] - mapPoint.y;\n\n        //begin: handle excessive change;\n        dx *= d;\n        dy *= d;\n        //end: handle excessive change;\n\n        mapPoint.x += dx;\n        mapPoint.y += dy;\n\n        newMapPoints.push(mapPoint);\n      }\n\n      handleOverweighted(newMapPoints);\n    }\n\n    function adaptWeights(polygons, flickeringMitigationRatio) {\n      var newMapPoints = [],\n        flickeringInfluence = 0.1;\n      var flickeringMitigation, polygon, mapPoint, currentArea, adaptRatio, adaptedWeight;\n\n      flickeringMitigation = flickeringInfluence * flickeringMitigationRatio;\n      for (var i = 0; i < siteCount; i++) {\n        polygon = polygons[i];\n        mapPoint = polygon.site.originalObject;\n        currentArea = d3Polygon.polygonArea(polygon);\n        adaptRatio = mapPoint.targetedArea / currentArea;\n\n        //begin: handle excessive change;\n        adaptRatio = Math.max(adaptRatio, 1 - flickeringInfluence + flickeringMitigation); // in [(1-flickeringInfluence), 1]\n        adaptRatio = Math.min(adaptRatio, 1 + flickeringInfluence - flickeringMitigation); // in [1, (1+flickeringInfluence)]\n        //end: handle excessive change;\n\n        adaptedWeight = mapPoint.weight * adaptRatio;\n        adaptedWeight = Math.max(adaptedWeight, epsilon);\n\n        mapPoint.weight = adaptedWeight;\n\n        newMapPoints.push(mapPoint);\n      }\n\n      handleOverweighted(newMapPoints);\n    }\n\n    // heuristics: lower heavy weights\n    function handleOverweighted0(mapPoints) {\n      var fixCount = 0;\n      var fixApplied, tpi, tpj, weightest, lightest, sqrD, adaptedWeight;\n      do {\n        if (fixCount > HANLDE_OVERWEIGHTED_MAX_ITERATION_COUNT) {\n          throw new d3VoronoiMapError('handleOverweighted0 is looping too much');\n        }\n        fixApplied = false;\n        for (var i = 0; i < siteCount; i++) {\n          tpi = mapPoints[i];\n          for (var j = i + 1; j < siteCount; j++) {\n            tpj = mapPoints[j];\n            if (tpi.weight > tpj.weight) {\n              weightest = tpi;\n              lightest = tpj;\n            } else {\n              weightest = tpj;\n              lightest = tpi;\n            }\n            sqrD = squaredDistance(tpi, tpj);\n            if (sqrD < weightest.weight - lightest.weight) {\n              // adaptedWeight = sqrD - epsilon; // as in ArlindNocaj/Voronoi-Treemap-Library\n              // adaptedWeight = sqrD + lightest.weight - epsilon; // works, but below heuristics performs better (less flickering)\n              adaptedWeight = sqrD + lightest.weight / 2;\n              adaptedWeight = Math.max(adaptedWeight, epsilon);\n              weightest.weight = adaptedWeight;\n              fixApplied = true;\n              fixCount++;\n              break;\n            }\n          }\n          if (fixApplied) {\n            break;\n          }\n        }\n      } while (fixApplied);\n\n      /*\n      if (fixCount > 0) {\n        console.log('# fix: ' + fixCount);\n      }\n      */\n    }\n\n    // heuristics: increase light weights\n    function handleOverweighted1(mapPoints) {\n      var fixCount = 0;\n      var fixApplied, tpi, tpj, weightest, lightest, sqrD, overweight;\n      do {\n        if (fixCount > HANLDE_OVERWEIGHTED_MAX_ITERATION_COUNT) {\n          throw new d3VoronoiMapError('handleOverweighted1 is looping too much');\n        }\n        fixApplied = false;\n        for (var i = 0; i < siteCount; i++) {\n          tpi = mapPoints[i];\n          for (var j = i + 1; j < siteCount; j++) {\n            tpj = mapPoints[j];\n            if (tpi.weight > tpj.weight) {\n              weightest = tpi;\n              lightest = tpj;\n            } else {\n              weightest = tpj;\n              lightest = tpi;\n            }\n            sqrD = squaredDistance(tpi, tpj);\n            if (sqrD < weightest.weight - lightest.weight) {\n              overweight = weightest.weight - lightest.weight - sqrD;\n              lightest.weight += overweight + epsilon;\n              fixApplied = true;\n              fixCount++;\n              break;\n            }\n          }\n          if (fixApplied) {\n            break;\n          }\n        }\n      } while (fixApplied);\n\n      /*\n      if (fixCount > 0) {\n        console.log('# fix: ' + fixCount);\n      }\n      */\n    }\n\n    function computeAreaError(polygons) {\n      //convergence based on summation of all sites current areas\n      var areaErrorSum = 0;\n      var polygon, mapPoint, currentArea;\n      for (var i = 0; i < siteCount; i++) {\n        polygon = polygons[i];\n        mapPoint = polygon.site.originalObject;\n        currentArea = d3Polygon.polygonArea(polygon);\n        areaErrorSum += Math.abs(mapPoint.targetedArea - currentArea);\n      }\n      return areaErrorSum;\n    }\n\n    function setHandleOverweighted() {\n      switch (HANDLE_OVERWEIGHTED_VARIANT) {\n        case 0:\n          handleOverweighted = handleOverweighted0;\n          break;\n        case 1:\n          handleOverweighted = handleOverweighted1;\n          break;\n        default:\n          console.error(\"unknown 'handleOverweighted' variant; using variant #1\");\n          handleOverweighted = handleOverweighted0;\n      }\n    }\n\n    return simulation;\n  }\n\n  exports.voronoiMapSimulation = voronoiMapSimulation;\n  exports.voronoiMapInitialPositionRandom = randomInitialPosition;\n  exports.voronoiMapInitialPositionPie = pie;\n  exports.d3VoronoiMapError = d3VoronoiMapError;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n}));", "var noop = {value: () => {}};\n\nfunction dispatch() {\n  for (var i = 0, n = arguments.length, _ = {}, t; i < n; ++i) {\n    if (!(t = arguments[i] + \"\") || (t in _) || /[\\s.]/.test(t)) throw new Error(\"illegal type: \" + t);\n    _[t] = [];\n  }\n  return new Dispatch(_);\n}\n\nfunction Dispatch(_) {\n  this._ = _;\n}\n\nfunction parseTypenames(typenames, types) {\n  return typenames.trim().split(/^|\\s+/).map(function(t) {\n    var name = \"\", i = t.indexOf(\".\");\n    if (i >= 0) name = t.slice(i + 1), t = t.slice(0, i);\n    if (t && !types.hasOwnProperty(t)) throw new Error(\"unknown type: \" + t);\n    return {type: t, name: name};\n  });\n}\n\nDispatch.prototype = dispatch.prototype = {\n  constructor: Dispatch,\n  on: function(typename, callback) {\n    var _ = this._,\n        T = parseTypenames(typename + \"\", _),\n        t,\n        i = -1,\n        n = T.length;\n\n    // If no callback was specified, return the callback of the given type and name.\n    if (arguments.length < 2) {\n      while (++i < n) if ((t = (typename = T[i]).type) && (t = get(_[t], typename.name))) return t;\n      return;\n    }\n\n    // If a type was specified, set the callback for the given type and name.\n    // Otherwise, if a null callback was specified, remove callbacks of the given name.\n    if (callback != null && typeof callback !== \"function\") throw new Error(\"invalid callback: \" + callback);\n    while (++i < n) {\n      if (t = (typename = T[i]).type) _[t] = set(_[t], typename.name, callback);\n      else if (callback == null) for (t in _) _[t] = set(_[t], typename.name, null);\n    }\n\n    return this;\n  },\n  copy: function() {\n    var copy = {}, _ = this._;\n    for (var t in _) copy[t] = _[t].slice();\n    return new Dispatch(copy);\n  },\n  call: function(type, that) {\n    if ((n = arguments.length - 2) > 0) for (var args = new Array(n), i = 0, n, t; i < n; ++i) args[i] = arguments[i + 2];\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  },\n  apply: function(type, that, args) {\n    if (!this._.hasOwnProperty(type)) throw new Error(\"unknown type: \" + type);\n    for (var t = this._[type], i = 0, n = t.length; i < n; ++i) t[i].value.apply(that, args);\n  }\n};\n\nfunction get(type, name) {\n  for (var i = 0, n = type.length, c; i < n; ++i) {\n    if ((c = type[i]).name === name) {\n      return c.value;\n    }\n  }\n}\n\nfunction set(type, name, callback) {\n  for (var i = 0, n = type.length; i < n; ++i) {\n    if (type[i].name === name) {\n      type[i] = noop, type = type.slice(0, i).concat(type.slice(i + 1));\n      break;\n    }\n  }\n  if (callback != null) type.push({name: name, value: callback});\n  return type;\n}\n\nexport default dispatch;\n", "var frame = 0, // is an animation frame pending?\n    timeout = 0, // is a timeout pending?\n    interval = 0, // are any timers active?\n    pokeDelay = 1000, // how frequently we check for clock skew\n    taskHead,\n    taskTail,\n    clockLast = 0,\n    clockNow = 0,\n    clockSkew = 0,\n    clock = typeof performance === \"object\" && performance.now ? performance : Date,\n    setFrame = typeof window === \"object\" && window.requestAnimationFrame ? window.requestAnimationFrame.bind(window) : function(f) { setTimeout(f, 17); };\n\nexport function now() {\n  return clockNow || (setFrame(clearNow), clockNow = clock.now() + clockSkew);\n}\n\nfunction clearNow() {\n  clockNow = 0;\n}\n\nexport function Timer() {\n  this._call =\n  this._time =\n  this._next = null;\n}\n\nTimer.prototype = timer.prototype = {\n  constructor: Timer,\n  restart: function(callback, delay, time) {\n    if (typeof callback !== \"function\") throw new TypeError(\"callback is not a function\");\n    time = (time == null ? now() : +time) + (delay == null ? 0 : +delay);\n    if (!this._next && taskTail !== this) {\n      if (taskTail) taskTail._next = this;\n      else taskHead = this;\n      taskTail = this;\n    }\n    this._call = callback;\n    this._time = time;\n    sleep();\n  },\n  stop: function() {\n    if (this._call) {\n      this._call = null;\n      this._time = Infinity;\n      sleep();\n    }\n  }\n};\n\nexport function timer(callback, delay, time) {\n  var t = new Timer;\n  t.restart(callback, delay, time);\n  return t;\n}\n\nexport function timerFlush() {\n  now(); // Get the current time, if not already set.\n  ++frame; // Pretend we’ve set an alarm, if we haven’t already.\n  var t = taskHead, e;\n  while (t) {\n    if ((e = clockNow - t._time) >= 0) t._call.call(null, e);\n    t = t._next;\n  }\n  --frame;\n}\n\nfunction wake() {\n  clockNow = (clockLast = clock.now()) + clockSkew;\n  frame = timeout = 0;\n  try {\n    timerFlush();\n  } finally {\n    frame = 0;\n    nap();\n    clockNow = 0;\n  }\n}\n\nfunction poke() {\n  var now = clock.now(), delay = now - clockLast;\n  if (delay > pokeDelay) clockSkew -= delay, clockLast = now;\n}\n\nfunction nap() {\n  var t0, t1 = taskHead, t2, time = Infinity;\n  while (t1) {\n    if (t1._call) {\n      if (time > t1._time) time = t1._time;\n      t0 = t1, t1 = t1._next;\n    } else {\n      t2 = t1._next, t1._next = null;\n      t1 = t0 ? t0._next = t2 : taskHead = t2;\n    }\n  }\n  taskTail = t0;\n  sleep(time);\n}\n\nfunction sleep(time) {\n  if (frame) return; // Soonest alarm already set, or will be.\n  if (timeout) timeout = clearTimeout(timeout);\n  var delay = time - clockNow; // Strictly less than if we recomputed clockNow.\n  if (delay > 24) {\n    if (time < Infinity) timeout = setTimeout(wake, time - clock.now() - clockSkew);\n    if (interval) interval = clearInterval(interval);\n  } else {\n    if (!interval) clockLast = clock.now(), interval = setInterval(poke, pokeDelay);\n    frame = 1, setFrame(wake);\n  }\n}\n", "import {Timer} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer;\n  delay = delay == null ? 0 : +delay;\n  t.restart(elapsed => {\n    t.stop();\n    callback(elapsed + delay);\n  }, delay, time);\n  return t;\n}\n", "import {Timer, now} from \"./timer.js\";\n\nexport default function(callback, delay, time) {\n  var t = new Timer, total = delay;\n  if (delay == null) return t.restart(callback, delay, time), t;\n  t._restart = t.restart;\n  t.restart = function(callback, delay, time) {\n    delay = +delay, time = time == null ? now() : +time;\n    t._restart(function tick(elapsed) {\n      elapsed += total;\n      t._restart(tick, total += delay, time);\n      callback(elapsed);\n    }, delay, time);\n  }\n  t.restart(callback, delay, time);\n  return t;\n}\n", "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-voronoi-map')) :\n  typeof define === 'function' && define.amd ? define(['exports', 'd3-voronoi-map'], factory) :\n  (factory((global.d3 = global.d3 || {}),global.d3));\n}(this, function (exports,d3VoronoiMap) { 'use strict';\n\n  function voronoiTreemap() {\n    //begin: constants\n    var DEFAULT_CONVERGENCE_RATIO = 0.01;\n    var DEFAULT_MAX_ITERATION_COUNT = 50;\n    var DEFAULT_MIN_WEIGHT_RATIO = 0.01;\n    var DEFAULT_PRNG = Math.random;\n    //end: constants\n\n    /////// Inputs ///////\n    var clip = [\n      [0, 0],\n      [0, 1],\n      [1, 1],\n      [1, 0],\n    ]; // clipping polygon\n    var extent = [\n      [0, 0],\n      [1, 1],\n    ]; // extent of the clipping polygon\n    var size = [1, 1]; // [width, height] of the clipping polygon\n    var convergenceRatio = DEFAULT_CONVERGENCE_RATIO; // targeted allowed error ratio; default 0.01 stops computation when cell areas error <= 1% clipping polygon's area\n    var maxIterationCount = DEFAULT_MAX_ITERATION_COUNT; // maximum allowed iteration; stops computation even if convergence is not reached; use a large amount for a sole converge-based computation stop\n    var minWeightRatio = DEFAULT_MIN_WEIGHT_RATIO; // used to compute the minimum allowed weight; default 0.01 means 1% of max weight; handle near-zero weights, and leaves enought space for cell hovering\n    var prng = DEFAULT_PRNG; // pseudorandom number generator\n\n    //begin: internals\n    var unrelevantButNeedeData = [\n      {\n        weight: 1,\n      },\n      {\n        weight: 1,\n      },\n    ];\n    var _convenientReusableVoronoiMapSimulation = d3VoronoiMap.voronoiMapSimulation(unrelevantButNeedeData).stop();\n    //end: internals\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n\n    function _voronoiTreemap(rootNode) {\n      recurse(clip, rootNode);\n    }\n\n    _voronoiTreemap.convergenceRatio = function (_) {\n      if (!arguments.length) {\n        return convergenceRatio;\n      }\n\n      convergenceRatio = _;\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.maxIterationCount = function (_) {\n      if (!arguments.length) {\n        return maxIterationCount;\n      }\n\n      maxIterationCount = _;\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.minWeightRatio = function (_) {\n      if (!arguments.length) {\n        return minWeightRatio;\n      }\n\n      minWeightRatio = _;\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.clip = function (_) {\n      if (!arguments.length) {\n        return clip;\n      }\n\n      //begin: use voronoiMap.clip() to handle clip/extent/size computation and borderline input (non-counterclockwise, non-convex, ...)\n      _convenientReusableVoronoiMapSimulation.clip(_);\n      //end: use voronoiMap.clip() to handle clip/extent/size computation\n      clip = _convenientReusableVoronoiMapSimulation.clip();\n      extent = _convenientReusableVoronoiMapSimulation.extent();\n      size = _convenientReusableVoronoiMapSimulation.size();\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.extent = function (_) {\n      if (!arguments.length) {\n        return extent;\n      }\n\n      //begin: use voronoiMap.extent() to handle clip/extent/size computation\n      _convenientReusableVoronoiMapSimulation.extent(_);\n      //end: use voronoiMap.clip() to handle clip/extent/size computation\n      clip = _convenientReusableVoronoiMapSimulation.clip();\n      extent = _convenientReusableVoronoiMapSimulation.extent();\n      size = _convenientReusableVoronoiMapSimulation.size();\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.size = function (_) {\n      if (!arguments.length) {\n        return size;\n      }\n\n      //begin: use voronoiMap.size() to handle clip/extent/size computation\n      _convenientReusableVoronoiMapSimulation.size(_);\n      //end: use voronoiMap.clip() to handle clip/extent/size computation\n      clip = _convenientReusableVoronoiMapSimulation.clip();\n      extent = _convenientReusableVoronoiMapSimulation.extent();\n      size = _convenientReusableVoronoiMapSimulation.size();\n      return _voronoiTreemap;\n    };\n\n    _voronoiTreemap.prng = function (_) {\n      if (!arguments.length) {\n        return prng;\n      }\n\n      prng = _;\n      return _voronoiTreemap;\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    function recurse(clippingPolygon, node) {\n      var simulation;\n\n      //assign polygon to node\n      node.polygon = clippingPolygon;\n\n      if (node.height != 0) {\n        //compute one-level Voronoi map of children\n        simulation = d3VoronoiMap.voronoiMapSimulation(node.children)\n          .clip(clippingPolygon)\n          .weight(function (d) {\n            return d.value;\n          })\n          .convergenceRatio(convergenceRatio)\n          .maxIterationCount(maxIterationCount)\n          .minWeightRatio(minWeightRatio)\n          .prng(prng)\n          .stop();\n\n        var state = simulation.state(); // retrieve the Voronoï map simulation's state\n\n        //begin: manually launch each iteration until the Voronoï map simulation ends\n        while (!state.ended) {\n          simulation.tick();\n          state = simulation.state();\n        }\n        //end: manually launch each iteration until the Voronoï map simulation ends\n\n        //begin: recurse on children\n        state.polygons.forEach(function (cp) {\n          recurse(cp, cp.site.originalObject.data.originalData);\n        });\n        //end: recurse on children\n      }\n    }\n\n    return _voronoiTreemap;\n  }\n\n  exports.voronoiTreemap = voronoiTreemap;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n}));", "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('d3-array'), require('d3-polygon')) :\n  typeof define === 'function' && define.amd ? define(['exports', 'd3-array', 'd3-polygon'], factory) :\n  (factory((global.d3 = global.d3 || {}),global.d3,global.d3));\n}(this, function (exports,d3Array,d3Polygon) { 'use strict';\n\n  var epsilon = 1e-10;\n\n  function epsilonesque(n) {\n    return n <= epsilon && n >= -epsilon;\n  }\n\n  // IN: vectors or vertices\n  // OUT: dot product\n  function dot(v0, v1) {\n    return v0.x * v1.x + v0.y * v1.y + v0.z * v1.z;\n  }\n\n  // IN: two vertex objects, v0 and v1\n  // OUT: true if they are linearly dependent, false otherwise\n  // from https://math.stackexchange.com/questions/1144357/how-can-i-prove-that-two-vectors-in-%E2%84%9D3-are-linearly-independent-iff-their-cro\n  function linearDependent(v0, v1) {\n    return (\n      epsilonesque(v0.x * v1.y - v0.y * v1.x) &&\n      epsilonesque(v0.y * v1.z - v0.z * v1.y) &&\n      epsilonesque(v0.z * v1.x - v0.x * v1.z)\n    );\n  }\n\n  // IN: an array of 2D-points [x,y]\n  // OUT: true if the set defines a convex polygon (non-intersecting, hole-free, non-concave)\n  // from https://gist.github.com/annatomka/82715127b74473859054, adapted to [x,y] syntax (instead of {x: ..., y: ...}) and optimizations\n  function polygonDirection(polygon) {\n    var sign, crossproduct, p0, p1, p2, v0, v1, i;\n\n    //begin: initialization\n    p0 = polygon[polygon.length - 2];\n    p1 = polygon[polygon.length - 1];\n    p2 = polygon[0];\n    v0 = vect(p0, p1);\n    v1 = vect(p1, p2);\n    crossproduct = calculateCrossproduct(v0, v1);\n    // console.log(`[[${p0}], [${p1}], [${p2}]] => (${v0}) x (${v1}) = ${crossproduct}`);\n    sign = Math.sign(crossproduct);\n    //end: initialization\n\n    p0 = p1; // p0 = polygon[polygon.length - 1];\n    p1 = p2; // p1 = polygon[0];\n    p2 = polygon[1];\n    v0 = v1;\n    v1 = vect(p1, p2);\n    crossproduct = calculateCrossproduct(v0, v1);\n    // console.log(`[[${p0}], [${p1}], [${p2}]] => (${v0}) x (${v1}) = ${crossproduct}`);\n    if (Math.sign(crossproduct) !== sign) {\n      return undefined;\n    } //different signs in cross products means concave polygon\n\n    //iterate on remaining 3 consecutive points\n    for (i = 2; i < polygon.length - 1; i++) {\n      p0 = p1;\n      p1 = p2;\n      p2 = polygon[i];\n      v0 = v1;\n      v1 = vect(p1, p2);\n      crossproduct = calculateCrossproduct(v0, v1);\n      // console.log(`[[${p0}], [${p1}], [${p2}]] => (${v0}) x (${v1}) = ${crossproduct}`);\n      if (Math.sign(crossproduct) !== sign) {\n        return undefined;\n      } //different signs in cross products means concave polygon\n    }\n\n    return sign;\n  }\n\n  function vect(from, to) {\n    return [to[0] - from[0], to[1] - from[1]];\n  }\n\n  function calculateCrossproduct(v0, v1) {\n    return v0[0] * v1[1] - v0[1] * v1[0];\n  }\n\n  // ConflictList and ConflictListNode\n\n  function ConflictListNode (face, vert) {\n    this.face = face;\n    this.vert = vert;\n    this.nextf = null;\n    this.prevf = null;\n    this.nextv = null;\n    this.prevv = null;\n  }\n\n  // IN: boolean forFace\n  function ConflictList (forFace) {\n    this.forFace = forFace;\n    this.head = null;\n  }\n\n  // IN: ConflictListNode cln\n  ConflictList.prototype.add = function(cln) {\n    if (this.head === null) {\n      this.head = cln;\n    } else {\n      if (this.forFace) {  // Is FaceList\n        this.head.prevv = cln;\n        cln.nextv = this.head;\n        this.head = cln;\n      } else {  // Is VertexList\n        this.head.prevf = cln;\n        cln.nextf = this.head;\n        this.head = cln;\n      }\n    }\n  }\n\n  ConflictList.prototype.isEmpty = function() {\n    return this.head === null;\n  }\n\n  // Array of faces visible\n  ConflictList.prototype.fill = function(visible) {\n    if (this.forFace) {\n      return;\n    }\n    var curr = this.head;\n    do {\n      visible.push(curr.face);\n      curr.face.marked = true;\n      curr = curr.nextf;\n    } while (curr !== null);\n  }\n\n  ConflictList.prototype.removeAll = function() {\n    if (this.forFace) {  // Remove all vertices from Face\n      var curr = this.head;\n      do {\n        if (curr.prevf === null) {  // Node is head\n          if (curr.nextf === null) {\n            curr.vert.conflicts.head = null;\n          } else {\n            curr.nextf.prevf = null;\n            curr.vert.conflicts.head = curr.nextf;\n          }\n        } else {  // Node is not head\n          if (curr.nextf != null) {\n            curr.nextf.prevf = curr.prevf;\n          }\n          curr.prevf.nextf = curr.nextf;\n        }\n        curr = curr.nextv;\n        if (curr != null) {\n          curr.prevv = null;\n        }\n      } while (curr != null);\n    } else {  // Remove all JFaces from vertex\n      var curr = this.head;\n      do {\n        if (curr.prevv == null) {  // Node is head\n          if (curr.nextv == null) {\n            curr.face.conflicts.head = null;\n          } else {\n            curr.nextv.prevv = null;\n            curr.face.conflicts.head = curr.nextv;\n          }\n        } else {  // Node is not head\n          if (curr.nextv != null) {\n            curr.nextv.prevv = curr.prevv;\n          }\n          curr.prevv.nextv = curr.nextv;\n        }\n        curr = curr.nextf;\n        if (curr != null)\n          curr.prevf = null;\n      } while (curr != null);\n    }\n  }\n\n  // IN: list of vertices\n  ConflictList.prototype.getVertices = function() {\n    var list = [],\n    \t\tcurr = this.head;\n    while (curr !== null) {\n      list.push(curr.vert);\n      curr = curr.nextv;\n    }\n    return list;\n  }\n\n  // IN: coordinates x, y, z\n  function Vertex (x, y, z, weight, orig, isDummy) {\n    this.x = x;\n    this.y = y;\n    this.weight = epsilon;\n    this.index = 0;\n    this.conflicts = new ConflictList(false);\n    this.neighbours = null;  // Potential trouble\n    this.nonClippedPolygon = null;\n    this.polygon = null;\n    this.originalObject = null;\n    this.isDummy = false;\n\n    if (orig !== undefined) {\n      this.originalObject = orig;\n    }\n    if (isDummy != undefined) {\n      this.isDummy = isDummy;\n    }\n    if (weight != null) {\n      this.weight = weight;\n    }\n    if (z != null) {\n      this.z = z;\n    } else {\n      this.z = this.projectZ(this.x, this.y, this.weight);\n    }\n  }\n\n  Vertex.prototype.projectZ = function(x, y, weight) {\n    return ((x*x) + (y*y) - weight);\n  }\n\n  Vertex.prototype.setWeight = function(weight) {\n    this.weight = weight;\n    this.z = this.projectZ(this.x, this.y, this.weight);\n  }\n\n  Vertex.prototype.subtract = function(v) {\n    return new Vertex(v.x - this.x, v.y - this.y, v.z - this.z);\n  }\n\n  Vertex.prototype.crossproduct = function(v) {\n    return new Vertex((this.y * v.z) - (this.z * v.y), (this.z * v.x) - (this.x * v.z), (this.x * v.y) - (this.y * v.x));\n  }\n\n  Vertex.prototype.equals = function(v) {\n    return (this.x === v.x && this.y === v.y && this.z === v.z);\n  }\n\n  // Plane3D and Point2D\n\n  // IN: Face face\n  function Plane3D (face) {\n    var p1 = face.verts[0];\n    var p2 = face.verts[1];\n    var p3 = face.verts[2];\n    this.a = p1.y * (p2.z-p3.z) + p2.y * (p3.z-p1.z) + p3.y * (p1.z-p2.z);\n    this.b = p1.z * (p2.x-p3.x) + p2.z * (p3.x-p1.x) + p3.z * (p1.x-p2.x);\n    this.c = p1.x * (p2.y-p3.y) + p2.x * (p3.y-p1.y) + p3.x * (p1.y-p2.y);\n    this.d = -1 * (p1.x * (p2.y*p3.z - p3.y*p2.z) + p2.x * (p3.y*p1.z - p1.y*p3.z) + p3.x * (p1.y*p2.z - p2.y*p1.z));\t\n  }\n\n  Plane3D.prototype.getNormZPlane = function() {\n    return [\n      -1 * (this.a / this.c),\n      -1 * (this.b / this.c),\n      -1 * (this.d / this.c)\n    ];\n  }\n\n  // OUT: point2D\n  Plane3D.prototype.getDualPointMappedToPlane = function() {\n    var nplane = this.getNormZPlane();\n    var dualPoint = new Point2D(nplane[0]/2, nplane[1]/2);\n    return dualPoint;\n  }\n\n  // IN: doubles x and y\n  function Point2D (x, y) {\n    this.x = x;\n    this.y = y;\n  }\n\n  // Vector\n\n  // IN: coordinates x, y, z\n  function Vector (x, y, z) {\n    this.x = x;\n    this.y = y;\n    this.z = z;\n  }\n\n  Vector.prototype.negate = function() {\n    this.x *= -1;\n    this.y *= -1;\n    this.z *= -1;\n  }\n\n  // Normalizes X Y and Z in-place\n  Vector.prototype.normalize = function() {\n    var lenght = Math.sqrt((this.x * this.x) + (this.y * this.y) + (this.z * this.z));\n    if (lenght > 0) {\n      this.x /= lenght;\n      this.y /= lenght;\n      this.z /= lenght;\n    }\n  }\n\n  // HEdge\n\n  // IN: vertex orig, vertex dest, Face face\n  function HEdge (orig, dest, face) {\n    this.next = null;\n    this.prev = null;\n    this.twin = null;\n    this.orig = orig;\n    this.dest = dest;\n    this.iFace = face;\n  }\n\n  HEdge.prototype.isHorizon = function() {\n    return this.twin !== null && !this.iFace.marked && this.twin.iFace.marked;\n  }\n\n  // IN: array horizon\n  HEdge.prototype.findHorizon = function(horizon) {\n    if (this.isHorizon()) {\n      if (horizon.length > 0 && this === horizon[0]) {\n        return;\n      } else {\n        horizon.push(this);\n        this.next.findHorizon(horizon);\n      }\n    } else {\n      if (this.twin !== null) {\n        this.twin.next.findHorizon(horizon);\n      }\n    }\n  }\n\n  // IN: vertices origin and dest\n  HEdge.prototype.isEqual = function(origin, dest) {\n    return ((this.orig.equals(origin) && this.dest.equals(dest)) || (this.orig.equals(dest) && this.dest.equals(origin)));\n  }\n\n  // from https://stackoverflow.com/questions/1382107/whats-a-good-way-to-extend-error-in-javascript\n  // (above link provided by https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Error)\n\n  function d3WeightedVoronoiError(message) {\n    this.message = message;\n    this.stack = new Error().stack;\n  }\n\n  d3WeightedVoronoiError.prototype.name = 'd3WeightedVoronoiError';\n  d3WeightedVoronoiError.prototype = new Error();\n\n  // IN: Vertices a, b, c\n  function Face(a, b, c, orient) {\n    this.conflicts = new ConflictList(true);\n    this.verts = [a, b, c];\n    this.marked = false;\n    var t = a.subtract(b).crossproduct(b.subtract(c));\n\n    this.normal = new Vector(-t.x, -t.y, -t.z);\n    this.normal.normalize();\n    this.createEdges();\n    this.dualPoint = null;\n\n    if (orient != undefined) {\n      this.orient(orient);\n    }\n  }\n\n  // OUT: Point2D\n  Face.prototype.getDualPoint = function () {\n    if (this.dualPoint == null) {\n      var plane3d = new Plane3D(this);\n      this.dualPoint = plane3d.getDualPointMappedToPlane();\n    }\n    return this.dualPoint;\n  };\n\n  Face.prototype.isVisibleFromBelow = function () {\n    return this.normal.z < -1.4259414393190911e-9;\n  };\n\n  Face.prototype.createEdges = function () {\n    this.edges = [];\n    this.edges[0] = new HEdge(this.verts[0], this.verts[1], this);\n    this.edges[1] = new HEdge(this.verts[1], this.verts[2], this);\n    this.edges[2] = new HEdge(this.verts[2], this.verts[0], this);\n    this.edges[0].next = this.edges[1];\n    this.edges[0].prev = this.edges[2];\n    this.edges[1].next = this.edges[2];\n    this.edges[1].prev = this.edges[0];\n    this.edges[2].next = this.edges[0];\n    this.edges[2].prev = this.edges[1];\n  };\n\n  // IN: vertex orient\n  Face.prototype.orient = function (orient) {\n    if (!(dot(this.normal, orient) < dot(this.normal, this.verts[0]))) {\n      var temp = this.verts[1];\n      this.verts[1] = this.verts[2];\n      this.verts[2] = temp;\n      this.normal.negate();\n      this.createEdges();\n    }\n  };\n\n  // IN: two vertices v0 and v1\n  Face.prototype.getEdge = function (v0, v1) {\n    for (var i = 0; i < 3; i++) {\n      if (this.edges[i].isEqual(v0, v1)) {\n        return this.edges[i];\n      }\n    }\n    return null;\n  };\n\n  // IN: Face face, vertices v0 and v1\n  Face.prototype.link = function (face, v0, v1) {\n    if (face instanceof Face) {\n      var twin = face.getEdge(v0, v1);\n      if (twin === null) {\n        throw new d3WeightedVoronoiError('when linking, twin is null');\n      }\n      var edge = this.getEdge(v0, v1);\n      if (edge === null) {\n        throw new d3WeightedVoronoiError('when linking, twin is null');\n      }\n      twin.twin = edge;\n      edge.twin = twin;\n    } else {\n      var twin = face; // face is a hEdge\n      var edge = this.getEdge(twin.orig, twin.dest);\n      twin.twin = edge;\n      edge.twin = twin;\n    }\n  };\n\n  // IN: vertex v\n  Face.prototype.conflict = function (v) {\n    return dot(this.normal, v) > dot(this.normal, this.verts[0]) + epsilon;\n  };\n\n  Face.prototype.getHorizon = function () {\n    for (var i = 0; i < 3; i++) {\n      if (this.edges[i].twin !== null && this.edges[i].twin.isHorizon()) {\n        return this.edges[i];\n      }\n    }\n    return null;\n  };\n\n  Face.prototype.removeConflict = function () {\n    this.conflicts.removeAll();\n  };\n\n  function ConvexHull() {\n    this.points = [];\n    this.facets = [];\n    this.created = [];\n    this.horizon = [];\n    this.visible = [];\n    this.current = 0;\n  }\n\n  // IN: sites (x,y,z)\n  ConvexHull.prototype.init = function (boundingSites, sites) {\n    this.points = [];\n    for (var i = 0; i < sites.length; i++) {\n      this.points[i] = new Vertex(sites[i].x, sites[i].y, sites[i].z, null, sites[i], false);\n    }\n    this.points = this.points.concat(boundingSites);\n  };\n\n  ConvexHull.prototype.permutate = function () {\n    var pointSize = this.points.length;\n    for (var i = pointSize - 1; i > 0; i--) {\n      var ra = Math.floor(Math.random() * i);\n      var temp = this.points[ra];\n      temp.index = i;\n      var currentItem = this.points[i];\n      currentItem.index = ra;\n      this.points.splice(ra, 1, currentItem);\n      this.points.splice(i, 1, temp);\n    }\n  };\n\n  (ConvexHull.prototype.prep = function () {\n    if (this.points.length <= 3) {\n      throw new d3WeightedVoronoiError('Less than 4 points');\n    }\n    for (var i = 0; i < this.points.length; i++) {\n      this.points[i].index = i;\n    }\n\n    var v0, v1, v2, v3;\n    var f1, f2, f3, f0;\n    v0 = this.points[0];\n    v1 = this.points[1];\n    v2 = v3 = null;\n\n    // Searching for a third vertex, not aligned with the 2 firsts\n    for (var i = 2; i < this.points.length; i++) {\n      if (!(linearDependent(v0, this.points[i]) && linearDependent(v1, this.points[i]))) {\n        v2 = this.points[i];\n        v2.index = 2;\n        this.points[2].index = i;\n        this.points.splice(i, 1, this.points[2]);\n        this.points.splice(2, 1, v2);\n        break;\n      }\n    }\n    if (v2 === null) {\n      throw new d3WeightedVoronoiError('Not enough non-planar Points (v2 is null)');\n    }\n\n    // Create first JFace\n    f0 = new Face(v0, v1, v2);\n    // Searching for a fourth vertex, not coplanar to the 3 firsts\n    for (var i = 3; i < this.points.length; i++) {\n      if (!epsilonesque(dot(f0.normal, f0.verts[0]) - dot(f0.normal, this.points[i]))) {\n        v3 = this.points[i];\n        v3.index = 3;\n        this.points[3].index = i;\n        this.points.splice(i, 1, this.points[3]);\n        this.points.splice(3, 1, v3);\n        break;\n      }\n    }\n    if (v3 === null) {\n      throw new d3WeightedVoronoiError('Not enough non-planar Points (v3 is null)');\n    }\n\n    f0.orient(v3);\n    f1 = new Face(v0, v2, v3, v1);\n    f2 = new Face(v0, v1, v3, v2);\n    f3 = new Face(v1, v2, v3, v0);\n    this.addFacet(f0);\n    this.addFacet(f1);\n    this.addFacet(f2);\n    this.addFacet(f3);\n    // Connect facets\n    f0.link(f1, v0, v2);\n    f0.link(f2, v0, v1);\n    f0.link(f3, v1, v2);\n    f1.link(f2, v0, v3);\n    f1.link(f3, v2, v3);\n    f2.link(f3, v3, v1);\n    this.current = 4;\n\n    var v;\n    for (var i = this.current; i < this.points.length; i++) {\n      v = this.points[i];\n      if (f0.conflict(v)) {\n        this.addConflict(f0, v);\n      }\n      if (f1.conflict(v)) {\n        this.addConflict(f1, v);\n      }\n      if (f2.conflict(v)) {\n        this.addConflict(f2, v);\n      }\n      if (f3.conflict(v)) {\n        this.addConflict(f3, v);\n      }\n    }\n  }),\n    // IN: Faces old1 old2 and fn\n    (ConvexHull.prototype.addConflicts = function (old1, old2, fn) {\n      var l1 = old1.conflicts.getVertices();\n      var l2 = old2.conflicts.getVertices();\n      var nCL = [];\n      var v1, v2;\n      var i, l;\n      i = l = 0;\n      // Fill the possible new Conflict List\n      while (i < l1.length || l < l2.length) {\n        if (i < l1.length && l < l2.length) {\n          v1 = l1[i];\n          v2 = l2[l];\n          // If the index is the same, it's the same vertex and only 1 has to be added\n          if (v1.index === v2.index) {\n            nCL.push(v1);\n            i++;\n            l++;\n          } else if (v1.index > v2.index) {\n            nCL.push(v1);\n            i++;\n          } else {\n            nCL.push(v2);\n            l++;\n          }\n        } else if (i < l1.length) {\n          nCL.push(l1[i++]);\n        } else {\n          nCL.push(l2[l++]);\n        }\n      }\n      // Check if the possible conflicts are real conflicts\n      for (var i = nCL.length - 1; i >= 0; i--) {\n        v1 = nCL[i];\n        if (fn.conflict(v1)) this.addConflict(fn, v1);\n      }\n    });\n\n  // IN: Face face, Vertex v\n  ConvexHull.prototype.addConflict = function (face, vert) {\n    var e = new ConflictListNode(face, vert);\n    face.conflicts.add(e);\n    vert.conflicts.add(e);\n  };\n\n  // IN: Face f\n  ConvexHull.prototype.removeConflict = function (f) {\n    f.removeConflict();\n    var index = f.index;\n    f.index = -1;\n    if (index === this.facets.length - 1) {\n      this.facets.splice(this.facets.length - 1, 1);\n      return;\n    }\n    if (index >= this.facets.length || index < 0) return;\n    var last = this.facets.splice(this.facets.length - 1, 1);\n    last[0].index = index;\n    this.facets.splice(index, 1, last[0]);\n  };\n\n  // IN: Face face\n  ConvexHull.prototype.addFacet = function (face) {\n    face.index = this.facets.length;\n    this.facets.push(face);\n  };\n\n  ConvexHull.prototype.compute = function () {\n    this.prep();\n    while (this.current < this.points.length) {\n      var next = this.points[this.current];\n      if (next.conflicts.isEmpty()) {\n        // No conflict, point in hull\n        this.current++;\n        continue;\n      }\n      this.created = []; // TODO: make sure this is okay and doesn't dangle references\n      this.horizon = [];\n      this.visible = [];\n      // The visible faces are also marked\n      next.conflicts.fill(this.visible);\n      // Horizon edges are orderly added to the horizon list\n      var e;\n      for (var jF = 0; jF < this.visible.length; jF++) {\n        e = this.visible[jF].getHorizon();\n        if (e !== null) {\n          e.findHorizon(this.horizon);\n          break;\n        }\n      }\n      var last = null,\n        first = null;\n      // Iterate over horizon edges and create new faces oriented with the marked face 3rd unused point\n      for (var hEi = 0; hEi < this.horizon.length; hEi++) {\n        var hE = this.horizon[hEi];\n        var fn = new Face(next, hE.orig, hE.dest, hE.twin.next.dest);\n        fn.conflicts = new ConflictList(true);\n        // Add to facet list\n        this.addFacet(fn);\n        this.created.push(fn);\n        // Add new conflicts\n        this.addConflicts(hE.iFace, hE.twin.iFace, fn);\n        // Link the new face with the horizon edge\n        fn.link(hE);\n        if (last !== null) fn.link(last, next, hE.orig);\n        last = fn;\n        if (first === null) first = fn;\n      }\n      // Links the first and the last created JFace\n      if (first !== null && last !== null) {\n        last.link(first, next, this.horizon[0].orig);\n      }\n      if (this.created.length != 0) {\n        // update conflict graph\n        for (var f = 0; f < this.visible.length; f++) {\n          this.removeConflict(this.visible[f]);\n        }\n        this.current++;\n        this.created = [];\n      }\n    }\n    return this.facets;\n  };\n\n  ConvexHull.prototype.clear = function () {\n    this.points = [];\n    this.facets = [];\n    this.created = [];\n    this.horizon = [];\n    this.visible = [];\n    this.current = 0;\n  };\n\n  function polygonClip(clip, subject) {\n    // Version 0.0.0. Copyright 2017 Mike Bostock.\n\n    // Clips the specified subject polygon to the specified clip polygon;\n    // requires the clip polygon to be counterclockwise and convex.\n    // https://en.wikipedia.org/wiki/Sutherland–Hodgman_algorithm\n    // https://observablehq.com/@d3/polygonclip\n\n    var input,\n      closed = polygonClosed(subject),\n      i = -1,\n      n = clip.length - polygonClosed(clip),\n      j,\n      m,\n      a = clip[n - 1],\n      b,\n      c,\n      d,\n      intersection;\n\n    while (++i < n) {\n      input = subject.slice();\n      subject.length = 0;\n      b = clip[i];\n      c = input[(m = input.length - closed) - 1];\n      j = -1;\n      while (++j < m) {\n        d = input[j];\n        if (polygonInside(d, a, b)) {\n          if (!polygonInside(c, a, b)) {\n            intersection = polygonIntersect(c, d, a, b);\n            if (isFinite(intersection[0])) {\n              subject.push(intersection);\n            }\n          }\n          subject.push(d);\n        } else if (polygonInside(c, a, b)) {\n          intersection = polygonIntersect(c, d, a, b);\n          if (isFinite(intersection[0])) {\n            subject.push(intersection);\n          }\n        }\n        c = d;\n      }\n      if (closed) subject.push(subject[0]);\n      a = b;\n    }\n\n    return subject;\n  }\n\n  function polygonInside(p, a, b) {\n    return (b[0] - a[0]) * (p[1] - a[1]) < (b[1] - a[1]) * (p[0] - a[0]);\n  }\n\n  // Intersect two infinite lines cd and ab.\n  // Return Infinity if cd and ab colinear\n  function polygonIntersect(c, d, a, b) {\n    var x1 = c[0],\n      x3 = a[0],\n      x21 = d[0] - x1,\n      x43 = b[0] - x3,\n      y1 = c[1],\n      y3 = a[1],\n      y21 = d[1] - y1,\n      y43 = b[1] - y3,\n      ua = (x43 * (y1 - y3) - y43 * (x1 - x3)) / (y43 * x21 - x43 * y21);\n    return [x1 + ua * x21, y1 + ua * y21];\n  }\n\n  // Returns true if the polygon is closed.\n  function polygonClosed(coordinates) {\n    var a = coordinates[0],\n      b = coordinates[coordinates.length - 1];\n    return !(a[0] - b[0] || a[1] - b[1]);\n  }\n\n  // IN: HEdge edge\n  function getFacesOfDestVertex(edge) {\n    var faces = [];\n    var previous = edge;\n    var first = edge.dest;\n    var site = first.originalObject;\n    var neighbours = [];\n    do {\n      previous = previous.twin.prev;\n      var siteOrigin = previous.orig.originalObject;\n      if (!siteOrigin.isDummy) {\n        neighbours.push(siteOrigin);\n      }\n      var iFace = previous.iFace;\n      if (iFace.isVisibleFromBelow()) {\n        faces.push(iFace);\n      }\n    } while (previous !== edge);\n    site.neighbours = neighbours;\n    return faces;\n  }\n\n  // IN: Omega = convex bounding polygon\n  // IN: S = unique set of sites with weights\n  // OUT: Set of lines making up the voronoi power diagram\n  function computePowerDiagramIntegrated (sites, boundingSites, clippingPolygon) {\n    var convexHull = new ConvexHull();\n    convexHull.clear();\n    convexHull.init(boundingSites, sites);\n\n    var facets = convexHull.compute(sites);\n    var polygons = []; \n    var verticesVisited = [];\n    var facetCount = facets.length;\n\n    for (var i = 0; i < facetCount; i++) {\n      var facet = facets[i];\n      if (facet.isVisibleFromBelow()) {\n        for (var e = 0; e < 3; e++) {\n          // go through the edges and start to build the polygon by going through the double connected edge list\n          var edge = facet.edges[e];\n          var destVertex = edge.dest;\n          var site = destVertex.originalObject; \n\n          if (!verticesVisited[destVertex.index]) {\n            verticesVisited[destVertex.index] = true;\n            if (site.isDummy) {\n              // Check if this is one of the sites making the bounding polygon\n              continue;\n            }\n            // faces around the vertices which correspond to the polygon corner points\n            var faces = getFacesOfDestVertex(edge);\n            var protopoly = [];\n            var lastX = null;\n            var lastY = null;\n            var dx = 1;\n            var dy = 1;\n            for (var j = 0; j < faces.length; j++) {\n              var point = faces[j].getDualPoint();\n              var x1 = point.x;\n              var y1 = point.y;\n              if (lastX !== null) {\n                dx = lastX - x1;\n                dy = lastY - y1;\n                if (dx < 0) {\n                  dx = -dx;\n                }\n                if (dy < 0) {\n                  dy = -dy;\n                }\n              }\n              if (dx > epsilon || dy > epsilon) {\n                protopoly.push([x1, y1]);\n                lastX = x1;\n                lastY = y1;\n              }\n            }\n            \n            site.nonClippedPolygon = protopoly.reverse();\n            if (!site.isDummy && d3Polygon.polygonLength(site.nonClippedPolygon) > 0) {\n              var clippedPoly = polygonClip(clippingPolygon, site.nonClippedPolygon);\n              site.polygon = clippedPoly;\n              clippedPoly.site = site;\n              if (clippedPoly.length > 0) {\n                polygons.push(clippedPoly);\n              }\n            }\n          }\n        }\n      }\n    }\n    return polygons;\n  }\n\n  function weightedVoronoi() {\n    /////// Inputs ///////\n    var x = function (d) {\n      return d.x;\n    }; // accessor to the x value\n    var y = function (d) {\n      return d.y;\n    }; // accessor to the y value\n    var weight = function (d) {\n      return d.weight;\n    }; // accessor to the weight\n    var clip = [\n      [0, 0],\n      [0, 1],\n      [1, 1],\n      [1, 0],\n    ]; // clipping polygon\n    var extent = [\n      [0, 0],\n      [1, 1],\n    ]; // extent of the clipping polygon\n    var size = [1, 1]; // [width, height] of the clipping polygon\n\n    ///////////////////////\n    ///////// API /////////\n    ///////////////////////\n\n    function _weightedVoronoi(data) {\n      var formatedSites;\n\n      //begin: map sites to the expected format of PowerDiagram\n      formatedSites = data.map(function (d) {\n        return new Vertex(x(d), y(d), null, weight(d), d, false);\n      });\n      //end: map sites to the expected format of PowerDiagram\n\n      return computePowerDiagramIntegrated(formatedSites, boundingSites(), clip);\n    }\n\n    _weightedVoronoi.x = function (_) {\n      if (!arguments.length) {\n        return x;\n      }\n\n      x = _;\n      return _weightedVoronoi;\n    };\n\n    _weightedVoronoi.y = function (_) {\n      if (!arguments.length) {\n        return y;\n      }\n\n      y = _;\n      return _weightedVoronoi;\n    };\n\n    _weightedVoronoi.weight = function (_) {\n      if (!arguments.length) {\n        return weight;\n      }\n\n      weight = _;\n      return _weightedVoronoi;\n    };\n\n    _weightedVoronoi.clip = function (_) {\n      var direction, xExtent, yExtent;\n\n      if (!arguments.length) {\n        return clip;\n      }\n\n      xExtent = d3Array.extent(\n        _.map(function (c) {\n          return c[0];\n        })\n      );\n      yExtent = d3Array.extent(\n        _.map(function (c) {\n          return c[1];\n        })\n      );\n      direction = polygonDirection(_);\n      if (direction === undefined) {\n        clip = d3Polygon.polygonHull(_); // ensure clip to be a convex, hole-free, counterclockwise polygon\n      } else if (direction === 1) {\n        clip = _.reverse(); // already convex, order array in the same direction as d3-polygon.polygonHull(...)\n      } else {\n        clip = _;\n      }\n      extent = [\n        [xExtent[0], yExtent[0]],\n        [xExtent[1], yExtent[1]],\n      ];\n      size = [xExtent[1] - xExtent[0], yExtent[1] - yExtent[0]];\n      return _weightedVoronoi;\n    };\n\n    _weightedVoronoi.extent = function (_) {\n      if (!arguments.length) {\n        return extent;\n      }\n\n      clip = [_[0], [_[0][0], _[1][1]], _[1], [_[1][0], _[0][1]]];\n      extent = _;\n      size = [_[1][0] - _[0][0], _[1][1] - _[0][1]];\n      return _weightedVoronoi;\n    };\n\n    _weightedVoronoi.size = function (_) {\n      if (!arguments.length) {\n        return size;\n      }\n\n      clip = [\n        [0, 0],\n        [0, _[1]],\n        [_[0], _[1]],\n        [_[0], 0],\n      ];\n      extent = [[0, 0], _];\n      size = _;\n      return _weightedVoronoi;\n    };\n\n    ///////////////////////\n    /////// Private ///////\n    ///////////////////////\n\n    function boundingSites() {\n      var minX,\n        maxX,\n        minY,\n        maxY,\n        width,\n        height,\n        x0,\n        x1,\n        y0,\n        y1,\n        boundingData = [],\n        boundingSites = [];\n\n      minX = extent[0][0];\n      maxX = extent[1][0];\n      minY = extent[0][1];\n      maxY = extent[1][1];\n      width = maxX - minX;\n      height = maxY - minY;\n      x0 = minX - width;\n      x1 = maxX + width;\n      y0 = minY - height;\n      y1 = maxY + height;\n\n      // MUST be counterclockwise\n      // if not, may produce 'TypeError: Cannot set property 'twin' of null' during computation\n      // don't know how to test as it is not exposed\n      boundingData[0] = [x0, y0];\n      boundingData[1] = [x0, y1];\n      boundingData[2] = [x1, y1];\n      boundingData[3] = [x1, y0];\n\n      for (var i = 0; i < 4; i++) {\n        boundingSites.push(\n          new Vertex(\n            boundingData[i][0],\n            boundingData[i][1],\n            null,\n            epsilon,\n            new Vertex(boundingData[i][0], boundingData[i][1], null, epsilon, null, true),\n            true\n          )\n        );\n      }\n\n      return boundingSites;\n    }\n\n    return _weightedVoronoi;\n  }\n\n  exports.weightedVoronoi = weightedVoronoi;\n  exports.d3WeightedVoronoiError = d3WeightedVoronoiError;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n}));", "export default function(a, b) {\n  return a < b ? -1 : a > b ? 1 : a >= b ? 0 : NaN;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function(f) {\n  let delta = f;\n  let compare = f;\n\n  if (f.length === 1) {\n    delta = (d, x) => f(d) - x;\n    compare = ascendingComparator(f);\n  }\n\n  function left(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) < 0) lo = mid + 1;\n      else hi = mid;\n    }\n    return lo;\n  }\n\n  function right(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    while (lo < hi) {\n      const mid = (lo + hi) >>> 1;\n      if (compare(a[mid], x) > 0) hi = mid;\n      else lo = mid + 1;\n    }\n    return lo;\n  }\n\n  function center(a, x, lo, hi) {\n    if (lo == null) lo = 0;\n    if (hi == null) hi = a.length;\n    const i = left(a, x, lo, hi - 1);\n    return i > lo && delta(a[i - 1], x) > -delta(a[i], x) ? i - 1 : i;\n  }\n\n  return {left, center, right};\n}\n\nfunction ascendingComparator(f) {\n  return (d, x) => ascending(f(d), x);\n}\n", "export default function(x) {\n  return x === null ? NaN : +x;\n}\n\nexport function* numbers(values, valueof) {\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        yield value;\n      }\n    }\n  }\n}\n", "import ascending from \"./ascending.js\";\nimport bisector from \"./bisector.js\";\nimport number from \"./number.js\";\n\nconst ascendingBisect = bisector(ascending);\nexport const bisectRight = ascendingBisect.right;\nexport const bisectLeft = ascendingBisect.left;\nexport const bisectCenter = bisector(number).center;\nexport default bisectRight;\n", "export default function count(values, valueof) {\n  let count = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count;\n      }\n    }\n  }\n  return count;\n}\n", "function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n", "export default function cumsum(values, valueof) {\n  var sum = 0, index = 0;\n  return Float64Array.from(values, valueof === undefined\n    ? v => (sum += +v || 0)\n    : v => (sum += +valueof(v, index++, values) || 0));\n}", "export default function(a, b) {\n  return b < a ? -1 : b > a ? 1 : b >= a ? 0 : NaN;\n}\n", "export default function variance(values, valueof) {\n  let count = 0;\n  let delta;\n  let mean = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        delta = value - mean;\n        mean += delta / ++count;\n        sum += delta * (value - mean);\n      }\n    }\n  }\n  if (count > 1) return sum / (count - 1);\n}\n", "import variance from \"./variance.js\";\n\nexport default function deviation(values, valueof) {\n  const v = variance(values, valueof);\n  return v ? Math.sqrt(v) : v;\n}\n", "export default function(values, valueof) {\n  let min;\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null) {\n        if (min === undefined) {\n          if (value >= value) min = max = value;\n        } else {\n          if (min > value) min = value;\n          if (max < value) max = value;\n        }\n      }\n    }\n  }\n  return [min, max];\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export class InternMap extends Map {\n  constructor(entries, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (entries != null) for (const [key, value] of entries) this.set(key, value);\n  }\n  get(key) {\n    return super.get(intern_get(this, key));\n  }\n  has(key) {\n    return super.has(intern_get(this, key));\n  }\n  set(key, value) {\n    return super.set(intern_set(this, key), value);\n  }\n  delete(key) {\n    return super.delete(intern_delete(this, key));\n  }\n}\n\nexport class InternSet extends Set {\n  constructor(values, key = keyof) {\n    super();\n    Object.defineProperties(this, {_intern: {value: new Map()}, _key: {value: key}});\n    if (values != null) for (const value of values) this.add(value);\n  }\n  has(value) {\n    return super.has(intern_get(this, value));\n  }\n  add(value) {\n    return super.add(intern_set(this, value));\n  }\n  delete(value) {\n    return super.delete(intern_delete(this, value));\n  }\n}\n\nfunction intern_get({_intern, _key}, value) {\n  const key = _key(value);\n  return _intern.has(key) ? _intern.get(key) : value;\n}\n\nfunction intern_set({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) return _intern.get(key);\n  _intern.set(key, value);\n  return value;\n}\n\nfunction intern_delete({_intern, _key}, value) {\n  const key = _key(value);\n  if (_intern.has(key)) {\n    value = _intern.get(value);\n    _intern.delete(key);\n  }\n  return value;\n}\n\nfunction keyof(value) {\n  return value !== null && typeof value === \"object\" ? value.valueOf() : value;\n}\n", "export default function(x) {\n  return x;\n}\n", "import {InternMap} from \"internmap\";\nimport identity from \"./identity.js\";\n\nexport default function group(values, ...keys) {\n  return nest(values, identity, identity, keys);\n}\n\nexport function groups(values, ...keys) {\n  return nest(values, Array.from, identity, keys);\n}\n\nexport function rollup(values, reduce, ...keys) {\n  return nest(values, identity, reduce, keys);\n}\n\nexport function rollups(values, reduce, ...keys) {\n  return nest(values, Array.from, reduce, keys);\n}\n\nexport function index(values, ...keys) {\n  return nest(values, identity, unique, keys);\n}\n\nexport function indexes(values, ...keys) {\n  return nest(values, Array.from, unique, keys);\n}\n\nfunction unique(values) {\n  if (values.length !== 1) throw new Error(\"duplicate key\");\n  return values[0];\n}\n\nfunction nest(values, map, reduce, keys) {\n  return (function regroup(values, i) {\n    if (i >= keys.length) return reduce(values);\n    const groups = new InternMap();\n    const keyof = keys[i++];\n    let index = -1;\n    for (const value of values) {\n      const key = keyof(value, ++index, values);\n      const group = groups.get(key);\n      if (group) group.push(value);\n      else groups.set(key, [value]);\n    }\n    for (const [key, values] of groups) {\n      groups.set(key, regroup(values, i));\n    }\n    return map(groups);\n  })(values, 0);\n}\n", "export default function(source, keys) {\n  return Array.from(keys, key => source[key]);\n}\n", "import ascending from \"./ascending.js\";\nimport permute from \"./permute.js\";\n\nexport default function sort(values, ...F) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  values = Array.from(values);\n  let [f = ascending] = F;\n  if (f.length === 1 || F.length > 1) {\n    const index = Uint32Array.from(values, (d, i) => i);\n    if (F.length > 1) {\n      F = F.map(f => values.map(f));\n      index.sort((i, j) => {\n        for (const f of F) {\n          const c = ascending(f[i], f[j]);\n          if (c) return c;\n        }\n      });\n    } else {\n      f = values.map(f);\n      index.sort((i, j) => ascending(f[i], f[j]));\n    }\n    return permute(values, index);\n  }\n  return values.sort(f);\n}\n", "import ascending from \"./ascending.js\";\nimport group, {rollup} from \"./group.js\";\nimport sort from \"./sort.js\";\n\nexport default function groupSort(values, reduce, key) {\n  return (reduce.length === 1\n    ? sort(rollup(values, reduce, key), (([ak, av], [bk, bv]) => ascending(av, bv) || ascending(ak, bk)))\n    : sort(group(values, key), (([ak, av], [bk, bv]) => reduce(av, bv) || ascending(ak, bk))))\n    .map(([key]) => key);\n}\n", "var array = Array.prototype;\n\nexport var slice = array.slice;\nexport var map = array.map;\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "var e10 = Math.sqrt(50),\n    e5 = Math.sqrt(10),\n    e2 = Math.sqrt(2);\n\nexport default function(start, stop, count) {\n  var reverse,\n      i = -1,\n      n,\n      ticks,\n      step;\n\n  stop = +stop, start = +start, count = +count;\n  if (start === stop && count > 0) return [start];\n  if (reverse = stop < start) n = start, start = stop, stop = n;\n  if ((step = tickIncrement(start, stop, count)) === 0 || !isFinite(step)) return [];\n\n  if (step > 0) {\n    let r0 = Math.round(start / step), r1 = Math.round(stop / step);\n    if (r0 * step < start) ++r0;\n    if (r1 * step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) * step;\n  } else {\n    step = -step;\n    let r0 = Math.round(start * step), r1 = Math.round(stop * step);\n    if (r0 / step < start) ++r0;\n    if (r1 / step > stop) --r1;\n    ticks = new Array(n = r1 - r0 + 1);\n    while (++i < n) ticks[i] = (r0 + i) / step;\n  }\n\n  if (reverse) ticks.reverse();\n\n  return ticks;\n}\n\nexport function tickIncrement(start, stop, count) {\n  var step = (stop - start) / Math.max(0, count),\n      power = Math.floor(Math.log(step) / Math.LN10),\n      error = step / Math.pow(10, power);\n  return power >= 0\n      ? (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1) * Math.pow(10, power)\n      : -Math.pow(10, -power) / (error >= e10 ? 10 : error >= e5 ? 5 : error >= e2 ? 2 : 1);\n}\n\nexport function tickStep(start, stop, count) {\n  var step0 = Math.abs(stop - start) / Math.max(0, count),\n      step1 = Math.pow(10, Math.floor(Math.log(step0) / Math.LN10)),\n      error = step0 / step1;\n  if (error >= e10) step1 *= 10;\n  else if (error >= e5) step1 *= 5;\n  else if (error >= e2) step1 *= 2;\n  return stop < start ? -step1 : step1;\n}\n", "import {tickIncrement} from \"./ticks.js\";\n\nexport default function nice(start, stop, count) {\n  let prestep;\n  while (true) {\n    const step = tickIncrement(start, stop, count);\n    if (step === prestep || step === 0 || !isFinite(step)) {\n      return [start, stop];\n    } else if (step > 0) {\n      start = Math.floor(start / step) * step;\n      stop = Math.ceil(stop / step) * step;\n    } else if (step < 0) {\n      start = Math.ceil(start * step) / step;\n      stop = Math.floor(stop * step) / step;\n    }\n    prestep = step;\n  }\n}\n", "import count from \"../count.js\";\n\nexport default function(values) {\n  return Math.ceil(Math.log(count(values)) / Math.LN2) + 1;\n}\n", "import {slice} from \"./array.js\";\nimport bisect from \"./bisect.js\";\nimport constant from \"./constant.js\";\nimport extent from \"./extent.js\";\nimport identity from \"./identity.js\";\nimport nice from \"./nice.js\";\nimport ticks, {tickIncrement} from \"./ticks.js\";\nimport sturges from \"./threshold/sturges.js\";\n\nexport default function() {\n  var value = identity,\n      domain = extent,\n      threshold = sturges;\n\n  function histogram(data) {\n    if (!Array.isArray(data)) data = Array.from(data);\n\n    var i,\n        n = data.length,\n        x,\n        values = new Array(n);\n\n    for (i = 0; i < n; ++i) {\n      values[i] = value(data[i], i, data);\n    }\n\n    var xz = domain(values),\n        x0 = xz[0],\n        x1 = xz[1],\n        tz = threshold(values, x0, x1);\n\n    // Convert number of thresholds into uniform thresholds, and nice the\n    // default domain accordingly.\n    if (!Array.isArray(tz)) {\n      const max = x1, tn = +tz;\n      if (domain === extent) [x0, x1] = nice(x0, x1, tn);\n      tz = ticks(x0, x1, tn);\n\n      // If the last threshold is coincident with the domain’s upper bound, the\n      // last bin will be zero-width. If the default domain is used, and this\n      // last threshold is coincident with the maximum input value, we can\n      // extend the niced upper bound by one tick to ensure uniform bin widths;\n      // otherwise, we simply remove the last threshold. Note that we don’t\n      // coerce values or the domain to numbers, and thus must be careful to\n      // compare order (>=) rather than strict equality (===)!\n      if (tz[tz.length - 1] >= x1) {\n        if (max >= x1 && domain === extent) {\n          const step = tickIncrement(x0, x1, tn);\n          if (isFinite(step)) {\n            if (step > 0) {\n              x1 = (Math.floor(x1 / step) + 1) * step;\n            } else if (step < 0) {\n              x1 = (Math.ceil(x1 * -step) + 1) / -step;\n            }\n          }\n        } else {\n          tz.pop();\n        }\n      }\n    }\n\n    // Remove any thresholds outside the domain.\n    var m = tz.length;\n    while (tz[0] <= x0) tz.shift(), --m;\n    while (tz[m - 1] > x1) tz.pop(), --m;\n\n    var bins = new Array(m + 1),\n        bin;\n\n    // Initialize bins.\n    for (i = 0; i <= m; ++i) {\n      bin = bins[i] = [];\n      bin.x0 = i > 0 ? tz[i - 1] : x0;\n      bin.x1 = i < m ? tz[i] : x1;\n    }\n\n    // Assign data to bins by value, ignoring any outside the domain.\n    for (i = 0; i < n; ++i) {\n      x = values[i];\n      if (x0 <= x && x <= x1) {\n        bins[bisect(tz, x, 0, m)].push(data[i]);\n      }\n    }\n\n    return bins;\n  }\n\n  histogram.value = function(_) {\n    return arguments.length ? (value = typeof _ === \"function\" ? _ : constant(_), histogram) : value;\n  };\n\n  histogram.domain = function(_) {\n    return arguments.length ? (domain = typeof _ === \"function\" ? _ : constant([_[0], _[1]]), histogram) : domain;\n  };\n\n  histogram.thresholds = function(_) {\n    return arguments.length ? (threshold = typeof _ === \"function\" ? _ : Array.isArray(_) ? constant(slice.call(_)) : constant(_), histogram) : threshold;\n  };\n\n  return histogram;\n}\n", "export default function max(values, valueof) {\n  let max;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value;\n      }\n    }\n  }\n  return max;\n}\n", "export default function min(values, valueof) {\n  let min;\n  if (valueof === undefined) {\n    for (const value of values) {\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\n\n// Based on https://github.com/mourner/quickselect\n// ISC license, Copyright 2018 Vladimir Agafonkin.\nexport default function quickselect(array, k, left = 0, right = array.length - 1, compare = ascending) {\n  while (right > left) {\n    if (right - left > 600) {\n      const n = right - left + 1;\n      const m = k - left + 1;\n      const z = Math.log(n);\n      const s = 0.5 * Math.exp(2 * z / 3);\n      const sd = 0.5 * Math.sqrt(z * s * (n - s) / n) * (m - n / 2 < 0 ? -1 : 1);\n      const newLeft = Math.max(left, Math.floor(k - m * s / n + sd));\n      const newRight = Math.min(right, Math.floor(k + (n - m) * s / n + sd));\n      quickselect(array, k, newLeft, newRight, compare);\n    }\n\n    const t = array[k];\n    let i = left;\n    let j = right;\n\n    swap(array, left, k);\n    if (compare(array[right], t) > 0) swap(array, left, right);\n\n    while (i < j) {\n      swap(array, i, j), ++i, --j;\n      while (compare(array[i], t) < 0) ++i;\n      while (compare(array[j], t) > 0) --j;\n    }\n\n    if (compare(array[left], t) === 0) swap(array, left, j);\n    else ++j, swap(array, j, right);\n\n    if (j <= k) left = j + 1;\n    if (k <= j) right = j - 1;\n  }\n  return array;\n}\n\nfunction swap(array, i, j) {\n  const t = array[i];\n  array[i] = array[j];\n  array[j] = t;\n}\n", "import max from \"./max.js\";\nimport min from \"./min.js\";\nimport quickselect from \"./quickselect.js\";\nimport number, {numbers} from \"./number.js\";\n\nexport default function quantile(values, p, valueof) {\n  values = Float64Array.from(numbers(values, valueof));\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return min(values);\n  if (p >= 1) return max(values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = max(quickselect(values, i0).subarray(0, i0 + 1)),\n      value1 = min(values.subarray(i0 + 1));\n  return value0 + (value1 - value0) * (i - i0);\n}\n\nexport function quantileSorted(values, p, valueof = number) {\n  if (!(n = values.length)) return;\n  if ((p = +p) <= 0 || n < 2) return +valueof(values[0], 0, values);\n  if (p >= 1) return +valueof(values[n - 1], n - 1, values);\n  var n,\n      i = (n - 1) * p,\n      i0 = Math.floor(i),\n      value0 = +valueof(values[i0], i0, values),\n      value1 = +valueof(values[i0 + 1], i0 + 1, values);\n  return value0 + (value1 - value0) * (i - i0);\n}\n", "import count from \"../count.js\";\nimport quantile from \"../quantile.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (2 * (quantile(values, 0.75) - quantile(values, 0.25)) * Math.pow(count(values), -1 / 3)));\n}\n", "import count from \"../count.js\";\nimport deviation from \"../deviation.js\";\n\nexport default function(values, min, max) {\n  return Math.ceil((max - min) / (3.5 * deviation(values) * Math.pow(count(values), -1 / 3)));\n}\n", "export default function maxIndex(values, valueof) {\n  let max;\n  let maxIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (max < value || (max === undefined && value >= value))) {\n        max = value, maxIndex = index;\n      }\n    }\n  }\n  return maxIndex;\n}\n", "export default function mean(values, valueof) {\n  let count = 0;\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null && (value = +value) >= value) {\n        ++count, sum += value;\n      }\n    }\n  }\n  if (count) return sum / count;\n}\n", "import quantile from \"./quantile.js\";\n\nexport default function(values, valueof) {\n  return quantile(values, 0.5, valueof);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "export default function minIndex(values, valueof) {\n  let min;\n  let minIndex = -1;\n  let index = -1;\n  if (valueof === undefined) {\n    for (const value of values) {\n      ++index;\n      if (value != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  } else {\n    for (let value of values) {\n      if ((value = valueof(value, ++index, values)) != null\n          && (min > value || (min === undefined && value >= value))) {\n        min = value, minIndex = index;\n      }\n    }\n  }\n  return minIndex;\n}\n", "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n", "export default function(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function least(values, compare = ascending) {\n  let min;\n  let defined = false;\n  if (compare.length === 1) {\n    let minValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, minValue) < 0\n          : ascending(value, value) === 0) {\n        min = element;\n        minValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, min) < 0\n          : compare(value, value) === 0) {\n        min = value;\n        defined = true;\n      }\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\nimport minIndex from \"./minIndex.js\";\n\nexport default function leastIndex(values, compare = ascending) {\n  if (compare.length === 1) return minIndex(values, compare);\n  let minValue;\n  let min = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (min < 0\n        ? compare(value, value) === 0\n        : compare(value, minValue) < 0) {\n      minValue = value;\n      min = index;\n    }\n  }\n  return min;\n}\n", "import ascending from \"./ascending.js\";\n\nexport default function greatest(values, compare = ascending) {\n  let max;\n  let defined = false;\n  if (compare.length === 1) {\n    let maxValue;\n    for (const element of values) {\n      const value = compare(element);\n      if (defined\n          ? ascending(value, maxValue) > 0\n          : ascending(value, value) === 0) {\n        max = element;\n        maxValue = value;\n        defined = true;\n      }\n    }\n  } else {\n    for (const value of values) {\n      if (defined\n          ? compare(value, max) > 0\n          : compare(value, value) === 0) {\n        max = value;\n        defined = true;\n      }\n    }\n  }\n  return max;\n}\n", "import ascending from \"./ascending.js\";\nimport maxIndex from \"./maxIndex.js\";\n\nexport default function greatestIndex(values, compare = ascending) {\n  if (compare.length === 1) return maxIndex(values, compare);\n  let maxValue;\n  let max = -1;\n  let index = -1;\n  for (const value of values) {\n    ++index;\n    if (max < 0\n        ? compare(value, value) === 0\n        : compare(value, maxValue) > 0) {\n      maxValue = value;\n      max = index;\n    }\n  }\n  return max;\n}\n", "import leastIndex from \"./leastIndex.js\";\n\nexport default function scan(values, compare) {\n  const index = leastIndex(values, compare);\n  return index < 0 ? undefined : index;\n}\n", "export default shuffler(Math.random);\n\nexport function shuffler(random) {\n  return function shuffle(array, i0 = 0, i1 = array.length) {\n    let m = i1 - (i0 = +i0);\n    while (m) {\n      const i = random() * m-- | 0, t = array[m + i0];\n      array[m + i0] = array[i + i0];\n      array[i + i0] = t;\n    }\n    return array;\n  };\n}\n", "export default function sum(values, valueof) {\n  let sum = 0;\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        sum += value;\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        sum += value;\n      }\n    }\n  }\n  return sum;\n}\n", "import min from \"./min.js\";\n\nexport default function(matrix) {\n  if (!(n = matrix.length)) return [];\n  for (var i = -1, m = min(matrix, length), transpose = new Array(m); ++i < m;) {\n    for (var j = -1, n, row = transpose[i] = new Array(n); ++j < n;) {\n      row[j] = matrix[j][i];\n    }\n  }\n  return transpose;\n}\n\nfunction length(d) {\n  return d.length;\n}\n", "import transpose from \"./transpose.js\";\n\nexport default function() {\n  return transpose(arguments);\n}\n", "export default function every(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (!test(value, ++index, values)) {\n      return false;\n    }\n  }\n  return true;\n}\n", "export default function some(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      return true;\n    }\n  }\n  return false;\n}\n", "export default function filter(values, test) {\n  if (typeof test !== \"function\") throw new TypeError(\"test is not a function\");\n  const array = [];\n  let index = -1;\n  for (const value of values) {\n    if (test(value, ++index, values)) {\n      array.push(value);\n    }\n  }\n  return array;\n}\n", "export default function map(values, mapper) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  if (typeof mapper !== \"function\") throw new TypeError(\"mapper is not a function\");\n  return Array.from(values, (value, index) => mapper(value, index, values));\n}\n", "export default function reduce(values, reducer, value) {\n  if (typeof reducer !== \"function\") throw new TypeError(\"reducer is not a function\");\n  const iterator = values[Symbol.iterator]();\n  let done, next, index = -1;\n  if (arguments.length < 3) {\n    ({done, value} = iterator.next());\n    if (done) return;\n    ++index;\n  }\n  while (({done, value: next} = iterator.next()), !done) {\n    value = reducer(value, next, ++index, values);\n  }\n  return value;\n}\n", "export default function reverse(values) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  return Array.from(values).reverse();\n}\n", "export default function difference(values, ...others) {\n  values = new Set(values);\n  for (const other of others) {\n    for (const value of other) {\n      values.delete(value);\n    }\n  }\n  return values;\n}\n", "export default function disjoint(values, other) {\n  const iterator = other[Symbol.iterator](), set = new Set();\n  for (const v of values) {\n    if (set.has(v)) return false;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) break;\n      if (Object.is(v, value)) return false;\n      set.add(value);\n    }\n  }\n  return true;\n}\n", "export default function set(values) {\n  return values instanceof Set ? values : new Set(values);\n}\n", "import set from \"./set.js\";\n\nexport default function intersection(values, ...others) {\n  values = new Set(values);\n  others = others.map(set);\n  out: for (const value of values) {\n    for (const other of others) {\n      if (!other.has(value)) {\n        values.delete(value);\n        continue out;\n      }\n    }\n  }\n  return values;\n}\n", "export default function superset(values, other) {\n  const iterator = values[Symbol.iterator](), set = new Set();\n  for (const o of other) {\n    if (set.has(o)) continue;\n    let value, done;\n    while (({value, done} = iterator.next())) {\n      if (done) return false;\n      set.add(value);\n      if (Object.is(o, value)) break;\n    }\n  }\n  return true;\n}\n", "import superset from \"./superset.js\";\n\nexport default function subset(values, other) {\n  return superset(other, values);\n}\n", "export default function union(...others) {\n  const set = new Set();\n  for (const other of others) {\n    for (const o of other) {\n      set.add(o);\n    }\n  }\n  return set;\n}\n", "// A library of seedable RNGs implemented in Javascript.\n//\n// Usage:\n//\n// var seedrandom = require('seedrandom');\n// var random = seedrandom(1); // or any seed.\n// var x = random();       // 0 <= x < 1.  Every bit is random.\n// var x = random.quick(); // 0 <= x < 1.  32 bits of randomness.\n\n// alea, a 53-bit multiply-with-carry generator by <PERSON>.\n// Period: ~2^116\n// Reported to pass all BigCrush tests.\nvar alea = require('./lib/alea');\n\n// xor128, a pure xor-shift generator by <PERSON>.\n// Period: 2^128-1.\n// Reported to fail: MatrixRank and LinearComp.\nvar xor128 = require('./lib/xor128');\n\n// xorwow, <PERSON>'s 160-bit xor-shift combined plus weyl.\n// Period: 2^192-2^32\n// Reported to fail: CollisionOver, SimpPoker, and LinearComp.\nvar xorwow = require('./lib/xorwow');\n\n// xorshift7, by <PERSON> and <PERSON>, takes\n// a different approach: it adds robustness by allowing more shifts\n// than Marsaglia's original three.  It is a 7-shift generator\n// with 256 bits, that passes BigCrush with no systmatic failures.\n// Period 2^256-1.\n// No systematic BigCrush failures reported.\nvar xorshift7 = require('./lib/xorshift7');\n\n// xor4096, by Richard Brent, is a 4096-bit xor-shift with a\n// very long period that also adds a Weyl generator. It also passes\n// BigCrush with no systematic failures.  Its long period may\n// be useful if you have many generators and need to avoid\n// collisions.\n// Period: 2^4128-2^32.\n// No systematic BigCrush failures reported.\nvar xor4096 = require('./lib/xor4096');\n\n// Tyche-i, by Samuel Neves and Filipe Araujo, is a bit-shifting random\n// number generator derived from ChaCha, a modern stream cipher.\n// https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n// Period: ~2^127\n// No systematic BigCrush failures reported.\nvar tychei = require('./lib/tychei');\n\n// The original ARC4-based prng included in this library.\n// Period: ~2^1600\nvar sr = require('./seedrandom');\n\nsr.alea = alea;\nsr.xor128 = xor128;\nsr.xorwow = xorwow;\nsr.xorshift7 = xorshift7;\nsr.xor4096 = xor4096;\nsr.tychei = tychei;\n\nmodule.exports = sr;\n", "// A port of an algorithm by <PERSON> <<EMAIL>>, 2010\n// http://baagoe.com/en/RandomMusings/javascript/\n// https://github.com/nquinlan/better-random-numbers-for-javascript-mirror\n// Original work is under MIT license -\n\n// Copyright (C) 2010 by <PERSON> <<EMAIL>>\n//\n// Permission is hereby granted, free of charge, to any person obtaining a copy\n// of this software and associated documentation files (the \"Software\"), to deal\n// in the Software without restriction, including without limitation the rights\n// to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n// copies of the Software, and to permit persons to whom the Software is\n// furnished to do so, subject to the following conditions:\n//\n// The above copyright notice and this permission notice shall be included in\n// all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n// AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n// OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n// THE SOFTWARE.\n\n\n\n(function(global, module, define) {\n\nfunction Alea(seed) {\n  var me = this, mash = Mash();\n\n  me.next = function() {\n    var t = 2091639 * me.s0 + me.c * 2.3283064365386963e-10; // 2^-32\n    me.s0 = me.s1;\n    me.s1 = me.s2;\n    return me.s2 = t - (me.c = t | 0);\n  };\n\n  // Apply the seeding algorithm from Baagoe.\n  me.c = 1;\n  me.s0 = mash(' ');\n  me.s1 = mash(' ');\n  me.s2 = mash(' ');\n  me.s0 -= mash(seed);\n  if (me.s0 < 0) { me.s0 += 1; }\n  me.s1 -= mash(seed);\n  if (me.s1 < 0) { me.s1 += 1; }\n  me.s2 -= mash(seed);\n  if (me.s2 < 0) { me.s2 += 1; }\n  mash = null;\n}\n\nfunction copy(f, t) {\n  t.c = f.c;\n  t.s0 = f.s0;\n  t.s1 = f.s1;\n  t.s2 = f.s2;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new Alea(seed),\n      state = opts && opts.state,\n      prng = xg.next;\n  prng.int32 = function() { return (xg.next() * 0x100000000) | 0; }\n  prng.double = function() {\n    return prng() + (prng() * 0x200000 | 0) * 1.1102230246251565e-16; // 2^-53\n  };\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nfunction Mash() {\n  var n = 0xefc8249d;\n\n  var mash = function(data) {\n    data = String(data);\n    for (var i = 0; i < data.length; i++) {\n      n += data.charCodeAt(i);\n      var h = 0.02519603282416938 * n;\n      n = h >>> 0;\n      h -= n;\n      h *= n;\n      n = h >>> 0;\n      h -= n;\n      n += h * 0x100000000; // 2^32\n    }\n    return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\n  };\n\n  return mash;\n}\n\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.alea = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"Tyche-i\" prng algorithm by\n// <PERSON> and <PERSON><PERSON><PERSON>.\n// See https://eden.dei.uc.pt/~sneves/pubs/2011-snfa2.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var b = me.b, c = me.c, d = me.d, a = me.a;\n    b = (b << 25) ^ (b >>> 7) ^ c;\n    c = (c - d) | 0;\n    d = (d << 24) ^ (d >>> 8) ^ a;\n    a = (a - b) | 0;\n    me.b = b = (b << 20) ^ (b >>> 12) ^ c;\n    me.c = c = (c - d) | 0;\n    me.d = (d << 16) ^ (c >>> 16) ^ a;\n    return me.a = (a - b) | 0;\n  };\n\n  /* The following is non-inverted tyche, which has better internal\n   * bit diffusion, but which is about 25% slower than tyche-i in JS.\n  me.next = function() {\n    var a = me.a, b = me.b, c = me.c, d = me.d;\n    a = (me.a + me.b | 0) >>> 0;\n    d = me.d ^ a; d = d << 16 ^ d >>> 16;\n    c = me.c + d | 0;\n    b = me.b ^ c; b = b << 12 ^ d >>> 20;\n    me.a = a = a + b | 0;\n    d = d ^ a; me.d = d = d << 8 ^ d >>> 24;\n    me.c = c = c + d | 0;\n    b = b ^ c;\n    return me.b = (b << 7 ^ b >>> 25);\n  }\n  */\n\n  me.a = 0;\n  me.b = 0;\n  me.c = 2654435769 | 0;\n  me.d = 1367130551;\n\n  if (seed === Math.floor(seed)) {\n    // Integer seed.\n    me.a = (seed / 0x100000000) | 0;\n    me.b = seed | 0;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 20; k++) {\n    me.b ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.a = f.a;\n  t.b = f.b;\n  t.c = f.c;\n  t.d = f.d;\n  return t;\n};\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.tychei = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of the \"xor128\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n\n  // Set up generator function.\n  me.next = function() {\n    var t = me.x ^ (me.x << 11);\n    me.x = me.y;\n    me.y = me.z;\n    me.z = me.w;\n    return me.w ^= (me.w >>> 19) ^ t ^ (t >>> 8);\n  };\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor128 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "// A Javascript implementaion of <PERSON>'s Xorgens xor4096 algorithm.\n//\n// This fast non-cryptographic random number generator is designed for\n// use in Monte-Carlo algorithms. It combines a long-period xorshift\n// generator with a Weyl generator, and it passes all common batteries\n// of stasticial tests for randomness while consuming only a few nanoseconds\n// for each prng generated.  For background on the generator, see <PERSON>'s\n// paper: \"Some long-period random number generators using shifts and xors.\"\n// http://arxiv.org/pdf/1004.3115v1.pdf\n//\n// Usage:\n//\n// var xor4096 = require('xor4096');\n// random = xor4096(1);                        // Seed with int32 or string.\n// assert.equal(random(), 0.1520436450538547); // (0, 1) range, 53 bits.\n// assert.equal(random.int32(), 1806534897);   // signed int32, 32 bits.\n//\n// For nonzero numeric keys, this impelementation provides a sequence\n// identical to that by <PERSON>'s xorgens 3 implementaion in C.  This\n// implementation also provides for initalizing the generator with\n// string seeds, or for saving and restoring the state of the generator.\n//\n// On Chrome, this prng benchmarks about 2.1 times slower than\n// Javascript's built-in Math.random().\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    var w = me.w,\n        X = me.X, i = me.i, t, v;\n    // Update Weyl generator.\n    me.w = w = (w + 0x61c88647) | 0;\n    // Update xor generator.\n    v = X[(i + 34) & 127];\n    t = X[i = ((i + 1) & 127)];\n    v ^= v << 13;\n    t ^= t << 17;\n    v ^= v >>> 15;\n    t ^= t >>> 12;\n    // Update Xor generator array state.\n    v = X[i] = v ^ t;\n    me.i = i;\n    // Result is the combination.\n    return (v + (w ^ (w >>> 16))) | 0;\n  };\n\n  function init(me, seed) {\n    var t, v, i, j, w, X = [], limit = 128;\n    if (seed === (seed | 0)) {\n      // Numeric seeds initialize v, which is used to generates X.\n      v = seed;\n      seed = null;\n    } else {\n      // String seeds are mixed into v and X one character at a time.\n      seed = seed + '\\0';\n      v = 0;\n      limit = Math.max(limit, seed.length);\n    }\n    // Initialize circular array and weyl value.\n    for (i = 0, j = -32; j < limit; ++j) {\n      // Put the unicode characters into the array, and shuffle them.\n      if (seed) v ^= seed.charCodeAt((j + 32) % seed.length);\n      // After 32 shuffles, take v as the starting w value.\n      if (j === 0) w = v;\n      v ^= v << 10;\n      v ^= v >>> 15;\n      v ^= v << 4;\n      v ^= v >>> 13;\n      if (j >= 0) {\n        w = (w + 0x61c88647) | 0;     // Weyl.\n        t = (X[j & 127] ^= (v + w));  // Combine xor and weyl to init array.\n        i = (0 == t) ? i + 1 : 0;     // Count zeroes.\n      }\n    }\n    // We have detected all zeroes; make the key nonzero.\n    if (i >= 128) {\n      X[(seed && seed.length || 0) & 127] = -1;\n    }\n    // Run the generator 512 times to further mix the state before using it.\n    // Factoring this as a function slows the main generator, so it is just\n    // unrolled here.  The weyl generator is not advanced while warming up.\n    i = 127;\n    for (j = 4 * 128; j > 0; --j) {\n      v = X[(i + 34) & 127];\n      t = X[i = ((i + 1) & 127)];\n      v ^= v << 13;\n      t ^= t << 17;\n      v ^= v >>> 15;\n      t ^= t >>> 12;\n      X[i] = v ^ t;\n    }\n    // Storing state as object members is faster than using closure variables.\n    me.w = w;\n    me.X = X;\n    me.i = i;\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.i = f.i;\n  t.w = f.w;\n  t.X = f.X.slice();\n  return t;\n};\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.X) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xor4096 = impl;\n}\n\n})(\n  this,                                     // window object or global\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n", "// A Javascript implementaion of the \"xorshift7\" algorithm by\n// <PERSON> and <PERSON>:\n// \"On the Xorgshift Random Number Generators\"\n// http://saluc.engr.uconn.edu/refs/crypto/rng/panneton05onthexorshift.pdf\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this;\n\n  // Set up generator function.\n  me.next = function() {\n    // Update xor generator.\n    var X = me.x, i = me.i, t, v, w;\n    t = X[i]; t ^= (t >>> 7); v = t ^ (t << 24);\n    t = X[(i + 1) & 7]; v ^= t ^ (t >>> 10);\n    t = X[(i + 3) & 7]; v ^= t ^ (t >>> 3);\n    t = X[(i + 4) & 7]; v ^= t ^ (t << 7);\n    t = X[(i + 7) & 7]; t = t ^ (t << 13); v ^= t ^ (t << 9);\n    X[i] = v;\n    me.i = (i + 1) & 7;\n    return v;\n  };\n\n  function init(me, seed) {\n    var j, w, X = [];\n\n    if (seed === (seed | 0)) {\n      // Seed state array using a 32-bit integer.\n      w = X[0] = seed;\n    } else {\n      // Seed state using a string.\n      seed = '' + seed;\n      for (j = 0; j < seed.length; ++j) {\n        X[j & 7] = (X[j & 7] << 15) ^\n            (seed.charCodeAt(j) + X[(j + 1) & 7] << 13);\n      }\n    }\n    // Enforce an array length of 8, not all zeroes.\n    while (X.length < 8) X.push(0);\n    for (j = 0; j < 8 && X[j] === 0; ++j);\n    if (j == 8) w = X[7] = -1; else w = X[j];\n\n    me.x = X;\n    me.i = 0;\n\n    // Discard an initial 256 values.\n    for (j = 256; j > 0; --j) {\n      me.next();\n    }\n  }\n\n  init(me, seed);\n}\n\nfunction copy(f, t) {\n  t.x = f.x.slice();\n  t.i = f.i;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  if (seed == null) seed = +(new Date);\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (state.x) copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorshift7 = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n", "// A Javascript implementaion of the \"xorwow\" prng algorithm by\n// <PERSON>.  See http://www.jstatsoft.org/v08/i14/paper\n\n(function(global, module, define) {\n\nfunction XorGen(seed) {\n  var me = this, strseed = '';\n\n  // Set up generator function.\n  me.next = function() {\n    var t = (me.x ^ (me.x >>> 2));\n    me.x = me.y; me.y = me.z; me.z = me.w; me.w = me.v;\n    return (me.d = (me.d + 362437 | 0)) +\n       (me.v = (me.v ^ (me.v << 4)) ^ (t ^ (t << 1))) | 0;\n  };\n\n  me.x = 0;\n  me.y = 0;\n  me.z = 0;\n  me.w = 0;\n  me.v = 0;\n\n  if (seed === (seed | 0)) {\n    // Integer seed.\n    me.x = seed;\n  } else {\n    // String seed.\n    strseed += seed;\n  }\n\n  // Mix in string seed, then discard an initial batch of 64 values.\n  for (var k = 0; k < strseed.length + 64; k++) {\n    me.x ^= strseed.charCodeAt(k) | 0;\n    if (k == strseed.length) {\n      me.d = me.x << 10 ^ me.x >>> 4;\n    }\n    me.next();\n  }\n}\n\nfunction copy(f, t) {\n  t.x = f.x;\n  t.y = f.y;\n  t.z = f.z;\n  t.w = f.w;\n  t.v = f.v;\n  t.d = f.d;\n  return t;\n}\n\nfunction impl(seed, opts) {\n  var xg = new XorGen(seed),\n      state = opts && opts.state,\n      prng = function() { return (xg.next() >>> 0) / 0x100000000; };\n  prng.double = function() {\n    do {\n      var top = xg.next() >>> 11,\n          bot = (xg.next() >>> 0) / 0x100000000,\n          result = (top + bot) / (1 << 21);\n    } while (result === 0);\n    return result;\n  };\n  prng.int32 = xg.next;\n  prng.quick = prng;\n  if (state) {\n    if (typeof(state) == 'object') copy(state, xg);\n    prng.state = function() { return copy(xg, {}); }\n  }\n  return prng;\n}\n\nif (module && module.exports) {\n  module.exports = impl;\n} else if (define && define.amd) {\n  define(function() { return impl; });\n} else {\n  this.xorwow = impl;\n}\n\n})(\n  this,\n  (typeof module) == 'object' && module,    // present in node.js\n  (typeof define) == 'function' && define   // present with an AMD loader\n);\n\n\n", "/*\nCopyright 2019 <PERSON>.\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n\"Software\"), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n*/\n\n(function (global, pool, math) {\n//\n// The following constants are related to IEEE 754 limits.\n//\n\nvar width = 256,        // each RC4 output is 0 <= x < 256\n    chunks = 6,         // at least six RC4 outputs for each double\n    digits = 52,        // there are 52 significant digits in a double\n    rngname = 'random', // rngname: name for Math.random and Math.seedrandom\n    startdenom = math.pow(width, chunks),\n    significance = math.pow(2, digits),\n    overflow = significance * 2,\n    mask = width - 1,\n    nodecrypto;         // node.js crypto module, initialized at the bottom.\n\n//\n// seedrandom()\n// This is the seedrandom function described above.\n//\nfunction seedrandom(seed, options, callback) {\n  var key = [];\n  options = (options == true) ? { entropy: true } : (options || {});\n\n  // Flatten the seed string or build one from local entropy if needed.\n  var shortseed = mixkey(flatten(\n    options.entropy ? [seed, tostring(pool)] :\n    (seed == null) ? autoseed() : seed, 3), key);\n\n  // Use the seed to initialize an ARC4 generator.\n  var arc4 = new ARC4(key);\n\n  // This function returns a random double in [0, 1) that contains\n  // randomness in every bit of the mantissa of the IEEE 754 value.\n  var prng = function() {\n    var n = arc4.g(chunks),             // Start with a numerator n < 2 ^ 48\n        d = startdenom,                 //   and denominator d = 2 ^ 48.\n        x = 0;                          //   and no 'extra last byte'.\n    while (n < significance) {          // Fill up all significant digits by\n      n = (n + x) * width;              //   shifting numerator and\n      d *= width;                       //   denominator and generating a\n      x = arc4.g(1);                    //   new least-significant-byte.\n    }\n    while (n >= overflow) {             // To avoid rounding up, before adding\n      n /= 2;                           //   last byte, shift everything\n      d /= 2;                           //   right using integer math until\n      x >>>= 1;                         //   we have exactly the desired bits.\n    }\n    return (n + x) / d;                 // Form the number within [0, 1).\n  };\n\n  prng.int32 = function() { return arc4.g(4) | 0; }\n  prng.quick = function() { return arc4.g(4) / 0x100000000; }\n  prng.double = prng;\n\n  // Mix the randomness into accumulated entropy.\n  mixkey(tostring(arc4.S), pool);\n\n  // Calling convention: what to return as a function of prng, seed, is_math.\n  return (options.pass || callback ||\n      function(prng, seed, is_math_call, state) {\n        if (state) {\n          // Load the arc4 state from the given state if it has an S array.\n          if (state.S) { copy(state, arc4); }\n          // Only provide the .state method if requested via options.state.\n          prng.state = function() { return copy(arc4, {}); }\n        }\n\n        // If called as a method of Math (Math.seedrandom()), mutate\n        // Math.random because that is how seedrandom.js has worked since v1.0.\n        if (is_math_call) { math[rngname] = prng; return seed; }\n\n        // Otherwise, it is a newer calling convention, so return the\n        // prng directly.\n        else return prng;\n      })(\n  prng,\n  shortseed,\n  'global' in options ? options.global : (this == math),\n  options.state);\n}\n\n//\n// ARC4\n//\n// An ARC4 implementation.  The constructor takes a key in the form of\n// an array of at most (width) integers that should be 0 <= x < (width).\n//\n// The g(count) method returns a pseudorandom integer that concatenates\n// the next (count) outputs from ARC4.  Its return value is a number x\n// that is in the range 0 <= x < (width ^ count).\n//\nfunction ARC4(key) {\n  var t, keylen = key.length,\n      me = this, i = 0, j = me.i = me.j = 0, s = me.S = [];\n\n  // The empty key [] is treated as [0].\n  if (!keylen) { key = [keylen++]; }\n\n  // Set up S using the standard key scheduling algorithm.\n  while (i < width) {\n    s[i] = i++;\n  }\n  for (i = 0; i < width; i++) {\n    s[i] = s[j = mask & (j + key[i % keylen] + (t = s[i]))];\n    s[j] = t;\n  }\n\n  // The \"g\" method returns the next (count) outputs as one number.\n  (me.g = function(count) {\n    // Using instance members instead of closure state nearly doubles speed.\n    var t, r = 0,\n        i = me.i, j = me.j, s = me.S;\n    while (count--) {\n      t = s[i = mask & (i + 1)];\n      r = r * width + s[mask & ((s[i] = s[j = mask & (j + t)]) + (s[j] = t))];\n    }\n    me.i = i; me.j = j;\n    return r;\n    // For robust unpredictability, the function call below automatically\n    // discards an initial batch of values.  This is called RC4-drop[256].\n    // See http://google.com/search?q=rsa+fluhrer+response&btnI\n  })(width);\n}\n\n//\n// copy()\n// Copies internal state of ARC4 to or from a plain object.\n//\nfunction copy(f, t) {\n  t.i = f.i;\n  t.j = f.j;\n  t.S = f.S.slice();\n  return t;\n};\n\n//\n// flatten()\n// Converts an object tree to nested arrays of strings.\n//\nfunction flatten(obj, depth) {\n  var result = [], typ = (typeof obj), prop;\n  if (depth && typ == 'object') {\n    for (prop in obj) {\n      try { result.push(flatten(obj[prop], depth - 1)); } catch (e) {}\n    }\n  }\n  return (result.length ? result : typ == 'string' ? obj : obj + '\\0');\n}\n\n//\n// mixkey()\n// Mixes a string seed into a key that is an array of integers, and\n// returns a shortened string seed that is equivalent to the result key.\n//\nfunction mixkey(seed, key) {\n  var stringseed = seed + '', smear, j = 0;\n  while (j < stringseed.length) {\n    key[mask & j] =\n      mask & ((smear ^= key[mask & j] * 19) + stringseed.charCodeAt(j++));\n  }\n  return tostring(key);\n}\n\n//\n// autoseed()\n// Returns an object for autoseeding, using window.crypto and Node crypto\n// module if available.\n//\nfunction autoseed() {\n  try {\n    var out;\n    if (nodecrypto && (out = nodecrypto.randomBytes)) {\n      // The use of 'out' to remember randomBytes makes tight minified code.\n      out = out(width);\n    } else {\n      out = new Uint8Array(width);\n      (global.crypto || global.msCrypto).getRandomValues(out);\n    }\n    return tostring(out);\n  } catch (e) {\n    var browser = global.navigator,\n        plugins = browser && browser.plugins;\n    return [+new Date, global, plugins, global.screen, tostring(pool)];\n  }\n}\n\n//\n// tostring()\n// Converts an array of charcodes to a string\n//\nfunction tostring(a) {\n  return String.fromCharCode.apply(0, a);\n}\n\n//\n// When seedrandom.js is loaded, we immediately mix a few bits\n// from the built-in RNG into the entropy pool.  Because we do\n// not want to interfere with deterministic PRNG state later,\n// seedrandom will not call math.random on its own again after\n// initialization.\n//\nmixkey(math.random(), pool);\n\n//\n// Nodejs and AMD support: export the implementation as a module using\n// either convention.\n//\nif ((typeof module) == 'object' && module.exports) {\n  module.exports = seedrandom;\n  // When in node.js, try using crypto package for autoseeding.\n  try {\n    nodecrypto = require('crypto');\n  } catch (ex) {}\n} else if ((typeof define) == 'function' && define.amd) {\n  define(function() { return seedrandom; });\n} else {\n  // When included as a plain script, set up Math.seedrandom global.\n  math['seed' + rngname] = seedrandom;\n}\n\n\n// End anonymous scope, and pass initial values.\n})(\n  // global: `self` in browsers (including strict mode and web workers),\n  // otherwise `this` in Node and other environments\n  (typeof self !== 'undefined') ? self : this,\n  [],     // pool: entropy pool starts empty\n  Math    // math: package containing random, pow, and seedrandom\n);\n", "import * as m from \"./../../dist/es2015/hierarchy.js\";\nexport const am5hierarchy = m;"], "names": ["HierarchyDefaultTheme", "Theme", "setupDefaultRules", "super", "ic", "this", "_root", "interfaceColors", "gridLayout", "r", "rule", "bind", "setAll", "legendLabelText", "legendValueText", "width", "height", "colors", "ColorSet", "new", "step", "downDepth", "initialDepth", "singleBranchOnly", "<PERSON><PERSON><PERSON>nt", "animationEasing", "to<PERSON><PERSON><PERSON>", "setStateOnChildren", "position", "isMeasured", "cursorOverStyle", "tooltipText", "centerX", "centerY", "paddingBottom", "paddingTop", "paddingRight", "paddingLeft", "text", "populateText", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "minScale", "strokeWidth", "strokeOpacity", "strength", "distance", "fillOpacity", "radius", "tooltipY", "opacity", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scale", "interactive", "states", "create", "layout", "stateAnimationDuration", "fontWeight", "upDepth", "visible", "x", "y", "rotation", "cornerRadiusBR", "cornerRadiusTR", "cornerRadiusBL", "cornerRadiusTL", "cornerRadius", "textType", "baseRadius", "minRadius", "maxRadius", "initialFrames", "centerStrength", "manyBodyStrength", "velocityDecay", "linkWithStrength", "showOnFrame", "Infinity", "<PERSON><PERSON><PERSON><PERSON>", "orientation", "nodePadding", "draggable", "layoutAlgorithm", "type", "minWeightRatio", "convergenceRatio", "maxIterationCount", "layer", "BreadcrumbBar", "Container", "List", "Template", "Label", "_new", "themeTags", "labels", "template", "get", "background", "RoundedRectangle", "<PERSON><PERSON><PERSON><PERSON>", "dataItem", "label", "make", "_setDataItem", "events", "on", "series", "selectDataItem", "push", "_afterNew", "_defaultThemes", "_settings", "_changed", "isDirty", "previous", "_prevSettings", "_disposer", "event", "_handleDataItem", "dispose", "set", "children", "clear", "parent", "addTag", "moveValue", "classNames", "concat", "className", "HierarchyNode", "disabled", "disableDataItem", "enableDataItem", "_clickDisposer", "_isDragging", "count", "node", "sum", "i", "length", "value", "hierarchy", "data", "Map", "undefined", "mapChildren", "objectChildren", "child", "childs", "n", "root", "Node", "nodes", "pop", "Array", "from", "depth", "eachBefore", "computeHeight", "d", "isArray", "copyData", "prototype", "constructor", "eachAfter", "each", "callback", "that", "index", "call", "next", "find", "sort", "compare", "path", "end", "start", "ancestor", "a", "b", "aNodes", "ancestors", "bNodes", "c", "leastCommonAncestor", "k", "splice", "descendants", "leaves", "links", "source", "target", "copy", "Symbol", "iterator", "current", "reverse", "Hierarchy", "Series", "_tag", "makeNode", "childData", "setRaw", "nodesContainer", "fields", "bulletsContainer", "_prepare<PERSON><PERSON><PERSON><PERSON>", "_valuesDirty", "_treeData", "first", "dataItems", "_makeHierarchyData", "_index", "_rootNode", "setPrivateRaw", "_updateValues", "_sizeDirty", "_updateVisuals", "_zoom", "_selectDataItem", "_updateNodes", "hierarchyNode", "_updateNode", "bullets", "_makeBullets", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_dataItem", "getDataItemById", "id", "_getDataItemById", "di", "childDataItem", "_handleBullets", "bullet", "_onDataClear", "reset", "patterns", "processDataItem", "_processDataItem", "_makeDataItem", "addChildData", "dataContext", "childDataField", "found", "d3HierarchyNode", "getPrivate", "once", "dataValue", "valuePercent", "markDirtyText", "valueLow", "valueHigh", "updateLegendValue", "d3HierarchyChild", "childrenDataArray", "disposeDataItem", "removeValue", "hideDataItem", "duration", "promises", "hiddenState", "stateAnimationEasing", "easing", "animate", "key", "to", "waitForStop", "hide", "hideTooltip", "Promise", "all", "showDataItem", "show", "max<PERSON><PERSON><PERSON>", "isHidden", "<PERSON><PERSON><PERSON>", "applyAnimate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dispatch", "Math", "min", "<PERSON><PERSON><PERSON><PERSON>", "inited", "max", "_currentDownDepth", "_handleSingle", "_makeBullet", "bulletFunction", "sprite", "_positionBullet", "locationX", "locationY", "hoverDataItem", "hover", "unhoverDataItem", "unhover", "LinkedHierarchyNode", "_updateLinks", "link", "_onHide", "_onShow", "HierarchyLink", "Graphics", "newBullet", "addDisposer", "_clear", "<PERSON><PERSON><PERSON><PERSON>", "linksContainer", "sourceNode", "targetNode", "x0", "y0", "x1", "y1", "_display", "moveTo", "lineTo", "sourceRadius", "targetRadius", "hypot", "trueDistance", "location", "atan2", "PI", "_beforeChanged", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "LinkedHierarchy", "Circle", "C", "circles", "outerCircles", "_disposers", "linkBullets", "onAll", "circle", "setPrivate", "outerCircle", "max<PERSON><PERSON><PERSON>", "maxHeight", "_handleRadiusChange", "parentDataItem", "linkDataItems", "updateLinkWith", "linkWith", "linkWithDataItem", "_getPoint", "_animatePositions", "point", "fill", "fillPattern", "_setDefault", "sourceLinks", "lnk", "targetLinks", "_processLink", "unlinkDataItems", "_disposeLink", "_handleUnlink", "areLinked", "linked", "_link", "_source", "_target", "m", "initialAngle", "sqrt", "simulation", "alpha", "alphaMin", "alphaDecay", "pow", "alphaTarget", "forces", "stepper", "timer", "random", "s", "lcg", "tick", "stop", "iterations", "for<PERSON>ach", "force", "fx", "vx", "fy", "vy", "initializeNodes", "isNaN", "angle", "cos", "sin", "initializeForce", "initialize", "restart", "_", "arguments", "randomSource", "name", "delete", "dx", "dy", "d2", "closest", "tree", "xm", "ym", "xp", "yp", "right", "bottom", "j", "leaf", "_x0", "_y0", "_x1", "_y1", "_x", "_y", "defaultX", "defaultY", "quadtree", "Quadtree", "NaN", "addAll", "leaf_copy", "treeProto", "radii", "xi", "yi", "ri", "ri2", "visitAfter", "prepare", "visit", "apply", "quad", "rj", "l", "jiggle", "constant", "_nodes", "_random", "nodeById", "nodeId", "Error", "strengths", "distances", "bias", "map", "initializeStrength", "initializeDistance", "add", "cover", "xz", "yz", "floor", "z", "extent", "x2", "y2", "q", "x3", "y3", "quads", "remove", "retainer", "removeAll", "size", "ForceDirected", "d3forceSimulation", "_tick", "updateNodePositions", "restartSimulation", "_updateForces", "d3ForceNode", "innerWidth", "innerHeight", "_nodesDirty", "collisionForce", "linkForce", "_links", "_update<PERSON><PERSON><PERSON>n", "w", "h", "pt", "pl", "distanceMin2", "distanceMax2", "theta2", "accumulate", "weight", "abs", "distanceMin", "distanceMax", "theta", "d3node", "_clearDirty", "linkDatum", "getDistance", "getStrength", "d3Nodes", "d3Node", "sourceDataItem", "targetDataItem", "_updateRadius", "valueWorking", "d3Link", "Pack", "isPrivateDirty", "_updateNodesScale", "packLayout", "_packLayout", "padding", "scaleR", "animatePrivate", "round", "partition", "positionNode", "Partition", "rectangles", "partitionLayout", "_partitionLayout", "rectangle", "scaleX", "scaleY", "_makeNode", "levelHeight", "levelWidth", "Sunburst", "Slice", "slices", "RadialLabel", "bounds", "wr", "left", "hr", "top", "ir", "dr", "hs", "slice", "startAngle", "endAngle", "sliceStartAngle", "arc", "sliceInnerRadius", "sliceRadius", "innerRadius", "_updateLabel", "labelAngle", "scaleDepth", "defaultSeparation", "nextLeft", "v", "t", "nextRight", "moveSubtree", "wm", "wp", "shift", "change", "nextAncestor", "vim", "TreeNode", "A", "separation", "nodeSize", "treeRoot", "firstWalk", "secondWalk", "sizeNode", "tx", "kx", "ky", "siblings", "executeShifts", "midpoint", "vip", "vop", "vom", "sip", "sop", "sim", "som", "apportion", "Object", "Tree", "_hierarchyLayout", "inversed", "custom", "ratio", "squarify", "row", "nodeValue", "sumValue", "minValue", "maxValue", "newRatio", "minRatio", "beta", "rows", "i0", "i1", "dice", "squarifyRatio", "tile", "paddingStack", "paddingInner", "treemap", "p", "paddingOuter", "sums", "valueOffset", "valueTarget", "hi", "mid", "valueLeft", "valueRight", "xk", "yk", "Treemap", "algorithm", "_treemapLayout", "selectedDataItem", "treemapLayout", "nodePaddingOuter", "visibleNodes", "_getVisibleNodes", "indexOf", "removePrivate", "_invalidateTabindexes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VoronoiTreemap", "Polygon", "polygons", "voronoiTreemap", "voronoi", "clip", "getCirclePolygon", "prng", "coords", "polygon", "coordinates", "minX", "maxX", "len", "site", "points", "dAngle", "area", "lexicographicOrder", "computeUpperHullIndexes", "indexes", "sortedPoints", "flippedPoints", "upperIndexes", "lowerIndexes", "skip<PERSON><PERSON><PERSON>", "skipRight", "hull", "inside", "xa", "ya", "xb", "yb", "perimeter", "exports", "d3Polygon", "d3Timer", "d3Dispatch", "d3WeightedVoronoi", "FlickeringMitigation", "growthChangesLength", "DEFAULT_LENGTH", "totalAvailableArea", "last<PERSON>reaE<PERSON>r", "lastGrowth", "growthChanges", "growthChangeWeights", "generateGrowthChangeWeights", "growthChangeWeightsSum", "computeGrowthChangeWeightsSum", "weightedCount", "randomInitialPosition", "clippingPolygon", "minY", "maxY", "arr", "voronoiMapSimulation", "shouldUpdateInternals", "polygonContains", "halfAverageAreaInitialWeight", "dataArray", "siteCount", "totalArea", "halfAverageArea", "polygonArea", "d3VoronoiMapError", "message", "stack", "parseInt", "console", "warn", "parseFloat", "areaError", "secondToLastAreaError", "secondToLastGrowth", "unshift", "weightedChangeCount", "areaErrorTreshold", "iterationCount", "ended", "DEFAULT_PRNG", "DEFAULT_INITIAL_POSITION", "DEFAULT_INITIAL_WEIGHT", "epsilon", "initialPosition", "initialWeight", "weightedVoronoi", "flickeringMitigation", "shouldInitialize", "HANDLE_OVERWEIGHTED_VARIANT", "HANLDE_OVERWEIGHTED_MAX_ITERATION_COUNT", "handleOverweighted", "sqr", "squaredDistance", "s0", "s1", "initializeSimulation", "flickeringMitigationRatio", "adaptedMapPoints", "mapPoint", "centroid", "newMapPoints", "originalObject", "polygonCentroid", "adaptPositions", "currentArea", "adaptRatio", "adaptedWeight", "flickeringInfluence", "targetedArea", "adaptWeights", "adapt", "areaErrorSum", "computeAreaError", "handleOverweighted0", "handleOverweighted1", "error", "setHandleOverweighted", "mapPoints", "minAllowed<PERSON>eight", "reduce", "basePoints", "totalWeight", "acc", "bp", "bps", "createMapPoints", "originalData", "fixApplied", "tpi", "tpj", "weightest", "lightest", "sqrD", "fixCount", "overweight", "state", "voronoiMapInitialPositionRandom", "voronoiMapInitialPositionPie", "dataArrayLength", "clippingPolygonCentroid", "halfIncircleRadius", "angleBetweenData", "_pie", "vertex", "distFromCurrentEdge", "minDist<PERSON>rom<PERSON>dges", "edgeIndex", "edgeVertex0", "edgeVertex1", "vDistance", "computeMinDistFromEdges", "xx", "yy", "D", "len_sq", "param", "defineProperty", "factory", "noop", "test", "Dispatch", "typename", "types", "T", "trim", "split", "hasOwnProperty", "args", "taskHead", "taskTail", "timeout", "interval", "poke<PERSON><PERSON><PERSON>", "clockLast", "clockNow", "clockSkew", "clock", "performance", "now", "Date", "set<PERSON>rame", "window", "requestAnimationFrame", "f", "setTimeout", "clearNow", "Timer", "_call", "_time", "_next", "delay", "time", "timer<PERSON><PERSON><PERSON>", "e", "wake", "t0", "t2", "t1", "sleep", "nap", "poke", "clearTimeout", "clearInterval", "setInterval", "elapsed", "total", "_restart", "TypeError", "d3VoronoiMap", "_convenientReusableVoronoiMapSimulation", "_voronoiTreemap", "rootNode", "recurse", "cp", "d3Array", "epsilonesque", "dot", "v0", "v1", "linearDependent", "vect", "calculateCrossproduct", "ConflictListNode", "face", "vert", "nextf", "prevf", "nextv", "prevv", "ConflictList", "forFace", "head", "Vertex", "orig", "isDummy", "conflicts", "neighbours", "nonClippedPolygon", "projectZ", "Plane3D", "p1", "verts", "p2", "p3", "Point2D", "Vector", "<PERSON><PERSON><PERSON>", "dest", "prev", "twin", "iFace", "d3WeightedVoronoiError", "Face", "orient", "marked", "subtract", "crossproduct", "normal", "normalize", "createEdges", "dualPoint", "ConvexHull", "facets", "created", "horizon", "polygonClip", "subject", "input", "intersection", "closed", "polygonClosed", "polygonInside", "polygonIntersect", "isFinite", "x21", "x43", "y21", "y43", "ua", "getFacesOfDestVertex", "edge", "faces", "siteOrigin", "isVisibleFromBelow", "cln", "isEmpty", "curr", "getVertices", "list", "setWeight", "equals", "getNormZPlane", "getDualPointMappedToPlane", "nplane", "negate", "lenght", "isHorizon", "findHorizon", "isEqual", "origin", "getDualPoint", "plane3d", "edges", "temp", "getEdge", "conflict", "getHorizon", "removeConflict", "init", "boundingSites", "sites", "permutate", "ra", "currentItem", "prep", "v2", "v3", "f1", "f2", "f3", "f0", "addFacet", "addConflict", "addConflicts", "old1", "old2", "fn", "l1", "l2", "nCL", "last", "compute", "jF", "hEi", "hE", "_weightedVoronoi", "convexHull", "verticesVisited", "facetCount", "facet", "destVertex", "protopoly", "lastX", "lastY", "polygonLength", "clippedPoly", "computePowerDiagramIntegrated", "boundingData", "direction", "xExtent", "yExtent", "sign", "p0", "polygonDirection", "polygonHull", "delta", "lo", "ascending", "ascendingComparator", "center", "ascendingBisect", "bisector", "bisectRight", "bisectLeft", "bisectCenter", "number", "values", "valueof", "array", "empty", "arrayify", "cross", "reducer", "lengths", "product", "some", "cumsum", "Float64Array", "variance", "mean", "deviation", "<PERSON><PERSON>", "_partials", "_n", "valueOf", "fsum", "adder", "fcumsum", "InternMap", "entries", "keyof", "defineProperties", "_intern", "_key", "intern_get", "has", "intern_set", "intern_delete", "InternSet", "Set", "group", "keys", "nest", "identity", "groups", "rollup", "rollups", "unique", "regroup", "F", "Uint32Array", "permute", "groupSort", "ak", "av", "bk", "bv", "e10", "e5", "e2", "ticks", "tickIncrement", "r0", "r1", "power", "log", "LN10", "tickStep", "step0", "step1", "nice", "prestep", "ceil", "LN2", "domain", "threshold", "sturges", "histogram", "tz", "tn", "bin", "bins", "bisect", "thresholds", "quickselect", "exp", "sd", "swap", "quantile", "numbers", "value0", "subarray", "quantileSorted", "maxIndex", "merge", "arrays", "flatten", "minIndex", "pairs", "pairof", "pair", "range", "least", "defined", "element", "leastIndex", "greatest", "greatestIndex", "scan", "shuffler", "matrix", "transpose", "every", "filter", "mapper", "done", "difference", "others", "other", "disjoint", "is", "out", "superset", "o", "subset", "union", "alea", "xor128", "xorwow", "xorshift7", "xor4096", "tychei", "sr", "module", "global", "define", "Alea", "seed", "me", "mash", "String", "charCodeAt", "s2", "impl", "opts", "xg", "int32", "double", "quick", "XorGen", "strseed", "result", "X", "limit", "pool", "math", "nodecrypto", "startdenom", "significance", "overflow", "mask", "seedrandom", "options", "shortseed", "mixkey", "entropy", "tostring", "randomBytes", "Uint8Array", "crypto", "msCrypto", "getRandomValues", "browser", "navigator", "plugins", "screen", "autoseed", "arc4", "ARC4", "g", "S", "pass", "is_math_call", "keylen", "obj", "prop", "typ", "smear", "stringseed", "fromCharCode", "ex", "self", "am5hierarchy"], "sourceRoot": ""}