## amCharts 5

amCharts 5 is the fastest, most advanced amCharts data vizualization library, ever.

For a short overview of features, visit [amCharts website](https://www.amcharts.com/).


### Important notice about support

amCharts support is guaranteed for holders of amPlus subscription service.
GitHub issues is not usually monitored by amCharts support staff and may not be
answered. If you do have a license/subscription, you
may [contact us directly](https://www.amcharts.com/support/support-info/priority-support/)
for support. If you don't, here are
[a few options](https://www.amcharts.com/support/support-info/free-support/)
for you.


### Documentation

For extensive documentation, including getting started tutorials, as well
as class reference visit [amCharts 5 documentation website](https://www.amcharts.com/docs/v5).


### amCharts 5 is available as

* [NPM package](https://www.npmjs.com/package/@amcharts/amcharts5)
* [GitHub repository](https://github.com/amcharts/amcharts5)
* [ZIP download & CDN info](https://www.amcharts.com/download/)


### Related packages

This package inlcudes `MapChart` (geographical maps) functionality. However,
it does not include geodata itself (map files) needed to instantiate the maps.

Those are available via separate package:

* [NPM package](https://www.npmjs.com/package/@amcharts/amcharts5-geodata)
* [GitHub repository (geodata)](https://github.com/amcharts/amcharts5-geodata)
* [ZIP download](https://www.amcharts.com/download/download/)


### License

If you have a commercial amCharts 5 license, this software is covered by your
license, which supersedes any other license bundled with this package.

If you don't have a commercial license, the use of this software is covered by
a freeware license. Refer to included LICENSE file. The license is also
[available online](https://github.com/amcharts/amcharts5/blob/master/packages/shared/LICENSE).


### Creating translations

Please refer to [this tutorial](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations//).


### Changelog

* [amCharts 5 Changelog](https://github.com/amcharts/amcharts5/blob/master/CHANGELOG.md)
* [Documentation Changelog](https://www.amcharts.com/docs/v5/changelog/)


### Questions?

[Contact amCharts](mailto:<EMAIL>).


### Found a bug?

Open an [issue](https://github.com/amcharts/amcharts5/issues).
