/*! For license information please see xlsx.js.LICENSE.txt */
"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[4297],{7289:function(e,t,r){r.r(t),r.d(t,{CFB:function(){return Ie},SSF:function(){return Ee},parse_xlscfb:function(){return wl},parse_zip:function(){return Gf},read:function(){return Jf},readFile:function(){return qf},readFileSync:function(){return qf},set_cptable:function(){return b},set_fs:function(){return Oe},stream:function(){return kh},utils:function(){return Eh},version:function(){return _h},write:function(){return sh},writeFile:function(){return oh},writeFileAsync:function(){return lh},writeFileSync:function(){return oh},writeFileXLSX:function(){return ch},writeXLSX:function(){return nh}});var a,n={version:"0.20.2"},s=1200,i=1252,o=[874,932,936,949,950,1250,1251,1252,1253,1254,1255,1256,1257,1258,1e4],c={0:1252,1:65001,2:65001,77:1e4,128:932,129:949,130:1361,134:936,136:950,161:1253,162:1254,163:1258,177:1255,178:1256,186:1257,204:1251,222:874,238:1250,255:1252,69:6969},l=function(e){-1!=o.indexOf(e)&&(i=c[0]=e)},f=function(e){s=e,l(e)};function h(){f(1200),l(1252)}function u(e){for(var t=[],r=0,a=e.length;r<a;++r)t[r]=e.charCodeAt(r);return t}function d(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r)+(e.charCodeAt(2*r+1)<<8));return t.join("")}function p(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e.charCodeAt(2*r+1)+(e.charCodeAt(2*r)<<8));return t.join("")}var m=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1);return 255==t&&254==r?d(e.slice(2)):254==t&&255==r?p(e.slice(2)):65279==t?e.slice(1):e},v=function(e){return String.fromCharCode(e)},g=function(e){return String.fromCharCode(e)};function b(e){a=e,f=function(e){s=e,l(e)},m=function(e){return 255===e.charCodeAt(0)&&254===e.charCodeAt(1)?a.utils.decode(1200,u(e.slice(2))):e},v=function(e){return 1200===s?String.fromCharCode(e):a.utils.decode(s,[255&e,e>>8])[0]},g=function(e){return a.utils.decode(i,[e])[0]},Fr()}var w=null,T="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";function y(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)s=(r=e.charCodeAt(l++))>>2,i=(3&r)<<4|(a=e.charCodeAt(l++))>>4,o=(15&a)<<2|(n=e.charCodeAt(l++))>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=T.charAt(s)+T.charAt(i)+T.charAt(o)+T.charAt(c);return t}function E(e){var t="",r=0,a=0,n=0,s=0,i=0,o=0;"data:"==e.slice(0,5)&&(c=e.slice(0,1024).indexOf(";base64,"))>-1&&(e=e.slice(c+8)),e=e.replace(/[^\w\+\/\=]/g,"");for(var c=0;c<e.length;)r=T.indexOf(e.charAt(c++))<<2|(s=T.indexOf(e.charAt(c++)))>>4,t+=String.fromCharCode(r),a=(15&s)<<4|(i=T.indexOf(e.charAt(c++)))>>2,64!==i&&(t+=String.fromCharCode(a)),n=(3&i)<<6|(o=T.indexOf(e.charAt(c++))),64!==o&&(t+=String.fromCharCode(n));return t}var k=function(){return"undefined"!=typeof Buffer&&"undefined"!=typeof process&&void 0!==process.versions&&!!process.versions.node}(),_=function(){if("undefined"!=typeof Buffer){var e=!Buffer.from;if(!e)try{Buffer.from("foo","utf8")}catch(t){e=!0}return e?function(e,t){return t?new Buffer(e,t):new Buffer(e)}:Buffer.from.bind(Buffer)}return function(){}}(),S=function(){if("undefined"==typeof Buffer)return!1;var e=_([65,0]);return!!e&&1==e.toString("utf16le").length}();function x(e){return k?Buffer.alloc?Buffer.alloc(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}function A(e){return k?Buffer.allocUnsafe?Buffer.allocUnsafe(e):new Buffer(e):"undefined"!=typeof Uint8Array?new Uint8Array(e):new Array(e)}var C=function(e){return k?_(e,"binary"):e.split("").map((function(e){return 255&e.charCodeAt(0)}))};function I(e){if("undefined"==typeof ArrayBuffer)return C(e);for(var t=new ArrayBuffer(e.length),r=new Uint8Array(t),a=0;a!=e.length;++a)r[a]=255&e.charCodeAt(a);return t}function O(e){if(Array.isArray(e))return e.map((function(e){return String.fromCharCode(e)})).join("");for(var t=[],r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}function R(e){if("undefined"==typeof ArrayBuffer)throw new Error("Unsupported");if(e instanceof ArrayBuffer)return R(new Uint8Array(e));for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=e[r];return t}var N=k?function(e){return Buffer.concat(e.map((function(e){return Buffer.isBuffer(e)?e:_(e)})))}:function(e){if("undefined"!=typeof Uint8Array){var t=0,r=0;for(t=0;t<e.length;++t)r+=e[t].length;var a=new Uint8Array(r),n=0;for(t=0,r=0;t<e.length;r+=n,++t)n=e[t].length,e[t]instanceof Uint8Array?a.set(e[t],r):"string"==typeof e[t]?a.set(new Uint8Array(C(e[t])),r):a.set(new Uint8Array(e[t]),r);return a}return[].concat.apply([],e.map((function(e){return Array.isArray(e)?e:[].slice.call(e)})))},F=/\u0000/g,D=/[\u0001-\u0006]/g;function P(e){for(var t="",r=e.length-1;r>=0;)t+=e.charAt(r--);return t}function M(e,t){var r=""+e;return r.length>=t?r:Ye("0",t-r.length)+r}function L(e,t){var r=""+e;return r.length>=t?r:Ye(" ",t-r.length)+r}function U(e,t){var r=""+e;return r.length>=t?r:r+Ye(" ",t-r.length)}var B=Math.pow(2,32);function W(e,t){return e>B||e<-B?function(e,t){var r=""+Math.round(e);return r.length>=t?r:Ye("0",t-r.length)+r}(e,t):function(e,t){var r=""+e;return r.length>=t?r:Ye("0",t-r.length)+r}(Math.round(e),t)}function z(e,t){return t=t||0,e.length>=7+t&&103==(32|e.charCodeAt(t))&&101==(32|e.charCodeAt(t+1))&&110==(32|e.charCodeAt(t+2))&&101==(32|e.charCodeAt(t+3))&&114==(32|e.charCodeAt(t+4))&&97==(32|e.charCodeAt(t+5))&&108==(32|e.charCodeAt(t+6))}var H=[["Sun","Sunday"],["Mon","Monday"],["Tue","Tuesday"],["Wed","Wednesday"],["Thu","Thursday"],["Fri","Friday"],["Sat","Saturday"]],V=[["J","Jan","January"],["F","Feb","February"],["M","Mar","March"],["A","Apr","April"],["M","May","May"],["J","Jun","June"],["J","Jul","July"],["A","Aug","August"],["S","Sep","September"],["O","Oct","October"],["N","Nov","November"],["D","Dec","December"]],G={0:"General",1:"0",2:"0.00",3:"#,##0",4:"#,##0.00",9:"0%",10:"0.00%",11:"0.00E+00",12:"# ?/?",13:"# ??/??",14:"m/d/yy",15:"d-mmm-yy",16:"d-mmm",17:"mmm-yy",18:"h:mm AM/PM",19:"h:mm:ss AM/PM",20:"h:mm",21:"h:mm:ss",22:"m/d/yy h:mm",37:"#,##0 ;(#,##0)",38:"#,##0 ;[Red](#,##0)",39:"#,##0.00;(#,##0.00)",40:"#,##0.00;[Red](#,##0.00)",45:"mm:ss",46:"[h]:mm:ss",47:"mmss.0",48:"##0.0E+0",49:"@",56:'"上午/下午 "hh"時"mm"分"ss"秒 "'},X={5:37,6:38,7:39,8:40,23:0,24:0,25:0,26:0,27:14,28:14,29:14,30:14,31:14,50:14,51:14,52:14,53:14,54:14,55:14,56:14,57:14,58:14,59:1,60:2,61:3,62:4,67:9,68:10,69:12,70:13,71:14,72:14,73:15,74:16,75:17,76:20,77:21,78:22,79:45,80:46,81:47,82:0},j={5:'"$"#,##0_);\\("$"#,##0\\)',63:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',41:'_(* #,##0_);_(* \\(#,##0\\);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* \\(#,##0\\);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* \\(#,##0.00\\);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* \\(#,##0.00\\);_("$"* "-"??_);_(@_)'};function K(e,t,r){for(var a=e<0?-1:1,n=e*a,s=0,i=1,o=0,c=1,l=0,f=0,h=Math.floor(n);l<t&&(o=(h=Math.floor(n))*i+s,f=h*l+c,!(n-h<5e-8));)n=1/(n-h),s=i,i=o,c=l,l=f;if(f>t&&(l>t?(f=c,o=s):(f=l,o=i)),!r)return[0,a*o,f];var u=Math.floor(a*o/f);return[u,a*o-u*f,f]}function Y(e,t,r){if(e>2958465||e<0)return null;var a=0|(e=function(e){var t=e.toPrecision(16);if(t.indexOf("e")>-1){var r=t.slice(0,t.indexOf("e"));return(r=r.indexOf(".")>-1?r.slice(0,"0."==r.slice(0,2)?17:16):r.slice(0,15)+Ye("0",r.length-15))+t.slice(t.indexOf("e"))}var a=t.indexOf(".")>-1?t.slice(0,"0."==t.slice(0,2)?17:16):t.slice(0,15)+Ye("0",t.length-15);return Number(a)}(e)),n=Math.floor(86400*(e-a)),s=0,i=[],o={D:a,T:n,u:86400*(e-a)-n,y:0,m:0,d:0,H:0,M:0,S:0,q:0};if(Math.abs(o.u)<1e-6&&(o.u=0),t&&t.date1904&&(a+=1462),o.u>.9999&&(o.u=0,86400==++n&&(o.T=n=0,++a,++o.D)),60===a)i=r?[1317,10,29]:[1900,2,29],s=3;else if(0===a)i=r?[1317,8,29]:[1900,1,0],s=6;else{a>60&&--a;var c=new Date(1900,0,1);c.setDate(c.getDate()+a-1),i=[c.getFullYear(),c.getMonth()+1,c.getDate()],s=c.getDay(),a<60&&(s=(s+6)%7),r&&(s=function(e,t){t[0]-=581;var r=e.getDay();return e<60&&(r=(r+6)%7),r}(c,i))}return o.y=i[0],o.m=i[1],o.d=i[2],o.S=n%60,n=Math.floor(n/60),o.M=n%60,n=Math.floor(n/60),o.H=n,o.q=s,o}function Z(e){return-1==e.indexOf(".")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)$/,"$1")}function J(e){var t,r=Math.floor(Math.log(Math.abs(e))*Math.LOG10E);return t=r>=-4&&r<=-1?e.toPrecision(10+r):Math.abs(r)<=9?function(e){var t=e<0?12:11,r=Z(e.toFixed(12));return r.length<=t||(r=e.toPrecision(10)).length<=t?r:e.toExponential(5)}(e):10===r?e.toFixed(10).substr(0,12):function(e){var t=Z(e.toFixed(11));return t.length>(e<0?12:11)||"0"===t||"-0"===t?e.toPrecision(6):t}(e),Z(function(e){return-1==e.indexOf("E")?e:e.replace(/(?:\.0*|(\.\d*[1-9])0+)[Ee]/,"$1E").replace(/(E[+-])(\d)$/,"$10$2")}(t.toUpperCase()))}function q(e,t){switch(typeof e){case"string":return e;case"boolean":return e?"TRUE":"FALSE";case"number":return(0|e)===e?e.toString(10):J(e);case"undefined":return"";case"object":if(null==e)return"";if(e instanceof Date)return be(14,We(e,t&&t.date1904),t)}throw new Error("unsupported value in General format: "+e)}function Q(e,t,r,a){var n,s="",i=0,o=0,c=r.y,l=0;switch(e){case 98:c=r.y+543;case 121:switch(t.length){case 1:case 2:n=c%100,l=2;break;default:n=c%1e4,l=4}break;case 109:switch(t.length){case 1:case 2:n=r.m,l=t.length;break;case 3:return V[r.m-1][1];case 5:return V[r.m-1][0];default:return V[r.m-1][2]}break;case 100:switch(t.length){case 1:case 2:n=r.d,l=t.length;break;case 3:return H[r.q][0];default:return H[r.q][1]}break;case 104:switch(t.length){case 1:case 2:n=1+(r.H+11)%12,l=t.length;break;default:throw"bad hour format: "+t}break;case 72:switch(t.length){case 1:case 2:n=r.H,l=t.length;break;default:throw"bad hour format: "+t}break;case 77:switch(t.length){case 1:case 2:n=r.M,l=t.length;break;default:throw"bad minute format: "+t}break;case 115:if("s"!=t&&"ss"!=t&&".0"!=t&&".00"!=t&&".000"!=t)throw"bad second format: "+t;return 0!==r.u||"s"!=t&&"ss"!=t?(o=a>=2?3===a?1e3:100:1===a?10:1,(i=Math.round(o*(r.S+r.u)))>=60*o&&(i=0),"s"===t?0===i?"0":""+i/o:(s=M(i,2+a),"ss"===t?s.substr(0,2):"."+s.substr(2,t.length-1))):M(r.S,t.length);case 90:switch(t){case"[h]":case"[hh]":n=24*r.D+r.H;break;case"[m]":case"[mm]":n=60*(24*r.D+r.H)+r.M;break;case"[s]":case"[ss]":n=60*(60*(24*r.D+r.H)+r.M)+(0==a?Math.round(r.S+r.u):r.S);break;default:throw"bad abstime format: "+t}l=3===t.length?1:2;break;case 101:n=c,l=1}return l>0?M(n,l):""}function ee(e){if(e.length<=3)return e;for(var t=e.length%3,r=e.substr(0,t);t!=e.length;t+=3)r+=(r.length>0?",":"")+e.substr(t,3);return r}var te=/%/g;function re(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+re(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),-1===(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).indexOf("e")){var i=Math.floor(Math.log(t)*Math.LOG10E);for(-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s);"0."===r.substr(0,2);)r=(r=r.charAt(0)+r.substr(2,n)+"."+r.substr(2+n)).replace(/^0+([1-9])/,"$1").replace(/^0+\./,"0.");r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}var ae=/# (\?+)( ?)\/( ?)(\d+)/,ne=/^#*0*\.([0#]+)/,se=/\)[^)]*[0#]/,ie=/\(###\) ###\\?-####/;function oe(e){for(var t,r="",a=0;a!=e.length;++a)switch(t=e.charCodeAt(a)){case 35:break;case 63:r+=" ";break;case 48:r+="0";break;default:r+=String.fromCharCode(t)}return r}function ce(e,t){var r=Math.pow(10,t);return""+Math.round(e*r)/r}function le(e,t){var r=e-Math.floor(e),a=Math.pow(10,t);return t<(""+Math.round(r*a)).length?0:Math.round(r*a)}function fe(e,t,r){if(40===e.charCodeAt(0)&&!t.match(se)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?fe("n",a,r):"("+fe("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return de(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(te,""),n=t.length-a.length;return de(e,a,r*Math.pow(10,2*n))+Ye("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return re(t,r);if(36===t.charCodeAt(0))return"$"+fe(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+W(c,t.length);if(t.match(/^[#?]+$/))return"0"===(n=W(r,0))&&(n=""),n.length>t.length?n:oe(t.substr(0,t.length-n.length))+n;if(s=t.match(ae))return function(e,t,r){var a=parseInt(e[4],10),n=Math.round(t*a),s=Math.floor(n/a),i=n-s*a,o=a;return r+(0===s?"":""+s)+" "+(0===i?Ye(" ",e[1].length+1+e[4].length):L(i,e[1].length)+e[2]+"/"+e[3]+M(o,e[4].length))}(s,c,l);if(t.match(/^#+0+$/))return l+W(c,t.length-t.indexOf("0"));if(s=t.match(ne))return n=ce(r,s[1].length).replace(/^([^\.]+)$/,"$1."+oe(s[1])).replace(/\.$/,"."+oe(s[1])).replace(/\.(\d*)$/,(function(e,t){return"."+t+Ye("0",oe(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+ce(c,s[2].length).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+ee(W(c,0));if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+fe(e,t,-r):ee(""+(Math.floor(r)+function(e,t){return t<(""+Math.round((e-Math.floor(e))*Math.pow(10,t))).length?1:0}(r,s[1].length)))+"."+M(le(r,s[1].length),s[1].length);if(s=t.match(/^#,#*,#0/))return fe(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=P(fe(e,t.replace(/[\\-]/g,""),r)),i=0,P(P(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(ie))return"("+(n=fe(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=K(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=de("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=U(o[2],i)).length<s[4].length&&(f=oe(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=K(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],i)+s[2]+"/"+s[3]+U(o[2],i):Ye(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=W(r,0),t.length<=n.length?n:oe(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0?]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return oe(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return i=le(r,s[1].length),r<0?"-"+fe(e,t,-r):ee(function(e){return e<2147483647&&e>-2147483648?""+(e>=0?0|e:e-1|0):""+Math.floor(e)}(r)).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e}))+"."+M(i,s[1].length);switch(t){case"###,##0.00":return fe(e,"#,##0.00",r);case"###,###":case"##,###":case"#,###":var d=ee(W(c,0));return"0"!==d?l+d:"";case"###,###.00":return fe(e,"###,##0.00",r).replace(/^0\./,".");case"#,###.00":return fe(e,"#,##0.00",r).replace(/^0\./,".")}throw new Error("unsupported format |"+t+"|")}function he(e,t){var r,a=e.indexOf("E")-e.indexOf(".")-1;if(e.match(/^#+0.0E\+0$/)){if(0==t)return"0.0E+0";if(t<0)return"-"+he(e,-t);var n=e.indexOf(".");-1===n&&(n=e.indexOf("E"));var s=Math.floor(Math.log(t)*Math.LOG10E)%n;if(s<0&&(s+=n),!(r=(t/Math.pow(10,s)).toPrecision(a+1+(n+s)%n)).match(/[Ee]/)){var i=Math.floor(Math.log(t)*Math.LOG10E);-1===r.indexOf(".")?r=r.charAt(0)+"."+r.substr(1)+"E+"+(i-r.length+s):r+="E+"+(i-s),r=r.replace(/\+-/,"-")}r=r.replace(/^([+-]?)(\d*)\.(\d*)[Ee]/,(function(e,t,r,a){return t+r+a.substr(0,(n+s)%n)+"."+a.substr(s)+"E"}))}else r=t.toExponential(a);return e.match(/E\+00$/)&&r.match(/e[+-]\d$/)&&(r=r.substr(0,r.length-1)+"0"+r.charAt(r.length-1)),e.match(/E\-/)&&r.match(/e\+/)&&(r=r.replace(/e\+/,"e")),r.replace("e","E")}function ue(e,t,r){if(40===e.charCodeAt(0)&&!t.match(se)){var a=t.replace(/\( */,"").replace(/ \)/,"").replace(/\)/,"");return r>=0?ue("n",a,r):"("+ue("n",a,-r)+")"}if(44===t.charCodeAt(t.length-1))return function(e,t,r){for(var a=t.length-1;44===t.charCodeAt(a-1);)--a;return de(e,t.substr(0,a),r/Math.pow(10,3*(t.length-a)))}(e,t,r);if(-1!==t.indexOf("%"))return function(e,t,r){var a=t.replace(te,""),n=t.length-a.length;return de(e,a,r*Math.pow(10,2*n))+Ye("%",n)}(e,t,r);if(-1!==t.indexOf("E"))return he(t,r);if(36===t.charCodeAt(0))return"$"+ue(e,t.substr(" "==t.charAt(1)?2:1),r);var n,s,i,o,c=Math.abs(r),l=r<0?"-":"";if(t.match(/^00+$/))return l+M(c,t.length);if(t.match(/^[#?]+$/))return n=""+r,0===r&&(n=""),n.length>t.length?n:oe(t.substr(0,t.length-n.length))+n;if(s=t.match(ae))return function(e,t,r){return r+(0===t?"":""+t)+Ye(" ",e[1].length+2+e[4].length)}(s,c,l);if(t.match(/^#+0+$/))return l+M(c,t.length-t.indexOf("0"));if(s=t.match(ne))return n=(n=(""+r).replace(/^([^\.]+)$/,"$1."+oe(s[1])).replace(/\.$/,"."+oe(s[1]))).replace(/\.(\d*)$/,(function(e,t){return"."+t+Ye("0",oe(s[1]).length-t.length)})),-1!==t.indexOf("0.")?n:n.replace(/^0\./,".");if(t=t.replace(/^#+([0.])/,"$1"),s=t.match(/^(0*)\.(#*)$/))return l+(""+c).replace(/\.(\d*[1-9])0*$/,".$1").replace(/^(-?\d*)$/,"$1.").replace(/^0\./,s[1].length?"0.":".");if(s=t.match(/^#{1,3},##0(\.?)$/))return l+ee(""+c);if(s=t.match(/^#,##0\.([#0]*0)$/))return r<0?"-"+ue(e,t,-r):ee(""+r)+"."+Ye("0",s[1].length);if(s=t.match(/^#,#*,#0/))return ue(e,t.replace(/^#,#*,/,""),r);if(s=t.match(/^([0#]+)(\\?-([0#]+))+$/))return n=P(ue(e,t.replace(/[\\-]/g,""),r)),i=0,P(P(t.replace(/\\/g,"")).replace(/[0#]/g,(function(e){return i<n.length?n.charAt(i++):"0"===e?"0":""})));if(t.match(ie))return"("+(n=ue(e,"##########",r)).substr(0,3)+") "+n.substr(3,3)+"-"+n.substr(6);var f="";if(s=t.match(/^([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(s[4].length,7),o=K(c,Math.pow(10,i)-1,!1),n=""+l," "==(f=de("n",s[1],o[1])).charAt(f.length-1)&&(f=f.substr(0,f.length-1)+"0"),n+=f+s[2]+"/"+s[3],(f=U(o[2],i)).length<s[4].length&&(f=oe(s[4].substr(s[4].length-f.length))+f),n+=f;if(s=t.match(/^# ([#0?]+)( ?)\/( ?)([#0?]+)/))return i=Math.min(Math.max(s[1].length,s[4].length),7),l+((o=K(c,Math.pow(10,i)-1,!0))[0]||(o[1]?"":"0"))+" "+(o[1]?L(o[1],i)+s[2]+"/"+s[3]+U(o[2],i):Ye(" ",2*i+1+s[2].length+s[3].length));if(s=t.match(/^[#0?]+$/))return n=""+r,t.length<=n.length?n:oe(t.substr(0,t.length-n.length))+n;if(s=t.match(/^([#0]+)\.([#0]+)$/)){n=""+r.toFixed(Math.min(s[2].length,10)).replace(/([^0])0+$/,"$1"),i=n.indexOf(".");var h=t.indexOf(".")-i,u=t.length-n.length-h;return oe(t.substr(0,h)+n+t.substr(t.length-u))}if(s=t.match(/^00,000\.([#0]*0)$/))return r<0?"-"+ue(e,t,-r):ee(""+r).replace(/^\d,\d{3}$/,"0$&").replace(/^\d*$/,(function(e){return"00,"+(e.length<3?M(0,3-e.length):"")+e}))+"."+M(0,s[1].length);switch(t){case"###,###":case"##,###":case"#,###":var d=ee(""+c);return"0"!==d?l+d:"";default:if(t.match(/\.[0#?]*$/))return ue(e,t.slice(0,t.lastIndexOf(".")),r)+oe(t.slice(t.lastIndexOf(".")))}throw new Error("unsupported format |"+t+"|")}function de(e,t,r){return(0|r)===r?ue(e,t,r):fe(e,t,r)}var pe=/\[[HhMmSs\u0E0A\u0E19\u0E17]*\]/;function me(e){for(var t=0,r="",a="";t<e.length;)switch(r=e.charAt(t)){case"G":z(e,t)&&(t+=6),t++;break;case'"':for(;34!==e.charCodeAt(++t)&&t<e.length;);++t;break;case"\\":case"_":t+=2;break;case"@":++t;break;case"B":case"b":if("1"===e.charAt(t+1)||"2"===e.charAt(t+1))return!0;case"M":case"D":case"Y":case"H":case"S":case"E":case"m":case"d":case"y":case"h":case"s":case"e":case"g":return!0;case"A":case"a":case"上":if("A/P"===e.substr(t,3).toUpperCase())return!0;if("AM/PM"===e.substr(t,5).toUpperCase())return!0;if("上午/下午"===e.substr(t,5).toUpperCase())return!0;++t;break;case"[":for(a=r;"]"!==e.charAt(t++)&&t<e.length;)a+=e.charAt(t);if(a.match(pe))return!0;break;case".":case"0":case"#":for(;t<e.length&&("0#?.,E+-%".indexOf(r=e.charAt(++t))>-1||"\\"==r&&"-"==e.charAt(t+1)&&"0#".indexOf(e.charAt(t+2))>-1););break;case"?":for(;e.charAt(++t)===r;);break;case"*":++t," "!=e.charAt(t)&&"*"!=e.charAt(t)||++t;break;case"(":case")":++t;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(;t<e.length&&"0123456789".indexOf(e.charAt(++t))>-1;);break;default:++t}return!1}var ve=/\[(=|>[=]?|<[>=]?)(-?\d+(?:\.\d*)?)\]/;function ge(e,t){if(null==t)return!1;var r=parseFloat(t[2]);switch(t[1]){case"=":if(e==r)return!0;break;case">":if(e>r)return!0;break;case"<":if(e<r)return!0;break;case"<>":if(e!=r)return!0;break;case">=":if(e>=r)return!0;break;case"<=":if(e<=r)return!0}return!1}function be(e,t,r){null==r&&(r={});var a="";switch(typeof e){case"string":a="m/d/yy"==e&&r.dateNF?r.dateNF:e;break;case"number":null==(a=14==e&&r.dateNF?r.dateNF:(null!=r.table?r.table:G)[e])&&(a=r.table&&r.table[X[e]]||G[X[e]]),null==a&&(a=j[e]||"General")}if(z(a,0))return q(t,r);t instanceof Date&&(t=We(t,r.date1904));var n=function(e,t){var r=function(e){for(var t=[],r=!1,a=0,n=0;a<e.length;++a)switch(e.charCodeAt(a)){case 34:r=!r;break;case 95:case 42:case 92:++a;break;case 59:t[t.length]=e.substr(n,a-n),n=a+1}if(t[t.length]=e.substr(n),!0===r)throw new Error("Format |"+e+"| unterminated string ");return t}(e),a=r.length,n=r[a-1].indexOf("@");if(a<4&&n>-1&&--a,r.length>4)throw new Error("cannot find right format for |"+r.join("|")+"|");if("number"!=typeof t)return[4,4===r.length||n>-1?r[r.length-1]:"@"];switch(r.length){case 1:r=n>-1?["General","General","General",r[0]]:[r[0],r[0],r[0],"@"];break;case 2:r=n>-1?[r[0],r[0],r[0],r[1]]:[r[0],r[1],r[0],"@"];break;case 3:r=n>-1?[r[0],r[1],r[0],r[2]]:[r[0],r[1],r[2],"@"]}var s=t>0?r[0]:t<0?r[1]:r[2];if(-1===r[0].indexOf("[")&&-1===r[1].indexOf("["))return[a,s];if(null!=r[0].match(/\[[=<>]/)||null!=r[1].match(/\[[=<>]/)){var i=r[0].match(ve),o=r[1].match(ve);return ge(t,i)?[a,r[0]]:ge(t,o)?[a,r[1]]:[a,r[null!=i&&null!=o?2:1]]}return[a,s]}(a,t);if(z(n[1]))return q(t,r);if(!0===t)t="TRUE";else if(!1===t)t="FALSE";else if(""===t||null==t)return"";return function(e,t,r,a){for(var n,s,i,o=[],c="",l=0,f="",h="t",u="H";l<e.length;)switch(f=e.charAt(l)){case"G":if(!z(e,l))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"G",v:"General"},l+=7;break;case'"':for(c="";34!==(i=e.charCodeAt(++l))&&l<e.length;)c+=String.fromCharCode(i);o[o.length]={t:"t",v:c},++l;break;case"\\":var d=e.charAt(++l),p="("===d||")"===d?d:"t";o[o.length]={t:p,v:d},++l;break;case"_":o[o.length]={t:"t",v:" "},l+=2;break;case"@":o[o.length]={t:"T",v:t},++l;break;case"B":case"b":if("1"===e.charAt(l+1)||"2"===e.charAt(l+1)){if(null==n&&null==(n=Y(t,r,"2"===e.charAt(l+1))))return"";o[o.length]={t:"X",v:e.substr(l,2)},h=f,l+=2;break}case"M":case"D":case"Y":case"H":case"S":case"E":f=f.toLowerCase();case"m":case"d":case"y":case"h":case"s":case"e":case"g":if(t<0)return"";if(null==n&&null==(n=Y(t,r)))return"";for(c=f;++l<e.length&&e.charAt(l).toLowerCase()===f;)c+=f;"m"===f&&"h"===h.toLowerCase()&&(f="M"),"h"===f&&(f=u),o[o.length]={t:f,v:c},h=f;break;case"A":case"a":case"上":var m={t:f,v:f};if(null==n&&(n=Y(t,r)),"A/P"===e.substr(l,3).toUpperCase()?(null!=n&&(m.v=n.H>=12?e.charAt(l+2):f),m.t="T",u="h",l+=3):"AM/PM"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"PM":"AM"),m.t="T",l+=5,u="h"):"上午/下午"===e.substr(l,5).toUpperCase()?(null!=n&&(m.v=n.H>=12?"下午":"上午"),m.t="T",l+=5,u="h"):(m.t="t",++l),null==n&&"T"===m.t)return"";o[o.length]=m,h=f;break;case"[":for(c=f;"]"!==e.charAt(l++)&&l<e.length;)c+=e.charAt(l);if("]"!==c.slice(-1))throw'unterminated "[" block: |'+c+"|";if(c.match(pe)){if(null==n&&null==(n=Y(t,r)))return"";o[o.length]={t:"Z",v:c.toLowerCase()},h=c.charAt(1)}else c.indexOf("$")>-1&&(c=(c.match(/\$([^-\[\]]*)/)||[])[1]||"$",me(e)||(o[o.length]={t:"t",v:c}));break;case".":if(null!=n){for(c=f;++l<e.length&&"0"===(f=e.charAt(l));)c+=f;o[o.length]={t:"s",v:c};break}case"0":case"#":for(c=f;++l<e.length&&"0#?.,E+-%".indexOf(f=e.charAt(l))>-1;)c+=f;o[o.length]={t:"n",v:c};break;case"?":for(c=f;e.charAt(++l)===f;)c+=f;o[o.length]={t:f,v:c},h=f;break;case"*":++l," "!=e.charAt(l)&&"*"!=e.charAt(l)||++l;break;case"(":case")":o[o.length]={t:1===a?"t":f,v:f},++l;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":for(c=f;l<e.length&&"0123456789".indexOf(e.charAt(++l))>-1;)c+=e.charAt(l);o[o.length]={t:"D",v:c};break;case" ":o[o.length]={t:f,v:f},++l;break;case"$":o[o.length]={t:"t",v:"$"},++l;break;default:if(-1===",$-+/():!^&'~{}<>=€acfijklopqrtuvwxzP".indexOf(f))throw new Error("unrecognized character "+f+" in "+e);o[o.length]={t:"t",v:f},++l}var v,g,b=0,w=0;for(l=o.length-1,h="t";l>=0;--l)switch(o[l].t){case"h":case"H":o[l].t=u,h="h",b<1&&(b=1);break;case"s":(v=o[l].v.match(/\.0+$/))&&(w=Math.max(w,v[0].length-1),b=4),b<3&&(b=3);case"d":case"y":case"e":h=o[l].t;break;case"M":h=o[l].t,b<2&&(b=2);break;case"m":"s"===h&&(o[l].t="M",b<2&&(b=2));break;case"X":break;case"Z":b<1&&o[l].v.match(/[Hh]/)&&(b=1),b<2&&o[l].v.match(/[Mm]/)&&(b=2),b<3&&o[l].v.match(/[Ss]/)&&(b=3)}switch(b){case 0:break;case 1:case 2:case 3:n.u>=.5&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H),n.H>=24&&(n.H=0,++n.D,(g=Y(n.D)).u=n.u,g.S=n.S,g.M=n.M,g.H=n.H,n=g);break;case 4:switch(w){case 1:n.u=Math.round(10*n.u)/10;break;case 2:n.u=Math.round(100*n.u)/100;break;case 3:n.u=Math.round(1e3*n.u)/1e3}n.u>=1&&(n.u=0,++n.S),n.S>=60&&(n.S=0,++n.M),n.M>=60&&(n.M=0,++n.H),n.H>=24&&(n.H=0,++n.D,(g=Y(n.D)).u=n.u,g.S=n.S,g.M=n.M,g.H=n.H,n=g)}var T,y="";for(l=0;l<o.length;++l)switch(o[l].t){case"t":case"T":case" ":case"D":break;case"X":o[l].v="",o[l].t=";";break;case"d":case"m":case"y":case"h":case"H":case"M":case"s":case"e":case"b":case"Z":o[l].v=Q(o[l].t.charCodeAt(0),o[l].v,n,w),o[l].t="t";break;case"n":case"?":for(T=l+1;null!=o[T]&&("?"===(f=o[T].t)||"D"===f||(" "===f||"t"===f)&&null!=o[T+1]&&("?"===o[T+1].t||"t"===o[T+1].t&&"/"===o[T+1].v)||"("===o[l].t&&(" "===f||"n"===f||")"===f)||"t"===f&&("/"===o[T].v||" "===o[T].v&&null!=o[T+1]&&"?"==o[T+1].t));)o[l].v+=o[T].v,o[T]={v:"",t:";"},++T;y+=o[l].v,l=T-1;break;case"G":o[l].t="t",o[l].v=q(t,r)}var E,k,_="";if(y.length>0){40==y.charCodeAt(0)?(E=t<0&&45===y.charCodeAt(0)?-t:t,k=de("n",y,E)):(k=de("n",y,E=t<0&&a>1?-t:t),E<0&&o[0]&&"t"==o[0].t&&(k=k.substr(1),o[0].v="-"+o[0].v)),T=k.length-1;var S=o.length;for(l=0;l<o.length;++l)if(null!=o[l]&&"t"!=o[l].t&&o[l].v.indexOf(".")>-1){S=l;break}var x=o.length;if(S===o.length&&-1===k.indexOf("E")){for(l=o.length-1;l>=0;--l)null!=o[l]&&-1!=="n?".indexOf(o[l].t)&&(T>=o[l].v.length-1?(T-=o[l].v.length,o[l].v=k.substr(T+1,o[l].v.length)):T<0?o[l].v="":(o[l].v=k.substr(0,T+1),T=-1),o[l].t="t",x=l);T>=0&&x<o.length&&(o[x].v=k.substr(0,T+1)+o[x].v)}else if(S!==o.length&&-1===k.indexOf("E")){for(T=k.indexOf(".")-1,l=S;l>=0;--l)if(null!=o[l]&&-1!=="n?".indexOf(o[l].t)){for(s=o[l].v.indexOf(".")>-1&&l===S?o[l].v.indexOf(".")-1:o[l].v.length-1,_=o[l].v.substr(s+1);s>=0;--s)T>=0&&("0"===o[l].v.charAt(s)||"#"===o[l].v.charAt(s))&&(_=k.charAt(T--)+_);o[l].v=_,o[l].t="t",x=l}for(T>=0&&x<o.length&&(o[x].v=k.substr(0,T+1)+o[x].v),T=k.indexOf(".")+1,l=S;l<o.length;++l)if(null!=o[l]&&(-1!=="n?(".indexOf(o[l].t)||l===S)){for(s=o[l].v.indexOf(".")>-1&&l===S?o[l].v.indexOf(".")+1:0,_=o[l].v.substr(0,s);s<o[l].v.length;++s)T<k.length&&(_+=k.charAt(T++));o[l].v=_,o[l].t="t",x=l}}}for(l=0;l<o.length;++l)null!=o[l]&&"n?".indexOf(o[l].t)>-1&&(E=a>1&&t<0&&l>0&&"-"===o[l-1].v?-t:t,o[l].v=de(o[l].t,o[l].v,E),o[l].t="t");var A="";for(l=0;l!==o.length;++l)null!=o[l]&&(A+=o[l].v);return A}(n[1],t,r,n[0])}function we(e,t){if("number"!=typeof t){t=+t||-1;for(var r=0;r<392;++r)if(null!=G[r]){if(G[r]==e){t=r;break}}else t<0&&(t=r);t<0&&(t=391)}return G[t]=e,t}function Te(e){for(var t=0;392!=t;++t)void 0!==e[t]&&we(e[t],t)}function ye(){var e;e||(e={}),e[0]="General",e[1]="0",e[2]="0.00",e[3]="#,##0",e[4]="#,##0.00",e[9]="0%",e[10]="0.00%",e[11]="0.00E+00",e[12]="# ?/?",e[13]="# ??/??",e[14]="m/d/yy",e[15]="d-mmm-yy",e[16]="d-mmm",e[17]="mmm-yy",e[18]="h:mm AM/PM",e[19]="h:mm:ss AM/PM",e[20]="h:mm",e[21]="h:mm:ss",e[22]="m/d/yy h:mm",e[37]="#,##0 ;(#,##0)",e[38]="#,##0 ;[Red](#,##0)",e[39]="#,##0.00;(#,##0.00)",e[40]="#,##0.00;[Red](#,##0.00)",e[45]="mm:ss",e[46]="[h]:mm:ss",e[47]="mmss.0",e[48]="##0.0E+0",e[49]="@",e[56]='"上午/下午 "hh"時"mm"分"ss"秒 "',G=e}var Ee={format:be,load:we,_table:G,load_table:Te,parse_date_code:Y,is_date:me,get_table:function(){return Ee._table=G}},ke={5:'"$"#,##0_);\\("$"#,##0\\)',6:'"$"#,##0_);[Red]\\("$"#,##0\\)',7:'"$"#,##0.00_);\\("$"#,##0.00\\)',8:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',23:"General",24:"General",25:"General",26:"General",27:"m/d/yy",28:"m/d/yy",29:"m/d/yy",30:"m/d/yy",31:"m/d/yy",32:"h:mm:ss",33:"h:mm:ss",34:"h:mm:ss",35:"h:mm:ss",36:"m/d/yy",41:'_(* #,##0_);_(* (#,##0);_(* "-"_);_(@_)',42:'_("$"* #,##0_);_("$"* (#,##0);_("$"* "-"_);_(@_)',43:'_(* #,##0.00_);_(* (#,##0.00);_(* "-"??_);_(@_)',44:'_("$"* #,##0.00_);_("$"* (#,##0.00);_("$"* "-"??_);_(@_)',50:"m/d/yy",51:"m/d/yy",52:"m/d/yy",53:"m/d/yy",54:"m/d/yy",55:"m/d/yy",56:"m/d/yy",57:"m/d/yy",58:"m/d/yy",59:"0",60:"0.00",61:"#,##0",62:"#,##0.00",63:'"$"#,##0_);\\("$"#,##0\\)',64:'"$"#,##0_);[Red]\\("$"#,##0\\)',65:'"$"#,##0.00_);\\("$"#,##0.00\\)',66:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',67:"0%",68:"0.00%",69:"# ?/?",70:"# ??/??",71:"m/d/yy",72:"m/d/yy",73:"d-mmm-yy",74:"d-mmm",75:"mmm-yy",76:"h:mm",77:"h:mm:ss",78:"m/d/yy h:mm",79:"mm:ss",80:"[h]:mm:ss",81:"mmss.0"},_e=/[dD]+|[mM]+|[yYeE]+|[Hh]+|[Ss]+/g,Se={"d.m":"d\\.m"};function xe(e,t){return we(Se[e]||e,t)}var Ae,Ce=function(){var e={version:"1.2.0"},t=function(){for(var e=0,t=new Array(256),r=0;256!=r;++r)e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=1&(e=r)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1)?-306674912^e>>>1:e>>>1,t[r]=e;return"undefined"!=typeof Int32Array?new Int32Array(t):t}(),r=function(e){var t=0,r=0,a=0,n="undefined"!=typeof Int32Array?new Int32Array(4096):new Array(4096);for(a=0;256!=a;++a)n[a]=e[a];for(a=0;256!=a;++a)for(r=e[a],t=256+a;t<4096;t+=256)r=n[t]=r>>>8^e[255&r];var s=[];for(a=1;16!=a;++a)s[a-1]="undefined"!=typeof Int32Array&&"function"==typeof n.subarray?n.subarray(256*a,256*a+256):n.slice(256*a,256*a+256);return s}(t),a=r[0],n=r[1],s=r[2],i=r[3],o=r[4],c=r[5],l=r[6],f=r[7],h=r[8],u=r[9],d=r[10],p=r[11],m=r[12],v=r[13],g=r[14];return e.table=t,e.bstr=function(e,r){for(var a=-1^r,n=0,s=e.length;n<s;)a=a>>>8^t[255&(a^e.charCodeAt(n++))];return~a},e.buf=function(e,r){for(var b=-1^r,w=e.length-15,T=0;T<w;)b=g[e[T++]^255&b]^v[e[T++]^b>>8&255]^m[e[T++]^b>>16&255]^p[e[T++]^b>>>24]^d[e[T++]]^u[e[T++]]^h[e[T++]]^f[e[T++]]^l[e[T++]]^c[e[T++]]^o[e[T++]]^i[e[T++]]^s[e[T++]]^n[e[T++]]^a[e[T++]]^t[e[T++]];for(w+=15;T<w;)b=b>>>8^t[255&(b^e[T++])];return~b},e.str=function(e,r){for(var a=-1^r,n=0,s=e.length,i=0,o=0;n<s;)(i=e.charCodeAt(n++))<128?a=a>>>8^t[255&(a^i)]:i<2048?a=(a=a>>>8^t[255&(a^(192|i>>6&31))])>>>8^t[255&(a^(128|63&i))]:i>=55296&&i<57344?(i=64+(1023&i),o=1023&e.charCodeAt(n++),a=(a=(a=(a=a>>>8^t[255&(a^(240|i>>8&7))])>>>8^t[255&(a^(128|i>>2&63))])>>>8^t[255&(a^(128|o>>6&15|(3&i)<<4))])>>>8^t[255&(a^(128|63&o))]):a=(a=(a=a>>>8^t[255&(a^(224|i>>12&15))])>>>8^t[255&(a^(128|i>>6&63))])>>>8^t[255&(a^(128|63&i))];return~a},e}(),Ie=function(){var e,t={};function r(e){if("/"==e.charAt(e.length-1))return-1===e.slice(0,-1).indexOf("/")?e:r(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(0,t+1)}function a(e){if("/"==e.charAt(e.length-1))return a(e.slice(0,-1));var t=e.lastIndexOf("/");return-1===t?e:e.slice(t+1)}function n(e,t){"string"==typeof t&&(t=new Date(t));var r=t.getHours();r=(r=r<<6|t.getMinutes())<<5|t.getSeconds()>>>1,e.write_shift(2,r);var a=t.getFullYear()-1980;a=(a=a<<4|t.getMonth()+1)<<5|t.getDate(),e.write_shift(2,a)}function s(e){Xr(e,0);for(var t={},r=0;e.l<=e.length-4;){var a=e.read_shift(2),n=e.read_shift(2),s=e.l+n,i={};switch(a){case 21589:1&(r=e.read_shift(1))&&(i.mtime=e.read_shift(4)),n>5&&(2&r&&(i.atime=e.read_shift(4)),4&r&&(i.ctime=e.read_shift(4))),i.mtime&&(i.mt=new Date(1e3*i.mtime));break;case 1:var o=e.read_shift(4),c=e.read_shift(4);i.usz=c*Math.pow(2,32)+o,o=e.read_shift(4),c=e.read_shift(4),i.csz=c*Math.pow(2,32)+o}e.l=s,t[a]=i}return t}function i(){return e||(e=Ae)}function o(e,t){if(80==e[0]&&75==e[1])return de(e,t);if(109==(32|e[0])&&105==(32|e[1]))return function(e,t){if("mime-version:"!=I(e.slice(0,13)).toLowerCase())throw new Error("Unsupported MAD header");var r=t&&t.root||"",a=(k&&Buffer.isBuffer(e)?e.toString("binary"):I(e)).split("\r\n"),n=0,s="";for(n=0;n<a.length;++n)if(s=a[n],/^Content-Location:/i.test(s)&&(s=s.slice(s.indexOf("file")),r||(r=s.slice(0,s.lastIndexOf("/")+1)),s.slice(0,r.length)!=r))for(;r.length>0&&(r=(r=r.slice(0,r.length-1)).slice(0,r.lastIndexOf("/")+1),s.slice(0,r.length)!=r););var i=(a[1]||"").match(/boundary="(.*?)"/);if(!i)throw new Error("MAD cannot find boundary");var o="--"+(i[1]||""),c={FileIndex:[],FullPaths:[]};u(c);var l,f=0;for(n=0;n<a.length;++n){var h=a[n];h!==o&&h!==o+"--"||(f++&&we(c,a.slice(l,n),r),l=n)}return c}(e,t);if(e.length<512)throw new Error("CFB file size "+e.length+" < 512");var r,a,n,s,i,o,d=512,p=[],m=e.slice(0,512);Xr(m,0);var v=function(e){if(80==e[e.l]&&75==e[e.l+1])return[0,0];e.chk(b,"Header Signature: "),e.l+=16;var t=e.read_shift(2,"u");return[e.read_shift(2,"u"),t]}(m);switch(r=v[0]){case 3:d=512;break;case 4:d=4096;break;case 0:if(0==v[1])return de(e,t);default:throw new Error("Major Version: Expected 3 or 4 saw "+r)}512!==d&&Xr(m=e.slice(0,d),28);var w=e.slice(0,d);!function(e,t){var r;switch(e.l+=2,r=e.read_shift(2)){case 9:if(3!=t)throw new Error("Sector Shift: Expected 9 saw "+r);break;case 12:if(4!=t)throw new Error("Sector Shift: Expected 12 saw "+r);break;default:throw new Error("Sector Shift: Expected 9 or 12 saw "+r)}e.chk("0600","Mini Sector Shift: "),e.chk("000000000000","Reserved: ")}(m,r);var T=m.read_shift(4,"i");if(3===r&&0!==T)throw new Error("# Directory Sectors: Expected 0 saw "+T);m.l+=4,s=m.read_shift(4,"i"),m.l+=4,m.chk("00100000","Mini Stream Cutoff Size: "),i=m.read_shift(4,"i"),a=m.read_shift(4,"i"),o=m.read_shift(4,"i"),n=m.read_shift(4,"i");for(var y=-1,E=0;E<109&&!((y=m.read_shift(4,"i"))<0);++E)p[E]=y;var _=function(e,t){for(var r=Math.ceil(e.length/t)-1,a=[],n=1;n<r;++n)a[n-1]=e.slice(n*t,(n+1)*t);return a[r-1]=e.slice(r*t),a}(e,d);l(o,n,_,d,p);var S=function(e,t,r,a){var n=e.length,s=[],i=[],o=[],c=[],l=a-1,f=0,h=0,u=0,d=0;for(f=0;f<n;++f)if(o=[],(u=f+t)>=n&&(u-=n),!i[u]){c=[];var p=[];for(h=u;h>=0;){p[h]=!0,i[h]=!0,o[o.length]=h,c.push(e[h]);var m=r[Math.floor(4*h/a)];if(a<4+(d=4*h&l))throw new Error("FAT boundary crossed: "+h+" 4 "+a);if(!e[m])break;if(p[h=Ur(e[m],d)])break}s[u]={nodes:o,data:dr([c])}}return s}(_,s,p,d);s<S.length&&(S[s].name="!Directory"),a>0&&i!==g&&(S[i].name="!MiniFAT"),S[p[0]].name="!FAT",S.fat_addrs=p,S.ssz=d;var x=[],A=[],C=[];!function(e,t,r,a,n,s,i,o){for(var l,u=0,d=a.length?2:0,p=t[e].data,m=0,v=0;m<p.length;m+=128){var b=p.slice(m,m+128);Xr(b,64),v=b.read_shift(2),l=mr(b,0,v-d),a.push(l);var w={name:l,type:b.read_shift(1),color:b.read_shift(1),L:b.read_shift(4,"i"),R:b.read_shift(4,"i"),C:b.read_shift(4,"i"),clsid:b.read_shift(16),state:b.read_shift(4,"i"),start:0,size:0};0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(w.ct=h(b,b.l-8)),0!==b.read_shift(2)+b.read_shift(2)+b.read_shift(2)+b.read_shift(2)&&(w.mt=h(b,b.l-8)),w.start=b.read_shift(4,"i"),w.size=b.read_shift(4,"i"),w.size<0&&w.start<0&&(w.size=w.type=0,w.start=g,w.name=""),5===w.type?(u=w.start,n>0&&u!==g&&(t[u].name="!StreamData")):w.size>=4096?(w.storage="fat",void 0===t[w.start]&&(t[w.start]=f(r,w.start,t.fat_addrs,t.ssz)),t[w.start].name=w.name,w.content=t[w.start].data.slice(0,w.size)):(w.storage="minifat",w.size<0?w.size=0:u!==g&&w.start!==g&&t[u]&&(w.content=c(w,t[u].data,(t[o]||{}).data))),w.content&&Xr(w.content,0),s[l]=w,i.push(w)}}(s,S,_,x,a,{},A,i),function(e,t,r){for(var a=0,n=0,s=0,i=0,o=0,c=r.length,l=[],f=[];a<c;++a)l[a]=f[a]=a,t[a]=r[a];for(;o<f.length;++o)n=e[a=f[o]].L,s=e[a].R,i=e[a].C,l[a]===a&&(-1!==n&&l[n]!==n&&(l[a]=l[n]),-1!==s&&l[s]!==s&&(l[a]=l[s])),-1!==i&&(l[i]=a),-1!==n&&a!=l[a]&&(l[n]=l[a],f.lastIndexOf(n)<o&&f.push(n)),-1!==s&&a!=l[a]&&(l[s]=l[a],f.lastIndexOf(s)<o&&f.push(s));for(a=1;a<c;++a)l[a]===a&&(-1!==s&&l[s]!==s?l[a]=l[s]:-1!==n&&l[n]!==n&&(l[a]=l[n]));for(a=1;a<c;++a)if(0!==e[a].type){if((o=a)!=l[o])do{o=l[o],t[a]=t[o]+"/"+t[a]}while(0!==o&&-1!==l[o]&&o!=l[o]);l[a]=-1}for(t[0]+="/",a=1;a<c;++a)2!==e[a].type&&(t[a]+="/")}(A,C,x),x.shift();var O={FileIndex:A,FullPaths:C};return t&&t.raw&&(O.raw={header:w,sectors:_}),O}function c(e,t,r){for(var a=e.start,n=e.size,s=[],i=a;r&&n>0&&i>=0;)s.push(t.slice(i*v,i*v+v)),n-=v,i=Ur(r,4*i);return 0===s.length?Kr(0):N(s).slice(0,e.size)}function l(e,t,r,a,n){var s=g;if(e===g){if(0!==t)throw new Error("DIFAT chain shorter than expected")}else if(-1!==e){var i=r[e],o=(a>>>2)-1;if(!i)return;for(var c=0;c<o&&(s=Ur(i,4*c))!==g;++c)n.push(s);t>=1&&l(Ur(i,a-4),t-1,r,a,n)}}function f(e,t,r,a,n){var s=[],i=[];n||(n=[]);var o=a-1,c=0,l=0;for(c=t;c>=0;){n[c]=!0,s[s.length]=c,i.push(e[c]);var f=r[Math.floor(4*c/a)];if(a<4+(l=4*c&o))throw new Error("FAT boundary crossed: "+c+" 4 "+a);if(!e[f])break;c=Ur(e[f],l)}return{nodes:s,data:dr([i])}}function h(e,t){return new Date(1e3*(Lr(e,t+4)/1e7*Math.pow(2,32)+Lr(e,t)/1e7-11644473600))}function u(e,t){var r=t||{},a=r.root||"Root Entry";if(e.FullPaths||(e.FullPaths=[]),e.FileIndex||(e.FileIndex=[]),e.FullPaths.length!==e.FileIndex.length)throw new Error("inconsistent CFB structure");0===e.FullPaths.length&&(e.FullPaths[0]=a+"/",e.FileIndex[0]={name:a,type:5}),r.CLSID&&(e.FileIndex[0].clsid=r.CLSID),function(e){var t="Sh33tJ5";if(!Ie.find(e,"/"+t)){var r=Kr(4);r[0]=55,r[1]=r[3]=50,r[2]=54,e.FileIndex.push({name:t,type:2,content:r,size:4,L:69,R:69,C:69}),e.FullPaths.push(e.FullPaths[0]+t),d(e)}}(e)}function d(e,t){u(e);for(var n=!1,s=!1,i=e.FullPaths.length-1;i>=0;--i){var o=e.FileIndex[i];switch(o.type){case 0:s?n=!0:(e.FileIndex.pop(),e.FullPaths.pop());break;case 1:case 2:case 5:s=!0,isNaN(o.R*o.L*o.C)&&(n=!0),o.R>-1&&o.L>-1&&o.R==o.L&&(n=!0);break;default:n=!0}}if(n||t){var c=new Date(1987,1,19),l=0,f=Object.create?Object.create(null):{},h=[];for(i=0;i<e.FullPaths.length;++i)f[e.FullPaths[i]]=!0,0!==e.FileIndex[i].type&&h.push([e.FullPaths[i],e.FileIndex[i]]);for(i=0;i<h.length;++i){var d=r(h[i][0]);for(s=f[d];!s;){for(;r(d)&&!f[r(d)];)d=r(d);h.push([d,{name:a(d).replace("/",""),type:1,clsid:T,ct:c,mt:c,content:null}]),f[d]=!0,s=f[d=r(h[i][0])]}}for(h.sort((function(e,t){return function(e,t){for(var r=e.split("/"),a=t.split("/"),n=0,s=0,i=Math.min(r.length,a.length);n<i;++n){if(s=r[n].length-a[n].length)return s;if(r[n]!=a[n])return r[n]<a[n]?-1:1}return r.length-a.length}(e[0],t[0])})),e.FullPaths=[],e.FileIndex=[],i=0;i<h.length;++i)e.FullPaths[i]=h[i][0],e.FileIndex[i]=h[i][1];for(i=0;i<h.length;++i){var p=e.FileIndex[i],m=e.FullPaths[i];if(p.name=a(m).replace("/",""),p.L=p.R=p.C=-(p.color=1),p.size=p.content?p.content.length:0,p.start=0,p.clsid=p.clsid||T,0===i)p.C=h.length>1?1:-1,p.size=0,p.type=5;else if("/"==m.slice(-1)){for(l=i+1;l<h.length&&r(e.FullPaths[l])!=m;++l);for(p.C=l>=h.length?-1:l,l=i+1;l<h.length&&r(e.FullPaths[l])!=r(m);++l);p.R=l>=h.length?-1:l,p.type=1}else r(e.FullPaths[i+1]||"")==r(m)&&(p.R=i+1),p.type=2}}}function p(e,t){var r=t||{};if("mad"==r.fileType)return function(e,t){for(var r=t||{},a=r.boundary||"SheetJS",n=["MIME-Version: 1.0",'Content-Type: multipart/related; boundary="'+(a="------="+a).slice(2)+'"',"","",""],s=e.FullPaths[0],i=s,o=e.FileIndex[0],c=1;c<e.FullPaths.length;++c)if(i=e.FullPaths[c].slice(s.length),(o=e.FileIndex[c]).size&&o.content&&"Sh33tJ5"!=i){i=i.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF]/g,(function(e){return"_x"+e.charCodeAt(0).toString(16)+"_"})).replace(/[\u0080-\uFFFF]/g,(function(e){return"_u"+e.charCodeAt(0).toString(16)+"_"}));for(var l=o.content,f=k&&Buffer.isBuffer(l)?l.toString("binary"):I(l),h=0,u=Math.min(1024,f.length),d=0,p=0;p<=u;++p)(d=f.charCodeAt(p))>=32&&d<128&&++h;var m=h>=4*u/5;n.push(a),n.push("Content-Location: "+(r.root||"file:///C:/SheetJS/")+i),n.push("Content-Transfer-Encoding: "+(m?"quoted-printable":"base64")),n.push("Content-Type: "+ve(o,i)),n.push(""),n.push(m?be(f):ge(f))}return n.push(a+"--\r\n"),n.join("\r\n")}(e,r);if(d(e),"zip"===r.fileType)return function(e,t){var r,a=t||{},s=[],i=[],o=Kr(1),c=a.compression?8:0,l=0,f=0,h=0,u=0,d=e.FullPaths[0],p=d,v=e.FileIndex[0],g=[],b=0;for(l=1;l<e.FullPaths.length;++l)if(p=e.FullPaths[l].slice(d.length),(v=e.FileIndex[l]).size&&v.content&&"Sh33tJ5"!=p){var w=h,T=Kr(p.length);for(f=0;f<p.length;++f)T.write_shift(1,127&p.charCodeAt(f));T=T.slice(0,T.l),g[u]="string"==typeof v.content?Ce.bstr(v.content,0):Ce.buf(v.content,0);var y="string"==typeof v.content?C(v.content):v.content;8==c&&(r=y,y=m?m.deflateRawSync(r):ne(r)),(o=Kr(30)).write_shift(4,67324752),o.write_shift(2,20),o.write_shift(2,0),o.write_shift(2,c),v.mt?n(o,v.mt):o.write_shift(4,0),o.write_shift(-4,g[u]),o.write_shift(4,y.length),o.write_shift(4,v.content.length),o.write_shift(2,T.length),o.write_shift(2,0),h+=o.length,s.push(o),h+=T.length,s.push(T),h+=y.length,s.push(y),(o=Kr(46)).write_shift(4,33639248),o.write_shift(2,0),o.write_shift(2,20),o.write_shift(2,0),o.write_shift(2,c),o.write_shift(4,0),o.write_shift(-4,g[u]),o.write_shift(4,y.length),o.write_shift(4,v.content.length),o.write_shift(2,T.length),o.write_shift(2,0),o.write_shift(2,0),o.write_shift(2,0),o.write_shift(2,0),o.write_shift(4,0),o.write_shift(4,w),b+=o.l,i.push(o),b+=T.length,i.push(T),++u}return(o=Kr(22)).write_shift(4,101010256),o.write_shift(2,0),o.write_shift(2,0),o.write_shift(2,u),o.write_shift(2,u),o.write_shift(4,b),o.write_shift(4,h),o.write_shift(2,0),N([N(s),N(i),o])}(e,r);var a=function(e){for(var t=0,r=0,a=0;a<e.FileIndex.length;++a){var n=e.FileIndex[a];if(n.content){var s=n.content.length;s>0&&(s<4096?t+=s+63>>6:r+=s+511>>9)}}for(var i=e.FullPaths.length+3>>2,o=t+127>>7,c=(t+7>>3)+r+i+o,l=c+127>>7,f=l<=109?0:Math.ceil((l-109)/127);c+l+f+127>>7>l;)f=++l<=109?0:Math.ceil((l-109)/127);var h=[1,f,l,o,i,r,t,0];return e.FileIndex[0].size=t<<6,h[7]=(e.FileIndex[0].start=h[0]+h[1]+h[2]+h[3]+h[4]+h[5])+(h[6]+7>>3),h}(e),s=Kr(a[7]<<9),i=0,o=0;for(i=0;i<8;++i)s.write_shift(1,w[i]);for(i=0;i<8;++i)s.write_shift(2,0);for(s.write_shift(2,62),s.write_shift(2,3),s.write_shift(2,65534),s.write_shift(2,9),s.write_shift(2,6),i=0;i<3;++i)s.write_shift(2,0);for(s.write_shift(4,0),s.write_shift(4,a[2]),s.write_shift(4,a[0]+a[1]+a[2]+a[3]-1),s.write_shift(4,0),s.write_shift(4,4096),s.write_shift(4,a[3]?a[0]+a[1]+a[2]-1:g),s.write_shift(4,a[3]),s.write_shift(-4,a[1]?a[0]-1:g),s.write_shift(4,a[1]),i=0;i<109;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);if(a[1])for(o=0;o<a[1];++o){for(;i<236+127*o;++i)s.write_shift(-4,i<a[2]?a[1]+i:-1);s.write_shift(-4,o===a[1]-1?g:o+1)}var c=function(e){for(o+=e;i<o-1;++i)s.write_shift(-4,i+1);e&&(++i,s.write_shift(-4,g))};for(o=i=0,o+=a[1];i<o;++i)s.write_shift(-4,S.DIFSECT);for(o+=a[2];i<o;++i)s.write_shift(-4,S.FATSECT);c(a[3]),c(a[4]);for(var l=0,f=0,h=e.FileIndex[0];l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&((f=h.content.length)<4096||(h.start=o,c(f+511>>9)));for(c(a[6]+7>>3);511&s.l;)s.write_shift(-4,S.ENDOFCHAIN);for(o=i=0,l=0;l<e.FileIndex.length;++l)(h=e.FileIndex[l]).content&&(!(f=h.content.length)||f>=4096||(h.start=o,c(f+63>>6)));for(;511&s.l;)s.write_shift(-4,S.ENDOFCHAIN);for(i=0;i<a[4]<<2;++i){var u=e.FullPaths[i];if(u&&0!==u.length){h=e.FileIndex[i],0===i&&(h.start=h.size?h.start-1:g);var p=0===i&&r.root||h.name;if(p.length>31&&(console.error("Name "+p+" will be truncated to "+p.slice(0,31)),p=p.slice(0,31)),f=2*(p.length+1),s.write_shift(64,p,"utf16le"),s.write_shift(2,f),s.write_shift(1,h.type),s.write_shift(1,h.color),s.write_shift(-4,h.L),s.write_shift(-4,h.R),s.write_shift(-4,h.C),h.clsid)s.write_shift(16,h.clsid,"hex");else for(l=0;l<4;++l)s.write_shift(4,0);s.write_shift(4,h.state||0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,0),s.write_shift(4,h.start),s.write_shift(4,h.size),s.write_shift(4,0)}else{for(l=0;l<17;++l)s.write_shift(4,0);for(l=0;l<3;++l)s.write_shift(4,-1);for(l=0;l<12;++l)s.write_shift(4,0)}}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>=4096)if(s.l=h.start+1<<9,k&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+511&-512;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;511&l;++l)s.write_shift(1,0)}for(i=1;i<e.FileIndex.length;++i)if((h=e.FileIndex[i]).size>0&&h.size<4096)if(k&&Buffer.isBuffer(h.content))h.content.copy(s,s.l,0,h.size),s.l+=h.size+63&-64;else{for(l=0;l<h.size;++l)s.write_shift(1,h.content[l]);for(;63&l;++l)s.write_shift(1,0)}if(k)s.l=s.length;else for(;s.l<s.length;)s.write_shift(1,0);return s}t.version="1.2.2";var m,v=64,g=-2,b="d0cf11e0a1b11ae1",w=[208,207,17,224,161,177,26,225],T="00000000000000000000000000000000",S={MAXREGSECT:-6,DIFSECT:-4,FATSECT:-3,ENDOFCHAIN:g,FREESECT:-1,HEADER_SIGNATURE:b,HEADER_MINOR_VERSION:"3e00",MAXREGSID:-6,NOSTREAM:-1,HEADER_CLSID:T,EntryTypes:["unknown","storage","stream","lockbytes","property","root"]};function I(e){for(var t=new Array(e.length),r=0;r<e.length;++r)t[r]=String.fromCharCode(e[r]);return t.join("")}for(var O,R,P=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],M=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258],L=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],U="undefined"!=typeof Uint8Array,B=U?new Uint8Array(256):[],W=0;W<256;++W)B[W]=(void 0,255&((R=139536&((O=W)<<1|O<<11)|558144&(O<<5|O<<15))>>16|R>>8|R));function z(e,t){var r=B[255&e];return t<=8?r>>>8-t:(r=r<<8|B[e>>8&255],t<=16?r>>>16-t:(r=r<<8|B[e>>16&255])>>>24-t)}function H(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=6?0:e[a+1]<<8))>>>r&3}function V(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=5?0:e[a+1]<<8))>>>r&7}function $(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=3?0:e[a+1]<<8))>>>r&31}function G(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=1?0:e[a+1]<<8))>>>r&127}function X(e,t,r){var a=7&t,n=t>>>3,s=(1<<r)-1,i=e[n]>>>a;return r<8-a?i&s:(i|=e[n+1]<<8-a,r<16-a?i&s:(i|=e[n+2]<<16-a,r<24-a?i&s:(i|=e[n+3]<<24-a)&s))}function j(e,t,r){var a=7&t,n=t>>>3;return a<=5?e[n]|=(7&r)<<a:(e[n]|=r<<a&255,e[n+1]=(7&r)>>8-a),t+3}function K(e,t,r){return r=(1&r)<<(7&t),e[t>>>3]|=r,t+1}function Y(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=r,t+8}function Z(e,t,r){var a=t>>>3;return r<<=7&t,e[a]|=255&r,r>>>=8,e[a+1]=255&r,e[a+2]=r>>>8,t+16}function J(e,t){var r=e.length,a=2*r>t?2*r:t+5,n=0;if(r>=t)return e;if(k){var s=A(a);if(e.copy)e.copy(s);else for(;n<e.length;++n)s[n]=e[n];return s}if(U){var i=new Uint8Array(a);if(i.set)i.set(e);else for(;n<r;++n)i[n]=e[n];return i}return e.length=a,e}function q(e){for(var t=new Array(e),r=0;r<e;++r)t[r]=0;return t}function Q(e,t,r){var a=1,n=0,s=0,i=0,o=0,c=e.length,l=U?new Uint16Array(32):q(32);for(s=0;s<32;++s)l[s]=0;for(s=c;s<r;++s)e[s]=0;c=e.length;var f=U?new Uint16Array(c):q(c);for(s=0;s<c;++s)l[n=e[s]]++,a<n&&(a=n),f[s]=0;for(l[0]=0,s=1;s<=a;++s)l[s+16]=o=o+l[s-1]<<1;for(s=0;s<c;++s)0!=(o=e[s])&&(f[s]=l[o+16]++);var h=0;for(s=0;s<c;++s)if(0!=(h=e[s]))for(o=z(f[s],a)>>a-h,i=(1<<a+4-h)-1;i>=0;--i)t[o|i<<h]=15&h|s<<4;return a}var ee=U?new Uint16Array(512):q(512),te=U?new Uint16Array(32):q(32);if(!U){for(var re=0;re<512;++re)ee[re]=0;for(re=0;re<32;++re)te[re]=0}!function(){for(var e=[],t=0;t<32;t++)e.push(5);Q(e,te,32);var r=[];for(t=0;t<=143;t++)r.push(8);for(;t<=255;t++)r.push(9);for(;t<=279;t++)r.push(7);for(;t<=287;t++)r.push(8);Q(r,ee,288)}();var ae=function(){for(var e=U?new Uint8Array(32768):[],t=0,r=0;t<L.length-1;++t)for(;r<L[t+1];++r)e[r]=t;for(;r<32768;++r)e[r]=29;var a=U?new Uint8Array(259):[];for(t=0,r=0;t<M.length-1;++t)for(;r<M[t+1];++r)a[r]=t;return function(t,r){return t.length<8?function(e,t){for(var r=0;r<e.length;){var a=Math.min(65535,e.length-r),n=r+a==e.length;for(t.write_shift(1,+n),t.write_shift(2,a),t.write_shift(2,65535&~a);a-- >0;)t[t.l++]=e[r++]}return t.l}(t,r):function(t,r){for(var n=0,s=0,i=U?new Uint16Array(32768):[];s<t.length;){var o=Math.min(65535,t.length-s);if(o<10){for(7&(n=j(r,n,+!(s+o!=t.length)))&&(n+=8-(7&n)),r.l=n/8|0,r.write_shift(2,o),r.write_shift(2,65535&~o);o-- >0;)r[r.l++]=t[s++];n=8*r.l}else{n=j(r,n,+!(s+o!=t.length)+2);for(var c=0;o-- >0;){var l=t[s],f=-1,h=0;if((f=i[c=32767&(c<<5^l)])&&((f|=-32768&s)>s&&(f-=32768),f<s))for(;t[f+h]==t[s+h]&&h<250;)++h;if(h>2){(l=a[h])<=22?n=Y(r,n,B[l+1]>>1)-1:(Y(r,n,3),Y(r,n+=5,B[l-23]>>5),n+=3);var u=l<8?0:l-4>>2;u>0&&(Z(r,n,h-M[l]),n+=u),l=e[s-f],n=Y(r,n,B[l]>>3),n-=3;var d=l<4?0:l-2>>1;d>0&&(Z(r,n,s-f-L[l]),n+=d);for(var p=0;p<h;++p)i[c]=32767&s,c=32767&(c<<5^t[s]),++s;o-=h-1}else l<=143?l+=48:n=K(r,n,1),n=Y(r,n,B[l]),i[c]=32767&s,++s}n=Y(r,n,0)-1}}return r.l=(n+7)/8|0,r.l}(t,r)}}();function ne(e){var t=Kr(50+Math.floor(1.1*e.length)),r=ae(e,t);return t.slice(0,r)}var se=U?new Uint16Array(32768):q(32768),ie=U?new Uint16Array(32768):q(32768),oe=U?new Uint16Array(128):q(128),ce=1,le=1;function fe(e,t){var r=$(e,t)+257,a=$(e,t+=5)+1,n=function(e,t){var r=7&t,a=t>>>3;return(e[a]|(r<=4?0:e[a+1]<<8))>>>r&15}(e,t+=5)+4;t+=4;for(var s=0,i=U?new Uint8Array(19):q(19),o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],c=1,l=U?new Uint8Array(8):q(8),f=U?new Uint8Array(8):q(8),h=i.length,u=0;u<n;++u)i[P[u]]=s=V(e,t),c<s&&(c=s),l[s]++,t+=3;var d=0;for(l[0]=0,u=1;u<=c;++u)f[u]=d=d+l[u-1]<<1;for(u=0;u<h;++u)0!=(d=i[u])&&(o[u]=f[d]++);var p=0;for(u=0;u<h;++u)if(0!=(p=i[u])){d=B[o[u]]>>8-p;for(var m=(1<<7-p)-1;m>=0;--m)oe[d|m<<p]=7&p|u<<3}var v=[];for(c=1;v.length<r+a;)switch(t+=7&(d=oe[G(e,t)]),d>>>=3){case 16:for(s=3+H(e,t),t+=2,d=v[v.length-1];s-- >0;)v.push(d);break;case 17:for(s=3+V(e,t),t+=3;s-- >0;)v.push(0);break;case 18:for(s=11+G(e,t),t+=7;s-- >0;)v.push(0);break;default:v.push(d),c<d&&(c=d)}var g=v.slice(0,r),b=v.slice(r);for(u=r;u<286;++u)g[u]=0;for(u=a;u<30;++u)b[u]=0;return ce=Q(g,se,286),le=Q(b,ie,30),t}function he(e,t){var r=function(e,t){if(3==e[0]&&!(3&e[1]))return[x(t),2];for(var r=0,a=0,n=A(t||1<<18),s=0,i=n.length>>>0,o=0,c=0;0==(1&a);)if(a=V(e,r),r+=3,a>>>1!=0)for(a>>1==1?(o=9,c=5):(r=fe(e,r),o=ce,c=le);;){!t&&i<s+32767&&(i=(n=J(n,s+32767)).length);var l=X(e,r,o),f=a>>>1==1?ee[l]:se[l];if(r+=15&f,0==((f>>>=4)>>>8&255))n[s++]=f;else{if(256==f)break;var h=(f-=257)<8?0:f-4>>2;h>5&&(h=0);var u=s+M[f];h>0&&(u+=X(e,r,h),r+=h),l=X(e,r,c),r+=15&(f=a>>>1==1?te[l]:ie[l]);var d=(f>>>=4)<4?0:f-2>>1,p=L[f];for(d>0&&(p+=X(e,r,d),r+=d),!t&&i<u&&(i=(n=J(n,u+100)).length);s<u;)n[s]=n[s-p],++s}}else{7&r&&(r+=8-(7&r));var m=e[r>>>3]|e[1+(r>>>3)]<<8;if(r+=32,m>0)for(!t&&i<s+m&&(i=(n=J(n,s+m)).length);m-- >0;)n[s++]=e[r>>>3],r+=8}return t?[n,r+7>>>3]:[n.slice(0,s),r+7>>>3]}(e.slice(e.l||0),t);return e.l+=r[1],r[0]}function ue(e,t){if(!e)throw new Error(t);"undefined"!=typeof console&&console.error(t)}function de(e,t){var r=e;Xr(r,0);var a={FileIndex:[],FullPaths:[]};u(a,{root:t.root});for(var n=r.length-4;(80!=r[n]||75!=r[n+1]||5!=r[n+2]||6!=r[n+3])&&n>=0;)--n;r.l=n+4,r.l+=4;var i=r.read_shift(2);r.l+=6;var o=r.read_shift(4);for(r.l=o,n=0;n<i;++n){r.l+=20;var c=r.read_shift(4),l=r.read_shift(4),f=r.read_shift(2),h=r.read_shift(2),d=r.read_shift(2);r.l+=8;var p=r.read_shift(4),m=s(r.slice(r.l+f,r.l+f+h));r.l+=f+h+d;var v=r.l;r.l=p+4,m&&m[1]&&((m[1]||{}).usz&&(l=m[1].usz),(m[1]||{}).csz&&(c=m[1].csz)),pe(r,c,l,a,m),r.l=v}return a}function pe(e,t,r,a,n){e.l+=2;var i=e.read_shift(2),o=e.read_shift(2),c=function(e){var t=65535&e.read_shift(2),r=65535&e.read_shift(2),a=new Date,n=31&r,s=15&(r>>>=5);r>>>=4,a.setMilliseconds(0),a.setFullYear(r+1980),a.setMonth(s-1),a.setDate(n);var i=31&t,o=63&(t>>>=5);return t>>>=6,a.setHours(t),a.setMinutes(o),a.setSeconds(i<<1),a}(e);if(8257&i)throw new Error("Unsupported ZIP encryption");e.read_shift(4);for(var l=e.read_shift(4),f=e.read_shift(4),h=e.read_shift(2),u=e.read_shift(2),d="",p=0;p<h;++p)d+=String.fromCharCode(e[e.l++]);if(u){var v=s(e.slice(e.l,e.l+u));(v[21589]||{}).mt&&(c=v[21589].mt),(v[1]||{}).usz&&(f=v[1].usz),(v[1]||{}).csz&&(l=v[1].csz),n&&((n[21589]||{}).mt&&(c=n[21589].mt),(n[1]||{}).usz&&(f=v[1].usz),(n[1]||{}).csz&&(l=v[1].csz))}e.l+=u;var g=e.slice(e.l,e.l+l);switch(o){case 8:g=function(e,t){if(!m)return he(e,t);var r=new(0,m.InflateRaw),a=r._processChunk(e.slice(e.l),r._finishFlushFlag);return e.l+=r.bytesRead,a}(e,f);break;case 0:break;default:throw new Error("Unsupported ZIP Compression method "+o)}var b=!1;8&i&&(134695760==e.read_shift(4)&&(e.read_shift(4),b=!0),l=e.read_shift(4),f=e.read_shift(4)),l!=t&&ue(b,"Bad compressed size: "+t+" != "+l),f!=r&&ue(b,"Bad uncompressed size: "+r+" != "+f),Te(a,d,g,{unsafe:!0,mt:c})}var me={htm:"text/html",xml:"text/xml",gif:"image/gif",jpg:"image/jpeg",png:"image/png",mso:"application/x-mso",thmx:"application/vnd.ms-officetheme",sh33tj5:"application/octet-stream"};function ve(e,t){if(e.ctype)return e.ctype;var r=e.name||"",a=r.match(/\.([^\.]+)$/);return a&&me[a[1]]||t&&(a=(r=t).match(/[\.\\]([^\.\\])+$/))&&me[a[1]]?me[a[1]]:"application/octet-stream"}function ge(e){for(var t=y(e),r=[],a=0;a<t.length;a+=76)r.push(t.slice(a,a+76));return r.join("\r\n")+"\r\n"}function be(e){var t=e.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7E-\xFF=]/g,(function(e){var t=e.charCodeAt(0).toString(16).toUpperCase();return"="+(1==t.length?"0"+t:t)}));"\n"==(t=t.replace(/ $/gm,"=20").replace(/\t$/gm,"=09")).charAt(0)&&(t="=0D"+t.slice(1));for(var r=[],a=(t=t.replace(/\r(?!\n)/gm,"=0D").replace(/\n\n/gm,"\n=0A").replace(/([^\r\n])\n/gm,"$1=0A")).split("\r\n"),n=0;n<a.length;++n){var s=a[n];if(0!=s.length)for(var i=0;i<s.length;){var o=76,c=s.slice(i,i+o);"="==c.charAt(o-1)?o--:"="==c.charAt(o-2)?o-=2:"="==c.charAt(o-3)&&(o-=3),c=s.slice(i,i+o),(i+=o)<s.length&&(c+="="),r.push(c)}else r.push("")}return r.join("\r\n")}function we(e,t,r){for(var a,n="",s="",i="",o=0;o<10;++o){var c=t[o];if(!c||c.match(/^\s*$/))break;var l=c.match(/^([^:]*?):\s*([^\s].*)$/);if(l)switch(l[1].toLowerCase()){case"content-location":n=l[2].trim();break;case"content-type":i=l[2].trim();break;case"content-transfer-encoding":s=l[2].trim()}}switch(++o,s.toLowerCase()){case"base64":a=C(E(t.slice(o).join("")));break;case"quoted-printable":a=function(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r];r<=e.length&&"="==a.charAt(a.length-1);)a=a.slice(0,a.length-1)+e[++r];t.push(a)}for(var n=0;n<t.length;++n)t[n]=t[n].replace(/[=][0-9A-Fa-f]{2}/g,(function(e){return String.fromCharCode(parseInt(e.slice(1),16))}));return C(t.join("\r\n"))}(t.slice(o));break;default:throw new Error("Unsupported Content-Transfer-Encoding "+s)}var f=Te(e,n.slice(r.length),a,{unsafe:!0});i&&(f.ctype=i)}function Te(e,t,r,n){var s=n&&n.unsafe;s||u(e);var i=!s&&Ie.find(e,t);if(!i){var o=e.FullPaths[0];t.slice(0,o.length)==o?o=t:("/"!=o.slice(-1)&&(o+="/"),o=(o+t).replace("//","/")),i={name:a(t),type:2},e.FileIndex.push(i),e.FullPaths.push(o),s||Ie.utils.cfb_gc(e)}return i.content=r,i.size=r?r.length:0,n&&(n.CLSID&&(i.clsid=n.CLSID),n.mt&&(i.mt=n.mt),n.ct&&(i.ct=n.ct)),i}return t.find=function(e,t){var r=e.FullPaths.map((function(e){return e.toUpperCase()})),a=r.map((function(e){var t=e.split("/");return t[t.length-("/"==e.slice(-1)?2:1)]})),n=!1;47===t.charCodeAt(0)?(n=!0,t=r[0].slice(0,-1)+t):n=-1!==t.indexOf("/");var s=t.toUpperCase(),i=!0===n?r.indexOf(s):a.indexOf(s);if(-1!==i)return e.FileIndex[i];var o=!s.match(D);for(s=s.replace(F,""),o&&(s=s.replace(D,"!")),i=0;i<r.length;++i){if((o?r[i].replace(D,"!"):r[i]).replace(F,"")==s)return e.FileIndex[i];if((o?a[i].replace(D,"!"):a[i]).replace(F,"")==s)return e.FileIndex[i]}return null},t.read=function(t,r){var a=r&&r.type;switch(a||k&&Buffer.isBuffer(t)&&(a="buffer"),a||"base64"){case"file":return function(t,r){return i(),o(e.readFileSync(t),r)}(t,r);case"base64":return o(C(E(t)),r);case"binary":return o(C(t),r)}return o(t,r)},t.parse=o,t.write=function(t,r){var a=p(t,r);switch(r&&r.type||"buffer"){case"file":return i(),e.writeFileSync(r.filename,a),a;case"binary":return"string"==typeof a?a:I(a);case"base64":return y("string"==typeof a?a:I(a));case"buffer":if(k)return Buffer.isBuffer(a)?a:_(a);case"array":return"string"==typeof a?C(a):a}return a},t.writeFile=function(t,r,a){i();var n=p(t,a);e.writeFileSync(r,n)},t.utils={cfb_new:function(e){var t={};return u(t,e),t},cfb_add:Te,cfb_del:function(e,t){u(e);var r=Ie.find(e,t);if(r)for(var a=0;a<e.FileIndex.length;++a)if(e.FileIndex[a]==r)return e.FileIndex.splice(a,1),e.FullPaths.splice(a,1),!0;return!1},cfb_mov:function(e,t,r){u(e);var n=Ie.find(e,t);if(n)for(var s=0;s<e.FileIndex.length;++s)if(e.FileIndex[s]==n)return e.FileIndex[s].name=a(r),e.FullPaths[s]=r,!0;return!1},cfb_gc:function(e){d(e,!0)},ReadShift:Wr,CheckField:Gr,prep_blob:Xr,bconcat:N,use_zlib:function(e){try{var t=new(0,e.InflateRaw);if(t._processChunk(new Uint8Array([3,0]),t._finishFlushFlag),!t.bytesRead)throw new Error("zlib does not expose bytesRead");m=e}catch(e){console.error("cannot use native zlib: "+(e.message||e))}},_deflateRaw:ne,_inflateRaw:he,consts:S},t}();function Oe(e){Ae=e}function Re(e){return"string"==typeof e?I(e):Array.isArray(e)?function(e){if("undefined"==typeof Uint8Array)throw new Error("Unsupported");return new Uint8Array(e)}(e):e}function Ne(e,t,r){if(void 0!==Ae&&Ae.writeFileSync)return r?Ae.writeFileSync(e,t,r):Ae.writeFileSync(e,t);if("undefined"!=typeof Deno){if(r&&"string"==typeof t)switch(r){case"utf8":t=new TextEncoder(r).encode(t);break;case"binary":t=I(t);break;default:throw new Error("Unsupported encoding "+r)}return Deno.writeFileSync(e,t)}var a="utf8"==r?Jt(t):t;if("undefined"!=typeof IE_SaveFile)return IE_SaveFile(a,e);if("undefined"!=typeof Blob){var n=new Blob([Re(a)],{type:"application/octet-stream"});if("undefined"!=typeof navigator&&navigator.msSaveBlob)return navigator.msSaveBlob(n,e);if("undefined"!=typeof saveAs)return saveAs(n,e);if("undefined"!=typeof URL&&"undefined"!=typeof document&&document.createElement&&URL.createObjectURL){var s=URL.createObjectURL(n);if("object"==typeof chrome&&"function"==typeof(chrome.downloads||{}).download)return URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),chrome.downloads.download({url:s,filename:e,saveAs:!0});var i=document.createElement("a");if(null!=i.download)return i.download=e,i.href=s,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL&&"undefined"!=typeof setTimeout&&setTimeout((function(){URL.revokeObjectURL(s)}),6e4),s}else if("undefined"!=typeof URL&&!URL.createObjectURL&&"object"==typeof chrome){var o="data:application/octet-stream;base64,"+function(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)s=(r=e[l++])>>2,i=(3&r)<<4|(a=e[l++])>>4,o=(15&a)<<2|(n=e[l++])>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=T.charAt(s)+T.charAt(i)+T.charAt(o)+T.charAt(c);return t}(new Uint8Array(Re(a)));return chrome.downloads.download({url:o,filename:e,saveAs:!0})}}if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var c=File(e);return c.open("w"),c.encoding="binary",Array.isArray(t)&&(t=O(t)),c.write(t),c.close(),t}catch(e){if(!e.message||-1==e.message.indexOf("onstruct"))throw e}throw new Error("cannot save file "+e)}function Fe(e){for(var t=Object.keys(e),r=[],a=0;a<t.length;++a)Object.prototype.hasOwnProperty.call(e,t[a])&&r.push(t[a]);return r}function De(e,t){for(var r=[],a=Fe(e),n=0;n!==a.length;++n)null==r[e[a[n]][t]]&&(r[e[a[n]][t]]=a[n]);return r}function Pe(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)t[e[r[a]]]=r[a];return t}function Me(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)t[e[r[a]]]=parseInt(r[a],10);return t}var Le=Date.UTC(1899,11,30,0,0,0),Ue=Date.UTC(1899,11,31,0,0,0),Be=Date.UTC(1904,0,1,0,0,0);function We(e,t){var r=(e.getTime()-Le)/864e5;return t?(r-=1462)<-1402?r-1:r:r<60?r-1:r}function ze(e){if(e>=60&&e<61)return e;var t=new Date;return t.setTime(24*(e>60?e:e+1)*60*60*1e3+Le),t}function He(e){var t=0,r=0,a=!1,n=e.match(/P([0-9\.]+Y)?([0-9\.]+M)?([0-9\.]+D)?T([0-9\.]+H)?([0-9\.]+M)?([0-9\.]+S)?/);if(!n)throw new Error("|"+e+"| is not an ISO8601 Duration");for(var s=1;s!=n.length;++s)if(n[s]){switch(r=1,s>3&&(a=!0),n[s].slice(n[s].length-1)){case"Y":throw new Error("Unsupported ISO Duration Field: "+n[s].slice(n[s].length-1));case"D":r*=24;case"H":r*=60;case"M":if(!a)throw new Error("Unsupported ISO Duration Field: M");r*=60}t+=r*parseInt(n[s],10)}return t}var Ve=/^(\d+):(\d+)(:\d+)?(\.\d+)?$/,$e=/^(\d+)-(\d+)-(\d+)$/,Ge=/^(\d+)-(\d+)-(\d+)[T ](\d+):(\d+)(:\d+)?(\.\d+)?$/;function Xe(e,t){if(e instanceof Date)return e;var r=e.match(Ve);return r?new Date((t?Be:Ue)+1e3*(60*(60*parseInt(r[1],10)+parseInt(r[2],10))+(r[3]?parseInt(r[3].slice(1),10):0))+(r[4]?parseInt((r[4]+"000").slice(1,4),10):0)):(r=e.match($e))?new Date(Date.UTC(+r[1],+r[2]-1,+r[3],0,0,0,0)):(r=e.match(Ge))?new Date(Date.UTC(+r[1],+r[2]-1,+r[3],+r[4],+r[5],r[6]&&parseInt(r[6].slice(1),10)||0,r[7]&&parseInt((r[7]+"0000").slice(1,4),10)||0)):new Date(e)}function je(e,t){if(k&&Buffer.isBuffer(e)){if(t&&S){if(255==e[0]&&254==e[1])return Jt(e.slice(2).toString("utf16le"));if(254==e[1]&&255==e[2])return Jt(p(e.slice(2).toString("binary")))}return e.toString("binary")}if("undefined"!=typeof TextDecoder)try{if(t){if(255==e[0]&&254==e[1])return Jt(new TextDecoder("utf-16le").decode(e.slice(2)));if(254==e[0]&&255==e[1])return Jt(new TextDecoder("utf-16be").decode(e.slice(2)))}var r={"€":"","‚":"","ƒ":"","„":"","…":"","†":"","‡":"","ˆ":"","‰":"","Š":"","‹":"","Œ":"","Ž":"","‘":"","’":"","“":"","”":"","•":"","–":"","—":"","˜":"","™":"","š":"","›":"","œ":"","ž":"","Ÿ":""};return Array.isArray(e)&&(e=new Uint8Array(e)),new TextDecoder("latin1").decode(e).replace(/[€‚ƒ„…†‡ˆ‰Š‹ŒŽ‘’“”•–—˜™š›œžŸ]/g,(function(e){return r[e]||e}))}catch(e){}var a=[],n=0;try{for(n=0;n<e.length-65536;n+=65536)a.push(String.fromCharCode.apply(0,e.slice(n,n+65536)));a.push(String.fromCharCode.apply(0,e.slice(n)))}catch(t){try{for(;n<e.length-16384;n+=16384)a.push(String.fromCharCode.apply(0,e.slice(n,n+16384)));a.push(String.fromCharCode.apply(0,e.slice(n)))}catch(t){for(;n!=e.length;++n)a.push(String.fromCharCode(e[n]))}}return a.join("")}function Ke(e){if("undefined"!=typeof JSON&&!Array.isArray(e))return JSON.parse(JSON.stringify(e));if("object"!=typeof e||null==e)return e;if(e instanceof Date)return new Date(e.getTime());var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=Ke(e[r]));return t}function Ye(e,t){for(var r="";r.length<t;)r+=e;return r}function Ze(e){var t=Number(e);if(!isNaN(t))return isFinite(t)?t:NaN;if(!/\d/.test(e))return t;var r=1,a=e.replace(/([\d]),([\d])/g,"$1$2").replace(/[$]/g,"").replace(/[%]/g,(function(){return r*=100,""}));return isNaN(t=Number(a))?(a=a.replace(/[(]([^()]*)[)]/,(function(e,t){return r=-r,t})),isNaN(t=Number(a))?t:t/r):t/r}var Je=/^(0?\d|1[0-2])(?:|:([0-5]?\d)(?:|(\.\d+)(?:|:([0-5]?\d))|:([0-5]?\d)(|\.\d+)))\s+([ap])m?$/,qe=/^([01]?\d|2[0-3])(?:|:([0-5]?\d)(?:|(\.\d+)(?:|:([0-5]?\d))|:([0-5]?\d)(|\.\d+)))$/,Qe=/^(\d+)-(\d+)-(\d+)[T ](\d+):(\d+)(:\d+)(\.\d+)?[Z]?$/,et=-177984e5==new Date("6/9/69 00:00 UTC").valueOf(),tt=["january","february","march","april","may","june","july","august","september","october","november","december"];function rt(e){if(Qe.test(e))return-1==e.indexOf("Z")?st(new Date(e)):new Date(e);var t=e.toLowerCase(),r=t.replace(/\s+/g," ").trim(),a=r.match(Je);if(a)return function(e){return e[2]?e[3]?e[4]?new Date(Date.UTC(1899,11,31,+e[1]%12+("p"==e[7]?12:0),+e[2],+e[4],1e3*parseFloat(e[3]))):new Date(Date.UTC(1899,11,31,"p"==e[7]?12:0,+e[1],+e[2],1e3*parseFloat(e[3]))):e[5]?new Date(Date.UTC(1899,11,31,+e[1]%12+("p"==e[7]?12:0),+e[2],+e[5],e[6]?1e3*parseFloat(e[6]):0)):new Date(Date.UTC(1899,11,31,+e[1]%12+("p"==e[7]?12:0),+e[2],0,0)):new Date(Date.UTC(1899,11,31,+e[1]%12+("p"==e[7]?12:0),0,0,0))}(a);if(a=r.match(qe))return function(e){return e[2]?e[3]?e[4]?new Date(Date.UTC(1899,11,31,+e[1],+e[2],+e[4],1e3*parseFloat(e[3]))):new Date(Date.UTC(1899,11,31,0,+e[1],+e[2],1e3*parseFloat(e[3]))):e[5]?new Date(Date.UTC(1899,11,31,+e[1],+e[2],+e[5],e[6]?1e3*parseFloat(e[6]):0)):new Date(Date.UTC(1899,11,31,+e[1],+e[2],0,0)):new Date(Date.UTC(1899,11,31,+e[1],0,0,0))}(a);if(a=r.match(Ge))return new Date(Date.UTC(+a[1],+a[2]-1,+a[3],+a[4],+a[5],a[6]&&parseInt(a[6].slice(1),10)||0,a[7]&&parseInt((a[7]+"0000").slice(1,4),10)||0));var n=new Date(et&&-1==e.indexOf("UTC")?e+" UTC":e),s=new Date(NaN),i=n.getYear(),o=(n.getMonth(),n.getDate());if(isNaN(o))return s;if(t.match(/jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec/)){if((t=t.replace(/[^a-z]/g,"").replace(/([^a-z]|^)[ap]m?([^a-z]|$)/,"")).length>3&&-1==tt.indexOf(t))return s}else if(t.replace(/[ap]m?/,"").match(/[a-z]/))return s;return i<0||i>8099||e.match(/[^-0-9:,\/\\\ ]/)?s:n}var at=function(){var e=5=="abacaba".split(/(:?b)/i).length;return function(t,r,a){if(e||"string"==typeof r)return t.split(r);for(var n=t.split(r),s=[n[0]],i=1;i<n.length;++i)s.push(a),s.push(n[i]);return s}}();function nt(e){return new Date(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate(),e.getUTCHours(),e.getUTCMinutes(),e.getUTCSeconds(),e.getUTCMilliseconds())}function st(e){return new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()))}function it(e){var t=e.slice(0,1024).indexOf("<!DOCTYPE");if(-1==t)return e;var r=e.match(/<[\w]/);return r?e.slice(0,t)+e.slice(r.index):e}function ot(e,t,r){for(var a=[],n=e.indexOf(t);n>-1;){var s=e.indexOf(r,n+t.length);if(-1==s)break;a.push(e.slice(n,s+r.length)),n=e.indexOf(t,s+r.length)}return a.length>0?a:null}function ct(e,t,r){var a=[],n=0,s=e.indexOf(t);if(-1==s)return e;for(;s>-1;){a.push(e.slice(n,s));var i=e.indexOf(r,s+t.length);if(-1==i)break;-1==(s=e.indexOf(t,n=i+r.length))&&a.push(e.slice(n))}return a.join("")}var lt={" ":1,"\t":1,"\r":1,"\n":1,">":1};function ft(e,t){for(var r=e.indexOf("<"+t),a=t.length+1,n=e.length;r>=0&&r<=n-a&&!lt[e.charAt(r+a)];)r=e.indexOf("<"+t,r+1);if(-1===r)return null;var s=e.indexOf(">",r+t.length);if(-1===s)return null;var i="</"+t+">",o=e.indexOf(i,s);return-1==o?null:[e.slice(r,o+i.length),e.slice(s+1,o)]}var ht=function(){var e={};return function(t,r){var a=e[r];a||(e[r]=a=[new RegExp("<(?:\\w+:)?"+r+"\\b[^<>]*>","g"),new RegExp("</(?:\\w+:)?"+r+">","g")]),a[0].lastIndex=a[1].lastIndex=0;var n=a[0].exec(t);if(!n)return null;var s=n.index,i=a[0].lastIndex;if(a[1].lastIndex=a[0].lastIndex,!(n=a[1].exec(t)))return null;var o=n.index,c=a[1].lastIndex;return[t.slice(s,c),t.slice(i,o)]}}(),ut=function(){var e={};return function(t,r){var a,n=[],s=e[r];for(s||(e[r]=s=[new RegExp("<(?:\\w+:)?"+r+"\\b[^<>]*>","g"),new RegExp("</(?:\\w+:)?"+r+">","g")]),s[0].lastIndex=s[1].lastIndex=0;a=s[0].exec(t);){var i=a.index;if(s[1].lastIndex=s[0].lastIndex,!(a=s[1].exec(t)))return null;var o=s[1].lastIndex;n.push(t.slice(i,o)),s[0].lastIndex=s[1].lastIndex}return 0==n.length?null:n}}(),dt=function(){var e={};return function(t,r){var a,n=[],s=e[r];s||(e[r]=s=[new RegExp("<(?:\\w+:)?"+r+"\\b[^<>]*>","g"),new RegExp("</(?:\\w+:)?"+r+">","g")]),s[0].lastIndex=s[1].lastIndex=0;for(var i=0,o=0;a=s[0].exec(t);){if(i=a.index,n.push(t.slice(o,i)),o=i,s[1].lastIndex=s[0].lastIndex,!(a=s[1].exec(t)))return null;o=s[1].lastIndex,s[0].lastIndex=s[1].lastIndex}return n.push(t.slice(o)),0==n.length?"":n.join("")}}(),pt=function(){var e={};return function(t,r){var a,n=[],s=e[r];for(s||(e[r]=s=[new RegExp("<"+r+"\\b[^<>]*>","ig"),new RegExp("</"+r+">","ig")]),s[0].lastIndex=s[1].lastIndex=0;a=s[0].exec(t);){var i=a.index;if(s[1].lastIndex=s[0].lastIndex,!(a=s[1].exec(t)))return null;var o=s[1].lastIndex;n.push(t.slice(i,o)),s[0].lastIndex=s[1].lastIndex}return 0==n.length?null:n}}();function mt(e){return e?e.content&&e.type?je(e.content,!0):e.data?m(e.data):e.asNodeBuffer&&k?m(e.asNodeBuffer().toString("binary")):e.asBinary?m(e.asBinary()):e._data&&e._data.getContent?m(je(Array.prototype.slice.call(e._data.getContent(),0))):null:null}function vt(e){if(!e)return null;if(e.data)return u(e.data);if(e.asNodeBuffer&&k)return e.asNodeBuffer();if(e._data&&e._data.getContent){var t=e._data.getContent();return"string"==typeof t?u(t):Array.prototype.slice.call(t)}return e.content&&e.type?e.content:null}function gt(e,t){for(var r=e.FullPaths||Fe(e.files),a=t.toLowerCase().replace(/[\/]/g,"\\"),n=a.replace(/\\/g,"/"),s=0;s<r.length;++s){var i=r[s].replace(/^Root Entry[\/]/,"").toLowerCase();if(a==i||n==i)return e.files?e.files[r[s]]:e.FileIndex[s]}return null}function bt(e,t){var r=gt(e,t);if(null==r)throw new Error("Cannot find file "+t+" in zip");return r}function wt(e,t,r){if(!r)return(a=bt(e,t))&&".bin"===a.name.slice(-4)?vt(a):mt(a);var a;if(!t)return null;try{return wt(e,t)}catch(e){return null}}function Tt(e,t,r){if(!r)return mt(bt(e,t));if(!t)return null;try{return Tt(e,t)}catch(e){return null}}function yt(e,t,r){if(!r)return vt(bt(e,t));if(!t)return null;try{return yt(e,t)}catch(e){return null}}function Et(e){for(var t=e.FullPaths||Fe(e.files),r=[],a=0;a<t.length;++a)"/"!=t[a].slice(-1)&&r.push(t[a].replace(/^Root Entry[\/]/,""));return r.sort()}function kt(e,t,r){if(e.FullPaths){var a;if("string"==typeof r)return a=k?_(r):function(e){for(var t=[],r=0,a=e.length+250,n=x(e.length+255),s=0;s<e.length;++s){var i=e.charCodeAt(s);if(i<128)n[r++]=i;else if(i<2048)n[r++]=192|i>>6&31,n[r++]=128|63&i;else if(i>=55296&&i<57344){i=64+(1023&i);var o=1023&e.charCodeAt(++s);n[r++]=240|i>>8&7,n[r++]=128|i>>2&63,n[r++]=128|o>>6&15|(3&i)<<4,n[r++]=128|63&o}else n[r++]=224|i>>12&15,n[r++]=128|i>>6&63,n[r++]=128|63&i;r>a&&(t.push(n.slice(0,r)),r=0,n=x(65535),a=65530)}return t.push(n.slice(0,r)),N(t)}(r),Ie.utils.cfb_add(e,t,a);Ie.utils.cfb_add(e,t,r)}else e.file(t,r)}function _t(){return Ie.utils.cfb_new()}function St(e,t){switch(t.type){case"base64":return Ie.read(e,{type:"base64"});case"binary":return Ie.read(e,{type:"binary"});case"buffer":case"array":return Ie.read(e,{type:"buffer"})}throw new Error("Unrecognized type "+t.type)}function xt(e,t){if("/"==e.charAt(0))return e.slice(1);var r=t.split("/");"/"!=t.slice(-1)&&r.pop();for(var a=e.split("/");0!==a.length;){var n=a.shift();".."===n?r.pop():"."!==n&&r.push(n)}return r.join("/")}var At='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\r\n',Ct=/\s([^"\s?>\/]+)\s*=\s*((?:")([^"]*)(?:")|(?:')([^']*)(?:')|([^'">\s]+))/g,It=/<[\/\?]?[a-zA-Z0-9:_-]+(?:\s+[^"\s?<>\/]+\s*=\s*(?:"[^"]*"|'[^']*'|[^'"<>\s=]+))*\s*[\/\?]?>/gm,Ot=At.match(It)?It:/<[^<>]*>/g,Rt=/<\w*:/,Nt=/<(\/?)\w+:/;function Ft(e,t,r){for(var a={},n=0,s=0;n!==e.length&&32!==(s=e.charCodeAt(n))&&10!==s&&13!==s;++n);if(t||(a[0]=e.slice(0,n)),n===e.length)return a;var i=e.match(Ct),o=0,c="",l=0,f="",h="",u=1;if(i)for(l=0;l!=i.length;++l){for(h=i[l].slice(1),s=0;s!=h.length&&61!==h.charCodeAt(s);++s);for(f=h.slice(0,s).trim();32==h.charCodeAt(s+1);)++s;for(u=34==(n=h.charCodeAt(s+1))||39==n?1:0,c=h.slice(s+1+u,h.length-u),o=0;o!=f.length&&58!==f.charCodeAt(o);++o);if(o===f.length)f.indexOf("_")>0&&(f=f.slice(0,f.indexOf("_"))),a[f]=c,r||(a[f.toLowerCase()]=c);else{var d=(5===o&&"xmlns"===f.slice(0,5)?"xmlns":"")+f.slice(o+1);if(a[d]&&"ext"==f.slice(o-3,o))continue;a[d]=c,r||(a[d.toLowerCase()]=c)}}return a}function Dt(e){return e.replace(Nt,"<$1")}var Pt={"&quot;":'"',"&apos;":"'","&gt;":">","&lt;":"<","&amp;":"&"},Mt=Pe(Pt),Lt=function(){var e=/&(?:quot|apos|gt|lt|amp|#x?([\da-fA-F]+));/gi,t=/_x([\da-fA-F]{4})_/gi;function r(a){var n=a+"",s=n.indexOf("<![CDATA[");if(-1==s)return n.replace(e,(function(e,t){return Pt[e]||String.fromCharCode(parseInt(t,e.indexOf("x")>-1?16:10))||e})).replace(t,(function(e,t){return String.fromCharCode(parseInt(t,16))}));var i=n.indexOf("]]>");return r(n.slice(0,s))+n.slice(s+9,i)+r(n.slice(i+3))}return function(e,t){var a=r(e);return t?a.replace(/\r\n/g,"\n"):a}}(),Ut=/[&<>'"]/g,Bt=/[\u0000-\u0008\u000b-\u001f\uFFFE-\uFFFF]/g;function Wt(e){return(e+"").replace(Ut,(function(e){return Mt[e]})).replace(Bt,(function(e){return"_x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+"_"}))}function zt(e){return Wt(e).replace(/ /g,"_x0020_")}var Ht=/[\u0000-\u001f]/g;function Vt(e){return(e+"").replace(Ut,(function(e){return Mt[e]})).replace(/\n/g,"<br/>").replace(Ht,(function(e){return"&#x"+("000"+e.charCodeAt(0).toString(16)).slice(-4)+";"}))}var $t=function(){var e=/&#(\d+);/g;function t(e,t){return String.fromCharCode(parseInt(t,10))}return function(r){return r.replace(e,t)}}();function Gt(e){switch(e){case 1:case!0:case"1":case"true":return!0;case 0:case!1:case"0":case"false":return!1}return!1}function Xt(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0;r<e.length;)(a=e.charCodeAt(r++))<128?t+=String.fromCharCode(a):(n=e.charCodeAt(r++),a>191&&a<224?(i=(31&a)<<6,i|=63&n,t+=String.fromCharCode(i)):(s=e.charCodeAt(r++),a<240?t+=String.fromCharCode((15&a)<<12|(63&n)<<6|63&s):(o=((7&a)<<18|(63&n)<<12|(63&s)<<6|63&(i=e.charCodeAt(r++)))-65536,t+=String.fromCharCode(55296+(o>>>10&1023)),t+=String.fromCharCode(56320+(1023&o)))));return t}function jt(e){var t,r,a,n=x(2*e.length),s=1,i=0,o=0;for(r=0;r<e.length;r+=s)s=1,(a=e.charCodeAt(r))<128?t=a:a<224?(t=64*(31&a)+(63&e.charCodeAt(r+1)),s=2):a<240?(t=4096*(15&a)+64*(63&e.charCodeAt(r+1))+(63&e.charCodeAt(r+2)),s=3):(s=4,t=262144*(7&a)+4096*(63&e.charCodeAt(r+1))+64*(63&e.charCodeAt(r+2))+(63&e.charCodeAt(r+3)),o=55296+((t-=65536)>>>10&1023),t=56320+(1023&t)),0!==o&&(n[i++]=255&o,n[i++]=o>>>8,o=0),n[i++]=t%256,n[i++]=t>>>8;return n.slice(0,i).toString("ucs2")}function Kt(e){return _(e,"binary").toString("utf8")}var Yt="foo bar bazâð£",Zt=k&&(Kt(Yt)==Xt(Yt)&&Kt||jt(Yt)==Xt(Yt)&&jt)||Xt,Jt=k?function(e){return _(e,"utf8").toString("binary")}:function(e){for(var t=[],r=0,a=0,n=0;r<e.length;)switch(a=e.charCodeAt(r++),!0){case a<128:t.push(String.fromCharCode(a));break;case a<2048:t.push(String.fromCharCode(192+(a>>6))),t.push(String.fromCharCode(128+(63&a)));break;case a>=55296&&a<57344:a-=55296,n=e.charCodeAt(r++)-56320+(a<<10),t.push(String.fromCharCode(240+(n>>18&7))),t.push(String.fromCharCode(144+(n>>12&63))),t.push(String.fromCharCode(128+(n>>6&63))),t.push(String.fromCharCode(128+(63&n)));break;default:t.push(String.fromCharCode(224+(a>>12))),t.push(String.fromCharCode(128+(a>>6&63))),t.push(String.fromCharCode(128+(63&a)))}return t.join("")},qt=function(){var e=[["nbsp"," "],["middot","·"],["quot",'"'],["apos","'"],["gt",">"],["lt","<"],["amp","&"]].map((function(e){return[new RegExp("&"+e[0]+";","ig"),e[1]]}));return function(t){for(var r=t.replace(/^[\t\n\r ]+/,"").replace(/(^|[^\t\n\r ])[\t\n\r ]+$/,"$1").replace(/>\s+/g,">").replace(/\b\s+</g,"<").replace(/[\t\n\r ]+/g," ").replace(/<\s*[bB][rR]\s*\/?>/g,"\n").replace(/<[^<>]*>/g,""),a=0;a<e.length;++a)r=r.replace(e[a][0],e[a][1]);return r}}(),Qt=/<\/?(?:vt:)?variant>/g,er=/<(?:vt:)([^<"'>]*)>([\s\S]*)</;function tr(e,t){var r=Ft(e),a=ut(e,r.baseType)||[],n=[];if(a.length!=r.size){if(t.WTF)throw new Error("unexpected vector length "+a.length+" != "+r.size);return n}return a.forEach((function(e){var t=e.replace(Qt,"").match(er);t&&n.push({v:Zt(t[2]),t:t[1]})})),n}var rr=/(^\s|\s$|\n)/;function ar(e,t){return"<"+e+(t.match(rr)?' xml:space="preserve"':"")+">"+t+"</"+e+">"}function nr(e){return Fe(e).map((function(t){return" "+t+'="'+e[t]+'"'})).join("")}function sr(e,t,r){return"<"+e+(null!=r?nr(r):"")+(null!=t?(t.match(rr)?' xml:space="preserve"':"")+">"+t+"</"+e:"/")+">"}function ir(e,t){try{return e.toISOString().replace(/\.\d*/,"")}catch(e){if(t)throw e}return""}function or(e){if(k&&Buffer.isBuffer(e))return e.toString("utf8");if("string"==typeof e)return e;if("undefined"!=typeof Uint8Array&&e instanceof Uint8Array)return Zt(O(R(e)));throw new Error("Bad input format: expected Buffer or string")}var cr=/<([\/]?)([^\s?><!\/:"]*:|)([^\s?<>:\/"]+)(?:\s+[^<>=?"'\s]+="[^"]*?")*\s*[\/]?>/gm,lr={CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/metadata/core-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/custom-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/extended-properties",CT:"http://schemas.openxmlformats.org/package/2006/content-types",RELS:"http://schemas.openxmlformats.org/package/2006/relationships",TCMNT:"http://schemas.microsoft.com/office/spreadsheetml/2018/threadedcomments",dc:"http://purl.org/dc/elements/1.1/",dcterms:"http://purl.org/dc/terms/",dcmitype:"http://purl.org/dc/dcmitype/",mx:"http://schemas.microsoft.com/office/mac/excel/2008/main",r:"http://schemas.openxmlformats.org/officeDocument/2006/relationships",sjs:"http://schemas.openxmlformats.org/package/2006/sheetjs/core-properties",vt:"http://schemas.openxmlformats.org/officeDocument/2006/docPropsVTypes",xsi:"http://www.w3.org/2001/XMLSchema-instance",xsd:"http://www.w3.org/2001/XMLSchema"},fr=["http://schemas.openxmlformats.org/spreadsheetml/2006/main","http://purl.oclc.org/ooxml/spreadsheetml/main","http://schemas.microsoft.com/office/excel/2006/main","http://schemas.microsoft.com/office/excel/2006/2"],hr={o:"urn:schemas-microsoft-com:office:office",x:"urn:schemas-microsoft-com:office:excel",ss:"urn:schemas-microsoft-com:office:spreadsheet",dt:"uuid:C2F41010-65B3-11d1-A29F-00AA00C14882",mv:"http://macVmlSchemaUri",v:"urn:schemas-microsoft-com:vml",html:"http://www.w3.org/TR/REC-html40"},ur=function(e){for(var t=[],r=0;r<e[0].length;++r)if(e[0][r])for(var a=0,n=e[0][r].length;a<n;a+=10240)t.push.apply(t,e[0][r].slice(a,a+10240));return t},dr=k?function(e){return e[0].length>0&&Buffer.isBuffer(e[0][0])?Buffer.concat(e[0].map((function(e){return Buffer.isBuffer(e)?e:_(e)}))):ur(e)}:ur,pr=function(e,t,r){for(var a=[],n=t;n<r;n+=2)a.push(String.fromCharCode(Pr(e,n)));return a.join("").replace(F,"")},mr=k?function(e,t,r){return Buffer.isBuffer(e)&&S?e.toString("utf16le",t,r).replace(F,""):pr(e,t,r)}:pr,vr=function(e,t,r){for(var a=[],n=t;n<t+r;++n)a.push(("0"+e[n].toString(16)).slice(-2));return a.join("")},gr=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("hex",t,t+r):vr(e,t,r)}:vr,br=function(e,t,r){for(var a=[],n=t;n<r;n++)a.push(String.fromCharCode(Dr(e,n)));return a.join("")},wr=k?function(e,t,r){return Buffer.isBuffer(e)?e.toString("utf8",t,r):br(e,t,r)}:br,Tr=function(e,t){var r=Lr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},yr=Tr,Er=function(e,t){var r=Lr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},kr=Er,_r=function(e,t){var r=2*Lr(e,t);return r>0?wr(e,t+4,t+4+r-1):""},Sr=_r,xr=function(e,t){var r=Lr(e,t);return r>0?mr(e,t+4,t+4+r):""},Ar=xr,Cr=function(e,t){var r=Lr(e,t);return r>0?wr(e,t+4,t+4+r):""},Ir=Cr,Or=function(e,t){return function(e,t){for(var r=1-2*(e[t+7]>>>7),a=((127&e[t+7])<<4)+(e[t+6]>>>4&15),n=15&e[t+6],s=5;s>=0;--s)n=256*n+e[t+s];return 2047==a?0==n?r*(1/0):NaN:(0==a?a=-1022:(a-=1023,n+=Math.pow(2,52)),r*Math.pow(2,a-52)*n)}(e,t)},Rr=Or,Nr=function(e){return Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array};function Fr(){mr=function(e,t,r){return a.utils.decode(1200,e.slice(t,r)).replace(F,"")},wr=function(e,t,r){return a.utils.decode(65001,e.slice(t,r))},yr=function(e,t){var r=Lr(e,t);return r>0?a.utils.decode(i,e.slice(t+4,t+4+r-1)):""},kr=function(e,t){var r=Lr(e,t);return r>0?a.utils.decode(s,e.slice(t+4,t+4+r-1)):""},Sr=function(e,t){var r=2*Lr(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r-1)):""},Ar=function(e,t){var r=Lr(e,t);return r>0?a.utils.decode(1200,e.slice(t+4,t+4+r)):""},Ir=function(e,t){var r=Lr(e,t);return r>0?a.utils.decode(65001,e.slice(t+4,t+4+r)):""}}k&&(yr=function(e,t){if(!Buffer.isBuffer(e))return Tr(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},kr=function(e,t){if(!Buffer.isBuffer(e))return Er(e,t);var r=e.readUInt32LE(t);return r>0?e.toString("utf8",t+4,t+4+r-1):""},Sr=function(e,t){if(!Buffer.isBuffer(e)||!S)return _r(e,t);var r=2*e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r-1)},Ar=function(e,t){if(!Buffer.isBuffer(e)||!S)return xr(e,t);var r=e.readUInt32LE(t);return e.toString("utf16le",t+4,t+4+r)},Ir=function(e,t){if(!Buffer.isBuffer(e))return Cr(e,t);var r=e.readUInt32LE(t);return e.toString("utf8",t+4,t+4+r)},Rr=function(e,t){return Buffer.isBuffer(e)?e.readDoubleLE(t):Or(e,t)},Nr=function(e){return Buffer.isBuffer(e)||Array.isArray(e)||"undefined"!=typeof Uint8Array&&e instanceof Uint8Array}),void 0!==a&&Fr();var Dr=function(e,t){return e[t]},Pr=function(e,t){return 256*e[t+1]+e[t]},Mr=function(e,t){var r=256*e[t+1]+e[t];return r<32768?r:-1*(65535-r+1)},Lr=function(e,t){return e[t+3]*(1<<24)+(e[t+2]<<16)+(e[t+1]<<8)+e[t]},Ur=function(e,t){return e[t+3]<<24|e[t+2]<<16|e[t+1]<<8|e[t]},Br=function(e,t){return e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3]};function Wr(e,t){var r,n,i,o,c,l,f="",h=[];switch(t){case"dbcs":if(l=this.l,k&&Buffer.isBuffer(this)&&S)f=this.slice(this.l,this.l+2*e).toString("utf16le");else for(c=0;c<e;++c)f+=String.fromCharCode(Pr(this,l)),l+=2;e*=2;break;case"utf8":f=wr(this,this.l,this.l+e);break;case"utf16le":e*=2,f=mr(this,this.l,this.l+e);break;case"wstr":if(void 0===a)return Wr.call(this,e,"dbcs");f=a.utils.decode(s,this.slice(this.l,this.l+2*e)),e*=2;break;case"lpstr-ansi":f=yr(this,this.l),e=4+Lr(this,this.l);break;case"lpstr-cp":f=kr(this,this.l),e=4+Lr(this,this.l);break;case"lpwstr":f=Sr(this,this.l),e=4+2*Lr(this,this.l);break;case"lpp4":e=4+Lr(this,this.l),f=Ar(this,this.l),2&e&&(e+=2);break;case"8lpp4":e=4+Lr(this,this.l),f=Ir(this,this.l),3&e&&(e+=4-(3&e));break;case"cstr":for(e=0,f="";0!==(i=Dr(this,this.l+e++));)h.push(v(i));f=h.join("");break;case"_wstr":for(e=0,f="";0!==(i=Pr(this,this.l+e));)h.push(v(i)),e+=2;e+=2,f=h.join("");break;case"dbcs-cont":for(f="",l=this.l,c=0;c<e;++c){if(this.lens&&-1!==this.lens.indexOf(l))return i=Dr(this,l),this.l=l+1,o=Wr.call(this,e-c,i?"dbcs-cont":"sbcs-cont"),h.join("")+o;h.push(v(Pr(this,l))),l+=2}f=h.join(""),e*=2;break;case"cpstr":if(void 0!==a){f=a.utils.decode(s,this.slice(this.l,this.l+e));break}case"sbcs-cont":for(f="",l=this.l,c=0;c!=e;++c){if(this.lens&&-1!==this.lens.indexOf(l))return i=Dr(this,l),this.l=l+1,o=Wr.call(this,e-c,i?"dbcs-cont":"sbcs-cont"),h.join("")+o;h.push(v(Dr(this,l))),l+=1}f=h.join("");break;default:switch(e){case 1:return r=Dr(this,this.l),this.l++,r;case 2:return r=("i"===t?Mr:Pr)(this,this.l),this.l+=2,r;case 4:case-4:return"i"===t||0==(128&this[this.l+3])?(r=(e>0?Ur:Br)(this,this.l),this.l+=4,r):(n=Lr(this,this.l),this.l+=4,n);case 8:case-8:if("f"===t)return n=8==e?Rr(this,this.l):Rr([this[this.l+7],this[this.l+6],this[this.l+5],this[this.l+4],this[this.l+3],this[this.l+2],this[this.l+1],this[this.l+0]],0),this.l+=8,n;e=8;case 16:f=gr(this,this.l,e)}}return this.l+=e,f}var zr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255,e[r+2]=t>>>16&255,e[r+3]=t>>>24&255},Hr=function(e,t,r){e[r]=255&t,e[r+1]=t>>8&255,e[r+2]=t>>16&255,e[r+3]=t>>24&255},Vr=function(e,t,r){e[r]=255&t,e[r+1]=t>>>8&255};function $r(e,t,r){var n=0,o=0;if("dbcs"===r){for(o=0;o!=t.length;++o)Vr(this,t.charCodeAt(o),this.l+2*o);n=2*t.length}else if("sbcs"===r||"cpstr"==r)if(void 0!==a&&874==i){for(o=0;o!=t.length;++o){var c=a.utils.encode(i,t.charAt(o));this[this.l+o]=c[0]}n=t.length}else if(void 0!==a&&"cpstr"==r){if((c=a.utils.encode(s,t)).length==t.length)for(o=0;o<t.length;++o)0==c[o]&&0!=t.charCodeAt(o)&&(c[o]=95);if(c.length==2*t.length)for(o=0;o<t.length;++o)0==c[2*o]&&0==c[2*o+1]&&0!=t.charCodeAt(o)&&(c[2*o]=95);for(o=0;o<c.length;++o)this[this.l+o]=c[o];n=c.length}else{for(t=t.replace(/[^\x00-\x7F]/g,"_"),o=0;o!=t.length;++o)this[this.l+o]=255&t.charCodeAt(o);n=t.length}else{if("hex"===r){for(;o<e;++o)this[this.l++]=parseInt(t.slice(2*o,2*o+2),16)||0;return this}if("utf16le"===r){var l=Math.min(this.l+e,this.length);for(o=0;o<Math.min(t.length,e);++o){var f=t.charCodeAt(o);this[this.l++]=255&f,this[this.l++]=f>>8}for(;this.l<l;)this[this.l++]=0;return this}switch(e){case 1:n=1,this[this.l]=255&t;break;case 2:n=2,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t;break;case 3:n=3,this[this.l]=255&t,t>>>=8,this[this.l+1]=255&t,t>>>=8,this[this.l+2]=255&t;break;case 4:n=4,zr(this,t,this.l);break;case 8:if(n=8,"f"===r){!function(e,t,r){var a=(t<0||1/t==-1/0?1:0)<<7,n=0,s=0,i=a?-t:t;isFinite(i)?0==i?n=s=0:(n=Math.floor(Math.log(i)/Math.LN2),s=i*Math.pow(2,52-n),n<=-1023&&(!isFinite(s)||s<Math.pow(2,52))?n=-1022:(s-=Math.pow(2,52),n+=1023)):(n=2047,s=isNaN(t)?26985:0);for(var o=0;o<=5;++o,s/=256)e[r+o]=255&s;e[r+6]=(15&n)<<4|15&s,e[r+7]=n>>4|a}(this,t,this.l);break}case 16:break;case-4:n=4,Hr(this,t,this.l)}}return this.l+=n,this}function Gr(e,t){var r=gr(this,this.l,e.length>>1);if(r!==e)throw new Error(t+"Expected "+e+" saw "+r);this.l+=e.length>>1}function Xr(e,t){e.l=t,e.read_shift=Wr,e.chk=Gr,e.write_shift=$r}function jr(e,t){e.l+=t}function Kr(e){var t=x(e);return Xr(t,0),t}function Yr(e,t,r){if(e){var a,n,s;Xr(e,e.l||0);for(var i=e.length,o=0,c=0;e.l<i;){128&(o=e.read_shift(1))&&(o=(127&o)+((127&e.read_shift(1))<<7));var l=Tl[o]||Tl[65535];for(s=127&(a=e.read_shift(1)),n=1;n<4&&128&a;++n)s+=(127&(a=e.read_shift(1)))<<7*n;c=e.l+s;var f=l.f&&l.f(e,s,r);if(e.l=c,t(f,l,o))return}}}function Zr(){var e=[],t=k?256:2048,r=function(e){var t=Kr(e);return Xr(t,0),t},a=r(t),n=function(){a&&(a.l&&(a.length>a.l&&((a=a.slice(0,a.l)).l=a.length),a.length>0&&e.push(a)),a=null)},s=function(e){return a&&e<a.length-a.l?a:(n(),a=r(Math.max(e+1,t)))};return{next:s,push:function(e){n(),null==(a=e).l&&(a.l=a.length),s(t)},end:function(){return n(),N(e)},_bufs:e}}function Jr(e,t,r,a){var n,s=+t;if(!isNaN(s)){a||(a=Tl[s].p||(r||[]).length||0),n=1+(s>=128?1:0)+1,a>=128&&++n,a>=16384&&++n,a>=2097152&&++n;var i=e.next(n);s<=127?i.write_shift(1,s):(i.write_shift(1,128+(127&s)),i.write_shift(1,s>>7));for(var o=0;4!=o;++o){if(!(a>=128)){i.write_shift(1,a);break}i.write_shift(1,128+(127&a)),a>>=7}a>0&&Nr(r)&&e.push(r)}}function qr(e,t,r){var a=Ke(e);if(t.s?(a.cRel&&(a.c+=t.s.c),a.rRel&&(a.r+=t.s.r)):(a.cRel&&(a.c+=t.c),a.rRel&&(a.r+=t.r)),!r||r.biff<12){for(;a.c>=256;)a.c-=256;for(;a.r>=65536;)a.r-=65536}return a}function Qr(e,t,r){var a=Ke(e);return a.s=qr(a.s,t.s,r),a.e=qr(a.e,t.s,r),a}function ea(e,t){if(e.cRel&&e.c<0)for(e=Ke(e);e.c<0;)e.c+=t>8?16384:256;if(e.rRel&&e.r<0)for(e=Ke(e);e.r<0;)e.r+=t>8?1048576:t>5?65536:16384;var r=oa(e);return e.cRel||null==e.cRel||(r=r.replace(/^([A-Z])/,"$$$1")),e.rRel||null==e.rRel||(r=r.replace(/([A-Z]|^)(\d+)$/,"$1$$$2")),r}function ta(e,t){return 0!=e.s.r||e.s.rRel||e.e.r!=(t.biff>=12?1048575:t.biff>=8?65536:16384)||e.e.rRel?0!=e.s.c||e.s.cRel||e.e.c!=(t.biff>=12?16383:255)||e.e.cRel?ea(e.s,t.biff)+":"+ea(e.e,t.biff):(e.s.rRel?"":"$")+aa(e.s.r)+":"+(e.e.rRel?"":"$")+aa(e.e.r):(e.s.cRel?"":"$")+sa(e.s.c)+":"+(e.e.cRel?"":"$")+sa(e.e.c)}function ra(e){return parseInt(e.replace(/\$(\d+)$/,"$1"),10)-1}function aa(e){return""+(e+1)}function na(e){for(var t=e.replace(/^\$([A-Z])/,"$1"),r=0,a=0;a!==t.length;++a)r=26*r+t.charCodeAt(a)-64;return r-1}function sa(e){if(e<0)throw new Error("invalid column "+e);var t="";for(++e;e;e=Math.floor((e-1)/26))t=String.fromCharCode((e-1)%26+65)+t;return t}function ia(e){for(var t=0,r=0,a=0;a<e.length;++a){var n=e.charCodeAt(a);n>=48&&n<=57?t=10*t+(n-48):n>=65&&n<=90&&(r=26*r+(n-64))}return{c:r-1,r:t-1}}function oa(e){for(var t=e.c+1,r="";t;t=(t-1)/26|0)r=String.fromCharCode((t-1)%26+65)+r;return r+(e.r+1)}function ca(e){var t=e.indexOf(":");return-1==t?{s:ia(e),e:ia(e)}:{s:ia(e.slice(0,t)),e:ia(e.slice(t+1))}}function la(e,t){return void 0===t||"number"==typeof t?la(e.s,e.e):("string"!=typeof e&&(e=oa(e)),"string"!=typeof t&&(t=oa(t)),e==t?e:e+":"+t)}function fa(e){var t=ca(e);return"$"+sa(t.s.c)+"$"+aa(t.s.r)+":$"+sa(t.e.c)+"$"+aa(t.e.r)}function ha(e,t){if(!(e||t&&t.biff<=5&&t.biff>=2))throw new Error("empty sheet name");return/[^\w\u4E00-\u9FFF\u3040-\u30FF]/.test(e)?"'"+e.replace(/'/g,"''")+"'":e}function ua(e){var t={s:{c:0,r:0},e:{c:0,r:0}},r=0,a=0,n=0,s=e.length;for(r=0;a<s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.s.c=--r,r=0;a<s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;if(t.s.r=--r,a===s||10!=n)return t.e.c=t.s.c,t.e.r=t.s.r,t;for(++a,r=0;a!=s&&!((n=e.charCodeAt(a)-64)<1||n>26);++a)r=26*r+n;for(t.e.c=--r,r=0;a!=s&&!((n=e.charCodeAt(a)-48)<0||n>9);++a)r=10*r+n;return t.e.r=--r,t}function da(e,t,r){return null==e||null==e.t||"z"==e.t?"":void 0!==e.w?e.w:("d"==e.t&&!e.z&&r&&r.dateNF&&(e.z=r.dateNF),"e"==e.t?tn[e.v]||e.v:function(e,t){var r="d"==e.t&&t instanceof Date;if(null!=e.z)try{return e.w=be(e.z,r?We(t):t)}catch(e){}try{return e.w=be((e.XF||{}).numFmtId||(r?14:0),r?We(t):t)}catch(e){return""+t}}(e,null==t?e.v:t))}function pa(e,t){var r=t&&t.sheet?t.sheet:"Sheet1",a={};return a[r]=e,{SheetNames:[r],Sheets:a}}function ma(e,t,r){var a=r||{},n=e?null!=e["!data"]:a.dense;null!=w&&null==n&&(n=w);var s=e||{};n&&!s["!data"]&&(s["!data"]=[]);var i=0,o=0;if(s&&null!=a.origin)if("number"==typeof a.origin)i=a.origin;else{var c="string"==typeof a.origin?ia(a.origin):a.origin;i=c.r,o=c.c}var l={s:{c:1e7,r:1e7},e:{c:0,r:0}};if(s["!ref"]){var f=ua(s["!ref"]);l.s.c=f.s.c,l.s.r=f.s.r,l.e.c=Math.max(l.e.c,f.e.c),l.e.r=Math.max(l.e.r,f.e.r),-1==i&&(l.e.r=i=s["!ref"]?f.e.r+1:0)}else l.s.c=l.e.c=l.s.r=l.e.r=0;for(var h=[],u=!1,d=0;d!=t.length;++d)if(t[d]){if(!Array.isArray(t[d]))throw new Error("aoa_to_sheet expects an array of arrays");var p=i+d,m=""+(p+1);n&&(s["!data"][p]||(s["!data"][p]=[]),h=s["!data"][p]);for(var v=0;v!=t[d].length;++v)if(void 0!==t[d][v]){var g={v:t[d][v]},b=o+v;if(l.s.r>p&&(l.s.r=p),l.s.c>b&&(l.s.c=b),l.e.r<p&&(l.e.r=p),l.e.c<b&&(l.e.c=b),u=!0,!t[d][v]||"object"!=typeof t[d][v]||Array.isArray(t[d][v])||t[d][v]instanceof Date)if(Array.isArray(g.v)&&(g.f=t[d][v][1],g.v=g.v[0]),null===g.v)if(g.f)g.t="n";else if(a.nullError)g.t="e",g.v=0;else{if(!a.sheetStubs)continue;g.t="z"}else"number"==typeof g.v?g.t="n":"boolean"==typeof g.v?g.t="b":g.v instanceof Date?(g.z=a.dateNF||G[14],a.UTC||(g.v=st(g.v)),a.cellDates?(g.t="d",g.w=be(g.z,We(g.v,a.date1904))):(g.t="n",g.v=We(g.v,a.date1904),g.w=be(g.z,g.v))):g.t="s";else g=t[d][v];if(n)h[b]&&h[b].z&&(g.z=h[b].z),h[b]=g;else{var T=sa(b)+m;s[T]&&s[T].z&&(g.z=s[T].z),s[T]=g}}}return u&&l.s.c<104e5&&(s["!ref"]=la(l)),s}function va(e,t){return ma(null,e,t)}function ga(e,t){return t||(t=Kr(4)),t.write_shift(4,e),t}function ba(e){var t=e.read_shift(4);return 0===t?"":e.read_shift(t,"dbcs")}function wa(e,t){var r=!1;return null==t&&(r=!0,t=Kr(4+2*e.length)),t.write_shift(4,e.length),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}function Ta(e){return{ich:e.read_shift(2),ifnt:e.read_shift(2)}}function ya(e,t){var r=e.l,a=e.read_shift(1),n=ba(e),s=[],i={t:n,h:n};if(0!=(1&a)){for(var o=e.read_shift(4),c=0;c!=o;++c)s.push(Ta(e));i.r=s}else i.r=[{ich:0,ifnt:0}];return e.l=r+t,i}var Ea=ya;function ka(e){var t=e.read_shift(4),r=e.read_shift(2);return r+=e.read_shift(1)<<16,e.l++,{c:t,iStyleRef:r}}function _a(e,t){return null==t&&(t=Kr(8)),t.write_shift(-4,e.c),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}function Sa(e){var t=e.read_shift(2);return t+=e.read_shift(1)<<16,e.l++,{c:-1,iStyleRef:t}}function xa(e,t){return null==t&&(t=Kr(4)),t.write_shift(3,e.iStyleRef||e.s),t.write_shift(1,0),t}var Aa=ba,Ca=wa;function Ia(e){var t=e.read_shift(4);return 0===t||4294967295===t?"":e.read_shift(t,"dbcs")}function Oa(e,t){var r=!1;return null==t&&(r=!0,t=Kr(127)),t.write_shift(4,e.length>0?e.length:4294967295),e.length>0&&t.write_shift(0,e,"dbcs"),r?t.slice(0,t.l):t}var Ra=ba,Na=Ia,Fa=Oa;function Da(e){var t=e.slice(e.l,e.l+4),r=1&t[0],a=2&t[0];e.l+=4;var n=0===a?Rr([0,0,0,0,252&t[0],t[1],t[2],t[3]],0):Ur(t,0)>>2;return r?n/100:n}function Pa(e,t){null==t&&(t=Kr(4));var r=0,a=0,n=100*e;if(e==(0|e)&&e>=-(1<<29)&&e<1<<29?a=1:n==(0|n)&&n>=-(1<<29)&&n<1<<29&&(a=1,r=1),!a)throw new Error("unsupported RkNumber "+e);t.write_shift(-4,((r?n:e)<<2)+(r+2))}function Ma(e){var t={s:{},e:{}};return t.s.r=e.read_shift(4),t.e.r=e.read_shift(4),t.s.c=e.read_shift(4),t.e.c=e.read_shift(4),t}var La=Ma,Ua=function(e,t){return t||(t=Kr(16)),t.write_shift(4,e.s.r),t.write_shift(4,e.e.r),t.write_shift(4,e.s.c),t.write_shift(4,e.e.c),t};function Ba(e){if(e.length-e.l<8)throw"XLS Xnum Buffer underflow";return e.read_shift(8,"f")}function Wa(e,t){return(t||Kr(8)).write_shift(8,e,"f")}function za(e,t){if(t||(t=Kr(8)),!e||e.auto)return t.write_shift(4,0),t.write_shift(4,0),t;null!=e.index?(t.write_shift(1,2),t.write_shift(1,e.index)):null!=e.theme?(t.write_shift(1,6),t.write_shift(1,e.theme)):(t.write_shift(1,5),t.write_shift(1,0));var r=e.tint||0;if(r>0?r*=32767:r<0&&(r*=32768),t.write_shift(2,r),e.rgb&&null==e.theme){var a=e.rgb||"FFFFFF";"number"==typeof a&&(a=("000000"+a.toString(16)).slice(-6)),t.write_shift(1,parseInt(a.slice(0,2),16)),t.write_shift(1,parseInt(a.slice(2,4),16)),t.write_shift(1,parseInt(a.slice(4,6),16)),t.write_shift(1,255)}else t.write_shift(2,0),t.write_shift(1,0),t.write_shift(1,0);return t}function Ha(e,t){var r=e.read_shift(4);switch(r){case 0:return"";case 4294967295:case 4294967294:return{2:"BITMAP",3:"METAFILEPICT",8:"DIB",14:"ENHMETAFILE"}[e.read_shift(4)]||""}if(r>400)throw new Error("Unsupported Clipboard: "+r.toString(16));return e.l-=4,e.read_shift(0,1==t?"lpstr":"lpwstr")}var Va=2,$a=3,Ga=12,Xa=80,ja=81,Ka=[Xa,ja],Ya={1:{n:"CodePage",t:Va},2:{n:"Category",t:Xa},3:{n:"PresentationFormat",t:Xa},4:{n:"ByteCount",t:$a},5:{n:"LineCount",t:$a},6:{n:"ParagraphCount",t:$a},7:{n:"SlideCount",t:$a},8:{n:"NoteCount",t:$a},9:{n:"HiddenCount",t:$a},10:{n:"MultimediaClipCount",t:$a},11:{n:"ScaleCrop",t:11},12:{n:"HeadingPairs",t:4108},13:{n:"TitlesOfParts",t:4126},14:{n:"Manager",t:Xa},15:{n:"Company",t:Xa},16:{n:"LinksUpToDate",t:11},17:{n:"CharacterCount",t:$a},19:{n:"SharedDoc",t:11},22:{n:"HyperlinksChanged",t:11},23:{n:"AppVersion",t:$a,p:"version"},24:{n:"DigSig",t:65},26:{n:"ContentType",t:Xa},27:{n:"ContentStatus",t:Xa},28:{n:"Language",t:Xa},29:{n:"Version",t:Xa},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},Za={1:{n:"CodePage",t:Va},2:{n:"Title",t:Xa},3:{n:"Subject",t:Xa},4:{n:"Author",t:Xa},5:{n:"Keywords",t:Xa},6:{n:"Comments",t:Xa},7:{n:"Template",t:Xa},8:{n:"LastAuthor",t:Xa},9:{n:"RevNumber",t:Xa},10:{n:"EditTime",t:64},11:{n:"LastPrinted",t:64},12:{n:"CreatedDate",t:64},13:{n:"ModifiedDate",t:64},14:{n:"PageCount",t:$a},15:{n:"WordCount",t:$a},16:{n:"CharCount",t:$a},17:{n:"Thumbnail",t:71},18:{n:"Application",t:Xa},19:{n:"DocSecurity",t:$a},255:{},2147483648:{n:"Locale",t:19},2147483651:{n:"Behavior",t:19},1919054434:{}},Ja={1:"US",2:"CA",3:"",7:"RU",20:"EG",30:"GR",31:"NL",32:"BE",33:"FR",34:"ES",36:"HU",39:"IT",41:"CH",43:"AT",44:"GB",45:"DK",46:"SE",47:"NO",48:"PL",49:"DE",52:"MX",55:"BR",61:"AU",64:"NZ",66:"TH",81:"JP",82:"KR",84:"VN",86:"CN",90:"TR",105:"JS",213:"DZ",216:"MA",218:"LY",351:"PT",354:"IS",358:"FI",420:"CZ",886:"TW",961:"LB",962:"JO",963:"SY",964:"IQ",965:"KW",966:"SA",971:"AE",972:"IL",974:"QA",981:"IR",65535:"US"},qa=[null,"solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"];function Qa(e){return e.map((function(e){return[e>>16&255,e>>8&255,255&e]}))}var en=Ke(Qa([0,16777215,16711680,65280,255,16776960,16711935,65535,0,16777215,16711680,65280,255,16776960,16711935,65535,8388608,32768,128,8421376,8388736,32896,12632256,8421504,10066431,10040166,16777164,13434879,6684774,16744576,26316,13421823,128,16711935,16776960,65535,8388736,8388608,32896,255,52479,13434879,13434828,16777113,10079487,16751052,13408767,16764057,3368703,3394764,10079232,16763904,16750848,16737792,6710937,9868950,13158,3381606,13056,3355392,10040064,10040166,3355545,3355443,0,16777215,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0])),tn={0:"#NULL!",7:"#DIV/0!",15:"#VALUE!",23:"#REF!",29:"#NAME?",36:"#NUM!",42:"#N/A",43:"#GETTING_DATA",255:"#WTF?"},rn={"#NULL!":0,"#DIV/0!":7,"#VALUE!":15,"#REF!":23,"#NAME?":29,"#NUM!":36,"#N/A":42,"#GETTING_DATA":43,"#WTF?":255},an=["_xlnm.Consolidate_Area","_xlnm.Auto_Open","_xlnm.Auto_Close","_xlnm.Extract","_xlnm.Database","_xlnm.Criteria","_xlnm.Print_Area","_xlnm.Print_Titles","_xlnm.Recorder","_xlnm.Data_Form","_xlnm.Auto_Activate","_xlnm.Auto_Deactivate","_xlnm.Sheet_Title","_xlnm._FilterDatabase"],nn={"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":"workbooks","application/vnd.ms-excel.sheet.macroEnabled.main+xml":"workbooks","application/vnd.ms-excel.sheet.binary.macroEnabled.main":"workbooks","application/vnd.ms-excel.addin.macroEnabled.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":"workbooks","application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":"sheets","application/vnd.ms-excel.worksheet":"sheets","application/vnd.ms-excel.binIndexWs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":"charts","application/vnd.ms-excel.chartsheet":"charts","application/vnd.ms-excel.macrosheet+xml":"macros","application/vnd.ms-excel.macrosheet":"macros","application/vnd.ms-excel.intlmacrosheet":"TODO","application/vnd.ms-excel.binIndexMs":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":"dialogs","application/vnd.ms-excel.dialogsheet":"dialogs","application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml":"strs","application/vnd.ms-excel.sharedStrings":"strs","application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":"styles","application/vnd.ms-excel.styles":"styles","application/vnd.openxmlformats-package.core-properties+xml":"coreprops","application/vnd.openxmlformats-officedocument.custom-properties+xml":"custprops","application/vnd.openxmlformats-officedocument.extended-properties+xml":"extprops","application/vnd.openxmlformats-officedocument.customXmlProperties+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.customProperty":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":"comments","application/vnd.ms-excel.comments":"comments","application/vnd.ms-excel.threadedcomments+xml":"threadedcomments","application/vnd.ms-excel.person+xml":"people","application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml":"metadata","application/vnd.ms-excel.sheetMetadata":"metadata","application/vnd.ms-excel.pivotTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotTable+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.chart+xml":"TODO","application/vnd.ms-office.chartcolorstyle+xml":"TODO","application/vnd.ms-office.chartstyle+xml":"TODO","application/vnd.ms-office.chartex+xml":"TODO","application/vnd.ms-excel.calcChain":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.calcChain+xml":"calcchains","application/vnd.openxmlformats-officedocument.spreadsheetml.printerSettings":"TODO","application/vnd.ms-office.activeX":"TODO","application/vnd.ms-office.activeX+xml":"TODO","application/vnd.ms-excel.attachedToolbars":"TODO","application/vnd.ms-excel.connections":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":"TODO","application/vnd.ms-excel.externalLink":"links","application/vnd.openxmlformats-officedocument.spreadsheetml.externalLink+xml":"links","application/vnd.ms-excel.pivotCacheDefinition":"TODO","application/vnd.ms-excel.pivotCacheRecords":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheDefinition+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.pivotCacheRecords+xml":"TODO","application/vnd.ms-excel.queryTable":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.queryTable+xml":"TODO","application/vnd.ms-excel.userNames":"TODO","application/vnd.ms-excel.revisionHeaders":"TODO","application/vnd.ms-excel.revisionLog":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionHeaders+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.revisionLog+xml":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.userNames+xml":"TODO","application/vnd.ms-excel.tableSingleCells":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.tableSingleCells+xml":"TODO","application/vnd.ms-excel.slicer":"TODO","application/vnd.ms-excel.slicerCache":"TODO","application/vnd.ms-excel.slicer+xml":"TODO","application/vnd.ms-excel.slicerCache+xml":"TODO","application/vnd.ms-excel.wsSortMap":"TODO","application/vnd.ms-excel.table":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":"TODO","application/vnd.openxmlformats-officedocument.theme+xml":"themes","application/vnd.openxmlformats-officedocument.themeOverride+xml":"TODO","application/vnd.ms-excel.Timeline+xml":"TODO","application/vnd.ms-excel.TimelineCache+xml":"TODO","application/vnd.ms-office.vbaProject":"vba","application/vnd.ms-office.vbaProjectSignature":"TODO","application/vnd.ms-office.volatileDependencies":"TODO","application/vnd.openxmlformats-officedocument.spreadsheetml.volatileDependencies+xml":"TODO","application/vnd.ms-excel.controlproperties+xml":"TODO","application/vnd.openxmlformats-officedocument.model+data":"TODO","application/vnd.ms-excel.Survey+xml":"TODO","application/vnd.openxmlformats-officedocument.drawing+xml":"drawings","application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramColors+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramData+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramLayout+xml":"TODO","application/vnd.openxmlformats-officedocument.drawingml.diagramStyle+xml":"TODO","application/vnd.openxmlformats-officedocument.vmlDrawing":"TODO","application/vnd.openxmlformats-package.relationships+xml":"rels","application/vnd.openxmlformats-officedocument.oleObject":"TODO","image/png":"TODO",sheet:"js"},sn={workbooks:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml",xlsm:"application/vnd.ms-excel.sheet.macroEnabled.main+xml",xlsb:"application/vnd.ms-excel.sheet.binary.macroEnabled.main",xlam:"application/vnd.ms-excel.addin.macroEnabled.main+xml",xltx:"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml"},strs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedStrings+xml",xlsb:"application/vnd.ms-excel.sharedStrings"},comments:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml",xlsb:"application/vnd.ms-excel.comments"},sheets:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml",xlsb:"application/vnd.ms-excel.worksheet"},charts:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml",xlsb:"application/vnd.ms-excel.chartsheet"},dialogs:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml",xlsb:"application/vnd.ms-excel.dialogsheet"},macros:{xlsx:"application/vnd.ms-excel.macrosheet+xml",xlsb:"application/vnd.ms-excel.macrosheet"},metadata:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetMetadata+xml",xlsb:"application/vnd.ms-excel.sheetMetadata"},styles:{xlsx:"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml",xlsb:"application/vnd.ms-excel.styles"}};function on(e,t,r){var a,n=function(e){for(var t=[],r=Fe(e),a=0;a!==r.length;++a)null==t[e[r[a]]]&&(t[e[r[a]]]=[]),t[e[r[a]]].push(r[a]);return t}(nn),s=[];r||(s[s.length]=At,s[s.length]=sr("Types",null,{xmlns:lr.CT,"xmlns:xsd":lr.xsd,"xmlns:xsi":lr.xsi}),s=s.concat([["xml","application/xml"],["bin","application/vnd.ms-excel.sheet.binary.macroEnabled.main"],["vml","application/vnd.openxmlformats-officedocument.vmlDrawing"],["data","application/vnd.openxmlformats-officedocument.model+data"],["bmp","image/bmp"],["png","image/png"],["gif","image/gif"],["emf","image/x-emf"],["wmf","image/x-wmf"],["jpg","image/jpeg"],["jpeg","image/jpeg"],["tif","image/tiff"],["tiff","image/tiff"],["pdf","application/pdf"],["rels","application/vnd.openxmlformats-package.relationships+xml"]].map((function(e){return sr("Default",null,{Extension:e[0],ContentType:e[1]})}))));var i=function(r){e[r]&&e[r].length>0&&(a=e[r][0],s[s.length]=sr("Override",null,{PartName:("/"==a[0]?"":"/")+a,ContentType:sn[r][t.bookType]||sn[r].xlsx}))},o=function(r){(e[r]||[]).forEach((function(e){s[s.length]=sr("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:sn[r][t.bookType]||sn[r].xlsx})}))},c=function(t){(e[t]||[]).forEach((function(e){s[s.length]=sr("Override",null,{PartName:("/"==e[0]?"":"/")+e,ContentType:n[t][0]})}))};return i("workbooks"),o("sheets"),o("charts"),c("themes"),["strs","styles"].forEach(i),["coreprops","extprops","custprops"].forEach(c),c("vba"),c("comments"),c("threadedcomments"),c("drawings"),o("metadata"),c("people"),!r&&s.length>2&&(s[s.length]="</Types>",s[1]=s[1].replace("/>",">")),s.join("")}var cn={WB:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",SHEET:"http://sheetjs.openxmlformats.org/officeDocument/2006/relationships/officeDocument",HLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",VML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/vmlDrawing",XPATH:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLinkPath",XMISS:"http://schemas.microsoft.com/office/2006/relationships/xlExternalLinkPath/xlPathMissing",XLINK:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/externalLink",CXML:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXml",CXMLP:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/customXmlProps",CMNT:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/comments",CORE_PROPS:"http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",EXT_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",CUST_PROPS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/custom-properties",SST:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sharedStrings",STY:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",THEME:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",CHART:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chart",CHARTEX:"http://schemas.microsoft.com/office/2014/relationships/chartEx",CS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/chartsheet",WS:["http://schemas.openxmlformats.org/officeDocument/2006/relationships/worksheet","http://purl.oclc.org/ooxml/officeDocument/relationships/worksheet"],DS:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/dialogsheet",MS:"http://schemas.microsoft.com/office/2006/relationships/xlMacrosheet",IMG:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",DRAW:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/drawing",XLMETA:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/sheetMetadata",TCMNT:"http://schemas.microsoft.com/office/2017/10/relationships/threadedComment",PEOPLE:"http://schemas.microsoft.com/office/2017/10/relationships/person",CONN:"http://schemas.openxmlformats.org/officeDocument/2006/relationships/connections",VBA:"http://schemas.microsoft.com/office/2006/relationships/vbaProject"};function ln(e){var t=e.lastIndexOf("/");return e.slice(0,t+1)+"_rels/"+e.slice(t+1)+".rels"}function fn(e,t){var r={"!id":{}};if(!e)return r;"/"!==t.charAt(0)&&(t="/"+t);var a={};return(e.match(Ot)||[]).forEach((function(e){var n=Ft(e);if("<Relationship"===n[0]){var s={};s.Type=n.Type,s.Target=Lt(n.Target),s.Id=n.Id,n.TargetMode&&(s.TargetMode=n.TargetMode);var i="External"===n.TargetMode?n.Target:xt(n.Target,t);r[i]=s,a[n.Id]=s}})),r["!id"]=a,r}function hn(e){var t=[At,sr("Relationships",null,{xmlns:lr.RELS})];return Fe(e["!id"]).forEach((function(r){t[t.length]=sr("Relationship",null,e["!id"][r])})),t.length>2&&(t[t.length]="</Relationships>",t[1]=t[1].replace("/>",">")),t.join("")}function un(e,t,r,a,n,s){if(n||(n={}),e["!id"]||(e["!id"]={}),e["!idx"]||(e["!idx"]=1),t<0)for(t=e["!idx"];e["!id"]["rId"+t];++t);if(e["!idx"]=t+1,n.Id="rId"+t,n.Type=a,n.Target=r,s?n.TargetMode=s:[cn.HLINK,cn.XPATH,cn.XMISS].indexOf(n.Type)>-1&&(n.TargetMode="External"),e["!id"][n.Id])throw new Error("Cannot rewrite rId "+t);return e["!id"][n.Id]=n,e[("/"+n.Target).replace("//","/")]=n,t}var dn="application/vnd.oasis.opendocument.spreadsheet";function pn(e,t,r){return['  <rdf:Description rdf:about="'+e+'">\n','    <rdf:type rdf:resource="http://docs.oasis-open.org/ns/office/1.2/meta/'+(r||"odf")+"#"+t+'"/>\n',"  </rdf:Description>\n"].join("")}function mn(e,t){return'<office:document-meta xmlns:office="urn:oasis:names:tc:opendocument:xmlns:office:1.0" xmlns:meta="urn:oasis:names:tc:opendocument:xmlns:meta:1.0" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:xlink="http://www.w3.org/1999/xlink" office:version="1.2"><office:meta><meta:generator>SheetJS '+n.version+"</meta:generator></office:meta></office:document-meta>"}var vn=[["cp:category","Category"],["cp:contentStatus","ContentStatus"],["cp:keywords","Keywords"],["cp:lastModifiedBy","LastAuthor"],["cp:lastPrinted","LastPrinted"],["cp:revision","RevNumber"],["cp:version","Version"],["dc:creator","Author"],["dc:description","Comments"],["dc:identifier","Identifier"],["dc:language","Language"],["dc:subject","Subject"],["dc:title","Title"],["dcterms:created","CreatedDate","date"],["dcterms:modified","ModifiedDate","date"]];function gn(e){var t={};e=Zt(e);for(var r=0;r<vn.length;++r){var a=vn[r],n=ft(e,a[0]);null!=n&&n.length>0&&(t[a[1]]=Lt(n[1])),"date"===a[2]&&t[a[1]]&&(t[a[1]]=Xe(t[a[1]]))}return t}function bn(e,t,r,a,n){null==n[e]&&null!=t&&""!==t&&(n[e]=t,t=Wt(t),a[a.length]=r?sr(e,t,r):ar(e,t))}function wn(e,t){var r=t||{},a=[At,sr("cp:coreProperties",null,{"xmlns:cp":lr.CORE_PROPS,"xmlns:dc":lr.dc,"xmlns:dcterms":lr.dcterms,"xmlns:dcmitype":lr.dcmitype,"xmlns:xsi":lr.xsi})],n={};if(!e&&!r.Props)return a.join("");e&&(null!=e.CreatedDate&&bn("dcterms:created","string"==typeof e.CreatedDate?e.CreatedDate:ir(e.CreatedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n),null!=e.ModifiedDate&&bn("dcterms:modified","string"==typeof e.ModifiedDate?e.ModifiedDate:ir(e.ModifiedDate,r.WTF),{"xsi:type":"dcterms:W3CDTF"},a,n));for(var s=0;s!=vn.length;++s){var i=vn[s],o=r.Props&&null!=r.Props[i[1]]?r.Props[i[1]]:e?e[i[1]]:null;!0===o?o="1":!1===o?o="0":"number"==typeof o&&(o=String(o)),null!=o&&bn(i[0],o,null,a,n)}return a.length>2&&(a[a.length]="</cp:coreProperties>",a[1]=a[1].replace("/>",">")),a.join("")}var Tn=[["Application","Application","string"],["AppVersion","AppVersion","string"],["Company","Company","string"],["DocSecurity","DocSecurity","string"],["Manager","Manager","string"],["HyperlinksChanged","HyperlinksChanged","bool"],["SharedDoc","SharedDoc","bool"],["LinksUpToDate","LinksUpToDate","bool"],["ScaleCrop","ScaleCrop","bool"],["HeadingPairs","HeadingPairs","raw"],["TitlesOfParts","TitlesOfParts","raw"]],yn=["Worksheets","SheetNames","NamedRanges","DefinedNames","Chartsheets","ChartNames"];function En(e,t,r,a){var n=[];if("string"==typeof e)n=tr(e,a);else for(var s=0;s<e.length;++s)n=n.concat(e[s].map((function(e){return{v:e}})));var i="string"==typeof t?tr(t,a).map((function(e){return e.v})):t,o=0,c=0;if(i.length>0)for(var l=0;l!==n.length;l+=2){switch(c=+n[l+1].v,n[l].v){case"Worksheets":case"工作表":case"Листы":case"أوراق العمل":case"ワークシート":case"גליונות עבודה":case"Arbeitsblätter":case"Çalışma Sayfaları":case"Feuilles de calcul":case"Fogli di lavoro":case"Folhas de cálculo":case"Planilhas":case"Regneark":case"Hojas de cálculo":case"Werkbladen":r.Worksheets=c,r.SheetNames=i.slice(o,o+c);break;case"Named Ranges":case"Rangos con nombre":case"名前付き一覧":case"Benannte Bereiche":case"Navngivne områder":r.NamedRanges=c,r.DefinedNames=i.slice(o,o+c);break;case"Charts":case"Diagramme":r.Chartsheets=c,r.ChartNames=i.slice(o,o+c)}o+=c}}function kn(e){var t=[],r=sr;return e||(e={}),e.Application="SheetJS",t[t.length]=At,t[t.length]=sr("Properties",null,{xmlns:lr.EXT_PROPS,"xmlns:vt":lr.vt}),Tn.forEach((function(a){if(void 0!==e[a[1]]){var n;switch(a[2]){case"string":n=Wt(String(e[a[1]]));break;case"bool":n=e[a[1]]?"true":"false"}void 0!==n&&(t[t.length]=r(a[0],n))}})),t[t.length]=r("HeadingPairs",r("vt:vector",r("vt:variant","<vt:lpstr>Worksheets</vt:lpstr>")+r("vt:variant",r("vt:i4",String(e.Worksheets))),{size:2,baseType:"variant"})),t[t.length]=r("TitlesOfParts",r("vt:vector",e.SheetNames.map((function(e){return"<vt:lpstr>"+Wt(e)+"</vt:lpstr>"})).join(""),{size:e.Worksheets,baseType:"lpstr"})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var _n=/<[^<>]+>[^<]*/g;function Sn(e){var t=[At,sr("Properties",null,{xmlns:lr.CUST_PROPS,"xmlns:vt":lr.vt})];if(!e)return t.join("");var r=1;return Fe(e).forEach((function(a){++r,t[t.length]=sr("property",function(e,t){switch(typeof e){case"string":var r=sr("vt:lpwstr",Wt(e));return r=r.replace(/&quot;/g,"_x0022_");case"number":return sr((0|e)==e?"vt:i4":"vt:r8",Wt(String(e)));case"boolean":return sr("vt:bool",e?"true":"false")}if(e instanceof Date)return sr("vt:filetime",ir(e));throw new Error("Unable to serialize "+e)}(e[a]),{fmtid:"{D5CDD505-2E9C-101B-9397-08002B2CF9AE}",pid:r,name:Wt(a)})})),t.length>2&&(t[t.length]="</Properties>",t[1]=t[1].replace("/>",">")),t.join("")}var xn,An={Title:"Title",Subject:"Subject",Author:"Author",Keywords:"Keywords",Comments:"Description",LastAuthor:"LastAuthor",RevNumber:"Revision",Application:"AppName",LastPrinted:"LastPrinted",CreatedDate:"Created",ModifiedDate:"LastSaved",Category:"Category",Manager:"Manager",Company:"Company",AppVersion:"Version",ContentStatus:"ContentStatus",Identifier:"Identifier",Language:"Language"};function Cn(e,t,r){xn||(xn=Pe(An)),e[t=xn[t]||t]=r}function In(e){var t=e.read_shift(4),r=e.read_shift(4);return new Date(1e3*(r/1e7*Math.pow(2,32)+t/1e7-11644473600)).toISOString().replace(/\.000/,"")}function On(e,t,r){var a=e.l,n=e.read_shift(0,"lpstr-cp");if(r)for(;e.l-a&3;)++e.l;return n}function Rn(e,t,r){var a=e.read_shift(0,"lpwstr");return r&&(e.l+=4-(a.length+1&3)&3),a}function Nn(e,t,r){return 31===t?Rn(e):On(e,0,r)}function Fn(e,t,r){return Nn(e,t,!1===r?0:4)}function Dn(e){var t=e.l,r=Ln(e,ja);return 0==e[e.l]&&0==e[e.l+1]&&e.l-t&2&&(e.l+=2),[r,Ln(e,$a)]}function Pn(e,t){for(var r=e.read_shift(4),a={},n=0;n!=r;++n){var s=e.read_shift(4),i=e.read_shift(4);a[s]=e.read_shift(i,1200===t?"utf16le":"utf8").replace(F,"").replace(D,"!"),1200===t&&i%2&&(e.l+=2)}return 3&e.l&&(e.l=e.l>>3<<2),a}function Mn(e){var t=e.read_shift(4),r=e.slice(e.l,e.l+t);return e.l+=t,(3&t)>0&&(e.l+=4-(3&t)&3),r}function Ln(e,t,r){var a,n=e.read_shift(2),s=r||{};if(e.l+=2,t!==Ga&&n!==t&&-1===Ka.indexOf(t)&&(4126!=(65534&t)||4126!=(65534&n)))throw new Error("Expected type "+t+" saw "+n);switch(t===Ga?n:t){case 2:return a=e.read_shift(2,"i"),s.raw||(e.l+=2),a;case 3:return e.read_shift(4,"i");case 11:return 0!==e.read_shift(4);case 19:return e.read_shift(4);case 30:return On(e,0,4).replace(F,"");case 31:return Rn(e);case 64:return In(e);case 65:return Mn(e);case 71:return function(e){var t={};return t.Size=e.read_shift(4),e.l+=t.Size+3-(t.Size-1)%4,t}(e);case 80:return Fn(e,n,!s.raw).replace(F,"");case 81:return function(e,t){if(!t)throw new Error("VtUnalignedString must have positive length");return Nn(e,t,0)}(e,n).replace(F,"");case 4108:return function(e){for(var t=e.read_shift(4),r=[],a=0;a<t/2;++a)r.push(Dn(e));return r}(e);case 4126:case 4127:return 4127==n?function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a){var n=e.l;r[a]=e.read_shift(0,"lpwstr").replace(F,""),e.l-n&2&&(e.l+=2)}return r}(e):function(e){for(var t=e.read_shift(4),r=[],a=0;a!=t;++a)r[a]=e.read_shift(0,"lpstr-cp").replace(F,"");return r}(e);default:throw new Error("TypedPropertyValue unrecognized type "+t+" "+n)}}function Un(e,t){var r=Kr(4),a=Kr(4);switch(r.write_shift(4,80==e?31:e),e){case 3:a.write_shift(-4,t);break;case 5:(a=Kr(8)).write_shift(8,t,"f");break;case 11:a.write_shift(4,t?1:0);break;case 64:a=function(e){var t=("string"==typeof e?new Date(Date.parse(e)):e).getTime()/1e3+11644473600,r=t%Math.pow(2,32),a=(t-r)/Math.pow(2,32);a*=1e7;var n=(r*=1e7)/Math.pow(2,32)|0;n>0&&(r%=Math.pow(2,32),a+=n);var s=Kr(8);return s.write_shift(4,r),s.write_shift(4,a),s}(t);break;case 31:case 80:for((a=Kr(4+2*(t.length+1)+(t.length%2?0:2))).write_shift(4,t.length+1),a.write_shift(0,t,"dbcs");a.l!=a.length;)a.write_shift(1,0);break;default:throw new Error("TypedPropertyValue unrecognized type "+e+" "+t)}return N([r,a])}function Bn(e,t){var r=e.l,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0,o=0,c=-1,l={};for(i=0;i!=n;++i){var h=e.read_shift(4),u=e.read_shift(4);s[i]=[h,u+r]}s.sort((function(e,t){return e[1]-t[1]}));var d={};for(i=0;i!=n;++i){if(e.l!==s[i][1]){var p=!0;if(i>0&&t)switch(t[s[i-1][0]].t){case 2:e.l+2===s[i][1]&&(e.l+=2,p=!1);break;case 80:case 4108:e.l<=s[i][1]&&(e.l=s[i][1],p=!1)}if((!t||0==i)&&e.l<=s[i][1]&&(p=!1,e.l=s[i][1]),p)throw new Error("Read Error: Expected address "+s[i][1]+" at "+e.l+" :"+i)}if(t){if(0==s[i][0]&&s.length>i+1&&s[i][1]==s[i+1][1])continue;var m=t[s[i][0]];if(d[m.n]=Ln(e,m.t,{raw:!0}),"version"===m.p&&(d[m.n]=String(d[m.n]>>16)+"."+("0000"+String(65535&d[m.n])).slice(-4)),"CodePage"==m.n)switch(d[m.n]){case 0:d[m.n]=1252;case 874:case 932:case 936:case 949:case 950:case 1250:case 1251:case 1253:case 1254:case 1255:case 1256:case 1257:case 1258:case 1e4:case 1200:case 1201:case 1252:case 65e3:case-536:case 65001:case-535:f(o=d[m.n]>>>0&65535);break;default:throw new Error("Unsupported CodePage: "+d[m.n])}}else if(1===s[i][0]){if(o=d.CodePage=Ln(e,Va),f(o),-1!==c){var v=e.l;e.l=s[c][1],l=Pn(e,o),e.l=v}}else if(0===s[i][0]){if(0===o){c=i,e.l=s[i+1][1];continue}l=Pn(e,o)}else{var g,b=l[s[i][0]];switch(e[e.l]){case 65:e.l+=4,g=Mn(e);break;case 30:case 31:e.l+=4,g=Fn(e,e[e.l-4]).replace(/(^|[^\u0000])\u0000+$/,"$1");break;case 3:e.l+=4,g=e.read_shift(4,"i");break;case 19:e.l+=4,g=e.read_shift(4);break;case 5:e.l+=4,g=e.read_shift(8,"f");break;case 11:e.l+=4,g=Xn(e,4);break;case 64:e.l+=4,g=Xe(In(e));break;default:throw new Error("unparsed value: "+e[e.l])}d[b]=g}}return e.l=r+a,d}var Wn=["CodePage","Thumbnail","_PID_LINKBASE","_PID_HLINKS","SystemIdentifier","FMTID"];function zn(e){switch(typeof e){case"boolean":return 11;case"number":return(0|e)==e?3:5;case"string":return 31;case"object":if(e instanceof Date)return 64}return-1}function Hn(e,t,r){var a=Kr(8),n=[],s=[],i=8,o=0,c=Kr(8),l=Kr(8);if(c.write_shift(4,2),c.write_shift(4,1200),l.write_shift(4,1),s.push(c),n.push(l),i+=8+c.length,!t){(l=Kr(8)).write_shift(4,0),n.unshift(l);var f=[Kr(4)];for(f[0].write_shift(4,e.length),o=0;o<e.length;++o){var h=e[o][0];for((c=Kr(8+2*(h.length+1)+(h.length%2?0:2))).write_shift(4,o+2),c.write_shift(4,h.length+1),c.write_shift(0,h,"dbcs");c.l!=c.length;)c.write_shift(1,0);f.push(c)}c=N(f),s.unshift(c),i+=8+c.length}for(o=0;o<e.length;++o)if((!t||t[e[o][0]])&&!(Wn.indexOf(e[o][0])>-1||yn.indexOf(e[o][0])>-1)&&null!=e[o][1]){var u=e[o][1],d=0;if(t){var p=r[d=+t[e[o][0]]];if("version"==p.p&&"string"==typeof u){var m=u.split(".");u=(+m[0]<<16)+(+m[1]||0)}c=Un(p.t,u)}else{var v=zn(u);-1==v&&(v=31,u=String(u)),c=Un(v,u)}s.push(c),(l=Kr(8)).write_shift(4,t?d:2+o),n.push(l),i+=8+c.length}var g=8*(s.length+1);for(o=0;o<s.length;++o)n[o].write_shift(4,g),g+=s[o].length;return a.write_shift(4,i),a.write_shift(4,s.length),N([a].concat(n).concat(s))}function Vn(e,t,r){var a=e.content;if(!a)return{};Xr(a,0);var n,s,i,o,c=0;a.chk("feff","Byte Order: "),a.read_shift(2);var l=a.read_shift(4),f=a.read_shift(16);if(f!==Ie.utils.consts.HEADER_CLSID&&f!==r)throw new Error("Bad PropertySet CLSID "+f);if(1!==(n=a.read_shift(4))&&2!==n)throw new Error("Unrecognized #Sets: "+n);if(s=a.read_shift(16),o=a.read_shift(4),1===n&&o!==a.l)throw new Error("Length mismatch: "+o+" !== "+a.l);2===n&&(i=a.read_shift(16),c=a.read_shift(4));var h,u=Bn(a,t),d={SystemIdentifier:l};for(var p in u)d[p]=u[p];if(d.FMTID=s,1===n)return d;if(c-a.l==2&&(a.l+=2),a.l!==c)throw new Error("Length mismatch 2: "+a.l+" !== "+c);try{h=Bn(a,null)}catch(e){}for(p in h)d[p]=h[p];return d.FMTID=[s,i],d}function $n(e,t,r,a,n,s){var i=Kr(n?68:48),o=[i];i.write_shift(2,65534),i.write_shift(2,0),i.write_shift(4,842412599),i.write_shift(16,Ie.utils.consts.HEADER_CLSID,"hex"),i.write_shift(4,n?2:1),i.write_shift(16,t,"hex"),i.write_shift(4,n?68:48);var c=Hn(e,r,a);if(o.push(c),n){var l=Hn(n,null,null);i.write_shift(16,s,"hex"),i.write_shift(4,68+c.length),o.push(l)}return N(o)}function Gn(e,t){return e.read_shift(t),null}function Xn(e,t){return 1===e.read_shift(t)}function jn(e,t){return t||(t=Kr(2)),t.write_shift(2,+!!e),t}function Kn(e){return e.read_shift(2,"u")}function Yn(e,t){return t||(t=Kr(2)),t.write_shift(2,e),t}function Zn(e,t){return function(e,t,r){for(var a=[],n=e.l+t;e.l<n;)a.push(r(e,n-e.l));if(n!==e.l)throw new Error("Slurp error");return a}(e,t,Kn)}function Jn(e){var t=e.read_shift(1);return 1===e.read_shift(1)?t:1===t}function qn(e,t,r){return r||(r=Kr(2)),r.write_shift(1,"e"==t?+e:+!!e),r.write_shift(1,"e"==t?1:0),r}function Qn(e,t,r){var a=e.read_shift(r&&r.biff>=12?2:1),n="sbcs-cont",i=s;r&&r.biff>=8&&(s=1200),r&&8!=r.biff?12==r.biff&&(n="wstr"):e.read_shift(1)&&(n="dbcs-cont"),r.biff>=2&&r.biff<=5&&(n="cpstr");var o=a?e.read_shift(a,n):"";return s=i,o}function es(e){var t=s;s=1200;var r,a=e.read_shift(2),n=e.read_shift(1),i=4&n,o=8&n,c=1+(1&n),l=0,f={};o&&(l=e.read_shift(2)),i&&(r=e.read_shift(4));var h=2==c?"dbcs-cont":"sbcs-cont",u=0===a?"":e.read_shift(a,h);return o&&(e.l+=4*l),i&&(e.l+=r),f.t=u,o||(f.raw="<t>"+f.t+"</t>",f.r=f.t),s=t,f}function ts(e){var t=e.t||"",r=Kr(3);r.write_shift(2,t.length),r.write_shift(1,1);var a=Kr(2*t.length);return a.write_shift(2*t.length,t,"utf16le"),N([r,a])}function rs(e,t,r){if(r){if(r.biff>=2&&r.biff<=5)return e.read_shift(t,"cpstr");if(r.biff>=12)return e.read_shift(t,"dbcs-cont")}return 0===e.read_shift(1)?e.read_shift(t,"sbcs-cont"):e.read_shift(t,"dbcs-cont")}function as(e,t,r){var a=e.read_shift(r&&2==r.biff?1:2);return 0===a?(e.l++,""):rs(e,a,r)}function ns(e,t,r){if(r.biff>5)return as(e,0,r);var a=e.read_shift(1);return 0===a?(e.l++,""):e.read_shift(a,r.biff<=4||!e.lens?"cpstr":"sbcs-cont")}function ss(e,t,r){return r||(r=Kr(3+2*e.length)),r.write_shift(2,e.length),r.write_shift(1,1),r.write_shift(31,e,"utf16le"),r}function is(e){var t=e.read_shift(4);return t>0?e.read_shift(t,"utf16le").replace(F,""):""}function os(e,t){t||(t=Kr(6+2*e.length)),t.write_shift(4,1+e.length);for(var r=0;r<e.length;++r)t.write_shift(2,e.charCodeAt(r));return t.write_shift(2,0),t}function cs(e){var t=Kr(512),r=0,a=e.Target;"file://"==a.slice(0,7)&&(a=a.slice(7));var n=a.indexOf("#"),s=n>-1?31:23;switch(a.charAt(0)){case"#":s=28;break;case".":s&=-3}t.write_shift(4,2),t.write_shift(4,s);var i=[8,6815827,6619237,4849780,83];for(r=0;r<i.length;++r)t.write_shift(4,i[r]);if(28==s)os(a=a.slice(1),t);else if(2&s){for(i="e0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));var o=n>-1?a.slice(0,n):a;for(t.write_shift(4,2*(o.length+1)),r=0;r<o.length;++r)t.write_shift(2,o.charCodeAt(r));t.write_shift(2,0),8&s&&os(n>-1?a.slice(n+1):"",t)}else{for(i="03 03 00 00 00 00 00 00 c0 00 00 00 00 00 00 46".split(" "),r=0;r<i.length;++r)t.write_shift(1,parseInt(i[r],16));for(var c=0;"../"==a.slice(3*c,3*c+3)||"..\\"==a.slice(3*c,3*c+3);)++c;for(t.write_shift(2,c),t.write_shift(4,a.length-3*c+1),r=0;r<a.length-3*c;++r)t.write_shift(1,255&a.charCodeAt(r+3*c));for(t.write_shift(1,0),t.write_shift(2,65535),t.write_shift(2,57005),r=0;r<6;++r)t.write_shift(4,0)}return t.slice(0,t.l)}function ls(e){return[e.read_shift(1),e.read_shift(1),e.read_shift(1),e.read_shift(1)]}function fs(e,t){var r=ls(e);return r[3]=0,r}function hs(e,t,r){var a={r:e.read_shift(2),c:e.read_shift(2),ixfe:0};if(r&&2==r.biff||7==t){var n=e.read_shift(1);a.ixfe=63&n,e.l+=2}else a.ixfe=e.read_shift(2);return a}function us(e,t,r,a){return a||(a=Kr(6)),a.write_shift(2,e),a.write_shift(2,t),a.write_shift(2,r||0),a}function ds(e,t,r){var a=r.biff>8?4:2;return[e.read_shift(a),e.read_shift(a,"i"),e.read_shift(a,"i")]}function ps(e){return[e.read_shift(2),Da(e)]}function ms(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(2),r:t},e:{c:e.read_shift(2),r:r}}}function vs(e,t){return t||(t=Kr(8)),t.write_shift(2,e.s.r),t.write_shift(2,e.e.r),t.write_shift(2,e.s.c),t.write_shift(2,e.e.c),t}function gs(e){var t=e.read_shift(2),r=e.read_shift(2);return{s:{c:e.read_shift(1),r:t},e:{c:e.read_shift(1),r:r}}}var bs=gs;function ws(e){e.l+=4;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(2);return e.l+=12,[r,t,a]}function Ts(e){e.l+=2,e.l+=e.read_shift(2)}var ys={0:Ts,4:Ts,5:Ts,6:Ts,7:function(e){return e.l+=4,e.cf=e.read_shift(2),{}},8:Ts,9:Ts,10:Ts,11:Ts,12:Ts,13:function(e){var t={};return e.l+=4,e.l+=16,t.fSharedNote=e.read_shift(2),e.l+=4,t},14:Ts,15:Ts,16:Ts,17:Ts,18:Ts,19:Ts,20:Ts,21:ws};function Es(e,t){var r={BIFFVer:0,dt:0};switch(r.BIFFVer=e.read_shift(2),(t-=2)>=2&&(r.dt=e.read_shift(2),e.l-=2),r.BIFFVer){case 1536:case 1280:case 1024:case 768:case 512:case 2:case 7:break;default:if(t>6)throw new Error("Unexpected BIFF Ver "+r.BIFFVer)}return e.read_shift(t),r}function ks(e,t,r){var a=1536,n=16;switch(r.bookType){case"biff8":case"xla":break;case"biff5":a=1280,n=8;break;case"biff4":a=4,n=6;break;case"biff3":a=3,n=6;break;case"biff2":a=2,n=4;break;default:throw new Error("unsupported BIFF version")}var s=Kr(n);return s.write_shift(2,a),s.write_shift(2,t),n>4&&s.write_shift(2,29282),n>6&&s.write_shift(2,1997),n>8&&(s.write_shift(2,49161),s.write_shift(2,1),s.write_shift(2,1798),s.write_shift(2,0)),s}function _s(e,t){var r=!t||t.biff>=8?2:1,a=Kr(8+r*e.name.length);a.write_shift(4,e.pos),a.write_shift(1,e.hs||0),a.write_shift(1,e.dt),a.write_shift(1,e.name.length),t.biff>=8&&a.write_shift(1,1),a.write_shift(r*e.name.length,e.name,t.biff<8?"sbcs":"utf16le");var n=a.slice(0,a.l);return n.l=a.l,n}function Ss(e,t,r){var a=0;r&&2==r.biff||(a=e.read_shift(2));var n=e.read_shift(2);return r&&2==r.biff&&(a=1-(n>>15),n&=32767),[{Unsynced:1&a,DyZero:(2&a)>>1,ExAsc:(4&a)>>2,ExDsc:(8&a)>>3},n]}function xs(e,t,r,a){var n=r&&5==r.biff;a||(a=Kr(n?3+t.length:5+2*t.length)),a.write_shift(2,e),a.write_shift(n?1:2,t.length),n||a.write_shift(1,1),a.write_shift((n?1:2)*t.length,t,n?"sbcs":"utf16le");var s=a.length>a.l?a.slice(0,a.l):a;return null==s.l&&(s.l=s.length),s}var As=ns;function Cs(e,t,r){var a=e.l+t,n=8!=r.biff&&r.biff?2:4,s=e.read_shift(n),i=e.read_shift(n),o=e.read_shift(2),c=e.read_shift(2);return e.l=a,{s:{r:s,c:o},e:{r:i,c:c}}}function Is(e,t,r,a){var n=r&&5==r.biff;a||(a=Kr(n?16:20)),a.write_shift(2,0),e.style?(a.write_shift(2,e.numFmtId||0),a.write_shift(2,65524)):(a.write_shift(2,e.numFmtId||0),a.write_shift(2,t<<4));var s=0;return e.numFmtId>0&&n&&(s|=1024),a.write_shift(4,s),a.write_shift(4,0),n||a.write_shift(4,0),a.write_shift(2,0),a}function Os(e){var t=Kr(12);return t.l++,t.write_shift(1,e.numFmtId),t.l+=10,t}var Rs=Os;function Ns(e,t,r,a,n,s){var i=Kr(8);return us(e,t,a,i),qn(r,s,i),i}var Fs=function(e,t,r){return 0===t?"":ns(e,0,r)};function Ds(e,t,r){var a,n=e.read_shift(2),s={fBuiltIn:1&n,fWantAdvise:n>>>1&1,fWantPict:n>>>2&1,fOle:n>>>3&1,fOleLink:n>>>4&1,cf:n>>>5&1023,fIcon:n>>>15&1};return 14849===r.sbcch&&(a=function(e,t,r){e.l+=4,t-=4;var a=e.l+t,n=Qn(e,0,r),s=e.read_shift(2);if(s!==(a-=e.l))throw new Error("Malformed AddinUdf: padding = "+a+" != "+s);return e.l+=s,n}(e,t-2,r)),s.body=a||e.read_shift(t-2),"string"==typeof a&&(s.Name=a),s}function Ps(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(r&&2==r.biff?1:2),c=0;(!r||r.biff>=5)&&(5!=r.biff&&(e.l+=2),c=e.read_shift(2),5==r.biff&&(e.l+=2),e.l+=4);var l=rs(e,i,r);32&n&&(l=an[l.charCodeAt(0)]);var f=a-e.l;r&&2==r.biff&&--f;var h=a!=e.l&&0!==o&&f>0?function(e,t,r,a){var n,s=e.l+t,i=Mo(e,a,r);return s!==e.l&&(n=Po(e,s-e.l,i,r)),[i,n]}(e,f,r,o):[];return{chKey:s,Name:l,itab:c,rgce:h}}function Ms(e,t,r){if(r.biff<8)return Ls(e,0,r);if(!(r.biff>8)&&t==e[e.l]+(3==e[e.l+1]?1:0)+1)return Ls(e,0,r);for(var a=[],n=e.l+t,s=e.read_shift(r.biff>8?4:2);0!=s--;)a.push(ds(e,r.biff,r));if(e.l!=n)throw new Error("Bad ExternSheet: "+e.l+" != "+n);return a}function Ls(e,t,r){3==e[e.l+1]&&e[e.l]++;var a=Qn(e,0,r);return 3==a.charCodeAt(0)?a.slice(1):a}function Us(e,t,r){var a=bs(e,6);switch(r.biff){case 2:e.l++,t-=7;break;case 3:case 4:e.l+=2,t-=8;break;default:e.l+=6,t-=12}return[a,Ho(e,t,r)]}function Bs(e,t,r,a){var n=Kr(6+(a||e.length));return n.write_shift(2,t),n.write_shift(2,r),n.write_shift(2,a||e.length),n.write_shift(e.length,e,"sbcs"),n}var Ws={8:function(e,t){var r=e.l+t;e.l+=10;var a=e.read_shift(2);e.l+=4,e.l+=2,e.l+=2,e.l+=2,e.l+=4;var n=e.read_shift(1);return e.l+=n,e.l=r,{fmt:a}}};function zs(e){var t=Kr(24),r=ia(e[0]);t.write_shift(2,r.r),t.write_shift(2,r.r),t.write_shift(2,r.c),t.write_shift(2,r.c);for(var a="d0 c9 ea 79 f9 ba ce 11 8c 82 00 aa 00 4b a9 0b".split(" "),n=0;n<16;++n)t.write_shift(1,parseInt(a[n],16));return N([t,cs(e[1])])}function Hs(e){var t=e[1].Tooltip,r=Kr(10+2*(t.length+1));r.write_shift(2,2048);var a=ia(e[0]);r.write_shift(2,a.r),r.write_shift(2,a.r),r.write_shift(2,a.c),r.write_shift(2,a.c);for(var n=0;n<t.length;++n)r.write_shift(2,t.charCodeAt(n));return r.write_shift(2,0),r}function Vs(e,t,r){if(!r.cellStyles)return jr(e,t);var a=r&&r.biff>=12?4:2,n=e.read_shift(a),s=e.read_shift(a),i=e.read_shift(a),o=e.read_shift(a),c=e.read_shift(2);2==a&&(e.l+=2);var l={s:n,e:s,w:i,ixfe:o,flags:c};return(r.biff>=5||!r.biff)&&(l.level=c>>8&7),l}var $s=hs,Gs=Zn,Xs=as;function js(e,t,r,a,n){return e||(e=Kr(7)),e.write_shift(2,t),e.write_shift(2,r),e.write_shift(1,a||0),e.write_shift(1,n||0),e.write_shift(1,0),e}var Ks=[2,3,48,49,131,139,140,245],Ys=function(){var e={1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127,8:865,9:437,10:850,11:437,13:437,14:850,15:437,16:850,17:437,18:850,19:932,20:850,21:437,22:850,23:865,24:437,25:437,26:850,27:437,28:863,29:850,31:852,34:852,35:852,36:860,37:850,38:866,55:850,64:852,77:936,78:949,79:950,80:874,87:1252,88:1252,89:1252,108:863,134:737,135:852,136:857,204:1257,255:16969},t=Pe({1:437,2:850,3:1252,4:1e4,100:852,101:866,102:865,103:861,104:895,105:620,106:737,107:857,120:950,121:949,122:936,123:932,124:874,125:1255,126:1256,150:10007,151:10029,152:10006,200:1250,201:1251,202:1254,203:1253,0:20127});function r(t,r){var n=r||{};n.dateNF||(n.dateNF="yyyymmdd");var s=va(function(t,r){var n=[],s=x(1);switch(r.type){case"base64":s=C(E(t));break;case"binary":s=C(t);break;case"buffer":case"array":s=t}Xr(s,0);var i=s.read_shift(1),o=!!(136&i),c=!1,l=!1;switch(i){case 2:case 3:case 131:case 139:case 245:break;case 48:case 49:c=!0,o=!0;break;case 140:l=!0;break;default:throw new Error("DBF Unsupported Version: "+i.toString(16))}var f=0,h=521;2==i&&(f=s.read_shift(2)),s.l+=3,2!=i&&(f=s.read_shift(4)),f>1048576&&(f=1e6),2!=i&&(h=s.read_shift(2));var u=s.read_shift(2),d=r.codepage||1252;2!=i&&(s.l+=16,s.read_shift(1),0!==s[s.l]&&(d=e[s[s.l]]),s.l+=1,s.l+=2),l&&(s.l+=36);for(var p=[],m={},v=Math.min(s.length,2==i?521:h-10-(c?264:0)),g=l?32:11;s.l<v&&13!=s[s.l];)switch((m={}).name=(void 0!==a?a.utils.decode(d,s.slice(s.l,s.l+g)):O(s.slice(s.l,s.l+g))).replace(/[\u0000\r\n][\S\s]*$/g,""),s.l+=g,m.type=String.fromCharCode(s.read_shift(1)),2==i||l||(m.offset=s.read_shift(4)),m.len=s.read_shift(1),2==i&&(m.offset=s.read_shift(2)),m.dec=s.read_shift(1),m.name.length&&p.push(m),2!=i&&(s.l+=l?13:14),m.type){case"B":c&&8==m.len||!r.WTF||console.log("Skipping "+m.name+":"+m.type);break;case"G":case"P":r.WTF&&console.log("Skipping "+m.name+":"+m.type);break;case"+":case"0":case"@":case"C":case"D":case"F":case"I":case"L":case"M":case"N":case"O":case"T":case"Y":break;default:throw new Error("Unknown Field Type: "+m.type)}if(13!==s[s.l]&&(s.l=h-1),13!==s.read_shift(1))throw new Error("DBF Terminator not found "+s.l+" "+s[s.l]);s.l=h;var b=0,w=0;for(n[0]=[],w=0;w!=p.length;++w)n[0][w]=p[w].name;for(;f-- >0;)if(42!==s[s.l])for(++s.l,n[++b]=[],w=0,w=0;w!=p.length;++w){var T=s.slice(s.l,s.l+p[w].len);s.l+=p[w].len,Xr(T,0);var y=void 0!==a?a.utils.decode(d,T):O(T);switch(p[w].type){case"C":y.trim().length&&(n[b][w]=y.replace(/([^\s])\s+$/,"$1"));break;case"D":8===y.length?(n[b][w]=new Date(Date.UTC(+y.slice(0,4),+y.slice(4,6)-1,+y.slice(6,8),0,0,0,0)),r&&r.UTC||(n[b][w]=nt(n[b][w]))):n[b][w]=y;break;case"F":n[b][w]=parseFloat(y.trim());break;case"+":case"I":n[b][w]=l?2147483648^T.read_shift(-4,"i"):T.read_shift(4,"i");break;case"L":switch(y.trim().toUpperCase()){case"Y":case"T":n[b][w]=!0;break;case"N":case"F":n[b][w]=!1;break;case"":case"\0":case"?":break;default:throw new Error("DBF Unrecognized L:|"+y+"|")}break;case"M":if(!o)throw new Error("DBF Unexpected MEMO for type "+i.toString(16));n[b][w]="##MEMO##"+(l?parseInt(y.trim(),10):T.read_shift(4));break;case"N":(y=y.replace(/\u0000/g,"").trim())&&"."!=y&&(n[b][w]=+y||0);break;case"@":n[b][w]=new Date(T.read_shift(-8,"f")-621356832e5);break;case"T":var k=T.read_shift(4),_=T.read_shift(4);if(0==k&&0==_)break;n[b][w]=new Date(864e5*(k-2440588)+_),r&&r.UTC||(n[b][w]=nt(n[b][w]));break;case"Y":n[b][w]=T.read_shift(4,"i")/1e4+T.read_shift(4,"i")/1e4*Math.pow(2,32);break;case"O":n[b][w]=-T.read_shift(-8,"f");break;case"B":if(c&&8==p[w].len){n[b][w]=T.read_shift(8,"f");break}case"G":case"P":T.l+=p[w].len;break;case"0":if("_NullFlags"===p[w].name)break;default:throw new Error("DBF Unsupported data type "+p[w].type)}}else s.l+=u;if(2!=i&&s.l<s.length&&26!=s[s.l++])throw new Error("DBF EOF Marker missing "+(s.l-1)+" of "+s.length+" "+s[s.l-1].toString(16));return r&&r.sheetRows&&(n=n.slice(0,r.sheetRows)),r.DBF=p,n}(t,n),n);return s["!cols"]=n.DBF.map((function(e){return{wch:e.len,DBF:e}})),delete n.DBF,s}var n={B:8,C:250,L:1,D:8,"?":0,"":0};return{to_workbook:function(e,t){try{var a=pa(r(e,t),t);return a.bookType="dbf",a}catch(e){if(t&&t.WTF)throw e}return{SheetNames:[],Sheets:{}}},to_sheet:r,from_sheet:function(r,o){if(!r["!ref"])throw new Error("Cannot export empty sheet to DBF");var c=o||{},l=s;if(+c.codepage>=0&&f(+c.codepage),"string"==c.type)throw new Error("Cannot write DBF to JS string");var h=Zr(),u=hh(r,{header:1,raw:!0,cellDates:!0}),d=u[0],p=u.slice(1),m=r["!cols"]||[],v=0,g=0,b=0,w=1;for(v=0;v<d.length;++v)if(((m[v]||{}).DBF||{}).name)d[v]=m[v].DBF.name,++b;else if(null!=d[v]){if(++b,"number"==typeof d[v]&&(d[v]=d[v].toString(10)),"string"!=typeof d[v])throw new Error("DBF Invalid column name "+d[v]+" |"+typeof d[v]+"|");if(d.indexOf(d[v])!==v)for(g=0;g<1024;++g)if(-1==d.indexOf(d[v]+"_"+g)){d[v]+="_"+g;break}}var T=ua(r["!ref"]),y=[],E=[],k=[];for(v=0;v<=T.e.c-T.s.c;++v){var _="",S="",x=0,A=[];for(g=0;g<p.length;++g)null!=p[g][v]&&A.push(p[g][v]);if(0!=A.length&&null!=d[v]){for(g=0;g<A.length;++g){switch(typeof A[g]){case"number":S="B";break;case"string":default:S="C";break;case"boolean":S="L";break;case"object":S=A[g]instanceof Date?"D":"C"}x=Math.max(x,(void 0!==a&&"string"==typeof A[g]?a.utils.encode(i,A[g]):String(A[g])).length),_=_&&_!=S?"C":S}x>250&&(x=250),"C"==(S=((m[v]||{}).DBF||{}).type)&&m[v].DBF.len>x&&(x=m[v].DBF.len),"B"==_&&"N"==S&&(_="N",k[v]=m[v].DBF.dec,x=m[v].DBF.len),E[v]="C"==_||"N"==S?x:n[_]||0,w+=E[v],y[v]=_}else y[v]="?"}var C=h.next(32);for(C.write_shift(4,318902576),C.write_shift(4,p.length),C.write_shift(2,296+32*b),C.write_shift(2,w),v=0;v<4;++v)C.write_shift(4,0);var I=+t[s]||3;for(C.write_shift(4,0|I<<8),e[I]!=+c.codepage&&(c.codepage&&console.error("DBF Unsupported codepage "+s+", using 1252"),s=1252),v=0,g=0;v<d.length;++v)if(null!=d[v]){var O=h.next(32),R=(d[v].slice(-10)+"\0\0\0\0\0\0\0\0\0\0\0").slice(0,11);O.write_shift(1,R,"sbcs"),O.write_shift(1,"?"==y[v]?"C":y[v],"sbcs"),O.write_shift(4,g),O.write_shift(1,E[v]||n[y[v]]||0),O.write_shift(1,k[v]||0),O.write_shift(1,2),O.write_shift(4,0),O.write_shift(1,0),O.write_shift(4,0),O.write_shift(4,0),g+=E[v]||n[y[v]]||0}var N=h.next(264);for(N.write_shift(4,13),v=0;v<65;++v)N.write_shift(4,0);for(v=0;v<p.length;++v){var F=h.next(w);for(F.write_shift(1,0),g=0;g<d.length;++g)if(null!=d[g])switch(y[g]){case"L":F.write_shift(1,null==p[v][g]?63:p[v][g]?84:70);break;case"B":F.write_shift(8,p[v][g]||0,"f");break;case"N":var D="0";for("number"==typeof p[v][g]&&(D=p[v][g].toFixed(k[g]||0)),D.length>E[g]&&(D=D.slice(0,E[g])),b=0;b<E[g]-D.length;++b)F.write_shift(1,32);F.write_shift(1,D,"sbcs");break;case"D":p[v][g]?(F.write_shift(4,("0000"+p[v][g].getFullYear()).slice(-4),"sbcs"),F.write_shift(2,("00"+(p[v][g].getMonth()+1)).slice(-2),"sbcs"),F.write_shift(2,("00"+p[v][g].getDate()).slice(-2),"sbcs")):F.write_shift(8,"00000000","sbcs");break;case"C":var P=F.l,M=String(null!=p[v][g]?p[v][g]:"").slice(0,E[g]);for(F.write_shift(1,M,"cpstr"),P+=E[g]-F.l,b=0;b<P;++b)F.write_shift(1,32)}}return s=l,h.next(1).write_shift(1,26),h.end()}}}(),Zs=function(){var e={AA:"À",BA:"Á",CA:"Â",DA:195,HA:"Ä",JA:197,AE:"È",BE:"É",CE:"Ê",HE:"Ë",AI:"Ì",BI:"Í",CI:"Î",HI:"Ï",AO:"Ò",BO:"Ó",CO:"Ô",DO:213,HO:"Ö",AU:"Ù",BU:"Ú",CU:"Û",HU:"Ü",Aa:"à",Ba:"á",Ca:"â",Da:227,Ha:"ä",Ja:229,Ae:"è",Be:"é",Ce:"ê",He:"ë",Ai:"ì",Bi:"í",Ci:"î",Hi:"ï",Ao:"ò",Bo:"ó",Co:"ô",Do:245,Ho:"ö",Au:"ù",Bu:"ú",Cu:"û",Hu:"ü",KC:"Ç",Kc:"ç",q:"æ",z:"œ",a:"Æ",j:"Œ",DN:209,Dn:241,Hy:255,S:169,c:170,R:174,"B ":180,0:176,1:177,2:178,3:179,5:181,6:182,7:183,Q:185,k:186,b:208,i:216,l:222,s:240,y:248,"!":161,'"':162,"#":163,"(":164,"%":165,"'":167,"H ":168,"+":171,";":187,"<":188,"=":189,">":190,"?":191,"{":223},t=new RegExp("N("+Fe(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1").replace("{","\\{")+"|\\|)","gm");try{t=new RegExp("N("+Fe(e).join("|").replace(/\|\|\|/,"|\\||").replace(/([?()+])/g,"\\$1")+"|\\|)","gm")}catch(e){}var r=function(t,r){var a=e[r];return"number"==typeof a?g(a):a},n=function(e,t,r){var a=t.charCodeAt(0)-32<<4|r.charCodeAt(0)-48;return 59==a?e:g(a)};e["|"]=254;function s(e,s){var i,o=e.split(/[\n\r]+/),c=-1,l=-1,h=0,u=0,d=[],p=[],m=null,v={},g=[],b=[],w=[],T=0,y={Workbook:{WBProps:{},Names:[]}};for(+s.codepage>=0&&f(+s.codepage);h!==o.length;++h){T=0;var E,k=o[h].trim().replace(/\x1B([\x20-\x2F])([\x30-\x3F])/g,n).replace(t,r),_=k.replace(/;;/g,"\0").split(";").map((function(e){return e.replace(/\u0000/g,";")})),S=_[0];if(k.length>0)switch(S){case"ID":case"E":case"B":case"W":break;case"O":for(u=1;u<_.length;++u)if("V"===_[u].charAt(0)){var x=parseInt(_[u].slice(1),10);x>=1&&x<=4&&(y.Workbook.WBProps.date1904=!0)}break;case"P":"P"===_[1].charAt(0)&&p.push(k.slice(3).replace(/;;/g,";"));break;case"NN":var A={Sheet:0};for(u=1;u<_.length;++u)switch(_[u].charAt(0)){case"N":A.Name=_[u].slice(1);break;case"E":A.Ref=(s&&s.sheet||"Sheet1")+"!"+lo(_[u].slice(1))}y.Workbook.Names.push(A);break;case"C":var C=!1,I=!1,O=!1,R=!1,N=-1,F=-1,D="",P="z",M="";for(u=1;u<_.length;++u)switch(_[u].charAt(0)){case"A":M=_[u].slice(1);break;case"X":l=parseInt(_[u].slice(1),10)-1,I=!0;break;case"Y":for(c=parseInt(_[u].slice(1),10)-1,I||(l=0),i=d.length;i<=c;++i)d[i]=[];break;case"K":'"'===(E=_[u].slice(1)).charAt(0)?(E=E.slice(1,E.length-1),P="s"):"TRUE"===E||"FALSE"===E?(E="TRUE"===E,P="b"):"#"==E.charAt(0)&&null!=rn[E]?(P="e",E=rn[E]):isNaN(Ze(E))||(E=Ze(E),P="n",null!==m&&me(m)&&s.cellDates&&(P="number"==typeof(E=ze(y.Workbook.WBProps.date1904?E+1462:E))?"n":"d")),void 0!==a&&"string"==typeof E&&"string"!=(s||{}).type&&(s||{}).codepage&&(E=a.utils.decode(s.codepage,E)),C=!0;break;case"E":R=!0,D=lo(_[u].slice(1),{r:c,c:l});break;case"S":O=!0;break;case"G":break;case"R":N=parseInt(_[u].slice(1),10)-1;break;case"C":F=parseInt(_[u].slice(1),10)-1;break;default:if(s&&s.WTF)throw new Error("SYLK bad record "+k)}if(C&&(d[c][l]?(d[c][l].t=P,d[c][l].v=E):d[c][l]={t:P,v:E},m&&(d[c][l].z=m),!1!==s.cellText&&m&&(d[c][l].w=be(d[c][l].z,d[c][l].v,{date1904:y.Workbook.WBProps.date1904})),m=null),O){if(R)throw new Error("SYLK shared formula cannot have own formula");var L=N>-1&&d[N][F];if(!L||!L[1])throw new Error("SYLK shared formula cannot find base");D=uo(L[1],{r:c-N,c:l-F})}D&&(d[c][l]?d[c][l].f=D:d[c][l]={t:"n",f:D}),M&&(d[c][l]||(d[c][l]={t:"z"}),d[c][l].c=[{a:"SheetJSYLK",t:M}]);break;case"F":var U=0;for(u=1;u<_.length;++u)switch(_[u].charAt(0)){case"X":l=parseInt(_[u].slice(1),10)-1,++U;break;case"Y":for(c=parseInt(_[u].slice(1),10)-1,i=d.length;i<=c;++i)d[i]=[];break;case"M":T=parseInt(_[u].slice(1),10)/20;break;case"F":case"G":case"S":case"D":case"N":break;case"P":m=p[parseInt(_[u].slice(1),10)];break;case"W":for(w=_[u].slice(1).split(" "),i=parseInt(w[0],10);i<=parseInt(w[1],10);++i)T=parseInt(w[2],10),b[i-1]=0===T?{hidden:!0}:{wch:T};break;case"C":b[l=parseInt(_[u].slice(1),10)-1]||(b[l]={});break;case"R":g[c=parseInt(_[u].slice(1),10)-1]||(g[c]={}),T>0?(g[c].hpt=T,g[c].hpx=Pi(T)):0===T&&(g[c].hidden=!0);break;default:if(s&&s.WTF)throw new Error("SYLK bad record "+k)}U<1&&(m=null);break;default:if(s&&s.WTF)throw new Error("SYLK bad record "+k)}}return g.length>0&&(v["!rows"]=g),b.length>0&&(v["!cols"]=b),b.forEach((function(e){Ni(e)})),s&&s.sheetRows&&(d=d.slice(0,s.sheetRows)),[d,v,y]}function i(e,t,r,a,n,s){var i="C;Y"+(r+1)+";X"+(a+1)+";K";switch(e.t){case"n":i+=e.v||0,e.f&&!e.F&&(i+=";E"+ho(e.f,{r:r,c:a}));break;case"b":i+=e.v?"TRUE":"FALSE";break;case"e":i+=e.w||tn[e.v]||e.v;break;case"d":i+=We(Xe(e.v,s),s);break;case"s":i+='"'+(null==e.v?"":String(e.v)).replace(/"/g,"").replace(/;/g,";;")+'"'}return i}function o(e,t,r){return"C;Y"+(t+1)+";X"+(r+1)+";A"+e.map((function(e){return e.t})).join("").replace(/\n/g," :").replace(/\r/g," =")}return{to_workbook:function(e,t){var r=function(e,t){switch(t.type){case"base64":return s(E(e),t);case"binary":return s(e,t);case"buffer":return s(k&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return s(je(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),a=r[0],n=r[1],i=r[2],o=Ke(t);o.date1904=(((i||{}).Workbook||{}).WBProps||{}).date1904;var c=va(a,o);Fe(n).forEach((function(e){c[e]=n[e]}));var l=pa(c,t);return Fe(i).forEach((function(e){l[e]=i[e]})),l.bookType="sylk",l},from_sheet:function(e,t,r){t||(t={}),t._formats=["General"];var a,n=["ID;PSheetJS;N;E"],s=[],c=ua(e["!ref"]||"A1"),l=null!=e["!data"],f="\r\n",h=(((r||{}).Workbook||{}).WBProps||{}).date1904;n.push("P;PGeneral");var u,d=c.s.r,p=c.s.c,m=[];if(e["!ref"])for(d=c.s.r;d<=c.e.r;++d)if(!l||e["!data"][d]){for(m=[],p=c.s.c;p<=c.e.c;++p)(a=l?e["!data"][d][p]:e[sa(p)+aa(d)])&&a.c&&m.push(o(a.c,d,p));m.length&&s.push(m.join(f))}if(e["!ref"])for(d=c.s.r;d<=c.e.r;++d)if(!l||e["!data"][d]){for(m=[],p=c.s.c;p<=c.e.c;++p)if((a=l?e["!data"][d][p]:e[sa(p)+aa(d)])&&(null!=a.v||a.f&&!a.F)){if("General"!=(a.z||("d"==a.t?G[14]:"General"))){var v=t._formats.indexOf(a.z);-1==v&&(t._formats.push(a.z),v=t._formats.length-1,n.push("P;P"+a.z.replace(/;/g,";;"))),m.push("F;P"+v+";Y"+(d+1)+";X"+(p+1))}m.push(i(a,0,d,p,0,h))}s.push(m.join(f))}return n.push("F;P0;DG0G8;M255"),e["!cols"]&&(u=n,e["!cols"].forEach((function(e,t){var r="F;W"+(t+1)+" "+(t+1)+" ";e.hidden?r+="0":("number"!=typeof e.width||e.wpx||(e.wpx=Ai(e.width)),"number"!=typeof e.wpx||e.wch||(e.wch=Ci(e.wpx)),"number"==typeof e.wch&&(r+=Math.round(e.wch)))," "!=r.charAt(r.length-1)&&u.push(r)}))),e["!rows"]&&function(e,t){t.forEach((function(t,r){var a="F;";t.hidden?a+="M0;":t.hpt?a+="M"+20*t.hpt+";":t.hpx&&(a+="M"+20*Di(t.hpx)+";"),a.length>2&&e.push(a+"R"+(r+1))}))}(n,e["!rows"]),e["!ref"]&&n.push("B;Y"+(c.e.r-c.s.r+1)+";X"+(c.e.c-c.s.c+1)+";D"+[c.s.c,c.s.r,c.e.c,c.e.r].join(" ")),n.push("O;L;D;B"+(h?";V4":"")+";K47;G100 0.001"),delete t._formats,n.join(f)+f+s.join(f)+f+"E"+f}}}(),Js=function(){function e(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s)if("BOT"!==r[s].trim()){if(!(a<0)){for(var o=r[s].trim().split(","),c=o[0],l=o[1],f=r[++s]||"";1&(f.match(/["]/g)||[]).length&&s<r.length-1;)f+="\n"+r[++s];switch(f=f.trim(),+c){case-1:if("BOT"===f){i[++a]=[],n=0;continue}if("EOD"!==f)throw new Error("Unrecognized DIF special command "+f);break;case 0:"TRUE"===f?i[a][n]=!0:"FALSE"===f?i[a][n]=!1:isNaN(Ze(l))?isNaN(rt(l).getDate())?i[a][n]=l:(i[a][n]=Xe(l),t&&t.UTC||(i[a][n]=nt(i[a][n]))):i[a][n]=Ze(l),++n;break;case 1:(f=(f=f.slice(1,f.length-1)).replace(/""/g,'"'))&&f.match(/^=".*"$/)&&(f=f.slice(2,-1)),i[a][n++]=""!==f?f:null}if("EOD"===f)break}}else i[++a]=[],n=0;return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}function t(t,r){return va(function(t,r){switch(r.type){case"base64":return e(E(t),r);case"binary":return e(t,r);case"buffer":return e(k&&Buffer.isBuffer(t)?t.toString("binary"):O(t),r);case"array":return e(je(t),r)}throw new Error("Unrecognized type "+r.type)}(t,r),r)}function r(e,t){return"0,"+String(e)+"\r\n"+t}function a(e){return'1,0\r\n"'+e.replace(/"/g,'""')+'"'}return{to_workbook:function(e,r){var a=pa(t(e,r),r);return a.bookType="dif",a},to_sheet:t,from_sheet:function(e){if(!e["!ref"])throw new Error("Cannot export empty sheet to DIF");for(var t=ua(e["!ref"]),n=null!=e["!data"],s=['TABLE\r\n0,1\r\n"sheetjs"\r\n',"VECTORS\r\n0,"+(t.e.r-t.s.r+1)+'\r\n""\r\n',"TUPLES\r\n0,"+(t.e.c-t.s.c+1)+'\r\n""\r\n','DATA\r\n0,0\r\n""\r\n'],i=t.s.r;i<=t.e.r;++i){for(var o=n?e["!data"][i]:[],c="-1,0\r\nBOT\r\n",l=t.s.c;l<=t.e.c;++l){var f=n?o&&o[l]:e[oa({r:i,c:l})];if(null!=f){switch(f.t){case"n":null!=f.w?c+="0,"+f.w+"\r\nV":null!=f.v?c+=r(f.v,"V"):null==f.f||f.F?c+='1,0\r\n""':c+=a("="+f.f);break;case"b":c+=f.v?r(1,"TRUE"):r(0,"FALSE");break;case"s":c+=a(isNaN(+f.v)?f.v:'="'+f.v+'"');break;case"d":f.w||(f.w=be(f.z||G[14],We(Xe(f.v)))),c+=r(f.w,"V");break;default:c+='1,0\r\n""'}c+="\r\n"}else c+='1,0\r\n""\r\n'}s.push(c)}return s.join("")+"-1,0\r\nEOD"}}}(),qs=function(){function e(e){return e.replace(/\\/g,"\\b").replace(/:/g,"\\c").replace(/\n/g,"\\n")}function t(e,t){return va(function(e,t){for(var r=e.split("\n"),a=-1,n=-1,s=0,i=[];s!==r.length;++s){var o=r[s].trim().split(":");if("cell"===o[0]){var c=ia(o[1]);if(i.length<=c.r)for(a=i.length;a<=c.r;++a)i[a]||(i[a]=[]);switch(a=c.r,n=c.c,o[2]){case"t":i[a][n]=o[3].replace(/\\b/g,"\\").replace(/\\c/g,":").replace(/\\n/g,"\n");break;case"v":i[a][n]=+o[3];break;case"vtf":var l=o[o.length-1];case"vtc":"nl"===o[3]?i[a][n]=!!+o[4]:i[a][n]=+o[4],"vtf"==o[2]&&(i[a][n]=[i[a][n],l])}}}return t&&t.sheetRows&&(i=i.slice(0,t.sheetRows)),i}(e,t),t)}var r=["socialcalc:version:1.5","MIME-Version: 1.0","Content-Type: multipart/mixed; boundary=SocialCalcSpreadsheetControlSave"].join("\n"),a=["--SocialCalcSpreadsheetControlSave","Content-type: text/plain; charset=UTF-8"].join("\n")+"\n",n=["# SocialCalc Spreadsheet Control Save","part:sheet"].join("\n"),s="--SocialCalcSpreadsheetControlSave--";function i(t){if(!t||!t["!ref"])return"";for(var r,a=[],n=[],s="",i=ca(t["!ref"]),o=null!=t["!data"],c=i.s.r;c<=i.e.r;++c)for(var l=i.s.c;l<=i.e.c;++l)if(s=oa({r:c,c:l}),(r=o?(t["!data"][c]||[])[l]:t[s])&&null!=r.v&&"z"!==r.t){switch(n=["cell",s,"t"],r.t){case"s":case"str":n.push(e(r.v));break;case"n":r.f?(n[2]="vtf",n[3]="n",n[4]=r.v,n[5]=e(r.f)):(n[2]="v",n[3]=r.v);break;case"b":n[2]="vt"+(r.f?"f":"c"),n[3]="nl",n[4]=r.v?"1":"0",n[5]=e(r.f||(r.v?"TRUE":"FALSE"));break;case"d":var f=We(Xe(r.v));n[2]="vtc",n[3]="nd",n[4]=""+f,n[5]=r.w||be(r.z||G[14],f);break;case"e":continue}a.push(n.join(":"))}return a.push("sheet:c:"+(i.e.c-i.s.c+1)+":r:"+(i.e.r-i.s.r+1)+":tvf:1"),a.push("valueformat:1:text-wiki"),a.join("\n")}return{to_workbook:function(e,r){return pa(t(e,r),r)},to_sheet:t,from_sheet:function(e){return[r,a,n,a,i(e),s].join("\n")}}}(),Qs=function(){function e(e,t,r,a,n){n.raw?t[r][a]=e:""===e||("TRUE"===e?t[r][a]=!0:"FALSE"===e?t[r][a]=!1:isNaN(Ze(e))?isNaN(rt(e).getDate())?t[r][a]=e:t[r][a]=Xe(e):t[r][a]=Ze(e))}var t={44:",",9:"\t",59:";",124:"|"},r={44:3,9:2,59:1,124:0};function n(e){for(var a={},n=!1,s=0,i=0;s<e.length;++s)34==(i=e.charCodeAt(s))?n=!n:!n&&i in t&&(a[i]=(a[i]||0)+1);for(s in i=[],a)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);if(!i.length)for(s in a=r)Object.prototype.hasOwnProperty.call(a,s)&&i.push([a[s],s]);return i.sort((function(e,t){return e[0]-t[0]||r[e[1]]-r[t[1]]})),t[i.pop()[1]]||44}function s(e,t){var r=t||{},a="";null!=w&&null==r.dense&&(r.dense=w);var s={};r.dense&&(s["!data"]=[]);var i={s:{c:0,r:0},e:{c:0,r:0}};"sep="==e.slice(0,4)?13==e.charCodeAt(5)&&10==e.charCodeAt(6)?(a=e.charAt(4),e=e.slice(7)):13==e.charCodeAt(5)||10==e.charCodeAt(5)?(a=e.charAt(4),e=e.slice(6)):a=n(e.slice(0,1024)):a=r&&r.FS?r.FS:n(e.slice(0,1024));var o,c,l=0,f=0,h=0,u=0,d=0,p=a.charCodeAt(0),m=!1,v=0,g=e.charCodeAt(0),b=null!=r.dateNF?(c=(c="number"==typeof(o=r.dateNF)?G[o]:o).replace(_e,"(\\d+)"),_e.lastIndex=0,new RegExp("^"+c+"$")):null;function T(){var t=e.slice(u,d);"\r"==t.slice(-1)&&(t=t.slice(0,-1));var a={};if('"'==t.charAt(0)&&'"'==t.charAt(t.length-1)&&(t=t.slice(1,-1).replace(/""/g,'"')),!1!==r.cellText&&(a.w=t),0===t.length)a.t="z";else if(r.raw)a.t="s",a.v=t;else if(0===t.trim().length)a.t="s",a.v=t;else if(61==t.charCodeAt(0))34==t.charCodeAt(1)&&34==t.charCodeAt(t.length-1)?(a.t="s",a.v=t.slice(2,-1).replace(/""/g,'"')):1!=t.length?(a.t="s",a.f=t.slice(1),a.v=t):(a.t="s",a.v=t);else if("TRUE"==t)a.t="b",a.v=!0;else if("FALSE"==t)a.t="b",a.v=!1;else if(isNaN(h=Ze(t)))if(!isNaN((h=rt(t)).getDate())||b&&t.match(b)){if(a.z=r.dateNF||G[14],b&&t.match(b)){var n=function(e,t,r){var a=-1,n=-1,s=-1,i=-1,o=-1,c=-1;(t.match(_e)||[]).forEach((function(e,t){var l=parseInt(r[t+1],10);switch(e.toLowerCase().charAt(0)){case"y":a=l;break;case"d":s=l;break;case"h":i=l;break;case"s":c=l;break;case"m":i>=0?o=l:n=l}})),_e.lastIndex=0,c>=0&&-1==o&&n>=0&&(o=n,n=-1);var l=(""+(a>=0?a:(new Date).getFullYear())).slice(-4)+"-"+("00"+(n>=1?n:1)).slice(-2)+"-"+("00"+(s>=1?s:1)).slice(-2);7==l.length&&(l="0"+l),8==l.length&&(l="20"+l);var f=("00"+(i>=0?i:0)).slice(-2)+":"+("00"+(o>=0?o:0)).slice(-2)+":"+("00"+(c>=0?c:0)).slice(-2);return-1==i&&-1==o&&-1==c?l:-1==a&&-1==n&&-1==s?f:l+"T"+f}(0,r.dateNF,t.match(b)||[]);h=Xe(n),r&&!1===r.UTC&&(h=nt(h))}else r&&!1===r.UTC?h=nt(h):!1!==r.cellText&&r.dateNF&&(a.w=be(a.z,h));r.cellDates?(a.t="d",a.v=h):(a.t="n",a.v=We(h)),r.cellNF||delete a.z}else a.t="s",a.v=t;else a.t="n",a.v=h;if("z"==a.t||(r.dense?(s["!data"][l]||(s["!data"][l]=[]),s["!data"][l][f]=a):s[oa({c:f,r:l})]=a),u=d+1,g=e.charCodeAt(u),i.e.c<f&&(i.e.c=f),i.e.r<l&&(i.e.r=l),v==p)++f;else if(f=0,++l,r.sheetRows&&r.sheetRows<=l)return!0}e:for(;d<e.length;++d)switch(v=e.charCodeAt(d)){case 34:34===g&&(m=!m);break;case 13:if(m)break;10==e.charCodeAt(d+1)&&++d;case p:case 10:if(!m&&T())break e}return d-u>0&&T(),s["!ref"]=la(i),s}function i(t,r){var n="",i="string"==r.type?[0,0,0,0]:Kf(t,r);switch(r.type){case"base64":n=E(t);break;case"binary":case"string":n=t;break;case"buffer":n=65001==r.codepage?t.toString("utf8"):r.codepage&&void 0!==a?a.utils.decode(r.codepage,t):k&&Buffer.isBuffer(t)?t.toString("binary"):O(t);break;case"array":n=je(t);break;default:throw new Error("Unrecognized type "+r.type)}return 239==i[0]&&187==i[1]&&191==i[2]?n=Zt(n.slice(3)):"string"!=r.type&&"buffer"!=r.type&&65001==r.codepage?n=Zt(n):"binary"==r.type&&void 0!==a&&r.codepage&&(n=a.utils.decode(r.codepage,a.utils.encode(28591,n))),"socialcalc:version:"==n.slice(0,19)?qs.to_sheet("string"==r.type?n:Zt(n),r):function(t,r){return r&&r.PRN?r.FS||"sep="==t.slice(0,4)||t.indexOf("\t")>=0||t.indexOf(",")>=0||t.indexOf(";")>=0?s(t,r):va(function(t,r){var a=r||{},n=[];if(!t||0===t.length)return n;for(var s=t.split(/[\r\n]/),i=s.length-1;i>=0&&0===s[i].length;)--i;for(var o=10,c=0,l=0;l<=i;++l)-1==(c=s[l].indexOf(" "))?c=s[l].length:c++,o=Math.max(o,c);for(l=0;l<=i;++l){n[l]=[];var f=0;for(e(s[l].slice(0,o).trim(),n,l,f,a),f=1;f<=(s[l].length-o)/10+1;++f)e(s[l].slice(o+10*(f-1),o+10*f).trim(),n,l,f,a)}return a.sheetRows&&(n=n.slice(0,a.sheetRows)),n}(t,r),r):s(t,r)}(n,r)}return{to_workbook:function(e,t){return pa(i(e,t),t)},to_sheet:i,from_sheet:function(e){var t=[];if(!e["!ref"])return"";for(var r,a=ua(e["!ref"]),n=null!=e["!data"],s=a.s.r;s<=a.e.r;++s){for(var i=[],o=a.s.c;o<=a.e.c;++o){var c=oa({r:s,c:o});if((r=n?(e["!data"][s]||[])[o]:e[c])&&null!=r.v){for(var l=(r.w||(da(r),r.w)||"").slice(0,10);l.length<10;)l+=" ";i.push(l+(0===o?" ":""))}else i.push("          ")}t.push(i.join(""))}return t.join("\n")}}}(),ei=function(){function e(e,t,r){if(e){Xr(e,e.l||0);for(var a=r.Enum||T;e.l<e.length;){var n=e.read_shift(2),s=a[n]||a[65535],i=e.read_shift(2),o=e.l+i,c=s.f&&s.f(e,i,r);if(e.l=o,t(c,s,n))return}}}var t=["mmmm","dd-mmm-yyyy","dd-mmm","mmm-yyyy","@","mm/dd","hh:mm:ss AM/PM","hh:mm AM/PM","mm/dd/yyyy","mm/dd","hh:mm:ss","hh:mm"];function r(r,a){if(!r)return r;var n=a||{};null!=w&&null==n.dense&&(n.dense=w);var s={},i="Sheet1",o="",c=0,l={},f=[],h=[],u=[];n.dense&&(u=s["!data"]=[]);var d={s:{r:0,c:0},e:{r:0,c:0}},p=n.sheetRows||0,m={};if(81==r[4]&&80==r[5]&&87==r[6])return function(e,t){Xr(e,0);var r=t||{};null!=w&&null==r.dense&&(r.dense=w);var a={};r.dense&&(a["!data"]=[]);var n=[],s="",i={s:{r:-1,c:-1},e:{r:-1,c:-1}},o=0,c=0,l=0,f=0,h={SheetNames:[],Sheets:{}},u=[];e:for(;e.l<e.length;){var d=e.read_shift(2),p=e.read_shift(2),m=e.slice(e.l,e.l+p);switch(Xr(m,0),d){case 1:if(962023505!=m.read_shift(4))throw"Bad QPW9 BOF!";break;case 2:break e;case 8:case 1025:case 1026:case 1032:default:break;case 10:for(var v=m.read_shift(4),g=(m.length-m.l)/v|0,b=0;b<v;++b){var T=m.l+g,y={};m.l+=2,y.numFmtId=m.read_shift(2),k[y.numFmtId]&&(y.z=k[y.numFmtId]),m.l=T,u.push(y)}break;case 1031:for(m.l+=12;m.l<m.length;)o=m.read_shift(2),c=m.read_shift(1),n.push(m.read_shift(o,"cstr"));break;case 1537:var E=m.read_shift(2);a={},r.dense&&(a["!data"]=[]),i.s.c=m.read_shift(2),i.e.c=m.read_shift(2),i.s.r=m.read_shift(4),i.e.r=m.read_shift(4),m.l+=4,m.l+2<m.length&&(o=m.read_shift(2),c=m.read_shift(1),s=0==o?"":m.read_shift(o,"cstr")),s||(s=sa(E));break;case 1538:if(i.s.c>255||i.s.r>999999)break;i.e.c<i.s.c&&(i.e.c=i.s.c),i.e.r<i.s.r&&(i.e.r=i.s.r),a["!ref"]=la(i),wh(h,a,s);break;case 2561:l=m.read_shift(2),i.e.c<l&&(i.e.c=l),i.s.c>l&&(i.s.c=l),f=m.read_shift(4),i.s.r>f&&(i.s.r=f),f=m.read_shift(4),i.e.r<f&&(i.e.r=f);break;case 3073:f=m.read_shift(4),o=m.read_shift(4),i.s.r>f&&(i.s.r=f),i.e.r<f+o-1&&(i.e.r=f+o-1);for(var S=sa(l);m.l<m.length;){var x={t:"z"},A=m.read_shift(1),C=-1;128&A&&(C=m.read_shift(2));var I=64&A?m.read_shift(2)-1:0;switch(31&A){case 0:case 1:break;case 2:x={t:"n",v:m.read_shift(2)};break;case 3:x={t:"n",v:m.read_shift(2,"i")};break;case 4:x={t:"n",v:Da(m)};break;case 5:x={t:"n",v:m.read_shift(8,"f")};break;case 7:x={t:"s",v:n[c=m.read_shift(4)-1]};break;case 8:x={t:"n",v:m.read_shift(8,"f")},m.l+=2,m.l+=4,isNaN(x.v)&&(x={t:"e",v:15});break;default:throw"Unrecognized QPW cell type "+(31&A)}-1!=C&&(u[C-1]||{}).z&&(x.z=u[C-1].z);var O=0;if(32&A)switch(31&A){case 2:case 7:O=m.read_shift(2);break;case 3:O=m.read_shift(2,"i");break;default:throw"Unsupported delta for QPW cell type "+(31&A)}if(r.sheetStubs||"z"!=x.t){var R=Ke(x);"n"==x.t&&x.z&&me(x.z)&&r.cellDates&&(R.v=ze(x.v),R.t="number"==typeof R.v?"n":"d"),null!=a["!data"]?(a["!data"][f]||(a["!data"][f]=[]),a["!data"][f][l]=R):a[S+aa(f)]=R}for(++f,--o;I-- >0&&o>=0;){if(32&A)switch(31&A){case 2:x={t:"n",v:x.v+O&65535};break;case 3:(x={t:"n",v:x.v+O&65535}).v>32767&&(x.v-=65536);break;case 7:x={t:"s",v:n[c=c+O>>>0]};break;default:throw"Cannot apply delta for QPW cell type "+(31&A)}else switch(31&A){case 1:x={t:"z"};break;case 2:x={t:"n",v:m.read_shift(2)};break;case 7:x={t:"s",v:n[c=m.read_shift(4)-1]};break;default:throw"Cannot apply repeat for QPW cell type "+(31&A)}(r.sheetStubs||"z"!=x.t)&&(null!=a["!data"]?(a["!data"][f]||(a["!data"][f]=[]),a["!data"][f][l]=x):a[S+aa(f)]=x),++f,--o}}break;case 3074:l=m.read_shift(2),f=m.read_shift(4);var N=_(m);null!=a["!data"]?(a["!data"][f]||(a["!data"][f]=[]),a["!data"][f][l]={t:"s",v:N}):a[sa(l)+aa(f)]={t:"s",v:N}}e.l+=p}return h}(r,a);if(0==r[2]&&(8==r[3]||9==r[3])&&r.length>=16&&5==r[14]&&108===r[15])throw new Error("Unsupported Works 3 for Mac file");if(2==r[2])n.Enum=T,e(r,(function(e,r,a){switch(a){case 0:n.vers=e,e>=4096&&(n.qpro=!0);break;case 255:n.vers=e,n.works=!0;break;case 6:d=e;break;case 204:e&&(o=e);break;case 222:o=e;break;case 15:case 51:(!n.qpro&&!n.works||51==a)&&e[1].v.charCodeAt(0)<48&&(e[1].v=e[1].v.slice(1)),(n.works||n.works2)&&(e[1].v=e[1].v.replace(/\r\n/g,"\n"));case 13:case 14:case 16:112==(112&e[2])&&(15&e[2])>1&&(15&e[2])<15&&(e[1].z=n.dateNF||t[(15&e[2])-1]||G[14],n.cellDates&&(e[1].v=ze(e[1].v),e[1].t="number"==typeof e[1].v?"n":"d")),n.qpro&&e[3]>c&&(s["!ref"]=la(d),l[i]=s,f.push(i),s={},n.dense&&(u=s["!data"]=[]),d={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],i=o||"Sheet"+(c+1),o="");var h=n.dense?(u[e[0].r]||[])[e[0].c]:s[oa(e[0])];if(h){h.t=e[1].t,h.v=e[1].v,null!=e[1].z&&(h.z=e[1].z),null!=e[1].f&&(h.f=e[1].f),m=h;break}n.dense?(u[e[0].r]||(u[e[0].r]=[]),u[e[0].r][e[0].c]=e[1]):s[oa(e[0])]=e[1],m=e[1];break;case 21509:n.works2=!0;break;case 21506:5281==e&&(m.z="hh:mm:ss",n.cellDates&&"n"==m.t&&(m.v=ze(m.v),m.t="number"==typeof m.v?"n":"d"))}}),n);else{if(26!=r[2]&&14!=r[2])throw new Error("Unrecognized LOTUS BOF "+r[2]);n.Enum=y,14==r[2]&&(n.qpro=!0,r.l=0),e(r,(function(e,t,r){switch(r){case 204:i=e;break;case 22:e[1].v.charCodeAt(0)<48&&(e[1].v=e[1].v.slice(1)),e[1].v=e[1].v.replace(/\x0F./g,(function(e){return String.fromCharCode(e.charCodeAt(1)-32)})).replace(/\r\n/g,"\n");case 23:case 24:case 25:case 37:case 39:case 40:if(e[3]>c&&(s["!ref"]=la(d),l[i]=s,f.push(i),s={},n.dense&&(u=s["!data"]=[]),d={s:{r:0,c:0},e:{r:0,c:0}},c=e[3],i="Sheet"+(c+1)),p>0&&e[0].r>=p)break;n.dense?(u[e[0].r]||(u[e[0].r]=[]),u[e[0].r][e[0].c]=e[1]):s[oa(e[0])]=e[1],d.e.c<e[0].c&&(d.e.c=e[0].c),d.e.r<e[0].r&&(d.e.r=e[0].r);break;case 27:e[14e3]&&(h[e[14e3][0]]=e[14e3][1]);break;case 1537:h[e[0]]=e[1],e[0]==c&&(i=e[1])}}),n)}if(s["!ref"]=la(d),l[o||i]=s,f.push(o||i),!h.length)return{SheetNames:f,Sheets:l};for(var v={},g=[],b=0;b<h.length;++b)l[f[b]]?(g.push(h[b]||f[b]),v[h[b]]=l[h[b]]||l[f[b]]):(g.push(h[b]),v[h[b]]={"!ref":"A1"});return{SheetNames:g,Sheets:v}}function a(e,t,r){var a=[{c:0,r:0},{t:"n",v:0},0,0];return r.qpro&&20768!=r.vers?(a[0].c=e.read_shift(1),a[3]=e.read_shift(1),a[0].r=e.read_shift(2),e.l+=2):r.works?(a[0].c=e.read_shift(2),a[0].r=e.read_shift(2),a[2]=e.read_shift(2)):(a[2]=e.read_shift(1),a[0].c=e.read_shift(2),a[0].r=e.read_shift(2)),a}function n(e){return e.z&&me(e.z)?240|(t.indexOf(e.z)+1||2):255}function s(e,t,r){var a=Kr(7+r.length);a.write_shift(1,255),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(1,39);for(var n=0;n<a.length;++n){var s=r.charCodeAt(n);a.write_shift(1,s>=128?95:s)}return a.write_shift(1,0),a}function i(e,t,r){var a=Kr(7);return a.write_shift(1,n(r)),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(2,r.v,"i"),a}function o(e,t,r){var a=Kr(13);return a.write_shift(1,n(r)),a.write_shift(2,t),a.write_shift(2,e),a.write_shift(8,r.v,"f"),a}function c(e,t,r){var a=32768&t;return t=(a?e:0)+((t&=-32769)>=8192?t-16384:t),(a?"":"$")+(r?sa(t):aa(t))}var l={31:["NA",0],33:["ABS",1],34:["TRUNC",1],35:["SQRT",1],36:["LOG",1],37:["LN",1],38:["PI",0],39:["SIN",1],40:["COS",1],41:["TAN",1],42:["ATAN2",2],43:["ATAN",1],44:["ASIN",1],45:["ACOS",1],46:["EXP",1],47:["MOD",2],49:["ISNA",1],50:["ISERR",1],51:["FALSE",0],52:["TRUE",0],53:["RAND",0],54:["DATE",3],63:["ROUND",2],64:["TIME",3],68:["ISNUMBER",1],69:["ISTEXT",1],70:["LEN",1],71:["VALUE",1],73:["MID",3],74:["CHAR",1],80:["SUM",69],81:["AVERAGEA",69],82:["COUNTA",69],83:["MINA",69],84:["MAXA",69],102:["UPPER",1],103:["LOWER",1],107:["PROPER",1],109:["TRIM",1],111:["T",1]},h=["","","","","","","","","","+","-","*","/","^","=","<>","<=",">=","<",">","","","","","&","","","","","","",""];function u(e){var t=[{c:0,r:0},{t:"n",v:0},0];return t[0].r=e.read_shift(2),t[3]=e[e.l++],t[0].c=e[e.l++],t}function d(e,t,r,a){var n=Kr(6+a.length);n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),n.write_shift(1,39);for(var s=0;s<a.length;++s){var i=a.charCodeAt(s);n.write_shift(1,i>=128?95:i)}return n.write_shift(1,0),n}function p(e,t){var r=u(e),a=e.read_shift(4),n=e.read_shift(4),s=e.read_shift(2);if(65535==s)return 0===a&&3221225472===n?(r[1].t="e",r[1].v=15):0===a&&3489660928===n?(r[1].t="e",r[1].v=42):r[1].v=0,r;var i=32768&s;return s=(32767&s)-16446,r[1].v=(1-2*i)*(n*Math.pow(2,s+32)+a*Math.pow(2,s)),r}function m(e,t,r,a){var n=Kr(14);if(n.write_shift(2,e),n.write_shift(1,r),n.write_shift(1,t),0==a)return n.write_shift(4,0),n.write_shift(4,0),n.write_shift(2,65535),n;var s,i=0,o=0,c=0;return a<0&&(i=1,a=-a),o=0|Math.log2(a),0==(2147483648&(c=(a/=Math.pow(2,o-31))>>>0))&&(++o,c=(a/=2)>>>0),a-=c,c|=2147483648,c>>>=0,s=(a*=Math.pow(2,32))>>>0,n.write_shift(4,s),n.write_shift(4,c),o+=16383+(i?32768:0),n.write_shift(2,o),n}function v(e,t){var r=u(e),a=e.read_shift(8,"f");return r[1].v=a,r}function g(e,t){return 0==e[e.l+t-1]?e.read_shift(t,"cstr"):""}function b(e,t){var r=Kr(5+e.length);r.write_shift(2,14e3),r.write_shift(2,t);for(var a=0;a<e.length;++a){var n=e.charCodeAt(a);r[r.l++]=n>127?95:n}return r[r.l++]=0,r}var T={0:{n:"BOF",f:Kn},1:{n:"EOF"},2:{n:"CALCMODE"},3:{n:"CALCORDER"},4:{n:"SPLIT"},5:{n:"SYNC"},6:{n:"RANGE",f:function(e,t,r){var a={s:{c:0,r:0},e:{c:0,r:0}};return 8==t&&r.qpro?(a.s.c=e.read_shift(1),e.l++,a.s.r=e.read_shift(2),a.e.c=e.read_shift(1),e.l++,a.e.r=e.read_shift(2),a):(a.s.c=e.read_shift(2),a.s.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),a.e.c=e.read_shift(2),a.e.r=e.read_shift(2),12==t&&r.qpro&&(e.l+=2),65535==a.s.c&&(a.s.c=a.e.c=a.s.r=a.e.r=0),a)}},7:{n:"WINDOW1"},8:{n:"COLW1"},9:{n:"WINTWO"},10:{n:"COLW2"},11:{n:"NAME"},12:{n:"BLANK"},13:{n:"INTEGER",f:function(e,t,r){var n=a(e,0,r);return n[1].v=e.read_shift(2,"i"),n}},14:{n:"NUMBER",f:function(e,t,r){var n=a(e,0,r);return n[1].v=e.read_shift(8,"f"),n}},15:{n:"LABEL",f:function(e,t,r){var n=e.l+t,s=a(e,0,r);if(s[1].t="s",20768==(65534&r.vers)){e.l++;var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return r.qpro&&e.l++,s[1].v=e.read_shift(n-e.l,"cstr"),s}},16:{n:"FORMULA",f:function(e,t,r){var n=e.l+t,s=a(e,0,r);if(s[1].v=e.read_shift(8,"f"),r.qpro)e.l=n;else{var i=e.read_shift(2);!function(e,t){Xr(e,0);for(var r=[],a=0,n="",s="",i="",o="";e.l<e.length;){var f=e[e.l++];switch(f){case 0:r.push(e.read_shift(8,"f"));break;case 1:s=c(t[0].c,e.read_shift(2),!0),n=c(t[0].r,e.read_shift(2),!1),r.push(s+n);break;case 2:var u=c(t[0].c,e.read_shift(2),!0),d=c(t[0].r,e.read_shift(2),!1);s=c(t[0].c,e.read_shift(2),!0),n=c(t[0].r,e.read_shift(2),!1),r.push(u+d+":"+s+n);break;case 3:if(e.l<e.length)return void console.error("WK1 premature formula end");break;case 4:r.push("("+r.pop()+")");break;case 5:r.push(e.read_shift(2));break;case 6:for(var p="";f=e[e.l++];)p+=String.fromCharCode(f);r.push('"'+p.replace(/"/g,'""')+'"');break;case 8:r.push("-"+r.pop());break;case 23:r.push("+"+r.pop());break;case 22:r.push("NOT("+r.pop()+")");break;case 20:case 21:o=r.pop(),i=r.pop(),r.push(["AND","OR"][f-20]+"("+i+","+o+")");break;default:if(f<32&&h[f])o=r.pop(),i=r.pop(),r.push(i+h[f]+o);else{if(!l[f])return f<=7?console.error("WK1 invalid opcode "+f.toString(16)):f<=24?console.error("WK1 unsupported op "+f.toString(16)):f<=30?console.error("WK1 invalid opcode "+f.toString(16)):f<=115?console.error("WK1 unsupported function opcode "+f.toString(16)):console.error("WK1 unrecognized opcode "+f.toString(16));if(69==(a=l[f][1])&&(a=e[e.l++]),a>r.length)return void console.error("WK1 bad formula parse 0x"+f.toString(16)+":|"+r.join("|")+"|");var m=r.slice(-a);r.length-=a,r.push(l[f][0]+"("+m.join(",")+")")}}}1==r.length?t[1].f=""+r[0]:console.error("WK1 bad formula parse |"+r.join("|")+"|")}(e.slice(e.l,e.l+i),s),e.l+=i}return s}},24:{n:"TABLE"},25:{n:"ORANGE"},26:{n:"PRANGE"},27:{n:"SRANGE"},28:{n:"FRANGE"},29:{n:"KRANGE1"},32:{n:"HRANGE"},35:{n:"KRANGE2"},36:{n:"PROTEC"},37:{n:"FOOTER"},38:{n:"HEADER"},39:{n:"SETUP"},40:{n:"MARGINS"},41:{n:"LABELFMT"},42:{n:"TITLES"},43:{n:"SHEETJS"},45:{n:"GRAPH"},46:{n:"NGRAPH"},47:{n:"CALCCOUNT"},48:{n:"UNFORMATTED"},49:{n:"CURSORW12"},50:{n:"WINDOW"},51:{n:"STRING",f:function(e,t,r){var n=e.l+t,s=a(e,0,r);if(s[1].t="s",20768==r.vers){var i=e.read_shift(1);return s[1].v=e.read_shift(i,"utf8"),s}return s[1].v=e.read_shift(n-e.l,"cstr"),s}},55:{n:"PASSWORD"},56:{n:"LOCKED"},60:{n:"QUERY"},61:{n:"QUERYNAME"},62:{n:"PRINT"},63:{n:"PRINTNAME"},64:{n:"GRAPH2"},65:{n:"GRAPHNAME"},66:{n:"ZOOM"},67:{n:"SYMSPLIT"},68:{n:"NSROWS"},69:{n:"NSCOLS"},70:{n:"RULER"},71:{n:"NNAME"},72:{n:"ACOMM"},73:{n:"AMACRO"},74:{n:"PARSE"},102:{n:"PRANGES??"},103:{n:"RRANGES??"},104:{n:"FNAME??"},105:{n:"MRANGES??"},204:{n:"SHEETNAMECS",f:g},222:{n:"SHEETNAMELP",f:function(e,t){var r=e[e.l++];r>t-1&&(r=t-1);for(var a="";a.length<r;)a+=String.fromCharCode(e[e.l++]);return a}},255:{n:"BOF",f:Kn},21506:{n:"WKSNF",f:Kn},65535:{n:""}},y={0:{n:"BOF"},1:{n:"EOF"},2:{n:"PASSWORD"},3:{n:"CALCSET"},4:{n:"WINDOWSET"},5:{n:"SHEETCELLPTR"},6:{n:"SHEETLAYOUT"},7:{n:"COLUMNWIDTH"},8:{n:"HIDDENCOLUMN"},9:{n:"USERRANGE"},10:{n:"SYSTEMRANGE"},11:{n:"ZEROFORCE"},12:{n:"SORTKEYDIR"},13:{n:"FILESEAL"},14:{n:"DATAFILLNUMS"},15:{n:"PRINTMAIN"},16:{n:"PRINTSTRING"},17:{n:"GRAPHMAIN"},18:{n:"GRAPHSTRING"},19:{n:"??"},20:{n:"ERRCELL"},21:{n:"NACELL"},22:{n:"LABEL16",f:function(e,t){var r=u(e);return r[1].t="s",r[1].v=e.read_shift(t-4,"cstr"),r}},23:{n:"NUMBER17",f:p},24:{n:"NUMBER18",f:function(e,t){var r=u(e);r[1].v=e.read_shift(2);var a=r[1].v>>1;if(1&r[1].v)switch(7&a){case 0:a=5e3*(a>>3);break;case 1:a=500*(a>>3);break;case 2:a=(a>>3)/20;break;case 3:a=(a>>3)/200;break;case 4:a=(a>>3)/2e3;break;case 5:a=(a>>3)/2e4;break;case 6:a=(a>>3)/16;break;case 7:a=(a>>3)/64}return r[1].v=a,r}},25:{n:"FORMULA19",f:function(e,t){var r=p(e);return e.l+=t-14,r}},26:{n:"FORMULA1A"},27:{n:"XFORMAT",f:function(e,t){for(var r={},a=e.l+t;e.l<a;){var n=e.read_shift(2);if(14e3==n){for(r[n]=[0,""],r[n][0]=e.read_shift(2);e[e.l];)r[n][1]+=String.fromCharCode(e[e.l]),e.l++;e.l++}}return r}},28:{n:"DTLABELMISC"},29:{n:"DTLABELCELL"},30:{n:"GRAPHWINDOW"},31:{n:"CPA"},32:{n:"LPLAUTO"},33:{n:"QUERY"},34:{n:"HIDDENSHEET"},35:{n:"??"},37:{n:"NUMBER25",f:function(e,t){var r=u(e),a=e.read_shift(4);return r[1].v=a>>6,r}},38:{n:"??"},39:{n:"NUMBER27",f:v},40:{n:"FORMULA28",f:function(e,t){var r=v(e);return e.l+=t-12,r}},142:{n:"??"},147:{n:"??"},150:{n:"??"},151:{n:"??"},152:{n:"??"},153:{n:"??"},154:{n:"??"},155:{n:"??"},156:{n:"??"},163:{n:"??"},174:{n:"??"},175:{n:"??"},176:{n:"??"},177:{n:"??"},184:{n:"??"},185:{n:"??"},186:{n:"??"},187:{n:"??"},188:{n:"??"},195:{n:"??"},201:{n:"??"},204:{n:"SHEETNAMECS",f:g},205:{n:"??"},206:{n:"??"},207:{n:"??"},208:{n:"??"},256:{n:"??"},259:{n:"??"},260:{n:"??"},261:{n:"??"},262:{n:"??"},263:{n:"??"},265:{n:"??"},266:{n:"??"},267:{n:"??"},268:{n:"??"},270:{n:"??"},271:{n:"??"},384:{n:"??"},389:{n:"??"},390:{n:"??"},393:{n:"??"},396:{n:"??"},512:{n:"??"},514:{n:"??"},513:{n:"??"},516:{n:"??"},517:{n:"??"},640:{n:"??"},641:{n:"??"},642:{n:"??"},643:{n:"??"},644:{n:"??"},645:{n:"??"},646:{n:"??"},647:{n:"??"},648:{n:"??"},658:{n:"??"},659:{n:"??"},660:{n:"??"},661:{n:"??"},662:{n:"??"},665:{n:"??"},666:{n:"??"},768:{n:"??"},772:{n:"??"},1537:{n:"SHEETINFOQP",f:function(e,t,r){if(r.qpro&&!(t<21)){var a=e.read_shift(1);return e.l+=17,e.l+=1,e.l+=2,[a,e.read_shift(t-21,"cstr")]}}},1600:{n:"??"},1602:{n:"??"},1793:{n:"??"},1794:{n:"??"},1795:{n:"??"},1796:{n:"??"},1920:{n:"??"},2048:{n:"??"},2049:{n:"??"},2052:{n:"??"},2688:{n:"??"},10998:{n:"??"},12849:{n:"??"},28233:{n:"??"},28484:{n:"??"},65535:{n:""}},k={5:"dd-mmm-yy",6:"dd-mmm",7:"mmm-yy",8:"mm/dd/yy",10:"hh:mm:ss AM/PM",11:"hh:mm AM/PM",14:"dd-mmm-yyyy",15:"mmm-yyyy",34:"0.00",50:"0.00;[Red]0.00",66:"0.00;(0.00)",82:"0.00;[Red](0.00)",162:'"$"#,##0.00;\\("$"#,##0.00\\)',288:"0%",304:"0E+00",320:"# ?/?"};function _(e){var t=e.read_shift(2),r=e.read_shift(1);if(0!=r)throw"unsupported QPW string type "+r.toString(16);return e.read_shift(t,"sbcs-cont")}return{sheet_to_wk1:function(e,t){var r=t||{};if(+r.codepage>=0&&f(+r.codepage),"string"==r.type)throw new Error("Cannot write WK1 to JS string");var a=Zr();if(!e["!ref"])throw new Error("Cannot export empty sheet to WK1");var n,c=ua(e["!ref"]),l=null!=e["!data"],h=[];El(a,0,(1030,(n=Kr(2)).write_shift(2,1030),n)),El(a,6,function(e){var t=Kr(8);return t.write_shift(2,e.s.c),t.write_shift(2,e.s.r),t.write_shift(2,e.e.c),t.write_shift(2,e.e.r),t}(c));for(var u=Math.min(c.e.r,8191),d=c.s.c;d<=c.e.c;++d)h[d]=sa(d);for(var p=c.s.r;p<=u;++p){var m=aa(p);for(d=c.s.c;d<=c.e.c;++d){var v=l?(e["!data"][p]||[])[d]:e[h[d]+m];if(v&&"z"!=v.t)switch(v.t){case"n":(0|v.v)==v.v&&v.v>=-32768&&v.v<=32767?El(a,13,i(p,d,v)):El(a,14,o(p,d,v));break;case"d":var g=We(v.v);(0|g)==g&&g>=-32768&&g<=32767?El(a,13,i(p,d,{t:"n",v:g,z:v.z||G[14]})):El(a,14,o(p,d,{t:"n",v:g,z:v.z||G[14]}));break;default:El(a,15,s(p,d,da(v).slice(0,239)))}}}return El(a,1),a.end()},book_to_wk3:function(e,t){var r=t||{};if(+r.codepage>=0&&f(+r.codepage),"string"==r.type)throw new Error("Cannot write WK3 to JS string");var a=Zr();El(a,0,function(e){var t=Kr(26);t.write_shift(2,4096),t.write_shift(2,4),t.write_shift(4,0);for(var r=0,a=0,n=0,s=0;s<e.SheetNames.length;++s){var i=e.SheetNames[s],o=e.Sheets[i];if(o&&o["!ref"]){++n;var c=ca(o["!ref"]);r<c.e.r&&(r=c.e.r),a<c.e.c&&(a=c.e.c)}}return r>8191&&(r=8191),t.write_shift(2,r),t.write_shift(1,n),t.write_shift(1,a),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(1,1),t.write_shift(1,2),t.write_shift(4,0),t.write_shift(4,0),t}(e));for(var n=0,s=0;n<e.SheetNames.length;++n)(e.Sheets[e.SheetNames[n]]||{})["!ref"]&&El(a,27,b(e.SheetNames[n],s++));var i=0;for(n=0;n<e.SheetNames.length;++n){var o=e.Sheets[e.SheetNames[n]];if(o&&o["!ref"]){for(var c=ua(o["!ref"]),l=null!=o["!data"],h=[],u=Math.min(c.e.r,8191),p=c.s.r;p<=u;++p)for(var v=aa(p),g=c.s.c;g<=c.e.c;++g){p===c.s.r&&(h[g]=sa(g));var w=h[g]+v,T=l?(o["!data"][p]||[])[g]:o[w];T&&"z"!=T.t&&("n"==T.t?El(a,23,m(p,g,i,T.v)):El(a,22,d(p,g,i,da(T).slice(0,239))))}++i}}return El(a,1),a.end()},to_workbook:function(e,t){switch(t.type){case"base64":return r(C(E(e)),t);case"binary":return r(C(e),t);case"buffer":case"array":return r(e,t)}throw"Unsupported type "+t.type}}}(),ti=function(){function e(e){var t=ht(e,"t");if(!t)return{t:"s",v:""};var r={t:"s",v:Lt(t[1])},a=ht(e,"rPr");return a&&(r.s=function(e){var t={},r=e.match(Ot),a=0,n=!1;if(r)for(;a!=r.length;++a){var s=Ft(r[a]);switch(s[0].replace(/<\w*:/g,"<")){case"<condense":case"<extend":break;case"<shadow":if(!s.val)break;case"<shadow>":case"<shadow/>":t.shadow=1;break;case"</shadow>":break;case"<charset":if("1"==s.val)break;t.cp=c[parseInt(s.val,10)];break;case"<outline":if(!s.val)break;case"<outline>":case"<outline/>":t.outline=1;break;case"</outline>":break;case"<rFont":t.name=s.val;break;case"<sz":t.sz=s.val;break;case"<strike":if(!s.val)break;case"<strike>":case"<strike/>":t.strike=1;break;case"</strike>":break;case"<u":if(!s.val)break;switch(s.val){case"double":t.uval="double";break;case"singleAccounting":t.uval="single-accounting";break;case"doubleAccounting":t.uval="double-accounting"}case"<u>":case"<u/>":t.u=1;break;case"</u>":break;case"<b":if("0"==s.val)break;case"<b>":case"<b/>":t.b=1;break;case"</b>":break;case"<i":if("0"==s.val)break;case"<i>":case"<i/>":t.i=1;break;case"</i>":break;case"<color":s.rgb&&(t.color=s.rgb.slice(2,8));break;case"<color>":case"<color/>":case"</color>":break;case"<family":t.family=s.val;break;case"<family>":case"<family/>":case"</family>":break;case"<vertAlign":t.valign=s.val;break;case"<vertAlign>":case"<vertAlign/>":case"</vertAlign>":case"<scheme":case"<scheme>":case"<scheme/>":case"</scheme>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<ext":n=!0;break;case"</ext>":n=!1;break;default:if(47!==s[0].charCodeAt(1)&&!n)throw new Error("Unrecognized rich format "+s[0])}}return t}(a[1])),r}var t=/<(?:\w+:)?r>/g,r=/<\/(?:\w+:)?r>/;return function(a){return a.replace(t,"").split(r).map(e).filter((function(e){return e.v}))}}(),ri=function(){var e=/(\r\n|\n)/g;function t(t){var r=[[],t.v,[]];return t.v?(t.s&&function(e,t,r){var a=[];e.u&&a.push("text-decoration: underline;"),e.uval&&a.push("text-underline-style:"+e.uval+";"),e.sz&&a.push("font-size:"+e.sz+"pt;"),e.outline&&a.push("text-effect: outline;"),e.shadow&&a.push("text-shadow: auto;"),t.push('<span style="'+a.join("")+'">'),e.b&&(t.push("<b>"),r.push("</b>")),e.i&&(t.push("<i>"),r.push("</i>")),e.strike&&(t.push("<s>"),r.push("</s>"));var n=e.valign||"";"superscript"==n||"super"==n?n="sup":"subscript"==n&&(n="sub"),""!=n&&(t.push("<"+n+">"),r.push("</"+n+">")),r.push("</span>")}(t.s,r[0],r[2]),r[0].join("")+r[1].replace(e,"<br/>")+r[2].join("")):""}return function(e){return e.map(t).join("")}}(),ai=/<(?:\w+:)?t\b[^<>]*>([^<]*)<\/(?:\w+:)?t>/g,ni=/<(?:\w+:)?r\b[^<>]*>/;function si(e,t){var r=!t||t.cellHTML,a={};return e?(e.match(/^\s*<(?:\w+:)?t[^>]*>/)?(a.t=Lt(Zt(e.slice(e.indexOf(">")+1).split(/<\/(?:\w+:)?t>/)[0]||""),!0),a.r=Zt(e),r&&(a.h=Vt(a.t))):e.match(ni)&&(a.r=Zt(e),a.t=Lt(Zt((dt(e,"rPh").match(ai)||[]).join("").replace(Ot,"")),!0),r&&(a.h=ri(ti(a.r)))),a):{t:""}}var ii=/<(?:\w+:)?(?:si|sstItem)>/g,oi=/<\/(?:\w+:)?(?:si|sstItem)>/,ci=/^\s|\s$|[\t\n\r]/,li=function(e,t){var r=!1;return null==t&&(r=!0,t=Kr(15+4*e.t.length)),t.write_shift(1,0),wa(e.t,t),r?t.slice(0,t.l):t};function fi(e){if(void 0!==a)return a.utils.encode(i,e);for(var t=[],r=e.split(""),n=0;n<r.length;++n)t[n]=r[n].charCodeAt(0);return t}function hi(e,t){var r={};return r.Major=e.read_shift(2),r.Minor=e.read_shift(2),t>=4&&(e.l+=t-4),r}function ui(e){for(var t=e.read_shift(4),r=e.l+t-4,a={},n=e.read_shift(4),s=[];n-- >0;)s.push({t:e.read_shift(4),v:e.read_shift(0,"lpp4")});if(a.name=e.read_shift(0,"lpp4"),a.comps=s,e.l!=r)throw new Error("Bad DataSpaceMapEntry: "+e.l+" != "+r);return a}function di(e,t){var r=e.l+t,a={};a.Flags=63&e.read_shift(4),e.l+=4,a.AlgID=e.read_shift(4);var n=!1;switch(a.AlgID){case 26126:case 26127:case 26128:n=36==a.Flags;break;case 26625:n=4==a.Flags;break;case 0:n=16==a.Flags||4==a.Flags||36==a.Flags;break;default:throw"Unrecognized encryption algorithm: "+a.AlgID}if(!n)throw new Error("Encryption Flags/AlgID mismatch");return a.AlgIDHash=e.read_shift(4),a.KeySize=e.read_shift(4),a.ProviderType=e.read_shift(4),e.l+=8,a.CSPName=e.read_shift(r-e.l>>1,"utf16le"),e.l=r,a}function pi(e,t){var r={},a=e.l+t;return e.l+=4,r.Salt=e.slice(e.l,e.l+16),e.l+=16,r.Verifier=e.slice(e.l,e.l+16),e.l+=16,e.read_shift(4),r.VerifierHash=e.slice(e.l,a),e.l=a,r}function mi(e){if(36!=(63&e.read_shift(4)))throw new Error("EncryptionInfo mismatch");var t=e.read_shift(4);return{t:"Std",h:di(e,t),v:pi(e,e.length-e.l)}}function vi(){throw new Error("File is password-protected: ECMA-376 Extensible")}function gi(e){var t=["saltSize","blockSize","keyBits","hashSize","cipherAlgorithm","cipherChaining","hashAlgorithm","saltValue"];e.l+=4;var r=e.read_shift(e.length-e.l,"utf8"),a={};return r.replace(Ot,(function(e){var r=Ft(e);switch(Dt(r[0])){case"<?xml":case"<encryption":case"</encryption>":case"</keyEncryptors>":case"</keyEncryptor>":break;case"<keyData":t.forEach((function(e){a[e]=r[e]}));break;case"<dataIntegrity":a.encryptedHmacKey=r.encryptedHmacKey,a.encryptedHmacValue=r.encryptedHmacValue;break;case"<keyEncryptors>":case"<keyEncryptors":a.encs=[];break;case"<keyEncryptor":a.uri=r.uri;break;case"<encryptedKey":a.encs.push(r);break;default:throw r[0]}})),a}function bi(e){var t,r,a=0,n=fi(e),s=n.length+1;for((t=x(s))[0]=n.length,r=1;r!=s;++r)t[r]=n[r-1];for(r=s-1;r>=0;--r)a=((0==(16384&a)?0:1)|a<<1&32767)^t[r];return 52811^a}var wi=function(){var e=[187,255,255,186,255,255,185,128,0,190,15,0,191,15,0],t=[57840,7439,52380,33984,4364,3600,61902,12606,6258,57657,54287,34041,10252,43370,20163],r=[44796,19929,39858,10053,20106,40212,10761,31585,63170,64933,60267,50935,40399,11199,17763,35526,1453,2906,5812,11624,23248,885,1770,3540,7080,14160,28320,56640,55369,41139,20807,41614,21821,43642,17621,28485,56970,44341,19019,38038,14605,29210,60195,50791,40175,10751,21502,43004,24537,18387,36774,3949,7898,15796,31592,63184,47201,24803,49606,37805,14203,28406,56812,17824,35648,1697,3394,6788,13576,27152,43601,17539,35078,557,1114,2228,4456,30388,60776,51953,34243,7079,14158,28316,14128,28256,56512,43425,17251,34502,7597,13105,26210,52420,35241,883,1766,3532,4129,8258,16516,33032,4657,9314,18628],a=function(e,t){return 255&((r=e^t)/2|128*r);var r};return function(n){for(var s,i,o,c=fi(n),l=function(e){for(var a=t[e.length-1],n=104,s=e.length-1;s>=0;--s)for(var i=e[s],o=0;7!=o;++o)64&i&&(a^=r[n]),i*=2,--n;return a}(c),f=c.length,h=x(16),u=0;16!=u;++u)h[u]=0;for(1==(1&f)&&(s=l>>8,h[f]=a(e[0],s),--f,s=255&l,i=c[c.length-1],h[f]=a(i,s));f>0;)s=l>>8,h[--f]=a(c[f],s),s=255&l,h[--f]=a(c[f],s);for(f=15,o=15-c.length;o>0;)s=l>>8,h[f]=a(e[o],s),--o,s=255&l,h[--f]=a(c[f],s),--f,--o;return h}}();function Ti(e,t){var r=t||{},a={},n=r.dense;n&&(a["!data"]=[]);var s=ot(e,"\\trowd","\\row");if(!s)throw new Error("RTF missing table");var i={s:{c:0,r:0},e:{c:0,r:s.length-1}},o=[];return s.forEach((function(e,t){n&&(o=a["!data"][t]=[]);for(var s,c=/\\[\w\-]+\b/g,l=0,f=-1,h=[];null!=(s=c.exec(e));){var u=e.slice(l,c.lastIndex-s[0].length);switch(32==u.charCodeAt(0)&&(u=u.slice(1)),u.length&&h.push(u),s[0]){case"\\cell":if(++f,h.length){var d={v:h.join(""),t:"s"};"TRUE"==d.v||"FALSE"==d.v?(d.v="TRUE"==d.v,d.t="b"):isNaN(Ze(d.v))||(d.t="n",!1!==r.cellText&&(d.w=d.v),d.v=Ze(d.v)),n?o[f]=d:a[oa({r:t,c:f})]=d}h=[];break;case"\\par":h.push("\n")}l=c.lastIndex}f>i.e.c&&(i.e.c=f)})),a["!ref"]=la(i),a}function yi(e){for(var t=0,r=1;3!=t;++t)r=256*r+(e[t]>255?255:e[t]<0?0:e[t]);return r.toString(16).toUpperCase().slice(1)}function Ei(e,t){if(0===t)return e;var r,a,n=function(e){var t=e[0]/255,r=e[1]/255,a=e[2]/255,n=Math.max(t,r,a),s=Math.min(t,r,a),i=n-s;if(0===i)return[0,0,t];var o,c=0,l=n+s;switch(o=i/(l>1?2-l:l),n){case t:c=((r-a)/i+6)%6;break;case r:c=(a-t)/i+2;break;case a:c=(t-r)/i+4}return[c/6,o,l/2]}((a=(r=e).slice("#"===r[0]?1:0).slice(0,6),[parseInt(a.slice(0,2),16),parseInt(a.slice(2,4),16),parseInt(a.slice(4,6),16)]));return n[2]=t<0?n[2]*(1+t):1-(1-n[2])*(1-t),yi(function(e){var t,r=e[0],a=e[1],n=e[2],s=2*a*(n<.5?n:1-n),i=n-s/2,o=[i,i,i],c=6*r;if(0!==a)switch(0|c){case 0:case 6:t=s*c,o[0]+=s,o[1]+=t;break;case 1:t=s*(2-c),o[0]+=t,o[1]+=s;break;case 2:t=s*(c-2),o[1]+=s,o[2]+=t;break;case 3:t=s*(4-c),o[1]+=t,o[2]+=s;break;case 4:t=s*(c-4),o[2]+=s,o[0]+=t;break;case 5:t=s*(6-c),o[2]+=t,o[0]+=s}for(var l=0;3!=l;++l)o[l]=Math.round(255*o[l]);return o}(n))}var ki=6,_i=15,Si=1,xi=ki;function Ai(e){return Math.floor((e+Math.round(128/xi)/256)*xi)}function Ci(e){return Math.floor((e-5)/xi*100+.5)/100}function Ii(e){return Math.round((e*xi+5)/xi*256)/256}function Oi(e){return Ii(Ci(Ai(e)))}function Ri(e){var t=Math.abs(e-Oi(e)),r=xi;if(t>.005)for(xi=Si;xi<_i;++xi)Math.abs(e-Oi(e))<=t&&(t=Math.abs(e-Oi(e)),r=xi);xi=r}function Ni(e){e.width?(e.wpx=Ai(e.width),e.wch=Ci(e.wpx),e.MDW=xi):e.wpx?(e.wch=Ci(e.wpx),e.width=Ii(e.wch),e.MDW=xi):"number"==typeof e.wch&&(e.width=Ii(e.wch),e.wpx=Ai(e.width),e.MDW=xi),e.customWidth&&delete e.customWidth}var Fi=96;function Di(e){return 96*e/Fi}function Pi(e){return e*Fi/96}var Mi={None:"none",Solid:"solid",Gray50:"mediumGray",Gray75:"darkGray",Gray25:"lightGray",HorzStripe:"darkHorizontal",VertStripe:"darkVertical",ReverseDiagStripe:"darkDown",DiagStripe:"darkUp",DiagCross:"darkGrid",ThickDiagCross:"darkTrellis",ThinHorzStripe:"lightHorizontal",ThinVertStripe:"lightVertical",ThinReverseDiagStripe:"lightDown",ThinHorzCross:"lightGrid"},Li=["numFmtId","fillId","fontId","borderId","xfId"],Ui=["applyAlignment","applyBorder","applyFill","applyFont","applyNumberFormat","applyProtection","pivotButton","quotePrefix"],Bi=function(){return function(e,t,r){var a,n={};return e?(e=it(ct(e,"\x3c!--","--\x3e")),(a=ht(e,"numFmts"))&&function(e,t,r){t.NumberFmt=[];for(var a=Fe(G),n=0;n<a.length;++n)t.NumberFmt[a[n]]=G[a[n]];var s=e.match(Ot);if(s)for(n=0;n<s.length;++n){var i=Ft(s[n]);switch(Dt(i[0])){case"<numFmts":case"</numFmts>":case"<numFmts/>":case"<numFmts>":case"</numFmt>":break;case"<numFmt":var o=Lt(Zt(i.formatCode)),c=parseInt(i.numFmtId,10);if(t.NumberFmt[c]=o,c>0){if(c>392){for(c=392;c>60&&null!=t.NumberFmt[c];--c);t.NumberFmt[c]=o}xe(o,c)}break;default:if(r.WTF)throw new Error("unrecognized "+i[0]+" in numFmts")}}}(a[0],n,r),(a=ht(e,"fonts"))&&function(e,t,r,a){t.Fonts=[];var n={},s=!1;(e.match(Ot)||[]).forEach((function(e){var i=Ft(e);switch(Dt(i[0])){case"<fonts":case"<fonts>":case"</fonts>":case"<font":case"<font>":case"<name/>":case"</name>":case"<sz/>":case"</sz>":case"<vertAlign/>":case"</vertAlign>":case"<family/>":case"</family>":case"<scheme/>":case"</scheme>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"</font>":case"<font/>":t.Fonts.push(n),n={};break;case"<name":i.val&&(n.name=Zt(i.val));break;case"<b":n.bold=i.val?Gt(i.val):1;break;case"<b/>":n.bold=1;break;case"<i":n.italic=i.val?Gt(i.val):1;break;case"<i/>":n.italic=1;break;case"<u":switch(i.val){case"none":n.underline=0;break;case"single":n.underline=1;break;case"double":n.underline=2;break;case"singleAccounting":n.underline=33;break;case"doubleAccounting":n.underline=34}break;case"<u/>":n.underline=1;break;case"<strike":n.strike=i.val?Gt(i.val):1;break;case"<strike/>":n.strike=1;break;case"<outline":n.outline=i.val?Gt(i.val):1;break;case"<outline/>":n.outline=1;break;case"<shadow":n.shadow=i.val?Gt(i.val):1;break;case"<shadow/>":n.shadow=1;break;case"<condense":n.condense=i.val?Gt(i.val):1;break;case"<condense/>":n.condense=1;break;case"<extend":n.extend=i.val?Gt(i.val):1;break;case"<extend/>":n.extend=1;break;case"<sz":i.val&&(n.sz=+i.val);break;case"<vertAlign":i.val&&(n.vertAlign=i.val);break;case"<family":i.val&&(n.family=parseInt(i.val,10));break;case"<scheme":i.val&&(n.scheme=i.val);break;case"<charset":if("1"==i.val)break;i.codepage=c[parseInt(i.val,10)];break;case"<color":if(n.color||(n.color={}),i.auto&&(n.color.auto=Gt(i.auto)),i.rgb)n.color.rgb=i.rgb.slice(-6);else if(i.indexed){n.color.index=parseInt(i.indexed,10);var o=en[n.color.index];81==n.color.index&&(o=en[1]),o||(o=en[1]),n.color.rgb=o[0].toString(16)+o[1].toString(16)+o[2].toString(16)}else i.theme&&(n.color.theme=parseInt(i.theme,10),i.tint&&(n.color.tint=parseFloat(i.tint)),i.theme&&r.themeElements&&r.themeElements.clrScheme&&(n.color.rgb=Ei(r.themeElements.clrScheme[n.color.theme].rgb,n.color.tint||0)));break;case"<AlternateContent":case"<ext":s=!0;break;case"</AlternateContent>":case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+i[0]+" in fonts")}}))}(a[0],n,t,r),(a=ht(e,"fills"))&&function(e,t,r,a){t.Fills=[];var n={},s=!1;(e.match(Ot)||[]).forEach((function(e){var r=Ft(e);switch(Dt(r[0])){case"<fills":case"<fills>":case"</fills>":case"</fill>":case"<gradientFill>":case"<patternFill/>":case"</patternFill>":case"<bgColor/>":case"</bgColor>":case"<fgColor/>":case"</fgColor>":case"<stop":case"<stop/>":case"</stop>":case"<color":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<fill>":case"<fill":case"<fill/>":n={},t.Fills.push(n);break;case"<gradientFill":case"</gradientFill>":t.Fills.push(n),n={};break;case"<patternFill":case"<patternFill>":r.patternType&&(n.patternType=r.patternType);break;case"<bgColor":n.bgColor||(n.bgColor={}),r.indexed&&(n.bgColor.indexed=parseInt(r.indexed,10)),r.theme&&(n.bgColor.theme=parseInt(r.theme,10)),r.tint&&(n.bgColor.tint=parseFloat(r.tint)),r.rgb&&(n.bgColor.rgb=r.rgb.slice(-6));break;case"<fgColor":n.fgColor||(n.fgColor={}),r.theme&&(n.fgColor.theme=parseInt(r.theme,10)),r.tint&&(n.fgColor.tint=parseFloat(r.tint)),null!=r.rgb&&(n.fgColor.rgb=r.rgb.slice(-6));break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in fills")}}))}(a[0],n,0,r),(a=ht(e,"borders"))&&function(e,t,r,a){t.Borders=[];var n={},s=!1;(e.match(Ot)||[]).forEach((function(e){var r=Ft(e);switch(Dt(r[0])){case"<borders":case"<borders>":case"</borders>":case"</border>":case"<left/>":case"<left":case"<left>":case"</left>":case"<right/>":case"<right":case"<right>":case"</right>":case"<top/>":case"<top":case"<top>":case"</top>":case"<bottom/>":case"<bottom":case"<bottom>":case"</bottom>":case"<diagonal":case"<diagonal>":case"<diagonal/>":case"</diagonal>":case"<horizontal":case"<horizontal>":case"<horizontal/>":case"</horizontal>":case"<vertical":case"<vertical>":case"<vertical/>":case"</vertical>":case"<start":case"<start>":case"<start/>":case"</start>":case"<end":case"<end>":case"<end/>":case"</end>":case"<color":case"<color>":case"<color/>":case"</color>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<border":case"<border>":case"<border/>":n={},r.diagonalUp&&(n.diagonalUp=Gt(r.diagonalUp)),r.diagonalDown&&(n.diagonalDown=Gt(r.diagonalDown)),t.Borders.push(n);break;case"<ext":s=!0;break;case"</ext>":s=!1;break;default:if(a&&a.WTF&&!s)throw new Error("unrecognized "+r[0]+" in borders")}}))}(a[0],n,0,r),(a=ht(e,"cellXfs"))&&function(e,t,r){var a;t.CellXf=[];var n=!1;(e.match(Ot)||[]).forEach((function(e){var s=Ft(e),i=0;switch(Dt(s[0])){case"<cellXfs":case"<cellXfs>":case"<cellXfs/>":case"</cellXfs>":case"</xf>":case"</alignment>":case"<protection":case"<protection>":case"</protection>":case"<protection/>":case"<extLst":case"<extLst>":case"</extLst>":break;case"<xf":case"<xf/>":case"<xf>":for(delete(a=s)[0],i=0;i<Li.length;++i)a[Li[i]]&&(a[Li[i]]=parseInt(a[Li[i]],10));for(i=0;i<Ui.length;++i)a[Ui[i]]&&(a[Ui[i]]=Gt(a[Ui[i]]));if(t.NumberFmt&&a.numFmtId>392)for(i=392;i>60;--i)if(t.NumberFmt[a.numFmtId]==t.NumberFmt[i]){a.numFmtId=i;break}t.CellXf.push(a);break;case"<alignment":case"<alignment/>":case"<alignment>":var o={};s.vertical&&(o.vertical=s.vertical),s.horizontal&&(o.horizontal=s.horizontal),null!=s.textRotation&&(o.textRotation=s.textRotation),s.indent&&(o.indent=s.indent),s.wrapText&&(o.wrapText=Gt(s.wrapText)),a.alignment=o;break;case"<AlternateContent":case"<AlternateContent>":case"<ext":n=!0;break;case"</AlternateContent>":case"</ext>":n=!1;break;default:if(r&&r.WTF&&!n)throw new Error("unrecognized "+s[0]+" in cellXfs")}}))}(a[0],n,r),n):n}}();function Wi(e,t,r){r||(r=Kr(6+4*t.length)),r.write_shift(2,e),wa(t,r);var a=r.length>r.l?r.slice(0,r.l):r;return null==r.l&&(r.l=r.length),a}var zi,Hi=["none","solid","mediumGray","darkGray","lightGray","darkHorizontal","darkVertical","darkDown","darkUp","darkGrid","darkTrellis","lightHorizontal","lightVertical","lightDown","lightUp","lightGrid","lightTrellis","gray125","gray0625"],Vi=jr;function $i(e,t){t||(t=Kr(84)),zi||(zi=Pe(Hi));var r=zi[e.patternType];null==r&&(r=40),t.write_shift(4,r);var a=0;if(40!=r)for(za({auto:1},t),za({auto:1},t);a<12;++a)t.write_shift(4,0);else{for(;a<4;++a)t.write_shift(4,0);for(;a<12;++a)t.write_shift(4,0)}return t.length>t.l?t.slice(0,t.l):t}function Gi(e,t,r){return r||(r=Kr(16)),r.write_shift(2,t||0),r.write_shift(2,e.numFmtId||0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(1,0),r}function Xi(e,t){return t||(t=Kr(10)),t.write_shift(1,0),t.write_shift(1,0),t.write_shift(4,0),t.write_shift(4,0),t}var ji=jr;function Ki(e,t){var r=Zr();return Jr(r,278),function(e,t){if(t){var r=0;[[5,8],[23,26],[41,44],[50,392]].forEach((function(e){for(var a=e[0];a<=e[1];++a)null!=t[a]&&++r})),0!=r&&(Jr(e,615,ga(r)),[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=t[a]&&Jr(e,44,Wi(a,t[a]))})),Jr(e,616))}}(r,e.SSF),function(e){Jr(e,611,ga(1)),Jr(e,43,function(e,t){t||(t=Kr(153)),t.write_shift(2,20*e.sz),function(e,t){t||(t=Kr(2));var r=(e.italic?2:0)|(e.strike?8:0)|(e.outline?16:0)|(e.shadow?32:0)|(e.condense?64:0)|(e.extend?128:0);t.write_shift(1,r),t.write_shift(1,0)}(e,t),t.write_shift(2,e.bold?700:400);var r=0;"superscript"==e.vertAlign?r=1:"subscript"==e.vertAlign&&(r=2),t.write_shift(2,r),t.write_shift(1,e.underline||0),t.write_shift(1,e.family||0),t.write_shift(1,e.charset||0),t.write_shift(1,0),za(e.color,t);var a=0;return"major"==e.scheme&&(a=1),"minor"==e.scheme&&(a=2),t.write_shift(1,a),wa(e.name,t),t.length>t.l?t.slice(0,t.l):t}({sz:12,color:{theme:1},name:"Calibri",family:2,scheme:"minor"})),Jr(e,612)}(r),function(e){Jr(e,603,ga(2)),Jr(e,45,$i({patternType:"none"})),Jr(e,45,$i({patternType:"gray125"})),Jr(e,604)}(r),function(e){var t;Jr(e,613,ga(1)),Jr(e,46,(t||(t=Kr(51)),t.write_shift(1,0),Xi(0,t),Xi(0,t),Xi(0,t),Xi(0,t),Xi(0,t),t.length>t.l?t.slice(0,t.l):t)),Jr(e,614)}(r),function(e){Jr(e,626,ga(1)),Jr(e,47,Gi({numFmtId:0,fontId:0,fillId:0,borderId:0},65535)),Jr(e,627)}(r),function(e,t){Jr(e,617,ga(t.length)),t.forEach((function(t){Jr(e,47,Gi(t,0))})),Jr(e,618)}(r,t.cellXfs),function(e){var t,r;Jr(e,619,ga(1)),Jr(e,48,(t={xfId:0,builtinId:0,name:"Normal"},r||(r=Kr(52)),r.write_shift(4,t.xfId),r.write_shift(2,1),r.write_shift(1,+t.builtinId),r.write_shift(1,0),Oa(t.name||"",r),r.length>r.l?r.slice(0,r.l):r)),Jr(e,620)}(r),function(e){Jr(e,505,ga(0)),Jr(e,506)}(r),function(e){var t;Jr(e,508,((t=Kr(2052)).write_shift(4,0),Oa("TableStyleMedium9",t),Oa("PivotStyleMedium4",t),t.length>t.l?t.slice(0,t.l):t)),Jr(e,509)}(r),Jr(r,279),r.end()}var Yi=["</a:lt1>","</a:dk1>","</a:lt2>","</a:dk2>","</a:accent1>","</a:accent2>","</a:accent3>","</a:accent4>","</a:accent5>","</a:accent6>","</a:hlink>","</a:folHlink>"];function Zi(e,t){var r;e&&0!==e.length||(e=Ji());var a={};if(!(r=ft(e,"a:themeElements")))throw new Error("themeElements not found in theme");return function(e,t,r){var a;if(t.themeElements={},!(a=ft(e,"a:clrScheme")))throw new Error("clrScheme not found in themeElements");if(function(e,t,r){t.themeElements.clrScheme=[];var a={};(e[0].match(Ot)||[]).forEach((function(e){var n=Ft(e);switch(n[0]){case"<a:clrScheme":case"</a:clrScheme>":case"</a:srgbClr>":case"</a:sysClr>":break;case"<a:srgbClr":a.rgb=n.val;break;case"<a:sysClr":a.rgb=n.lastClr;break;case"</a:dk1>":case"</a:lt1>":case"<a:dk1>":case"<a:lt1>":case"<a:dk2>":case"</a:dk2>":case"<a:lt2>":case"</a:lt2>":case"<a:accent1>":case"</a:accent1>":case"<a:accent2>":case"</a:accent2>":case"<a:accent3>":case"</a:accent3>":case"<a:accent4>":case"</a:accent4>":case"<a:accent5>":case"</a:accent5>":case"<a:accent6>":case"</a:accent6>":case"<a:hlink>":case"</a:hlink>":case"<a:folHlink>":case"</a:folHlink>":"/"===n[0].charAt(1)?(t.themeElements.clrScheme[Yi.indexOf(n[0])]=a,a={}):a.name=n[0].slice(3,n[0].length-1);break;default:if(r&&r.WTF)throw new Error("Unrecognized "+n[0]+" in clrScheme")}}))}(a,t,r),!(a=ft(e,"a:fontScheme")))throw new Error("fontScheme not found in themeElements");if(!(a=ft(e,"a:fmtScheme")))throw new Error("fmtScheme not found in themeElements")}(r[0],a,t),a.raw=e,a}function Ji(e,t){if(t&&t.themeXLSX)return t.themeXLSX;if(e&&"string"==typeof e.raw)return e.raw;var r=[At];return r[r.length]='<a:theme xmlns:a="http://schemas.openxmlformats.org/drawingml/2006/main" name="Office Theme">',r[r.length]="<a:themeElements>",r[r.length]='<a:clrScheme name="Office">',r[r.length]='<a:dk1><a:sysClr val="windowText" lastClr="000000"/></a:dk1>',r[r.length]='<a:lt1><a:sysClr val="window" lastClr="FFFFFF"/></a:lt1>',r[r.length]='<a:dk2><a:srgbClr val="1F497D"/></a:dk2>',r[r.length]='<a:lt2><a:srgbClr val="EEECE1"/></a:lt2>',r[r.length]='<a:accent1><a:srgbClr val="4F81BD"/></a:accent1>',r[r.length]='<a:accent2><a:srgbClr val="C0504D"/></a:accent2>',r[r.length]='<a:accent3><a:srgbClr val="9BBB59"/></a:accent3>',r[r.length]='<a:accent4><a:srgbClr val="8064A2"/></a:accent4>',r[r.length]='<a:accent5><a:srgbClr val="4BACC6"/></a:accent5>',r[r.length]='<a:accent6><a:srgbClr val="F79646"/></a:accent6>',r[r.length]='<a:hlink><a:srgbClr val="0000FF"/></a:hlink>',r[r.length]='<a:folHlink><a:srgbClr val="800080"/></a:folHlink>',r[r.length]="</a:clrScheme>",r[r.length]='<a:fontScheme name="Office">',r[r.length]="<a:majorFont>",r[r.length]='<a:latin typeface="Cambria"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Times New Roman"/>',r[r.length]='<a:font script="Hebr" typeface="Times New Roman"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="MoolBoran"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Times New Roman"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:majorFont>",r[r.length]="<a:minorFont>",r[r.length]='<a:latin typeface="Calibri"/>',r[r.length]='<a:ea typeface=""/>',r[r.length]='<a:cs typeface=""/>',r[r.length]='<a:font script="Jpan" typeface="ＭＳ Ｐゴシック"/>',r[r.length]='<a:font script="Hang" typeface="맑은 고딕"/>',r[r.length]='<a:font script="Hans" typeface="宋体"/>',r[r.length]='<a:font script="Hant" typeface="新細明體"/>',r[r.length]='<a:font script="Arab" typeface="Arial"/>',r[r.length]='<a:font script="Hebr" typeface="Arial"/>',r[r.length]='<a:font script="Thai" typeface="Tahoma"/>',r[r.length]='<a:font script="Ethi" typeface="Nyala"/>',r[r.length]='<a:font script="Beng" typeface="Vrinda"/>',r[r.length]='<a:font script="Gujr" typeface="Shruti"/>',r[r.length]='<a:font script="Khmr" typeface="DaunPenh"/>',r[r.length]='<a:font script="Knda" typeface="Tunga"/>',r[r.length]='<a:font script="Guru" typeface="Raavi"/>',r[r.length]='<a:font script="Cans" typeface="Euphemia"/>',r[r.length]='<a:font script="Cher" typeface="Plantagenet Cherokee"/>',r[r.length]='<a:font script="Yiii" typeface="Microsoft Yi Baiti"/>',r[r.length]='<a:font script="Tibt" typeface="Microsoft Himalaya"/>',r[r.length]='<a:font script="Thaa" typeface="MV Boli"/>',r[r.length]='<a:font script="Deva" typeface="Mangal"/>',r[r.length]='<a:font script="Telu" typeface="Gautami"/>',r[r.length]='<a:font script="Taml" typeface="Latha"/>',r[r.length]='<a:font script="Syrc" typeface="Estrangelo Edessa"/>',r[r.length]='<a:font script="Orya" typeface="Kalinga"/>',r[r.length]='<a:font script="Mlym" typeface="Kartika"/>',r[r.length]='<a:font script="Laoo" typeface="DokChampa"/>',r[r.length]='<a:font script="Sinh" typeface="Iskoola Pota"/>',r[r.length]='<a:font script="Mong" typeface="Mongolian Baiti"/>',r[r.length]='<a:font script="Viet" typeface="Arial"/>',r[r.length]='<a:font script="Uigh" typeface="Microsoft Uighur"/>',r[r.length]='<a:font script="Geor" typeface="Sylfaen"/>',r[r.length]="</a:minorFont>",r[r.length]="</a:fontScheme>",r[r.length]='<a:fmtScheme name="Office">',r[r.length]="<a:fillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="50000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="35000"><a:schemeClr val="phClr"><a:tint val="37000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="15000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="1"/>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="100000"/><a:shade val="100000"/><a:satMod val="130000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:tint val="50000"/><a:shade val="100000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:lin ang="16200000" scaled="0"/>',r[r.length]="</a:gradFill>",r[r.length]="</a:fillStyleLst>",r[r.length]="<a:lnStyleLst>",r[r.length]='<a:ln w="9525" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"><a:shade val="95000"/><a:satMod val="105000"/></a:schemeClr></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="25400" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]='<a:ln w="38100" cap="flat" cmpd="sng" algn="ctr"><a:solidFill><a:schemeClr val="phClr"/></a:solidFill><a:prstDash val="solid"/></a:ln>',r[r.length]="</a:lnStyleLst>",r[r.length]="<a:effectStyleLst>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="20000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="38000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]="</a:effectStyle>",r[r.length]="<a:effectStyle>",r[r.length]="<a:effectLst>",r[r.length]='<a:outerShdw blurRad="40000" dist="23000" dir="5400000" rotWithShape="0"><a:srgbClr val="000000"><a:alpha val="35000"/></a:srgbClr></a:outerShdw>',r[r.length]="</a:effectLst>",r[r.length]='<a:scene3d><a:camera prst="orthographicFront"><a:rot lat="0" lon="0" rev="0"/></a:camera><a:lightRig rig="threePt" dir="t"><a:rot lat="0" lon="0" rev="1200000"/></a:lightRig></a:scene3d>',r[r.length]='<a:sp3d><a:bevelT w="63500" h="25400"/></a:sp3d>',r[r.length]="</a:effectStyle>",r[r.length]="</a:effectStyleLst>",r[r.length]="<a:bgFillStyleLst>",r[r.length]='<a:solidFill><a:schemeClr val="phClr"/></a:solidFill>',r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="40000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="40000"><a:schemeClr val="phClr"><a:tint val="45000"/><a:shade val="99000"/><a:satMod val="350000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="20000"/><a:satMod val="255000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="-80000" r="50000" b="180000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]='<a:gradFill rotWithShape="1">',r[r.length]="<a:gsLst>",r[r.length]='<a:gs pos="0"><a:schemeClr val="phClr"><a:tint val="80000"/><a:satMod val="300000"/></a:schemeClr></a:gs>',r[r.length]='<a:gs pos="100000"><a:schemeClr val="phClr"><a:shade val="30000"/><a:satMod val="200000"/></a:schemeClr></a:gs>',r[r.length]="</a:gsLst>",r[r.length]='<a:path path="circle"><a:fillToRect l="50000" t="50000" r="50000" b="50000"/></a:path>',r[r.length]="</a:gradFill>",r[r.length]="</a:bgFillStyleLst>",r[r.length]="</a:fmtScheme>",r[r.length]="</a:themeElements>",r[r.length]="<a:objectDefaults>",r[r.length]="<a:spDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="1"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="3"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="2"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="lt1"/></a:fontRef></a:style>',r[r.length]="</a:spDef>",r[r.length]="<a:lnDef>",r[r.length]='<a:spPr/><a:bodyPr/><a:lstStyle/><a:style><a:lnRef idx="2"><a:schemeClr val="accent1"/></a:lnRef><a:fillRef idx="0"><a:schemeClr val="accent1"/></a:fillRef><a:effectRef idx="1"><a:schemeClr val="accent1"/></a:effectRef><a:fontRef idx="minor"><a:schemeClr val="tx1"/></a:fontRef></a:style>',r[r.length]="</a:lnDef>",r[r.length]="</a:objectDefaults>",r[r.length]="<a:extraClrSchemeLst/>",r[r.length]="</a:theme>",r.join("")}function qi(e){var t=e.read_shift(2),r=e.read_shift(2)-4,a=[t];switch(t){case 4:case 5:case 7:case 8:case 9:case 10:case 11:case 13:a[1]=function(e){var t={};switch(t.xclrType=e.read_shift(2),t.nTintShade=e.read_shift(2),t.xclrType){case 0:case 4:e.l+=4;break;case 1:t.xclrValue=function(e,t){return jr(e,4)}(e);break;case 2:t.xclrValue=ls(e);break;case 3:t.xclrValue=function(e){return e.read_shift(4)}(e)}return e.l+=8,t}(e);break;case 6:a[1]=function(e,t){return jr(e,t)}(e,r);break;case 14:case 15:a[1]=e.read_shift(1===r?1:2);break;default:throw new Error("Unrecognized ExtProp type: "+t+" "+r)}return a}function Qi(e,t,r){var a=[21600,21600],n=["m0,0l0",a[1],a[0],a[1],a[0],"0xe"].join(","),s=[sr("xml",null,{"xmlns:v":hr.v,"xmlns:o":hr.o,"xmlns:x":hr.x,"xmlns:mv":hr.mv}).replace(/\/>/,">"),sr("o:shapelayout",sr("o:idmap",null,{"v:ext":"edit",data:e}),{"v:ext":"edit"})],i=65536*e,o=t||[];return o.length>0&&s.push(sr("v:shapetype",[sr("v:stroke",null,{joinstyle:"miter"}),sr("v:path",null,{gradientshapeok:"t","o:connecttype":"rect"})].join(""),{id:"_x0000_t202",coordsize:a.join(","),"o:spt":202,path:n})),o.forEach((function(e){++i,s.push(function(e,t,r){var a=ia(e[0]),n={color2:"#BEFF82",type:"gradient"};"gradient"==n.type&&(n.angle="-180");var s="gradient"==n.type?sr("o:fill",null,{type:"gradientUnscaled","v:ext":"view"}):null,i=sr("v:fill",s,n);return["<v:shape"+nr({id:"_x0000_s"+t,type:"#_x0000_t202",style:"position:absolute; margin-left:80pt;margin-top:5pt;width:104pt;height:64pt;z-index:10"+(e[1].hidden?";visibility:hidden":""),fillcolor:"#ECFAD4",strokecolor:"#edeaa1"})+">",i,sr("v:shadow",null,{on:"t",obscured:"t"}),sr("v:path",null,{"o:connecttype":"none"}),'<v:textbox><div style="text-align:left"></div></v:textbox>','<x:ClientData ObjectType="Note">',"<x:MoveWithCells/>","<x:SizeWithCells/>",ar("x:Anchor",[a.c+1,0,a.r+1,0,a.c+3,20,a.r+5,20].join(",")),ar("x:AutoFill","False"),ar("x:Row",String(a.r)),ar("x:Column",String(a.c)),e[1].hidden?"":"<x:Visible/>","</x:ClientData>","</v:shape>"].join("")}(e,i))})),s.push("</xml>"),s.join("")}function eo(e,t,r,a){var n,s=null!=e["!data"];t.forEach((function(t){var i=ia(t.ref);if(!(i.r<0||i.c<0)){if(s?(e["!data"][i.r]||(e["!data"][i.r]=[]),n=e["!data"][i.r][i.c]):n=e[t.ref],!n){n={t:"z"},s?e["!data"][i.r][i.c]=n:e[t.ref]=n;var o=ua(e["!ref"]||"BDWGO1000001:A1");o.s.r>i.r&&(o.s.r=i.r),o.e.r<i.r&&(o.e.r=i.r),o.s.c>i.c&&(o.s.c=i.c),o.e.c<i.c&&(o.e.c=i.c);var c=la(o);e["!ref"]=c}n.c||(n.c=[]);var l={a:t.author,t:t.t,r:t.r,T:r};t.h&&(l.h=t.h);for(var f=n.c.length-1;f>=0;--f){if(!r&&n.c[f].T)return;r&&!n.c[f].T&&n.c.splice(f,1)}if(r&&a)for(f=0;f<a.length;++f)if(l.a==a[f].id){l.a=a[f].name||l.a;break}n.c.push(l)}}))}function to(e){var t=[At,sr("comments",null,{xmlns:fr[0]})],r=[];return t.push("<authors>"),e.forEach((function(e){e[1].forEach((function(e){var a=Wt(e.a);-1==r.indexOf(a)&&(r.push(a),t.push("<author>"+a+"</author>")),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),t.push("<author>tc="+e.ID+"</author>"))}))})),0==r.length&&(r.push("SheetJ5"),t.push("<author>SheetJ5</author>")),t.push("</authors>"),t.push("<commentList>"),e.forEach((function(e){var a=0,n=[],s=0;if(e[1][0]&&e[1][0].T&&e[1][0].ID&&(a=r.indexOf("tc="+e[1][0].ID)),e[1].forEach((function(e){e.a&&(a=r.indexOf(Wt(e.a))),e.T&&++s,n.push(null==e.t?"":Wt(e.t))})),0===s)e[1].forEach((function(a){t.push('<comment ref="'+e[0]+'" authorId="'+r.indexOf(Wt(a.a))+'"><text>'),t.push(ar("t",null==a.t?"":Wt(a.t))),t.push("</text></comment>")}));else{e[1][0]&&e[1][0].T&&e[1][0].ID&&(a=r.indexOf("tc="+e[1][0].ID)),t.push('<comment ref="'+e[0]+'" authorId="'+a+'"><text>');for(var i="Comment:\n    "+n[0]+"\n",o=1;o<n.length;++o)i+="Reply:\n    "+n[o]+"\n";t.push(ar("t",Wt(i))),t.push("</text></comment>")}})),t.push("</commentList>"),t.length>2&&(t[t.length]="</comments>",t[1]=t[1].replace("/>",">")),t.join("")}function ro(e,t,r){var a=[At,sr("ThreadedComments",null,{xmlns:lr.TCMNT}).replace(/[\/]>/,">")];return e.forEach((function(e){var n="";(e[1]||[]).forEach((function(s,i){if(s.T){s.a&&-1==t.indexOf(s.a)&&t.push(s.a);var o={ref:e[0],id:"{54EE7951-7262-4200-6969-"+("000000000000"+r.tcid++).slice(-12)+"}"};0==i?n=o.id:o.parentId=n,s.ID=o.id,s.a&&(o.personId="{54EE7950-7262-4200-6969-"+("000000000000"+t.indexOf(s.a)).slice(-12)+"}"),a.push(sr("threadedComment",ar("text",s.t||""),o))}else delete s.ID}))})),a.push("</ThreadedComments>"),a.join("")}function ao(e){var t=[At,sr("personList",null,{xmlns:lr.TCMNT,"xmlns:x":fr[0]}).replace(/[\/]>/,">")];return e.forEach((function(e,r){t.push(sr("person",null,{displayName:e,id:"{54EE7950-7262-4200-6969-"+("000000000000"+r).slice(-12)+"}",userId:e,providerId:"None"}))})),t.push("</personList>"),t.join("")}var no=ba;function so(e){return wa(e.slice(0,54))}function io(e){var t=Zr(),r=[];return Jr(t,628),Jr(t,630),e.forEach((function(e){e[1].forEach((function(e){r.indexOf(e.a)>-1||(r.push(e.a.slice(0,54)),Jr(t,632,so(e.a)),e.T&&e.ID&&-1==r.indexOf("tc="+e.ID)&&(r.push("tc="+e.ID),Jr(t,632,so("tc="+e.ID))))}))})),Jr(t,631),Jr(t,633),e.forEach((function(e){e[1].forEach((function(a){var n=-1;a.ID&&(n=r.indexOf("tc="+a.ID)),-1==n&&e[1][0].T&&e[1][0].ID&&(n=r.indexOf("tc="+e[1][0].ID)),-1==n&&(n=r.indexOf(a.a)),a.iauthor=n;var s,i,o,c={s:ia(e[0]),e:ia(e[0])};Jr(t,635,function(e,t){return null==t&&(t=Kr(36)),t.write_shift(4,e[1].iauthor),Ua(e[0],t),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t.write_shift(4,0),t}([c,a])),a.t&&a.t.length>0&&Jr(t,637,(s=a,o=!1,null==i&&(o=!0,i=Kr(23+4*s.t.length)),i.write_shift(1,1),wa(s.t,i),i.write_shift(4,1),function(e,t){t||(t=Kr(4)),t.write_shift(2,e.ich||0),t.write_shift(2,e.ifnt||0)}({ich:0,ifnt:0},i),o?i.slice(0,i.l):i)),Jr(t,636),delete a.iauthor}))})),Jr(t,634),Jr(t,629),t.end()}var oo="application/vnd.ms-office.vbaProject",co=["xlsb","xlsm","xlam","biff8","xla"],lo=function(){var e=/(^|[^A-Za-z_])R(\[?-?\d+\]|[1-9]\d*|)C(\[?-?\d+\]|[1-9]\d*|)(?![A-Za-z0-9_])/g,t={r:0,c:0};function r(e,r,a,n){var s=!1,i=!1;0==a.length?i=!0:"["==a.charAt(0)&&(i=!0,a=a.slice(1,-1)),0==n.length?s=!0:"["==n.charAt(0)&&(s=!0,n=n.slice(1,-1));var o=a.length>0?0|parseInt(a,10):0,c=n.length>0?0|parseInt(n,10):0;return s?c+=t.c:--c,i?o+=t.r:--o,r+(s?"":"$")+sa(c)+(i?"":"$")+aa(o)}return function(a,n){return t=n,a.replace(e,r)}}(),fo=/(^|[^._A-Z0-9])(\$?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])(\$?)(\d{1,7})(?![_.\(A-Za-z0-9])/g;try{fo=/(^|[^._A-Z0-9])([$]?)([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])([$]?)(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})(?![_.\(A-Za-z0-9])/g}catch(e){}var ho=function(){return function(e,t){return e.replace(fo,(function(e,r,a,n,s,i){var o=na(n)-(a?0:t.c),c=ra(i)-(s?0:t.r);return r+"R"+("$"==s?c+1:0==c?"":"["+c+"]")+"C"+("$"==a?o+1:0==o?"":"["+o+"]")}))}}();function uo(e,t){return e.replace(fo,(function(e,r,a,n,s,i){return r+("$"==a?a+n:sa(na(n)+t.c))+("$"==s?s+i:aa(ra(i)+t.r))}))}function po(e,t,r){var a=ca(t).s,n=ia(r);return uo(e,{r:n.r-a.r,c:n.c-a.c})}function mo(e){return e.replace(/_xlfn\./g,"")}function vo(e){e.l+=1}function go(e,t){var r=e.read_shift(1==t?1:2);return[16383&r,r>>14&1,r>>15&1]}function bo(e,t,r){var a=2;if(r){if(r.biff>=2&&r.biff<=5)return wo(e);12==r.biff&&(a=4)}var n=e.read_shift(a),s=e.read_shift(a),i=go(e,2),o=go(e,2);return{s:{r:n,c:i[0],cRel:i[1],rRel:i[2]},e:{r:s,c:o[0],cRel:o[1],rRel:o[2]}}}function wo(e){var t=go(e,2),r=go(e,2),a=e.read_shift(1),n=e.read_shift(1);return{s:{r:t[0],c:a,cRel:t[1],rRel:t[2]},e:{r:r[0],c:n,cRel:r[1],rRel:r[2]}}}function To(e,t,r){if(r&&r.biff>=2&&r.biff<=5)return function(e){var t=go(e,2),r=e.read_shift(1);return{r:t[0],c:r,cRel:t[1],rRel:t[2]}}(e);var a=e.read_shift(r&&12==r.biff?4:2),n=go(e,2);return{r:a,c:n[0],cRel:n[1],rRel:n[2]}}function yo(e){var t=e.read_shift(2),r=e.read_shift(2);return{r:t,c:255&r,fQuoted:!!(16384&r),cRel:r>>15,rRel:r>>15}}function Eo(e){var t=1&e[e.l+1];return e.l+=4,[t,1]}function ko(e){return[e.read_shift(1),e.read_shift(1)]}function _o(e,t){var r=[e.read_shift(1)];if(12==t)switch(r[0]){case 2:r[0]=4;break;case 4:r[0]=16;break;case 0:r[0]=1;break;case 1:r[0]=2}switch(r[0]){case 4:r[1]=Xn(e,1)?"TRUE":"FALSE",12!=t&&(e.l+=7);break;case 37:case 16:r[1]=tn[e[e.l]],e.l+=12==t?4:8;break;case 0:e.l+=8;break;case 1:r[1]=Ba(e);break;case 2:r[1]=ns(e,0,{biff:t>0&&t<8?2:t});break;default:throw new Error("Bad SerAr: "+r[0])}return r}function So(e,t,r){for(var a=e.read_shift(12==r.biff?4:2),n=[],s=0;s!=a;++s)n.push((12==r.biff?La:ms)(e,8));return n}function xo(e,t,r){var a=0,n=0;12==r.biff?(a=e.read_shift(4),n=e.read_shift(4)):(n=1+e.read_shift(1),a=1+e.read_shift(2)),r.biff>=2&&r.biff<8&&(--a,0==--n&&(n=256));for(var s=0,i=[];s!=a&&(i[s]=[]);++s)for(var o=0;o!=n;++o)i[s][o]=_o(e,r.biff);return i}function Ao(e,t,r){return e.l+=2,[yo(e)]}function Co(e){return e.l+=6,[]}function Io(e){return e.l+=2,[Kn(e),1&e.read_shift(2)]}var Oo=["Data","All","Headers","??","?Data2","??","?DataHeaders","??","Totals","??","??","??","?DataTotals","??","??","??","?Current"],Ro={1:{n:"PtgExp",f:function(e,t,r){return e.l++,r&&12==r.biff?[e.read_shift(4,"i"),0]:[e.read_shift(2),e.read_shift(r&&2==r.biff?1:2)]}},2:{n:"PtgTbl",f:jr},3:{n:"PtgAdd",f:vo},4:{n:"PtgSub",f:vo},5:{n:"PtgMul",f:vo},6:{n:"PtgDiv",f:vo},7:{n:"PtgPower",f:vo},8:{n:"PtgConcat",f:vo},9:{n:"PtgLt",f:vo},10:{n:"PtgLe",f:vo},11:{n:"PtgEq",f:vo},12:{n:"PtgGe",f:vo},13:{n:"PtgGt",f:vo},14:{n:"PtgNe",f:vo},15:{n:"PtgIsect",f:vo},16:{n:"PtgUnion",f:vo},17:{n:"PtgRange",f:vo},18:{n:"PtgUplus",f:vo},19:{n:"PtgUminus",f:vo},20:{n:"PtgPercent",f:vo},21:{n:"PtgParen",f:vo},22:{n:"PtgMissArg",f:vo},23:{n:"PtgStr",f:function(e,t,r){return e.l++,Qn(e,0,r)}},26:{n:"PtgSheet",f:function(e,t,r){return e.l+=5,e.l+=2,e.l+=2==r.biff?1:4,["PTGSHEET"]}},27:{n:"PtgEndSheet",f:function(e,t,r){return e.l+=2==r.biff?4:5,["PTGENDSHEET"]}},28:{n:"PtgErr",f:function(e){return e.l++,tn[e.read_shift(1)]}},29:{n:"PtgBool",f:function(e){return e.l++,0!==e.read_shift(1)}},30:{n:"PtgInt",f:function(e){return e.l++,e.read_shift(2)}},31:{n:"PtgNum",f:function(e){return e.l++,Ba(e)}},32:{n:"PtgArray",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=2==r.biff?6:12==r.biff?14:7,[a]}},33:{n:"PtgFunc",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(r&&r.biff<=3?1:2);return[ec[n],Qo[n],a]}},34:{n:"PtgFuncVar",f:function(e,t,r){var a=e[e.l++],n=e.read_shift(1),s=r&&r.biff<=3?[88==a?-1:0,e.read_shift(1)]:function(e){return[e[e.l+1]>>7,32767&e.read_shift(2)]}(e);return[n,(0===s[0]?Qo:qo)[s[1]]]}},35:{n:"PtgName",f:function(e,t,r){var a=e.read_shift(1)>>>5&3,n=!r||r.biff>=8?4:2,s=e.read_shift(n);switch(r.biff){case 2:e.l+=5;break;case 3:case 4:e.l+=8;break;case 5:e.l+=12}return[a,0,s]}},36:{n:"PtgRef",f:function(e,t,r){var a=(96&e[e.l])>>5;return e.l+=1,[a,To(e,0,r)]}},37:{n:"PtgArea",f:function(e,t,r){return[(96&e[e.l++])>>5,bo(e,r.biff>=2&&r.biff,r)]}},38:{n:"PtgMemArea",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=r&&2==r.biff?3:4,[a,e.read_shift(r&&2==r.biff?1:2)]}},39:{n:"PtgMemErr",f:jr},40:{n:"PtgMemNoMem",f:jr},41:{n:"PtgMemFunc",f:function(e,t,r){return[e.read_shift(1)>>>5&3,e.read_shift(r&&2==r.biff?1:2)]}},42:{n:"PtgRefErr",f:function(e,t,r){var a=e.read_shift(1)>>>5&3;return e.l+=4,r.biff<8&&e.l--,12==r.biff&&(e.l+=2),[a]}},43:{n:"PtgAreaErr",f:function(e,t,r){var a=(96&e[e.l++])>>5;return e.l+=r&&r.biff>8?12:r.biff<8?6:8,[a]}},44:{n:"PtgRefN",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=function(e,t,r){var a=r&&r.biff?r.biff:8;if(a>=2&&a<=5)return function(e){var t=e.read_shift(2),r=e.read_shift(1),a=(32768&t)>>15,n=(16384&t)>>14;return t&=16383,1==a&&t>=8192&&(t-=16384),1==n&&r>=128&&(r-=256),{r:t,c:r,cRel:n,rRel:a}}(e);var n=e.read_shift(a>=12?4:2),s=e.read_shift(2),i=(16384&s)>>14,o=(32768&s)>>15;if(s&=16383,1==o)for(;n>524287;)n-=1048576;if(1==i)for(;s>8191;)s-=16384;return{r:n,c:s,cRel:i,rRel:o}}(e,0,r);return[a,n]}},45:{n:"PtgAreaN",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=function(e,t,r){if(r.biff<8)return wo(e);var a=e.read_shift(12==r.biff?4:2),n=e.read_shift(12==r.biff?4:2),s=go(e,2),i=go(e,2);return{s:{r:a,c:s[0],cRel:s[1],rRel:s[2]},e:{r:n,c:i[0],cRel:i[1],rRel:i[2]}}}(e,0,r);return[a,n]}},46:{n:"PtgMemAreaN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},47:{n:"PtgMemNoMemN",f:function(e){return[e.read_shift(1)>>>5&3,e.read_shift(2)]}},57:{n:"PtgNameX",f:function(e,t,r){return 5==r.biff?function(e){var t=e.read_shift(1)>>>5&3,r=e.read_shift(2,"i");e.l+=8;var a=e.read_shift(2);return e.l+=12,[t,r,a]}(e):[e.read_shift(1)>>>5&3,e.read_shift(2),e.read_shift(4)]}},58:{n:"PtgRef3d",f:function(e,t,r){var a=(96&e[e.l])>>5;e.l+=1;var n=e.read_shift(2);return r&&5==r.biff&&(e.l+=12),[a,n,To(e,0,r)]}},59:{n:"PtgArea3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2,"i");if(r&&5===r.biff)e.l+=12;return[a,n,bo(e,0,r)]}},60:{n:"PtgRefErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=4;if(r)switch(r.biff){case 5:s=15;break;case 12:s=6}return e.l+=s,[a,n]}},61:{n:"PtgAreaErr3d",f:function(e,t,r){var a=(96&e[e.l++])>>5,n=e.read_shift(2),s=8;if(r)switch(r.biff){case 5:e.l+=12,s=6;break;case 12:s=12}return e.l+=s,[a,n]}},255:{}},No={64:32,96:32,65:33,97:33,66:34,98:34,67:35,99:35,68:36,100:36,69:37,101:37,70:38,102:38,71:39,103:39,72:40,104:40,73:41,105:41,74:42,106:42,75:43,107:43,76:44,108:44,77:45,109:45,78:46,110:46,79:47,111:47,88:34,120:34,89:57,121:57,90:58,122:58,91:59,123:59,92:60,124:60,93:61,125:61},Fo={1:{n:"PtgElfLel",f:Io},2:{n:"PtgElfRw",f:Ao},3:{n:"PtgElfCol",f:Ao},6:{n:"PtgElfRwV",f:Ao},7:{n:"PtgElfColV",f:Ao},10:{n:"PtgElfRadical",f:Ao},11:{n:"PtgElfRadicalS",f:Co},13:{n:"PtgElfColS",f:Co},15:{n:"PtgElfColSV",f:Co},16:{n:"PtgElfRadicalLel",f:Io},25:{n:"PtgList",f:function(e){e.l+=2;var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n=e.read_shift(2),s=e.read_shift(2);return{ixti:t,coltype:3&r,rt:Oo[r>>2&31],idx:a,c:n,C:s}}},29:{n:"PtgSxName",f:function(e){return e.l+=2,[e.read_shift(4)]}},255:{}},Do={0:{n:"PtgAttrNoop",f:function(e){return e.l+=4,[0,0]}},1:{n:"PtgAttrSemi",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=r&&2==r.biff?3:4,[a]}},2:{n:"PtgAttrIf",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},4:{n:"PtgAttrChoose",f:function(e,t,r){e.l+=2;for(var a=e.read_shift(r&&2==r.biff?1:2),n=[],s=0;s<=a;++s)n.push(e.read_shift(r&&2==r.biff?1:2));return n}},8:{n:"PtgAttrGoto",f:function(e,t,r){var a=255&e[e.l+1]?1:0;return e.l+=2,[a,e.read_shift(r&&2==r.biff?1:2)]}},16:{n:"PtgAttrSum",f:function(e,t,r){e.l+=r&&2==r.biff?3:4}},32:{n:"PtgAttrBaxcel",f:Eo},33:{n:"PtgAttrBaxcel",f:Eo},64:{n:"PtgAttrSpace",f:function(e){return e.read_shift(2),ko(e)}},65:{n:"PtgAttrSpaceSemi",f:function(e){return e.read_shift(2),ko(e)}},128:{n:"PtgAttrIfError",f:function(e){var t=255&e[e.l+1]?1:0;return e.l+=2,[t,e.read_shift(2)]}},255:{}};function Po(e,t,r,a){if(a.biff<8)return jr(e,t);for(var n=e.l+t,s=[],i=0;i!==r.length;++i)switch(r[i][0]){case"PtgArray":r[i][1]=xo(e,0,a),s.push(r[i][1]);break;case"PtgMemArea":r[i][2]=So(e,r[i][1],a),s.push(r[i][2]);break;case"PtgExp":a&&12==a.biff&&(r[i][1][1]=e.read_shift(4),s.push(r[i][1]));break;case"PtgList":case"PtgElfRadicalS":case"PtgElfColS":case"PtgElfColSV":throw"Unsupported "+r[i][0]}return 0!=(t=n-e.l)&&s.push(jr(e,t)),s}function Mo(e,t,r){for(var a,n,s=e.l+t,i=[];s!=e.l;)t=s-e.l,n=e[e.l],a=Ro[n]||Ro[No[n]],24!==n&&25!==n||(a=(24===n?Fo:Do)[e[e.l+1]]),a&&a.f?i.push([a.n,a.f(e,t,r)]):jr(e,t);return i}function Lo(e){for(var t=[],r=0;r<e.length;++r){for(var a=e[r],n=[],s=0;s<a.length;++s){var i=a[s];i?2===i[0]?n.push('"'+i[1].replace(/"/g,'""')+'"'):n.push(i[1]):n.push("")}t.push(n.join(","))}return t.join(";")}var Uo={PtgAdd:"+",PtgConcat:"&",PtgDiv:"/",PtgEq:"=",PtgGe:">=",PtgGt:">",PtgLe:"<=",PtgLt:"<",PtgMul:"*",PtgNe:"<>",PtgPower:"^",PtgSub:"-"};function Bo(e,t,r){if(!e)return"SH33TJSERR0";if(r.biff>8&&(!e.XTI||!e.XTI[t]))return e.SheetNames[t];if(!e.XTI)return"SH33TJSERR6";var a=e.XTI[t];if(r.biff<8)return t>1e4&&(t-=65536),t<0&&(t=-t),0==t?"":e.XTI[t-1];if(!a)return"SH33TJSERR1";var n="";if(r.biff>8)switch(e[a[0]][0]){case 357:return n=-1==a[1]?"#REF":e.SheetNames[a[1]],a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 358:return null!=r.SID?e.SheetNames[r.SID]:"SH33TJSSAME"+e[a[0]][0];default:return"SH33TJSSRC"+e[a[0]][0]}switch(e[a[0]][0][0]){case 1025:return n=-1==a[1]?"#REF":e.SheetNames[a[1]]||"SH33TJSERR3",a[1]==a[2]?n:n+":"+e.SheetNames[a[2]];case 14849:return e[a[0]].slice(1).map((function(e){return e.Name})).join(";;");default:return e[a[0]][0][3]?(n=-1==a[1]?"#REF":e[a[0]][0][3][a[1]]||"SH33TJSERR4",a[1]==a[2]?n:n+":"+e[a[0]][0][3][a[2]]):"SH33TJSERR2"}}function Wo(e,t,r){var a=Bo(e,t,r);return"#REF"==a?a:ha(a,r)}function zo(e,t,r,a,n){var s,i,o,c,l=n&&n.biff||8,f={s:{c:0,r:0},e:{c:0,r:0}},h=[],u=0,d=0,p="";if(!e[0]||!e[0][0])return"";for(var m,v,g,b,w=-1,T="",y=0,E=e[0].length;y<E;++y){var k=e[0][y];switch(k[0]){case"PtgUminus":h.push("-"+h.pop());break;case"PtgUplus":h.push("+"+h.pop());break;case"PtgPercent":h.push(h.pop()+"%");break;case"PtgAdd":case"PtgConcat":case"PtgDiv":case"PtgEq":case"PtgGe":case"PtgGt":case"PtgLe":case"PtgLt":case"PtgMul":case"PtgNe":case"PtgPower":case"PtgSub":if(s=h.pop(),i=h.pop(),w>=0){switch(e[0][w][1][0]){case 0:T=Ye(" ",e[0][w][1][1]);break;case 1:T=Ye("\r",e[0][w][1][1]);break;default:if(T="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][w][1][0])}i+=T,w=-1}h.push(i+Uo[k[0]]+s);break;case"PtgIsect":s=h.pop(),i=h.pop(),h.push(i+" "+s);break;case"PtgUnion":s=h.pop(),i=h.pop(),h.push(i+","+s);break;case"PtgRange":s=h.pop(),i=h.pop(),h.push((v=s,void 0,void 0,g=(m=i).lastIndexOf("!"),b=v.lastIndexOf("!"),-1==g&&-1==b?m+":"+v:g>0&&b>0&&m.slice(0,g).toLowerCase()==v.slice(0,b).toLowerCase()?m+":"+v.slice(b+1):(console.error("Cannot hydrate range",m,v),m+":"+v)));break;case"PtgAttrChoose":case"PtgAttrGoto":case"PtgAttrIf":case"PtgAttrIfError":case"PtgAttrBaxcel":case"PtgAttrSemi":case"PtgMemArea":case"PtgTbl":case"PtgMemErr":case"PtgMemAreaN":case"PtgMemNoMemN":case"PtgAttrNoop":case"PtgSheet":case"PtgEndSheet":case"PtgMemFunc":case"PtgMemNoMem":break;case"PtgRef":o=qr(k[1][1],f,n),h.push(ea(o,l));break;case"PtgRefN":o=r?qr(k[1][1],r,n):k[1][1],h.push(ea(o,l));break;case"PtgRef3d":u=k[1][1],o=qr(k[1][2],f,n),p=Wo(a,u,n),h.push(p+"!"+ea(o,l));break;case"PtgFunc":case"PtgFuncVar":var _=k[1][0],S=k[1][1];_||(_=0);var x=0==(_&=127)?[]:h.slice(-_);h.length-=_,"User"===S&&(S=x.shift()),h.push(S+"("+x.join(",")+")");break;case"PtgBool":h.push(k[1]?"TRUE":"FALSE");break;case"PtgInt":case"PtgErr":h.push(k[1]);break;case"PtgNum":h.push(String(k[1]));break;case"PtgStr":h.push('"'+k[1].replace(/"/g,'""')+'"');break;case"PtgAreaN":c=Qr(k[1][1],r?{s:r}:f,n),h.push(ta(c,n));break;case"PtgArea":c=Qr(k[1][1],f,n),h.push(ta(c,n));break;case"PtgArea3d":u=k[1][1],c=k[1][2],p=Wo(a,u,n),h.push(p+"!"+ta(c,n));break;case"PtgAttrSum":h.push("SUM("+h.pop()+")");break;case"PtgName":d=k[1][2];var A=(a.names||[])[d-1]||(a[0]||[])[d],C=A?A.Name:"SH33TJSNAME"+String(d);C&&"_xlfn."==C.slice(0,6)&&!n.xlfn&&(C=C.slice(6)),h.push(C);break;case"PtgNameX":var I,O=k[1][1];if(d=k[1][2],!(n.biff<=5)){var R="";if(14849==((a[O]||[])[0]||[])[0]||(1025==((a[O]||[])[0]||[])[0]?a[O][d]&&a[O][d].itab>0&&(R=a.SheetNames[a[O][d].itab-1]+"!"):R=a.SheetNames[d-1]+"!"),a[O]&&a[O][d])R+=a[O][d].Name;else if(a[0]&&a[0][d])R+=a[0][d].Name;else{var N=(Bo(a,O,n)||"").split(";;");N[d-1]?R=N[d-1]:R+="SH33TJSERRX"}h.push(R);break}O<0&&(O=-O),a[O]&&(I=a[O][d]),I||(I={Name:"SH33TJSERRY"}),h.push(I.Name);break;case"PtgParen":var F="(",D=")";if(w>=0){switch(T="",e[0][w][1][0]){case 2:F=Ye(" ",e[0][w][1][1])+F;break;case 3:F=Ye("\r",e[0][w][1][1])+F;break;case 4:D=Ye(" ",e[0][w][1][1])+D;break;case 5:D=Ye("\r",e[0][w][1][1])+D;break;default:if(n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+e[0][w][1][0])}w=-1}h.push(F+h.pop()+D);break;case"PtgRefErr":case"PtgRefErr3d":case"PtgAreaErr":case"PtgAreaErr3d":h.push("#REF!");break;case"PtgExp":o={c:k[1][1],r:k[1][0]};var P={c:r.c,r:r.r};if(a.sharedf[oa(o)]){var M=a.sharedf[oa(o)];h.push(zo(M,0,P,a,n))}else{var L=!1;for(s=0;s!=a.arrayf.length;++s)if(i=a.arrayf[s],!(o.c<i[0].s.c||o.c>i[0].e.c||o.r<i[0].s.r||o.r>i[0].e.r)){h.push(zo(i[1],0,P,a,n)),L=!0;break}L||h.push(k[1])}break;case"PtgArray":h.push("{"+Lo(k[1])+"}");break;case"PtgAttrSpace":case"PtgAttrSpaceSemi":w=y;break;case"PtgMissArg":h.push("");break;case"PtgList":h.push("Table"+k[1].idx+"[#"+k[1].rt+"]");break;case"PtgElfCol":case"PtgElfColS":case"PtgElfColSV":case"PtgElfColV":case"PtgElfLel":case"PtgElfRadical":case"PtgElfRadicalLel":case"PtgElfRadicalS":case"PtgElfRw":case"PtgElfRwV":throw new Error("Unsupported ELFs");default:throw new Error("Unrecognized Formula Token: "+String(k))}if(3!=n.biff&&w>=0&&-1==["PtgAttrSpace","PtgAttrSpaceSemi","PtgAttrGoto"].indexOf(e[0][y][0])){var U=!0;switch((k=e[0][w])[1][0]){case 4:U=!1;case 0:T=Ye(" ",k[1][1]);break;case 5:U=!1;case 1:T=Ye("\r",k[1][1]);break;default:if(T="",n.WTF)throw new Error("Unexpected PtgAttrSpaceType "+k[1][0])}h.push((U?T:"")+h.pop()+(U?"":T)),w=-1}}if(h.length>1&&n.WTF)throw new Error("bad formula stack");return"TRUE"==h[0]||"FALSE"!=h[0]&&h[0]}function Ho(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],jr(e,t-2)];var o=Mo(e,i,r);return t!==i+s&&(a=Po(e,t-i-s,o,r)),e.l=n,[o,a]}function Vo(e,t,r){var a,n=e.l+t,s=e.read_shift(2),i=Mo(e,s,r);return 65535==s?[[],jr(e,t-2)]:(t!==s+2&&(a=Po(e,n-s-2,i,r)),[i,a])}function $o(e,t,r){var a=e.l+t,n=hs(e,6,r),s=function(e){var t;if(65535!==Pr(e,e.l+6))return[Ba(e),"n"];switch(e[e.l]){case 0:return e.l+=8,["String","s"];case 1:return t=1===e[e.l+2],e.l+=8,[t,"b"];case 2:return t=e[e.l+2],e.l+=8,[t,"e"];case 3:return e.l+=8,["","s"]}return[]}(e),i=e.read_shift(1);2!=r.biff&&(e.read_shift(1),r.biff>=5&&e.read_shift(4));var o=function(e,t,r){var a,n=e.l+t,s=2==r.biff?1:2,i=e.read_shift(s);if(65535==i)return[[],jr(e,t-2)];var o=Mo(e,i,r);return t!==i+s&&(a=Po(e,t-i-s,o,r)),e.l=n,[o,a]}(e,a-e.l,r);return{cell:n,val:s[0],formula:o,shared:i>>3&1,tt:s[1]}}function Go(e,t,r){var a=e.read_shift(4),n=Mo(e,a,r),s=e.read_shift(4);return[n,s>0?Po(e,s,n,r):null]}var Xo=Go,jo=Go,Ko=Go,Yo=Go;function Zo(e){if((0|e)==e&&e<Math.pow(2,16)&&e>=0){var t=Kr(11);return t.write_shift(4,3),t.write_shift(1,30),t.write_shift(2,e),t.write_shift(4,0),t}var r=Kr(17);return r.write_shift(4,11),r.write_shift(1,31),r.write_shift(8,e),r.write_shift(4,0),r}var Jo=function(e,t){if("number"==typeof e)return Zo(e);if("boolean"==typeof e)return function(e){var t=Kr(10);return t.write_shift(4,2),t.write_shift(1,29),t.write_shift(1,e?1:0),t.write_shift(4,0),t}(e);if(/^#(DIV\/0!|GETTING_DATA|N\/A|NAME\?|NULL!|NUM!|REF!|VALUE!)$/.test(e))return function(e){var t=Kr(10);return t.write_shift(4,2),t.write_shift(1,28),t.write_shift(1,e),t.write_shift(4,0),t}(+rn[e]);if(e.match(/^\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})$/))return a=ia(r=e),(n=Kr(15)).write_shift(4,7),n.write_shift(1,36),n.write_shift(4,a.r),n.write_shift(2,a.c|("$"==r.charAt(0)?0:1)<<14|(r.match(/\$\d/)?0:1)<<15),n.write_shift(4,0),n;var r,a,n;if(e.match(/^\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5}):\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})$/))return function(e){var t=e.split(":"),r=t[0],a=Kr(23);a.write_shift(4,15);var n=ia(r=t[0]);return a.write_shift(1,36),a.write_shift(4,n.r),a.write_shift(2,n.c|("$"==r.charAt(0)?0:1)<<14|(r.match(/\$\d/)?0:1)<<15),a.write_shift(4,0),n=ia(r=t[1]),a.write_shift(1,36),a.write_shift(4,n.r),a.write_shift(2,n.c|("$"==r.charAt(0)?0:1)<<14|(r.match(/\$\d/)?0:1)<<15),a.write_shift(4,0),a.write_shift(1,17),a.write_shift(4,0),a}(e);if(e.match(/^#REF!\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5}):\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})$/))return function(e,t){var r=e.lastIndexOf("!"),a=e.slice(0,r);e=e.slice(r+1),"'"==a.charAt(0)&&(a=a.slice(1,-1).replace(/''/g,"'"));var n=ca(e),s=Kr(23);return s.write_shift(4,15),s.write_shift(1,59),s.write_shift(2,2+t.SheetNames.map((function(e){return e.toLowerCase()})).indexOf(a.toLowerCase())),s.write_shift(4,n.s.r),s.write_shift(4,n.e.r),s.write_shift(2,n.s.c),s.write_shift(2,n.e.c),s.write_shift(4,0),s}(e,t);if(e.match(/^(?:'[^\\\/?*\[\]:]*'|[^'][^\\\/?*\[\]:'`~!@#$%^()\-=+{}|;,<.>]*)!\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})$/))return function(e,t){var r=e.lastIndexOf("!"),a=e.slice(0,r),n=ia(e=e.slice(r+1));"'"==a.charAt(0)&&(a=a.slice(1,-1).replace(/''/g,"'"));var s=Kr(17);return s.write_shift(4,9),s.write_shift(1,58),s.write_shift(2,2+t.SheetNames.map((function(e){return e.toLowerCase()})).indexOf(a.toLowerCase())),s.write_shift(4,n.r),s.write_shift(2,n.c|("$"==e.charAt(0)?0:1)<<14|(e.match(/\$\d/)?0:1)<<15),s.write_shift(4,0),s}(e,t);if(e.match(/^(?:'[^\\\/?*\[\]:]*'|[^'][^\\\/?*\[\]:'`~!@#$%^()\-=+{}|;,<.>]*)!\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5}):\$?(?:[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D]|[A-Z]{1,2})\$?(?:10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})$/))return function(e,t){var r=e.lastIndexOf("!"),a=e.slice(0,r);e=e.slice(r+1),"'"==a.charAt(0)&&(a=a.slice(1,-1).replace(/''/g,"'"));var n=e.split(":"),s=Kr(27);s.write_shift(4,19);var i=n[0],o=ia(i);return s.write_shift(1,58),s.write_shift(2,2+t.SheetNames.map((function(e){return e.toLowerCase()})).indexOf(a.toLowerCase())),s.write_shift(4,o.r),s.write_shift(2,o.c|("$"==i.charAt(0)?0:1)<<14|(i.match(/\$\d/)?0:1)<<15),o=ia(i=n[1]),s.write_shift(1,58),s.write_shift(2,2+t.SheetNames.map((function(e){return e.toLowerCase()})).indexOf(a.toLowerCase())),s.write_shift(4,o.r),s.write_shift(2,o.c|("$"==i.charAt(0)?0:1)<<14|(i.match(/\$\d/)?0:1)<<15),s.write_shift(1,17),s.write_shift(4,0),s}(e,t);if(/^(?:'[^\\\/?*\[\]:]*'|[^'][^\\\/?*\[\]:'`~!@#$%^()\-=+{}|;,<.>]*)!#REF!$/.test(e))return function(e,t){var r=e.lastIndexOf("!"),a=e.slice(0,r);e=e.slice(r+1),"'"==a.charAt(0)&&(a=a.slice(1,-1).replace(/''/g,"'"));var n=Kr(17);return n.write_shift(4,9),n.write_shift(1,60),n.write_shift(2,2+t.SheetNames.map((function(e){return e.toLowerCase()})).indexOf(a.toLowerCase())),n.write_shift(4,0),n.write_shift(2,0),n.write_shift(4,0),n}(e,t);if(/^".*"$/.test(e))return function(e){var t=Kr(7);t.write_shift(4,3+2*e.length),t.write_shift(1,23),t.write_shift(2,e.length);var r=Kr(2*e.length);r.write_shift(2*e.length,e,"utf16le");var a=Kr(4);return a.write_shift(4,0),N([t,r,a])}(e);if(/^[+-]\d+$/.test(e))return Zo(parseInt(e,10));throw"Formula |"+e+"| not supported for XLSB"},qo={0:"BEEP",1:"OPEN",2:"OPEN.LINKS",3:"CLOSE.ALL",4:"SAVE",5:"SAVE.AS",6:"FILE.DELETE",7:"PAGE.SETUP",8:"PRINT",9:"PRINTER.SETUP",10:"QUIT",11:"NEW.WINDOW",12:"ARRANGE.ALL",13:"WINDOW.SIZE",14:"WINDOW.MOVE",15:"FULL",16:"CLOSE",17:"RUN",22:"SET.PRINT.AREA",23:"SET.PRINT.TITLES",24:"SET.PAGE.BREAK",25:"REMOVE.PAGE.BREAK",26:"FONT",27:"DISPLAY",28:"PROTECT.DOCUMENT",29:"PRECISION",30:"A1.R1C1",31:"CALCULATE.NOW",32:"CALCULATION",34:"DATA.FIND",35:"EXTRACT",36:"DATA.DELETE",37:"SET.DATABASE",38:"SET.CRITERIA",39:"SORT",40:"DATA.SERIES",41:"TABLE",42:"FORMAT.NUMBER",43:"ALIGNMENT",44:"STYLE",45:"BORDER",46:"CELL.PROTECTION",47:"COLUMN.WIDTH",48:"UNDO",49:"CUT",50:"COPY",51:"PASTE",52:"CLEAR",53:"PASTE.SPECIAL",54:"EDIT.DELETE",55:"INSERT",56:"FILL.RIGHT",57:"FILL.DOWN",61:"DEFINE.NAME",62:"CREATE.NAMES",63:"FORMULA.GOTO",64:"FORMULA.FIND",65:"SELECT.LAST.CELL",66:"SHOW.ACTIVE.CELL",67:"GALLERY.AREA",68:"GALLERY.BAR",69:"GALLERY.COLUMN",70:"GALLERY.LINE",71:"GALLERY.PIE",72:"GALLERY.SCATTER",73:"COMBINATION",74:"PREFERRED",75:"ADD.OVERLAY",76:"GRIDLINES",77:"SET.PREFERRED",78:"AXES",79:"LEGEND",80:"ATTACH.TEXT",81:"ADD.ARROW",82:"SELECT.CHART",83:"SELECT.PLOT.AREA",84:"PATTERNS",85:"MAIN.CHART",86:"OVERLAY",87:"SCALE",88:"FORMAT.LEGEND",89:"FORMAT.TEXT",90:"EDIT.REPEAT",91:"PARSE",92:"JUSTIFY",93:"HIDE",94:"UNHIDE",95:"WORKSPACE",96:"FORMULA",97:"FORMULA.FILL",98:"FORMULA.ARRAY",99:"DATA.FIND.NEXT",100:"DATA.FIND.PREV",101:"FORMULA.FIND.NEXT",102:"FORMULA.FIND.PREV",103:"ACTIVATE",104:"ACTIVATE.NEXT",105:"ACTIVATE.PREV",106:"UNLOCKED.NEXT",107:"UNLOCKED.PREV",108:"COPY.PICTURE",109:"SELECT",110:"DELETE.NAME",111:"DELETE.FORMAT",112:"VLINE",113:"HLINE",114:"VPAGE",115:"HPAGE",116:"VSCROLL",117:"HSCROLL",118:"ALERT",119:"NEW",120:"CANCEL.COPY",121:"SHOW.CLIPBOARD",122:"MESSAGE",124:"PASTE.LINK",125:"APP.ACTIVATE",126:"DELETE.ARROW",127:"ROW.HEIGHT",128:"FORMAT.MOVE",129:"FORMAT.SIZE",130:"FORMULA.REPLACE",131:"SEND.KEYS",132:"SELECT.SPECIAL",133:"APPLY.NAMES",134:"REPLACE.FONT",135:"FREEZE.PANES",136:"SHOW.INFO",137:"SPLIT",138:"ON.WINDOW",139:"ON.DATA",140:"DISABLE.INPUT",142:"OUTLINE",143:"LIST.NAMES",144:"FILE.CLOSE",145:"SAVE.WORKBOOK",146:"DATA.FORM",147:"COPY.CHART",148:"ON.TIME",149:"WAIT",150:"FORMAT.FONT",151:"FILL.UP",152:"FILL.LEFT",153:"DELETE.OVERLAY",155:"SHORT.MENUS",159:"SET.UPDATE.STATUS",161:"COLOR.PALETTE",162:"DELETE.STYLE",163:"WINDOW.RESTORE",164:"WINDOW.MAXIMIZE",166:"CHANGE.LINK",167:"CALCULATE.DOCUMENT",168:"ON.KEY",169:"APP.RESTORE",170:"APP.MOVE",171:"APP.SIZE",172:"APP.MINIMIZE",173:"APP.MAXIMIZE",174:"BRING.TO.FRONT",175:"SEND.TO.BACK",185:"MAIN.CHART.TYPE",186:"OVERLAY.CHART.TYPE",187:"SELECT.END",188:"OPEN.MAIL",189:"SEND.MAIL",190:"STANDARD.FONT",191:"CONSOLIDATE",192:"SORT.SPECIAL",193:"GALLERY.3D.AREA",194:"GALLERY.3D.COLUMN",195:"GALLERY.3D.LINE",196:"GALLERY.3D.PIE",197:"VIEW.3D",198:"GOAL.SEEK",199:"WORKGROUP",200:"FILL.GROUP",201:"UPDATE.LINK",202:"PROMOTE",203:"DEMOTE",204:"SHOW.DETAIL",206:"UNGROUP",207:"OBJECT.PROPERTIES",208:"SAVE.NEW.OBJECT",209:"SHARE",210:"SHARE.NAME",211:"DUPLICATE",212:"APPLY.STYLE",213:"ASSIGN.TO.OBJECT",214:"OBJECT.PROTECTION",215:"HIDE.OBJECT",216:"SET.EXTRACT",217:"CREATE.PUBLISHER",218:"SUBSCRIBE.TO",219:"ATTRIBUTES",220:"SHOW.TOOLBAR",222:"PRINT.PREVIEW",223:"EDIT.COLOR",224:"SHOW.LEVELS",225:"FORMAT.MAIN",226:"FORMAT.OVERLAY",227:"ON.RECALC",228:"EDIT.SERIES",229:"DEFINE.STYLE",240:"LINE.PRINT",243:"ENTER.DATA",249:"GALLERY.RADAR",250:"MERGE.STYLES",251:"EDITION.OPTIONS",252:"PASTE.PICTURE",253:"PASTE.PICTURE.LINK",254:"SPELLING",256:"ZOOM",259:"INSERT.OBJECT",260:"WINDOW.MINIMIZE",265:"SOUND.NOTE",266:"SOUND.PLAY",267:"FORMAT.SHAPE",268:"EXTEND.POLYGON",269:"FORMAT.AUTO",272:"GALLERY.3D.BAR",273:"GALLERY.3D.SURFACE",274:"FILL.AUTO",276:"CUSTOMIZE.TOOLBAR",277:"ADD.TOOL",278:"EDIT.OBJECT",279:"ON.DOUBLECLICK",280:"ON.ENTRY",281:"WORKBOOK.ADD",282:"WORKBOOK.MOVE",283:"WORKBOOK.COPY",284:"WORKBOOK.OPTIONS",285:"SAVE.WORKSPACE",288:"CHART.WIZARD",289:"DELETE.TOOL",290:"MOVE.TOOL",291:"WORKBOOK.SELECT",292:"WORKBOOK.ACTIVATE",293:"ASSIGN.TO.TOOL",295:"COPY.TOOL",296:"RESET.TOOL",297:"CONSTRAIN.NUMERIC",298:"PASTE.TOOL",302:"WORKBOOK.NEW",305:"SCENARIO.CELLS",306:"SCENARIO.DELETE",307:"SCENARIO.ADD",308:"SCENARIO.EDIT",309:"SCENARIO.SHOW",310:"SCENARIO.SHOW.NEXT",311:"SCENARIO.SUMMARY",312:"PIVOT.TABLE.WIZARD",313:"PIVOT.FIELD.PROPERTIES",314:"PIVOT.FIELD",315:"PIVOT.ITEM",316:"PIVOT.ADD.FIELDS",318:"OPTIONS.CALCULATION",319:"OPTIONS.EDIT",320:"OPTIONS.VIEW",321:"ADDIN.MANAGER",322:"MENU.EDITOR",323:"ATTACH.TOOLBARS",324:"VBAActivate",325:"OPTIONS.CHART",328:"VBA.INSERT.FILE",330:"VBA.PROCEDURE.DEFINITION",336:"ROUTING.SLIP",338:"ROUTE.DOCUMENT",339:"MAIL.LOGON",342:"INSERT.PICTURE",343:"EDIT.TOOL",344:"GALLERY.DOUGHNUT",350:"CHART.TREND",352:"PIVOT.ITEM.PROPERTIES",354:"WORKBOOK.INSERT",355:"OPTIONS.TRANSITION",356:"OPTIONS.GENERAL",370:"FILTER.ADVANCED",373:"MAIL.ADD.MAILER",374:"MAIL.DELETE.MAILER",375:"MAIL.REPLY",376:"MAIL.REPLY.ALL",377:"MAIL.FORWARD",378:"MAIL.NEXT.LETTER",379:"DATA.LABEL",380:"INSERT.TITLE",381:"FONT.PROPERTIES",382:"MACRO.OPTIONS",383:"WORKBOOK.HIDE",384:"WORKBOOK.UNHIDE",385:"WORKBOOK.DELETE",386:"WORKBOOK.NAME",388:"GALLERY.CUSTOM",390:"ADD.CHART.AUTOFORMAT",391:"DELETE.CHART.AUTOFORMAT",392:"CHART.ADD.DATA",393:"AUTO.OUTLINE",394:"TAB.ORDER",395:"SHOW.DIALOG",396:"SELECT.ALL",397:"UNGROUP.SHEETS",398:"SUBTOTAL.CREATE",399:"SUBTOTAL.REMOVE",400:"RENAME.OBJECT",412:"WORKBOOK.SCROLL",413:"WORKBOOK.NEXT",414:"WORKBOOK.PREV",415:"WORKBOOK.TAB.SPLIT",416:"FULL.SCREEN",417:"WORKBOOK.PROTECT",420:"SCROLLBAR.PROPERTIES",421:"PIVOT.SHOW.PAGES",422:"TEXT.TO.COLUMNS",423:"FORMAT.CHARTTYPE",424:"LINK.FORMAT",425:"TRACER.DISPLAY",430:"TRACER.NAVIGATE",431:"TRACER.CLEAR",432:"TRACER.ERROR",433:"PIVOT.FIELD.GROUP",434:"PIVOT.FIELD.UNGROUP",435:"CHECKBOX.PROPERTIES",436:"LABEL.PROPERTIES",437:"LISTBOX.PROPERTIES",438:"EDITBOX.PROPERTIES",439:"PIVOT.REFRESH",440:"LINK.COMBO",441:"OPEN.TEXT",442:"HIDE.DIALOG",443:"SET.DIALOG.FOCUS",444:"ENABLE.OBJECT",445:"PUSHBUTTON.PROPERTIES",446:"SET.DIALOG.DEFAULT",447:"FILTER",448:"FILTER.SHOW.ALL",449:"CLEAR.OUTLINE",450:"FUNCTION.WIZARD",451:"ADD.LIST.ITEM",452:"SET.LIST.ITEM",453:"REMOVE.LIST.ITEM",454:"SELECT.LIST.ITEM",455:"SET.CONTROL.VALUE",456:"SAVE.COPY.AS",458:"OPTIONS.LISTS.ADD",459:"OPTIONS.LISTS.DELETE",460:"SERIES.AXES",461:"SERIES.X",462:"SERIES.Y",463:"ERRORBAR.X",464:"ERRORBAR.Y",465:"FORMAT.CHART",466:"SERIES.ORDER",467:"MAIL.LOGOFF",468:"CLEAR.ROUTING.SLIP",469:"APP.ACTIVATE.MICROSOFT",470:"MAIL.EDIT.MAILER",471:"ON.SHEET",472:"STANDARD.WIDTH",473:"SCENARIO.MERGE",474:"SUMMARY.INFO",475:"FIND.FILE",476:"ACTIVE.CELL.FONT",477:"ENABLE.TIPWIZARD",478:"VBA.MAKE.ADDIN",480:"INSERTDATATABLE",481:"WORKGROUP.OPTIONS",482:"MAIL.SEND.MAILER",485:"AUTOCORRECT",489:"POST.DOCUMENT",491:"PICKLIST",493:"VIEW.SHOW",494:"VIEW.DEFINE",495:"VIEW.DELETE",509:"SHEET.BACKGROUND",510:"INSERT.MAP.OBJECT",511:"OPTIONS.MENONO",517:"MSOCHECKS",518:"NORMAL",519:"LAYOUT",520:"RM.PRINT.AREA",521:"CLEAR.PRINT.AREA",522:"ADD.PRINT.AREA",523:"MOVE.BRK",545:"HIDECURR.NOTE",546:"HIDEALL.NOTES",547:"DELETE.NOTE",548:"TRAVERSE.NOTES",549:"ACTIVATE.NOTES",620:"PROTECT.REVISIONS",621:"UNPROTECT.REVISIONS",647:"OPTIONS.ME",653:"WEB.PUBLISH",667:"NEWWEBQUERY",673:"PIVOT.TABLE.CHART",753:"OPTIONS.SAVE",755:"OPTIONS.SPELL",808:"HIDEALL.INKANNOTS"},Qo={0:"COUNT",1:"IF",2:"ISNA",3:"ISERROR",4:"SUM",5:"AVERAGE",6:"MIN",7:"MAX",8:"ROW",9:"COLUMN",10:"NA",11:"NPV",12:"STDEV",13:"DOLLAR",14:"FIXED",15:"SIN",16:"COS",17:"TAN",18:"ATAN",19:"PI",20:"SQRT",21:"EXP",22:"LN",23:"LOG10",24:"ABS",25:"INT",26:"SIGN",27:"ROUND",28:"LOOKUP",29:"INDEX",30:"REPT",31:"MID",32:"LEN",33:"VALUE",34:"TRUE",35:"FALSE",36:"AND",37:"OR",38:"NOT",39:"MOD",40:"DCOUNT",41:"DSUM",42:"DAVERAGE",43:"DMIN",44:"DMAX",45:"DSTDEV",46:"VAR",47:"DVAR",48:"TEXT",49:"LINEST",50:"TREND",51:"LOGEST",52:"GROWTH",53:"GOTO",54:"HALT",55:"RETURN",56:"PV",57:"FV",58:"NPER",59:"PMT",60:"RATE",61:"MIRR",62:"IRR",63:"RAND",64:"MATCH",65:"DATE",66:"TIME",67:"DAY",68:"MONTH",69:"YEAR",70:"WEEKDAY",71:"HOUR",72:"MINUTE",73:"SECOND",74:"NOW",75:"AREAS",76:"ROWS",77:"COLUMNS",78:"OFFSET",79:"ABSREF",80:"RELREF",81:"ARGUMENT",82:"SEARCH",83:"TRANSPOSE",84:"ERROR",85:"STEP",86:"TYPE",87:"ECHO",88:"SET.NAME",89:"CALLER",90:"DEREF",91:"WINDOWS",92:"SERIES",93:"DOCUMENTS",94:"ACTIVE.CELL",95:"SELECTION",96:"RESULT",97:"ATAN2",98:"ASIN",99:"ACOS",100:"CHOOSE",101:"HLOOKUP",102:"VLOOKUP",103:"LINKS",104:"INPUT",105:"ISREF",106:"GET.FORMULA",107:"GET.NAME",108:"SET.VALUE",109:"LOG",110:"EXEC",111:"CHAR",112:"LOWER",113:"UPPER",114:"PROPER",115:"LEFT",116:"RIGHT",117:"EXACT",118:"TRIM",119:"REPLACE",120:"SUBSTITUTE",121:"CODE",122:"NAMES",123:"DIRECTORY",124:"FIND",125:"CELL",126:"ISERR",127:"ISTEXT",128:"ISNUMBER",129:"ISBLANK",130:"T",131:"N",132:"FOPEN",133:"FCLOSE",134:"FSIZE",135:"FREADLN",136:"FREAD",137:"FWRITELN",138:"FWRITE",139:"FPOS",140:"DATEVALUE",141:"TIMEVALUE",142:"SLN",143:"SYD",144:"DDB",145:"GET.DEF",146:"REFTEXT",147:"TEXTREF",148:"INDIRECT",149:"REGISTER",150:"CALL",151:"ADD.BAR",152:"ADD.MENU",153:"ADD.COMMAND",154:"ENABLE.COMMAND",155:"CHECK.COMMAND",156:"RENAME.COMMAND",157:"SHOW.BAR",158:"DELETE.MENU",159:"DELETE.COMMAND",160:"GET.CHART.ITEM",161:"DIALOG.BOX",162:"CLEAN",163:"MDETERM",164:"MINVERSE",165:"MMULT",166:"FILES",167:"IPMT",168:"PPMT",169:"COUNTA",170:"CANCEL.KEY",171:"FOR",172:"WHILE",173:"BREAK",174:"NEXT",175:"INITIATE",176:"REQUEST",177:"POKE",178:"EXECUTE",179:"TERMINATE",180:"RESTART",181:"HELP",182:"GET.BAR",183:"PRODUCT",184:"FACT",185:"GET.CELL",186:"GET.WORKSPACE",187:"GET.WINDOW",188:"GET.DOCUMENT",189:"DPRODUCT",190:"ISNONTEXT",191:"GET.NOTE",192:"NOTE",193:"STDEVP",194:"VARP",195:"DSTDEVP",196:"DVARP",197:"TRUNC",198:"ISLOGICAL",199:"DCOUNTA",200:"DELETE.BAR",201:"UNREGISTER",204:"USDOLLAR",205:"FINDB",206:"SEARCHB",207:"REPLACEB",208:"LEFTB",209:"RIGHTB",210:"MIDB",211:"LENB",212:"ROUNDUP",213:"ROUNDDOWN",214:"ASC",215:"DBCS",216:"RANK",219:"ADDRESS",220:"DAYS360",221:"TODAY",222:"VDB",223:"ELSE",224:"ELSE.IF",225:"END.IF",226:"FOR.CELL",227:"MEDIAN",228:"SUMPRODUCT",229:"SINH",230:"COSH",231:"TANH",232:"ASINH",233:"ACOSH",234:"ATANH",235:"DGET",236:"CREATE.OBJECT",237:"VOLATILE",238:"LAST.ERROR",239:"CUSTOM.UNDO",240:"CUSTOM.REPEAT",241:"FORMULA.CONVERT",242:"GET.LINK.INFO",243:"TEXT.BOX",244:"INFO",245:"GROUP",246:"GET.OBJECT",247:"DB",248:"PAUSE",251:"RESUME",252:"FREQUENCY",253:"ADD.TOOLBAR",254:"DELETE.TOOLBAR",255:"User",256:"RESET.TOOLBAR",257:"EVALUATE",258:"GET.TOOLBAR",259:"GET.TOOL",260:"SPELLING.CHECK",261:"ERROR.TYPE",262:"APP.TITLE",263:"WINDOW.TITLE",264:"SAVE.TOOLBAR",265:"ENABLE.TOOL",266:"PRESS.TOOL",267:"REGISTER.ID",268:"GET.WORKBOOK",269:"AVEDEV",270:"BETADIST",271:"GAMMALN",272:"BETAINV",273:"BINOMDIST",274:"CHIDIST",275:"CHIINV",276:"COMBIN",277:"CONFIDENCE",278:"CRITBINOM",279:"EVEN",280:"EXPONDIST",281:"FDIST",282:"FINV",283:"FISHER",284:"FISHERINV",285:"FLOOR",286:"GAMMADIST",287:"GAMMAINV",288:"CEILING",289:"HYPGEOMDIST",290:"LOGNORMDIST",291:"LOGINV",292:"NEGBINOMDIST",293:"NORMDIST",294:"NORMSDIST",295:"NORMINV",296:"NORMSINV",297:"STANDARDIZE",298:"ODD",299:"PERMUT",300:"POISSON",301:"TDIST",302:"WEIBULL",303:"SUMXMY2",304:"SUMX2MY2",305:"SUMX2PY2",306:"CHITEST",307:"CORREL",308:"COVAR",309:"FORECAST",310:"FTEST",311:"INTERCEPT",312:"PEARSON",313:"RSQ",314:"STEYX",315:"SLOPE",316:"TTEST",317:"PROB",318:"DEVSQ",319:"GEOMEAN",320:"HARMEAN",321:"SUMSQ",322:"KURT",323:"SKEW",324:"ZTEST",325:"LARGE",326:"SMALL",327:"QUARTILE",328:"PERCENTILE",329:"PERCENTRANK",330:"MODE",331:"TRIMMEAN",332:"TINV",334:"MOVIE.COMMAND",335:"GET.MOVIE",336:"CONCATENATE",337:"POWER",338:"PIVOT.ADD.DATA",339:"GET.PIVOT.TABLE",340:"GET.PIVOT.FIELD",341:"GET.PIVOT.ITEM",342:"RADIANS",343:"DEGREES",344:"SUBTOTAL",345:"SUMIF",346:"COUNTIF",347:"COUNTBLANK",348:"SCENARIO.GET",349:"OPTIONS.LISTS.GET",350:"ISPMT",351:"DATEDIF",352:"DATESTRING",353:"NUMBERSTRING",354:"ROMAN",355:"OPEN.DIALOG",356:"SAVE.DIALOG",357:"VIEW.GET",358:"GETPIVOTDATA",359:"HYPERLINK",360:"PHONETIC",361:"AVERAGEA",362:"MAXA",363:"MINA",364:"STDEVPA",365:"VARPA",366:"STDEVA",367:"VARA",368:"BAHTTEXT",369:"THAIDAYOFWEEK",370:"THAIDIGIT",371:"THAIMONTHOFYEAR",372:"THAINUMSOUND",373:"THAINUMSTRING",374:"THAISTRINGLENGTH",375:"ISTHAIDIGIT",376:"ROUNDBAHTDOWN",377:"ROUNDBAHTUP",378:"THAIYEAR",379:"RTD",380:"CUBEVALUE",381:"CUBEMEMBER",382:"CUBEMEMBERPROPERTY",383:"CUBERANKEDMEMBER",384:"HEX2BIN",385:"HEX2DEC",386:"HEX2OCT",387:"DEC2BIN",388:"DEC2HEX",389:"DEC2OCT",390:"OCT2BIN",391:"OCT2HEX",392:"OCT2DEC",393:"BIN2DEC",394:"BIN2OCT",395:"BIN2HEX",396:"IMSUB",397:"IMDIV",398:"IMPOWER",399:"IMABS",400:"IMSQRT",401:"IMLN",402:"IMLOG2",403:"IMLOG10",404:"IMSIN",405:"IMCOS",406:"IMEXP",407:"IMARGUMENT",408:"IMCONJUGATE",409:"IMAGINARY",410:"IMREAL",411:"COMPLEX",412:"IMSUM",413:"IMPRODUCT",414:"SERIESSUM",415:"FACTDOUBLE",416:"SQRTPI",417:"QUOTIENT",418:"DELTA",419:"GESTEP",420:"ISEVEN",421:"ISODD",422:"MROUND",423:"ERF",424:"ERFC",425:"BESSELJ",426:"BESSELK",427:"BESSELY",428:"BESSELI",429:"XIRR",430:"XNPV",431:"PRICEMAT",432:"YIELDMAT",433:"INTRATE",434:"RECEIVED",435:"DISC",436:"PRICEDISC",437:"YIELDDISC",438:"TBILLEQ",439:"TBILLPRICE",440:"TBILLYIELD",441:"PRICE",442:"YIELD",443:"DOLLARDE",444:"DOLLARFR",445:"NOMINAL",446:"EFFECT",447:"CUMPRINC",448:"CUMIPMT",449:"EDATE",450:"EOMONTH",451:"YEARFRAC",452:"COUPDAYBS",453:"COUPDAYS",454:"COUPDAYSNC",455:"COUPNCD",456:"COUPNUM",457:"COUPPCD",458:"DURATION",459:"MDURATION",460:"ODDLPRICE",461:"ODDLYIELD",462:"ODDFPRICE",463:"ODDFYIELD",464:"RANDBETWEEN",465:"WEEKNUM",466:"AMORDEGRC",467:"AMORLINC",468:"CONVERT",724:"SHEETJS",469:"ACCRINT",470:"ACCRINTM",471:"WORKDAY",472:"NETWORKDAYS",473:"GCD",474:"MULTINOMIAL",475:"LCM",476:"FVSCHEDULE",477:"CUBEKPIMEMBER",478:"CUBESET",479:"CUBESETCOUNT",480:"IFERROR",481:"COUNTIFS",482:"SUMIFS",483:"AVERAGEIF",484:"AVERAGEIFS"},ec={2:1,3:1,10:0,15:1,16:1,17:1,18:1,19:0,20:1,21:1,22:1,23:1,24:1,25:1,26:1,27:2,30:2,31:3,32:1,33:1,34:0,35:0,38:1,39:2,40:3,41:3,42:3,43:3,44:3,45:3,47:3,48:2,53:1,61:3,63:0,65:3,66:3,67:1,68:1,69:1,70:1,71:1,72:1,73:1,74:0,75:1,76:1,77:1,79:2,80:2,83:1,85:0,86:1,89:0,90:1,94:0,95:0,97:2,98:1,99:1,101:3,102:3,105:1,106:1,108:2,111:1,112:1,113:1,114:1,117:2,118:1,119:4,121:1,126:1,127:1,128:1,129:1,130:1,131:1,133:1,134:1,135:1,136:2,137:2,138:2,140:1,141:1,142:3,143:4,144:4,161:1,162:1,163:1,164:1,165:2,172:1,175:2,176:2,177:3,178:2,179:1,184:1,186:1,189:3,190:1,195:3,196:3,197:1,198:1,199:3,201:1,207:4,210:3,211:1,212:2,213:2,214:1,215:1,225:0,229:1,230:1,231:1,232:1,233:1,234:1,235:3,244:1,247:4,252:2,257:1,261:1,271:1,273:4,274:2,275:2,276:2,277:3,278:3,279:1,280:3,281:3,282:3,283:1,284:1,285:2,286:4,287:3,288:2,289:4,290:3,291:3,292:3,293:4,294:1,295:3,296:1,297:3,298:1,299:2,300:3,301:3,302:4,303:2,304:2,305:2,306:2,307:2,308:2,309:3,310:2,311:2,312:2,313:2,314:2,315:2,316:4,325:2,326:2,327:2,328:2,331:2,332:2,337:2,342:1,343:1,346:2,347:1,350:4,351:3,352:1,353:2,360:1,368:1,369:1,370:1,371:1,372:1,373:1,374:1,375:1,376:1,377:1,378:1,382:3,385:1,392:1,393:1,396:2,397:2,398:2,399:1,400:1,401:1,402:1,403:1,404:1,405:1,406:1,407:1,408:1,409:1,410:1,414:4,415:1,416:1,417:2,420:1,421:1,422:2,424:1,425:2,426:2,427:2,428:2,430:3,438:3,439:3,440:3,443:2,444:2,445:2,446:2,447:6,448:6,449:2,450:2,464:2,468:3,476:2,479:1,480:2,65535:0};function tc(e){return"of:"==e.slice(0,3)&&(e=e.slice(3)),61==e.charCodeAt(0)&&61==(e=e.slice(1)).charCodeAt(0)&&(e=e.slice(1)),(e=(e=(e=(e=(e=e.replace(/COM\.MICROSOFT\./g,"")).replace(/\[((?:\.[A-Z]+[0-9]+)(?::\.[A-Z]+[0-9]+)?)\]/g,(function(e,t){return t.replace(/\./g,"")}))).replace(/\$'([^']|'')+'/g,(function(e){return e.slice(1)}))).replace(/\$([^\]\. #$]+)/g,(function(e,t){return t.match(/^([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])?(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})?$/)?e:t}))).replace(/\[.(#[A-Z]*[?!])\]/g,"$1")).replace(/[;~]/g,",").replace(/\|/g,";")}function rc(e){var t=(e=(e=e.replace(/\$'([^']|'')+'/g,(function(e){return e.slice(1)}))).replace(/\$([^\]\. #$]+)/g,(function(e,t){return t.match(/^([A-Z]{1,2}|[A-W][A-Z]{2}|X[A-E][A-Z]|XF[A-D])?(10[0-3]\d{4}|104[0-7]\d{3}|1048[0-4]\d{2}|10485[0-6]\d|104857[0-6]|[1-9]\d{0,5})?$/)?e:t}))).split(":");return[t[0].split(".")[0],t[0].split(".")[1]+(t.length>1?":"+(t[1].split(".")[1]||t[1].split(".")[0]):"")]}function ac(e){return e.replace(/!/,".").replace(/:/,":.")}var nc={},sc={},ic="undefined"!=typeof Map;function oc(e,t,r){var a=0,n=e.length;if(r){if(ic?r.has(t):Object.prototype.hasOwnProperty.call(r,t))for(var s=ic?r.get(t):r[t];a<s.length;++a)if(e[s[a]].t===t)return e.Count++,s[a]}else for(;a<n;++a)if(e[a].t===t)return e.Count++,a;return e[n]={t:t},e.Count++,e.Unique++,r&&(ic?(r.has(t)||r.set(t,[]),r.get(t).push(n)):(Object.prototype.hasOwnProperty.call(r,t)||(r[t]=[]),r[t].push(n))),n}function cc(e,t){var r={min:e+1,max:e+1},a=-1;return t.MDW&&(xi=t.MDW),null!=t.width?r.customWidth=1:null!=t.wpx?a=Ci(t.wpx):null!=t.wch&&(a=t.wch),a>-1?(r.width=Ii(a),r.customWidth=1):null!=t.width&&(r.width=t.width),t.hidden&&(r.hidden=!0),null!=t.level&&(r.outlineLevel=r.level=t.level),r}function lc(e,t){if(e){var r=[.7,.7,.75,.75,.3,.3];"xlml"==t&&(r=[1,1,1,1,.5,.5]),null==e.left&&(e.left=r[0]),null==e.right&&(e.right=r[1]),null==e.top&&(e.top=r[2]),null==e.bottom&&(e.bottom=r[3]),null==e.header&&(e.header=r[4]),null==e.footer&&(e.footer=r[5])}}function fc(e,t,r){var a=r.revssf[null!=t.z?t.z:"General"],n=60,s=e.length;if(null==a&&r.ssf)for(;n<392;++n)if(null==r.ssf[n]){xe(t.z,n),r.ssf[n]=t.z,r.revssf[t.z]=a=n;break}for(n=0;n!=s;++n)if(e[n].numFmtId===a)return n;return e[s]={numFmtId:a,fontId:0,fillId:0,borderId:0,xfId:0,applyNumberFormat:1},s}function hc(e,t,r,a,n,s,i){try{a.cellNF&&(e.z=G[t])}catch(e){if(a.WTF)throw e}if("z"!==e.t||a.cellStyles){if("d"===e.t&&"string"==typeof e.v&&(e.v=Xe(e.v)),(!a||!1!==a.cellText)&&"z"!==e.t)try{if(null==G[t]&&xe(ke[t]||"General",t),"e"===e.t)e.w=e.w||tn[e.v];else if(0===t)if("n"===e.t)(0|e.v)===e.v?e.w=e.v.toString(10):e.w=J(e.v);else if("d"===e.t){var o=We(e.v,!!i);e.w=(0|o)===o?o.toString(10):J(o)}else{if(void 0===e.v)return"";e.w=q(e.v,sc)}else"d"===e.t?e.w=be(t,We(e.v,!!i),sc):e.w=be(t,e.v,sc)}catch(e){if(a.WTF)throw e}if(a.cellStyles&&null!=r)try{e.s=s.Fills[r],e.s.fgColor&&e.s.fgColor.theme&&!e.s.fgColor.rgb&&(e.s.fgColor.rgb=Ei(n.themeElements.clrScheme[e.s.fgColor.theme].rgb,e.s.fgColor.tint||0),a.WTF&&(e.s.fgColor.raw_rgb=n.themeElements.clrScheme[e.s.fgColor.theme].rgb)),e.s.bgColor&&e.s.bgColor.theme&&(e.s.bgColor.rgb=Ei(n.themeElements.clrScheme[e.s.bgColor.theme].rgb,e.s.bgColor.tint||0),a.WTF&&(e.s.bgColor.raw_rgb=n.themeElements.clrScheme[e.s.bgColor.theme].rgb))}catch(e){if(a.WTF&&s.Fills)throw e}}}function uc(e,t,r){if(e&&e["!ref"]){var a=ua(e["!ref"]);if(a.e.c<a.s.c||a.e.r<a.s.r)throw new Error("Bad range ("+r+"): "+e["!ref"])}}var dc=/<(?:\w+:)?mergeCell ref=["'][A-Z0-9:]+['"]\s*[\/]?>/g,pc=/<(?:\w+:)?hyperlink [^<>]*>/gm,mc=/"(\w*:\w*)"/,vc=/<(?:\w+:)?col\b[^<>]*[\/]?>/g,gc=/<(?:\w+:)?autoFilter[^>]*/g,bc=/<(?:\w+:)?pageMargins[^<>]*\/>/g,wc=/<(?:\w+:)?sheetPr\b[^<>]*?\/>/;function Tc(e,t,r,a){var n=Ft(e);r.Sheets[a]||(r.Sheets[a]={}),n.codeName&&(r.Sheets[a].CodeName=Lt(Zt(n.codeName)))}var yc=["objects","scenarios","selectLockedCells","selectUnlockedCells"],Ec=["formatColumns","formatRows","formatCells","insertColumns","insertRows","insertHyperlinks","deleteColumns","deleteRows","sort","autoFilter","pivotTables"],kc=/<(?:\w:)?sheetView(?:[^<>a-z][^<>]*)?\/?>/g;function _c(e,t,r,a,n,s,i){if(e.c&&r["!comments"].push([t,e.c]),(void 0===e.v||"z"===e.t&&!(a||{}).sheetStubs)&&"string"!=typeof e.f&&void 0===e.z)return"";var o="",c=e.t,l=e.v;if("z"!==e.t)switch(e.t){case"b":o=e.v?"1":"0";break;case"n":isNaN(e.v)?(e.t="e",o=tn[e.v=36]):isFinite(e.v)?o=""+e.v:(e.t="e",o=tn[e.v=7]);break;case"e":o=tn[e.v];break;case"d":if(a&&a.cellDates){var f=Xe(e.v,i);o=f.toISOString(),f.getUTCFullYear()<1900&&(o=o.slice(o.indexOf("T")+1).replace("Z",""))}else(e=Ke(e)).t="n",o=""+(e.v=We(Xe(e.v,i),i));void 0===e.z&&(e.z=G[14]);break;default:o=e.v}var h="z"==e.t||null==e.v?"":ar("v",Wt(o)),u={r:t},d=fc(a.cellXfs,e,a);switch(0!==d&&(u.s=d),e.t){case"n":case"z":break;case"d":u.t="d";break;case"b":u.t="b";break;case"e":u.t="e";break;default:if(null==e.v){delete e.t;break}if(e.v.length>32767)throw new Error("Text length must not exceed 32767 characters");if(a&&a.bookSST){h=ar("v",""+oc(a.Strings,e.v,a.revStrings)),u.t="s";break}u.t="str"}if(e.t!=c&&(e.t=c,e.v=l),"string"==typeof e.f&&e.f){var p=e.F&&e.F.slice(0,t.length)==t?{t:"array",ref:e.F}:null;h=sr("f",Wt(e.f),p)+(null!=e.v?h:"")}return e.l&&(e.l.display=Wt(o),r["!links"].push([t,e.l])),e.D&&(u.cm=1),sr("c",h,u)}var Sc=function(){var e=/<(?:\w+:)?c[ \/>]/,t=/<\/(?:\w+:)?row>/,r=/r=["']([^"']*)["']/,a=/ref=["']([^"']*)["']/;return function(n,s,i,o,c,l,f){for(var h,u,d,p,m,v=0,g="",b=[],w=[],T=0,y=0,E=0,k="",_=0,S=0,x=0,A=0,C=Array.isArray(l.CellXf),I=[],O=[],R=null!=s["!data"],N=[],F={},D=!1,P=!!i.sheetStubs,M=!!((f||{}).WBProps||{}).date1904,L=n.split(t),U=0,B=L.length;U!=B;++U){var W=(g=L[U].trim()).length;if(0!==W){var z=0;e:for(v=0;v<W;++v)switch(g[v]){case">":if("/"!=g[v-1]){++v;break e}if(i&&i.cellStyles){if(_=null!=(u=Ft(g.slice(z,v),!0)).r?parseInt(u.r,10):_+1,S=-1,i.sheetRows&&i.sheetRows<_)continue;F={},D=!1,u.ht&&(D=!0,F.hpt=parseFloat(u.ht),F.hpx=Pi(F.hpt)),u.hidden&&Gt(u.hidden)&&(D=!0,F.hidden=!0),null!=u.outlineLevel&&(D=!0,F.level=+u.outlineLevel),D&&(N[_-1]=F)}break;case"<":z=v}if(z>=v)break;if(_=null!=(u=Ft(g.slice(z,v),!0)).r?parseInt(u.r,10):_+1,S=-1,!(i.sheetRows&&i.sheetRows<_)){i.nodim||(o.s.r>_-1&&(o.s.r=_-1),o.e.r<_-1&&(o.e.r=_-1)),i&&i.cellStyles&&(F={},D=!1,u.ht&&(D=!0,F.hpt=parseFloat(u.ht),F.hpx=Pi(F.hpt)),u.hidden&&Gt(u.hidden)&&(D=!0,F.hidden=!0),null!=u.outlineLevel&&(D=!0,F.level=+u.outlineLevel),D&&(N[_-1]=F)),b=g.slice(v).split(e);for(var H=0;H!=b.length&&"<"==b[H].trim().charAt(0);++H);for(b=b.slice(H),v=0;v!=b.length;++v)if(0!==(g=b[v].trim()).length){if(w=g.match(r),T=v,y=0,E=0,g="<c "+("<"==g.slice(0,1)?">":"")+g,null!=w&&2===w.length){for(T=0,k=w[1],y=0;y!=k.length&&!((E=k.charCodeAt(y)-64)<1||E>26);++y)T=26*T+E;S=--T}else++S;for(y=0;y!=g.length&&62!==g.charCodeAt(y);++y);if(++y,(u=Ft(g.slice(0,y),!0)).r||(u.r=oa({r:_-1,c:S})),k=g.slice(y),h={t:""},null!=(w=ht(k,"v"))&&""!==w[1]&&(h.v=Lt(w[1])),i.cellFormula){if(null!=(w=ht(k,"f"))){if(""==w[1])w[0].indexOf('t="shared"')>-1&&O[(p=Ft(w[0])).si]&&(h.f=po(O[p.si][1],O[p.si][2],u.r));else if(h.f=Lt(Zt(w[1]),!0),i.xlfn||(h.f=mo(h.f)),w[0].indexOf('t="array"')>-1)h.F=(k.match(a)||[])[1],h.F.indexOf(":")>-1&&I.push([ua(h.F),h.F]);else if(w[0].indexOf('t="shared"')>-1){p=Ft(w[0]);var V=Lt(Zt(w[1]));i.xlfn||(V=mo(V)),O[parseInt(p.si,10)]=[p,V,u.r]}}else(w=k.match(/<f[^<>]*\/>/))&&O[(p=Ft(w[0])).si]&&(h.f=po(O[p.si][1],O[p.si][2],u.r));var $=ia(u.r);for(y=0;y<I.length;++y)$.r>=I[y][0].s.r&&$.r<=I[y][0].e.r&&$.c>=I[y][0].s.c&&$.c<=I[y][0].e.c&&(h.F=I[y][1])}if(null==u.t&&void 0===h.v)if(h.f||h.F)h.v=0,h.t="n";else{if(!P)continue;h.t="z"}else h.t=u.t||"n";switch(o.s.c>S&&(o.s.c=S),o.e.c<S&&(o.e.c=S),h.t){case"n":if(""==h.v||null==h.v){if(!P)continue;h.t="z"}else h.v=parseFloat(h.v);break;case"s":if(void 0===h.v){if(!P)continue;h.t="z"}else d=nc[parseInt(h.v,10)],h.v=d.t,h.r=d.r,i.cellHTML&&(h.h=d.h);break;case"str":h.t="s",h.v=null!=h.v?Lt(Zt(h.v),!0):"",i.cellHTML&&(h.h=Vt(h.v));break;case"inlineStr":w=ht(k,"is"),h.t="s",null!=w&&(d=si(w[1]))?(h.v=d.t,i.cellHTML&&(h.h=d.h)):h.v="";break;case"b":h.v=Gt(h.v);break;case"d":i.cellDates?h.v=Xe(h.v,M):(h.v=We(Xe(h.v,M),M),h.t="n");break;case"e":i&&!1===i.cellText||(h.w=h.v),h.v=rn[h.v]}if(x=A=0,m=null,C&&void 0!==u.s&&null!=(m=l.CellXf[u.s])&&(null!=m.numFmtId&&(x=m.numFmtId),i.cellStyles&&null!=m.fillId&&(A=m.fillId)),hc(h,x,A,i,c,l,M),i.cellDates&&C&&"n"==h.t&&me(G[x])&&(h.v=ze(h.v+(M?1462:0)),h.t="number"==typeof h.v?"n":"d"),u.cm&&i.xlmeta){var X=(i.xlmeta.Cell||[])[+u.cm-1];X&&"XLDAPR"==X.type&&(h.D=!0)}var j;i.nodim&&(j=ia(u.r),o.s.r>j.r&&(o.s.r=j.r),o.e.r<j.r&&(o.e.r=j.r)),R?(j=ia(u.r),s["!data"][j.r]||(s["!data"][j.r]=[]),s["!data"][j.r][j.c]=h):s[u.r]=h}}}}N.length>0&&(s["!rows"]=N)}}();function xc(e,t,r,a){var n,s=[At,sr("worksheet",null,{xmlns:fr[0],"xmlns:r":lr.r})],i=r.SheetNames[e],o="",c=r.Sheets[i];null==c&&(c={});var l=c["!ref"]||"A1",f=ua(l);if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+l+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575),l=la(f)}a||(a={}),c["!comments"]=[];var h=[];!function(e,t,r,a,n){var s=!1,i={},o=null;if("xlsx"!==a.bookType&&t.vbaraw){var c=t.SheetNames[r];try{t.Workbook&&(c=t.Workbook.Sheets[r].CodeName||c)}catch(e){}s=!0,i.codeName=Jt(Wt(c))}if(e&&e["!outline"]){var l={summaryBelow:1,summaryRight:1};e["!outline"].above&&(l.summaryBelow=0),e["!outline"].left&&(l.summaryRight=0),o=(o||"")+sr("outlinePr",null,l)}(s||o)&&(n[n.length]=sr("sheetPr",o,i))}(c,r,e,t,s),s[s.length]=sr("dimension",null,{ref:l}),s[s.length]=function(e,t,r,a){var n={workbookViewId:"0"};return(((a||{}).Workbook||{}).Views||[])[0]&&(n.rightToLeft=a.Workbook.Views[0].RTL?"1":"0"),sr("sheetViews",sr("sheetView",null,n),{})}(0,0,0,r),t.sheetFormat&&(s[s.length]=sr("sheetFormatPr",null,{defaultRowHeight:t.sheetFormat.defaultRowHeight||"16",baseColWidth:t.sheetFormat.baseColWidth||"10",outlineLevelRow:t.sheetFormat.outlineLevelRow||"7"})),null!=c["!cols"]&&c["!cols"].length>0&&(s[s.length]=function(e,t){for(var r,a=["<cols>"],n=0;n!=t.length;++n)(r=t[n])&&(a[a.length]=sr("col",null,cc(n,r)));return a[a.length]="</cols>",a.join("")}(0,c["!cols"])),s[n=s.length]="<sheetData/>",c["!links"]=[],null!=c["!ref"]&&(o=function(e,t,r,a){var n,s,i=[],o=[],c=ua(e["!ref"]),l="",f="",h=[],u=0,d=0,p=e["!rows"],m=null!=e["!data"],v={r:f},g=-1,b=(((a||{}).Workbook||{}).WBProps||{}).date1904;for(d=c.s.c;d<=c.e.c;++d)h[d]=sa(d);for(u=c.s.r;u<=c.e.r;++u){for(o=[],f=aa(u),d=c.s.c;d<=c.e.c;++d){n=h[d]+f;var w=m?(e["!data"][u]||[])[d]:e[n];void 0!==w&&null!=(l=_c(w,n,e,t,0,0,b))&&o.push(l)}(o.length>0||p&&p[u])&&(v={r:f},p&&p[u]&&((s=p[u]).hidden&&(v.hidden=1),g=-1,s.hpx?g=Di(s.hpx):s.hpt&&(g=s.hpt),g>-1&&(v.ht=g,v.customHeight=1),s.level&&(v.outlineLevel=s.level)),i[i.length]=sr("row",o.join(""),v))}if(p)for(;u<p.length;++u)p&&p[u]&&(v={r:u+1},(s=p[u]).hidden&&(v.hidden=1),g=-1,s.hpx?g=Di(s.hpx):s.hpt&&(g=s.hpt),g>-1&&(v.ht=g,v.customHeight=1),s.level&&(v.outlineLevel=s.level),i[i.length]=sr("row","",v));return i.join("")}(c,t,0,r),o.length>0&&(s[s.length]=o)),s.length>n+1&&(s[s.length]="</sheetData>",s[n]=s[n].replace("/>",">")),c["!protect"]&&(s[s.length]=function(e){var t={sheet:1};return yc.forEach((function(r){null!=e[r]&&e[r]&&(t[r]="1")})),Ec.forEach((function(r){null==e[r]||e[r]||(t[r]="0")})),e.password&&(t.password=bi(e.password).toString(16).toUpperCase()),sr("sheetProtection",null,t)}(c["!protect"])),null!=c["!autofilter"]&&(s[s.length]=function(e,t,r,a){var n="string"==typeof e.ref?e.ref:la(e.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var s=r.Workbook.Names,i=ca(n);i.s.r==i.e.r&&(i.e.r=ca(t["!ref"]).e.r,n=la(i));for(var o=0;o<s.length;++o){var c=s[o];if("_xlnm._FilterDatabase"==c.Name&&c.Sheet==a){c.Ref=ha(r.SheetNames[a])+"!"+fa(n);break}}return o==s.length&&s.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:"'"+r.SheetNames[a]+"'!"+n}),sr("autoFilter",null,{ref:n})}(c["!autofilter"],c,r,e)),null!=c["!merges"]&&c["!merges"].length>0&&(s[s.length]=function(e){if(0===e.length)return"";for(var t='<mergeCells count="'+e.length+'">',r=0;r!=e.length;++r)t+='<mergeCell ref="'+la(e[r])+'"/>';return t+"</mergeCells>"}(c["!merges"]));var u,d,p=-1,m=-1;return c["!links"].length>0&&(s[s.length]="<hyperlinks>",c["!links"].forEach((function(e){e[1].Target&&(u={ref:e[0]},"#"!=e[1].Target.charAt(0)&&(m=un(a,-1,Wt(e[1].Target).replace(/#[\s\S]*$/,""),cn.HLINK),u["r:id"]="rId"+m),(p=e[1].Target.indexOf("#"))>-1&&(u.location=Wt(e[1].Target.slice(p+1))),e[1].Tooltip&&(u.tooltip=Wt(e[1].Tooltip)),u.display=e[1].display,s[s.length]=sr("hyperlink",null,u))})),s[s.length]="</hyperlinks>"),delete c["!links"],null!=c["!margins"]&&(s[s.length]=(lc(d=c["!margins"]),sr("pageMargins",null,d))),t&&!t.ignoreEC&&null!=t.ignoreEC||(s[s.length]=ar("ignoredErrors",sr("ignoredError",null,{numberStoredAsText:1,sqref:l}))),h.length>0&&(m=un(a,-1,"../drawings/drawing"+(e+1)+".xml",cn.DRAW),s[s.length]=sr("drawing",null,{"r:id":"rId"+m}),c["!drawing"]=h),c["!comments"].length>0&&(m=un(a,-1,"../drawings/vmlDrawing"+(e+1)+".vml",cn.VML),s[s.length]=sr("legacyDrawing",null,{"r:id":"rId"+m}),c["!legacy"]=m),s.length>1&&(s[s.length]="</worksheet>",s[1]=s[1].replace("/>",">")),s.join("")}function Ac(e,t,r,a){var n=function(e,t,r){var a=Kr(145),n=(r["!rows"]||[])[e]||{};a.write_shift(4,e),a.write_shift(4,0);var s=320;n.hpx?s=20*Di(n.hpx):n.hpt&&(s=20*n.hpt),a.write_shift(2,s),a.write_shift(1,0);var i=0;n.level&&(i|=n.level),n.hidden&&(i|=16),(n.hpx||n.hpt)&&(i|=32),a.write_shift(1,i),a.write_shift(1,0);var o=0,c=a.l;a.l+=4;for(var l={r:e,c:0},f=null!=r["!data"],h=0;h<16;++h)if(!(t.s.c>h+1<<10||t.e.c<h<<10)){for(var u=-1,d=-1,p=h<<10;p<h+1<<10;++p)l.c=p,(f?(r["!data"][l.r]||[])[l.c]:r[oa(l)])&&(u<0&&(u=p),d=p);u<0||(++o,a.write_shift(4,u),a.write_shift(4,d))}var m=a.l;return a.l=c,a.write_shift(4,o),a.l=m,a.length>a.l?a.slice(0,a.l):a}(a,r,t);(n.length>17||(t["!rows"]||[])[a])&&Jr(e,0,n)}var Cc=La,Ic=Ua;function Oc(e,t,r){return null==r&&(r=Kr(9)),_a(t,r),r.write_shift(1,e.v),r}function Rc(e,t,r){return null==r&&(r=Kr(8)),xa(t,r),r.write_shift(1,e.v),r.write_shift(2,0),r.write_shift(1,0),r}function Nc(e){return[Sa(e),Ba(e),"n"]}var Fc=La,Dc=Ua,Pc=["left","right","top","bottom","header","footer"];function Mc(e,t,r,a,n,s,i,o){var c={r:r,c:a};if(t.c&&s["!comments"].push([oa(c),t.c]),void 0===t.v)return!1;var l="";switch(t.t){case"b":l=t.v?"1":"0";break;case"d":(t=Ke(t)).z=t.z||G[14],t.v=We(Xe(t.v,o),o),t.t="n";break;case"n":case"e":l=""+t.v;break;default:l=t.v}switch(c.s=fc(n.cellXfs,t,n),t.l&&s["!links"].push([oa(c),t.l]),t.t){case"s":case"str":return n.bookSST?(l=oc(n.Strings,null==t.v?"":String(t.v),n.revStrings),c.t="s",c.v=l,i?Jr(e,18,function(e,t,r){return null==r&&(r=Kr(8)),xa(t,r),r.write_shift(4,t.v),r}(0,c)):Jr(e,7,function(e,t,r){return null==r&&(r=Kr(12)),_a(t,r),r.write_shift(4,t.v),r}(0,c))):(c.t="str",i?Jr(e,17,function(e,t,r){var a=null==e.v?"":String(e.v);return null==r&&(r=Kr(8+4*a.length)),xa(t,r),wa(a,r),r.length>r.l?r.slice(0,r.l):r}(t,c)):Jr(e,6,function(e,t,r){var a=null==e.v?"":String(e.v);return null==r&&(r=Kr(12+4*e.v.length)),_a(t,r),wa(a,r),r.length>r.l?r.slice(0,r.l):r}(t,c))),!0;case"n":return t.v==(0|t.v)&&t.v>-1e3&&t.v<1e3?i?Jr(e,13,function(e,t,r){return null==r&&(r=Kr(8)),xa(t,r),Pa(e.v,r),r}(t,c)):Jr(e,2,function(e,t,r){return null==r&&(r=Kr(12)),_a(t,r),Pa(e.v,r),r}(t,c)):isNaN(t.v)?i?Jr(e,14,Rc({t:"e",v:36},c)):Jr(e,3,Oc({t:"e",v:36},c)):isFinite(t.v)?i?Jr(e,16,function(e,t,r){return null==r&&(r=Kr(12)),xa(t,r),Wa(e.v,r),r}(t,c)):Jr(e,5,function(e,t,r){return null==r&&(r=Kr(16)),_a(t,r),Wa(e.v,r),r}(t,c)):i?Jr(e,14,Rc({t:"e",v:7},c)):Jr(e,3,Oc({t:"e",v:7},c)),!0;case"b":return c.t="b",i?Jr(e,15,function(e,t,r){return null==r&&(r=Kr(5)),xa(t,r),r.write_shift(1,e.v?1:0),r}(t,c)):Jr(e,4,function(e,t,r){return null==r&&(r=Kr(9)),_a(t,r),r.write_shift(1,e.v?1:0),r}(t,c)),!0;case"e":return c.t="e",i?Jr(e,14,Rc(t,c)):Jr(e,3,Oc(t,c)),!0}return i?Jr(e,12,function(e,t,r){return null==r&&(r=Kr(4)),xa(t,r)}(0,c)):Jr(e,1,function(e,t,r){return null==r&&(r=Kr(8)),_a(t,r)}(0,c)),!0}function Lc(e,t,r,a){var n=Zr(),s=r.SheetNames[e],i=r.Sheets[s]||{},o=s;try{r&&r.Workbook&&(o=r.Workbook.Sheets[e].CodeName||o)}catch(e){}var c,l,f=ua(i["!ref"]||"A1");if(f.e.c>16383||f.e.r>1048575){if(t.WTF)throw new Error("Range "+(i["!ref"]||"A1")+" exceeds format limit A1:XFD1048576");f.e.c=Math.min(f.e.c,16383),f.e.r=Math.min(f.e.c,1048575)}return i["!links"]=[],i["!comments"]=[],Jr(n,129),(r.vbaraw||i["!outline"])&&Jr(n,147,function(e,t,r){null==r&&(r=Kr(84+4*e.length));var a=192;t&&(t.above&&(a&=-65),t.left&&(a&=-129)),r.write_shift(1,a);for(var n=1;n<3;++n)r.write_shift(1,0);return za({auto:1},r),r.write_shift(-4,-1),r.write_shift(-4,-1),Ca(e,r),r.slice(0,r.l)}(o,i["!outline"])),Jr(n,148,Ic(f)),function(e,t,r){Jr(e,133),Jr(e,137,function(e,t,r){null==r&&(r=Kr(30));var a=924;return(((t||{}).Views||[])[0]||{}).RTL&&(a|=32),r.write_shift(2,a),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(1,0),r.write_shift(1,0),r.write_shift(2,0),r.write_shift(2,100),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(2,0),r.write_shift(4,0),r}(0,r)),Jr(e,138),Jr(e,134)}(n,0,r.Workbook),function(e,t){t&&t["!cols"]&&(Jr(e,390),t["!cols"].forEach((function(t,r){t&&Jr(e,60,function(e,t,r){null==r&&(r=Kr(18));var a=cc(e,t);r.write_shift(-4,e),r.write_shift(-4,e),r.write_shift(4,256*(a.width||10)),r.write_shift(4,0);var n=0;return t.hidden&&(n|=1),"number"==typeof a.width&&(n|=2),t.level&&(n|=t.level<<8),r.write_shift(2,n),r}(r,t))})),Jr(e,391))}(n,i),function(e,t,r,a,n){var s,i=ua(t["!ref"]||"A1"),o="",c=[],l=(((n||{}).Workbook||{}).WBProps||{}).date1904;Jr(e,145);var f=null!=t["!data"],h=i.e.r;t["!rows"]&&(h=Math.max(i.e.r,t["!rows"].length-1));for(var u=i.s.r;u<=h;++u){o=aa(u),Ac(e,t,i,u);var d=!1;if(u<=i.e.r)for(var p=i.s.c;p<=i.e.c;++p){u===i.s.r&&(c[p]=sa(p)),s=c[p]+o;var m=f?(t["!data"][u]||[])[p]:t[s];d=!!m&&Mc(e,m,u,p,a,t,d,l)}}Jr(e,146)}(n,i,0,t,r),function(e,t){var r,a;t["!protect"]&&Jr(e,535,(r=t["!protect"],null==a&&(a=Kr(66)),a.write_shift(2,r.password?bi(r.password):0),a.write_shift(4,1),[["objects",!1],["scenarios",!1],["formatCells",!0],["formatColumns",!0],["formatRows",!0],["insertColumns",!0],["insertRows",!0],["insertHyperlinks",!0],["deleteColumns",!0],["deleteRows",!0],["selectLockedCells",!1],["sort",!0],["autoFilter",!0],["pivotTables",!0],["selectUnlockedCells",!1]].forEach((function(e){e[1]?a.write_shift(4,null==r[e[0]]||r[e[0]]?0:1):a.write_shift(4,null!=r[e[0]]&&r[e[0]]?0:1)})),a))}(n,i),function(e,t,r,a){if(t["!autofilter"]){var n=t["!autofilter"],s="string"==typeof n.ref?n.ref:la(n.ref);r.Workbook||(r.Workbook={Sheets:[]}),r.Workbook.Names||(r.Workbook.Names=[]);var i=r.Workbook.Names,o=ca(s);o.s.r==o.e.r&&(o.e.r=ca(t["!ref"]).e.r,s=la(o));for(var c=0;c<i.length;++c){var l=i[c];if("_xlnm._FilterDatabase"==l.Name&&l.Sheet==a){l.Ref=ha(r.SheetNames[a])+"!"+fa(s);break}}c==i.length&&i.push({Name:"_xlnm._FilterDatabase",Sheet:a,Ref:ha(r.SheetNames[a])+"!"+fa(s)}),Jr(e,161,Ua(ua(s))),Jr(e,162)}}(n,i,r,e),function(e,t){var r,a;t&&t["!merges"]&&(Jr(e,177,(r=t["!merges"].length,null==a&&(a=Kr(4)),a.write_shift(4,r),a)),t["!merges"].forEach((function(t){Jr(e,176,Dc(t))})),Jr(e,178))}(n,i),function(e,t,r){t["!links"].forEach((function(t){if(t[1].Target){var a=un(r,-1,t[1].Target.replace(/#[\s\S]*$/,""),cn.HLINK);Jr(e,494,function(e,t){var r=Kr(50+4*(e[1].Target.length+(e[1].Tooltip||"").length));Ua({s:ia(e[0]),e:ia(e[0])},r),Fa("rId"+t,r);var a=e[1].Target.indexOf("#");return wa((-1==a?"":e[1].Target.slice(a+1))||"",r),wa(e[1].Tooltip||"",r),wa("",r),r.slice(0,r.l)}(t,a))}})),delete t["!links"]}(n,i,a),i["!margins"]&&Jr(n,476,(c=i["!margins"],null==l&&(l=Kr(48)),lc(c),Pc.forEach((function(e){Wa(c[e],l)})),l)),t&&!t.ignoreEC&&null!=t.ignoreEC||function(e,t){var r,a;t&&t["!ref"]&&(Jr(e,648),Jr(e,649,(r=ua(t["!ref"]),(a=Kr(24)).write_shift(4,4),a.write_shift(4,1),Ua(r,a),a)),Jr(e,650))}(n,i),function(e,t,r,a){if(t["!comments"].length>0){var n=un(a,-1,"../drawings/vmlDrawing"+(r+1)+".vml",cn.VML);Jr(e,551,Fa("rId"+n)),t["!legacy"]=n}}(n,i,e,a),Jr(n,130),n.end()}var Uc=[["allowRefreshQuery",!1,"bool"],["autoCompressPictures",!0,"bool"],["backupFile",!1,"bool"],["checkCompatibility",!1,"bool"],["CodeName",""],["date1904",!1,"bool"],["defaultThemeVersion",0,"int"],["filterPrivacy",!1,"bool"],["hidePivotFieldList",!1,"bool"],["promptedSolutions",!1,"bool"],["publishItems",!1,"bool"],["refreshAllConnections",!1,"bool"],["saveExternalLinkValues",!0,"bool"],["showBorderUnselectedTables",!0,"bool"],["showInkAnnotation",!0,"bool"],["showObjects","all"],["showPivotChartFilter",!1,"bool"],["updateLinks","userSet"]],Bc=[["activeTab",0,"int"],["autoFilterDateGrouping",!0,"bool"],["firstSheet",0,"int"],["minimized",!1,"bool"],["showHorizontalScroll",!0,"bool"],["showSheetTabs",!0,"bool"],["showVerticalScroll",!0,"bool"],["tabRatio",600,"int"],["visibility","visible"]],Wc=[],zc=[["calcCompleted","true"],["calcMode","auto"],["calcOnSave","true"],["concurrentCalc","true"],["fullCalcOnLoad","false"],["fullPrecision","true"],["iterate","false"],["iterateCount","100"],["iterateDelta","0.001"],["refMode","A1"]];function Hc(e,t){for(var r=0;r!=e.length;++r)for(var a=e[r],n=0;n!=t.length;++n){var s=t[n];if(null==a[s[0]])a[s[0]]=s[1];else switch(s[2]){case"bool":"string"==typeof a[s[0]]&&(a[s[0]]=Gt(a[s[0]]));break;case"int":"string"==typeof a[s[0]]&&(a[s[0]]=parseInt(a[s[0]],10))}}}function Vc(e,t){for(var r=0;r!=t.length;++r){var a=t[r];if(null==e[a[0]])e[a[0]]=a[1];else switch(a[2]){case"bool":"string"==typeof e[a[0]]&&(e[a[0]]=Gt(e[a[0]]));break;case"int":"string"==typeof e[a[0]]&&(e[a[0]]=parseInt(e[a[0]],10))}}}function $c(e){Vc(e.WBProps,Uc),Vc(e.CalcPr,zc),Hc(e.WBView,Bc),Hc(e.Sheets,Wc),sc.date1904=Gt(e.WBProps.date1904)}var Gc=":][*?/\\".split("");function Xc(e,t){try{if(""==e)throw new Error("Sheet name cannot be blank");if(e.length>31)throw new Error("Sheet name cannot exceed 31 chars");if(39==e.charCodeAt(0)||39==e.charCodeAt(e.length-1))throw new Error("Sheet name cannot start or end with apostrophe (')");if("history"==e.toLowerCase())throw new Error("Sheet name cannot be 'History'");Gc.forEach((function(t){if(-1!=e.indexOf(t))throw new Error("Sheet name cannot contain : \\ / ? * [ ]")}))}catch(e){if(t)return!1;throw e}return!0}function jc(e){if(!e||!e.SheetNames||!e.Sheets)throw new Error("Invalid Workbook");if(!e.SheetNames.length)throw new Error("Workbook is empty");var t,r,a,n=e.Workbook&&e.Workbook.Sheets||[];t=e.SheetNames,r=n,a=!!e.vbaraw,t.forEach((function(e,n){Xc(e);for(var s=0;s<n;++s)if(e==t[s])throw new Error("Duplicate Sheet Name: "+e);if(a){var i=r&&r[n]&&r[n].CodeName||e;if(95==i.charCodeAt(0)&&i.length>22)throw new Error("Bad Code Name: Worksheet"+i)}}));for(var s=0;s<e.SheetNames.length;++s)uc(e.Sheets[e.SheetNames[s]],e.SheetNames[s],s);e.SheetNames.forEach((function(t,r){var a=e.Sheets[t];if(a&&a["!autofilter"]){var n;e.Workbook||(e.Workbook={}),e.Workbook.Names||(e.Workbook.Names=[]),e.Workbook.Names.forEach((function(e){"_xlnm._FilterDatabase"==e.Name&&e.Sheet==r&&(n=e)}));var s=ha(t)+"!"+fa(a["!autofilter"].ref);n?n.Ref=s:e.Workbook.Names.push({Name:"_xlnm._FilterDatabase",Sheet:r,Ref:s})}}))}var Kc=/<\w+:workbook/;function Yc(e,t){var r={};return e.read_shift(4),r.ArchID=e.read_shift(4),e.l+=t-8,r}function Zc(e,t){var r=Zr();return Jr(r,131),Jr(r,128,function(e,t){t||(t=Kr(127));for(var r=0;4!=r;++r)t.write_shift(4,0);return wa("SheetJS",t),wa(n.version,t),wa(n.version,t),wa("7262",t),t.length>t.l?t.slice(0,t.l):t}()),Jr(r,153,function(e,t){t||(t=Kr(72));var r=0;return e&&(e.date1904&&(r|=1),e.filterPrivacy&&(r|=8)),t.write_shift(4,r),t.write_shift(4,0),Ca(e&&e.CodeName||"ThisWorkbook",t),t.slice(0,t.l)}(e.Workbook&&e.Workbook.WBProps||null)),function(e,t){if(t.Workbook&&t.Workbook.Sheets){for(var r,a,n=t.Workbook.Sheets,s=0,i=-1,o=-1;s<n.length;++s)!n[s]||!n[s].Hidden&&-1==i?i=s:1==n[s].Hidden&&-1==o&&(o=s);o>i||(Jr(e,135),Jr(e,158,(r=i,a||(a=Kr(29)),a.write_shift(-4,0),a.write_shift(-4,460),a.write_shift(4,28800),a.write_shift(4,17600),a.write_shift(4,500),a.write_shift(4,r),a.write_shift(4,r),a.write_shift(1,120),a.length>a.l?a.slice(0,a.l):a)),Jr(e,136))}}(r,e),function(e,t){Jr(e,143);for(var r=0;r!=t.SheetNames.length;++r)Jr(e,156,(a={Hidden:t.Workbook&&t.Workbook.Sheets&&t.Workbook.Sheets[r]&&t.Workbook.Sheets[r].Hidden||0,iTabID:r+1,strRelID:"rId"+(r+1),name:t.SheetNames[r]},(n=void 0)||(n=Kr(127)),n.write_shift(4,a.Hidden),n.write_shift(4,a.iTabID),Fa(a.strRelID,n),wa(a.name.slice(0,31),n),n.length>n.l?n.slice(0,n.l):n));var a,n;Jr(e,144)}(r,e),function(e,t){Jr(e,353),Jr(e,357),Jr(e,362,function(e){var t=e.SheetNames.length,r=Kr(12*t+28);r.write_shift(4,t+2),r.write_shift(4,0),r.write_shift(4,-2),r.write_shift(4,-2),r.write_shift(4,0),r.write_shift(4,-1),r.write_shift(4,-1);for(var a=0;a<t;++a)r.write_shift(4,0),r.write_shift(4,a),r.write_shift(4,a);return r}(t)),Jr(e,354)}(r,e),(e.Workbook||{}).Names&&function(e,t){t.Workbook&&t.Workbook.Names&&t.Workbook.Names.forEach((function(r){try{if(14&r.Flags)return;Jr(e,39,function(e,t){var r=Kr(9),a=0,n=e.Name;an.indexOf(n)>-1&&(a|=32,n=n.slice(6)),r.write_shift(4,a),r.write_shift(1,0),r.write_shift(4,null==e.Sheet?4294967295:e.Sheet);var s=[r,wa(n),Jo(e.Ref,t)];if(e.Comment)s.push(Oa(e.Comment));else{var i=Kr(4);i.write_shift(4,4294967295),s.push(i)}return N(s)}(r,t))}catch(e){console.error("Could not serialize defined name "+JSON.stringify(r))}}))}(r,e),Jr(r,132),r.end()}function Jc(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n,s,i){if(!e)return e;var o=t||{};a||(a={"!id":{}}),null!=w&&null==o.dense&&(o.dense=w);var c,l={};o.dense&&(l["!data"]=[]);var f,h,u,d,p,m,v,g,b,T={s:{r:2e6,c:2e6},e:{r:0,c:0}},y=[],E=!1,k=!1,_=[];o.biff=12,o["!row"]=0;var S=0,x=!1,A=[],C={},I=o.supbooks||n.supbooks||[[]];if(I.sharedf=C,I.arrayf=A,I.SheetNames=n.SheetNames||n.Sheets.map((function(e){return e.name})),!o.supbooks&&(o.supbooks=I,n.Names))for(var O=0;O<n.Names.length;++O)I[0][O+1]=n.Names[O];var R,N,F=[],D=[],P=!1;Tl[16]={n:"BrtShortReal",f:Nc};var M=1462*+!!((n||{}).WBProps||{}).date1904;if(Yr(e,(function(e,t,w){if(!k)switch(w){case 148:c=e;break;case 0:f=e,o.sheetRows&&o.sheetRows<=f.r&&(k=!0),g=aa(d=f.r),o["!row"]=f.r,(e.hidden||e.hpt||null!=e.level)&&(e.hpt&&(e.hpx=Pi(e.hpt)),D[e.r]=e);break;case 2:case 3:case 4:case 5:case 6:case 7:case 8:case 9:case 10:case 11:case 13:case 14:case 15:case 16:case 17:case 18:case 62:switch(h={t:e[2]},e[2]){case"n":h.v=e[1];break;case"s":v=nc[e[1]],h.v=v.t,h.r=v.r;break;case"b":h.v=!!e[1];break;case"e":h.v=e[1],!1!==o.cellText&&(h.w=tn[h.v]);break;case"str":h.t="s",h.v=e[1];break;case"is":h.t="s",h.v=e[1].t}if((u=i.CellXf[e[0].iStyleRef])&&hc(h,u.numFmtId,null,o,s,i,M>0),p=-1==e[0].c?p+1:e[0].c,o.dense?(l["!data"][d]||(l["!data"][d]=[]),l["!data"][d][p]=h):l[sa(p)+g]=h,o.cellFormula){for(x=!1,S=0;S<A.length;++S){var O=A[S];f.r>=O[0].s.r&&f.r<=O[0].e.r&&p>=O[0].s.c&&p<=O[0].e.c&&(h.F=la(O[0]),x=!0)}!x&&e.length>3&&(h.f=e[3])}if(T.s.r>f.r&&(T.s.r=f.r),T.s.c>p&&(T.s.c=p),T.e.r<f.r&&(T.e.r=f.r),T.e.c<p&&(T.e.c=p),o.cellDates&&u&&"n"==h.t&&me(G[u.numFmtId])){var L=Y(h.v+M);L&&(h.t="d",h.v=new Date(Date.UTC(L.y,L.m-1,L.d,L.H,L.M,L.S,L.u)))}R&&("XLDAPR"==R.type&&(h.D=!0),R=void 0),N&&(N=void 0);break;case 1:case 12:if(!o.sheetStubs||E)break;h={t:"z",v:void 0},p=-1==e[0].c?p+1:e[0].c,o.dense?(l["!data"][d]||(l["!data"][d]=[]),l["!data"][d][p]=h):l[sa(p)+g]=h,T.s.r>f.r&&(T.s.r=f.r),T.s.c>p&&(T.s.c=p),T.e.r<f.r&&(T.e.r=f.r),T.e.c<p&&(T.e.c=p),R&&("XLDAPR"==R.type&&(h.D=!0),R=void 0),N&&(N=void 0);break;case 176:_.push(e);break;case 49:R=((o.xlmeta||{}).Cell||[])[e-1];break;case 494:var U=a["!id"][e.relId];for(U?(e.Target=U.Target,e.loc&&(e.Target+="#"+e.loc),e.Rel=U):""==e.relId&&(e.Target="#"+e.loc),d=e.rfx.s.r;d<=e.rfx.e.r;++d)for(p=e.rfx.s.c;p<=e.rfx.e.c;++p)o.dense?(l["!data"][d]||(l["!data"][d]=[]),l["!data"][d][p]||(l["!data"][d][p]={t:"z",v:void 0}),l["!data"][d][p].l=e):(m=sa(p)+aa(d),l[m]||(l[m]={t:"z",v:void 0}),l[m].l=e);break;case 426:if(!o.cellFormula)break;A.push(e),(b=o.dense?l["!data"][d][p]:l[sa(p)+g]).f=zo(e[1],0,{r:f.r,c:p},I,o),b.F=la(e[0]);break;case 427:if(!o.cellFormula)break;C[oa(e[0].s)]=e[1],(b=o.dense?l["!data"][d][p]:l[sa(p)+g]).f=zo(e[1],0,{r:f.r,c:p},I,o);break;case 60:if(!o.cellStyles)break;for(;e.e>=e.s;)F[e.e--]={width:e.w/256,hidden:!!(1&e.flags),level:e.level},P||(P=!0,Ri(e.w/256)),Ni(F[e.e+1]);break;case 551:e&&(l["!legrel"]=e);break;case 161:l["!autofilter"]={ref:la(e)};break;case 476:l["!margins"]=e;break;case 147:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name),(e.above||e.left)&&(l["!outline"]={above:e.above,left:e.left});break;case 137:n.Views||(n.Views=[{}]),n.Views[0]||(n.Views[0]={}),e.RTL&&(n.Views[0].RTL=!0);break;case 485:case 64:case 1053:case 151:case 152:case 175:case 644:case 625:case 562:case 396:case 1112:case 1146:case 471:case 1050:case 649:case 1105:case 589:case 607:case 564:case 1055:case 168:case 174:case 1180:case 499:case 507:case 550:case 171:case 167:case 1177:case 169:case 1181:case 552:case 661:case 639:case 478:case 537:case 477:case 536:case 1103:case 680:case 1104:case 1024:case 663:case 535:case 678:case 504:case 1043:case 428:case 170:case 3072:case 50:case 2070:case 1045:break;case 35:E=!0;break;case 36:E=!1;break;case 37:y.push(w),E=!0;break;case 38:y.pop(),E=!1;break;default:if(t.T);else if(!E||o.WTF)throw new Error("Unexpected record 0x"+w.toString(16))}}),o),delete o.supbooks,delete o["!row"],!l["!ref"]&&(T.s.r<2e6||c&&(c.e.r>0||c.e.c>0||c.s.r>0||c.s.c>0))&&(l["!ref"]=la(c||T)),o.sheetRows&&l["!ref"]){var L=ua(l["!ref"]);o.sheetRows<=+L.e.r&&(L.e.r=o.sheetRows-1,L.e.r>T.e.r&&(L.e.r=T.e.r),L.e.r<L.s.r&&(L.s.r=L.e.r),L.e.c>T.e.c&&(L.e.c=T.e.c),L.e.c<L.s.c&&(L.s.c=L.e.c),l["!fullref"]=l["!ref"],l["!ref"]=la(L))}return _.length>0&&(l["!merges"]=_),F.length>0&&(l["!cols"]=F),D.length>0&&(l["!rows"]=D),a["!id"][l["!legrel"]]&&(l["!legdrawel"]=a["!id"][l["!legrel"]]),l}(e,a,r,n,s,i,o):function(e,t,r,a,n,s,i){if(!e)return e;a||(a={"!id":{}}),null!=w&&null==t.dense&&(t.dense=w);var o={};t.dense&&(o["!data"]=[]);var c={s:{r:2e6,c:2e6},e:{r:0,c:0}},l="",f="",h=ht(e,"sheetData");h?(l=e.slice(0,h.index),f=e.slice(h.index+h[0].length)):l=f=e;var u=l.match(wc);u?Tc(u[0],0,n,r):(u=ht(l,"sheetPr"))&&function(e,t,r,a,n){Tc(e.slice(0,e.indexOf(">")),0,a,n)}(u[0],u[1],0,n,r);var d=(l.match(/<(?:\w*:)?dimension/)||{index:-1}).index;if(d>0){var p=l.slice(d,d+50).match(mc);!p||t&&t.nodim||function(e,t){var r=ua(t);r.s.r<=r.e.r&&r.s.c<=r.e.c&&r.s.r>=0&&r.s.c>=0&&(e["!ref"]=la(r))}(o,p[1])}var m=ht(l,"sheetViews");m&&m[1]&&function(e,t){t.Views||(t.Views=[{}]),(e.match(kc)||[]).forEach((function(e,r){var a=Ft(e);t.Views[r]||(t.Views[r]={}),+a.zoomScale&&(t.Views[r].zoom=+a.zoomScale),a.rightToLeft&&Gt(a.rightToLeft)&&(t.Views[r].RTL=!0)}))}(m[1],n);var v=[];if(t.cellStyles){var g=l.match(vc);g&&function(e,t){for(var r=!1,a=0;a!=t.length;++a){var n=Ft(t[a],!0);n.hidden&&(n.hidden=Gt(n.hidden));var s=parseInt(n.min,10)-1,i=parseInt(n.max,10)-1;for(n.outlineLevel&&(n.level=+n.outlineLevel||0),delete n.min,delete n.max,n.width=+n.width,!r&&n.width&&(r=!0,Ri(n.width)),Ni(n);s<=i;)e[s++]=Ke(n)}}(v,g)}h&&Sc(h[1],o,t,c,s,i,n);var b=f.match(gc);b&&(o["!autofilter"]=function(e){return{ref:(e.match(/ref="([^"]*)"/)||[])[1]}}(b[0]));var T=[],y=f.match(dc);if(y)for(d=0;d!=y.length;++d)T[d]=ua(y[d].slice(y[d].indexOf('"')+1));var E=f.match(pc);E&&function(e,t,r){for(var a=null!=e["!data"],n=0;n!=t.length;++n){var s=Ft(Zt(t[n]),!0);if(!s.ref)return;var i=((r||{})["!id"]||[])[s.id];i?(s.Target=i.Target,s.location&&(s.Target+="#"+Lt(s.location))):(s.Target="#"+Lt(s.location),i={Target:s.Target,TargetMode:"Internal"}),s.Rel=i,s.tooltip&&(s.Tooltip=s.tooltip,delete s.tooltip);for(var o=ua(s.ref),c=o.s.r;c<=o.e.r;++c)for(var l=o.s.c;l<=o.e.c;++l){var f=sa(l)+aa(c);a?(e["!data"][c]||(e["!data"][c]=[]),e["!data"][c][l]||(e["!data"][c][l]={t:"z",v:void 0}),e["!data"][c][l].l=s):(e[f]||(e[f]={t:"z",v:void 0}),e[f].l=s)}}}(o,E,a);var k,_,S,x=f.match(bc);if(x&&(o["!margins"]=(k=Ft(x[0]),_={},["left","right","top","bottom","header","footer"].forEach((function(e){k[e]&&(_[e]=parseFloat(k[e]))})),_)),(S=f.match(/legacyDrawing r:id="(.*?)"/))&&(o["!legrel"]=S[1]),t&&t.nodim&&(c.s.c=c.s.r=0),!o["!ref"]&&c.e.c>=c.s.c&&c.e.r>=c.s.r&&(o["!ref"]=la(c)),t.sheetRows>0&&o["!ref"]){var A=ua(o["!ref"]);t.sheetRows<=+A.e.r&&(A.e.r=t.sheetRows-1,A.e.r>c.e.r&&(A.e.r=c.e.r),A.e.r<A.s.r&&(A.s.r=A.e.r),A.e.c>c.e.c&&(A.e.c=c.e.c),A.e.c<A.s.c&&(A.s.c=A.e.c),o["!fullref"]=o["!ref"],o["!ref"]=la(A))}return v.length>0&&(o["!cols"]=v),T.length>0&&(o["!merges"]=T),a["!id"][o["!legrel"]]&&(o["!legdrawel"]=a["!id"][o["!legrel"]]),o}(e,a,r,n,s,i,o)}var qc,Qc=/\b((?:\w+:)?[\w]+)=((?:")([^"]*)(?:")|(?:')([^']*)(?:'))/g,el=/\b((?:\w+:)?[\w]+)=((?:")(?:[^"]*)(?:")|(?:')(?:[^']*)(?:'))/;function tl(e,t){var r=e.split(/\s+/),a=[];if(t||(a[0]=r[0]),1===r.length)return a;var n,s,i,o=e.match(Qc);if(o)for(i=0;i!=o.length;++i)-1===(s=(n=o[i].match(el))[1].indexOf(":"))?a[n[1]]=n[2].slice(1,n[2].length-1):a["xmlns:"===n[1].slice(0,6)?"xmlns"+n[1].slice(6):n[1].slice(s+1)]=n[2].slice(1,n[2].length-1);return a}function rl(e){var t={};if(1===e.split(/\s+/).length)return t;var r,a,n,s=e.match(Qc);if(s)for(n=0;n!=s.length;++n)-1===(a=(r=s[n].match(el))[1].indexOf(":"))?t[r[1]]=r[2].slice(1,r[2].length-1):t["xmlns:"===r[1].slice(0,6)?"xmlns"+r[1].slice(6):r[1].slice(a+1)]=r[2].slice(1,r[2].length-1);return t}function al(e,t,r,a){var n=a;switch((r[0].match(/dt:dt="([\w.]+)"/)||["",""])[1]){case"boolean":n=Gt(a);break;case"i2":case"int":n=parseInt(a,10);break;case"r4":case"float":n=parseFloat(a);break;case"date":case"dateTime.tz":n=Xe(a);break;case"i8":case"string":case"fixed":case"uuid":case"bin.base64":break;default:throw new Error("bad custprop:"+r[0])}e[Lt(t)]=n}function nl(e,t,r){if(r.cellStyles&&t.Interior){var a=t.Interior;a.Pattern&&(a.patternType=Mi[a.Pattern]||a.Pattern)}e[t.ID]=t}function sl(e,t,r,a,n,s,i,o,c,l,f){var h="General",u=a.StyleID,d={};l=l||{};var p=[],m=0;for(void 0===u&&o&&(u=o.StyleID),void 0===u&&i&&(u=i.StyleID);void 0!==s[u];){var v=s[u];if(v.nf&&(h=v.nf),v.Interior&&p.push(v.Interior),!v.Parent)break;u=v.Parent}switch(r.Type){case"Boolean":a.t="b",a.v=Gt(e);break;case"String":a.t="s",a.r=$t(Lt(e)),a.v=e.indexOf("<")>-1?Lt(t||e).replace(/<[^<>]*>/g,""):a.r;break;case"DateTime":"Z"!=e.slice(-1)&&(e+="Z"),a.v=We(Xe(e,f),f),a.v!=a.v&&(a.v=Lt(e)),h&&"General"!=h||(h="yyyy-mm-dd");case"Number":void 0===a.v&&(a.v=+e),a.t||(a.t="n");break;case"Error":a.t="e",a.v=rn[e],!1!==l.cellText&&(a.w=e);break;default:""==e&&""==t?a.t="z":(a.t="s",a.v=$t(t||e))}if(function(e,t,r,a){if("z"!==e.t){if(!r||!1!==r.cellText)try{"e"===e.t?e.w=e.w||tn[e.v]:"General"===t?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=J(e.v):e.w=q(e.v):e.w=function(e,t,r){var a=qc[e]||Lt(e);return"General"===a?q(t):be(a,t,{date1904:!!r})}(t||"General",e.v,a)}catch(e){if(r.WTF)throw e}try{var n=qc[t]||t||"General";if(r.cellNF&&(e.z=n),r.cellDates&&"n"==e.t&&me(n)){var s=Y(e.v+(a?1462:0));s&&(e.t="d",e.v=new Date(Date.UTC(s.y,s.m-1,s.d,s.H,s.M,s.S,s.u)))}}catch(e){if(r.WTF)throw e}}}(a,h,l,f),!1!==l.cellFormula)if(a.Formula){var g=Lt(a.Formula);61==g.charCodeAt(0)&&(g=g.slice(1)),a.f=lo(g,n),delete a.Formula,"RC"==a.ArrayRange?a.F=lo("RC:RC",n):a.ArrayRange&&(a.F=lo(a.ArrayRange,n),c.push([ua(a.F),a.F]))}else for(m=0;m<c.length;++m)n.r>=c[m][0].s.r&&n.r<=c[m][0].e.r&&n.c>=c[m][0].s.c&&n.c<=c[m][0].e.c&&(a.F=c[m][1]);l.cellStyles&&(p.forEach((function(e){!d.patternType&&e.patternType&&(d.patternType=e.patternType)})),a.s=d),void 0!==a.StyleID&&(a.ixfe=a.StyleID)}function il(e){e.t=e.v||"",e.t=e.t.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),e.v=e.w=e.ixfe=void 0}function ol(e,t){var r=t||{};ye();var n=m(or(e));"binary"!=r.type&&"array"!=r.type&&"base64"!=r.type||(n=void 0!==a?a.utils.decode(65001,u(n)):Zt(n));var s,i=n.slice(0,1024).toLowerCase(),o=!1;if((1023&(i=i.replace(/".*?"/g,"")).indexOf(">"))>Math.min(1023&i.indexOf(","),1023&i.indexOf(";"))){var c=Ke(r);return c.type="string",Qs.to_workbook(n,c)}if(-1==i.indexOf("<?xml")&&["html","table","head","meta","script","style","div"].forEach((function(e){i.indexOf("<"+e)>=0&&(o=!0)})),o)return function(e,t){var r=pt(e,"table");if(!r||0==r.length)throw new Error("Invalid HTML: could not find <table>");if(1==r.length){var a=pa(Nl(r[0],t),t);return a.bookType="html",a}var n=bh();return r.forEach((function(e,r){wh(n,Nl(e,t),"Sheet"+(r+1))})),n.bookType="html",n}(n,r);qc={"General Number":"General","General Date":G[22],"Long Date":"dddd, mmmm dd, yyyy","Medium Date":G[15],"Short Date":G[14],"Long Time":G[19],"Medium Time":G[18],"Short Time":G[20],Currency:'"$"#,##0.00_);[Red]\\("$"#,##0.00\\)',Fixed:G[2],Standard:G[4],Percent:G[10],Scientific:G[11],"Yes/No":'"Yes";"Yes";"No";@',"True/False":'"True";"True";"False";@',"On/Off":'"Yes";"Yes";"No";@'};var l,f=[];null!=w&&null==r.dense&&(r.dense=w);var h={},d=[],p={},v="";r.dense&&(p["!data"]=[]);var g,b={},T={},y=tl('<Data ss:Type="String">'),E=0,k=0,_=0,S={s:{r:2e6,c:2e6},e:{r:0,c:0}},x={},A={},C="",I=0,O=[],R={},N={},F=0,D=[],P=[],M={},L=[],U=!1,B=[],W=[],z={},H=0,V=0,$={Sheets:[],WBProps:{date1904:!1}},X={};cr.lastIndex=0,n=ct(n,"\x3c!--","--\x3e");for(var j,K="";s=cr.exec(n);)switch(s[3]=(K=s[3]).toLowerCase()){case"data":if("data"==K){if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else"/"!==s[0].charAt(s[0].length-2)&&f.push([s[3],!0]);break}if(f[f.length-1][1])break;"/"===s[1]?sl(n.slice(E,s.index),C,y,"comment"==f[f.length-1][0]?M:b,{c:k,r:_},x,L[k],T,B,r,$.WBProps.date1904):(C="",y=tl(s[0]),E=s.index+s[0].length);break;case"cell":if("/"===s[1])if(P.length>0&&(b.c=P),(!r.sheetRows||r.sheetRows>_)&&void 0!==b.v&&(r.dense?(p["!data"][_]||(p["!data"][_]=[]),p["!data"][_][k]=b):p[sa(k)+aa(_)]=b),b.HRef&&(b.l={Target:Lt(b.HRef)},b.HRefScreenTip&&(b.l.Tooltip=b.HRefScreenTip),delete b.HRef,delete b.HRefScreenTip),(b.MergeAcross||b.MergeDown)&&(H=k+(0|parseInt(b.MergeAcross,10)),V=_+(0|parseInt(b.MergeDown,10)),(H>k||V>_)&&O.push({s:{c:k,r:_},e:{c:H,r:V}})),r.sheetStubs)if(b.MergeAcross||b.MergeDown){for(var Y=k;Y<=H;++Y)for(var Z=_;Z<=V;++Z)(Y>k||Z>_)&&(r.dense?(p["!data"][Z]||(p["!data"][Z]=[]),p["!data"][Z][Y]={t:"z"}):p[sa(Y)+aa(Z)]={t:"z"});k=H+1}else++k;else b.MergeAcross?k=H+1:++k;else(b=rl(s[0])).Index&&(k=+b.Index-1),k<S.s.c&&(S.s.c=k),k>S.e.c&&(S.e.c=k),"/>"===s[0].slice(-2)&&++k,P=[];break;case"row":"/"===s[1]||"/>"===s[0].slice(-2)?(_<S.s.r&&(S.s.r=_),_>S.e.r&&(S.e.r=_),"/>"===s[0].slice(-2)&&(T=tl(s[0])).Index&&(_=+T.Index-1),k=0,++_):((T=tl(s[0])).Index&&(_=+T.Index-1),z={},("0"==T.AutoFitHeight||T.Height)&&(z.hpx=parseInt(T.Height,10),z.hpt=Di(z.hpx),W[_]=z),"1"==T.Hidden&&(z.hidden=!0,W[_]=z));break;case"worksheet":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"));d.push(v),S.s.r<=S.e.r&&S.s.c<=S.e.c&&(p["!ref"]=la(S),r.sheetRows&&r.sheetRows<=S.e.r&&(p["!fullref"]=p["!ref"],S.e.r=r.sheetRows-1,p["!ref"]=la(S))),O.length&&(p["!merges"]=O),L.length>0&&(p["!cols"]=L),W.length>0&&(p["!rows"]=W),h[v]=p}else S={s:{r:2e6,c:2e6},e:{r:0,c:0}},_=k=0,f.push([s[3],!1]),l=tl(s[0]),v=Lt(l.Name),p={},r.dense&&(p["!data"]=[]),O=[],B=[],W=[],X={name:v,Hidden:0},$.Sheets.push(X);break;case"table":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else{if("/>"==s[0].slice(-2))break;f.push([s[3],!1]),L=[],U=!1}break;case"style":"/"===s[1]?nl(x,A,r):A=tl(s[0]);break;case"numberformat":A.nf=Lt(tl(s[0]).Format||"General"),qc[A.nf]&&(A.nf=qc[A.nf]);for(var J=0;392!=J&&G[J]!=A.nf;++J);if(392==J)for(J=57;392!=J;++J)if(null==G[J]){xe(A.nf,J);break}break;case"column":if("table"!==f[f.length-1][0])break;if("/"===s[1])break;if((g=tl(s[0])).Hidden&&(g.hidden=!0,delete g.Hidden),g.Width&&(g.wpx=parseInt(g.Width,10)),!U&&g.wpx>10){U=!0,xi=ki;for(var q=0;q<L.length;++q)L[q]&&Ni(L[q])}U&&Ni(g),L[g.Index-1||L.length]=g;for(var Q=0;Q<+g.Span;++Q)L[L.length]=Ke(g);break;case"namedrange":if("/"===s[1])break;$.Names||($.Names=[]);var ee=Ft(s[0]),te={Name:(j=ee.Name,an.indexOf("_xlnm."+j)>-1?"_xlnm."+j:j),Ref:lo(ee.RefersTo.slice(1),{r:0,c:0})};$.Sheets.length>0&&(te.Sheet=$.Sheets.length-1),$.Names.push(te);break;case"namedcell":case"b":case"i":case"u":case"s":case"em":case"h2":case"h3":case"sub":case"sup":case"span":case"alignment":case"borders":case"border":case"protection":case"paragraphs":case"name":case"pixelsperinch":case"null":break;case"font":if("/>"===s[0].slice(-2))break;"/"===s[1]?C+=n.slice(I,s.index):I=s.index+s[0].length;break;case"interior":if(!r.cellStyles)break;A.Interior=tl(s[0]);break;case"author":case"title":case"description":case"created":case"keywords":case"subject":case"category":case"company":case"lastauthor":case"lastsaved":case"lastprinted":case"version":case"revision":case"totaltime":case"hyperlinkbase":case"manager":case"contentstatus":case"identifier":case"language":case"appname":if("/>"===s[0].slice(-2))break;"/"===s[1]?Cn(R,K,n.slice(F,s.index)):F=s.index+s[0].length;break;case"styles":case"workbook":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else f.push([s[3],!1]);break;case"comment":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"));il(M),P.push(M)}else f.push([s[3],!1]),Gt((l=tl(s[0])).ShowAlways||"0")||(P.hidden=!0),M={a:l.Author};break;case"autofilter":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else if("/"!==s[0].charAt(s[0].length-2)){var re=tl(s[0]);p["!autofilter"]={ref:lo(re.Range).replace(/\$/g,"")},f.push([s[3],!0])}break;case"datavalidation":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else"/"!==s[0].charAt(s[0].length-2)&&f.push([s[3],!0]);break;case"componentoptions":case"documentproperties":case"customdocumentproperties":case"officedocumentsettings":case"pivottable":case"pivotcache":case"names":case"mapinfo":case"pagebreaks":case"querytable":case"sorting":case"schema":case"conditionalformatting":case"smarttagtype":case"smarttags":case"excelworkbook":case"workbookoptions":case"worksheetoptions":if("/"===s[1]){if((l=f.pop())[0]!==s[3])throw new Error("Bad state: "+l.join("|"))}else"/"!==s[0].charAt(s[0].length-2)&&f.push([s[3],!0]);break;default:if(0==f.length&&"document"==s[3])return $l(n,r);if(0==f.length&&"uof"==s[3])return $l(n,r);var ae=!0;switch(f[f.length-1][0]){case"officedocumentsettings":switch(s[3]){case"allowpng":case"removepersonalinformation":case"downloadcomponents":case"locationofcomponents":case"colors":case"color":case"index":case"rgb":case"targetscreensize":case"readonlyrecommended":break;default:ae=!1}break;case"componentoptions":switch(s[3]){case"toolbar":case"hideofficelogo":case"spreadsheetautofit":case"label":case"caption":case"maxheight":case"maxwidth":case"nextsheetnumber":break;default:ae=!1}break;case"excelworkbook":switch(s[3]){case"date1904":$.WBProps.date1904=!0;break;case"hidehorizontalscrollbar":case"hideverticalscrollbar":case"hideworkbooktabs":case"windowheight":case"windowwidth":case"windowtopx":case"windowtopy":case"tabratio":case"protectstructure":case"protectwindow":case"protectwindows":case"activesheet":case"displayinknotes":case"firstvisiblesheet":case"supbook":case"sheetname":case"sheetindex":case"sheetindexfirst":case"sheetindexlast":case"dll":case"acceptlabelsinformulas":case"donotsavelinkvalues":case"iteration":case"maxiterations":case"maxchange":case"path":case"xct":case"count":case"selectedsheets":case"calculation":case"uncalced":case"startupprompt":case"crn":case"externname":case"formula":case"colfirst":case"collast":case"wantadvise":case"boolean":case"error":case"text":case"ole":case"noautorecover":case"publishobjects":case"donotcalculatebeforesave":case"number":case"refmoder1c1":case"embedsavesmarttags":break;default:ae=!1}break;case"workbookoptions":switch(s[3]){case"owcversion":case"height":case"width":break;default:ae=!1}break;case"worksheetoptions":switch(s[3]){case"visible":if("/>"===s[0].slice(-2));else if("/"===s[1])switch(n.slice(F,s.index)){case"SheetHidden":X.Hidden=1;break;case"SheetVeryHidden":X.Hidden=2}else F=s.index+s[0].length;break;case"header":p["!margins"]||lc(p["!margins"]={},"xlml"),isNaN(+Ft(s[0]).Margin)||(p["!margins"].header=+Ft(s[0]).Margin);break;case"footer":p["!margins"]||lc(p["!margins"]={},"xlml"),isNaN(+Ft(s[0]).Margin)||(p["!margins"].footer=+Ft(s[0]).Margin);break;case"pagemargins":var ne=Ft(s[0]);p["!margins"]||lc(p["!margins"]={},"xlml"),isNaN(+ne.Top)||(p["!margins"].top=+ne.Top),isNaN(+ne.Left)||(p["!margins"].left=+ne.Left),isNaN(+ne.Right)||(p["!margins"].right=+ne.Right),isNaN(+ne.Bottom)||(p["!margins"].bottom=+ne.Bottom);break;case"displayrighttoleft":$.Views||($.Views=[]),$.Views[0]||($.Views[0]={}),$.Views[0].RTL=!0;break;case"freezepanes":case"frozennosplit":case"splithorizontal":case"splitvertical":case"donotdisplaygridlines":case"activerow":case"activecol":case"toprowbottompane":case"leftcolumnrightpane":case"unsynced":case"print":case"printerrors":case"panes":case"scale":case"pane":case"number":case"layout":case"pagesetup":case"selected":case"protectobjects":case"enableselection":case"protectscenarios":case"validprinterinfo":case"horizontalresolution":case"verticalresolution":case"numberofcopies":case"activepane":case"toprowvisible":case"leftcolumnvisible":case"fittopage":case"rangeselection":case"papersizeindex":case"pagelayoutzoom":case"pagebreakzoom":case"filteron":case"fitwidth":case"fitheight":case"commentslayout":case"zoom":case"lefttoright":case"gridlines":case"allowsort":case"allowfilter":case"allowinsertrows":case"allowdeleterows":case"allowinsertcols":case"allowdeletecols":case"allowinserthyperlinks":case"allowformatcells":case"allowsizecols":case"allowsizerows":case"tabcolorindex":case"donotdisplayheadings":case"showpagelayoutzoom":case"blackandwhite":case"donotdisplayzeros":case"displaypagebreak":case"rowcolheadings":case"donotdisplayoutline":case"noorientation":case"allowusepivottables":case"zeroheight":case"viewablerange":case"selection":case"protectcontents":break;case"nosummaryrowsbelowdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].above=!0;break;case"nosummarycolumnsrightdetail":p["!outline"]||(p["!outline"]={}),p["!outline"].left=!0;break;default:ae=!1}break;case"pivottable":case"pivotcache":switch(s[3]){case"immediateitemsondrop":case"showpagemultipleitemlabel":case"compactrowindent":case"location":case"pivotfield":case"orientation":case"layoutform":case"layoutsubtotallocation":case"layoutcompactrow":case"position":case"pivotitem":case"datatype":case"datafield":case"sourcename":case"parentfield":case"ptlineitems":case"ptlineitem":case"countofsameitems":case"item":case"itemtype":case"ptsource":case"cacheindex":case"consolidationreference":case"filename":case"reference":case"nocolumngrand":case"norowgrand":case"blanklineafteritems":case"hidden":case"subtotal":case"basefield":case"mapchilditems":case"function":case"refreshonfileopen":case"printsettitles":case"mergelabels":case"defaultversion":case"refreshname":case"refreshdate":case"refreshdatecopy":case"versionlastrefresh":case"versionlastupdate":case"versionupdateablemin":case"versionrefreshablemin":case"calculation":break;default:ae=!1}break;case"pagebreaks":switch(s[3]){case"colbreaks":case"colbreak":case"rowbreaks":case"rowbreak":case"colstart":case"colend":case"rowend":break;default:ae=!1}break;case"autofilter":switch(s[3]){case"autofiltercolumn":case"autofiltercondition":case"autofilterand":case"autofilteror":break;default:ae=!1}break;case"querytable":switch(s[3]){case"id":case"autoformatfont":case"autoformatpattern":case"querysource":case"querytype":case"enableredirections":case"refreshedinxl9":case"urlstring":case"htmltables":case"connection":case"commandtext":case"refreshinfo":case"notitles":case"nextid":case"columninfo":case"overwritecells":case"donotpromptforfile":case"textwizardsettings":case"source":case"number":case"decimal":case"thousandseparator":case"trailingminusnumbers":case"formatsettings":case"fieldtype":case"delimiters":case"tab":case"comma":case"autoformatname":case"versionlastedit":case"versionlastrefresh":break;default:ae=!1}break;case"datavalidation":switch(s[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":case"cellrangelist":break;default:ae=!1}break;case"sorting":case"conditionalformatting":switch(s[3]){case"range":case"type":case"min":case"max":case"sort":case"descending":case"order":case"casesensitive":case"value":case"errorstyle":case"errormessage":case"errortitle":case"cellrangelist":case"inputmessage":case"inputtitle":case"combohide":case"inputhide":case"condition":case"qualifier":case"useblank":case"value1":case"value2":case"format":break;default:ae=!1}break;case"mapinfo":case"schema":case"data":switch(s[3]){case"map":case"entry":case"range":case"xpath":case"field":case"xsdtype":case"filteron":case"aggregate":case"elementtype":case"attributetype":case"schema":case"element":case"complextype":case"datatype":case"all":case"attribute":case"extends":case"row":break;default:ae=!1}break;case"smarttags":break;default:ae=!1}if(ae)break;if(s[3].match(/!\[CDATA/))break;if(!f[f.length-1][1])throw"Unrecognized tag: "+s[3]+"|"+f.join("|");if("customdocumentproperties"===f[f.length-1][0]){if("/>"===s[0].slice(-2))break;"/"===s[1]?al(N,K,D,n.slice(F,s.index)):(D=s,F=s.index+s[0].length);break}if(r.WTF)throw"Unrecognized tag: "+s[3]+"|"+f.join("|")}var se={};return r.bookSheets||r.bookProps||(se.Sheets=h),se.SheetNames=d,se.Workbook=$,se.SSF=Ke(G),se.Props=R,se.Custprops=N,se.bookType="xlml",se}function cl(e,t){switch(zf(t=t||{}),t.type||"base64"){case"base64":return ol(E(e),t);case"binary":case"buffer":case"file":return ol(e,t);case"array":return ol(O(e),t)}}function ll(e){return sr("NamedRange",null,{"ss:Name":"_xlnm."==e.Name.slice(0,6)?e.Name.slice(6):e.Name,"ss:RefersTo":"="+ho(e.Ref,{r:0,c:0})})}function fl(e,t,r,a,n,s,i){if(!e||null==e.v&&null==e.f)return"";var o={};if(e.f&&(o["ss:Formula"]="="+Wt(ho(e.f,i))),e.F&&e.F.slice(0,t.length)==t){var c=ia(e.F.slice(t.length+1));o["ss:ArrayRange"]="RC:R"+(c.r==i.r?"":"["+(c.r-i.r)+"]")+"C"+(c.c==i.c?"":"["+(c.c-i.c)+"]")}if(e.l&&e.l.Target&&(o["ss:HRef"]=Wt(e.l.Target),e.l.Tooltip&&(o["x:HRefScreenTip"]=Wt(e.l.Tooltip))),r["!merges"])for(var l=r["!merges"],f=0;f!=l.length;++f)l[f].s.c==i.c&&l[f].s.r==i.r&&(l[f].e.c>l[f].s.c&&(o["ss:MergeAcross"]=l[f].e.c-l[f].s.c),l[f].e.r>l[f].s.r&&(o["ss:MergeDown"]=l[f].e.r-l[f].s.r));var h="",u="";switch(e.t){case"z":if(!a.sheetStubs)return"";break;case"n":h="Number",u=String(e.v);break;case"b":h="Boolean",u=e.v?"1":"0";break;case"e":h="Error",u=tn[e.v];break;case"d":h="DateTime",u=new Date(e.v).toISOString(),null==e.z&&(e.z=e.z||G[14]);break;case"s":h="String",u=((e.v||"")+"").replace(Ut,(function(e){return Mt[e]})).replace(Ht,(function(e){return"&#x"+e.charCodeAt(0).toString(16).toUpperCase()+";"}))}var d=fc(a.cellXfs,e,a);o["ss:StyleID"]="s"+(21+d),o["ss:Index"]=i.c+1;var p,m=null!=e.v?u:"",v="z"==e.t?"":'<Data ss:Type="'+h+'">'+m+"</Data>";return(e.c||[]).length>0&&(v+=(p=e.c).map((function(e){var t=sr("ss:Data",(e.t||"").replace(/(\r\n|[\r\n])/g,"&#10;"),{xmlns:"http://www.w3.org/TR/REC-html40"}),r={};return e.a&&(r["ss:Author"]=e.a),p.hidden||(r["ss:ShowAlways"]="1"),sr("Comment",t,r)})).join("")),sr("Cell",v,o)}function hl(e,t){var r='<Row ss:Index="'+(e+1)+'"';return t&&(t.hpt&&!t.hpx&&(t.hpx=Pi(t.hpt)),t.hpx&&(r+=' ss:AutoFitHeight="0" ss:Height="'+t.hpx+'"'),t.hidden&&(r+=' ss:Hidden="1"')),r+">"}function ul(e,t,r){var a=[],n=r.SheetNames[e],s=r.Sheets[n],i=s?function(e,t,r,a){if(!e)return"";if(!((a||{}).Workbook||{}).Names)return"";for(var n=a.Workbook.Names,s=[],i=0;i<n.length;++i){var o=n[i];o.Sheet==r&&(o.Name.match(/^_xlfn\./)||s.push(ll(o)))}return s.join("")}(s,0,e,r):"";return i.length>0&&a.push("<Names>"+i+"</Names>"),i=s?function(e,t,r,a){if(!e["!ref"])return"";var n=ua(e["!ref"]),s=e["!merges"]||[],i=0,o=[];e["!cols"]&&e["!cols"].forEach((function(e,t){Ni(e);var r=!!e.width,a=cc(t,e),n={"ss:Index":t+1};r&&(n["ss:Width"]=Ai(a.width)),e.hidden&&(n["ss:Hidden"]="1"),o.push(sr("Column",null,n))}));for(var c=null!=e["!data"],l=n.s.r;l<=n.e.r;++l){for(var f=[hl(l,(e["!rows"]||[])[l])],h=n.s.c;h<=n.e.c;++h){var u=!1;for(i=0;i!=s.length;++i)if(!(s[i].s.c>h||s[i].s.r>l||s[i].e.c<h||s[i].e.r<l)){s[i].s.c==h&&s[i].s.r==l||(u=!0);break}if(!u){var d={r:l,c:h},p=sa(h)+aa(l),m=c?(e["!data"][l]||[])[h]:e[p];f.push(fl(m,p,e,t,0,0,d))}}f.push("</Row>"),f.length>2&&o.push(f.join(""))}return o.join("")}(s,t):"",i.length>0&&a.push("<Table>"+i+"</Table>"),a.push(function(e,t,r,a){if(!e)return"";var n=[];if(e["!margins"]&&(n.push("<PageSetup>"),e["!margins"].header&&n.push(sr("Header",null,{"x:Margin":e["!margins"].header})),e["!margins"].footer&&n.push(sr("Footer",null,{"x:Margin":e["!margins"].footer})),n.push(sr("PageMargins",null,{"x:Bottom":e["!margins"].bottom||"0.75","x:Left":e["!margins"].left||"0.7","x:Right":e["!margins"].right||"0.7","x:Top":e["!margins"].top||"0.75"})),n.push("</PageSetup>")),a&&a.Workbook&&a.Workbook.Sheets&&a.Workbook.Sheets[r])if(a.Workbook.Sheets[r].Hidden)n.push(sr("Visible",1==a.Workbook.Sheets[r].Hidden?"SheetHidden":"SheetVeryHidden",{}));else{for(var s=0;s<r&&(!a.Workbook.Sheets[s]||a.Workbook.Sheets[s].Hidden);++s);s==r&&n.push("<Selected/>")}return((((a||{}).Workbook||{}).Views||[])[0]||{}).RTL&&n.push("<DisplayRightToLeft/>"),e["!protect"]&&(n.push(ar("ProtectContents","True")),e["!protect"].objects&&n.push(ar("ProtectObjects","True")),e["!protect"].scenarios&&n.push(ar("ProtectScenarios","True")),null==e["!protect"].selectLockedCells||e["!protect"].selectLockedCells?null==e["!protect"].selectUnlockedCells||e["!protect"].selectUnlockedCells||n.push(ar("EnableSelection","UnlockedCells")):n.push(ar("EnableSelection","NoSelection")),[["formatCells","AllowFormatCells"],["formatColumns","AllowSizeCols"],["formatRows","AllowSizeRows"],["insertColumns","AllowInsertCols"],["insertRows","AllowInsertRows"],["insertHyperlinks","AllowInsertHyperlinks"],["deleteColumns","AllowDeleteCols"],["deleteRows","AllowDeleteRows"],["sort","AllowSort"],["autoFilter","AllowFilter"],["pivotTables","AllowUsePivotTables"]].forEach((function(t){e["!protect"][t[0]]&&n.push("<"+t[1]+"/>")}))),0==n.length?"":sr("WorksheetOptions",n.join(""),{xmlns:hr.x})}(s,0,e,r)),s["!autofilter"]&&a.push('<AutoFilter x:Range="'+ho(fa(s["!autofilter"].ref),{r:0,c:0})+'" xmlns="urn:schemas-microsoft-com:office:excel"></AutoFilter>'),a.join("")}function dl(e,t){t||(t={}),e.SSF||(e.SSF=Ke(G)),e.SSF&&(ye(),Te(e.SSF),t.revssf=Me(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF,t.cellXfs=[],fc(t.cellXfs,{},{revssf:{General:0}}));var r=[];r.push(function(e,t){var r=[];return e.Props&&r.push(function(e,t){var r=[];return Fe(An).map((function(e){for(var t=0;t<vn.length;++t)if(vn[t][1]==e)return vn[t];for(t=0;t<Tn.length;++t)if(Tn[t][1]==e)return Tn[t];throw e})).forEach((function(a){if(null!=e[a[1]]){var n=t&&t.Props&&null!=t.Props[a[1]]?t.Props[a[1]]:e[a[1]];"date"===a[2]&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"Z")),"number"==typeof n?n=String(n):!0===n||!1===n?n=n?"1":"0":n instanceof Date&&(n=new Date(n).toISOString().replace(/\.\d*Z/,"")),r.push(ar(An[a[1]]||a[1],n))}})),sr("DocumentProperties",r.join(""),{xmlns:hr.o})}(e.Props,t)),e.Custprops&&r.push(function(e,t){var r=["Worksheets","SheetNames"],a="CustomDocumentProperties",n=[];return e&&Fe(e).forEach((function(t){if(Object.prototype.hasOwnProperty.call(e,t)){for(var a=0;a<vn.length;++a)if(t==vn[a][1])return;for(a=0;a<Tn.length;++a)if(t==Tn[a][1])return;for(a=0;a<r.length;++a)if(t==r[a])return;var s=e[t],i="string";"number"==typeof s?(i="float",s=String(s)):!0===s||!1===s?(i="boolean",s=s?"1":"0"):s=String(s),n.push(sr(zt(t),s,{"dt:dt":i}))}})),t&&Fe(t).forEach((function(r){if(Object.prototype.hasOwnProperty.call(t,r)&&(!e||!Object.prototype.hasOwnProperty.call(e,r))){var a=t[r],s="string";"number"==typeof a?(s="float",a=String(a)):!0===a||!1===a?(s="boolean",a=a?"1":"0"):a instanceof Date?(s="dateTime.tz",a=a.toISOString()):a=String(a),n.push(sr(zt(r),a,{"dt:dt":s}))}})),"<"+a+' xmlns="'+hr.o+'">'+n.join("")+"</"+a+">"}(e.Props,e.Custprops)),r.join("")}(e,t)),r.push(function(e){return(((e||{}).Workbook||{}).WBProps||{}).date1904?'<ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"><Date1904/></ExcelWorkbook>':""}(e)),r.push(""),r.push("");for(var a=0;a<e.SheetNames.length;++a)r.push(sr("Worksheet",ul(a,t,e),{"ss:Name":Wt(e.SheetNames[a])}));return r[2]=function(e,t){var r=['<Style ss:ID="Default" ss:Name="Normal"><NumberFormat/></Style>'];return t.cellXfs.forEach((function(e,t){var a=[];a.push(sr("NumberFormat",null,{"ss:Format":Wt(G[e.numFmtId])}));var n={"ss:ID":"s"+(21+t)};r.push(sr("Style",a.join(""),n))})),sr("Styles",r.join(""))}(0,t),r[3]=function(e){if(!((e||{}).Workbook||{}).Names)return"";for(var t=e.Workbook.Names,r=[],a=0;a<t.length;++a){var n=t[a];null==n.Sheet&&(n.Name.match(/^_xlfn\./)||r.push(ll(n)))}return sr("Names",r.join(""))}(e),At+sr("Workbook",r.join(""),{xmlns:hr.ss,"xmlns:o":hr.o,"xmlns:x":hr.x,"xmlns:ss":hr.ss,"xmlns:dt":hr.dt,"xmlns:html":hr.html})}var pl=[60,1084,2066,2165,2175];function ml(e,t,r,a,n){var s=a,i=[],o=r.slice(r.l,r.l+s);if(n&&n.enc&&n.enc.insitu&&o.length>0)switch(e){case 9:case 521:case 1033:case 2057:case 47:case 405:case 225:case 406:case 312:case 404:case 10:case 133:break;default:n.enc.insitu(o)}i.push(o),r.l+=s;for(var c=Pr(r,r.l),l=yl[c],f=0;null!=l&&pl.indexOf(c)>-1;)s=Pr(r,r.l+2),f=r.l+4,2066==c?f+=4:2165!=c&&2175!=c||(f+=12),o=r.slice(f,r.l+4+s),i.push(o),r.l+=4+s,l=yl[c=Pr(r,r.l)];var h=N(i);Xr(h,0);var u=0;h.lens=[];for(var d=0;d<i.length;++d)h.lens.push(u),u+=i[d].length;if(h.length<a)throw"XLS Record 0x"+e.toString(16)+" Truncated: "+h.length+" < "+a;return t.f(h,h.length,n)}function vl(e,t,r){if("z"!==e.t&&e.XF){var a=0;try{a=e.z||e.XF.numFmtId||0,t.cellNF&&null==e.z&&(e.z=G[a])}catch(e){if(t.WTF)throw e}if(!t||!1!==t.cellText)try{"e"===e.t?e.w=e.w||tn[e.v]:0===a||"General"==a?"n"===e.t?(0|e.v)===e.v?e.w=e.v.toString(10):e.w=J(e.v):e.w=q(e.v):e.w=be(a,e.v,{date1904:!!r,dateNF:t&&t.dateNF})}catch(e){if(t.WTF)throw e}if(t.cellDates&&a&&"n"==e.t&&me(G[a]||String(a))){var n=Y(e.v+(r?1462:0));n&&(e.t="d",e.v=new Date(Date.UTC(n.y,n.m-1,n.d,n.H,n.M,n.S,n.u)))}}}function gl(e,t,r){return{v:e,ixfe:t,t:r}}var bl={SI:"e0859ff2f94f6810ab9108002b27b3d9",DSI:"02d5cdd59c2e1b10939708002b2cf9ae",UDI:"05d5cdd59c2e1b10939708002b2cf9ae"};function wl(e,t){var r,a,n,s;if(t||(t={}),zf(t),h(),t.codepage&&l(t.codepage),e.FullPaths){if(Ie.find(e,"/encryption"))throw new Error("File is password-protected");r=Ie.find(e,"!CompObj"),a=Ie.find(e,"/Workbook")||Ie.find(e,"/Book")}else{switch(t.type){case"base64":e=C(E(e));break;case"binary":e=C(e);break;case"buffer":break;case"array":Array.isArray(e)||(e=Array.prototype.slice.call(e))}Xr(e,0),a={content:e}}if(r&&function(e){var t={},r=e.content;if(r.l=28,t.AnsiUserType=r.read_shift(0,"lpstr-ansi"),t.AnsiClipboardFormat=function(e){return Ha(e,1)}(r),r.length-r.l<=4)return t;var a=r.read_shift(4);0==a||a>40||(r.l-=4,t.Reserved1=r.read_shift(0,"lpstr-ansi"),r.length-r.l<=4||1907505652!==(a=r.read_shift(4))||(t.UnicodeClipboardFormat=function(e){return Ha(e,2)}(r),0==(a=r.read_shift(4))||a>40||(r.l-=4,t.Reserved2=r.read_shift(0,"lpwstr"))))}(r),t.bookProps&&!t.bookSheets)n={};else{var i=k?"buffer":"array";if(a&&a.content)n=function(e,t){var r={opts:{}},a={};null!=w&&null==t.dense&&(t.dense=w);var n={};t.dense&&(n["!data"]=[]);var s,i,o,c,l,h,u,d,p={},m={},v=null,g=[],b="",T={},y="",E={},k=[],_=[],S=[],x={Sheets:[],WBProps:{date1904:!1},Views:[{}]},A={},C=!1,I=function(e){return e<8?en[e]:e<64&&S[e-8]||en[e]},O=function(e,t,r){if((C||!(W>1))&&!(r.sheetRows&&e.r>=r.sheetRows)){if(r.cellStyles&&t.XF&&t.XF.data&&function(e,t){var r,a=e.XF.data;a&&a.patternType&&t&&t.cellStyles&&(e.s={},e.s.patternType=a.patternType,(r=yi(I(a.icvFore)))&&(e.s.fgColor={rgb:r}),(r=yi(I(a.icvBack)))&&(e.s.bgColor={rgb:r}))}(t,r),delete t.ixfe,delete t.XF,s=e,y=oa(e),m&&m.s&&m.e||(m={s:{r:0,c:0},e:{r:0,c:0}}),e.r<m.s.r&&(m.s.r=e.r),e.c<m.s.c&&(m.s.c=e.c),e.r+1>m.e.r&&(m.e.r=e.r+1),e.c+1>m.e.c&&(m.e.c=e.c+1),r.cellFormula&&t.f)for(var a=0;a<k.length;++a)if(!(k[a][0].s.c>e.c||k[a][0].s.r>e.r||k[a][0].e.c<e.c||k[a][0].e.r<e.r)){t.F=la(k[a][0]),k[a][0].s.c==e.c&&k[a][0].s.r==e.r||delete t.f,t.f&&(t.f=""+zo(k[a][1],0,e,L,R));break}r.dense?(n["!data"][e.r]||(n["!data"][e.r]=[]),n["!data"][e.r][e.c]=t):n[y]=t}},R={enc:!1,sbcch:0,snames:[],sharedf:E,arrayf:k,rrtabid:[],lastuser:"",biff:8,codepage:0,winlocked:0,cellStyles:!!t&&!!t.cellStyles,WTF:!!t&&!!t.wtf};t.password&&(R.password=t.password);var N=[],F=[],D=[],P=[],M=!1,L=[];L.SheetNames=R.snames,L.sharedf=R.sharedf,L.arrayf=R.arrayf,L.names=[],L.XTI=[];var U,B=0,W=0,z=0,H=[],V=[];R.codepage=1200,f(1200);for(var $=!1;e.l<e.length-1;){var X=e.l,j=e.read_shift(2);if(0===j&&10===B)break;var K=e.l===e.length?0:e.read_shift(2),Y=yl[j];if(0==W&&-1==[9,521,1033,2057].indexOf(j))break;if(Y&&Y.f){if(t.bookSheets&&133===B&&133!==j)break;if(B=j,2===Y.r||12==Y.r){var Z=e.read_shift(2);if(K-=2,!R.enc&&Z!==j&&((255&Z)<<8|Z>>8)!==j)throw new Error("rt mismatch: "+Z+"!="+j);12==Y.r&&(e.l+=10,K-=10)}var J={};if(J=10===j?Y.f(e,K,R):ml(j,Y,e,K,R),0==W&&-1===[9,521,1033,2057].indexOf(B))continue;switch(j){case 34:r.opts.Date1904=x.WBProps.date1904=J;break;case 134:r.opts.WriteProtect=!0;break;case 47:if(R.enc||(e.l=0),R.enc=J,!t.password)throw new Error("File is password-protected");if(null==J.valid)throw new Error("Encryption scheme unsupported");if(!J.valid)throw new Error("Password is incorrect");break;case 92:R.lastuser=J;break;case 66:var q=Number(J);switch(q){case 21010:q=1200;break;case 32768:q=1e4;break;case 32769:q=1252}f(R.codepage=q),$=!0;break;case 317:R.rrtabid=J;break;case 25:R.winlocked=J;break;case 439:r.opts.RefreshAll=J;break;case 12:r.opts.CalcCount=J;break;case 16:r.opts.CalcDelta=J;break;case 17:r.opts.CalcIter=J;break;case 13:r.opts.CalcMode=J;break;case 14:r.opts.CalcPrecision=J;break;case 95:r.opts.CalcSaveRecalc=J;break;case 15:R.CalcRefMode=J;break;case 2211:r.opts.FullCalc=J;break;case 129:J.fDialog&&(n["!type"]="dialog"),J.fBelow||((n["!outline"]||(n["!outline"]={})).above=!0),J.fRight||((n["!outline"]||(n["!outline"]={})).left=!0);break;case 67:case 579:case 1091:case 224:_.push(J);break;case 430:L.push([J]),L[L.length-1].XTI=[];break;case 35:case 547:L[L.length-1].push(J);break;case 24:case 536:U={Name:J.Name,Ref:zo(J.rgce,0,null,L,R)},J.itab>0&&(U.Sheet=J.itab-1),L.names.push(U),L[0]||(L[0]=[],L[0].XTI=[]),L[L.length-1].push(J),"_xlnm._FilterDatabase"==J.Name&&J.itab>0&&J.rgce&&J.rgce[0]&&J.rgce[0][0]&&"PtgArea3d"==J.rgce[0][0][0]&&(V[J.itab-1]={ref:la(J.rgce[0][0][1][2])});break;case 22:R.ExternCount=J;break;case 23:0==L.length&&(L[0]=[],L[0].XTI=[]),L[L.length-1].XTI=L[L.length-1].XTI.concat(J),L.XTI=L.XTI.concat(J);break;case 2196:if(R.biff<8)break;null!=U&&(U.Comment=J[1]);break;case 18:n["!protect"]=J;break;case 19:0!==J&&R.WTF&&console.error("Password verifier: "+J);break;case 133:p[4==R.biff?R.snames.length:J.pos]=J,R.snames.push(J.name);break;case 10:if(--W?!C:C)break;if(m.e){if(m.e.r>0&&m.e.c>0){if(m.e.r--,m.e.c--,n["!ref"]=la(m),t.sheetRows&&t.sheetRows<=m.e.r){var Q=m.e.r;m.e.r=t.sheetRows-1,n["!fullref"]=n["!ref"],n["!ref"]=la(m),m.e.r=Q}m.e.r++,m.e.c++}N.length>0&&(n["!merges"]=N),F.length>0&&(n["!objects"]=F),D.length>0&&(n["!cols"]=D),P.length>0&&(n["!rows"]=P),x.Sheets.push(A)}""===b?T=n:a[b]=n,n={},t.dense&&(n["!data"]=[]);break;case 9:case 521:case 1033:case 2057:if(8===R.biff&&(R.biff={9:2,521:3,1033:4}[j]||{512:2,768:3,1024:4,1280:5,1536:8,2:2,7:2}[J.BIFFVer]||8),R.biffguess=0==J.BIFFVer,0==J.BIFFVer&&4096==J.dt&&(R.biff=5,$=!0,f(R.codepage=28591)),4==R.biff&&256&J.dt&&(C=!0),8==R.biff&&0==J.BIFFVer&&16==J.dt&&(R.biff=2),W++&&!C)break;if(n={},t.dense&&(n["!data"]=[]),R.biff<8&&!$&&($=!0,f(R.codepage=t.codepage||1252)),4==R.biff&&C)b=(p[R.snames.indexOf(b)+1]||{name:""}).name;else if(R.biff<5||0==J.BIFFVer&&4096==J.dt){""===b&&(b="Sheet1"),m={s:{r:0,c:0},e:{r:0,c:0}};var ee={pos:e.l-K,name:b};p[ee.pos]=ee,R.snames.push(b)}else b=(p[X]||{name:""}).name;32==J.dt&&(n["!type"]="chart"),64==J.dt&&(n["!type"]="macro"),N=[],F=[],R.arrayf=k=[],D=[],P=[],M=!1,A={Hidden:(p[X]||{hs:0}).hs,name:b};break;case 515:case 3:case 2:"chart"==n["!type"]&&(t.dense?(n["!data"][J.r]||[])[J.c]:n[sa(J.c)+aa(J.r)])&&++J.c,h={ixfe:J.ixfe,XF:_[J.ixfe]||{},v:J.val,t:"n"},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t);break;case 5:case 517:h={ixfe:J.ixfe,XF:_[J.ixfe],v:J.val,t:J.t},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t);break;case 638:h={ixfe:J.ixfe,XF:_[J.ixfe],v:J.rknum,t:"n"},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t);break;case 189:for(var te=J.c;te<=J.C;++te){var re=J.rkrec[te-J.c][0];h={ixfe:re,XF:_[re],v:J.rkrec[te-J.c][1],t:"n"},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:te,r:J.r},h,t)}break;case 6:case 518:case 1030:if("String"==J.val){v=J;break}if((h=gl(J.val,J.cell.ixfe,J.tt)).XF=_[h.ixfe],t.cellFormula){var ae=J.formula;if(ae&&ae[0]&&ae[0][0]&&"PtgExp"==ae[0][0][0]){var ne=ae[0][0][1][0],se=ae[0][0][1][1],ie=oa({r:ne,c:se});E[ie]?h.f=""+zo(J.formula,0,J.cell,L,R):h.F=((t.dense?(n["!data"][ne]||[])[se]:n[ie])||{}).F}else h.f=""+zo(J.formula,0,J.cell,L,R)}z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O(J.cell,h,t),v=J;break;case 7:case 519:if(!v)throw new Error("String record expects Formula");v.val=J,(h=gl(J,v.cell.ixfe,"s")).XF=_[h.ixfe],t.cellFormula&&(h.f=""+zo(v.formula,0,v.cell,L,R)),z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O(v.cell,h,t),v=null;break;case 33:case 545:k.push(J);var oe=oa(J[0].s);if(i=t.dense?(n["!data"][J[0].s.r]||[])[J[0].s.c]:n[oe],t.cellFormula&&i){if(!v)break;if(!oe||!i)break;i.f=""+zo(J[1],0,J[0],L,R),i.F=la(J[0])}break;case 1212:if(!t.cellFormula)break;if(y){if(!v)break;E[oa(v.cell)]=J[0],((i=t.dense?(n["!data"][v.cell.r]||[])[v.cell.c]:n[oa(v.cell)])||{}).f=""+zo(J[0],0,s,L,R)}break;case 253:h=gl(g[J.isst].t,J.ixfe,"s"),g[J.isst].h&&(h.h=g[J.isst].h),h.XF=_[h.ixfe],z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t);break;case 513:t.sheetStubs&&(h={ixfe:J.ixfe,XF:_[J.ixfe],t:"z"},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t));break;case 190:if(t.sheetStubs)for(var ce=J.c;ce<=J.C;++ce){var le=J.ixfe[ce-J.c];h={ixfe:le,XF:_[le],t:"z"},z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:ce,r:J.r},h,t)}break;case 214:case 516:case 4:(h=gl(J.val,J.ixfe,"s")).XF=_[h.ixfe],z>0&&(h.z=h.XF&&h.XF.numFmtId&&H[h.XF.numFmtId]||H[h.ixfe>>8&63]),vl(h,t,r.opts.Date1904),O({c:J.c,r:J.r},h,t);break;case 0:case 512:1===W&&(m=J);break;case 252:g=J;break;case 1054:if(R.biff>=3&&R.biff<=4){H[z++]=J[1];for(var fe=0;fe<z+163&&G[fe]!=J[1];++fe);fe>=163&&xe(J[1],z+163)}else xe(J[1],J[0]);break;case 30:H[z++]=J;for(var he=0;he<z+163&&G[he]!=J;++he);he>=163&&xe(J,z+163);break;case 229:N=N.concat(J);break;case 93:F[J.cmo[0]]=R.lastobj=J;break;case 438:R.lastobj.TxO=J;break;case 127:R.lastobj.ImData=J;break;case 440:for(l=J[0].s.r;l<=J[0].e.r;++l)for(c=J[0].s.c;c<=J[0].e.c;++c)(i=t.dense?(n["!data"][l]||[])[c]:n[oa({c:c,r:l})])&&(i.l=J[1]);break;case 2048:for(l=J[0].s.r;l<=J[0].e.r;++l)for(c=J[0].s.c;c<=J[0].e.c;++c)(i=t.dense?(n["!data"][l]||[])[c]:n[oa({c:c,r:l})])&&i.l&&(i.l.Tooltip=J[1]);break;case 28:if((i=t.dense?(n["!data"][J[0].r]||[])[J[0].c]:n[oa(J[0])])||(t.dense?(n["!data"][J[0].r]||(n["!data"][J[0].r]=[]),i=n["!data"][J[0].r][J[0].c]={t:"z"}):i=n[oa(J[0])]={t:"z"},m.e.r=Math.max(m.e.r,J[0].r),m.s.r=Math.min(m.s.r,J[0].r),m.e.c=Math.max(m.e.c,J[0].c),m.s.c=Math.min(m.s.c,J[0].c)),i.c||(i.c=[]),R.biff<=5&&R.biff>=2)o={a:"SheetJ5",t:J[1]};else{var ue=F[J[2]];o={a:J[1],t:ue.TxO.t},null==J[3]||2&J[3]||(i.c.hidden=!0)}i.c.push(o);break;case 2173:_[J.ixfe],J.ext.forEach((function(e){e[0]}));break;case 125:if(!R.cellStyles)break;for(;J.e>=J.s;)D[J.e--]={width:J.w/256,level:J.level||0,hidden:!!(1&J.flags)},M||(M=!0,Ri(J.w/256)),Ni(D[J.e+1]);break;case 520:var de={};null!=J.level&&(P[J.r]=de,de.level=J.level),J.hidden&&(P[J.r]=de,de.hidden=!0),J.hpt&&(P[J.r]=de,de.hpt=J.hpt,de.hpx=Pi(J.hpt));break;case 38:case 39:case 40:case 41:n["!margins"]||lc(n["!margins"]={}),n["!margins"][{38:"left",39:"right",40:"top",41:"bottom"}[j]]=J;break;case 161:n["!margins"]||lc(n["!margins"]={}),n["!margins"].header=J.header,n["!margins"].footer=J.footer;break;case 574:J.RTL&&(x.Views[0].RTL=!0);break;case 146:S=J;break;case 2198:d=J;break;case 140:u=J;break;case 442:b?A.CodeName=J||A.name:x.WBProps.CodeName=J||"ThisWorkbook"}}else Y||console.error("Missing Info for XLS Record 0x"+j.toString(16)),e.l+=K}return r.SheetNames=Fe(p).sort((function(e,t){return Number(e)-Number(t)})).map((function(e){return p[e].name})),t.bookSheets||(r.Sheets=a),!r.SheetNames.length&&T["!ref"]?(r.SheetNames.push("Sheet1"),r.Sheets&&(r.Sheets.Sheet1=T)):r.Preamble=T,r.Sheets&&V.forEach((function(e,t){r.Sheets[r.SheetNames[t]]["!autofilter"]=e})),r.Strings=g,r.SSF=Ke(G),R.enc&&(r.Encryption=R.enc),d&&(r.Themes=d),r.Metadata={},void 0!==u&&(r.Metadata.Country=u),L.names.length>0&&(x.Names=L.names),r.Workbook=x,r}(a.content,t);else if((s=Ie.find(e,"PerfectOffice_MAIN"))&&s.content)n=ei.to_workbook(s.content,(t.type=i,t));else{if(!(s=Ie.find(e,"NativeContent_MAIN"))||!s.content)throw(s=Ie.find(e,"MN0"))&&s.content?new Error("Unsupported Works 4 for Mac file"):new Error("Cannot find Workbook stream");n=ei.to_workbook(s.content,(t.type=i,t))}t.bookVBA&&e.FullPaths&&Ie.find(e,"/_VBA_PROJECT_CUR/VBA/dir")&&(n.vbaraw=function(e){var t=Ie.utils.cfb_new({root:"R"});return e.FullPaths.forEach((function(r,a){if("/"!==r.slice(-1)&&r.match(/_VBA_PROJECT_CUR/)){var n=r.replace(/^[^\/]*/,"R").replace(/\/_VBA_PROJECT_CUR\u0000*/,"");Ie.utils.cfb_add(t,n,e.FileIndex[a].content)}})),Ie.write(t)}(e))}var o={};return e.FullPaths&&function(e,t,r){var a=Ie.find(e,"/!DocumentSummaryInformation");if(a&&a.size>0)try{var n=Vn(a,Ya,bl.DSI);for(var s in n)t[s]=n[s]}catch(e){if(r.WTF)throw e}var i=Ie.find(e,"/!SummaryInformation");if(i&&i.size>0)try{var o=Vn(i,Za,bl.SI);for(var c in o)null==t[c]&&(t[c]=o[c])}catch(e){if(r.WTF)throw e}t.HeadingPairs&&t.TitlesOfParts&&(En(t.HeadingPairs,t.TitlesOfParts,t,r),delete t.HeadingPairs,delete t.TitlesOfParts)}(e,o,t),n.Props=n.Custprops=o,t.bookFiles&&(n.cfb=e),n}var Tl={0:{f:function(e,t){var r={},a=e.l+t;r.r=e.read_shift(4),e.l+=4;var n=e.read_shift(2);e.l+=1;var s=e.read_shift(1);return e.l=a,7&s&&(r.level=7&s),16&s&&(r.hidden=!0),32&s&&(r.hpt=n/20),r}},1:{f:function(e){return[ka(e)]}},2:{f:function(e){return[ka(e),Da(e),"n"]}},3:{f:function(e){return[ka(e),e.read_shift(1),"e"]}},4:{f:function(e){return[ka(e),e.read_shift(1),"b"]}},5:{f:function(e){return[ka(e),Ba(e),"n"]}},6:{f:function(e){return[ka(e),ba(e),"str"]}},7:{f:function(e){return[ka(e),e.read_shift(4),"s"]}},8:{f:function(e,t,r){var a=e.l+t,n=ka(e);n.r=r["!row"];var s=[n,ba(e),"str"];if(r.cellFormula){e.l+=2;var i=jo(e,a-e.l,r);s[3]=zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},9:{f:function(e,t,r){var a=e.l+t,n=ka(e);n.r=r["!row"];var s=[n,Ba(e),"n"];if(r.cellFormula){e.l+=2;var i=jo(e,a-e.l,r);s[3]=zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},10:{f:function(e,t,r){var a=e.l+t,n=ka(e);n.r=r["!row"];var s=[n,e.read_shift(1),"b"];if(r.cellFormula){e.l+=2;var i=jo(e,a-e.l,r);s[3]=zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},11:{f:function(e,t,r){var a=e.l+t,n=ka(e);n.r=r["!row"];var s=[n,e.read_shift(1),"e"];if(r.cellFormula){e.l+=2;var i=jo(e,a-e.l,r);s[3]=zo(i,0,n,r.supbooks,r)}else e.l=a;return s}},12:{f:function(e){return[Sa(e)]}},13:{f:function(e){return[Sa(e),Da(e),"n"]}},14:{f:function(e){return[Sa(e),e.read_shift(1),"e"]}},15:{f:function(e){return[Sa(e),e.read_shift(1),"b"]}},16:{f:Nc},17:{f:function(e){return[Sa(e),ba(e),"str"]}},18:{f:function(e){return[Sa(e),e.read_shift(4),"s"]}},19:{f:ya},20:{},21:{},22:{},23:{},24:{},25:{},26:{},27:{},28:{},29:{},30:{},31:{},32:{},33:{},34:{},35:{T:1},36:{T:-1},37:{T:1},38:{T:-1},39:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(4);e.l+=1;var s,i=e.read_shift(4),o=Ra(e),c="";try{s=Ko(e,0,r);try{c=Ia(e)}catch(e){}}catch(e){console.error("Could not parse defined name "+o)}32&n&&(o="_xlnm."+o),e.l=a;var l={Name:o,Ptg:s,Flags:n};return i<268435455&&(l.Sheet=i),c&&(l.Comment=c),l}},40:{},42:{},43:{f:function(e,t,r){var a={};a.sz=e.read_shift(2)/20;var n=function(e){var t=e.read_shift(1);return e.l++,{fBold:1&t,fItalic:2&t,fUnderline:4&t,fStrikeout:8&t,fOutline:16&t,fShadow:32&t,fCondense:64&t,fExtend:128&t}}(e);switch(n.fItalic&&(a.italic=1),n.fCondense&&(a.condense=1),n.fExtend&&(a.extend=1),n.fShadow&&(a.shadow=1),n.fOutline&&(a.outline=1),n.fStrikeout&&(a.strike=1),700===e.read_shift(2)&&(a.bold=1),e.read_shift(2)){case 1:a.vertAlign="superscript";break;case 2:a.vertAlign="subscript"}var s=e.read_shift(1);0!=s&&(a.underline=s);var i=e.read_shift(1);i>0&&(a.family=i);var o=e.read_shift(1);switch(o>0&&(a.charset=o),e.l++,a.color=function(e){var t={},r=e.read_shift(1)>>>1,a=e.read_shift(1),n=e.read_shift(2,"i"),s=e.read_shift(1),i=e.read_shift(1),o=e.read_shift(1);switch(e.l++,r){case 0:t.auto=1;break;case 1:t.index=a;var c=en[a];c&&(t.rgb=yi(c));break;case 2:t.rgb=yi([s,i,o]);break;case 3:t.theme=a}return 0!=n&&(t.tint=n>0?n/32767:n/32768),t}(e),e.read_shift(1)){case 1:a.scheme="major";break;case 2:a.scheme="minor"}return a.name=ba(e),a}},44:{f:function(e,t){return[e.read_shift(2),ba(e)]}},45:{f:Vi},46:{f:ji},47:{f:function(e,t){var r=e.l+t,a=e.read_shift(2),n=e.read_shift(2);return e.l=r,{ixfe:a,numFmtId:n}}},48:{},49:{f:function(e){return e.read_shift(4,"i")}},50:{},51:{f:function(e){for(var t=[],r=e.read_shift(4);r-- >0;)t.push([e.read_shift(4),e.read_shift(4)]);return t}},52:{T:1},53:{T:-1},54:{T:1},55:{T:-1},56:{T:1},57:{T:-1},58:{},59:{},60:{f:Vs},62:{f:function(e){return[ka(e),ya(e),"is"]}},63:{f:function(e){var t={};t.i=e.read_shift(4);var r={};r.r=e.read_shift(4),r.c=e.read_shift(4),t.r=oa(r);var a=e.read_shift(1);return 2&a&&(t.l="1"),8&a&&(t.a="1"),t}},64:{f:function(){}},65:{},66:{},67:{},68:{},69:{},70:{},128:{},129:{T:1},130:{T:-1},131:{T:1,f:jr,p:0},132:{T:-1},133:{T:1},134:{T:-1},135:{T:1},136:{T:-1},137:{T:1,f:function(e){var t=e.read_shift(2);return e.l+=28,{RTL:32&t}}},138:{T:-1},139:{T:1},140:{T:-1},141:{T:1},142:{T:-1},143:{T:1},144:{T:-1},145:{T:1},146:{T:-1},147:{f:function(e,t){var r={},a=e[e.l];return++e.l,r.above=!(64&a),r.left=!(128&a),e.l+=18,r.name=Aa(e,t-19),r}},148:{f:Cc,p:16},151:{f:function(){}},152:{},153:{f:function(e,t){var r={},a=e.read_shift(4);r.defaultThemeVersion=e.read_shift(4);var n=t>8?ba(e):"";return n.length>0&&(r.CodeName=n),r.autoCompressPictures=!!(65536&a),r.backupFile=!!(64&a),r.checkCompatibility=!!(4096&a),r.date1904=!!(1&a),r.filterPrivacy=!!(8&a),r.hidePivotFieldList=!!(1024&a),r.promptedSolutions=!!(16&a),r.publishItems=!!(2048&a),r.refreshAllConnections=!!(262144&a),r.saveExternalLinkValues=!!(128&a),r.showBorderUnselectedTables=!!(4&a),r.showInkAnnotation=!!(32&a),r.showObjects=["all","placeholders","none"][a>>13&3],r.showPivotChartFilter=!!(32768&a),r.updateLinks=["userSet","never","always"][a>>8&3],r}},154:{},155:{},156:{f:function(e,t){var r={};return r.Hidden=e.read_shift(4),r.iTabID=e.read_shift(4),r.strRelID=Na(e,t-8),r.name=ba(e),r}},157:{},158:{},159:{T:1,f:function(e){return[e.read_shift(4),e.read_shift(4)]}},160:{T:-1},161:{T:1,f:La},162:{T:-1},163:{T:1},164:{T:-1},165:{T:1},166:{T:-1},167:{},168:{},169:{},170:{},171:{},172:{T:1},173:{T:-1},174:{},175:{},176:{f:Fc},177:{T:1},178:{T:-1},179:{T:1},180:{T:-1},181:{T:1},182:{T:-1},183:{T:1},184:{T:-1},185:{T:1},186:{T:-1},187:{T:1},188:{T:-1},189:{T:1},190:{T:-1},191:{T:1},192:{T:-1},193:{T:1},194:{T:-1},195:{T:1},196:{T:-1},197:{T:1},198:{T:-1},199:{T:1},200:{T:-1},201:{T:1},202:{T:-1},203:{T:1},204:{T:-1},205:{T:1},206:{T:-1},207:{T:1},208:{T:-1},209:{T:1},210:{T:-1},211:{T:1},212:{T:-1},213:{T:1},214:{T:-1},215:{T:1},216:{T:-1},217:{T:1},218:{T:-1},219:{T:1},220:{T:-1},221:{T:1},222:{T:-1},223:{T:1},224:{T:-1},225:{T:1},226:{T:-1},227:{T:1},228:{T:-1},229:{T:1},230:{T:-1},231:{T:1},232:{T:-1},233:{T:1},234:{T:-1},235:{T:1},236:{T:-1},237:{T:1},238:{T:-1},239:{T:1},240:{T:-1},241:{T:1},242:{T:-1},243:{T:1},244:{T:-1},245:{T:1},246:{T:-1},247:{T:1},248:{T:-1},249:{T:1},250:{T:-1},251:{T:1},252:{T:-1},253:{T:1},254:{T:-1},255:{T:1},256:{T:-1},257:{T:1},258:{T:-1},259:{T:1},260:{T:-1},261:{T:1},262:{T:-1},263:{T:1},264:{T:-1},265:{T:1},266:{T:-1},267:{T:1},268:{T:-1},269:{T:1},270:{T:-1},271:{T:1},272:{T:-1},273:{T:1},274:{T:-1},275:{T:1},276:{T:-1},277:{},278:{T:1},279:{T:-1},280:{T:1},281:{T:-1},282:{T:1},283:{T:1},284:{T:-1},285:{T:1},286:{T:-1},287:{T:1},288:{T:-1},289:{T:1},290:{T:-1},291:{T:1},292:{T:-1},293:{T:1},294:{T:-1},295:{T:1},296:{T:-1},297:{T:1},298:{T:-1},299:{T:1},300:{T:-1},301:{T:1},302:{T:-1},303:{T:1},304:{T:-1},305:{T:1},306:{T:-1},307:{T:1},308:{T:-1},309:{T:1},310:{T:-1},311:{T:1},312:{T:-1},313:{T:-1},314:{T:1},315:{T:-1},316:{T:1},317:{T:-1},318:{T:1},319:{T:-1},320:{T:1},321:{T:-1},322:{T:1},323:{T:-1},324:{T:1},325:{T:-1},326:{T:1},327:{T:-1},328:{T:1},329:{T:-1},330:{T:1},331:{T:-1},332:{T:1},333:{T:-1},334:{T:1},335:{f:function(e,t){return{flags:e.read_shift(4),version:e.read_shift(4),name:ba(e)}}},336:{T:-1},337:{f:function(e){return e.l+=4,0!=e.read_shift(4)},T:1},338:{T:-1},339:{T:1},340:{T:-1},341:{T:1},342:{T:-1},343:{T:1},344:{T:-1},345:{T:1},346:{T:-1},347:{T:1},348:{T:-1},349:{T:1},350:{T:-1},351:{},352:{},353:{T:1},354:{T:-1},355:{f:Na},357:{},358:{},359:{},360:{T:1},361:{},362:{f:Ms},363:{},364:{},366:{},367:{},368:{},369:{},370:{},371:{},372:{T:1},373:{T:-1},374:{T:1},375:{T:-1},376:{T:1},377:{T:-1},378:{T:1},379:{T:-1},380:{T:1},381:{T:-1},382:{T:1},383:{T:-1},384:{T:1},385:{T:-1},386:{T:1},387:{T:-1},388:{T:1},389:{T:-1},390:{T:1},391:{T:-1},392:{T:1},393:{T:-1},394:{T:1},395:{T:-1},396:{},397:{},398:{},399:{},400:{},401:{T:1},403:{},404:{},405:{},406:{},407:{},408:{},409:{},410:{},411:{},412:{},413:{},414:{},415:{},416:{},417:{},418:{},419:{},420:{},421:{},422:{T:1},423:{T:1},424:{T:-1},425:{T:-1},426:{f:function(e,t,r){var a=e.l+t,n=Ma(e),s=e.read_shift(1),i=[n];if(i[2]=s,r.cellFormula){var o=Xo(e,a-e.l,r);i[1]=o}else e.l=a;return i}},427:{f:function(e,t,r){var a=e.l+t,n=[La(e,16)];if(r.cellFormula){var s=Yo(e,a-e.l,r);n[1]=s,e.l=a}else e.l=a;return n}},428:{},429:{T:1},430:{T:-1},431:{T:1},432:{T:-1},433:{T:1},434:{T:-1},435:{T:1},436:{T:-1},437:{T:1},438:{T:-1},439:{T:1},440:{T:-1},441:{T:1},442:{T:-1},443:{T:1},444:{T:-1},445:{T:1},446:{T:-1},447:{T:1},448:{T:-1},449:{T:1},450:{T:-1},451:{T:1},452:{T:-1},453:{T:1},454:{T:-1},455:{T:1},456:{T:-1},457:{T:1},458:{T:-1},459:{T:1},460:{T:-1},461:{T:1},462:{T:-1},463:{T:1},464:{T:-1},465:{T:1},466:{T:-1},467:{T:1},468:{T:-1},469:{T:1},470:{T:-1},471:{},472:{},473:{T:1},474:{T:-1},475:{},476:{f:function(e){var t={};return Pc.forEach((function(r){t[r]=Ba(e)})),t}},477:{},478:{},479:{T:1},480:{T:-1},481:{T:1},482:{T:-1},483:{T:1},484:{T:-1},485:{f:function(){}},486:{T:1},487:{T:-1},488:{T:1},489:{T:-1},490:{T:1},491:{T:-1},492:{T:1},493:{T:-1},494:{f:function(e,t){var r=e.l+t,a=La(e,16),n=Ia(e),s=ba(e),i=ba(e),o=ba(e);e.l=r;var c={rfx:a,relId:n,loc:s,display:o};return i&&(c.Tooltip=i),c}},495:{T:1},496:{T:-1},497:{T:1},498:{T:-1},499:{},500:{T:1},501:{T:-1},502:{T:1},503:{T:-1},504:{},505:{T:1},506:{T:-1},507:{},508:{T:1},509:{T:-1},510:{T:1},511:{T:-1},512:{},513:{},514:{T:1},515:{T:-1},516:{T:1},517:{T:-1},518:{T:1},519:{T:-1},520:{T:1},521:{T:-1},522:{},523:{},524:{},525:{},526:{},527:{},528:{T:1},529:{T:-1},530:{T:1},531:{T:-1},532:{T:1},533:{T:-1},534:{},535:{},536:{},537:{},538:{T:1},539:{T:-1},540:{T:1},541:{T:-1},542:{T:1},548:{},549:{},550:{f:Na},551:{f:Ia},552:{},553:{},554:{T:1},555:{T:-1},556:{T:1},557:{T:-1},558:{T:1},559:{T:-1},560:{T:1},561:{T:-1},562:{},564:{},565:{T:1},566:{T:-1},569:{T:1},570:{T:-1},572:{},573:{T:1},574:{T:-1},577:{},578:{},579:{},580:{},581:{},582:{},583:{},584:{},585:{},586:{},587:{},588:{T:-1},589:{},590:{T:1},591:{T:-1},592:{T:1},593:{T:-1},594:{T:1},595:{T:-1},596:{},597:{T:1},598:{T:-1},599:{T:1},600:{T:-1},601:{T:1},602:{T:-1},603:{T:1},604:{T:-1},605:{T:1},606:{T:-1},607:{},608:{T:1},609:{T:-1},610:{},611:{T:1},612:{T:-1},613:{T:1},614:{T:-1},615:{T:1},616:{T:-1},617:{T:1},618:{T:-1},619:{T:1},620:{T:-1},625:{},626:{T:1},627:{T:-1},628:{T:1},629:{T:-1},630:{T:1},631:{T:-1},632:{f:no},633:{T:1},634:{T:-1},635:{T:1,f:function(e){var t={};t.iauthor=e.read_shift(4);var r=La(e,16);return t.rfx=r.s,t.ref=oa(r.s),e.l+=16,t}},636:{T:-1},637:{f:Ea},638:{T:1},639:{},640:{T:-1},641:{T:1},642:{T:-1},643:{T:1},644:{},645:{T:-1},646:{T:1},648:{T:1},649:{},650:{T:-1},651:{f:function(e,t){return e.l+=10,{name:ba(e)}}},652:{},653:{T:1},654:{T:-1},655:{T:1},656:{T:-1},657:{T:1},658:{T:-1},659:{},660:{T:1},661:{},662:{T:-1},663:{},664:{T:1},665:{},666:{T:-1},667:{},668:{},669:{},671:{T:1},672:{T:-1},673:{T:1},674:{T:-1},675:{},676:{},677:{},678:{},679:{},680:{},681:{},1024:{},1025:{},1026:{T:1},1027:{T:-1},1028:{T:1},1029:{T:-1},1030:{},1031:{T:1},1032:{T:-1},1033:{T:1},1034:{T:-1},1035:{},1036:{},1037:{},1038:{T:1},1039:{T:-1},1040:{},1041:{T:1},1042:{T:-1},1043:{},1044:{},1045:{},1046:{T:1},1047:{T:-1},1048:{T:1},1049:{T:-1},1050:{},1051:{T:1},1052:{T:1},1053:{f:function(){}},1054:{T:1},1055:{},1056:{T:1},1057:{T:-1},1058:{T:1},1059:{T:-1},1061:{},1062:{T:1},1063:{T:-1},1064:{T:1},1065:{T:-1},1066:{T:1},1067:{T:-1},1068:{T:1},1069:{T:-1},1070:{T:1},1071:{T:-1},1072:{T:1},1073:{T:-1},1075:{T:1},1076:{T:-1},1077:{T:1},1078:{T:-1},1079:{T:1},1080:{T:-1},1081:{T:1},1082:{T:-1},1083:{T:1},1084:{T:-1},1085:{},1086:{T:1},1087:{T:-1},1088:{T:1},1089:{T:-1},1090:{T:1},1091:{T:-1},1092:{T:1},1093:{T:-1},1094:{T:1},1095:{T:-1},1096:{},1097:{T:1},1098:{},1099:{T:-1},1100:{T:1},1101:{T:-1},1102:{},1103:{},1104:{},1105:{},1111:{},1112:{},1113:{T:1},1114:{T:-1},1115:{T:1},1116:{T:-1},1117:{},1118:{T:1},1119:{T:-1},1120:{T:1},1121:{T:-1},1122:{T:1},1123:{T:-1},1124:{T:1},1125:{T:-1},1126:{},1128:{T:1},1129:{T:-1},1130:{},1131:{T:1},1132:{T:-1},1133:{T:1},1134:{T:-1},1135:{T:1},1136:{T:-1},1137:{T:1},1138:{T:-1},1139:{T:1},1140:{T:-1},1141:{},1142:{T:1},1143:{T:-1},1144:{T:1},1145:{T:-1},1146:{},1147:{T:1},1148:{T:-1},1149:{T:1},1150:{T:-1},1152:{T:1},1153:{T:-1},1154:{T:-1},1155:{T:-1},1156:{T:-1},1157:{T:1},1158:{T:-1},1159:{T:1},1160:{T:-1},1161:{T:1},1162:{T:-1},1163:{T:1},1164:{T:-1},1165:{T:1},1166:{T:-1},1167:{T:1},1168:{T:-1},1169:{T:1},1170:{T:-1},1171:{},1172:{T:1},1173:{T:-1},1177:{},1178:{T:1},1180:{},1181:{},1182:{},2048:{T:1},2049:{T:-1},2050:{},2051:{T:1},2052:{T:-1},2053:{},2054:{},2055:{T:1},2056:{T:-1},2057:{T:1},2058:{T:-1},2060:{},2067:{},2068:{T:1},2069:{T:-1},2070:{},2071:{},2072:{T:1},2073:{T:-1},2075:{},2076:{},2077:{T:1},2078:{T:-1},2079:{},2080:{T:1},2081:{T:-1},2082:{},2083:{T:1},2084:{T:-1},2085:{T:1},2086:{T:-1},2087:{T:1},2088:{T:-1},2089:{T:1},2090:{T:-1},2091:{},2092:{},2093:{T:1},2094:{T:-1},2095:{},2096:{T:1},2097:{T:-1},2098:{T:1},2099:{T:-1},2100:{T:1},2101:{T:-1},2102:{},2103:{T:1},2104:{T:-1},2105:{},2106:{T:1},2107:{T:-1},2108:{},2109:{T:1},2110:{T:-1},2111:{T:1},2112:{T:-1},2113:{T:1},2114:{T:-1},2115:{},2116:{},2117:{},2118:{T:1},2119:{T:-1},2120:{},2121:{T:1},2122:{T:-1},2123:{T:1},2124:{T:-1},2125:{},2126:{T:1},2127:{T:-1},2128:{},2129:{T:1},2130:{T:-1},2131:{T:1},2132:{T:-1},2133:{T:1},2134:{},2135:{},2136:{},2137:{T:1},2138:{T:-1},2139:{T:1},2140:{T:-1},2141:{},3072:{},3073:{},4096:{T:1},4097:{T:-1},5002:{T:1},5003:{T:-1},5081:{T:1},5082:{T:-1},5083:{},5084:{T:1},5085:{T:-1},5086:{T:1},5087:{T:-1},5088:{},5089:{},5090:{},5092:{T:1},5093:{T:-1},5094:{},5095:{T:1},5096:{T:-1},5097:{},5099:{},65535:{n:""}},yl={6:{f:$o},10:{f:Gn},12:{f:Kn},13:{f:Kn},14:{f:Xn},15:{f:Xn},16:{f:Ba},17:{f:Xn},18:{f:Xn},19:{f:Kn},20:{f:Fs},21:{f:Fs},23:{f:Ms},24:{f:Ps},25:{f:Xn},26:{},27:{},28:{f:function(e,t,r){if(r&&r.biff<8){var a=e.read_shift(2),n=e.read_shift(2);if(65535==a||-1==a)return;var s=e.read_shift(2);return[{r:a,c:n},e.read_shift(Math.min(s,2048),"cpstr")]}return function(e,t,r){var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2),i=e.read_shift(2);return[{r:a,c:n},ns(e,0,r),i,s]}(e,0,r)}},29:{},34:{f:Xn},35:{f:Ds},38:{f:Ba},39:{f:Ba},40:{f:Ba},41:{f:Ba},42:{f:Xn},43:{f:Xn},47:{f:function(e,t,r){var a={Type:r.biff>=8?e.read_shift(2):0};return a.Type?function(e,t,r){var a=r||{};a.Info=e.read_shift(2),e.l-=2,1===a.Info?a.Data=function(e){var t={},r=t.EncryptionVersionInfo=hi(e,4);if(1!=r.Major||1!=r.Minor)throw"unrecognized version code "+r.Major+" : "+r.Minor;return t.Salt=e.read_shift(16),t.EncryptedVerifier=e.read_shift(16),t.EncryptedVerifierHash=e.read_shift(16),t}(e):a.Data=function(e,t){var r={},a=r.EncryptionVersionInfo=hi(e,4);if(t-=4,2!=a.Minor)throw new Error("unrecognized minor version code: "+a.Minor);if(a.Major>4||a.Major<2)throw new Error("unrecognized major version code: "+a.Major);r.Flags=e.read_shift(4),t-=4;var n=e.read_shift(4);return t-=4,r.EncryptionHeader=di(e,n),t-=n,r.EncryptionVerifier=pi(e,t),r}(e,t)}(e,t-2,a):function(e,t,r,a){var n,s,i,o={key:Kn(e),verificationBytes:Kn(e)};r.password&&(o.verifier=bi(r.password)),a.valid=o.verificationBytes===o.verifier,a.valid&&(a.insitu=(n=r.password,s=0,i=wi(n),function(e){var t=function(e,t,r,a,n){var s,i;for(n||(n=t),a||(a=wi("")),s=0;s!=t.length;++s)i=t[s],i=255&((i^=a[r])>>5|i<<3),n[s]=i,++r;return[n,r,a]}(0,e,s,i);return s=t[1],t[0]}))}(e,r.biff,r,a),a}},49:{f:function(e,t,r){var a={dyHeight:e.read_shift(2),fl:e.read_shift(2)};switch(r&&r.biff||8){case 2:break;case 3:case 4:e.l+=2;break;default:e.l+=10}return a.name=Qn(e,0,r),a}},51:{f:Kn},60:{},61:{f:function(e){return{Pos:[e.read_shift(2),e.read_shift(2)],Dim:[e.read_shift(2),e.read_shift(2)],Flags:e.read_shift(2),CurTab:e.read_shift(2),FirstTab:e.read_shift(2),Selected:e.read_shift(2),TabRatio:e.read_shift(2)}}},64:{f:Xn},65:{f:function(){}},66:{f:Kn},77:{},80:{},81:{},82:{},85:{f:Kn},89:{},90:{},91:{},92:{f:function(e,t,r){if(r.enc)return e.l+=t,"";var a=e.l,n=ns(e,0,r);return e.read_shift(t+a-e.l),n}},93:{f:function(e,t,r){if(r&&r.biff<8)return function(e,t,r){e.l+=4;var a=e.read_shift(2),n=e.read_shift(2),s=e.read_shift(2);e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=2,e.l+=6,t-=36;var i=[];return i.push((Ws[a]||jr)(e,t,r)),{cmo:[n,a,s],ft:i}}(e,t,r);var a=ws(e),n=function(e,t){for(var r=e.l+t,a=[];e.l<r;){var n=e.read_shift(2);e.l-=2;try{a[n]=ys[n](e,r-e.l)}catch(t){return e.l=r,a}}return e.l!=r&&(e.l=r),a}(e,t-22,a[1]);return{cmo:a,ft:n}}},94:{},95:{f:Xn},96:{},97:{},99:{f:Xn},125:{f:Vs},128:{f:function(e){e.l+=4;var t=[e.read_shift(2),e.read_shift(2)];if(0!==t[0]&&t[0]--,0!==t[1]&&t[1]--,t[0]>7||t[1]>7)throw new Error("Bad Gutters: "+t.join("|"));return t}},129:{f:function(e,t,r){var a=r&&8==r.biff||2==t?e.read_shift(2):(e.l+=t,0);return{fDialog:16&a,fBelow:64&a,fRight:128&a}}},130:{f:Kn},131:{f:Xn},132:{f:Xn},133:{f:function(e,t,r){var a="";if(4==r.biff)return 0===(a=Qn(e,0,r)).length&&(a="Sheet1"),{name:a};var n=e.read_shift(4),s=3&e.read_shift(1),i=e.read_shift(1);switch(i){case 0:i="Worksheet";break;case 1:i="Macrosheet";break;case 2:i="Chartsheet";break;case 6:i="VBAModule"}return 0===(a=Qn(e,0,r)).length&&(a="Sheet1"),{pos:n,hs:s,dt:i,name:a}}},134:{},140:{f:function(e){var t,r=[0,0];return t=e.read_shift(2),r[0]=Ja[t]||t,t=e.read_shift(2),r[1]=Ja[t]||t,r}},141:{f:Kn},144:{},146:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(fs(e));return r}},151:{},152:{},153:{},154:{},155:{},156:{f:Kn},157:{},158:{},160:{f:Gs},161:{f:function(e,t){var r={};return t<32||(e.l+=16,r.header=Ba(e),r.footer=Ba(e),e.l+=2),r}},174:{},175:{},176:{},177:{},178:{},180:{},181:{},182:{},184:{},185:{},189:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(ps(e));if(e.l!==r)throw new Error("MulRK read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulRK length mismatch");return{r:a,c:n,C:i,rkrec:s}}},190:{f:function(e,t){for(var r=e.l+t-2,a=e.read_shift(2),n=e.read_shift(2),s=[];e.l<r;)s.push(e.read_shift(2));if(e.l!==r)throw new Error("MulBlank read error");var i=e.read_shift(2);if(s.length!=i-n+1)throw new Error("MulBlank length mismatch");return{r:a,c:n,C:i,ixfe:s}}},193:{f:Gn},197:{},198:{},199:{},200:{},201:{},202:{f:Xn},203:{},204:{},205:{},206:{},207:{},208:{},209:{},210:{},211:{},213:{},215:{},216:{},217:{},218:{f:Kn},220:{},221:{f:Xn},222:{},224:{f:function(e,t,r){var a={};return a.ifnt=e.read_shift(2),a.numFmtId=e.read_shift(2),a.flags=e.read_shift(2),a.fStyle=a.flags>>2&1,a.data=function(e,t,r,a){var n={},s=e.read_shift(4),i=e.read_shift(4),o=e.read_shift(4),c=e.read_shift(2);return n.patternType=qa[o>>26],a.cellStyles?(n.alc=7&s,n.fWrap=s>>3&1,n.alcV=s>>4&7,n.fJustLast=s>>7&1,n.trot=s>>8&255,n.cIndent=s>>16&15,n.fShrinkToFit=s>>20&1,n.iReadOrder=s>>22&2,n.fAtrNum=s>>26&1,n.fAtrFnt=s>>27&1,n.fAtrAlc=s>>28&1,n.fAtrBdr=s>>29&1,n.fAtrPat=s>>30&1,n.fAtrProt=s>>31&1,n.dgLeft=15&i,n.dgRight=i>>4&15,n.dgTop=i>>8&15,n.dgBottom=i>>12&15,n.icvLeft=i>>16&127,n.icvRight=i>>23&127,n.grbitDiag=i>>30&3,n.icvTop=127&o,n.icvBottom=o>>7&127,n.icvDiag=o>>14&127,n.dgDiag=o>>21&15,n.icvFore=127&c,n.icvBack=c>>7&127,n.fsxButton=c>>14&1,n):n}(e,0,a.fStyle,r),a}},225:{f:function(e,t){return 0===t||e.read_shift(2),1200}},226:{f:Gn},227:{},229:{f:function(e,t){for(var r=[],a=e.read_shift(2);a--;)r.push(ms(e));return r}},233:{},235:{},236:{},237:{},239:{},240:{},241:{},242:{},244:{},245:{},246:{},247:{},248:{},249:{},251:{},252:{f:function(e,t){for(var r=e.l+t,a=e.read_shift(4),n=e.read_shift(4),s=[],i=0;i!=n&&e.l<r;++i)s.push(es(e));return s.Count=a,s.Unique=n,s}},253:{f:function(e,t,r){var a=hs(e,t,r);return a.isst=e.read_shift(4),a}},255:{f:function(e,t){var r={};return r.dsst=e.read_shift(2),e.l+=t-2,r}},256:{},259:{},290:{},311:{},312:{},315:{},317:{f:Zn},318:{},319:{},320:{},330:{},331:{},333:{},334:{},335:{},336:{},337:{},338:{},339:{},340:{},351:{},352:{f:Xn},353:{f:Gn},401:{},402:{},403:{},404:{},405:{},406:{},407:{},408:{},425:{},426:{},427:{},428:{},429:{},430:{f:function(e,t,r){var a=e.l+t,n=e.read_shift(2),s=e.read_shift(2);if(r.sbcch=s,1025==s||14849==s)return[s,n];if(s<1||s>255)throw new Error("Unexpected SupBook type: "+s);for(var i=rs(e,s),o=[];a>e.l;)o.push(as(e));return[s,n,i,o]}},431:{f:Xn},432:{},433:{},434:{},437:{},438:{f:function(e,t,r){var a=e.l,n="";try{e.l+=4;var s=(r.lastobj||{cmo:[0,0]}).cmo[1];-1==[0,5,7,11,12,14].indexOf(s)?e.l+=6:function(e){var t=e.read_shift(1);e.l++;var r=e.read_shift(2);e.l+=2}(e);var i=e.read_shift(2);e.read_shift(2),Kn(e);var o=e.read_shift(2);e.l+=o;for(var c=1;c<e.lens.length-1;++c){if(e.l-a!=e.lens[c])throw new Error("TxO: bad continue record");var l=e[e.l];if((n+=rs(e,e.lens[c+1]-e.lens[c]-1)).length>=(l?i:2*i))break}if(n.length!==i&&n.length!==2*i)throw new Error("cchText: "+i+" != "+n.length);return e.l=a+t,{t:n}}catch(r){return e.l=a+t,{t:n}}}},439:{f:Xn},440:{f:function(e,t){var r=ms(e);e.l+=16;var a=function(e,t){var r=e.l+t,a=e.read_shift(4);if(2!==a)throw new Error("Unrecognized streamVersion: "+a);var n=e.read_shift(2);e.l+=2;var s,i,o,c,l,f,h="";16&n&&(s=is(e,e.l)),128&n&&(i=is(e,e.l)),257==(257&n)&&(o=is(e,e.l)),1==(257&n)&&(c=function(e,t){var r=e.read_shift(16);switch(r){case"e0c9ea79f9bace118c8200aa004ba90b":return function(e){var t=e.read_shift(4),r=e.l,a=!1;t>24&&(e.l+=t-24,"795881f43b1d7f48af2c825dc4852763"===e.read_shift(16)&&(a=!0),e.l=r);var n=e.read_shift((a?t-24:t)>>1,"utf16le").replace(F,"");return a&&(e.l+=24),n}(e);case"0303000000000000c000000000000046":return function(e){for(var t=e.read_shift(2),r="";t-- >0;)r+="../";var a=e.read_shift(0,"lpstr-ansi");if(e.l+=2,57005!=e.read_shift(2))throw new Error("Bad FileMoniker");if(0===e.read_shift(4))return r+a.replace(/\\/g,"/");var n=e.read_shift(4);if(3!=e.read_shift(2))throw new Error("Bad FileMoniker");return r+e.read_shift(n>>1,"utf16le").replace(F,"")}(e);default:throw new Error("Unsupported Moniker "+r)}}(e,e.l)),8&n&&(h=is(e,e.l)),32&n&&(l=e.read_shift(16)),64&n&&(f=In(e)),e.l=r;var u=i||o||c||"";u&&h&&(u+="#"+h),u||(u="#"+h),2&n&&"/"==u.charAt(0)&&"/"!=u.charAt(1)&&(u="file://"+u);var d={Target:u};return l&&(d.guid=l),f&&(d.time=f),s&&(d.Tooltip=s),d}(e,t-24);return[r,a]}},441:{},442:{f:as},443:{},444:{f:Kn},445:{},446:{},448:{f:Gn},449:{f:function(e){return e.read_shift(2),e.read_shift(4)},r:2},450:{f:Gn},512:{f:Cs},513:{f:$s},515:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5);var a=hs(e,6,r),n=Ba(e);return a.val=n,a}},516:{f:function(e,t,r){r.biffguess&&2==r.biff&&(r.biff=5),e.l;var a=hs(e,t,r),n=as(e,e.l,r);return a.val=n,a}},517:{f:function(e,t,r){var a=hs(e,6,r),n=Jn(e);return a.val=n,a.t=!0===n||!1===n?"b":"e",a}},519:{f:Xs},520:{f:function(e){var t={};t.r=e.read_shift(2),t.c=e.read_shift(2),t.cnt=e.read_shift(2)-t.c;var r=e.read_shift(2);e.l+=4;var a=e.read_shift(1);return e.l+=3,7&a&&(t.level=7&a),32&a&&(t.hidden=!0),64&a&&(t.hpt=r/20),t}},523:{},545:{f:Us},549:{f:Ss},566:{},574:{f:function(e,t,r){return r&&r.biff>=2&&r.biff<5?{}:{RTL:64&e.read_shift(2)}}},638:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=ps(e);return{r:t,c:r,ixfe:a[0],rknum:a[1]}}},659:{},1048:{},1054:{f:function(e,t,r){return[e.read_shift(2),ns(e,0,r)]}},1084:{},1212:{f:function(e,t,r){var a=gs(e);e.l++;var n=e.read_shift(1);return[Vo(e,t-=8,r),n,a]}},2048:{f:function(e,t){e.read_shift(2);var r=ms(e),a=e.read_shift((t-10)/2,"dbcs-cont");return[r,a=a.replace(F,"")]}},2049:{},2050:{},2051:{},2052:{},2053:{},2054:{},2055:{},2056:{},2057:{f:Es},2058:{},2059:{},2060:{},2061:{},2062:{},2063:{},2064:{},2066:{},2067:{},2128:{},2129:{},2130:{},2131:{},2132:{},2133:{},2134:{},2135:{},2136:{},2137:{},2138:{},2146:{},2147:{r:12},2148:{},2149:{},2150:{},2151:{f:Gn},2152:{},2154:{},2155:{},2156:{},2161:{},2162:{},2164:{},2165:{},2166:{},2167:{},2168:{},2169:{},2170:{},2171:{},2172:{f:function(e){e.l+=2;var t={cxfs:0,crc:0};return t.cxfs=e.read_shift(2),t.crc=e.read_shift(4),t},r:12},2173:{f:function(e,t){e.l,e.l+=2;var r=e.read_shift(2);e.l+=2;for(var a=e.read_shift(2),n=[];a-- >0;)n.push(qi(e,e.l));return{ixfe:r,ext:n}},r:12},2174:{},2175:{},2180:{},2181:{},2182:{},2183:{},2184:{},2185:{},2186:{},2187:{},2188:{f:Xn,r:12},2189:{},2190:{r:12},2191:{},2192:{},2194:{},2195:{},2196:{f:function(e,t,r){if(!(r.biff<8)){var a=e.read_shift(2),n=e.read_shift(2);return[rs(e,a,r),rs(e,n,r)]}e.l+=t},r:12},2197:{},2198:{f:function(e,t,r){var a=e.l+t;if(124226!==e.read_shift(4))if(r.cellStyles){var n,s=e.slice(e.l);e.l=a;try{n=St(s,{type:"array"})}catch(e){return}var i=Tt(n,"theme/theme/theme1.xml",!0);if(i)return Zi(i,r)}else e.l=a},r:12},2199:{},2200:{},2201:{},2202:{f:function(e){return[0!==e.read_shift(4),0!==e.read_shift(4),e.read_shift(4)]},r:12},2203:{f:Gn},2204:{},2205:{},2206:{},2207:{},2211:{f:function(e){var t=function(e){var t=e.read_shift(2),r=e.read_shift(2);return e.l+=8,{type:t,flags:r}}(e);if(2211!=t.type)throw new Error("Invalid Future Record "+t.type);return 0!==e.read_shift(4)}},2212:{},2213:{},2214:{},2215:{},4097:{},4098:{},4099:{},4102:{},4103:{},4105:{},4106:{},4107:{},4108:{},4109:{},4116:{},4117:{},4118:{},4119:{},4120:{},4121:{},4122:{},4123:{},4124:{},4125:{},4126:{},4127:{},4128:{},4129:{},4130:{},4132:{},4133:{},4134:{f:Kn},4135:{},4146:{},4147:{},4148:{},4149:{},4154:{},4156:{},4157:{},4158:{},4159:{},4160:{},4161:{},4163:{},4164:{f:function(e,t,r){var a={area:!1};if(5!=r.biff)return e.l+=t,a;var n=e.read_shift(1);return e.l+=3,16&n&&(a.area=!0),a}},4165:{},4166:{},4168:{},4170:{},4171:{},4174:{},4175:{},4176:{},4177:{},4187:{},4188:{f:function(e){for(var t=e.read_shift(2),r=[];t-- >0;)r.push(fs(e));return r}},4189:{},4191:{},4192:{},4193:{},4194:{},4195:{},4196:{},4197:{},4198:{},4199:{},4200:{},0:{f:Cs},1:{},2:{f:function(e,t,r){var a=hs(e,7,r),n=e.read_shift(2);return a.t="n",a.val=n,a}},3:{f:function(e,t,r){var a=hs(e,7,r),n=Ba(e);return a.t="n",a.val=n,a}},4:{f:function(e,t,r){r.biffguess&&5==r.biff&&(r.biff=2);var a=hs(e,7,r),n=ns(e,0,r);return a.t="str",a.val=n,a}},5:{f:function(e,t,r){var a=e.l+7,n=hs(e,6,r);e.l=a;var s=Jn(e);return n.val=s,n.t=!0===s||!1===s?"b":"e",n}},7:{f:function(e){var t=e.read_shift(1);return 0===t?(e.l++,""):e.read_shift(t,"sbcs-cont")}},8:{},9:{f:Es},11:{},22:{f:Kn},30:{f:As},31:{},32:{},33:{f:Us},36:{},37:{f:Ss},50:{f:function(e,t){e.l+=6,e.l+=2,e.l+=1,e.l+=3,e.l+=1,e.l+=t-13}},62:{},52:{},67:{f:function(e){var t={};return t.ifnt=e.read_shift(1),e.l++,t.flags=e.read_shift(1),t.numFmtId=63&t.flags,t.flags>>=6,t.fStyle=0,t.data={},t}},68:{f:Kn},69:{},86:{},126:{},127:{f:function(e){var t=e.read_shift(2),r=e.read_shift(2),a=e.read_shift(4),n={fmt:t,env:r,len:a,data:e.slice(e.l,e.l+a)};return e.l+=a,n}},135:{},136:{},137:{},143:{f:function(e){var t=e.read_shift(4),r=e.read_shift(1),a=e.read_shift(r,"sbcs");return 0===a.length&&(a="Sheet1"),{flags:t,name:a}}},145:{},148:{},149:{},150:{},169:{},171:{},188:{},191:{},192:{},194:{},195:{},214:{f:function(e,t,r){var a=e.l+t,n=hs(e,6,r),s=e.read_shift(2),i=rs(e,s,r);return e.l=a,n.t="str",n.val=i,n}},223:{},234:{},354:{},421:{},518:{f:$o},521:{f:Es},536:{f:Ps},547:{f:Ds},561:{},579:{f:function(e){var t={};return t.ifnt=e.read_shift(1),t.numFmtId=e.read_shift(1),t.flags=e.read_shift(2),t.fStyle=t.flags>>2&1,t.data={},t}},1030:{f:$o},1033:{f:Es},1091:{f:function(e){var t={};return t.ifnt=e.read_shift(1),t.numFmtId=e.read_shift(1),t.flags=e.read_shift(2),t.fStyle=t.flags>>2&1,t.data={},t}},2157:{},2163:{},2177:{},2240:{},2241:{},2242:{},2243:{},2244:{},2245:{},2246:{},2247:{},2248:{},2249:{},2250:{},2251:{},2262:{r:12},101:{},102:{},105:{},106:{},107:{},109:{},112:{},114:{},29282:{}};function El(e,t,r,a){var n=t;if(!isNaN(n)){var s=a||(r||[]).length||0,i=e.next(4);i.write_shift(2,n),i.write_shift(2,s),s>0&&Nr(r)&&e.push(r)}}function kl(e,t,r,a){var n=Kr(9);return js(n,e,t),qn(r,a||"b",n),n}function _l(e,t){t.forEach((function(t){var r=t[0].map((function(e){return e.t})).join("");if(r.length<=2048)return El(e,28,Bs(r,t[1],t[2]));El(e,28,Bs(r.slice(0,2048),t[1],t[2],r.length));for(var a=2048;a<r.length;a+=2048)El(e,28,Bs(r.slice(a,Math.min(a+2048,r.length)),-1,-1,Math.min(2048,r.length-a)))}))}function Sl(e,t,r,a,n,s){var i=0;null!=t.z&&-1==(i=n._BIFF2FmtTable.indexOf(t.z))&&(n._BIFF2FmtTable.push(t.z),i=n._BIFF2FmtTable.length-1);var o,c,l,f,h=0;if(null!=t.z){for(;h<n.cellXfs.length&&n.cellXfs[h].numFmtId!=i;++h);h==n.cellXfs.length&&n.cellXfs.push({numFmtId:i})}if(null!=t.v)switch(t.t){case"d":case"n":var u="d"==t.t?We(Xe(t.v,s),s):t.v;return void(2==n.biff&&u==(0|u)&&u>=0&&u<65536?El(e,2,function(e,t,r,a,n){var s=Kr(9);return js(s,e,t,a||0,n||0),s.write_shift(2,r),s}(r,a,u,h,i)):isNaN(u)?El(e,5,kl(r,a,36,"e")):isFinite(u)?El(e,3,function(e,t,r,a,n){var s=Kr(15);return js(s,e,t,a||0,n||0),s.write_shift(8,r,"f"),s}(r,a,u,h,i)):El(e,5,kl(r,a,7,"e")));case"b":case"e":return void El(e,5,kl(r,a,t.v,t.t));case"s":case"str":return void El(e,4,(o=r,c=a,l=null==t.v?"":String(t.v).slice(0,255),f=Kr(8+2*l.length),js(f,o,c),f.write_shift(1,l.length),f.write_shift(l.length,l,"sbcs"),f.l<f.length?f.slice(0,f.l):f))}El(e,1,js(null,r,a))}var xl=1,Al=[];function Cl(e,t,r,a,n,s){var i=16+fc(n.cellXfs,t,n);if(null!=t.v||t.bf)if(t.bf)El(e,6,function(e,t,r,a,n){var s=us(t,r,n),i=function(e){if(null==e){var t=Kr(8);return t.write_shift(1,3),t.write_shift(1,0),t.write_shift(2,0),t.write_shift(2,0),t.write_shift(2,65535),t}return Wa("number"==typeof e?e:0)}(e.v),o=Kr(6);o.write_shift(2,33),o.write_shift(4,0);for(var c=Kr(e.bf.length),l=0;l<e.bf.length;++l)c[l]=e.bf[l];return N([s,i,o,c])}(t,r,a,0,i));else switch(t.t){case"d":case"n":var o="d"==t.t?We(Xe(t.v,s),s):t.v;isNaN(o)?El(e,517,Ns(r,a,36,i,0,"e")):isFinite(o)?El(e,515,function(e,t,r,a){var n=Kr(14);return us(e,t,a,n),Wa(r,n),n}(r,a,o,i)):El(e,517,Ns(r,a,7,i,0,"e"));break;case"b":case"e":El(e,517,Ns(r,a,t.v,i,0,t.t));break;case"s":case"str":n.bookSST?El(e,253,function(e,t,r,a){var n=Kr(10);return us(e,t,a,n),n.write_shift(4,r),n}(r,a,oc(n.Strings,null==t.v?"":String(t.v),n.revStrings),i)):El(e,516,function(e,t,r,a,n){var s=!n||8==n.biff,i=Kr(+s+8+(1+s)*r.length);return us(e,t,a,i),i.write_shift(2,r.length),s&&i.write_shift(1,1),i.write_shift((1+s)*r.length,r,s?"utf16le":"sbcs"),i}(r,a,(null==t.v?"":String(t.v)).slice(0,255),i,n));break;default:El(e,513,us(r,a,i))}else El(e,513,us(r,a,i))}function Il(e,t,r){var a,n,s,i=Zr(),o=r.SheetNames[e],c=r.Sheets[o]||{},l=(r||{}).Workbook||{},f=(l.Sheets||[])[e]||{},h=null!=c["!data"],u=8==t.biff,d="",p=[],m=ua(c["!ref"]||"A1"),v=u?65536:16384;if(m.e.c>255||m.e.r>=v){if(t.WTF)throw new Error("Range "+(c["!ref"]||"A1")+" exceeds format limit A1:IV16384");m.e.c=Math.min(m.e.c,255),m.e.r=Math.min(m.e.c,v-1)}El(i,2057,ks(0,16,t)),El(i,13,Yn(1)),El(i,12,Yn(100)),El(i,15,jn(!0)),El(i,17,jn(!1)),El(i,16,Wa(.001)),El(i,95,jn(!0)),El(i,42,jn(!1)),El(i,43,jn(!1)),El(i,130,Yn(1)),El(i,128,(n=[0,0],(s=Kr(8)).write_shift(4,0),s.write_shift(2,n[0]?n[0]+1:0),s.write_shift(2,n[1]?n[1]+1:0),s)),El(i,131,jn(!1)),El(i,132,jn(!1)),u&&function(e,t){if(t){var r=0;t.forEach((function(t,a){++r<=256&&t&&El(e,125,function(e,t){var r=Kr(12);r.write_shift(2,t),r.write_shift(2,t),r.write_shift(2,256*e.width),r.write_shift(2,0);var a=0;return e.hidden&&(a|=1),r.write_shift(1,a),a=e.level||0,r.write_shift(1,a),r.write_shift(2,0),r}(cc(a,t),a))}))}}(i,c["!cols"]),El(i,512,function(e,t){var r=8!=t.biff&&t.biff?2:4,a=Kr(2*r+6);return a.write_shift(r,e.s.r),a.write_shift(r,e.e.r+1),a.write_shift(2,e.s.c),a.write_shift(2,e.e.c+1),a.write_shift(2,0),a}(m,t));var g=(((r||{}).Workbook||{}).WBProps||{}).date1904;u&&(c["!links"]=[]);for(var b=m.s.c;b<=m.e.c;++b)p[b]=sa(b);for(var w=[],T=[],y=m.s.r;y<=m.e.r;++y)for(h&&(T=c["!data"][y]||[]),d=aa(y),b=m.s.c;b<=m.e.c;++b){a=p[b]+d;var E=h?T[b]:c[a];E&&(Cl(i,E,y,b,t,g),u&&E.l&&c["!links"].push([a,E.l]),E.c&&w.push([E.c,y,b]))}var k=f.CodeName||f.name||o;return u?function(e,t){var r,a=[],n=0,s=Zr(),i=xl;t.forEach((function(e,t){var i="",o=e[0].map((function(e){return e.a&&!i&&(i=e.a),e.t})).join("");++xl;var c=Kr(150);c.write_shift(2,15),c.write_shift(2,61444),c.write_shift(4,150),c.write_shift(2,3234),c.write_shift(2,61450),c.write_shift(4,8),c.write_shift(4,xl),c.write_shift(4,2560),c.write_shift(2,227),c.write_shift(2,61451),c.write_shift(4,84),c.write_shift(2,128),c.write_shift(4,0),c.write_shift(2,139),c.write_shift(4,2),c.write_shift(2,191),c.write_shift(4,524296),c.write_shift(2,344),c.l+=4,c.write_shift(2,385),c.write_shift(4,134217808),c.write_shift(2,387),c.write_shift(4,134217808),c.write_shift(2,389),c.write_shift(4,268435700),c.write_shift(2,447),c.write_shift(4,1048592),c.write_shift(2,448),c.write_shift(4,134217809),c.write_shift(2,451),c.write_shift(4,268435700),c.write_shift(2,513),c.write_shift(4,134217809),c.write_shift(2,515),c.write_shift(4,268435700),c.write_shift(2,575),c.write_shift(4,196609),c.write_shift(2,959),c.write_shift(4,131072|(e[0].hidden?2:0)),c.l+=2,c.write_shift(2,61456),c.write_shift(4,18),c.write_shift(2,3),c.write_shift(2,e[2]+2),c.l+=2,c.write_shift(2,e[1]+1),c.l+=2,c.write_shift(2,e[2]+4),c.l+=2,c.write_shift(2,e[1]+5),c.l+=2,c.l+=2,c.write_shift(2,61457),c.l+=4,c.l=150,0==t?r=c:El(s,236,c),n+=150;var l=Kr(52);l.write_shift(2,21),l.write_shift(2,18),l.write_shift(2,25),l.write_shift(2,xl),l.write_shift(2,0),l.l=22,l.write_shift(2,13),l.write_shift(2,22),l.write_shift(4,1651663474),l.write_shift(4,2503426821),l.write_shift(4,2150634280),l.write_shift(4,1768515844+256*xl),l.write_shift(2,0),l.write_shift(4,0),l.l+=4,El(s,93,l);var f=Kr(8);f.l+=2,f.write_shift(2,61453),f.l+=4,El(s,236,f),n+=8;var h=Kr(18);h.write_shift(2,18),h.l+=8,h.write_shift(2,o.length),h.write_shift(2,16),h.l+=4,El(s,438,h);var u=Kr(1+o.length);u.write_shift(1,0),u.write_shift(o.length,o,"sbcs"),El(s,60,u);var d=Kr(16);d.l+=8,d.write_shift(2,o.length),d.l+=6,El(s,60,d);var p=Kr(12+i.length);p.write_shift(2,e[1]),p.write_shift(2,e[2]),p.write_shift(2,0|(e[0].hidden?0:2)),p.write_shift(2,xl),p.write_shift(2,i.length),p.write_shift(1,0),p.write_shift(i.length,i,"sbcs"),p.l++,a.push(p)}));var o=Kr(80);o.write_shift(2,15),o.write_shift(2,61442),o.write_shift(4,n+o.length-8),o.write_shift(2,16),o.write_shift(2,61448),o.write_shift(4,8),o.write_shift(4,t.length+1),o.write_shift(4,xl),o.write_shift(2,15),o.write_shift(2,61443),o.write_shift(4,n+48),o.write_shift(2,15),o.write_shift(2,61444),o.write_shift(4,40),o.write_shift(2,1),o.write_shift(2,61449),o.write_shift(4,16),o.l+=16,o.write_shift(2,2),o.write_shift(2,61450),o.write_shift(4,8),o.write_shift(4,i),o.write_shift(4,5),El(e,236,r?N([o,r]):o),e.push(s.end()),a.forEach((function(t){El(e,28,t)})),Al.push([i,t.length+1,xl]),++xl}(i,w):_l(i,w),u&&El(i,574,function(e){var t=Kr(18),r=1718;return e&&e.RTL&&(r|=64),t.write_shift(2,r),t.write_shift(4,0),t.write_shift(4,64),t.write_shift(4,0),t.write_shift(4,0),t}((l.Views||[])[0])),u&&(c["!merges"]||[]).length&&El(i,229,function(e){var t=Kr(2+8*e.length);t.write_shift(2,e.length);for(var r=0;r<e.length;++r)vs(e[r],t);return t}(c["!merges"])),u&&function(e,t){for(var r=0;r<t["!links"].length;++r){var a=t["!links"][r];El(e,440,zs(a)),a[1].Tooltip&&El(e,2048,Hs(a))}delete t["!links"]}(i,c),El(i,442,ss(k)),u&&function(e,t){var r=Kr(19);r.write_shift(4,2151),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,1),r.write_shift(4,0),El(e,2151,r),(r=Kr(39)).write_shift(4,2152),r.write_shift(4,0),r.write_shift(4,0),r.write_shift(2,3),r.write_shift(1,0),r.write_shift(4,0),r.write_shift(2,1),r.write_shift(4,4),r.write_shift(2,0),vs(ua(t["!ref"]||"A1"),r),r.write_shift(4,4),El(e,2152,r)}(i,c),El(i,10),i.end()}function Ol(e,t,r){var a,n=Zr(),s=(e||{}).Workbook||{},i=s.Sheets||[],o=s.WBProps||{},c=8==r.biff,l=5==r.biff;El(n,2057,ks(0,5,r)),"xla"==r.bookType&&El(n,135),El(n,225,c?Yn(1200):null),El(n,193,function(e,t){t||(t=Kr(2));for(var r=0;r<2;++r)t.write_shift(1,0);return t}()),l&&El(n,191),l&&El(n,192),El(n,226),El(n,92,function(e,t){var r=!t||8==t.biff,a=Kr(r?112:54);for(a.write_shift(8==t.biff?2:1,7),r&&a.write_shift(1,0),a.write_shift(4,859007059),a.write_shift(4,5458548|(r?0:536870912));a.l<a.length;)a.write_shift(1,r?0:32);return a}(0,r)),El(n,66,Yn(c?1200:1252)),c&&El(n,353,Yn(0)),c&&El(n,448),El(n,317,function(e){for(var t=Kr(2*e),r=0;r<e;++r)t.write_shift(2,r+1);return t}(e.SheetNames.length)),c&&e.vbaraw&&El(n,211),c&&e.vbaraw&&El(n,442,ss(o.CodeName||"ThisWorkbook")),El(n,156,Yn(17)),El(n,25,jn(!1)),El(n,18,jn(!1)),El(n,19,Yn(0)),c&&El(n,431,jn(!1)),c&&El(n,444,Yn(0)),El(n,61,((a=Kr(18)).write_shift(2,0),a.write_shift(2,0),a.write_shift(2,29280),a.write_shift(2,17600),a.write_shift(2,56),a.write_shift(2,0),a.write_shift(2,0),a.write_shift(2,1),a.write_shift(2,500),a)),El(n,64,jn(!1)),El(n,141,Yn(0)),El(n,34,jn("true"==function(e){return e.Workbook&&e.Workbook.WBProps&&Gt(e.Workbook.WBProps.date1904)?"true":"false"}(e))),El(n,14,jn(!0)),c&&El(n,439,jn(!1)),El(n,218,Yn(0)),function(e,t,r){El(e,49,function(e,t){var r=e.name||"Arial",a=t&&5==t.biff,n=Kr(a?15+r.length:16+2*r.length);return n.write_shift(2,20*(e.sz||12)),n.write_shift(4,0),n.write_shift(2,400),n.write_shift(4,0),n.write_shift(2,0),n.write_shift(1,r.length),a||n.write_shift(1,1),n.write_shift((a?1:2)*r.length,r,a?"sbcs":"utf16le"),n}({sz:12,color:{theme:1},name:"Arial",family:2,scheme:"minor"},r))}(n,0,r),function(e,t,r){t&&[[5,8],[23,26],[41,44],[50,392]].forEach((function(a){for(var n=a[0];n<=a[1];++n)null!=t[n]&&El(e,1054,xs(n,t[n],r))}))}(n,e.SSF,r),function(e,t){for(var r=0;r<16;++r)El(e,224,Is({numFmtId:0,style:!0},0,t));t.cellXfs.forEach((function(r){El(e,224,Is(r,0,t))}))}(n,r),c&&El(n,352,jn(!1));var f=n.end(),h=Zr();c&&El(h,140,function(e){return e||(e=Kr(4)),e.write_shift(2,1),e.write_shift(2,1),e}()),c&&Al.length&&El(h,235,function(){var e=Kr(82+8*Al.length);e.write_shift(2,15),e.write_shift(2,61440),e.write_shift(4,74+8*Al.length),e.write_shift(2,0),e.write_shift(2,61446),e.write_shift(4,16+8*Al.length),e.write_shift(4,xl),e.write_shift(4,Al.length+1);for(var t=0,r=0;r<Al.length;++r)t+=Al[r]&&Al[r][1]||0;return e.write_shift(4,t),e.write_shift(4,Al.length),Al.forEach((function(t){e.write_shift(4,t[0]),e.write_shift(4,t[2])})),e.write_shift(2,51),e.write_shift(2,61451),e.write_shift(4,18),e.write_shift(2,191),e.write_shift(4,524296),e.write_shift(2,385),e.write_shift(4,134217793),e.write_shift(2,448),e.write_shift(4,134217792),e.write_shift(2,64),e.write_shift(2,61726),e.write_shift(4,16),e.write_shift(4,134217741),e.write_shift(4,134217740),e.write_shift(4,134217751),e.write_shift(4,268435703),e}()),c&&r.Strings&&function(e,t,r,a){var n=(r||[]).length||0;if(n<=8224)return El(e,252,r,n);if(!isNaN(252)){for(var s=r.parts||[],i=0,o=0,c=0;c+(s[i]||8224)<=8224;)c+=s[i]||8224,i++;var l=e.next(4);for(l.write_shift(2,252),l.write_shift(2,c),e.push(r.slice(o,o+c)),o+=c;o<n;){for((l=e.next(4)).write_shift(2,60),c=0;c+(s[i]||8224)<=8224;)c+=s[i]||8224,i++;l.write_shift(2,c),e.push(r.slice(o,o+c)),o+=c}}}(h,0,function(e,t){var r=Kr(8);r.write_shift(4,e.Count),r.write_shift(4,e.Unique);for(var a=[],n=0;n<e.length;++n)a[n]=ts(e[n]);var s=N([r].concat(a));return s.parts=[r.length].concat(a.map((function(e){return e.length}))),s}(r.Strings)),El(h,10);var u=h.end(),d=Zr(),p=0,m=0;for(m=0;m<e.SheetNames.length;++m)p+=(c?12:11)+(c?2:1)*e.SheetNames[m].length;var v=f.length+p+u.length;for(m=0;m<e.SheetNames.length;++m)El(d,133,_s({pos:v,hs:(i[m]||{}).Hidden||0,dt:0,name:e.SheetNames[m]},r)),v+=t[m].length;var g=d.end();if(p!=g.length)throw new Error("BS8 "+p+" != "+g.length);var b=[];return f.length&&b.push(f),g.length&&b.push(g),u.length&&b.push(u),N(b)}function Rl(e,t){for(var r=0;r<=e.SheetNames.length;++r){var a=e.Sheets[e.SheetNames[r]];a&&a["!ref"]&&ca(a["!ref"]).e.c>255&&"undefined"!=typeof console&&console.error&&console.error("Worksheet '"+e.SheetNames[r]+"' extends beyond column IV (255).  Data may be lost.")}var n=t||{};switch(n.biff||2){case 8:case 5:return function(e,t){var r=t||{},a=[];e&&!e.SSF&&(e.SSF=Ke(G)),e&&e.SSF&&(ye(),Te(e.SSF),r.revssf=Me(e.SSF),r.revssf[e.SSF[65535]]=0,r.ssf=e.SSF),xl=1,Al=[],r.Strings=[],r.Strings.Count=0,r.Strings.Unique=0,Hf(r),r.cellXfs=[],fc(r.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={});for(var n=0;n<e.SheetNames.length;++n)a[a.length]=Il(n,r,e);return a.unshift(Ol(e,a,r)),N(a)}(e,t);case 4:case 3:case 2:return function(e,t){for(var r=t||{},a=Zr(),n=0,s=0;s<e.SheetNames.length;++s)e.SheetNames[s]==r.sheet&&(n=s);if(0==n&&r.sheet&&e.SheetNames[0]!=r.sheet)throw new Error("Sheet not found: "+r.sheet);El(a,4==r.biff?1033:3==r.biff?521:9,ks(0,16,r)),((e.Workbook||{}).WBProps||{}).date1904&&El(a,34,jn(!0)),r.cellXfs=[{numFmtId:0}],r._BIFF2FmtTable=["General"],r._Fonts=[];var i=Zr();return function(e,t,r,a,n){var s=null!=t["!data"],i=ua(t["!ref"]||"A1"),o="",c=[];if(i.e.c>255||i.e.r>16383){if(a.WTF)throw new Error("Range "+(t["!ref"]||"A1")+" exceeds format limit A1:IV16384");i.e.c=Math.min(i.e.c,255),i.e.r=Math.min(i.e.c,16383)}for(var l=(((n||{}).Workbook||{}).WBProps||{}).date1904,f=[],h=[],u=i.s.c;u<=i.e.c;++u)c[u]=sa(u);for(var d=i.s.r;d<=i.e.r;++d)for(s&&(f=t["!data"][d]||[]),o=aa(d),u=i.s.c;u<=i.e.c;++u){var p=s?f[u]:t[c[u]+o];p&&(Sl(e,p,d,u,a,l),p.c&&h.push([p.c,d,u]))}_l(e,h)}(i,e.Sheets[e.SheetNames[n]],0,r,e),r._BIFF2FmtTable.forEach((function(e){r.biff<=3?El(a,30,function(e){var t=Kr(1+e.length);return t.write_shift(1,e.length),t.write_shift(e.length,e,"sbcs"),t}(e)):El(a,1054,function(e){var t=Kr(3+e.length);return t.l+=2,t.write_shift(1,e.length),t.write_shift(e.length,e,"sbcs"),t}(e))})),r.cellXfs.forEach((function(e){switch(r.biff){case 2:El(a,67,function(e){var t=Kr(4);return t.l+=2,t.write_shift(1,e.numFmtId),t.l++,t}(e));break;case 3:El(a,579,Os(e));break;case 4:El(a,1091,Rs(e))}})),delete r._BIFF2FmtTable,delete r.cellXfs,delete r._Fonts,a.push(i.end()),El(a,10),a.end()}(e,t)}throw new Error("invalid type "+n.bookType+" for BIFF")}function Nl(e,t){var r=t||{},a=null!=r.dense?r.dense:w,n={};a&&(n["!data"]=[]);var s=(e=ct(e,"\x3c!--","--\x3e")).match(/<table/i);if(!s)throw new Error("Invalid HTML: could not find <table>");var i=e.match(/<\/table/i),o=s.index,c=i&&i.index||e.length,l=at(e.slice(o,c),/(:?<tr[^<>]*>)/i,"<tr>"),f=-1,h=0,u=0,d=0,p={s:{r:1e7,c:1e7},e:{r:0,c:0}},m=[];for(o=0;o<l.length;++o){var v=l[o].trim(),g=v.slice(0,3).toLowerCase();if("<tr"!=g){if("<td"==g||"<th"==g){var b=v.split(/<\/t[dh]>/i);for(c=0;c<b.length;++c){var T=b[c].trim();if(T.match(/<t[dh]/i)){for(var y=T,E=0;"<"==y.charAt(0)&&(E=y.indexOf(">"))>-1;)y=y.slice(E+1);for(var k=0;k<m.length;++k){var _=m[k];_.s.c==h&&_.s.r<f&&f<=_.e.r&&(h=_.e.c+1,k=-1)}var S=Ft(T.slice(0,T.indexOf(">")));d=S.colspan?+S.colspan:1,((u=+S.rowspan)>1||d>1)&&m.push({s:{r:f,c:h},e:{r:f+(u||1)-1,c:h+d-1}});var x=S.t||S["data-t"]||"";if(y.length)if(y=qt(y),p.s.r>f&&(p.s.r=f),p.e.r<f&&(p.e.r=f),p.s.c>h&&(p.s.c=h),p.e.c<h&&(p.e.c=h),y.length){var A={t:"s",v:y};r.raw||!y.trim().length||"s"==x||("TRUE"===y?A={t:"b",v:!0}:"FALSE"===y?A={t:"b",v:!1}:isNaN(Ze(y))?isNaN(rt(y).getDate())||(A={t:"d",v:Xe(y)},!1===r.UTC&&(A.v=nt(A.v)),r.cellDates||(A={t:"n",v:We(A.v)}),A.z=r.dateNF||G[14]):A={t:"n",v:Ze(y)}),!1!==A.cellText&&(A.w=y),a?(n["!data"][f]||(n["!data"][f]=[]),n["!data"][f][h]=A):n[oa({r:f,c:h})]=A,h+=d}else h+=d;else h+=d}}}}else{if(++f,r.sheetRows&&r.sheetRows<=f){--f;break}h=0}}return n["!ref"]=la(p),m.length&&(n["!merges"]=m),n}function Fl(e,t,r,a){for(var n=e["!merges"]||[],s=[],i={},o=null!=e["!data"],c=t.s.c;c<=t.e.c;++c){for(var l=0,f=0,h=0;h<n.length;++h)if(!(n[h].s.r>r||n[h].s.c>c||n[h].e.r<r||n[h].e.c<c)){if(n[h].s.r<r||n[h].s.c<c){l=-1;break}l=n[h].e.r-n[h].s.r+1,f=n[h].e.c-n[h].s.c+1;break}if(!(l<0)){var u=sa(c)+aa(r),d=o?(e["!data"][r]||[])[c]:e[u],p=d&&null!=d.v&&(d.h||Vt(d.w||(da(d),d.w)||""))||"";i={},l>1&&(i.rowspan=l),f>1&&(i.colspan=f),a.editable?p='<span contenteditable="true">'+p+"</span>":d&&(i["data-t"]=d&&d.t||"z",null!=d.v&&(i["data-v"]=Vt(d.v instanceof Date?d.v.toISOString():d.v)),null!=d.z&&(i["data-z"]=d.z),d.l&&"#"!=(d.l.Target||"#").charAt(0)&&(p='<a href="'+Vt(d.l.Target)+'">'+p+"</a>")),i.id=(a.id||"sjs")+"-"+u,s.push(sr("td",p,i))}}return"<tr>"+s.join("")+"</tr>"}var Dl='<html><head><meta charset="utf-8"/><title>SheetJS Table Export</title></head><body>',Pl="</body></html>";function Ml(e,t,r){return[].join("")+"<table"+(r&&r.id?' id="'+r.id+'"':"")+">"}function Ll(e,t){var r=t||{},a=null!=r.header?r.header:Dl,n=null!=r.footer?r.footer:Pl,s=[a],i=ca(e["!ref"]||"A1");if(s.push(Ml(0,0,r)),e["!ref"])for(var o=i.s.r;o<=i.e.r;++o)s.push(Fl(e,i,o,r));return s.push("</table>"+n),s.join("")}function Ul(e,t,r){var a=t.rows;if(!a)throw"Unsupported origin when "+t.tagName+" is not a TABLE";var n=r||{},s=null!=e["!data"],i=0,o=0;if(null!=n.origin)if("number"==typeof n.origin)i=n.origin;else{var c="string"==typeof n.origin?ia(n.origin):n.origin;i=c.r,o=c.c}var l=Math.min(n.sheetRows||1e7,a.length),f={s:{r:0,c:0},e:{r:i,c:o}};if(e["!ref"]){var h=ca(e["!ref"]);f.s.r=Math.min(f.s.r,h.s.r),f.s.c=Math.min(f.s.c,h.s.c),f.e.r=Math.max(f.e.r,h.e.r),f.e.c=Math.max(f.e.c,h.e.c),-1==i&&(f.e.r=i=h.e.r+1)}var u=[],d=0,p=e["!rows"]||(e["!rows"]=[]),m=0,v=0,g=0,b=0,w=0,T=0;for(e["!cols"]||(e["!cols"]=[]);m<a.length&&v<l;++m){var y=a[m];if(Wl(y)){if(n.display)continue;p[v]={hidden:!0}}var E=y.cells;for(g=b=0;g<E.length;++g){var k=E[g];if(!n.display||!Wl(k)){var _=k.hasAttribute("data-v")?k.getAttribute("data-v"):k.hasAttribute("v")?k.getAttribute("v"):qt(k.innerHTML),S=k.getAttribute("data-z")||k.getAttribute("z");for(d=0;d<u.length;++d){var x=u[d];x.s.c==b+o&&x.s.r<v+i&&v+i<=x.e.r&&(b=x.e.c+1-o,d=-1)}T=+k.getAttribute("colspan")||1,((w=+k.getAttribute("rowspan")||1)>1||T>1)&&u.push({s:{r:v+i,c:b+o},e:{r:v+i+(w||1)-1,c:b+o+(T||1)-1}});var A={t:"s",v:_},C=k.getAttribute("data-t")||k.getAttribute("t")||"";null!=_&&(0==_.length?A.t=C||"z":n.raw||0==_.trim().length||"s"==C||("TRUE"===_?A={t:"b",v:!0}:"FALSE"===_?A={t:"b",v:!1}:isNaN(Ze(_))?isNaN(rt(_).getDate())||(A={t:"d",v:Xe(_)},n.UTC&&(A.v=st(A.v)),n.cellDates||(A={t:"n",v:We(A.v)}),A.z=n.dateNF||G[14]):A={t:"n",v:Ze(_)})),void 0===A.z&&null!=S&&(A.z=S);var I="",O=k.getElementsByTagName("A");if(O&&O.length)for(var R=0;R<O.length&&(!O[R].hasAttribute("href")||"#"==(I=O[R].getAttribute("href")).charAt(0));++R);I&&"#"!=I.charAt(0)&&"javascript:"!=I.slice(0,11).toLowerCase()&&(A.l={Target:I}),s?(e["!data"][v+i]||(e["!data"][v+i]=[]),e["!data"][v+i][b+o]=A):e[oa({c:b+o,r:v+i})]=A,f.e.c<b+o&&(f.e.c=b+o),b+=T}}++v}return u.length&&(e["!merges"]=(e["!merges"]||[]).concat(u)),f.e.r=Math.max(f.e.r,v-1+i),e["!ref"]=la(f),v>=l&&(e["!fullref"]=la((f.e.r=a.length-m+v-1+i,f))),e}function Bl(e,t){var r={};return(t||{}).dense&&(r["!data"]=[]),Ul(r,e,t)}function Wl(e){var t="",r=function(e){return e.ownerDocument.defaultView&&"function"==typeof e.ownerDocument.defaultView.getComputedStyle?e.ownerDocument.defaultView.getComputedStyle:"function"==typeof getComputedStyle?getComputedStyle:null}(e);return r&&(t=r(e).getPropertyValue("display")),t||(t=e.style&&e.style.display),"none"===t}function zl(e,t,r){var a=r||{},n=or(e);cr.lastIndex=0,n=it(ct(n,"\x3c!--","--\x3e"));for(var s,i,o,c="",l="",f=0,h=-1,u="";s=cr.exec(n);)switch(s[3]=s[3].replace(/_[\s\S]*$/,"")){case"number-style":case"currency-style":case"percentage-style":case"date-style":case"time-style":case"text-style":"/"===s[1]?("false"==i["truncate-on-overflow"]&&(c.match(/h/)?c=c.replace(/h+/,"[$&]"):c.match(/m/)?c=c.replace(/m+/,"[$&]"):c.match(/s/)&&(c=c.replace(/s+/,"[$&]"))),a[i.name]=c,c=""):"/"!==s[0].charAt(s[0].length-2)&&(c="",i=Ft(s[0],!1));break;case"boolean-style":"/"===s[1]?(a[i.name]="General",c=""):"/"!==s[0].charAt(s[0].length-2)&&(c="",i=Ft(s[0],!1));break;case"boolean":c+="General";break;case"text":"/"===s[1]?"%"==(u=n.slice(h,cr.lastIndex-s[0].length))&&"<number:percentage-style"==i[0]?c+="%":c+='"'+u.replace(/"/g,'""')+'"':"/"!==s[0].charAt(s[0].length-2)&&(h=cr.lastIndex);break;case"day":"short"===(o=Ft(s[0],!1)).style?c+="d":c+="dd";break;case"day-of-week":switch((o=Ft(s[0],!1)).style){case"short":default:c+="ddd";break;case"long":c+="dddd"}break;case"era":"short"===(o=Ft(s[0],!1)).style?c+="ee":c+="eeee";break;case"hours":"short"===(o=Ft(s[0],!1)).style?c+="h":c+="hh";break;case"minutes":"short"===(o=Ft(s[0],!1)).style?c+="m":c+="mm";break;case"month":switch((o=Ft(s[0],!1)).textual&&(c+="mm"),o.style){case"short":default:c+="m";break;case"long":c+="mm"}break;case"seconds":"short"===(o=Ft(s[0],!1)).style?c+="s":c+="ss",o["decimal-places"]&&(c+="."+Ye("0",+o["decimal-places"]));break;case"year":switch((o=Ft(s[0],!1)).style){case"short":default:c+="yy";break;case"long":c+="yyyy"}break;case"am-pm":c+="AM/PM";break;case"week-of-year":case"quarter":console.error("Excel does not support ODS format token "+s[3]);break;case"fill-character":"/"===s[1]?c+='"'+(u=n.slice(h,cr.lastIndex-s[0].length)).replace(/"/g,'""')+'"*':"/"!==s[0].charAt(s[0].length-2)&&(h=cr.lastIndex);break;case"scientific-number":c+="0."+Ye("0",+(o=Ft(s[0],!1))["min-decimal-places"]||+o["decimal-places"]||2)+Ye("?",+o["decimal-places"]-+o["min-decimal-places"]||0)+"E"+(Gt(o["forced-exponent-sign"])?"+":"")+Ye("0",+o["min-exponent-digits"]||2);break;case"fraction":+(o=Ft(s[0],!1))["min-integer-digits"]?c+=Ye("0",+o["min-integer-digits"]):c+="#",c+=" ",c+=Ye("?",+o["min-numerator-digits"]||1),c+="/",+o["denominator-value"]?c+=o["denominator-value"]:c+=Ye("?",+o["min-denominator-digits"]||1);break;case"currency-symbol":"/"===s[1]?c+='"'+n.slice(h,cr.lastIndex-s[0].length).replace(/"/g,'""')+'"':"/"!==s[0].charAt(s[0].length-2)?h=cr.lastIndex:c+="$";break;case"text-properties":switch(((o=Ft(s[0],!1)).color||"").toLowerCase().replace("#","")){case"ff0000":case"red":c="[Red]"+c}break;case"text-content":c+="@";break;case"map":o=Ft(s[0],!1),"value()>=0"==Lt(o.condition)?c=a[o["apply-style-name"]]+";"+c:console.error("ODS number format may be incorrect: "+o.condition);break;case"number":if("/"===s[1])break;l="",l+=Ye("0",+(o=Ft(s[0],!1))["min-integer-digits"]||1),Gt(o.grouping)&&(l=ee(Ye("#",Math.max(0,4-l.length))+l)),(+o["min-decimal-places"]||+o["decimal-places"])&&(l+="."),+o["min-decimal-places"]&&(l+=Ye("0",+o["min-decimal-places"]||1)),+o["decimal-places"]-(+o["min-decimal-places"]||0)&&(l+=Ye("0",+o["decimal-places"]-(+o["min-decimal-places"]||0))),c+=l;break;case"embedded-text":"/"===s[1]?0==f?c+='"'+n.slice(h,cr.lastIndex-s[0].length).replace(/"/g,'""')+'"':c=c.slice(0,f)+'"'+n.slice(h,cr.lastIndex-s[0].length).replace(/"/g,'""')+'"'+c.slice(f):"/"!==s[0].charAt(s[0].length-2)&&(h=cr.lastIndex,f=-+Ft(s[0],!1).position||0)}return a}function Hl(e,t,r){var a=t||{};null!=w&&null==a.dense&&(a.dense=w);var n,s,i,o,c,l,f,h=or(e),u=[],d=0,p={},m=[],v={};a.dense&&(v["!data"]=[]);var g,b={value:""},T="",y=0,E="",k=0,_=[],S=[],x=-1,A=-1,C={s:{r:1e6,c:1e7},e:{r:0,c:0}},I=0,O=r||{},R={},N=[],F={},D=[],P=1,M=1,L=[],U={Names:[],WBProps:{}},B={},W=["",""],z=[],H={},V="",$=0,G=!1,X=!1,j=0;for(cr.lastIndex=0,h=it(ct(h,"\x3c!--","--\x3e"));l=cr.exec(h);)switch(l[3]=l[3].replace(/_[\s\S]*$/,"")){case"table":case"工作表":"/"===l[1]?(C.e.c>=C.s.c&&C.e.r>=C.s.r?v["!ref"]=la(C):v["!ref"]="A1:A1",a.sheetRows>0&&a.sheetRows<=C.e.r&&(v["!fullref"]=v["!ref"],C.e.r=a.sheetRows-1,v["!ref"]=la(C)),N.length&&(v["!merges"]=N),D.length&&(v["!rows"]=D),o.name=o["名称"]||o.name,"undefined"!=typeof JSON&&JSON.stringify(o),m.push(o.name),p[o.name]=v,X=!1):"/"!==l[0].charAt(l[0].length-2)&&(o=Ft(l[0],!1),x=A=-1,C.s.r=C.s.c=1e7,C.e.r=C.e.c=0,v={},a.dense&&(v["!data"]=[]),N=[],D=[],X=!0);break;case"table-row-group":"/"===l[1]?--I:++I;break;case"table-row":case"行":if("/"===l[1]){x+=P,P=1;break}if((c=Ft(l[0],!1))["行号"]?x=c["行号"]-1:-1==x&&(x=0),(P=+c["number-rows-repeated"]||1)<10)for(j=0;j<P;++j)I>0&&(D[x+j]={level:I});A=-1;break;case"covered-table-cell":"/"!==l[1]&&++A,a.sheetStubs&&(a.dense?(v["!data"][x]||(v["!data"][x]=[]),v["!data"][x][A]={t:"z"}):v[oa({r:x,c:A})]={t:"z"}),T="",_=[];break;case"table-cell":case"数据":if("/"===l[0].charAt(l[0].length-2))++A,b=Ft(l[0],!1),M=parseInt(b["number-columns-repeated"]||"1",10),f={t:"z",v:null},b.formula&&0!=a.cellFormula&&(f.f=tc(Lt(b.formula))),b["style-name"]&&R[b["style-name"]]&&(f.z=R[b["style-name"]]),"string"==(b["数据类型"]||b["value-type"])&&(f.t="s",f.v=Lt(b["string-value"]||""),a.dense?(v["!data"][x]||(v["!data"][x]=[]),v["!data"][x][A]=f):v[sa(A)+aa(x)]=f),A+=M-1;else if("/"!==l[1]){T=E="",y=k=0,_=[],S=[],M=1;var K=P?x+P-1:x;if(++A>C.e.c&&(C.e.c=A),A<C.s.c&&(C.s.c=A),x<C.s.r&&(C.s.r=x),K>C.e.r&&(C.e.r=K),z=[],H={},f={t:(b=Ft(l[0],!1))["数据类型"]||b["value-type"],v:null},b["style-name"]&&R[b["style-name"]]&&(f.z=R[b["style-name"]]),a.cellFormula)if(b.formula&&(b.formula=Lt(b.formula)),b["number-matrix-columns-spanned"]&&b["number-matrix-rows-spanned"]&&(F={s:{r:x,c:A},e:{r:x+(parseInt(b["number-matrix-rows-spanned"],10)||0)-1,c:A+(parseInt(b["number-matrix-columns-spanned"],10)||0)-1}},f.F=la(F),L.push([F,f.F])),b.formula)f.f=tc(b.formula);else for(j=0;j<L.length;++j)x>=L[j][0].s.r&&x<=L[j][0].e.r&&A>=L[j][0].s.c&&A<=L[j][0].e.c&&(f.F=L[j][1]);switch((b["number-columns-spanned"]||b["number-rows-spanned"])&&(F={s:{r:x,c:A},e:{r:x+(parseInt(b["number-rows-spanned"],10)||0)-1,c:A+(parseInt(b["number-columns-spanned"],10)||0)-1}},N.push(F)),b["number-columns-repeated"]&&(M=parseInt(b["number-columns-repeated"],10)),f.t){case"boolean":f.t="b",f.v=Gt(b["boolean-value"])||+b["boolean-value"]>=1;break;case"float":f.t="n",f.v=parseFloat(b.value),a.cellDates&&f.z&&me(f.z)&&(f.v=ze(f.v+(U.WBProps.date1904?1462:0)),f.t="number"==typeof f.v?"n":"d");break;case"percentage":case"currency":f.t="n",f.v=parseFloat(b.value);break;case"date":f.t="d",f.v=Xe(b["date-value"],U.WBProps.date1904),a.cellDates||(f.t="n",f.v=We(f.v,U.WBProps.date1904)),f.z||(f.z="m/d/yy");break;case"time":f.t="n",f.v=He(b["time-value"])/86400,a.cellDates&&(f.v=ze(f.v),f.t="number"==typeof f.v?"n":"d"),f.z||(f.z="HH:MM:SS");break;case"number":f.t="n",f.v=parseFloat(b["数据数值"]);break;default:if("string"!==f.t&&"text"!==f.t&&f.t)throw new Error("Unsupported value type "+f.t);f.t="s",null!=b["string-value"]&&(T=Lt(b["string-value"]),_=[])}}else{if(G=!1,"s"===f.t&&(f.v=T||"",_.length&&(f.R=_),G=0==y),B.Target&&(f.l=B),z.length>0&&(f.c=z,z=[]),T&&!1!==a.cellText&&(f.w=T),G&&(f.t="z",delete f.v),(!G||a.sheetStubs)&&!(a.sheetRows&&a.sheetRows<=x))for(var Y=0;Y<P;++Y){if(M=parseInt(b["number-columns-repeated"]||"1",10),a.dense)for(v["!data"][x+Y]||(v["!data"][x+Y]=[]),v["!data"][x+Y][A]=0==Y?f:Ke(f);--M>0;)v["!data"][x+Y][A+M]=Ke(f);else for(v[oa({r:x+Y,c:A})]=f;--M>0;)v[oa({r:x+Y,c:A+M})]=Ke(f);C.e.c<=A&&(C.e.c=A)}A+=(M=parseInt(b["number-columns-repeated"]||"1",10))-1,M=0,f={},T="",_=[]}B={};break;case"document":case"document-content":case"电子表格文档":case"spreadsheet":case"主体":case"scripts":case"styles":case"font-face-decls":case"master-styles":if("/"===l[1]){if((n=u.pop())[0]!==l[3])throw"Bad state: "+n}else"/"!==l[0].charAt(l[0].length-2)&&u.push([l[3],!0]);break;case"annotation":if("/"===l[1]){if((n=u.pop())[0]!==l[3])throw"Bad state: "+n;H.t=T,_.length&&(H.R=_),H.a=V,z.push(H),T=E,y=k,_=S}else if("/"!==l[0].charAt(l[0].length-2)){u.push([l[3],!1]);var Z=Ft(l[0],!0);Z.display&&Gt(Z.display)||(z.hidden=!0),E=T,k=y,S=_,T="",y=0,_=[]}V="",$=0;break;case"creator":"/"===l[1]?V=h.slice($,l.index):$=l.index+l[0].length;break;case"meta":case"元数据":case"settings":case"config-item-set":case"config-item-map-indexed":case"config-item-map-entry":case"config-item-map-named":case"shapes":case"frame":case"text-box":case"image":case"data-pilot-tables":case"list-style":case"form":case"dde-links":case"event-listeners":case"chart":if("/"===l[1]){if((n=u.pop())[0]!==l[3])throw"Bad state: "+n}else"/"!==l[0].charAt(l[0].length-2)&&u.push([l[3],!1]);T="",y=0,_=[];break;case"scientific-number":case"currency-symbol":case"fill-character":case"script":case"libraries":case"automatic-styles":case"default-style":case"page-layout":case"map":case"font-face":case"paragraph-properties":case"table-properties":case"table-column-properties":case"table-row-properties":case"table-cell-properties":case"number":case"fraction":case"day":case"month":case"year":case"era":case"day-of-week":case"week-of-year":case"quarter":case"hours":case"minutes":case"seconds":case"am-pm":case"boolean":case"text-content":case"text-properties":case"embedded-text":case"body":case"电子表格":case"forms":case"table-column":case"table-header-rows":case"table-rows":case"table-column-group":case"table-header-columns":case"table-columns":case"graphic-properties":case"calculation-settings":case"named-expressions":case"label-range":case"label-ranges":case"named-expression":case"sort":case"sort-by":case"sort-groups":case"tab":case"line-break":case"span":case"s":case"date":case"object":case"title":case"标题":case"desc":case"binary-data":case"table-source":case"scenario":case"iteration":case"content-validations":case"content-validation":case"help-message":case"error-message":case"database-ranges":case"filter":case"filter-and":case"filter-or":case"filter-condition":case"filter-set-item":case"list-level-style-bullet":case"list-level-style-number":case"list-level-properties":case"sender-firstname":case"sender-lastname":case"sender-initials":case"sender-title":case"sender-position":case"sender-email":case"sender-phone-private":case"sender-fax":case"sender-company":case"sender-phone-work":case"sender-street":case"sender-city":case"sender-postal-code":case"sender-country":case"sender-state-or-province":case"author-name":case"author-initials":case"chapter":case"file-name":case"template-name":case"sheet-name":case"event-listener":case"initial-creator":case"creation-date":case"print-date":case"generator":case"document-statistic":case"user-defined":case"editing-duration":case"editing-cycles":case"config-item":case"page-number":case"page-count":case"time":case"cell-range-source":case"detective":case"operation":case"highlighted-range":case"data-pilot-table":case"source-cell-range":case"source-service":case"data-pilot-field":case"data-pilot-level":case"data-pilot-subtotals":case"data-pilot-subtotal":case"data-pilot-members":case"data-pilot-member":case"data-pilot-display-info":case"data-pilot-sort-info":case"data-pilot-layout-info":case"data-pilot-field-reference":case"data-pilot-groups":case"data-pilot-group":case"data-pilot-group-member":case"rect":case"dde-connection-decls":case"dde-connection-decl":case"dde-link":case"dde-source":case"properties":case"property":case"table-protection":case"data-pilot-grand-total":case"office-document-common-attrs":break;case"text-style":case"boolean-style":case"number-style":case"currency-style":case"percentage-style":case"date-style":case"time-style":if("/"===l[1]){var J=cr.lastIndex;zl(h.slice(i,cr.lastIndex),0,O),cr.lastIndex=J}else"/"!==l[0].charAt(l[0].length-2)&&(i=cr.lastIndex-l[0].length);break;case"style":var q=Ft(l[0],!1);"table-cell"==q.family&&O[q["data-style-name"]]&&(R[q.name]=O[q["data-style-name"]]);break;case"text":if("/>"===l[0].slice(-2))break;if("/"===l[1])switch(u[u.length-1][0]){case"number-style":case"date-style":case"time-style":h.slice(d,l.index)}else d=l.index+l[0].length;break;case"named-range":W=rc((s=Ft(l[0],!1))["cell-range-address"]);var Q={Name:s.name,Ref:W[0]+"!"+W[1]};X&&(Q.Sheet=m.length),U.Names.push(Q);break;case"null-date":"1904-01-01"===(s=Ft(l[0],!1))["date-value"]&&(U.WBProps.date1904=!0);break;case"p":case"文本串":if(["master-styles"].indexOf(u[u.length-1][0])>-1)break;if("/"!==l[1]||b&&b["string-value"])"/>"==l[0].slice(-2)?T+="\n":(Ft(l[0],!1),y=l.index+l[0].length);else{var ee=(g=void 0,g=h.slice(y,l.index).replace(/[\t\r\n]/g," ").trim().replace(/ +/g," ").replace(/<text:s\/>/g," ").replace(/<text:s text:c="(\d+)"\/>/g,(function(e,t){return Array(parseInt(t,10)+1).join(" ")})).replace(/<text:tab[^<>]*\/>/g,"\t").replace(/<text:line-break\/>/g,"\n"),[Lt(g.replace(/<[^<>]*>/g,""))]);T=(T.length>0?T+"\n":"")+ee[0]}break;case"database-range":if("/"===l[1])break;try{p[(W=rc(Ft(l[0])["target-range-address"]))[0]]["!autofilter"]={ref:W[1]}}catch(e){}break;case"a":if("/"!==l[1]){if(!(B=Ft(l[0],!1)).href)break;B.Target=Lt(B.href),delete B.href,"#"==B.Target.charAt(0)&&B.Target.indexOf(".")>-1?(W=rc(B.Target.slice(1)),B.Target="#"+W[0]+"!"+W[1]):B.Target.match(/^\.\.[\\\/]/)&&(B.Target=B.Target.slice(3))}break;default:switch(l[2]){case"dc:":case"calcext:":case"loext:":case"ooo:":case"chartooo:":case"draw:":case"style:":case"chart:":case"form:":case"uof:":case"表:":case"字:":break;default:if(a.WTF)throw new Error(l)}}var te={Sheets:p,SheetNames:m,Workbook:U};return a.bookSheets&&delete te.Sheets,te}function Vl(e,t){t=t||{},gt(e,"META-INF/manifest.xml")&&function(e,t){for(var r,a,n=or(e);r=cr.exec(n);)switch(r[3]){case"manifest":break;case"file-entry":if("/"==(a=Ft(r[0],!1)).path&&a.type!==dn)throw new Error("This OpenDocument is not a spreadsheet");break;case"encryption-data":case"algorithm":case"start-key-generation":case"key-derivation":throw new Error("Unsupported ODS Encryption");default:if(t&&t.WTF)throw r}}(wt(e,"META-INF/manifest.xml"),t);var r=Tt(e,"styles.xml"),a=r&&zl(Zt(r)),n=Tt(e,"content.xml");if(!n)throw new Error("Missing content.xml in ODS / UOF file");var s=Hl(Zt(n),t,a);return gt(e,"meta.xml")&&(s.Props=gn(wt(e,"meta.xml"))),s.bookType="ods",s}function $l(e,t){var r=Hl(e,t);return r.bookType="fods",r}var Gl=function(){var e=["<office:master-styles>",'<style:master-page style:name="mp1" style:page-layout-name="mp1">',"<style:header/>",'<style:header-left style:display="false"/>',"<style:footer/>",'<style:footer-left style:display="false"/>',"</style:master-page>","</office:master-styles>"].join(""),t="<office:document-styles "+nr({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","office:version":"1.2"})+">"+e+"</office:document-styles>";return function(){return At+t}}();function Xl(e,t){var r="number",a="",n={"style:name":t},s="",i=0;e:if((e=e.replace(/"[$]"/g,"$")).indexOf(";")>-1&&(console.error("Unsupported ODS Style Map exported.  Using first branch of "+e),e=e.slice(0,e.indexOf(";"))),"@"!=e){if(e.indexOf(/\$/)>-1&&(r="currency"),'"'==e[i]){for(s="";'"'!=e[++i]||'"'==e[++i];)s+=e[i];"*"==e[1+--i]?(i++,a+="<number:fill-character>"+Wt(s.replace(/""/g,'"'))+"</number:fill-character>"):a+="<number:text>"+Wt(s.replace(/""/g,'"'))+"</number:text>",e=e.slice(i+1),i=0}var o=e.match(/# (\?+)\/(\?+)/);if(o)a+=sr("number:fraction",null,{"number:min-integer-digits":0,"number:min-numerator-digits":o[1].length,"number:max-denominator-value":Math.max(+o[1].replace(/./g,"9"),+o[2].replace(/./g,"9"))});else if(o=e.match(/# (\?+)\/(\d+)/))a+=sr("number:fraction",null,{"number:min-integer-digits":0,"number:min-numerator-digits":o[1].length,"number:denominator-value":+o[2]});else if(o=e.match(/\b(\d+)(|\.\d+)%/))r="percentage",a+=sr("number:number",null,{"number:decimal-places":o[2]&&o.length-1||0,"number:min-decimal-places":o[2]&&o.length-1||0,"number:min-integer-digits":o[1].length})+"<number:text>%</number:text>";else{var c=!1;if(["y","m","d"].indexOf(e[0])>-1){r="date";t:for(;i<e.length;++i)switch(s=e[i].toLowerCase()){case"h":case"s":c=!0,--i;break t;case"m":r:for(var l=i+1;l<e.length;++l)switch(e[l]){case"y":case"d":break r;case"h":case"s":c=!0,--i;break t}case"y":case"d":for(;(e[++i]||"").toLowerCase()==s[0];)s+=s[0];switch(--i,s){case"y":case"yy":a+="<number:year/>";break;case"yyy":case"yyyy":a+='<number:year number:style="long"/>';break;case"mmmmm":console.error("ODS has no equivalent of format |mmmmm|");case"m":case"mm":case"mmm":case"mmmm":a+='<number:month number:style="'+(s.length%2?"short":"long")+'" number:textual="'+(s.length>=3?"true":"false")+'"/>';break;case"d":case"dd":a+='<number:day number:style="'+(s.length%2?"short":"long")+'"/>';break;case"ddd":case"dddd":a+='<number:day-of-week number:style="'+(s.length%2?"short":"long")+'"/>'}break;case'"':for(;'"'!=e[++i]||'"'==e[++i];)s+=e[i];--i,a+="<number:text>"+Wt(s.slice(1).replace(/""/g,'"'))+"</number:text>";break;case"\\":a+="<number:text>"+Wt(s=e[++i])+"</number:text>";break;case"/":case":":a+="<number:text>"+Wt(s)+"</number:text>";break;default:console.error("unrecognized character "+s+" in ODF format "+e)}if(!c)break e;e=e.slice(i+1),i=0}if(e.match(/^\[?[hms]/))for("number"==r&&(r="time"),e.match(/\[/)&&(e=e.replace(/[\[\]]/g,""),n["number:truncate-on-overflow"]="false");i<e.length;++i)switch(s=e[i].toLowerCase()){case"h":case"m":case"s":for(;(e[++i]||"").toLowerCase()==s[0];)s+=s[0];switch(--i,s){case"h":case"hh":a+='<number:hours number:style="'+(s.length%2?"short":"long")+'"/>';break;case"m":case"mm":a+='<number:minutes number:style="'+(s.length%2?"short":"long")+'"/>';break;case"s":case"ss":if("."==e[i+1])do{s+=e[i+1],++i}while("0"==e[i+1]);a+='<number:seconds number:style="'+(s.match("ss")?"long":"short")+'"'+(s.match(/\./)?' number:decimal-places="'+(s.match(/0+/)||[""])[0].length+'"':"")+"/>"}break;case'"':for(;'"'!=e[++i]||'"'==e[++i];)s+=e[i];--i,a+="<number:text>"+Wt(s.slice(1).replace(/""/g,'"'))+"</number:text>";break;case"/":case":":a+="<number:text>"+Wt(s)+"</number:text>";break;case"a":if("a/p"==e.slice(i,i+3).toLowerCase()){a+="<number:am-pm/>",i+=2;break}if("am/pm"==e.slice(i,i+5).toLowerCase()){a+="<number:am-pm/>",i+=4;break}default:console.error("unrecognized character "+s+" in ODF format "+e)}else{if(e.indexOf(/\$/)>-1&&(r="currency"),"$"==e[0]&&(a+='<number:currency-symbol number:language="en" number:country="US">$</number:currency-symbol>',e=e.slice(1),i=0),'"'==e[i=0]){for(;'"'!=e[++i]||'"'==e[++i];)s+=e[i];"*"==e[1+--i]?(i++,a+="<number:fill-character>"+Wt(s.replace(/""/g,'"'))+"</number:fill-character>"):a+="<number:text>"+Wt(s.replace(/""/g,'"'))+"</number:text>",e=e.slice(i+1),i=0}var f=e.match(/([#0][0#,]*)(\.[0#]*|)(E[+]?0*|)/i);if(f&&f[0]){var h=f[1].replace(/,/g,"");a+="<number:"+(f[3]?"scientific-":"")+'number number:min-integer-digits="'+(-1==h.indexOf("0")?"0":h.length-h.indexOf("0"))+'"'+(f[0].indexOf(",")>-1?' number:grouping="true"':"")+(f[2]&&' number:decimal-places="'+(f[2].length-1)+'"'||' number:decimal-places="0"')+(f[3]&&f[3].indexOf("+")>-1?' number:forced-exponent-sign="true"':"")+(f[3]?' number:min-exponent-digits="'+f[3].match(/0+/)[0].length+'"':"")+"></number:"+(f[3]?"scientific-":"")+"number>",i=f.index+f[0].length}else console.error("Could not find numeric part of "+e);if('"'==e[i]){for(s="";'"'!=e[++i]||'"'==e[++i];)s+=e[i];--i,a+="<number:text>"+Wt(s.replace(/""/g,'"'))+"</number:text>"}}}}else r="text",a="<number:text-content/>";return a?sr("number:"+r+"-style",a,n):(console.error("Could not generate ODS number format for |"+e+"|"),"")}function jl(e,t,r){for(var a=[],n=0;n<e.length;++n){var s=e[n];s&&s.Sheet==(-1==r?null:r)&&a.push(s)}return a.length?"      <table:named-expressions>\n"+a.map((function(e){var t=(-1==r?"$":"")+ac(e.Ref);return"        "+sr("table:named-range",null,{"table:name":e.Name,"table:cell-range-address":t,"table:base-cell-address":t.replace(/[\.][^\.]*$/,".$A$1")})})).join("\n")+"\n      </table:named-expressions>\n":""}var Kl=function(){var e=function(e,t){return Wt(e).replace(/  +/g,(function(e){return'<text:s text:c="'+e.length+'"/>'})).replace(/\t/g,"<text:tab/>").replace(/\n/g,t?"<text:line-break/>":"</text:p><text:p>").replace(/^ /,"<text:s/>").replace(/ $/,"<text:s/>")},t="          <table:table-cell />\n",r=function(r,a,n,s,i,o){var c=[];c.push('      <table:table table:name="'+Wt(a.SheetNames[n])+'" table:style-name="ta1">\n');var l=0,f=0,h=ca(r["!ref"]||"A1"),u=r["!merges"]||[],d=0,p=null!=r["!data"];if(r["!cols"])for(f=0;f<=h.e.c;++f)c.push("        <table:table-column"+(r["!cols"][f]?' table:style-name="co'+r["!cols"][f].ods+'"':"")+"></table:table-column>\n");var m="",v=r["!rows"]||[];for(l=0;l<h.s.r;++l)m=v[l]?' table:style-name="ro'+v[l].ods+'"':"",c.push("        <table:table-row"+m+"></table:table-row>\n");for(;l<=h.e.r;++l){for(m=v[l]?' table:style-name="ro'+v[l].ods+'"':"",c.push("        <table:table-row"+m+">\n"),f=0;f<h.s.c;++f)c.push(t);for(;f<=h.e.c;++f){var g=!1,b={},w="";for(d=0;d!=u.length;++d)if(!(u[d].s.c>f||u[d].s.r>l||u[d].e.c<f||u[d].e.r<l)){u[d].s.c==f&&u[d].s.r==l||(g=!0),b["table:number-columns-spanned"]=u[d].e.c-u[d].s.c+1,b["table:number-rows-spanned"]=u[d].e.r-u[d].s.r+1;break}if(g)c.push("          <table:covered-table-cell/>\n");else{var T=oa({r:l,c:f}),y=p?(r["!data"][l]||[])[f]:r[T];if(y&&y.f&&(b["table:formula"]=Wt(("of:="+y.f.replace(fo,"$1[.$2$3$4$5]").replace(/\]:\[/g,":")).replace(/;/g,"|").replace(/,/g,";")),y.F&&y.F.slice(0,T.length)==T)){var E=ca(y.F);b["table:number-matrix-columns-spanned"]=E.e.c-E.s.c+1,b["table:number-matrix-rows-spanned"]=E.e.r-E.s.r+1}if(y){switch(y.t){case"b":w=y.v?"TRUE":"FALSE",b["office:value-type"]="boolean",b["office:boolean-value"]=y.v?"true":"false";break;case"n":w=y.w||String(y.v||0),b["office:value-type"]="float",b["office:value"]=y.v||0;break;case"s":case"str":w=null==y.v?"":y.v,b["office:value-type"]="string";break;case"d":w=y.w||Xe(y.v,o).toISOString(),b["office:value-type"]="date",b["office:date-value"]=Xe(y.v,o).toISOString(),b["table:style-name"]="ce1";break;default:c.push(t);continue}var k=e(w);if(y.l&&y.l.Target){var _=y.l.Target;"#"==(_="#"==_.charAt(0)?"#"+ac(_.slice(1)):_).charAt(0)||_.match(/^\w+:/)||(_="../"+_),k=sr("text:a",k,{"xlink:href":_.replace(/&/g,"&amp;")})}i[y.z]&&(b["table:style-name"]="ce"+i[y.z].slice(1));var S=sr("text:p",k,{});if(y.c){for(var x="",A="",C={},I=0;I<y.c.length;++I)!x&&y.c[I].a&&(x=y.c[I].a),A+="<text:p>"+e(y.c[I].t)+"</text:p>";y.c.hidden||(C["office:display"]=!0),S=sr("office:annotation",A,C)+S}c.push("          "+sr("table:table-cell",S,b)+"\n")}else c.push(t)}}c.push("        </table:table-row>\n")}return(a.Workbook||{}).Names&&c.push(jl(a.Workbook.Names,a.SheetNames,n)),c.push("      </table:table>\n"),c.join("")};return function(e,t){var a=[At],n=nr({"xmlns:office":"urn:oasis:names:tc:opendocument:xmlns:office:1.0","xmlns:table":"urn:oasis:names:tc:opendocument:xmlns:table:1.0","xmlns:style":"urn:oasis:names:tc:opendocument:xmlns:style:1.0","xmlns:text":"urn:oasis:names:tc:opendocument:xmlns:text:1.0","xmlns:draw":"urn:oasis:names:tc:opendocument:xmlns:drawing:1.0","xmlns:fo":"urn:oasis:names:tc:opendocument:xmlns:xsl-fo-compatible:1.0","xmlns:xlink":"http://www.w3.org/1999/xlink","xmlns:dc":"http://purl.org/dc/elements/1.1/","xmlns:meta":"urn:oasis:names:tc:opendocument:xmlns:meta:1.0","xmlns:number":"urn:oasis:names:tc:opendocument:xmlns:datastyle:1.0","xmlns:presentation":"urn:oasis:names:tc:opendocument:xmlns:presentation:1.0","xmlns:svg":"urn:oasis:names:tc:opendocument:xmlns:svg-compatible:1.0","xmlns:chart":"urn:oasis:names:tc:opendocument:xmlns:chart:1.0","xmlns:dr3d":"urn:oasis:names:tc:opendocument:xmlns:dr3d:1.0","xmlns:math":"http://www.w3.org/1998/Math/MathML","xmlns:form":"urn:oasis:names:tc:opendocument:xmlns:form:1.0","xmlns:script":"urn:oasis:names:tc:opendocument:xmlns:script:1.0","xmlns:ooo":"http://openoffice.org/2004/office","xmlns:ooow":"http://openoffice.org/2004/writer","xmlns:oooc":"http://openoffice.org/2004/calc","xmlns:dom":"http://www.w3.org/2001/xml-events","xmlns:xforms":"http://www.w3.org/2002/xforms","xmlns:xsd":"http://www.w3.org/2001/XMLSchema","xmlns:xsi":"http://www.w3.org/2001/XMLSchema-instance","xmlns:sheet":"urn:oasis:names:tc:opendocument:sh33tjs:1.0","xmlns:rpt":"http://openoffice.org/2005/report","xmlns:of":"urn:oasis:names:tc:opendocument:xmlns:of:1.2","xmlns:xhtml":"http://www.w3.org/1999/xhtml","xmlns:grddl":"http://www.w3.org/2003/g/data-view#","xmlns:tableooo":"http://openoffice.org/2009/table","xmlns:drawooo":"http://openoffice.org/2010/draw","xmlns:calcext":"urn:org:documentfoundation:names:experimental:calc:xmlns:calcext:1.0","xmlns:loext":"urn:org:documentfoundation:names:experimental:office:xmlns:loext:1.0","xmlns:field":"urn:openoffice:names:experimental:ooo-ms-interop:xmlns:field:1.0","xmlns:formx":"urn:openoffice:names:experimental:ooxml-odf-interop:xmlns:form:1.0","xmlns:css3t":"http://www.w3.org/TR/css3-text/","office:version":"1.2"}),s=nr({"xmlns:config":"urn:oasis:names:tc:opendocument:xmlns:config:1.0","office:mimetype":"application/vnd.oasis.opendocument.spreadsheet"});"fods"==t.bookType?(a.push("<office:document"+n+s+">\n"),a.push(mn().replace(/<office:document-meta[^<>]*?>/,"").replace(/<\/office:document-meta>/,"")+"\n")):a.push("<office:document-content"+n+">\n");var i=function(e,t){e.push(" <office:automatic-styles>\n");var r=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!cols"])for(var a=0;a<t["!cols"].length;++a)if(t["!cols"][a]){var n=t["!cols"][a];if(null==n.width&&null==n.wpx&&null==n.wch)continue;Ni(n),n.ods=r;var s=t["!cols"][a].wpx+"px";e.push('  <style:style style:name="co'+r+'" style:family="table-column">\n'),e.push('   <style:table-column-properties fo:break-before="auto" style:column-width="'+s+'"/>\n'),e.push("  </style:style>\n"),++r}}));var a=0;t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t&&t["!rows"])for(var r=0;r<t["!rows"].length;++r)if(t["!rows"][r]){t["!rows"][r].ods=a;var n=t["!rows"][r].hpx+"px";e.push('  <style:style style:name="ro'+a+'" style:family="table-row">\n'),e.push('   <style:table-row-properties fo:break-before="auto" style:row-height="'+n+'"/>\n'),e.push("  </style:style>\n"),++a}})),e.push('  <style:style style:name="ta1" style:family="table" style:master-page-name="mp1">\n'),e.push('   <style:table-properties table:display="true" style:writing-mode="lr-tb"/>\n'),e.push("  </style:style>\n"),e.push('  <number:date-style style:name="N37" number:automatic-order="true">\n'),e.push('   <number:month number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push('   <number:day number:style="long"/>\n'),e.push("   <number:text>/</number:text>\n"),e.push("   <number:year/>\n"),e.push("  </number:date-style>\n");var n={},s=69;return t.SheetNames.map((function(e){return t.Sheets[e]})).forEach((function(t){if(t){var r=null!=t["!data"];if(t["!ref"])for(var a=ca(t["!ref"]),i=0;i<=a.e.r;++i)for(var o=0;o<=a.e.c;++o){var c=r?(t["!data"][i]||[])[o]:t[oa({r:i,c:o})];if(c&&c.z&&"general"!=c.z.toLowerCase()&&!n[c.z]){var l=Xl(c.z,"N"+s);l&&(n[c.z]="N"+s,++s,e.push(l+"\n"))}}}})),e.push('  <style:style style:name="ce1" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="N37"/>\n'),Fe(n).forEach((function(t){e.push('<style:style style:name="ce'+n[t].slice(1)+'" style:family="table-cell" style:parent-style-name="Default" style:data-style-name="'+n[t]+'"/>\n')})),e.push(" </office:automatic-styles>\n"),n}(a,e);a.push("  <office:body>\n"),a.push("    <office:spreadsheet>\n"),((e.Workbook||{}).WBProps||{}).date1904&&a.push('      <table:calculation-settings table:case-sensitive="false" table:search-criteria-must-apply-to-whole-cell="true" table:use-wildcards="true" table:use-regular-expressions="false" table:automatic-find-labels="false">\n        <table:null-date table:date-value="1904-01-01"/>\n      </table:calculation-settings>\n');for(var o=0;o!=e.SheetNames.length;++o)a.push(r(e.Sheets[e.SheetNames[o]],e,o,0,i,((e.Workbook||{}).WBProps||{}).date1904));return(e.Workbook||{}).Names&&a.push(jl(e.Workbook.Names,e.SheetNames,-1)),a.push("    </office:spreadsheet>\n"),a.push("  </office:body>\n"),"fods"==t.bookType?a.push("</office:document>"):a.push("</office:document-content>"),a.join("")}}();function Yl(e,t){if("fods"==t.bookType)return Kl(e,t);var r=_t(),a="",n=[],s=[];return kt(r,a="mimetype","application/vnd.oasis.opendocument.spreadsheet"),kt(r,a="content.xml",Kl(e,t)),n.push([a,"text/xml"]),s.push([a,"ContentFile"]),kt(r,a="styles.xml",Gl(e,t)),n.push([a,"text/xml"]),s.push([a,"StylesFile"]),kt(r,a="meta.xml",At+mn()),n.push([a,"text/xml"]),s.push([a,"MetadataFile"]),kt(r,a="manifest.rdf",function(e){var t=[At];t.push('<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#">\n');for(var r=0;r!=e.length;++r)t.push(pn(e[r][0],e[r][1])),t.push(("",['  <rdf:Description rdf:about="">\n','    <ns0:hasPart xmlns:ns0="http://docs.oasis-open.org/ns/office/1.2/meta/pkg#" rdf:resource="'+e[r][0]+'"/>\n',"  </rdf:Description>\n"].join("")));return t.push(pn("","Document","pkg")),t.push("</rdf:RDF>"),t.join("")}(s)),n.push([a,"application/rdf+xml"]),kt(r,a="META-INF/manifest.xml",function(e){var t=[At];t.push('<manifest:manifest xmlns:manifest="urn:oasis:names:tc:opendocument:xmlns:manifest:1.0" manifest:version="1.2">\n'),t.push('  <manifest:file-entry manifest:full-path="/" manifest:version="1.2" manifest:media-type="application/vnd.oasis.opendocument.spreadsheet"/>\n');for(var r=0;r<e.length;++r)t.push('  <manifest:file-entry manifest:full-path="'+e[r][0]+'" manifest:media-type="'+e[r][1]+'"/>\n');return t.push("</manifest:manifest>"),t.join("")}(n)),r}var Zl=function(){try{return"undefined"==typeof Uint8Array||void 0===Uint8Array.prototype.subarray?"slice":"undefined"!=typeof Buffer?void 0===Buffer.prototype.subarray?"slice":("function"==typeof Buffer.from?Buffer.from([72,62]):new Buffer([72,62]))instanceof Uint8Array?"subarray":"slice":"subarray"}catch(e){return"slice"}}();function Jl(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function ql(e){return"undefined"!=typeof TextDecoder?(new TextDecoder).decode(e):Zt(O(e))}function Ql(e){return"undefined"!=typeof TextEncoder?(new TextEncoder).encode(e):C(Jt(e))}function ef(e){for(var t=0,r=0;r<e.length;++r)t+=e[r].length;var a=new Uint8Array(t),n=0;for(r=0;r<e.length;++r){var s=e[r],i=s.length;if(i<250)for(var o=0;o<i;++o)a[n++]=s[o];else a.set(s,n),n+=i}return a}function tf(e){return 16843009*((e=(858993459&(e-=e>>1&1431655765))+(e>>2&858993459))+(e>>4)&252645135)>>>24}function rf(e,t){var r=t.l,a=127&e[r];e:if(e[r++]>=128){if(a|=(127&e[r])<<7,e[r++]<128)break e;if(a|=(127&e[r])<<14,e[r++]<128)break e;if(a|=(127&e[r])<<21,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,28),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,35),++r,e[r++]<128)break e;if(a+=(127&e[r])*Math.pow(2,42),++r,e[r++]<128)break e}return t.l=r,a}function af(e){var t=new Uint8Array(7);t[0]=127&e;var r=1;e:if(e>127){if(t[r-1]|=128,t[r]=e>>7&127,++r,e<=16383)break e;if(t[r-1]|=128,t[r]=e>>14&127,++r,e<=2097151)break e;if(t[r-1]|=128,t[r]=e>>21&127,++r,e<=268435455)break e;if(t[r-1]|=128,t[r]=e/256>>>21&127,++r,e<=34359738367)break e;if(t[r-1]|=128,t[r]=e/65536>>>21&127,++r,e<=4398046511103)break e;t[r-1]|=128,t[r]=e/16777216>>>21&127,++r}return t[Zl](0,r)}function nf(e){for(var t={l:0},r=[];t.l<e.length;)r.push(rf(e,t));return r}function sf(e){return ef(e.map((function(e){return af(e)})))}function of(e){var t=0,r=127&e[t];return e[t++]<128?r:(r|=(127&e[t])<<7,e[t++]<128?r:(r|=(127&e[t])<<14,e[t++]<128?r:(r|=(127&e[t])<<21,e[t++]<128?r:r|=(15&e[t])<<28)))}function cf(e){for(var t=[],r={l:0};r.l<e.length;){var a,n=r.l,s=rf(e,r),i=7&s;s=s/8|0;var o=r.l;switch(i){case 0:for(;e[o++]>=128;);a=e[Zl](r.l,o),r.l=o;break;case 1:a=e[Zl](o,o+8),r.l=o+8;break;case 2:var c=rf(e,r);a=e[Zl](r.l,r.l+c),r.l+=c;break;case 5:a=e[Zl](o,o+4),r.l=o+4;break;default:throw new Error("PB Type ".concat(i," for Field ").concat(s," at offset ").concat(n))}var l={data:a,type:i};null==t[s]&&(t[s]=[]),t[s].push(l)}return t}function lf(e){var t=[];return e.forEach((function(e,r){0!=r&&e.forEach((function(e){e.data&&(t.push(af(8*r+e.type)),2==e.type&&t.push(af(e.data.length)),t.push(e.data))}))})),ef(t)}function ff(e,t){return(null==e?void 0:e.map((function(e){return t(e.data)})))||[]}function hf(e){for(var t,r=[],a={l:0};a.l<e.length;){var n=rf(e,a),s=cf(e[Zl](a.l,a.l+n));a.l+=n;var i={id:of(s[1][0].data),messages:[]};s[2].forEach((function(t){var r=cf(t.data),n=of(r[3][0].data);i.messages.push({meta:r,data:e[Zl](a.l,a.l+n)}),a.l+=n})),(null==(t=s[3])?void 0:t[0])&&(i.merge=of(s[3][0].data)>>>0>0),r.push(i)}return r}function uf(e){var t=[];return e.forEach((function(e){var r=[[],[{data:af(e.id),type:0}],[]];null!=e.merge&&(r[3]=[{data:af(+!!e.merge),type:0}]);var a=[];e.messages.forEach((function(e){a.push(e.data),e.meta[3]=[{type:0,data:af(e.data.length)}],r[2].push({data:lf(e.meta),type:2})}));var n=lf(r);t.push(af(n.length)),t.push(n),a.forEach((function(e){return t.push(e)}))})),ef(t)}function df(e,t){if(0!=e)throw new Error("Unexpected Snappy chunk type ".concat(e));for(var r={l:0},a=rf(t,r),n=[],s=r.l;s<t.length;){var i=3&t[s];if(0!=i){var o=0,c=0;if(1==i?(c=4+(t[s]>>2&7),o=(224&t[s++])<<3,o|=t[s++]):(c=1+(t[s++]>>2),2==i?(o=t[s]|t[s+1]<<8,s+=2):(o=(t[s]|t[s+1]<<8|t[s+2]<<16|t[s+3]<<24)>>>0,s+=4)),0==o)throw new Error("Invalid offset 0");for(var l=n.length-1,f=o;l>=0&&f>=n[l].length;)f-=n[l].length,--l;if(l<0){if(0!=f)throw new Error("Invalid offset beyond length");f=n[l=0].length}if(c<f)n.push(n[l][Zl](n[l].length-f,n[l].length-f+c));else{for(f>0&&(n.push(n[l][Zl](n[l].length-f)),c-=f),++l;c>=n[l].length;)n.push(n[l]),c-=n[l].length,++l;c&&n.push(n[l][Zl](0,c))}n.length>25&&(n=[ef(n)])}else{var h=t[s++]>>2;if(h<60)++h;else{var u=h-59;h=t[s],u>1&&(h|=t[s+1]<<8),u>2&&(h|=t[s+2]<<16),u>3&&(h|=t[s+3]<<24),h>>>=0,h++,s+=u}n.push(t[Zl](s,s+h)),s+=h}}for(var d=0,p=0;p<n.length;++p)d+=n[p].length;if(d!=a)throw new Error("Unexpected length: ".concat(d," != ").concat(a));return n}function pf(e){Array.isArray(e)&&(e=new Uint8Array(e));for(var t=[],r=0;r<e.length;){var a=e[r++],n=e[r]|e[r+1]<<8|e[r+2]<<16;r+=3,t.push.apply(t,df(a,e[Zl](r,r+n))),r+=n}if(r!==e.length)throw new Error("data is not a valid framed stream!");return 1==t.length?t[0]:ef(t)}function mf(e){for(var t=[],r=0;r<e.length;){var a=Math.min(e.length-r,268435455),n=new Uint8Array(4);t.push(n);var s=af(a),i=s.length;t.push(s),a<=60?(i++,t.push(new Uint8Array([a-1<<2]))):a<=256?(i+=2,t.push(new Uint8Array([240,a-1&255]))):a<=65536?(i+=3,t.push(new Uint8Array([244,a-1&255,a-1>>8&255]))):a<=16777216?(i+=4,t.push(new Uint8Array([248,a-1&255,a-1>>8&255,a-1>>16&255]))):a<=4294967296&&(i+=5,t.push(new Uint8Array([252,a-1&255,a-1>>8&255,a-1>>16&255,a-1>>>24&255]))),t.push(e[Zl](r,r+a)),i+=a,n[0]=0,n[1]=255&i,n[2]=i>>8&255,n[3]=i>>16&255,r+=a}return ef(t)}var vf=function(){return{sst:[],rsst:[],ofmt:[],nfmt:[],fmla:[],ferr:[],cmnt:[]}};function gf(e,t,r,a,n){var s,i,o,c,l=255&t,f=t>>8,h=f>=5?n:a;e:if(r&(f>4?8:4)&&"n"==e.t&&7==l){var u=(null==(s=h[7])?void 0:s[0])?of(h[7][0].data):-1;if(-1==u)break e;var d=(null==(i=h[15])?void 0:i[0])?of(h[15][0].data):-1,p=(null==(o=h[16])?void 0:o[0])?of(h[16][0].data):-1,m=(null==(c=h[40])?void 0:c[0])?of(h[40][0].data):-1,v=e.v,g=v;t:if(m){if(0==v){d=p=2;break t}d=v>=604800?1:v>=86400?2:v>=3600?4:v>=60?8:v>=1?16:32,Math.floor(v)!=v?p=32:v%60?p=16:v%3600?p=8:v%86400?p=4:v%604800&&(p=2),p<d&&(p=d)}if(-1==d||-1==p)break e;var b=[],w=[];1==d&&(g=v/604800,1==p?w.push('d"d"'):v-=604800*(g|=0),b.push(g+(2==u?" week"+(1==g?"":"s"):1==u?"w":""))),d<=2&&p>=2&&(g=v/86400,p>2&&(v-=86400*(g|=0)),w.push('d"d"'),b.push(g+(2==u?" day"+(1==g?"":"s"):1==u?"d":""))),d<=4&&p>=4&&(g=v/3600,p>4&&(v-=3600*(g|=0)),w.push((d>=4?"[h]":"h")+'"h"'),b.push(g+(2==u?" hour"+(1==g?"":"s"):1==u?"h":""))),d<=8&&p>=8&&(g=v/60,p>8&&(v-=60*(g|=0)),w.push((d>=8?"[m]":"m")+'"m"'),0==u?b.push((8==d&&8==p||g>=10?"":"0")+g):b.push(g+(2==u?" minute"+(1==g?"":"s"):1==u?"m":""))),d<=16&&p>=16&&(g=v,p>16&&(v-=g|=0),w.push((d>=16?"[s]":"s")+'"s"'),0==u?b.push((16==p&&16==d||g>=10?"":"0")+g):b.push(g+(2==u?" second"+(1==g?"":"s"):1==u?"s":""))),p>=32&&(g=Math.round(1e3*v),d<32&&w.push('.000"ms"'),0==u?b.push((g>=100?"":g>=10?"0":"00")+g):b.push(g+(2==u?" millisecond"+(1==g?"":"s"):1==u?"ms":""))),e.w=b.join(0==u?":":" "),e.z=w.join(0==u?'":"':" "),0==u&&(e.w=e.w.replace(/:(\d\d\d)$/,".$1"))}}function bf(e,t){var r=new Uint8Array(32),a=Jl(r),n=12,s=0;switch(r[0]=5,e.t){case"n":if(e.z&&me(e.z)){r[1]=5,a.setFloat64(n,(ze(e.v+1462).getTime()-Date.UTC(2001,0,1))/1e3,!0),s|=4,n+=8;break}r[1]=2,function(e,t,r){var a=Math.floor(0==r?0:Math.LOG10E*Math.log(Math.abs(r)))+6176-16,n=r/Math.pow(10,a-6176);e[t+15]|=a>>7,e[t+14]|=(127&a)<<1;for(var s=0;n>=1;++s,n/=256)e[t+s]=255&n;e[t+15]|=r>=0?0:128}(r,n,e.v),s|=1,n+=16;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=2,n+=8;break;case"s":var i=null==e.v?"":String(e.v);if(e.l){var o=t.rsst.findIndex((function(t){var r;return t.v==i&&t.l==(null==(r=e.l)?void 0:r.Target)}));-1==o&&(t.rsst[o=t.rsst.length]={v:i,l:e.l.Target}),r[1]=9,a.setUint32(n,o,!0),s|=16,n+=4}else{var c=t.sst.indexOf(i);-1==c&&(t.sst[c=t.sst.length]=i),r[1]=3,a.setUint32(n,c,!0),s|=8,n+=4}break;case"d":r[1]=5,a.setFloat64(n,(e.v.getTime()-Date.UTC(2001,0,1))/1e3,!0),s|=4,n+=8;break;case"z":r[1]=0;break;default:throw"unsupported cell type "+e.t}return e.c&&(t.cmnt.push(function(e){for(var t={a:"",t:"",replies:[]},r=0;r<e.length;++r)0==r?(t.a=e[r].a,t.t=e[r].t):t.replies.push({a:e[r].a,t:e[r].t});return t}(e.c)),a.setUint32(n,t.cmnt.length-1,!0),s|=524288,n+=4),a.setUint32(8,s,!0),r[Zl](0,n)}function wf(e,t){var r=new Uint8Array(32),a=Jl(r),n=12,s=0,i="";switch(r[0]=4,e.t){case"n":case"b":case"d":case"e":case"z":break;case"s":if(i=null==e.v?"":String(e.v),e.l){var o=t.rsst.findIndex((function(t){var r;return t.v==i&&t.l==(null==(r=e.l)?void 0:r.Target)}));-1==o&&(t.rsst[o=t.rsst.length]={v:i,l:e.l.Target}),r[1]=9,a.setUint32(n,o,!0),s|=512,n+=4}break;default:throw"unsupported cell type "+e.t}switch(e.c&&(a.setUint32(n,t.cmnt.length-1,!0),s|=4096,n+=4),e.t){case"n":r[1]=2,a.setFloat64(n,e.v,!0),s|=32,n+=8;break;case"b":r[1]=6,a.setFloat64(n,e.v?1:0,!0),s|=32,n+=8;break;case"s":if(i=null==e.v?"":String(e.v),e.l);else{var c=t.sst.indexOf(i);-1==c&&(t.sst[c=t.sst.length]=i),r[1]=3,a.setUint32(n,c,!0),s|=16,n+=4}break;case"d":r[1]=5,a.setFloat64(n,(e.v.getTime()-Date.UTC(2001,0,1))/1e3,!0),s|=64,n+=8;break;case"z":r[1]=0;break;default:throw"unsupported cell type "+e.t}return a.setUint32(8,s,!0),r[Zl](0,n)}function Tf(e,t,r){switch(e[0]){case 0:case 1:case 2:case 3:case 4:return function(e,t,r,a){var n,s=Jl(e),i=s.getUint32(4,!0),o=-1,c=-1,l=-1,f=NaN,h=0,u=new Date(Date.UTC(2001,0,1)),d=r>1?12:8;2&i&&(l=s.getUint32(d,!0),d+=4),d+=4*tf(i&(r>1?3468:396)),512&i&&(o=s.getUint32(d,!0),d+=4),d+=4*tf(i&(r>1?12288:4096)),16&i&&(c=s.getUint32(d,!0),d+=4),32&i&&(f=s.getFloat64(d,!0),d+=8),64&i&&(u.setTime(u.getTime()+1e3*(h=s.getFloat64(d,!0))),d+=8),r>1&&255&(i=s.getUint32(8,!0)>>>16)&&(-1==l&&(l=s.getUint32(d,!0)),d+=4);var p=e[r>=4?1:2];switch(p){case 0:return;case 2:case 7:n={t:"n",v:f};break;case 3:n={t:"s",v:t.sst[c]};break;case 5:n=(null==a?void 0:a.cellDates)?{t:"d",v:u}:{t:"n",v:h/86400+35430,z:G[14]};break;case 6:n={t:"b",v:f>0};break;case 8:n={t:"e",v:0};break;case 9:if(!(o>-1))throw new Error("Unsupported cell type ".concat(e[Zl](0,4)));var m=t.rsst[o];n={t:"s",v:m.v},m.l&&(n.l={Target:m.l});break;default:throw new Error("Unsupported cell type ".concat(e[Zl](0,4)))}return l>-1&&gf(n,p|r<<8,i,t.ofmt[l],t.nfmt[l]),7==p&&(n.v/=86400),n}(e,t,e[0],r);case 5:return function(e,t,r){var a,n=Jl(e),s=(n.getUint32(4,!0),n.getUint32(8,!0)),i=12,o=-1,c=-1,l=-1,f=NaN,h=NaN,u=0,d=new Date(Date.UTC(2001,0,1));1&s&&(f=function(e,t){for(var r=(127&e[t+15])<<7|e[t+14]>>1,a=1&e[t+14],n=t+13;n>=t;--n)a=256*a+e[n];return(128&e[t+15]?-a:a)*Math.pow(10,r-6176)}(e,i),i+=16),2&s&&(h=n.getFloat64(i,!0),i+=8),4&s&&(d.setTime(d.getTime()+1e3*(u=n.getFloat64(i,!0))),i+=8),8&s&&(c=n.getUint32(i,!0),i+=4),16&s&&(o=n.getUint32(i,!0),i+=4),i+=4*tf(480&s),512&s&&(n.getUint32(i,!0),i+=4),i+=4*tf(1024&s),2048&s&&(n.getUint32(i,!0),i+=4);var p,m,v=e[1];switch(v){case 0:a={t:"z"};break;case 2:case 10:a={t:"n",v:f};break;case 3:a={t:"s",v:t.sst[c]};break;case 5:a=(null==r?void 0:r.cellDates)?{t:"d",v:d}:{t:"n",v:u/86400+35430,z:G[14]};break;case 6:a={t:"b",v:h>0};break;case 7:a={t:"n",v:h};break;case 8:a={t:"e",v:0};break;case 9:if(!(o>-1))throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e[Zl](0,4)));var g=t.rsst[o];a={t:"s",v:g.v},g.l&&(a.l={Target:g.l});break;default:throw new Error("Unsupported cell type ".concat(e[1]," : ").concat(31&s," : ").concat(e[Zl](0,4)))}if(i+=4*tf(4096&s),516096&s&&(-1==l&&(l=n.getUint32(i,!0)),i+=4),524288&s){var b=n.getUint32(i,!0);i+=4,t.cmnt[b]&&(a.c=(p=t.cmnt[b],(m=[]).push({t:p.t||"",a:p.a,T:p.replies&&p.replies.length>0}),p.replies&&p.replies.forEach((function(e){m.push({t:e.t||"",a:e.a,T:!0})})),m))}return l>-1&&gf(a,1280|v,s>>13,t.ofmt[l],t.nfmt[l]),7==v&&(a.v/=86400),a}(e,t,r);default:throw new Error("Unsupported payload version ".concat(e[0]))}}function yf(e){return of(cf(e)[1][0].data)}function Ef(e){return lf([[],[{type:0,data:af(e)}]])}function kf(e,t){var r,a=(null==(r=e.messages[0].meta[5])?void 0:r[0])?nf(e.messages[0].meta[5][0].data):[];-1==a.indexOf(t)&&(a.push(t),e.messages[0].meta[5]=[{type:2,data:sf(a)}])}function _f(e,t){var r,a=(null==(r=e.messages[0].meta[5])?void 0:r[0])?nf(e.messages[0].meta[5][0].data):[];e.messages[0].meta[5]=[{type:2,data:sf(a.filter((function(e){return e!=t})))}]}function Sf(e,t){var r=cf(t.data),a=of(r[1][0].data),n=r[3],s=[];return(n||[]).forEach((function(t){var r,n,i=cf(t.data);if(i[1]){var o=of(i[1][0].data)>>>0;switch(a){case 1:s[o]=ql(i[3][0].data);break;case 8:var c=cf(e[yf(i[9][0].data)][0].data),l=e[yf(c[1][0].data)][0],f=of(l.meta[1][0].data);if(2001!=f)throw new Error("2000 unexpected reference to ".concat(f));var h=cf(l.data),u={v:h[3].map((function(e){return ql(e.data)})).join("")};s[o]=u;e:if(null==(r=null==h?void 0:h[11])?void 0:r[0]){var d=null==(n=cf(h[11][0].data))?void 0:n[1];if(!d)break e;d.forEach((function(t){var r,a,n,s=cf(t.data);if(null==(r=s[2])?void 0:r[0]){var i=e[yf(null==(a=s[2])?void 0:a[0].data)][0],o=of(i.meta[1][0].data);switch(o){case 2032:var c=cf(i.data);(null==(n=null==c?void 0:c[2])?void 0:n[0])&&!u.l&&(u.l=ql(c[2][0].data));break;case 2039:break;default:console.log("unrecognized ObjectAttribute type ".concat(o))}}}))}break;case 2:s[o]=cf(i[6][0].data);break;case 3:s[o]=cf(i[5][0].data);break;case 10:var p=e[yf(i[10][0].data)][0];s[o]=xf(e,p.data);break;default:throw a}}})),s}function xf(e,t){var r,a,n,s,i,o,c,l,f,h,u={t:"",a:""},d=cf(t);if((null==(a=null==(r=null==d?void 0:d[1])?void 0:r[0])?void 0:a.data)&&(u.t=ql(null==(s=null==(n=null==d?void 0:d[1])?void 0:n[0])?void 0:s.data)||""),null==(o=null==(i=null==d?void 0:d[3])?void 0:i[0])?void 0:o.data){var p=cf(e[yf(null==(l=null==(c=null==d?void 0:d[3])?void 0:c[0])?void 0:l.data)][0].data);(null==(h=null==(f=p[1])?void 0:f[0])?void 0:h.data)&&(u.a=ql(p[1][0].data))}return(null==d?void 0:d[4])&&(u.replies=[],d[4].forEach((function(t){var r=e[yf(t.data)][0];u.replies.push(xf(e,r.data))}))),u}function Af(e,t,r){var a=cf(t.data),n={"!ref":"A1"};(null==r?void 0:r.dense)&&(n["!data"]=[]);var s=e[yf(a[2][0].data)],i=of(s[0].meta[1][0].data);if(6001!=i)throw new Error("6000 unexpected reference to ".concat(i));return function(e,t,r,a){var n,s,i,o,c,l,f,h,u,d,p=cf(t.data),m={s:{r:0,c:0},e:{r:0,c:0}};if(m.e.r=(of(p[6][0].data)>>>0)-1,m.e.r<0)throw new Error("Invalid row varint ".concat(p[6][0].data));if(m.e.c=(of(p[7][0].data)>>>0)-1,m.e.c<0)throw new Error("Invalid col varint ".concat(p[7][0].data));r["!ref"]=la(m);var v=null!=r["!data"],g=r,b=cf(p[4][0].data),w=vf();(null==(n=b[4])?void 0:n[0])&&(w.sst=Sf(e,e[yf(b[4][0].data)][0])),(null==(s=b[6])?void 0:s[0])&&(w.fmla=Sf(e,e[yf(b[6][0].data)][0])),(null==(i=b[11])?void 0:i[0])&&(w.ofmt=Sf(e,e[yf(b[11][0].data)][0])),(null==(o=b[12])?void 0:o[0])&&(w.ferr=Sf(e,e[yf(b[12][0].data)][0])),(null==(c=b[17])?void 0:c[0])&&(w.rsst=Sf(e,e[yf(b[17][0].data)][0])),(null==(l=b[19])?void 0:l[0])&&(w.cmnt=Sf(e,e[yf(b[19][0].data)][0])),(null==(f=b[22])?void 0:f[0])&&(w.nfmt=Sf(e,e[yf(b[22][0].data)][0]));var T=cf(b[3][0].data),y=0;if(!(null==(h=b[9])?void 0:h[0]))throw"NUMBERS file missing row tree";if(cf(b[9][0].data)[1].map((function(e){return cf(e.data)})).forEach((function(t){y=of(t[1][0].data);var n=of(t[2][0].data),s=T[1][n];if(!s)throw"NUMBERS missing tile "+n;var i=cf(s.data),o=e[yf(i[2][0].data)][0],c=of(o.meta[1][0].data);if(6002!=c)throw new Error("6001 unexpected reference to ".concat(c));var l=function(e,t){var r,a=cf(t.data),n=-1;(null==(r=null==a?void 0:a[7])?void 0:r[0])&&(n=of(a[7][0].data)>>>0?1:0);var s=ff(a[5],(function(e){return function(e,t){var r,a,n,s,i,o,c,l,f,h,u,d,p,m,v,g,b=cf(e),w=of(b[1][0].data)>>>0,T=of(b[2][0].data)>>>0,y=(null==(a=null==(r=b[8])?void 0:r[0])?void 0:a.data)&&of(b[8][0].data)>0||!1;if((null==(s=null==(n=b[7])?void 0:n[0])?void 0:s.data)&&0!=t)v=null==(o=null==(i=b[7])?void 0:i[0])?void 0:o.data,g=null==(l=null==(c=b[6])?void 0:c[0])?void 0:l.data;else{if(!(null==(h=null==(f=b[4])?void 0:f[0])?void 0:h.data)||1==t)throw"NUMBERS Tile missing ".concat(t," cell storage");v=null==(d=null==(u=b[4])?void 0:u[0])?void 0:d.data,g=null==(m=null==(p=b[3])?void 0:p[0])?void 0:m.data}for(var E=y?4:1,k=Jl(v),_=[],S=0;S<v.length/2;++S){var x=k.getUint16(2*S,!0);x<65535&&_.push([S,x])}if(_.length!=T)throw"Expected ".concat(T," cells, found ").concat(_.length);var A=[];for(S=0;S<_.length-1;++S)A[_[S][0]]=g[Zl](_[S][1]*E,_[S+1][1]*E);return _.length>=1&&(A[_[_.length-1][0]]=g[Zl](_[_.length-1][1]*E)),{R:w,cells:A}}(e,n)}));return{nrows:of(a[4][0].data)>>>0,data:s.reduce((function(e,t){return e[t.R]||(e[t.R]=[]),t.cells.forEach((function(r,a){if(e[t.R][a])throw new Error("Duplicate cell r=".concat(t.R," c=").concat(a));e[t.R][a]=r})),e}),[])}}(0,o);l.data.forEach((function(e,t){e.forEach((function(e,n){var s=Tf(e,w,a);s&&(v?(g["!data"][y+t]||(g["!data"][y+t]=[]),g["!data"][y+t][n]=s):r[sa(n)+aa(y+t)]=s)}))})),y+=l.nrows})),null==(u=b[13])?void 0:u[0]){var E=e[yf(b[13][0].data)][0],k=of(E.meta[1][0].data);if(6144!=k)throw new Error("Expected merge type 6144, found ".concat(k));r["!merges"]=null==(d=cf(E.data))?void 0:d[1].map((function(e){var t=cf(e.data),r=Jl(cf(t[1][0].data)[1][0].data),a=Jl(cf(t[2][0].data)[1][0].data);return{s:{r:r.getUint16(0,!0),c:r.getUint16(2,!0)},e:{r:r.getUint16(0,!0)+a.getUint16(0,!0)-1,c:r.getUint16(2,!0)+a.getUint16(2,!0)-1}}}))}}(e,s[0],n,r),n}function Cf(e,t){var r,a,n,s,i,o,c,l={},f=[];if(e.FullPaths.forEach((function(e){if(e.match(/\.iwpv2/))throw new Error("Unsupported password protection")})),e.FileIndex.forEach((function(e){if(e.name.match(/\.iwa$/)&&0==e.content[0]){var t,r;try{t=pf(e.content)}catch(t){return console.log("?? "+e.content.length+" "+(t.message||t))}try{r=hf(t)}catch(e){return console.log("## "+(e.message||e))}r.forEach((function(e){l[e.id]=e.messages,f.push(e.id)}))}})),!f.length)throw new Error("File has no messages");if((null==(n=null==(a=null==(r=null==l?void 0:l[1])?void 0:r[0].meta)?void 0:a[1])?void 0:n[0].data)&&1e4==of(l[1][0].meta[1][0].data))throw new Error("Pages documents are not supported");var h=(null==(c=null==(o=null==(i=null==(s=null==l?void 0:l[1])?void 0:s[0])?void 0:i.meta)?void 0:o[1])?void 0:c[0].data)&&1==of(l[1][0].meta[1][0].data)&&l[1][0];if(h||f.forEach((function(e){l[e].forEach((function(e){if(1==of(e.meta[1][0].data)>>>0){if(h)throw new Error("Document has multiple roots");h=e}}))})),!h)throw new Error("Cannot find Document root");return function(e,t,r){var a,n=bh();n.Workbook={WBProps:{date1904:!0}};var s=cf(t.data);if(null==(a=s[2])?void 0:a[0])throw new Error("Keynote presentations are not supported");if(ff(s[1],yf).forEach((function(t){e[t].forEach((function(t){if(2==of(t.meta[1][0].data)){var a=function(e,t,r){var a,n=cf(t.data),s={name:(null==(a=n[1])?void 0:a[0])?ql(n[1][0].data):"",sheets:[]};return ff(n[2],yf).forEach((function(t){e[t].forEach((function(t){6e3==of(t.meta[1][0].data)&&s.sheets.push(Af(e,t,r))}))})),s}(e,t,r);a.sheets.forEach((function(e,t){wh(n,e,0==t?a.name:a.name+"_"+t,!0)}))}}))})),0==n.SheetNames.length)throw new Error("Empty NUMBERS file");return n.bookType="numbers",n}(l,h,t)}function If(e,t,r){var a,n,s,i=[[],[{type:0,data:af(0)}],[{type:0,data:af(0)}],[{type:2,data:new Uint8Array([])}],[{type:2,data:new Uint8Array(Array.from({length:510},(function(){return 255})))}],[{type:0,data:af(5)}],[{type:2,data:new Uint8Array([])}],[{type:2,data:new Uint8Array(Array.from({length:510},(function(){return 255})))}],[{type:0,data:af(1)}]];if(!(null==(a=i[6])?void 0:a[0])||!(null==(n=i[7])?void 0:n[0]))throw"Mutation only works on post-BNC storages!";var o=0;if(i[7][0].data.length<2*e.length){var c=new Uint8Array(2*e.length);c.set(i[7][0].data),i[7][0].data=c}if(i[4][0].data.length<2*e.length){var l=new Uint8Array(2*e.length);l.set(i[4][0].data),i[4][0].data=l}for(var f=Jl(i[7][0].data),h=0,u=[],d=Jl(i[4][0].data),p=0,m=[],v=r?4:1,g=0;g<e.length;++g)if(null==e[g]||"z"==e[g].t&&!(null==(s=e[g].c)?void 0:s.length)||"e"==e[g].t)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535);else{var b,w;switch(f.setUint16(2*g,h/v,!0),d.setUint16(2*g,p/v,!0),e[g].t){case"d":if(e[g].v instanceof Date){b=bf(e[g],t),w=wf(e[g],t);break}b=bf(e[g],t),w=wf(e[g],t);break;case"s":case"n":case"b":case"z":b=bf(e[g],t),w=wf(e[g],t);break;default:throw new Error("Unsupported value "+e[g])}u.push(b),h+=b.length,m.push(w),p+=w.length,++o}for(i[2][0].data=af(o),i[5][0].data=af(5);g<i[7][0].data.length/2;++g)f.setUint16(2*g,65535,!0),d.setUint16(2*g,65535,!0);return i[6][0].data=ef(u),i[3][0].data=ef(m),i[8]=[{type:0,data:af(r?1:0)}],i}function Of(e,t){return{meta:[[],[{type:0,data:af(e)}]],data:t}}function Rf(e,t){t.last||(t.last=927262);for(var r=t.last;r<2e6;++r)if(!t[r])return t[t.last=r]=e,r;throw new Error("Too many messages")}function Nf(e,t,r){return lf([[],[{type:0,data:af(1)}],[],[{type:5,data:new Uint8Array(Float32Array.from([e/255]).buffer)}],[{type:5,data:new Uint8Array(Float32Array.from([t/255]).buffer)}],[{type:5,data:new Uint8Array(Float32Array.from([r/255]).buffer)}],[{type:5,data:new Uint8Array(Float32Array.from([1]).buffer)}],[],[],[],[],[],[{type:0,data:af(1)}]])}function Ff(e){switch(e){case 0:return Nf(99,222,171);case 1:return Nf(162,197,240);case 2:return Nf(255,189,189)}return Nf(255*Math.random(),255*Math.random(),255*Math.random())}function Df(e,t,r,a){var n=Ie.find(e,t[r].location);if(!n)throw"Could not find ".concat(t[r].location," in Numbers template");var s=hf(pf(n.content));a(s.find((function(e){return e.id==r})),s),n.content=mf(uf(s)),n.size=n.content.length}function Pf(e,t,r){var a=Ie.find(e,t[r].location);if(!a)throw"Could not find ".concat(t[r].location," in Numbers template");return hf(pf(a.content)).find((function(e){return e.id==r}))}function Mf(e,t,r){e[3].push({type:2,data:lf([[],[{type:0,data:af(t)}],[{type:2,data:Ql(r.replace(/-[\s\S]*$/,""))}],[{type:2,data:Ql(r)}],[{type:2,data:new Uint8Array([2,0,0])}],[{type:2,data:new Uint8Array([2,0,0])}],[],[],[],[],[{type:0,data:af(0)}],[],[{type:0,data:af(0)}]])}),e[1]=[{type:0,data:af(Math.max(t+1,of(e[1][0].data)))}]}function Lf(e,t,r,a,n,s){s||(s=Rf({deps:[],location:"",type:t},n));var i="".concat(a,"-").concat(s,".iwa");n[s].location="Root Entry"+i,Ie.utils.cfb_add(e,i,mf(uf([{id:s,messages:[Of(t,lf(r))]}])));var o=i.replace(/^[\/]/,"").replace(/^Index\//,"").replace(/\.iwa$/,"");return Df(e,n,2,(function(e){var t=cf(e.messages[0].data);Mf(t,s||0,o),e.messages[0].data=lf(t)})),s}function Uf(e,t,r,a){var n=t[r].location.replace(/^Root Entry\//,"").replace(/^Index\//,"").replace(/\.iwa$/,""),s=e[3].findIndex((function(e){var t,r,a=cf(e.data);return(null==(t=a[3])?void 0:t[0])?ql(a[3][0].data)==n:!(!(null==(r=a[2])?void 0:r[0])||ql(a[2][0].data)!=n)})),i=cf(e[3][s].data);i[6]||(i[6]=[]),(Array.isArray(a)?a:[a]).forEach((function(e){i[6].push({type:2,data:lf([[],[{type:0,data:af(e)}]])})})),e[3][s].data=lf(i)}var Bf=!0;function Wf(e){return function(t){for(var r=0;r!=e.length;++r){var a=e[r];void 0===t[a[0]]&&(t[a[0]]=a[1]),"n"===a[2]&&(t[a[0]]=Number(t[a[0]]))}}}function zf(e){Wf([["cellNF",!1],["cellHTML",!0],["cellFormula",!0],["cellStyles",!1],["cellText",!0],["cellDates",!1],["sheetStubs",!1],["sheetRows",0,"n"],["bookDeps",!1],["bookSheets",!1],["bookProps",!1],["bookFiles",!1],["bookVBA",!1],["password",""],["WTF",!1]])(e)}function Hf(e){Wf([["cellDates",!1],["bookSST",!1],["bookType","xlsx"],["compression",!1],["WTF",!1]])(e)}function Vf(e,t,r,a,n,s,i,o,c,l,f,h){try{s[a]=fn(Tt(e,r,!0),t);var u,d=wt(e,t);switch(o){case"sheet":u=Jc(d,t,n,c,s[a],l,f,h);break;case"chart":if(!(u=function(e,t,r,a,n,s,i,o){return".bin"===t.slice(-4)?function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s={"!type":"chart","!drawel":null,"!rel":""},i=[],o=!1;return Yr(e,(function(e,a,c){switch(c){case 550:s["!rel"]=e;break;case 651:n.Sheets[r]||(n.Sheets[r]={}),e.name&&(n.Sheets[r].CodeName=e.name);break;case 562:case 652:case 669:case 679:case 551:case 552:case 476:case 3072:break;case 35:o=!0;break;case 36:o=!1;break;case 37:i.push(c);break;case 38:i.pop();break;default:if(a.T>0)i.push(c);else if(a.T<0)i.pop();else if(!o||t.WTF)throw new Error("Unexpected record 0x"+c.toString(16))}}),t),a["!id"][s["!rel"]]&&(s["!drawel"]=a["!id"][s["!rel"]]),s}(e,a,r,n,s):function(e,t,r,a,n){if(!e)return e;a||(a={"!id":{}});var s,i={"!type":"chart","!drawel":null,"!rel":""},o=e.match(wc);return o&&Tc(o[0],0,n,r),(s=e.match(/drawing r:id="(.*?)"/))&&(i["!rel"]=s[1]),a["!id"][i["!rel"]]&&(i["!drawel"]=a["!id"][i["!rel"]]),i}(e,0,r,n,s)}(d,t,n,c,s[a],l))||!u["!drawel"])break;var p=xt(u["!drawel"].Target,t),m=ln(p),v=function(e,t){if(!e)return"??";var r=(e.match(/<c:chart [^<>]*r:id="([^<>"]*)"/)||["",""])[1];return t["!id"][r].Target}(Tt(e,p,!0),fn(Tt(e,m,!0),p)),g=xt(v,p),b=ln(g);u=function(e,t,r,a,n,s){var i=s||{"!type":"chart"};if(!e)return s;var o=0,c=0,l="A",f={s:{r:2e6,c:2e6},e:{r:0,c:0}};return(ot(e,"<c:numCache>","</c:numCache>")||[]).forEach((function(e){var t=function(e){var t,r=[],a=e.match(/^<c:numCache>/);(e.match(/<c:pt idx="(\d*)"[^<>\/]*><c:v>([^<])<\/c:v><\/c:pt>/gm)||[]).forEach((function(e){var t=e.match(/<c:pt idx="(\d*)"[^<>\/]*><c:v>([^<]*)<\/c:v><\/c:pt>/);t&&(r[+t[1]]=a?+t[2]:t[2])}));var n=Lt((ft(e,"c:formatCode")||["","General"])[1]);return(ot(e,"<c:f>","</c:f>")||[]).forEach((function(e){t=e.replace(/<[^<>]*>/g,"")})),[r,n,t]}(e);f.s.r=f.s.c=0,f.e.c=o,l=sa(o),t[0].forEach((function(e,r){i["!data"]?(i["!data"][r]||(i["!data"][r]=[]),i["!data"][r][o]={t:"n",v:e,z:t[1]}):i[l+aa(r)]={t:"n",v:e,z:t[1]},c=r})),f.e.r<c&&(f.e.r=c),++o})),o>0&&(i["!ref"]=la(f)),i}(Tt(e,g,!0),0,0,fn(Tt(e,b,!0),g),0,u);break;case"macro":y=t,s[a],y.slice(-4),u={"!type":"macro"};break;case"dialog":u=function(e,t,r,a,n,s,i,o){return t.slice(-4),{"!type":"dialog"}}(0,t,0,0,s[a]);break;default:throw new Error("Unrecognized sheet type "+o)}i[a]=u;var w=[],T=[];s&&s[a]&&Fe(s[a]).forEach((function(r){var n="";if(s[a][r].Type==cn.CMNT){if(n=xt(s[a][r].Target,t),!(w=function(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=[],n={},s=!1;return Yr(e,(function(e,i,o){switch(o){case 632:a.push(e);break;case 635:n=e;break;case 637:n.t=e.t,n.h=e.h,n.r=e.r;break;case 636:if(n.author=a[n.iauthor],delete n.iauthor,t.sheetRows&&n.rfx&&t.sheetRows<=n.rfx.r)break;n.t||(n.t=""),delete n.rfx,r.push(n);break;case 3072:case 37:case 38:break;case 35:s=!0;break;case 36:s=!1;break;default:if(i.T);else if(!s||t.WTF)throw new Error("Unexpected record 0x"+o.toString(16))}})),r}(e,r):function(e,t){if(e.match(/<(?:\w+:)?comments *\/>/))return[];var r=[],a=[],n=ht(e,"authors");n&&n[1]&&n[1].split(/<\/\w*:?author>/).forEach((function(e){if(""!==e&&""!==e.trim()){var t=e.match(/<(?:\w+:)?author[^<>]*>(.*)/);t&&r.push(t[1])}}));var s=ht(e,"commentList");return s&&s[1]&&s[1].split(/<\/\w*:?comment>/).forEach((function(e){if(""!==e&&""!==e.trim()){var n=e.match(/<(?:\w+:)?comment[^<>]*>/);if(n){var s=Ft(n[0]),i={author:s.authorId&&r[s.authorId]||"sheetjsghost",ref:s.ref,guid:s.guid},o=ia(s.ref);if(!(t.sheetRows&&t.sheetRows<=o.r)){var c=ht(e,"text"),l=!!c&&!!c[1]&&si(c[1])||{r:"",t:"",h:""};i.r=l.r,"<t></t>"==l.r&&(l.t=l.h=""),i.t=(l.t||"").replace(/\r\n/g,"\n").replace(/\r/g,"\n"),t.cellHTML&&(i.h=l.h),a.push(i)}}}})),a}(e,r)}(wt(e,n,!0),n,c))||!w.length)return;eo(u,w,!1)}s[a][r].Type==cn.TCMNT&&(n=xt(s[a][r].Target,t),T=T.concat(function(e,t){var r=[],a=!1,n={},s=0;return e.replace(Ot,(function(i,o){var c=Ft(i);switch(Dt(c[0])){case"<?xml":case"<ThreadedComments":case"</ThreadedComments>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<threadedComment":n={author:c.personId,guid:c.id,ref:c.ref,T:1};break;case"</threadedComment>":null!=n.t&&r.push(n);break;case"<text>":case"<text":s=o+i.length;break;case"</text>":n.t=e.slice(s,o).replace(/\r\n/g,"\n").replace(/\r/g,"\n");break;case"<mentions":case"<mentions>":case"<ext":a=!0;break;case"</mentions>":case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+c[0]+" in threaded comments")}return i})),r}(wt(e,n,!0),c)))})),T&&T.length&&eo(u,T,!0,c.people||[]),function(e,t,r,a,n,s,i,o){if(e&&e["!legdrawel"]){var c=Tt(r,xt(e["!legdrawel"].Target,a),!0);c&&function(e,t,r){var a=0;(ut(e,"shape")||[]).forEach((function(e){var n="",s=!0,i=-1,o=-1,c=-1;if(e.replace(Ot,(function(t,r){var a=Ft(t);switch(Dt(a[0])){case"<ClientData":a.ObjectType&&(n=a.ObjectType);break;case"<Visible":case"<Visible/>":s=!1;break;case"<Row":case"<Row>":case"<Column":case"<Column>":i=r+t.length;break;case"</Row>":o=+e.slice(i,r).trim();break;case"</Column>":c=+e.slice(i,r).trim()}return""})),"Note"===n){var l=gh(t,o>=0&&c>=0?oa({r:o,c:c}):r[a].ref);l.c&&(l.c.hidden=s),++a}}))}(Zt(c),e,o||[])}}(u,0,e,t,0,0,0,w)}catch(e){if(c.WTF)throw e}var y}function $f(e){return"/"==e.charAt(0)?e.slice(1):e}function Gf(e,t){if(ye(),zf(t=t||{}),gt(e,"META-INF/manifest.xml"))return Vl(e,t);if(gt(e,"objectdata.xml"))return Vl(e,t);if(gt(e,"Index/Document.iwa")){if("undefined"==typeof Uint8Array)throw new Error("NUMBERS file parsing requires Uint8Array support");if(void 0!==Cf){if(e.FileIndex)return Cf(e,t);var r=Ie.utils.cfb_new();return Et(e).forEach((function(t){kt(r,t,yt(e,t))})),Cf(r,t)}throw new Error("Unsupported NUMBERS file")}if(!gt(e,"[Content_Types].xml")){if(gt(e,"index.xml.gz"))throw new Error("Unsupported NUMBERS 08 file");if(gt(e,"index.xml"))throw new Error("Unsupported NUMBERS 09 file");var a=Ie.find(e,"Index.zip");if(a)return delete(t=Ke(t)).type,"string"==typeof a.content&&(t.type="binary"),"undefined"!=typeof Bun&&Buffer.isBuffer(a.content)?Jf(new Uint8Array(a.content),t):Jf(a.content,t);throw new Error("Unsupported ZIP file")}var n,s,i=Et(e),o=function(e){var t={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};if(!e||!e.match)return t;var r={};if((e.match(Ot)||[]).forEach((function(e){var a=Ft(e);switch(a[0].replace(Rt,"<")){case"<?xml":break;case"<Types":t.xmlns=a["xmlns"+(a[0].match(/<(\w+):/)||["",""])[1]];break;case"<Default":r[a.Extension.toLowerCase()]=a.ContentType;break;case"<Override":void 0!==t[nn[a.ContentType]]&&t[nn[a.ContentType]].push(a.PartName)}})),t.xmlns!==lr.CT)throw new Error("Unknown Namespace: "+t.xmlns);return t.calcchain=t.calcchains.length>0?t.calcchains[0]:"",t.sst=t.strs.length>0?t.strs[0]:"",t.style=t.styles.length>0?t.styles[0]:"",t.defaults=r,delete t.calcchains,t}(Tt(e,"[Content_Types].xml")),c=!1;if(0===o.workbooks.length&&wt(e,s="xl/workbook.xml",!0)&&o.workbooks.push(s),0===o.workbooks.length){if(!wt(e,s="xl/workbook.bin",!0))throw new Error("Could not find workbook");o.workbooks.push(s),c=!0}"bin"==o.workbooks[0].slice(-3)&&(c=!0);var l={},f={};if(!t.bookSheets&&!t.bookProps){if(nc=[],o.sst)try{nc=function(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r=[],a=!1;return Yr(e,(function(e,n,s){switch(s){case 159:r.Count=e[0],r.Unique=e[1];break;case 19:r.push(e);break;case 160:return!0;case 35:a=!0;break;case 36:a=!1;break;default:if(n.T,!a||t.WTF)throw new Error("Unexpected record 0x"+s.toString(16))}})),r}(e,r):function(e,t){var r=[],a="";if(!e)return r;var n=ht(e,"sst");if(n){a=n[1].replace(ii,"").split(oi);for(var s=0;s!=a.length;++s){var i=si(a[s].trim(),t);null!=i&&(r[r.length]=i)}n=Ft(n[0].slice(0,n[0].indexOf(">"))),r.Count=n.count,r.Unique=n.uniqueCount}return r}(e,r)}(wt(e,$f(o.sst)),o.sst,t)}catch(e){if(t.WTF)throw e}t.cellStyles&&o.themes.length&&(l=Zi(Tt(e,o.themes[0].replace(/^\//,""),!0)||"",t)),o.style&&(f=function(e,t,r,a){return".bin"===t.slice(-4)?function(e,t,r){var a={NumberFmt:[]};for(var n in G)a.NumberFmt[n]=G[n];a.CellXf=[],a.Fonts=[];var s=[],i=!1;return Yr(e,(function(e,n,o){switch(o){case 44:a.NumberFmt[e[0]]=e[1],xe(e[1],e[0]);break;case 43:a.Fonts.push(e),null!=e.color.theme&&t&&t.themeElements&&t.themeElements.clrScheme&&(e.color.rgb=Ei(t.themeElements.clrScheme[e.color.theme].rgb,e.color.tint||0));break;case 1025:case 45:case 46:case 48:case 507:case 572:case 475:case 1171:case 2102:case 1130:case 512:case 2095:case 3072:break;case 47:617==s[s.length-1]&&a.CellXf.push(e);break;case 35:i=!0;break;case 36:i=!1;break;case 37:s.push(o),i=!0;break;case 38:s.pop(),i=!1;break;default:if(n.T>0)s.push(o);else if(n.T<0)s.pop();else if(!i||r.WTF&&37!=s[s.length-1])throw new Error("Unexpected record 0x"+o.toString(16))}})),a}(e,r,a):Bi(e,r,a)}(wt(e,$f(o.style)),o.style,l,t))}o.links.map((function(r){try{return fn(Tt(e,ln($f(r))),r),function(e,t,r,a){if(".bin"===r.slice(-4))return function(e,t,r,a){if(!e)return e;var n=a||{},s=!1;Yr(e,(function(e,t,r){switch(r){case 359:case 363:case 364:case 366:case 367:case 368:case 369:case 370:case 371:case 472:case 577:case 578:case 579:case 580:case 581:case 582:case 583:case 584:case 585:case 586:case 587:break;case 35:s=!0;break;case 36:s=!1;break;default:if(t.T);else if(!s||n.WTF)throw new Error("Unexpected record 0x"+r.toString(16))}}),n)}(e,0,0,a)}(wt(e,$f(r)),0,r,t)}catch(e){}}));var h=function(e,t,r){return".bin"===t.slice(-4)?function(e,t){var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},xmlns:""},a=[],n=!1;t||(t={}),t.biff=12;var s=[],i=[[]];return i.SheetNames=[],i.XTI=[],Tl[16]={n:"BrtFRTArchID$",f:Yc},Yr(e,(function(e,o,c){switch(c){case 156:i.SheetNames.push(e.name),r.Sheets.push(e);break;case 153:r.WBProps=e;break;case 39:null!=e.Sheet&&(t.SID=e.Sheet),e.Ref=e.Ptg?zo(e.Ptg,0,null,i,t):"#REF!",delete t.SID,delete e.Ptg,s.push(e);break;case 1036:case 361:case 2071:case 158:case 143:case 664:case 353:case 3072:case 3073:case 534:case 677:case 157:case 610:case 2050:case 155:case 548:case 676:case 128:case 665:case 2128:case 2125:case 549:case 2053:case 596:case 2076:case 2075:case 2082:case 397:case 154:case 1117:case 553:case 2091:case 16:break;case 357:case 358:case 355:case 667:i[0].length?i.push([c,e]):i[0]=[c,e],i[i.length-1].XTI=[];break;case 362:0===i.length&&(i[0]=[],i[0].XTI=[]),i[i.length-1].XTI=i[i.length-1].XTI.concat(e),i.XTI=i.XTI.concat(e);break;case 35:case 37:a.push(c),n=!0;break;case 36:case 38:a.pop(),n=!1;break;default:if(o.T);else if(!n||t.WTF&&37!=a[a.length-1]&&35!=a[a.length-1])throw new Error("Unexpected record 0x"+c.toString(16))}}),t),$c(r),r.Names=s,r.supbooks=i,r}(e,r):function(e,t){if(!e)throw new Error("Could not find file");var r={AppVersion:{},WBProps:{},WBView:[],Sheets:[],CalcPr:{},Names:[],xmlns:""},a=!1,n="xmlns",s={},i=0;if(e.replace(Ot,(function(o,c){var l=Ft(o);switch(Dt(l[0])){case"<?xml":case"</workbook>":case"<fileVersion/>":case"</fileVersion>":case"<fileSharing":case"<fileSharing/>":case"</workbookPr>":case"<workbookProtection":case"<workbookProtection/>":case"<bookViews":case"<bookViews>":case"</bookViews>":case"</workbookView>":case"<sheets":case"<sheets>":case"</sheets>":case"</sheet>":case"<functionGroups":case"<functionGroups/>":case"<functionGroup":case"<externalReferences":case"</externalReferences>":case"<externalReferences>":case"<externalReference":case"<definedNames/>":case"<definedName/>":case"</calcPr>":case"<oleSize":case"<customWorkbookViews>":case"</customWorkbookViews>":case"<customWorkbookViews":case"<customWorkbookView":case"</customWorkbookView>":case"<pivotCaches>":case"</pivotCaches>":case"<pivotCaches":case"<pivotCache":case"<smartTagPr":case"<smartTagPr/>":case"<smartTagTypes":case"<smartTagTypes>":case"</smartTagTypes>":case"<smartTagType":case"<webPublishing":case"<webPublishing/>":case"<fileRecoveryPr":case"<fileRecoveryPr/>":case"<webPublishObjects>":case"<webPublishObjects":case"</webPublishObjects>":case"<webPublishObject":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":case"<ArchID":case"<revisionPtr":break;case"<workbook":o.match(Kc)&&(n="xmlns"+o.match(/<(\w+):/)[1]),r.xmlns=l[n];break;case"<fileVersion":delete l[0],r.AppVersion=l;break;case"<workbookPr":case"<workbookPr/>":Uc.forEach((function(e){if(null!=l[e[0]])switch(e[2]){case"bool":r.WBProps[e[0]]=Gt(l[e[0]]);break;case"int":r.WBProps[e[0]]=parseInt(l[e[0]],10);break;default:r.WBProps[e[0]]=l[e[0]]}})),l.codeName&&(r.WBProps.CodeName=Zt(l.codeName));break;case"<workbookView":case"<workbookView/>":delete l[0],r.WBView.push(l);break;case"<sheet":switch(l.state){case"hidden":l.Hidden=1;break;case"veryHidden":l.Hidden=2;break;default:l.Hidden=0}delete l.state,l.name=Lt(Zt(l.name)),delete l[0],r.Sheets.push(l);break;case"<definedNames>":case"<definedNames":case"<ext":case"<AlternateContent":case"<AlternateContent>":a=!0;break;case"</definedNames>":case"</ext>":case"</AlternateContent>":a=!1;break;case"<definedName":(s={}).Name=Zt(l.name),l.comment&&(s.Comment=l.comment),l.localSheetId&&(s.Sheet=+l.localSheetId),Gt(l.hidden||"0")&&(s.Hidden=!0),i=c+o.length;break;case"</definedName>":s.Ref=Lt(Zt(e.slice(i,c))),r.Names.push(s);break;case"<calcPr":case"<calcPr/>":delete l[0],r.CalcPr=l;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+l[0]+" in workbook")}return o})),-1===fr.indexOf(r.xmlns))throw new Error("Unknown Namespace: "+r.xmlns);return $c(r),r}(e,r)}(wt(e,$f(o.workbooks[0])),o.workbooks[0],t),u={},d="";o.coreprops.length&&((d=wt(e,$f(o.coreprops[0]),!0))&&(u=gn(d)),0!==o.extprops.length&&(d=wt(e,$f(o.extprops[0]),!0))&&function(e,t,r){var a={};t||(t={}),e=Zt(e),Tn.forEach((function(r){var n=(ht(e,r[0])||[])[1];switch(r[2]){case"string":n&&(t[r[1]]=Lt(n));break;case"bool":t[r[1]]="true"===n;break;case"raw":var s=ft(e,r[0]);s&&s.length>0&&(a[r[1]]=s[1])}})),a.HeadingPairs&&a.TitlesOfParts&&En(a.HeadingPairs,a.TitlesOfParts,t,r)}(d,u,t));var p={};t.bookSheets&&!t.bookProps||0!==o.custprops.length&&(d=Tt(e,$f(o.custprops[0]),!0))&&(p=function(e,t){var r={},a="",n=e.match(_n);if(n)for(var s=0;s!=n.length;++s){var i=n[s],o=Ft(i);switch(Dt(o[0])){case"<?xml":case"<Properties":break;case"<property":a=Lt(o.name);break;case"</property>":a=null;break;default:if(0===i.indexOf("<vt:")){var c=i.split(">"),l=c[0].slice(4),f=c[1];switch(l){case"lpstr":case"bstr":case"lpwstr":case"cy":case"error":r[a]=Lt(f);break;case"bool":r[a]=Gt(f);break;case"i1":case"i2":case"i4":case"i8":case"int":case"uint":r[a]=parseInt(f,10);break;case"r4":case"r8":case"decimal":r[a]=parseFloat(f);break;case"filetime":case"date":r[a]=Xe(f);break;default:if("/"==l.slice(-1))break;t.WTF&&"undefined"!=typeof console&&console.warn("Unexpected",i,l,c)}}else if("</"===i.slice(0,2));else if(t.WTF)throw new Error(i)}}return r}(d,t));var m={};if((t.bookSheets||t.bookProps)&&(h.Sheets?n=h.Sheets.map((function(e){return e.name})):u.Worksheets&&u.SheetNames.length>0&&(n=u.SheetNames),t.bookProps&&(m.Props=u,m.Custprops=p),t.bookSheets&&void 0!==n&&(m.SheetNames=n),t.bookSheets?m.SheetNames:t.bookProps))return m;n={};var v,g={};t.bookDeps&&o.calcchain&&(v=wt(e,$f(o.calcchain)),g=".bin"===o.calcchain.slice(-4)?function(e,t,r){var a=[];return Yr(e,(function(e,t,r){if(63===r)a.push(e);else if(!t.T)throw new Error("Unexpected record 0x"+r.toString(16))})),a}(v):function(e){var t=[];if(!e)return t;var r=1;return(e.match(Ot)||[]).forEach((function(e){var a=Ft(e);switch(a[0]){case"<?xml":case"<calcChain":case"<calcChain>":case"</calcChain>":break;case"<c":delete a[0],a.i?r=a.i:a.i=r,t.push(a)}})),t}(v));var b,w,T=0,y={},E=h.Sheets;u.Worksheets=E.length,u.SheetNames=[];for(var k=0;k!=E.length;++k)u.SheetNames[k]=E[k].name;var _=c?"bin":"xml",S=o.workbooks[0].lastIndexOf("/"),x=(o.workbooks[0].slice(0,S+1)+"_rels/"+o.workbooks[0].slice(S+1)+".rels").replace(/^\//,"");gt(e,x)||(x="xl/_rels/workbook."+_+".rels");var A=fn(Tt(e,x,!0),x.replace(/_rels.*/,"s5s"));(o.metadata||[]).length>=1&&(t.xlmeta=function(e,t,r){return".bin"===t.slice(-4)?function(e,t,r){var a={Types:[],Cell:[],Value:[]},n=r||{},s=[],i=!1,o=2;return Yr(e,(function(e,t,r){switch(r){case 335:a.Types.push({name:e.name});break;case 51:e.forEach((function(e){1==o?a.Cell.push({type:a.Types[e[0]-1].name,index:e[1]}):0==o&&a.Value.push({type:a.Types[e[0]-1].name,index:e[1]})}));break;case 337:o=e?1:0;break;case 338:o=2;break;case 35:s.push(r),i=!0;break;case 36:s.pop(),i=!1;break;default:if(t.T);else if(!i||n.WTF&&35!=s[s.length-1])throw new Error("Unexpected record 0x"+r.toString(16))}})),a}(e,0,r):function(e,t,r){var a={Types:[],Cell:[],Value:[]};if(!e)return a;var n,s=!1,i=2;return e.replace(Ot,(function(e){var t=Ft(e);switch(Dt(t[0])){case"<?xml":case"<metadata":case"</metadata>":case"<metadataTypes":case"</metadataTypes>":case"</metadataType>":case"</futureMetadata>":case"<bk>":case"</bk>":case"</rc>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<metadataType":a.Types.push({name:t.name});break;case"<futureMetadata":for(var o=0;o<a.Types.length;++o)a.Types[o].name==t.name&&(n=a.Types[o]);break;case"<rc":1==i?a.Cell.push({type:a.Types[t.t-1].name,index:+t.v}):0==i&&a.Value.push({type:a.Types[t.t-1].name,index:+t.v});break;case"<cellMetadata":i=1;break;case"</cellMetadata>":case"</valueMetadata>":i=2;break;case"<valueMetadata":i=0;break;case"<ext":s=!0;break;case"</ext>":s=!1;break;case"<rvb":if(!n)break;n.offsets||(n.offsets=[]),n.offsets.push(+t.i);break;default:if(!s&&(null==r?void 0:r.WTF))throw new Error("unrecognized "+t[0]+" in metadata")}return e})),a}(e,0,r)}(wt(e,$f(o.metadata[0])),o.metadata[0],t)),(o.people||[]).length>=1&&(t.people=function(e,t){var r=[],a=!1;return e.replace(Ot,(function(e){var n=Ft(e);switch(Dt(n[0])){case"<?xml":case"<personList":case"</personList>":case"</person>":case"<extLst":case"<extLst>":case"</extLst>":case"<extLst/>":break;case"<person":r.push({name:n.displayname,id:n.id});break;case"<ext":a=!0;break;case"</ext>":a=!1;break;default:if(!a&&t.WTF)throw new Error("unrecognized "+n[0]+" in threaded comments")}return e})),r}(wt(e,$f(o.people[0])),t)),A&&(A=function(e,t){if(!e)return 0;try{e=t.map((function(t){return t.id||(t.id=t.strRelID),[t.name,e["!id"][t.id].Target,(r=e["!id"][t.id].Type,cn.WS.indexOf(r)>-1?"sheet":cn.CS&&r==cn.CS?"chart":cn.DS&&r==cn.DS?"dialog":cn.MS&&r==cn.MS?"macro":r&&r.length?r:"sheet")];var r}))}catch(e){return null}return e&&0!==e.length?e:null}(A,h.Sheets));var C=wt(e,"xl/worksheets/sheet.xml",!0)?1:0;e:for(T=0;T!=u.Worksheets;++T){var I="sheet";if(A&&A[T]?(b="xl/"+A[T][1].replace(/[\/]?xl\//,""),gt(e,b)||(b=A[T][1]),gt(e,b)||(b=x.replace(/_rels\/[\S\s]*$/,"")+A[T][1]),I=A[T][2]):b=(b="xl/worksheets/sheet"+(T+1-C)+"."+_).replace(/sheet0\./,"sheet."),w=b.replace(/^(.*)(\/)([^\/]*)$/,"$1/_rels/$3.rels"),t&&null!=t.sheets)switch(typeof t.sheets){case"number":if(T!=t.sheets)continue e;break;case"string":if(u.SheetNames[T].toLowerCase()!=t.sheets.toLowerCase())continue e;break;default:if(Array.isArray&&Array.isArray(t.sheets)){for(var O=!1,R=0;R!=t.sheets.length;++R)"number"==typeof t.sheets[R]&&t.sheets[R]==T&&(O=1),"string"==typeof t.sheets[R]&&t.sheets[R].toLowerCase()==u.SheetNames[T].toLowerCase()&&(O=1);if(!O)continue e}}Vf(e,b,w,u.SheetNames[T],T,y,n,I,t,h,l,f)}return m={Directory:o,Workbook:h,Props:u,Custprops:p,Deps:g,Sheets:n,SheetNames:u.SheetNames,Strings:nc,Styles:f,Themes:l,SSF:Ke(G)},t&&t.bookFiles&&(e.files?(m.keys=i,m.files=e.files):(m.keys=[],m.files={},e.FullPaths.forEach((function(t,r){t=t.replace(/^Root Entry[\/]/,""),m.keys.push(t),m.files[t]=e.FileIndex[r]})))),t&&t.bookVBA&&(o.vba.length>0?m.vbaraw=wt(e,$f(o.vba[0]),!0):o.defaults&&o.defaults.bin===oo&&(m.vbaraw=wt(e,"xl/vbaProject.bin",!0))),m.bookType=c?"xlsb":"xlsx",m}function Xf(e,t){var r,a,n=t||{},s="Workbook",i=Ie.find(e,s);try{if(s="/!DataSpaces/Version",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(r=i.content,(a={}).id=r.read_shift(0,"lpp4"),a.R=hi(r,4),a.U=hi(r,4),a.W=hi(r,4),s="/!DataSpaces/DataSpaceMap",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var o=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(ui(e));return t}(i.content);if(1!==o.length||1!==o[0].comps.length||0!==o[0].comps[0].t||"StrongEncryptionDataSpace"!==o[0].name||"EncryptedPackage"!==o[0].comps[0].v)throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/DataSpaceInfo/StrongEncryptionDataSpace",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var c=function(e){var t=[];e.l+=4;for(var r=e.read_shift(4);r-- >0;)t.push(e.read_shift(0,"lpp4"));return t}(i.content);if(1!=c.length||"StrongEncryptionTransform"!=c[0])throw new Error("ECMA-376 Encrypted file bad "+s);if(s="/!DataSpaces/TransformInfo/StrongEncryptionTransform/!Primary",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);!function(e){var t=function(e){var t={};return e.read_shift(4),e.l+=4,t.id=e.read_shift(0,"lpp4"),t.name=e.read_shift(0,"lpp4"),t.R=hi(e,4),t.U=hi(e,4),t.W=hi(e,4),t}(e);if(t.ename=e.read_shift(0,"8lpp4"),t.blksz=e.read_shift(4),t.cmode=e.read_shift(4),4!=e.read_shift(4))throw new Error("Bad !Primary record")}(i.content)}catch(e){}if(s="/EncryptionInfo",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);var l=function(e){var t=hi(e);switch(t.Minor){case 2:return[t.Minor,mi(e)];case 3:return[t.Minor,vi()];case 4:return[t.Minor,gi(e)]}throw new Error("ECMA-376 Encrypted file unrecognized Version: "+t.Minor)}(i.content);if(s="/EncryptedPackage",!(i=Ie.find(e,s))||!i.content)throw new Error("ECMA-376 Encrypted file missing "+s);if(4==l[0]&&"undefined"!=typeof decrypt_agile)return decrypt_agile(l[1],i.content,n.password||"",n);if(2==l[0]&&"undefined"!=typeof decrypt_std76)return decrypt_std76(l[1],i.content,n.password||"",n);throw new Error("File is password-protected")}function jf(e,t){e&&!e.SSF&&(e.SSF=Ke(G)),e&&e.SSF&&(ye(),Te(e.SSF),t.revssf=Me(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,ic?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="xml",a=co.indexOf(t.bookType)>-1,n={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Hf(t=t||{});var s=_t(),i="",o=0;if(t.cellXfs=[],fc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),kt(s,i="docProps/core.xml",wn(e.Props,t)),n.coreprops.push(i),un(t.rels,2,i,cn.CORE_PROPS),i="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var c=[],l=0;l<e.SheetNames.length;++l)2!=(e.Workbook.Sheets[l]||{}).Hidden&&c.push(e.SheetNames[l]);e.Props.SheetNames=c}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,kt(s,i,kn(e.Props)),n.extprops.push(i),un(t.rels,3,i,cn.EXT_PROPS),e.Custprops!==e.Props&&Fe(e.Custprops||{}).length>0&&(kt(s,i="docProps/custom.xml",Sn(e.Custprops)),n.custprops.push(i),un(t.rels,4,i,cn.CUST_PROPS));var f,h=["SheetJ5"];for(t.tcid=0,o=1;o<=e.SheetNames.length;++o){var u={"!id":{}},d=e.Sheets[e.SheetNames[o-1]];if((d||{})["!type"],kt(s,i="xl/worksheets/sheet"+o+"."+r,xc(o-1,t,e,u)),n.sheets.push(i),un(t.wbrels,-1,"worksheets/sheet"+o+"."+r,cn.WS[0]),d){var p=d["!comments"],m=!1,v="";if(p&&p.length>0){var g=!1;p.forEach((function(e){e[1].forEach((function(e){1==e.T&&(g=!0)}))})),g&&(kt(s,v="xl/threadedComments/threadedComment"+o+".xml",ro(p,h,t)),n.threadedcomments.push(v),un(u,-1,"../threadedComments/threadedComment"+o+".xml",cn.TCMNT)),kt(s,v="xl/comments"+o+"."+r,to(p)),n.comments.push(v),un(u,-1,"../comments"+o+"."+r,cn.CMNT),m=!0}d["!legacy"]&&m&&kt(s,"xl/drawings/vmlDrawing"+o+".vml",Qi(o,d["!comments"])),delete d["!comments"],delete d["!legacy"]}u["!id"].rId1&&kt(s,ln(i),hn(u))}return null!=t.Strings&&t.Strings.length>0&&(kt(s,i="xl/sharedStrings."+r,function(e,t){if(!t.bookSST)return"";var r=[At];r[r.length]=sr("sst",null,{xmlns:fr[0],count:e.Count,uniqueCount:e.Unique});for(var a=0;a!=e.length;++a)if(null!=e[a]){var n=e[a],s="<si>";n.r?s+=n.r:(s+="<t",n.t||(n.t=""),"string"!=typeof n.t&&(n.t=String(n.t)),n.t.match(ci)&&(s+=' xml:space="preserve"'),s+=">"+Wt(n.t)+"</t>"),s+="</si>",r[r.length]=s}return r.length>2&&(r[r.length]="</sst>",r[1]=r[1].replace("/>",">")),r.join("")}(t.Strings,t)),n.strs.push(i),un(t.wbrels,-1,"sharedStrings."+r,cn.SST)),kt(s,i="xl/workbook."+r,function(e){var t=[At];t[t.length]=sr("workbook",null,{xmlns:fr[0],"xmlns:r":lr.r});var r=e.Workbook&&(e.Workbook.Names||[]).length>0,a={codeName:"ThisWorkbook"};e.Workbook&&e.Workbook.WBProps&&(Uc.forEach((function(t){null!=e.Workbook.WBProps[t[0]]&&e.Workbook.WBProps[t[0]]!=t[1]&&(a[t[0]]=e.Workbook.WBProps[t[0]])})),e.Workbook.WBProps.CodeName&&(a.codeName=e.Workbook.WBProps.CodeName,delete a.CodeName)),t[t.length]=sr("workbookPr",null,a);var n=e.Workbook&&e.Workbook.Sheets||[],s=0;if(n&&n[0]&&n[0].Hidden){for(t[t.length]="<bookViews>",s=0;s!=e.SheetNames.length&&n[s]&&n[s].Hidden;++s);s==e.SheetNames.length&&(s=0),t[t.length]='<workbookView firstSheet="'+s+'" activeTab="'+s+'"/>',t[t.length]="</bookViews>"}for(t[t.length]="<sheets>",s=0;s!=e.SheetNames.length;++s){var i={name:Wt(e.SheetNames[s].slice(0,31))};if(i.sheetId=""+(s+1),i["r:id"]="rId"+(s+1),n[s])switch(n[s].Hidden){case 1:i.state="hidden";break;case 2:i.state="veryHidden"}t[t.length]=sr("sheet",null,i)}return t[t.length]="</sheets>",r&&(t[t.length]="<definedNames>",e.Workbook&&e.Workbook.Names&&e.Workbook.Names.forEach((function(e){var r={name:e.Name};e.Comment&&(r.comment=e.Comment),null!=e.Sheet&&(r.localSheetId=""+e.Sheet),e.Hidden&&(r.hidden="1"),e.Ref&&(t[t.length]=sr("definedName",Wt(e.Ref),r))})),t[t.length]="</definedNames>"),t.length>2&&(t[t.length]="</workbook>",t[1]=t[1].replace("/>",">")),t.join("")}(e)),n.workbooks.push(i),un(t.rels,1,i,cn.WB),kt(s,i="xl/theme/theme1.xml",Ji(e.Themes,t)),n.themes.push(i),un(t.wbrels,-1,"theme/theme1.xml",cn.THEME),kt(s,i="xl/styles."+r,function(e,t){var r,a=[At,sr("styleSheet",null,{xmlns:fr[0],"xmlns:vt":lr.vt})];return e.SSF&&null!=(r=function(e){var t=["<numFmts>"];return[[5,8],[23,26],[41,44],[50,392]].forEach((function(r){for(var a=r[0];a<=r[1];++a)null!=e[a]&&(t[t.length]=sr("numFmt",null,{numFmtId:a,formatCode:Wt(e[a])}))})),1===t.length?"":(t[t.length]="</numFmts>",t[0]=sr("numFmts",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(e.SSF))&&(a[a.length]=r),a[a.length]='<fonts count="1"><font><sz val="12"/><color theme="1"/><name val="Calibri"/><family val="2"/><scheme val="minor"/></font></fonts>',a[a.length]='<fills count="2"><fill><patternFill patternType="none"/></fill><fill><patternFill patternType="gray125"/></fill></fills>',a[a.length]='<borders count="1"><border><left/><right/><top/><bottom/><diagonal/></border></borders>',a[a.length]='<cellStyleXfs count="1"><xf numFmtId="0" fontId="0" fillId="0" borderId="0"/></cellStyleXfs>',(r=function(e){var t=[];return t[t.length]=sr("cellXfs",null),e.forEach((function(e){t[t.length]=sr("xf",null,e)})),t[t.length]="</cellXfs>",2===t.length?"":(t[0]=sr("cellXfs",null,{count:t.length-2}).replace("/>",">"),t.join(""))}(t.cellXfs))&&(a[a.length]=r),a[a.length]='<cellStyles count="1"><cellStyle name="Normal" xfId="0" builtinId="0"/></cellStyles>',a[a.length]='<dxfs count="0"/>',a[a.length]='<tableStyles count="0" defaultTableStyle="TableStyleMedium9" defaultPivotStyle="PivotStyleMedium4"/>',a.length>2&&(a[a.length]="</styleSheet>",a[1]=a[1].replace("/>",">")),a.join("")}(e,t)),n.styles.push(i),un(t.wbrels,-1,"styles."+r,cn.STY),e.vbaraw&&a&&(kt(s,i="xl/vbaProject.bin",e.vbaraw),n.vba.push(i),un(t.wbrels,-1,"vbaProject.bin",cn.VBA)),kt(s,i="xl/metadata."+r,((f=[At]).push('<metadata xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:xlrd="http://schemas.microsoft.com/office/spreadsheetml/2017/richdata" xmlns:xda="http://schemas.microsoft.com/office/spreadsheetml/2017/dynamicarray">\n  <metadataTypes count="1">\n    <metadataType name="XLDAPR" minSupportedVersion="120000" copy="1" pasteAll="1" pasteValues="1" merge="1" splitFirst="1" rowColShift="1" clearFormats="1" clearComments="1" assign="1" coerce="1" cellMeta="1"/>\n  </metadataTypes>\n  <futureMetadata name="XLDAPR" count="1">\n    <bk>\n      <extLst>\n        <ext uri="{bdbb8cdc-fa1e-496e-a857-3c3f30c029c3}">\n          <xda:dynamicArrayProperties fDynamic="1" fCollapsed="0"/>\n        </ext>\n      </extLst>\n    </bk>\n  </futureMetadata>\n  <cellMetadata count="1">\n    <bk>\n      <rc t="1" v="0"/>\n    </bk>\n  </cellMetadata>\n</metadata>'),f.join(""))),n.metadata.push(i),un(t.wbrels,-1,"metadata."+r,cn.XLMETA),h.length>1&&(kt(s,i="xl/persons/person.xml",ao(h)),n.people.push(i),un(t.wbrels,-1,"persons/person.xml",cn.PEOPLE)),kt(s,"[Content_Types].xml",on(n,t)),kt(s,"_rels/.rels",hn(t.rels)),kt(s,"xl/_rels/workbook.xml.rels",hn(t.wbrels)),delete t.revssf,delete t.ssf,s}function Kf(e,t){var r="";switch((t||{}).type||"base64"){case"buffer":case"array":return[e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7]];case"base64":r=E(e.slice(0,12));break;case"binary":r=e;break;default:throw new Error("Unrecognized type "+(t&&t.type||"undefined"))}return[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3),r.charCodeAt(4),r.charCodeAt(5),r.charCodeAt(6),r.charCodeAt(7)]}function Yf(e,t){var r=0;e:for(;r<e.length;)switch(e.charCodeAt(r)){case 10:case 13:case 32:++r;break;case 60:return cl(e.slice(r),t);default:break e}return Qs.to_workbook(e,t)}function Zf(e,t,r,a){return a?(r.type="string",Qs.to_workbook(e,r)):Qs.to_workbook(t,r)}function Jf(e,t){h();var r=t||{};if(r.codepage&&void 0===a&&console.error("Codepage tables are not loaded.  Non-ASCII characters may not give expected results"),"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer)return Jf(new Uint8Array(e),((r=Ke(r)).type="array",r));"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&!r.type&&(r.type="undefined"!=typeof Deno?"buffer":"array");var n,s=e,i=!1;if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),sc={},r.dateNF&&(sc.dateNF=r.dateNF),r.type||(r.type=k&&Buffer.isBuffer(e)?"buffer":"base64"),"file"==r.type&&(r.type=k?"buffer":"binary",s=function(e){if(void 0!==Ae)return Ae.readFileSync(e);if("undefined"!=typeof Deno)return Deno.readFileSync(e);if("undefined"!=typeof $&&"undefined"!=typeof File&&"undefined"!=typeof Folder)try{var t=File(e);t.open("r"),t.encoding="binary";var r=t.read();return t.close(),r}catch(e){if(!e.message||-1==e.message.indexOf("onstruct"))throw e}throw new Error("Cannot access file "+e)}(e),"undefined"==typeof Uint8Array||k||(r.type="array")),"string"==r.type&&(i=!0,r.type="binary",r.codepage=65001,s=function(e){return e.match(/[^\x00-\x7F]/)?Jt(e):e}(e)),"array"==r.type&&"undefined"!=typeof Uint8Array&&e instanceof Uint8Array&&"undefined"!=typeof ArrayBuffer){var o=new ArrayBuffer(3),c=new Uint8Array(o);if(c.foo="bar",!c.foo)return(r=Ke(r)).type="array",Jf(R(s),r)}switch((n=Kf(s,r))[0]){case 208:if(207===n[1]&&17===n[2]&&224===n[3]&&161===n[4]&&177===n[5]&&26===n[6]&&225===n[7])return function(e,t){return Ie.find(e,"EncryptedPackage")?Xf(e,t):wl(e,t)}(Ie.read(s,r),r);break;case 9:if(n[1]<=8)return wl(s,r);break;case 60:return cl(s,r);case 73:if(73===n[1]&&42===n[2]&&0===n[3])throw new Error("TIFF Image File is not a spreadsheet");if(68===n[1])return function(e,t){var r=t||{},a=!!r.WTF;r.WTF=!0;try{var n=Zs.to_workbook(e,r);return r.WTF=a,n}catch(n){if(r.WTF=a,-1==n.message.indexOf("SYLK bad record ID")&&a)throw n;return Qs.to_workbook(e,t)}}(s,r);break;case 84:if(65===n[1]&&66===n[2]&&76===n[3])return Js.to_workbook(s,r);break;case 80:return 75===n[1]&&n[2]<9&&n[3]<9?function(e,t){var r=e,a=t||{};return a.type||(a.type=k&&Buffer.isBuffer(e)?"buffer":"base64"),Gf(St(r,a),a)}(s,r):Zf(e,s,r,i);case 239:return 60===n[3]?cl(s,r):Zf(e,s,r,i);case 255:if(254===n[1])return function(e,t){var r=e;return"base64"==t.type&&(r=E(r)),"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer&&(r=new Uint8Array(e)),r=void 0!==a?a.utils.decode(1200,r.slice(2),"str"):k&&Buffer.isBuffer(e)?e.slice(2).toString("utf16le"):"undefined"!=typeof Uint8Array&&r instanceof Uint8Array?"undefined"!=typeof TextDecoder?new TextDecoder("utf-16le").decode(r.slice(2)):function(e){for(var t=[],r=0;r<e.length>>1;++r)t[r]=String.fromCharCode(e[2*r]+(e[2*r+1]<<8));return t.join("")}(r.slice(2)):d(r.slice(2)),t.type="binary",Yf(r,t)}(s,r);if(0===n[1]&&2===n[2]&&0===n[3])return ei.to_workbook(s,r);break;case 0:if(0===n[1]){if(n[2]>=2&&0===n[3])return ei.to_workbook(s,r);if(0===n[2]&&(8===n[3]||9===n[3]))return ei.to_workbook(s,r)}break;case 3:case 131:case 139:case 140:return Ys.to_workbook(s,r);case 123:if(92===n[1]&&114===n[2]&&116===n[3])return function(e,t){var r=pa(function(e,t){switch(t.type){case"base64":return Ti(E(e),t);case"binary":return Ti(e,t);case"buffer":return Ti(k&&Buffer.isBuffer(e)?e.toString("binary"):O(e),t);case"array":return Ti(je(e),t)}throw new Error("Unrecognized type "+t.type)}(e,t),t);return r.bookType="rtf",r}(s,r);break;case 10:case 13:case 32:return function(e,t){var r="",a=Kf(e,t);switch(t.type){case"base64":r=E(e);break;case"binary":r=e;break;case"buffer":r=e.toString("binary");break;case"array":r=je(e);break;default:throw new Error("Unrecognized type "+t.type)}return 239==a[0]&&187==a[1]&&191==a[2]&&(r=Zt(r)),t.type="binary",Yf(r,t)}(s,r);case 137:if(80===n[1]&&78===n[2]&&71===n[3])throw new Error("PNG Image File is not a spreadsheet");break;case 8:if(231===n[1])throw new Error("Unsupported Multiplan 1.x file!");break;case 12:if(236===n[1])throw new Error("Unsupported Multiplan 2.x file!");if(237===n[1])throw new Error("Unsupported Multiplan 3.x file!")}return Ks.indexOf(n[0])>-1&&n[2]<=12&&n[3]<=31?Ys.to_workbook(s,r):Zf(e,s,r,i)}function qf(e,t){var r=t||{};return r.type="file",Jf(e,r)}function Qf(e,t){switch(t.type){case"base64":case"binary":break;case"buffer":case"array":t.type="";break;case"file":return Ne(t.file,Ie.write(e,{type:k?"buffer":""}));case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");default:throw new Error("Unrecognized type "+t.type)}return Ie.write(e,t)}function eh(e,t){switch(t.bookType){case"ods":return Yl(e,t);case"numbers":return function(e,t){if(!t||!t.numbers)throw new Error("Must pass a `numbers` option -- check the README");var r=Ie.read(t.numbers,{type:"base64"}),a=function(e){var t={},r=[];return e.FileIndex.map((function(t,r){return[t,e.FullPaths[r]]})).forEach((function(e){var a=e[0],n=e[1];2==a.type&&a.name.match(/\.iwa/)&&0==a.content[0]&&hf(pf(a.content)).forEach((function(e){r.push(e.id),t[e.id]={deps:[],location:n,type:of(e.messages[0].meta[1][0].data)}}))})),e.FileIndex.forEach((function(e){e.name.match(/\.iwa/)&&0==e.content[0]&&hf(pf(e.content)).forEach((function(e){e.messages.forEach((function(r){[5,6].forEach((function(a){r.meta[a]&&r.meta[a].forEach((function(r){t[e.id].deps.push(of(r.data))}))}))}))}))})),t}(r),n=Pf(r,a,1);if(null==n)throw"Could not find message ".concat(1," in Numbers template");var s=ff(cf(n.messages[0].data)[1],yf);if(s.length>1)throw new Error("Template NUMBERS file must have exactly one sheet");return e.SheetNames.forEach((function(t,i){i>=1&&(function(e,t,r){var a=-1,n=-1,s={};Df(e,t,1,(function(r,i){var o=cf(r.messages[0].data);a=yf(cf(r.messages[0].data)[1][0].data),n=Rf({deps:[1],location:t[a].location,type:2},t),s[a]=n,kf(r,n),o[1].push({type:2,data:Ef(n)});var c=Pf(e,t,a);c.id=n,t[1].location==t[n].location?i.push(c):Df(e,t,n,(function(e,t){return t.push(c)})),r.messages[0].data=lf(o)}));var i=-1;Df(e,t,n,(function(r,a){for(var o=cf(r.messages[0].data),c=3;c<=69;++c)delete o[c];var l=ff(o[2],yf);l.forEach((function(e){return _f(r,e)})),i=Rf({deps:[n],location:t[l[0]].location,type:t[l[0]].type},t),kf(r,i),s[l[0]]=i,o[2]=[{type:2,data:Ef(i)}];var f=Pf(e,t,l[0]);f.id=i,t[l[0]].location==t[n].location?a.push(f):(Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Uf(r,t,n,i),e.messages[0].data=lf(r)})),Df(e,t,i,(function(e,t){return t.push(f)}))),r.messages[0].data=lf(o)}));var o=-1;Df(e,t,i,(function(r,a){for(var n=cf(r.messages[0].data),c=cf(n[1][0].data),l=3;l<=69;++l)delete c[l];var f=yf(c[2][0].data);c[2][0].data=Ef(s[f]),n[1][0].data=lf(c);var h=yf(n[2][0].data);_f(r,h),kf(r,o=Rf({deps:[i],location:t[h].location,type:t[h].type},t)),s[h]=o,n[2][0].data=Ef(o);var u=Pf(e,t,h);u.id=o,t[i].location==t[o].location?a.push(u):Df(e,t,o,(function(e,t){return t.push(u)})),r.messages[0].data=lf(n)})),Df(e,t,o,(function(a,n){var i,c,l=cf(a.messages[0].data),f=ql(l[1][0].data).replace(/-[A-Z0-9]*/,"-".concat(("0000"+r.toString(16)).slice(-4)));if(l[1][0].data=Ql(f),[12,13,29,31,32,33,39,44,47,81,82,84].forEach((function(e){return delete l[e]})),l[45]){var h=yf(cf(l[45][0].data)[1][0].data);_f(a,h),delete l[45]}l[70]&&(null==(i=cf(l[70][0].data)[2])||i.forEach((function(e){var t=cf(e.data);[2,3].map((function(e){return t[e][0]})).forEach((function(e){var t=cf(e.data);if(t[8]){var r=yf(t[8][0].data);_f(a,r)}}))})),delete l[70]),[46,30,34,35,36,38,48,49,60,61,62,63,64,71,72,73,74,75,85,86,87,88,89].forEach((function(e){if(l[e]){var t=yf(l[e][0].data);delete l[e],_f(a,t)}}));var u=cf(l[4][0].data);[2,4,5,6,11,12,13,15,16,17,18,19,20,21,22].forEach((function(r){var i;if(null==(i=u[r])?void 0:i[0]){var c=yf(u[r][0].data),l=Rf({deps:[o],location:t[c].location,type:t[c].type},t);_f(a,c),kf(a,l),s[c]=l;var f=Pf(e,t,c);if(f.id=l,t[c].location==t[o].location)n.push(f);else{t[l].location=t[c].location.replace(c.toString(),l.toString()),t[l].location==t[c].location&&(t[l].location=t[l].location.replace(/\.iwa/,"-".concat(l,".iwa"))),Ie.utils.cfb_add(e,t[l].location,mf(uf([f])));var h=t[l].location.replace(/^Root Entry\//,"").replace(/^Index\//,"").replace(/\.iwa$/,"");Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Mf(r,l,h),Uf(r,t,o,l),e.messages[0].data=lf(r)}))}u[r][0].data=Ef(l)}}));var d=cf(u[1][0].data);null==(c=d[2])||c.forEach((function(r){var i=yf(r.data),c=Rf({deps:[o],location:t[i].location,type:t[i].type},t);_f(a,i),kf(a,c),s[i]=c;var l=Pf(e,t,i);if(l.id=c,t[i].location==t[o].location)n.push(l);else{t[c].location=t[i].location.replace(i.toString(),c.toString()),t[c].location==t[i].location&&(t[c].location=t[c].location.replace(/\.iwa/,"-".concat(c,".iwa"))),Ie.utils.cfb_add(e,t[c].location,mf(uf([l])));var f=t[c].location.replace(/^Root Entry\//,"").replace(/^Index\//,"").replace(/\.iwa$/,"");Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Mf(r,c,f),Uf(r,t,o,c),e.messages[0].data=lf(r)}))}r.data=Ef(c)})),u[1][0].data=lf(d);var p=cf(u[3][0].data);p[1].forEach((function(r){var n=cf(r.data),i=yf(n[2][0].data),c=s[i];if(!s[i]){c=Rf({deps:[o],location:"",type:t[i].type},t),t[c].location="Root Entry/Index/Tables/Tile-".concat(c,".iwa"),s[i]=c;var l=Pf(e,t,i);l.id=c,_f(a,i),kf(a,c),Ie.utils.cfb_add(e,"/Index/Tables/Tile-".concat(c,".iwa"),mf(uf([l]))),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);r[3].push({type:2,data:lf([[],[{type:0,data:af(c)}],[{type:2,data:Ql("Tables/Tile")}],[{type:2,data:Ql("Tables/Tile-".concat(c))}],[{type:2,data:new Uint8Array([2,0,0])}],[{type:2,data:new Uint8Array([2,0,0])}],[],[],[],[],[{type:0,data:af(0)}],[],[{type:0,data:af(0)}]])}),r[1]=[{type:0,data:af(Math.max(c+1,of(r[1][0].data)))}],Uf(r,t,o,c),e.messages[0].data=lf(r)}))}n[2][0].data=Ef(c),r.data=lf(n)})),u[3][0].data=lf(p),l[4][0].data=lf(u),a.messages[0].data=lf(l)}))}(r,a,i+1),n=Pf(r,a,1),s=ff(cf(n.messages[0].data)[1],yf)),function(e,t,r,a,n,s){var i=[];Df(e,t,s,(function(e){var t=cf(e.messages[0].data);t[1]=[{type:2,data:Ql(a)}],i=ff(t[2],yf),e.messages[0].data=lf(t)}));var o=yf(cf(Pf(e,t,i[0]).messages[0].data)[2][0].data);Df(e,t,o,(function(a,n){return function(e,t,r,a,n,s){if(!r["!ref"])throw new Error("Cannot export empty sheet to NUMBERS");var i=ca(r["!ref"]);i.s.r=i.s.c=0;var o=!1;i.e.c>999&&(o=!0,i.e.c=999),i.e.r>999999&&(o=!0,i.e.r=999999),o&&console.error("Truncating to ".concat(la(i)));var c=[];if(r["!data"])c=r["!data"];else{for(var l=[],f=0;f<=i.e.c;++f)l[f]=sa(f);for(var h=0;h<=i.e.r;++h){c[h]=[];var u=""+(h+1);for(f=0;f<=i.e.c;++f){var d=r[l[f]+u];d&&(c[h][f]=d)}}}var p={cmnt:[{a:"~54ee77S~",t:"... the people who are crazy enough to think they can change the world, are the ones who do."}],ferr:[],fmla:[],nfmt:[],ofmt:[],rsst:[{v:"~54ee77S~",l:"https://sheetjs.com/"}],sst:["~Sh33tJ5~"]},m=cf(a.messages[0].data);m[6][0].data=af(i.e.r+1),m[7][0].data=af(i.e.c+1),delete m[46];var v=cf(m[4][0].data),g=yf(cf(v[1][0].data)[2][0].data);Df(e,t,g,(function(e,t){var r,a=cf(e.messages[0].data);if(null==(r=null==a?void 0:a[2])?void 0:r[0])for(var n=0;n<c.length;++n){var s=cf(a[2][0].data);s[1][0].data=af(n),s[4][0].data=af(c[n].length),a[2][n]={type:a[2][0].type,data:lf(s)}}e.messages[0].data=lf(a)}));var b=yf(v[2][0].data);Df(e,t,b,(function(e,t){for(var r=cf(e.messages[0].data),a=0;a<=i.e.c;++a){var n=cf(r[2][0].data);n[1][0].data=af(a),n[4][0].data=af(i.e.r+1),r[2][a]={type:r[2][0].type,data:lf(n)}}e.messages[0].data=lf(r)}));var w=cf(v[9][0].data);w[1]=[];var T=cf(v[3][0].data),y=256;T[2]=[{type:0,data:af(y)}];var E,k=yf(cf(T[1][0].data)[2][0].data),_=(null==(E=cf(Pf(e,t,2).messages[0].data)[3].filter((function(e){return of(cf(e.data)[1][0].data)==k})))?void 0:E.length)?of(cf(E[0].data)[12][0].data):0;Ie.utils.cfb_del(e,t[k].location),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);r[3]=r[3].filter((function(e){return of(cf(e.data)[1][0].data)!=k})),function(e,t,r,a){var n=t[r].location.replace(/^Root Entry\//,"").replace(/^Index\//,"").replace(/\.iwa$/,""),s=e[3].findIndex((function(e){var t,r,a=cf(e.data);return(null==(t=a[3])?void 0:t[0])?ql(a[3][0].data)==n:!(!(null==(r=a[2])?void 0:r[0])||ql(a[2][0].data)!=n)})),i=cf(e[3][s].data);i[6]||(i[6]=[]),i[6]=i[6].filter((function(e){return of(cf(e.data)[1][0].data)!=a})),e[3][s].data=lf(i)}(r,t,s,k),e.messages[0].data=lf(r)})),_f(a,k),T[1]=[];for(var S=Math.ceil((i.e.r+1)/y),x=0;x<S;++x){var A=Rf({deps:[],location:"",type:6002},t);t[A].location="Root Entry/Index/Tables/Tile-".concat(A,".iwa");for(var C=[[],[{type:0,data:af(0)}],[{type:0,data:af(Math.min(i.e.r+1,(x+1)*y))}],[{type:0,data:af(0)}],[{type:0,data:af(Math.min((x+1)*y,i.e.r+1)-x*y)}],[],[{type:0,data:af(5)}],[{type:0,data:af(1)}],[{type:0,data:af(Bf?1:0)}]],I=x*y;I<=Math.min(i.e.r,(x+1)*y-1);++I){var O=If(c[I],p,Bf);O[1][0].data=af(I-x*y),C[5].push({data:lf(O),type:2})}T[1].push({type:2,data:lf([[],[{type:0,data:af(x)}],[{type:2,data:Ef(A)}]])});var R=mf(uf([{id:A,messages:[Of(6002,lf(C))]}]));Ie.utils.cfb_add(e,"/Index/Tables/Tile-".concat(A,".iwa"),R),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);r[3].push({type:2,data:lf([[],[{type:0,data:af(A)}],[{type:2,data:Ql("Tables/Tile")}],[{type:2,data:Ql("Tables/Tile-".concat(A))}],[{type:2,data:new Uint8Array([2,0,0])}],[{type:2,data:new Uint8Array([2,0,0])}],[],[],[],[],[{type:0,data:af(0)}],[],[{type:0,data:af(_)}]])}),r[1]=[{type:0,data:af(Math.max(A+1,of(r[1][0].data)))}],Uf(r,t,s,A),e.messages[0].data=lf(r)})),kf(a,A),w[1].push({type:2,data:lf([[],[{type:0,data:af(x*y)}],[{type:0,data:af(x)}]])})}if(v[3][0].data=lf(T),v[9][0].data=lf(w),v[10]=[{type:2,data:new Uint8Array([])}],r["!merges"]){var N=Rf({type:6144,deps:[s],location:t[s].location},t);n.push({id:N,messages:[Of(6144,lf([[],r["!merges"].map((function(e){return{type:2,data:lf([[],[{type:2,data:lf([[],[{type:5,data:new Uint8Array(new Uint16Array([e.s.r,e.s.c]).buffer)}]])}],[{type:2,data:lf([[],[{type:5,data:new Uint8Array(new Uint16Array([e.e.r-e.s.r+1,e.e.c-e.s.c+1]).buffer)}]])}]])}}))]))]}),v[13]=[{type:2,data:Ef(N)}],Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Uf(r,t,s,N),e.messages[0].data=lf(r)})),kf(a,N)}else delete v[13];var F=yf(v[4][0].data);Df(e,t,F,(function(e){var t=cf(e.messages[0].data);t[3]=[],p.sst.forEach((function(e,r){0!=r&&t[3].push({type:2,data:lf([[],[{type:0,data:af(r)}],[{type:0,data:af(1)}],[{type:2,data:Ql(e)}]])})})),e.messages[0].data=lf(t)}));var D=yf(v[17][0].data);if(Df(e,t,D,(function(r){var a=cf(r.messages[0].data);a[3]=[];var n=[904980,903835,903815,903845];p.rsst.forEach((function(s,i){if(0!=i){var o=[[],[{type:0,data:new Uint8Array([5])}],[],[{type:2,data:Ql(s.v)}]];o[10]=[{type:0,data:new Uint8Array([1])}],o[19]=[{type:2,data:new Uint8Array([10,6,8,0,18,2,101,110])}],o[5]=[{type:2,data:new Uint8Array([10,8,8,0,18,4,8,155,149,55])}],o[2]=[{type:2,data:new Uint8Array([8,148,158,55])}],o[6]=[{type:2,data:new Uint8Array([10,6,8,0,16,0,24,0])}],o[7]=[{type:2,data:new Uint8Array([10,8,8,0,18,4,8,135,149,55])}],o[8]=[{type:2,data:new Uint8Array([10,8,8,0,18,4,8,165,149,55])}],o[14]=[{type:2,data:new Uint8Array([10,6,8,0,16,0,24,0])}],o[24]=[{type:2,data:new Uint8Array([10,6,8,0,16,0,24,0])}];var c=Rf({deps:[],location:"",type:2001},t),l=[];if(s.l){var f=Lf(e,2032,[[],[],[{type:2,data:Ql(s.l)}]],"/Index/Tables/DataList",t);o[11]=[];var h=[[],[]];h[1]||(h[1]=[]),h[1].push({type:2,data:lf([[],[{type:0,data:af(0)}],[{type:2,data:Ef(f)}]])}),o[11][0]={type:2,data:lf(h)},l.push(f)}Lf(e,2001,o,"/Index/Tables/DataList",t,c),Df(e,t,c,(function(e){n.forEach((function(t){return kf(e,t)})),l.forEach((function(t){return kf(e,t)}))}));var u=Lf(e,6218,[[],[{type:2,data:Ef(c)}],[],[{type:2,data:new Uint8Array([13,255,255,255,0,18,10,16,255,255,1,24,255,255,255,255,7])}]],"/Index/Tables/DataList",t);Df(e,t,u,(function(e){return kf(e,c)})),a[3].push({type:2,data:lf([[],[{type:0,data:af(i)}],[{type:0,data:af(1)}],[],[],[],[],[],[],[{type:2,data:Ef(u)}]])}),kf(r,u),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Uf(r,t,D,u),Uf(r,t,u,c),Uf(r,t,c,l),Uf(r,t,c,n),e.messages[0].data=lf(r)}))}})),r.messages[0].data=lf(a)})),p.cmnt.length>1){var P=yf(v[19][0].data),M={},L=0;Df(e,t,P,(function(r){var a=cf(r.messages[0].data);a[3]=[],p.cmnt.forEach((function(n,s){if(0!=s){var i=[];n.replies&&n.replies.forEach((function(r){M[r.a||""]||(M[r.a||""]=Lf(e,212,[[],[{type:2,data:Ql(r.a||"")}],[{type:2,data:Ff(++L)}],[],[{type:0,data:af(0)}]],"/Index/Tables/DataList",t));var a=M[r.a||""],n=Lf(e,3056,[[],[{type:2,data:Ql(r.t||"")}],[{type:2,data:lf([[],[{type:1,data:new Uint8Array([0,0,0,128,116,109,182,65])}]])}],[{type:2,data:Ef(a)}]],"/Index/Tables/DataList",t);Df(e,t,n,(function(e){return kf(e,a)})),i.push(n),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Uf(r,t,n,a),e.messages[0].data=lf(r)}))})),M[n.a||""]||(M[n.a||""]=Lf(e,212,[[],[{type:2,data:Ql(n.a||"")}],[{type:2,data:Ff(++L)}],[],[{type:0,data:af(0)}]],"/Index/Tables/DataList",t));var o=M[n.a||""],c=Lf(e,3056,[[],[{type:2,data:Ql(n.t||"")}],[{type:2,data:lf([[],[{type:1,data:new Uint8Array([0,0,0,128,116,109,182,65])}]])}],[{type:2,data:Ef(o)}],i.map((function(e){return{type:2,data:Ef(e)}})),[{type:2,data:lf([[],[{type:0,data:af(s)}],[{type:0,data:af(0)}]])}]],"/Index/Tables/DataList",t);Df(e,t,c,(function(e){kf(e,o),i.forEach((function(t){return kf(e,t)}))})),a[3].push({type:2,data:lf([[],[{type:0,data:af(s)}],[{type:0,data:af(1)}],[],[],[],[],[],[],[],[{type:2,data:Ef(c)}]])}),kf(r,c),Df(e,t,2,(function(e){var r=cf(e.messages[0].data);Uf(r,t,P,c),Uf(r,t,c,o),i.length&&Uf(r,t,c,i),e.messages[0].data=lf(r)}))}})),a[2][0].data=af(p.cmnt.length+1),r.messages[0].data=lf(a)}))}m[4][0].data=lf(v),a.messages[0].data=lf(m)}(e,t,r,a,n,o)}))}(r,a,e.Sheets[t],t,0,s[i])})),r}(e,t);case"xlsb":return function(e,t){e&&!e.SSF&&(e.SSF=Ke(G)),e&&e.SSF&&(ye(),Te(e.SSF),t.revssf=Me(e.SSF),t.revssf[e.SSF[65535]]=0,t.ssf=e.SSF),t.rels={},t.wbrels={},t.Strings=[],t.Strings.Count=0,t.Strings.Unique=0,ic?t.revStrings=new Map:(t.revStrings={},t.revStrings.foo=[],delete t.revStrings.foo);var r="bin",a={workbooks:[],sheets:[],charts:[],dialogs:[],macros:[],rels:[],strs:[],comments:[],threadedcomments:[],links:[],coreprops:[],extprops:[],custprops:[],themes:[],styles:[],calcchains:[],vba:[],drawings:[],metadata:[],people:[],TODO:[],xmlns:""};Hf(t=t||{});var n=_t(),s="",i=0;if(t.cellXfs=[],fc(t.cellXfs,{},{revssf:{General:0}}),e.Props||(e.Props={}),kt(n,s="docProps/core.xml",wn(e.Props,t)),a.coreprops.push(s),un(t.rels,2,s,cn.CORE_PROPS),s="docProps/app.xml",e.Props&&e.Props.SheetNames);else if(e.Workbook&&e.Workbook.Sheets){for(var o=[],c=0;c<e.SheetNames.length;++c)2!=(e.Workbook.Sheets[c]||{}).Hidden&&o.push(e.SheetNames[c]);e.Props.SheetNames=o}else e.Props.SheetNames=e.SheetNames;e.Props.Worksheets=e.Props.SheetNames.length,kt(n,s,kn(e.Props)),a.extprops.push(s),un(t.rels,3,s,cn.EXT_PROPS),e.Custprops!==e.Props&&Fe(e.Custprops||{}).length>0&&(kt(n,s="docProps/custom.xml",Sn(e.Custprops)),a.custprops.push(s),un(t.rels,4,s,cn.CUST_PROPS));var l,f,h,u=["SheetJ5"];for(t.tcid=0,i=1;i<=e.SheetNames.length;++i){var d={"!id":{}},p=e.Sheets[e.SheetNames[i-1]];if((p||{})["!type"],kt(n,s="xl/worksheets/sheet"+i+"."+r,Lc(i-1,t,e,d)),a.sheets.push(s),un(t.wbrels,-1,"worksheets/sheet"+i+"."+r,cn.WS[0]),p){var m=p["!comments"],v=!1,g="";if(m&&m.length>0){var b=!1;m.forEach((function(e){e[1].forEach((function(e){1==e.T&&(b=!0)}))})),b&&(kt(n,g="xl/threadedComments/threadedComment"+i+".xml",ro(m,u,t)),a.threadedcomments.push(g),un(d,-1,"../threadedComments/threadedComment"+i+".xml",cn.TCMNT)),kt(n,g="xl/comments"+i+"."+r,io(m)),a.comments.push(g),un(d,-1,"../comments"+i+"."+r,cn.CMNT),v=!0}p["!legacy"]&&v&&kt(n,"xl/drawings/vmlDrawing"+i+".vml",Qi(i,p["!comments"])),delete p["!comments"],delete p["!legacy"]}d["!id"].rId1&&kt(n,ln(s),hn(d))}return null!=t.Strings&&t.Strings.length>0&&(kt(n,s="xl/sharedStrings."+r,function(e){var t=Zr();Jr(t,159,function(e,t){return t||(t=Kr(8)),t.write_shift(4,e.Count),t.write_shift(4,e.Unique),t}(e));for(var r=0;r<e.length;++r)Jr(t,19,li(e[r]));return Jr(t,160),t.end()}(t.Strings)),a.strs.push(s),un(t.wbrels,-1,"sharedStrings."+r,cn.SST)),kt(n,s="xl/workbook."+r,Zc(e)),a.workbooks.push(s),un(t.rels,1,s,cn.WB),kt(n,s="xl/theme/theme1.xml",Ji(e.Themes,t)),a.themes.push(s),un(t.wbrels,-1,"theme/theme1.xml",cn.THEME),kt(n,s="xl/styles."+r,Ki(e,t)),a.styles.push(s),un(t.wbrels,-1,"styles."+r,cn.STY),e.vbaraw&&(kt(n,s="xl/vbaProject.bin",e.vbaraw),a.vba.push(s),un(t.wbrels,-1,"vbaProject.bin",cn.VBA)),kt(n,s="xl/metadata."+r,(Jr(h=Zr(),332),Jr(h,334,ga(1)),Jr(h,335,((f=Kr(12+2*(l={name:"XLDAPR",version:12e4,flags:3496657072}).name.length)).write_shift(4,l.flags),f.write_shift(4,l.version),wa(l.name,f),f.slice(0,f.l))),Jr(h,336),Jr(h,339,function(e,t){var r=Kr(20);return r.write_shift(4,1),wa(t,r),r.slice(0,r.l)}(0,"XLDAPR")),Jr(h,52),Jr(h,35,ga(514)),Jr(h,4096,ga(0)),Jr(h,4097,Yn(1)),Jr(h,36),Jr(h,53),Jr(h,340),Jr(h,337,function(e,t){var r=Kr(8);return r.write_shift(4,1),r.write_shift(4,1),r}()),Jr(h,51,function(e){var t=Kr(4+8*e.length);t.write_shift(4,e.length);for(var r=0;r<e.length;++r)t.write_shift(4,e[r][0]),t.write_shift(4,e[r][1]);return t}([[1,0]])),Jr(h,338),Jr(h,333),h.end())),a.metadata.push(s),un(t.wbrels,-1,"metadata."+r,cn.XLMETA),u.length>1&&(kt(n,s="xl/persons/person.xml",ao(u)),a.people.push(s),un(t.wbrels,-1,"persons/person.xml",cn.PEOPLE)),kt(n,"[Content_Types].xml",on(a,t)),kt(n,"_rels/.rels",hn(t.rels)),kt(n,"xl/_rels/workbook.bin.rels",hn(t.wbrels)),delete t.revssf,delete t.ssf,n}(e,t);default:return jf(e,t)}}function th(e,t){var r={},a=k?"nodebuffer":"undefined"!=typeof Uint8Array?"array":"string";if(t.compression&&(r.compression="DEFLATE"),t.password)r.type=a;else switch(t.type){case"base64":r.type="base64";break;case"binary":r.type="string";break;case"string":throw new Error("'string' output type invalid for '"+t.bookType+"' files");case"buffer":case"file":r.type=a;break;default:throw new Error("Unrecognized type "+t.type)}var n=e.FullPaths?Ie.write(e,{fileType:"zip",type:{nodebuffer:"buffer",string:"binary"}[r.type]||r.type,compression:!!t.compression}):e.generate(r);if("undefined"!=typeof Deno&&"string"==typeof n){if("binary"==t.type||"base64"==t.type)return n;n=new Uint8Array(I(n))}return t.password&&"undefined"!=typeof encrypt_agile?Qf(encrypt_agile(n,t.password),t):"file"===t.type?Ne(t.file,n):"string"==t.type?Zt(n):n}function rh(e,t,r){r||(r="");var a=r+e;switch(t.type){case"base64":return y(Jt(a));case"binary":return Jt(a);case"string":return e;case"file":return Ne(t.file,a,"utf8");case"buffer":return k?_(a,"utf8"):"undefined"!=typeof TextEncoder?(new TextEncoder).encode(a):rh(a,{type:"binary"}).split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}function ah(e,t){switch(t.type){case"string":case"base64":case"binary":for(var r="",a=0;a<e.length;++a)r+=String.fromCharCode(e[a]);return"base64"==t.type?y(r):"string"==t.type?Zt(r):r;case"file":return Ne(t.file,e);case"buffer":return e;default:throw new Error("Unrecognized type "+t.type)}}function nh(e,t){h(),jc(e);var r=Ke(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var a=nh(e,r);return r.type="array",I(a)}return function(e,t){var r=Ke(t||{});return th(jf(e,r),r)}(e,r)}function sh(e,t){h(),jc(e);var r=Ke(t||{});if(r.cellStyles&&(r.cellNF=!0,r.sheetStubs=!0),"array"==r.type){r.type="binary";var a=sh(e,r);return r.type="array",I(a)}var n=0;if(r.sheet&&(n="number"==typeof r.sheet?r.sheet:e.SheetNames.indexOf(r.sheet),!e.SheetNames[n]))throw new Error("Sheet not found: "+r.sheet+" : "+typeof r.sheet);switch(r.bookType||"xlsb"){case"xml":case"xlml":return rh(dl(e,r),r);case"slk":case"sylk":return rh(Zs.from_sheet(e.Sheets[e.SheetNames[n]],r,e),r);case"htm":case"html":return rh(Ll(e.Sheets[e.SheetNames[n]],r),r);case"txt":return function(e,t){switch(t.type){case"base64":return function(e){for(var t="",r=0,a=0,n=0,s=0,i=0,o=0,c=0,l=0;l<e.length;)(r=e.charCodeAt(l++))>255&&(r=95),s=r>>2,(a=e.charCodeAt(l++))>255&&(a=95),i=(3&r)<<4|a>>4,(n=e.charCodeAt(l++))>255&&(n=95),o=(15&a)<<2|n>>6,c=63&n,isNaN(a)?o=c=64:isNaN(n)&&(c=64),t+=T.charAt(s)+T.charAt(i)+T.charAt(o)+T.charAt(c);return t}(e);case"binary":case"string":return e;case"file":return Ne(t.file,e,"binary");case"buffer":return k?_(e,"binary"):e.split("").map((function(e){return e.charCodeAt(0)}))}throw new Error("Unrecognized type "+t.type)}(mh(e.Sheets[e.SheetNames[n]],r),r);case"csv":return rh(ph(e.Sheets[e.SheetNames[n]],r),r,"\ufeff");case"dif":return rh(Js.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"dbf":return ah(Ys.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"prn":return rh(Qs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"rtf":return rh(function(e,t){var r=["{\\rtf1\\ansi"];if(!e["!ref"])return r[0]+"}";for(var a,n=ua(e["!ref"]),s=null!=e["!data"],i=[],o=n.s.r;o<=n.e.r;++o){r.push("\\trowd\\trautofit1");for(var c=n.s.c;c<=n.e.c;++c)r.push("\\cellx"+(c+1));for(r.push("\\pard\\intbl"),s&&(i=e["!data"][o]||[]),c=n.s.c;c<=n.e.c;++c){var l=oa({r:o,c:c});(a=s?i[c]:e[l])&&(null!=a.v||a.f&&!a.F)?(r.push(" "+(a.w||(da(a),a.w)||"").replace(/[\r\n]/g,"\\par ")),r.push("\\cell")):r.push(" \\cell")}r.push("\\pard\\intbl\\row")}return r.join("")+"}"}(e.Sheets[e.SheetNames[n]]),r);case"eth":return rh(qs.from_sheet(e.Sheets[e.SheetNames[n]],r),r);case"fods":return rh(Yl(e,r),r);case"wk1":return ah(ei.sheet_to_wk1(e.Sheets[e.SheetNames[n]],r),r);case"wk3":return ah(ei.book_to_wk3(e,r),r);case"biff2":r.biff||(r.biff=2);case"biff3":r.biff||(r.biff=3);case"biff4":return r.biff||(r.biff=4),ah(Rl(e,r),r);case"biff5":r.biff||(r.biff=5);case"biff8":case"xla":case"xls":return r.biff||(r.biff=8),function(e,t){var r=t||{};return Qf(function(e,t){var r=t||{},a=Ie.utils.cfb_new({root:"R"}),n="/Workbook";switch(r.bookType||"xls"){case"xls":r.bookType="biff8";case"xla":r.bookType||(r.bookType="xla");case"biff8":n="/Workbook",r.biff=8;break;case"biff5":n="/Book",r.biff=5;break;default:throw new Error("invalid type "+r.bookType+" for XLS CFB")}return Ie.utils.cfb_add(a,n,Rl(e,r)),8==r.biff&&(e.Props||e.Custprops)&&function(e,t){var r,a=[],n=[],s=[],i=0,o=De(Ya,"n"),c=De(Za,"n");if(e.Props)for(r=Fe(e.Props),i=0;i<r.length;++i)(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Props[r[i]]]);if(e.Custprops)for(r=Fe(e.Custprops),i=0;i<r.length;++i)Object.prototype.hasOwnProperty.call(e.Props||{},r[i])||(Object.prototype.hasOwnProperty.call(o,r[i])?a:Object.prototype.hasOwnProperty.call(c,r[i])?n:s).push([r[i],e.Custprops[r[i]]]);var l=[];for(i=0;i<s.length;++i)Wn.indexOf(s[i][0])>-1||yn.indexOf(s[i][0])>-1||null!=s[i][1]&&l.push(s[i]);n.length&&Ie.utils.cfb_add(t,"/SummaryInformation",$n(n,bl.SI,c,Za)),(a.length||l.length)&&Ie.utils.cfb_add(t,"/DocumentSummaryInformation",$n(a,bl.DSI,o,Ya,l.length?l:null,bl.UDI))}(e,a),8==r.biff&&e.vbaraw&&function(e,t){t.FullPaths.forEach((function(r,a){if(0!=a){var n=r.replace(/^[\/]*[^\/]*[\/]/,"/_VBA_PROJECT_CUR/");"/"!==n.slice(-1)&&Ie.utils.cfb_add(e,n,t.FileIndex[a].content)}}))}(a,Ie.read(e.vbaraw,{type:"string"==typeof e.vbaraw?"binary":"buffer"})),a}(e,r),r)}(e,r);case"xlsx":case"xlsm":case"xlam":case"xlsb":case"numbers":case"ods":return function(e,t){var r=Ke(t||{});return th(eh(e,r),r)}(e,r);default:throw new Error("Unrecognized bookType |"+r.bookType+"|")}}function ih(e){if(!e.bookType){var t=e.file.slice(e.file.lastIndexOf(".")).toLowerCase();t.match(/^\.[a-z]+$/)&&(e.bookType=t.slice(1)),e.bookType={xls:"biff8",htm:"html",slk:"sylk",socialcalc:"eth",Sh33tJS:"WTF"}[e.bookType]||e.bookType}}function oh(e,t,r){var a=r||{};return a.type="file",a.file=t,ih(a),sh(e,a)}function ch(e,t,r){var a=r||{};return a.type="file",a.file=t,ih(a),nh(e,a)}function lh(e,t,r,a){var n=r||{};n.type="file",n.file=e,ih(n),n.type="buffer";var s=a;return s instanceof Function||(s=r),Ae.writeFile(e,sh(t,n),s)}function fh(e,t,r,a,n,s,i){var o=aa(r),c=i.defval,l=i.raw||!Object.prototype.hasOwnProperty.call(i,"raw"),f=!0,h=null!=e["!data"],u=1===n?[]:{};if(1!==n)if(Object.defineProperty)try{Object.defineProperty(u,"__rowNum__",{value:r,enumerable:!1})}catch(e){u.__rowNum__=r}else u.__rowNum__=r;if(!h||e["!data"][r])for(var d=t.s.c;d<=t.e.c;++d){var p=h?(e["!data"][r]||[])[d]:e[a[d]+o];if(null!=p&&void 0!==p.t){var m=p.v;switch(p.t){case"z":if(null==m)break;continue;case"e":m=0==m?null:void 0;break;case"s":case"b":case"n":if(!p.z||!me(p.z))break;if("number"==typeof(m=ze(m)))break;case"d":i&&(i.UTC||!1===i.raw)||(m=nt(new Date(m)));break;default:throw new Error("unrecognized type "+p.t)}if(null!=s[d]){if(null==m)if("e"==p.t&&null===m)u[s[d]]=null;else if(void 0!==c)u[s[d]]=c;else{if(!l||null!==m)continue;u[s[d]]=null}else u[s[d]]=("n"===p.t&&"boolean"==typeof i.rawNumbers?i.rawNumbers:l)?m:da(p,m,i);null!=m&&(f=!1)}}else{if(void 0===c)continue;null!=s[d]&&(u[s[d]]=c)}}return{row:u,isempty:f}}function hh(e,t){if(null==e||null==e["!ref"])return[];var r={t:"n",v:0},a=0,n=1,s=[],i=0,o="",c={s:{r:0,c:0},e:{r:0,c:0}},l=t||{},f=null!=l.range?l.range:e["!ref"];switch(1===l.header?a=1:"A"===l.header?a=2:Array.isArray(l.header)?a=3:null==l.header&&(a=0),typeof f){case"string":c=ua(f);break;case"number":(c=ua(e["!ref"])).s.r=f;break;default:c=f}a>0&&(n=0);var h=aa(c.s.r),u=[],d=[],p=0,m=0,v=null!=e["!data"],g=c.s.r,b=0,w={};v&&!e["!data"][g]&&(e["!data"][g]=[]);var T=l.skipHidden&&e["!cols"]||[],y=l.skipHidden&&e["!rows"]||[];for(b=c.s.c;b<=c.e.c;++b)if(!(T[b]||{}).hidden)switch(u[b]=sa(b),r=v?e["!data"][g][b]:e[u[b]+h],a){case 1:s[b]=b-c.s.c;break;case 2:s[b]=u[b];break;case 3:s[b]=l.header[b-c.s.c];break;default:if(null==r&&(r={w:"__EMPTY",t:"s"}),o=i=da(r,null,l),m=w[i]||0){do{o=i+"_"+m++}while(w[o]);w[i]=m,w[o]=1}else w[i]=1;s[b]=o}for(g=c.s.r+n;g<=c.e.r;++g)if(!(y[g]||{}).hidden){var E=fh(e,c,g,u,a,s,l);(!1===E.isempty||(1===a?!1!==l.blankrows:l.blankrows))&&(d[p++]=E.row)}return d.length=p,d}var uh=/"/g;function dh(e,t,r,a,n,s,i,o){for(var c=!0,l=[],f="",h=aa(r),u=null!=e["!data"],d=u&&e["!data"][r]||[],p=t.s.c;p<=t.e.c;++p)if(a[p]){var m=u?d[p]:e[a[p]+h];if(null==m)f="";else if(null!=m.v){c=!1,f=""+(o.rawNumbers&&"n"==m.t?m.v:da(m,null,o));for(var v=0,g=0;v!==f.length;++v)if((g=f.charCodeAt(v))===n||g===s||34===g||o.forceQuotes){f='"'+f.replace(uh,'""')+'"';break}"ID"==f&&(f='"ID"')}else null==m.f||m.F?f="":(c=!1,(f="="+m.f).indexOf(",")>=0&&(f='"'+f.replace(uh,'""')+'"'));l.push(f)}if(o.strip)for(;""===l[l.length-1];)--l.length;return!1===o.blankrows&&c?null:l.join(i)}function ph(e,t){var r=[],a=null==t?{}:t;if(null==e||null==e["!ref"])return"";for(var n=ua(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l="",f=[],h=a.skipHidden&&e["!cols"]||[],u=a.skipHidden&&e["!rows"]||[],d=n.s.c;d<=n.e.c;++d)(h[d]||{}).hidden||(f[d]=sa(d));for(var p=0,m=n.s.r;m<=n.e.r;++m)(u[m]||{}).hidden||null!=(l=dh(e,n,m,f,i,c,s,a))&&(l||!1!==a.blankrows)&&r.push((p++?o:"")+l);return r.join("")}function mh(e,t){t||(t={}),t.FS="\t",t.RS="\n";var r=ph(e,t);if(void 0===a||"string"==t.type)return r;var n=a.utils.encode(1200,r,"str");return String.fromCharCode(255)+String.fromCharCode(254)+n}function vh(e,t,r){var a=r||{},n=e?null!=e["!data"]:a.dense;null!=w&&null==n&&(n=w);var s=+!a.skipHeader,i=e||{};!e&&n&&(i["!data"]=[]);var o=0,c=0;if(i&&null!=a.origin)if("number"==typeof a.origin)o=a.origin;else{var l="string"==typeof a.origin?ia(a.origin):a.origin;o=l.r,c=l.c}var f={s:{c:0,r:0},e:{c:c,r:o+t.length-1+s}};if(i["!ref"]){var h=ua(i["!ref"]);f.e.c=Math.max(f.e.c,h.e.c),f.e.r=Math.max(f.e.r,h.e.r),-1==o&&(o=h.e.r+1,f.e.r=o+t.length-1+s)}else-1==o&&(o=0,f.e.r=t.length-1+s);var u=a.header||[],d=0,p=[];t.forEach((function(e,t){n&&!i["!data"][o+t+s]&&(i["!data"][o+t+s]=[]),n&&(p=i["!data"][o+t+s]),Fe(e).forEach((function(r){-1==(d=u.indexOf(r))&&(u[d=u.length]=r);var l=e[r],f="z",h="",m=n?"":sa(c+d)+aa(o+t+s),v=n?p[c+d]:i[m];!l||"object"!=typeof l||l instanceof Date?("number"==typeof l?f="n":"boolean"==typeof l?f="b":"string"==typeof l?f="s":l instanceof Date?(f="d",a.UTC||(l=st(l)),a.cellDates||(f="n",l=We(l)),h=null!=v&&v.z&&me(v.z)?v.z:a.dateNF||G[14]):null===l&&a.nullError&&(f="e",l=0),v?(v.t=f,v.v=l,delete v.w,delete v.R,h&&(v.z=h)):n?p[c+d]=v={t:f,v:l}:i[m]=v={t:f,v:l},h&&(v.z=h)):n?p[c+d]=l:i[m]=l}))})),f.e.c=Math.max(f.e.c,c+u.length-1);var m=aa(o);if(n&&!i["!data"][o]&&(i["!data"][o]=[]),s)for(d=0;d<u.length;++d)n?i["!data"][o][d+c]={t:"s",v:u[d]}:i[sa(d+c)+m]={t:"s",v:u[d]};return i["!ref"]=la(f),i}function gh(e,t,r){if("string"==typeof t){if(null!=e["!data"]){var a=ia(t);return e["!data"][a.r]||(e["!data"][a.r]=[]),e["!data"][a.r][a.c]||(e["!data"][a.r][a.c]={t:"z"})}return e[t]||(e[t]={t:"z"})}return gh(e,"number"!=typeof t?oa(t):sa(r||0)+aa(t))}function bh(e,t){var r={SheetNames:[],Sheets:{}};return e&&wh(r,e,t||"Sheet1"),r}function wh(e,t,r,a){var n=1;if(!r)for(;n<=65535&&-1!=e.SheetNames.indexOf(r="Sheet"+n);++n,r=void 0);if(!r||e.SheetNames.length>=65535)throw new Error("Too many worksheets");if(a&&e.SheetNames.indexOf(r)>=0&&r.length<32){var s=r.match(/\d+$/);n=s&&+s[0]||0;var i=s&&r.slice(0,s.index)||r;for(++n;n<=65535&&-1!=e.SheetNames.indexOf(r=i+n);++n);}if(Xc(r),e.SheetNames.indexOf(r)>=0)throw new Error("Worksheet with name |"+r+"| already exists!");return e.SheetNames.push(r),e.Sheets[r]=t,r}function Th(e,t,r){return t?(e.l={Target:t},r&&(e.l.Tooltip=r)):delete e.l,e}var yh,Eh={encode_col:sa,encode_row:aa,encode_cell:oa,encode_range:la,decode_col:na,decode_row:ra,split_cell:function(e){return e.replace(/(\$?[A-Z]*)(\$?\d*)/,"$1,$2").split(",")},decode_cell:ia,decode_range:ca,format_cell:da,sheet_new:function(e){var t={};return(e||{}).dense&&(t["!data"]=[]),t},sheet_add_aoa:ma,sheet_add_json:vh,sheet_add_dom:Ul,aoa_to_sheet:va,json_to_sheet:function(e,t){return vh(null,e,t)},table_to_sheet:Bl,table_to_book:function(e,t){return pa(Bl(e,t),t)},sheet_to_csv:ph,sheet_to_txt:mh,sheet_to_json:hh,sheet_to_html:Ll,sheet_to_formulae:function(e){var t,r="",a="";if(null==e||null==e["!ref"])return[];var n,s=ua(e["!ref"]),i="",o=[],c=[],l=null!=e["!data"];for(n=s.s.c;n<=s.e.c;++n)o[n]=sa(n);for(var f=s.s.r;f<=s.e.r;++f)for(i=aa(f),n=s.s.c;n<=s.e.c;++n)if(r=o[n]+i,a="",void 0!==(t=l?(e["!data"][f]||[])[n]:e[r])){if(null!=t.F){if(r=t.F,!t.f)continue;a=t.f,-1==r.indexOf(":")&&(r=r+":"+r)}if(null!=t.f)a=t.f;else{if("z"==t.t)continue;if("n"==t.t&&null!=t.v)a=""+t.v;else if("b"==t.t)a=t.v?"TRUE":"FALSE";else if(void 0!==t.w)a="'"+t.w;else{if(void 0===t.v)continue;a="s"==t.t?"'"+t.v:""+t.v}}c[c.length]=r+"="+a}return c},sheet_to_row_object_array:hh,sheet_get_cell:gh,book_new:bh,book_append_sheet:wh,book_set_sheet_visibility:function(e,t,r){e.Workbook||(e.Workbook={}),e.Workbook.Sheets||(e.Workbook.Sheets=[]);var a=function(e,t){if("number"==typeof t){if(t>=0&&e.SheetNames.length>t)return t;throw new Error("Cannot find sheet # "+t)}if("string"==typeof t){var r=e.SheetNames.indexOf(t);if(r>-1)return r;throw new Error("Cannot find sheet name |"+t+"|")}throw new Error("Cannot find sheet |"+t+"|")}(e,t);switch(e.Workbook.Sheets[a]||(e.Workbook.Sheets[a]={}),r){case 0:case 1:case 2:break;default:throw new Error("Bad sheet visibility setting "+r)}e.Workbook.Sheets[a].Hidden=r},cell_set_number_format:function(e,t){return e.z=t,e},cell_set_hyperlink:Th,cell_set_internal_link:function(e,t,r){return Th(e,"#"+t,r)},cell_add_comment:function(e,t,r){e.c||(e.c=[]),e.c.push({t:t,a:r||"SheetJS"})},sheet_set_array_formula:function(e,t,r,a){for(var n="string"!=typeof t?t:ua(t),s="string"==typeof t?t:la(t),i=n.s.r;i<=n.e.r;++i)for(var o=n.s.c;o<=n.e.c;++o){var c=gh(e,i,o);c.t="n",c.F=s,delete c.v,i==n.s.r&&o==n.s.c&&(c.f=r,a&&(c.D=!0))}var l=ca(e["!ref"]);return l.s.r>n.s.r&&(l.s.r=n.s.r),l.s.c>n.s.c&&(l.s.c=n.s.c),l.e.r<n.e.r&&(l.e.r=n.e.r),l.e.c<n.e.c&&(l.e.c=n.e.c),e["!ref"]=la(l),e},consts:{SHEET_VISIBLE:0,SHEET_HIDDEN:1,SHEET_VERY_HIDDEN:2}},kh={to_json:function(e,t){var r=yh({objectMode:!0});if(null==e||null==e["!ref"])return r.push(null),r;var a={t:"n",v:0},n=0,s=1,i=[],o=0,c="",l={s:{r:0,c:0},e:{r:0,c:0}},f=t||{},h=null!=f.range?f.range:e["!ref"];switch(1===f.header?n=1:"A"===f.header?n=2:Array.isArray(f.header)&&(n=3),typeof h){case"string":l=ua(h);break;case"number":(l=ua(e["!ref"])).s.r=h;break;default:l=h}n>0&&(s=0);var u=aa(l.s.r),d=[],p=0,m=null!=e["!data"],v=l.s.r,g=0,b={};m&&!e["!data"][v]&&(e["!data"][v]=[]);var w=f.skipHidden&&e["!cols"]||[],T=f.skipHidden&&e["!rows"]||[];for(g=l.s.c;g<=l.e.c;++g)if(!(w[g]||{}).hidden)switch(d[g]=sa(g),a=m?e["!data"][v][g]:e[d[g]+u],n){case 1:i[g]=g-l.s.c;break;case 2:i[g]=d[g];break;case 3:i[g]=f.header[g-l.s.c];break;default:if(null==a&&(a={w:"__EMPTY",t:"s"}),c=o=da(a,null,f),p=b[o]||0){do{c=o+"_"+p++}while(b[c]);b[o]=p,b[c]=1}else b[o]=1;i[g]=c}return v=l.s.r+s,r._read=function(){for(;v<=l.e.r;)if(!(T[v-1]||{}).hidden){var t=fh(e,l,v,d,n,i,f);if(++v,!1===t.isempty||(1===n?!1!==f.blankrows:f.blankrows))return void r.push(t.row)}return r.push(null)},r},to_html:function(e,t){var r=yh(),a=t||{},n=null!=a.header?a.header:Dl,s=null!=a.footer?a.footer:Pl;r.push(n);var i=ca(e["!ref"]);r.push(Ml(0,0,a));var o=i.s.r,c=!1;return r._read=function(){if(o>i.e.r)return c||(c=!0,r.push("</table>"+s)),r.push(null);for(;o<=i.e.r;){r.push(Fl(e,i,o,a)),++o;break}},r},to_csv:function(e,t){var r=yh(),a=null==t?{}:t;if(null==e||null==e["!ref"])return r.push(null),r;for(var n=ua(e["!ref"]),s=void 0!==a.FS?a.FS:",",i=s.charCodeAt(0),o=void 0!==a.RS?a.RS:"\n",c=o.charCodeAt(0),l="",f=[],h=a.skipHidden&&e["!cols"]||[],u=a.skipHidden&&e["!rows"]||[],d=n.s.c;d<=n.e.c;++d)(h[d]||{}).hidden||(f[d]=sa(d));var p=n.s.r,m=!1,v=0;return r._read=function(){if(!m)return m=!0,r.push("\ufeff");for(;p<=n.e.r;)if(++p,!(u[p-1]||{}).hidden&&null!=(l=dh(e,n,p-1,f,i,c,s,a))&&(l||!1!==a.blankrows))return r.push((v++?o:"")+l);return r.push(null)},r},set_readable:function(e){yh=e}};const _h=n.version;t.default={parse_xlscfb:wl,parse_zip:Gf,read:Jf,readFile:qf,readFileSync:qf,write:sh,writeFile:oh,writeFileSync:oh,writeFileAsync:lh,writeXLSX:nh,writeFileXLSX:ch,utils:Eh,set_fs:Oe,set_cptable:b,stream:kh,SSF:Ee,CFB:Ie}}}]);
//# sourceMappingURL=xlsx.js.map