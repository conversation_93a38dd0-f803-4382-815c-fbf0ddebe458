{"version": 3, "file": "deps/markerjs2.js", "mappings": ";27CAgBA,IAAIA,EAAgB,SAASC,EAAGC,GAI5B,OAHAF,EAAgBG,OAAOC,gBAClB,CAAEC,UAAW,cAAgBC,OAAS,SAAUL,EAAGC,GAAKD,EAAEI,UAAYH,CAAAA,GACvE,SAAUD,EAAGC,GAAK,IAAK,IAAIK,KAAKL,EAAOC,OAAOK,UAAUC,eAAeC,KAAKR,EAAGK,KAAIN,EAAEM,GAAKL,EAAEK,GAAAA,GAC3EN,EAAGC,EAAAA,EAGrB,SAASS,EAAUV,EAAGC,GAEzB,SAASU,IAAOC,KAAKC,YAAcb,CAAAA,CADnCD,EAAcC,EAAGC,GAEjBD,EAAEO,UAAkB,OAANN,EAAaC,OAAOY,OAAOb,IAAMU,EAAGJ,UAAYN,EAAEM,UAAW,IAAII,EAAAA,CAyC5E,SAASI,EAAUC,EAASC,EAAYC,EAAGC,GAE9C,OAAO,IAAKD,IAAMA,EAAIE,WAAU,SAAUC,EAASC,GAC/C,SAASC,EAAUC,GAAS,IAAMC,EAAKN,EAAUO,KAAKF,GAAAA,CAAW,MAAOG,GAAKL,EAAOK,EAAAA,CAAAA,CACpF,SAASC,EAASJ,GAAS,IAAMC,EAAKN,EAAiB,MAAEK,GAAAA,CAAW,MAAOG,GAAKL,EAAOK,EAAAA,CAAAA,CACvF,SAASF,EAAKI,GAJlB,IAAeL,EAIaK,EAAOC,KAAOT,EAAQQ,EAAOL,QAJ1CA,EAIyDK,EAAOL,MAJhDA,aAAiBN,EAAIM,EAAQ,IAAIN,GAAAA,SAAYG,GAAWA,EAAQG,EAAAA,KAITO,KAAKR,EAAWK,EAAAA,CAClGH,GAAMN,EAAYA,EAAUa,MAAMhB,EAASC,GAAc,KAAKS,OAAAA,GAAAA,CAI/D,SAASO,EAAYjB,EAASkB,GACjC,IAAsGC,EAAGC,EAAGC,EAAGC,EAA3GC,EAAI,CAAEC,MAAO,EAAGC,KAAM,WAAa,GAAW,EAAPJ,EAAE,GAAQ,MAAMA,EAAE,GAAI,OAAOA,EAAE,EAAE,EAAKK,KAAM,GAAIC,IAAK,IAChG,OAAOL,EAAI,CAAEZ,KAAMkB,EAAK,GAAIC,MAASD,EAAK,GAAIE,OAAUF,EAAK,IAAwB,mBAAXG,SAA0BT,EAAES,OAAOC,UAAY,WAAa,OAAOpC,IAAI,GAAM0B,EACvJ,SAASM,EAAKK,GAAK,OAAO,SAAUC,GAAK,OACzC,SAAcC,GACV,GAAIhB,EAAG,MAAM,IAAIiB,UAAU,mCAC3B,KAAOb,GAAAA,IACH,GAAIJ,EAAI,EAAGC,IAAMC,EAAY,EAARc,EAAG,GAASf,EAAU,OAAIe,EAAG,GAAKf,EAAS,SAAOC,EAAID,EAAU,SAAMC,EAAE5B,KAAK2B,GAAI,GAAKA,EAAEV,SAAWW,EAAIA,EAAE5B,KAAK2B,EAAGe,EAAG,KAAKrB,KAAM,OAAOO,EAE3J,OADID,EAAI,EAAGC,IAAGc,EAAK,CAAS,EAARA,EAAG,GAAQd,EAAEb,QACzB2B,EAAG,IACP,KAAK,EAAG,KAAK,EAAGd,EAAIc,EAAI,MACxB,KAAK,EAAc,OAAXZ,EAAEC,QAAgB,CAAEhB,MAAO2B,EAAG,GAAIrB,MAAK,GAC/C,KAAK,EAAGS,EAAEC,QAASJ,EAAIe,EAAG,GAAIA,EAAK,CAAC,GAAI,SACxC,KAAK,EAAGA,EAAKZ,EAAEI,IAAIU,MAAOd,EAAEG,KAAKW,MAAO,SACxC,QACI,MAAkBhB,GAAZA,EAAIE,EAAEG,MAAYY,OAAS,GAAKjB,EAAEA,EAAEiB,OAAS,KAAkB,IAAVH,EAAG,IAAsB,IAAVA,EAAG,IAAW,CAAEZ,EAAI,EAAG,QAAQ,CACzG,GAAc,IAAVY,EAAG,MAAcd,GAAMc,EAAG,GAAKd,EAAE,IAAMc,EAAG,GAAKd,EAAE,IAAM,CAAEE,EAAEC,MAAQW,EAAG,GAAI,KAAK,CACnF,GAAc,IAAVA,EAAG,IAAYZ,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIA,EAAIc,EAAI,KAAK,CAClE,GAAId,GAAKE,EAAEC,MAAQH,EAAE,GAAI,CAAEE,EAAEC,MAAQH,EAAE,GAAIE,EAAEI,IAAIY,KAAKJ,GAAK,KAAK,CAC5Dd,EAAE,IAAIE,EAAEI,IAAIU,MAChBd,EAAEG,KAAKW,MAAO,SAEtBF,EAAKjB,EAAKzB,KAAKO,EAASuB,EAAAA,CAC1B,MAAOZ,GAAKwB,EAAK,CAAC,EAAGxB,GAAIS,EAAI,CAAC,CAAD,QAAeD,EAAIE,EAAI,CAAC,CACvD,GAAY,EAARc,EAAG,GAAQ,MAAMA,EAAG,GAAI,MAAO,CAAE3B,MAAO2B,EAAG,GAAKA,EAAG,UAAarB,MAAK,EAAC,CApB9E,CADqD,CAACmB,EAAGC,GAAAA,CAAAA,CAAAA,CAwEtD,SAASM,IACZ,IAAK,IAAIC,EAAI,EAAGC,EAAI,EAAGC,EAAKC,UAAUN,OAAQI,EAAIC,EAAID,IAAKD,GAAKG,UAAUF,GAAGJ,OACxE,IAAIO,EAAIxD,MAAMoD,GAAIK,EAAI,EAA3B,IAA8BJ,EAAI,EAAGA,EAAIC,EAAID,IACzC,IAAK,IAAIK,EAAIH,UAAUF,GAAIM,EAAI,EAAGC,EAAKF,EAAET,OAAQU,EAAIC,EAAID,IAAKF,IAC1DD,EAAEC,GAAKC,EAAEC,GACjB,OAAOH,CAAAA,CAAAA,IAAAA,EAAAA,WC1JX,cA2SA,OAvSgBK,EAAAA,WAAd,WAGE,OAFaC,SAASC,gBAAgB,6BAA8B,SAUxDF,EAAAA,cAAd,SACEG,EACAC,GAEA,IAA4B,QAAAC,EAAAA,EAAAC,EAAAA,EAAAA,OAAAA,IAAY,CAA7B,IAAAC,EAAAA,EAAAA,GAACC,EAAAA,EAAAA,GAAMlD,EAAAA,EAAAA,GAChB6C,EAAGM,aAAaD,EAAMlD,EAAAA,CAAAA,EAUZ0C,EAAAA,WAAd,SACEU,EACAC,EACAP,GAEA,IAAMQ,EAAOX,SAASC,gBAAgB,6BAA8B,QAQpE,OANAU,EAAKH,aAAa,QAASC,EAAMG,YACjCD,EAAKH,aAAa,SAAUE,EAAOE,YAC/BT,GACFJ,EAAUc,cAAcF,EAAMR,GAGzBQ,CAAAA,EAWKZ,EAAAA,WAAd,SACEe,EACAC,EACAC,EACAC,EACAd,GAEA,IAAMe,EAAOlB,SAASC,gBAAgB,6BAA8B,QAUpE,OARAiB,EAAKV,aAAa,KAAMM,EAAGF,YAC3BM,EAAKV,aAAa,KAAMO,EAAGH,YAC3BM,EAAKV,aAAa,KAAMQ,EAAGJ,YAC3BM,EAAKV,aAAa,KAAMS,EAAGL,YACvBT,GACFJ,EAAUc,cAAcK,EAAMf,GAGzBe,CAAAA,EAQKnB,EAAAA,cAAd,SACEoB,EACAhB,GAEA,IAAMiB,EAAUpB,SAASC,gBACvB,6BACA,WAQF,OALAmB,EAAQZ,aAAa,SAAUW,GAC3BhB,GACFJ,EAAUc,cAAcO,EAASjB,GAG5BiB,CAAAA,EAQKrB,EAAAA,aAAd,SACEsB,EACAlB,GAEA,IAAMmB,EAAStB,SAASC,gBACtB,6BACA,UAUF,OAPAqB,EAAOd,aAAa,MAAOa,EAAS,GAAGT,YACvCU,EAAOd,aAAa,MAAOa,EAAS,GAAGT,YACvCU,EAAOd,aAAa,IAAKa,EAAOT,YAC5BT,GACFJ,EAAUc,cAAcS,EAAQnB,GAG3BmB,CAAAA,EASKvB,EAAAA,cAAd,SACEwB,EACAC,EACArB,GAEA,IAAMsB,EAAUzB,SAASC,gBACvB,6BACA,WAWF,OARAwB,EAAQjB,aAAa,MAAOe,EAAK,GAAGX,YACpCa,EAAQjB,aAAa,MAAOgB,EAAK,GAAGZ,YACpCa,EAAQjB,aAAa,MAAOe,EAAK,GAAGX,YACpCa,EAAQjB,aAAa,MAAOgB,EAAK,GAAGZ,YAChCT,GACFJ,EAAUc,cAAcY,EAAStB,GAG5BsB,CAAAA,EAOK1B,EAAAA,YAAd,SAA0BI,GACxB,IAAMhC,EAAI6B,SAASC,gBAAgB,6BAA8B,KAIjE,OAHIE,GACFJ,EAAUc,cAAc1C,EAAGgC,GAEtBhC,CAAAA,EAMK4B,EAAAA,gBAAd,WAGE,OAFYC,SAASC,gBAAgB,6BAA8B,OAExDyB,oBAAoB,EAanB3B,EAAAA,aAAd,SACE4B,EACAC,EACAC,EACAC,EACAC,EACAC,EACAC,GAEA,IAAMC,EAASlC,SAASC,gBACtB,6BACA,UAaF,OAXAF,EAAUc,cAAcqB,EAAQ,CAC9B,CAAC,KAAMP,GACP,CAAC,SAAUC,GACX,CAAC,cAAeC,EAAYjB,YAC5B,CAAC,eAAgBkB,EAAalB,YAC9B,CAAC,OAAQmB,EAAKnB,YACd,CAAC,OAAQoB,EAAKpB,cAGhBsB,EAAOC,YAAYF,GAEZC,CAAAA,EAOKnC,EAAAA,WAAd,SACEI,GAEA,IAAMiC,EAAOpC,SAASC,gBAAgB,6BAA8B,QAQpE,OAPAmC,EAAK5B,aAAa,IAAK,KACvB4B,EAAK5B,aAAa,IAAK,KAEnBL,GACFJ,EAAUc,cAAcuB,EAAMjC,GAGzBiC,CAAAA,EAQKrC,EAAAA,YAAd,SACEqC,EACAjC,GAEA,IAAMkC,EAAQrC,SAASC,gBACrB,6BACA,SAQF,OANAoC,EAAMC,YAAcF,EAEhBjC,GACFJ,EAAUc,cAAcwB,EAAOlC,GAG1BkC,CAAAA,EAOKtC,EAAAA,YAAd,SACEI,GAEA,IAAMoC,EAAQvC,SAASC,gBACrB,6BACA,SAOF,OAJIE,GACFJ,EAAUc,cAAc0B,EAAOpC,GAG1BoC,CAAC,EAQIxC,EAAAA,YAAd,SACEyC,EACAvE,GAEE,IACMwE,EADMzC,SAASC,gBAAgB,6BAA8B,OAC9CyC,iBAIrB,OAHAD,EAASD,EAAIA,EACbC,EAASxE,EAAIA,EAENwE,CAAAA,EAQI1C,EAAAA,WAAd,SACClE,EACAsE,GAEA,IAAMwC,EAAO3C,SAASC,gBAAgB,6BAA8B,QAOpE,OALA0C,EAAKnC,aAAa,IAAK3E,GACnBsE,GACFJ,EAAUc,cAAc8B,EAAMxC,GAGzBwC,CAAAA,EAAAA,CAAAA,CD/IAjD,GC+IAiD,EAAAA,WCzSX,cA0BA,OAnBgBC,EAAAA,OAAd,SAAqBC,GACnBD,EAAUC,IAAMA,CAAAA,EAMlB9G,OAAAA,eAAkB6G,EAAAA,aAAAA,CAAAA,IAAlB,WAKE,QAAIA,EAAUC,KACK,IAAIC,OAAO,8CAA+C,KAC3DC,KAAKH,EAAUC,IAAI,EAAJA,YAAAA,EAAAA,cAAAA,IAAAA,CAAAA,CDoR1BF,GCpR0BE,EAAAA,WCrBrC,aAIWpG,KAAAA,aAAAA,EAIAA,KAAAA,UAAY,YAYZA,KAAAA,aAAAA,CAAc,CA+FzB,OAxEWuG,EAAAA,UAAAA,UAAP,SACIC,EACAC,EACAC,GAHJ,WAKI,OAAO,IAAIlG,SAAAA,SAAiBC,GACxB,IAAMkG,OAAAA,IAASD,EAA6BA,EAAenD,SAASqD,cAAc,UAEnE,OAAXJ,IACAK,EAAKC,aAAAA,EACLD,EAAKE,aAAAA,GAGT,IAAMC,EAAkBzD,SAASC,gBACjC,6BACA,OAEAwD,EAAgBjD,aAAa,QAAS,8BACtCiD,EAAgBjD,aAAa,QAAS0C,EAAYzC,MAAMiD,QAAQC,eAChEF,EAAgBjD,aACd,SACA0C,EAAYxC,OAAOgD,QAAQC,eAE7BF,EAAgBjD,aACd,UACA,OACE0C,EAAYU,QAAQF,QAAQjD,MAAMG,WAClC,IACAsC,EAAYU,QAAQF,QAAQhD,OAAOE,YAEvC6C,EAAgBI,UAAYX,EAAYW,WAAAA,IAEpCP,EAAKE,aAELC,EAAgBhD,MAAMiD,QAAQrG,MAAQ4F,EAAOa,aAC7CL,EAAgB/C,OAAOgD,QAAQrG,MAAQ4F,EAAOc,oBAAAA,IACvCT,EAAK7C,YAAAA,IAAuB6C,EAAK5C,SAExC+C,EAAgBhD,MAAMiD,QAAQrG,MAAQiG,EAAK7C,MAC3CgD,EAAgB/C,OAAOgD,QAAQrG,MAAQiG,EAAK5C,QAGhD0C,EAAO3C,MAAQgD,EAAgBhD,MAAMiD,QAAQrG,MAC7C+F,EAAO1C,OAAS+C,EAAgB/C,OAAOgD,QAAQrG,MAE/C,IAAM2G,EAAOP,EAAgBQ,UAEvBC,EAAMd,EAAOe,WAAW,OAAM,IAChCb,EAAKC,aACLW,EAAIE,UAAUnB,EAAQ,EAAG,EAAGG,EAAO3C,MAAO2C,EAAO1C,QAGrD,IAAM2D,EAASC,OAAOC,IAEhBC,EAAM,IAAIC,MAAMrB,EAAO3C,MAAO2C,EAAO1C,QAC3C8D,EAAIhE,aAAa,cAAe,aAEhC,IAAMkE,EAAO,IAAIC,KAAK,CAACX,GAAO,CAAEY,KAAM,kBAEhCC,EAAMR,EAAOS,gBAAgBJ,GAEnCF,EAAIO,OAAS,WACTb,EAAIE,UAAUI,EAAK,EAAG,GACtBH,EAAOW,gBAAgBH,GAEvB,IAAMnH,EAAS0F,EAAO6B,UAAU3B,EAAK4B,UAAW5B,EAAK6B,cACrDjI,EAAQQ,EAAAA,EAGZ8G,EAAIY,IAAMP,CAAAA,GAAAA,EAAAA,CAAAA,CD3FehC,GC2FfgC,EC7GtB,0BAoGE,WAAYQ,GArFJ5I,KAAAA,qBAAuB,eAiBvBA,KAAAA,QAAwB,GACxBA,KAAAA,MAAqB,GAgDtBA,KAAAA,SAA2BA,KAAK6I,gBAoBrC7I,KAAK8I,iBAAsB9I,KAAK+I,qBAAAA,IAAwBH,EAAAA,GAAAA,CAiG5D,OAlLEtJ,OAAAA,eAAW0J,EAAAA,UAAAA,sBAAAA,CAAAA,IAAX,WACE,OAAOhJ,KAAK+I,oBAAoB,EAApBA,YAAAA,EAAAA,cAAAA,IAOdzJ,OAAAA,eAAW0J,EAAAA,UAAAA,kBAAAA,CAAAA,IAAX,WACE,OAAOhJ,KAAK8I,gBAAgB,EAAhBA,YAAAA,EAAAA,cAAAA,IA6BdxJ,OAAAA,eAAW0J,EAAAA,UAAAA,kBAAAA,CAAAA,IAAX,WACE,MAAO,CACLC,sBAAuB,UACvBC,uBAAwB,UACxBC,4BAA6B,UAC7BC,aAAc,UACdC,cAAe,GAEfC,aAAc,UACdC,mBAAoB,UACpBC,mBAAkB,EAClBC,mBAAkB,EAClBC,mBAAkB,EAClBC,sBAAqB,EACrBC,oBAAmB,EACnBC,0BAAyB,EACzBC,aAAc,yCAYlBxK,OAAAA,eAAW0J,EAAAA,UAAAA,2BAAAA,CAAAA,IAAX,WACE,OAAUhJ,KAAK+J,gBAAAA,SAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAKjBzK,OAAAA,eAAW0J,EAAAA,UAAAA,4BAAAA,CAAAA,IAAX,WACE,OAAUhJ,KAAK+J,gBAAAA,UAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAeVf,EAAAA,UAAAA,SAAP,SAAgBgB,GAUd,YAAO,IATHhK,KAAKiK,YACPjK,KAAKkK,gBAEPF,EAAWG,KAAO,GAAGnK,KAAK+J,gBAAkBC,EAAWI,UACvDpK,KAAKqK,QAAQ1H,KAAKqH,GAClBhK,KAAKiK,WAAWK,MAAMC,WACpB,IAAIP,EAAWG,KAAAA,KAASH,EAAWQ,MAAAA,IACnCxK,KAAKiK,WAAWK,MAAMG,SAAS/H,QAE1BsH,CAAAA,EAOFhB,EAAAA,UAAAA,QAAP,SAAe0B,QAAAA,IACT1K,KAAKiK,YACPjK,KAAKkK,gBAEPlK,KAAK2K,MAAMhI,KAAK+H,GAChB1K,KAAKiK,WAAWK,MAAMC,WACjBG,EAAUE,SAAAA,KAAaF,EAAUF,MAAAA,IACpCxK,KAAKiK,WAAWK,MAAMG,SAAS/H,OAAAA,EAI3BsG,EAAAA,UAAAA,cAAR,iBACEhJ,KAAKiK,WAAa1G,SAASqD,cAAc,oBACxC5G,KAAK6K,sBAAAA,IAAAA,EAAAA,EAAkBtH,SAASuH,MAAMpF,YAAY1F,KAAKiK,YAGxDjK,KAAK+K,QACH,IAAIC,EAAU,IAAIhL,KAAK+J,gBAAAA,MAAsB,4BAG/C/J,KAAK+K,QACH,IAAIC,EACF,cAAchL,KAAK+J,gBAAAA,4BACnB,6GAUJ/J,KAAK+K,QACH,IAAIC,EACF,cAAchL,KAAK+J,gBAAAA,6BACnB,6GAWJ/J,KAAKiL,SACH,IAAIC,EACF,UACA,4DAEgBlL,KAAK+J,gBAAAA,qCAIzB/J,KAAKiL,SACH,IAAIC,EACF,WACA,4DAEgBlL,KAAK+J,gBAAAA,qCAAAA,EAMpBf,EAAAA,UAAAA,iBAAP,iBACMhJ,KAAKiK,cAAAA,QAAAA,EACNjK,KAAK6K,sBAAAA,IAAAA,EAAAA,EAAkBtH,SAASuH,MAAMK,YAAYnL,KAAKiK,YACxDjK,KAAKiK,gBAAAA,EAAamB,EAAAA,CAAAA,CAnMxB,GAmMwBA,EAsBtB,SAAYR,EAAkBJ,GAC5BxK,KAAK4K,SAAWA,EAChB5K,KAAKwK,MAAQA,CAAAA,EAAAA,EA4Bf,SAAYL,EAAcK,GACxBxK,KAAKoK,UAAYD,EACjBnK,KAAKwK,MAAQA,CAAAA,EAAAA,EAAAA,WCtLf,WACEa,EACAC,EACAC,EACAC,EACAC,GAxCMzL,KAAAA,QAA4B,GAC5BA,KAAAA,cAAkC,GAmBlCA,KAAAA,qBAAoD,GAsB1DA,KAAKqL,kBAAoBA,EACzBrL,KAAKsL,YAAcA,EACnBtL,KAAKuL,YAAcA,EACnBvL,KAAKwL,gBAAkBA,EACvBxL,KAAKyL,OAASA,EACdzL,KAAK0L,YAEL1L,KAAK2L,aAAe3L,KAAK2L,aAAaC,KAAK5L,MAC3CA,KAAK6L,sBAAwB7L,KAAK6L,sBAAsBD,KAAK5L,MAC7DA,KAAK8L,iBAAmB9L,KAAK8L,iBAAiBF,KAAK5L,KAAK,CAgd5D,OA1cS+L,EAAAA,UAAAA,KAAP,SAAYC,GAAZ,WACEhM,KAAKiM,YAAc1I,SAASqD,cAAc,OAC1C5G,KAAKiM,YAAYzB,MAAM0B,WAAaF,EACpChM,KAAKiM,YAAYE,UAAenM,KAAKoM,kBAAkBjC,KAAAA,IACrDnK,KAAKyL,OAAOY,yBAAAA,KAEZrM,KAAKwL,gBAAgBc,4BACjBtM,KAAKwL,gBAAgBc,4BACrBtM,KAAKuM,wBAAwBpC,MAGnC,IAAMqC,EAAoBjJ,SAASqD,cAAc,OACjD4F,EAAkBL,UAAYnM,KAAKyM,uBAAuBtC,KAC1DqC,EAAkBhC,MAAMkC,WAAa,SACrC1M,KAAKiM,YAAYvG,YAAY8G,GAE7BxM,KAAK2M,gBAAgBH,EAAAA,wYAA+B,SAAU,eAC9DxM,KAAK2M,gBAAgBH,EAAAA,6IAA+B,SAAU,iBAC1DxM,KAAKwL,gBAAgB5B,oBACvB5J,KAAK2M,gBAAgBH,EAAAA,4PAA8B,QAAS,sBAE1DxM,KAAKwL,gBAAgBhC,mBACvBxJ,KAAK2M,gBAAgBH,EAAAA,2LAA6B,OAAQ,QAExDxM,KAAKwL,gBAAgB/B,mBACvBzJ,KAAK2M,gBAAgBH,EAAAA,uLAA6B,OAAQ,QAExDxM,KAAKwL,gBAAgB9B,mBACvB1J,KAAK2M,gBAAgBH,EAAAA,iSAA6B,OAAQ,WAG1DxM,KAAKwL,gBAAgB9B,mBACrB1J,KAAKwL,gBAAgB7B,sBAErB3J,KAAK2M,gBAAgBH,EAAAA,4QAAgC,WAAY,YAE/DxM,KAAKwL,gBAAgBoB,oBACvB5M,KAAK2M,gBAAgBH,EAAAA,mTAA8B,QAAS,SAG9DxM,KAAK6M,kBAAoBtJ,SAASqD,cAAc,OAChD5G,KAAK6M,kBAAkBV,UAAYnM,KAAKyM,uBAAuBtC,KAC/DnK,KAAK6M,kBAAkBrC,MAAMsC,SAAW,IACxC9M,KAAK6M,kBAAkBrC,MAAMuC,UAAY,SACzC/M,KAAKiM,YAAYvG,YAAY1F,KAAK6M,mBAElC7M,KAAKgN,0BAA4BzJ,SAASqD,cAAc,OACxD5G,KAAKgN,0BAA0Bb,UAC7BnM,KAAKiN,+BAA+B9C,KAAAA,KAEpCnK,KAAKwL,gBAAgB0B,yCACjBlN,KAAKwL,gBAAgB0B,yCACrBlN,KAAKmN,qCAAqChD,MAEhDnK,KAAKgN,0BAA0BxC,MAAM4C,QAAU,OAC/CpN,KAAKiM,YAAYvG,YAAY1F,KAAKgN,2BAE9BhN,KAAKuL,cACPvL,KAAKuL,YAAY8B,SAAQ,SAACC,GACxB,IAAMC,EAAkBhK,SAASqD,cAAc,OAC/C2G,EAAgBpB,UAAY,GAAGtF,EAAK2G,wBAAwBrD,KAC5DoD,EAAgBxJ,aAAa,iBAAkBuJ,EAAGG,UAClDF,EAAgBxJ,aAAa,aAAcuJ,EAAGI,OAC9CH,EAAgBxJ,aAAa,QAASuJ,EAAGI,OAIzCH,EAAgBnG,UAAYkG,EAAGK,KAC/BJ,EAAgBK,iBAAiB,oBAC/B/G,EAAKgH,2BAA2BN,EAAiBD,EAAAA,IAGnDzG,EAAKiH,QAAQnL,KAAK4K,GAClB1G,EAAKkH,cAAcpL,KAAK4K,EAAAA,IAE1BvN,KAAKgO,eAAiBzK,SAASqD,cAAc,OAC7C5G,KAAKgO,eAAe7B,UAAenM,KAAKwN,wBAAwBrD,KAAAA,KAC9DnK,KAAKwL,gBAAgByC,kCACjBjO,KAAKwL,gBAAgByC,kCACrBjO,KAAKkO,8BAA8B/D,MAEzCnK,KAAKgO,eAAe5G,UAAAA,mNACpBpH,KAAKgO,eAAeJ,iBAAiB,QAAS5N,KAAK6L,uBACnD7L,KAAK6M,kBAAkBnH,YAAY1F,KAAKgO,iBAG1C,IAAMG,EAAoB5K,SAASqD,cAAc,OACjDuH,EAAkBhC,UAAYnM,KAAKyM,uBAAuBtC,KAC1DgE,EAAkB3D,MAAMkC,WAAa,SACrCyB,EAAkB3D,MAAM4C,SAAAA,IACtBpN,KAAKwL,gBAAgB3B,yBAAqC,GAAK,OACjE7J,KAAKiM,YAAYvG,YAAYyI,GAE7BnO,KAAK2M,gBAAgBwB,EAAAA,gHAA8B,SAAU,kBAC7DnO,KAAK2M,gBAAgBwB,EAAAA,yJAA8B,QAAS,SAE5DnO,KAAKqL,kBAAkB3F,YAAY1F,KAAKiM,aACxCjM,KAAKoO,gBAELpO,KAAK8L,mBAEL9L,KAAK2L,cAAc,EAQdI,EAAAA,UAAAA,uBAAP,SAA8BsC,GAC5BrO,KAAKsO,qBAAqB3L,KAAK0L,EAAAA,EAO1BtC,EAAAA,UAAAA,0BAAP,SAAiCsC,GAC3BrO,KAAKsO,qBAAqBC,QAAQF,IAAa,GACjDrO,KAAKsO,qBAAqBE,OACxBxO,KAAKsO,qBAAqBC,QAAQF,GAClC,IAQCtC,EAAAA,UAAAA,cAAP,WACE/L,KAAKyO,oBACLzO,KAAK0O,gBAAgB1O,KAAK8N,QAAQ,GAAG,EAMhC/B,EAAAA,UAAAA,aAAP,WACE,GAAI/L,KAAK+N,eAAiB/N,KAAK+N,cAAcrL,OAAS,EAAG,CACvD,IAAMiM,EACJC,KAAKC,MACH7O,KAAK6M,kBAAkBiC,YACrB9O,KAAKwL,gBAAgBnC,eACrB,EACNrJ,KAAK6M,kBAAkBzF,UAAY,GACnCpH,KAAKgN,0BAA0B5F,UAAY,GAC3C,IACE,IAAI2H,EAAc,EAClBA,EAAc/O,KAAK+N,cAAcrL,OACjCqM,IAGEA,EAAcJ,GACbI,IAAgBJ,GACf3O,KAAK+N,cAAcrL,OAAS,IAAMiM,EAEpC3O,KAAK6M,kBAAkBnH,YAAY1F,KAAK+N,cAAcgB,KAElDA,IAAgBJ,GAClB3O,KAAK6M,kBAAkBnH,YAAY1F,KAAKgO,gBAE1ChO,KAAKgN,0BAA0BtH,YAC7B1F,KAAK+N,cAAcgB,IAAAA,CAAAA,EAOrBhD,EAAAA,UAAAA,sBAAR,WACuD,SAAjD/L,KAAKgN,0BAA0BxC,MAAM4C,SACvCpN,KAAKgN,0BAA0Bb,UAAYnM,KAAKgN,0BAA0Bb,UAAU6C,QAClFhP,KAAKyL,OAAOY,yBACZ,IAEFrM,KAAKgN,0BAA0BxC,MAAM4C,QAAU,SAE/CpN,KAAKgN,0BAA0Bb,WAAa,IAAInM,KAAKyL,OAAOY,yBAC5DrM,KAAKgN,0BAA0BxC,MAAMyE,IACnCjP,KAAKiM,YAAYiD,UAAYlP,KAAKgO,eAAemB,aAAAA,KAEnDnP,KAAKgN,0BAA0BxC,MAAM4E,MACnCpP,KAAKiM,YAAYoD,YACjBrP,KAAKgO,eAAesB,WACpBtP,KAAKgO,eAAeqB,YACU,EAA9BrP,KAAKiM,YAAYqD,WAAAA,KAEnBtP,KAAKgN,0BAA0BxC,MAAM4C,QAAU,iBAI3CrB,EAAAA,UAAAA,kBAAR,sBACE/L,KAAK8N,QAAQT,SAAQ,SAACkC,GACpBA,EAAOpD,UAAYoD,EAAOpD,UACvB6C,QACCnI,EAAK2E,gBAAgByC,kCACjBpH,EAAK2E,gBAAgByC,kCACrBpH,EAAKqH,8BAA8B/D,KACvC,IAEDqF,OACHD,EAAOpD,UAAYoD,EAAOpD,UACvB6C,QACCnI,EAAK2E,gBAAgBiE,wCACjB5I,EAAK2E,gBAAgBiE,wCACrB5I,EAAK6I,oCAAoCvF,KAC7C,IAEDqF,OACHD,EAAOpD,WAAa,KAClBtF,EAAK2E,gBAAgByC,kCACjBpH,EAAK2E,gBAAgByC,kCACrBpH,EAAKqH,8BAA8B/D,KAAAA,GAAAA,EAKrC4B,EAAAA,UAAAA,gBAAR,SACE4D,EACAhC,EACA/M,EACA8M,GAJF,WAMQkC,EAAerM,SAASqD,cAAc,OAY5C,OAXAgJ,EAAazD,UAAY,GAAGnM,KAAKwN,wBAAwBrD,KAIzDyF,EAAaxI,UAAYuG,EACzBiC,EAAa7L,aAAa,cAAenD,GACzCgP,EAAalC,MAAQA,EACrBkC,EAAa7L,aAAa,aAAc2J,GACxCkC,EAAahC,iBAAiB,oBAC5B/G,EAAKgJ,2BAA2BD,EAAchP,EAAAA,IAExCA,GACN,IAAK,SAOL,IAAK,OAGL,IAAK,OACHgP,EAAapF,MAAMsF,KAAO9P,KAAKwL,gBAAgBuE,kBAC/C,MATF,IAAK,SACL,IAAK,QACHH,EAAapF,MAAMsF,KAAO9P,KAAKwL,gBAAgBwE,kBAC/C,MAOF,IAAK,SACHJ,EAAapF,MAAMsF,KAAO9P,KAAKwL,gBAAgByE,cAC/C,MACF,IAAK,QACHL,EAAapF,MAAMsF,KAAO9P,KAAKwL,gBAAgB0E,iBAInDP,EAAUjK,YAAYkK,GACtB5P,KAAK8N,QAAQnL,KAAKiN,EAAAA,EAGZ7D,EAAAA,UAAAA,UAAR,WACE/L,KAAKoM,kBAAoBpM,KAAKyL,OAAOR,SACnC,IAAIC,EACF,UACA,6JAMQlL,KAAKwL,gBAAgBnC,cAAAA,+CAGR,WAArBrJ,KAAKsL,YACD,2BAA2BsD,KAAKuB,MAC9BnQ,KAAKwL,gBAAgBnC,cAAgB,UAEvC,gBAGiB,WAArBrJ,KAAKsL,YACD,4BAA4BsD,KAAKuB,MAC/BnQ,KAAKwL,gBAAgBnC,cAAgB,UAEvC,wCAORrJ,KAAKuM,wBAA0BvM,KAAKyL,OAAOR,SACzC,IAAIC,EACF,iBACA,6BACkBlL,KAAKwL,gBAAgBtC,uBAAAA,8DAM3ClJ,KAAKyM,uBAAyBzM,KAAKyL,OAAOR,SACxC,IAAIC,EACF,gBACA,6EAOJlL,KAAKiN,+BAAiCjN,KAAKyL,OAAOR,SAChD,IAAIC,EACF,yBACA,+CAEOlL,KAAKwL,gBAAgBnC,cAAAA,2BACsB,EAArCrJ,KAAKwL,gBAAgBnC,cAAAA,wEAMtCrJ,KAAKmN,qCAAuCnN,KAAKyL,OAAOR,SACtD,IAAIC,EACF,gCACA,+BACoBlL,KAAKwL,gBAAgBtC,uBAAAA,cAK7C,IAAMkH,EAAgBpQ,KAAKwL,gBAAgBnC,cAAgB,EAC3DrJ,KAAKwN,wBAA0BxN,KAAKyL,OAAOR,SACzC,IAAIC,EACF,iBACA,iDAEOlL,KAAKwL,gBAAgBnC,cAAgC,EAAhB+G,GAAAA,uBACpCpQ,KAAKwL,gBAAgBnC,cAAgC,EAAhB+G,GAAAA,uBACpCA,EAAAA,8CAKbpQ,KAAKkO,8BAAgClO,KAAKyL,OAAOR,SAC/C,IAAIC,EACF,wBACA,iBACMlL,KAAKwL,gBAAgBpC,aAAAA,YAK/BpJ,KAAK0P,oCAAsC1P,KAAKyL,OAAOR,SACrD,IAAIC,EACF,wBACA,iBACMlL,KAAKwL,gBAAgBpC,aAAAA,8BACTpJ,KAAKwL,gBAAgBrC,4BAAAA,WAK3CnJ,KAAKyL,OAAOV,QACV,IAAIC,EACF,IAAIhL,KAAKwN,wBAAwBrD,KAAAA,OACjC,mBACQnK,KAAKwL,gBAAgBnC,cAAgB,gBAKjDrJ,KAAKyL,OAAOV,QACV,IAAIC,EACF,IAAIhL,KAAKkO,8BAA8B/D,KAAAA,SACvC,+BACoBnK,KAAKwL,gBAAgBrC,4BAAAA,UAAAA,EAMvC4C,EAAAA,UAAAA,2BAAR,SACEwD,EACAc,GAEArQ,KAAK0O,gBAAgBa,GACjBvP,KAAKsO,sBAAwBtO,KAAKsO,qBAAqB5L,OAAS,GAClE1C,KAAKsO,qBAAqBjB,SAAQ,SAACgB,GACjC,OAAAA,EAAS,SAAUgC,EAAAA,IAGvBrQ,KAAKgN,0BAA0BxC,MAAM4C,QAAU,QAGzCrB,EAAAA,UAAAA,2BAAR,SAAmCwD,EAAwBe,GACrDtQ,KAAKsO,sBAAwBtO,KAAKsO,qBAAqB5L,OAAS,GAClE1C,KAAKsO,qBAAqBjB,SAAQ,SAACgB,GACjC,OAAAA,EAAS,SAAUiC,EAAAA,IAGvBtQ,KAAKgN,0BAA0BxC,MAAM4C,QAAU,OAC/CpN,KAAK0O,gBAAgB1O,KAAK8N,QAAQ,GAAG,EAG/B/B,EAAAA,UAAAA,gBAAR,SAAwBwD,GACtBvP,KAAKyO,oBACLc,EAAOpD,UAAYoD,EAAOpD,UACvB6C,QACChP,KAAKwL,gBAAgByC,kCACjBjO,KAAKwL,gBAAgByC,kCACrBjO,KAAKkO,8BAA8B/D,KACvC,IAEDqF,OACHD,EAAOpD,WAAa,KAClBnM,KAAKwL,gBAAgBiE,wCACjBzP,KAAKwL,gBAAgBiE,wCACrBzP,KAAK0P,oCAAoCvF,KAAAA,EAU1C4B,EAAAA,UAAAA,sBAAP,SAA6B0B,GAC3B,IAAM8C,EAAYvQ,KAAK+N,cAAcyC,MAAK,SACvCC,GAAQ,OAAAA,EAAIC,aAAa,oBAAsBjD,CAAAA,IAE9C8C,GACFvQ,KAAK0O,gBAAgB6B,EAAAA,EAQlBxE,EAAAA,UAAAA,iBAAP,SAAwBtG,GAAxB,WACEzF,KAAK2Q,cAAgBlL,EACOzF,KAAK8N,QAAQ8C,QAAO,SAACH,GAC/C,qBAAenK,KAAKmK,EAAIC,aAAa,mBAEnBrD,SAAQ,SAACoD,QAAAA,IACvB5J,EAAK8J,eACPF,EAAIjG,MAAMqG,YAAc,MACxBJ,EAAIjG,MAAMsG,cAAgB,SAE1BL,EAAIjG,MAAMqG,YAAc,IACxBJ,EAAIjG,MAAMsG,cAAgB,aDtSjBtG,GCsSiB,aCxahC,WAAYa,EAAmCC,EAA0BE,EAAiCC,GApHlGzL,KAAAA,OAAyB,GAEzBA,KAAAA,aAAiC,GAmHvCA,KAAKqL,kBAAoBA,EACzBrL,KAAKsL,YAAcA,EACnBtL,KAAKwL,gBAAkBA,EACvBxL,KAAKyL,OAASA,EAEdzL,KAAK+Q,iBAAmB/Q,KAAK+Q,iBAAiBnF,KAAK5L,MAEnDA,KAAK0L,WAAW,CA8FpB,OAnMUsF,EAAAA,UAAAA,UAAR,iBACEhR,KAAKiR,kBAAoBjR,KAAKyL,OAAOR,SACnC,IAAIC,EACF,UACA,4IAMqB,UAArBlL,KAAKsL,YAA0B,UAAiD,IAArCtL,KAAKwL,gBAAgBnC,cAAsB,MAAQ,gDAEzE,UAArBrJ,KAAKsL,YAA0B,qBAAqBtL,KAAKwL,gBAAgBvC,sBAAAA,IAA2B,gBAC/E,WAArBjJ,KAAKsL,YAA2B,8BAA8BsD,KAAKuB,MAAMnQ,KAAKwL,gBAAgBnC,cAAc,UAAW,gBAClG,WAArBrJ,KAAKsL,YAA2B,+BAA+BsD,KAAKuB,MAAMnQ,KAAKwL,gBAAgBnC,cAAc,UAAW,wCAK5HrJ,KAAKkR,wBAA0BlR,KAAKyL,OAAOR,SACzC,IAAIC,EACF,iBACA,kBACOlL,KAAKwL,gBAAgBlC,aAAAA,YAKhC,IAAM8G,EAAgBpQ,KAAKwL,gBAAgBnC,cAAgB,EAC3DrJ,KAAKmR,2BAA6BnR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,qBAAsB,yFAK5FlL,KAAKoR,iCAAmCpR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,4BAA6B,6BACnFlL,KAAKwL,gBAAgBtC,uBAAAA,YAG3ClJ,KAAKqR,0BAA4BrR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,oBAAqB,kCAEjE,WAArBlL,KAAKsL,YAA2B,sBAAwB,gBACnC,WAArBtL,KAAKsL,YAA2B,WAAatL,KAAKwL,gBAAgBnC,cAAgB,MAAQ,+CAE7C,IAArCrJ,KAAKwL,gBAAgBnC,cAAAA,eACR,WAArBrJ,KAAKsL,YAA2B,eAAiB,+CAGrDtL,KAAKsR,gCAAkCtR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,2BAA4B,wCACjFlL,KAAKwL,gBAAgB+F,8BAAAA,IAAAA,EAAAA,EAA0BvR,KAAKwL,gBAAgBrC,6BAAAA,YAG1FnJ,KAAKwR,wBAA0BxR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,iBAAkB,iDAE1ElL,KAAKwL,gBAAgBnC,cAAgC,EAAhB+G,GAAAA,uBACpCpQ,KAAKwL,gBAAgBnC,cAAgC,EAAhB+G,GAAAA,uBACpCA,EAAAA,8CAGbpQ,KAAKyR,8BAAgCzR,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,wBAAyB,iBACxFlL,KAAKwL,gBAAgBpC,aAAAA,YAG/BpJ,KAAK0R,oCAAsC1R,KAAKyL,OAAOR,SAAS,IAAIC,EAAW,+BAAgC,6BACzFlL,KAAKwL,gBAAgBrC,4BAAAA,kBACjCnJ,KAAKwL,gBAAgBpC,aAAAA,YAG/BpJ,KAAKyL,OAAOV,QACV,IAAIC,EACF,IAAIhL,KAAKyR,8BAA8BtH,KAAAA,SACvC,+BACoBnK,KAAKwL,gBAAgBrC,4BAAAA,WAK7CnJ,KAAKyL,OAAOV,QACV,IAAIC,EACF,IAAIhL,KAAKwR,wBAAwBrH,KAAAA,OACjC,mBACQnK,KAAKwL,gBAAgBnC,cAAgB,iBA2B5C2H,EAAAA,UAAAA,KAAP,SAAYhF,GAAAA,IAAAA,EACVhM,KAAKiM,YAAc1I,SAASqD,cAAc,OAC1C5G,KAAKiM,YAAYzB,MAAM0B,WAAaF,EACpChM,KAAKiM,YAAYE,UAAenM,KAAKiR,kBAAkB9G,KAAAA,KAAAA,QAAAA,EACrDnK,KAAKwL,gBAAgBmG,mCAAAA,IAAAA,EAAAA,EAA+B3R,KAAKkR,wBAAwB/G,MAEnFnK,KAAKqL,kBAAkB3F,YAAY1F,KAAKiM,YAAY,EAO/C+E,EAAAA,UAAAA,gBAAP,SAAuBY,GAAvB,eACE5R,KAAK4R,OAASA,OAAAA,IACV5R,KAAKiM,cACPjM,KAAKiM,YAAY7E,UAAY,GAE7BpH,KAAK6R,SAAWtO,SAASqD,cAAc,OACvC5G,KAAK6R,SAAS1F,UAAenM,KAAKqR,0BAA0BlH,KAAAA,KAAAA,QAAAA,EAC1DnK,KAAKwL,gBAAgBsG,2CAAAA,IAAAA,EAAAA,EAAuC9R,KAAKsR,gCAAgCnH,MACnGnK,KAAKiM,YAAYvG,YAAY1F,KAAK6R,UAClC7R,KAAK+R,UAAYxO,SAASqD,cAAc,OACxC5G,KAAK+R,UAAU5F,UAAenM,KAAKmR,2BAA2BhH,KAAAA,KAAAA,QAAAA,EAC5DnK,KAAKwL,gBAAgBwG,4CAAAA,IAAAA,EAAAA,EAAwChS,KAAKoR,iCAAiCjH,MAAAA,IACrGnK,KAAKiM,YAAYvG,YAAY1F,KAAK+R,WAElC/R,KAAKiS,aAAazD,OAAO,GAEzBxO,KAAK4R,OAAOvE,SAAQ,SAAA6E,GAAAA,IAAAA,EAClBA,EAAM1G,gBAAkB3E,EAAK2E,gBAC7B,IAAM2G,EAAc5O,SAASqD,cAAc,OAC3CuL,EAAYhG,UAAetF,EAAK2K,wBAAwBrH,KAAAA,KAAAA,QAAAA,EACtDtD,EAAK2E,gBAAgB4G,yCAAAA,IAAAA,EAAAA,EAAqCvL,EAAK4K,8BAA8BtH,MAC/FgI,EAAY/K,UAAY8K,EAAMvE,KAC9BwE,EAAYzE,MAAQwE,EAAMxE,MAC1ByE,EAAYvE,iBAAiB,oBAC3B/G,EAAKkK,iBAAiBmB,EAAAA,IAExBrL,EAAKoL,aAAatP,KAAKwP,GACvBtL,EAAKkL,UAAUrM,YAAYyM,EAAAA,IAEJ,WAArBnS,KAAKsL,YACPtL,KAAK6R,SAASrH,MAAM4C,QAAU,OAE9BpN,KAAK6R,SAASrH,MAAM0B,WAAa,WAS/B8E,EAAAA,UAAAA,iBAAR,SAAyBkB,GAAzB,WACMG,GAAc,EAClB,GAAIH,IAAUlS,KAAKsS,YAAa,CAC9BD,EAAarS,KAAK4R,OAAOrD,QAAQ2D,GACjClS,KAAK6R,SAASzK,UAAY,GAC1B,IAAMmL,EAAUL,EAAMM,QACtBD,EAAQ/H,MAAMiI,OAAYzS,KAAKwL,gBAAgBnC,cAAgB,OAC/DrJ,KAAK6R,SAASnM,YAAY6M,GAC1BvS,KAAK6R,SAASrH,MAAM4C,QAAU,OAC9BpN,KAAK6R,SAASrH,MAAM0B,WAAa,UACjClM,KAAK6R,SAAS1F,UAAYnM,KAAK6R,SAAS1F,UAAU6C,QAAQhP,KAAKyL,OAAOiH,0BAA2B,IACjG1S,KAAK6R,SAAS1F,WAAa,IAAInM,KAAKyL,OAAOY,yBAC3CrM,KAAKsS,YAAcJ,CAAAA,MAEnBlS,KAAKsS,iBAAAA,EAELtS,KAAK6R,SAAS1F,UAAYnM,KAAK6R,SAAS1F,UAAU6C,QAAQhP,KAAKyL,OAAOY,yBAA0B,IAChGrM,KAAK6R,SAAS1F,WAAa,IAAInM,KAAKyL,OAAOiH,0BAC3CC,YAAW,WACgB,WAArB9L,EAAKyE,YACPzE,EAAKgL,SAASrH,MAAM4C,QAAU,OAE9BvG,EAAKgL,SAASrH,MAAM0B,WAAa,WAElC,KAELlM,KAAKiS,aAAa5E,SAAQ,SAACuF,EAAIC,GAAAA,IAAAA,EAAAA,EAC7BD,EAAGzG,UAAetF,EAAK2K,wBAAwBrH,KAAAA,KAC5C0I,IAAUR,EACP,cAAGxL,EAAK2E,gBAAgBsH,+CAAAA,IAAAA,EAAAA,EAA2CjM,EAAK6K,oCAAoCvH,MAC3G,cAAGtD,EAAK2E,gBAAgB4G,yCAAAA,IAAAA,EAAAA,EAAqCvL,EAAK4K,8BAA8BtH,MAAAA,GAAAA,EAAAA,CAAAA,CDsU3E,GCtU2EA,ECtM3G,SAAYuD,EAAeC,GACzB3N,KAAK0N,MAAQA,EACb1N,KAAK2N,KAAOA,CAAAA,EAAAA,EAAAA,SAAAA,GCGd,WAAYD,EAAeqF,EAAkBC,EAAuBrF,GAApE,MACEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,0gBAAAA,KAAAA,OAnBR9G,EAAAA,OAAmB,GAElBA,EAAAA,gBAAAA,EAEAA,EAAAA,WAA+B,GAgBrCA,EAAKkM,OAASA,EACdlM,EAAKmM,aAAeA,EAEpBnM,EAAKqM,gBAAkBrM,EAAKqM,gBAAgBtH,KAAK/E,GACjDA,EAAKsM,YAActM,EAAKsM,YAAYvH,KAAK/E,GAAAA,CAAAA,CAqE7C,OA9FsC/G,EAAAA,EAAAA,GA+B7BsT,EAAAA,UAAAA,MAAP,sBACQC,EAAW9P,SAASqD,cAAc,OAQxC,OAPAyM,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMkC,WAAa,SAC5B1M,KAAK+S,OAAO1F,SAAQ,SAACkG,GACnB,IAAMC,EAAoB3M,EAAKsM,YAAYI,GAC3CF,EAAS3N,YAAY8N,GACrB3M,EAAK4M,WAAW9Q,KAAK6Q,EAAAA,IAEhBH,CAAAA,EAGDD,EAAAA,UAAAA,YAAR,SAAoBG,GAApB,WACQnD,EAAgBpQ,KAAKwL,gBAAgBnC,cAAgB,EACrDqK,EAAe1T,KAAKwL,gBAAgBnC,cAAgB+G,EAEpDoD,EAAoBjQ,SAASqD,cAAc,OACjD4M,EAAkBhJ,MAAM4C,QAAU,eAClCoG,EAAkBhJ,MAAMmJ,UAAY,cACpCH,EAAkBhJ,MAAMxG,MAAW0P,EAAe,OAClDF,EAAkBhJ,MAAMvG,OAAYyP,EAAe,OACnDF,EAAkBhJ,MAAMoJ,QAAU,MAClCJ,EAAkBhJ,MAAMqJ,YAAc,MACtCL,EAAkBhJ,MAAMsJ,aAAe,MACvCN,EAAkBhJ,MAAMuJ,YAAc,MACtCP,EAAkBhJ,MAAMwJ,YAAc,QACtCR,EAAkBhJ,MAAMyJ,cAAmBP,EAAe,GAAG,OAC7DF,EAAkBhJ,MAAM0J,YACtBX,IAAUvT,KAAKgT,aAAehT,KAAKwL,gBAAgBjC,mBAAqB,cAE1EiK,EAAkB5F,iBAAiB,oBACjC/G,EAAKqM,gBAAgBK,EAAOC,EAAAA,IAG9B,IAAMW,EAAW5Q,SAASqD,cAAc,OAexC,OAdAuN,EAAS3J,MAAM4C,QAAU,eACzB+G,EAAS3J,MAAMxG,MAAW0P,EAAe,OACzCS,EAAS3J,MAAMvG,OAAYyP,EAAe,OAC1CS,EAAS3J,MAAM4J,gBAAkBb,EACjCY,EAAS3J,MAAMyJ,aAAkBP,EAAa,OAChC,gBAAVH,IACFY,EAAS3J,MAAMsF,KAAO9P,KAAKwL,gBAAgBjC,mBAC3C4K,EAAS/M,UAAY,unBAKvBoM,EAAkB9N,YAAYyO,GAEvBX,CAAAA,EAGDJ,EAAAA,UAAAA,gBAAR,SAAwBG,EAAe/M,GAAvC,WACExG,KAAKgT,aAAeO,EAEpBvT,KAAKyT,WAAWpG,SAAQ,SAAAgH,GACtBA,EAAI7J,MAAM0J,YAAcG,IAAQ7N,EAASK,EAAK2E,gBAAgBjC,mBAAqB,iBAGjFvJ,KAAKsU,gBACPtU,KAAKsU,eAAef,EAAAA,EAAAA,CAAAA,CD3EV5F,CChBsB4G,GAAAA,EAAAA,WCmGpC,WAAY5E,EAAwB6E,EAAkCC,GAnE5DzU,KAAAA,OAAsB,MA4FtBA,KAAAA,aAAAA,EAxBRA,KAAK0U,WAAa/E,EAClB3P,KAAK2U,kBAAoBH,EACzBxU,KAAK4U,eAAiBH,EAEtBzU,KAAK6U,aAAe7U,KAAK6U,aAAajJ,KAAK5L,MAC3CA,KAAK8U,aAAe9U,KAAK8U,aAAalJ,KAAK5L,MAC3CA,KAAK+U,iBAAmB/U,KAAK+U,iBAAiBnJ,KAAK5L,KAAK,CAuK5D,OArQEV,OAAAA,eAAW0V,EAAAA,UAAAA,WAAAA,CAAAA,IAAX,WACE,OAAO1V,OAAO2V,eAAejV,MAAMC,YAAYwN,QAAQ,EAARA,YAAAA,EAAAA,cAAAA,IAOjDnO,OAAAA,eAAW0V,EAAAA,UAAAA,YAAAA,CAAAA,IAAX,WACE,OAAOhV,KAAK0U,UAAU,EAAVA,YAAAA,EAAAA,cAAAA,IAQdpV,OAAAA,eAAW0V,EAAAA,UAAAA,mBAAAA,CAAAA,IAAX,WACE,OAAOhV,KAAK2U,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAQdrV,OAAAA,eAAW0V,EAAAA,UAAAA,QAAAA,CAAAA,IAAX,WACE,OAAOhV,KAAKkV,MAAM,EAANA,YAAAA,EAAAA,cAAAA,IAYd5V,OAAAA,eAAW0V,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,EAAE,EAAF,gCA+DFA,EAAAA,UAAAA,WAAP,SAAkBvR,GAChB,OAAM,CAAC,EAeTnE,OAAAA,eAAW0V,EAAAA,UAAAA,aAAAA,CAAAA,IAAX,WACE,OAAOhV,KAAKmV,WAAW,EAAXA,YAAAA,EAAAA,cAAAA,IAMPH,EAAAA,UAAAA,OAAP,WACEhV,KAAK2P,UAAUnF,MAAM4K,OAAS,OAC9BpV,KAAKmV,aAAAA,EACLnV,KAAKqV,uBAAyBrV,KAAKsV,UAAU,EAMxCN,EAAAA,UAAAA,SAAP,WACEhV,KAAK2P,UAAUnF,MAAM4K,OAAS,UAC9BpV,KAAKmV,aAAAA,EACLnV,KAAK6U,cAAc,EAUdG,EAAAA,UAAAA,YAAP,SAAmBO,EAAe/O,GAAAA,EAS3BwO,EAAAA,UAAAA,SAAP,SAAgBO,EAAe/O,GAAAA,EAQxBwO,EAAAA,UAAAA,WAAP,SAAkBO,GAAAA,EAQXP,EAAAA,UAAAA,UAAP,SAAiBO,GACfvV,KAAK6U,cAAc,EAOdG,EAAAA,UAAAA,QAAP,aAEUA,EAAAA,UAAAA,2BAAV,SAAqCQ,GAC/BxV,KAAK2P,UAAU8F,WAAW/S,OAAS,EACrC1C,KAAK2P,UAAU+F,aAAaF,EAASxV,KAAK2P,UAAU8F,WAAW,IAE/DzV,KAAK2P,UAAUjK,YAAY8P,EAAAA,EAOxBR,EAAAA,UAAAA,SAAP,WACE,MAAO,CACLvH,SAAUuH,EAAWvH,SACrBkI,MAAO3V,KAAK2V,MACZC,MAAO5V,KAAK4V,MAAAA,EASTZ,EAAAA,UAAAA,aAAP,SAAoBW,GAClB3V,KAAKkV,OAASS,EAAMA,MACpB3V,KAAK4V,MAAQD,EAAMC,KAAK,EAUnBZ,EAAAA,UAAAA,MAAP,SAAaa,EAAgBC,GAAAA,EAMnBd,EAAAA,UAAAA,aAAV,SAAuBzB,GACjBvT,KAAKsU,gBACPtU,KAAKsU,eAAef,GAEtBvT,KAAK6U,cAAc,EAMXG,EAAAA,UAAAA,iBAAV,SAA2BzB,GACrBvT,KAAK+V,oBACP/V,KAAK+V,mBAAmBxC,GAE1BvT,KAAK6U,cAAc,EASXG,EAAAA,UAAAA,aAAV,WACE,GAAIhV,KAAKgW,gBAAiC,aAAfhW,KAAK2V,OAAuC,QAAf3V,KAAK2V,MAAiB,CAC5E,IAAMM,EAAejW,KAAKsV,gBAAW,IAEjCtV,KAAKqV,yBACPrV,KAAKqV,uBAAuBM,MAAQ,UAEtCM,EAAaN,MAAQ,SACjBO,KAAKC,UAAUnW,KAAKqV,yBAA2Ba,KAAKC,UAAUF,IAChEjW,KAAKgW,eAAehW,KAAK,CAALA,EAxQZgV,EAAAA,SAAW,eDLWT,GCKX,aC0BzB,aACEvU,KAAKoW,iBAAmBpW,KAAKoW,iBAAiBxK,KAAK5L,KAAK,CA8B5D,OAvBSqW,EAAAA,UAAAA,iBAAP,SACEC,GAEE,OAAItW,KAAKuW,QAAQC,WAAWF,GACnBtW,KAAKuW,QACHvW,KAAKyW,UAAUD,WAAWF,GAC5BtW,KAAKyW,UACHzW,KAAK0W,SAASF,WAAWF,GAC3BtW,KAAK0W,SACH1W,KAAK2W,WAAWH,WAAWF,GAC7BtW,KAAK2W,WACH3W,KAAK4W,YAAYJ,WAAWF,GAC9BtW,KAAK4W,YACH5W,KAAK6W,WAAWL,WAAWF,GAC7BtW,KAAK6W,WACH7W,KAAK8W,aAAaN,WAAWF,GAC/BtW,KAAK8W,aACH9W,KAAK+W,YAAYP,WAAWF,GAC9BtW,KAAK+W,iBAAAA,CAEZ,IDtDmB,GCsDnB,aCnDN,aALgB/W,KAAAA,UAAY,GAM1BA,KAAKgX,OAAS1T,EAAU2T,cACxBjX,KAAKgX,OAAOtR,YACVpC,EAAU4T,aAA8B,IAAjBlX,KAAKmX,UAAiB,CAAC,CAAC,OAAQ,kBAEzDnX,KAAKgX,OAAOtR,YACVpC,EAAU4T,aAAalX,KAAKmX,UAAW,CACrC,CAAC,OAAQ,WACT,CAAC,eAAgB,OACjB,CAAC,SAAU,WACX,CAAC,eAAgB,KACjB,CAAC,iBAAkB,UAqB3B,OAXSC,EAAAA,UAAAA,WAAP,SAAkB3T,GAChB,OACEA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAKgX,OAAOvB,WAAW,IAC9BhS,IAAOzD,KAAKgX,OAAOvB,WAAW,EAAE,EAAF,ED0B5B,GC1B4B,aC7BpC,cAoBA,OAnBgB4B,EAAAA,mBAAd,SAAiCC,GAC/B,MAAO,CACLnU,EAAGmU,EAAOnU,EACV9D,EAAGiY,EAAOjY,EACVkY,EAAGD,EAAOC,EACVnY,EAAGkY,EAAOlY,EACV2B,EAAGuW,EAAOvW,EACVQ,EAAG+V,EAAO/V,EAAAA,EAGA8V,EAAAA,YAAd,SAA0BG,EAA0BC,GAOlD,OANAD,EAAcrU,EAAIsU,EAAUtU,EAC5BqU,EAAcnY,EAAIoY,EAAUpY,EAC5BmY,EAAcD,EAAIE,EAAUF,EAC5BC,EAAcpY,EAAIqY,EAAUrY,EAC5BoY,EAAczW,EAAI0W,EAAU1W,EAC5ByW,EAAcjW,EAAIkW,EAAUlW,EACrBiW,CAAAA,EAAAA,CAAAA,CDWyB,GCXzBA,EAAAA,SAAAA,GC6FT,WAAY7H,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAzG3B5N,EAAAA,KAAO,EAIPA,EAAAA,IAAM,EAINA,EAAAA,MAAQ,EAIRA,EAAAA,OAAS,EAKTA,EAAAA,YAAsB,CAACd,EAAG,GAAIvE,EAAG,IA+BjCqF,EAAAA,QAAU,EAIVA,EAAAA,QAAU,EAKVA,EAAAA,cAAgB,EAgCTA,EAAAA,YAAsB,GA8F7BA,EAAAA,4BAAAA,EA3ERA,EAAK8I,UAAU+H,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBAEtD/Q,EAAKgR,kBAAAA,CAAAA,CA8dT,OAhlB8C/X,EAAAA,EAAAA,GAkE5CR,OAAAA,eAAcwY,EAAAA,UAAAA,UAAAA,CAAAA,IAAd,WACE,OAAO9X,KAAK+X,KAAO/X,KAAKgE,MAAQ,CAAC,EAAD,gCAKlC1E,OAAAA,eAAcwY,EAAAA,UAAAA,UAAAA,CAAAA,IAAd,WACE,OAAO9X,KAAKiP,IAAMjP,KAAKiE,OAAS,CAAC,EAAD,gCAOlC3E,OAAAA,eAAcwY,EAAAA,UAAAA,SAAAA,CAAAA,IAAd,WACE,OAAO9X,KAAKgY,OAAO,EAAPA,IAEd,SAAqBpX,GACnBZ,KAAKgY,QAAUpX,EACf,IAAMqX,EAAY3U,EAAUsU,kBAC5B5X,KAAKgY,QAAQN,UAAUzQ,QAAQ0Q,WAAWM,EAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAoCrCH,EAAAA,UAAAA,WAAP,SAAkBrU,GAChB,QAAIwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,YAAAA,IAGnBzD,KAAKkY,aAAa9B,iBAAiB3S,SAAAA,IAClCzD,KAAKmY,aAA6BnY,KAAKmY,YAAY3B,WAAW/S,GAAAA,EAc5DqU,EAAAA,UAAAA,YAAP,SAAmBvC,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAEN,QAAfxG,KAAK2V,QACP3V,KAAK+X,KAAOxC,EAAMxP,EAClB/F,KAAKiP,IAAMsG,EAAM/T,GAGnBxB,KAAKqY,sBAAwBrY,KAAK+X,KAClC/X,KAAKsY,qBAAuBtY,KAAKiP,IACjCjP,KAAKuY,uBAAyBvY,KAAKgE,MACnChE,KAAKwY,wBAA0BxY,KAAKiE,OAEpC,IAAMwU,EAAezY,KAAK0Y,cAAcnD,GAOxC,GANAvV,KAAK2Y,mBAAqBF,EAAa1S,EACvC/F,KAAK4Y,mBAAqBH,EAAajX,EAEvCxB,KAAK6Y,QAAUJ,EAAa1S,EAAI/F,KAAK+X,KACrC/X,KAAK8Y,QAAUL,EAAajX,EAAIxB,KAAKiP,IAElB,QAAfjP,KAAK2V,MAGP,GAFA3V,KAAK+Y,SACL/Y,KAAKgZ,WAAahZ,KAAKkY,aAAa9B,iBAAiB5P,QAAAA,IACjDxG,KAAKgZ,WACPhZ,KAAKkV,OAAS,cACT,YAAIlV,KAAKmY,aAA6BnY,KAAKmY,YAAY3B,WAAWhQ,GAAS,CAChFxG,KAAKgZ,WAAahZ,KAAKmY,YAEvB,IAAMc,EAAgBjZ,KAAKkZ,YAAY,CAACnT,EAAG/F,KAAKmZ,QAAS3X,EAAGxB,KAAKoZ,UACjEpZ,KAAK+X,KAAOkB,EAAclT,EAAI/F,KAAKgE,MAAQ,EAC3ChE,KAAKiP,IAAMgK,EAAczX,EAAIxB,KAAKiE,OAAS,EAC3CjE,KAAKqZ,WAAW,CAAEtT,EAAG/F,KAAK+X,KAAMvW,EAAGxB,KAAKiP,MAExC,IAAMqK,EAAStZ,KAAK2P,UAAU+H,UAAUzQ,QAAQsS,QAAQ,GACxDD,EAAOE,UAAUxZ,KAAKyZ,cAAezZ,KAAKmZ,QAASnZ,KAAKoZ,SACxDpZ,KAAK2P,UAAU+H,UAAUzQ,QAAQyS,YAAYJ,EAAQ,GAErDtZ,KAAK2Z,mBAEL3Z,KAAKkV,OAAS,cAEdlV,KAAKkV,OAAS,QAYb4C,EAAAA,UAAAA,UAAP,SAAiBvC,GACf,IAAMqE,EAAU5Z,KAAK2V,MACrB1C,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GACG,aAAfvV,KAAK2V,OAAwB3V,KAAKgE,MAAQ,IAAMhE,KAAKiE,OAAS,IAChEjE,KAAKgE,MAAQhE,KAAK8Z,YAAY/T,EAC9B/F,KAAKiE,OAASjE,KAAK8Z,YAAYtY,GAE/BxB,KAAK+Z,WAAWxE,GAElBvV,KAAKkV,OAAS,SACE,aAAZ0E,GAA0B5Z,KAAKga,kBAAAA,IAAmBha,KAAKia,4BACzDja,KAAKga,gBAAgBha,KAAK,EAQpB8X,EAAAA,UAAAA,WAAV,SAAqBvC,GACnBvV,KAAKgX,OAAOxM,MAAMkN,UAAY,aAAanC,EAAMxP,EAAAA,OAAQwP,EAAM/T,EAAAA,KAAAA,EAW1DsW,EAAAA,UAAAA,WAAP,SAAkBvC,GAChB,IAAMkD,EAAezY,KAAK0Y,cAAcnD,GAErB,aAAfvV,KAAK2V,MACP3V,KAAKka,OAAO3E,GACY,SAAfvV,KAAK2V,OACd3V,KAAK+X,KACH/X,KAAKqY,uBACJI,EAAa1S,EAAI/F,KAAKqY,uBACvBrY,KAAK6Y,QACP7Y,KAAKiP,IACHjP,KAAKsY,sBACJG,EAAajX,EAAIxB,KAAKsY,sBACvBtY,KAAK8Y,QACP9Y,KAAKqZ,WAAW,CAACtT,EAAG/F,KAAK+X,KAAMvW,EAAGxB,KAAKiP,MACvCjP,KAAK2Z,oBACmB,WAAf3Z,KAAK2V,MACd3V,KAAKka,OAAOzB,GACY,WAAfzY,KAAK2V,OACd3V,KAAKsZ,OAAO/D,EAAAA,EAQNuC,EAAAA,UAAAA,OAAV,SAAiBvC,GACf,IAAI4E,EAAOna,KAAKqY,sBACZ+B,EAAWpa,KAAKuY,uBAChB8B,EAAOra,KAAKsY,qBACZgC,EAAYta,KAAKwY,wBAErB,OAAOxY,KAAKgZ,YACV,KAAKhZ,KAAKkY,aAAarB,WACvB,KAAK7W,KAAKkY,aAAavB,WACvB,KAAK3W,KAAKkY,aAAa3B,QACrB4D,EAAOna,KAAKqY,sBAAwB9C,EAAMxP,EAAI/F,KAAK2Y,mBACnDyB,EAAWpa,KAAKuY,uBAAyBvY,KAAKqY,sBAAwB8B,EACtE,MACF,KAAKna,KAAKkY,aAAanB,YACvB,KAAK/W,KAAKkY,aAAatB,YACvB,KAAK5W,KAAKkY,aAAaxB,SACvB,UAAK,EACH0D,EAAWpa,KAAKuY,uBAAyBhD,EAAMxP,EAAI/F,KAAK2Y,mBAI5D,OAAO3Y,KAAKgZ,YACV,KAAKhZ,KAAKkY,aAAazB,UACvB,KAAKzW,KAAKkY,aAAa3B,QACvB,KAAKvW,KAAKkY,aAAaxB,SACrB2D,EAAOra,KAAKsY,qBAAuB/C,EAAM/T,EAAIxB,KAAK4Y,mBAClD0B,EAAYta,KAAKwY,wBAA0BxY,KAAKsY,qBAAuB+B,EACvE,MACF,KAAKra,KAAKkY,aAAapB,aACvB,KAAK9W,KAAKkY,aAAarB,WACvB,KAAK7W,KAAKkY,aAAanB,YACvB,UAAK,EACHuD,EAAYta,KAAKwY,wBAA0BjD,EAAM/T,EAAIxB,KAAK4Y,mBAI1DwB,GAAY,GACdpa,KAAK+X,KAAOoC,EACZna,KAAKgE,MAAQoW,IAEbpa,KAAK+X,KAAOoC,EAAOC,EACnBpa,KAAKgE,OAASoW,GAEZE,GAAa,GACfta,KAAKiP,IAAMoL,EACXra,KAAKiE,OAASqW,IAEdta,KAAKiP,IAAMoL,EAAOC,EAClBta,KAAKiE,QAAUqW,GAGjBta,KAAKua,SAAS,EAMNzC,EAAAA,UAAAA,QAAV,WACE9X,KAAKqZ,WAAW,CAACtT,EAAG/F,KAAK+X,KAAMvW,EAAGxB,KAAKiP,MACvCjP,KAAK2Z,kBAAkB,EAGjB7B,EAAAA,UAAAA,OAAR,SAAevC,GAEb,GAAI3G,KAAK4L,IAAIjF,EAAMxP,EAAI/F,KAAKmZ,SAAW,GAAK,CAC1C,IAAMsB,EAAO7L,KAAK6L,KAAKlF,EAAMxP,EAAI/F,KAAKmZ,SACtCnZ,KAAKyZ,cAC+D,IAAjE7K,KAAK8L,MAAMnF,EAAM/T,EAAIxB,KAAKoZ,UAAY7D,EAAMxP,EAAI/F,KAAKmZ,UACpDvK,KAAK+L,GACP,GAAKF,EACPza,KAAK4a,eAAe,CAAfA,EAID9C,EAAAA,UAAAA,cAAR,WACE,IAAMwB,EAAStZ,KAAK2P,UAAU+H,UAAUzQ,QAAQsS,QAAQ,GACxDD,EAAOE,UAAUxZ,KAAKyZ,cAAezZ,KAAKmZ,QAASnZ,KAAKoZ,SACxDpZ,KAAK2P,UAAU+H,UAAUzQ,QAAQyS,YAAYJ,EAAQ,EAAE,EAO/CxB,EAAAA,UAAAA,YAAV,SAAsBvC,GACpB,GAA2B,IAAvBvV,KAAKyZ,cACP,OAAOlE,EAGT,IAAM+B,EAAStX,KAAK2P,UAAUkL,SAC1B7U,EAAW1C,EAAUwX,YAAYvF,EAAMxP,EAAGwP,EAAM/T,GAKpD,MAFe,CAAEuE,GAFjBC,EAAWA,EAAS+U,gBAAgBzD,IAEPvR,EAAGvE,EAAGwE,EAASxE,EAAAA,EASpCsW,EAAAA,UAAAA,cAAV,SAAwBvC,GACtB,GAA2B,IAAvBvV,KAAKyZ,cACP,OAAOlE,EAGT,IAAI+B,EAAStX,KAAK2P,UAAUkL,SAC5BvD,EAASA,EAAO0D,UAChB,IAAIhV,EAAW1C,EAAUwX,YAAYvF,EAAMxP,EAAGwP,EAAM/T,GAKpD,MAFe,CAAEuE,GAFjBC,EAAWA,EAAS+U,gBAAgBzD,IAEPvR,EAAGvE,EAAGwE,EAASxE,EAAAA,EAQvCsW,EAAAA,UAAAA,OAAP,WACE7E,EAAAA,UAAM8F,OAAAA,KAAAA,MACN/Y,KAAK2Z,mBACL3Z,KAAKib,WAAWzQ,MAAM4C,QAAU,IAM3B0K,EAAAA,UAAAA,SAAP,WACE7E,EAAAA,UAAMiI,SAAAA,KAAAA,MACNlb,KAAKib,WAAWzQ,MAAM4C,QAAU,QAG1B0K,EAAAA,UAAAA,gBAAR,WACE9X,KAAKib,WAAa3X,EAAU2T,cAC5B,IAAMgB,EAAY3U,EAAUsU,kBAC5BK,EAAUkD,cAAcnb,KAAKob,YAAc,GAAIpb,KAAKob,YAAc,GAClEpb,KAAKib,WAAWvD,UAAUzQ,QAAQ0Q,WAAWM,GAE7CjY,KAAK2P,UAAUjK,YAAY1F,KAAKib,YAEhCjb,KAAKqb,YAAc/X,EAAUgY,WAC3Btb,KAAKgE,MAAQhE,KAAKob,YAClBpb,KAAKiE,OAASjE,KAAKob,YACnB,CACE,CAAC,SAAU,SACX,CAAC,eAAgB,KACjB,CAAC,iBAAkB,OACnB,CAAC,mBAAoB,QACrB,CAAC,OAAQ,eACT,CAAC,iBAAkB,UAIvBpb,KAAKib,WAAWvV,YAAY1F,KAAKqb,cAAAA,IAE7Brb,KAAK4U,eAAe2G,kBACtBvb,KAAKwb,gBAAkBlY,EAAUmY,YAC9Bzb,KAAKgE,MAA2B,EAAnBhE,KAAKob,aAAmB,EACtCpb,KAAKiP,IAAMjP,KAAKob,aACfpb,KAAKgE,MAA2B,EAAnBhE,KAAKob,aAAmB,EACtCpb,KAAKiP,IAAyB,EAAnBjP,KAAKob,YAChB,CACE,CAAC,SAAU,SACX,CAAC,eAAgB,KACjB,CAAC,iBAAkB,OACnB,CAAC,mBAAoB,UAIzBpb,KAAKib,WAAWvV,YAAY1F,KAAKwb,kBAGnCxb,KAAKkY,aAAe,IAAI7B,EACxBrW,KAAK0b,kBAEL1b,KAAKib,WAAWzQ,MAAM4C,QAAU,QAG1B0K,EAAAA,UAAAA,iBAAR,WACE,IAAMG,EAAYjY,KAAKib,WAAWvD,UAAUzQ,QAAQsS,QAAQ,GAC5DtB,EAAUkD,aACRnb,KAAK+X,KAAO/X,KAAKob,YAAc,EAC/Bpb,KAAKiP,IAAMjP,KAAKob,YAAc,GAEhCpb,KAAKib,WAAWvD,UAAUzQ,QAAQyS,YAAYzB,EAAW,GACzDjY,KAAKqb,YAAYtX,aACf,SACC/D,KAAKgE,MAAQhE,KAAKob,aAAajX,YAElCnE,KAAKqb,YAAYtX,aACf,UACC/D,KAAKiE,OAASjE,KAAKob,aAAajX,iBAAAA,IAG/BnE,KAAKwb,kBACPxb,KAAKwb,gBAAgBzX,aACnB,OACE/D,KAAKgE,MAAQhE,KAAKob,aAAe,GAAGjX,YAExCnE,KAAKwb,gBAAgBzX,aAAa,OAAQ/D,KAAKob,YAAc,GAAGjX,YAChEnE,KAAKwb,gBAAgBzX,aACnB,OACE/D,KAAKgE,MAAQhE,KAAKob,aAAe,GAAGjX,YAExCnE,KAAKwb,gBAAgBzX,aAAa,MAA2B,GAAnB/D,KAAKob,aAAiBjX,aAGlEnE,KAAK2b,eAAe,EAGd7D,EAAAA,UAAAA,gBAAR,WACE9X,KAAKkY,aAAa3B,QAAUvW,KAAK4b,aACjC5b,KAAKkY,aAAazB,UAAYzW,KAAK4b,aACnC5b,KAAKkY,aAAaxB,SAAW1W,KAAK4b,aAClC5b,KAAKkY,aAAavB,WAAa3W,KAAK4b,aACpC5b,KAAKkY,aAAatB,YAAc5W,KAAK4b,aACrC5b,KAAKkY,aAAarB,WAAa7W,KAAK4b,aACpC5b,KAAKkY,aAAapB,aAAe9W,KAAK4b,aACtC5b,KAAKkY,aAAanB,YAAc/W,KAAK4b,cAAAA,IAEjC5b,KAAK4U,eAAe2G,kBACtBvb,KAAKmY,YAAcnY,KAAK4b,cAG1B5b,KAAK2b,eAAe,EAGd7D,EAAAA,UAAAA,WAAR,WACE,IAAM+D,EAAO,IAAIzE,EAIjB,OAHAyE,EAAK7E,OAAOU,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACnD5X,KAAKib,WAAWvV,YAAYmW,EAAK7E,QAE1B6E,CAAAA,EAGD/D,EAAAA,UAAAA,cAAR,WACE,IAAMgE,EAAW9b,KAAKkY,aAAa3B,QAAQY,UAErCY,GAAQ+D,EAAW,EACnB7M,EAAM8I,EACNgE,GAAM/b,KAAKgE,MAAQhE,KAAKob,aAAe,EAAIU,EAAW,EACtDE,GAAMhc,KAAKiE,OAASjE,KAAKob,aAAe,EAAIU,EAAW,EACvDG,EAASjc,KAAKiE,OAASjE,KAAKob,YAAcU,EAAW,EACrD1M,EAAQpP,KAAKgE,MAAQhE,KAAKob,YAAcU,EAAW,EAEzD9b,KAAKkc,aAAalc,KAAKkY,aAAa3B,QAAQS,OAAQe,EAAM9I,GAC1DjP,KAAKkc,aAAalc,KAAKkY,aAAazB,UAAUO,OAAQ+E,EAAI9M,GAC1DjP,KAAKkc,aAAalc,KAAKkY,aAAaxB,SAASM,OAAQ5H,EAAOH,GAC5DjP,KAAKkc,aAAalc,KAAKkY,aAAavB,WAAWK,OAAQe,EAAMiE,GAC7Dhc,KAAKkc,aAAalc,KAAKkY,aAAatB,YAAYI,OAAQ5H,EAAO4M,GAC/Dhc,KAAKkc,aAAalc,KAAKkY,aAAarB,WAAWG,OAAQe,EAAMkE,GAC7Djc,KAAKkc,aAAalc,KAAKkY,aAAapB,aAAaE,OAAQ+E,EAAIE,GAC7Djc,KAAKkc,aAAalc,KAAKkY,aAAanB,YAAYC,OAAQ5H,EAAO6M,QAAAA,IAE3Djc,KAAKmY,aACPnY,KAAKkc,aAAalc,KAAKmY,YAAYnB,OAAQ+E,EAAI9M,EAAyB,EAAnBjP,KAAKob,YAAAA,EAItDtD,EAAAA,UAAAA,aAAR,SAAqB+D,EAA0B9V,EAAWvE,GACxD,IAAMyW,EAAY4D,EAAKnE,UAAUzQ,QAAQsS,QAAQ,GACjDtB,EAAUkD,aAAapV,EAAGvE,GAC1Bqa,EAAKnE,UAAUzQ,QAAQyS,YAAYzB,EAAW,EAAE,EAMxCH,EAAAA,UAAAA,eAAV,WACE9X,KAAKib,WAAWzQ,MAAM4C,QAAU,QAKxB0K,EAAAA,UAAAA,eAAV,WACE9X,KAAKib,WAAWzQ,MAAM4C,QAAU,IAM3B0K,EAAAA,UAAAA,SAAP,WAYE,OAX8CxY,OAAO6c,OAAO,CAC1DpE,KAAM/X,KAAK+X,KACX9I,IAAKjP,KAAKiP,IACVjL,MAAOhE,KAAKgE,MACZC,OAAQjE,KAAKiE,OACbwV,cAAezZ,KAAKyZ,cACpB2C,sBAAuB/E,EAAgBgF,mBAAmBrc,KAAKgX,OAAOU,UAAUzQ,QAAQsS,QAAQ,GAAGjC,QACnGgF,yBAA0BjF,EAAgBgF,mBAAmBrc,KAAK2P,UAAU+H,UAAUzQ,QAAQsS,QAAQ,GAAGjC,SAE3GrE,EAAAA,UAAMqC,SAAAA,KAAAA,MAAAA,EASDwC,EAAAA,UAAAA,aAAP,SAAoBnC,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB,IAAM6G,EAAW7G,EACjB3V,KAAK+X,KAAOyE,EAASzE,KACrB/X,KAAKiP,IAAMuN,EAASvN,IACpBjP,KAAKgE,MAAQwY,EAASxY,MACtBhE,KAAKiE,OAASuY,EAASvY,OACvBjE,KAAKyZ,cAAgB+C,EAAS/C,cAC9BzZ,KAAKgX,OAAOU,UAAUzQ,QAAQsS,QAAQ,GAAGkD,UACvCpF,EAAgBqF,YAAY1c,KAAKgX,OAAOU,UAAUzQ,QAAQsS,QAAQ,GAAGjC,OAAQkF,EAASJ,wBAExFpc,KAAK2P,UAAU+H,UAAUzQ,QAAQsS,QAAQ,GAAGkD,UAC1CpF,EAAgBqF,YAAY1c,KAAK2P,UAAU+H,UAAUzQ,QAAQsS,QAAQ,GAAGjC,OAAQkF,EAASF,0BAAAA,EAYtFxE,EAAAA,UAAAA,MAAP,SAAajC,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB,IAAM8G,EAAS5c,KAAKkZ,YAAY,CAACnT,EAAG/F,KAAK+X,KAAMvW,EAAGxB,KAAKiP,MACjDsG,EAAQvV,KAAK0Y,cAAc,CAAC3S,EAAG6W,EAAO7W,EAAI8P,EAAQrU,EAAGob,EAAOpb,EAAIsU,IAEtE9V,KAAK+X,KAAOxC,EAAMxP,EAClB/F,KAAKiP,IAAMsG,EAAM/T,EACjBxB,KAAKgE,MAAQhE,KAAKgE,MAAQ6R,EAC1B7V,KAAKiE,OAASjE,KAAKiE,OAAS6R,EAE5B9V,KAAK2Z,kBAAkB,EAAlBA,CAAAA,CD9jBEnC,CCfmCxC,GAAAA,EAAAA,SAAAA,GC4B5C,WAAYrF,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OA1B3B5N,EAAAA,UAAY,cAIZA,EAAAA,YAAc,cAIdA,EAAAA,YAAc,EAIdA,EAAAA,gBAAkB,GAIlBA,EAAAA,QAAU,EAYlBA,EAAKgW,eAAiBhW,EAAKgW,eAAejR,KAAK/E,GAC/CA,EAAKiW,aAAejW,EAAKiW,aAAalR,KAAK/E,GAC3CA,EAAKkW,eAAiBlW,EAAKkW,eAAenR,KAAK/E,GAC/CA,EAAKmW,mBAAqBnW,EAAKmW,mBAAmBpR,KAAK/E,GACvDA,EAAKoW,aAAepW,EAAKoW,aAAarR,KAAK/E,GAAAA,CAAAA,CAkL/C,OA7N8C/G,EAAAA,EAAAA,GAmDrCod,EAAAA,UAAAA,WAAP,SAAkBzZ,GAChB,SAAIwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IAAOA,IAAOzD,KAAKgX,OAAO,EAUvCkG,EAAAA,UAAAA,aAAV,WACEld,KAAKgX,OAAS1T,EAAUgY,WAAW,EAAG,EAAG,CACvC,CAAC,OAAQtb,KAAKmd,WACd,CAAC,SAAUnd,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,YAClC,CAAC,mBAAoBnE,KAAKsd,iBAC1B,CAAC,UAAWtd,KAAKud,QAAQpZ,cAE3BnE,KAAKwd,2BAA2Bxd,KAAKgX,OAAO,EASvCkG,EAAAA,UAAAA,YAAP,SAAmB3H,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GACN,QAAfxG,KAAK2V,QACP3V,KAAKid,eAELjd,KAAKqZ,WAAW9D,GAEhBvV,KAAKkV,OAAS,aASXgI,EAAAA,UAAAA,WAAP,SAAkB3H,GAChBtC,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,EAAAA,EAOT2H,EAAAA,UAAAA,OAAV,SAAiB3H,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbvV,KAAKua,SAAS,EAMN2C,EAAAA,UAAAA,QAAV,WACEjK,EAAAA,UAAMsH,QAAAA,KAAAA,MACNjX,EAAUc,cAAcpE,KAAKgX,OAAQ,CACnC,CAAC,QAAShX,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,aAAAA,EAUpB+Y,EAAAA,UAAAA,UAAP,SAAiB3H,GACftC,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GAChBvV,KAAKua,SAAS,EAON2C,EAAAA,UAAAA,eAAV,SAAyB3J,GACvBvT,KAAKod,YAAc7J,EACfvT,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,SAAUhX,KAAKod,eAExDpd,KAAK8U,aAAavB,GAClBvT,KAAK6U,cAAc,EAMXqI,EAAAA,UAAAA,aAAV,SAAuB3J,GACrBvT,KAAKmd,UAAY5J,EACbvT,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,OAAQhX,KAAKmd,aAEtDnd,KAAK6U,cAAc,EAMXqI,EAAAA,UAAAA,eAAV,SAAyBlZ,GACvBhE,KAAKqd,YAAcrZ,EACfhE,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,eAAgBhX,KAAKqd,YAAYlZ,cAE1EnE,KAAK6U,cAAc,EAMXqI,EAAAA,UAAAA,mBAAV,SAA6BO,GAC3Bzd,KAAKsd,gBAAkBG,EACnBzd,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,mBAAoBhX,KAAKsd,mBAElEtd,KAAK6U,cAAc,EAMdqI,EAAAA,UAAAA,SAAP,WASE,OARqC5d,OAAO6c,OAAO,CACjDgB,UAAWnd,KAAKmd,UAChBC,YAAapd,KAAKod,YAClBC,YAAard,KAAKqd,YAClBC,gBAAiBtd,KAAKsd,gBACtBC,QAASvd,KAAKud,SACbtK,EAAAA,UAAMqC,SAAAA,KAAAA,MAAAA,EAUJ4H,EAAAA,UAAAA,aAAP,SAAoBvH,GAClB,IAAM+H,EAAY/H,EAClB3V,KAAKmd,UAAYO,EAAUP,UAC3Bnd,KAAKod,YAAcM,EAAUN,YAC7Bpd,KAAKqd,YAAcK,EAAUL,YAC7Brd,KAAKsd,gBAAkBI,EAAUJ,gBACjCtd,KAAKud,QAAUG,EAAUH,QAEzBvd,KAAKid,eACLhK,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKua,SAAS,EAST2C,EAAAA,UAAAA,MAAP,SAAarH,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKua,SAAS,EApNF2C,EAAAA,MAAQ,qBDEsBlI,CCRA8C,GAAAA,EAAAA,SAAAA,GCmB5C,WAAYpK,EAAeiQ,EAAkBC,EAAuBjQ,GAApE,MACEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,yGAAAA,KAAAA,OAlBP9G,EAAAA,OAAmB,GAGnBA,EAAAA,WAA+B,GAgBrCA,EAAK8W,OAASA,EACd9W,EAAK+W,aAAeA,EAEpB/W,EAAKgX,gBAAkBhX,EAAKgX,gBAAgBjS,KAAK/E,GAAAA,CAAAA,CAoErD,OA3FoC/G,EAAAA,EAAAA,GA6B3Bge,EAAAA,UAAAA,MAAP,sBACQzK,EAAW9P,SAASqD,cAAc,OA+CxC,OA9CAyM,EAAS7I,MAAM4C,QAAU,OACzBiG,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMsC,SAAW,IAC1B9M,KAAK2d,OAAOtQ,SAAQ,SAAC0Q,GACnB,IAAMC,EAAoBza,SAASqD,cAAc,OACjDoX,EAAkBxT,MAAM4C,QAAU,OAClC4Q,EAAkBxT,MAAMsC,SAAW,IACnCkR,EAAkBxT,MAAMyT,WAAa,SACrCD,EAAkBxT,MAAM0T,eAAiB,gBACzCF,EAAkBxT,MAAMoJ,QAAU,MAClCoK,EAAkBxT,MAAMuJ,YAAc,MACtCiK,EAAkBxT,MAAMwJ,YAAc,QACtCgK,EAAkBxT,MAAM0J,YACtB6J,IAAclX,EAAK+W,aAAe/W,EAAK2E,gBAAgBjC,mBAAqB,cAE9EyU,EAAkBpQ,iBAAiB,oBACjC/G,EAAKgX,gBAAgBE,EAAWC,EAAAA,IAElC3K,EAAS3N,YAAYsY,GAErB,IAAMpc,EAAQ2B,SAASqD,cAAc,OACrChF,EAAMuc,UAAYJ,EAAU5Z,WAC5BvC,EAAM4I,MAAMqJ,YAAc,MAC1BmK,EAAkBtY,YAAY9D,GAE9B,IAAMwc,EAAW7a,SAASqD,cAAc,OACxCwX,EAAS5T,MAAM6T,UAAY,OAC3BD,EAAS5T,MAAMsC,SAAW,IAC1BsR,EAAS5T,MAAM4C,QAAU,OACzBgR,EAAS5T,MAAMyT,WAAa,SAE5B,IAAMK,EAAK/a,SAASqD,cAAc,MAClC0X,EAAG9T,MAAM+T,SAAW,OACpBD,EAAG9T,MAAMgU,OAAS,MAClBF,EAAG9T,MAAMiU,UAAeV,EAAAA,YAAqBlX,EAAK2E,gBAAgBlC,aAClEgV,EAAG9T,MAAMsC,SAAW,IACpBsR,EAAS1Y,YAAY4Y,GAMrBN,EAAkBtY,YAAY0Y,GAE9BvX,EAAK6X,WAAW/b,KAAKqb,EAAAA,IAEhB3K,CAAAA,EAGDyK,EAAAA,UAAAA,gBAAR,SAAwB1D,EAAkB5T,GAA1C,WACExG,KAAK4d,aAAexD,EAEpBpa,KAAK0e,WAAWrR,SAAQ,SAAAgH,GACtBA,EAAI7J,MAAM0J,YAAcG,IAAQ7N,EAASK,EAAK2E,gBAAgBjC,mBAAqB,iBAGjFvJ,KAAK2e,gBACP3e,KAAK2e,eAAe3e,KAAK4d,aAAa,EAAbA,CAAAA,CDzFe9F,CCCVvD,GAAAA,EAAAA,SAAAA,GCkBlC,WAAY7G,EAAejC,EAAkBmT,EAAuBjR,GAApE,MACEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,8MAAAA,KAAAA,OAlBP9G,EAAAA,OAAmB,GAGnBA,EAAAA,WAA+B,GAgBrCA,EAAK4E,OAASA,EACd5E,EAAK+X,aAAeA,EAEpB/X,EAAKgY,gBAAkBhY,EAAKgY,gBAAgBjT,KAAK/E,GAAAA,CAAAA,CA4DrD,OAnFoC/G,EAAAA,EAAAA,GA6B3Bgf,EAAAA,UAAAA,MAAP,sBACQzL,EAAW9P,SAASqD,cAAc,OAuCxC,OAtCAyM,EAAS7I,MAAM4C,QAAU,OACzBiG,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMsC,SAAW,IAC1B9M,KAAKyL,OAAO4B,SAAQ,SAAC0R,GACnB,IAAMC,EAAoBzb,SAASqD,cAAc,OACjDoY,EAAkBxU,MAAM4C,QAAU,OAClC4R,EAAkBxU,MAAMyT,WAAa,SACrCe,EAAkBxU,MAAM0T,eAAiB,gBACzCc,EAAkBxU,MAAMoJ,QAAU,MAClCoL,EAAkBxU,MAAMuJ,YAAc,MACtCiL,EAAkBxU,MAAMwJ,YAAc,QACtCgL,EAAkBxU,MAAM8I,SAAW,SACnC0L,EAAkBxU,MAAMyU,SAAc,IAAMpY,EAAK4E,OAAO/I,OAAS,MACjEsc,EAAkBxU,MAAM0J,YACtB6K,IAAclY,EAAK+X,aAAe/X,EAAK2E,gBAAgBjC,mBAAqB,cAE9EyV,EAAkBpR,iBAAiB,oBACjC/G,EAAKgY,gBAAgBE,EAAWC,EAAAA,IAElC3L,EAAS3N,YAAYsZ,GAErB,IAAME,EAAW3b,SAASqD,cAAc,OACxCsY,EAAS1U,MAAM6T,UAAY,OAC3Ba,EAAS1U,MAAMsC,SAAW,IAC1BoS,EAAS1U,MAAM8I,SAAW,SAE1B,IAAM6L,EAAc,sFAElBtY,EAAK2E,gBAAgBlC,aAAAA,uBACL,KAAdyV,EAAmB,qBAAuBA,EAAY,IAAM,oBAGhEG,EAAS9X,UAAY+X,EAErBH,EAAkBtZ,YAAYwZ,GAE9BrY,EAAKuY,WAAWzc,KAAKqc,EAAAA,IAEhB3L,CAAAA,EAGDyL,EAAAA,UAAAA,gBAAR,SAAwBO,EAAkB7Y,GAA1C,WACExG,KAAK4e,aAAeS,EAEpBrf,KAAKof,WAAW/R,SAAQ,SAAAgH,GACtBA,EAAI7J,MAAM0J,YAAcG,IAAQ7N,EAASK,EAAK2E,gBAAgBjC,mBAAqB,iBAGjFvJ,KAAKsf,gBACPtf,KAAKsf,eAAetf,KAAK4e,aAAa,EAAbA,CAAAA,CDhFKrK,CCAAA,GAAAA,EAAAA,SAAAA,GC0BlC,WAAY5E,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAEnC5N,EAAKuW,YAAc3I,EAAS8K,aAC5B1Y,EAAKwW,YAAc5I,EAAS+K,mBAC5B3Y,EAAKyW,gBAAkB7I,EAASgL,uBAEhC5Y,EAAK6Y,YAAc,IAAItM,EACrB,aACAqB,EAASkL,gBACTlL,EAAS8K,cAEX1Y,EAAK6Y,YAAYpL,eAAiBzN,EAAKgW,eAEvChW,EAAK+Y,iBAAmB,IAAI9B,EAC1B,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAK+Y,iBAAiBjB,eAAiB9X,EAAKkW,eAE5ClW,EAAKiZ,iBAAmB,IAAIhB,EAC1B,aACArK,EAASsL,wBACTtL,EAASgL,wBAEX5Y,EAAKiZ,iBAAiBR,eAAiBzY,EAAKmW,mBAAAA,CAAAA,CAkBhD,OAxEiCld,EAAAA,EAAAA,GA4D/BR,OAAAA,eAAW0gB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAChgB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAMjDE,EAAAA,UAAAA,SAAP,WACE,IAAM/e,EAASgS,EAAAA,UAAMqC,SAAAA,KAAAA,MAErB,OADArU,EAAOwM,SAAWuS,EAAYvS,SACvBxM,CAAAA,EAhEK+e,EAAAA,SAAW,cAKXA,EAAAA,MAAQ,eAIRA,EAAAA,KAAAA,4EAAAA,CAAAA,CDboBzL,CCFH2I,GAAAA,ECIjC,WAISld,KAAAA,gBAA4B,CACjC,UACA,UACA,UACA,UACA,UACA,UACA,UACA,WAMKA,KAAAA,aAAeA,KAAK2f,gBAAgB,GAIpC3f,KAAAA,iBAAmBA,KAAK2f,gBAAgB,GAIxC3f,KAAAA,mBAAqBA,KAAK2f,gBAAgB,GAI1C3f,KAAAA,sBAAwBA,KAAK2f,gBAAgB,GAI7C3f,KAAAA,mBAAqB,EAIrBA,KAAAA,uBAAyB,GAIzBA,KAAAA,wBAA0B,GAK1BA,KAAAA,kBAAoB,+BAKpBA,KAAAA,oBAAsB,CAAC,EAAG,EAAG,EAAG,EAAG,IAKnCA,KAAAA,wBAA0B,CAAC,GAAI,IAAK,OAAQ,WAK5CA,KAAAA,oBAAsB,CAAC,GAAK,IAAM,GAAK,IAAM,GAK7CA,KAAAA,YAA2B,SAK3BA,KAAAA,oBAAsB,CAC3B,kCACA,+BACA,oCACA,UACA,WAMKA,KAAAA,YAAc,GAKdA,KAAAA,8BAAAA,EAQAA,KAAAA,kCAAAA,EAQAA,KAAAA,mBAAqB,EAQrBA,KAAAA,iBAAAA,EAqCCA,KAAAA,uBAAyB,OAMzBA,KAAAA,mBAAqB,OAMrBA,KAAAA,UAAAA,CAAW,gBCjGnB,WAAY2P,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OA7D3B5N,EAAAA,GAAK,EAILA,EAAAA,GAAK,EAILA,EAAAA,GAAK,EAILA,EAAAA,GAAK,EAKLA,EAAAA,cAAgB,GAKhBA,EAAAA,mBAAqB,EACrBA,EAAAA,mBAAqB,EAEvBA,EAAAA,oBAAsB,EACtBA,EAAAA,oBAAsB,EACtBA,EAAAA,oBAAsB,EACtBA,EAAAA,oBAAsB,EAmC5BA,EAAKgR,kBAAAA,CAAAA,CA4PT,OA/TsC/X,EAAAA,EAAAA,GA2E7BmgB,EAAAA,UAAAA,WAAP,SAAkBxc,GAChB,QAAIwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,OAGnBzD,KAAKkgB,MAAM1J,WAAW/S,KAAOzD,KAAKmgB,MAAM3J,WAAW/S,GAAAA,EAehDwc,EAAAA,UAAAA,YAAP,SAAmB1K,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAEzBxG,KAAK2Y,mBAAqBpD,EAAMxP,EAChC/F,KAAK4Y,mBAAqBrD,EAAM/T,EAEb,QAAfxB,KAAK2V,QACP3V,KAAKqE,GAAKkR,EAAMxP,EAChB/F,KAAKsE,GAAKiR,EAAM/T,EAChBxB,KAAKuE,GAAKgR,EAAMxP,EAChB/F,KAAKwE,GAAK+Q,EAAM/T,GAGlBxB,KAAKogB,oBAAsBpgB,KAAKqE,GAChCrE,KAAKqgB,oBAAsBrgB,KAAKsE,GAChCtE,KAAKsgB,oBAAsBtgB,KAAKuE,GAChCvE,KAAKugB,oBAAsBvgB,KAAKwE,GAEb,QAAfxE,KAAK2V,QACP3V,KAAK+Y,SACD/Y,KAAKkgB,MAAM1J,WAAWhQ,GACxBxG,KAAKgZ,WAAahZ,KAAKkgB,MACdlgB,KAAKmgB,MAAM3J,WAAWhQ,GAC/BxG,KAAKgZ,WAAahZ,KAAKmgB,MAEvBngB,KAAKgZ,gBAAAA,EAGHhZ,KAAKgZ,WACPhZ,KAAKkV,OAAS,SAEdlV,KAAKkV,OAAS,SAWb+K,EAAAA,UAAAA,UAAP,SAAiB1K,GACf,IAAMqE,EAAU5Z,KAAK2V,MACrB1C,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GACG,aAAfvV,KAAK2V,OAAwB/G,KAAK4L,IAAIxa,KAAKqE,GAAKrE,KAAKuE,IAAM,IAAMqK,KAAK4L,IAAIxa,KAAKsE,GAAKtE,KAAKwE,IAAM,IACjGxE,KAAKuE,GAAKvE,KAAKqE,GAAKrE,KAAKwgB,cACzBxgB,KAAKygB,eACLzgB,KAAK2Z,oBAEL3Z,KAAK+Z,WAAWxE,GAElBvV,KAAKkV,OAAS,SACE,aAAZ0E,GAA0B5Z,KAAKga,iBACjCha,KAAKga,gBAAgBha,KAAK,EAQpBigB,EAAAA,UAAAA,aAAV,aAOOA,EAAAA,UAAAA,WAAP,SAAkB1K,GACG,aAAfvV,KAAK2V,MACP3V,KAAKka,OAAO3E,GACY,SAAfvV,KAAK2V,OACd3V,KAAKqE,GAAKrE,KAAKogB,oBAAsB7K,EAAMxP,EAAI/F,KAAK2Y,mBACpD3Y,KAAKsE,GAAKtE,KAAKqgB,oBAAsB9K,EAAM/T,EAAIxB,KAAK4Y,mBACpD5Y,KAAKuE,GAAKvE,KAAKsgB,oBAAsB/K,EAAMxP,EAAI/F,KAAK2Y,mBACpD3Y,KAAKwE,GAAKxE,KAAKugB,oBAAsBhL,EAAM/T,EAAIxB,KAAK4Y,mBACpD5Y,KAAKygB,eACLzgB,KAAK2Z,oBACmB,WAAf3Z,KAAK2V,OACd3V,KAAKka,OAAO3E,EAAAA,EAQN0K,EAAAA,UAAAA,OAAV,SAAiB1K,GACf,OAAOvV,KAAKgZ,YACV,KAAKhZ,KAAKkgB,MACRlgB,KAAKqE,GAAKkR,EAAMxP,EAChB/F,KAAKsE,GAAKiR,EAAM/T,EAChB,MACF,KAAKxB,KAAKmgB,MACV,UAAK,EACHngB,KAAKuE,GAAKgR,EAAMxP,EAChB/F,KAAKwE,GAAK+Q,EAAM/T,EAGpBxB,KAAKygB,eACLzgB,KAAK2Z,kBAAkB,EAMlBsG,EAAAA,UAAAA,OAAP,WACEhN,EAAAA,UAAM8F,OAAAA,KAAAA,MACN/Y,KAAK2Z,mBACL3Z,KAAKib,WAAWzQ,MAAM4C,QAAU,IAM3B6S,EAAAA,UAAAA,SAAP,WACEhN,EAAAA,UAAMiI,SAAAA,KAAAA,MACNlb,KAAKib,WAAWzQ,MAAM4C,QAAU,QAMxB6S,EAAAA,UAAAA,gBAAV,WACEjgB,KAAKib,WAAa3X,EAAU2T,cAC5BjX,KAAK2P,UAAUjK,YAAY1F,KAAKib,YAEhCjb,KAAK0b,kBAEL1b,KAAKib,WAAWzQ,MAAM4C,QAAU,QAG1B6S,EAAAA,UAAAA,iBAAR,WACEjgB,KAAK2b,eAAe,EAMZsE,EAAAA,UAAAA,gBAAV,WACEjgB,KAAKkgB,MAAQlgB,KAAK4b,aAClB5b,KAAKmgB,MAAQngB,KAAK4b,aAElB5b,KAAK2b,eAAe,EAOZsE,EAAAA,UAAAA,WAAV,WACE,IAAMpE,EAAO,IAAIzE,EAIjB,OAHAyE,EAAK7E,OAAOU,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACnD5X,KAAKib,WAAWvV,YAAYmW,EAAK7E,QAE1B6E,CAAAA,EAMCoE,EAAAA,UAAAA,cAAV,WACE,IAAMnE,EAAW9b,KAAKkgB,MAAM/I,UAE5BnX,KAAKkc,aAAalc,KAAKkgB,MAAMlJ,OAAQhX,KAAKqE,GAAKyX,EAAW,EAAG9b,KAAKsE,GAAKwX,EAAW,GAClF9b,KAAKkc,aAAalc,KAAKmgB,MAAMnJ,OAAQhX,KAAKuE,GAAKuX,EAAW,EAAG9b,KAAKwE,GAAKsX,EAAW,EAAE,EAS5EmE,EAAAA,UAAAA,aAAV,SAAuBpE,EAA0B9V,EAAWvE,GAC1D,IAAMyW,EAAY4D,EAAKnE,UAAUzQ,QAAQsS,QAAQ,GACjDtB,EAAUkD,aAAapV,EAAGvE,GAC1Bqa,EAAKnE,UAAUzQ,QAAQyS,YAAYzB,EAAW,EAAE,EAM3CgI,EAAAA,UAAAA,SAAP,WAQE,OAPsC3gB,OAAO6c,OAAO,CAClD9X,GAAIrE,KAAKqE,GACTC,GAAItE,KAAKsE,GACTC,GAAIvE,KAAKuE,GACTC,GAAIxE,KAAKwE,IACRyO,EAAAA,UAAMqC,SAAAA,KAAAA,MAAAA,EASJ2K,EAAAA,UAAAA,aAAP,SAAoBtK,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB,IAAM+K,EAAW/K,EACjB3V,KAAKqE,GAAKqc,EAASrc,GACnBrE,KAAKsE,GAAKoc,EAASpc,GACnBtE,KAAKuE,GAAKmc,EAASnc,GACnBvE,KAAKwE,GAAKkc,EAASlc,EAAE,EAShByb,EAAAA,UAAAA,MAAP,SAAapK,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKqE,GAAKrE,KAAKqE,GAAKwR,EACpB7V,KAAKsE,GAAKtE,KAAKsE,GAAKwR,EACpB9V,KAAKuE,GAAKvE,KAAKuE,GAAKsR,EACpB7V,KAAKwE,GAAKxE,KAAKwE,GAAKsR,EAEpB9V,KAAKygB,eACLzgB,KAAK2Z,kBAAkB,EAAlBA,CAAAA,CD5JY,CCjKiB3E,GAAAA,EAAAA,SAAAA,GC0DpC,WAAYrF,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OA/B3B5N,EAAAA,YAAc,cAIdA,EAAAA,YAAc,EAIdA,EAAAA,gBAAkB,GAyB1BA,EAAKgW,eAAiBhW,EAAKgW,eAAejR,KAAK/E,GAC/CA,EAAKkW,eAAiBlW,EAAKkW,eAAenR,KAAK/E,GAC/CA,EAAKmW,mBAAqBnW,EAAKmW,mBAAmBpR,KAAK/E,GAEvDA,EAAKuW,YAAc3I,EAAS8K,aAC5B1Y,EAAKwW,YAAc5I,EAAS+K,mBAC5B3Y,EAAKyW,gBAAkB7I,EAASgL,uBAEhC5Y,EAAK6Y,YAAc,IAAItM,EACrB,aACAqB,EAASkL,gBACTlL,EAAS8K,cAEX1Y,EAAK6Y,YAAYpL,eAAiBzN,EAAKgW,eAEvChW,EAAK+Y,iBAAmB,IAAI9B,EAC1B,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAK+Y,iBAAiBjB,eAAiB9X,EAAKkW,eAE5ClW,EAAKiZ,iBAAmB,IAAIhB,EAC1B,aACArK,EAASsL,wBACTtL,EAASgL,wBAEX5Y,EAAKiZ,iBAAiBR,eAAiBzY,EAAKmW,mBAAAA,CAAAA,CAuJhD,OAhPgCld,EAAAA,EAAAA,GAiGvB6gB,EAAAA,UAAAA,WAAP,SAAkBld,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAK4gB,cACZnd,IAAOzD,KAAK6gB,YAAAA,EAQRF,EAAAA,UAAAA,aAAR,WACE3gB,KAAKgX,OAAS1T,EAAU2T,cACxBjX,KAAK4gB,aAAetd,EAAUmY,WAC5Bzb,KAAKqE,GACLrE,KAAKsE,GACLtE,KAAKuE,GACLvE,KAAKwE,GACL,CACE,CAAC,SAAU,eACX,CAAC,gBAAiBxE,KAAKqd,YAAc,IAAIlZ,cAG7CnE,KAAK6gB,YAAcvd,EAAUmY,WAC3Bzb,KAAKqE,GACLrE,KAAKsE,GACLtE,KAAKuE,GACLvE,KAAKwE,GACL,CACE,CAAC,SAAUxE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,cAGtCnE,KAAKgX,OAAOtR,YAAY1F,KAAK4gB,cAC7B5gB,KAAKgX,OAAOtR,YAAY1F,KAAK6gB,aAE7B7gB,KAAKwd,2BAA2Bxd,KAAKgX,OAAO,EASvC2J,EAAAA,UAAAA,YAAP,SAAmBpL,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GACN,QAAfxG,KAAK2V,QACP3V,KAAKid,eACLjd,KAAKygB,eAELzgB,KAAKkV,OAAS,aAORyL,EAAAA,UAAAA,aAAV,WACM3gB,KAAK4gB,cAAgB5gB,KAAK6gB,cAC5B7gB,KAAK4gB,aAAa7c,aAAa,KAAM/D,KAAKqE,GAAGF,YAC7CnE,KAAK4gB,aAAa7c,aAAa,KAAM/D,KAAKsE,GAAGH,YAC7CnE,KAAK4gB,aAAa7c,aAAa,KAAM/D,KAAKuE,GAAGJ,YAC7CnE,KAAK4gB,aAAa7c,aAAa,KAAM/D,KAAKwE,GAAGL,YAE7CnE,KAAK6gB,YAAY9c,aAAa,KAAM/D,KAAKqE,GAAGF,YAC5CnE,KAAK6gB,YAAY9c,aAAa,KAAM/D,KAAKsE,GAAGH,YAC5CnE,KAAK6gB,YAAY9c,aAAa,KAAM/D,KAAKuE,GAAGJ,YAC5CnE,KAAK6gB,YAAY9c,aAAa,KAAM/D,KAAKwE,GAAGL,YAE5Cb,EAAUc,cAAcpE,KAAK6gB,YAAa,CAAC,CAAC,SAAU7gB,KAAKod,eAC3D9Z,EAAUc,cAAcpE,KAAK6gB,YAAa,CAAC,CAAC,eAAgB7gB,KAAKqd,YAAYlZ,cAC7Eb,EAAUc,cAAcpE,KAAK6gB,YAAa,CAAC,CAAC,mBAAoB7gB,KAAKsd,gBAAgBnZ,cAAAA,EAQ/Ewc,EAAAA,UAAAA,eAAV,SAAyBpN,GACvBvT,KAAKod,YAAc7J,EACnBvT,KAAKygB,eACLzgB,KAAK8U,aAAavB,EAAAA,EAMVoN,EAAAA,UAAAA,eAAV,SAAyB3c,GACvBhE,KAAKqd,YAAcrZ,EACnBhE,KAAKygB,cAAc,EAOXE,EAAAA,UAAAA,mBAAV,SAA6BlD,GAC3Bzd,KAAKsd,gBAAkBG,EACvBzd,KAAKygB,eACLzgB,KAAK6U,cAAc,EAMrBvV,OAAAA,eAAWqhB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAC3gB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAMjDa,EAAAA,UAAAA,SAAP,WACE,IAAM1f,EAA0B3B,OAAO6c,OAAO,CAC5CiB,YAAapd,KAAKod,YAClBC,YAAard,KAAKqd,YAClBC,gBAAiBtd,KAAKsd,iBACrBrK,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAWkT,EAAWlT,SAEtBxM,CAAAA,EAQF0f,EAAAA,UAAAA,aAAP,SAAoBhL,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GAEnB,IAAMmL,EAAUnL,EAChB3V,KAAKod,YAAc0D,EAAQ1D,YAC3Bpd,KAAKqd,YAAcyD,EAAQzD,YAC3Brd,KAAKsd,gBAAkBwD,EAAQxD,gBAE/Btd,KAAKid,eACLjd,KAAKygB,cAAc,EAxOPE,EAAAA,SAAW,aAKXA,EAAAA,MAAQ,cAIRA,EAAAA,KAAAA,+DAAAA,CAAAA,CDdsB3L,CCDNiL,GAAAA,EAAAA,SAAAA,GCiB9B,WAAYvS,EAAeqT,EAAiBC,EAAsBrT,GAAlE,MACEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,yLAAAA,KAAAA,OAlBP9G,EAAAA,MAAkB,GAGlBA,EAAAA,UAA8B,GAgBpCA,EAAKka,MAAQA,EACbla,EAAKma,YAAcA,EAEnBna,EAAKoa,eAAiBpa,EAAKoa,eAAerV,KAAK/E,GAAAA,CAAAA,CA+DnD,OAtFqC/G,EAAAA,EAAAA,GA6B5BohB,EAAAA,UAAAA,MAAP,sBACQ7N,EAAW9P,SAASqD,cAAc,OA0CxC,OAxCAyM,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMsC,SAAW,IAC1B9M,KAAK+gB,MAAM1T,SAAQ,SAAC8T,GAClB,IAAMC,EAAmB7d,SAASqD,cAAc,OAChDwa,EAAiB5W,MAAM4C,QAAU,eAEjCgU,EAAiB5W,MAAMyT,WAAa,SACpCmD,EAAiB5W,MAAM0T,eAAiB,gBACxCkD,EAAiB5W,MAAMoJ,QAAU,MACjCwN,EAAiB5W,MAAMuJ,YAAc,MACrCqN,EAAiB5W,MAAMwJ,YAAc,QACrCoN,EAAiB5W,MAAM8I,SAAW,SAClC8N,EAAiB5W,MAAMyU,SAAc,IAAMpY,EAAKka,MAAMre,OAAS,MAC/D0e,EAAiB5W,MAAM0J,YACrBiN,IAASta,EAAKma,YAAcna,EAAK2E,gBAAgBjC,mBAAqB,cAExE6X,EAAiBxT,iBAAiB,oBAChC/G,EAAKoa,eAAeE,EAAMC,EAAAA,IAE5B/N,EAAS3N,YAAY0b,GAErB,IAAMC,EAAU9d,SAASqD,cAAc,OACvCya,EAAQ7W,MAAM4C,QAAU,OACxBiU,EAAQ7W,MAAM6T,UAAY,OAC1BgD,EAAQ7W,MAAMsC,SAAW,IACzBuU,EAAQ7W,MAAM8W,WAAaH,EAC3BE,EAAQ7W,MAAM8I,SAAW,SAEzB,IAAMiO,EAAYhe,SAASqD,cAAc,OACzC2a,EAAU/W,MAAMkC,WAAa,SAC7B6U,EAAU/W,MAAM8I,SAAW,SAC3BiO,EAAU/W,MAAMgX,aAAe,WAC/BD,EAAUna,UAAY,8CAEtBia,EAAQ3b,YAAY6b,GAEpBH,EAAiB1b,YAAY2b,GAE7Bxa,EAAK4a,UAAU9e,KAAKye,EAAAA,IAEf/N,CAAAA,EAGD6N,EAAAA,UAAAA,eAAR,SAAuBQ,EAAiBlb,GAAxC,WACExG,KAAKghB,YAAcU,EAEnB1hB,KAAKyhB,UAAUpU,SAAQ,SAAAgH,GACrBA,EAAI7J,MAAM0J,YAAcG,IAAQ7N,EAASK,EAAK2E,gBAAgBjC,mBAAqB,iBAGjFvJ,KAAK2hB,eACP3hB,KAAK2hB,cAAc3hB,KAAKghB,YAAY,EAAZA,CAAAA,CDlFEf,CCDK1L,GAAAA,EAAAA,SAAAA,GCqEnC,WACE5E,EACA6E,EACAC,GAHF,MAKExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAtD3B5N,EAAAA,MAAQ,cAQRA,EAAAA,QAAU,EAWHA,EAAAA,aAAe,iBACxBA,EAAAA,KAAeA,EAAK+a,aAkBlB/a,EAAAA,SAAAA,EAkBRA,EAAK0M,MAAQkB,EAAS8K,aACtB1Y,EAAKya,WAAa7M,EAASoN,kBAE3Bhb,EAAKiT,YAAc,CAAE/T,EAAG,IAAKvE,EAAG,IAEhCqF,EAAKib,SAAWjb,EAAKib,SAASlW,KAAK/E,GACnCA,EAAKkb,QAAUlb,EAAKkb,QAAQnW,KAAK/E,GACjCA,EAAKmb,WAAanb,EAAKmb,WAAWpW,KAAK/E,GACvCA,EAAKob,SAAWpb,EAAKob,SAASrW,KAAK/E,GACnCA,EAAKqb,mBAAqBrb,EAAKqb,mBAAmBtW,KAAK/E,GACvDA,EAAKsb,eAAiBtb,EAAKsb,eAAevW,KAAK/E,GAC/CA,EAAK0T,QAAU1T,EAAK0T,QAAQ3O,KAAK/E,GACjCA,EAAKub,mBAAqBvb,EAAKub,mBAAmBxW,KAAK/E,GACvDA,EAAKwb,SAAWxb,EAAKwb,SAASzW,KAAK/E,GAEnCA,EAAKyb,WAAa,IAAIlP,EACpB,QACAqB,EAASkL,gBACTlL,EAAS8K,cAEX1Y,EAAKyb,WAAWhO,eAAiBzN,EAAKib,SAEtCjb,EAAK0b,gBAAkB,IAAIrB,EACzB,OACAzM,EAAS+N,oBACT/N,EAASoN,mBAEXhb,EAAK0b,gBAAgBZ,cAAgB9a,EAAKkb,QAAAA,CAAAA,CAihB9C,OAxnBgCjiB,EAAAA,EAAAA,GA+GvB2iB,EAAAA,UAAAA,WAAP,SAAkBhf,GAChB,GACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAK0iB,aACZjf,IAAOzD,KAAK2iB,YAEZ,OAAM,EAEN,IAAIC,GAAAA,EAMJ,OALA5iB,KAAK0iB,YAAYjN,WAAWpI,SAAQ,SAACwV,GAC/BA,IAASpf,IACXmf,GAAAA,EAAQ,IAGLA,CAAAA,EAODH,EAAAA,UAAAA,aAAV,WACEziB,KAAKgX,OAAS1T,EAAU2T,cAExBjX,KAAK2iB,YAAcrf,EAAUgY,WAAW,EAAG,EAAG,CAAC,CAAC,OAAQ,iBACxDtb,KAAKgX,OAAOtR,YAAY1F,KAAK2iB,aAE7B3iB,KAAK0iB,YAAcpf,EAAUwf,WAAW,CACtC,CAAC,OAAQ9iB,KAAKuT,OACd,CAAC,cAAevT,KAAKshB,YACrB,CAAC,YAAa,QACd,CAAC,IAAK,KACN,CAAC,IAAK,OAERthB,KAAK0iB,YAAYhL,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACxD5X,KAAK0iB,YAAYhL,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBAExD5X,KAAKgX,OAAOtR,YAAY1F,KAAK0iB,aAE7B1iB,KAAKwd,2BAA2Bxd,KAAKgX,QACrChX,KAAKgiB,YAAY,EASZS,EAAAA,UAAAA,YAAP,SAAmBlN,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAEzBxG,KAAK+iB,SAAAA,EACL/iB,KAAKgjB,iBAAmBzN,EACxBvV,KAAKijB,qBAAuBC,KAAKC,MAEd,QAAfnjB,KAAK2V,QACP3V,KAAKid,eACLjd,KAAKqZ,WAAW9D,GAChBvV,KAAKkV,OAAS,aAIVuN,EAAAA,UAAAA,SAAR,WACE,SAASW,EAAmBC,GAC1B,IAEIC,EAAmBD,EAAU,GAAG3gB,OAOpC,OANA2gB,EAAUhW,SAAQ,SAAA5I,GACZA,EAAK/B,OAAS4gB,IAChBA,EAAmB7e,EAAK/B,OAAAA,IALE,IASvB4gB,EAA2CD,EAAU3gB,MAAM,CAGpE,GAAkB,KAAd1C,KAAK2F,KAAa,CAQpB,IAPA,IAAM4d,EAAQvjB,KAAK2F,KAAK6d,MAAM,mCACxBC,EAA8B,EAAbzjB,KAAKgE,MAAchE,KAAKiE,OAC3Cyf,EAAAA,IAAqBjkB,MAAAA,KAAAA,MAAAA,MAAAA,EAAAA,MAAAA,GAAiB8jB,KAEtCI,EAAkBP,EAAmBM,GAErCE,EAAgBC,OAAOC,UAAAA,EAAAA,WAEzB,IAAIC,EAAcL,EAAe,GACjCA,EAAerW,SAAQ,SAAA5I,GACjBA,EAAK/B,OAASqhB,EAAYrhB,SAC5BqhB,EAActf,EAAAA,KAGlBmf,EAAgBG,EAAYC,YAAY,IAAKJ,EAAgB,IAEzC,GAClBF,EAAiB,GACjBH,EAAMlW,SAAQ,SAAA5I,GAEZ,IADA,IAAIwf,EAAexf,EACZwf,EAAavhB,OAASkhB,GAAe,CAC1C,IAAIM,EAASD,EAAaD,YAAY,IAAKJ,GACvCM,EAAS,IAEXA,EAASD,EAAa1V,QAAQ,MAE5B2V,EAAS,GACXR,EAAe/gB,KAAKshB,EAAaE,UAAU,EAAGD,IAC9CD,EAAeA,EAAaE,UAAUD,GAAQ1U,SAE9CkU,EAAe/gB,KAAKshB,GACpBA,EAAe,IAGnBP,EAAe/gB,KAAKshB,EAAAA,IAEtBN,EAAkBP,EAAmBM,IAGrCC,GAAmB,CAAC,EAhCjBA,EAAkBF,GAAAA,IAoCzB,OAAOC,EAAeU,KAAK,QAE3B,OAAOpkB,KAAK2F,IAAI,EAIZ8c,EAAAA,UAAAA,WAAR,sBAGE,GAAIziB,KAAK0iB,YAAa,CACpB,KAAO1iB,KAAK0iB,YAAY2B,WACtBrkB,KAAK0iB,YAAYvX,YAAYnL,KAAK0iB,YAAY2B,YAG1BrkB,KAAK4U,eAAeyN,SAAWriB,KAAKqiB,WAAariB,KAAK2F,MAChD6d,MAAM,mCAC5BnW,SAAQ,SAAC5I,GACboC,EAAK6b,YAAYhd,YACfpC,EAAUghB,YAEQ,KAAhB7f,EAAK+K,OAAgB,IAAM/K,EAAK+K,OAAQ,CACxC,CAAC,IAAK,KACN,CAAC,KAfS,eAoBhBmD,WAAW3S,KAAKiiB,SAAU,GAAG,CAAH,EAItBQ,EAAAA,UAAAA,aAAR,WACE,IAAM8B,EAAWvkB,KAAK0iB,YAAY8B,UAC9B7H,EAAQ,EACZ,GAAI4H,EAASvgB,MAAQ,GAAKugB,EAAStgB,OAAS,EAAG,CAC7C,IAAMwgB,GACU,EAAbzkB,KAAKgE,MAAehE,KAAKgE,MAAQhE,KAAK4T,QAAU,EAAK,KACtD2Q,EAASvgB,MACL0gB,GACW,EAAd1kB,KAAKiE,OAAgBjE,KAAKiE,OAASjE,KAAK4T,QAAU,EAAK,KACxD2Q,EAAStgB,OACX0Y,EAAQ/N,KAAK+V,IAAIF,EAAQC,EAAAA,CAE3B,OAAO/H,CAAAA,EAGD8F,EAAAA,UAAAA,gBAAR,SAAwB9F,GACtB,IAAMiI,EAAgE,QAAxD/c,OAAOgd,iBAAiB7kB,KAAK0iB,aAAaoC,UAAsB,GAAK,EAC7EP,EAAWvkB,KAAK0iB,YAAY8B,UAC9Bze,EAAI,EACJvE,EAAI,EAKR,OAJI+iB,EAASvgB,MAAQ,GAAKugB,EAAStgB,OAAS,IAC1C8B,GAAK/F,KAAKgE,MAAQ4gB,EAAQL,EAASvgB,MAAQ2Y,GAAS,EACpDnb,EAAIxB,KAAKiE,OAAS,EAAKsgB,EAAStgB,OAAS0Y,EAAS,GAE7C,CAAE5W,EAAGA,EAAGvE,EAAGA,EAAAA,EAGZihB,EAAAA,UAAAA,SAAR,WACE,IAAMsC,EAAW/kB,KAAK0iB,YAAY8B,UAC5B7H,EAAQ3c,KAAKglB,eACbC,EAAWjlB,KAAKklB,gBAAgBvI,GACtCsI,EAASzjB,GAAKujB,EAASvjB,EAAImb,EAEvBwI,UAAUC,UAAU7W,QAAQ,UAAY,EAE1CvO,KAAK0iB,YAAYlY,MAAMkN,UAAY,aAAauN,EAASlf,EAAAA,OAAQkf,EAASzjB,EAAAA,aAAcmb,EAAAA,KAAUA,EAAAA,KAElG3c,KAAK0iB,YAAYhL,UAAUzQ,QACxBsS,QAAQ,GACR4B,aAAa8J,EAASlf,EAAGkf,EAASzjB,GACrCxB,KAAK0iB,YAAYhL,UAAUzQ,QAAQsS,QAAQ,GAAG8L,SAAS1I,EAAOA,GAAAA,EAS3D8F,EAAAA,UAAAA,WAAP,SAAkBlN,GAChBtC,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,QAAAA,IACbvV,KAAKgjB,mBACPhjB,KAAK+iB,QACHnU,KAAK4L,IAAIjF,EAAMxP,EAAI/F,KAAKgjB,iBAAiBjd,GAAK,GAC9C6I,KAAK4L,IAAIjF,EAAM/T,EAAIxB,KAAKgjB,iBAAiBxhB,GAAK,IAQ1CihB,EAAAA,UAAAA,OAAV,SAAiBlN,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbvV,KAAK+iB,SAAAA,EACL/iB,KAAKua,UACDva,KAAK4U,eAAeyN,SACtBriB,KAAKgiB,aAELhiB,KAAKiiB,UAAU,EAOTQ,EAAAA,UAAAA,QAAV,WACExP,EAAAA,UAAMsH,QAAAA,KAAAA,MACFva,KAAKgX,QAAUhX,KAAK2iB,cACtBrf,EAAUc,cAAcpE,KAAKgX,OAAQ,CACnC,CAAC,QAAShX,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,cAEzBb,EAAUc,cAAcpE,KAAK2iB,YAAa,CACxC,CAAC,QAAS3iB,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,cAAAA,EAUtBse,EAAAA,UAAAA,UAAP,SAAiBlN,GACf,IAAMqE,EAAU5Z,KAAK2V,MACL,aAAZiE,IACF5Z,KAAKia,4BAAAA,GAEPhH,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GAChBvV,KAAKua,WAES,aAAZX,IACE5Z,KAAK+iB,SAAWG,KAAKC,MAAQnjB,KAAKijB,qBAAuB,MAE3DjjB,KAAKmiB,iBAEPniB,KAAKgjB,sBAAAA,CAAmB5X,EAGlBqX,EAAAA,UAAAA,eAAR,sBACEziB,KAAKkV,OAAS,OACdlV,KAAKwU,iBAAiBpN,UAAY,GAElCpH,KAAKslB,YAAc/hB,SAASqD,cAAc,OAE1C5G,KAAKslB,YAAY9a,MAAMsC,SAAW,IAElC9M,KAAKslB,YAAY9a,MAAMyT,WAAa,SACpCje,KAAKslB,YAAY9a,MAAM0T,eAAiB,SACxCle,KAAKslB,YAAY9a,MAAMsG,cAAgB,OACvC9Q,KAAKslB,YAAY9a,MAAM8I,SAAW,SAElCtT,KAAKulB,WAAahiB,SAASqD,cAAc,OACzC5G,KAAKulB,WAAW/a,MAAMya,SAAW,WACjCjlB,KAAKulB,WAAW/a,MAAM8W,WAAathB,KAAKshB,WACxCthB,KAAKulB,WAAW/a,MAAMgb,WAAa,MACnCxlB,KAAKulB,WAAWpH,UAAYne,KAAK2F,KACjC3F,KAAKulB,WAAWE,gBAAkB,OAClCzlB,KAAKulB,WAAW/a,MAAM+I,MAAQvT,KAAKuT,MACnCvT,KAAKulB,WAAW/a,MAAMkC,WAAa,MAEnC1M,KAAKoiB,qBACLpiB,KAAKulB,WAAW3X,iBAAiB,sBAAc8X,GAC7CA,EAAGC,iBAAAA,IAEA3lB,KAAK4U,eAAeyN,UACvBriB,KAAKulB,WAAW3X,iBAAiB,oBAE/B,IADA,IAAIgY,EAAW/B,OAAOgC,WAAWhf,EAAK0e,WAAW/a,MAAMob,UAErD/e,EAAK0e,WAAWzW,aACd+U,OAAOiC,SAASjf,EAAK0e,WAAW/a,MAAMyU,WACxC2G,EAAW,IAEXA,GAAY,GACZ/e,EAAK0e,WAAW/a,MAAMob,SAAchX,KAAKmX,IAAIH,EAAU,YAI7D5lB,KAAKulB,WAAW3X,iBAAiB,kBAAU8X,GACzCA,EAAGM,cAAAA,CAAe,IAEpBhmB,KAAKulB,WAAW3X,iBAAiB,kBAAU8X,GACzC,GAAIA,EAAGO,cAAe,CAEpB,IAAMC,EAAUR,EAAGO,cAAcE,QAAQ,QACnCC,EAAYve,OAAOwe,eACzB,IAAKD,EAAUE,WAAY,OAAM,EACjCF,EAAUG,qBACVH,EAAUI,WAAW,GAAGC,WAAWljB,SAASmjB,eAAeR,IAC3DR,EAAGiB,gBAAgB,CAAhBA,IAIP3mB,KAAKslB,YAAY1X,iBAAiB,wBAChC/G,EAAKqb,mBAAmBrb,EAAK0e,WAAWpH,UAAAA,IAE1Cne,KAAKslB,YAAY5f,YAAY1F,KAAKulB,YAClCvlB,KAAKwU,iBAAiB9O,YAAY1F,KAAKslB,aAEvCtlB,KAAK4mB,aAEL5mB,KAAKulB,WAAWsB,QAChBtjB,SAASujB,YAAY,cAGfrE,EAAAA,UAAAA,mBAAR,WACE,GAAmB,SAAfziB,KAAK2V,MACP,YAAI3V,KAAKulB,WACPvlB,KAAKmiB,sBAEL,GAAIniB,KAAK4U,eAAeyN,SACtBriB,KAAKulB,WAAW/a,MAAMuN,KAAU/X,KAAK+X,KAAO/X,KAAK4T,QAAAA,KACjD5T,KAAKulB,WAAW/a,MAAMyE,IAASjP,KAAKiP,IAAMjP,KAAK4T,QAAAA,KAC/C5T,KAAKulB,WAAW/a,MAAMxG,MAAWhE,KAAKgE,MAAuB,EAAfhE,KAAK4T,QAAAA,KACnD5T,KAAKulB,WAAW/a,MAAMvG,OAAYjE,KAAKiE,OAAwB,EAAfjE,KAAK4T,QAAAA,KACrD5T,KAAKulB,WAAW/a,MAAMuc,UAAY/mB,KAAKulB,WAAW/a,MAAMvG,OACxDjE,KAAKulB,WAAW/a,MAAMkC,WAAa,WAC9B,CACL1M,KAAK0iB,YAAYlY,MAAM4C,QAAU,GACjC,IAAM4Z,EAAYhnB,KAAKglB,eAEjBiC,EAAYjnB,KAAKkZ,YAAY,CACjCnT,EAAG/F,KAAK+X,KAAO/X,KAAKgE,MAAQ,EAC5BxC,EAAGxB,KAAKiP,IAAMjP,KAAKiE,OAAS,IAExBsgB,EAAWvkB,KAAK0iB,YAAY8B,UAC5B0C,EAAM,CACVnhB,EAAGwe,EAASvgB,MAAQgjB,EACpBxlB,EAAG+iB,EAAStgB,OAAS+iB,GAEvBC,EAAUlhB,GAAKmhB,EAAInhB,EAAI,EACvBkhB,EAAUzlB,GAAK0lB,EAAI1lB,EAAI,EAEvBxB,KAAKulB,WAAW/a,MAAMyE,IAASgY,EAAUzlB,EAAAA,KACzCxB,KAAKulB,WAAW/a,MAAMuN,KAAUkP,EAAUlhB,EAAAA,KAC1C/F,KAAKulB,WAAW/a,MAAMyU,SACpBjf,KAAKwU,iBAAiBnF,YAAc4X,EAAUlhB,EAAAA,KAEhD/F,KAAKulB,WAAW/a,MAAMob,SAAchX,KAAKmX,IAAI,GAAKiB,EAAW,SAC7DhnB,KAAK0iB,YAAYlY,MAAM4C,QAAU,SAMjCqV,EAAAA,UAAAA,mBAAR,SAA2B9c,GACzB3F,KAAK2F,KAAOA,EAAK6J,OACjBxP,KAAKwU,iBAAiBpN,UAAY,GAClCpH,KAAKgiB,aACLhiB,KAAKmnB,aACDnnB,KAAKia,6BACPja,KAAKia,4BAAAA,EACDja,KAAKga,iBACPha,KAAKga,gBAAgBha,OAGzBA,KAAK6U,cAAc,EAGd4N,EAAAA,UAAAA,OAAP,WACExP,EAAAA,UAAM8F,OAAAA,KAAAA,MACa,SAAf/Y,KAAK2V,OACP3V,KAAKkiB,mBAAmBliB,KAAKulB,WAAWpH,UAAU,EAO/CsE,EAAAA,UAAAA,SAAP,WACqB,SAAfziB,KAAK2V,OACP3V,KAAKkiB,mBAAmBliB,KAAKulB,WAAWpH,WAE1ClL,EAAAA,UAAMiI,SAAAA,KAAAA,KAAAA,EAQDuH,EAAAA,UAAAA,SAAP,SAAgBlN,EAAe/O,GAC7ByM,EAAAA,UAAMmU,SAAAA,KAAAA,KAAS7R,EAAO/O,GAEtBxG,KAAKmiB,gBAAgB,EAObM,EAAAA,UAAAA,SAAV,SAAmBlP,GACbvT,KAAK0iB,aACPpf,EAAUc,cAAcpE,KAAK0iB,YAAa,CAAC,CAAC,OAAQnP,KAEtDvT,KAAKuT,MAAQA,EACTvT,KAAKulB,aACPvlB,KAAKulB,WAAW/a,MAAM+I,MAAQvT,KAAKuT,OAErCvT,KAAK8U,aAAavB,EAAAA,EAOVkP,EAAAA,UAAAA,QAAV,SAAkBtB,GACZnhB,KAAK0iB,aACPpf,EAAUc,cAAcpE,KAAK0iB,YAAa,CAAC,CAAC,cAAevB,KAE7DnhB,KAAKshB,WAAaH,EACdnhB,KAAKulB,aACPvlB,KAAKulB,WAAW/a,MAAM8W,WAAathB,KAAKshB,YAE1CthB,KAAKgiB,aACLhiB,KAAK6U,cAAc,EAMX4N,EAAAA,UAAAA,WAAV,WACEziB,KAAK0iB,YAAYlY,MAAM4C,QAAU,OACjCpN,KAAKqnB,gBAAgB,EAKb5E,EAAAA,UAAAA,WAAV,WACqB,SAAfziB,KAAK2V,QACP3V,KAAKkV,OAAS,UAEhBlV,KAAK0iB,YAAYlY,MAAM4C,QAAU,GACjCpN,KAAKsnB,gBAAgB,EAMvBhoB,OAAAA,eAAWmjB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAACziB,KAAKsiB,WAAYtiB,KAAKuiB,gBAAgB,EAAhBA,YAAAA,EAAAA,cAAAA,IAMzBE,EAAAA,UAAAA,SAAP,WACE,IAAMxhB,EAA0B3B,OAAO6c,OACrC,CACE5I,MAAOvT,KAAKuT,MACZ+N,WAAYthB,KAAKshB,WACjB1N,QAAS5T,KAAK4T,QACdjO,KAAM3F,KAAK2F,KACX0c,SAAUriB,KAAK4U,eAAeyN,UAEhCpP,EAAAA,UAAMqC,SAAAA,KAAAA,OAIR,OAFArU,EAAOwM,SAAWgV,EAAWhV,SAEtBxM,CAAAA,EAQFwhB,EAAAA,UAAAA,aAAP,SAAoB9M,GAClB,IAAM4R,EAAY5R,EAClB3V,KAAKuT,MAAQgU,EAAUhU,MACvBvT,KAAKshB,WAAaiG,EAAUjG,WAC5BthB,KAAK4T,QAAU2T,EAAU3T,QACzB5T,KAAK2F,KAAO4hB,EAAU5hB,KAEtB3F,KAAKid,eACLhK,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKua,UACDva,KAAK4U,eAAeyN,UAEtBriB,KAAKgiB,YAAY,EAUdS,EAAAA,UAAAA,MAAP,SAAa5M,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKua,UACLva,KAAKiiB,WACLjiB,KAAKoiB,oBAAoB,EAhnBbK,EAAAA,SAAW,aAKXA,EAAAA,MAAQ,cAIRA,EAAAA,KAAAA,gHAAAA,CAAAA,CDfqBlO,CCALuD,GAAAA,EAAAA,SAAAA,GCgD9B,WACEnI,EACA6E,EACAC,GAHF,MAKExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAhC3B5N,EAAAA,MAAQ,cAIRA,EAAAA,UAAY,EAYdA,EAAAA,SAAAA,EAEAA,EAAAA,WAAa,EAgBnBA,EAAK0M,MAAQkB,EAAS8K,aACtB1Y,EAAKkX,UAAYtJ,EAAS+K,mBAC1B3Y,EAAK2gB,WAAa/S,EAASgT,mBAE3B5gB,EAAKib,SAAWjb,EAAKib,SAASlW,KAAK/E,GACnCA,EAAK6gB,UAAY7gB,EAAK6gB,UAAU9b,KAAK/E,GACrCA,EAAK8gB,eAAiB9gB,EAAK8gB,eAAe/b,KAAK/E,GAC/CA,EAAK+gB,aAAe/gB,EAAK+gB,aAAahc,KAAK/E,GAE3CA,EAAKyb,WAAa,IAAIlP,EACpB,QACAqB,EAASkL,gBACTlL,EAAS8K,cAEX1Y,EAAKyb,WAAWhO,eAAiBzN,EAAKib,SAEtCjb,EAAKghB,eAAiB,IAAI/J,EACxB,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAKghB,eAAelJ,eAAiB9X,EAAK+gB,aAAAA,CAAAA,CA0R9C,OArWoC9nB,EAAAA,EAAAA,GAoF3BgoB,EAAAA,UAAAA,WAAP,SAAkBrkB,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAK+nB,aAAAA,EAQRD,EAAAA,UAAAA,aAAR,WACE9nB,KAAKgX,OAAS1T,EAAU2T,cACxBjX,KAAK+nB,aAAezkB,EAAU0kB,cAC9BhoB,KAAKgX,OAAOtR,YAAY1F,KAAK+nB,cAE7B,IAAM9P,EAAY3U,EAAUsU,kBAC5B5X,KAAKgX,OAAOU,UAAUzQ,QAAQ0Q,WAAWM,GACzCjY,KAAKwd,2BAA2Bxd,KAAKgX,OAAO,EASvC8Q,EAAAA,UAAAA,YAAP,SAAmBvS,EAAe/O,GACb,QAAfxG,KAAK2V,QACP3V,KAAK0nB,YAEL1nB,KAAKid,eAELjd,KAAKkV,OAAS,YAGG,aAAflV,KAAK2V,OACP3V,KAAKioB,cAAcC,YAAcloB,KAAKuT,MACtCvT,KAAKioB,cAAclK,UAAY/d,KAAK+d,UACpC/d,KAAKioB,cAAcE,YACnBnoB,KAAKioB,cAAcG,OAAO7S,EAAMxP,EAAGwP,EAAM/T,GACzCxB,KAAKqoB,SAAAA,GAELpV,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,EAAAA,EAStBshB,EAAAA,UAAAA,WAAP,SAAkBvS,GACG,aAAfvV,KAAK2V,MACH3V,KAAKqoB,UACProB,KAAKioB,cAAcK,OAAO/S,EAAMxP,EAAGwP,EAAM/T,GACzCxB,KAAKioB,cAAcM,UAGrBtV,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,EAAAA,EAQXuS,EAAAA,UAAAA,OAAV,SAAiBvS,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbjS,EAAUc,cAAcpE,KAAKgX,OAAQ,CACnC,CAAC,QAAShX,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,cAEzBb,EAAUc,cAAcpE,KAAK+nB,aAAc,CACzC,CAAC,QAAS/nB,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,aAAAA,EASpB2jB,EAAAA,UAAAA,UAAP,SAAiBvS,GACK,aAAhBvV,KAAKkV,OACHlV,KAAKqoB,UACProB,KAAKioB,cAAcO,YACnBxoB,KAAKqoB,SAAAA,EACDroB,KAAK4U,eAAe6T,8BACtBzoB,KAAK2nB,kBAIT1U,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,EAAAA,EAIZuS,EAAAA,UAAAA,UAAR,WACE9nB,KAAKwU,iBAAiBpN,UAAY,GAElCpH,KAAK0oB,cAAgBnlB,SAASqD,cAAc,UAC5C5G,KAAK0oB,cAAc1kB,MAAQhE,KAAKwU,iBAAiB1F,YAAc9O,KAAKwnB,WACpExnB,KAAK0oB,cAAczkB,OAASjE,KAAKwU,iBAAiBmU,aAAe3oB,KAAKwnB,WACtExnB,KAAKioB,cAAgBjoB,KAAK0oB,cAAchhB,WAAW,MACnD1H,KAAKioB,cAActL,MAAM3c,KAAKwnB,WAAYxnB,KAAKwnB,YAC/CxnB,KAAKwU,iBAAiB9O,YAAY1F,KAAK0oB,cAAc,EAMhDZ,EAAAA,UAAAA,OAAP,WACqB,aAAf9nB,KAAK2V,OACP3V,KAAK2nB,iBAEP1U,EAAAA,UAAM8F,OAAAA,KAAAA,KAAAA,EAMD+O,EAAAA,UAAAA,SAAP,WACqB,aAAf9nB,KAAK2V,OACP3V,KAAK2nB,iBAEP1U,EAAAA,UAAMiI,SAAAA,KAAAA,KAAAA,EAGA4M,EAAAA,UAAAA,eAAR,WAeE,IAdA,IAAMc,EAAU5oB,KAAKioB,cAAcY,aACjC,EACA,EACA7oB,KAAK0oB,cAAc1kB,MACnBhE,KAAK0oB,cAAczkB,QAGjBJ,EAA+B,CACjC7D,KAAK0oB,cAAc1kB,MAAQ,EAC3BhE,KAAK0oB,cAAczkB,OAAS,GAC3B,GACA,GAJE6kB,EAAAA,EAAAA,GAAQC,EAAAA,EAAAA,GAAQC,EAAAA,EAAAA,GAAMC,EAAAA,EAAAA,GAMvBC,GAAAA,EACKC,EAAM,EAAGA,EAAMnpB,KAAK0oB,cAAczkB,OAAQklB,IACjD,IAAK,IAAIC,EAAM,EAAGA,EAAMppB,KAAK0oB,cAAc1kB,MAAOolB,IAE9CR,EAAQrhB,KAAK4hB,EAAMnpB,KAAK0oB,cAAc1kB,MAAQ,EAAU,EAANolB,EAAU,GAC/C,IACbF,GAAAA,EACIC,EAAMJ,IACRA,EAASI,GAEPC,EAAMN,IACRA,EAASM,GAEPD,EAAMF,IACRA,EAAOE,GAELC,EAAMJ,IACRA,EAAOI,IAMf,GAAIF,EAAc,CAChBlpB,KAAK+X,KAAO+Q,EAAS9oB,KAAKwnB,WAC1BxnB,KAAKiP,IAAM8Z,EAAS/oB,KAAKwnB,WACzBxnB,KAAKgE,OAASglB,EAAOF,GAAU9oB,KAAKwnB,WACpCxnB,KAAKiE,QAAUglB,EAAOF,GAAU/oB,KAAKwnB,WAErC,IAAM6B,EAAY9lB,SAASqD,cAAc,UACzCyiB,EAAUrlB,MAAQglB,EAAOF,EACzBO,EAAUplB,OAASglB,EAAOF,EACXM,EAAU3hB,WAAW,MAC7B4hB,aACLtpB,KAAKioB,cAAcY,aACjBC,EACAC,EACAC,EAAOF,EACPG,EAAOF,GAET,EACA,GAGF/oB,KAAKupB,cAAgBF,EAAU7gB,UAAU,aACzCxI,KAAKwpB,kBAELxpB,KAAKkV,OAAS,SACVlV,KAAKga,iBACPha,KAAKga,gBAAgBha,KAAK,CAG9BA,KAAKwU,iBAAiBpN,UAAY,IAG5B0gB,EAAAA,UAAAA,gBAAR,WACExkB,EAAUc,cAAcpE,KAAK+nB,aAAc,CACzC,CAAC,QAAS/nB,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,cAEzBb,EAAUc,cAAcpE,KAAK+nB,aAAc,CAAC,CAAC,OAAQ/nB,KAAKupB,iBAC1DvpB,KAAKqZ,WAAW,CAAEtT,EAAG/F,KAAK+X,KAAMvW,EAAGxB,KAAKiP,KAAAA,EAOhC6Y,EAAAA,UAAAA,SAAV,SAAmBvU,GACjBvT,KAAKuT,MAAQA,EACbvT,KAAK8U,aAAavB,EAAAA,EAOTuU,EAAAA,UAAAA,aAAV,SAAuB9jB,GACtBhE,KAAK+d,UAAY/Z,CAAAA,EAOnB1E,OAAAA,eAAWwoB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAmB,QAAf9nB,KAAK2V,OAAkC,aAAf3V,KAAK2V,MACxB,CAAC3V,KAAKsiB,WAAYtiB,KAAK6nB,gBAEvB,EAAE,EAAF,gCAOJC,EAAAA,UAAAA,SAAP,WACE,IAAM7mB,EAA8B3B,OAAO6c,OAAO,CAChDoN,cAAevpB,KAAKupB,eACnBtW,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAWqa,EAAera,SAE1BxM,CAAAA,EAQF6mB,EAAAA,UAAAA,aAAP,SAAoBnS,GAClB3V,KAAKid,eACLhK,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKupB,cAAiB5T,EAA8B4T,cACpDvpB,KAAKwpB,iBAAiB,EASjB1B,EAAAA,UAAAA,MAAP,SAAajS,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKwpB,iBAAiB,EA5VV1B,EAAAA,SAAW,iBAKXA,EAAAA,MAAQ,kBAIRA,EAAAA,KAAAA,8iBAAAA,CAAAA,CDhBgBhQ,CCCIA,GAAAA,EAAAA,SAAAA,GCwBlC,WACEpK,EACA+b,EACA9b,GAHF,MAKEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,oFAAAA,KAAAA,OAlBP9G,EAAAA,UAA8B,GAmBpCA,EAAK4iB,YAAcA,EAEnB5iB,EAAK6iB,eAAiB7iB,EAAK6iB,eAAe9d,KAAK/E,GAAAA,CAAAA,CAqHnD,OA7IoC/G,EAAAA,EAAAA,GA8B3B6pB,EAAAA,UAAAA,MAAP,sBACQtW,EAAW9P,SAASqD,cAAc,OACxCyM,EAAS7I,MAAM4C,QAAU,OACzBiG,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMsC,SAAW,IAC1B,mBAAS8c,GACP,IAAIC,EAAuB,OAC3B,OAAQD,GACN,KAAK,EACHC,EAAY,OACZ,MACF,KAAK,EACHA,EAAY,QACZ,MACF,KAAK,EACHA,EAAY,MACZ,MACF,KAAK,EACHA,EAAY,OAGhB,IAAMC,EAAmBvmB,SAASqD,cAAc,OAkBhD,GAjBAkjB,EAAiBtf,MAAM4C,QAAU,OACjC0c,EAAiBtf,MAAMsC,SAAW,IAClCgd,EAAiBtf,MAAMyT,WAAa,SACpC6L,EAAiBtf,MAAM0T,eAAiB,gBACxC4L,EAAiBtf,MAAMoJ,QAAU,MACjCkW,EAAiBtf,MAAMuJ,YAAc,MACrC+V,EAAiBtf,MAAMwJ,YAAc,QACrC8V,EAAiBtf,MAAM0J,YACrB2V,IAAcE,EAAKN,YACfM,EAAKve,gBAAgBjC,mBACrB,cAENugB,EAAiBlc,iBAAiB,oBAChC/G,EAAK6iB,eAAeG,EAAWC,EAAAA,IAEjCzW,EAAS3N,YAAYokB,GAEH,SAAdD,GAAsC,UAAdA,EAAuB,CACjD,IAAMG,EAAUzmB,SAASqD,cAAc,OACvCojB,EAAQxf,MAAM4C,QAAU,OACxB4c,EAAQxf,MAAMyT,WAAa,SAC3B+L,EAAQxf,MAAM6T,UAAY,OAC1B2L,EAAQ5iB,UAAY,mJAEhB2iB,EAAKve,gBACDue,EAAKve,gBAAgBlC,aACrB,kCAGR0gB,EAAQxf,MAAMyf,WAAa,MAC3BH,EAAiBpkB,YAAYskB,EAAAA,CAG/B,IAAME,EAAU3mB,SAASqD,cAAc,OACvCsjB,EAAQ1f,MAAM4C,QAAU,OACxB8c,EAAQ1f,MAAMyT,WAAa,SAC3BiM,EAAQ1f,MAAM6T,UAAY,OAC1B6L,EAAQ1f,MAAMsC,SAAW,IAEzB,IAAMwR,EAAK/a,SAASqD,cAAc,MAalC,GAZA0X,EAAG9T,MAAM+T,SAAW,OACpBD,EAAG9T,MAAMgU,OAAS,MAClBF,EAAG9T,MAAMiU,UAAY,uBACnBsL,EAAKve,gBACDue,EAAKve,gBAAgBlC,aACrB,WAENgV,EAAG9T,MAAMsC,SAAW,IACpBod,EAAQxkB,YAAY4Y,GAEpBwL,EAAiBpkB,YAAYwkB,GAEX,SAAdL,GAAsC,QAAdA,EAAqB,CAC/C,IAAMM,EAAW5mB,SAASqD,cAAc,OACxCujB,EAAS3f,MAAM4C,QAAU,OACzB+c,EAAS3f,MAAMyT,WAAa,SAC5BkM,EAAS3f,MAAM6T,UAAY,OAC3B8L,EAAS/iB,UAAY,kJAEjB2iB,EAAKve,gBACDue,EAAKve,gBAAgBlC,aACrB,kCAGR6gB,EAAS3f,MAAMqJ,YAAc,MAC7BiW,EAAiBpkB,YAAYykB,EAAAA,CAG/BJ,EAAKK,UAAUznB,KAAKmnB,EAAAA,EAAAA,EAAAA,KArFbF,EAAK,EAAGA,EAAK,EAAGA,IAAAA,EAAhBA,GAuFT,OAAOvW,CAAAA,EAGDsW,EAAAA,UAAAA,eAAR,SAAuBU,EAAoB7jB,GAA3C,WACExG,KAAKypB,YAAcY,EAEnBrqB,KAAKoqB,UAAU/c,SAAQ,SAACgH,GACtBA,EAAI7J,MAAM0J,YACRG,IAAQ7N,OAAAA,IACJK,EAAK2E,gBACH3E,EAAK2E,gBAAgBjC,mBACrB,UACF,iBAGJvJ,KAAKsqB,oBACPtqB,KAAKsqB,mBAAmBtqB,KAAKypB,YAAY,EAAZA,CAAAA,CDlJC3R,CCQAvD,GAAAA,EAAAA,SAAAA,GC8BlC,WAAY5E,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAlB7B5N,EAAAA,UAAuB,MAEvBA,EAAAA,gBAAkB,GAClBA,EAAAA,eAAiB,GAiBvBA,EAAK0jB,eAAiB1jB,EAAK0jB,eAAe3e,KAAK/E,GAC/CA,EAAK2jB,aAAe3jB,EAAK2jB,aAAa5e,KAAK/E,GAE3CA,EAAK4jB,eAAiB,IAAId,EAAe,aAAc,OACvD9iB,EAAK4jB,eAAeH,mBAAqBzjB,EAAK2jB,aAAAA,CAAAA,CA6HlD,OAzKiC1qB,EAAAA,EAAAA,GAoDxB4qB,EAAAA,UAAAA,WAAP,SAAkBjnB,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAK2qB,QAAUlnB,IAAOzD,KAAK4qB,OAAAA,EAQ9BF,EAAAA,UAAAA,eAAR,SAAuB7R,EAAiBC,GACtC,IAAM9U,EAAQhE,KAAK6qB,eAAoC,EAAnB7qB,KAAKqd,YACnCpZ,EAASjE,KAAK8qB,gBAAqC,EAAnB9qB,KAAKqd,YAC3C,OAAUxE,EAAU7U,EAAQ,OAC1B8U,EAAU7U,EAAS,OACjB4U,EAAAA,KAAWC,EAAU7U,EAAS,QAChC4U,EAAU7U,EAAQ,QAAK8U,EAAU7U,EAAS,IAGtCymB,EAAAA,UAAAA,WAAR,WACE1qB,KAAK2qB,OAASrnB,EAAUynB,cAAc/qB,KAAKuqB,eAAevqB,KAAKqE,GAAIrE,KAAKsE,IAAK,CAAC,CAAC,OAAQtE,KAAKod,eAC5Fpd,KAAK2qB,OAAOjT,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACnD5X,KAAKgX,OAAOtR,YAAY1F,KAAK2qB,QAE7B3qB,KAAK4qB,OAAStnB,EAAUynB,cAAc/qB,KAAKuqB,eAAevqB,KAAKuE,GAAIvE,KAAKwE,IAAK,CAAC,CAAC,OAAQxE,KAAKod,eAC5Fpd,KAAK4qB,OAAOlT,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACnD5X,KAAKgX,OAAOtR,YAAY1F,KAAK4qB,OAAO,EAS/BF,EAAAA,UAAAA,YAAP,SAAmBnV,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GACN,aAAfxG,KAAK2V,OACP3V,KAAKgrB,YAAY,EAOXN,EAAAA,UAAAA,aAAV,WAGE,GAFAzX,EAAAA,UAAMwN,aAAAA,KAAAA,MAEFzgB,KAAK2qB,QAAU3qB,KAAK4qB,OAAQ,CAC9B5qB,KAAK2qB,OAAOngB,MAAM4C,QAA8B,SAAnBpN,KAAK6pB,WAA2C,UAAnB7pB,KAAK6pB,UAAyB,GAAK,OAC7F7pB,KAAK4qB,OAAOpgB,MAAM4C,QAA8B,SAAnBpN,KAAK6pB,WAA2C,QAAnB7pB,KAAK6pB,UAAuB,GAAK,OAE3FvmB,EAAUc,cAAcpE,KAAK2qB,OAAQ,CACnC,CAAC,SAAU3qB,KAAKuqB,eAAevqB,KAAKqE,GAAIrE,KAAKsE,KAC7C,CAAC,OAAQtE,KAAKod,eAEhB9Z,EAAUc,cAAcpE,KAAK4qB,OAAQ,CACnC,CAAC,SAAU5qB,KAAKuqB,eAAevqB,KAAKuE,GAAIvE,KAAKwE,KAC7C,CAAC,OAAQxE,KAAKod,eAGhB,IAAI6N,EAAa,EACbrc,KAAK4L,IAAIxa,KAAKqE,GAAKrE,KAAKuE,IAAM,KAChC0mB,EAC0D,IAAvDrc,KAAK8L,MAAM1a,KAAKwE,GAAKxE,KAAKsE,KAAOtE,KAAKuE,GAAKvE,KAAKqE,KAAcuK,KAAK+L,GAAK,GAAK/L,KAAK6L,KAAKza,KAAKqE,GAAKrE,KAAKuE,KAE3G,IAAM2mB,EAAclrB,KAAK2qB,OAAOjT,UAAUzQ,QAAQsS,QAAQ,GAC1D2R,EAAY1R,UAAUyR,EAAYjrB,KAAKqE,GAAIrE,KAAKsE,IAChDtE,KAAK2qB,OAAOjT,UAAUzQ,QAAQyS,YAAYwR,EAAa,GAEvD,IAAMC,EAAcnrB,KAAK4qB,OAAOlT,UAAUzQ,QAAQsS,QAAQ,GAC1D4R,EAAY3R,UAAUyR,EAAa,IAAKjrB,KAAKuE,GAAIvE,KAAKwE,IACtDxE,KAAK4qB,OAAOlT,UAAUzQ,QAAQyS,YAAYyR,EAAa,EAAE,CAAF,EAInDT,EAAAA,UAAAA,aAAR,SAAqBb,GACnB7pB,KAAK6pB,UAAYA,EACjB7pB,KAAKygB,eACLzgB,KAAK6U,cAAc,EAMrBvV,OAAAA,eAAWorB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAC1qB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAkB9f,KAAKyqB,eAAe,EAAfA,YAAAA,EAAAA,cAAAA,IAMxEC,EAAAA,UAAAA,SAAP,WACE,IAAMzpB,EAA2B3B,OAAO6c,OAAO,CAC7C0N,UAAW7pB,KAAK6pB,WACf5W,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAWid,EAAYjd,SAEvBxM,CAAAA,EAQFypB,EAAAA,UAAAA,aAAP,SAAoB/U,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GAEnB,IAAMyV,EAAUzV,EAChB3V,KAAK6pB,UAAYuB,EAAQvB,UAEzB7pB,KAAKgrB,aACLhrB,KAAKygB,cAAc,EAhKPiK,EAAAA,SAAW,cAKXA,EAAAA,MAAQ,eAIRA,EAAAA,KAAAA,iGAAAA,CAAAA,CDRoBnW,CCPHoM,GAAAA,EAAAA,SAAAA,GCuB/B,WAAYhR,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAEnC5N,EAAKsW,UAAY1I,EAAS4W,iBAC1BxkB,EAAKwW,YAAc,EAEnBxW,EAAKykB,UAAY,IAAIlY,EACnB,QACAqB,EAASkL,gBACTlL,EAAS4W,kBAEXxkB,EAAKykB,UAAUhX,eAAiBzN,EAAKiW,aAAAA,CAAAA,CAkBzC,OA1DiChd,EAAAA,EAAAA,GA8C/BR,OAAAA,eAAWisB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAACvrB,KAAKsrB,UAAU,EAAVA,YAAAA,EAAAA,cAAAA,IAMRC,EAAAA,UAAAA,SAAP,WACE,IAAMtqB,EAASgS,EAAAA,UAAMqC,SAAAA,KAAAA,MAErB,OADArU,EAAOwM,SAAW8d,EAAY9d,SACvBxM,CAAAA,EAlDKsqB,EAAAA,SAAW,cAKXA,EAAAA,MAAQ,eAIRA,EAAAA,KAAAA,6DAAAA,CAAAA,CDTiB5K,CCNAzD,GAAAA,EAAAA,SAAAA,GCsB/B,WAAYxP,EAAe8d,EAAqBC,EAAyB9d,GAAzE,MACEsF,EAAAA,KAAAA,KAAMvF,EAAOC,GAAAA,mQAAAA,KAAAA,OAlBP9G,EAAAA,UAAsB,GAGtBA,EAAAA,aAAiC,GAgBvCA,EAAK2kB,UAAYA,EACjB3kB,EAAK4kB,eAAiBA,EAEtB5kB,EAAK6kB,kBAAoB7kB,EAAK6kB,kBAAkB9f,KAAK/E,GAAAA,CAAAA,CAiDzD,OAxEkC/G,EAAAA,EAAAA,GA6BzB6rB,EAAAA,UAAAA,MAAP,sBACQtY,EAAW9P,SAASqD,cAAc,OA4BxC,OA3BAyM,EAAS7I,MAAM4C,QAAU,OACzBiG,EAAS7I,MAAM8I,SAAW,SAC1BD,EAAS7I,MAAMsC,SAAW,IAC1BuG,EAAS7I,MAAM0T,eAAiB,gBAChCle,KAAKwrB,UAAUne,SAAQ,SAACkQ,GACtB,IAAMqO,EAAsBroB,SAASqD,cAAc,OACnDglB,EAAoBphB,MAAM4C,QAAU,OAEpCwe,EAAoBphB,MAAMyT,WAAa,SACvC2N,EAAoBphB,MAAM0T,eAAiB,SAC3C0N,EAAoBphB,MAAMoJ,QAAU,MACpCgY,EAAoBphB,MAAMuJ,YAAc,MACxC6X,EAAoBphB,MAAMwJ,YAAc,QACxC4X,EAAoBphB,MAAM0J,YACxBqJ,IAAY1W,EAAK4kB,eAAiB5kB,EAAK2E,gBAAgBjC,mBAAqB,cAE9EqiB,EAAoBhe,iBAAiB,oBACnC/G,EAAK6kB,kBAAkBnO,EAASqO,EAAAA,IAElCvY,EAAS3N,YAAYkmB,GAErB,IAAMhqB,EAAQ2B,SAASqD,cAAc,OACrChF,EAAMuc,UAA0B,IAAVZ,EAAAA,IACtBqO,EAAoBlmB,YAAY9D,GAEhCiF,EAAKglB,aAAalpB,KAAKipB,EAAAA,IAElBvY,CAAAA,EAGDsY,EAAAA,UAAAA,kBAAR,SAA0BvR,EAAkB5T,GAA5C,WACExG,KAAKyrB,eAAiBrR,EAEtBpa,KAAK6rB,aAAaxe,SAAQ,SAAAgH,GACxBA,EAAI7J,MAAM0J,YAAcG,IAAQ7N,EAASK,EAAK2E,gBAAgBjC,mBAAqB,iBAGjFvJ,KAAK8rB,kBACP9rB,KAAK8rB,iBAAiB9rB,KAAKyrB,eAAe,EAAfA,CAAAA,CDzEAvO,CCIC3I,GAAAA,EAAAA,SAAAA,GCuBhC,WAAY5E,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAEnC5N,EAAKklB,WAAallB,EAAKklB,WAAWngB,KAAK/E,GAEvCA,EAAKsW,UAAY1I,EAASuX,sBAC1BnlB,EAAKwW,YAAc,EACnBxW,EAAK0W,QAAU9I,EAASwX,wBAExBplB,EAAKykB,UAAY,IAAIlY,EACnB,QACAqB,EAASkL,gBACT9Y,EAAKsW,WAEPtW,EAAKykB,UAAUhX,eAAiBzN,EAAKiW,aAErCjW,EAAKqlB,aAAe,IAAIP,EACtB,UACAlX,EAAS0X,oBACTtlB,EAAK0W,SAEP1W,EAAKqlB,aAAaJ,iBAAmBjlB,EAAKklB,WAAAA,CAAAA,CA8B9C,OA5EqCjsB,EAAAA,EAAAA,GAqDzBssB,EAAAA,UAAAA,WAAV,SAAqB7O,GACnBvd,KAAKud,QAAUA,EACXvd,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,UAAWhX,KAAKud,QAAQpZ,cAEjEnE,KAAK6U,cAAc,EAMrBvV,OAAAA,eAAW8sB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAACpsB,KAAKsrB,UAAWtrB,KAAKksB,aAAa,EAAbA,YAAAA,EAAAA,cAAAA,IAMxBE,EAAAA,UAAAA,SAAP,WACE,IAAMnrB,EAASgS,EAAAA,UAAMqC,SAAAA,KAAAA,MAErB,OADArU,EAAOwM,SAAW2e,EAAgB3e,SAC3BxM,CAAAA,EApEKmrB,EAAAA,SAAW,kBAIXA,EAAAA,MAAQ,mBAIRA,EAAAA,KAAAA,iSAAAA,CAAAA,CDZkB7X,CCFGgX,GAAAA,EAAAA,yHAAAA,EAAAA,6RAAAA,EAAAA,SAAAA,GC0CnC,WACE5b,EACA6E,EACAC,GAHF,MAKExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAzB7B5N,EAAAA,QAAU,cAMVA,EAAAA,YAAsB,CAAEd,EAAG,EAAGvE,EAAG,GACjCqF,EAAAA,iBAA2B,CAAEd,EAAG,EAAGvE,EAAG,GACtCqF,EAAAA,iBAA2B,CAAEd,EAAG,EAAGvE,EAAG,GAGtCqF,EAAAA,WAAAA,EAgBNA,EAAK0M,MAAQkB,EAAS4X,mBACtBxlB,EAAKylB,QAAU7X,EAAS4W,iBACxBxkB,EAAKya,WAAa7M,EAASoN,kBAE3Bhb,EAAKiT,YAAc,CAAE/T,EAAG,IAAKvE,EAAG,IAEhCqF,EAAK0lB,WAAa1lB,EAAK0lB,WAAW3gB,KAAK/E,GACvCA,EAAK2lB,aAAe3lB,EAAK2lB,aAAa5gB,KAAK/E,GAC3CA,EAAK4lB,YAAc5lB,EAAK4lB,YAAY7gB,KAAK/E,GACzCA,EAAK6lB,aAAe7lB,EAAK6lB,aAAa9gB,KAAK/E,GAE3CA,EAAKyb,WAAa,IAAIlP,EACpB,aACAqB,EAASkL,gBACT9Y,EAAK0M,MACLoZ,GAEF9lB,EAAKyb,WAAWhO,eAAiBzN,EAAKib,SAEtCjb,EAAK+lB,aAAe,IAAIxZ,EACtB,aACAqB,EAASkL,gBACT9Y,EAAKylB,QACLO,GAEFhmB,EAAK+lB,aAAatY,eAAiBzN,EAAK0lB,WAExC1lB,EAAK0b,gBAAkB,IAAIrB,EACzB,OACAzM,EAAS+N,oBACT/N,EAASoN,mBAEXhb,EAAK0b,gBAAgBZ,cAAgB9a,EAAKkb,QAE1Clb,EAAKimB,QAAU,IAAI1V,EACnBvQ,EAAKimB,QAAQ9V,OAAOU,UAAUzQ,QAAQ0Q,WACpCrU,EAAUsU,mBAEZ/Q,EAAKoU,WAAWvV,YAAYmB,EAAKimB,QAAQ9V,QAAAA,CAAAA,CA2O7C,OA7TmClX,EAAAA,EAAAA,GA0F1BitB,EAAAA,UAAAA,WAAP,SAAkBtpB,GAChB,OACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IAAOzD,KAAK8sB,QAAQtW,WAAW/S,IAAOzD,KAAKgtB,MAAQvpB,CAAC,EAIjEspB,EAAAA,UAAAA,UAAR,WACEzpB,EAAUc,cAAcpE,KAAK2iB,YAAa,CACxC,CAAC,OAAQ3iB,KAAKssB,SACd,CAAC,KAAM,UAGTtsB,KAAKgtB,IAAM1pB,EAAUynB,cAAc/qB,KAAKwsB,eAAgB,CACtD,CAAC,OAAQxsB,KAAKssB,WAEhBtsB,KAAKgX,OAAOtR,YAAY1F,KAAKgtB,IAAI,EAS5BD,EAAAA,UAAAA,YAAP,SAAmBxX,EAAe/O,GACb,QAAfxG,KAAK2V,OACP1C,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAGR,aAAfxG,KAAK2V,MACP3V,KAAKitB,YACIjtB,KAAK8sB,QAAQtW,WAAWhQ,IACjCxG,KAAKqY,sBAAwBrY,KAAK+X,KAClC/X,KAAKsY,qBAAuBtY,KAAKiP,IACjCjP,KAAKktB,WAAAA,GAELja,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,EAAAA,EAStBumB,EAAAA,UAAAA,UAAP,SAAiBxX,GACf,GAAIvV,KAAKktB,UACPltB,KAAKktB,WAAAA,EACLltB,KAAK+iB,SAAAA,EACL9P,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,OACX,CACL,IAAM4X,EAA4B,aAAfntB,KAAK2V,MACxB1C,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GAChBvV,KAAK0sB,aAAaS,GAClBntB,KAAKysB,aAAa,CAAbA,EASFM,EAAAA,UAAAA,WAAP,SAAkBxX,GAChB,GAAIvV,KAAKktB,UAAW,CAClB,IAAMzU,EAAezY,KAAK0Y,cAAcnD,GACxCvV,KAAKotB,YAAc,CACjBrnB,EAAG0S,EAAa1S,EAAI/F,KAAKqY,sBACzB7W,EAAGiX,EAAajX,EAAIxB,KAAKsY,sBAE3BtY,KAAKysB,aAAa,MAElBxZ,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,EAAAA,EAQXwX,EAAAA,UAAAA,WAAV,SAAqBxZ,GACfvT,KAAK2iB,aAAe3iB,KAAKgtB,MAC3B1pB,EAAUc,cAAcpE,KAAK2iB,YAAa,CAAC,CAAC,OAAQpP,KACpDjQ,EAAUc,cAAcpE,KAAKgtB,IAAK,CAAC,CAAC,OAAQzZ,MAE9CvT,KAAKssB,QAAU/Y,EACfvT,KAAK+U,iBAAiBxB,EAAAA,EAGhBwZ,EAAAA,UAAAA,aAAR,WAEE,OADA/sB,KAAK0sB,aAA4B,aAAf1sB,KAAK2V,OACb3V,KAAKqtB,iBAAiBtnB,EAAAA,IAAK/F,KAAKqtB,iBAAiB7rB,EAAAA,IACrDxB,KAAKstB,iBAAiBvnB,EAAAA,IAAK/F,KAAKstB,iBAAiB9rB,EAAAA,IACjDxB,KAAKotB,YAAYrnB,EAAAA,IAAK/F,KAAKotB,YAAY5rB,CAAC,EAGxCurB,EAAAA,UAAAA,aAAR,SAAqBI,QAAAA,IAAAA,IAAAA,GAAAA,GACnB,IAAII,EAAS3e,KAAK+V,IAAI3kB,KAAKiE,OAAS,EAAG,IACnCupB,EAAYxtB,KAAKiE,OAAS,EAC1BkpB,IACFntB,KAAKotB,YAAc,CAAErnB,EAAGwnB,EAASC,EAAY,EAAGhsB,EAAGxB,KAAKiE,OAAS,KAGnE,IAAMwpB,EAAc7e,KAAK8L,KAAM1a,KAAKiE,OAAS,GAAMjE,KAAKgE,MAAQ,IAC5DhE,KAAKotB,YAAYrnB,EAAI/F,KAAKgE,MAAQ,GAAKhE,KAAKotB,YAAY5rB,EAAIxB,KAAKiE,OAAS,EAGxEwpB,EADa7e,KAAK8L,MAAM1a,KAAKiE,OAAS,EAAIjE,KAAKotB,YAAY5rB,IAAMxB,KAAKgE,MAAQ,EAAIhE,KAAKotB,YAAYrnB,KAErGynB,EAAYxtB,KAAKgE,MAAQ,EACzBupB,EAAS3e,KAAK+V,IAAI3kB,KAAKgE,MAAQ,EAAG,IAClChE,KAAKqtB,iBAAmB,CAAEtnB,EAAGwnB,EAAQ/rB,EAAG,GACxCxB,KAAKstB,iBAAmB,CAAEvnB,EAAGwnB,EAASC,EAAWhsB,EAAG,KAEpDxB,KAAKqtB,iBAAmB,CAAEtnB,EAAG,EAAGvE,EAAG+rB,GACnCvtB,KAAKstB,iBAAmB,CAAEvnB,EAAG,EAAGvE,EAAG+rB,EAASC,IAErCxtB,KAAKotB,YAAYrnB,GAAK/F,KAAKgE,MAAQ,GAAKhE,KAAKotB,YAAY5rB,EAAIxB,KAAKiE,OAAS,EAGhFwpB,EADa7e,KAAK8L,MAAM1a,KAAKiE,OAAS,EAAIjE,KAAKotB,YAAY5rB,IAAMxB,KAAKotB,YAAYrnB,EAAI/F,KAAKgE,MAAQ,KAErGwpB,EAAYxtB,KAAKgE,MAAQ,EACzBupB,EAAS3e,KAAK+V,IAAI3kB,KAAKgE,MAAQ,EAAG,IAClChE,KAAKqtB,iBAAmB,CAAEtnB,EAAG/F,KAAKgE,MAAQupB,EAASC,EAAWhsB,EAAG,GACjExB,KAAKstB,iBAAmB,CAAEvnB,EAAG/F,KAAKgE,MAAQupB,EAAQ/rB,EAAG,KAErDxB,KAAKqtB,iBAAmB,CAAEtnB,EAAG/F,KAAKgE,MAAOxC,EAAG+rB,GAC5CvtB,KAAKstB,iBAAmB,CAAEvnB,EAAG/F,KAAKgE,MAAOxC,EAAG+rB,EAASC,IAE9CxtB,KAAKotB,YAAYrnB,GAAK/F,KAAKgE,MAAQ,GAAKhE,KAAKotB,YAAY5rB,GAAKxB,KAAKiE,OAAS,EAGjFwpB,EADa7e,KAAK8L,MAAM1a,KAAKotB,YAAY5rB,EAAIxB,KAAKiE,OAAS,IAAMjE,KAAKotB,YAAYrnB,EAAI/F,KAAKgE,MAAQ,KAErGwpB,EAAYxtB,KAAKgE,MAAQ,EACzBupB,EAAS3e,KAAK+V,IAAI3kB,KAAKgE,MAAQ,EAAG,IAClChE,KAAKqtB,iBAAmB,CAAEtnB,EAAG/F,KAAKgE,MAAQupB,EAASC,EAAWhsB,EAAGxB,KAAKiE,QACtEjE,KAAKstB,iBAAmB,CAAEvnB,EAAG/F,KAAKgE,MAAQupB,EAAQ/rB,EAAGxB,KAAKiE,UAE1DjE,KAAKqtB,iBAAmB,CAAEtnB,EAAG/F,KAAKgE,MAAOxC,EAAGxB,KAAKiE,OAASspB,EAASC,GACnExtB,KAAKstB,iBAAmB,CAAEvnB,EAAG/F,KAAKgE,MAAOxC,EAAGxB,KAAKiE,OAASspB,IAKxDE,EADa7e,KAAK8L,MAAM1a,KAAKotB,YAAY5rB,EAAIxB,KAAKiE,OAAS,IAAMjE,KAAKgE,MAAQ,EAAIhE,KAAKotB,YAAYrnB,KAErGynB,EAAYxtB,KAAKgE,MAAQ,EACzBupB,EAAS3e,KAAK+V,IAAI3kB,KAAKgE,MAAQ,EAAG,IAClChE,KAAKqtB,iBAAmB,CAAEtnB,EAAGwnB,EAAQ/rB,EAAGxB,KAAKiE,QAC7CjE,KAAKstB,iBAAmB,CAAEvnB,EAAGwnB,EAASC,EAAWhsB,EAAGxB,KAAKiE,UAEzDjE,KAAKqtB,iBAAmB,CAAEtnB,EAAG,EAAGvE,EAAGxB,KAAKiE,OAASspB,GACjDvtB,KAAKstB,iBAAmB,CAAEvnB,EAAG,EAAGvE,EAAGxB,KAAKiE,OAASspB,EAASC,GAAAA,EAStDT,EAAAA,UAAAA,OAAV,SAAiBxX,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbvV,KAAKysB,aAAa,EAGZM,EAAAA,UAAAA,YAAR,WACEzpB,EAAUc,cAAcpE,KAAKgtB,IAAK,CAAC,CAAC,SAAUhtB,KAAKwsB,kBACnD,IAAMvU,EAAYjY,KAAK8sB,QAAQ9V,OAAOU,UAAUzQ,QAAQsS,QAAQ,GAChEtB,EAAUkD,aAAanb,KAAKotB,YAAYrnB,EAAG/F,KAAKotB,YAAY5rB,GAC5DxB,KAAK8sB,QAAQ9V,OAAOU,UAAUzQ,QAAQyS,YAAYzB,EAAW,EAAE,EAMjE3Y,OAAAA,eAAWytB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAC/sB,KAAKsiB,WAAYtiB,KAAK4sB,aAAc5sB,KAAKuiB,gBAAgB,EAAhBA,YAAAA,EAAAA,cAAAA,IAM5CwK,EAAAA,UAAAA,OAAP,WACE/sB,KAAKysB,cACLxZ,EAAAA,UAAM8F,OAAAA,KAAAA,KAAAA,EAMDgU,EAAAA,UAAAA,SAAP,WACE,IAAM9rB,EAA6B3B,OAAO6c,OAAO,CAC/CmQ,QAAStsB,KAAKssB,QACdc,YAAaptB,KAAKotB,aACjBna,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAWsf,EAActf,SAEzBxM,CAAAA,EAQF8rB,EAAAA,UAAAA,aAAP,SAAoBpX,GAClB,IAAM+X,EAAe/X,EACrB3V,KAAKssB,QAAUoB,EAAapB,QAC5BtsB,KAAKotB,YAAcM,EAAaN,YAEhCna,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKitB,YACLjtB,KAAK0sB,cAAc,EASdK,EAAAA,UAAAA,MAAP,SAAalX,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKotB,YAAc,CAACrnB,EAAG/F,KAAKotB,YAAYrnB,EAAI8P,EAAQrU,EAAGxB,KAAKotB,YAAY5rB,EAAIsU,GAE5E9V,KAAKysB,aAAa,EArTNM,EAAAA,SAAW,gBAKXA,EAAAA,MAAQ,iBAIRA,EAAAA,KAAAA,2LAAAA,CAAAA,CDpBqBxB,CCKF9I,GAAAA,EAAAA,SAAAA,GCkDjC,WAAY9S,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAhC3B5N,EAAAA,UAAY,cAIZA,EAAAA,YAAc,cAIdA,EAAAA,YAAc,EAIdA,EAAAA,gBAAkB,GAIlBA,EAAAA,QAAU,EAkBlBA,EAAKuW,YAAc3I,EAAS8K,aAC5B1Y,EAAKwW,YAAc5I,EAAS+K,mBAC5B3Y,EAAKyW,gBAAkB7I,EAASgL,uBAChC5Y,EAAKsW,UAAY1I,EAAS4W,iBAE1BxkB,EAAKgW,eAAiBhW,EAAKgW,eAAejR,KAAK/E,GAC/CA,EAAKiW,aAAejW,EAAKiW,aAAalR,KAAK/E,GAC3CA,EAAKkW,eAAiBlW,EAAKkW,eAAenR,KAAK/E,GAC/CA,EAAKmW,mBAAqBnW,EAAKmW,mBAAmBpR,KAAK/E,GACvDA,EAAKklB,WAAallB,EAAKklB,WAAWngB,KAAK/E,GACvCA,EAAKoW,aAAepW,EAAKoW,aAAarR,KAAK/E,GAE3CA,EAAK6Y,YAAc,IAAItM,EACrB,eACIqB,EAASkL,gBAAgB,CAAC,gBAC9BlL,EAAS8K,cAEX1Y,EAAK6Y,YAAYpL,eAAiBzN,EAAKgW,eAEvChW,EAAKykB,UAAY,IAAIlY,EACnB,eACIqB,EAASkL,gBAAgB,CAAC,gBAC9B9Y,EAAKsW,UACL0P,GAEFhmB,EAAKykB,UAAUhX,eAAiBzN,EAAKiW,aAErCjW,EAAK+Y,iBAAmB,IAAI9B,EAC1B,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAK+Y,iBAAiBjB,eAAiB9X,EAAKkW,eAE5ClW,EAAKiZ,iBAAmB,IAAIhB,EAC1B,aACArK,EAASsL,wBACTtL,EAASgL,wBAEX5Y,EAAKiZ,iBAAiBR,eAAiBzY,EAAKmW,mBAE5CnW,EAAKqlB,aAAe,IAAIP,EACtB,UACAlX,EAAS0X,oBACTtlB,EAAK0W,SAEP1W,EAAKqlB,aAAaJ,iBAAmBjlB,EAAKklB,WAAAA,CAAAA,CAsM9C,OAzSmCjsB,EAAAA,EAAAA,GA2G1B6tB,EAAAA,UAAAA,WAAP,SAAkBlqB,GAChB,SAAIwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IAAOA,IAAOzD,KAAKgX,OAAO,EAUvC2W,EAAAA,UAAAA,aAAV,WACE3tB,KAAKgX,OAAS1T,EAAUsqB,cAAc5tB,KAAKgE,MAAQ,EAAGhE,KAAKiE,OAAS,EAAG,CACrE,CAAC,OAAQjE,KAAKmd,WACd,CAAC,SAAUnd,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,YAClC,CAAC,mBAAoBnE,KAAKsd,iBAC1B,CAAC,UAAWtd,KAAKud,QAAQpZ,cAE3BnE,KAAKwd,2BAA2Bxd,KAAKgX,OAAO,EASvC2W,EAAAA,UAAAA,YAAP,SAAmBpY,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GACN,QAAfxG,KAAK2V,QACP3V,KAAKid,eAELjd,KAAKqZ,WAAW9D,GAEhBvV,KAAKkV,OAAS,aASXyY,EAAAA,UAAAA,WAAP,SAAkBpY,GAChBtC,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,EAAAA,EAOToY,EAAAA,UAAAA,OAAV,SAAiBpY,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbvV,KAAKua,SAAS,EAMNoT,EAAAA,UAAAA,QAAV,WACE1a,EAAAA,UAAMsH,QAAAA,KAAAA,MACNjX,EAAUc,cAAcpE,KAAKgX,OAAQ,CACnC,CAAC,MAAOhX,KAAKgE,MAAQ,GAAGG,YACxB,CAAC,MAAOnE,KAAKiE,OAAS,GAAGE,YACzB,CAAC,MAAOnE,KAAKgE,MAAQ,GAAGG,YACxB,CAAC,MAAOnE,KAAKiE,OAAS,GAAGE,aAAAA,EAStBwpB,EAAAA,UAAAA,UAAP,SAAiBpY,GACftC,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GAChBvV,KAAKua,SAAS,EAONoT,EAAAA,UAAAA,eAAV,SAAyBpa,GACvBvT,KAAKod,YAAc7J,EACfvT,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,SAAUhX,KAAKod,eAExDpd,KAAK8U,aAAavB,GAClBvT,KAAK6U,cAAc,EAMX8Y,EAAAA,UAAAA,aAAV,SAAuBpa,GACrBvT,KAAKmd,UAAY5J,EACbvT,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,OAAQhX,KAAKmd,aAEtDnd,KAAK+U,iBAAiBxB,GACtBvT,KAAK6U,cAAc,EAMX8Y,EAAAA,UAAAA,eAAV,SAAyB3pB,GACvBhE,KAAKqd,YAAcrZ,EACfhE,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,eAAgBhX,KAAKqd,YAAYlZ,cAE1EnE,KAAK6U,cAAc,EAMX8Y,EAAAA,UAAAA,mBAAV,SAA6BlQ,GAC3Bzd,KAAKsd,gBAAkBG,EACnBzd,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,mBAAoBhX,KAAKsd,mBAElEtd,KAAK6U,cAAc,EAMX8Y,EAAAA,UAAAA,WAAV,SAAqBpQ,GACnBvd,KAAKud,QAAUA,EACXvd,KAAKgX,QACP1T,EAAUc,cAAcpE,KAAKgX,OAAQ,CAAC,CAAC,UAAWhX,KAAKud,QAAQpZ,cAEjEnE,KAAK6U,cAAc,EAMrBvV,OAAAA,eAAWquB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAC3tB,KAAK0f,YAAa1f,KAAKsrB,UAAWtrB,KAAK4f,iBAAkB5f,KAAK8f,iBAAkB9f,KAAKksB,aAAa,EAAbA,YAAAA,EAAAA,cAAAA,IAMxFyB,EAAAA,UAAAA,SAAP,WACE,IAAM1sB,EAA+B3B,OAAO6c,OAAO,CACjDgB,UAAWnd,KAAKmd,UAChBC,YAAapd,KAAKod,YAClBC,YAAard,KAAKqd,YAClBC,gBAAiBtd,KAAKsd,gBACtBC,QAASvd,KAAKud,SACbtK,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAWkgB,EAAclgB,SAEzBxM,CAAAA,EAQF0sB,EAAAA,UAAAA,aAAP,SAAoBhY,GAClB,IAAM+H,EAAY/H,EAClB3V,KAAKmd,UAAYO,EAAUP,UAC3Bnd,KAAKod,YAAcM,EAAUN,YAC7Bpd,KAAKqd,YAAcK,EAAUL,YAC7Brd,KAAKsd,gBAAkBI,EAAUJ,gBACjCtd,KAAKud,QAAUG,EAAUH,QAEzBvd,KAAKid,eACLhK,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKua,SAAS,EASToT,EAAAA,UAAAA,MAAP,SAAa9X,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKua,SAAS,EAjSFoT,EAAAA,SAAW,gBAIXA,EAAAA,MAAQ,iBAIRA,EAAAA,KAAAA,yGAAAA,CAAAA,CDdmBlL,CCAA3K,GAAAA,EAAAA,SAAAA,GC0BjC,WAAYnI,EAAwB6E,EAAkCC,GAAAA,OACpExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,IAAAA,CA8HvC,OA9JuC3U,EAAAA,EAAAA,GAoBrCR,OAAAA,eAAYuuB,EAAAA,UAAAA,YAAAA,CAAAA,IAAZ,WACE,OAAO,GAAwB,EAAnB7tB,KAAKqd,WAAW,EAAXA,YAAAA,EAAAA,cAAAA,IAmBZwQ,EAAAA,UAAAA,WAAP,SAAkBpqB,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAK8tB,MAAQrqB,IAAOzD,KAAK+tB,KAAAA,EAQ5BF,EAAAA,UAAAA,WAAR,WACE7tB,KAAK8tB,KAAOxqB,EAAUmY,WACpBzb,KAAKqE,GAAKrE,KAAKguB,UAAY,EAC3BhuB,KAAKsE,GACLtE,KAAKqE,GAAKrE,KAAKguB,UAAY,EAC3BhuB,KAAKsE,GACL,CACE,CAAC,SAAUtE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,cAEtCnE,KAAK8tB,KAAKpW,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACjD5X,KAAKgX,OAAOtR,YAAY1F,KAAK8tB,MAE7B9tB,KAAK+tB,KAAOzqB,EAAUmY,WACpBzb,KAAKuE,GAAKvE,KAAKguB,UAAY,EAC3BhuB,KAAKwE,GACLxE,KAAKuE,GAAKvE,KAAKguB,UAAY,EAC3BhuB,KAAKwE,GACL,CACE,CAAC,SAAUxE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,cAEtCnE,KAAK+tB,KAAKrW,UAAUzQ,QAAQ0Q,WAAWrU,EAAUsU,mBACjD5X,KAAKgX,OAAOtR,YAAY1F,KAAK+tB,KAAK,EAS7BF,EAAAA,UAAAA,YAAP,SAAmBtY,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GACN,aAAfxG,KAAK2V,OACP3V,KAAKgrB,YAAY,EAOX6C,EAAAA,UAAAA,aAAV,WAGE,GAFA5a,EAAAA,UAAMwN,aAAAA,KAAAA,MAEFzgB,KAAK8tB,MAAQ9tB,KAAK+tB,OAEpBzqB,EAAUc,cAAcpE,KAAK8tB,KAAK,CAChC,CAAC,MAAO9tB,KAAKqE,GAAKrE,KAAKguB,UAAY,GAAG7pB,YACtC,CAAC,KAAMnE,KAAKsE,GAAGH,YACf,CAAC,MAAOnE,KAAKqE,GAAKrE,KAAKguB,UAAY,GAAG7pB,YACtC,CAAC,KAAMnE,KAAKsE,GAAGH,YACf,CAAC,SAAUnE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,cAEpCb,EAAUc,cAAcpE,KAAK+tB,KAAK,CAChC,CAAC,MAAO/tB,KAAKuE,GAAKvE,KAAKguB,UAAY,GAAG7pB,YACtC,CAAC,KAAMnE,KAAKwE,GAAGL,YACf,CAAC,MAAOnE,KAAKuE,GAAKvE,KAAKguB,UAAY,GAAG7pB,YACtC,CAAC,KAAMnE,KAAKwE,GAAGL,YACf,CAAC,SAAUnE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,cAGhCyK,KAAK4L,IAAIxa,KAAKqE,GAAKrE,KAAKuE,IAAM,IAAK,CACrC,IAAM0mB,EACoD,IAAvDrc,KAAK8L,MAAM1a,KAAKwE,GAAKxE,KAAKsE,KAAOtE,KAAKuE,GAAKvE,KAAKqE,KAAcuK,KAAK+L,GAAK,GAAK/L,KAAK6L,KAAKza,KAAKqE,GAAKrE,KAAKuE,IAEnG2mB,EAAclrB,KAAK8tB,KAAKpW,UAAUzQ,QAAQsS,QAAQ,GACxD2R,EAAY1R,UAAUyR,EAAYjrB,KAAKqE,GAAIrE,KAAKsE,IAChDtE,KAAK8tB,KAAKpW,UAAUzQ,QAAQyS,YAAYwR,EAAa,GAErD,IAAMC,EAAcnrB,KAAK+tB,KAAKrW,UAAUzQ,QAAQsS,QAAQ,GACxD4R,EAAY3R,UAAUyR,EAAa,IAAKjrB,KAAKuE,GAAIvE,KAAKwE,IACtDxE,KAAK+tB,KAAKrW,UAAUzQ,QAAQyS,YAAYyR,EAAa,EAAE,CAAF,EAQ3D7rB,OAAAA,eAAWuuB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAAC7tB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAMjD+N,EAAAA,UAAAA,SAAP,WACE,IAAM5sB,EAAQgS,EAAAA,UAAMqC,SAAAA,KAAAA,MAGpB,OAFArU,EAAOwM,SAAWogB,EAAkBpgB,SAE7BxM,CAAAA,EAQF4sB,EAAAA,UAAAA,aAAP,SAAoBlY,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GAEnB3V,KAAKgrB,aACLhrB,KAAKygB,cAAc,EAtJPoN,EAAAA,SAAW,oBAKXA,EAAAA,MAAQ,qBAIRA,EAAAA,KAAAA,+UAAAA,CAAAA,CDVmB/V,CCLI6I,GAAAA,EAAAA,SAAAA,GCoBrC,WAAYhR,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAGnC5N,EAAK6Y,YAAY3M,OAAQ0B,EAASkL,gBAElC9Y,EAAKsW,UAAY,gBAkBrB,OA/CwCrd,EAAAA,EAAAA,GAmCtCR,OAAAA,eAAW2uB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAACjuB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAMjDmO,EAAAA,UAAAA,SAAP,WACE,IAAMhtB,EAASgS,EAAAA,UAAMqC,SAAAA,KAAAA,MAErB,OADArU,EAAOwM,SAAWwgB,EAAmBxgB,SAC9BxM,CAAAA,EAvCKgtB,EAAAA,SAAW,qBAIXA,EAAAA,MAAQ,uBAIRA,EAAAA,KAAAA,iKAAAA,CAAAA,CDXuBtN,CCHCgN,GAAAA,EAAAA,WCHxC,aACU3tB,KAAAA,UAAiB,GACjBA,KAAAA,UAAiB,EAAE,CAgG7B,OAzFEV,OAAAA,eAAW4uB,EAAAA,UAAAA,iBAAAA,CAAAA,IAAX,WACE,OAAOluB,KAAKmuB,UAAUzrB,OAAS,CAAC,EAAD,gCAMjCpD,OAAAA,eAAW4uB,EAAAA,UAAAA,iBAAAA,CAAAA,IAAX,WACE,OAAOluB,KAAKouB,UAAU1rB,OAAS,CAAC,EAAD,gCAQjCpD,OAAAA,eAAW4uB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,OAAOluB,KAAKmuB,UAAUzrB,MAAM,EAANA,YAAAA,EAAAA,cAAAA,IAQvBpD,OAAAA,eAAW4uB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACC,OAAOluB,KAAKouB,UAAU1rB,MAAM,EAANA,YAAAA,EAAAA,cAAAA,IAOjBwrB,EAAAA,UAAAA,YAAP,SAAmBG,GAEW,IAA1BruB,KAAKmuB,UAAUzrB,QACfwT,KAAKC,UAAUnW,KAAKmuB,UAAUnuB,KAAKmuB,UAAUzrB,OAAS,MACpDwT,KAAKC,UAAUkY,KAEfruB,KAAKmuB,UAAUxrB,KAAK0rB,GAChBnY,KAAKC,UAAUnW,KAAKsuB,gBAAkBpY,KAAKC,UAAUkY,IACvDruB,KAAKouB,UAAU5f,OAAO,EAAGxO,KAAKouB,UAAU1rB,QAAAA,EASzCwrB,EAAAA,UAAAA,oBAAP,SAA2BG,GACrBruB,KAAKmuB,UAAUzrB,OAAS,IACxB1C,KAAKmuB,UAAUnuB,KAAKmuB,UAAUzrB,OAAS,GAAK2rB,EAAAA,EAO3CH,EAAAA,UAAAA,gBAAP,WACE,OAAIluB,KAAKmuB,UAAUzrB,OAAS,EACjB1C,KAAKmuB,UAAUnuB,KAAKmuB,UAAUzrB,OAAS,SAEhD,EAQGwrB,EAAAA,UAAAA,KAAP,WACE,GAAIluB,KAAKmuB,UAAUzrB,OAAS,EAAG,CAC7B,IAAM6rB,EAAWvuB,KAAKmuB,UAAU1rB,MAIhC,YAAO,IAHH8rB,GACFvuB,KAAKouB,UAAUzrB,KAAK4rB,GAEfvuB,KAAKmuB,UAAUzrB,OAAS,EAAI1C,KAAKmuB,UAAUnuB,KAAKmuB,UAAUzrB,OAAS,SAAK0I,CAAAA,EAQ5E8iB,EAAAA,UAAAA,KAAP,WAEE,OADAluB,KAAKsuB,aAAetuB,KAAKouB,UAAU3rB,MAC5BzC,KAAKsuB,YAAY,EAAZA,CAAAA,CD7FwBX,GC6FxBW,EAAAA,SAAAA,GCjBd,WAAY3e,EAAwB6E,EAAkCC,GAAtE,MACExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAzC3B5N,EAAAA,YAAc,cAIdA,EAAAA,YAAc,EAIdA,EAAAA,gBAAkB,GAgBpBA,EAAAA,OAAS,EACTA,EAAAA,OAAS,EAETA,EAAAA,wBAA0B,EAC1BA,EAAAA,wBAA0B,EAehCA,EAAKgW,eAAiBhW,EAAKgW,eAAejR,KAAK/E,GAC/CA,EAAKkW,eAAiBlW,EAAKkW,eAAenR,KAAK/E,GAC/CA,EAAKmW,mBAAqBnW,EAAKmW,mBAAmBpR,KAAK/E,GACvDA,EAAK8U,cAAgB9U,EAAK8U,cAAc/P,KAAK/E,GAC7CA,EAAK6U,gBAAkB7U,EAAK6U,gBAAgB9P,KAAK/E,GACjDA,EAAK4Z,aAAe5Z,EAAK4Z,aAAa7U,KAAK/E,GAC3CA,EAAKgR,gBAAkBhR,EAAKgR,gBAAgBjM,KAAK/E,GACjDA,EAAKqT,OAASrT,EAAKqT,OAAOtO,KAAK/E,GAE/BA,EAAKuW,YAAc3I,EAAS8K,aAC5B1Y,EAAKwW,YAAc5I,EAAS+K,mBAC5B3Y,EAAKyW,gBAAkB7I,EAASgL,uBAEhC5Y,EAAK6Y,YAAc,IAAItM,EACrB,aACAqB,EAASkL,gBACTlL,EAAS8K,cAEX1Y,EAAK6Y,YAAYpL,eAAiBzN,EAAKgW,eAEvChW,EAAK+Y,iBAAmB,IAAI9B,EAC1B,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAK+Y,iBAAiBjB,eAAiB9X,EAAKkW,eAE5ClW,EAAKiZ,iBAAmB,IAAIhB,EAC1B,aACArK,EAASsL,wBACTtL,EAASgL,wBAEX5Y,EAAKiZ,iBAAiBR,eAAiBzY,EAAKmW,mBAAAA,CAAAA,CA2QhD,OAnXiCld,EAAAA,EAAAA,GAgHxB0uB,EAAAA,UAAAA,WAAP,SAAkB/qB,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAKyuB,eACZhrB,IAAOzD,KAAK0uB,eACZ1uB,KAAK2uB,UAAUnY,WAAW/S,GAAAA,EAQtB+qB,EAAAA,UAAAA,SAAR,WAEE,MADe,KAAKxuB,KAAKqE,GAAAA,IAAMrE,KAAKsE,GAAAA,MAAQtE,KAAK4uB,OAAAA,IAAU5uB,KAAK6uB,OAAAA,KAAW7uB,KAAKuE,GAAAA,IAAMvE,KAAKwE,EAAE,EAIvFgqB,EAAAA,UAAAA,aAAR,WACExuB,KAAKgX,OAAS1T,EAAU2T,cACxBjX,KAAKyuB,cAAgBnrB,EAAUwrB,WAC7B9uB,KAAK+uB,WACL,CACE,CAAC,SAAU,eACX,CAAC,gBAAiB/uB,KAAKqd,YAAc,IAAIlZ,YACzC,CAAC,OAAQ,iBAGbnE,KAAK0uB,aAAeprB,EAAUwrB,WAC5B9uB,KAAK+uB,WACL,CACE,CAAC,SAAU/uB,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,YAClC,CAAC,OAAQ,iBAGbnE,KAAKgX,OAAOtR,YAAY1F,KAAKyuB,eAC7BzuB,KAAKgX,OAAOtR,YAAY1F,KAAK0uB,cAE7B1uB,KAAKwd,2BAA2Bxd,KAAKgX,OAAO,EASvCwX,EAAAA,UAAAA,YAAP,SAAmBjZ,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAEzBxG,KAAKgvB,wBAA0BhvB,KAAK4uB,OACpC5uB,KAAKivB,wBAA0BjvB,KAAK6uB,OACjB,QAAf7uB,KAAK2V,QACP3V,KAAK4uB,OAASrZ,EAAMxP,EACpB/F,KAAK6uB,OAAStZ,EAAM/T,GAGH,QAAfxB,KAAK2V,OACP3V,KAAKid,eACLjd,KAAKygB,eAELzgB,KAAKkV,OAAS,YACLlV,KAAK2uB,UAAUnY,WAAWhQ,KACnCxG,KAAKgZ,WAAahZ,KAAK2uB,UACvB3uB,KAAKkV,OAAS,WAORsZ,EAAAA,UAAAA,aAAV,WACMxuB,KAAKyuB,eAAiBzuB,KAAK0uB,eAC7B1uB,KAAKyuB,cAAc1qB,aAAa,IAAK/D,KAAK+uB,YAE1C/uB,KAAK0uB,aAAa3qB,aAAa,IAAK/D,KAAK+uB,YAEzCzrB,EAAUc,cAAcpE,KAAK0uB,aAAc,CAAC,CAAC,SAAU1uB,KAAKod,eAC5D9Z,EAAUc,cAAcpE,KAAK0uB,aAAc,CAAC,CAAC,eAAgB1uB,KAAKqd,YAAYlZ,cAC9Eb,EAAUc,cAAcpE,KAAK0uB,aAAc,CAAC,CAAC,mBAAoB1uB,KAAKsd,gBAAgBnZ,cAAAA,EAOhFqqB,EAAAA,UAAAA,gBAAV,WACEvb,EAAAA,UAAM4E,gBAAAA,KAAAA,MACN7X,KAAKkvB,kBAAoB5rB,EAAUmY,WACjCzb,KAAKqE,GACLrE,KAAKsE,GACLtE,KAAK4uB,OACL5uB,KAAK6uB,OACL,CACE,CAAC,SAAU,SACX,CAAC,eAAgB,KACjB,CAAC,iBAAkB,OACnB,CAAC,mBAAoB,UAGzB7uB,KAAKmvB,kBAAoB7rB,EAAUmY,WACjCzb,KAAKuE,GACLvE,KAAKwE,GACLxE,KAAK4uB,OACL5uB,KAAK6uB,OACL,CACE,CAAC,SAAU,SACX,CAAC,eAAgB,KACjB,CAAC,iBAAkB,OACnB,CAAC,mBAAoB,UAIzB7uB,KAAKib,WAAWvF,aAAa1V,KAAKkvB,kBAAmBlvB,KAAKib,WAAWmU,YACrEpvB,KAAKib,WAAWvF,aAAa1V,KAAKmvB,kBAAmBnvB,KAAKib,WAAWmU,WAAW,EAMxEZ,EAAAA,UAAAA,gBAAV,WACExuB,KAAK2uB,UAAY3uB,KAAK4b,aACtB5b,KAAK4uB,OAAS,EACd5uB,KAAK6uB,OAAS,EACd5b,EAAAA,UAAMyI,gBAAAA,KAAAA,KAAAA,EAME8S,EAAAA,UAAAA,cAAV,WACEvb,EAAAA,UAAM0I,cAAAA,KAAAA,MACN,IAAMG,EAAW9b,KAAK2uB,UAAUxX,UAChCnX,KAAKkc,aAAalc,KAAK2uB,UAAU3X,OAAQhX,KAAK4uB,OAAS9S,EAAW,EAAG9b,KAAK6uB,OAAS/S,EAAW,GAE1F9b,KAAKkvB,mBAAqBlvB,KAAKmvB,oBACjCnvB,KAAKkvB,kBAAkBnrB,aAAa,KAAM/D,KAAKqE,GAAGF,YAClDnE,KAAKkvB,kBAAkBnrB,aAAa,KAAM/D,KAAKsE,GAAGH,YAClDnE,KAAKkvB,kBAAkBnrB,aAAa,KAAM/D,KAAK4uB,OAAOzqB,YACtDnE,KAAKkvB,kBAAkBnrB,aAAa,KAAM/D,KAAK6uB,OAAO1qB,YAEtDnE,KAAKmvB,kBAAkBprB,aAAa,KAAM/D,KAAKuE,GAAGJ,YAClDnE,KAAKmvB,kBAAkBprB,aAAa,KAAM/D,KAAKwE,GAAGL,YAClDnE,KAAKmvB,kBAAkBprB,aAAa,KAAM/D,KAAK4uB,OAAOzqB,YACtDnE,KAAKmvB,kBAAkBprB,aAAa,KAAM/D,KAAK6uB,OAAO1qB,YAAAA,EAQnDqqB,EAAAA,UAAAA,WAAP,SAAkBjZ,GACG,SAAfvV,KAAK2V,QACP3V,KAAK4uB,OAAS5uB,KAAKgvB,wBAA0BzZ,EAAMxP,EAAI/F,KAAK2Y,mBAC5D3Y,KAAK6uB,OAAS7uB,KAAKivB,wBAA0B1Z,EAAM/T,EAAIxB,KAAK4Y,oBAE9D3F,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,EAAAA,EAOTiZ,EAAAA,UAAAA,OAAV,SAAiBjZ,GACXvV,KAAKgZ,aAAehZ,KAAK2uB,YAC3B3uB,KAAK4uB,OAASrZ,EAAMxP,EACpB/F,KAAK6uB,OAAStZ,EAAM/T,GAEtByR,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACM,aAAfvV,KAAK2V,QACP3V,KAAK4uB,OAAS5uB,KAAKqE,IAAMrE,KAAKuE,GAAKvE,KAAKqE,IAAM,EAC9CrE,KAAK6uB,OAAS7uB,KAAKsE,IAAMtE,KAAKwE,GAAKxE,KAAKsE,IAAM,IAQxCkqB,EAAAA,UAAAA,eAAV,SAAyBjb,GACvBvT,KAAKod,YAAc7J,EACnBvT,KAAKygB,eACLzgB,KAAK8U,aAAavB,EAAAA,EAMVib,EAAAA,UAAAA,eAAV,SAAyBxqB,GACvBhE,KAAKqd,YAAcrZ,EACnBhE,KAAKygB,cAAc,EAOX+N,EAAAA,UAAAA,mBAAV,SAA6B/Q,GAC3Bzd,KAAKsd,gBAAkBG,EACvBzd,KAAKygB,cAAc,EASd+N,EAAAA,UAAAA,MAAP,SAAa3Y,EAAgBC,GAC3B9V,KAAK4uB,OAAS5uB,KAAK4uB,OAAS/Y,EAC5B7V,KAAK6uB,OAAS7uB,KAAK6uB,OAAS/Y,EAC5B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,EAAAA,EAOtBxW,OAAAA,eAAWkvB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CAACxuB,KAAK0f,YAAa1f,KAAK4f,iBAAkB5f,KAAK8f,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAMjD0O,EAAAA,UAAAA,SAAP,WACE,IAAMvtB,EAA2B3B,OAAO6c,OAAO,CAC7CiB,YAAapd,KAAKod,YAClBC,YAAard,KAAKqd,YAClBC,gBAAiBtd,KAAKsd,gBACtBsR,OAAQ5uB,KAAK4uB,OACbC,OAAQ7uB,KAAK6uB,QACZ5b,EAAAA,UAAMqC,SAAAA,KAAAA,OAGT,OAFArU,EAAOwM,SAAW+gB,EAAY/gB,SAEvBxM,CAAAA,EAQFutB,EAAAA,UAAAA,aAAP,SAAoB7Y,GAClB1C,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GAEnB,IAAMmL,EAAUnL,EAChB3V,KAAKod,YAAc0D,EAAQ1D,YAC3Bpd,KAAKqd,YAAcyD,EAAQzD,YAC3Brd,KAAKsd,gBAAkBwD,EAAQxD,gBAC/Btd,KAAK4uB,OAAS9N,EAAQ8N,OACtB5uB,KAAK6uB,OAAS/N,EAAQ+N,OAEtB7uB,KAAKid,eACLjd,KAAKygB,cAAc,EA3WP+N,EAAAA,SAAW,cAKXA,EAAAA,MAAQ,eAIRA,EAAAA,KAAAA,4iBAAAA,CAAAA,CDuEAF,CCtFiBrO,GAAAA,EAAAA,SAAAA,GCsG/B,WACEtQ,EACA6E,EACAC,GAHF,MAKExB,EAAAA,KAAAA,KAAMtD,EAAW6E,EAAkBC,IAAAA,KAAAA,OAtF3B5N,EAAAA,UAAY,cAIZA,EAAAA,YAAc,cAIdA,EAAAA,YAAc,EAIdA,EAAAA,gBAAkB,GAQlBA,EAAAA,UAAY,cAIZA,EAAAA,SAAW,OA6BXA,EAAAA,SAAAA,EAmBAA,EAAAA,YAAc,UA6LPA,EAAAA,QAAU,EACnBA,EAAAA,gBAAkB,EAClBA,EAAAA,iBAAmB,EA/KzBA,EAAKuW,YAAc3I,EAAS8K,aAC5B1Y,EAAKwW,YAAc5I,EAAS+K,mBAC5B3Y,EAAKyW,gBAAkB7I,EAASgL,uBAChC5Y,EAAKsW,UAAY1I,EAAS4W,iBAC1BxkB,EAAKwoB,UAAY5a,EAAS4X,mBAC1BxlB,EAAKya,WAAa7M,EAASoN,kBAC3Bhb,EAAK+e,SAAWnR,EAAS6a,uBACzBzoB,EAAK0oB,YAAc9a,EAAS+a,mBAE5B3oB,EAAKgW,eAAiBhW,EAAKgW,eAAejR,KAAK/E,GAC/CA,EAAKiW,aAAejW,EAAKiW,aAAalR,KAAK/E,GAC3CA,EAAKkW,eAAiBlW,EAAKkW,eAAenR,KAAK/E,GAC/CA,EAAKmW,mBAAqBnW,EAAKmW,mBAAmBpR,KAAK/E,GACvDA,EAAKoW,aAAepW,EAAKoW,aAAarR,KAAK/E,GAC3CA,EAAK4oB,YAAc5oB,EAAK4oB,YAAY7jB,KAAK/E,GACzCA,EAAK6oB,eAAiB7oB,EAAK6oB,eAAe9jB,KAAK/E,GAC/CA,EAAKsb,eAAiBtb,EAAKsb,eAAevW,KAAK/E,GAC/CA,EAAKub,mBAAqBvb,EAAKub,mBAAmBxW,KAAK/E,GACvDA,EAAK8oB,kBAAoB9oB,EAAK8oB,kBAAkB/jB,KAAK/E,GACrDA,EAAKkb,QAAUlb,EAAKkb,QAAQnW,KAAK/E,GACjCA,EAAK+oB,aAAe/oB,EAAK+oB,aAAahkB,KAAK/E,GAE3CA,EAAK6Y,YAAc,IAAItM,EACrB,eACIqB,EAASkL,gBAAgB,CAAC,gBAC9B9Y,EAAKuW,aAEPvW,EAAK6Y,YAAYpL,eAAiBzN,EAAKgW,eAEvChW,EAAKykB,UAAY,IAAIlY,EACnB,eACIqB,EAASkL,gBAAgB,CAAC,gBAC9B9Y,EAAKsW,UACL0P,GAEFhmB,EAAKykB,UAAUhX,eAAiBzN,EAAKiW,aAErCjW,EAAK+Y,iBAAmB,IAAI9B,EAC1B,aACArJ,EAASoL,oBACTpL,EAAS+K,oBAEX3Y,EAAK+Y,iBAAiBjB,eAAiB9X,EAAKkW,eAE5ClW,EAAKiZ,iBAAmB,IAAIhB,EAC1B,aACArK,EAASsL,wBACTtL,EAASgL,wBAEX5Y,EAAKiZ,iBAAiBR,eAAiBzY,EAAKmW,mBAE5CnW,EAAK0b,gBAAkB,IAAIrB,EACzB,OACAzM,EAAS+N,oBACT/N,EAASoN,mBAEXhb,EAAK0b,gBAAgBZ,cAAgB9a,EAAKkb,QAE1Clb,EAAKgpB,eAAiB,IAAIzc,EACxB,aACAqB,EAASkL,gBACT9Y,EAAKwoB,UACL1C,GAEF9lB,EAAKgpB,eAAevb,eAAiBzN,EAAK+oB,aAAAA,CAAAA,CAwZ9C,OAnkBwC9vB,EAAAA,EAAAA,GAoL/BgwB,EAAAA,UAAAA,WAAP,SAAkBrsB,GAChB,SACEwP,EAAAA,UAAMuD,WAAAA,KAAAA,KAAW/S,IACjBA,IAAOzD,KAAKgX,QACZvT,IAAOzD,KAAK+vB,OACZtsB,IAAOzD,KAAKgwB,WACZvsB,IAAOzD,KAAKiwB,eAAAA,EAWNH,EAAAA,UAAAA,aAAV,WACE9vB,KAAKgX,OAAS1T,EAAU2T,cACxBjX,KAAKwd,2BAA2Bxd,KAAKgX,QAErChX,KAAKgwB,UAAY1sB,EAAUgY,WAAW,EAAG,EAAG,CAAC,CAAC,OAAQtb,KAAKmd,aAC3Dnd,KAAKgX,OAAOtR,YAAY1F,KAAKgwB,WAE7BhwB,KAAKiwB,eAAiB3sB,EAAUwf,WAAW,CACzC,CAAC,OAAQ9iB,KAAKqvB,WACd,CAAC,cAAervB,KAAKshB,cAEvBthB,KAAKiwB,eAAezlB,MAAMob,SAAW5lB,KAAK4lB,SAC1C5lB,KAAKiwB,eAAezlB,MAAM0lB,WAAa,QACvClwB,KAAKiwB,eAAezlB,MAAM2lB,iBAAmB,mBAC7CnwB,KAAKiwB,eAAepqB,YAAc7F,KAAKuvB,YACvCvvB,KAAKgX,OAAOtR,YAAY1F,KAAKiwB,gBAE7BjwB,KAAK+vB,MAAQzsB,EAAUgY,WAAWtb,KAAKgE,MAAOhE,KAAKiE,OAAQ,CACzD,CAAC,OAAQ,eACT,CAAC,SAAUjE,KAAKod,aAChB,CAAC,eAAgBpd,KAAKqd,YAAYlZ,YAClC,CAAC,mBAAoBnE,KAAKsd,mBAG5Btd,KAAKgX,OAAOtR,YAAY1F,KAAK+vB,OAC7B/vB,KAAKyvB,aAAa,EAObK,EAAAA,UAAAA,eAAP,SAAsBnqB,GACpB3F,KAAKuvB,YAAc5pB,EACnB3F,KAAKiwB,eAAepqB,YAAc7F,KAAKuvB,YACvCvvB,KAAKyvB,aAAa,EASbK,EAAAA,UAAAA,YAAP,SAAmBva,EAAe/O,GAChCyM,EAAAA,UAAMmF,YAAAA,KAAAA,KAAY7C,EAAO/O,GAEzBxG,KAAK+iB,SAAAA,EACL/iB,KAAKgjB,iBAAmBzN,EACxBvV,KAAKijB,qBAAuBC,KAAKC,MAEd,QAAfnjB,KAAK2V,QACP3V,KAAKid,eAELjd,KAAKqZ,WAAW9D,GAEhBvV,KAAKkV,OAAS,aASX4a,EAAAA,UAAAA,WAAP,SAAkBva,GAChBtC,EAAAA,UAAM8G,WAAAA,KAAAA,KAAWxE,QAAAA,IACbvV,KAAKgjB,mBACPhjB,KAAK+iB,QACHnU,KAAK4L,IAAIjF,EAAMxP,EAAI/F,KAAKgjB,iBAAiBjd,GAAK,GAC9C6I,KAAK4L,IAAIjF,EAAM/T,EAAIxB,KAAKgjB,iBAAiBxhB,GAAK,IAQ1CsuB,EAAAA,UAAAA,OAAV,SAAiBva,GACftC,EAAAA,UAAMiH,OAAAA,KAAAA,KAAO3E,GACbvV,KAAKua,SAAS,EASNuV,EAAAA,UAAAA,YAAV,WACE,IAAM/K,EAAW/kB,KAAKiwB,eAAezL,UACL,KAA5BxkB,KAAKuvB,YAAY/f,QACnBxP,KAAKowB,gBAAkBrL,EAAS/gB,MAAuB,EAAfhE,KAAKqwB,QAC7CrwB,KAAKswB,iBAAmBvL,EAAS9gB,OAAwB,EAAfjE,KAAKqwB,UAE/CrwB,KAAKowB,gBAAkB,EACvBpwB,KAAKswB,iBAAmB,GAG1BhtB,EAAUc,cAAcpE,KAAKgwB,UAAW,CACtC,CAAC,QAAShwB,KAAKowB,gBAAgBjsB,YAC/B,CAAC,SAAUnE,KAAKswB,iBAAiBnsB,YACjC,CACE,YACA,eAAenE,KAAKgE,MAAAA,KAAUhE,KAAKiE,OAAAA,MAAYjE,KAAKgE,MAAAA,UAGxDV,EAAUc,cAAcpE,KAAKiwB,eAAgB,CAC3C,CAAC,IAAKjwB,KAAKqwB,QAAQlsB,YACnB,CAAC,IAAKnE,KAAKqwB,QAAQlsB,YACnB,CACE,YACA,gBAAenE,KAAKgE,MAAQhE,KAAKqwB,SAAAA,KAAYrwB,KAAKiE,OAAAA,OAC/CjE,KAAKgE,MAAQhE,KAAKqwB,SAAAA,SAAAA,EASnBP,EAAAA,UAAAA,eAAR,sBACE9vB,KAAKkV,OAAS,OACdlV,KAAKwU,iBAAiBpN,UAAY,GAElCpH,KAAKslB,YAAc/hB,SAASqD,cAAc,OAC1C5G,KAAKslB,YAAY9a,MAAMsC,SAAW,IAClC9M,KAAKslB,YAAY9a,MAAMyT,WAAa,SACpCje,KAAKslB,YAAY9a,MAAM0T,eAAiB,SACxCle,KAAKslB,YAAY9a,MAAMsG,cAAgB,OACvC9Q,KAAKslB,YAAY9a,MAAM8I,SAAW,SAElCtT,KAAKuwB,YAAchtB,SAASqD,cAAc,SAC1C5G,KAAKuwB,YAAY/lB,MAAMya,SAAW,WAClCjlB,KAAKuwB,YAAY/lB,MAAMxG,MAAWhE,KAAKgE,MAAAA,KACnChE,KAAKswB,iBAAmB,IAC1BtwB,KAAKuwB,YAAY/lB,MAAMvG,OAAYjE,KAAKswB,iBAAAA,MAE1CtwB,KAAKuwB,YAAY/lB,MAAMob,SAAW5lB,KAAK4lB,SACvC5lB,KAAKuwB,YAAY/lB,MAAM8W,WAAathB,KAAKshB,WACzCthB,KAAKuwB,YAAY/lB,MAAM4J,gBAAkBpU,KAAKmd,UAC9Cnd,KAAKuwB,YAAY/lB,MAAM+I,MAAQvT,KAAKqvB,UACpCrvB,KAAKuwB,YAAY/lB,MAAMuJ,YAAc,IACrC/T,KAAKuwB,YAAYxsB,aAAa,QAAS/D,KAAKuvB,aAC5CvvB,KAAKuwB,YAAYxX,SAEjB/Y,KAAKslB,YAAY5f,YAAY1F,KAAKuwB,aAClCvwB,KAAKwU,iBAAiB9O,YAAY1F,KAAKslB,aAEvCtlB,KAAKuwB,YAAY3iB,iBAAiB,sBAAc8X,GAC9CA,EAAGC,iBAAAA,IAEL3lB,KAAKuwB,YAAY3iB,iBAAiB,qBAAa8X,GAC9B,UAAXA,EAAGtf,KACLS,EAAK8oB,kBAAkB9oB,EAAK0pB,YAAY3vB,MAAAA,IAG5CZ,KAAKuwB,YAAY3iB,iBAAiB,kBAAU8X,GAC1CA,EAAGM,cAAAA,CAAe,IAEpBhmB,KAAKuwB,YAAY3iB,iBAAiB,mBAChC/G,EAAK8oB,kBAAkB9oB,EAAK0pB,YAAY3vB,MAAAA,IAE1CZ,KAAKslB,YAAY1X,iBAAiB,wBAChC/G,EAAK8oB,kBAAkB9oB,EAAK0pB,YAAY3vB,MAAAA,IAG1CZ,KAAKoiB,qBACLpiB,KAAKuwB,YAAY1J,OAAO,EAGlBiJ,EAAAA,UAAAA,mBAAR,WACqB,SAAf9vB,KAAK2V,aAAAA,IACH3V,KAAKuwB,YACPvwB,KAAKmiB,kBAELniB,KAAKuwB,YAAY/lB,MAAMuN,KAAU/X,KAAK+X,KAAAA,KACtC/X,KAAKuwB,YAAY/lB,MAAMyE,IAASjP,KAAKiP,IAAAA,KACrCjP,KAAKuwB,YAAY/lB,MAAMkN,UAAY,UAAU1X,KAAKyZ,cAAAA,OAClDzZ,KAAKuwB,YAAY/lB,MAAMgmB,gBAAqBxwB,KAAKgE,MAAQ,QACvDhE,KAAKiE,OAAS,UAMd6rB,EAAAA,UAAAA,kBAAR,SAA0BnqB,GACxB3F,KAAK0vB,eAAe/pB,EAAK6J,QACzBxP,KAAKwU,iBAAiBpN,UAAY,GAClCpH,KAAK6U,cAAc,EAOXib,EAAAA,UAAAA,QAAV,SAAkB3O,GACZnhB,KAAKiwB,gBACP3sB,EAAUc,cAAcpE,KAAKiwB,eAAgB,CAAC,CAAC,cAAe9O,KAEhEnhB,KAAKshB,WAAaH,EACdnhB,KAAKuwB,cACPvwB,KAAKuwB,YAAY/lB,MAAM8W,WAAathB,KAAKshB,YAE3CthB,KAAKyvB,cACLzvB,KAAK6U,cAAc,EAOXib,EAAAA,UAAAA,aAAV,SAAuBvc,GACjBvT,KAAKiwB,gBACP3sB,EAAUc,cAAcpE,KAAKiwB,eAAgB,CAAC,CAAC,OAAQ1c,KAEzDvT,KAAKqvB,UAAY9b,EACbvT,KAAKuwB,cACPvwB,KAAKuwB,YAAY/lB,MAAM+I,MAAQvT,KAAKqvB,WAEtCrvB,KAAK6U,cAAc,EAMXib,EAAAA,UAAAA,QAAV,WACE7c,EAAAA,UAAMsH,QAAAA,KAAAA,MACNjX,EAAUc,cAAcpE,KAAK+vB,MAAO,CAClC,CAAC,QAAS/vB,KAAKgE,MAAMG,YACrB,CAAC,SAAUnE,KAAKiE,OAAOE,cAEzBnE,KAAKyvB,aAAa,EAQbK,EAAAA,UAAAA,UAAP,SAAiBva,GACftC,EAAAA,UAAM4G,UAAAA,KAAAA,KAAUtE,GAChBvV,KAAKua,WAEAva,KAAK+iB,SAAWG,KAAKC,MAAQnjB,KAAKijB,qBAAuB,KAC5DjjB,KAAKmiB,iBAEPniB,KAAKgjB,sBAAAA,CAAmB5X,EAQnB0kB,EAAAA,UAAAA,SAAP,SAAgBva,EAAe/O,GAC7ByM,EAAAA,UAAMmU,SAAAA,KAAAA,KAAS7R,EAAO/O,GAEtBxG,KAAKmiB,gBAAgB,EAOb2N,EAAAA,UAAAA,eAAV,SAAyBvc,GACvBvT,KAAKod,YAAc7J,EACfvT,KAAK+vB,OACPzsB,EAAUc,cAAcpE,KAAK+vB,MAAO,CAAC,CAAC,SAAU/vB,KAAKod,eAEvDpd,KAAK8U,aAAavB,GAClBvT,KAAK6U,cAAc,EAMXib,EAAAA,UAAAA,aAAV,SAAuBvc,GACrBvT,KAAKmd,UAAY5J,EACbvT,KAAKgwB,WACP1sB,EAAUc,cAAcpE,KAAKgwB,UAAW,CAAC,CAAC,OAAQhwB,KAAKmd,aAEzDnd,KAAK+U,iBAAiBxB,GACtBvT,KAAK6U,cAAc,EAMXib,EAAAA,UAAAA,eAAV,SAAyB9rB,GACvBhE,KAAKqd,YAAcrZ,EACfhE,KAAK+vB,OACPzsB,EAAUc,cAAcpE,KAAK+vB,MAAO,CAClC,CAAC,eAAgB/vB,KAAKqd,YAAYlZ,cAGtCnE,KAAK6U,cAAc,EAMXib,EAAAA,UAAAA,mBAAV,SAA6BrS,GAC3Bzd,KAAKsd,gBAAkBG,EACnBzd,KAAK+vB,OACPzsB,EAAUc,cAAcpE,KAAK+vB,MAAO,CAClC,CAAC,mBAAoB/vB,KAAKsd,mBAG9Btd,KAAK6U,cAAc,EAMrBvV,OAAAA,eAAWwwB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,MAAO,CACL9vB,KAAK0f,YACL1f,KAAKsrB,UACLtrB,KAAK4f,iBACL5f,KAAK8f,iBACL9f,KAAKuiB,gBACLviB,KAAK6vB,eAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAOFC,EAAAA,UAAAA,SAAP,WACE,IAAM7uB,EAAkC3B,OAAO6c,OAC7C,CACEgB,UAAWnd,KAAKmd,UAChBC,YAAapd,KAAKod,YAClBC,YAAard,KAAKqd,YAClBC,gBAAiBtd,KAAKsd,gBACtBC,QAAS,EACT8R,UAAWrvB,KAAKqvB,UAChB/N,WAAYthB,KAAKshB,WACjBsE,SAAU5lB,KAAK4lB,SACf2J,YAAavvB,KAAKuvB,aAEpBtc,EAAAA,UAAMqC,SAAAA,KAAAA,OAIR,OAFArU,EAAOwM,SAAWzN,KAAKyN,SAEhBxM,CAAAA,EAQF6uB,EAAAA,UAAAA,aAAP,SAAoBna,GAClB,IAAM8a,EAAU9a,EAChB3V,KAAKmd,UAAYsT,EAAQtT,UACzBnd,KAAKod,YAAcqT,EAAQrT,YAC3Bpd,KAAKqd,YAAcoT,EAAQpT,YAC3Brd,KAAKsd,gBAAkBmT,EAAQnT,gBAC/Btd,KAAKqvB,UAAYoB,EAAQpB,UACzBrvB,KAAKshB,WAAamP,EAAQnP,WAC1BthB,KAAKuvB,YAAckB,EAAQlB,YAC3BvvB,KAAK4lB,SAAW6K,EAAQ7K,SAExB5lB,KAAKid,eACLhK,EAAAA,UAAMsJ,aAAAA,KAAAA,KAAa5G,GACnB3V,KAAKua,SAAS,EASTuV,EAAAA,UAAAA,MAAP,SAAaja,EAAgBC,GAC3B7C,EAAAA,UAAM0J,MAAAA,KAAAA,KAAM9G,EAAQC,GAEpB9V,KAAKua,SAAS,EA3jBFuV,EAAAA,SAAW,qBAIXA,EAAAA,MAAQ,uBAIRA,EAAAA,KAAAA,4JAAAA,CAAAA,CDhBiB7P,CCEOnI,GAAAA,EAAAA,WCEtC,WAAY4Y,EAAwBC,QAAAA,IAAAA,IAAAA,GAAAA,GAX7B3wB,KAAAA,YAAAA,EAECA,KAAAA,mBAAAA,EAUNA,KAAK0wB,WAAaA,EAClB1wB,KAAK2wB,WAAaA,CAAAA,CAEtB,OAZErxB,OAAAA,eAAWsxB,EAAAA,UAAAA,mBAAAA,CAAAA,IAAX,WACE,OAAO5wB,KAAK6wB,iBAAiB,EAAjBA,YAAAA,EAAAA,cAAAA,IAGPD,EAAAA,UAAAA,eAAP,WACE5wB,KAAK6wB,mBAAAA,CAAoB,EAAApvB,CAAA,CDCWqW,GCDX,WAAArW,GAa3B,WAAYivB,EAAwBI,EAAiBnb,GAArD,MACE1C,EAAAA,KAAAA,KAAMyd,GAAAA,IAAY,YAClB7pB,EAAKiqB,QAAUA,EACfjqB,EAAK8O,MAAQA,EAAAA,CAAAA,CAEjB,OAT2C7V,EAAAA,EAAAA,GAAAA,CAAAA,CATd,CASc8wB,GAAAA,EAAAA,SAAAA,GAezC,WAAYF,EAAwBjrB,EAAqBkrB,QAAAA,IAAAA,IAAAA,GAAAA,GAAzD,MACE1d,EAAAA,KAAAA,KAAMyd,EAAYC,IAAAA,KAAAA,OAClB9pB,EAAKpB,OAASA,EAAAA,CAAAA,CAElB,OAPiC3F,EAAAA,EAAAA,GAAAA,CAAAA,CAZU8wB,CAYVA,GAAAA,EAAAA,WA4HjC,aAIE5wB,KAAAA,OAAyC,GAIzCA,KAAAA,YAAwC,GAIxCA,KAAAA,MAAkC,GAIlCA,KAAAA,KAAiC,GAIjCA,KAAAA,aAAyC,GAMzCA,KAAAA,YAAwC,GAIxCA,KAAAA,aAAqC,GAIrCA,KAAAA,eAAuC,GAIvCA,KAAAA,eAAuC,GAIvCA,KAAAA,aAAqC,GAIrCA,KAAAA,mBAA2C,GAI3CA,KAAAA,aAAqC,GAMrCA,KAAAA,aAAqC,GAMrCA,KAAAA,MAAkC,GAMlCA,KAAAA,KAAiC,EAAE,CA6BrC,OArBS+wB,EAAAA,UAAAA,iBAAP,SACEC,EACAC,GAEyBjxB,KAAKgxB,GAAYruB,KAAKsuB,EAAAA,EAQ1CF,EAAAA,UAAAA,oBAAP,SACEC,EACAC,GAEA,IAAMpe,EAAiC7S,KAAKgxB,GAAYziB,QAAQ0iB,GAC5Dpe,GAAS,GACc7S,KAAKgxB,GAAYxiB,OAAOqE,EAAO,EAAE,EAAF,EA1N7B+d,GA0N6B,aC6I5D,WAAYpqB,GAnSJxG,KAAAA,YAAc,EA0EdA,KAAAA,sBAA6CA,KAClDkxB,qBAmCKlxB,KAAAA,KAAuB,SAYvBA,KAAAA,QAAwB,GAExBA,KAAAA,YAAAA,EAOAA,KAAAA,qBAA6C,GAC7CA,KAAAA,oBAA2C,GAE5CA,KAAAA,SAAqB,IAAImxB,EAGxBnxB,KAAAA,SAAAA,EAUAA,KAAAA,gBAEJ,IAAIkuB,EAkCDluB,KAAAA,qBAAAA,EAMAA,KAAAA,gBAAkB,YAiBlBA,KAAAA,mBAAAA,EA4BAA,KAAAA,UAAY,CAAC,EAAG,IAAK,EAAG,GACvBA,KAAAA,WAAa,EAuVbA,KAAAA,aAAAA,EAurBAA,KAAAA,aAAuB,CAAE+F,EAAG,EAAGvE,EAAG,GAuXlCxB,KAAAA,eAAiB,IAAI+wB,EA+BrB/wB,KAAAA,mBAAAA,EAsBAA,KAAAA,YAAAA,EAj4CNA,KAAKoxB,YAAcC,EAAWC,kBAE9BtxB,KAAKyL,OAAS,IAAIzC,EAAahJ,KAAK4I,YAEpC5I,KAAKwL,gBAAkBxL,KAAKyL,OAAOgJ,SAEnCzU,KAAKwG,OAASA,EACdxG,KAAKuxB,WAAahuB,SAASjC,KAE3BtB,KAAKgE,MAAQwC,EAAOsI,YACpB9O,KAAKiE,OAASuC,EAAOmiB,aAErB3oB,KAAKyL,OAAO+lB,mBAEZxxB,KAAKyxB,KAAOzxB,KAAKyxB,KAAK7lB,KAAK5L,MAC3BA,KAAK0xB,WAAa1xB,KAAK0xB,WAAW9lB,KAAK5L,MAEvCA,KAAK2xB,qBAAuB3xB,KAAK2xB,qBAAqB/lB,KAAK5L,MAC3DA,KAAK4xB,gBAAkB5xB,KAAK4xB,gBAAgBhmB,KAAK5L,MACjDA,KAAK6xB,aAAe7xB,KAAK6xB,aAAajmB,KAAK5L,MAC3CA,KAAK8xB,cAAgB9xB,KAAK8xB,cAAclmB,KAAK5L,MAC7CA,KAAK8L,iBAAmB9L,KAAK8L,iBAAiBF,KAAK5L,MACnDA,KAAK+xB,cAAgB/xB,KAAK+xB,cAAcnmB,KAAK5L,MAC7CA,KAAKgyB,WAAahyB,KAAKgyB,WAAWpmB,KAAK5L,MACvCA,KAAKiyB,cAAgBjyB,KAAKiyB,cAAcrmB,KAAK5L,MAC7CA,KAAKkyB,YAAclyB,KAAKkyB,YAAYtmB,KAAK5L,MACzCA,KAAKmyB,aAAenyB,KAAKmyB,aAAavmB,KAAK5L,MAC3CA,KAAKoyB,QAAUpyB,KAAKoyB,QAAQxmB,KAAK5L,MACjCA,KAAKqyB,iBAAmBryB,KAAKqyB,iBAAiBzmB,KAAK5L,MACnDA,KAAKsyB,gBAAkBtyB,KAAKsyB,gBAAgB1mB,KAAK5L,MACjDA,KAAKuyB,MAAQvyB,KAAKuyB,MAAM3mB,KAAK5L,MAC7BA,KAAKwyB,QAAUxyB,KAAKwyB,QAAQ5mB,KAAK5L,MACjCA,KAAKyyB,sBAAwBzyB,KAAKyyB,sBAAsB7mB,KAAK5L,MAC7DA,KAAK0yB,yBAA2B1yB,KAAK0yB,yBAAyB9mB,KAAK5L,MACnEA,KAAK2yB,uBAAyB3yB,KAAK2yB,uBAAuB/mB,KAAK5L,MAC/DA,KAAK4yB,0BAA4B5yB,KAAK4yB,0BAA0BhnB,KAAK5L,MACrEA,KAAK6yB,yBAA2B7yB,KAAK6yB,yBAAyBjnB,KAAK5L,MACnEA,KAAK8yB,eAAiB9yB,KAAK8yB,eAAelnB,KAAK5L,MAC/CA,KAAK+yB,qBAAuB/yB,KAAK+yB,qBAAqBnnB,KAAK5L,MAC3DA,KAAKgzB,gBAAkBhzB,KAAKgzB,gBAAgBpnB,KAAK5L,MACjDA,KAAKizB,aAAejzB,KAAKizB,aAAarnB,KAAK5L,MAC3CA,KAAK8U,aAAe9U,KAAK8U,aAAalJ,KAAK5L,MAC3CA,KAAK+U,iBAAmB/U,KAAK+U,iBAAiBnJ,KAAK5L,MACnDA,KAAKkzB,oBAAsBlzB,KAAKkzB,oBAAoBtnB,KAAK5L,MACzDA,KAAKmzB,gBAAkBnzB,KAAKmzB,gBAAgBvnB,KAAK5L,MACjDA,KAAKozB,gBAAkBpzB,KAAKozB,gBAAgBxnB,KAAK5L,MACjDA,KAAKqzB,SAAWrzB,KAAKqzB,SAASznB,KAAK5L,MACnCA,KAAK6mB,MAAQ7mB,KAAK6mB,MAAMjb,KAAK5L,MAC7BA,KAAKszB,KAAOtzB,KAAKszB,KAAK1nB,KAAK5L,MAC3BA,KAAKuzB,mBAAqBvzB,KAAKuzB,mBAAmB3nB,KAAK5L,MACvDA,KAAKwzB,mBAAqBxzB,KAAKwzB,mBAAmB5nB,KAAK5L,MACvDA,KAAKyzB,QAAUzzB,KAAKyzB,QAAQ7nB,KAAK5L,MACjCA,KAAK0zB,eAAiB1zB,KAAK0zB,eAAe9nB,KAAK5L,KAAK,CA+3CxD,OAhsDEV,OAAAA,eAAW+xB,EAAAA,UAAAA,mBAAAA,CAAAA,IAAX,WACE,MAAO,CACLrR,EACA8H,EACA4C,EACAjI,EACAwL,EACAN,EACAvB,EACAW,EACAc,EACAtC,EACA5K,EACA6N,EACAsB,EAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAUJxwB,OAAAA,eAAW+xB,EAAAA,UAAAA,uBAAAA,CAAAA,IAAX,WACE,MAAO,CACLrR,EACA8H,EACA4C,EACAjI,EACAkL,EACAvB,EACAW,EAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IASJztB,OAAAA,eAAW+xB,EAAAA,UAAAA,qBAAAA,CAAAA,IAAX,WACE,MAAO,CACLrR,EACA8H,EACA4C,EACAjI,EACA2J,EAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAiBJ9sB,OAAAA,eAAW+xB,EAAAA,UAAAA,uBAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAK2zB,qBAAqB,EAArBA,IAGd,SAAgC/yB,GAAhC,WACEZ,KAAK2zB,sBAAsBnlB,OAAO,GAClC5N,EAAMyM,SAAQ,SAACumB,GACb,GAAkB,iBAAPA,EAAiB,CAC1B,IAAMC,EAAWhtB,EAAKitB,iBAAiBtjB,MAAK,SACzCujB,GAAS,OAAAA,EAAKtmB,WAAammB,CAAAA,SAAAA,IAE1BC,GACFhtB,EAAK8sB,sBAAsBhxB,KAAKkxB,EAAAA,MAGlChtB,EAAK8sB,sBAAsBhxB,KAAKixB,EAAAA,GAAAA,EAAAA,YAAAA,EAAAA,cAAAA,IAiBtCt0B,OAAAA,eAAW+xB,EAAAA,UAAAA,gBAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAKg0B,cAAc,EAAdA,YAAAA,EAAAA,cAAAA,IAuBd10B,OAAAA,eAAW+xB,EAAAA,UAAAA,SAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAKi0B,OAAO,EAAPA,YAAAA,EAAAA,cAAAA,IAYd30B,OAAAA,eAAW+xB,EAAAA,UAAAA,iBAAAA,CAAAA,IAAX,WACE,SAAIrxB,KAAKk0B,kBAAmBl0B,KAAKk0B,gBAAgBC,eAAe,EAAfA,YAAAA,EAAAA,cAAAA,IAYlD70B,OAAAA,eAAW+xB,EAAAA,UAAAA,iBAAAA,CAAAA,IAAX,WACC,SAAIrxB,KAAKk0B,kBAAmBl0B,KAAKk0B,gBAAgBE,eAAe,EAAfA,YAAAA,EAAAA,cAAAA,IAuEnD90B,OAAAA,eAAW+xB,EAAAA,UAAAA,YAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAKq0B,UAAU,EAAVA,IAOd,SAAqBzzB,GACnBZ,KAAKq0B,WAAazzB,EACdZ,KAAKs0B,cAAgBt0B,KAAKu0B,aAC5Bv0B,KAAKs0B,aAAa9pB,MAAMkN,UAAY,SAAS1X,KAAKq0B,WAAAA,IAClDr0B,KAAKu0B,WAAWC,SAAS,CACvBzc,MACG/X,KAAKs0B,aAAaxlB,YAAc9O,KAAKq0B,WACpCr0B,KAAKu0B,WAAWzlB,aAClB,EACFG,KACGjP,KAAKs0B,aAAa3L,aAAe3oB,KAAKq0B,WACrCr0B,KAAKu0B,WAAW5L,cAClB,sCAORrpB,OAAAA,eAAW+xB,EAAAA,UAAAA,aAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAKoxB,WAAW,EAAXA,YAAAA,EAAAA,cAAAA,IA8ENC,EAAAA,UAAAA,KAAR,WACErxB,KAAKy0B,sBACLz0B,KAAK00B,mBACL10B,KAAK0xB,aACL1xB,KAAK20B,mBACL30B,KAAK40B,cACL50B,KAAK60B,eAC6B,UAA9B70B,KAAKyU,SAASnJ,aAChBtL,KAAKkzB,sBAGF/sB,EAAU2uB,YAKb90B,KAAK+0B,UAGP/0B,KAAKi0B,SAAAA,EACLj0B,KAAKg1B,YAAAA,CAAa,EAMb3D,EAAAA,UAAAA,KAAP,+BAEMrxB,KAAKyL,OAAOZ,qBAAAA,IAAgCoqB,EAAMpqB,iBACpD7K,KAAKyL,OAAOZ,eAAiBoqB,EAAMpqB,gBAIrC7K,KAAKk1B,QAAQ1mB,OAAO,GAEpBxO,KAAKgzB,kBACLhzB,KAAKm1B,SACLn1B,KAAKyxB,OACLzxB,KAAKo1B,eAAqB,KAAE/nB,SAAQ,SAACgB,GACnC,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,GAAAA,EAUpBwqB,EAAAA,UAAAA,OAAb,2GAaE,OAZArxB,KAAK8L,oBAECupB,EAAW,IAAI9uB,GACZQ,YAAc/G,KAAKs1B,oBAC5BD,EAAS5sB,UAAYzI,KAAKu1B,gBAC1BF,EAAS3sB,aAAe1I,KAAKw1B,mBAC7BH,EAASvuB,YAAc9G,KAAKy1B,kBAC5BJ,EAASrxB,MAAQhE,KAAK01B,YACtBL,EAASpxB,OAASjE,KAAK21B,aAAAA,CAAAA,EAIjBN,EAASO,UACb51B,KAAKwG,kBAAkBqvB,iBAAmB71B,KAAKwG,OAAS,KACxDxG,KAAKyG,YACLzG,KAAK81B,eAAAA,KAAAA,EAGA,OANPjyB,EAAAA,OAAAA,CAAAA,EAMawxB,EAASO,UACpB51B,KAAKwG,kBAAkBqvB,iBAAmB71B,KAAKwG,OAAS,KACxDxG,KAAKyG,YACLzG,KAAK81B,eAAAA,KAAAA,EAHP,MAAM,CAAN,EAAOjyB,EAAAA,QAAAA,GAAAA,GAAAA,EAUFwtB,EAAAA,UAAAA,MAAP,SAAa0E,GAAb,WACE,gBADWA,GAAAA,GACP/1B,KAAKg2B,OAAQ,CACf,IAAIC,GAAAA,EAECF,GACH/1B,KAAKo1B,eAA4B,YAAE/nB,SAAQ,SAACgB,GAC1C,IAAMqX,EAAK,IAAIkL,EAAgB/pB,GAAAA,GAC/BwH,EAASqX,GACLA,EAAGwQ,mBACLD,GAAAA,EAAS,IAKVA,IACCj2B,KAAKm2B,UACPn2B,KAAKwyB,UAEHxyB,KAAKo2B,iBACPp2B,KAAKo2B,eAAeC,UAAUr2B,KAAKwG,QACnCxG,KAAKo2B,eAAeC,UAAUr2B,KAAKs0B,eAEH,UAA9Bt0B,KAAKyU,SAASnJ,aAChBzD,OAAOyuB,oBAAoB,SAAUt2B,KAAKgzB,iBAG5ChzB,KAAKo1B,eAAsB,MAAE/nB,SAAQ,SAACgB,GACpC,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,IAE/B7G,KAAKu2B,eACLv2B,KAAKi0B,SAAAA,EAAU,GAUd5C,EAAAA,UAAAA,oBAAP,8BAA2BztB,EAAAA,UAAAA,OAAAA,IAAAsxB,EAAAA,GAAAA,UAAAA,IACzBrxB,EAAA7D,KAAK2zB,uBAAsBhxB,KAAAA,MAAAA,EAAQuyB,EAAAA,EAuB9B7D,EAAAA,UAAAA,uBAAP,SAA8BhjB,GAE5BrO,KAAK4N,iBAAiB,mBAAW4oB,GAC/BnoB,EAASmoB,EAAM1F,QAAS0F,EAAM7gB,MAAAA,GAAAA,EAW3B0b,EAAAA,UAAAA,0BAAP,SAAiChjB,GAAAA,EAgB1BgjB,EAAAA,UAAAA,sBAAP,SAA6BhjB,GAE3BrO,KAAK4N,iBAAiB,oBACpBS,GAAAA,GAAAA,EAWGgjB,EAAAA,UAAAA,yBAAP,SAAgChjB,GAAAA,EASxBgjB,EAAAA,UAAAA,oBAAR,sBACoC,WAA9BrxB,KAAKyU,SAASnJ,YACZzD,OAAO4uB,iBACTz2B,KAAKo2B,eAAiB,IAAIK,gBAAe,WACvC5vB,EAAKqT,OAAOrT,EAAKL,OAAOsI,YAAajI,EAAKL,OAAOmiB,aAAAA,IAEnD3oB,KAAKo2B,eAAeM,QAAQ12B,KAAKwG,SAEI,UAA9BxG,KAAKyU,SAASnJ,cACnBzD,OAAO4uB,iBACTz2B,KAAKo2B,eAAiB,IAAIK,gBAAe,WACvC,OAAA5vB,EAAKqsB,qBAAAA,IAEPlzB,KAAKo2B,eAAeM,QAAQ12B,KAAKs0B,eAEnCzsB,OAAO+F,iBAAiB,SAAU5N,KAAKgzB,iBAAAA,EAInC3B,EAAAA,UAAAA,oBAAR,WACE,IAAMsF,EAAS,EAAM32B,KAAKwG,OAAOsI,YAAe9O,KAAKwG,OAAOmiB,aACtDvO,EACJpa,KAAKs0B,aAAaxlB,YAAc6nB,EAAQ32B,KAAKs0B,aAAa3L,aACtD3oB,KAAKs0B,aAAa3L,aAAegO,EACjC32B,KAAKs0B,aAAaxlB,YAClBwL,EACJF,EAAWpa,KAAKs0B,aAAaxlB,YACzB9O,KAAKs0B,aAAa3L,aAClB3oB,KAAKs0B,aAAaxlB,YAAc6nB,EACtC32B,KAAKka,OAAOE,EAAUE,EAAAA,EAGhB+W,EAAAA,UAAAA,gBAAR,WACErxB,KAAK42B,aAAe/uB,OAAOgvB,WAAW,EAIhCxF,EAAAA,UAAAA,OAAR,SAAejX,EAAkBE,GAC/Bta,KAAK82B,aAAAA,EAEL,IAAMjhB,EAASuE,EAAWpa,KAAK+2B,WACzBjhB,EAASwE,EAAYta,KAAKg3B,YAEhCh3B,KAAK+2B,WAAanoB,KAAKuB,MAAMiK,GAC7Bpa,KAAKg3B,YAAcpoB,KAAKuB,MAAMmK,GAE5Bta,KAAKwG,kBAAkBqvB,kBACvB71B,KAAKi3B,yBAAyBpB,mBAE9B71B,KAAKi3B,cAActuB,IAAM3I,KAAKwG,OAAOmC,KAEvC3I,KAAKi3B,cAAcjzB,MAAQhE,KAAK+2B,WAChC/2B,KAAKi3B,cAAchzB,OAASjE,KAAKg3B,YACjCh3B,KAAKi3B,cAAczsB,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KACzC/2B,KAAKi3B,cAAczsB,MAAMvG,OAAYjE,KAAKg3B,YAAAA,KAE1Ch3B,KAAKyG,YAAY1C,aAAa,QAAS/D,KAAK+2B,WAAW5yB,YACvDnE,KAAKyG,YAAY1C,aAAa,SAAU/D,KAAKg3B,YAAY7yB,YACzDnE,KAAKyG,YAAY1C,aACf,UACA,OAAS/D,KAAK+2B,WAAW5yB,WAAa,IAAMnE,KAAKg3B,YAAY7yB,YAG/DnE,KAAKk3B,kBAAkB1sB,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KAC7C/2B,KAAKk3B,kBAAkB1sB,MAAMvG,OAAYjE,KAAKg3B,YAAAA,KAE9Ch3B,KAAKwU,iBAAiBhK,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KAC5C/2B,KAAKwU,iBAAiBhK,MAAMvG,OAAYjE,KAAKg3B,YAAAA,KAEX,UAA9Bh3B,KAAKyU,SAASnJ,YAChBtL,KAAKm2B,SAAS3rB,MAAMxG,MAAWhE,KAAK+2B,WAAW5yB,WAAAA,MAE/CnE,KAAK0xB,aACL1xB,KAAKm3B,4BAAAA,IAGHn3B,KAAKo3B,SACPp3B,KAAKo3B,QAAQzrB,eAGf3L,KAAKq3B,eAELr3B,KAAKs3B,aAAazhB,EAAQC,GAE1B9V,KAAK82B,aAAAA,CAAc,EAGbzF,EAAAA,UAAAA,aAAR,SAAqBxb,EAAgBC,GAArC,IACMyhB,EAAAA,EAAAA,KACEv3B,KAAKg0B,gBAAkBh0B,KAAKg0B,0BAA0BvR,EAK1DziB,KAAKg0B,eAAerX,MAAM9G,EAAQC,IAHlCyhB,EAAyBv3B,KAAKg0B,eAC9Bh0B,KAAK8L,oBAIP9L,KAAKk1B,QAAQ7nB,SAAQ,SAAC5H,GAChBA,IAAWoB,EAAKmtB,gBAClBvuB,EAAOkX,MAAM9G,EAAQC,EAAAA,SAAAA,IAGrByhB,GACFv3B,KAAK8L,iBAAiByrB,EAAAA,EAIlBlG,EAAAA,UAAAA,iBAAR,WACErxB,KAAK+2B,WAAanoB,KAAKuB,MAAMnQ,KAAKwG,OAAOsI,aACzC9O,KAAKg3B,YAAcpoB,KAAKuB,MAAMnQ,KAAKwG,OAAOmiB,cAExC3oB,KAAKwG,kBAAkBqvB,kBACvB71B,KAAKi3B,yBAAyBpB,mBAE9B71B,KAAKi3B,cAActuB,IAAM3I,KAAKwG,OAAOmC,KAEvC3I,KAAKi3B,cAAcjzB,MAAQhE,KAAK+2B,WAChC/2B,KAAKi3B,cAAchzB,OAASjE,KAAKg3B,YACjCh3B,KAAKi3B,cAAczsB,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KACzC/2B,KAAKi3B,cAAczsB,MAAMvG,OAAYjE,KAAKg3B,YAAAA,IAAAA,EAGpC3F,EAAAA,UAAAA,WAAR,WACE,IAAMmG,EAAax3B,KAAKi3B,cAAcQ,wBAChCC,EAAW13B,KAAKs0B,aAAamD,wBACnCz3B,KAAK+X,KAAOyf,EAAWzf,KAAO2f,EAAS3f,KACvC/X,KAAKiP,IAAMuoB,EAAWvoB,IAAMyoB,EAASzoB,GAAG,EAGlCoiB,EAAAA,UAAAA,iBAAR,WACErxB,KAAKk3B,kBAAoB3zB,SAASqD,cAAc,OAChD5G,KAAKk3B,kBAAkB1sB,MAAMmtB,YAAY,eAAgB,cAEzD33B,KAAKyG,YAAclD,SAASC,gBAC1B,6BACA,OAEFxD,KAAKyG,YAAY1C,aAAa,QAAS,8BACvC/D,KAAKyG,YAAY1C,aAAa,QAAS/D,KAAK+2B,WAAW5yB,YACvDnE,KAAKyG,YAAY1C,aAAa,SAAU/D,KAAKg3B,YAAY7yB,YACzDnE,KAAKyG,YAAY1C,aACf,UACA,OAAS/D,KAAK+2B,WAAW5yB,WAAa,IAAMnE,KAAKg3B,YAAY7yB,YAE/DnE,KAAKyG,YAAY+D,MAAMsG,cAAgB,OAEvC9Q,KAAKk3B,kBAAkB1sB,MAAMya,SAAW,WACxCjlB,KAAKk3B,kBAAkB1sB,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KAC7C/2B,KAAKk3B,kBAAkB1sB,MAAMvG,OAAYjE,KAAKg3B,YAAAA,KAC9Ch3B,KAAKk3B,kBAAkB1sB,MAAMgmB,gBAAkB,WAC/CxwB,KAAKm3B,sBAELn3B,KAAKk3B,kBAAkBxxB,YAAY1F,KAAKyG,aAExCzG,KAAKs0B,aAAa5uB,YAAY1F,KAAKk3B,kBAAkB,EAUhD7F,EAAAA,UAAAA,QAAP,8BAAeztB,EAAAA,UAAAA,OAAAA,IAAAg0B,EAAAA,GAAAA,UAAAA,GACb53B,KAAK63B,KAAOv0B,EAAUw0B,aACtB93B,KAAK0zB,kBAEL7vB,EAAA7D,KAAK63B,MAAKE,OAAAA,MAAAA,EAAUH,EAAAA,EAGdvG,EAAAA,UAAAA,eAAR,WACMrxB,KAAK63B,MACP73B,KAAKyG,YAAYiP,aAAa1V,KAAK63B,KAAM73B,KAAKyG,YAAY2oB,WAAW,EAIjEiC,EAAAA,UAAAA,YAAR,WACErxB,KAAKwU,iBAAmBjR,SAASqD,cAAc,OAC/C5G,KAAKwU,iBAAiBhK,MAAMya,SAAW,WACvCjlB,KAAKwU,iBAAiBhK,MAAMuN,KAAO,MACnC/X,KAAKwU,iBAAiBhK,MAAMyE,IAAM,MAClCjP,KAAKwU,iBAAiBhK,MAAMxG,MAAWhE,KAAK+2B,WAAAA,KAC5C/2B,KAAKwU,iBAAiBhK,MAAMvG,OAAYjE,KAAKg3B,YAAAA,KAC7Ch3B,KAAKwU,iBAAiBhK,MAAM4C,QAAU,OACtCpN,KAAKk3B,kBAAkBxxB,YAAY1F,KAAKwU,iBAAiB,EAGnD6c,EAAAA,UAAAA,oBAAR,WACErxB,KAAKk3B,kBAAkB1sB,MAAMyE,IAAMjP,KAAKiP,IAAMjP,KAAKg4B,UAAY,KAC/Dh4B,KAAKk3B,kBAAkB1sB,MAAMuN,KAAO/X,KAAK+X,KAAO/X,KAAKg4B,UAAY,MAG3D3G,EAAAA,UAAAA,aAAR,WACErxB,KAAKyG,YAAYmH,iBAAiB,cAAe5N,KAAK+xB,eAGtD/xB,KAAKyG,YAAYmH,iBAAiB,sBAAa8X,GAAM,OAAAA,EAAGiB,gBAAAA,IAExD3mB,KAAKyG,YAAYmH,iBAAiB,WAAY5N,KAAKgyB,YACnDhyB,KAAKi4B,oBAAoB,EAGnB5G,EAAAA,UAAAA,mBAAR,WACExpB,OAAO+F,iBAAiB,cAAe5N,KAAKiyB,eAC5CpqB,OAAO+F,iBAAiB,YAAa5N,KAAKkyB,aAC1CrqB,OAAO+F,iBAAiB,gBAAiB5N,KAAKmyB,cAC9CtqB,OAAO+F,iBAAiB,aAAc5N,KAAKmyB,cAC3CtqB,OAAO+F,iBAAiB,eAAgB5N,KAAKkyB,aAC7CrqB,OAAO+F,iBAAiB,SAAU5N,KAAK8yB,gBACvCjrB,OAAO+F,iBAAiB,QAAS5N,KAAKoyB,QAAQ,EAGxCf,EAAAA,UAAAA,aAAR,WACErxB,KAAKyG,YAAY6vB,oBAAoB,cAAet2B,KAAK+xB,eACzD/xB,KAAKyG,YAAY6vB,oBAAoB,WAAYt2B,KAAKgyB,YACtDhyB,KAAKk4B,oBAAoB,EAGnB7G,EAAAA,UAAAA,mBAAR,WACExpB,OAAOyuB,oBAAoB,cAAet2B,KAAKiyB,eAC/CpqB,OAAOyuB,oBAAoB,YAAat2B,KAAKkyB,aAC7CrqB,OAAOyuB,oBAAoB,gBAAiBt2B,KAAKmyB,cACjDtqB,OAAOyuB,oBAAoB,aAAct2B,KAAKmyB,cAC9CtqB,OAAOyuB,oBAAoB,eAAgBt2B,KAAKkyB,aAChDrqB,OAAOyuB,oBAAoB,SAAUt2B,KAAK8yB,gBAC1CjrB,OAAOyuB,oBAAoB,QAASt2B,KAAKoyB,QAAQ,EAW3Cf,EAAAA,UAAAA,QAAR,WACErxB,KAAKm4B,OAAS50B,SAASqD,cAAc,OACrC5G,KAAKm4B,OAAO3tB,MAAM4C,QAAU,eAC5BpN,KAAKm4B,OAAO3tB,MAAMiI,OAAS,MAC3BzS,KAAKm4B,OAAO3tB,MAAMoJ,QAAU,MAC5B5T,KAAKm4B,OAAO3tB,MAAMsF,KAAO,UAEzB,IAAMsoB,EAAO70B,SAASqD,cAAc,KACpCwxB,EAAKC,KAAO,wBACZD,EAAK5xB,OAAS,SACd4xB,EAAKhxB,UAAAA,87CACLgxB,EAAK1qB,MAAQ,uBAEb0qB,EAAK5tB,MAAM4C,QAAU,OACrBgrB,EAAK5tB,MAAMyT,WAAa,SACxBma,EAAK5tB,MAAM8tB,aAAe,SAC1BF,EAAK5tB,MAAMoJ,QAAU,MACrBwkB,EAAK5tB,MAAMxG,MAAQ,OACnBo0B,EAAK5tB,MAAMvG,OAAS,OAEpBjE,KAAKm4B,OAAOzyB,YAAY0yB,GAExBp4B,KAAKs0B,aAAa5uB,YAAY1F,KAAKm4B,QAEnCn4B,KAAKm4B,OAAO3tB,MAAMya,SAAW,WAC7BjlB,KAAKm4B,OAAO3tB,MAAMsG,cAAgB,MAClC9Q,KAAKq3B,cAAc,EAGbhG,EAAAA,UAAAA,aAAR,WACMrxB,KAAKm4B,SACmC,UAAtCn4B,KAAKwL,gBAAgB1B,aACvB9J,KAAKm4B,OAAO3tB,MAAMuN,KAAU/X,KAAKk3B,kBAAkB5nB,WAAa,QAEhEtP,KAAKm4B,OAAO3tB,MAAMuN,KAChB/X,KAAKk3B,kBAAkB5nB,WACvBtP,KAAKk3B,kBAAkB7nB,YACvBrP,KAAKm4B,OAAOrpB,YACZ,QAGJ9O,KAAKm4B,OAAO3tB,MAAMyE,IAChBjP,KAAKk3B,kBAAkBhoB,UACvBlP,KAAKk3B,kBAAkB/nB,aACvBnP,KAAKm4B,OAAOxP,aACZ,UAKE0I,EAAAA,UAAAA,iBAAR,WAEErxB,KAAKu4B,aAAe1wB,OAAO2wB,QAC3Bx4B,KAAKy4B,aAAe5wB,OAAO6wB,QAC3B14B,KAAK24B,kBAAoBp1B,SAASjC,KAAKkJ,MAAM8I,SAE7CzL,OAAO+wB,OAAO,CAAE3pB,IAAK,EAAG8I,KAAM,IAC9BxU,SAASjC,KAAKkJ,MAAM8I,SAAW,UAGzB+d,EAAAA,UAAAA,gBAAR,WACE9tB,SAASjC,KAAKkJ,MAAM8I,SAAWtT,KAAK24B,kBACpC9wB,OAAO+wB,OAAO,CAAE3pB,IAAKjP,KAAKy4B,aAAc1gB,KAAM/X,KAAKu4B,cAAAA,EAG7ClH,EAAAA,UAAAA,OAAR,iBAeE,OAdkC,UAA9BrxB,KAAKyU,SAASnJ,aAChBtL,KAAKqyB,mBAGPryB,KAAKm2B,SAAW5yB,SAASqD,cAAc,OAEvC5G,KAAKm2B,SAAS3rB,MAAM0B,WAAalM,KAAK64B,kBAClC,SACA,UACJ74B,KAAKm2B,SAAShqB,UAAenM,KAAKyL,OAAOqtB,oBAAAA,IAAuB94B,KAAKyL,OAAO1B,gBAE5E/J,KAAKm2B,SAAS3rB,MAAMob,SAAW,OAC/B5lB,KAAKm2B,SAAS3rB,MAAMuuB,WAAa,OAEzB/4B,KAAKyU,SAASnJ,aACpB,IAAK,SACHtL,KAAKm2B,SAAS3rB,MAAMya,SAAW,WAC/B,IAAM+T,OAAAA,IAAWh5B,KAAKyU,SAASwkB,YAC7Bj5B,KAAKwG,OAAO0I,UAAYlP,KAAKyU,SAASwkB,YACpCj5B,KAAKwG,OAAO0I,UAAYlP,KAAKyL,OAAOgJ,SAASpL,cACzCrJ,KAAKwG,OAAO0I,UAAYlP,KAAKyL,OAAOgJ,SAASpL,cAC7C,EACF6vB,EAAYl5B,KAAKwG,OAAO8I,YAAAA,QAAAA,EAActP,KAAKyU,SAAS0kB,oBAAAA,IAAAA,EAAAA,EAAgB,GAC1En5B,KAAKm2B,SAAS3rB,MAAMyE,IAAS+pB,EAAAA,KAC7Bh5B,KAAKm2B,SAAS3rB,MAAMuN,KAAUmhB,EAAAA,KAC9Bl5B,KAAKm2B,SAAS3rB,MAAMxG,MAAWhE,KAAKwG,OAAO6I,YAAYlL,WAAAA,KAEvDnE,KAAKm2B,SAAS3rB,MAAM4uB,YAAAA,IAClBp5B,KAAKwL,gBAAgB4tB,OACjBp5B,KAAKwL,gBAAgB4tB,OACrB,IAGN,MAEF,IAAK,QACHp5B,KAAKm2B,SAAS3rB,MAAMya,SAAW,WAC/BjlB,KAAKm2B,SAAS3rB,MAAMyE,IAAM,MAC1BjP,KAAKm2B,SAAS3rB,MAAMuN,KAAO,MAC3B/X,KAAKm2B,SAAS3rB,MAAMxG,MAAQ,QAC5BhE,KAAKm2B,SAAS3rB,MAAMvG,OAAY4D,OAAOgvB,YAAAA,KACvC72B,KAAKm2B,SAAS3rB,MAAM4J,gBAAkB,sBACtCpU,KAAKm2B,SAAS3rB,MAAM4uB,YAAAA,IAClBp5B,KAAKwL,gBAAgB4tB,OACjBp5B,KAAKwL,gBAAgB4tB,OACrB,OACNp5B,KAAKm2B,SAAS3rB,MAAM4C,QAAU,OAIlCpN,KAAKuxB,WAAW7rB,YAAY1F,KAAKm2B,UAEjCn2B,KAAKq5B,MAAQ91B,SAASqD,cAAc,OACpC5G,KAAKq5B,MAAM7uB,MAAM4C,QAAU,OAC3BpN,KAAKq5B,MAAM7uB,MAAM8uB,cAAgB,SACjCt5B,KAAKq5B,MAAM7uB,MAAMsC,SAAW,IAC5B9M,KAAKq5B,MAAM7uB,MAAMiI,OACe,UAA9BzS,KAAKyU,SAASnJ,YACPtL,KAAKyU,SAAS8kB,YAAAA,KACjB,MAC4B,UAA9Bv5B,KAAKyU,SAASnJ,cAChBtL,KAAKq5B,MAAM7uB,MAAMyU,SAAW,gBAA4C,EAA5Bjf,KAAKyU,SAAS8kB,YAAAA,MAE5Dv5B,KAAKq5B,MAAM7uB,MAAMgU,OAAS,MAG1Bxe,KAAKm2B,SAASzwB,YAAY1F,KAAKq5B,OAE/Br5B,KAAKo3B,QAAU,IAAIrrB,EACjB/L,KAAKq5B,MACLr5B,KAAKyU,SAASnJ,YACdtL,KAAK2zB,sBACL3zB,KAAKwL,gBACLxL,KAAKyL,QAEPzL,KAAKo3B,QAAQoC,uBAAuBx5B,KAAK2xB,sBACzC3xB,KAAKo3B,QAAQqC,KACXz5B,KAAK64B,mBAAqB74B,KAAKwL,gBAAgBkuB,YAC3C,SACA,WAGN15B,KAAKu0B,WAAahxB,SAASqD,cAAc,OACzC5G,KAAKu0B,WAAW/pB,MAAM4C,QAAU,OAChCpN,KAAKu0B,WAAW/pB,MAAM8uB,cAAgB,MACtCt5B,KAAKu0B,WAAW/pB,MAAMsC,SAAW,IACjC9M,KAAKu0B,WAAW/pB,MAAMmvB,WAAa,IACD,UAA9B35B,KAAKyU,SAASnJ,cAChBtL,KAAKu0B,WAAW/pB,MAAM4J,gBAAkBpU,KAAKwL,gBAAgBvC,sBAC7DjJ,KAAKu0B,WAAW/pB,MAAMuc,UACpB/mB,KAAK42B,aACuB,EAA5B52B,KAAKyU,SAAS8kB,YACuB,IAArCv5B,KAAKwL,gBAAgBnC,cAAAA,KAIvBrJ,KAAKu0B,WAAW/pB,MAAMyU,SAAW,gBACH,EAA5Bjf,KAAKyU,SAAS8kB,YAAAA,OAGlBv5B,KAAKu0B,WAAW/pB,MAAM8I,SAAW,OACjCtT,KAAKq5B,MAAM3zB,YAAY1F,KAAKu0B,YAE5Bv0B,KAAKs0B,aAAe/wB,SAASqD,cAAc,OAC3C5G,KAAKs0B,aAAa9pB,MAAMsC,SAAW,IACnC9M,KAAKs0B,aAAa9pB,MAAMmvB,WAAa,IACrC35B,KAAKs0B,aAAa9pB,MAAMya,SAAW,WACnCjlB,KAAKs0B,aAAa9pB,MAAM8I,SAAW,SACnCtT,KAAKs0B,aAAa9pB,MAAM4C,QAAU,OACA,UAA9BpN,KAAKyU,SAASnJ,cAChBtL,KAAKs0B,aAAa9pB,MAAMyT,WAAa,SACrCje,KAAKs0B,aAAa9pB,MAAM0T,eAAiB,UAE3Cle,KAAKs0B,aAAa9pB,MAAMsG,cAAgB,OACxC9Q,KAAKs0B,aAAa9pB,MAAMgmB,gBAAkB,WAC1CxwB,KAAKs0B,aAAa9pB,MAAMkN,UAAY,SAAS1X,KAAKg4B,UAAAA,IAClDh4B,KAAKu0B,WAAW7uB,YAAY1F,KAAKs0B,cAEjCt0B,KAAKi3B,cACHj3B,KAAKwG,kBAAkBqvB,iBACnBtyB,SAASqD,cAAc,OACvBrD,SAASqD,cAAc,UACK,WAA9B5G,KAAKyU,SAASnJ,kBAAAA,IACbtL,KAAKyU,SAASwkB,aACdj5B,KAAKwG,OAAO0I,UAAYlP,KAAKyL,OAAOgJ,SAASpL,gBAChDrJ,KAAKi3B,cAAczsB,MAAMovB,UACvB55B,KAAKwG,OAAO0I,UAAYlP,KAAKyL,OAAOgJ,SAASpL,cAAAA,MAGjDrJ,KAAKs0B,aAAa5uB,YAAY1F,KAAKi3B,eAEnCj3B,KAAK65B,QAAU,IAAI7oB,EACjBhR,KAAKq5B,MACLr5B,KAAKyU,SAASnJ,YACdtL,KAAKwL,gBACLxL,KAAKyL,QAEPzL,KAAK65B,QAAQJ,KACXz5B,KAAK64B,mBAAqB74B,KAAKwL,gBAAgBsuB,YAC3C,SACA,YAIAzI,EAAAA,UAAAA,QAAR,WACoC,UAA9BrxB,KAAKyU,SAASnJ,aAChBtL,KAAKsyB,kBAGPtyB,KAAKuxB,WAAWpmB,YAAYnL,KAAKm2B,UACjCn2B,KAAKm2B,SAAS4D,SACd/5B,KAAKm2B,SAAW,IAAI,EAGd9E,EAAAA,UAAAA,aAAR,SAAqB5rB,GACnBzF,KAAKyG,YAAY0E,YAAY1F,EAAOkK,WAChC3P,KAAKk1B,QAAQ3mB,QAAQ9I,IAAW,GAClCzF,KAAKk1B,QAAQ1mB,OAAOxO,KAAKk1B,QAAQ3mB,QAAQ9I,GAAS,GAEpDA,EAAOu0B,SAAS,EAGX3I,EAAAA,UAAAA,mBAAP,WACErxB,KAAKi6B,KAAO,SACZj6B,KAAKozB,uBAAAA,IACDpzB,KAAKg0B,iBAC2B,QAA9Bh0B,KAAKg0B,eAAere,MACtB3V,KAAKg0B,eAAejb,UAEpB/Y,KAAKizB,aAAajzB,KAAKg0B,gBACvBh0B,KAAK8L,mBACL9L,KAAKyG,YAAY+D,MAAM4K,OAAS,WAElCpV,KAAKk6B,cAAAA,EAID7I,EAAAA,UAAAA,qBAAR,SACE8I,EACAv5B,GAEA,GAAmB,WAAfu5B,QAAAA,IAA2Bv5B,EAC7BZ,KAAK4xB,gBAAmChxB,QACnC,GAAmB,WAAfu5B,EACT,OAAQv5B,GACN,IAAK,SACHZ,KAAKwzB,qBAGLxzB,KAAKwzB,qBACL,MAEF,IAAK,SACHxzB,KAAK+yB,uBACL,MAEF,IAAK,QACH/yB,KAAKo6B,QACL,MAEF,IAAK,OACHp6B,KAAKq6B,OACL,MAEF,IAAK,OACHr6B,KAAKs6B,OACL,MAEF,IAAK,OACHt6B,KAAKqzB,WACL,MAEF,IAAK,WACHrzB,KAAKg4B,UAAY,EACjB,MAEF,IAAK,iBACCh4B,KAAKu6B,WACPv6B,KAAKwzB,qBACLxzB,KAAKg4B,UAAY,EACjBh4B,KAAKmzB,mBAELnzB,KAAKwzB,qBAEP,MAEF,IAAK,QACHxzB,KAAKuyB,QACL,MAEF,IAAK,SACHvyB,KAAKwzB,qBACLxzB,KAAKw6B,sBAAAA,EAUNnJ,EAAAA,UAAAA,qBAAP,sBACE,YAAIrxB,KAAKg0B,eAA8B,CACrC,IAAIyG,GAAAA,EAUJ,GARAz6B,KAAKo1B,eAAmC,mBAAE/nB,SAAQ,SAACgB,GACjD,IAAMqX,EAAK,IAAIgV,EAAY7zB,EAAMA,EAAKmtB,gBAAe,GACrD3lB,EAASqX,GACLA,EAAGwQ,mBACLuE,GAAAA,EAAS,KAIRA,EAAQ,CACX,IAAME,EAAS36B,KAAKg0B,eACpBh0B,KAAKg0B,eAAegG,UACpBh6B,KAAKyG,YAAY0E,YAAYnL,KAAKg0B,eAAerkB,WACjD3P,KAAKk1B,QAAQ1mB,OAAOxO,KAAKk1B,QAAQ3mB,QAAQvO,KAAKg0B,gBAAiB,GAC/Dh0B,KAAK8L,mBACL9L,KAAKk6B,cACLl6B,KAAKo1B,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAM8zB,GAAAA,GAAAA,CAAAA,CAAAA,EAWhCtJ,EAAAA,UAAAA,MAAP,sBACMuJ,GAAAA,EACJ,GAAI56B,KAAKk1B,QAAQxyB,OAAS,IACxB1C,KAAKo1B,eAAmC,mBAAE/nB,SAAQ,SAACgB,GACjD,IAAMqX,EAAK,IAAIgV,EAAY7zB,OAAAA,GAAMuE,GACjCiD,EAASqX,GACLA,EAAGwQ,mBACL0E,GAAAA,EAAS,KAGRA,GAAQ,CACX56B,KAAK8L,mBACL,mBAAShJ,GACP,IAAM2C,EAASskB,EAAKmL,QAAQpyB,GAC5BinB,EAAKje,iBAAiBie,EAAKmL,QAAQpyB,IACnCinB,EAAKiK,eAAegG,UACpBjQ,EAAKtjB,YAAY0E,YAAY4e,EAAKiK,eAAerkB,WACjDoa,EAAKmL,QAAQ1mB,OAAOub,EAAKmL,QAAQ3mB,QAAQwb,EAAKiK,gBAAiB,GAC/DjK,EAAKqL,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMpB,GAAAA,GAAAA,EAAAA,EAAAA,KAP1B3C,EAAI9C,KAAKk1B,QAAQxyB,OAAS,EAAGI,GAAK,EAAGA,IAAAA,EAArCA,GAUT9C,KAAKk6B,aAAa,CAAbA,EAMX56B,OAAAA,eAAY+xB,EAAAA,UAAAA,kBAAAA,CAAAA,IAAZ,WACE,YAAO,IAAArxB,KAAKu6B,SAAS,EAATA,YAAAA,EAAAA,cAAAA,IAGNlJ,EAAAA,UAAAA,gBAAR,0BACMrxB,KAAKg0B,iBACPh0B,KAAKwU,iBAAiBpN,UAAY,GAClCpH,KAAKu6B,UAAYh3B,SAASqD,cAAc,YACxC5G,KAAKu6B,UAAUpuB,UAAYnM,KAAKwL,gBAAgBqvB,wBAChD76B,KAAKu6B,UAAU/vB,MAAMsG,cAAgB,OACrC9Q,KAAKu6B,UAAU/vB,MAAMswB,UAAY,UACjC96B,KAAKu6B,UAAU/vB,MAAMxG,MAAQ,OAC7BhE,KAAKu6B,UAAU/vB,MAAMiI,OACnBzS,KAAKwL,gBAAgBnC,cAAgB,OAEvCrJ,KAAKu6B,UAAU35B,MAAAA,QAAAA,EAAQZ,KAAKg0B,eAAepe,aAAAA,IAAAA,EAAAA,EAAS,GACpD5V,KAAKwU,iBAAiB9O,YAAY1F,KAAKu6B,WAAAA,EAGnClJ,EAAAA,UAAAA,gBAAR,WACMrxB,KAAK+6B,uBAAAA,IACH/6B,KAAKg0B,iBACPh0B,KAAKg0B,eAAepe,MACc,KAAhC5V,KAAKu6B,UAAU35B,MAAM4O,OAAgBxP,KAAKu6B,UAAU35B,WAAAA,GAExDZ,KAAKwU,iBAAiBrJ,YAAYnL,KAAKu6B,WACvCv6B,KAAKu6B,eAAAA,EAAYnvB,EAIbimB,EAAAA,UAAAA,iBAAR,WACMrxB,KAAKk1B,QAAQxyB,OAAS,EACxB1C,KAAK8L,iBAAiB9L,KAAKk1B,QAAQl1B,KAAKk1B,QAAQxyB,OAAS,IAEzD1C,KAAK8L,kBAAkB,EAInBulB,EAAAA,UAAAA,YAAR,sBACE,YACErxB,KAAKg0B,gBACyB,SAA9Bh0B,KAAKg0B,eAAere,MACpB,CACA,IAAMM,EAAejW,KAAKsV,WACpB0lB,EAAgBh7B,KAAKk0B,gBAAgB+G,kBAC3C,IACED,GACCA,EAAch3B,QAAUiS,EAAajS,OACpCg3B,EAAc/2B,SAAWgS,EAAahS,OASnC,CACL,IAAMi3B,EAAcl7B,KAAKk0B,gBAAgBiH,cACzCn7B,KAAKk0B,gBAAgBgG,YAAYjkB,GAC7BilB,EAAcl7B,KAAKk0B,gBAAgBiH,eACrCn7B,KAAKo1B,eAA4B,YAAE/nB,SAAQ,SAACgB,GAC1C,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,GAAAA,MAXjC7G,KAAKk0B,gBAAgBkH,oBAAoBnlB,EAAAA,CAAAA,EAuBxCob,EAAAA,UAAAA,KAAP,WACErxB,KAAKwzB,qBACLxzB,KAAKk6B,cACLl6B,KAAKq7B,UAAU,EAGThK,EAAAA,UAAAA,SAAR,sBACQhD,EAAWruB,KAAKk0B,gBAAgBmG,YAAO,IACzChM,IACFruB,KAAKuc,aAAa8R,GAClBruB,KAAK0zB,iBACL1zB,KAAKs7B,mBACLt7B,KAAKo1B,eAA4B,YAAE/nB,SAAQ,SAACgB,GAC1C,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,IAAAA,EAU5BwqB,EAAAA,UAAAA,KAAP,WACErxB,KAAKwzB,qBACLxzB,KAAKu7B,UAAU,EAGTlK,EAAAA,UAAAA,SAAR,sBACQhD,EAAWruB,KAAKk0B,gBAAgBoG,YAAO,IACzCjM,IACFruB,KAAKuc,aAAa8R,GAClBruB,KAAK0zB,iBACL1zB,KAAKs7B,mBACLt7B,KAAKo1B,eAA4B,YAAE/nB,SAAQ,SAACgB,GAC1C,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,IAAAA,EAW5BwqB,EAAAA,UAAAA,SAAP,WACE,IAAMmK,EAAgBx7B,KAAKy7B,UAAUltB,QAAQvO,KAAKg4B,WAClDh4B,KAAKg4B,UACHwD,EAAgBx7B,KAAKy7B,UAAU/4B,OAAS,EACpC1C,KAAKy7B,UAAUD,EAAgB,GAC/Bx7B,KAAKy7B,UAAU,EAAE,EAIjBpK,EAAAA,UAAAA,MAAR,SAAc9b,GACZvV,KAAKu0B,WAAWmH,SAAS,CACvB3jB,KAAM/X,KAAK27B,aAAa51B,EAAIwP,EAAMxP,EAClCkJ,IAAKjP,KAAK27B,aAAan6B,EAAI+T,EAAM/T,IAEnCxB,KAAK27B,aAAepmB,CAAAA,EAQT8b,EAAAA,UAAAA,oBAAb,oHACiB,SAAMrxB,KAAK47B,UAAAA,KAAAA,EAAAA,OAApB36B,EAAS4C,EAAAA,OACT8R,EAAQ3V,KAAKsV,WAEnBtV,KAAKo1B,eAAuB,OAAE/nB,SAAQ,SAACgB,GACrC,OAAAA,EAAS,IAAIwtB,EAAsBh1B,EAAM5F,EAAQ0U,GAAAA,IAEnD3V,KAAKuyB,OAAM,eASNlB,EAAAA,UAAAA,SAAP,SAAgByK,IAAAA,IACVA,GACF97B,KAAK8L,mBAEP,IAAM7K,EAA0B,CAC9B+C,MAAOhE,KAAK+2B,WACZ9yB,OAAQjE,KAAKg3B,YACb9B,QAAS,IAGX,OADAl1B,KAAKk1B,QAAQ7nB,SAAQ,SAAC5H,GAAW,OAAAxE,EAAOi0B,QAAQvyB,KAAK8C,EAAO6P,WAAAA,IACrDrU,CAAAA,EAiBFowB,EAAAA,UAAAA,aAAP,SAAoB1b,GAApB,WAEE,IADA3V,KAAKk1B,QAAQ1mB,OAAO,GACbxO,KAAKyG,YAAY4d,WACtBrkB,KAAKyG,YAAY0E,YAAYnL,KAAKyG,YAAY4d,WAGhD1O,EAAMuf,QAAQ7nB,SAAQ,SAAC0uB,GACrB,IAAM1rB,EAAaxJ,EAAK8sB,sBAAsBnjB,MAAK,SAChDwrB,GAAU,OAAAA,EAAMvuB,WAAasuB,EAAYtuB,QAAAA,IAE5C,YAAI4C,EAA0B,CAC5B,IAAM5K,EAASoB,EAAKgrB,aAAaxhB,GACjC5K,EAAO8W,aAAawf,GACpBl1B,EAAKquB,QAAQvyB,KAAK8C,EAAAA,CAAAA,IAIpBkQ,EAAM3R,OACN2R,EAAM1R,SACL0R,EAAM3R,QAAUhE,KAAK+2B,YAAcphB,EAAM1R,SAAWjE,KAAKg3B,cAE1Dh3B,KAAKs3B,aACHt3B,KAAK+2B,WAAaphB,EAAM3R,MACxBhE,KAAKg3B,YAAcrhB,EAAM1R,QAG7BjE,KAAKo1B,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,GAAAA,EAIzBwqB,EAAAA,UAAAA,aAAR,SAAqBhhB,GACnB,IAAM3O,EAAI4B,EAAU2T,cAGpB,OAFAjX,KAAKyG,YAAYf,YAAYhE,GAEtB,IAAI2O,EAAW3O,EAAG1B,KAAKwU,iBAAkBxU,KAAKyU,SAAS,EAiBzD4c,EAAAA,UAAAA,gBAAP,SAAuBhhB,GAAvB,IACM2rB,EAAAA,EAAAA,MAGFA,EADwB,iBAAf3rB,EACDrQ,KAAK2zB,sBAAsBnjB,MAAK,SACrCojB,GAAO,OAAAA,EAAGnmB,WAAa4C,CAAAA,IAGlBA,KAIRrQ,KAAK8L,mBACL9L,KAAKk6B,cACLl6B,KAAKg0B,eAAiBh0B,KAAK6xB,aAAamK,GACxCh8B,KAAKg0B,eAAeha,gBAAkBha,KAAK8xB,cAC3C9xB,KAAKg0B,eAAe1f,eAAiBtU,KAAK8U,aAC1C9U,KAAKg0B,eAAeje,mBAAqB/V,KAAK+U,iBAC9C/U,KAAKg0B,eAAehe,eAAiBhW,KAAKuzB,mBAC1CvzB,KAAKyG,YAAY+D,MAAM4K,OAAS,YAChCpV,KAAKo3B,QAAQ6E,sBAAsBD,EAAMvuB,UACzCzN,KAAK65B,QAAQqC,gBAAgBl8B,KAAKg0B,eAAemI,eACjDn8B,KAAKo1B,eAA+B,eAAE/nB,SAAQ,SAACgB,GAC7C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMA,EAAKmtB,gBAAAA,IAAAA,EAKlC3C,EAAAA,UAAAA,cAAR,SAAsB5rB,GAAtB,WACEzF,KAAKi6B,KAAO,SACZj6B,KAAKyG,YAAY+D,MAAM4K,OAAS,UAChCpV,KAAKk1B,QAAQvyB,KAAK8C,GAClBzF,KAAK8L,iBAAiBrG,GAEpBA,aAAkBqiB,GAClB9nB,KAAKyU,SAASgU,6BAEdzoB,KAAK4xB,gBAAgB9J,GAErB9nB,KAAKo3B,QAAQhpB,gBAEfpO,KAAKk6B,cACLl6B,KAAKo1B,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMA,EAAKmtB,gBAAAA,GAAAA,EAIhC3C,EAAAA,UAAAA,aAAR,SAAqB9d,GACfvT,KAAKyU,SAAS2nB,mCAChBp8B,KAAKyU,SAAS8K,aAAehM,EAC7BvT,KAAKyU,SAAS4X,mBAAqB9Y,EAAAA,EAG/B8d,EAAAA,UAAAA,iBAAR,SAAyB9d,GACnBvT,KAAKyU,SAAS2nB,mCAChBp8B,KAAKyU,SAAS4W,iBAAmB9X,EAAAA,EAI7B8d,EAAAA,UAAAA,mBAAR,SAA2B5rB,GAA3B,WACEzF,KAAKo1B,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMpB,GAAAA,GAAAA,EAS5B4rB,EAAAA,UAAAA,iBAAP,SAAwB5rB,GAAxB,WACMzF,KAAKg0B,iBAAmBvuB,QAAAA,IAEtBzF,KAAKg0B,iBACPh0B,KAAKg0B,eAAe9Y,WACpBlb,KAAKo3B,QAAQtrB,mBACb9L,KAAK65B,QAAQqC,gBAAgB,IAExBl8B,KAAK82B,aACR92B,KAAKo1B,eAA+B,eAAE/nB,SAAQ,SAACgB,GAC7C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMA,EAAKmtB,gBAAAA,KAK5Ch0B,KAAKg0B,eAAiBvuB,OAAAA,IAClBzF,KAAKg0B,gBAAiCh0B,KAAKg0B,eAAeqI,aAC1B,QAA9Br8B,KAAKg0B,eAAere,OACxB3V,KAAKg0B,eAAejb,SAEpB/Y,KAAKo3B,QAAQtrB,iBAAiB9L,KAAKg0B,gBACnCh0B,KAAK65B,QAAQqC,gBAAgBl8B,KAAKg0B,eAAemI,eAE5Cn8B,KAAK82B,aACR92B,KAAKo1B,eAA6B,aAAE/nB,SAAQ,SAACgB,GAC3C,OAAAA,EAAS,IAAIqsB,EAAY7zB,EAAMA,EAAKmtB,gBAAAA,IAAAA,EAMpC3C,EAAAA,UAAAA,cAAR,SAAsB3L,GAMpB,GALK1lB,KAAKg1B,YACRh1B,KAAK6mB,QAGP7mB,KAAKs8B,cACoB,IAArBt8B,KAAKs8B,aAAwC,UAAnB5W,EAAG6W,YAC/B,YACEv8B,KAAKg0B,gBAC0B,QAA9Bh0B,KAAKg0B,eAAere,OACW,aAA9B3V,KAAKg0B,eAAere,OAMjB,GAAkB,WAAd3V,KAAKi6B,KAAmB,CACjC,IAAMuC,EAAYx8B,KAAKk1B,QAAQ1kB,MAAK,SAACisB,GAAM,OAAAA,EAAEjmB,WAAWkP,EAAGlf,OAAAA,SAAAA,IACvDg2B,GACFx8B,KAAK8L,iBAAiB0wB,GACtBx8B,KAAK08B,YAAAA,EACL18B,KAAKg0B,eAAe5b,YAClBpY,KAAK6yB,yBAAyBnN,EAAGiX,QAASjX,EAAGkX,SAC7ClX,EAAGlf,UAGLxG,KAAK8L,mBACL9L,KAAK08B,YAAAA,EACL18B,KAAK27B,aAAe,CAAE51B,EAAG2f,EAAGiX,QAASn7B,EAAGkkB,EAAGkX,SAAAA,OAhB7C58B,KAAK08B,YAAAA,EACL18B,KAAKg0B,eAAe5b,YAClBpY,KAAK6yB,yBAAyBnN,EAAGiX,QAASjX,EAAGkX,SAAAA,EAoB7CvL,EAAAA,UAAAA,WAAR,SAAmB3L,GAKjB,GAJK1lB,KAAKg1B,YACRh1B,KAAK6mB,QAGW,WAAd7mB,KAAKi6B,KAAmB,CAC1B,IAAMuC,EAAYx8B,KAAKk1B,QAAQ1kB,MAAK,SAACisB,GAAM,OAAAA,EAAEjmB,WAAWkP,EAAGlf,OAAAA,SAAAA,IACvDg2B,GAA2BA,IAAcx8B,KAAKg0B,gBAChDh0B,KAAK8L,iBAAiB0wB,QAAAA,IAEpBx8B,KAAKg0B,eACPh0B,KAAKg0B,eAAe5M,SAClBpnB,KAAK6yB,yBAAyBnN,EAAGiX,QAASjX,EAAGkX,SAC7ClX,EAAGlf,QAGLxG,KAAK8L,kBAAkB,CAAlBA,EAKHulB,EAAAA,UAAAA,cAAR,SAAsB3L,GACK,IAArB1lB,KAAKs8B,aAAwC,UAAnB5W,EAAG6W,mBAAAA,IAC3Bv8B,KAAKg0B,gBAAgCh0B,KAAK08B,mBAAAA,IAG1C18B,KAAKg0B,gBACyB,SAA9Bh0B,KAAKg0B,eAAere,OAEpB+P,EAAGiB,sBAAAA,IAGD3mB,KAAKg0B,eACPh0B,KAAKg0B,eAAeja,WAClB/Z,KAAK6yB,yBAAyBnN,EAAGiX,QAASjX,EAAGkX,UAEtC58B,KAAKg4B,UAAY,GAC1Bh4B,KAAK68B,MAAM,CAAE92B,EAAG2f,EAAGiX,QAASn7B,EAAGkkB,EAAGkX,UAAAA,EAKlCvL,EAAAA,UAAAA,YAAR,SAAoB3L,GACd1lB,KAAKs8B,YAAc,GACrBt8B,KAAKs8B,cAEkB,IAArBt8B,KAAKs8B,aACHt8B,KAAK08B,iBAAAA,IAAc18B,KAAKg0B,gBAC1Bh0B,KAAKg0B,eAAena,UAClB7Z,KAAK6yB,yBAAyBnN,EAAGiX,QAASjX,EAAGkX,UAInD58B,KAAK08B,YAAAA,EACL18B,KAAKk6B,aAAa,EAGZ7I,EAAAA,UAAAA,aAAR,WACMrxB,KAAKs8B,YAAc,GACrBt8B,KAAKs8B,aAAa,EAIdjL,EAAAA,UAAAA,QAAR,SAAgB3L,QAAAA,IAEZ1lB,KAAKg0B,qBAAAA,IACLh0B,KAAKu6B,WACO,WAAX7U,EAAGtf,KAA+B,cAAXsf,EAAGtf,KAE3BpG,KAAK+yB,sBAAsB,EAOvB1B,EAAAA,UAAAA,yBAAR,SAAiCtrB,EAAWvE,GAC1C,IAAMs7B,EAAa98B,KAAKyG,YAAYgxB,wBAC9B5hB,EAASinB,EAAW94B,MAAQhE,KAAK+2B,WAAa/2B,KAAKg4B,UACnDliB,EAASgnB,EAAW74B,OAASjE,KAAKg3B,YAAch3B,KAAKg4B,UAC3D,MAAO,CACLjyB,GAAIA,EAAI+2B,EAAW/kB,MAAQ/X,KAAKg4B,UAAYniB,EAC5CrU,GAAIA,EAAIs7B,EAAW7tB,KAAOjP,KAAKg4B,UAAYliB,EAAAA,EAIvCub,EAAAA,UAAAA,eAAR,WACErxB,KAAK+8B,YAAY,EAGX1L,EAAAA,UAAAA,WAAR,WAEE,OADArxB,KAAK0xB,aACG1xB,KAAKyU,SAASnJ,aACpB,IAAK,SACH,IAAM0xB,EAAQh9B,KAAKwG,OAAOy2B,iBACpBjE,EACJgE,EAAMt6B,OAAS,GAAKs6B,EAAME,KAAK,IAC9BF,EAAME,KAAK,GAAG17B,EAAIxB,KAAKyL,OAAOgJ,SAASpL,cACpCrJ,KAAKwG,OAAO0I,UAAYlP,KAAKyL,OAAOgJ,SAASpL,cAC7C,EACNrJ,KAAKm2B,SAAS3rB,MAAMyE,IAAS+pB,EAAAA,KAC7Bh5B,KAAKm2B,SAAS3rB,MAAMuN,KAAU/X,KAAKwG,OAAO8I,WAAWnL,WAAAA,KACrD,MAEF,IAAK,QACHnE,KAAKm2B,SAAS3rB,MAAMyE,IAAM,MAC1BjP,KAAKm2B,SAAS3rB,MAAMuN,KAAO,MAC3B/X,KAAKm2B,SAAS3rB,MAAMxG,MAAQ,QAC5BhE,KAAKm2B,SAAS3rB,MAAMvG,OAAYjE,KAAK42B,aAAAA,KACrC52B,KAAKu0B,WAAW/pB,MAAMuc,UACpB/mB,KAAK42B,aACuB,EAA5B52B,KAAKyU,SAAS8kB,YACuB,IAArCv5B,KAAKyL,OAAOgJ,SAASpL,cAAAA,KAI3BrJ,KAAKm3B,sBACLn3B,KAAKq3B,cAAc,EAUdhG,EAAAA,UAAAA,cAAP,SAAqBjrB,GACnBD,EAAUg3B,OAAO/2B,EAAAA,EAYZirB,EAAAA,UAAAA,iBAAP,SACEL,EACAC,GAEAjxB,KAAKo1B,eAAexnB,iBAAiBojB,EAAWC,EAAAA,EAW3CI,EAAAA,UAAAA,oBAAP,SACEL,EACAC,GAEAjxB,KAAKo1B,eAAekB,oBAAoBtF,EAAWC,EAAAA,EAc9CI,EAAAA,UAAAA,YAAP,SAAmB1b,GACjB3V,KAAK64B,mBAAAA,EACL74B,KAAKyU,SAASnJ,YAAc,SACvBtL,KAAKg2B,QACRh2B,KAAKy5B,OAEPz5B,KAAKuc,aAAa5G,GAClB3V,KAAKw6B,sBACLx6B,KAAK64B,mBAAAA,CAAoB,EAS3Bv5B,OAAAA,eAAW+xB,EAAAA,UAAAA,YAAAA,CAAAA,IAAX,WACE,OAAOrxB,KAAKg1B,UAAU,EAAVA,YAAAA,EAAAA,cAAAA,IAYP3D,EAAAA,UAAAA,MAAP,sBACOrxB,KAAKg1B,aACRh1B,KAAKi4B,qBACLj4B,KAAKg1B,YAAAA,OAAa,IACdh1B,KAAKo9B,wBACPp9B,KAAK8L,iBAAiB9L,KAAKo9B,wBAE7Bp9B,KAAKo1B,eAAsB,MAAE/nB,SAAQ,SAACgB,GACpC,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,IAAAA,EAY5BwqB,EAAAA,UAAAA,KAAP,sBACMrxB,KAAKg1B,aACPh1B,KAAKk4B,qBACLl4B,KAAKg1B,YAAAA,EACLh1B,KAAKo9B,uBAAyBp9B,KAAKg0B,eACnCh0B,KAAK8L,mBACL9L,KAAKo1B,eAAqB,KAAE/nB,SAAQ,SAACgB,GACnC,OAAAA,EAAS,IAAIuiB,EAAgB/pB,GAAAA,IAAAA,EAz8CpBwqB,EAAAA,gBAAkB,IDpH2B", "sources": ["webpack://@amcharts/amcharts5/./node_modules/node_modules/tslib/tslib.es6.js", "webpack://@amcharts/amcharts5/./node_modules/src/core/SvgHelper.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/Activator.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/Renderer.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/Style.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/Toolbar.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/Toolbox.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/ToolboxPanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/ColorPickerPanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/MarkerBase.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/RectangularBoxMarkerGrips.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/ResizeGrip.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/TransformMatrix.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/RectangularBoxMarkerBase.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/RectangleMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/LineWidthPanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/LineStylePanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/frame-marker/FrameMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/Settings.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/LinearMarkerBase.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/line-marker/LineMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/FontFamilyPanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/text-marker/TextMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/freehand-marker/FreehandMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/ArrowTypePanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/arrow-marker/ArrowMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/cover-marker/CoverMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/ui/toolbox-panels/OpacityPanel.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/highlight-marker/HighlightMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/callout-marker/CalloutMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/ellipse-marker/EllipseMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/measurement-marker/MeasurementMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/ellipse-frame-marker/EllipseFrameMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/UndoRedoManager.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/curve-marker/CurveMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/markers/caption-frame-marker/CaptionFrameMarker.ts", "webpack://@amcharts/amcharts5/./node_modules/src/core/Events.ts", "webpack://@amcharts/amcharts5/./node_modules/src/MarkerArea.ts"], "sourcesContent": ["/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nexport function __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nexport var __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    }\r\n    return __assign.apply(this, arguments);\r\n}\r\n\r\nexport function __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\r\n\r\nexport function __decorate(decorators, target, key, desc) {\r\n    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\r\n    if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\r\n    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\r\n    return c > 3 && r && Object.defineProperty(target, key, r), r;\r\n}\r\n\r\nexport function __param(paramIndex, decorator) {\r\n    return function (target, key) { decorator(target, key, paramIndex); }\r\n}\r\n\r\nexport function __metadata(metadataKey, metadataValue) {\r\n    if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\r\n}\r\n\r\nexport function __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\nexport function __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\r\n\r\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n});\r\n\r\nexport function __exportStar(m, o) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\r\n}\r\n\r\nexport function __values(o) {\r\n    var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\r\n    if (m) return m.call(o);\r\n    if (o && typeof o.length === \"number\") return {\r\n        next: function () {\r\n            if (o && i >= o.length) o = void 0;\r\n            return { value: o && o[i++], done: !o };\r\n        }\r\n    };\r\n    throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\r\n}\r\n\r\nexport function __read(o, n) {\r\n    var m = typeof Symbol === \"function\" && o[Symbol.iterator];\r\n    if (!m) return o;\r\n    var i = m.call(o), r, ar = [], e;\r\n    try {\r\n        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\r\n    }\r\n    catch (error) { e = { error: error }; }\r\n    finally {\r\n        try {\r\n            if (r && !r.done && (m = i[\"return\"])) m.call(i);\r\n        }\r\n        finally { if (e) throw e.error; }\r\n    }\r\n    return ar;\r\n}\r\n\r\nexport function __spread() {\r\n    for (var ar = [], i = 0; i < arguments.length; i++)\r\n        ar = ar.concat(__read(arguments[i]));\r\n    return ar;\r\n}\r\n\r\nexport function __spreadArrays() {\r\n    for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\r\n    for (var r = Array(s), k = 0, i = 0; i < il; i++)\r\n        for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\r\n            r[k] = a[j];\r\n    return r;\r\n};\r\n\r\nexport function __await(v) {\r\n    return this instanceof __await ? (this.v = v, this) : new __await(v);\r\n}\r\n\r\nexport function __asyncGenerator(thisArg, _arguments, generator) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var g = generator.apply(thisArg, _arguments || []), i, q = [];\r\n    return i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i;\r\n    function verb(n) { if (g[n]) i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; }\r\n    function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\r\n    function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\r\n    function fulfill(value) { resume(\"next\", value); }\r\n    function reject(value) { resume(\"throw\", value); }\r\n    function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\r\n}\r\n\r\nexport function __asyncDelegator(o) {\r\n    var i, p;\r\n    return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\r\n    function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: n === \"return\" } : f ? f(v) : v; } : f; }\r\n}\r\n\r\nexport function __asyncValues(o) {\r\n    if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\r\n    var m = o[Symbol.asyncIterator], i;\r\n    return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\r\n    function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\r\n    function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\r\n}\r\n\r\nexport function __makeTemplateObject(cooked, raw) {\r\n    if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\r\n    return cooked;\r\n};\r\n\r\nvar __setModuleDefault = Object.create ? (function(o, v) {\r\n    Object.defineProperty(o, \"default\", { enumerable: true, value: v });\r\n}) : function(o, v) {\r\n    o[\"default\"] = v;\r\n};\r\n\r\nexport function __importStar(mod) {\r\n    if (mod && mod.__esModule) return mod;\r\n    var result = {};\r\n    if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\r\n    __setModuleDefault(result, mod);\r\n    return result;\r\n}\r\n\r\nexport function __importDefault(mod) {\r\n    return (mod && mod.__esModule) ? mod : { default: mod };\r\n}\r\n\r\nexport function __classPrivateFieldGet(receiver, privateMap) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to get private field on non-instance\");\r\n    }\r\n    return privateMap.get(receiver);\r\n}\r\n\r\nexport function __classPrivateFieldSet(receiver, privateMap, value) {\r\n    if (!privateMap.has(receiver)) {\r\n        throw new TypeError(\"attempted to set private field on non-instance\");\r\n    }\r\n    privateMap.set(receiver, value);\r\n    return value;\r\n}\r\n", "/**\r\n * Utility class to simplify SVG operations.\r\n */\r\nexport class Svg<PERSON>elper {\r\n  /**\r\n   * Creates SVG \"defs\".\r\n   */\r\n  public static createDefs(): SVGDefsElement {\r\n    const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');\r\n\r\n    return defs;\r\n  }\r\n\r\n  /**\r\n   * Sets attributes on an arbitrary SVG element\r\n   * @param el - target SVG element.\r\n   * @param attributes - set of name-value attribute pairs.\r\n   */\r\n  public static setAttributes(\r\n    el: SVGElement,\r\n    attributes: Array<[string, string]>\r\n  ): void {\r\n    for (const [attr, value] of attributes) {\r\n      el.setAttribute(attr, value);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG rectangle with the specified width and height.\r\n   * @param width \r\n   * @param height \r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createRect(\r\n    width: number | string,\r\n    height: number | string,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGRectElement {\r\n    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');\r\n\r\n    rect.setAttribute('width', width.toString());\r\n    rect.setAttribute('height', height.toString());\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(rect, attributes);\r\n    }\r\n\r\n    return rect;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG line with specified end-point coordinates.\r\n   * @param x1 \r\n   * @param y1 \r\n   * @param x2 \r\n   * @param y2 \r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createLine(\r\n    x1: number | string,\r\n    y1: number | string,\r\n    x2: number | string,\r\n    y2: number | string,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGLineElement {\r\n    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');\r\n\r\n    line.setAttribute('x1', x1.toString());\r\n    line.setAttribute('y1', y1.toString());\r\n    line.setAttribute('x2', x2.toString());\r\n    line.setAttribute('y2', y2.toString());\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(line, attributes);\r\n    }\r\n\r\n    return line;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG polygon with specified points.\r\n   * @param points - points as string.\r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createPolygon(\r\n    points: string,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGPolygonElement {\r\n    const polygon = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'polygon'\r\n    );\r\n\r\n    polygon.setAttribute('points', points);\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(polygon, attributes);\r\n    }\r\n\r\n    return polygon;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG circle with the specified radius.\r\n   * @param radius \r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createCircle(\r\n    radius: number,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGCircleElement {\r\n    const circle = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'circle'\r\n    );\r\n\r\n    circle.setAttribute('cx', (radius / 2).toString());\r\n    circle.setAttribute('cy', (radius / 2).toString());\r\n    circle.setAttribute('r', radius.toString());\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(circle, attributes);\r\n    }\r\n\r\n    return circle;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG ellipse with the specified horizontal and vertical radii.\r\n   * @param rx \r\n   * @param ry \r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createEllipse(\r\n    rx: number,\r\n    ry: number,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGEllipseElement {\r\n    const ellipse = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'ellipse'\r\n    );\r\n\r\n    ellipse.setAttribute('cx', (rx / 2).toString());\r\n    ellipse.setAttribute('cy', (ry / 2).toString());\r\n    ellipse.setAttribute('rx', (rx / 2).toString());\r\n    ellipse.setAttribute('ry', (ry / 2).toString());\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(ellipse, attributes);\r\n    }\r\n\r\n    return ellipse;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG group.\r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createGroup(attributes?: Array<[string, string]>): SVGGElement {\r\n    const g = document.createElementNS('http://www.w3.org/2000/svg', 'g');\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(g, attributes);\r\n    }\r\n    return g;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG transform.\r\n   */\r\n  public static createTransform(): SVGTransform {\r\n    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\r\n\r\n    return svg.createSVGTransform();\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG marker.\r\n   * @param id \r\n   * @param orient \r\n   * @param markerWidth \r\n   * @param markerHeight \r\n   * @param refX \r\n   * @param refY \r\n   * @param markerElement \r\n   */\r\n  public static createMarker(\r\n    id: string,\r\n    orient: string,\r\n    markerWidth: number | string,\r\n    markerHeight: number | string,\r\n    refX: number | string,\r\n    refY: number | string,\r\n    markerElement: SVGGraphicsElement\r\n  ): SVGMarkerElement {\r\n    const marker = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'marker'\r\n    );\r\n    SvgHelper.setAttributes(marker, [\r\n      ['id', id],\r\n      ['orient', orient],\r\n      ['markerWidth', markerWidth.toString()],\r\n      ['markerHeight', markerHeight.toString()],\r\n      ['refX', refX.toString()],\r\n      ['refY', refY.toString()],\r\n    ]);\r\n\r\n    marker.appendChild(markerElement);\r\n\r\n    return marker;\r\n  }\r\n\r\n  /**\r\n   * Creaes an SVG text element.\r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createText(\r\n    attributes?: Array<[string, string]>\r\n  ): SVGTextElement {\r\n    const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');\r\n    text.setAttribute('x', '0');\r\n    text.setAttribute('y', '0');\r\n\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(text, attributes);\r\n    }\r\n\r\n    return text;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG TSpan.\r\n   * @param text - inner text.\r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createTSpan(\r\n    text: string,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGTSpanElement {\r\n    const tspan = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'tspan'\r\n    );\r\n    tspan.textContent = text;\r\n\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(tspan, attributes);\r\n    }\r\n\r\n    return tspan;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG image element.\r\n   * @param attributes - additional attributes.\r\n   */\r\n  public static createImage(\r\n    attributes?: Array<[string, string]>\r\n  ): SVGImageElement {\r\n    const image = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'image'\r\n    );\r\n\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(image, attributes);\r\n    }\r\n\r\n    return image;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG point with the specified coordinates.\r\n   * @param x \r\n   * @param y \r\n   */\r\n  public static createPoint(      \r\n    x: number,\r\n    y: number\r\n  ): SVGPoint {\r\n      const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');\r\n      const svgPoint = svg.createSVGPoint();\r\n      svgPoint.x = x;\r\n      svgPoint.y = y;\r\n  \r\n      return svgPoint;\r\n  }\r\n\r\n  /**\r\n   * Creates an SVG path with the specified shape (d).\r\n   * @param d - path shape\r\n   * @param attributes - additional attributes.\r\n   */\r\n   public static createPath(\r\n    d: string,\r\n    attributes?: Array<[string, string]>\r\n  ): SVGPathElement {\r\n    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');\r\n\r\n    path.setAttribute('d', d);\r\n    if (attributes) {\r\n      SvgHelper.setAttributes(path, attributes);\r\n    }\r\n\r\n    return path;\r\n  }\r\n}\r\n", "/**\r\n * Manages commercial marker.js 2 licenses.\r\n */\r\nexport class Activator {\r\n  private static key: string;\r\n\r\n  /**\r\n   * Add a license key\r\n   * @param key license key sent to you after purchase.\r\n   */\r\n  public static addKey(key: string): void {\r\n    Activator.key = key;\r\n  }\r\n\r\n  /**\r\n   * Returns true if the copy of marker.js is commercially licensed.\r\n   */\r\n  public static get isLicensed(): boolean {\r\n    // NOTE:\r\n    // before removing or modifying this please consider supporting marker.js\r\n    // by visiting https://markerjs.com/ for details\r\n    // thank you!\r\n    if (Activator.key) {\r\n      const keyRegex = new RegExp(/^MJS2-[A-Z][0-9]{3}-[A-Z][0-9]{3}-[0-9]{4}$/, 'i');\r\n      return keyRegex.test(Activator.key);\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n", "/**\r\n * Renders the original image and markup to a flat raster image.\r\n */\r\nexport class Renderer {\r\n    /**\r\n     * Whether the image should be rendered at the original (natural) target image size.\r\n     */\r\n    public naturalSize = false; \r\n    /**\r\n     * Rendered image type (`image/png`, `image/jpeg`, etc.).\r\n     */\r\n    public imageType = 'image/png';\r\n    /**\r\n     * For formats that support it, specifies rendering quality.\r\n     * \r\n     * In the case of `image/jpeg` you can specify a value between 0 and 1 (lowest to highest quality).\r\n     *\r\n     * @type {number} - image rendering quality (0..1)\r\n     */\r\n    public imageQuality?: number;\r\n    /**\r\n     * When set to true, only the marker layer without the original image will be rendered.\r\n     */\r\n    public markersOnly = false;\r\n\r\n    /**\r\n     * When set and {@linkcode naturalSize} is `false` sets the width of the rendered image.\r\n     * \r\n     * Both `width` and `height` have to be set for this to take effect.\r\n     */\r\n    public width?: number;\r\n    /**\r\n     * When set and {@linkcode naturalSize} is `false` sets the height of the rendered image.\r\n     * \r\n     * Both `width` and `height` have to be set for this to take effect.\r\n     */\r\n    public height?: number;\r\n\r\n\r\n    /**\r\n     * Initiates rendering of the result image and returns a promise which when resolved\r\n     * contains a data URL for the rendered image.\r\n     * \r\n     * @param target - target (underlying original) image\r\n     * @param markerImage - marker layer\r\n     */\r\n    public rasterize(\r\n        target: HTMLImageElement, \r\n        markerImage: SVGSVGElement,\r\n        targetCanvas?: HTMLCanvasElement \r\n    ): Promise<string> {\r\n        return new Promise<string>((resolve) => {\r\n            const canvas = targetCanvas !== undefined ? targetCanvas : document.createElement(\"canvas\");\r\n\r\n            if (target === null) {\r\n                this.markersOnly = true;\r\n                this.naturalSize = false;\r\n            }\r\n\r\n            const markerImageCopy = document.createElementNS(\r\n            'http://www.w3.org/2000/svg',\r\n            'svg'\r\n            );\r\n            markerImageCopy.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\r\n            markerImageCopy.setAttribute('width', markerImage.width.baseVal.valueAsString);\r\n            markerImageCopy.setAttribute(\r\n              'height',\r\n              markerImage.height.baseVal.valueAsString\r\n            );\r\n            markerImageCopy.setAttribute(\r\n              'viewBox',\r\n              '0 0 ' +\r\n                markerImage.viewBox.baseVal.width.toString() +\r\n                ' ' +\r\n                markerImage.viewBox.baseVal.height.toString()\r\n            );            \r\n            markerImageCopy.innerHTML = markerImage.innerHTML;\r\n\r\n            if (this.naturalSize === true) {\r\n                // scale to full image size\r\n                markerImageCopy.width.baseVal.value = target.naturalWidth;\r\n                markerImageCopy.height.baseVal.value = target.naturalHeight;\r\n            } else if (this.width !== undefined && this.height !== undefined) {\r\n                // scale to specific dimensions\r\n                markerImageCopy.width.baseVal.value = this.width;\r\n                markerImageCopy.height.baseVal.value = this.height;\r\n            }\r\n    \r\n            canvas.width = markerImageCopy.width.baseVal.value;\r\n            canvas.height = markerImageCopy.height.baseVal.value;\r\n    \r\n            const data = markerImageCopy.outerHTML;\r\n\r\n            const ctx = canvas.getContext(\"2d\");\r\n            if (this.markersOnly !== true) { \r\n                ctx.drawImage(target, 0, 0, canvas.width, canvas.height);\r\n            }\r\n    \r\n            const DOMURL = window.URL; // || window.webkitURL || window;\r\n    \r\n            const img = new Image(canvas.width, canvas.height);\r\n            img.setAttribute(\"crossOrigin\", \"anonymous\");\r\n    \r\n            const blob = new Blob([data], { type: \"image/svg+xml\" });\r\n    \r\n            const url = DOMURL.createObjectURL(blob);\r\n    \r\n            img.onload = () => {\r\n                ctx.drawImage(img, 0, 0);\r\n                DOMURL.revokeObjectURL(url);\r\n    \r\n                const result = canvas.toDataURL(this.imageType, this.imageQuality);\r\n                resolve(result);\r\n            };\r\n    \r\n            img.src = url;\r\n        });\r\n    }\r\n}\r\n", "import { IStyleSettings } from './IStyleSettings';\r\n\r\n/**\r\n * @see {@link StyleManager}\r\n * @deprecated use instance level `styles` instead.\r\n */\r\nexport class Style {\r\n  /**\r\n   * @see {@link StyleManager}\r\n   * @deprecated use instance level `styles.styleSheetRoot` instead.\r\n   */\r\n  public static styleSheetRoot: HTMLElement;\r\n}\r\n\r\n/**\r\n * Simple utility CSS-in-JS implementation.\r\n */\r\nexport class StyleManager {\r\n  /**\r\n   * Prefix used for all internally created CSS classes.\r\n   */\r\n  private _classNamePrefixBase = '__markerjs2_';\r\n\r\n  /**\r\n   * Static CSS class name used for the wrapper element.\r\n   */\r\n  public get classNamePrefixBase(): string {\r\n    return this._classNamePrefixBase;\r\n  }\r\n\r\n  private _classNamePrefix: string;\r\n  /**\r\n   * Prefix used for all internally created CSS classes.\r\n   */\r\n  public get classNamePrefix(): string {\r\n    return this._classNamePrefix;\r\n  }\r\n\r\n  private classes: StyleClass[] = [];\r\n  private rules: StyleRule[] = [];\r\n  private styleSheet?: HTMLStyleElement;\r\n\r\n  /**\r\n   * For cases when you need to add the stylesheet to anything\r\n   * other than document.head (default), set this property\r\n   * befor calling `MarkerArea.show()`.\r\n   *\r\n   * Example: here we set the rendering/placement root (targetRoot)\r\n   * to the `shadowRoot` of a web componet and set `styleSheetRoot`\r\n   * to the same value as well.\r\n   *\r\n   * ```javascript\r\n   * const markerArea = new markerjs2.MarkerArea(target);\r\n   * markerArea.targetRoot = this.shadowRoot;\r\n   * markerArea.styles.styleSheetRoot = this.shadowRoot;\r\n   * markerArea.show();\r\n   * ```\r\n   *\r\n   */\r\n  public styleSheetRoot: HTMLElement;\r\n\r\n  /**\r\n   * Returns default UI styles.\r\n   */\r\n  public get defaultSettings(): IStyleSettings {\r\n    return {\r\n      canvasBackgroundColor: '#ffffff',\r\n      toolbarBackgroundColor: '#111111',\r\n      toolbarBackgroundHoverColor: '#333333',\r\n      toolbarColor: '#eeeeee',\r\n      toolbarHeight: 40,\r\n      // toolboxBackgroundColor: '#2a2a2a',\r\n      toolboxColor: '#eeeeee',\r\n      toolboxAccentColor: '#3080c3',\r\n      undoButtonVisible: true,\r\n      redoButtonVisible: false,\r\n      zoomButtonVisible: false,\r\n      zoomOutButtonVisible: false,\r\n      clearButtonVisible: false,\r\n      resultButtonBlockVisible: true,\r\n      logoPosition: 'left',\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Holds current UI styles.\r\n   */\r\n  public settings: IStyleSettings = this.defaultSettings;\r\n\r\n  /**\r\n   * Returns global fade-in animation class name.\r\n   */\r\n  public get fadeInAnimationClassName(): string {\r\n    return `${this.classNamePrefix}fade_in`;\r\n  }\r\n  /**\r\n   * Returns global fade-out animation class name.\r\n   */\r\n  public get fadeOutAnimationClassName(): string {\r\n    return `${this.classNamePrefix}fade_out`;\r\n  }\r\n\r\n  /**\r\n   * Initializes a new style manager.\r\n   * @param instanceNo - instance id.\r\n   */\r\n  constructor(instanceNo: number) {\r\n    this._classNamePrefix = `${this._classNamePrefixBase}_${instanceNo}_`;\r\n  }\r\n\r\n  /**\r\n   * Adds a CSS class declaration.\r\n   * @param styleClass - class to add.\r\n   */\r\n  public addClass(styleClass: StyleClass): StyleClass {\r\n    if (this.styleSheet === undefined) {\r\n      this.addStyleSheet();\r\n    }\r\n    styleClass.name = `${this.classNamePrefix}${styleClass.localName}`;\r\n    this.classes.push(styleClass);\r\n    this.styleSheet.sheet.insertRule(\r\n      `.${styleClass.name} {${styleClass.style}}`,\r\n      this.styleSheet.sheet.cssRules.length\r\n    );\r\n    return styleClass;\r\n  }\r\n\r\n  /**\r\n   * Add arbitrary CSS rule\r\n   * @param styleRule - CSS rule to add.\r\n   */\r\n  public addRule(styleRule: StyleRule): void {\r\n    if (this.styleSheet === undefined) {\r\n      this.addStyleSheet();\r\n    }\r\n    this.rules.push(styleRule);\r\n    this.styleSheet.sheet.insertRule(\r\n      `${styleRule.selector} {${styleRule.style}}`,\r\n      this.styleSheet.sheet.cssRules.length\r\n    );\r\n  }\r\n\r\n  private addStyleSheet() {\r\n    this.styleSheet = document.createElement('style');\r\n    (this.styleSheetRoot ?? document.head).appendChild(this.styleSheet);\r\n\r\n    // add global rules\r\n    this.addRule(\r\n      new StyleRule(`.${this.classNamePrefix} h3`, 'font-family: sans-serif')\r\n    );\r\n\r\n    this.addRule(\r\n      new StyleRule(\r\n        `@keyframes ${this.classNamePrefix}_fade_in_animation_frames`,\r\n        `\r\n        from {\r\n          opacity: 0;\r\n        }\r\n        to {\r\n          opacity: 1;\r\n        }\r\n    `\r\n      )\r\n    );\r\n    this.addRule(\r\n      new StyleRule(\r\n        `@keyframes ${this.classNamePrefix}_fade_out_animation_frames`,\r\n        `\r\n        from {\r\n          opacity: 1;\r\n        }\r\n        to {\r\n          opacity: 0;\r\n        }\r\n    `\r\n      )\r\n    );\r\n\r\n    this.addClass(\r\n      new StyleClass(\r\n        'fade_in',\r\n        `\r\n      animation-duration: 0.3s;\r\n      animation-name: ${this.classNamePrefix}_fade_in_animation_frames;\r\n    `\r\n      )\r\n    );\r\n    this.addClass(\r\n      new StyleClass(\r\n        'fade_out',\r\n        `\r\n      animation-duration: 0.3s;\r\n      animation-name: ${this.classNamePrefix}_fade_out_animation_frames;\r\n    `\r\n      )\r\n    );\r\n  }\r\n\r\n  public removeStyleSheet(): void {\r\n    if (this.styleSheet) {\r\n      (this.styleSheetRoot ?? document.head).removeChild(this.styleSheet);\r\n      this.styleSheet = undefined;\r\n    }\r\n  }\r\n}\r\n\r\n/**\r\n * Represents an arbitrary CSS rule.\r\n */\r\nexport class StyleRule {\r\n  /**\r\n   * CSS selector.\r\n   */\r\n  public selector: string;\r\n  /**\r\n   * Style declaration for the rule.\r\n   */\r\n  public style: string;\r\n  /**\r\n   * Creates an arbitrary CSS rule using the selector and style rules.\r\n   * @param selector - CSS selector\r\n   * @param style - styles to apply\r\n   */\r\n  constructor(selector: string, style: string) {\r\n    this.selector = selector;\r\n    this.style = style;\r\n  }\r\n}\r\n\r\n/**\r\n * Represents a CSS class.\r\n */\r\nexport class StyleClass {\r\n  /**\r\n   * CSS style rules for the class.\r\n   */\r\n  public style: string;\r\n\r\n  /**\r\n   * Class name without the global prefix.\r\n   */\r\n  public localName: string;\r\n\r\n  /**\r\n   * Fully qualified CSS class name.\r\n   */\r\n  public name: string;\r\n\r\n  /**\r\n   * Creates a CSS class declaration based on supplied (local) name and style rules.\r\n   * @param name - local CSS class name (will be prefixed with the marker.js prefix).\r\n   * @param style - style declarations.\r\n   */\r\n  constructor(name: string, style: string) {\r\n    this.localName = name;\r\n    this.style = style;\r\n  }\r\n}\r\n", "import { MarkerBase } from './../core/MarkerBase';\r\nimport { StyleManager, StyleClass, StyleRule } from './../core/Style';\r\n\r\nimport CursorIcon from './toolbar-core-icons/cursor.svg';\r\nimport DeleteIcon from './toolbar-core-icons/delete.svg';\r\nimport ClearIcon from './toolbar-core-icons/clear.svg';\r\nimport CheckIcon from './toolbar-core-icons/check.svg';\r\nimport CloseIcon from './toolbar-core-icons/close.svg';\r\nimport OverflowIcon from './toolbar-core-icons/overflow.svg';\r\nimport UndoIcon from './toolbar-core-icons/undo.svg';\r\nimport RedoIcon from './toolbar-core-icons/redo.svg';\r\nimport NotesIcon from './toolbar-core-icons/notes.svg';\r\nimport ZoomIcon from './toolbar-core-icons/zoom.svg';\r\nimport ZoomOutIcon from './toolbar-core-icons/zoom-out.svg';\r\nimport { IStyleSettings } from '../core/IStyleSettings';\r\nimport { DisplayMode } from '../core/Settings';\r\n\r\n/**\r\n * Toolbar button type:\r\n * - `action` for non-marker buttons like select, delete, etc.\r\n * - `marker` for marker type buttons.\r\n */\r\nexport type ToolbarButtonType = 'action' | 'marker';\r\n\r\n/**\r\n * Click handler type for toolbar button click events.\r\n */\r\nexport type ToolbarButtonClickHandler = (\r\n  buttonType: ToolbarButtonType,\r\n  value?: typeof MarkerBase | string\r\n) => void;\r\n\r\n/**\r\n * Toolbar represents the main toolbar of the marker.js 2 interface.\r\n */\r\nexport class Toolbar {\r\n  private markerItems: typeof MarkerBase[];\r\n\r\n  private buttons: HTMLDivElement[] = [];\r\n  private markerButtons: HTMLDivElement[] = [];\r\n  private overflowButton: HTMLDivElement;\r\n\r\n  private markerjsContainer: HTMLDivElement;\r\n  private displayMode: DisplayMode;\r\n  private uiContainer: HTMLDivElement;\r\n\r\n  private toolbarStyleClass: StyleClass;\r\n  private toolbarStyleColorsClass: StyleClass;\r\n  private toolbarBlockStyleClass: StyleClass;\r\n  private toolbarOverflowBlockStyleClass: StyleClass;\r\n  private toolbarOverflowBlockStyleColorsClass: StyleClass;\r\n  private toolbarButtonStyleClass: StyleClass;\r\n  private toolbarButtonStyleColorsClass: StyleClass;\r\n  private toolbarActiveButtonStyleColorsClass: StyleClass;\r\n\r\n  private markerButtonBlock: HTMLDivElement;\r\n  private markerButtonOverflowBlock: HTMLDivElement;\r\n\r\n  private buttonClickListeners: ToolbarButtonClickHandler[] = [];\r\n\r\n  private uiStyleSettings: IStyleSettings;\r\n\r\n  private currentMarker?: MarkerBase;\r\n\r\n  private styles: StyleManager;\r\n\r\n  /**\r\n   * Creates the main marker.js toolbar.\r\n   * @param markerjsContainer - container for the toolbar in the marker.js UI.\r\n   * @param displayMode - marker.js display mode (`inline` or `popup`).\r\n   * @param markerItems - available marker types.\r\n   * @param uiStyleSettings - settings for styling the tooblar ui.\r\n   */\r\n  constructor(\r\n    markerjsContainer: HTMLDivElement,\r\n    displayMode: DisplayMode,\r\n    markerItems: typeof MarkerBase[],\r\n    uiStyleSettings: IStyleSettings,\r\n    styles: StyleManager\r\n  ) {\r\n    this.markerjsContainer = markerjsContainer;\r\n    this.displayMode = displayMode;\r\n    this.markerItems = markerItems;\r\n    this.uiStyleSettings = uiStyleSettings;\r\n    this.styles = styles;\r\n    this.addStyles();\r\n\r\n    this.adjustLayout = this.adjustLayout.bind(this);\r\n    this.overflowButtonClicked = this.overflowButtonClicked.bind(this);\r\n    this.setCurrentMarker = this.setCurrentMarker.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Creates and displays the toolbar UI.\r\n   */\r\n  public show(visiblity: string): void {\r\n    this.uiContainer = document.createElement('div');\r\n    this.uiContainer.style.visibility = visiblity;\r\n    this.uiContainer.className = `${this.toolbarStyleClass.name} ${\r\n      this.styles.fadeInAnimationClassName\r\n    } ${\r\n      this.uiStyleSettings.toolbarStyleColorsClassName\r\n        ? this.uiStyleSettings.toolbarStyleColorsClassName\r\n        : this.toolbarStyleColorsClass.name\r\n    }`;\r\n\r\n    const actionButtonBlock = document.createElement('div');\r\n    actionButtonBlock.className = this.toolbarBlockStyleClass.name;\r\n    actionButtonBlock.style.whiteSpace = 'nowrap';\r\n    this.uiContainer.appendChild(actionButtonBlock);\r\n\r\n    this.addActionButton(actionButtonBlock, CursorIcon, 'select', 'Select mode');\r\n    this.addActionButton(actionButtonBlock, DeleteIcon, 'delete', 'Delete marker');\r\n    if (this.uiStyleSettings.clearButtonVisible) {\r\n      this.addActionButton(actionButtonBlock, ClearIcon, 'clear', 'Delete all markers');\r\n    }\r\n    if (this.uiStyleSettings.undoButtonVisible) {\r\n      this.addActionButton(actionButtonBlock, UndoIcon, 'undo', 'Undo');\r\n    }\r\n    if (this.uiStyleSettings.redoButtonVisible) {\r\n      this.addActionButton(actionButtonBlock, RedoIcon, 'redo', 'Redo');\r\n    }\r\n    if (this.uiStyleSettings.zoomButtonVisible) {\r\n      this.addActionButton(actionButtonBlock, ZoomIcon, 'zoom', 'Zoom in');\r\n    }\r\n    if (\r\n      this.uiStyleSettings.zoomButtonVisible &&\r\n      this.uiStyleSettings.zoomOutButtonVisible\r\n    ) {\r\n      this.addActionButton(actionButtonBlock, ZoomOutIcon, 'zoom-out', 'Zoom out');\r\n    }\r\n    if (this.uiStyleSettings.notesButtonVisible) {\r\n      this.addActionButton(actionButtonBlock, NotesIcon, 'notes', 'Notes');\r\n    }\r\n\r\n    this.markerButtonBlock = document.createElement('div');\r\n    this.markerButtonBlock.className = this.toolbarBlockStyleClass.name;\r\n    this.markerButtonBlock.style.flexGrow = '2';\r\n    this.markerButtonBlock.style.textAlign = 'center';\r\n    this.uiContainer.appendChild(this.markerButtonBlock);\r\n\r\n    this.markerButtonOverflowBlock = document.createElement('div');\r\n    this.markerButtonOverflowBlock.className = `${\r\n      this.toolbarOverflowBlockStyleClass.name\r\n    } ${\r\n      this.uiStyleSettings.toolbarOverflowBlockStyleColorsClassName\r\n        ? this.uiStyleSettings.toolbarOverflowBlockStyleColorsClassName\r\n        : this.toolbarOverflowBlockStyleColorsClass.name\r\n    }`;\r\n    this.markerButtonOverflowBlock.style.display = 'none';\r\n    this.uiContainer.appendChild(this.markerButtonOverflowBlock);\r\n\r\n    if (this.markerItems) {\r\n      this.markerItems.forEach((mi) => {\r\n        const buttonContainer = document.createElement('div');\r\n        buttonContainer.className = `${this.toolbarButtonStyleClass.name}`;\r\n        buttonContainer.setAttribute('data-type-name', mi.typeName);\r\n        buttonContainer.setAttribute('aria-label', mi.title);\r\n        buttonContainer.setAttribute('title', mi.title);\r\n        //  ${\r\n        //   this.uiStyleSettings.toolbarButtonStyleColorsClassName ?\r\n        //   this.uiStyleSettings.toolbarButtonStyleColorsClassName : this.toolbarButtonStyleColorsClass.name}`;\r\n        buttonContainer.innerHTML = mi.icon;\r\n        buttonContainer.addEventListener('click', () => {\r\n          this.markerToolbarButtonClicked(buttonContainer, mi);\r\n        });\r\n        //this.markerButtonBlock.appendChild(buttonContainer);\r\n        this.buttons.push(buttonContainer);\r\n        this.markerButtons.push(buttonContainer);\r\n      });\r\n      this.overflowButton = document.createElement('div');\r\n      this.overflowButton.className = `${this.toolbarButtonStyleClass.name} ${\r\n        this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          ? this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          : this.toolbarButtonStyleColorsClass.name\r\n      }`;\r\n      this.overflowButton.innerHTML = OverflowIcon;\r\n      this.overflowButton.addEventListener('click', this.overflowButtonClicked);\r\n      this.markerButtonBlock.appendChild(this.overflowButton);\r\n    }\r\n\r\n    const resultButtonBlock = document.createElement('div');\r\n    resultButtonBlock.className = this.toolbarBlockStyleClass.name;\r\n    resultButtonBlock.style.whiteSpace = 'nowrap';\r\n    resultButtonBlock.style.display =\r\n      this.uiStyleSettings.resultButtonBlockVisible !== false ? '' : 'none';\r\n    this.uiContainer.appendChild(resultButtonBlock);\r\n\r\n    this.addActionButton(resultButtonBlock, CheckIcon, 'render', 'Save and close');\r\n    this.addActionButton(resultButtonBlock, CloseIcon, 'close', 'Close');\r\n\r\n    this.markerjsContainer.appendChild(this.uiContainer);\r\n    this.setSelectMode();\r\n\r\n    this.setCurrentMarker();\r\n\r\n    this.adjustLayout();\r\n    // setTimeout(this.adjustLayout, 10);\r\n  }\r\n\r\n  /**\r\n   * Add a listener to the toolbar button click event.\r\n   * @param listener\r\n   */\r\n  public addButtonClickListener(listener: ToolbarButtonClickHandler): void {\r\n    this.buttonClickListeners.push(listener);\r\n  }\r\n\r\n  /**\r\n   * Remove a listener for the toolbar button click event.\r\n   * @param listener\r\n   */\r\n  public removeButtonClickListener(listener: ToolbarButtonClickHandler): void {\r\n    if (this.buttonClickListeners.indexOf(listener) > -1) {\r\n      this.buttonClickListeners.splice(\r\n        this.buttonClickListeners.indexOf(listener),\r\n        1\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Switch toolbar to the `select` mode.\r\n   */\r\n  public setSelectMode(): void {\r\n    this.resetButtonStyles();\r\n    this.setActiveButton(this.buttons[0]);\r\n  }\r\n\r\n  /**\r\n   * Adjusts toolbar layout.\r\n   */\r\n  public adjustLayout(): void {\r\n    if (this.markerButtons && this.markerButtons.length > 0) {\r\n      const numberToFit =\r\n        Math.floor(\r\n          this.markerButtonBlock.clientWidth /\r\n            this.uiStyleSettings.toolbarHeight\r\n        ) - 1;\r\n      this.markerButtonBlock.innerHTML = '';\r\n      this.markerButtonOverflowBlock.innerHTML = '';\r\n      for (\r\n        let buttonIndex = 0;\r\n        buttonIndex < this.markerButtons.length;\r\n        buttonIndex++\r\n      ) {\r\n        if (\r\n          buttonIndex < numberToFit ||\r\n          (buttonIndex === numberToFit &&\r\n            this.markerButtons.length - 1 === numberToFit)\r\n        ) {\r\n          this.markerButtonBlock.appendChild(this.markerButtons[buttonIndex]);\r\n        } else {\r\n          if (buttonIndex === numberToFit) {\r\n            this.markerButtonBlock.appendChild(this.overflowButton);\r\n          }\r\n          this.markerButtonOverflowBlock.appendChild(\r\n            this.markerButtons[buttonIndex]\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private overflowButtonClicked() {\r\n    if (this.markerButtonOverflowBlock.style.display !== 'none') {\r\n      this.markerButtonOverflowBlock.className = this.markerButtonOverflowBlock.className.replace(\r\n        this.styles.fadeInAnimationClassName,\r\n        ''\r\n      );\r\n      this.markerButtonOverflowBlock.style.display = 'none';\r\n    } else {\r\n      this.markerButtonOverflowBlock.className += ` ${this.styles.fadeInAnimationClassName}`;\r\n      this.markerButtonOverflowBlock.style.top = `${\r\n        this.uiContainer.offsetTop + this.overflowButton.offsetHeight\r\n      }px`;\r\n      this.markerButtonOverflowBlock.style.right = `${\r\n        this.uiContainer.offsetWidth -\r\n        this.overflowButton.offsetLeft -\r\n        this.overflowButton.offsetWidth +\r\n        this.uiContainer.offsetLeft * 2\r\n      }px`;\r\n      this.markerButtonOverflowBlock.style.display = 'inline-block';\r\n    }\r\n  }\r\n\r\n  private resetButtonStyles() {\r\n    this.buttons.forEach((button) => {\r\n      button.className = button.className\r\n        .replace(\r\n          this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n            ? this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n            : this.toolbarButtonStyleColorsClass.name,\r\n          ''\r\n        )\r\n        .trim();\r\n      button.className = button.className\r\n        .replace(\r\n          this.uiStyleSettings.toolbarActiveButtonStyleColorsClassName\r\n            ? this.uiStyleSettings.toolbarActiveButtonStyleColorsClassName\r\n            : this.toolbarActiveButtonStyleColorsClass.name,\r\n          ''\r\n        )\r\n        .trim();\r\n      button.className += ` ${\r\n        this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          ? this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          : this.toolbarButtonStyleColorsClass.name\r\n      }`;\r\n    });\r\n  }\r\n\r\n  private addActionButton(\r\n    container: HTMLDivElement,\r\n    icon: string,\r\n    value: string,\r\n    title: string\r\n  ) {\r\n    const actionButton = document.createElement('div');\r\n    actionButton.className = `${this.toolbarButtonStyleClass.name}`;\r\n    //  ${\r\n    //   this.uiStyleSettings.toolbarButtonStyleColorsClassName ?\r\n    //   this.uiStyleSettings.toolbarButtonStyleColorsClassName : this.toolbarButtonStyleColorsClass.name}`;\r\n    actionButton.innerHTML = icon;\r\n    actionButton.setAttribute('data-action', value);\r\n    actionButton.title = title;\r\n    actionButton.setAttribute('aria-label', title);\r\n    actionButton.addEventListener('click', () => {\r\n      this.actionToolbarButtonClicked(actionButton, value);\r\n    });\r\n    switch (value) {\r\n      case 'select':\r\n        actionButton.style.fill = this.uiStyleSettings.selectButtonColor;\r\n        break;\r\n      case 'delete':\r\n      case 'clear':\r\n        actionButton.style.fill = this.uiStyleSettings.deleteButtonColor;\r\n        break;\r\n      case 'undo':\r\n        actionButton.style.fill = this.uiStyleSettings.selectButtonColor;\r\n        break;\r\n      case 'redo':\r\n        actionButton.style.fill = this.uiStyleSettings.selectButtonColor;\r\n        break;\r\n      case 'render':\r\n        actionButton.style.fill = this.uiStyleSettings.okButtonColor;\r\n        break;\r\n      case 'close':\r\n        actionButton.style.fill = this.uiStyleSettings.closeButtonColor;\r\n        break;\r\n    }\r\n\r\n    container.appendChild(actionButton);\r\n    this.buttons.push(actionButton);\r\n  }\r\n\r\n  private addStyles() {\r\n    this.toolbarStyleClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar',\r\n        `\r\n      width: 100%;\r\n      flex-shrink: 0;\r\n      display: flex;\r\n      flex-direction: row;\r\n      justify-content: space-between;      \r\n      height: ${this.uiStyleSettings.toolbarHeight}px;\r\n      box-sizing: content-box;\r\n      ${\r\n        this.displayMode === 'inline'\r\n          ? `border-top-left-radius: ${Math.round(\r\n              this.uiStyleSettings.toolbarHeight / 10\r\n            )}px;`\r\n          : ''\r\n      }\r\n      ${\r\n        this.displayMode === 'inline'\r\n          ? `border-top-right-radius: ${Math.round(\r\n              this.uiStyleSettings.toolbarHeight / 10\r\n            )}px;`\r\n          : ''\r\n      }\r\n      overflow: hidden;\r\n    `\r\n      )\r\n    );\r\n\r\n    this.toolbarStyleColorsClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar_colors',\r\n        `\r\n      background-color: ${this.uiStyleSettings.toolbarBackgroundColor};\r\n      box-shadow: 0px 3px rgba(33, 33, 33, 0.1);\r\n    `\r\n      )\r\n    );\r\n\r\n    this.toolbarBlockStyleClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar-block',\r\n        `\r\n        display: inline-block;\r\n        box-sizing: content-box;\r\n    `\r\n      )\r\n    );\r\n\r\n    this.toolbarOverflowBlockStyleClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar-overflow-block',\r\n        `\r\n        position: absolute;\r\n        top: ${this.uiStyleSettings.toolbarHeight}px;\r\n        max-width: ${this.uiStyleSettings.toolbarHeight * 2}px;\r\n        z-index: 10;\r\n        box-sizing: content-box;\r\n      `\r\n      )\r\n    );\r\n    this.toolbarOverflowBlockStyleColorsClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar-overflow-block_colors',\r\n        `\r\n        background-color: ${this.uiStyleSettings.toolbarBackgroundColor};\r\n      `\r\n      )\r\n    );\r\n\r\n    const buttonPadding = this.uiStyleSettings.toolbarHeight / 4;\r\n    this.toolbarButtonStyleClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar_button',\r\n        `\r\n      display: inline-block;\r\n      width: ${this.uiStyleSettings.toolbarHeight - buttonPadding * 2}px;\r\n      height: ${this.uiStyleSettings.toolbarHeight - buttonPadding * 2}px;\r\n      padding: ${buttonPadding}px;\r\n      box-sizing: content-box;\r\n    `\r\n      )\r\n    );\r\n    this.toolbarButtonStyleColorsClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar_button_colors',\r\n        `\r\n      fill: ${this.uiStyleSettings.toolbarColor};\r\n    `\r\n      )\r\n    );\r\n\r\n    this.toolbarActiveButtonStyleColorsClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbar_active_button',\r\n        `\r\n      fill: ${this.uiStyleSettings.toolbarColor};\r\n      background-color: ${this.uiStyleSettings.toolbarBackgroundHoverColor}\r\n    `\r\n      )\r\n    );\r\n\r\n    this.styles.addRule(\r\n      new StyleRule(\r\n        `.${this.toolbarButtonStyleClass.name} svg`,\r\n        `\r\n      height: ${this.uiStyleSettings.toolbarHeight / 2}px;\r\n    `\r\n      )\r\n    );\r\n\r\n    this.styles.addRule(\r\n      new StyleRule(\r\n        `.${this.toolbarButtonStyleColorsClass.name}:hover`,\r\n        `\r\n        background-color: ${this.uiStyleSettings.toolbarBackgroundHoverColor}\r\n    `\r\n      )\r\n    );\r\n  }\r\n\r\n  private markerToolbarButtonClicked(\r\n    button: HTMLDivElement,\r\n    markerType: typeof MarkerBase\r\n  ) {\r\n    this.setActiveButton(button);\r\n    if (this.buttonClickListeners && this.buttonClickListeners.length > 0) {\r\n      this.buttonClickListeners.forEach((listener) =>\r\n        listener('marker', markerType)\r\n      );\r\n    }\r\n    this.markerButtonOverflowBlock.style.display = 'none';\r\n  }\r\n\r\n  private actionToolbarButtonClicked(button: HTMLDivElement, action: string) {\r\n    if (this.buttonClickListeners && this.buttonClickListeners.length > 0) {\r\n      this.buttonClickListeners.forEach((listener) =>\r\n        listener('action', action)\r\n      );\r\n    }\r\n    this.markerButtonOverflowBlock.style.display = 'none';\r\n    this.setActiveButton(this.buttons[0]);\r\n  }\r\n\r\n  private setActiveButton(button: HTMLDivElement) {\r\n    this.resetButtonStyles();\r\n    button.className = button.className\r\n      .replace(\r\n        this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          ? this.uiStyleSettings.toolbarButtonStyleColorsClassName\r\n          : this.toolbarButtonStyleColorsClass.name,\r\n        ''\r\n      )\r\n      .trim();\r\n    button.className += ` ${\r\n      this.uiStyleSettings.toolbarActiveButtonStyleColorsClassName\r\n        ? this.uiStyleSettings.toolbarActiveButtonStyleColorsClassName\r\n        : this.toolbarActiveButtonStyleColorsClass.name\r\n    }`;\r\n  }\r\n\r\n  /**\r\n   * Selects toolbar button for a specified marker type.\r\n   * @param typeName Marker type name\r\n   *\r\n   * @since 2.17.0\r\n   */\r\n  public setActiveMarkerButton(typeName: string): void {\r\n    const activeBtn = this.markerButtons.find(\r\n      (btn) => btn.getAttribute('data-type-name') === typeName\r\n    );\r\n    if (activeBtn) {\r\n      this.setActiveButton(activeBtn);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets current marker and enables/disables action buttons accordingly.\r\n   * @param marker\r\n   */\r\n  public setCurrentMarker(marker?: MarkerBase): void {\r\n    this.currentMarker = marker;\r\n    const activeMarkerButtons = this.buttons.filter((btn) =>\r\n      /delete|notes/.test(btn.getAttribute('data-action'))\r\n    );\r\n    activeMarkerButtons.forEach((btn) => {\r\n      if (this.currentMarker === undefined) {\r\n        btn.style.fillOpacity = '0.4';\r\n        btn.style.pointerEvents = 'none';\r\n      } else {\r\n        btn.style.fillOpacity = '1';\r\n        btn.style.pointerEvents = 'all';\r\n      }\r\n    });\r\n  }\r\n}\r\n", "import { ToolboxPanel } from './ToolboxPanel';\r\nimport { StyleManager, StyleClass, StyleRule } from './../core/Style';\r\nimport { DisplayMode } from '../core/Settings';\r\nimport { IStyleSettings } from '../core/IStyleSettings';\r\n\r\n/**\r\n * Represents the contextual toolbox for the selected marker type.\r\n */\r\nexport class Toolbox {\r\n  private panels: ToolboxPanel[] = [];\r\n  private activePanel: ToolboxPanel;\r\n  private panelButtons: HTMLDivElement[] = [];\r\n\r\n  private markerjsContainer: HTMLDivElement;\r\n  private displayMode: DisplayMode;\r\n  private uiContainer: HTMLDivElement;\r\n  private buttonRow: HTMLDivElement;\r\n  private panelRow: HTMLDivElement;\r\n\r\n  private toolboxStyleClass: StyleClass;\r\n  private toolboxStyleColorsClass: StyleClass;\r\n  private toolboxButtonStyleClass: StyleClass;\r\n  private toolboxButtonStyleColorsClass: StyleClass;\r\n  private toolboxActiveButtonStyleColorsClass: StyleClass;\r\n  private toolboxButtonRowStyleClass: StyleClass;\r\n  private toolboxButtonRowStyleColorsClass: StyleClass;\r\n  private toolboxPanelRowStyleClass: StyleClass;\r\n  private toolboxPanelRowStyleColorsClass: StyleClass;\r\n\r\n  private uiStyleSettings: IStyleSettings;\r\n  private styles: StyleManager;\r\n  \r\n  private addStyles() {\r\n    this.toolboxStyleClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbox',\r\n        `\r\n      width: 100%;\r\n      flex-shrink: 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      font-family: sans-serif;\r\n      ${this.displayMode === 'popup' ? 'height:' + this.uiStyleSettings.toolbarHeight * 2.5 + 'px;' : ''}\r\n      box-sizing: content-box;\r\n      ${this.displayMode === 'popup' ? `background-color: ${this.uiStyleSettings.canvasBackgroundColor};` : ''}\r\n      ${this.displayMode === 'inline' ? `border-bottom-left-radius: ${Math.round(this.uiStyleSettings.toolbarHeight/10)}px;` : ''}\r\n      ${this.displayMode === 'inline' ? `border-bottom-right-radius: ${Math.round(this.uiStyleSettings.toolbarHeight/10)}px;` : ''}\r\n      overflow: hidden;\r\n    `\r\n      )\r\n    );\r\n    this.toolboxStyleColorsClass = this.styles.addClass(\r\n      new StyleClass(\r\n        'toolbox_colors',\r\n        `\r\n      color: ${this.uiStyleSettings.toolboxColor};\r\n    `\r\n      )\r\n    );\r\n\r\n    const buttonPadding = this.uiStyleSettings.toolbarHeight / 4;\r\n    this.toolboxButtonRowStyleClass = this.styles.addClass(new StyleClass('toolbox-button-row', `\r\n      display: flex;\r\n      cursor: default;\r\n      box-sizing: content-box;\r\n    `));\r\n    this.toolboxButtonRowStyleColorsClass = this.styles.addClass(new StyleClass('toolbox-button-row_colors', `\r\n      background-color: ${this.uiStyleSettings.toolbarBackgroundColor};\r\n    `));\r\n\r\n    this.toolboxPanelRowStyleClass = this.styles.addClass(new StyleClass('toolbox-panel-row', `\r\n      display: flex;\r\n      ${this.displayMode === 'inline' ? 'position: absolute;' : '' }\r\n      ${this.displayMode === 'inline' ? 'bottom: ' + this.uiStyleSettings.toolbarHeight + 'px;' : '' }\r\n      cursor: default;\r\n      height: ${this.uiStyleSettings.toolbarHeight * 1.5}px;\r\n      ${this.displayMode === 'inline' ? 'width: 100%;' : ''}\r\n      box-sizing: content-box;\r\n    `));\r\n    this.toolboxPanelRowStyleColorsClass = this.styles.addClass(new StyleClass('toolbox-panel-row_colors', `\r\n      background-color: ${this.uiStyleSettings.toolboxBackgroundColor ?? this.uiStyleSettings.toolbarBackgroundHoverColor};\r\n    `));\r\n\r\n    this.toolboxButtonStyleClass = this.styles.addClass(new StyleClass('toolbox_button', `\r\n      display: inline-block;\r\n      width: ${this.uiStyleSettings.toolbarHeight - buttonPadding * 2}px;\r\n      height: ${this.uiStyleSettings.toolbarHeight - buttonPadding * 2}px;\r\n      padding: ${buttonPadding}px;\r\n      box-sizing: content-box;\r\n    `));\r\n    this.toolboxButtonStyleColorsClass = this.styles.addClass(new StyleClass('toolbox-button_colors', `\r\n      fill: ${this.uiStyleSettings.toolbarColor};\r\n    `));\r\n\r\n    this.toolboxActiveButtonStyleColorsClass = this.styles.addClass(new StyleClass('toolbox-active-button_colors', `\r\n      background-color: ${this.uiStyleSettings.toolbarBackgroundHoverColor};\r\n      fill: ${this.uiStyleSettings.toolbarColor};\r\n    `));\r\n\r\n    this.styles.addRule(\r\n      new StyleRule(\r\n        `.${this.toolboxButtonStyleColorsClass.name}:hover`,\r\n        `\r\n        background-color: ${this.uiStyleSettings.toolbarBackgroundHoverColor}\r\n    `\r\n      )\r\n    );\r\n\r\n    this.styles.addRule(\r\n      new StyleRule(\r\n        `.${this.toolboxButtonStyleClass.name} svg`,\r\n        `\r\n      height: ${this.uiStyleSettings.toolbarHeight / 2}px;\r\n    `\r\n      )\r\n    );\r\n\r\n  }\r\n\r\n  /**\r\n   * Creates the toolbox object\r\n   * @param markerjsContainer - container for the toolbox in marker.js UI.\r\n   * @param displayMode - marker.js display mode (`inline` or `popup`).\r\n   * @param uiStyleSettings - settings for styling the toolbox elements.\r\n   */\r\n  constructor(markerjsContainer: HTMLDivElement, displayMode: DisplayMode, uiStyleSettings: IStyleSettings, styles: StyleManager) {\r\n    this.markerjsContainer = markerjsContainer;\r\n    this.displayMode = displayMode;\r\n    this.uiStyleSettings = uiStyleSettings;\r\n    this.styles = styles;\r\n\r\n    this.panelButtonClick = this.panelButtonClick.bind(this);\r\n\r\n    this.addStyles();\r\n  }\r\n\r\n  /**\r\n   * Creates and displays the main toolbox UI.\r\n   */\r\n  public show(visiblity: string): void {\r\n    this.uiContainer = document.createElement('div');\r\n    this.uiContainer.style.visibility = visiblity;\r\n    this.uiContainer.className = `${this.toolboxStyleClass.name} ${\r\n      this.uiStyleSettings.toolboxStyleColorsClassName ?? this.toolboxStyleColorsClass.name}`;\r\n\r\n    this.markerjsContainer.appendChild(this.uiContainer);\r\n  }\r\n\r\n  /**\r\n   * Creaes buttons for the top-level toolbox panel.\r\n   * @param panels - available panels.\r\n   */\r\n  public setPanelButtons(panels: ToolboxPanel[]): void {\r\n    this.panels = panels;\r\n    if (this.uiContainer !== undefined) {\r\n      this.uiContainer.innerHTML = '';\r\n\r\n      this.panelRow = document.createElement('div');\r\n      this.panelRow.className = `${this.toolboxPanelRowStyleClass.name} ${\r\n        this.uiStyleSettings.toolboxPanelRowStyleColorsClassName ?? this.toolboxPanelRowStyleColorsClass.name}`;\r\n      this.uiContainer.appendChild(this.panelRow);\r\n      this.buttonRow = document.createElement('div');\r\n      this.buttonRow.className = `${this.toolboxButtonRowStyleClass.name} ${\r\n        this.uiStyleSettings.toolboxButtonRowStyleColorsClassName ?? this.toolboxButtonRowStyleColorsClass.name} `;\r\n      this.uiContainer.appendChild(this.buttonRow);\r\n\r\n      this.panelButtons.splice(0);\r\n\r\n      this.panels.forEach(panel => {\r\n        panel.uiStyleSettings = this.uiStyleSettings;\r\n        const panelBtnDiv = document.createElement('div');\r\n        panelBtnDiv.className = `${this.toolboxButtonStyleClass.name} ${\r\n          this.uiStyleSettings.toolboxButtonStyleColorsClassName ?? this.toolboxButtonStyleColorsClass.name}`;\r\n        panelBtnDiv.innerHTML = panel.icon;\r\n        panelBtnDiv.title = panel.title;\r\n        panelBtnDiv.addEventListener('click', () => {\r\n          this.panelButtonClick(panel);\r\n        })\r\n        this.panelButtons.push(panelBtnDiv);\r\n        this.buttonRow.appendChild(panelBtnDiv);\r\n      });\r\n      if (this.displayMode === 'inline') {\r\n        this.panelRow.style.display = 'none';\r\n      } else {\r\n        this.panelRow.style.visibility = 'hidden';\r\n      }\r\n    }\r\n    // if (this.displayMode === 'popup' && this.panels.length > 0) {\r\n    //   // this.showPanel(this.activePanel ? this.activePanel : this.panels[0]);\r\n    //   this.panelButtonClick(this.panels[0]);\r\n    // }\r\n  }\r\n\r\n  private panelButtonClick(panel: ToolboxPanel) {\r\n    let panelIndex = -1; \r\n    if (panel !== this.activePanel) {\r\n      panelIndex = this.panels.indexOf(panel);\r\n      this.panelRow.innerHTML = '';\r\n      const panelUI = panel.getUi();\r\n      panelUI.style.margin = `${this.uiStyleSettings.toolbarHeight / 4}px`;\r\n      this.panelRow.appendChild(panelUI);\r\n      this.panelRow.style.display = 'flex';\r\n      this.panelRow.style.visibility = 'visible';\r\n      this.panelRow.className = this.panelRow.className.replace(this.styles.fadeOutAnimationClassName, '');\r\n      this.panelRow.className += ` ${this.styles.fadeInAnimationClassName}`;\r\n      this.activePanel = panel;\r\n    } else {\r\n      this.activePanel = undefined;\r\n      // hide panel\r\n      this.panelRow.className = this.panelRow.className.replace(this.styles.fadeInAnimationClassName, '');\r\n      this.panelRow.className += ` ${this.styles.fadeOutAnimationClassName}`;\r\n      setTimeout(() => {\r\n        if (this.displayMode === 'inline') {\r\n          this.panelRow.style.display = 'none';\r\n        } else {\r\n          this.panelRow.style.visibility = 'hidden';\r\n        }\r\n      }, 200);\r\n    }\r\n    this.panelButtons.forEach((pb, index) => {\r\n      pb.className = `${this.toolboxButtonStyleClass.name} ` +\r\n        (index === panelIndex\r\n          ? `${this.uiStyleSettings.toolboxActiveButtonStyleColorsClassName ?? this.toolboxActiveButtonStyleColorsClass.name}`\r\n          :  `${this.uiStyleSettings.toolboxButtonStyleColorsClassName ?? this.toolboxButtonStyleColorsClass.name}`);\r\n    });\r\n  }\r\n\r\n}\r\n", "import { IStyleSettings } from '../core/IStyleSettings';\r\n\r\n/**\r\n * Base class for all toolbox property panels.\r\n */\r\nexport abstract class ToolboxPanel {\r\n  /**\r\n   * Panel name/title.\r\n   */\r\n  public title: string;\r\n  /**\r\n   * Panel button icon as an SVG markup.\r\n   */\r\n  public icon: string;\r\n\r\n  /**\r\n   * UI style settings for colors, etc.\r\n   */\r\n  public uiStyleSettings: IStyleSettings;\r\n\r\n  /**\r\n   * Create panel with supplied title and icon.\r\n   * @param title - panel name (used for accessibility)\r\n   * @param icon - panel button icon (SVG image markup)\r\n   */\r\n  constructor(title: string, icon?: string) {\r\n    this.title = title;\r\n    this.icon = icon;\r\n  }\r\n  /**\r\n   * Returns toolbox panel UI.\r\n   */\r\n  public abstract getUi(): HTMLDivElement;\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './color-picker-panel-icon.svg';\r\n\r\n/**\r\n * Hand<PERSON> type for the color change event.\r\n */\r\nexport type ColorChangeHandler = (newColor: string) => void;\r\n\r\n/**\r\n * Color picker panel.\r\n */\r\nexport class ColorPickerPanel extends ToolboxPanel {\r\n  public colors: string[] = [];\r\n  private currentColor?: string;\r\n  private addTransparent = false;\r\n\r\n  private colorBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Color change event handler.\r\n   */\r\n  public onColorChanged?: ColorChangeHandler;\r\n\r\n  /**\r\n   * Creates a color picker panel.\r\n   * @param title - panel title.\r\n   * @param colors - available colors.\r\n   * @param currentColor - currently selected color.\r\n   * @param icon - panel button icon (SVG imager markup).\r\n   */\r\n  constructor(title: string, colors: string[], currentColor?: string, icon?: string) {\r\n    super(title, icon ? icon : Icon);\r\n    this.colors = colors;\r\n    this.currentColor = currentColor;\r\n\r\n    this.setCurrentColor = this.setCurrentColor.bind(this);\r\n    this.getColorBox = this.getColorBox.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.whiteSpace = 'nowrap';\r\n    this.colors.forEach((color) => {\r\n      const colorBoxContainer = this.getColorBox(color);\r\n      panelDiv.appendChild(colorBoxContainer);\r\n      this.colorBoxes.push(colorBoxContainer);\r\n    });\r\n    return panelDiv;\r\n  }\r\n\r\n  private getColorBox(color): HTMLDivElement {\r\n    const buttonPadding = this.uiStyleSettings.toolbarHeight / 4;\r\n    const buttonHeight = this.uiStyleSettings.toolbarHeight - buttonPadding;\r\n\r\n    const colorBoxContainer = document.createElement('div');\r\n    colorBoxContainer.style.display = 'inline-block';\r\n    colorBoxContainer.style.boxSizing = 'content-box';\r\n    colorBoxContainer.style.width = `${buttonHeight - 2}px`;\r\n    colorBoxContainer.style.height = `${buttonHeight - 2}px`;\r\n    colorBoxContainer.style.padding = '1px';\r\n    colorBoxContainer.style.marginRight = '2px';\r\n    colorBoxContainer.style.marginBottom = '2px';\r\n    colorBoxContainer.style.borderWidth = '2px';\r\n    colorBoxContainer.style.borderStyle = 'solid';\r\n    colorBoxContainer.style.borderRadius = `${(buttonHeight + 2)/2}px`\r\n    colorBoxContainer.style.borderColor =\r\n      color === this.currentColor ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n\r\n    colorBoxContainer.addEventListener('click', () => {\r\n      this.setCurrentColor(color, colorBoxContainer);\r\n    })\r\n\r\n    const colorBox = document.createElement('div');\r\n    colorBox.style.display = 'inline-block';\r\n    colorBox.style.width = `${buttonHeight - 2}px`;\r\n    colorBox.style.height = `${buttonHeight - 2}px`;\r\n    colorBox.style.backgroundColor = color;\r\n    colorBox.style.borderRadius = `${buttonHeight/2}px`;\r\n    if (color === 'transparent') {\r\n      colorBox.style.fill = this.uiStyleSettings.toolboxAccentColor;\r\n      colorBox.innerHTML = `<svg viewBox=\"0 0 24 24\">\r\n        <path d=\"M2,5.27L3.28,4L20,20.72L18.73,22L15.65,18.92C14.5,19.3 13.28,19.5 12,19.5C7,19.5 2.73,16.39 1,12C1.69,10.24 2.79,8.69 4.19,7.46L2,5.27M12,9A3,3 0 0,1 15,12C15,12.35 14.94,12.69 14.83,13L11,9.17C11.31,9.06 11.65,9 12,9M12,4.5C17,4.5 21.27,7.61 23,12C22.18,14.08 20.79,15.88 19,17.19L17.58,15.76C18.94,14.82 20.06,13.54 20.82,12C19.17,8.64 15.76,6.5 12,6.5C10.91,6.5 9.84,6.68 8.84,7L7.3,5.47C8.74,4.85 10.33,4.5 12,4.5M3.18,12C4.83,15.36 8.24,17.5 12,17.5C12.69,17.5 13.37,17.43 14,17.29L11.72,15C10.29,14.85 9.15,13.71 9,12.28L5.6,8.87C4.61,9.72 3.78,10.78 3.18,12Z\" />\r\n      </svg>`;\r\n    }\r\n\r\n    colorBoxContainer.appendChild(colorBox);\r\n\r\n    return colorBoxContainer;\r\n  }\r\n\r\n  private setCurrentColor(color: string, target: HTMLDivElement) {\r\n    this.currentColor = color;\r\n\r\n    this.colorBoxes.forEach(box => {\r\n      box.style.borderColor = box === target ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n    });\r\n\r\n    if (this.onColorChanged) {\r\n      this.onColorChanged(color);\r\n    }\r\n  }\r\n}\r\n", "import { IPoint } from './IPoint';\r\nimport { ToolboxPanel } from '../ui/ToolboxPanel';\r\nimport { MarkerBaseState, MarkerState } from './MarkerBaseState';\r\nimport { Settings } from './Settings';\r\n\r\n/**\r\n * Base class for all available and custom marker types.\r\n * \r\n * All markers used with marker.js 2 should be descendants of this class.\r\n */\r\nexport class MarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'MarkerBase';\r\n\r\n  /**\r\n   * Instance property returning marker's type name.\r\n   * \r\n   * @since 2.16.0\r\n   */\r\n  public get typeName(): string {\r\n    return Object.getPrototypeOf(this).constructor.typeName;\r\n  }\r\n\r\n  protected _container: SVGGElement;\r\n  /**\r\n   * SVG container object holding the marker's visual.\r\n   */\r\n  public get container(): SVGGElement {\r\n    return this._container;\r\n  }\r\n  protected _overlayContainer: HTMLDivElement;\r\n  /**\r\n   * HTML container that can be used to render overlay objects while the marker is active.\r\n   * \r\n   * For example, this is used for the text editing layer while editing text in the {@see TextMarker}.\r\n   */\r\n  public get overlayContainer(): HTMLDivElement {\r\n    return this._overlayContainer;\r\n  }\r\n  protected _state: MarkerState = 'new';\r\n  /**\r\n   * Current marker state.\r\n   *\r\n   * Both MarkerArea and the marker itself can react differently to different events based on what state the marker is in.\r\n   */\r\n  public get state(): MarkerState {\r\n    return this._state;\r\n  }\r\n  protected globalSettings: Settings;\r\n\r\n  /**\r\n   * Additional information about the marker\r\n   */\r\n  public notes?: string;\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [];\r\n  }\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title: string;\r\n\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon: string;\r\n\r\n  /**\r\n   * Method called when marker creation is finished.\r\n   */\r\n  public onMarkerCreated: (marker: MarkerBase) => void;\r\n\r\n  /**\r\n   * Method to call when foreground color changes.\r\n   */\r\n  public onColorChanged?: (color: string) => void;\r\n  /**\r\n   * Method to call when background/fill color changes.\r\n   */\r\n  public onFillColorChanged?: (color: string) => void;\r\n  /**\r\n   * Method to call when marker state changes.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  public onStateChanged?: (marker: MarkerBase) => void;\r\n\r\n  /**\r\n   * Marker's state when it is selected\r\n   *\r\n   * @since 2.23.0\r\n   */\r\n  protected manipulationStartState?: MarkerBaseState;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    this._container = container;\r\n    this._overlayContainer = overlayContainer;\r\n    this.globalSettings = settings;\r\n\r\n    this.stateChanged = this.stateChanged.bind(this);\r\n    this.colorChanged = this.colorChanged.bind(this);\r\n    this.fillColorChanged = this.fillColorChanged.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    return false;\r\n  }\r\n\r\n  /**\r\n   * Is this marker selected?\r\n   * \r\n   * @since 2.16.0\r\n   */\r\n  protected _isSelected = false;\r\n\r\n  /**\r\n   * Returns true if the marker is currently selected\r\n   * \r\n   * @since 2.16.0\r\n   */\r\n  public get isSelected(): boolean {\r\n    return this._isSelected;\r\n  }\r\n\r\n  /**\r\n   * Selects this marker and displays appropriate selected marker UI.\r\n   */\r\n  public select(): void {\r\n    this.container.style.cursor = 'move';\r\n    this._isSelected = true;\r\n    this.manipulationStartState = this.getState();\r\n  }\r\n\r\n  /**\r\n   * Deselects this marker and hides selected marker UI.\r\n   */\r\n  public deselect(): void {\r\n    this.container.style.cursor = 'default';\r\n    this._isSelected = false;\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function\r\n  public pointerDown(point: IPoint, target?: EventTarget):void {}\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) double click event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function\r\n  public dblClick(point: IPoint, target?: EventTarget):void {}\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function\r\n  public manipulate(point: IPoint):void {}\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/no-empty-function\r\n  public pointerUp(point: IPoint):void {\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Disposes the marker and clean's up.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n  public dispose(): void {}\r\n\r\n  protected addMarkerVisualToContainer(element: SVGElement): void {\r\n    if (this.container.childNodes.length > 0) {\r\n      this.container.insertBefore(element, this.container.childNodes[0]);\r\n    } else {\r\n      this.container.appendChild(element);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): MarkerBaseState {\r\n    return { \r\n      typeName: MarkerBase.typeName, \r\n      state: this.state,\r\n      notes: this.notes\r\n    };\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    this._state = state.state;\r\n    this.notes = state.notes;\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function, @typescript-eslint/no-unused-vars\r\n  public scale(scaleX: number, scaleY: number): void {}\r\n\r\n  /**\r\n   * Called by a marker when its foreground color changes.\r\n   * @param color \r\n   */\r\n  protected colorChanged(color: string): void {\r\n    if (this.onColorChanged) {\r\n      this.onColorChanged(color);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Called by a marker when its background/fill color changes.\r\n   * @param color \r\n   */\r\n  protected fillColorChanged(color: string): void {\r\n    if (this.onFillColorChanged) {\r\n      this.onFillColorChanged(color);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Called by a marker when its state could have changed.\r\n   * Does a check if the state has indeed changed before firing the handler.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  protected stateChanged(): void {\r\n    if (this.onStateChanged && this.state !== 'creating' && this.state !== 'new') {\r\n      const currentState = this.getState();\r\n      // avoid reacting to state (mode) differences\r\n      if (this.manipulationStartState !== undefined) {\r\n        this.manipulationStartState.state = 'select';\r\n      }\r\n      currentState.state = 'select';\r\n      if (JSON.stringify(this.manipulationStartState) != JSON.stringify(currentState)) {\r\n        this.onStateChanged(this);\r\n      }\r\n    }\r\n  }\r\n}\r\n", "import { ResizeGrip } from './ResizeGrip';\r\n\r\n/**\r\n * RectangularBoxMarkerGrips is a set of resize/rotation grips for a rectangular marker.\r\n */\r\nexport class RectangularBoxMarkerGrips {\r\n  /**\r\n   * Top-left grip.\r\n   */\r\n  public topLeft: ResizeGrip;\r\n  /**\r\n   * Top-center grip.\r\n   */\r\n  public topCenter: ResizeGrip;\r\n  /**\r\n   * Top-right grip.\r\n   */\r\n  public topRight: ResizeGrip;\r\n  /**\r\n   * Center-left grip.\r\n   */\r\n  public centerLeft: ResizeGrip;\r\n  /**\r\n   * Center-right grip.\r\n   */\r\n  public centerRight: ResizeGrip;\r\n  /**\r\n   * Bottom-left grip.\r\n   */\r\n  public bottomLeft: ResizeGrip;\r\n  /**\r\n   * Bottom-center grip.\r\n   */\r\n  public bottomCenter: ResizeGrip;\r\n  /**\r\n   * Bottom-right grip.\r\n   */\r\n  public bottomRight: ResizeGrip;\r\n\r\n  /**\r\n   * Creates a new marker grip set.\r\n   */\r\n  constructor() {\r\n    this.findGripByVisual = this.findGripByVisual.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns a marker grip owning the specified visual.\r\n   * @param gripVisual - visual for owner to be determined.\r\n   */\r\n  public findGripByVisual(\r\n    gripVisual: EventTarget\r\n  ): ResizeGrip | undefined {\r\n      if (this.topLeft.ownsTarget(gripVisual)) {\r\n        return this.topLeft;\r\n      } else if (this.topCenter.ownsTarget(gripVisual)) {\r\n        return this.topCenter;\r\n      } else if (this.topRight.ownsTarget(gripVisual)) {\r\n        return this.topRight;\r\n      } else if (this.centerLeft.ownsTarget(gripVisual)) {\r\n        return this.centerLeft;\r\n      } else if (this.centerRight.ownsTarget(gripVisual)) {\r\n        return this.centerRight;\r\n      } else if (this.bottomLeft.ownsTarget(gripVisual)) {\r\n        return this.bottomLeft;\r\n      } else if (this.bottomCenter.ownsTarget(gripVisual)) {\r\n        return this.bottomCenter;\r\n      } else if (this.bottomRight.ownsTarget(gripVisual)) {\r\n        return this.bottomRight;\r\n      } else {\r\n        return undefined;\r\n      }\r\n  }\r\n}\r\n", "import { SvgHelper } from '../core/SvgHelper';\r\n\r\n/**\r\n * Represents a single resize-manipulation grip used in marker's manipulation controls.\r\n */\r\nexport class ResizeGrip {\r\n  /**\r\n   * Grip's visual element.\r\n   */\r\n  public visual: SVGGraphicsElement;\r\n\r\n  /**\r\n   * Grip's size (raduis).\r\n   */\r\n  public readonly GRIP_SIZE = 10;\r\n\r\n  /**\r\n   * Creates a new grip.\r\n   */\r\n  constructor() {\r\n    this.visual = SvgHelper.createGroup();\r\n    this.visual.appendChild(\r\n      SvgHelper.createCircle(this.GRIP_SIZE * 1.5, [['fill', 'transparent']])\r\n    );\r\n    this.visual.appendChild(\r\n      SvgHelper.createCircle(this.GRIP_SIZE, [\r\n        ['fill', '#cccccc'],\r\n        ['fill-opacity', '0.7'],\r\n        ['stroke', '#333333'],\r\n        ['stroke-width', '2'],\r\n        ['stroke-opacity', '0.7']\r\n      ])\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the grip. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      el === this.visual ||\r\n      el === this.visual.childNodes[0] ||\r\n      el === this.visual.childNodes[1]\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n}\r\n", "/**\r\n * Represents a simplified version of the SVGMatrix.\r\n */\r\nexport interface ITransformMatrix {\r\n  a: number;\r\n  b: number;\r\n  c: number;\r\n  d: number;\r\n  e: number;\r\n  f: number;\r\n}\r\n\r\n/**\r\n * A utility class to transform between SVGMatrix and its simplified representation.\r\n */\r\nexport class TransformMatrix {\r\n  public static toITransformMatrix(matrix: SVGMatrix): ITransformMatrix {\r\n    return {\r\n      a: matrix.a,\r\n      b: matrix.b,\r\n      c: matrix.c,\r\n      d: matrix.d,\r\n      e: matrix.e,\r\n      f: matrix.f\r\n    }\r\n  }\r\n  public static toSVGMatrix(currentMatrix: SVGMatrix, newMatrix: ITransformMatrix): SVGMatrix {\r\n    currentMatrix.a = newMatrix.a;\r\n    currentMatrix.b = newMatrix.b;\r\n    currentMatrix.c = newMatrix.c;\r\n    currentMatrix.d = newMatrix.d;\r\n    currentMatrix.e = newMatrix.e;\r\n    currentMatrix.f = newMatrix.f;\r\n    return currentMatrix;\r\n  }\r\n}", "import { MarkerBase } from '../core/MarkerBase';\r\n\r\nimport { IPoint } from '../core/IPoint';\r\nimport { SvgHelper } from '../core/SvgHelper';\r\n\r\nimport { RectangularBoxMarkerGrips } from './RectangularBoxMarkerGrips';\r\nimport { ResizeGrip } from './ResizeGrip';\r\nimport { Settings } from '../core/Settings';\r\nimport { RectangularBoxMarkerBaseState } from './RectangularBoxMarkerBaseState';\r\nimport { MarkerBaseState } from '../core/MarkerBaseState';\r\nimport { TransformMatrix } from '../core/TransformMatrix';\r\n\r\n/**\r\n * RectangularBoxMarkerBase is a base class for all marker's with rectangular controls such as all rectangle markers,\r\n * text and callout markers.\r\n * \r\n * It creates and manages the rectangular control box and related resize, move, and rotate manipulations.\r\n */\r\nexport class RectangularBoxMarkerBase extends MarkerBase {\r\n  /**\r\n   * x coordinate of the top-left corner.\r\n   */\r\n  protected left = 0;\r\n  /**\r\n   * y coordinate of the top-left corner.\r\n   */\r\n  protected top = 0;\r\n  /**\r\n   * Marker width.\r\n   */\r\n  protected width = 0;\r\n  /**\r\n   * Marker height.\r\n   */\r\n  protected height = 0;\r\n\r\n  /**\r\n   * The default marker size when the marker is created with a click (without dragging).\r\n   */\r\n  protected defaultSize: IPoint = {x: 50, y: 20};\r\n\r\n  /**\r\n   * x coordinate of the top-left corner at the start of manipulation.\r\n   */\r\n  protected manipulationStartLeft: number;\r\n  /**\r\n   * y coordinate of the top-left corner at the start of manipulation.\r\n   */\r\n  protected manipulationStartTop: number;\r\n  /**\r\n   * Width at the start of manipulation.\r\n   */\r\n  protected manipulationStartWidth: number;\r\n  /**\r\n   * Height at the start of manipulation.\r\n   */\r\n  protected manipulationStartHeight: number;\r\n\r\n  /**\r\n   * x coordinate of the pointer at the start of manipulation.\r\n   */\r\n  protected manipulationStartX: number;\r\n  /**\r\n   * y coordinate of the pointer at the start of manipulation.\r\n   */\r\n  protected manipulationStartY: number;\r\n\r\n  /**\r\n   * Pointer's horizontal distance from the top left corner.\r\n   */\r\n  protected offsetX = 0;\r\n  /**\r\n   * Pointer's vertical distance from the top left corner.\r\n   */\r\n  protected offsetY = 0;\r\n\r\n  /**\r\n   * Marker's rotation angle.\r\n   */\r\n  protected rotationAngle = 0;\r\n\r\n  /**\r\n   * x coordinate of the marker's center.\r\n   */\r\n  protected get centerX(): number {\r\n    return this.left + this.width / 2;\r\n  }\r\n  /**\r\n   * y coordinate of the marker's center.\r\n   */\r\n  protected get centerY(): number {\r\n    return this.top + this.height / 2;\r\n  }\r\n\r\n  private _visual: SVGGraphicsElement;\r\n  /**\r\n   * Container for the marker's visual.\r\n   */\r\n  protected get visual(): SVGGraphicsElement {\r\n    return this._visual;\r\n  }\r\n  protected set visual(value: SVGGraphicsElement) {\r\n    this._visual = value;\r\n    const translate = SvgHelper.createTransform();\r\n    this._visual.transform.baseVal.appendItem(translate);\r\n  }\r\n\r\n  /**\r\n   * Container for the marker's editing controls.\r\n   */\r\n  protected controlBox: SVGGElement;\r\n  private readonly CB_DISTANCE: number = 10;\r\n  private controlRect: SVGRectElement;\r\n  private rotatorGripLine: SVGLineElement;\r\n\r\n  private controlGrips: RectangularBoxMarkerGrips;\r\n  private rotatorGrip: ResizeGrip;\r\n  private activeGrip: ResizeGrip;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    // add rotation transform\r\n    this.container.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n\r\n    this.setupControlBox();\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (super.ownsTarget(el)) {\r\n      return true;\r\n    } else if (\r\n      this.controlGrips.findGripByVisual(el) !== undefined ||\r\n      (this.rotatorGrip !== undefined && this.rotatorGrip.ownsTarget(el))\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n\r\n    if (this.state === 'new') {\r\n      this.left = point.x;\r\n      this.top = point.y;\r\n    }\r\n\r\n    this.manipulationStartLeft = this.left;\r\n    this.manipulationStartTop = this.top;\r\n    this.manipulationStartWidth = this.width;\r\n    this.manipulationStartHeight = this.height;\r\n\r\n    const rotatedPoint = this.unrotatePoint(point);\r\n    this.manipulationStartX = rotatedPoint.x;\r\n    this.manipulationStartY = rotatedPoint.y;\r\n\r\n    this.offsetX = rotatedPoint.x - this.left;\r\n    this.offsetY = rotatedPoint.y - this.top;\r\n\r\n    if (this.state !== 'new') {\r\n      this.select();\r\n      this.activeGrip = this.controlGrips.findGripByVisual(target as SVGGraphicsElement);\r\n      if (this.activeGrip !== undefined) {\r\n        this._state = 'resize';\r\n      } else if (this.rotatorGrip !== undefined && this.rotatorGrip.ownsTarget(target)) {\r\n        this.activeGrip = this.rotatorGrip;\r\n\r\n        const rotatedCenter = this.rotatePoint({x: this.centerX, y: this.centerY});\r\n        this.left = rotatedCenter.x - this.width / 2;\r\n        this.top = rotatedCenter.y - this.height / 2;\r\n        this.moveVisual({ x: this.left, y: this.top });\r\n\r\n        const rotate = this.container.transform.baseVal.getItem(0);\r\n        rotate.setRotate(this.rotationAngle, this.centerX, this.centerY);\r\n        this.container.transform.baseVal.replaceItem(rotate, 0);\r\n\r\n        this.adjustControlBox();\r\n\r\n        this._state = 'rotate';\r\n      } else {\r\n        this._state = 'move';\r\n      }\r\n    }\r\n  }\r\n\r\n  protected _suppressMarkerCreateEvent = false;\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    const inState = this.state;\r\n    super.pointerUp(point);\r\n    if (this.state === 'creating' && this.width < 10 && this.height < 10) {\r\n      this.width = this.defaultSize.x;\r\n      this.height = this.defaultSize.y;\r\n    } else {\r\n      this.manipulate(point);\r\n    }\r\n    this._state = 'select';\r\n    if (inState === 'creating' && this.onMarkerCreated && this._suppressMarkerCreateEvent === false) {\r\n      this.onMarkerCreated(this);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Moves visual to the specified coordinates.\r\n   * @param point - coordinates of the new top-left corner of the visual.\r\n   */\r\n  protected moveVisual(point: IPoint): void {\r\n    this.visual.style.transform = `translate(${point.x}px, ${point.y}px)`;\r\n    // const translate = this.visual.transform.baseVal.getItem(0);\r\n    // translate.setTranslate(point.x, point.y);\r\n    // this.visual.transform.baseVal.replaceItem(translate, 0);\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    const rotatedPoint = this.unrotatePoint(point);\r\n\r\n    if (this.state === 'creating') {\r\n      this.resize(point);\r\n    } else if (this.state === 'move') {\r\n      this.left =\r\n        this.manipulationStartLeft +\r\n        (rotatedPoint.x - this.manipulationStartLeft) -\r\n        this.offsetX;\r\n      this.top =\r\n        this.manipulationStartTop +\r\n        (rotatedPoint.y - this.manipulationStartTop) -\r\n        this.offsetY;\r\n      this.moveVisual({x: this.left, y: this.top});\r\n      this.adjustControlBox();\r\n    } else if (this.state === 'resize') {\r\n      this.resize(rotatedPoint);\r\n    } else if (this.state === 'rotate') {\r\n      this.rotate(point);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resizes the marker based on pointer coordinates and context.\r\n   * @param point - pointer coordinates.\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    let newX = this.manipulationStartLeft;\r\n    let newWidth = this.manipulationStartWidth;\r\n    let newY = this.manipulationStartTop;\r\n    let newHeight = this.manipulationStartHeight;\r\n\r\n    switch(this.activeGrip) {\r\n      case this.controlGrips.bottomLeft:\r\n      case this.controlGrips.centerLeft:\r\n      case this.controlGrips.topLeft:\r\n        newX = this.manipulationStartLeft + point.x - this.manipulationStartX;\r\n        newWidth = this.manipulationStartWidth + this.manipulationStartLeft - newX;\r\n        break; \r\n      case this.controlGrips.bottomRight:\r\n      case this.controlGrips.centerRight:\r\n      case this.controlGrips.topRight:\r\n      case undefined:\r\n        newWidth = this.manipulationStartWidth + point.x - this.manipulationStartX;\r\n        break; \r\n    }\r\n\r\n    switch(this.activeGrip) {\r\n      case this.controlGrips.topCenter:\r\n      case this.controlGrips.topLeft:\r\n      case this.controlGrips.topRight:\r\n        newY = this.manipulationStartTop + point.y - this.manipulationStartY;\r\n        newHeight = this.manipulationStartHeight + this.manipulationStartTop - newY;\r\n        break; \r\n      case this.controlGrips.bottomCenter:\r\n      case this.controlGrips.bottomLeft:\r\n      case this.controlGrips.bottomRight:\r\n      case undefined:\r\n        newHeight = this.manipulationStartHeight + point.y - this.manipulationStartY;\r\n        break; \r\n    }\r\n\r\n    if (newWidth >= 0) {\r\n      this.left = newX;\r\n      this.width = newWidth;\r\n    } else {\r\n      this.left = newX + newWidth;\r\n      this.width = -newWidth;\r\n    }\r\n    if (newHeight >= 0) {\r\n      this.top = newY;\r\n      this.height = newHeight;\r\n    } else {\r\n      this.top = newY + newHeight;\r\n      this.height = -newHeight;\r\n    }\r\n\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Sets control box size and location.\r\n   */\r\n  protected setSize(): void {\r\n    this.moveVisual({x: this.left, y: this.top});\r\n    this.adjustControlBox();\r\n  }\r\n\r\n  private rotate(point: IPoint) {\r\n    // avoid glitch when crossing the 0 rotation point\r\n    if (Math.abs(point.x - this.centerX) > 0.1) {\r\n      const sign = Math.sign(point.x - this.centerX);\r\n      this.rotationAngle =\r\n        (Math.atan((point.y - this.centerY) / (point.x - this.centerX)) * 180) /\r\n          Math.PI +\r\n        90 * sign;\r\n      this.applyRotation();\r\n    }\r\n  }\r\n\r\n  private applyRotation() {\r\n    const rotate = this.container.transform.baseVal.getItem(0);\r\n    rotate.setRotate(this.rotationAngle, this.centerX, this.centerY);\r\n    this.container.transform.baseVal.replaceItem(rotate, 0);\r\n  }\r\n\r\n  /**\r\n   * Returns point coordinates based on the actual screen coordinates and marker's rotation.\r\n   * @param point - original pointer coordinates\r\n   */\r\n  protected rotatePoint(point: IPoint): IPoint {\r\n    if (this.rotationAngle === 0) {\r\n      return point;\r\n    }\r\n    \r\n    const matrix = this.container.getCTM();\r\n    let svgPoint = SvgHelper.createPoint(point.x, point.y);\r\n    svgPoint = svgPoint.matrixTransform(matrix);\r\n\r\n    const result = { x: svgPoint.x, y: svgPoint.y };\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Returns original point coordinates based on coordinates with rotation applied.\r\n   * @param point - rotated point coordinates.\r\n   */\r\n  protected unrotatePoint(point: IPoint): IPoint {\r\n    if (this.rotationAngle === 0) {\r\n      return point;\r\n    }\r\n    \r\n    let matrix = this.container.getCTM();\r\n    matrix = matrix.inverse();\r\n    let svgPoint = SvgHelper.createPoint(point.x, point.y);\r\n    svgPoint = svgPoint.matrixTransform(matrix);\r\n\r\n    const result = { x: svgPoint.x, y: svgPoint.y };\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Displays marker's controls.\r\n   */\r\n  public select(): void {\r\n    super.select();\r\n    this.adjustControlBox();\r\n    this.controlBox.style.display = '';\r\n  }\r\n\r\n  /**\r\n   * Hides marker's controls.\r\n   */\r\n  public deselect(): void {\r\n    super.deselect();\r\n    this.controlBox.style.display = 'none';\r\n  }\r\n\r\n  private setupControlBox() {\r\n    this.controlBox = SvgHelper.createGroup();\r\n    const translate = SvgHelper.createTransform();\r\n    translate.setTranslate(-this.CB_DISTANCE / 2, -this.CB_DISTANCE / 2);\r\n    this.controlBox.transform.baseVal.appendItem(translate);\r\n\r\n    this.container.appendChild(this.controlBox);\r\n\r\n    this.controlRect = SvgHelper.createRect(\r\n      this.width + this.CB_DISTANCE,\r\n      this.height + this.CB_DISTANCE,\r\n      [\r\n        ['stroke', 'black'],\r\n        ['stroke-width', '1'],\r\n        ['stroke-opacity', '0.5'],\r\n        ['stroke-dasharray', '3, 2'],\r\n        ['fill', 'transparent'],\r\n        ['pointer-events', 'none']\r\n      ]\r\n    );\r\n\r\n    this.controlBox.appendChild(this.controlRect);\r\n\r\n    if (this.globalSettings.disableRotation !== true) {\r\n      this.rotatorGripLine = SvgHelper.createLine(\r\n        (this.width + this.CB_DISTANCE * 2) / 2,\r\n        this.top - this.CB_DISTANCE,\r\n        (this.width + this.CB_DISTANCE * 2) / 2,\r\n        this.top - this.CB_DISTANCE * 3,\r\n        [\r\n          ['stroke', 'black'],\r\n          ['stroke-width', '1'],\r\n          ['stroke-opacity', '0.5'],\r\n          ['stroke-dasharray', '3, 2'],\r\n        ]\r\n      );\r\n\r\n      this.controlBox.appendChild(this.rotatorGripLine);\r\n    }\r\n\r\n    this.controlGrips = new RectangularBoxMarkerGrips();\r\n    this.addControlGrips();\r\n\r\n    this.controlBox.style.display = 'none';\r\n  }\r\n\r\n  private adjustControlBox() {\r\n    const translate = this.controlBox.transform.baseVal.getItem(0);\r\n    translate.setTranslate(\r\n      this.left - this.CB_DISTANCE / 2,\r\n      this.top - this.CB_DISTANCE / 2\r\n    );\r\n    this.controlBox.transform.baseVal.replaceItem(translate, 0);\r\n    this.controlRect.setAttribute(\r\n      'width',\r\n      (this.width + this.CB_DISTANCE).toString()\r\n    );\r\n    this.controlRect.setAttribute(\r\n      'height',\r\n      (this.height + this.CB_DISTANCE).toString()\r\n    );\r\n\r\n    if (this.rotatorGripLine !== undefined) {\r\n      this.rotatorGripLine.setAttribute(\r\n        'x1',\r\n        ((this.width + this.CB_DISTANCE) / 2).toString()\r\n      );\r\n      this.rotatorGripLine.setAttribute('y1', (-this.CB_DISTANCE / 2).toString());\r\n      this.rotatorGripLine.setAttribute(\r\n        'x2',\r\n        ((this.width + this.CB_DISTANCE) / 2).toString()\r\n      );\r\n      this.rotatorGripLine.setAttribute('y2', (-this.CB_DISTANCE * 3).toString());\r\n    }\r\n    \r\n    this.positionGrips();\r\n  }\r\n\r\n  private addControlGrips() {\r\n    this.controlGrips.topLeft = this.createGrip();\r\n    this.controlGrips.topCenter = this.createGrip();\r\n    this.controlGrips.topRight = this.createGrip();\r\n    this.controlGrips.centerLeft = this.createGrip();\r\n    this.controlGrips.centerRight = this.createGrip();\r\n    this.controlGrips.bottomLeft = this.createGrip();\r\n    this.controlGrips.bottomCenter = this.createGrip();\r\n    this.controlGrips.bottomRight = this.createGrip();\r\n\r\n    if (this.globalSettings.disableRotation !== true) {\r\n      this.rotatorGrip = this.createGrip();\r\n    }\r\n\r\n    this.positionGrips();\r\n  }\r\n\r\n  private createGrip(): ResizeGrip {\r\n    const grip = new ResizeGrip();\r\n    grip.visual.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.controlBox.appendChild(grip.visual);\r\n\r\n    return grip;\r\n  }\r\n\r\n  private positionGrips() {\r\n    const gripSize = this.controlGrips.topLeft.GRIP_SIZE;\r\n\r\n    const left = -gripSize / 2;\r\n    const top = left;\r\n    const cx = (this.width + this.CB_DISTANCE) / 2 - gripSize / 2;\r\n    const cy = (this.height + this.CB_DISTANCE) / 2 - gripSize / 2;\r\n    const bottom = this.height + this.CB_DISTANCE - gripSize / 2;\r\n    const right = this.width + this.CB_DISTANCE - gripSize / 2;\r\n\r\n    this.positionGrip(this.controlGrips.topLeft.visual, left, top);\r\n    this.positionGrip(this.controlGrips.topCenter.visual, cx, top);\r\n    this.positionGrip(this.controlGrips.topRight.visual, right, top);\r\n    this.positionGrip(this.controlGrips.centerLeft.visual, left, cy);\r\n    this.positionGrip(this.controlGrips.centerRight.visual, right, cy);\r\n    this.positionGrip(this.controlGrips.bottomLeft.visual, left, bottom);\r\n    this.positionGrip(this.controlGrips.bottomCenter.visual, cx, bottom);\r\n    this.positionGrip(this.controlGrips.bottomRight.visual, right, bottom);\r\n\r\n    if (this.rotatorGrip !== undefined) {\r\n      this.positionGrip(this.rotatorGrip.visual, cx, top - this.CB_DISTANCE * 3);\r\n    }\r\n  }\r\n\r\n  private positionGrip(grip: SVGGraphicsElement, x: number, y: number) {\r\n    const translate = grip.transform.baseVal.getItem(0);\r\n    translate.setTranslate(x, y);\r\n    grip.transform.baseVal.replaceItem(translate, 0);\r\n  }\r\n\r\n  /**\r\n   * Hides marker's editing controls.\r\n   */\r\n  protected hideControlBox(): void {\r\n    this.controlBox.style.display = 'none';\r\n  }\r\n  /**\r\n   * Shows marker's editing controls.\r\n   */\r\n  protected showControlBox(): void {\r\n    this.controlBox.style.display = '';\r\n  }\r\n\r\n  /**\r\n   * Returns marker's state.\r\n   */\r\n  public getState(): RectangularBoxMarkerBaseState {\r\n    const result: RectangularBoxMarkerBaseState = Object.assign({\r\n      left: this.left,\r\n      top: this.top,\r\n      width: this.width,\r\n      height: this.height,\r\n      rotationAngle: this.rotationAngle,\r\n      visualTransformMatrix: TransformMatrix.toITransformMatrix(this.visual.transform.baseVal.getItem(0).matrix),\r\n      containerTransformMatrix: TransformMatrix.toITransformMatrix(this.container.transform.baseVal.getItem(0).matrix)\r\n    },\r\n    super.getState());\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores marker's state to the previously saved one.\r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n    const rbmState = state as RectangularBoxMarkerBaseState;\r\n    this.left = rbmState.left;\r\n    this.top = rbmState.top;\r\n    this.width = rbmState.width;\r\n    this.height = rbmState.height;\r\n    this.rotationAngle = rbmState.rotationAngle;\r\n    this.visual.transform.baseVal.getItem(0).setMatrix(\r\n      TransformMatrix.toSVGMatrix(this.visual.transform.baseVal.getItem(0).matrix, rbmState.visualTransformMatrix)\r\n    );\r\n    this.container.transform.baseVal.getItem(0).setMatrix(\r\n      TransformMatrix.toSVGMatrix(this.container.transform.baseVal.getItem(0).matrix, rbmState.containerTransformMatrix)\r\n    );\r\n    // this.moveVisual({x: this.left, y: this.top});\r\n    // this.applyRotation();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    const rPoint = this.rotatePoint({x: this.left, y: this.top});\r\n    const point = this.unrotatePoint({x: rPoint.x * scaleX, y: rPoint.y * scaleY});\r\n\r\n    this.left = point.x;\r\n    this.top = point.y;\r\n    this.width = this.width * scaleX;\r\n    this.height = this.height * scaleY;\r\n\r\n    this.adjustControlBox();\r\n  }\r\n\r\n}\r\n", "import { IPoint } from '../core/IPoint';\r\nimport { SvgHelper } from '../core/SvgHelper';\r\nimport { RectangularBoxMarkerBase } from './RectangularBoxMarkerBase';\r\nimport { Settings } from '../core/Settings';\r\nimport { RectangleMarkerState } from './RectangleMarkerState';\r\nimport { MarkerBaseState } from '../core/MarkerBaseState';\r\n\r\n/**\r\n * RecatngleMarker is a base class for all rectangular markers (Frame, Cover, Highlight, etc.)\r\n */\r\nexport abstract class RectangleMarker extends RectangularBoxMarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static title = 'Rectangle marker';\r\n\r\n  /**\r\n   * Recangle fill color.\r\n   */\r\n  protected fillColor = 'transparent';\r\n  /**\r\n   * Rectangle stroke color.\r\n   */\r\n  protected strokeColor = 'transparent';\r\n  /**\r\n   * Rectangle border stroke width.\r\n   */\r\n  protected strokeWidth = 0;\r\n  /**\r\n   * Rectangle border stroke dash array.\r\n   */\r\n  protected strokeDasharray = '';\r\n  /**\r\n   * Rectangle opacity (alpha). 0 to 1.\r\n   */\r\n  protected opacity = 1;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.setStrokeColor = this.setStrokeColor.bind(this);\r\n    this.setFillColor = this.setFillColor.bind(this);\r\n    this.setStrokeWidth = this.setStrokeWidth.bind(this);\r\n    this.setStrokeDasharray = this.setStrokeDasharray.bind(this);\r\n    this.createVisual = this.createVisual.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (super.ownsTarget(el) || el === this.visual) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates the marker's rectangle visual.\r\n   */\r\n  protected createVisual(): void {\r\n    this.visual = SvgHelper.createRect(1, 1, [\r\n      ['fill', this.fillColor],\r\n      ['stroke', this.strokeColor],\r\n      ['stroke-width', this.strokeWidth.toString()],\r\n      ['stroke-dasharray', this.strokeDasharray],\r\n      ['opacity', this.opacity.toString()]\r\n    ]);\r\n    this.addMarkerVisualToContainer(this.visual);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n\r\n      this.moveVisual(point);\r\n\r\n      this._state = 'creating';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    super.manipulate(point);\r\n  }\r\n\r\n  /**\r\n   * Resizes the marker based on the pointer coordinates.\r\n   * @param point - current pointer coordinates.\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Sets visual's width and height attributes based on marker's width and height.\r\n   */\r\n  protected setSize(): void {\r\n    super.setSize();\r\n    SvgHelper.setAttributes(this.visual, [\r\n      ['width', this.width.toString()],\r\n      ['height', this.height.toString()],\r\n    ]);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    super.pointerUp(point);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Sets rectangle's border stroke color.\r\n   * @param color - color as string\r\n   */\r\n  protected setStrokeColor(color: string): void {\r\n    this.strokeColor = color;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke', this.strokeColor]]);\r\n    }\r\n    this.colorChanged(color);\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets rectangle's fill color.\r\n   * @param color - color as string\r\n   */\r\n  protected setFillColor(color: string): void {\r\n    this.fillColor = color;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['fill', this.fillColor]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets rectangle's border stroke (line) width.\r\n   * @param color - color as string\r\n   */\r\n  protected setStrokeWidth(width: number): void {\r\n    this.strokeWidth = width;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke-width', this.strokeWidth.toString()]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets rectangle's border stroke dash array.\r\n   * @param color - color as string\r\n   */\r\n  protected setStrokeDasharray(dashes: string): void {\r\n    this.strokeDasharray = dashes;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke-dasharray', this.strokeDasharray]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result: RectangleMarkerState = Object.assign({\r\n      fillColor: this.fillColor,\r\n      strokeColor: this.strokeColor,\r\n      strokeWidth: this.strokeWidth,\r\n      strokeDasharray: this.strokeDasharray,\r\n      opacity: this.opacity\r\n    }, super.getState());\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    const rectState = state as RectangleMarkerState;\r\n    this.fillColor = rectState.fillColor;\r\n    this.strokeColor = rectState.strokeColor;\r\n    this.strokeWidth = rectState.strokeWidth;\r\n    this.strokeDasharray = rectState.strokeDasharray;\r\n    this.opacity = rectState.opacity;\r\n\r\n    this.createVisual();\r\n    super.restoreState(state);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.setSize();\r\n  }\r\n\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './line-width-panel-icon.svg';\r\n\r\n/**\r\n * Line width change event handler type.\r\n */\r\nexport type WidthChangeHandler = (newWidth: number) => void;\r\n\r\n/**\r\n * Line width toolbox panel.\r\n */\r\nexport class LineWidthPanel extends ToolboxPanel {\r\n  private widths: number[] = [];\r\n  private currentWidth?: number;\r\n\r\n  private widthBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Line width change event handler.\r\n   */\r\n  public onWidthChanged?: WidthChangeHandler;\r\n\r\n  /**\r\n   * Creates a line width toolbox panel.\r\n   * @param title - panel title.\r\n   * @param widths - available widths.\r\n   * @param currentWidth - currently set width.\r\n   * @param icon - toolbox panel icon (SVG image markup).\r\n   */\r\n  constructor(title: string, widths: number[], currentWidth?: number, icon?: string) {\r\n    super(title, icon ? icon : Icon);\r\n    this.widths = widths;\r\n    this.currentWidth = currentWidth;\r\n\r\n    this.setCurrentWidth = this.setCurrentWidth.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    panelDiv.style.display = 'flex';\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.flexGrow = '2';\r\n    this.widths.forEach((lineWidth) => {\r\n      const widthBoxContainer = document.createElement('div');\r\n      widthBoxContainer.style.display = 'flex';\r\n      widthBoxContainer.style.flexGrow = '2';\r\n      widthBoxContainer.style.alignItems = 'center';\r\n      widthBoxContainer.style.justifyContent = 'space-between';\r\n      widthBoxContainer.style.padding = '5px';\r\n      widthBoxContainer.style.borderWidth = '2px';\r\n      widthBoxContainer.style.borderStyle = 'solid';\r\n      widthBoxContainer.style.borderColor =\r\n        lineWidth === this.currentWidth ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n\r\n      widthBoxContainer.addEventListener('click', () => {\r\n        this.setCurrentWidth(lineWidth, widthBoxContainer);\r\n      })\r\n      panelDiv.appendChild(widthBoxContainer);\r\n\r\n      const label = document.createElement('div');\r\n      label.innerText = lineWidth.toString();\r\n      label.style.marginRight = '5px';\r\n      widthBoxContainer.appendChild(label);\r\n\r\n      const widthBox = document.createElement('div');\r\n      widthBox.style.minHeight = '20px';\r\n      widthBox.style.flexGrow = '2';\r\n      widthBox.style.display = 'flex';\r\n      widthBox.style.alignItems = 'center';\r\n\r\n      const hr = document.createElement('hr');\r\n      hr.style.minWidth = '20px';\r\n      hr.style.border = '0px';\r\n      hr.style.borderTop = `${lineWidth}px solid ${this.uiStyleSettings.toolboxColor}`;\r\n      hr.style.flexGrow = '2';\r\n      widthBox.appendChild(hr);\r\n\r\n      // widthBox.innerHTML = `<svg viewBox=\"0 0 140 20\" width=\"140\" height=\"20\" xmlns=\"http://www.w3.org/2000/svg\">\r\n      //   <line x1=\"0\" y1=\"10\" x2=\"140\" y2=\"10\" stroke=\"${this.uiStyleSettings.toolboxColor}\" stroke-width=\"${lineWidth}\" />\r\n      // </svg>`;\r\n\r\n      widthBoxContainer.appendChild(widthBox);\r\n\r\n      this.widthBoxes.push(widthBoxContainer);\r\n    });\r\n    return panelDiv;\r\n  }\r\n\r\n  private setCurrentWidth(newWidth: number, target: HTMLDivElement) {\r\n    this.currentWidth = newWidth;\r\n\r\n    this.widthBoxes.forEach(box => {\r\n      box.style.borderColor = box === target ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n    });\r\n\r\n    if (this.onWidthChanged) {\r\n      this.onWidthChanged(this.currentWidth);\r\n    }\r\n  }\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './line-style-panel-icon.svg';\r\n\r\n/**\r\n * Line style change event handler type.\r\n */\r\nexport type StyleChangeHandler = (newStyle: string) => void;\r\n\r\n/**\r\n * Line style (solid, dashed, etc.) toolbox panel.\r\n */\r\nexport class LineStylePanel extends ToolboxPanel {\r\n  private styles: string[] = [];\r\n  private currentStyle?: string;\r\n\r\n  private styleBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Handler for the style change event.\r\n   */\r\n  public onStyleChanged?: StyleChangeHandler;\r\n\r\n  /**\r\n   * Creates a line style toolbox panel.\r\n   * @param title - panel title\r\n   * @param styles - available line styles (dash array).\r\n   * @param currentStyle - currently selected style.\r\n   * @param icon - panel button icon (SVG image markup).\r\n   */\r\n  constructor(title: string, styles: string[], currentStyle?: string, icon?: string) {\r\n    super(title, icon ? icon : Icon);\r\n    this.styles = styles;\r\n    this.currentStyle = currentStyle;\r\n\r\n    this.setCurrentStyle = this.setCurrentStyle.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    panelDiv.style.display = 'flex';\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.flexGrow = '2';\r\n    this.styles.forEach((lineStyle) => {\r\n      const styleBoxContainer = document.createElement('div');\r\n      styleBoxContainer.style.display = 'flex'; //'inline-block';\r\n      styleBoxContainer.style.alignItems = 'center';\r\n      styleBoxContainer.style.justifyContent = 'space-between';\r\n      styleBoxContainer.style.padding = '5px';\r\n      styleBoxContainer.style.borderWidth = '2px';\r\n      styleBoxContainer.style.borderStyle = 'solid';\r\n      styleBoxContainer.style.overflow = 'hidden';\r\n      styleBoxContainer.style.maxWidth = `${100 / this.styles.length - 5}%`;\r\n      styleBoxContainer.style.borderColor =\r\n        lineStyle === this.currentStyle ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n\r\n      styleBoxContainer.addEventListener('click', () => {\r\n        this.setCurrentStyle(lineStyle, styleBoxContainer);\r\n      })\r\n      panelDiv.appendChild(styleBoxContainer);\r\n\r\n      const styleBox = document.createElement('div');\r\n      styleBox.style.minHeight = '20px';\r\n      styleBox.style.flexGrow = '2';\r\n      styleBox.style.overflow = 'hidden';\r\n\r\n      const styleSample = `<svg width=\"100\" height=\"20\">\r\n      <line x1=\"0\" y1=\"10\" x2=\"100\" y2=\"10\" stroke=\"${\r\n        this.uiStyleSettings.toolboxColor}\" stroke-width=\"3\" ${\r\n          lineStyle !== '' ? 'stroke-dasharray=\"' + lineStyle + '\"' : ''} />\r\n  </svg>`;\r\n\r\n      styleBox.innerHTML = styleSample;\r\n\r\n      styleBoxContainer.appendChild(styleBox);\r\n\r\n      this.styleBoxes.push(styleBoxContainer);\r\n    });\r\n    return panelDiv;\r\n  }\r\n\r\n  private setCurrentStyle(newStyle: string, target: HTMLDivElement) {\r\n    this.currentStyle = newStyle;\r\n\r\n    this.styleBoxes.forEach(box => {\r\n      box.style.borderColor = box === target ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n    });\r\n\r\n    if (this.onStyleChanged) {\r\n      this.onStyleChanged(this.currentStyle);\r\n    }\r\n  }\r\n}\r\n", "import Icon from './frame-marker-icon.svg';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { Settings } from '../../core/Settings';\r\nimport { RectangleMarker } from '../RectangleMarker';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\nimport { LineStylePanel } from '../../ui/toolbox-panels/LineStylePanel';\r\nimport { RectangleMarkerState } from '../RectangleMarkerState';\r\n\r\nexport class FrameMarker extends RectangleMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'FrameMarker';\r\n  \r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Frame marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  private strokePanel: ColorPickerPanel;\r\n  private strokeWidthPanel: LineWidthPanel;\r\n  private strokeStylePanel: LineStylePanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.strokeColor = settings.defaultColor;\r\n    this.strokeWidth = settings.defaultStrokeWidth;\r\n    this.strokeDasharray = settings.defaultStrokeDasharray;\r\n\r\n    this.strokePanel = new ColorPickerPanel(\r\n      'Line color',\r\n      settings.defaultColorSet,\r\n      settings.defaultColor\r\n    );\r\n    this.strokePanel.onColorChanged = this.setStrokeColor;\r\n\r\n    this.strokeWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.strokeWidthPanel.onWidthChanged = this.setStrokeWidth;\r\n\r\n    this.strokeStylePanel = new LineStylePanel(\r\n      'Line style',\r\n      settings.defaultStrokeDasharrays,\r\n      settings.defaultStrokeDasharray\r\n    );\r\n    this.strokeStylePanel.onStyleChanged = this.setStrokeDasharray;\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result = super.getState();\r\n    result.typeName = FrameMarker.typeName;\r\n    return result;\r\n  }\r\n}\r\n", "/**\r\n * Represents a list of colors.\r\n */\r\nexport type ColorSet = string[];\r\n\r\n/**\r\n * marker.js 2 display mode - `inline` or `popup`.\r\n */\r\nexport type DisplayMode = 'inline' | 'popup';\r\n\r\n/**\r\n * Default settings for marker.js 2 markers.\r\n */\r\nexport class Settings {\r\n  /**\r\n   * List of colors used in color pickers.\r\n   */\r\n  public defaultColorSet: ColorSet = [\r\n    '#EF4444', // red\r\n    '#10B981', // green\r\n    '#2563EB', // blue\r\n    '#FFFF00', // yellow\r\n    '#7C3AED', // purple\r\n    '#F472B6', // pink\r\n    '#000000', // black\r\n    '#FFFFFF' //white\r\n  ];\r\n\r\n  /**\r\n   * Default foreground color.\r\n   */\r\n  public defaultColor = this.defaultColorSet[0];\r\n  /**\r\n   * Default fill color.\r\n   */\r\n  public defaultFillColor = this.defaultColorSet[0];\r\n  /**\r\n   * Default stroke color for markers with background (eg. {@link CalloutMarker}).\r\n   */\r\n  public defaultStrokeColor = this.defaultColorSet[7];\r\n  /**\r\n   * Default highlighter color.\r\n   */\r\n  public defaultHighlightColor = this.defaultColorSet[3];\r\n  /**\r\n   * Default stroke (line) width.\r\n   */\r\n  public defaultStrokeWidth = 3;\r\n  /**\r\n   * Default line dash array\r\n   */\r\n  public defaultStrokeDasharray = '';\r\n  /**\r\n   * Default opacity (alpha) of the {@link HighlightMarker} (and other highlighters).\r\n   */\r\n  public defaultHighlightOpacity = 0.5;\r\n  /**\r\n   * Default font family for text-based markers (eg. {@link TextMarker} and {@link CalloutMarker}).\r\n   *\r\n   */\r\n  public defaultFontFamily = 'Helvetica, Arial, sans-serif';\r\n\r\n  /**\r\n   * Stroke (line) width options.\r\n   */\r\n  public defaultStrokeWidths = [1, 2, 3, 5, 10];\r\n  \r\n  /**\r\n   * Stroke dash array options.\r\n   */\r\n  public defaultStrokeDasharrays = ['', '3', '12 3', '9 6 3 6'];\r\n\r\n  /**\r\n   * Opacity options.\r\n   */\r\n  public defaultOpacitySteps = [0.1, 0.25, 0.5, 0.75, 1];\r\n\r\n  /**\r\n   * Default display mode.\r\n   */\r\n  public displayMode: DisplayMode = 'inline';\r\n\r\n  /**\r\n   * Font family options.\r\n   */\r\n  public defaultFontFamilies = [\r\n    'Times, \"Times New Roman\", serif',\r\n    'Helvetica, Arial, sans-serif',\r\n    'Courier, \"Courier New\", monospace',\r\n    'cursive',\r\n    'fantasy'\r\n  ];\r\n\r\n  /**\r\n   * Margin in pixels between marker.js popup UI and window borders.\r\n   */\r\n  public popupMargin = 30;\r\n\r\n  /**\r\n   * Create a new Freehand marker for every stroke.\r\n   */\r\n  public newFreehandMarkerOnPointerUp = false;\r\n\r\n  /**\r\n   * If set to true, when colors on a marker are changed \r\n   * it changes the default color for other markers as well.\r\n   * \r\n   * @since 2.7.0\r\n   */\r\n  public defaultColorsFollowCurrentColors = false;\r\n\r\n  /**\r\n   * Increase this setting for smoother FreehandMarker lines.\r\n   * Note that it will also take more space when you save the state.\r\n   *\r\n   * @since 2.20.0\r\n   */\r\n  public freehandPixelRatio = 1;\r\n\r\n  /**\r\n   * When set to true rotation feature is disabled on markers.\r\n   * This doesn't affect markers restored from a previously saved state.\r\n   * \r\n   * @since 2.22.0\r\n   */\r\n  public disableRotation = false;\r\n\r\n  /**\r\n   * If set, the UI will be offset by the specified value, \r\n   * otherwise it will be offset by -toolbarHeight or 0 if \r\n   * there's less space than toolbarHeight on top.\r\n   * \r\n   * Use this if you want to control the position inside a\r\n   * `position: relative` parent, as auto-calculation\r\n   * will calculate available space from the relative\r\n   * container and not the whole page.\r\n   * \r\n   * Common usage when used with a relatively positioned parent would be:\r\n   * \r\n   * ```typescript\r\n   * markerArea.targetRoot = document.getElementById('relativeParent');\r\n   * markerArea.settings.uiOffsetTop = -markerArea.styles.settings.toolbarHeight;\r\n   * ```\r\n   * This would ensure that the toolbar is placed above the image\r\n   * even if the image's offset from the relative parent is 0.\r\n   * \r\n   * @since 2.28.0\r\n   */\r\n   public uiOffsetTop?: number;\r\n   \r\n   /**\r\n    * If set, the UI will be offset by the specefied number of pixels on the left.\r\n    * \r\n    * @since 2.31.0\r\n    */\r\n   public uiOffsetLeft?: number;\r\n\r\n   /**\r\n    * Default font size for the `CaptionFrameMarker`\r\n    * \r\n    * @since 2.29.0\r\n    */\r\n   public defaultCaptionFontSize = '1rem';\r\n   /**\r\n    * Default caption text for the `CaptionFrameMarker`\r\n    * \r\n    * @since 2.29.0\r\n    */\r\n   public defaultCaptionText = 'Text';\r\n   /**\r\n    * Enable word wrapping in text markers (`TextMarker`, `CalloutMarker`)\r\n    * \r\n    * @since 2.30.0\r\n    */\r\n   public wrapText = false;\r\n}\r\n", "import { MarkerBase } from '../core/MarkerBase';\r\n\r\nimport { IPoint } from '../core/IPoint';\r\nimport { SvgHelper } from '../core/SvgHelper';\r\n\r\nimport { ResizeGrip } from './ResizeGrip';\r\nimport { Settings } from '../core/Settings';\r\nimport { LinearMarkerBaseState } from './LinearMarkerBaseState';\r\nimport { MarkerBaseState } from '../core/MarkerBaseState';\r\n\r\n/**\r\n * LinearMarkerBase is a base class for all line-type markers (Line, Arrow, Measurement Tool, etc.).\r\n */\r\nexport class LinearMarkerBase extends MarkerBase {\r\n  /**\r\n   * x coordinate of the first end-point\r\n   */\r\n  protected x1 = 0;\r\n  /**\r\n   * y coordinate of the first end-point\r\n   */\r\n  protected y1 = 0;\r\n  /**\r\n   * x coordinate of the second end-point\r\n   */\r\n  protected x2 = 0;\r\n  /**\r\n   * y coordinate of the second end-point\r\n   */\r\n  protected y2 = 0;\r\n\r\n  /**\r\n   * Default line length when marker is created with a simple click (without dragging).\r\n   */\r\n  protected defaultLength = 50;\r\n\r\n  /**\r\n   * Pointer coordinates at the satart of move or resize.\r\n   */\r\n  protected manipulationStartX = 0;\r\n  protected manipulationStartY = 0;\r\n\r\n  private manipulationStartX1 = 0;\r\n  private manipulationStartY1 = 0;\r\n  private manipulationStartX2 = 0;\r\n  private manipulationStartY2 = 0;\r\n\r\n  /**\r\n   * Marker's main visual.\r\n   */\r\n  protected visual: SVGGraphicsElement;\r\n\r\n  /**\r\n   * Container for control elements.\r\n   */\r\n  protected controlBox: SVGGElement;\r\n\r\n  /**\r\n   * First manipulation grip\r\n   */\r\n  protected grip1: ResizeGrip;\r\n  /**\r\n   * Second manipulation grip.\r\n   */\r\n  protected grip2: ResizeGrip;\r\n  /**\r\n   * Active manipulation grip.\r\n   */\r\n  protected activeGrip: ResizeGrip;\r\n\r\n  /**\r\n   * Creates a LineMarkerBase object.\r\n   * \r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.setupControlBox();\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (super.ownsTarget(el)) {\r\n      return true;\r\n    } else if (\r\n      this.grip1.ownsTarget(el) || this.grip2.ownsTarget(el)\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  \r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n\r\n    this.manipulationStartX = point.x;\r\n    this.manipulationStartY = point.y;\r\n\r\n    if (this.state === 'new') {\r\n      this.x1 = point.x;\r\n      this.y1 = point.y;\r\n      this.x2 = point.x;\r\n      this.y2 = point.y;\r\n    }\r\n\r\n    this.manipulationStartX1 = this.x1;\r\n    this.manipulationStartY1 = this.y1;\r\n    this.manipulationStartX2 = this.x2;\r\n    this.manipulationStartY2 = this.y2;\r\n\r\n    if (this.state !== 'new') {\r\n      this.select();\r\n      if (this.grip1.ownsTarget(target)) {\r\n        this.activeGrip = this.grip1;\r\n      } else if (this.grip2.ownsTarget(target)) {\r\n        this.activeGrip = this.grip2;\r\n      } else {\r\n        this.activeGrip = undefined;\r\n      }\r\n\r\n      if (this.activeGrip) {\r\n        this._state = 'resize';\r\n      } else {\r\n        this._state = 'move';\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    const inState = this.state;\r\n    super.pointerUp(point);\r\n    if (this.state === 'creating' && Math.abs(this.x1 - this.x2) < 10 && Math.abs(this.y1 - this.y2) < 10) {\r\n      this.x2 = this.x1 + this.defaultLength;\r\n      this.adjustVisual();\r\n      this.adjustControlBox()\r\n    } else {\r\n      this.manipulate(point);\r\n    }\r\n    this._state = 'select';\r\n    if (inState === 'creating' && this.onMarkerCreated) {\r\n      this.onMarkerCreated(this);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * When implemented adjusts marker visual after manipulation when needed.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\r\n  protected adjustVisual(): void {}\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    if (this.state === 'creating') {\r\n      this.resize(point);\r\n    } else if (this.state === 'move') {\r\n      this.x1 = this.manipulationStartX1 + point.x - this.manipulationStartX;\r\n      this.y1 = this.manipulationStartY1 + point.y - this.manipulationStartY;\r\n      this.x2 = this.manipulationStartX2 + point.x - this.manipulationStartX;\r\n      this.y2 = this.manipulationStartY2 + point.y - this.manipulationStartY;\r\n      this.adjustVisual();\r\n      this.adjustControlBox();\r\n    } else if (this.state === 'resize') {\r\n      this.resize(point);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resizes the line marker.\r\n   * @param point - current manipulation coordinates.\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    switch(this.activeGrip) {\r\n      case this.grip1:\r\n        this.x1 = point.x;\r\n        this.y1 = point.y;\r\n        break; \r\n      case this.grip2:\r\n      case undefined:\r\n        this.x2 = point.x;\r\n        this.y2 = point.y;\r\n        break; \r\n    }\r\n    this.adjustVisual();\r\n    this.adjustControlBox();\r\n  }\r\n\r\n  /**\r\n   * Displays marker's controls.\r\n   */\r\n  public select(): void {\r\n    super.select();\r\n    this.adjustControlBox();\r\n    this.controlBox.style.display = '';\r\n  }\r\n\r\n  /**\r\n   * Hides marker's controls.\r\n   */\r\n  public deselect(): void {\r\n    super.deselect();\r\n    this.controlBox.style.display = 'none';\r\n  }\r\n\r\n  /**\r\n   * Creates control box for manipulation controls.\r\n   */\r\n  protected setupControlBox(): void {\r\n    this.controlBox = SvgHelper.createGroup();\r\n    this.container.appendChild(this.controlBox);\r\n\r\n    this.addControlGrips();\r\n\r\n    this.controlBox.style.display = 'none';\r\n  }\r\n\r\n  private adjustControlBox() {\r\n    this.positionGrips();\r\n  }\r\n\r\n  /**\r\n   * Adds control grips to control box.\r\n   */\r\n  protected addControlGrips(): void {\r\n    this.grip1 = this.createGrip();\r\n    this.grip2 = this.createGrip();\r\n\r\n    this.positionGrips();\r\n  }\r\n\r\n  /**\r\n   * Creates manipulation grip.\r\n   * @returns - manipulation grip.\r\n   */\r\n  protected createGrip(): ResizeGrip {\r\n    const grip = new ResizeGrip();\r\n    grip.visual.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.controlBox.appendChild(grip.visual);\r\n\r\n    return grip;\r\n  }\r\n\r\n  /**\r\n   * Updates manipulation grip layout.\r\n   */\r\n  protected positionGrips(): void {\r\n    const gripSize = this.grip1.GRIP_SIZE;\r\n\r\n    this.positionGrip(this.grip1.visual, this.x1 - gripSize / 2, this.y1 - gripSize / 2);\r\n    this.positionGrip(this.grip2.visual, this.x2 - gripSize / 2, this.y2 - gripSize / 2);\r\n  }\r\n\r\n  /**\r\n   * Positions manipulation grip.\r\n   * @param grip - grip to position\r\n   * @param x - new X coordinate\r\n   * @param y - new Y coordinate\r\n   */\r\n  protected positionGrip(grip: SVGGraphicsElement, x: number, y: number): void {\r\n    const translate = grip.transform.baseVal.getItem(0);\r\n    translate.setTranslate(x, y);\r\n    grip.transform.baseVal.replaceItem(translate, 0);\r\n  }\r\n\r\n  /**\r\n   * Returns marker's state.\r\n   */\r\n  public getState(): LinearMarkerBaseState {\r\n    const result: LinearMarkerBaseState = Object.assign({\r\n      x1: this.x1,\r\n      y1: this.y1,\r\n      x2: this.x2,\r\n      y2: this.y2\r\n    }, super.getState());\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores marker's state to the previously saved one.\r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n    const lmbState = state as LinearMarkerBaseState;\r\n    this.x1 = lmbState.x1;\r\n    this.y1 = lmbState.y1;\r\n    this.x2 = lmbState.x2;\r\n    this.y2 = lmbState.y2;\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.x1 = this.x1 * scaleX;\r\n    this.y1 = this.y1 * scaleY;\r\n    this.x2 = this.x2 * scaleX;\r\n    this.y2 = this.y2 * scaleY;\r\n\r\n    this.adjustVisual();\r\n    this.adjustControlBox();\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { Settings } from '../../core/Settings';\r\nimport { LinearMarkerBase } from '../LinearMarkerBase';\r\nimport Icon from './line-marker-icon.svg';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\nimport { LineStylePanel } from '../../ui/toolbox-panels/LineStylePanel';\r\nimport { LineMarkerState } from './LineMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\n\r\nexport class LineMarker extends LinearMarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'LineMarker';\r\n  \r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Line marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Invisible wider line to make selection easier/possible.\r\n   */\r\n  protected selectorLine: SVGLineElement;\r\n  /**\r\n   * Visible marker line.\r\n   */\r\n  protected visibleLine: SVGLineElement;\r\n\r\n  /**\r\n   * Line color.\r\n   */\r\n  protected strokeColor = 'transparent';\r\n  /**\r\n   * Line width.\r\n   */\r\n  protected strokeWidth = 0;\r\n  /**\r\n   * Line dash array.\r\n   */\r\n  protected strokeDasharray = '';\r\n\r\n  /**\r\n   * Color pickar panel for line color.\r\n   */\r\n  protected strokePanel: ColorPickerPanel;\r\n  /**\r\n   * Line width toolbox panel.\r\n   */\r\n  protected strokeWidthPanel: LineWidthPanel;\r\n  /**\r\n   * Line dash array toolbox panel.\r\n   */\r\n  protected strokeStylePanel: LineStylePanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.setStrokeColor = this.setStrokeColor.bind(this);\r\n    this.setStrokeWidth = this.setStrokeWidth.bind(this);\r\n    this.setStrokeDasharray = this.setStrokeDasharray.bind(this);\r\n\r\n    this.strokeColor = settings.defaultColor;\r\n    this.strokeWidth = settings.defaultStrokeWidth;\r\n    this.strokeDasharray = settings.defaultStrokeDasharray;\r\n\r\n    this.strokePanel = new ColorPickerPanel(\r\n      'Line color',\r\n      settings.defaultColorSet,\r\n      settings.defaultColor\r\n    );\r\n    this.strokePanel.onColorChanged = this.setStrokeColor;\r\n\r\n    this.strokeWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.strokeWidthPanel.onWidthChanged = this.setStrokeWidth;\r\n\r\n    this.strokeStylePanel = new LineStylePanel(\r\n      'Line style',\r\n      settings.defaultStrokeDasharrays,\r\n      settings.defaultStrokeDasharray\r\n    );\r\n    this.strokeStylePanel.onStyleChanged = this.setStrokeDasharray;\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.visual ||\r\n      el === this.selectorLine ||\r\n      el === this.visibleLine\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private createVisual() {\r\n    this.visual = SvgHelper.createGroup();\r\n    this.selectorLine = SvgHelper.createLine(\r\n      this.x1,\r\n      this.y1,\r\n      this.x2,\r\n      this.y2,\r\n      [\r\n        ['stroke', 'transparent'],\r\n        ['stroke-width', (this.strokeWidth + 10).toString()],\r\n      ]\r\n    );\r\n    this.visibleLine = SvgHelper.createLine(\r\n      this.x1,\r\n      this.y1,\r\n      this.x2,\r\n      this.y2,\r\n      [\r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()],\r\n      ]\r\n    );\r\n    this.visual.appendChild(this.selectorLine);\r\n    this.visual.appendChild(this.visibleLine);\r\n\r\n    this.addMarkerVisualToContainer(this.visual);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n      this.adjustVisual();\r\n\r\n      this._state = 'creating';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adjusts visual after manipulation.\r\n   */\r\n  protected adjustVisual(): void {\r\n    if (this.selectorLine && this.visibleLine) {\r\n      this.selectorLine.setAttribute('x1', this.x1.toString());\r\n      this.selectorLine.setAttribute('y1', this.y1.toString());\r\n      this.selectorLine.setAttribute('x2', this.x2.toString());\r\n      this.selectorLine.setAttribute('y2', this.y2.toString());\r\n\r\n      this.visibleLine.setAttribute('x1', this.x1.toString());\r\n      this.visibleLine.setAttribute('y1', this.y1.toString());\r\n      this.visibleLine.setAttribute('x2', this.x2.toString());\r\n      this.visibleLine.setAttribute('y2', this.y2.toString());\r\n\r\n      SvgHelper.setAttributes(this.visibleLine, [['stroke', this.strokeColor]]);\r\n      SvgHelper.setAttributes(this.visibleLine, [['stroke-width', this.strokeWidth.toString()]]);\r\n      SvgHelper.setAttributes(this.visibleLine, [['stroke-dasharray', this.strokeDasharray.toString()]]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets line color.\r\n   * @param color - new color.\r\n   */\r\n  protected setStrokeColor(color: string): void {\r\n    this.strokeColor = color;\r\n    this.adjustVisual();\r\n    this.colorChanged(color);\r\n  }\r\n  /**\r\n   * Sets line width.\r\n   * @param width - new width.\r\n   */\r\n  protected setStrokeWidth(width: number): void {\r\n    this.strokeWidth = width\r\n    this.adjustVisual();\r\n  }\r\n\r\n  /**\r\n   * Sets line dash array.\r\n   * @param dashes - new dash array.\r\n   */\r\n  protected setStrokeDasharray(dashes: string): void {\r\n    this.strokeDasharray = dashes;\r\n    this.adjustVisual();\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): LineMarkerState {\r\n    const result: LineMarkerState = Object.assign({\r\n      strokeColor: this.strokeColor,\r\n      strokeWidth: this.strokeWidth,\r\n      strokeDasharray: this.strokeDasharray\r\n    }, super.getState());\r\n    result.typeName = LineMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n\r\n    const lmState = state as LineMarkerState;\r\n    this.strokeColor = lmState.strokeColor;\r\n    this.strokeWidth = lmState.strokeWidth;\r\n    this.strokeDasharray = lmState.strokeDasharray;\r\n\r\n    this.createVisual();\r\n    this.adjustVisual();\r\n  }\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './font-family-panel-icon.svg';\r\n\r\n/**\r\n * Font change event handler type.\r\n */\r\nexport type FontChangeHandler = (newFont: string) => void;\r\n\r\n/**\r\n * Font family selection toolbox panel.\r\n */\r\nexport class FontFamilyPanel extends ToolboxPanel {\r\n  private fonts: string[] = [];\r\n  private currentFont?: string;\r\n\r\n  private fontBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Handler for the font family change event.\r\n   */\r\n  public onFontChanged?: FontChangeHandler;\r\n\r\n  /**\r\n   * Creates a font family toolbox panel.\r\n   * @param title - panel title.\r\n   * @param fonts - available font families.\r\n   * @param currentFont - currently selected font family.\r\n   * @param icon - panel button icon (SVG image markup).\r\n   */\r\n  constructor(title: string, fonts: string[], currentFont?: string, icon?: string) {\r\n    super(title, icon ? icon : Icon);\r\n    this.fonts = fonts;\r\n    this.currentFont = currentFont;\r\n\r\n    this.setCurrentFont = this.setCurrentFont.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    // panelDiv.style.display = 'flex';\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.flexGrow = '2';\r\n    this.fonts.forEach((font) => {\r\n      const fontBoxContainer = document.createElement('div');\r\n      fontBoxContainer.style.display = 'inline-block';\r\n      // fontBoxContainer.style.flexGrow = '2';\r\n      fontBoxContainer.style.alignItems = 'center';\r\n      fontBoxContainer.style.justifyContent = 'space-between';\r\n      fontBoxContainer.style.padding = '5px';\r\n      fontBoxContainer.style.borderWidth = '2px';\r\n      fontBoxContainer.style.borderStyle = 'solid';\r\n      fontBoxContainer.style.overflow = 'hidden';\r\n      fontBoxContainer.style.maxWidth = `${100 / this.fonts.length - 5}%`;\r\n      fontBoxContainer.style.borderColor =\r\n        font === this.currentFont ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n\r\n      fontBoxContainer.addEventListener('click', () => {\r\n        this.setCurrentFont(font, fontBoxContainer);\r\n      })\r\n      panelDiv.appendChild(fontBoxContainer);\r\n\r\n      const fontBox = document.createElement('div');\r\n      fontBox.style.display = 'flex';\r\n      fontBox.style.minHeight = '20px';\r\n      fontBox.style.flexGrow = '2';\r\n      fontBox.style.fontFamily = font;\r\n      fontBox.style.overflow = 'hidden';\r\n\r\n      const fontLabel = document.createElement('div');\r\n      fontLabel.style.whiteSpace = 'nowrap';\r\n      fontLabel.style.overflow = 'hidden';\r\n      fontLabel.style.textOverflow = 'ellipsis';\r\n      fontLabel.innerHTML = 'The quick brown fox jumps over the lazy dog';\r\n\r\n      fontBox.appendChild(fontLabel);\r\n\r\n      fontBoxContainer.appendChild(fontBox);\r\n\r\n      this.fontBoxes.push(fontBoxContainer);\r\n    });\r\n    return panelDiv;\r\n  }\r\n\r\n  private setCurrentFont(newFont: string, target: HTMLDivElement) {\r\n    this.currentFont = newFont;\r\n\r\n    this.fontBoxes.forEach(box => {\r\n      box.style.borderColor = box === target ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n    });\r\n\r\n    if (this.onFontChanged) {\r\n      this.onFontChanged(this.currentFont);\r\n    }\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { RectangularBoxMarkerBase } from '../RectangularBoxMarkerBase';\r\nimport { Settings } from '../../core/Settings';\r\nimport Icon from './text-marker-icon.svg';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { FontFamilyPanel } from '../../ui/toolbox-panels/FontFamilyPanel';\r\nimport { TextMarkerState } from './TextMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\n\r\nexport class TextMarker extends RectangularBoxMarkerBase {\r\n  /**\r\n   * String type name of the marker type.\r\n   *\r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'TextMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Text marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Text color.\r\n   */\r\n  protected color = 'transparent';\r\n  /**\r\n   * Text's font family.\r\n   */\r\n  protected fontFamily: string;\r\n  /**\r\n   * Padding inside of the marker's bounding box in percents.\r\n   */\r\n  protected padding = 5;\r\n\r\n  /**\r\n   * Text color picker toolbox panel.\r\n   */\r\n  protected colorPanel: ColorPickerPanel;\r\n  /**\r\n   * Text font family toolbox panel.\r\n   */\r\n  protected fontFamilyPanel: FontFamilyPanel;\r\n\r\n  private readonly DEFAULT_TEXT = 'your text here';\r\n  private text: string = this.DEFAULT_TEXT;\r\n  /**\r\n   * Visual text element.\r\n   */\r\n  protected textElement: SVGTextElement;\r\n  /**\r\n   * Text background rectangle.\r\n   */\r\n  protected bgRectangle: SVGRectElement;\r\n  /**\r\n   * Div element for the text editor container.\r\n   */\r\n  protected textEditDiv: HTMLDivElement;\r\n  /**\r\n   * Editable text element.\r\n   */\r\n  protected textEditor: HTMLDivElement;\r\n\r\n  protected isMoved = false;\r\n  private pointerDownPoint: IPoint;\r\n  private pointerDownTimestamp: number;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(\r\n    container: SVGGElement,\r\n    overlayContainer: HTMLDivElement,\r\n    settings: Settings\r\n  ) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.color = settings.defaultColor;\r\n    this.fontFamily = settings.defaultFontFamily;\r\n\r\n    this.defaultSize = { x: 100, y: 30 };\r\n\r\n    this.setColor = this.setColor.bind(this);\r\n    this.setFont = this.setFont.bind(this);\r\n    this.renderText = this.renderText.bind(this);\r\n    this.sizeText = this.sizeText.bind(this);\r\n    this.textEditDivClicked = this.textEditDivClicked.bind(this);\r\n    this.showTextEditor = this.showTextEditor.bind(this);\r\n    this.setSize = this.setSize.bind(this);\r\n    this.positionTextEditor = this.positionTextEditor.bind(this);\r\n    this.wrapText = this.wrapText.bind(this);\r\n\r\n    this.colorPanel = new ColorPickerPanel(\r\n      'Color',\r\n      settings.defaultColorSet,\r\n      settings.defaultColor\r\n    );\r\n    this.colorPanel.onColorChanged = this.setColor;\r\n\r\n    this.fontFamilyPanel = new FontFamilyPanel(\r\n      'Font',\r\n      settings.defaultFontFamilies,\r\n      settings.defaultFontFamily\r\n    );\r\n    this.fontFamilyPanel.onFontChanged = this.setFont;\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   *\r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.visual ||\r\n      el === this.textElement ||\r\n      el === this.bgRectangle\r\n    ) {\r\n      return true;\r\n    } else {\r\n      let found = false;\r\n      this.textElement.childNodes.forEach((span) => {\r\n        if (span === el) {\r\n          found = true;\r\n        }\r\n      });\r\n      return found;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates text marker visual.\r\n   */\r\n  protected createVisual(): void {\r\n    this.visual = SvgHelper.createGroup();\r\n\r\n    this.bgRectangle = SvgHelper.createRect(1, 1, [['fill', 'transparent']]);\r\n    this.visual.appendChild(this.bgRectangle);\r\n\r\n    this.textElement = SvgHelper.createText([\r\n      ['fill', this.color],\r\n      ['font-family', this.fontFamily],\r\n      ['font-size', '16px'],\r\n      ['x', '0'],\r\n      ['y', '0'],\r\n    ]);\r\n    this.textElement.transform.baseVal.appendItem(SvgHelper.createTransform()); // translate transorm\r\n    this.textElement.transform.baseVal.appendItem(SvgHelper.createTransform()); // scale transorm\r\n\r\n    this.visual.appendChild(this.textElement);\r\n\r\n    this.addMarkerVisualToContainer(this.visual);\r\n    this.renderText();\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   *\r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n\r\n    this.isMoved = false;\r\n    this.pointerDownPoint = point;\r\n    this.pointerDownTimestamp = Date.now();\r\n\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n      this.moveVisual(point);\r\n      this._state = 'creating';\r\n    }\r\n  }\r\n\r\n  private wrapText(): string {\r\n    function getTextAspectRatio(textLines: string[]): number {\r\n      const charsLinesAspectRatio = 0.35;\r\n\r\n      let longestLineChars = textLines[0].length;\r\n      textLines.forEach(line => {\r\n        if (line.length > longestLineChars) {\r\n          longestLineChars = line.length;\r\n        }\r\n      });\r\n\r\n      return longestLineChars * charsLinesAspectRatio / textLines.length;\r\n    }\r\n\r\n    if (this.text !== '') {\r\n      const lines = this.text.split(/\\r\\n|[\\n\\v\\f\\r\\x85\\u2028\\u2029]/);\r\n      const boxAspectRatio = this.width * 1.0 / this.height;\r\n      let processedLines = new Array<string>(...lines);\r\n      \r\n      let textAspectRatio = getTextAspectRatio(processedLines);\r\n\r\n      let maxLineLength = Number.MAX_VALUE;\r\n      while (textAspectRatio > boxAspectRatio) {\r\n        let longestLine = processedLines[0];\r\n        processedLines.forEach(line => {\r\n          if (line.length > longestLine.length) {\r\n            longestLine = line;\r\n          }\r\n        });\r\n        maxLineLength = longestLine.lastIndexOf(' ', maxLineLength - 1);\r\n\r\n        if (maxLineLength > 0) {\r\n          processedLines = [];\r\n          lines.forEach(line => {\r\n            let reminderLine = line;\r\n            while (reminderLine.length > maxLineLength) {\r\n              let maxEnd = reminderLine.lastIndexOf(' ', maxLineLength);\r\n              if (maxEnd < 0) {\r\n                // if the first word is longer than max, at least wrap after it\r\n                maxEnd = reminderLine.indexOf(' ');\r\n              }\r\n              if (maxEnd > 0) {\r\n                processedLines.push(reminderLine.substring(0, maxEnd));\r\n                reminderLine = reminderLine.substring(maxEnd).trim();\r\n              } else {\r\n                processedLines.push(reminderLine);\r\n                reminderLine = '';\r\n              }\r\n            }\r\n            processedLines.push(reminderLine);\r\n          });\r\n          textAspectRatio = getTextAspectRatio(processedLines);\r\n        } else {\r\n          // can't wrap no more\r\n          textAspectRatio = -1;\r\n        }\r\n      }\r\n\r\n      return processedLines.join(`\\r\\n`);\r\n    } else {\r\n      return this.text;\r\n    }\r\n  }\r\n\r\n  private renderText() {\r\n    const LINE_SIZE = '1.2em';\r\n\r\n    if (this.textElement) {\r\n      while (this.textElement.lastChild) {\r\n        this.textElement.removeChild(this.textElement.lastChild);\r\n      }\r\n\r\n      const processedText = this.globalSettings.wrapText ? this.wrapText() : this.text;\r\n      const lines = processedText.split(/\\r\\n|[\\n\\v\\f\\r\\x85\\u2028\\u2029]/);\r\n      lines.forEach((line) => {\r\n        this.textElement.appendChild(\r\n          SvgHelper.createTSpan(\r\n            // workaround for swallowed empty lines\r\n            line.trim() === '' ? ' ' : line.trim(), [\r\n            ['x', '0'],\r\n            ['dy', LINE_SIZE],\r\n          ])\r\n        );\r\n      });\r\n\r\n      setTimeout(this.sizeText, 10);\r\n    }\r\n  }\r\n\r\n  private getTextScale(): number {\r\n    const textSize = this.textElement.getBBox();\r\n    let scale = 1.0;\r\n    if (textSize.width > 0 && textSize.height > 0) {\r\n      const xScale =\r\n        (this.width * 1.0 - (this.width * this.padding * 2) / 100) /\r\n        textSize.width;\r\n      const yScale =\r\n        (this.height * 1.0 - (this.height * this.padding * 2) / 100) /\r\n        textSize.height;\r\n      scale = Math.min(xScale, yScale);\r\n    }\r\n    return scale;\r\n  }\r\n\r\n  private getTextPosition(scale: number): IPoint {\r\n    const xSign = window.getComputedStyle(this.textElement).direction === 'rtl' ? 1 : -1;\r\n    const textSize = this.textElement.getBBox();\r\n    let x = 0;\r\n    let y = 0;\r\n    if (textSize.width > 0 && textSize.height > 0) {\r\n      x = (this.width + xSign * textSize.width * scale) / 2;\r\n      y = this.height / 2 - (textSize.height * scale) / 2;\r\n    }\r\n    return { x: x, y: y };\r\n  }\r\n\r\n  private sizeText() {\r\n    const textBBox = this.textElement.getBBox();\r\n    const scale = this.getTextScale();\r\n    const position = this.getTextPosition(scale);\r\n    position.y -= textBBox.y * scale; // workaround adjustment for text not being placed at y=0\r\n\r\n    if (navigator.userAgent.indexOf('Edge/') > -1) {\r\n      // workaround for legacy Edge as transforms don't work otherwise but this way it doesn't work in Safari\r\n      this.textElement.style.transform = `translate(${position.x}px, ${position.y}px) scale(${scale}, ${scale})`;\r\n    } else {\r\n      this.textElement.transform.baseVal\r\n        .getItem(0)\r\n        .setTranslate(position.x, position.y);\r\n      this.textElement.transform.baseVal.getItem(1).setScale(scale, scale);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   *\r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    super.manipulate(point);\r\n    if (this.pointerDownPoint !== undefined) {\r\n      this.isMoved =\r\n        Math.abs(point.x - this.pointerDownPoint.x) > 5 ||\r\n        Math.abs(point.y - this.pointerDownPoint.y) > 5;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resize marker based on current pointer coordinates and context.\r\n   * @param point\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    this.isMoved = true;\r\n    this.setSize();\r\n    if (this.globalSettings.wrapText) {\r\n      this.renderText();\r\n    } else {\r\n      this.sizeText();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets size of marker elements after manipulation.\r\n   */\r\n  protected setSize(): void {\r\n    super.setSize();\r\n    if (this.visual && this.bgRectangle) {\r\n      SvgHelper.setAttributes(this.visual, [\r\n        ['width', this.width.toString()],\r\n        ['height', this.height.toString()],\r\n      ]);\r\n      SvgHelper.setAttributes(this.bgRectangle, [\r\n        ['width', this.width.toString()],\r\n        ['height', this.height.toString()],\r\n      ]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   *\r\n   * @param point - event coordinates.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    const inState = this.state;\r\n    if (inState === 'creating') {\r\n      this._suppressMarkerCreateEvent = true;\r\n    }\r\n    super.pointerUp(point);\r\n    this.setSize();\r\n    if (\r\n      inState === 'creating' ||\r\n      (!this.isMoved && Date.now() - this.pointerDownTimestamp > 500)\r\n    ) {\r\n      this.showTextEditor();\r\n    }\r\n    this.pointerDownPoint = undefined;\r\n  }\r\n\r\n  private showTextEditor() {\r\n    this._state = 'edit';\r\n    this.overlayContainer.innerHTML = '';\r\n\r\n    this.textEditDiv = document.createElement('div');\r\n    // textEditDiv.style.display = 'flex';\r\n    this.textEditDiv.style.flexGrow = '2';\r\n    //textEditDiv.style.backgroundColor = 'rgb(0,0,0,0.7)';\r\n    this.textEditDiv.style.alignItems = 'center';\r\n    this.textEditDiv.style.justifyContent = 'center';\r\n    this.textEditDiv.style.pointerEvents = 'auto';\r\n    this.textEditDiv.style.overflow = 'hidden';\r\n\r\n    this.textEditor = document.createElement('div');\r\n    this.textEditor.style.position = 'absolute';\r\n    this.textEditor.style.fontFamily = this.fontFamily;\r\n    this.textEditor.style.lineHeight = '1em';\r\n    this.textEditor.innerText = this.text;\r\n    this.textEditor.contentEditable = 'true';\r\n    this.textEditor.style.color = this.color;\r\n    this.textEditor.style.whiteSpace = 'pre';\r\n    //this.textEditor.style.outline = 'none';\r\n    this.positionTextEditor();\r\n    this.textEditor.addEventListener('pointerup', (ev) => {\r\n      ev.stopPropagation();\r\n    });\r\n    if (!this.globalSettings.wrapText) {\r\n      this.textEditor.addEventListener('input', () => {\r\n        let fontSize = Number.parseFloat(this.textEditor.style.fontSize);\r\n        while (\r\n          this.textEditor.clientWidth >=\r\n            Number.parseInt(this.textEditor.style.maxWidth) &&\r\n          fontSize > 0.9\r\n        ) {\r\n          fontSize -= 0.1;\r\n          this.textEditor.style.fontSize = `${Math.max(fontSize, 0.9)}em`;\r\n        }\r\n      });\r\n    }\r\n    this.textEditor.addEventListener('keyup', (ev) => {\r\n      ev.cancelBubble = true;\r\n    });\r\n    this.textEditor.addEventListener('paste', (ev) => {\r\n      if (ev.clipboardData) {\r\n        // paste plain text\r\n        const content = ev.clipboardData.getData('text');\r\n        const selection = window.getSelection();\r\n        if (!selection.rangeCount) return false;\r\n        selection.deleteFromDocument();\r\n        selection.getRangeAt(0).insertNode(document.createTextNode(content));\r\n        ev.preventDefault();\r\n      }\r\n    });\r\n\r\n    this.textEditDiv.addEventListener('pointerup', () => {\r\n      this.textEditDivClicked(this.textEditor.innerText);\r\n    });\r\n    this.textEditDiv.appendChild(this.textEditor);\r\n    this.overlayContainer.appendChild(this.textEditDiv);\r\n\r\n    this.hideVisual();\r\n\r\n    this.textEditor.focus();\r\n    document.execCommand('selectAll');\r\n  }\r\n\r\n  private positionTextEditor() {\r\n    if (this.state === 'edit') {\r\n      if (this.textEditor === undefined) {\r\n        this.showTextEditor();\r\n      } else {\r\n        if (this.globalSettings.wrapText) {\r\n          this.textEditor.style.left = `${this.left + this.padding}px`;\r\n          this.textEditor.style.top = `${this.top + this.padding}px`;\r\n          this.textEditor.style.width = `${this.width - this.padding * 2}px`;\r\n          this.textEditor.style.height = `${this.height - this.padding * 2}px`;\r\n          this.textEditor.style.maxHeight = this.textEditor.style.height;\r\n          this.textEditor.style.whiteSpace = 'wrap';\r\n        } else {\r\n          this.textElement.style.display = '';\r\n          const textScale = this.getTextScale();\r\n          // const textPosition = this.getTextPosition(textScale);\r\n          const rPosition = this.rotatePoint({\r\n            x: this.left + this.width / 2,\r\n            y: this.top + this.height / 2,\r\n          });\r\n          const textSize = this.textElement.getBBox();\r\n          const rWH = {\r\n            x: textSize.width * textScale,\r\n            y: textSize.height * textScale,\r\n          };\r\n          rPosition.x -= rWH.x / 2;\r\n          rPosition.y -= rWH.y / 2;\r\n\r\n          this.textEditor.style.top = `${rPosition.y}px`;\r\n          this.textEditor.style.left = `${rPosition.x}px`;\r\n          this.textEditor.style.maxWidth = `${\r\n            this.overlayContainer.offsetWidth - rPosition.x\r\n          }px`;\r\n          this.textEditor.style.fontSize = `${Math.max(16 * textScale, 12)}px`;\r\n          this.textElement.style.display = 'none';\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private textEditDivClicked(text: string) {\r\n    this.text = text.trim();\r\n    this.overlayContainer.innerHTML = '';\r\n    this.renderText();\r\n    this.showVisual();\r\n    if (this._suppressMarkerCreateEvent) {\r\n      this._suppressMarkerCreateEvent = false;\r\n      if (this.onMarkerCreated) {\r\n        this.onMarkerCreated(this);\r\n      }\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  public select(): void {\r\n    super.select();    \r\n    if (this.state === 'edit') {\r\n      this.textEditDivClicked(this.textEditor.innerText);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Deselects this marker, renders text (if necessary), and hides selected marker UI.\r\n   */\r\n  public deselect(): void {\r\n    if (this.state === 'edit') {\r\n      this.textEditDivClicked(this.textEditor.innerText);\r\n    }\r\n    super.deselect();\r\n  }\r\n\r\n  /**\r\n   * Opens text editor on double-click.\r\n   * @param point\r\n   * @param target\r\n   */\r\n  public dblClick(point: IPoint, target?: EventTarget): void {\r\n    super.dblClick(point, target);\r\n\r\n    this.showTextEditor();\r\n  }\r\n\r\n  /**\r\n   * Sets text color.\r\n   * @param color - new text color.\r\n   */\r\n  protected setColor(color: string): void {\r\n    if (this.textElement) {\r\n      SvgHelper.setAttributes(this.textElement, [['fill', color]]);\r\n    }\r\n    this.color = color;\r\n    if (this.textEditor) {\r\n      this.textEditor.style.color = this.color;\r\n    }\r\n    this.colorChanged(color);\r\n  }\r\n\r\n  /**\r\n   * Sets font family.\r\n   * @param font - new font family.\r\n   */\r\n  protected setFont(font: string): void {\r\n    if (this.textElement) {\r\n      SvgHelper.setAttributes(this.textElement, [['font-family', font]]);\r\n    }\r\n    this.fontFamily = font;\r\n    if (this.textEditor) {\r\n      this.textEditor.style.fontFamily = this.fontFamily;\r\n    }\r\n    this.renderText();\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Hides marker visual.\r\n   */\r\n  protected hideVisual(): void {\r\n    this.textElement.style.display = 'none';\r\n    this.hideControlBox();\r\n  }\r\n  /**\r\n   * Shows marker visual.\r\n   */\r\n  protected showVisual(): void {\r\n    if (this.state === 'edit') {\r\n      this._state = 'select';\r\n    }\r\n    this.textElement.style.display = '';\r\n    this.showControlBox();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.colorPanel, this.fontFamilyPanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): TextMarkerState {\r\n    const result: TextMarkerState = Object.assign(\r\n      {\r\n        color: this.color,\r\n        fontFamily: this.fontFamily,\r\n        padding: this.padding,\r\n        text: this.text,\r\n        wrapText: this.globalSettings.wrapText\r\n      },\r\n      super.getState()\r\n    );\r\n    result.typeName = TextMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   *\r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    const textState = state as TextMarkerState;\r\n    this.color = textState.color;\r\n    this.fontFamily = textState.fontFamily;\r\n    this.padding = textState.padding;\r\n    this.text = textState.text;\r\n\r\n    this.createVisual();\r\n    super.restoreState(state);\r\n    this.setSize();\r\n    if (this.globalSettings.wrapText) {\r\n      // need a rerender post setting size for wrapping\r\n      this.renderText();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   *\r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.setSize();\r\n    this.sizeText();\r\n    this.positionTextEditor();\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { RectangularBoxMarkerBase } from '../RectangularBoxMarkerBase';\r\nimport { Settings } from '../../core/Settings';\r\nimport Icon from './freehand-marker-icon.svg';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { FreehandMarkerState } from './FreehandMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\n\r\n\r\nexport class FreehandMarker extends RectangularBoxMarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'FreehandMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Freehand marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Marker color.\r\n   */\r\n  protected color = 'transparent';\r\n  /**\r\n   * Marker's stroke width.\r\n   */\r\n  protected lineWidth = 3;\r\n\r\n  private colorPanel: ColorPickerPanel;\r\n  private lineWidthPanel: LineWidthPanel;\r\n\r\n\r\n  private canvasElement: HTMLCanvasElement;\r\n  private canvasContext: CanvasRenderingContext2D;\r\n\r\n  private drawingImage: SVGImageElement;\r\n  private drawingImgUrl: string;\r\n\r\n  private drawing = false;\r\n\r\n  private pixelRatio = 1;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(\r\n    container: SVGGElement,\r\n    overlayContainer: HTMLDivElement,\r\n    settings: Settings\r\n  ) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.color = settings.defaultColor;\r\n    this.lineWidth = settings.defaultStrokeWidth;\r\n    this.pixelRatio = settings.freehandPixelRatio;\r\n\r\n    this.setColor = this.setColor.bind(this);\r\n    this.addCanvas = this.addCanvas.bind(this);\r\n    this.finishCreation = this.finishCreation.bind(this);\r\n    this.setLineWidth = this.setLineWidth.bind(this);\r\n\r\n    this.colorPanel = new ColorPickerPanel(\r\n      'Color',\r\n      settings.defaultColorSet,\r\n      settings.defaultColor\r\n    );\r\n    this.colorPanel.onColorChanged = this.setColor;\r\n\r\n    this.lineWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.lineWidthPanel.onWidthChanged = this.setLineWidth;\r\n\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.visual ||\r\n      el === this.drawingImage\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private createVisual() {\r\n    this.visual = SvgHelper.createGroup();\r\n    this.drawingImage = SvgHelper.createImage();\r\n    this.visual.appendChild(this.drawingImage);\r\n\r\n    const translate = SvgHelper.createTransform();\r\n    this.visual.transform.baseVal.appendItem(translate);\r\n    this.addMarkerVisualToContainer(this.visual);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    if (this.state === 'new') {\r\n      this.addCanvas();\r\n\r\n      this.createVisual();\r\n\r\n      this._state = 'creating';\r\n    }\r\n\r\n    if (this.state === 'creating') {\r\n      this.canvasContext.strokeStyle = this.color;\r\n      this.canvasContext.lineWidth = this.lineWidth;\r\n      this.canvasContext.beginPath();\r\n      this.canvasContext.moveTo(point.x, point.y);\r\n      this.drawing = true;\r\n    } else {\r\n      super.pointerDown(point, target);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    if (this.state === 'creating') {\r\n      if (this.drawing) {\r\n        this.canvasContext.lineTo(point.x, point.y);\r\n        this.canvasContext.stroke();\r\n      }\r\n    } else {\r\n      super.manipulate(point);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resize marker based on current pointer coordinates and context.\r\n   * @param point \r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    SvgHelper.setAttributes(this.visual, [\r\n      ['width', this.width.toString()],\r\n      ['height', this.height.toString()],\r\n    ]);\r\n    SvgHelper.setAttributes(this.drawingImage, [\r\n      ['width', this.width.toString()],\r\n      ['height', this.height.toString()],\r\n    ]);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    if (this._state === 'creating') {\r\n      if (this.drawing) {\r\n        this.canvasContext.closePath();\r\n        this.drawing = false;\r\n        if (this.globalSettings.newFreehandMarkerOnPointerUp) {\r\n          this.finishCreation();\r\n        }\r\n      }\r\n    } else {\r\n      super.pointerUp(point);\r\n    }\r\n  }\r\n\r\n  private addCanvas() {\r\n    this.overlayContainer.innerHTML = '';\r\n\r\n    this.canvasElement = document.createElement('canvas');\r\n    this.canvasElement.width = this.overlayContainer.clientWidth * this.pixelRatio;\r\n    this.canvasElement.height = this.overlayContainer.clientHeight * this.pixelRatio;\r\n    this.canvasContext = this.canvasElement.getContext('2d');\r\n    this.canvasContext.scale(this.pixelRatio, this.pixelRatio);\r\n    this.overlayContainer.appendChild(this.canvasElement);\r\n  }\r\n\r\n  /**\r\n   * Selects this marker and displays appropriate selected marker UI.\r\n   */\r\n  public select(): void {\r\n    if (this.state === 'creating') {\r\n      this.finishCreation();\r\n    }\r\n    super.select();\r\n  }\r\n\r\n  /**\r\n   * Deselects this marker and hides selected marker UI.\r\n   */\r\n  public deselect(): void {\r\n    if (this.state === 'creating') {\r\n      this.finishCreation();\r\n    }\r\n    super.deselect();\r\n  }\r\n\r\n  private finishCreation() {\r\n    const imgData = this.canvasContext.getImageData(\r\n      0,\r\n      0,\r\n      this.canvasElement.width,\r\n      this.canvasElement.height\r\n    );\r\n\r\n    let [startX, startY, endX, endY] = [\r\n      this.canvasElement.width + 1,\r\n      this.canvasElement.height + 1,\r\n      -1,\r\n      -1,\r\n    ];\r\n    let containsData = false;\r\n    for (let row = 0; row < this.canvasElement.height; row++) {\r\n      for (let col = 0; col < this.canvasElement.width; col++) {\r\n        const pixAlpha =\r\n          imgData.data[row * this.canvasElement.width * 4 + col * 4 + 3];\r\n        if (pixAlpha > 0) {\r\n          containsData = true;\r\n          if (row < startY) {\r\n            startY = row;\r\n          }\r\n          if (col < startX) {\r\n            startX = col;\r\n          }\r\n          if (row > endY) {\r\n            endY = row;\r\n          }\r\n          if (col > endX) {\r\n            endX = col;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    if (containsData) {\r\n      this.left = startX / this.pixelRatio;\r\n      this.top = startY / this.pixelRatio;\r\n      this.width = (endX - startX) / this.pixelRatio;\r\n      this.height = (endY - startY) / this.pixelRatio;\r\n\r\n      const tmpCanvas = document.createElement('canvas');\r\n      tmpCanvas.width = endX - startX;\r\n      tmpCanvas.height = endY - startY;\r\n      const tmpCtx = tmpCanvas.getContext('2d');\r\n      tmpCtx.putImageData(\r\n        this.canvasContext.getImageData(\r\n          startX,\r\n          startY,\r\n          endX - startX,\r\n          endY - startY\r\n        ),\r\n        0,\r\n        0\r\n      );\r\n\r\n      this.drawingImgUrl = tmpCanvas.toDataURL('image/png');\r\n      this.setDrawingImage();\r\n\r\n      this._state = 'select';\r\n      if (this.onMarkerCreated) {\r\n        this.onMarkerCreated(this);\r\n      }\r\n    }\r\n    this.overlayContainer.innerHTML = '';\r\n  }\r\n\r\n  private setDrawingImage() {\r\n    SvgHelper.setAttributes(this.drawingImage, [\r\n      ['width', this.width.toString()],\r\n      ['height', this.height.toString()],\r\n    ]);\r\n    SvgHelper.setAttributes(this.drawingImage, [['href', this.drawingImgUrl]]);\r\n    this.moveVisual({ x: this.left, y: this.top });\r\n  }\r\n\r\n  /**\r\n   * Sets marker drawing color.\r\n   * @param color - new color.\r\n   */\r\n  protected setColor(color: string): void {\r\n    this.color = color;\r\n    this.colorChanged(color);\r\n  }\r\n\r\n  /**\r\n   * Sets line width.\r\n   * @param width - new line width\r\n   */\r\n   protected setLineWidth(width: number): void {\r\n    this.lineWidth = width;\r\n  }\r\n\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    if (this.state === 'new' || this.state === 'creating') {\r\n      return [this.colorPanel, this.lineWidthPanel];\r\n    } else {\r\n      return [];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): FreehandMarkerState {\r\n    const result: FreehandMarkerState = Object.assign({\r\n      drawingImgUrl: this.drawingImgUrl\r\n    }, super.getState());\r\n    result.typeName = FreehandMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    this.createVisual();\r\n    super.restoreState(state);\r\n    this.drawingImgUrl = (state as FreehandMarkerState).drawingImgUrl;\r\n    this.setDrawingImage();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.setDrawingImage();\r\n  }\r\n\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './arrow-type-panel-icon.svg';\r\n\r\n/**\r\n * Represents available arrow types.\r\n *\r\n * - `both` - arrow tips on both sides.\r\n * - `start` - arrow tip on the starting point of line.\r\n * - `end` - arrow tip on the ending point of line.\r\n * - `none` - no arrow tips.\r\n */\r\nexport type ArrowType = 'both' | 'start' | 'end' | 'none';\r\n/**\r\n * Handler for arrow type change event.\r\n */\r\nexport type ArrowTypeChangeHandler = (newType: ArrowType) => void;\r\n\r\n/**\r\n * Arrow type selection panel.\r\n */\r\nexport class ArrowTypePanel extends ToolboxPanel {\r\n  private currentType?: ArrowType;\r\n\r\n  private typeBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Event handler for the arrow type change event.\r\n   */\r\n  public onArrowTypeChanged?: ArrowTypeChangeHandler;\r\n\r\n  /**\r\n   * Creates an ArrowTypePanel.\r\n   * @param title - panel title.\r\n   * @param currentType - currently set arrow type.\r\n   * @param icon - panel button icon (SVG image markup).\r\n   */\r\n  constructor(\r\n    title: string,\r\n    currentType?: ArrowType,\r\n    icon?: string\r\n  ) {\r\n    super(title, icon ? icon : Icon);\r\n    this.currentType = currentType;\r\n\r\n    this.setCurrentType = this.setCurrentType.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    panelDiv.style.display = 'flex';\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.flexGrow = '2';\r\n    for (let ti = 0; ti < 4; ti++) {\r\n      let arrowType: ArrowType = 'both';\r\n      switch (ti) {\r\n        case 0:\r\n          arrowType = 'both';\r\n          break;\r\n        case 1:\r\n          arrowType = 'start';\r\n          break;\r\n        case 2:\r\n          arrowType = 'end';\r\n          break;\r\n        case 3:\r\n          arrowType = 'none';\r\n          break;\r\n      }\r\n      const typeBoxContainer = document.createElement('div');\r\n      typeBoxContainer.style.display = 'flex';\r\n      typeBoxContainer.style.flexGrow = '2';\r\n      typeBoxContainer.style.alignItems = 'center';\r\n      typeBoxContainer.style.justifyContent = 'space-between';\r\n      typeBoxContainer.style.padding = '5px';\r\n      typeBoxContainer.style.borderWidth = '2px';\r\n      typeBoxContainer.style.borderStyle = 'solid';\r\n      typeBoxContainer.style.borderColor =\r\n        arrowType === this.currentType\r\n          ? this.uiStyleSettings.toolboxAccentColor\r\n          : 'transparent';\r\n\r\n      typeBoxContainer.addEventListener('click', () => {\r\n        this.setCurrentType(arrowType, typeBoxContainer);\r\n      });\r\n      panelDiv.appendChild(typeBoxContainer);\r\n\r\n      if (arrowType === 'both' || arrowType === 'start') {\r\n        const leftTip = document.createElement('div');\r\n        leftTip.style.display = 'flex';\r\n        leftTip.style.alignItems = 'center';\r\n        leftTip.style.minHeight = '20px';\r\n        leftTip.innerHTML = `<svg viewBox=\"0 0 10 10\" width=\"10\" height=\"10\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <polygon points=\"0,5 10,0 10,10\" fill=\"${\r\n            this.uiStyleSettings !== undefined\r\n              ? this.uiStyleSettings.toolboxColor\r\n              : '#eeeeee'\r\n          }\" />\r\n        </svg>`;\r\n        leftTip.style.marginLeft = '5px';\r\n        typeBoxContainer.appendChild(leftTip);\r\n      }\r\n\r\n      const lineBox = document.createElement('div');\r\n      lineBox.style.display = 'flex';\r\n      lineBox.style.alignItems = 'center';\r\n      lineBox.style.minHeight = '20px';\r\n      lineBox.style.flexGrow = '2';\r\n\r\n      const hr = document.createElement('hr');\r\n      hr.style.minWidth = '20px';\r\n      hr.style.border = '0px';\r\n      hr.style.borderTop = `3px solid ${\r\n        this.uiStyleSettings !== undefined\r\n          ? this.uiStyleSettings.toolboxColor\r\n          : '#eeeeee'\r\n      }`;\r\n      hr.style.flexGrow = '2';\r\n      lineBox.appendChild(hr);\r\n\r\n      typeBoxContainer.appendChild(lineBox);\r\n\r\n      if (arrowType === 'both' || arrowType === 'end') {\r\n        const rightTip = document.createElement('div');\r\n        rightTip.style.display = 'flex';\r\n        rightTip.style.alignItems = 'center';\r\n        rightTip.style.minHeight = '20px';\r\n        rightTip.innerHTML = `<svg viewBox=\"0 0 10 10\" width=\"10\" height=\"10\" xmlns=\"http://www.w3.org/2000/svg\">\r\n          <polygon points=\"0,0 10,5 0,10\" fill=\"${\r\n            this.uiStyleSettings !== undefined\r\n              ? this.uiStyleSettings.toolboxColor\r\n              : '#eeeeee'\r\n          }\" />\r\n        </svg>`;\r\n        rightTip.style.marginRight = '5px';\r\n        typeBoxContainer.appendChild(rightTip);\r\n      }\r\n\r\n      this.typeBoxes.push(typeBoxContainer);\r\n    }\r\n    return panelDiv;\r\n  }\r\n\r\n  private setCurrentType(newType: ArrowType, target: HTMLDivElement) {\r\n    this.currentType = newType;\r\n\r\n    this.typeBoxes.forEach((box) => {\r\n      box.style.borderColor =\r\n        box === target\r\n          ? this.uiStyleSettings !== undefined\r\n            ? this.uiStyleSettings.toolboxAccentColor\r\n            : '#3080c3'\r\n          : 'transparent';\r\n    });\r\n\r\n    if (this.onArrowTypeChanged) {\r\n      this.onArrowTypeChanged(this.currentType);\r\n    }\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { Settings } from '../../core/Settings';\r\nimport Icon from './arrow-marker-icon.svg';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { LineMarker } from '../line-marker/LineMarker';\r\nimport { ArrowType, ArrowTypePanel } from '../../ui/toolbox-panels/ArrowTypePanel';\r\nimport { ArrowMarkerState } from './ArrowMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\n\r\n/**\r\n * Represents an arrow marker.\r\n */\r\nexport class ArrowMarker extends LineMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'ArrowMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Arrow marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  private arrow1: SVGPolygonElement;\r\n  private arrow2: SVGPolygonElement;\r\n\r\n  private arrowType: ArrowType = 'end';\r\n\r\n  private arrowBaseHeight = 10;\r\n  private arrowBaseWidth = 10;\r\n\r\n  /**\r\n   * Toolbox panel for arrow type selection.\r\n   */\r\n  protected arrowTypePanel: ArrowTypePanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.getArrowPoints = this.getArrowPoints.bind(this);\r\n    this.setArrowType = this.setArrowType.bind(this);\r\n\r\n    this.arrowTypePanel = new ArrowTypePanel('Arrow type', 'end');\r\n    this.arrowTypePanel.onArrowTypeChanged = this.setArrowType;\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.arrow1 || el === this.arrow2\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private getArrowPoints(offsetX: number, offsetY: number): string {\r\n    const width = this.arrowBaseWidth + this.strokeWidth * 2;\r\n    const height = this.arrowBaseHeight + this.strokeWidth * 2;\r\n    return `${offsetX - width / 2},${\r\n      offsetY + height / 2\r\n    } ${offsetX},${offsetY - height / 2} ${\r\n      offsetX + width / 2},${offsetY + height / 2}`;\r\n  }\r\n\r\n  private createTips() {\r\n    this.arrow1 = SvgHelper.createPolygon(this.getArrowPoints(this.x1, this.y1), [['fill', this.strokeColor]]);\r\n    this.arrow1.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.visual.appendChild(this.arrow1);\r\n\r\n    this.arrow2 = SvgHelper.createPolygon(this.getArrowPoints(this.x2, this.y2), [['fill', this.strokeColor]]);\r\n    this.arrow2.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.visual.appendChild(this.arrow2);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n    if (this.state === 'creating') {\r\n      this.createTips();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adjusts marker visual after manipulation.\r\n   */\r\n  protected adjustVisual(): void {\r\n    super.adjustVisual();\r\n\r\n    if (this.arrow1 && this.arrow2) {\r\n      this.arrow1.style.display = (this.arrowType === 'both' || this.arrowType === 'start') ? '' : 'none';\r\n      this.arrow2.style.display = (this.arrowType === 'both' || this.arrowType === 'end') ? '' : 'none';\r\n\r\n      SvgHelper.setAttributes(this.arrow1, [\r\n        ['points', this.getArrowPoints(this.x1, this.y1)],\r\n        ['fill', this.strokeColor]\r\n      ]);\r\n      SvgHelper.setAttributes(this.arrow2, [\r\n        ['points', this.getArrowPoints(this.x2, this.y2)],\r\n        ['fill', this.strokeColor]\r\n      ]);\r\n\r\n      let lineAngle1 = 0;\r\n      if (Math.abs(this.x1 - this.x2) > 0.1) {\r\n        lineAngle1 =\r\n          (Math.atan((this.y2 - this.y1) / (this.x2 - this.x1)) * 180) / Math.PI + 90 * Math.sign(this.x1 - this.x2);\r\n      }\r\n      const a1transform = this.arrow1.transform.baseVal.getItem(0);\r\n      a1transform.setRotate(lineAngle1, this.x1, this.y1);\r\n      this.arrow1.transform.baseVal.replaceItem(a1transform, 0);\r\n\r\n      const a2transform = this.arrow2.transform.baseVal.getItem(0);\r\n      a2transform.setRotate(lineAngle1 + 180, this.x2, this.y2);\r\n      this.arrow2.transform.baseVal.replaceItem(a2transform, 0);\r\n    }\r\n  }\r\n\r\n  private setArrowType(arrowType: ArrowType) {\r\n    this.arrowType = arrowType;\r\n    this.adjustVisual();\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel, this.arrowTypePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): ArrowMarkerState {\r\n    const result: ArrowMarkerState = Object.assign({\r\n      arrowType: this.arrowType\r\n    }, super.getState());\r\n    result.typeName = ArrowMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n\r\n    const amState = state as ArrowMarkerState;\r\n    this.arrowType = amState.arrowType;\r\n\r\n    this.createTips();\r\n    this.adjustVisual();\r\n  }\r\n\r\n}\r\n", "import Icon from './cover-marker-icon.svg';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { Settings } from '../../core/Settings';\r\nimport { RectangleMarker } from '../RectangleMarker';\r\nimport { RectangleMarkerState } from '../RectangleMarkerState';\r\n\r\nexport class CoverMarker extends RectangleMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'CoverMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Cover marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Color picker panel for the background color.\r\n   */\r\n  protected fillPanel: ColorPickerPanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.fillColor = settings.defaultFillColor;\r\n    this.strokeWidth = 0;\r\n\r\n    this.fillPanel = new ColorPickerPanel(\r\n      'Color',\r\n      settings.defaultColorSet,\r\n      settings.defaultFillColor\r\n    );\r\n    this.fillPanel.onColorChanged = this.setFillColor;\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.fillPanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result = super.getState();\r\n    result.typeName = CoverMarker.typeName;\r\n    return result;\r\n  }\r\n}\r\n", "import { ToolboxPanel } from '../ToolboxPanel';\r\nimport Icon from './opacity-panel-icon.svg';\r\n\r\n/**\r\n * Opacity chage event handler type.\r\n */\r\nexport type OpacityChangeHandler = (newOpacity: number) => void;\r\n\r\n/**\r\n * Opacity panel.\r\n */\r\nexport class OpacityPanel extends ToolboxPanel {\r\n  private opacities: number[] = [];\r\n  private currentOpacity?: number;\r\n\r\n  private opacityBoxes: HTMLDivElement[] = [];\r\n\r\n  /**\r\n   * Opacity change event handler.\r\n   */\r\n  public onOpacityChanged?: OpacityChangeHandler;\r\n\r\n  /**\r\n   * Creates an opacity panel.\r\n   * @param title - panel title.\r\n   * @param opacities - available opacities.\r\n   * @param currentOpacity - current opacity.\r\n   * @param icon - toolbox panel button (SVG image markup).\r\n   */\r\n  constructor(title: string, opacities: number[], currentOpacity?: number, icon?: string) {\r\n    super(title, icon ? icon : Icon);\r\n    this.opacities = opacities;\r\n    this.currentOpacity = currentOpacity;\r\n\r\n    this.setCurrentOpacity = this.setCurrentOpacity.bind(this);\r\n  }\r\n\r\n  /**\r\n   * Returns panel UI.\r\n   */\r\n  public getUi(): HTMLDivElement {\r\n    const panelDiv = document.createElement('div');\r\n    panelDiv.style.display = 'flex';\r\n    panelDiv.style.overflow = 'hidden';\r\n    panelDiv.style.flexGrow = '2';\r\n    panelDiv.style.justifyContent = 'space-between';\r\n    this.opacities.forEach((opacity) => {\r\n      const opacityBoxContainer = document.createElement('div');\r\n      opacityBoxContainer.style.display = 'flex';\r\n      //opacityBoxContainer.style.flexGrow = '2';\r\n      opacityBoxContainer.style.alignItems = 'center';\r\n      opacityBoxContainer.style.justifyContent = 'center';\r\n      opacityBoxContainer.style.padding = '5px';\r\n      opacityBoxContainer.style.borderWidth = '2px';\r\n      opacityBoxContainer.style.borderStyle = 'solid';\r\n      opacityBoxContainer.style.borderColor =\r\n        opacity === this.currentOpacity ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n\r\n      opacityBoxContainer.addEventListener('click', () => {\r\n        this.setCurrentOpacity(opacity, opacityBoxContainer);\r\n      })\r\n      panelDiv.appendChild(opacityBoxContainer);\r\n\r\n      const label = document.createElement('div');\r\n      label.innerText = `${(opacity * 100)}%`;\r\n      opacityBoxContainer.appendChild(label);\r\n\r\n      this.opacityBoxes.push(opacityBoxContainer);\r\n    });\r\n    return panelDiv;\r\n  }\r\n\r\n  private setCurrentOpacity(newWidth: number, target: HTMLDivElement) {\r\n    this.currentOpacity = newWidth;\r\n\r\n    this.opacityBoxes.forEach(box => {\r\n      box.style.borderColor = box === target ? this.uiStyleSettings.toolboxAccentColor : 'transparent';\r\n    });\r\n\r\n    if (this.onOpacityChanged) {\r\n      this.onOpacityChanged(this.currentOpacity);\r\n    }\r\n  }\r\n}\r\n", "import Icon from './highlight-marker-icon.svg';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { Settings } from '../../core/Settings';\r\nimport { CoverMarker } from '../cover-marker/CoverMarker';\r\nimport { OpacityPanel } from '../../ui/toolbox-panels/OpacityPanel';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { RectangleMarkerState } from '../RectangleMarkerState';\r\n\r\nexport class HighlightMarker extends CoverMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'HighlightMarker';\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Highlight marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  protected opacityPanel: OpacityPanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.setOpacity = this.setOpacity.bind(this);\r\n\r\n    this.fillColor = settings.defaultHighlightColor;\r\n    this.strokeWidth = 0;\r\n    this.opacity = settings.defaultHighlightOpacity;\r\n\r\n    this.fillPanel = new ColorPickerPanel(\r\n      'Color',\r\n      settings.defaultColorSet,\r\n      this.fillColor\r\n    );\r\n    this.fillPanel.onColorChanged = this.setFillColor;\r\n\r\n    this.opacityPanel = new OpacityPanel(\r\n      'Opacity',\r\n      settings.defaultOpacitySteps,\r\n      this.opacity\r\n    );\r\n    this.opacityPanel.onOpacityChanged = this.setOpacity;\r\n  }\r\n\r\n  /**\r\n   * Sets marker's opacity (0..1).\r\n   * @param opacity - new opacity value.\r\n   */\r\n  protected setOpacity(opacity: number): void {\r\n    this.opacity = opacity;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['opacity', this.opacity.toString()]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.fillPanel, this.opacityPanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result = super.getState();\r\n    result.typeName = HighlightMarker.typeName;\r\n    return result;\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { Settings } from '../../core/Settings';\r\nimport Icon from './callout-marker-icon.svg';\r\nimport TextColorIcon from '../../ui/toolbox-panels/text-color-icon.svg';\r\nimport FillColorIcon from '../../ui/toolbox-panels/fill-color-icon.svg';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { FontFamilyPanel } from '../../ui/toolbox-panels/FontFamilyPanel';\r\nimport { TextMarker } from '../text-marker/TextMarker';\r\nimport { ResizeGrip } from '../ResizeGrip';\r\nimport { CalloutMarkerState } from './CalloutMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\n\r\nexport class CalloutMarker extends TextMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'CalloutMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Callout marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  private bgColor = 'transparent';\r\n  /**\r\n   * Color picker toolbox panel for the background (fill) color.\r\n   */\r\n  protected bgColorPanel: ColorPickerPanel;\r\n\r\n  private tipPosition: IPoint = { x: 0, y: 0 };\r\n  private tipBase1Position: IPoint = { x: 0, y: 0 };\r\n  private tipBase2Position: IPoint = { x: 0, y: 0 };\r\n  private tip: SVGPolygonElement;\r\n  private tipGrip: ResizeGrip;\r\n  private tipMoving = false;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(\r\n    container: SVGGElement,\r\n    overlayContainer: HTMLDivElement,\r\n    settings: Settings\r\n  ) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.color = settings.defaultStrokeColor;\r\n    this.bgColor = settings.defaultFillColor;\r\n    this.fontFamily = settings.defaultFontFamily;\r\n\r\n    this.defaultSize = { x: 100, y: 30 };\r\n\r\n    this.setBgColor = this.setBgColor.bind(this);\r\n    this.getTipPoints = this.getTipPoints.bind(this);\r\n    this.positionTip = this.positionTip.bind(this);\r\n    this.setTipPoints = this.setTipPoints.bind(this);\r\n\r\n    this.colorPanel = new ColorPickerPanel(\r\n      'Text color',\r\n      settings.defaultColorSet,\r\n      this.color,\r\n      TextColorIcon\r\n    );\r\n    this.colorPanel.onColorChanged = this.setColor;\r\n\r\n    this.bgColorPanel = new ColorPickerPanel(\r\n      'Fill color',\r\n      settings.defaultColorSet,\r\n      this.bgColor,\r\n      FillColorIcon\r\n    );\r\n    this.bgColorPanel.onColorChanged = this.setBgColor;\r\n\r\n    this.fontFamilyPanel = new FontFamilyPanel(\r\n      'Font',\r\n      settings.defaultFontFamilies,\r\n      settings.defaultFontFamily\r\n    );\r\n    this.fontFamilyPanel.onFontChanged = this.setFont;\r\n\r\n    this.tipGrip = new ResizeGrip();\r\n    this.tipGrip.visual.transform.baseVal.appendItem(\r\n      SvgHelper.createTransform()\r\n    );\r\n    this.controlBox.appendChild(this.tipGrip.visual);\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    return (\r\n      super.ownsTarget(el) || this.tipGrip.ownsTarget(el) || this.tip === el\r\n    );\r\n  }\r\n\r\n  private createTip() {\r\n    SvgHelper.setAttributes(this.bgRectangle, [\r\n      ['fill', this.bgColor],\r\n      ['rx', '10px'],\r\n    ]);\r\n\r\n    this.tip = SvgHelper.createPolygon(this.getTipPoints(), [\r\n      ['fill', this.bgColor],\r\n    ]);\r\n    this.visual.appendChild(this.tip);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    if (this.state === 'new') {\r\n      super.pointerDown(point, target);\r\n    }\r\n\r\n    if (this.state === 'creating') {\r\n      this.createTip();\r\n    } else if (this.tipGrip.ownsTarget(target)) {\r\n      this.manipulationStartLeft = this.left;\r\n      this.manipulationStartTop = this.top;\r\n      this.tipMoving = true;\r\n    } else {\r\n      super.pointerDown(point, target);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    if (this.tipMoving) {\r\n      this.tipMoving = false;\r\n      this.isMoved = true;\r\n      super.pointerUp(point);\r\n    } else {\r\n      const isCreating = this.state === 'creating';\r\n      super.pointerUp(point);\r\n      this.setTipPoints(isCreating);\r\n      this.positionTip();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    if (this.tipMoving) {\r\n      const rotatedPoint = this.unrotatePoint(point);\r\n      this.tipPosition = {\r\n        x: rotatedPoint.x - this.manipulationStartLeft,\r\n        y: rotatedPoint.y - this.manipulationStartTop,\r\n      };\r\n      this.positionTip();\r\n    } else {\r\n      super.manipulate(point);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets marker's background/fill color.\r\n   * @param color - new background color.\r\n   */\r\n  protected setBgColor(color: string): void {\r\n    if (this.bgRectangle && this.tip) {\r\n      SvgHelper.setAttributes(this.bgRectangle, [['fill', color]]);\r\n      SvgHelper.setAttributes(this.tip, [['fill', color]]);\r\n    }\r\n    this.bgColor = color;\r\n    this.fillColorChanged(color);\r\n  }\r\n\r\n  private getTipPoints(): string {\r\n    this.setTipPoints(this.state === 'creating');\r\n    return `${this.tipBase1Position.x},${this.tipBase1Position.y\r\n      } ${this.tipBase2Position.x},${this.tipBase2Position.y\r\n      } ${this.tipPosition.x},${this.tipPosition.y}`;\r\n  }\r\n\r\n  private setTipPoints(isCreating = false) {\r\n    let offset = Math.min(this.height / 2, 15);\r\n    let baseWidth = this.height / 5;\r\n    if (isCreating) {\r\n      this.tipPosition = { x: offset + baseWidth / 2, y: this.height + 20 };\r\n    }\r\n\r\n    const cornerAngle = Math.atan((this.height / 2) / (this.width / 2));\r\n    if (this.tipPosition.x < this.width / 2 && this.tipPosition.y < this.height / 2) {\r\n      // top left\r\n      const tipAngle = Math.atan((this.height / 2 - this.tipPosition.y) / (this.width / 2 - this.tipPosition.x));\r\n      if (cornerAngle < tipAngle) {\r\n        baseWidth = this.width / 5;\r\n        offset = Math.min(this.width / 2, 15);\r\n        this.tipBase1Position = { x: offset, y: 0 };\r\n        this.tipBase2Position = { x: offset + baseWidth, y: 0 };\r\n      } else {\r\n        this.tipBase1Position = { x: 0, y: offset };\r\n        this.tipBase2Position = { x: 0, y: offset + baseWidth };\r\n      }\r\n    } else if (this.tipPosition.x >= this.width / 2 && this.tipPosition.y < this.height / 2) {\r\n      // top right\r\n      const tipAngle = Math.atan((this.height / 2 - this.tipPosition.y) / (this.tipPosition.x - this.width / 2));\r\n      if (cornerAngle < tipAngle) {\r\n        baseWidth = this.width / 5;\r\n        offset = Math.min(this.width / 2, 15);\r\n        this.tipBase1Position = { x: this.width - offset - baseWidth, y: 0 };\r\n        this.tipBase2Position = { x: this.width - offset, y: 0 };\r\n      } else {\r\n        this.tipBase1Position = { x: this.width, y: offset };\r\n        this.tipBase2Position = { x: this.width, y: offset + baseWidth };\r\n      }\r\n    } else if (this.tipPosition.x >= this.width / 2 && this.tipPosition.y >= this.height / 2) {\r\n      // bottom right\r\n      const tipAngle = Math.atan((this.tipPosition.y - this.height / 2) / (this.tipPosition.x - this.width / 2));\r\n      if (cornerAngle < tipAngle) {\r\n        baseWidth = this.width / 5;\r\n        offset = Math.min(this.width / 2, 15);\r\n        this.tipBase1Position = { x: this.width - offset - baseWidth, y: this.height };\r\n        this.tipBase2Position = { x: this.width - offset, y: this.height };\r\n      } else {\r\n        this.tipBase1Position = { x: this.width, y: this.height - offset - baseWidth };\r\n        this.tipBase2Position = { x: this.width, y: this.height - offset };\r\n      }\r\n    } else {\r\n      // bottom left\r\n      const tipAngle = Math.atan((this.tipPosition.y - this.height / 2) / (this.width / 2 - this.tipPosition.x));\r\n      if (cornerAngle < tipAngle) {\r\n        baseWidth = this.width / 5;\r\n        offset = Math.min(this.width / 2, 15);\r\n        this.tipBase1Position = { x: offset, y: this.height };\r\n        this.tipBase2Position = { x: offset + baseWidth, y: this.height };\r\n      } else {\r\n        this.tipBase1Position = { x: 0, y: this.height - offset };\r\n        this.tipBase2Position = { x: 0, y: this.height - offset - baseWidth };\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resize marker based on current pointer coordinates and context.\r\n   * @param point \r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    this.positionTip();\r\n  }\r\n\r\n  private positionTip() {\r\n    SvgHelper.setAttributes(this.tip, [['points', this.getTipPoints()]]);\r\n    const translate = this.tipGrip.visual.transform.baseVal.getItem(0);\r\n    translate.setTranslate(this.tipPosition.x, this.tipPosition.y);\r\n    this.tipGrip.visual.transform.baseVal.replaceItem(translate, 0);\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.colorPanel, this.bgColorPanel, this.fontFamilyPanel];\r\n  }\r\n\r\n  /**\r\n   * Selects this marker and displays appropriate selected marker UI.\r\n   */\r\n  public select(): void {\r\n    this.positionTip();\r\n    super.select();\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): CalloutMarkerState {\r\n    const result: CalloutMarkerState = Object.assign({\r\n      bgColor: this.bgColor,\r\n      tipPosition: this.tipPosition\r\n    }, super.getState());\r\n    result.typeName = CalloutMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    const calloutState = state as CalloutMarkerState;\r\n    this.bgColor = calloutState.bgColor;\r\n    this.tipPosition = calloutState.tipPosition;\r\n\r\n    super.restoreState(state);\r\n    this.createTip();\r\n    this.setTipPoints();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.tipPosition = {x: this.tipPosition.x * scaleX, y: this.tipPosition.y * scaleY};\r\n\r\n    this.positionTip();\r\n  }\r\n}\r\n", "import Icon from './ellipse-marker-icon.svg';\r\nimport { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { RectangularBoxMarkerBase } from '../RectangularBoxMarkerBase';\r\nimport { Settings } from '../../core/Settings';\r\nimport { RectangleMarkerState } from '../RectangleMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\nimport { LineStylePanel } from '../../ui/toolbox-panels/LineStylePanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport FillColorIcon from '../../ui/toolbox-panels/fill-color-icon.svg';\r\nimport { OpacityPanel } from '../../ui/toolbox-panels/OpacityPanel';\r\n\r\nexport class EllipseMarker extends RectangularBoxMarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'EllipseMarker';\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Ellipse marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Ellipse fill color.\r\n   */\r\n  protected fillColor = 'transparent';\r\n  /**\r\n   * Ellipse border color.\r\n   */\r\n  protected strokeColor = 'transparent';\r\n  /**\r\n   * Ellipse border line width.\r\n   */\r\n  protected strokeWidth = 0;\r\n  /**\r\n   * Ellipse border dash array.\r\n   */\r\n  protected strokeDasharray = '';\r\n  /**\r\n   * Ellipse opacity (0..1).\r\n   */\r\n  protected opacity = 1;\r\n\r\n  protected strokePanel: ColorPickerPanel;\r\n  protected fillPanel: ColorPickerPanel;\r\n  protected strokeWidthPanel: LineWidthPanel;\r\n  protected strokeStylePanel: LineStylePanel;\r\n  protected opacityPanel: OpacityPanel;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.strokeColor = settings.defaultColor;\r\n    this.strokeWidth = settings.defaultStrokeWidth;\r\n    this.strokeDasharray = settings.defaultStrokeDasharray;\r\n    this.fillColor = settings.defaultFillColor;\r\n\r\n    this.setStrokeColor = this.setStrokeColor.bind(this);\r\n    this.setFillColor = this.setFillColor.bind(this);\r\n    this.setStrokeWidth = this.setStrokeWidth.bind(this);\r\n    this.setStrokeDasharray = this.setStrokeDasharray.bind(this);\r\n    this.setOpacity = this.setOpacity.bind(this);\r\n    this.createVisual = this.createVisual.bind(this);\r\n\r\n    this.strokePanel = new ColorPickerPanel(\r\n      'Line color',\r\n      [...settings.defaultColorSet, 'transparent'],\r\n      settings.defaultColor\r\n    );\r\n    this.strokePanel.onColorChanged = this.setStrokeColor;\r\n\r\n    this.fillPanel = new ColorPickerPanel(\r\n      'Fill color',\r\n      [...settings.defaultColorSet, 'transparent'],\r\n      this.fillColor,\r\n      FillColorIcon\r\n    );\r\n    this.fillPanel.onColorChanged = this.setFillColor;\r\n\r\n    this.strokeWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.strokeWidthPanel.onWidthChanged = this.setStrokeWidth;\r\n\r\n    this.strokeStylePanel = new LineStylePanel(\r\n      'Line style',\r\n      settings.defaultStrokeDasharrays,\r\n      settings.defaultStrokeDasharray\r\n    );\r\n    this.strokeStylePanel.onStyleChanged = this.setStrokeDasharray;\r\n\r\n    this.opacityPanel = new OpacityPanel(\r\n      'Opacity',\r\n      settings.defaultOpacitySteps,\r\n      this.opacity\r\n    );\r\n    this.opacityPanel.onOpacityChanged = this.setOpacity;\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (super.ownsTarget(el) || el === this.visual) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates marker visual.\r\n   */\r\n  protected createVisual(): void {\r\n    this.visual = SvgHelper.createEllipse(this.width / 2, this.height / 2, [\r\n      ['fill', this.fillColor],\r\n      ['stroke', this.strokeColor],\r\n      ['stroke-width', this.strokeWidth.toString()],\r\n      ['stroke-dasharray', this.strokeDasharray],\r\n      ['opacity', this.opacity.toString()]\r\n    ]);\r\n    this.addMarkerVisualToContainer(this.visual);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n\r\n      this.moveVisual(point);\r\n\r\n      this._state = 'creating';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    super.manipulate(point);\r\n  }\r\n\r\n  /**\r\n   * Resize marker based on current pointer coordinates and context.\r\n   * @param point \r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Sets marker's visual size after manipulation.\r\n   */\r\n  protected setSize(): void {\r\n    super.setSize();\r\n    SvgHelper.setAttributes(this.visual, [\r\n      ['cx', (this.width / 2).toString()],\r\n      ['cy', (this.height / 2).toString()],\r\n      ['rx', (this.width / 2).toString()],\r\n      ['ry', (this.height / 2).toString()],\r\n    ]);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   * \r\n   * @param point - event coordinates.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    super.pointerUp(point);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Sets marker's line color.\r\n   * @param color - new line color.\r\n   */\r\n  protected setStrokeColor(color: string): void {\r\n    this.strokeColor = color;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke', this.strokeColor]]);\r\n    }\r\n    this.colorChanged(color);\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's fill (background) color.\r\n   * @param color - new fill color.\r\n   */\r\n  protected setFillColor(color: string): void {\r\n    this.fillColor = color;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['fill', this.fillColor]]);\r\n    }\r\n    this.fillColorChanged(color);\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's line width.\r\n   * @param width - new line width\r\n   */\r\n  protected setStrokeWidth(width: number): void {\r\n    this.strokeWidth = width;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke-width', this.strokeWidth.toString()]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's border dash array.\r\n   * @param dashes - new dash array.\r\n   */\r\n  protected setStrokeDasharray(dashes: string): void {\r\n    this.strokeDasharray = dashes;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['stroke-dasharray', this.strokeDasharray]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's opacity.\r\n   * @param opacity - new opacity value (0..1).\r\n   */\r\n  protected setOpacity(opacity: number): void {\r\n    this.opacity = opacity;\r\n    if (this.visual) {\r\n      SvgHelper.setAttributes(this.visual, [['opacity', this.opacity.toString()]]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.fillPanel, this.strokeWidthPanel, this.strokeStylePanel, this.opacityPanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result: RectangleMarkerState = Object.assign({\r\n      fillColor: this.fillColor,\r\n      strokeColor: this.strokeColor,\r\n      strokeWidth: this.strokeWidth,\r\n      strokeDasharray: this.strokeDasharray,\r\n      opacity: this.opacity\r\n    }, super.getState());\r\n    result.typeName = EllipseMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    const rectState = state as RectangleMarkerState;\r\n    this.fillColor = rectState.fillColor;\r\n    this.strokeColor = rectState.strokeColor;\r\n    this.strokeWidth = rectState.strokeWidth;\r\n    this.strokeDasharray = rectState.strokeDasharray;\r\n    this.opacity = rectState.opacity;\r\n\r\n    this.createVisual();\r\n    super.restoreState(state);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.setSize();\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { Settings } from '../../core/Settings';\r\nimport Icon from './measurement-marker-icon.svg';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { LineMarker } from '../line-marker/LineMarker';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\nimport { LineMarkerState } from '../line-marker/LineMarkerState';\r\n\r\nexport class MeasurementMarker extends LineMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'MeasurementMarker';\r\n\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Measurement marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  private tip1: SVGLineElement;\r\n  private tip2: SVGLineElement;\r\n\r\n  private get tipLength(): number {\r\n    return 10 + this.strokeWidth * 3;\r\n  }\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.tip1 || el === this.tip2\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private createTips() {\r\n    this.tip1 = SvgHelper.createLine(\r\n      this.x1 - this.tipLength / 2, \r\n      this.y1, \r\n      this.x1 + this.tipLength / 2, \r\n      this.y1, \r\n      [\r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()]\r\n      ]);\r\n    this.tip1.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.visual.appendChild(this.tip1);\r\n\r\n    this.tip2 = SvgHelper.createLine(\r\n      this.x2 - this.tipLength / 2, \r\n      this.y2, \r\n      this.x2 + this.tipLength / 2, \r\n      this.y2, \r\n      [\r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()]\r\n      ]);\r\n    this.tip2.transform.baseVal.appendItem(SvgHelper.createTransform());\r\n    this.visual.appendChild(this.tip2);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n    if (this.state === 'creating') {\r\n      this.createTips();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adjusts marker visual after manipulation.\r\n   */\r\n  protected adjustVisual(): void {\r\n    super.adjustVisual();\r\n\r\n    if (this.tip1 && this.tip2) {\r\n\r\n      SvgHelper.setAttributes(this.tip1,[\r\n        ['x1', (this.x1 - this.tipLength / 2).toString()], \r\n        ['y1', this.y1.toString()], \r\n        ['x2', (this.x1 + this.tipLength / 2).toString()], \r\n        ['y2', this.y1.toString()], \r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()]\r\n      ]);\r\n      SvgHelper.setAttributes(this.tip2,[\r\n        ['x1', (this.x2 - this.tipLength / 2).toString()], \r\n        ['y1', this.y2.toString()], \r\n        ['x2', (this.x2 + this.tipLength / 2).toString()], \r\n        ['y2', this.y2.toString()], \r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()]\r\n      ]);\r\n\r\n      if (Math.abs(this.x1 - this.x2) > 0.1) {\r\n        const lineAngle1 =\r\n          (Math.atan((this.y2 - this.y1) / (this.x2 - this.x1)) * 180) / Math.PI + 90 * Math.sign(this.x1 - this.x2);\r\n\r\n        const a1transform = this.tip1.transform.baseVal.getItem(0);\r\n        a1transform.setRotate(lineAngle1, this.x1, this.y1);\r\n        this.tip1.transform.baseVal.replaceItem(a1transform, 0);\r\n\r\n        const a2transform = this.tip2.transform.baseVal.getItem(0);\r\n        a2transform.setRotate(lineAngle1 + 180, this.x2, this.y2);\r\n        this.tip2.transform.baseVal.replaceItem(a2transform, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): LineMarkerState {\r\n    const result =super.getState();\r\n    result.typeName = MeasurementMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n\r\n    this.createTips();\r\n    this.adjustVisual();\r\n  }\r\n}\r\n", "import Icon from './ellipse-frame-marker-icon.svg';\r\nimport { Settings } from '../../core/Settings';\r\nimport { RectangleMarkerState } from '../RectangleMarkerState';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { EllipseMarker } from '../ellipse-marker/EllipseMarker';\r\n\r\nexport class EllipseFrameMarker extends EllipseMarker {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'EllipseFrameMarker';\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Ellipse frame marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    // reset colors so 'transparent' is excluded\r\n    this.strokePanel.colors= settings.defaultColorSet;\r\n\r\n    this.fillColor = 'transparent';\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): RectangleMarkerState {\r\n    const result = super.getState();\r\n    result.typeName = EllipseFrameMarker.typeName;\r\n    return result;\r\n  }\r\n}\r\n", "/**\r\n * Manages undo and redo stacks.\r\n */\r\nexport class UndoRedoManager<T> {\r\n  private undoStack: T[] = [];\r\n  private redoStack: T[] = [];\r\n\r\n  private lastRedoStep: T;\r\n\r\n  /**\r\n   * Returns true if there are items in the undo stack.\r\n   */\r\n  public get isUndoPossible(): boolean {\r\n    return this.undoStack.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Returns true if there are items in the redo stack.\r\n   */\r\n  public get isRedoPossible(): boolean {\r\n    return this.redoStack.length > 0;\r\n  }\r\n\r\n  /**\r\n   * Returns the number of items in the undo stack\r\n   *\r\n   * @since 2.23.0\r\n   */\r\n  public get undoStepCount(): number {\r\n    return this.undoStack.length;\r\n  }\r\n\r\n  /**\r\n   * Returns the number of items in the redo stack\r\n   *\r\n   * @since 2.23.0\r\n   */\r\n   public get redoStepCount(): number {\r\n    return this.redoStack.length;\r\n  }\r\n\r\n  /**\r\n   * Adds a step to the undo stack.\r\n   * @param stepData data representing a state.\r\n   */\r\n  public addUndoStep(stepData: T): void {\r\n    if (\r\n      this.undoStack.length === 0 ||\r\n      JSON.stringify(this.undoStack[this.undoStack.length - 1]) !==\r\n        JSON.stringify(stepData)\r\n    ) {\r\n        this.undoStack.push(stepData);\r\n        if (JSON.stringify(this.lastRedoStep) !== JSON.stringify(stepData)) {\r\n          this.redoStack.splice(0, this.redoStack.length);\r\n        }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Replaces the last undo step with step data provided\r\n   * @param stepData data representing a state.\r\n   */\r\n  public replaceLastUndoStep(stepData: T): void {\r\n    if (this.undoStack.length > 0) {\r\n        this.undoStack[this.undoStack.length - 1] = stepData;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns the last step in the undo log\r\n   */\r\n  public getLastUndoStep(): T | undefined {\r\n    if (this.undoStack.length > 0) {\r\n        return this.undoStack[this.undoStack.length - 1];\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns data for the previous step in the undo stack and adds last step to the redo stack.\r\n   * @returns \r\n   */\r\n  public undo(): T | undefined {\r\n    if (this.undoStack.length > 1) {\r\n      const lastStep = this.undoStack.pop();\r\n      if (lastStep !== undefined) {\r\n        this.redoStack.push(lastStep);\r\n      }\r\n      return this.undoStack.length > 0 ? this.undoStack[this.undoStack.length - 1] : undefined;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns most recent item in the redo stack.\r\n   * @returns \r\n   */\r\n  public redo(): T | undefined {\r\n    this.lastRedoStep = this.redoStack.pop();\r\n    return this.lastRedoStep;\r\n  }\r\n}\r\n", "import { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { Settings } from '../../core/Settings';\r\nimport { LinearMarkerBase } from '../LinearMarkerBase';\r\nimport Icon from './curve-marker-icon.svg';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\nimport { LineStylePanel } from '../../ui/toolbox-panels/LineStylePanel';\r\nimport { CurveMarkerState } from './CurveMarkerState';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\nimport { ResizeGrip } from '../ResizeGrip';\r\n\r\nexport class CurveMarker extends LinearMarkerBase {\r\n  /**\r\n   * String type name of the marker type. \r\n   * \r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'CurveMarker';\r\n  \r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Curve marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Invisible wider curve to make selection easier/possible.\r\n   */\r\n  protected selectorCurve: SVGPathElement;\r\n  /**\r\n   * Visible marker curve.\r\n   */\r\n  protected visibleCurve: SVGPathElement;\r\n\r\n  /**\r\n   * Line color.\r\n   */\r\n  protected strokeColor = 'transparent';\r\n  /**\r\n   * Line width.\r\n   */\r\n  protected strokeWidth = 0;\r\n  /**\r\n   * Line dash array.\r\n   */\r\n  protected strokeDasharray = '';\r\n\r\n  /**\r\n   * Color picker panel for line color.\r\n   */\r\n  protected strokePanel: ColorPickerPanel;\r\n  /**\r\n   * Line width toolbox panel.\r\n   */\r\n  protected strokeWidthPanel: LineWidthPanel;\r\n  /**\r\n   * Line dash array toolbox panel.\r\n   */\r\n  protected strokeStylePanel: LineStylePanel;\r\n\r\n  private curveGrip: ResizeGrip;\r\n  private curveX = 0;\r\n  private curveY = 0;\r\n\r\n  private manipulationStartCurveX = 0;\r\n  private manipulationStartCurveY = 0;\r\n\r\n  private curveControlLine1: SVGLineElement;\r\n  private curveControlLine2: SVGLineElement;\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(container: SVGGElement, overlayContainer: HTMLDivElement, settings: Settings) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.setStrokeColor = this.setStrokeColor.bind(this);\r\n    this.setStrokeWidth = this.setStrokeWidth.bind(this);\r\n    this.setStrokeDasharray = this.setStrokeDasharray.bind(this);\r\n    this.positionGrips = this.positionGrips.bind(this);\r\n    this.addControlGrips = this.addControlGrips.bind(this);\r\n    this.adjustVisual = this.adjustVisual.bind(this);\r\n    this.setupControlBox = this.setupControlBox.bind(this);\r\n    this.resize = this.resize.bind(this);\r\n\r\n    this.strokeColor = settings.defaultColor;\r\n    this.strokeWidth = settings.defaultStrokeWidth;\r\n    this.strokeDasharray = settings.defaultStrokeDasharray;\r\n\r\n    this.strokePanel = new ColorPickerPanel(\r\n      'Line color',\r\n      settings.defaultColorSet,\r\n      settings.defaultColor\r\n    );\r\n    this.strokePanel.onColorChanged = this.setStrokeColor;\r\n\r\n    this.strokeWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.strokeWidthPanel.onWidthChanged = this.setStrokeWidth;\r\n\r\n    this.strokeStylePanel = new LineStylePanel(\r\n      'Line style',\r\n      settings.defaultStrokeDasharrays,\r\n      settings.defaultStrokeDasharray\r\n    );\r\n    this.strokeStylePanel.onStyleChanged = this.setStrokeDasharray;\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   * \r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.visual ||\r\n      el === this.selectorCurve ||\r\n      el === this.visibleCurve ||\r\n      this.curveGrip.ownsTarget(el)\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  private getPathD(): string {\r\n    const result = `M ${this.x1} ${this.y1} Q ${this.curveX} ${this.curveY}, ${this.x2} ${this.y2}`;\r\n    return result;\r\n  }\r\n\r\n  private createVisual() {\r\n    this.visual = SvgHelper.createGroup();\r\n    this.selectorCurve = SvgHelper.createPath(\r\n      this.getPathD(),\r\n      [\r\n        ['stroke', 'transparent'],\r\n        ['stroke-width', (this.strokeWidth + 10).toString()],\r\n        ['fill', 'transparent'],\r\n      ]\r\n    );\r\n    this.visibleCurve = SvgHelper.createPath(\r\n      this.getPathD(),\r\n      [\r\n        ['stroke', this.strokeColor],\r\n        ['stroke-width', this.strokeWidth.toString()],\r\n        ['fill', 'transparent'],\r\n      ]\r\n    );\r\n    this.visual.appendChild(this.selectorCurve);\r\n    this.visual.appendChild(this.visibleCurve);\r\n\r\n    this.addMarkerVisualToContainer(this.visual);\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   * \r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n\r\n    this.manipulationStartCurveX = this.curveX;\r\n    this.manipulationStartCurveY = this.curveY;\r\n    if (this.state === 'new') {\r\n      this.curveX = point.x;\r\n      this.curveY = point.y;\r\n    }\r\n\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n      this.adjustVisual();\r\n\r\n      this._state = 'creating';\r\n    } else if (this.curveGrip.ownsTarget(target)) {\r\n      this.activeGrip = this.curveGrip;\r\n      this._state = 'resize';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adjusts visual after manipulation.\r\n   */\r\n  protected adjustVisual(): void {\r\n    if (this.selectorCurve && this.visibleCurve) {\r\n      this.selectorCurve.setAttribute('d', this.getPathD());\r\n\r\n      this.visibleCurve.setAttribute('d', this.getPathD());\r\n\r\n      SvgHelper.setAttributes(this.visibleCurve, [['stroke', this.strokeColor]]);\r\n      SvgHelper.setAttributes(this.visibleCurve, [['stroke-width', this.strokeWidth.toString()]]);\r\n      SvgHelper.setAttributes(this.visibleCurve, [['stroke-dasharray', this.strokeDasharray.toString()]]);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets manipulation grips up.\r\n   */\r\n  protected setupControlBox(): void {\r\n    super.setupControlBox();\r\n    this.curveControlLine1 = SvgHelper.createLine(\r\n      this.x1,\r\n      this.y1,\r\n      this.curveX,\r\n      this.curveY,\r\n      [\r\n        ['stroke', 'black'],\r\n        ['stroke-width', '1'],\r\n        ['stroke-opacity', '0.5'],\r\n        ['stroke-dasharray', '3, 2'],\r\n      ]\r\n    );\r\n    this.curveControlLine2 = SvgHelper.createLine(\r\n      this.x2,\r\n      this.y2,\r\n      this.curveX,\r\n      this.curveY,\r\n      [\r\n        ['stroke', 'black'],\r\n        ['stroke-width', '1'],\r\n        ['stroke-opacity', '0.5'],\r\n        ['stroke-dasharray', '3, 2'],\r\n      ]\r\n    );\r\n\r\n    this.controlBox.insertBefore(this.curveControlLine1, this.controlBox.firstChild);\r\n    this.controlBox.insertBefore(this.curveControlLine2, this.controlBox.firstChild);\r\n  }\r\n\r\n  /**\r\n   * Add manipulation grips to the control box.\r\n   */\r\n  protected addControlGrips(): void {\r\n    this.curveGrip = this.createGrip();\r\n    this.curveX = 0;\r\n    this.curveY = 0;\r\n    super.addControlGrips();\r\n  }\r\n\r\n  /**\r\n   * Positions manipulation grips.\r\n   */\r\n  protected positionGrips(): void {\r\n    super.positionGrips();\r\n    const gripSize = this.curveGrip.GRIP_SIZE;\r\n    this.positionGrip(this.curveGrip.visual, this.curveX - gripSize / 2, this.curveY - gripSize / 2);\r\n\r\n    if (this.curveControlLine1 && this.curveControlLine2) {\r\n      this.curveControlLine1.setAttribute('x1', this.x1.toString());\r\n      this.curveControlLine1.setAttribute('y1', this.y1.toString());\r\n      this.curveControlLine1.setAttribute('x2', this.curveX.toString());\r\n      this.curveControlLine1.setAttribute('y2', this.curveY.toString());\r\n\r\n      this.curveControlLine2.setAttribute('x1', this.x2.toString());\r\n      this.curveControlLine2.setAttribute('y1', this.y2.toString());\r\n      this.curveControlLine2.setAttribute('x2', this.curveX.toString());\r\n      this.curveControlLine2.setAttribute('y2', this.curveY.toString());\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Moves or resizes the marker.\r\n   * @param point event coordinates\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    if (this.state === 'move') {\r\n      this.curveX = this.manipulationStartCurveX + point.x - this.manipulationStartX;\r\n      this.curveY = this.manipulationStartCurveY + point.y - this.manipulationStartY;\r\n    }\r\n    super.manipulate(point);\r\n  }\r\n\r\n  /**\r\n   * Resizes the marker.\r\n   * @param point event coordinates.\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    if (this.activeGrip === this.curveGrip) {\r\n      this.curveX = point.x;\r\n      this.curveY = point.y;\r\n    }\r\n    super.resize(point);\r\n    if (this.state === 'creating') {\r\n      this.curveX = this.x1 + (this.x2 - this.x1) / 2;\r\n      this.curveY = this.y1 + (this.y2 - this.y1) / 2;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Sets line color.\r\n   * @param color - new color.\r\n   */\r\n  protected setStrokeColor(color: string): void {\r\n    this.strokeColor = color;\r\n    this.adjustVisual();\r\n    this.colorChanged(color);\r\n  }\r\n  /**\r\n   * Sets line width.\r\n   * @param width - new width.\r\n   */\r\n  protected setStrokeWidth(width: number): void {\r\n    this.strokeWidth = width\r\n    this.adjustVisual();\r\n  }\r\n\r\n  /**\r\n   * Sets line dash array.\r\n   * @param dashes - new dash array.\r\n   */\r\n  protected setStrokeDasharray(dashes: string): void {\r\n    this.strokeDasharray = dashes;\r\n    this.adjustVisual();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   * \r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    this.curveX = this.curveX * scaleX;\r\n    this.curveY = this.curveY * scaleY;\r\n    super.scale(scaleX, scaleY);\r\n  }\r\n\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [this.strokePanel, this.strokeWidthPanel, this.strokeStylePanel];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): CurveMarkerState {\r\n    const result: CurveMarkerState = Object.assign({\r\n      strokeColor: this.strokeColor,\r\n      strokeWidth: this.strokeWidth,\r\n      strokeDasharray: this.strokeDasharray,\r\n      curveX: this.curveX,\r\n      curveY: this.curveY\r\n    }, super.getState());\r\n    result.typeName = CurveMarker.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   * \r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    super.restoreState(state);\r\n\r\n    const lmState = state as CurveMarkerState;\r\n    this.strokeColor = lmState.strokeColor;\r\n    this.strokeWidth = lmState.strokeWidth;\r\n    this.strokeDasharray = lmState.strokeDasharray;\r\n    this.curveX = lmState.curveX;\r\n    this.curveY = lmState.curveY;\r\n\r\n    this.createVisual();\r\n    this.adjustVisual();\r\n  }\r\n}\r\n", "import Icon from './caption-frame-marker-icon.svg';\r\nimport { IPoint } from '../../core/IPoint';\r\nimport { SvgHelper } from '../../core/SvgHelper';\r\nimport { RectangularBoxMarkerBase } from '../RectangularBoxMarkerBase';\r\nimport { Settings } from '../../core/Settings';\r\nimport { MarkerBaseState } from '../../core/MarkerBaseState';\r\nimport { ColorPickerPanel } from '../../ui/toolbox-panels/ColorPickerPanel';\r\nimport { LineWidthPanel } from '../../ui/toolbox-panels/LineWidthPanel';\r\nimport { LineStylePanel } from '../../ui/toolbox-panels/LineStylePanel';\r\nimport { ToolboxPanel } from '../../ui/ToolboxPanel';\r\nimport FillColorIcon from '../../ui/toolbox-panels/fill-color-icon.svg';\r\nimport TextColorIcon from '../../ui/toolbox-panels/text-color-icon.svg';\r\nimport { FontFamilyPanel } from '../../ui/toolbox-panels/FontFamilyPanel';\r\nimport { CaptionFrameMarkerState } from './CaptionFrameMarkerState';\r\n\r\nexport class CaptionFrameMarker extends RectangularBoxMarkerBase {\r\n  /**\r\n   * String type name of the marker type.\r\n   *\r\n   * Used when adding {@link MarkerArea.availableMarkerTypes} via a string and to save and restore state.\r\n   */\r\n  public static typeName = 'CaptionFrameMarker';\r\n  /**\r\n   * Marker type title (display name) used for accessibility and other attributes.\r\n   */\r\n  public static title = 'Caption frame marker';\r\n  /**\r\n   * SVG icon markup displayed on toolbar buttons.\r\n   */\r\n  public static icon = Icon;\r\n\r\n  /**\r\n   * Caption background (fill) color.\r\n   */\r\n  protected fillColor = 'transparent';\r\n  /**\r\n   * Frame border color.\r\n   */\r\n  protected strokeColor = 'transparent';\r\n  /**\r\n   * Frame border line width.\r\n   */\r\n  protected strokeWidth = 0;\r\n  /**\r\n   * Frame border dash array.\r\n   */\r\n  protected strokeDasharray = '';\r\n  /**\r\n   * Caption font family.\r\n   */\r\n  protected fontFamily: string;\r\n  /**\r\n   * Caption text color.\r\n   */\r\n  protected textColor = 'transparent';\r\n  /**\r\n   * Caption font size.\r\n   */\r\n  protected fontSize = '1rem';\r\n\r\n  /**\r\n   * Frame stroke color toolbox panel.\r\n   */\r\n  protected strokePanel: ColorPickerPanel;\r\n  /**\r\n   * Caption background (fill) color toolbox panel.\r\n   */\r\n  protected fillPanel: ColorPickerPanel;\r\n  /**\r\n   * Frame stroke width toolbox panel.\r\n   */\r\n  protected strokeWidthPanel: LineWidthPanel;\r\n  /**\r\n   * Frame stroke style toolbox panel.\r\n   */\r\n  protected strokeStylePanel: LineStylePanel;\r\n  /**\r\n   * Text font family toolbox panel.\r\n   */\r\n  protected fontFamilyPanel: FontFamilyPanel;\r\n  /**\r\n   * Text color picker toolbox panel.\r\n   */\r\n  protected textColorPanel: ColorPickerPanel;\r\n  /**\r\n   * True if the marker has moved in the manipulation.\r\n   */\r\n  protected isMoved = false;\r\n  private pointerDownPoint: IPoint;\r\n  private pointerDownTimestamp: number;\r\n\r\n  /**\r\n   * Frame rectangle.\r\n   */\r\n  protected frame: SVGRectElement;\r\n  /**\r\n   * Caption background element.\r\n   */\r\n  protected captionBg: SVGRectElement;\r\n  /**\r\n   * Caption text element.\r\n   */\r\n  protected captionElement!: SVGTextElement;\r\n  /**\r\n   * Caption text.\r\n   */\r\n  protected captionText = 'Caption';\r\n\r\n  /**\r\n   * Creates a new marker.\r\n   *\r\n   * @param container - SVG container to hold marker's visual.\r\n   * @param overlayContainer - overlay HTML container to hold additional overlay elements while editing.\r\n   * @param settings - settings object containing default markers settings.\r\n   */\r\n  constructor(\r\n    container: SVGGElement,\r\n    overlayContainer: HTMLDivElement,\r\n    settings: Settings\r\n  ) {\r\n    super(container, overlayContainer, settings);\r\n\r\n    this.strokeColor = settings.defaultColor;\r\n    this.strokeWidth = settings.defaultStrokeWidth;\r\n    this.strokeDasharray = settings.defaultStrokeDasharray;\r\n    this.fillColor = settings.defaultFillColor;\r\n    this.textColor = settings.defaultStrokeColor;\r\n    this.fontFamily = settings.defaultFontFamily;\r\n    this.fontSize = settings.defaultCaptionFontSize;\r\n    this.captionText = settings.defaultCaptionText;\r\n\r\n    this.setStrokeColor = this.setStrokeColor.bind(this);\r\n    this.setFillColor = this.setFillColor.bind(this);\r\n    this.setStrokeWidth = this.setStrokeWidth.bind(this);\r\n    this.setStrokeDasharray = this.setStrokeDasharray.bind(this);\r\n    this.createVisual = this.createVisual.bind(this);\r\n    this.sizeCaption = this.sizeCaption.bind(this);\r\n    this.setCaptionText = this.setCaptionText.bind(this);\r\n    this.showTextEditor = this.showTextEditor.bind(this);\r\n    this.positionTextEditor = this.positionTextEditor.bind(this);\r\n    this.finishTextEditing = this.finishTextEditing.bind(this);\r\n    this.setFont = this.setFont.bind(this);\r\n    this.setTextColor = this.setTextColor.bind(this);\r\n\r\n    this.strokePanel = new ColorPickerPanel(\r\n      'Line color',\r\n      [...settings.defaultColorSet, 'transparent'],\r\n      this.strokeColor\r\n    );\r\n    this.strokePanel.onColorChanged = this.setStrokeColor;\r\n\r\n    this.fillPanel = new ColorPickerPanel(\r\n      'Fill color',\r\n      [...settings.defaultColorSet, 'transparent'],\r\n      this.fillColor,\r\n      FillColorIcon\r\n    );\r\n    this.fillPanel.onColorChanged = this.setFillColor;\r\n\r\n    this.strokeWidthPanel = new LineWidthPanel(\r\n      'Line width',\r\n      settings.defaultStrokeWidths,\r\n      settings.defaultStrokeWidth\r\n    );\r\n    this.strokeWidthPanel.onWidthChanged = this.setStrokeWidth;\r\n\r\n    this.strokeStylePanel = new LineStylePanel(\r\n      'Line style',\r\n      settings.defaultStrokeDasharrays,\r\n      settings.defaultStrokeDasharray\r\n    );\r\n    this.strokeStylePanel.onStyleChanged = this.setStrokeDasharray;\r\n\r\n    this.fontFamilyPanel = new FontFamilyPanel(\r\n      'Font',\r\n      settings.defaultFontFamilies,\r\n      settings.defaultFontFamily\r\n    );\r\n    this.fontFamilyPanel.onFontChanged = this.setFont;\r\n\r\n    this.textColorPanel = new ColorPickerPanel(\r\n      'Text color',\r\n      settings.defaultColorSet,\r\n      this.textColor,\r\n      TextColorIcon\r\n    );\r\n    this.textColorPanel.onColorChanged = this.setTextColor;\r\n\r\n  }\r\n\r\n  /**\r\n   * Returns true if passed SVG element belongs to the marker. False otherwise.\r\n   *\r\n   * @param el - target element.\r\n   */\r\n  public ownsTarget(el: EventTarget): boolean {\r\n    if (\r\n      super.ownsTarget(el) ||\r\n      el === this.visual ||\r\n      el === this.frame ||\r\n      el === this.captionBg ||\r\n      el === this.captionElement\r\n    ) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Creates marker visual.\r\n   */\r\n  protected createVisual(): void {\r\n    this.visual = SvgHelper.createGroup();\r\n    this.addMarkerVisualToContainer(this.visual);\r\n\r\n    this.captionBg = SvgHelper.createRect(1, 1, [['fill', this.fillColor]]);\r\n    this.visual.appendChild(this.captionBg);\r\n\r\n    this.captionElement = SvgHelper.createText([\r\n      ['fill', this.textColor],\r\n      ['font-family', this.fontFamily],\r\n    ]);\r\n    this.captionElement.style.fontSize = this.fontSize;\r\n    this.captionElement.style.textAnchor = 'start';\r\n    this.captionElement.style.dominantBaseline = 'text-before-edge';\r\n    this.captionElement.textContent = this.captionText;\r\n    this.visual.appendChild(this.captionElement);\r\n\r\n    this.frame = SvgHelper.createRect(this.width, this.height, [\r\n      ['fill', 'transparent'],\r\n      ['stroke', this.strokeColor],\r\n      ['stroke-width', this.strokeWidth.toString()],\r\n      ['stroke-dasharray', this.strokeDasharray],\r\n    ]);\r\n\r\n    this.visual.appendChild(this.frame);\r\n    this.sizeCaption();\r\n  }\r\n\r\n  /**\r\n   * Sets caption text.\r\n   * @param text - new caption text.\r\n   */\r\n  public setCaptionText(text: string): void {\r\n    this.captionText = text;\r\n    this.captionElement.textContent = this.captionText;\r\n    this.sizeCaption();\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) down event.\r\n   *\r\n   * @param point - event coordinates.\r\n   * @param target - direct event target element.\r\n   */\r\n  public pointerDown(point: IPoint, target?: EventTarget): void {\r\n    super.pointerDown(point, target);\r\n\r\n    this.isMoved = false;\r\n    this.pointerDownPoint = point;\r\n    this.pointerDownTimestamp = Date.now();\r\n\r\n    if (this.state === 'new') {\r\n      this.createVisual();\r\n\r\n      this.moveVisual(point);\r\n\r\n      this._state = 'creating';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Handles marker manipulation (move, resize, rotate, etc.).\r\n   *\r\n   * @param point - event coordinates.\r\n   */\r\n  public manipulate(point: IPoint): void {\r\n    super.manipulate(point);\r\n    if (this.pointerDownPoint !== undefined) {\r\n      this.isMoved =\r\n        Math.abs(point.x - this.pointerDownPoint.x) > 5 ||\r\n        Math.abs(point.y - this.pointerDownPoint.y) > 5;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Resize marker based on current pointer coordinates and context.\r\n   * @param point\r\n   */\r\n  protected resize(point: IPoint): void {\r\n    super.resize(point);\r\n    this.setSize();\r\n  }\r\n\r\n  private readonly PADDING = 5;\r\n  private captionBoxWidth = 0;\r\n  private captionBoxHeight = 0;\r\n  /**\r\n   * Adjusts caption size and location.\r\n   */\r\n  protected sizeCaption(): void {\r\n    const textBBox = this.captionElement.getBBox();\r\n    if (this.captionText.trim() !== '') {\r\n      this.captionBoxWidth = textBBox.width + this.PADDING * 2;\r\n      this.captionBoxHeight = textBBox.height + this.PADDING * 2;\r\n    } else {\r\n      this.captionBoxWidth = 0;\r\n      this.captionBoxHeight = 0;\r\n    }\r\n\r\n    SvgHelper.setAttributes(this.captionBg, [\r\n      ['width', this.captionBoxWidth.toString()],\r\n      ['height', this.captionBoxHeight.toString()],\r\n      [\r\n        'clip-path',\r\n        `path('M0,0 H${this.width} V${this.height} H${-this.width} Z')`,\r\n      ],\r\n    ]);\r\n    SvgHelper.setAttributes(this.captionElement, [\r\n      ['x', this.PADDING.toString()],\r\n      ['y', this.PADDING.toString()],\r\n      [\r\n        'clip-path',\r\n        `path('M0,0 H${this.width - this.PADDING} V${this.height} H${\r\n          -this.width - this.PADDING\r\n        } Z')`,\r\n      ],\r\n    ]);\r\n  }\r\n\r\n  private textEditDiv: HTMLDivElement;\r\n  private textEditBox: HTMLInputElement;\r\n\r\n  private showTextEditor() {\r\n    this._state = 'edit';\r\n    this.overlayContainer.innerHTML = '';\r\n\r\n    this.textEditDiv = document.createElement('div');\r\n    this.textEditDiv.style.flexGrow = '2';\r\n    this.textEditDiv.style.alignItems = 'center';\r\n    this.textEditDiv.style.justifyContent = 'center';\r\n    this.textEditDiv.style.pointerEvents = 'auto';\r\n    this.textEditDiv.style.overflow = 'hidden';\r\n\r\n    this.textEditBox = document.createElement('input');\r\n    this.textEditBox.style.position = 'absolute';\r\n    this.textEditBox.style.width = `${this.width}px`;\r\n    if (this.captionBoxHeight > 0) {\r\n      this.textEditBox.style.height = `${this.captionBoxHeight}px`;\r\n    }\r\n    this.textEditBox.style.fontSize = this.fontSize;\r\n    this.textEditBox.style.fontFamily = this.fontFamily;\r\n    this.textEditBox.style.backgroundColor = this.fillColor;\r\n    this.textEditBox.style.color = this.textColor;\r\n    this.textEditBox.style.borderWidth = '0';\r\n    this.textEditBox.setAttribute('value', this.captionText);\r\n    this.textEditBox.select();\r\n\r\n    this.textEditDiv.appendChild(this.textEditBox);\r\n    this.overlayContainer.appendChild(this.textEditDiv);\r\n\r\n    this.textEditBox.addEventListener('pointerup', (ev) => {\r\n      ev.stopPropagation();\r\n    });\r\n    this.textEditBox.addEventListener('keypress', (ev) => {\r\n      if (ev.key === 'Enter') {\r\n        this.finishTextEditing(this.textEditBox.value);\r\n      }\r\n    });\r\n    this.textEditBox.addEventListener('keyup', (ev) => {\r\n      ev.cancelBubble = true;\r\n    });\r\n    this.textEditBox.addEventListener('blur', () => {\r\n      this.finishTextEditing(this.textEditBox.value);\r\n    });\r\n    this.textEditDiv.addEventListener('pointerup', () => {\r\n      this.finishTextEditing(this.textEditBox.value);\r\n    });\r\n\r\n    this.positionTextEditor();\r\n    this.textEditBox.focus();\r\n  }\r\n\r\n  private positionTextEditor() {\r\n    if (this.state === 'edit') {\r\n      if (this.textEditBox === undefined) {\r\n        this.showTextEditor();\r\n      } else {\r\n        this.textEditBox.style.left = `${this.left}px`;\r\n        this.textEditBox.style.top = `${this.top}px`;\r\n        this.textEditBox.style.transform = `rotate(${this.rotationAngle}deg)`;\r\n        this.textEditBox.style.transformOrigin = `${this.width / 2}px ${\r\n          this.height / 2\r\n        }px`;\r\n      }\r\n    }\r\n  }\r\n\r\n  private finishTextEditing(text: string) {\r\n    this.setCaptionText(text.trim());\r\n    this.overlayContainer.innerHTML = '';\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Sets font family.\r\n   * @param font - new font family.\r\n   */\r\n  protected setFont(font: string): void {\r\n    if (this.captionElement) {\r\n      SvgHelper.setAttributes(this.captionElement, [['font-family', font]]);\r\n    }\r\n    this.fontFamily = font;\r\n    if (this.textEditBox) {\r\n      this.textEditBox.style.fontFamily = this.fontFamily;\r\n    }\r\n    this.sizeCaption();\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Sets text color.\r\n   * @param color - new text color.\r\n   */\r\n  protected setTextColor(color: string): void {\r\n    if (this.captionElement) {\r\n      SvgHelper.setAttributes(this.captionElement, [['fill', color]]);\r\n    }\r\n    this.textColor = color;\r\n    if (this.textEditBox) {\r\n      this.textEditBox.style.color = this.textColor;\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Sets marker's visual size after manipulation.\r\n   */\r\n  protected setSize(): void {\r\n    super.setSize();\r\n    SvgHelper.setAttributes(this.frame, [\r\n      ['width', this.width.toString()],\r\n      ['height', this.height.toString()],\r\n    ]);\r\n    this.sizeCaption();\r\n  }\r\n\r\n  /**\r\n   * Handles pointer (mouse, touch, stylus, etc.) up event.\r\n   *\r\n   * @param point - event coordinates.\r\n   */\r\n  public pointerUp(point: IPoint): void {\r\n    super.pointerUp(point);\r\n    this.setSize();\r\n\r\n    if (!this.isMoved && Date.now() - this.pointerDownTimestamp > 500) {\r\n      this.showTextEditor();\r\n    }\r\n    this.pointerDownPoint = undefined;\r\n  }\r\n\r\n  /**\r\n   * Opens text editor on double-click.\r\n   * @param point\r\n   * @param target\r\n   */\r\n  public dblClick(point: IPoint, target?: EventTarget): void {\r\n    super.dblClick(point, target);\r\n\r\n    this.showTextEditor();\r\n  }\r\n\r\n  /**\r\n   * Sets marker's line color.\r\n   * @param color - new line color.\r\n   */\r\n  protected setStrokeColor(color: string): void {\r\n    this.strokeColor = color;\r\n    if (this.frame) {\r\n      SvgHelper.setAttributes(this.frame, [['stroke', this.strokeColor]]);\r\n    }\r\n    this.colorChanged(color);\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's fill (background) color.\r\n   * @param color - new fill color.\r\n   */\r\n  protected setFillColor(color: string): void {\r\n    this.fillColor = color;\r\n    if (this.captionBg) {\r\n      SvgHelper.setAttributes(this.captionBg, [['fill', this.fillColor]]);\r\n    }\r\n    this.fillColorChanged(color);\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's line width.\r\n   * @param width - new line width\r\n   */\r\n  protected setStrokeWidth(width: number): void {\r\n    this.strokeWidth = width;\r\n    if (this.frame) {\r\n      SvgHelper.setAttributes(this.frame, [\r\n        ['stroke-width', this.strokeWidth.toString()],\r\n      ]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n  /**\r\n   * Sets marker's border dash array.\r\n   * @param dashes - new dash array.\r\n   */\r\n  protected setStrokeDasharray(dashes: string): void {\r\n    this.strokeDasharray = dashes;\r\n    if (this.frame) {\r\n      SvgHelper.setAttributes(this.frame, [\r\n        ['stroke-dasharray', this.strokeDasharray],\r\n      ]);\r\n    }\r\n    this.stateChanged();\r\n  }\r\n\r\n  /**\r\n   * Returns the list of toolbox panels for this marker type.\r\n   */\r\n  public get toolboxPanels(): ToolboxPanel[] {\r\n    return [\r\n      this.strokePanel,\r\n      this.fillPanel,\r\n      this.strokeWidthPanel,\r\n      this.strokeStylePanel,\r\n      this.fontFamilyPanel,\r\n      this.textColorPanel,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Returns current marker state that can be restored in the future.\r\n   */\r\n  public getState(): CaptionFrameMarkerState {\r\n    const result: CaptionFrameMarkerState = Object.assign(\r\n      {\r\n        fillColor: this.fillColor,\r\n        strokeColor: this.strokeColor,\r\n        strokeWidth: this.strokeWidth,\r\n        strokeDasharray: this.strokeDasharray,\r\n        opacity: 1,\r\n        textColor: this.textColor,\r\n        fontFamily: this.fontFamily,\r\n        fontSize: this.fontSize,\r\n        captionText: this.captionText\r\n      },\r\n      super.getState()\r\n    );\r\n    result.typeName = this.typeName;\r\n\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores previously saved marker state.\r\n   *\r\n   * @param state - previously saved state.\r\n   */\r\n  public restoreState(state: MarkerBaseState): void {\r\n    const frState = state as CaptionFrameMarkerState;\r\n    this.fillColor = frState.fillColor;\r\n    this.strokeColor = frState.strokeColor;\r\n    this.strokeWidth = frState.strokeWidth;\r\n    this.strokeDasharray = frState.strokeDasharray;\r\n    this.textColor = frState.textColor;\r\n    this.fontFamily = frState.fontFamily;\r\n    this.captionText = frState.captionText;\r\n    this.fontSize = frState.fontSize;\r\n\r\n    this.createVisual();\r\n    super.restoreState(state);\r\n    this.setSize();\r\n  }\r\n\r\n  /**\r\n   * Scales marker. Used after the image resize.\r\n   *\r\n   * @param scaleX - horizontal scale\r\n   * @param scaleY - vertical scale\r\n   */\r\n  public scale(scaleX: number, scaleY: number): void {\r\n    super.scale(scaleX, scaleY);\r\n\r\n    this.setSize();\r\n  }\r\n}\r\n", "import { MarkerArea } from '../MarkerArea';\r\nimport { MarkerAreaState } from '../MarkerAreaState';\r\nimport { MarkerBase } from './MarkerBase';\r\n\r\nexport class MarkerAreaEvent {\r\n  public markerArea: MarkerArea;\r\n  public cancelable = false;\r\n\r\n  private _defaultPrevented = false;\r\n  public get defaultPrevented(): boolean {\r\n    return this._defaultPrevented;\r\n  }\r\n\r\n  public preventDefault(): void {\r\n    this._defaultPrevented = true;\r\n  }\r\n\r\n  constructor(markerArea: MarkerArea, cancelable = false) {\r\n    this.markerArea = markerArea;\r\n    this.cancelable = cancelable;\r\n  }\r\n}\r\n\r\nexport class MarkerAreaRenderEvent extends MarkerAreaEvent {\r\n  public dataUrl: string;\r\n  public state: MarkerAreaState;\r\n\r\n  constructor(markerArea: MarkerArea, dataUrl: string, state: MarkerAreaState) {\r\n    super(markerArea, false);\r\n    this.dataUrl = dataUrl;\r\n    this.state = state;\r\n  }\r\n}\r\n\r\n\r\nexport class MarkerEvent extends MarkerAreaEvent {\r\n  public marker?: MarkerBase;\r\n\r\n  constructor(markerArea: MarkerArea, marker?: MarkerBase, cancelable = false) {\r\n    super(markerArea, cancelable);\r\n    this.marker = marker;\r\n  }\r\n}\r\n\r\n/**\r\n * General MarkerArea event handler type.\r\n */\r\nexport type MarkerAreaEventHandler = (event: MarkerAreaEvent) => void;\r\n\r\n/**\r\n * MarkerArea render event handler type.\r\n */\r\nexport type MarkerAreaRenderEventHandler = (event: MarkerAreaRenderEvent) => void;\r\n\r\n/**\r\n * Marker event handler type.\r\n */\r\nexport type MarkerEventHandler = (event: MarkerEvent) => void;\r\n\r\n/**\r\n * Describes a repository of MarkerArea event handlers.\r\n */\r\nexport interface IEventListenerRepository {\r\n  /**\r\n   * Event handlers for the `render` event.\r\n   */\r\n  render: MarkerAreaRenderEventHandler[];\r\n  /**\r\n   * Event handlers for the `beforeclose` event.\r\n   */\r\n  beforeclose: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `close` event.\r\n   */\r\n  close: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `show` event.\r\n   */\r\n  show: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `restorestate` event.\r\n   */\r\n  restorestate: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `statechange` event.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  statechange: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `markerselect` event.\r\n   */\r\n  markerselect: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markerdeselect` event.\r\n   */\r\n  markerdeselect: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markercreating` event.\r\n   */\r\n  markercreating: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markercreated` event.\r\n   */\r\n  markercreate: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markerbeforedelete` event.\r\n   */\r\n  markerbeforedelete: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markerdelete` event.\r\n   */\r\n  markerdelete: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `markerchange` event.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  markerchange: MarkerEventHandler[];\r\n  /**\r\n   * Event handlers for the `focus` event.\r\n   * \r\n   * @since 2.19.0\r\n   */\r\n  focus: MarkerAreaEventHandler[];\r\n  /**\r\n   * Event handlers for the `blur` event.\r\n   * \r\n   * @since 2.19.0\r\n   */\r\n  blur: MarkerAreaEventHandler[];\r\n}\r\n\r\n/**\r\n * Event handler type for a specific event type.\r\n */\r\nexport type EventHandler<\r\n  T extends keyof IEventListenerRepository\r\n> = T extends 'markerselect'\r\n  ? MarkerEventHandler\r\n  : T extends 'markerdeselect'\r\n  ? MarkerEventHandler\r\n  : T extends 'markercreating'\r\n  ? MarkerEventHandler\r\n  : T extends 'markercreate'\r\n  ? MarkerEventHandler\r\n  : T extends 'markerbeforedelete'\r\n  ? MarkerEventHandler\r\n  : T extends 'markerdelete'\r\n  ? MarkerEventHandler\r\n  : T extends 'markerchange'\r\n  ? MarkerEventHandler\r\n  : T extends 'render'\r\n  ? MarkerAreaRenderEventHandler\r\n  : MarkerAreaEventHandler;\r\n\r\n/**\r\n * Event handler repository.\r\n */\r\nexport class EventListenerRepository implements IEventListenerRepository {\r\n  /**\r\n   * Event handlers for the `render` event.\r\n   */\r\n  render: MarkerAreaRenderEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `beforeclose` event.\r\n   */\r\n  beforeclose: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `close` event.\r\n   */\r\n  close: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `show` event.\r\n   */\r\n  show: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `restorestate` event.\r\n   */\r\n  restorestate: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `statechange` event.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  statechange: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markerselect` event.\r\n   */\r\n  markerselect: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markerdeselect` event.\r\n   */\r\n  markerdeselect: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markercreating` event.\r\n   */\r\n  markercreating: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markercreate` event.\r\n   */\r\n  markercreate: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markerbeforedelete` event.\r\n   */\r\n  markerbeforedelete: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markerdelete` event.\r\n   */\r\n  markerdelete: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `markerchange` event.\r\n   * \r\n   * @since 2.23.0\r\n   */\r\n  markerchange: MarkerEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `focus` event.\r\n   * \r\n   * @since 2.19.0\r\n   */\r\n  focus: MarkerAreaEventHandler[] = [];\r\n  /**\r\n   * Event handlers for the `blur` event.\r\n   * \r\n   * @since 2.19.0\r\n   */\r\n  blur: MarkerAreaEventHandler[] = [];\r\n\r\n\r\n  /**\r\n   * Add an event handler for a specific event type.\r\n   * @param eventType - event type.\r\n   * @param handler - function to handle the event.\r\n   */\r\n  public addEventListener<T extends keyof IEventListenerRepository>(\r\n    eventType: T,\r\n    handler: EventHandler<T>\r\n  ): void {\r\n    (<Array<EventHandler<T>>>this[eventType]).push(handler);\r\n  }\r\n\r\n  /**\r\n   * Remove an event handler for a specific event type.\r\n   * @param eventType - event type.\r\n   * @param handler - function currently handling the event.\r\n   */\r\n  public removeEventListener<T extends keyof IEventListenerRepository>(\r\n    eventType: T,\r\n    handler: EventHandler<T>\r\n  ): void {\r\n    const index = (<Array<EventHandler<T>>>this[eventType]).indexOf(handler);\r\n    if (index > -1) {\r\n      (<Array<EventHandler<T>>>this[eventType]).splice(index, 1);\r\n    }\r\n  }\r\n}\r\n", "import { SvgHelper } from './core/SvgHelper';\r\nimport { Activator } from './core/Activator';\r\nimport { Renderer } from './core/Renderer';\r\n\r\nimport Logo from './assets/markerjs-logo-m.svg';\r\nimport { MarkerBase } from './core/MarkerBase';\r\nimport { Toolbar, ToolbarButtonType } from './ui/Toolbar';\r\nimport { Toolbox } from './ui/Toolbox';\r\nimport { FrameMarker } from './markers/frame-marker/FrameMarker';\r\nimport { Settings } from './core/Settings';\r\nimport { StyleManager, Style } from './core/Style';\r\nimport { LineMarker } from './markers/line-marker/LineMarker';\r\nimport { TextMarker } from './markers/text-marker/TextMarker';\r\nimport { FreehandMarker } from './markers/freehand-marker/FreehandMarker';\r\nimport { ArrowMarker } from './markers/arrow-marker/ArrowMarker';\r\nimport { CoverMarker } from './markers/cover-marker/CoverMarker';\r\nimport { HighlightMarker } from './markers/highlight-marker/HighlightMarker';\r\nimport { CalloutMarker } from './markers/callout-marker/CalloutMarker';\r\nimport { MarkerAreaState } from './MarkerAreaState';\r\nimport { EllipseMarker } from './markers/ellipse-marker/EllipseMarker';\r\nimport { IStyleSettings } from './core/IStyleSettings';\r\nimport { MeasurementMarker } from './markers/measurement-marker/MeasurementMarker';\r\nimport { IPoint } from './core/IPoint';\r\nimport { EllipseFrameMarker } from './markers/ellipse-frame-marker/EllipseFrameMarker';\r\nimport { UndoRedoManager } from './core/UndoRedoManager';\r\nimport { CurveMarker } from './markers/curve-marker/CurveMarker';\r\nimport { CaptionFrameMarker } from './markers/caption-frame-marker/CaptionFrameMarker';\r\nimport {\r\n  EventHandler,\r\n  EventListenerRepository,\r\n  IEventListenerRepository,\r\n  MarkerAreaEvent,\r\n  MarkerAreaRenderEvent,\r\n  MarkerEvent,\r\n} from './core/Events';\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport type MarkerAreaMode = 'select' | 'create' | 'delete';\r\n\r\n/**\r\n * Identifier for marker type when setting {@linkcode availableMarkerTypes}.\r\n * Marker type can be set as either a string or a marker type reference.\r\n */\r\nexport type MarkerTypeIdentifier = string | typeof MarkerBase;\r\n\r\n/**\r\n * Event handler type for {@linkcode MarkerArea} `render` event.\r\n */\r\nexport type RenderEventHandler = (\r\n  dataURL: string,\r\n  state?: MarkerAreaState\r\n) => void;\r\n/**\r\n * Event handler type for {@linkcode MarkerArea} `close` event.\r\n */\r\nexport type CloseEventHandler = () => void;\r\n\r\n/**\r\n * MarkerArea is the main class of marker.js 2. It controls the behavior and appearance of the library.\r\n *\r\n * The simplest marker.js 2 usage scenario looks something like this:\r\n *\r\n * ```typescript\r\n * import * as markerjs2 from 'markerjs2';\r\n * // create an instance of MarkerArea and pass the target image reference as a parameter\r\n * let markerArea = new markerjs2.MarkerArea(document.getElementById('myimg'));\r\n *\r\n * // register an event listener for when user clicks OK/save in the marker.js UI\r\n * markerArea.addEventListener('render', event => {\r\n *   // we are setting the markup result to replace our original image on the page\r\n *   // but you can set a different image or upload it to your server\r\n *   document.getElementById('myimg').src = event.dataUrl;\r\n * });\r\n *\r\n * // finally, call the show() method and marker.js UI opens\r\n * markerArea.show();\r\n * ```\r\n */\r\nexport class MarkerArea {\r\n  private target: HTMLImageElement | HTMLElement;\r\n  private targetObserver: ResizeObserver;\r\n\r\n  private width: number;\r\n  private height: number;\r\n  private imageWidth: number;\r\n  private imageHeight: number;\r\n  private left: number;\r\n  private top: number;\r\n  private windowHeight: number;\r\n\r\n  private markerImage: SVGSVGElement;\r\n  private markerImageHolder: HTMLDivElement;\r\n  private defs: SVGDefsElement;\r\n\r\n  private coverDiv: HTMLDivElement;\r\n  private uiDiv: HTMLDivElement;\r\n  private contentDiv: HTMLDivElement;\r\n  private editorCanvas: HTMLDivElement;\r\n  private editingTarget: HTMLImageElement | HTMLCanvasElement;\r\n  private overlayContainer: HTMLDivElement;\r\n\r\n  private touchPoints = 0;\r\n\r\n  private logoUI: HTMLElement;\r\n\r\n  /**\r\n   * `targetRoot` is used to set an alternative positioning root for the marker.js UI.\r\n   *\r\n   * This is useful in cases when your target image is positioned, say, inside a div with `position: relative;`\r\n   *\r\n   * ```typescript\r\n   * // set targetRoot to a specific div instead of document.body\r\n   * markerArea.targetRoot = document.getElementById('myRootElement');\r\n   * ```\r\n   *\r\n   * @default document.body\r\n   */\r\n  public targetRoot: HTMLElement;\r\n\r\n  /**\r\n   * Returns a list of all built-in marker types for use with {@linkcode availableMarkerTypes}\r\n   *\r\n   * @readonly\r\n   */\r\n  public get ALL_MARKER_TYPES(): typeof MarkerBase[] {\r\n    return [\r\n      FrameMarker,\r\n      FreehandMarker,\r\n      ArrowMarker,\r\n      TextMarker,\r\n      EllipseFrameMarker,\r\n      EllipseMarker,\r\n      HighlightMarker,\r\n      CalloutMarker,\r\n      MeasurementMarker,\r\n      CoverMarker,\r\n      LineMarker,\r\n      CurveMarker,\r\n      CaptionFrameMarker,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Returns a list of default set of built-in marker types.\r\n   * Used when {@linkcode availableMarkerTypes} isn't set explicitly.\r\n   *\r\n   * @readonly\r\n   */\r\n  public get DEFAULT_MARKER_TYPES(): typeof MarkerBase[] {\r\n    return [\r\n      FrameMarker,\r\n      FreehandMarker,\r\n      ArrowMarker,\r\n      TextMarker,\r\n      EllipseMarker,\r\n      HighlightMarker,\r\n      CalloutMarker,\r\n    ];\r\n  }\r\n\r\n  /**\r\n   * Returns a short list of essential built-in marker types for use with {@linkcode availableMarkerTypes}\r\n   *\r\n   * @readonly\r\n   */\r\n  public get BASIC_MARKER_TYPES(): typeof MarkerBase[] {\r\n    return [\r\n      FrameMarker,\r\n      FreehandMarker,\r\n      ArrowMarker,\r\n      TextMarker,\r\n      HighlightMarker,\r\n    ];\r\n  }\r\n\r\n  private _availableMarkerTypes: typeof MarkerBase[] = this\r\n    .DEFAULT_MARKER_TYPES;\r\n\r\n  /**\r\n   * Gets or sets a list of marker types avaiable to the user in the toolbar.\r\n   * The types can be passed as either type reference or a string type name.\r\n   *\r\n   * ```typescript\r\n   * this.markerArea1.availableMarkerTypes = ['CalloutMarker', ...this.markerArea1.BASIC_MARKER_TYPES];\r\n   * ```\r\n   *\r\n   * @default {@linkcode DEFAULT_MARKER_TYPES}\r\n   */\r\n  public get availableMarkerTypes(): MarkerTypeIdentifier[] {\r\n    return this._availableMarkerTypes;\r\n  }\r\n\r\n  public set availableMarkerTypes(value: MarkerTypeIdentifier[]) {\r\n    this._availableMarkerTypes.splice(0);\r\n    value.forEach((mt) => {\r\n      if (typeof mt === 'string') {\r\n        const typeType = this.ALL_MARKER_TYPES.find(\r\n          (allT) => allT.typeName === mt\r\n        );\r\n        if (typeType !== undefined) {\r\n          this._availableMarkerTypes.push(typeType);\r\n        }\r\n      } else {\r\n        this._availableMarkerTypes.push(mt);\r\n      }\r\n    });\r\n  }\r\n\r\n  private toolbar: Toolbar;\r\n  private toolbox: Toolbox;\r\n\r\n  private mode: MarkerAreaMode = 'select';\r\n\r\n  private _currentMarker?: MarkerBase;\r\n  /**\r\n   * Gets currently selected marker\r\n   *\r\n   * @readonly\r\n   * @since 2.27.0\r\n   */\r\n  public get currentMarker() : MarkerBase | undefined {\r\n    return this._currentMarker;\r\n  }\r\n  private markers: MarkerBase[] = [];\r\n\r\n  private isDragging = false;\r\n\r\n  // for preserving orginal window state before opening the editor\r\n  private bodyOverflowState: string;\r\n  private scrollYState: number;\r\n  private scrollXState: number;\r\n\r\n  private renderEventListeners: RenderEventHandler[] = [];\r\n  private closeEventListeners: CloseEventHandler[] = [];\r\n\r\n  public settings: Settings = new Settings();\r\n  public uiStyleSettings: IStyleSettings;\r\n\r\n  private _isOpen = false;\r\n  /**\r\n   * Returns `true` when MarkerArea is open and `false` otherwise.\r\n   *\r\n   * @readonly\r\n   */\r\n  public get isOpen(): boolean {\r\n    return this._isOpen;\r\n  }\r\n\r\n  private undoRedoManager: UndoRedoManager<\r\n    MarkerAreaState\r\n  > = new UndoRedoManager<MarkerAreaState>();\r\n\r\n  /**\r\n   * Returns true if undo operation can be performed (undo stack is not empty).\r\n   * \r\n   * @since 2.26.0\r\n   */\r\n  public get isUndoPossible(): boolean {\r\n    if (this.undoRedoManager && this.undoRedoManager.isUndoPossible) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns true if redo operation can be performed (redo stack is not empty).\r\n   * \r\n   * @since 2.26.0\r\n   */\r\n   public get isRedoPossible(): boolean {\r\n    if (this.undoRedoManager && this.undoRedoManager.isRedoPossible) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * When set to true resulting image will be rendered at the natural (original) resolution\r\n   * of the target image. Otherwise (default), screen dimensions of the image are used.\r\n   *\r\n   * @default false (use screen dimensions)\r\n   */\r\n  public renderAtNaturalSize = false;\r\n  /**\r\n   * Type of image for the rendering result. Eg. `image/png` (default) or `image/jpeg`.\r\n   *\r\n   * @default `image/png`\r\n   */\r\n  public renderImageType = 'image/png';\r\n  /**\r\n   * When rendering engine/format supports it (jpeg, for exmample),\r\n   * sets the rendering quality for the resulting image.\r\n   *\r\n   * In case of `image/jpeg` the value should be between 0 (worst quality) and 1 (best quality).\r\n   */\r\n  public renderImageQuality?: number;\r\n  /**\r\n   * When set to `true`, will render only the marker layer without the original image.\r\n   * This could be useful when you want to non-destructively overlay markers on top of the original image.\r\n   *\r\n   * Note that in order for the markers layer to have a transparent background {@linkcode renderImageType}\r\n   * should be set to a format supporting transparency, such as `image/png`.\r\n   *\r\n   * @default false\r\n   */\r\n  public renderMarkersOnly = false;\r\n\r\n  /**\r\n   * When set and {@linkcode renderAtNaturalSize} is `false` sets the width of the rendered image.\r\n   *\r\n   * Both `renderWidth` and `renderHeight` have to be set for this to take effect.\r\n   */\r\n  public renderWidth?: number;\r\n  /**\r\n   * When set and {@linkcode renderAtNaturalSize} is `false` sets the height of the rendered image.\r\n   *\r\n   * Both `renderWidth` and `renderHeight` have to be set for this to take effect.\r\n   */\r\n  public renderHeight?: number;\r\n\r\n  /**\r\n   * If a canvas is specified here, then marker.js will render the output to this canvas\r\n   * in addition to generating an image.\r\n   *\r\n   * @since 2.14.0\r\n   */\r\n  public renderTarget?: HTMLCanvasElement;\r\n\r\n  /**\r\n   * Pressing zoom button iterates through values in this array.\r\n   *\r\n   * @since 2.12.0\r\n   */\r\n  public zoomSteps = [1, 1.5, 2, 4];\r\n  private _zoomLevel = 1;\r\n  /**\r\n   * Gets current zoom level.\r\n   *\r\n   * @since 2.12.0\r\n   */\r\n  public get zoomLevel(): number {\r\n    return this._zoomLevel;\r\n  }\r\n  /**\r\n   * Sets current zoom level.\r\n   *\r\n   * @since 2.12.0\r\n   */\r\n  public set zoomLevel(value: number) {\r\n    this._zoomLevel = value;\r\n    if (this.editorCanvas && this.contentDiv) {\r\n      this.editorCanvas.style.transform = `scale(${this._zoomLevel})`;\r\n      this.contentDiv.scrollTo({\r\n        left:\r\n          (this.editorCanvas.clientWidth * this._zoomLevel -\r\n            this.contentDiv.clientWidth) /\r\n          2,\r\n        top:\r\n          (this.editorCanvas.clientHeight * this._zoomLevel -\r\n            this.contentDiv.clientHeight) /\r\n          2,\r\n      });\r\n    }\r\n  }\r\n\r\n  private static instanceCounter = 0;\r\n  private _instanceNo: number;\r\n  public get instanceNo(): number {\r\n    return this._instanceNo;\r\n  }\r\n\r\n  /**\r\n   * Manage style releated settings via the `styles` property.\r\n   */\r\n  public styles: StyleManager;\r\n\r\n  /**\r\n   * Creates a new MarkerArea for the specified target image.\r\n   *\r\n   * ```typescript\r\n   * // create an instance of MarkerArea and pass the target image (or other HTML element) reference as a parameter\r\n   * let markerArea = new markerjs2.MarkerArea(document.getElementById('myimg'));\r\n   * ```\r\n   *\r\n   * When `target` is not an image object the output is limited to \"markers only\" (@linkcode renderMarkersOnly)\r\n   * and \"popup\" mode won't work properly as the target object stays in it's original position and, unlike images,\r\n   * is not copied.\r\n   *\r\n   * @param target image object to mark up.\r\n   */\r\n  constructor(target: HTMLImageElement | HTMLElement) {\r\n    this._instanceNo = MarkerArea.instanceCounter++;\r\n\r\n    this.styles = new StyleManager(this.instanceNo);\r\n\r\n    this.uiStyleSettings = this.styles.settings;\r\n\r\n    this.target = target;\r\n    this.targetRoot = document.body;\r\n\r\n    this.width = target.clientWidth;\r\n    this.height = target.clientHeight;\r\n\r\n    this.styles.removeStyleSheet();\r\n\r\n    this.open = this.open.bind(this);\r\n    this.setTopLeft = this.setTopLeft.bind(this);\r\n\r\n    this.toolbarButtonClicked = this.toolbarButtonClicked.bind(this);\r\n    this.createNewMarker = this.createNewMarker.bind(this);\r\n    this.addNewMarker = this.addNewMarker.bind(this);\r\n    this.markerCreated = this.markerCreated.bind(this);\r\n    this.setCurrentMarker = this.setCurrentMarker.bind(this);\r\n    this.onPointerDown = this.onPointerDown.bind(this);\r\n    this.onDblClick = this.onDblClick.bind(this);\r\n    this.onPointerMove = this.onPointerMove.bind(this);\r\n    this.onPointerUp = this.onPointerUp.bind(this);\r\n    this.onPointerOut = this.onPointerOut.bind(this);\r\n    this.onKeyUp = this.onKeyUp.bind(this);\r\n    this.overrideOverflow = this.overrideOverflow.bind(this);\r\n    this.restoreOverflow = this.restoreOverflow.bind(this);\r\n    this.close = this.close.bind(this);\r\n    this.closeUI = this.closeUI.bind(this);\r\n    this.addCloseEventListener = this.addCloseEventListener.bind(this);\r\n    this.removeCloseEventListener = this.removeCloseEventListener.bind(this);\r\n    this.addRenderEventListener = this.addRenderEventListener.bind(this);\r\n    this.removeRenderEventListener = this.removeRenderEventListener.bind(this);\r\n    this.clientToLocalCoordinates = this.clientToLocalCoordinates.bind(this);\r\n    this.onWindowResize = this.onWindowResize.bind(this);\r\n    this.deleteSelectedMarker = this.deleteSelectedMarker.bind(this);\r\n    this.setWindowHeight = this.setWindowHeight.bind(this);\r\n    this.removeMarker = this.removeMarker.bind(this);\r\n    this.colorChanged = this.colorChanged.bind(this);\r\n    this.fillColorChanged = this.fillColorChanged.bind(this);\r\n    this.onPopupTargetResize = this.onPopupTargetResize.bind(this);\r\n    this.showNotesEditor = this.showNotesEditor.bind(this);\r\n    this.hideNotesEditor = this.hideNotesEditor.bind(this);\r\n    this.stepZoom = this.stepZoom.bind(this);\r\n    this.focus = this.focus.bind(this);\r\n    this.blur = this.blur.bind(this);\r\n    this.markerStateChanged = this.markerStateChanged.bind(this);\r\n    this.switchToSelectMode = this.switchToSelectMode.bind(this);\r\n    this.addDefs = this.addDefs.bind(this);\r\n    this.addDefsToImage = this.addDefsToImage.bind(this);\r\n  }\r\n\r\n  private open(): void {\r\n    this.setupResizeObserver();\r\n    this.setEditingTarget();\r\n    this.setTopLeft();\r\n    this.initMarkerCanvas();\r\n    this.initOverlay();\r\n    this.attachEvents();\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.onPopupTargetResize();\r\n    }\r\n\r\n    if (!Activator.isLicensed) {\r\n      // NOTE:\r\n      // before removing this call please consider supporting marker.js\r\n      // by visiting https://markerjs.com/ for details\r\n      // thank you!\r\n      this.addLogo();\r\n    }\r\n\r\n    this._isOpen = true;\r\n    this._isFocused = true;\r\n  }\r\n\r\n  /**\r\n   * Initializes the MarkerArea and opens the UI.\r\n   */\r\n  public show(): void {\r\n    // backwards compatibility with deprecated static Style class\r\n    if (this.styles.styleSheetRoot === undefined && Style.styleSheetRoot !== undefined) {\r\n      this.styles.styleSheetRoot = Style.styleSheetRoot;\r\n    }\r\n\r\n    // reset markers array\r\n    this.markers.splice(0);\r\n\r\n    this.setWindowHeight();\r\n    this.showUI();\r\n    this.open();\r\n    this.eventListeners['show'].forEach((listener) =>\r\n      listener(new MarkerAreaEvent(this))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Renders the annotation result.\r\n   *\r\n   * Normally, you should use {@linkcode addEventListener} method to set a listener for the `render` event\r\n   * rather than calling this method directly.\r\n   */\r\n  public async render(): Promise<string> {\r\n    this.setCurrentMarker();\r\n\r\n    const renderer = new Renderer();\r\n    renderer.naturalSize = this.renderAtNaturalSize;\r\n    renderer.imageType = this.renderImageType;\r\n    renderer.imageQuality = this.renderImageQuality;\r\n    renderer.markersOnly = this.renderMarkersOnly;\r\n    renderer.width = this.renderWidth;\r\n    renderer.height = this.renderHeight;\r\n\r\n    // workaround for an issue in Safari where FreeHand marker\r\n    // is not rendered on the first try for some reason\r\n    await renderer.rasterize(\r\n      this.target instanceof HTMLImageElement ? this.target : null,\r\n      this.markerImage,\r\n      this.renderTarget\r\n    );\r\n\r\n    return await renderer.rasterize(\r\n      this.target instanceof HTMLImageElement ? this.target : null,\r\n      this.markerImage,\r\n      this.renderTarget\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Closes the MarkerArea UI.\r\n   */\r\n  public close(suppressBeforeClose = false): void {\r\n    if (this.isOpen) {\r\n      let cancel = false;\r\n\r\n      if (!suppressBeforeClose) {\r\n        this.eventListeners['beforeclose'].forEach((listener) => {\r\n          const ev = new MarkerAreaEvent(this, true);\r\n          listener(ev);\r\n          if (ev.defaultPrevented) {\r\n            cancel = true;\r\n          }\r\n        });\r\n      }\r\n\r\n      if (!cancel) {\r\n        if (this.coverDiv) {\r\n          this.closeUI();\r\n        }\r\n        if (this.targetObserver) {\r\n          this.targetObserver.unobserve(this.target);\r\n          this.targetObserver.unobserve(this.editorCanvas);\r\n        }\r\n        if (this.settings.displayMode === 'popup') {\r\n          window.removeEventListener('resize', this.setWindowHeight);\r\n        }\r\n        //this.closeEventListeners.forEach((listener) => listener());\r\n        this.eventListeners['close'].forEach((listener) =>\r\n          listener(new MarkerAreaEvent(this))\r\n        );\r\n        this.detachEvents();\r\n        this._isOpen = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Adds one or more markers to the toolbar.\r\n   *\r\n   * @param markers - one or more marker types to be added.\r\n   */\r\n  public addMarkersToToolbar(...markers: typeof MarkerBase[]): void {\r\n    this._availableMarkerTypes.push(...markers);\r\n  }\r\n\r\n  /**\r\n   * Add a `render` event listener which is called when user clicks on the OK/save button\r\n   * in the toolbar.\r\n   *\r\n   * ```typescript\r\n   * // register an event listener for when user clicks OK/save in the marker.js UI\r\n   * markerArea.addRenderEventListener(dataUrl => {\r\n   *   // we are setting the markup result to replace our original image on the page\r\n   *   // but you can set a different image or upload it to your server\r\n   *   document.getElementById('myimg').src = dataUrl;\r\n   * });\r\n   * ```\r\n   *\r\n   * This is where you place your code to save a resulting image and/or MarkerAreaState.\r\n   *\r\n   * @param listener - a method handling rendering results\r\n   *\r\n   * @see {@link MarkerAreaState}\r\n   * @deprecated use `addEventListener('render', ...)` instead.\r\n   */\r\n  public addRenderEventListener(listener: RenderEventHandler): void {\r\n    //this.renderEventListeners.push(listener);\r\n    this.addEventListener('render', (event: MarkerAreaRenderEvent) => {\r\n      listener(event.dataUrl, event.state);\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Remove a `render` event handler.\r\n   *\r\n   * @param listener - previously registered `render` event handler.\r\n   * @deprecated use `removeEventListener('render', ...)` instead.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  public removeRenderEventListener(listener: RenderEventHandler): void {\r\n    // if (this.renderEventListeners.indexOf(listener) > -1) {\r\n    //   this.renderEventListeners.splice(\r\n    //     this.renderEventListeners.indexOf(listener),\r\n    //     1\r\n    //   );\r\n    // }\r\n  }\r\n\r\n  /**\r\n   * Add a `close` event handler to perform actions in your code after the user\r\n   * clicks on the close button (without saving).\r\n   *\r\n   * @param listener - close event listener\r\n   * @deprecated use `addEventListener('close', ...)` instead.\r\n   */\r\n  public addCloseEventListener(listener: CloseEventHandler): void {\r\n    //this.closeEventListeners.push(listener);\r\n    this.addEventListener('close', () => {\r\n      listener();\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Remove a `close` event handler.\r\n   *\r\n   * @param listener - previously registered `close` event handler.\r\n   * @deprecated use `removeEventListener('close', ...)` instead.\r\n   */\r\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\r\n  public removeCloseEventListener(listener: CloseEventHandler): void {\r\n    // if (this.closeEventListeners.indexOf(listener) > -1) {\r\n    //   this.closeEventListeners.splice(\r\n    //     this.closeEventListeners.indexOf(listener),\r\n    //     1\r\n    //   );\r\n    // }\r\n  }\r\n\r\n  private setupResizeObserver() {\r\n    if (this.settings.displayMode === 'inline') {\r\n      if (window.ResizeObserver) {\r\n        this.targetObserver = new ResizeObserver(() => {\r\n          this.resize(this.target.clientWidth, this.target.clientHeight);\r\n        });\r\n        this.targetObserver.observe(this.target);\r\n      }\r\n    } else if (this.settings.displayMode === 'popup') {\r\n      if (window.ResizeObserver) {\r\n        this.targetObserver = new ResizeObserver(() =>\r\n          this.onPopupTargetResize()\r\n        );\r\n        this.targetObserver.observe(this.editorCanvas);\r\n      }\r\n      window.addEventListener('resize', this.setWindowHeight);\r\n    }\r\n  }\r\n\r\n  private onPopupTargetResize() {\r\n    const ratio = (1.0 * this.target.clientWidth) / this.target.clientHeight;\r\n    const newWidth =\r\n      this.editorCanvas.clientWidth / ratio > this.editorCanvas.clientHeight\r\n        ? this.editorCanvas.clientHeight * ratio\r\n        : this.editorCanvas.clientWidth;\r\n    const newHeight =\r\n      newWidth < this.editorCanvas.clientWidth\r\n        ? this.editorCanvas.clientHeight\r\n        : this.editorCanvas.clientWidth / ratio;\r\n    this.resize(newWidth, newHeight);\r\n  }\r\n\r\n  private setWindowHeight() {\r\n    this.windowHeight = window.innerHeight;\r\n  }\r\n\r\n  private _isResizing = false;\r\n  private resize(newWidth: number, newHeight: number) {\r\n    this._isResizing = true;\r\n\r\n    const scaleX = newWidth / this.imageWidth;\r\n    const scaleY = newHeight / this.imageHeight;\r\n\r\n    this.imageWidth = Math.round(newWidth);\r\n    this.imageHeight = Math.round(newHeight);\r\n    if (\r\n      this.target instanceof HTMLImageElement &&\r\n      this.editingTarget instanceof HTMLImageElement\r\n    ) {\r\n      this.editingTarget.src = this.target.src;\r\n    }\r\n    this.editingTarget.width = this.imageWidth;\r\n    this.editingTarget.height = this.imageHeight;\r\n    this.editingTarget.style.width = `${this.imageWidth}px`;\r\n    this.editingTarget.style.height = `${this.imageHeight}px`;\r\n\r\n    this.markerImage.setAttribute('width', this.imageWidth.toString());\r\n    this.markerImage.setAttribute('height', this.imageHeight.toString());\r\n    this.markerImage.setAttribute(\r\n      'viewBox',\r\n      '0 0 ' + this.imageWidth.toString() + ' ' + this.imageHeight.toString()\r\n    );\r\n\r\n    this.markerImageHolder.style.width = `${this.imageWidth}px`;\r\n    this.markerImageHolder.style.height = `${this.imageHeight}px`;\r\n\r\n    this.overlayContainer.style.width = `${this.imageWidth}px`;\r\n    this.overlayContainer.style.height = `${this.imageHeight}px`;\r\n\r\n    if (this.settings.displayMode !== 'popup') {\r\n      this.coverDiv.style.width = `${this.imageWidth.toString()}px`;\r\n    } else {\r\n      this.setTopLeft();\r\n      this.positionMarkerImage();\r\n    }\r\n\r\n    if (this.toolbar !== undefined) {\r\n      this.toolbar.adjustLayout();\r\n    }\r\n\r\n    this.positionLogo();\r\n\r\n    this.scaleMarkers(scaleX, scaleY);\r\n\r\n    this._isResizing = false;\r\n  }\r\n\r\n  private scaleMarkers(scaleX: number, scaleY: number) {\r\n    let preScaleSelectedMarker: MarkerBase;\r\n    if (!(this._currentMarker && this._currentMarker instanceof TextMarker)) {\r\n      // can't unselect text marker as it would hide keyboard on mobile\r\n      preScaleSelectedMarker = this._currentMarker;\r\n      this.setCurrentMarker();\r\n    } else {\r\n      this._currentMarker.scale(scaleX, scaleY);\r\n    }\r\n    this.markers.forEach((marker) => {\r\n      if (marker !== this._currentMarker) {\r\n        marker.scale(scaleX, scaleY)\r\n      }\r\n    });\r\n    if (preScaleSelectedMarker !== undefined) {\r\n      this.setCurrentMarker(preScaleSelectedMarker);\r\n    }\r\n  }\r\n\r\n  private setEditingTarget() {\r\n    this.imageWidth = Math.round(this.target.clientWidth);\r\n    this.imageHeight = Math.round(this.target.clientHeight);\r\n    if (\r\n      this.target instanceof HTMLImageElement &&\r\n      this.editingTarget instanceof HTMLImageElement\r\n    ) {\r\n      this.editingTarget.src = this.target.src;\r\n    }\r\n    this.editingTarget.width = this.imageWidth;\r\n    this.editingTarget.height = this.imageHeight;\r\n    this.editingTarget.style.width = `${this.imageWidth}px`;\r\n    this.editingTarget.style.height = `${this.imageHeight}px`;\r\n  }\r\n\r\n  private setTopLeft() {\r\n    const targetRect = this.editingTarget.getBoundingClientRect();\r\n    const bodyRect = this.editorCanvas.getBoundingClientRect();\r\n    this.left = targetRect.left - bodyRect.left;\r\n    this.top = targetRect.top - bodyRect.top;\r\n  }\r\n\r\n  private initMarkerCanvas(): void {\r\n    this.markerImageHolder = document.createElement('div');\r\n    this.markerImageHolder.style.setProperty('touch-action', 'pinch-zoom');\r\n\r\n    this.markerImage = document.createElementNS(\r\n      'http://www.w3.org/2000/svg',\r\n      'svg'\r\n    );\r\n    this.markerImage.setAttribute('xmlns', 'http://www.w3.org/2000/svg');\r\n    this.markerImage.setAttribute('width', this.imageWidth.toString());\r\n    this.markerImage.setAttribute('height', this.imageHeight.toString());\r\n    this.markerImage.setAttribute(\r\n      'viewBox',\r\n      '0 0 ' + this.imageWidth.toString() + ' ' + this.imageHeight.toString()\r\n    );\r\n    this.markerImage.style.pointerEvents = 'auto';\r\n\r\n    this.markerImageHolder.style.position = 'absolute';\r\n    this.markerImageHolder.style.width = `${this.imageWidth}px`;\r\n    this.markerImageHolder.style.height = `${this.imageHeight}px`;\r\n    this.markerImageHolder.style.transformOrigin = 'top left';\r\n    this.positionMarkerImage();\r\n\r\n    this.markerImageHolder.appendChild(this.markerImage);\r\n\r\n    this.editorCanvas.appendChild(this.markerImageHolder);\r\n  }\r\n\r\n  /**\r\n   * Adds \"defs\" element to the marker SVG element.\r\n   * Useful for using custom fonts and potentially other scenarios.\r\n   *\r\n   * @param {(...(string | Node)[])} nodes\r\n   * @see Documentation article on adding custom fonts for an example\r\n   */\r\n  public addDefs(...nodes: (string | Node)[]): void {\r\n    this.defs = SvgHelper.createDefs();\r\n    this.addDefsToImage();\r\n\r\n    this.defs.append(...nodes);\r\n  }\r\n\r\n  private addDefsToImage() {\r\n    if (this.defs) {\r\n      this.markerImage.insertBefore(this.defs, this.markerImage.firstChild);\r\n    }\r\n  }\r\n\r\n  private initOverlay(): void {\r\n    this.overlayContainer = document.createElement('div');\r\n    this.overlayContainer.style.position = 'absolute';\r\n    this.overlayContainer.style.left = '0px';\r\n    this.overlayContainer.style.top = '0px';\r\n    this.overlayContainer.style.width = `${this.imageWidth}px`;\r\n    this.overlayContainer.style.height = `${this.imageHeight}px`;\r\n    this.overlayContainer.style.display = 'flex';\r\n    this.markerImageHolder.appendChild(this.overlayContainer);\r\n  }\r\n\r\n  private positionMarkerImage() {\r\n    this.markerImageHolder.style.top = this.top / this.zoomLevel + 'px';\r\n    this.markerImageHolder.style.left = this.left / this.zoomLevel + 'px';\r\n  }\r\n\r\n  private attachEvents() {\r\n    this.markerImage.addEventListener('pointerdown', this.onPointerDown);\r\n    // workaround to prevent a bug with Apple Pencil\r\n    // https://bugs.webkit.org/show_bug.cgi?id=217430\r\n    this.markerImage.addEventListener('touchmove', ev => ev.preventDefault());\r\n    \r\n    this.markerImage.addEventListener('dblclick', this.onDblClick);\r\n    this.attachWindowEvents();\r\n  }\r\n\r\n  private attachWindowEvents() {\r\n    window.addEventListener('pointermove', this.onPointerMove);\r\n    window.addEventListener('pointerup', this.onPointerUp);\r\n    window.addEventListener('pointercancel', this.onPointerOut);\r\n    window.addEventListener('pointerout', this.onPointerOut);\r\n    window.addEventListener('pointerleave', this.onPointerUp);\r\n    window.addEventListener('resize', this.onWindowResize);\r\n    window.addEventListener('keyup', this.onKeyUp);\r\n  }\r\n\r\n  private detachEvents() {\r\n    this.markerImage.removeEventListener('pointerdown', this.onPointerDown);\r\n    this.markerImage.removeEventListener('dblclick', this.onDblClick);\r\n    this.detachWindowEvents();\r\n  }\r\n\r\n  private detachWindowEvents() {\r\n    window.removeEventListener('pointermove', this.onPointerMove);\r\n    window.removeEventListener('pointerup', this.onPointerUp);\r\n    window.removeEventListener('pointercancel', this.onPointerOut);\r\n    window.removeEventListener('pointerout', this.onPointerOut);\r\n    window.removeEventListener('pointerleave', this.onPointerUp);\r\n    window.removeEventListener('resize', this.onWindowResize);\r\n    window.removeEventListener('keyup', this.onKeyUp);\r\n  }\r\n\r\n  /**\r\n   * NOTE:\r\n   *\r\n   * before removing or modifying this method please consider supporting marker.js\r\n   * by visiting https://markerjs.com/#price for details\r\n   *\r\n   * thank you!\r\n   */\r\n  private addLogo() {\r\n    this.logoUI = document.createElement('div');\r\n    this.logoUI.style.display = 'inline-block';\r\n    this.logoUI.style.margin = '0px';\r\n    this.logoUI.style.padding = '0px';\r\n    this.logoUI.style.fill = '#333333';\r\n\r\n    const link = document.createElement('a');\r\n    link.href = 'https://markerjs.com/';\r\n    link.target = '_blank';\r\n    link.innerHTML = Logo;\r\n    link.title = 'Powered by marker.js';\r\n\r\n    link.style.display = 'grid';\r\n    link.style.alignItems = 'center';\r\n    link.style.justifyItems = 'center';\r\n    link.style.padding = '3px';\r\n    link.style.width = '20px';\r\n    link.style.height = '20px';\r\n\r\n    this.logoUI.appendChild(link);\r\n\r\n    this.editorCanvas.appendChild(this.logoUI);\r\n\r\n    this.logoUI.style.position = 'absolute';\r\n    this.logoUI.style.pointerEvents = 'all';\r\n    this.positionLogo();\r\n  }\r\n\r\n  private positionLogo() {\r\n    if (this.logoUI) {\r\n      if (this.uiStyleSettings.logoPosition !== 'right') {\r\n        this.logoUI.style.left = `${this.markerImageHolder.offsetLeft + 10}px`;\r\n      } else {\r\n        this.logoUI.style.left = `${\r\n          this.markerImageHolder.offsetLeft +\r\n          this.markerImageHolder.offsetWidth -\r\n          this.logoUI.clientWidth -\r\n          10\r\n        }px`;\r\n      }\r\n      this.logoUI.style.top = `${\r\n        this.markerImageHolder.offsetTop +\r\n        this.markerImageHolder.offsetHeight -\r\n        this.logoUI.clientHeight -\r\n        10\r\n      }px`;\r\n    }\r\n  }\r\n\r\n  private overrideOverflow() {\r\n    // backup current state of scrolling and overflow\r\n    this.scrollXState = window.scrollX;\r\n    this.scrollYState = window.scrollY;\r\n    this.bodyOverflowState = document.body.style.overflow;\r\n\r\n    window.scroll({ top: 0, left: 0 });\r\n    document.body.style.overflow = 'hidden';\r\n  }\r\n\r\n  private restoreOverflow() {\r\n    document.body.style.overflow = this.bodyOverflowState;\r\n    window.scroll({ top: this.scrollYState, left: this.scrollXState });\r\n  }\r\n\r\n  private showUI(): void {\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.overrideOverflow();\r\n    }\r\n\r\n    this.coverDiv = document.createElement('div');\r\n    // prevent UI from blinking when just rendering state\r\n    this.coverDiv.style.visibility = this._silentRenderMode\r\n      ? 'hidden'\r\n      : 'visible';\r\n    this.coverDiv.className = `${this.styles.classNamePrefixBase} ${this.styles.classNamePrefix}`;\r\n    // hardcode font size so nothing inside is affected by higher up settings\r\n    this.coverDiv.style.fontSize = '16px';\r\n    this.coverDiv.style.userSelect = 'none';\r\n\r\n    switch (this.settings.displayMode) {\r\n      case 'inline': {\r\n        this.coverDiv.style.position = 'absolute';\r\n        const coverTop = this.settings.uiOffsetTop !== undefined ? \r\n          this.target.offsetTop + this.settings.uiOffsetTop :\r\n            this.target.offsetTop > this.styles.settings.toolbarHeight\r\n              ? this.target.offsetTop - this.styles.settings.toolbarHeight\r\n              : 0;\r\n        const coverLeft = this.target.offsetLeft + (this.settings.uiOffsetLeft ?? 0); \r\n        this.coverDiv.style.top = `${coverTop}px`;\r\n        this.coverDiv.style.left = `${coverLeft}px`;\r\n        this.coverDiv.style.width = `${this.target.offsetWidth.toString()}px`;\r\n        //this.coverDiv.style.height = `${this.target.offsetHeight.toString()}px`;\r\n        this.coverDiv.style.zIndex =\r\n          this.uiStyleSettings.zIndex !== undefined\r\n            ? this.uiStyleSettings.zIndex\r\n            : '5';\r\n        // flex causes the ui to stretch when toolbox has wider nowrap panels\r\n        //this.coverDiv.style.display = 'flex';\r\n        break;\r\n      }\r\n      case 'popup': {\r\n        this.coverDiv.style.position = 'absolute';\r\n        this.coverDiv.style.top = '0px';\r\n        this.coverDiv.style.left = '0px';\r\n        this.coverDiv.style.width = '100vw';\r\n        this.coverDiv.style.height = `${window.innerHeight}px`;\r\n        this.coverDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.75)';\r\n        this.coverDiv.style.zIndex =\r\n          this.uiStyleSettings.zIndex !== undefined\r\n            ? this.uiStyleSettings.zIndex\r\n            : '1000';\r\n        this.coverDiv.style.display = 'flex';\r\n        // this.coverDiv.style.overflow = 'auto';\r\n      }\r\n    }\r\n    this.targetRoot.appendChild(this.coverDiv);\r\n\r\n    this.uiDiv = document.createElement('div');\r\n    this.uiDiv.style.display = 'flex';\r\n    this.uiDiv.style.flexDirection = 'column';\r\n    this.uiDiv.style.flexGrow = '2';\r\n    this.uiDiv.style.margin =\r\n      this.settings.displayMode === 'popup'\r\n        ? `${this.settings.popupMargin}px`\r\n        : '0px';\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.uiDiv.style.maxWidth = `calc(100vw - ${this.settings.popupMargin * 2}px`;\r\n    }\r\n    this.uiDiv.style.border = '0px';\r\n    // this.uiDiv.style.overflow = 'hidden';\r\n    //this.uiDiv.style.backgroundColor = '#ffffff';\r\n    this.coverDiv.appendChild(this.uiDiv);\r\n\r\n    this.toolbar = new Toolbar(\r\n      this.uiDiv,\r\n      this.settings.displayMode,\r\n      this._availableMarkerTypes,\r\n      this.uiStyleSettings,\r\n      this.styles\r\n    );\r\n    this.toolbar.addButtonClickListener(this.toolbarButtonClicked);\r\n    this.toolbar.show(\r\n      this._silentRenderMode || this.uiStyleSettings.hideToolbar\r\n        ? 'hidden'\r\n        : 'visible'\r\n    );\r\n\r\n    this.contentDiv = document.createElement('div');\r\n    this.contentDiv.style.display = 'flex';\r\n    this.contentDiv.style.flexDirection = 'row';\r\n    this.contentDiv.style.flexGrow = '2';\r\n    this.contentDiv.style.flexShrink = '1';\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.contentDiv.style.backgroundColor = this.uiStyleSettings.canvasBackgroundColor;\r\n      this.contentDiv.style.maxHeight = `${\r\n        this.windowHeight -\r\n        this.settings.popupMargin * 2 -\r\n        this.uiStyleSettings.toolbarHeight * 3.5\r\n      }px`;\r\n      // this.contentDiv.style.maxHeight = `calc(100vh - ${\r\n      //   this.settings.popupMargin * 2 + this.uiStyleSettings.toolbarHeight * 3.5}px)`;\r\n      this.contentDiv.style.maxWidth = `calc(100vw - ${\r\n        this.settings.popupMargin * 2\r\n      }px)`;\r\n    }\r\n    this.contentDiv.style.overflow = 'auto';\r\n    this.uiDiv.appendChild(this.contentDiv);\r\n\r\n    this.editorCanvas = document.createElement('div');\r\n    this.editorCanvas.style.flexGrow = '2';\r\n    this.editorCanvas.style.flexShrink = '1';\r\n    this.editorCanvas.style.position = 'relative';\r\n    this.editorCanvas.style.overflow = 'hidden';\r\n    this.editorCanvas.style.display = 'flex';\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.editorCanvas.style.alignItems = 'center';\r\n      this.editorCanvas.style.justifyContent = 'center';\r\n    }\r\n    this.editorCanvas.style.pointerEvents = 'none';\r\n    this.editorCanvas.style.transformOrigin = 'left top';\r\n    this.editorCanvas.style.transform = `scale(${this.zoomLevel})`;\r\n    this.contentDiv.appendChild(this.editorCanvas);\r\n\r\n    this.editingTarget =\r\n      this.target instanceof HTMLImageElement\r\n        ? document.createElement('img')\r\n        : document.createElement('canvas');\r\n    if (this.settings.displayMode === 'inline' \r\n      && this.settings.uiOffsetTop === undefined \r\n      && this.target.offsetTop < this.styles.settings.toolbarHeight) {\r\n      this.editingTarget.style.marginTop = `${\r\n        this.target.offsetTop - this.styles.settings.toolbarHeight\r\n      }px`;\r\n    }\r\n    this.editorCanvas.appendChild(this.editingTarget);\r\n\r\n    this.toolbox = new Toolbox(\r\n      this.uiDiv,\r\n      this.settings.displayMode,\r\n      this.uiStyleSettings,\r\n      this.styles\r\n    );\r\n    this.toolbox.show(\r\n      this._silentRenderMode || this.uiStyleSettings.hideToolbox\r\n        ? 'hidden'\r\n        : 'visible'\r\n    );\r\n  }\r\n\r\n  private closeUI() {\r\n    if (this.settings.displayMode === 'popup') {\r\n      this.restoreOverflow();\r\n    }\r\n    // @todo better cleanup\r\n    this.targetRoot.removeChild(this.coverDiv);\r\n    this.coverDiv.remove();\r\n    this.coverDiv = null;\r\n  }\r\n\r\n  private removeMarker(marker: MarkerBase) {\r\n    this.markerImage.removeChild(marker.container);\r\n    if (this.markers.indexOf(marker) > -1) {\r\n      this.markers.splice(this.markers.indexOf(marker), 1);\r\n    }\r\n    marker.dispose();\r\n  }\r\n\r\n  public switchToSelectMode(): void {\r\n    this.mode = 'select';\r\n    this.hideNotesEditor();\r\n    if (this._currentMarker !== undefined) {\r\n      if (this._currentMarker.state !== 'new') {\r\n        this._currentMarker.select();\r\n      } else {\r\n        this.removeMarker(this._currentMarker);\r\n        this.setCurrentMarker();\r\n        this.markerImage.style.cursor = 'default';\r\n      }\r\n      this.addUndoStep();\r\n    }\r\n  }\r\n\r\n  private toolbarButtonClicked(\r\n    buttonType: ToolbarButtonType,\r\n    value?: typeof MarkerBase | string\r\n  ) {\r\n    if (buttonType === 'marker' && value !== undefined) {\r\n      this.createNewMarker(<typeof MarkerBase>value);\r\n    } else if (buttonType === 'action') {\r\n      switch (value) {\r\n        case 'select': {\r\n          this.switchToSelectMode();\r\n          // workaround for text markers in continuos mode\r\n          // otherwise it continues creation until clicked a second time\r\n          this.switchToSelectMode();\r\n          break;\r\n        }\r\n        case 'delete': {\r\n          this.deleteSelectedMarker();\r\n          break;\r\n        }\r\n        case 'clear': {\r\n          this.clear();\r\n          break;\r\n        }\r\n        case 'undo': {\r\n          this.undo();\r\n          break;\r\n        }\r\n        case 'redo': {\r\n          this.redo();\r\n          break;\r\n        }\r\n        case 'zoom': {\r\n          this.stepZoom();\r\n          break;\r\n        }\r\n        case 'zoom-out': {\r\n          this.zoomLevel = 1;\r\n          break;\r\n        }\r\n        case 'notes': {\r\n          if (this.notesArea === undefined) {\r\n            this.switchToSelectMode();\r\n            this.zoomLevel = 1;\r\n            this.showNotesEditor();\r\n          } else {\r\n            this.switchToSelectMode();\r\n          }\r\n          break;\r\n        }\r\n        case 'close': {\r\n          this.close();\r\n          break;\r\n        }\r\n        case 'render': {\r\n          this.switchToSelectMode();\r\n          this.startRenderAndClose();\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes currently selected marker.\r\n   */\r\n  public deleteSelectedMarker(): void {\r\n    if (this._currentMarker !== undefined) {\r\n      let cancel = false;\r\n\r\n      this.eventListeners['markerbeforedelete'].forEach((listener) => {\r\n        const ev = new MarkerEvent(this, this._currentMarker, true);\r\n        listener(ev);\r\n        if (ev.defaultPrevented) {\r\n          cancel = true;\r\n        }\r\n      });\r\n\r\n      if (!cancel) {\r\n        const marker = this._currentMarker;\r\n        this._currentMarker.dispose();\r\n        this.markerImage.removeChild(this._currentMarker.container);\r\n        this.markers.splice(this.markers.indexOf(this._currentMarker), 1);\r\n        this.setCurrentMarker();\r\n        this.addUndoStep();\r\n        this.eventListeners['markerdelete'].forEach((listener) =>\r\n          listener(new MarkerEvent(this, marker))\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Removes all markers.\r\n   *\r\n   * @since 2.15.0\r\n   */\r\n  public clear(): void {\r\n    let cancel = false;\r\n    if (this.markers.length > 0) {\r\n      this.eventListeners['markerbeforedelete'].forEach((listener) => {\r\n        const ev = new MarkerEvent(this, undefined, true);\r\n        listener(ev);\r\n        if (ev.defaultPrevented) {\r\n          cancel = true;\r\n        }\r\n      });\r\n      if (!cancel) {\r\n        this.setCurrentMarker();\r\n        for (let i = this.markers.length - 1; i >= 0; i--) {\r\n          const marker = this.markers[i];\r\n          this.setCurrentMarker(this.markers[i]);\r\n          this._currentMarker.dispose();\r\n          this.markerImage.removeChild(this._currentMarker.container);\r\n          this.markers.splice(this.markers.indexOf(this._currentMarker), 1);\r\n          this.eventListeners['markerdelete'].forEach((listener) =>\r\n            listener(new MarkerEvent(this, marker))\r\n          );\r\n        }\r\n        this.addUndoStep();\r\n      }\r\n    }\r\n  }\r\n\r\n  private notesArea?: HTMLTextAreaElement;\r\n  private get isNotesAreaOpen(): boolean {\r\n    return this.notesArea !== undefined;\r\n  }\r\n\r\n  private showNotesEditor() {\r\n    if (this._currentMarker !== undefined) {\r\n      this.overlayContainer.innerHTML = '';\r\n      this.notesArea = document.createElement('textarea');\r\n      this.notesArea.className = this.uiStyleSettings.notesAreaStyleClassName;\r\n      this.notesArea.style.pointerEvents = 'auto';\r\n      this.notesArea.style.alignSelf = 'stretch';\r\n      this.notesArea.style.width = '100%';\r\n      this.notesArea.style.margin = `${\r\n        this.uiStyleSettings.toolbarHeight / 4\r\n      }px`;\r\n      this.notesArea.value = this._currentMarker.notes ?? '';\r\n      this.overlayContainer.appendChild(this.notesArea);\r\n    }\r\n  }\r\n  private hideNotesEditor() {\r\n    if (this.isNotesAreaOpen) {\r\n      if (this._currentMarker !== undefined) {\r\n        this._currentMarker.notes =\r\n          this.notesArea.value.trim() !== '' ? this.notesArea.value : undefined;\r\n      }\r\n      this.overlayContainer.removeChild(this.notesArea);\r\n      this.notesArea = undefined;\r\n    }\r\n  }\r\n\r\n  private selectLastMarker() {\r\n    if (this.markers.length > 0) {\r\n      this.setCurrentMarker(this.markers[this.markers.length - 1]);\r\n    } else {\r\n      this.setCurrentMarker();\r\n    }\r\n  }\r\n\r\n  private addUndoStep() {\r\n    if (\r\n      this._currentMarker === undefined ||\r\n      this._currentMarker.state !== 'edit'\r\n    ) {\r\n      const currentState = this.getState();\r\n      const lastUndoState = this.undoRedoManager.getLastUndoStep();\r\n      if (\r\n        lastUndoState &&\r\n        (lastUndoState.width !== currentState.width ||\r\n          lastUndoState.height !== currentState.height)\r\n      ) {\r\n        // if the size changed just replace the last step with a resized one\r\n        this.undoRedoManager.replaceLastUndoStep(currentState);\r\n        // @todo was sometimes fired on zoom events in popup mode\r\n        // need to find the root cause before restoring statechange event here (if needed?)\r\n        // this.eventListeners['statechange'].forEach((listener) =>\r\n        //   listener(new MarkerAreaEvent(this))\r\n        // );\r\n      } else {\r\n        const beforeSteps = this.undoRedoManager.undoStepCount;\r\n        this.undoRedoManager.addUndoStep(currentState);\r\n        if (beforeSteps < this.undoRedoManager.undoStepCount) {\r\n          this.eventListeners['statechange'].forEach((listener) =>\r\n            listener(new MarkerAreaEvent(this))\r\n          );\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Undo last action.\r\n   *\r\n   * @since 2.6.0\r\n   */\r\n  public undo(): void {\r\n    this.switchToSelectMode();\r\n    this.addUndoStep();\r\n    this.undoStep();\r\n  }\r\n\r\n  private undoStep(): void {\r\n    const stepData = this.undoRedoManager.undo();\r\n    if (stepData !== undefined) {\r\n      this.restoreState(stepData);\r\n      this.addDefsToImage();\r\n      this.selectLastMarker();\r\n      this.eventListeners['statechange'].forEach((listener) =>\r\n        listener(new MarkerAreaEvent(this))\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Redo previously undone action.\r\n   *\r\n   * @since 2.6.0\r\n   */\r\n  public redo(): void {\r\n    this.switchToSelectMode();\r\n    this.redoStep();\r\n  }\r\n\r\n  private redoStep(): void {\r\n    const stepData = this.undoRedoManager.redo();\r\n    if (stepData !== undefined) {\r\n      this.restoreState(stepData);\r\n      this.addDefsToImage();\r\n      this.selectLastMarker();\r\n      this.eventListeners['statechange'].forEach((listener) =>\r\n        listener(new MarkerAreaEvent(this))\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Iterate zoom steps (@linkcode zoomSteps).\r\n   * Next zoom level is selected or returns to the first zoom level restarting the sequence.\r\n   *\r\n   * @since 2.12.0\r\n   */\r\n  public stepZoom(): void {\r\n    const zoomStepIndex = this.zoomSteps.indexOf(this.zoomLevel);\r\n    this.zoomLevel =\r\n      zoomStepIndex < this.zoomSteps.length - 1\r\n        ? this.zoomSteps[zoomStepIndex + 1]\r\n        : this.zoomSteps[0];\r\n  }\r\n\r\n  private prevPanPoint: IPoint = { x: 0, y: 0 };\r\n  private panTo(point: IPoint) {\r\n    this.contentDiv.scrollBy({\r\n      left: this.prevPanPoint.x - point.x,\r\n      top: this.prevPanPoint.y - point.y,\r\n    });\r\n    this.prevPanPoint = point;\r\n  }\r\n\r\n  /**\r\n   * Initiates markup rendering.\r\n   *\r\n   * Get results by adding a render event listener via {@linkcode addRenderEventListener}.\r\n   */\r\n  public async startRenderAndClose(): Promise<void> {\r\n    const result = await this.render();\r\n    const state = this.getState();\r\n    //this.renderEventListeners.forEach((listener) => listener(result, state));\r\n    this.eventListeners['render'].forEach((listener) =>\r\n      listener(new MarkerAreaRenderEvent(this, result, state))\r\n    );\r\n    this.close(true);\r\n  }\r\n\r\n  /**\r\n   * Returns the complete state for the MarkerArea that can be preserved and used\r\n   * to continue annotation next time.\r\n   *\r\n   * @param deselectCurrentMarker - when `true` is passed, currently selected marker will be deselected before getting the state.\r\n   */\r\n  public getState(deselectCurrentMarker?: boolean): MarkerAreaState {\r\n    if (deselectCurrentMarker === true) {\r\n      this.setCurrentMarker();\r\n    }\r\n    const result: MarkerAreaState = {\r\n      width: this.imageWidth,\r\n      height: this.imageHeight,\r\n      markers: [],\r\n    };\r\n    this.markers.forEach((marker) => result.markers.push(marker.getState()));\r\n    return result;\r\n  }\r\n\r\n  /**\r\n   * Restores MarkerArea state to continue previous annotation session.\r\n   *\r\n   * **IMPORTANT**: call `restoreState()` __after__ you've opened the MarkerArea with {@linkcode show}.\r\n   *\r\n   * ```typescript\r\n   * this.markerArea1.show();\r\n   * if (this.currentState) {\r\n   *   this.markerArea1.restoreState(this.currentState);\r\n   * }\r\n   * ```\r\n   *\r\n   * @param state - previously saved state object.\r\n   */\r\n  public restoreState(state: MarkerAreaState): void {\r\n    this.markers.splice(0);\r\n    while (this.markerImage.lastChild) {\r\n      this.markerImage.removeChild(this.markerImage.lastChild);\r\n    }\r\n\r\n    state.markers.forEach((markerState) => {\r\n      const markerType = this._availableMarkerTypes.find(\r\n        (mType) => mType.typeName === markerState.typeName\r\n      );\r\n      if (markerType !== undefined) {\r\n        const marker = this.addNewMarker(markerType);\r\n        marker.restoreState(markerState);\r\n        this.markers.push(marker);\r\n      }\r\n    });\r\n    if (\r\n      state.width &&\r\n      state.height &&\r\n      (state.width !== this.imageWidth || state.height !== this.imageHeight)\r\n    ) {\r\n      this.scaleMarkers(\r\n        this.imageWidth / state.width,\r\n        this.imageHeight / state.height\r\n      );\r\n    }\r\n    this.eventListeners['restorestate'].forEach((listener) =>\r\n      listener(new MarkerAreaEvent(this))\r\n    );\r\n  }\r\n\r\n  private addNewMarker(markerType: typeof MarkerBase): MarkerBase {\r\n    const g = SvgHelper.createGroup();\r\n    this.markerImage.appendChild(g);\r\n\r\n    return new markerType(g, this.overlayContainer, this.settings);\r\n  }\r\n\r\n  /**\r\n   * Initiate new marker creation.\r\n   *\r\n   * marker.js switches to marker creation mode for the marker type specified\r\n   * and users can draw a new marker like they would by pressing a corresponding\r\n   * toolbar button.\r\n   *\r\n   * This example initiates creation of a `FrameMarker`:\r\n   * ```typescript\r\n   * this.markerArea1.createNewMarker(FrameMarker);\r\n   * ```\r\n   *\r\n   * @param markerType\r\n   */\r\n  public createNewMarker(markerType: typeof MarkerBase | string): void {\r\n    let mType: typeof MarkerBase;\r\n\r\n    if (typeof markerType === 'string') {\r\n      mType = this._availableMarkerTypes.find(\r\n        (mt) => mt.typeName === markerType\r\n      );\r\n    } else {\r\n      mType = markerType;\r\n    }\r\n\r\n    if (mType) {\r\n      this.setCurrentMarker();\r\n      this.addUndoStep();\r\n      this._currentMarker = this.addNewMarker(mType);\r\n      this._currentMarker.onMarkerCreated = this.markerCreated;\r\n      this._currentMarker.onColorChanged = this.colorChanged;\r\n      this._currentMarker.onFillColorChanged = this.fillColorChanged;\r\n      this._currentMarker.onStateChanged = this.markerStateChanged;\r\n      this.markerImage.style.cursor = 'crosshair';\r\n      this.toolbar.setActiveMarkerButton(mType.typeName);\r\n      this.toolbox.setPanelButtons(this._currentMarker.toolboxPanels);\r\n      this.eventListeners['markercreating'].forEach((listener) =>\r\n        listener(new MarkerEvent(this, this._currentMarker))\r\n      );\r\n    }\r\n  }\r\n\r\n  private markerCreated(marker: MarkerBase) {\r\n    this.mode = 'select';\r\n    this.markerImage.style.cursor = 'default';\r\n    this.markers.push(marker);\r\n    this.setCurrentMarker(marker);\r\n    if (\r\n      marker instanceof FreehandMarker &&\r\n      this.settings.newFreehandMarkerOnPointerUp\r\n    ) {\r\n      this.createNewMarker(FreehandMarker);\r\n    } else {\r\n      this.toolbar.setSelectMode();\r\n    }\r\n    this.addUndoStep();\r\n    this.eventListeners['markercreate'].forEach((listener) =>\r\n      listener(new MarkerEvent(this, this._currentMarker))\r\n    );\r\n  }\r\n\r\n  private colorChanged(color: string): void {\r\n    if (this.settings.defaultColorsFollowCurrentColors) {\r\n      this.settings.defaultColor = color;\r\n      this.settings.defaultStrokeColor = color;\r\n    }\r\n  }\r\n  private fillColorChanged(color: string): void {\r\n    if (this.settings.defaultColorsFollowCurrentColors) {\r\n      this.settings.defaultFillColor = color;\r\n    }\r\n  }\r\n\r\n  private markerStateChanged(marker: MarkerBase): void {\r\n    this.eventListeners['markerchange'].forEach((listener) =>\r\n      listener(new MarkerEvent(this, marker))\r\n    );\r\n  }\r\n\r\n  /**\r\n   * Sets the currently selected marker or deselects it if no parameter passed.\r\n   *\r\n   * @param marker marker to select. Deselects current marker if undefined.\r\n   */\r\n  public setCurrentMarker(marker?: MarkerBase): void {\r\n    if (this._currentMarker !== marker) {\r\n      // no need to deselect if not changed\r\n      if (this._currentMarker !== undefined) {\r\n        this._currentMarker.deselect();\r\n        this.toolbar.setCurrentMarker();\r\n        this.toolbox.setPanelButtons([]);\r\n\r\n        if (!this._isResizing) {\r\n          this.eventListeners['markerdeselect'].forEach((listener) =>\r\n            listener(new MarkerEvent(this, this._currentMarker))\r\n          );\r\n        }\r\n      }\r\n    }\r\n    this._currentMarker = marker;\r\n    if (this._currentMarker !== undefined && !this._currentMarker.isSelected) {\r\n      if (this._currentMarker.state !== 'new') {\r\n      this._currentMarker.select();\r\n      }\r\n      this.toolbar.setCurrentMarker(this._currentMarker);\r\n      this.toolbox.setPanelButtons(this._currentMarker.toolboxPanels);\r\n\r\n      if (!this._isResizing) {\r\n        this.eventListeners['markerselect'].forEach((listener) =>\r\n          listener(new MarkerEvent(this, this._currentMarker))\r\n        );\r\n      }\r\n    }\r\n  }\r\n\r\n  private onPointerDown(ev: PointerEvent) {\r\n    if (!this._isFocused) {\r\n      this.focus();\r\n    }\r\n\r\n    this.touchPoints++;\r\n    if (this.touchPoints === 1 || ev.pointerType !== 'touch') {\r\n      if (\r\n        this._currentMarker !== undefined &&\r\n        (this._currentMarker.state === 'new' ||\r\n          this._currentMarker.state === 'creating')\r\n      ) {\r\n        this.isDragging = true;\r\n        this._currentMarker.pointerDown(\r\n          this.clientToLocalCoordinates(ev.clientX, ev.clientY)\r\n        );\r\n      } else if (this.mode === 'select') {\r\n        const hitMarker = this.markers.find((m) => m.ownsTarget(ev.target));\r\n        if (hitMarker !== undefined) {\r\n          this.setCurrentMarker(hitMarker);\r\n          this.isDragging = true;\r\n          this._currentMarker.pointerDown(\r\n            this.clientToLocalCoordinates(ev.clientX, ev.clientY),\r\n            ev.target\r\n          );\r\n        } else {\r\n          this.setCurrentMarker();\r\n          this.isDragging = true;\r\n          this.prevPanPoint = { x: ev.clientX, y: ev.clientY };\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  private onDblClick(ev: PointerEvent) {\r\n    if (!this._isFocused) {\r\n      this.focus();\r\n    }\r\n\r\n    if (this.mode === 'select') {\r\n      const hitMarker = this.markers.find((m) => m.ownsTarget(ev.target));\r\n      if (hitMarker !== undefined && hitMarker !== this._currentMarker) {\r\n        this.setCurrentMarker(hitMarker);\r\n      }\r\n      if (this._currentMarker !== undefined) {\r\n        this._currentMarker.dblClick(\r\n          this.clientToLocalCoordinates(ev.clientX, ev.clientY),\r\n          ev.target\r\n        );\r\n      } else {\r\n        this.setCurrentMarker();\r\n      }\r\n    }\r\n  }\r\n\r\n  private onPointerMove(ev: PointerEvent) {\r\n    if (this.touchPoints === 1 || ev.pointerType !== 'touch') {\r\n      if (this._currentMarker !== undefined || this.isDragging) {\r\n        // don't swallow the event when editing text markers\r\n        if (\r\n          this._currentMarker === undefined ||\r\n          this._currentMarker.state !== 'edit'\r\n        ) {\r\n          ev.preventDefault();\r\n        }\r\n\r\n        if (this._currentMarker !== undefined) {\r\n          this._currentMarker.manipulate(\r\n            this.clientToLocalCoordinates(ev.clientX, ev.clientY)\r\n          );\r\n        } else if (this.zoomLevel > 1) {\r\n          this.panTo({ x: ev.clientX, y: ev.clientY });\r\n        }\r\n      }\r\n    }\r\n  }\r\n  private onPointerUp(ev: PointerEvent) {\r\n    if (this.touchPoints > 0) {\r\n      this.touchPoints--;\r\n    }\r\n    if (this.touchPoints === 0) {\r\n      if (this.isDragging && this._currentMarker !== undefined) {\r\n        this._currentMarker.pointerUp(\r\n          this.clientToLocalCoordinates(ev.clientX, ev.clientY)\r\n        );\r\n      }\r\n    }\r\n    this.isDragging = false;\r\n    this.addUndoStep();\r\n  }\r\n\r\n  private onPointerOut(/*ev: PointerEvent*/) {\r\n    if (this.touchPoints > 0) {\r\n      this.touchPoints--;\r\n    }\r\n  }\r\n\r\n  private onKeyUp(ev: KeyboardEvent) {\r\n    if (\r\n      this._currentMarker !== undefined &&\r\n      this.notesArea === undefined &&\r\n      (ev.key === 'Delete' || ev.key === 'Backspace')\r\n    ) {\r\n      this.deleteSelectedMarker();\r\n      // this.setCurrentMarker();\r\n      // this.markerImage.style.cursor = 'default';\r\n      // this.addUndoStep();\r\n    }\r\n  }\r\n\r\n  private clientToLocalCoordinates(x: number, y: number): IPoint {\r\n    const clientRect = this.markerImage.getBoundingClientRect();\r\n    const scaleX = clientRect.width / this.imageWidth / this.zoomLevel;\r\n    const scaleY = clientRect.height / this.imageHeight / this.zoomLevel;\r\n    return {\r\n      x: (x - clientRect.left) / this.zoomLevel / scaleX,\r\n      y: (y - clientRect.top) / this.zoomLevel / scaleY,\r\n    };\r\n  }\r\n\r\n  private onWindowResize() {\r\n    this.positionUI();\r\n  }\r\n\r\n  private positionUI() {\r\n    this.setTopLeft();\r\n    switch (this.settings.displayMode) {\r\n      case 'inline': {\r\n        const rects = this.target.getClientRects();\r\n        const coverTop =\r\n          rects.length > 0 && rects.item(0) &&\r\n          (rects.item(0).y > this.styles.settings.toolbarHeight)\r\n            ? this.target.offsetTop - this.styles.settings.toolbarHeight\r\n            : 0;\r\n        this.coverDiv.style.top = `${coverTop}px`;\r\n        this.coverDiv.style.left = `${this.target.offsetLeft.toString()}px`;\r\n        break;\r\n      }\r\n      case 'popup': {\r\n        this.coverDiv.style.top = '0px';\r\n        this.coverDiv.style.left = '0px';\r\n        this.coverDiv.style.width = '100vw';\r\n        this.coverDiv.style.height = `${this.windowHeight}px`;\r\n        this.contentDiv.style.maxHeight = `${\r\n          this.windowHeight -\r\n          this.settings.popupMargin * 2 -\r\n          this.styles.settings.toolbarHeight * 3.5\r\n        }px`;\r\n      }\r\n    }\r\n    this.positionMarkerImage();\r\n    this.positionLogo();\r\n  }\r\n\r\n  /**\r\n   * Add license key.\r\n   *\r\n   * This is a proxy method for {@linkcode Activator.addKey()}.\r\n   *\r\n   * @param key - commercial license key.\r\n   */\r\n  public addLicenseKey(key: string): void {\r\n    Activator.addKey(key);\r\n  }\r\n\r\n  private eventListeners = new EventListenerRepository();\r\n  /**\r\n   * Adds an event listener for one of the marker.js Live events.\r\n   *\r\n   * @param eventType - type of the event.\r\n   * @param handler - function handling the event.\r\n   *\r\n   * @since 2.16.0\r\n   */\r\n  public addEventListener<T extends keyof IEventListenerRepository>(\r\n    eventType: T,\r\n    handler: EventHandler<T>\r\n  ): void {\r\n    this.eventListeners.addEventListener(eventType, handler);\r\n  }\r\n\r\n  /**\r\n   * Removes an event listener for one of the marker.js Live events.\r\n   *\r\n   * @param eventType - type of the event.\r\n   * @param handler - function currently handling the event.\r\n   *\r\n   * @since 2.16.0\r\n   */\r\n  public removeEventListener<T extends keyof IEventListenerRepository>(\r\n    eventType: T,\r\n    handler: EventHandler<T>\r\n  ): void {\r\n    this.eventListeners.removeEventListener(eventType, handler);\r\n  }\r\n\r\n  private _silentRenderMode = false;\r\n  /**\r\n   * Renders previously saved state without user intervention.\r\n   *\r\n   * The rendered image is returned to the `render` event handlers (as in the regular interactive process).\r\n   * Rendering options set on `MarkerArea` are respected.\r\n   *\r\n   * @param state state to render\r\n   *\r\n   * @since 2.17.0\r\n   */\r\n  public renderState(state: MarkerAreaState): void {\r\n    this._silentRenderMode = true;\r\n    this.settings.displayMode = 'inline';\r\n    if (!this.isOpen) {\r\n      this.show();\r\n    }\r\n    this.restoreState(state);\r\n    this.startRenderAndClose();\r\n    this._silentRenderMode = false;\r\n  }\r\n\r\n  private _isFocused = false;\r\n  /**\r\n   * Returns true when this MarkerArea is focused.\r\n   *\r\n   * @since 2.19.0\r\n   */\r\n  public get isFocused(): boolean {\r\n    return this._isFocused;\r\n  }\r\n\r\n  private _previousCurrentMarker?: MarkerBase;\r\n\r\n  /**\r\n   * Focuses the MarkerArea to receive all input from the window.\r\n   *\r\n   * Is called automatically when user clicks inside of the marker area. Call manually to set focus explicitly.\r\n   *\r\n   * @since 2.19.0\r\n   */\r\n  public focus(): void {\r\n    if (!this._isFocused) {\r\n      this.attachWindowEvents();\r\n      this._isFocused = true;\r\n      if (this._previousCurrentMarker !== undefined) {\r\n        this.setCurrentMarker(this._previousCurrentMarker);\r\n      }\r\n      this.eventListeners['focus'].forEach((listener) =>\r\n        listener(new MarkerAreaEvent(this))\r\n      );\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Tells MarkerArea to stop reacting to input outside of the immediate marker image.\r\n   *\r\n   * Call `focus()` to re-enable.\r\n   *\r\n   * @since 2.19.0\r\n   */\r\n  public blur(): void {\r\n    if (this._isFocused) {\r\n      this.detachWindowEvents();\r\n      this._isFocused = false;\r\n      this._previousCurrentMarker = this._currentMarker;\r\n      this.setCurrentMarker();\r\n      this.eventListeners['blur'].forEach((listener) =>\r\n        listener(new MarkerAreaEvent(this))\r\n      );\r\n    }\r\n  }\r\n}\r\n"], "names": ["extendStatics", "d", "b", "Object", "setPrototypeOf", "__proto__", "Array", "p", "prototype", "hasOwnProperty", "call", "__extends", "__", "this", "constructor", "create", "__awaiter", "thisArg", "_arguments", "P", "generator", "Promise", "resolve", "reject", "fulfilled", "value", "step", "next", "e", "rejected", "result", "done", "then", "apply", "__generator", "body", "f", "y", "t", "g", "_", "label", "sent", "trys", "ops", "verb", "throw", "return", "Symbol", "iterator", "n", "v", "op", "TypeError", "pop", "length", "push", "__spreadA<PERSON>ys", "s", "i", "il", "arguments", "r", "k", "a", "j", "jl", "SvgHelper", "document", "createElementNS", "el", "attributes", "attributes_1", "_i", "_a", "attr", "setAttribute", "width", "height", "rect", "toString", "setAttributes", "x1", "y1", "x2", "y2", "line", "points", "polygon", "radius", "circle", "rx", "ry", "ellipse", "createSVGTransform", "id", "orient", "marker<PERSON>id<PERSON>", "markerHeight", "refX", "refY", "markerElement", "marker", "append<PERSON><PERSON><PERSON>", "text", "tspan", "textContent", "image", "x", "svgPoint", "createSVGPoint", "path", "Activator", "key", "RegExp", "test", "<PERSON><PERSON><PERSON>", "target", "markerImage", "targetCanvas", "canvas", "createElement", "_this", "markersOnly", "naturalSize", "markerImageCopy", "baseVal", "valueAsString", "viewBox", "innerHTML", "naturalWidth", "naturalHeight", "data", "outerHTML", "ctx", "getContext", "drawImage", "DOMURL", "window", "URL", "img", "Image", "blob", "Blob", "type", "url", "createObjectURL", "onload", "revokeObjectURL", "toDataURL", "imageType", "imageQuality", "src", "instanceNo", "defaultSettings", "_classNamePrefix", "_classNamePrefixBase", "StyleManager", "canvasBackgroundColor", "toolbarBackgroundColor", "toolbarBackgroundHoverColor", "toolbarColor", "toolbarHeight", "toolboxColor", "toolboxAccentColor", "undoButtonVisible", "redoButtonVisible", "zoomButtonVisible", "zoomOutButtonVisible", "clearButtonVisible", "resultButtonBlockVisible", "logoPosition", "classNamePrefix", "styleClass", "styleSheet", "addStyleSheet", "name", "localName", "classes", "sheet", "insertRule", "style", "cssRules", "styleRule", "rules", "selector", "styleSheetRoot", "head", "addRule", "StyleRule", "addClass", "StyleClass", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "displayMode", "markerItems", "uiStyleSettings", "styles", "addStyles", "adjustLayout", "bind", "overflowButtonClicked", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "visiblity", "uiContainer", "visibility", "className", "toolbarStyleClass", "fadeInAnimationClassName", "toolbarStyleColorsClassName", "toolbarStyleColorsClass", "actionButtonBlock", "toolbarBlockStyleClass", "whiteSpace", "addActionButton", "notesButtonVisible", "markerButtonBlock", "flexGrow", "textAlign", "markerButtonOverflowBlock", "toolbarOverflowBlockStyleClass", "toolbarOverflowBlockStyleColorsClassName", "toolbarOverflowBlockStyleColorsClass", "display", "for<PERSON>ach", "mi", "buttonContainer", "toolbarButtonStyleClass", "typeName", "title", "icon", "addEventListener", "markerToolbarButtonClicked", "buttons", "markerButtons", "overflowButton", "toolbarButtonStyleColorsClassName", "toolbarButtonStyleColorsClass", "resultButtonBlock", "setSelectMode", "listener", "buttonClickListeners", "indexOf", "splice", "resetButtonStyles", "setActiveButton", "numberToFit", "Math", "floor", "clientWidth", "buttonIndex", "replace", "top", "offsetTop", "offsetHeight", "right", "offsetWidth", "offsetLeft", "button", "trim", "toolbarActiveButtonStyleColorsClassName", "toolbarActiveButtonStyleColorsClass", "container", "actionButton", "actionToolbarButtonClicked", "fill", "selectButtonColor", "deleteButtonColor", "okButtonColor", "closeButtonColor", "round", "buttonPadding", "markerType", "action", "activeBtn", "find", "btn", "getAttribute", "<PERSON><PERSON><PERSON><PERSON>", "filter", "fillOpacity", "pointerEvents", "panelButtonClick", "Toolbox", "toolboxStyleClass", "toolboxStyleColorsClass", "toolboxButtonRowStyleClass", "toolboxButtonRowStyleColorsClass", "toolboxPanelRowStyleClass", "toolboxPanelRowStyleColorsClass", "toolboxBackgroundColor", "toolboxButtonStyleClass", "toolboxButtonStyleColorsClass", "toolboxActiveButtonStyleColorsClass", "toolboxStyleColorsClassName", "panels", "panelRow", "toolboxPanelRowStyleColorsClassName", "buttonRow", "toolboxButtonRowStyleColorsClassName", "panelButtons", "panel", "panelBtnDiv", "toolboxButtonStyleColorsClassName", "panelIndex", "activePanel", "panelUI", "getUi", "margin", "fadeOutAnimationClassName", "setTimeout", "pb", "index", "toolboxActiveButtonStyleColorsClassName", "colors", "currentColor", "_super", "setCurrentColor", "getColorBox", "ColorPickerPanel", "panelDiv", "overflow", "color", "colorBoxContainer", "colorBoxes", "buttonHeight", "boxSizing", "padding", "marginRight", "marginBottom", "borderWidth", "borderStyle", "borderRadius", "borderColor", "colorBox", "backgroundColor", "box", "onColorChanged", "ToolboxPanel", "overlayContainer", "settings", "_container", "_overlayContainer", "globalSettings", "stateChanged", "colorChanged", "fillColorChanged", "Marker<PERSON>ase", "getPrototypeOf", "_state", "_isSelected", "cursor", "manipulationStartState", "getState", "point", "element", "childNodes", "insertBefore", "state", "notes", "scaleX", "scaleY", "onFillColorChanged", "onStateChanged", "currentState", "JSON", "stringify", "findGripByVisual", "RectangularBoxMarkerGrips", "gripVisual", "topLeft", "ownsTarget", "topCenter", "topRight", "centerLeft", "centerRight", "bottomLeft", "bottomCenter", "bottomRight", "visual", "createGroup", "createCircle", "GRIP_SIZE", "ResizeGrip", "TransformMatrix", "matrix", "c", "currentMatrix", "newMatrix", "transform", "appendItem", "createTransform", "setupControlBox", "RectangularBoxMarkerBase", "left", "_visual", "translate", "controlGrips", "rotatorGrip", "pointerDown", "manipulationStartLeft", "manipulationStartTop", "manipulationStartWidth", "manipulationStartHeight", "rotatedPoint", "unrotatePoint", "manipulationStartX", "manipulationStartY", "offsetX", "offsetY", "select", "activeGrip", "rotatedCenter", "rotatePoint", "centerX", "centerY", "moveVisual", "rotate", "getItem", "setRotate", "rotationAngle", "replaceItem", "adjustControlBox", "inState", "pointerUp", "defaultSize", "manipulate", "onMarkerCreated", "_suppressMarkerCreateEvent", "resize", "newX", "newWidth", "newY", "newHeight", "setSize", "abs", "sign", "atan", "PI", "applyRotation", "getCTM", "createPoint", "matrixTransform", "inverse", "controlBox", "deselect", "setTranslate", "CB_DISTANCE", "controlRect", "createRect", "disableRotation", "rotatorGripLine", "createLine", "addControlGrips", "positionGrips", "createGrip", "grip", "gripSize", "cx", "cy", "bottom", "positionGrip", "assign", "visualTransformMatrix", "toITransformMatrix", "containerTransformMatrix", "restoreState", "rbmState", "setMatrix", "toSVGMatrix", "scale", "rPoint", "setStrokeColor", "setFillColor", "setStrokeWidth", "setStrokeDash<PERSON>y", "createVisual", "Rectangle<PERSON><PERSON><PERSON>", "fillColor", "strokeColor", "strokeWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "opacity", "addMarkerVisualToContainer", "dashes", "rectState", "widths", "currentWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LineWidthPanel", "lineWidth", "widthBoxContainer", "alignItems", "justifyContent", "innerText", "widthBox", "minHeight", "hr", "min<PERSON><PERSON><PERSON>", "border", "borderTop", "widthBoxes", "onWidthChanged", "currentStyle", "setCurrentStyle", "LineStylePanel", "lineStyle", "styleBoxContainer", "max<PERSON><PERSON><PERSON>", "styleBox", "styleSample", "styleBoxes", "newStyle", "onStyleChanged", "defaultColor", "defaultStrokeWidth", "defaultStrokeDasharray", "strokePanel", "defaultColorSet", "strokeWidthPanel", "defaultStrokeWidths", "strokeStylePanel", "defaultStrokeDasharrays", "FrameMarker", "LinearMarkerBase", "grip1", "grip2", "manipulationStartX1", "manipulationStartY1", "manipulationStartX2", "manipulationStartY2", "defaultLength", "adjustVisual", "lmbState", "LineMarker", "selectorLine", "visibleLine", "lmState", "fonts", "currentFont", "setCurrentFont", "FontFamilyPanel", "font", "fontBoxContainer", "fontBox", "fontFamily", "fontLabel", "textOverflow", "fontBoxes", "newFont", "onFontChanged", "DEFAULT_TEXT", "defaultFontFamily", "setColor", "setFont", "renderText", "sizeText", "textEditDivClicked", "showTextEditor", "positionTextEditor", "wrapText", "colorPanel", "fontFamilyPanel", "defaultFontFamilies", "TextMarker", "textElement", "bgRectangle", "found_1", "span", "createText", "isMoved", "pointerDownPoint", "pointerDownTimestamp", "Date", "now", "getTextAspectRatio", "textLines", "longestLineChars", "lines", "split", "boxAspectRatio", "processedLines_1", "textAspectRatio", "maxLineLength_1", "Number", "MAX_VALUE", "longestLine", "lastIndexOf", "reminderLine", "maxEnd", "substring", "join", "<PERSON><PERSON><PERSON><PERSON>", "createTSpan", "textSize", "getBBox", "xScale", "yScale", "min", "xSign", "getComputedStyle", "direction", "textBBox", "getTextScale", "position", "getTextPosition", "navigator", "userAgent", "setScale", "textEditDiv", "textEditor", "lineHeight", "contentEditable", "ev", "stopPropagation", "fontSize", "parseFloat", "parseInt", "max", "cancelBubble", "clipboardData", "content", "getData", "selection", "getSelection", "rangeCount", "deleteFromDocument", "getRangeAt", "insertNode", "createTextNode", "preventDefault", "hideVisual", "focus", "execCommand", "maxHeight", "textScale", "rPosition", "rWH", "showVisual", "dblClick", "hideControlBox", "showControlBox", "textState", "pixelRatio", "freehandPixelRatio", "addCanvas", "finishCreation", "setLineWidth", "lineWidthPanel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "drawingImage", "createImage", "canvasContext", "strokeStyle", "beginPath", "moveTo", "drawing", "lineTo", "stroke", "closePath", "newFreehandMarkerOnPointerUp", "canvasElement", "clientHeight", "imgData", "getImageData", "startX", "startY", "endX", "endY", "containsData", "row", "col", "tmpCanvas", "putImageData", "drawingImgUrl", "setDrawingImage", "currentType", "setCurrentType", "ArrowTypePanel", "ti", "arrowType", "typeBoxContainer", "this_1", "leftTip", "marginLeft", "lineBox", "rightTip", "typeBoxes", "newType", "onArrowTypeChanged", "getArrowPoints", "setArrowType", "arrowTypePanel", "Arrow<PERSON><PERSON><PERSON>", "arrow1", "arrow2", "arrow<PERSON>ase<PERSON><PERSON><PERSON>", "arrowBaseHeight", "createPolygon", "createTips", "lineAngle1", "a1transform", "a2transform", "amState", "defaultFillColor", "fillPanel", "<PERSON><PERSON><PERSON><PERSON>", "opacities", "currentOpacity", "setCurrentOpacity", "OpacityPanel", "opacityBoxContainer", "opacityBoxes", "onOpacityChanged", "setOpacity", "defaultHighlightColor", "defaultHighlightOpacity", "opacityPanel", "defaultOpacitySteps", "HighlightMarker", "defaultStrokeColor", "bgColor", "setBgColor", "getTipPoints", "positionTip", "setTipPoints", "TextColorIcon", "bgColorPanel", "FillColorIcon", "tipGrip", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tip", "createTip", "tipMoving", "isCreating", "tipPosition", "tipBase1Position", "tipBase2Position", "offset", "baseWidth", "cornerAngle", "calloutState", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createEllipse", "Measurement<PERSON>arker", "tip1", "tip2", "tip<PERSON>ength", "EllipseFrame<PERSON><PERSON><PERSON>", "UndoRedoManager", "undoStack", "redoStack", "stepData", "lastRedoStep", "lastStep", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectorCurve", "visibleCurve", "curveGrip", "curveX", "curveY", "createPath", "getPathD", "manipulationStartCurveX", "manipulationStartCurveY", "curveControlLine1", "curveControlLine2", "<PERSON><PERSON><PERSON><PERSON>", "textColor", "defaultCaptionFontSize", "captionText", "defaultCaptionText", "sizeCaption", "setCaptionText", "finishTextEditing", "setTextColor", "textColorPanel", "Capt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "frame", "captionBg", "captionElement", "textAnchor", "dominantBaseline", "captionBoxWidth", "PADDING", "captionBoxHeight", "textEditBox", "transform<PERSON><PERSON>in", "frState", "markerArea", "cancelable", "MarkerAreaEvent", "_defaultPrevented", "dataUrl", "EventListenerRepository", "eventType", "handler", "DEFAULT_MARKER_TYPES", "Settings", "_instanceNo", "<PERSON><PERSON><PERSON><PERSON>", "instance<PERSON><PERSON><PERSON>", "targetRoot", "removeStyleSheet", "open", "setTopLeft", "toolbarButtonClicked", "createNewMarker", "addNewMarker", "markerCreated", "onPointerDown", "onDblClick", "onPointerMove", "onPointerUp", "onPointerOut", "onKeyUp", "overrideOverflow", "restoreOverflow", "close", "closeUI", "addCloseEventListener", "removeCloseEventListener", "addRenderEventListener", "removeRenderEventListener", "clientToLocalCoordinates", "onWindowResize", "deleteSelectedMarker", "setWindowHeight", "<PERSON><PERSON><PERSON><PERSON>", "onPopupTargetResize", "showNotesEditor", "hideNotesEditor", "<PERSON><PERSON><PERSON>", "blur", "markerStateChanged", "switchToSelectMode", "addDefs", "addDefsToImage", "_availableMarkerTypes", "mt", "typeType", "ALL_MARKER_TYPES", "allT", "_current<PERSON><PERSON>er", "_isOpen", "undoRedoManager", "isUndoPossible", "isRedoPossible", "_zoomLevel", "editor<PERSON><PERSON><PERSON>", "contentDiv", "scrollTo", "setupResizeObserver", "setEditingTarget", "initMarkerCanvas", "initOverlay", "attachEvents", "isLicensed", "addLogo", "_isFocused", "Style", "markers", "showUI", "eventListeners", "renderer", "renderAtNaturalSize", "renderImageType", "renderImageQuality", "renderMarkersOnly", "render<PERSON>idth", "renderHeight", "rasterize", "HTMLImageElement", "renderTarget", "suppressBeforeClose", "isOpen", "cancel_1", "defaultPrevented", "coverDiv", "targetObserver", "unobserve", "removeEventListener", "detachEvents", "event", "ResizeObserver", "observe", "ratio", "windowHeight", "innerHeight", "_isResizing", "imageWidth", "imageHeight", "editing<PERSON>arget", "markerImageHolder", "positionMarkerImage", "toolbar", "positionLogo", "scaleMarkers", "preScaleSelectedMarker", "targetRect", "getBoundingClientRect", "bodyRect", "setProperty", "nodes", "defs", "createDefs", "append", "zoomLevel", "attachWindowEvents", "detachWindowEvents", "logoUI", "link", "href", "justifyItems", "scrollXState", "scrollX", "scrollYState", "scrollY", "bodyOverflowState", "scroll", "_silentRenderMode", "classNamePrefixBase", "userSelect", "coverTop", "uiOffsetTop", "coverLeft", "uiOffsetLeft", "zIndex", "uiDiv", "flexDirection", "popup<PERSON><PERSON><PERSON>", "addButtonClickListener", "show", "hideToolbar", "flexShrink", "marginTop", "toolbox", "hideToolbox", "remove", "dispose", "mode", "addUndoStep", "buttonType", "clear", "undo", "redo", "notesArea", "startRenderAndClose", "cancel_2", "<PERSON><PERSON><PERSON><PERSON>", "marker_1", "cancel", "notesAreaStyleClassName", "alignSelf", "isNotesAreaOpen", "lastUndoState", "getLastUndoStep", "beforeSteps", "undoStepCount", "replaceLastUndoStep", "undoStep", "selectLastMarker", "redoStep", "zoomStepIndex", "zoomSteps", "scrollBy", "prevPanPoint", "render", "MarkerAreaRenderEvent", "deselectCurrentMarker", "markerState", "mType", "setActiveMarkerButton", "setPanelButtons", "toolboxPanels", "defaultColorsFollowCurrentColors", "isSelected", "touchPoints", "pointerType", "hitMarker", "m", "isDragging", "clientX", "clientY", "panTo", "clientRect", "positionUI", "rects", "getClientRects", "item", "<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sourceRoot": ""}