{"version": 3, "file": "percent.js", "mappings": "+fASO,MAAMA,UAA4BC,EAAA,EAC9B,iBAAAC,GACTC,MAAMD,oBAEN,MAAME,EAAKC,KAAKC,MAAMC,gBAChBC,EAAIH,KAAKI,KAAKC,KAAKL,MAQzBG,EAAE,iBAAiBG,OAAO,CACzBC,gBAAiB,aACjBC,gBAAiB,4CACjBC,OAAQC,EAAA,EAASC,IAAIX,KAAKC,MAAO,CAAC,GAClCW,MAAO,KACPC,OAAQ,OASTV,EAAE,YAAYG,OAAO,CACpBQ,QAAQ,QAAQ,IAChBC,YAAa,GACbC,SAAU,MAGXb,EAAE,aAAaG,OAAO,CACrBW,aAAa,EACbF,YAAa,GACbC,SAAU,MAGXb,EAAE,aAAae,OAAOC,OAAO,SAAU,CAAEH,UAAW,GAAII,QAAS,IAEjEjB,EAAE,QAAS,CAAC,QAAQG,OAAO,CAC1Be,SAAU,WACVC,YAAY,EACZC,EAAG,EACHC,EAAG,EACHC,UAAW,SACXC,YAAa,wDACbC,YAAa,EACbC,cAAe,EACfC,KAAM,SACNC,SAAS,UAGV3B,EAAE,QAAS,CAAC,QAAQe,OAAOC,OAAO,SAAU,CAAEY,YAAa,GAAIC,MAAO,IACtE7B,EAAE,QAAS,CAAC,QAAQe,OAAOC,OAAO,cAAe,CAAEa,MAAO,OAC1D7B,EAAE,QAAS,CAAC,QAAQe,OAAOC,OAAO,QAAS,CAAEa,MAAO,OAEpD7B,EAAE,cAAe,CAAC,QAAQG,OAAO,CAChC2B,SAAU,UACVnB,OAAQ,GACRoB,KAAM,wDACNC,WAAY,EACZC,cAAe,EACfC,cAAc,IAGflC,EAAE,OAAQ,CAAC,QAAQG,OAAO,CACzBgC,SAAU,IAUXnC,EAAE,eAAeG,OAAO,CACvBiC,YAAa,GACbC,aAAc,GACdL,WAAY,GACZC,cAAe,KAShBjC,EAAE,gBAAgBG,OAAO,CACxBmC,cAAe,EACfC,YAAa,EACbC,YAAa,WACb1B,aAAa,EACb2B,wBAAwB,IAGzBzC,EAAE,eAAeG,OAAO,CACvBuC,aAAa,EACbC,eAAgB,IAIjB3C,EAAE,eAAee,OAAOC,OAAO,QAAS,CAAE2B,eAAgB,MAE1D3C,EAAE,QAAS,CAAC,WAAWG,OAAO,CAC7B+B,cAAc,EACdH,KAAM,wDACNa,QAAS,OAGV5C,EAAE,QAAS,CAAC,SAAU,eAAeG,OAAO,CAC3C0C,QAAS,EACTD,QAAS,KACTE,UAAW,KAIZ9C,EAAE,QAAS,CAAC,SAAU,aAAaG,OAAO,CACzCyC,QAAS,KACTC,QAAS,IAGV7C,EAAE,OAAQ,CAAC,WAAWG,OAAO,CAC5BgC,SAAU,IAGXnC,EAAE,cAAe,CAAC,SAAU,SAASG,OAAO,CAC3C4C,YAAa,GACbtB,cAAe,EACfkB,gBAAiB,KAGlB3C,EAAE,cAAe,CAAC,SAAU,OAAQ,aAAaG,OAAO,CACvDO,OAAQ,KAGTV,EAAE,cAAe,CAAC,SAAU,OAAQ,eAAeG,OAAO,CACzDM,MAAO,KAURT,EAAE,iBAAiBG,OAAO,CACzB6C,QAAS,SAGVhD,EAAE,cAAe,CAAC,UAAW,SAASG,OAAO,CAC5C4C,YAAa,KAGd/C,EAAE,cAAe,CAAC,UAAW,OAAQ,aAAaG,OAAO,CACxDO,OAAQ,IAGTV,EAAE,cAAe,CAAC,UAAW,OAAQ,eAAeG,OAAO,CAC1DM,MAAO,IAGRT,EAAE,cAAe,CAAC,YAAYG,OAAO,CACpCuC,aAAa,EACbC,eAAgB,IAGjB3C,EAAE,cAAe,CAAC,YAAYe,OAAOC,OAAO,QAAS,CAAE2B,eAAgB,MAEvE3C,EAAE,QAAS,CAAC,YAAYG,OAAO,CAC9B+B,cAAc,EACdH,KAAM,wDACNa,QAAS,OAGV5C,EAAE,QAAS,CAAC,UAAW,eAAeG,OAAO,CAC5C0C,QAAS,EACTD,QAAS,KACTE,UAAW,KAGZ9C,EAAE,QAAS,CAAC,UAAW,aAAaG,OAAO,CAC1CyC,QAAS,KACTC,QAAS,IAGV7C,EAAE,OAAQ,CAAC,YAAYG,OAAO,CAC7BgC,SAAU,IAWXnC,EAAE,cAAe,CAAC,cAAcG,OAAO,CACtCuC,aAAa,EACbnB,YAAa,0DAGdvB,EAAE,QAAS,CAAC,cAAcG,OAAO,CAChC+B,cAAc,EACdH,KAAM,wDACNa,QAAS,OAGV5C,EAAE,QAAS,CAAC,YAAa,eAAeG,OAAO,CAC9C0C,QAAS,EACTD,QAAS,KACTE,UAAW,KAGZ9C,EAAE,QAAS,CAAC,YAAa,aAAaG,OAAO,CAC5CyC,QAAS,KACTC,QAAS,IAGV7C,EAAE,cAAe,CAAC,YAAa,SAASG,OAAO,CAC9C4C,YAAa,GACbtC,MAAO,EACPC,OAAQ,IAGTV,EAAE,OAAQ,CAAC,cAAcG,OAAO,CAC/BgC,SAAU,KAGX,CACC,MAAMlC,EAAOD,EAAE,WAAY,CAAC,YAAa,eAEzCC,EAAKE,OAAO,CACX4C,YAAa,MAGd,OAAS9C,EAAM,OAAQL,EAAI,wB,CAG7B,EC5OM,MAAeqD,UAAqBC,EAAA,EAQhC,SAAAC,GACTtD,KAAKuD,eAAeC,KAAK7D,EAAoBgB,IAAIX,KAAKC,QAEtDH,MAAMwD,YAENtD,KAAKyD,eAAeC,SAASF,KAAKxD,KAAK2D,iBACvC3D,KAAK2D,gBAAgBD,SAASF,KAAKxD,KAAK4D,iBACzC,CAEU,cAAAC,CAAeC,GACxBhE,MAAM+D,eAAeC,GACrB9D,KAAK2D,gBAAgBD,SAASK,UAAU/D,KAAK4D,iBAAkB5D,KAAK2D,gBAAgBD,SAASM,OAAS,EACvG,EAnBA,qC,gDAAkC,iBAClC,sC,gDAA0CX,EAAA,EAAYY,WAAWC,OAAO,CAACd,EAAae,c,gECsHhF,MAAeC,UAAsBC,EAAA,EAA5C,c,oBAYC,8C,gDAAkCrE,KAAK0D,SAASF,KAAKc,EAAA,EAAU3D,IAAIX,KAAKC,MAAO,CAAEoB,SAAU,WAAYC,YAAY,OACnH,8C,gDAAkCtB,KAAK0D,SAASF,KAAKc,EAAA,EAAU3D,IAAIX,KAAKC,MAAO,CAAEoB,SAAU,WAAYC,YAAY,OACnH,6C,gDAAiCtB,KAAK0D,SAASF,KAAKc,EAAA,EAAU3D,IAAIX,KAAKC,MAAO,CAAEoB,SAAU,WAAYC,YAAY,OAElH,uC,gDAAyD,KACzD,uC,gDAAyD,KACzD,uC,gDAAyD,KAOzD,qC,gDAA2DtB,KAAKuE,gBAoChE,qC,gDAA2DvE,KAAKwE,gBAoBhE,oC,gDAAyDxE,KAAKyE,cAud/D,CAtgBQ,SAAAC,CAAUC,GAChB,MAAMC,EAAQ5E,KAAK6E,gBAAgBnB,SAASF,KAAKxD,KAAK8E,OAAOC,QAkB7D,OAhBAH,EAAMI,GAAG,QAAQ,KAChBhF,KAAKiF,mBAAmBN,EAAS,IAGlCC,EAAMI,GAAG,eAAe,KACvBhF,KAAKiF,mBAAmBN,EAAS,IAGlCC,EAAMI,GAAG,UAAU,KAClBhF,KAAKiF,mBAAmBN,EAAS,IAGlCC,EAAMM,aAAaP,GACnBA,EAASQ,IAAI,QAASP,GACtB5E,KAAK8E,OAAOtB,KAAKoB,GAEVA,CACR,CAcO,SAAAQ,CAAUT,GAChB,MAAMU,EAAQrF,KAAKsF,gBAAgB5B,SAASF,KAAKxD,KAAKuF,OAAOR,QAI7D,OAHAM,EAAMH,aAAaP,GACnBA,EAASQ,IAAI,QAASE,GACtBrF,KAAKuF,OAAO/B,KAAK6B,GACVA,CACR,CAYU,iBAAAG,CAAkBb,GAC3B,OAA6B,MAAzBA,EAASc,IAAI,QAIlB,CAKO,QAAAC,CAASf,GACf,MAAMgB,EAAO3F,KAAK4F,eAAelC,SAASF,KAAKxD,KAAK6F,MAAMd,QAI1D,OAHAY,EAAKT,aAAaP,GAClBA,EAASQ,IAAI,OAAQQ,GACrB3F,KAAK6F,MAAMrC,KAAKmC,GACTA,CACR,CAEU,SAAArC,GACTtD,KAAK8F,OAAOtC,KAAK,WAAY,QAC7B1D,MAAMwD,WACP,CAEU,YAAAyC,GACT,MAAMtF,EAAST,KAAKyF,IAAI,UACpBhF,GACHA,EAAOuF,QAER,MAAMC,EAAWjG,KAAKyF,IAAI,YACtBQ,GACHA,EAASD,OAEX,CAEO,gBAAAE,GAON,GANApG,MAAMoG,mBAENlG,KAAKmG,SAAW,GAChBnG,KAAKoG,SAAW,GAChBpG,KAAKqG,SAAW,GAEZrG,KAAKsG,aAAc,CACtB,IAAIC,EAAM,EACNC,EAAS,EACTC,EAAY,EACZC,EAAWC,IACXC,EAAQ,EACZ,OAAY5G,KAAK6G,YAAalC,IAC7B,IAAImC,EAAenC,EAASc,IAAI,eAAgB,GAChDc,GAAOO,EACPN,GAAUO,KAAKC,IAAIF,EAAa,IAGjC,OAAY9G,KAAK6G,YAAalC,IAC7B,IAAIsC,EAAQtC,EAASc,IAAI,eAAgB,GAErCwB,EAAQR,IACXA,EAAYQ,GAGTA,EAAQP,IACXA,EAAWO,GAGZL,IAEA,IAAIM,EAAeD,EAAQT,EAEb,GAAVA,IACHU,EAAe,GAGhBvC,EAASwC,OAAO,oBAAoC,IAAfD,EAAmB,IAGzDlH,KAAKoH,cAAc,WAAYV,GAC/B1G,KAAKoH,cAAc,YAAaX,GAChCzG,KAAKoH,cAAc,WAAYb,GAC/BvG,KAAKoH,cAAc,eAAgBb,EAAMK,GACzC5G,KAAKoH,cAAc,mBAAoBZ,E,CAEzC,CAQa,IAAAa,CAAKC,G,uGACjB,IAAIC,EAAgC,GACpCA,EAAS/D,KAAK,EAAM6D,KAAI,UAACC,IAEzBC,EAAS/D,KAAKxD,KAAKwH,oBAAmB,EAAMF,UACtCG,QAAQC,IAAIH,EACnB,G,CAQa,IAAAI,CAAKL,G,uGACjB,IAAIC,EAAgC,GACpCA,EAAS/D,KAAK,EAAMmE,KAAI,UAACL,IAEzBC,EAAS/D,KAAKxD,KAAKwH,oBAAmB,EAAOF,UACvCG,QAAQC,IAAIH,EACnB,G,CAKO,eAAAK,GACN9H,MAAM8H,kBACF5H,KAAKsG,cACR,OAAYtG,KAAK6G,YAAalC,IAC7BA,EAASc,IAAI,SAASvD,KAAK2F,eAAe,KAIxC7H,KAAK8H,QAAQ,oBAAsB9H,KAAK8H,QAAQ,qBACnD,OAAY9H,KAAK6G,YAAalC,IAC7B3E,KAAK+H,kBAAkBpD,EAAS,IAIlC3E,KAAKgI,UACN,CAEU,QAAAA,GACThI,KAAKiI,aAAajI,KAAKmG,UACvBnG,KAAKkI,WAAWlI,KAAKmG,UAErBnG,KAAKiI,aAAajI,KAAKoG,UACvBpG,KAAKkI,WAAWlI,KAAKoG,UAErBpG,KAAKmI,aAAanI,KAAKqG,UACvBrG,KAAKoI,cAAcpI,KAAKqG,UAExB,OAAYrG,KAAKqI,WAAY1D,IAC5B3E,KAAKsI,YAAY3D,EAAS,GAE5B,CAGO,aAAA4D,GACNzI,MAAMyI,gBACNvI,KAAKgI,UACN,CAEU,eAAAQ,CAAgB7D,GAGzB,GAFA7E,MAAM0I,gBAAgB7D,GAEM,MAAxBA,EAASc,IAAI,QAAiB,CACjC,IAAIhF,EAAST,KAAKyF,IAAI,UAClBhF,GACHkE,EAASwC,OAAO,OAAQ1G,EAAOgI,O,CAIjC,GAAmC,MAA/B9D,EAASc,IAAI,eAAwB,CACxC,IAAIQ,EAAWjG,KAAKyF,IAAI,YACpBQ,GACHtB,EAASwC,OAAO,cAAelB,EAASwC,O,CAG3C,CASa,YAAAC,CAAa/D,EAA+C2C,G,uHACxE,MAAMC,EAAW,CAAC,EAAMmB,aAAY,UAAC/D,EAAU2C,IAC1C,WAAeA,KACnBA,EAAWtH,KAAKyF,IAAI,yBAA0B,IAG/C,MAAMkD,EAAS3I,KAAKyF,IAAI,wBAExB,IAAIwB,EAAQtC,EAASc,IAAI,SAEzB,MAAMmD,EAAYjE,EAASkE,QAAQ,CAAEC,IAAK,eAAgBC,GAAI9B,EAAOK,SAAUA,EAAUqB,OAAQA,IAC7FC,GACHrB,EAAS/D,KAAKoF,EAAUI,eAGzB,MAAMrD,EAAOhB,EAASc,IAAI,QACtBE,GACH4B,EAAS/D,KAAKmC,EAAK0B,KAAKC,IAEzB,MAAMjC,EAAQV,EAASc,IAAI,SACvBJ,GACHkC,EAAS/D,KAAK6B,EAAMgC,KAAKC,IAG1B,MAAM1C,EAAQD,EAASc,IAAI,SACvBb,GACH2C,EAAS/D,KAAKoB,EAAMyC,KAAKC,IAGtB1C,EAAMa,IAAI,WACbb,EAAM1D,OAAO+H,aAAa,gBAGrBxB,QAAQC,IAAIH,EACnB,G,CASa,YAAA2B,CAAavE,EAA+C2C,G,uHACxE,MAAMC,EAAW,CAAC,EAAM2B,aAAY,UAACvE,EAAU2C,IACzC6B,EAAcnJ,KAAKkB,OAAOC,OAAO,SAAU,CAAC,GAE7C,WAAemG,KACnBA,EAAW6B,EAAY1D,IAAI,yBAA0BzF,KAAKyF,IAAI,yBAA0B,KAGzF,MAAMkD,EAASQ,EAAY1D,IAAI,uBAAwBzF,KAAKyF,IAAI,yBAE1DmD,EAAYjE,EAASkE,QAAQ,CAAEC,IAAK,eAAgBC,GAAI,EAAGzB,SAAUA,EAAUqB,OAAQA,IACzFC,GACHrB,EAAS/D,KAAKoF,EAAUI,eAGzB,MAAMrD,EAAOhB,EAASc,IAAI,QACtBE,GACH4B,EAAS/D,KAAKmC,EAAKgC,KAAKL,IAEzB,MAAMjC,EAAQV,EAASc,IAAI,SACvBJ,GACHkC,EAAS/D,KAAK6B,EAAMsC,KAAKL,IAG1B,MAAM1C,EAAQD,EAASc,IAAI,SAC3Bb,EAAMwE,cAEFxE,GACH2C,EAAS/D,KAAKoB,EAAM+C,KAAKL,UAGpBG,QAAQC,IAAIH,EACnB,G,CAKO,eAAA8B,CAAgB1E,GACtB7E,MAAMuJ,gBAAgB1E,GACtB,IAAIU,EAAQV,EAASc,IAAI,SACrBJ,IACHrF,KAAKuF,OAAO+D,YAAYjE,GACxBA,EAAMkE,WAEP,IAAI5D,EAAOhB,EAASc,IAAI,QACpBE,IACH3F,KAAK6F,MAAMyD,YAAY3D,GACvBA,EAAK4D,WAEN,IAAI3E,EAAQD,EAASc,IAAI,SACrBb,IACH5E,KAAK8E,OAAOwE,YAAY1E,GACxBA,EAAM2E,UAER,CAQO,aAAAC,CAAc7E,GACpB,MAAMC,EAAQD,EAASc,IAAI,SACvBb,IAAUA,EAAM6E,YACnB7E,EAAM8E,OAER,CAQO,eAAAC,CAAgBhF,GACtB,MAAMC,EAAQD,EAASc,IAAI,SACvBb,GACHA,EAAMgF,SAER,CAKO,kBAAA3E,CAAmBN,GACzB,GAAIA,EAAU,CACb,MAAMC,EAAQD,EAASc,IAAI,SAE3B,GAAIb,EAAO,CACV,MAAMiF,EAAiBlF,EAASc,IAAI,kBACpC,GAAIoE,EAAgB,CACnB,MAAMC,EAAkBD,EAAepE,IAAI,mBAC3C,OAAY,KAAiBsE,IACF,MAAtBnF,EAAMa,IAAIsE,IACbD,EAAgB3E,IAAI4E,EAASnF,EAAMa,IAAIsE,G,MAM7C,CAEU,YAAA9B,CAAa1C,GACtB,GAAIA,EAAQ,CAEX,IAAIkD,EAAOzI,KAAKgK,eAEhBzE,EAAO0E,MAAK,CAACC,EAAGC,IACXD,EAAE1I,EAAI2I,EAAE3I,EACJ,EAEC0I,EAAE1I,EAAI2I,EAAE3I,GACR,EAGD,IAIT,OAAY+D,GAAS6E,IACpB,MAAMC,EAASD,EAAE/E,MAAMiF,sBACvB,IAAIC,EAAWF,EAAOG,IAClBJ,EAAE5I,EAAI+I,EAAW9B,IACpB2B,EAAE5I,EAAIiH,EAAO8B,GAEdH,EAAE/E,MAAMF,IAAI,IAAKiF,EAAE5I,GAEnBiH,EAAO2B,EAAE5I,EAAI6I,EAAOI,MAAM,G,CAG7B,CAEU,UAAAC,GACT,OAAO1K,KAAKsF,gBAAgBqF,WAC7B,CAEU,YAAAX,GACT,OAAO,CACR,CAEU,UAAA9B,CAAW3C,GACpB,GAAIA,EAAQ,CACX,IAAIkD,EAAOzI,KAAK0K,aAEhBnF,EAAO0E,MAAK,CAACC,EAAGC,IACXD,EAAE1I,EAAI2I,EAAE3I,EACJ,EAEC0I,EAAE1I,EAAI2I,EAAE3I,GACR,EAGD,IAIT,OAAY+D,GAAS6E,IACpB,MAAMC,EAASD,EAAE/E,MAAMiF,sBACvB,IAAIM,EAAcP,EAAOI,OACrBL,EAAE5I,EAAIoJ,EAAcnC,IACvB2B,EAAE5I,EAAIiH,EAAOmC,GAEdR,EAAE/E,MAAMF,IAAI,IAAKiF,EAAE5I,GACnBiH,EAAO2B,EAAE5I,EAAI6I,EAAOG,GAAG,G,CAG1B,CAEU,aAAApC,CAAc7C,GACvB,GAAIA,EAAQ,CAEX,IAAIkD,EAAO,EAEXlD,EAAO0E,MAAK,CAACC,EAAGC,IACXD,EAAE1I,EAAI2I,EAAE3I,EACJ,EAEC0I,EAAE1I,EAAI2I,EAAE3I,GACR,EAGD,IAIT,OAAY+D,GAAS6E,IACpB,MAAMC,EAASD,EAAE/E,MAAMiF,sBACvB,IAAIO,EAAYR,EAAOS,KACnBV,EAAE5I,EAAIqJ,EAAYpC,IACrB2B,EAAE5I,EAAIiH,EAAOoC,GAEdT,EAAE/E,MAAMF,IAAI,IAAKiF,EAAE5I,GAEnBiH,EAAO2B,EAAE5I,EAAI6I,EAAOU,KAAK,G,CAG5B,CAEU,YAAA5C,CAAa5C,GACtB,GAAIA,EAAQ,CACX,IAAIkD,EAAOzI,KAAKsF,gBAAgB0F,WAEhCzF,EAAO0E,MAAK,CAACC,EAAGC,IACXD,EAAE1I,EAAI2I,EAAE3I,EACJ,EAEC0I,EAAE1I,EAAI2I,EAAE3I,GACR,EAGD,IAIT,OAAY+D,GAAS6E,IACpB,MAAMC,EAASD,EAAE/E,MAAMiF,sBACvB,IAAIW,EAAaZ,EAAOU,MACpBX,EAAE5I,EAAIyJ,EAAaxC,IACtB2B,EAAE5I,EAAIiH,EAAOwC,GAEdb,EAAE/E,MAAMF,IAAI,IAAKiF,EAAE5I,GACnBiH,EAAO2B,EAAE5I,EAAI6I,EAAOS,IAAI,G,CAG3B,CAEO,WAAAI,GACNpL,MAAMoL,cACNlL,KAAKmL,WACN,CAEU,WAAA7C,CAAY8C,GAEtB,CAEU,QAAAC,GACTvL,MAAMuL,WAEN,MAAMC,EAAQtL,KAAKsL,MACfA,GACHA,EAAMxH,OAAOwF,YAAYtJ,KAE3B,EApiBA,qC,gDAAkC,kBAClC,sC,gDAA0CqE,EAAA,EAAOJ,WAAWC,OAAO,CAACE,EAAcD,c,uBCtE5E,MAAMoH,UAAiBnI,EAA9B,c,oBASC,yC,gDAA4B,GAyH7B,CAvHW,SAAAE,GACTxD,MAAMwD,YACNtD,KAAK2D,gBAAgBrD,OAAO,CAAEiB,EAAG,KAAKC,EAAG,MAC1C,CAEO,gBAAA0E,GACNpG,MAAMoG,mBAEN,MAAMzC,EAAiBzD,KAAKyD,eACtB+H,EAAI/H,EAAegI,aACnBC,EAAIjI,EAAekI,cAEnB5K,EAAaf,KAAKyF,IAAI,aAAc,GACpCzE,EAAWhB,KAAKyF,IAAI,WAAY,GAChCmG,EAAc5L,KAAKyF,IAAI,eAE7B,IAAI4E,EAAS,eAAmB,EAAG,EAAGtJ,EAAYC,EAAU,GAE5D,MAAM6K,EAAKL,GAAKnB,EAAOU,MAAQV,EAAOS,MAChCgB,EAAKJ,GAAKrB,EAAOI,OAASJ,EAAOG,KAEvC,IAAIuB,EAAc,CAAEjB,KAAM,EAAGC,MAAO,EAAGP,IAAK,EAAGC,OAAQ,GAEvD,GAAImB,aAAuBI,EAAA,GAAS,CACnC,IAAI/E,EAAQ2E,EAAY3E,MACpBgF,EAAKlF,KAAKmF,IAAIL,EAAIC,GACtB7E,EAAQF,KAAKoF,IAAIF,EAAKhF,EAAOgF,EAAKlF,KAAKmF,IAAIR,EAAGF,IAAMS,EACpDF,EAAc,eAAmB,EAAG,EAAGhL,EAAYC,EAAUiG,GAC7DjH,KAAKoH,cAAc,aAAcH,EAAQ2E,EAAY3E,M,CAGtDoD,EAAS,cAAkB,CAACA,EAAQ0B,IAEpC,MAAMK,EAAapM,KAAKqM,WACxBrM,KAAKqM,WAAatF,KAAKmF,IAAIL,EAAIC,GAE/B,MAAMhL,EAAS,kBAAuBd,KAAKyF,IAAI,SAAU,GAAIzF,KAAKqM,YAClErM,KAAK2D,gBAAgBrD,OAAO,CAC3BgM,IAAKxL,GAAUuJ,EAAOI,OAASJ,EAAOG,KAAO,EAAG+B,IAAKzL,GAAUuJ,EAAOU,MAAQV,EAAOS,MAAQ,KAG1F9K,KAAK8H,QAAQ,eAAiB9H,KAAK8H,QAAQ,aAAesE,GAAcpM,KAAKqM,aAChFrM,KAAK8D,OAAO0I,MAAM1I,IACjBA,EAAO2I,cAAc,aAAa,KAIjCzM,KAAK8H,QAAQ,gBAAkB9H,KAAK8H,QAAQ,YAC9C9H,KAAK8D,OAAO0I,MAAM1I,IACjBA,EAAO2I,cAAc,cAAc,GAGtC,CAWO,MAAA3L,CAAOgD,GAEb,IAAIhD,EAAS,kBAAuBd,KAAKyF,IAAI,SAAU,GAAIzF,KAAKqM,YAC5DT,EAAc,kBAAuB5L,KAAKyF,IAAI,cAAe,GAAI3E,GAErE,GAAIgD,EAAQ,CACX,IAAI4I,EAAQ1M,KAAK8D,OAAO6I,QAAQ7I,GAC5BE,EAAShE,KAAK8D,OAAOE,OAErB4I,EAAe9I,EAAO2B,IAAI,UAC9B,OAAoB,MAAhBmH,EACIhB,EAAc,kBAAuBgB,EAAc9L,EAAS8K,GAG5DA,GAAe9K,EAAS8K,GAAe5H,GAAU0I,EAAQ,E,CAGlE,OAAO5L,CACR,CAWO,WAAA8K,CAAY9H,GAClB,MAAMhD,EAASd,KAAKc,SACpB,IAAI8K,EAAc,kBAAuB5L,KAAKyF,IAAI,cAAe,GAAI3E,GAMrE,GAJI8K,EAAc,IACjBA,EAAc9K,EAAS8K,GAGpB9H,EAAQ,CACX,IAAI4I,EAAQ1M,KAAK8D,OAAO6I,QAAQ7I,GAC5BE,EAAShE,KAAK8D,OAAOE,OAErB6I,EAAoB/I,EAAO2B,IAAI,eACnC,OAAyB,MAArBoH,EACIjB,EAAc,kBAAuBiB,EAAmB/L,EAAS8K,GAGjEA,GAAe9K,EAAS8K,GAAe5H,EAAS0I,C,CAGzD,OAAOd,CACR,CAEO,WAAAV,GACNpL,MAAMoL,cACNlL,KAAK8M,aAAa,SACnB,EA/HA,qC,gDAAkC,aAClC,sC,gDAA0C1J,EAAaa,WAAWC,OAAO,CAACqH,EAASpH,c,qDCL7E,MAAM4I,UAAkB3I,EAcpB,WAAAG,GACT,OAAO,IAAIyI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAMuM,EAAA,EAAMC,KAAKnN,KAAKC,MAAO,CAC5BmN,UAAW,YAAiBpN,KAAK8E,OAAOuI,SAAS5H,IAAI,YAAa,IAAK,CAAC,MAAO,YAC7E,CAACzF,KAAK8E,OAAOuI,YAElB,CAEU,WAAA7I,GACT,OAAO,IAAIwI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAM2M,EAAA,EAAYH,KAAKnN,KAAKC,MAAO,CAClCmN,UAAW,YAAiBpN,KAAKuF,OAAO8H,SAAS5H,IAAI,YAAa,IAAK,CAAC,MAAO,YAC7E,CAACzF,KAAKuF,OAAO8H,YAElB,CAEU,UAAA5I,GACT,OAAO,IAAIuI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAM4M,EAAA,EAAKJ,KAAKnN,KAAKC,MAAO,CAC3BmN,UAAW,YAAiBpN,KAAK6F,MAAMwH,SAAS5H,IAAI,YAAa,IAAK,CAAC,MAAO,YAC5E,CAACzF,KAAK6F,MAAMwH,YAEjB,CAEU,eAAA7E,CAAgB7D,GACzB7E,MAAM0I,gBAAgB7D,GAEtB,MAAMC,EAAQ5E,KAAK0E,UAAUC,GAE7BC,EAAMI,GAAG,SAAS,KACjBhF,KAAKsI,YAAY3D,EAAS,IAE3BC,EAAMI,GAAG,eAAe,KACvBhF,KAAKsI,YAAY3D,EAAS,IAE3BC,EAAM4I,OAAOxI,GAAG,mBAAmB,KAClChF,KAAKsI,YAAY3D,EAAS,IAG3B,MAAMU,EAAQrF,KAAKoF,UAAUT,GAE7BU,EAAMmI,OAAOxI,GAAG,mBAAmB,KAClChF,KAAKsI,YAAY3D,EAAS,IAG3B3E,KAAK0F,SAASf,GAEdC,EAAM4I,OAAOxI,GAAG,mBAAmB,KAClCK,EAAM8F,WAAW,GAEnB,CAEU,UAAAT,GACT,MAAMY,EAAQtL,KAAKsL,MACnB,OAAIA,EACIA,EAAMe,WAEPrM,KAAKsF,gBAAgBqF,YAAc,CAC3C,CAEU,YAAAX,GACT,MAAMsB,EAAQtL,KAAKsL,MACnB,OAAIA,GACKA,EAAMe,YAEPrM,KAAKsF,gBAAgBqF,YAAc,CAE5C,CAEO,gBAAAzE,GACNpG,MAAMoG,mBACN,MAAMoF,EAAQtL,KAAKsL,MACnB,GAAIA,EAAO,CAEV,GAAItL,KAAK8H,QAAQ,eAAgB,CAChC,IAAI2F,EAAiBzN,KAAKuF,OAAO8H,SAEjC,GAAIrN,KAAKyF,IAAI,eACZgI,EAAetI,IAAI,WAAY,eAE3B,CACJ,IAAIlD,EAAWwL,EAAehI,IAAI,YAClB,MAAZxD,GAAgC,WAAZA,GACvBwL,EAAetI,IAAI,WAAY,W,EAMlC,GAAInF,KAAKsG,cAAgBtG,KAAK8H,QAAQ,WAAa9H,KAAK8H,QAAQ,gBAAkB9H,KAAK8H,QAAQ,eAAiB9H,KAAK8H,QAAQ,aAAe9H,KAAK8H,QAAQ,eAAgB,CACxK9H,KAAK0N,kBACL,MAAM3M,EAAaf,KAAKyF,IAAI,aAAc6F,EAAM7F,IAAI,cAAe,KAE7DkI,EADW3N,KAAKyF,IAAI,WAAY6F,EAAM7F,IAAI,WAAY,MACrC1E,EACvB,IAAI6M,EAAe7M,EAEnB,MAAMD,EAASwK,EAAMxK,OAAOd,MAC5BA,KAAKoH,cAAc,SAAUtG,GAC7B,IAAI8K,EAAcN,EAAMM,YAAY5L,MAAQsL,EAAMuC,WAAW,aAAc,GAEvEjC,EAAc,IACjBA,EAAc9K,EAAS8K,GAIxB,OAAY5L,KAAK6G,YAAalC,IAE7B3E,KAAK+H,kBAAkBpD,GAEvB,IAAImJ,EAAaH,EAAMhJ,EAASc,IAAI,qBAAuB,IAC3D,MAAMb,EAAQD,EAASc,IAAI,SAC3B,GAAIb,EAAO,CACVA,EAAMO,IAAI,SAAUrE,GACpB8D,EAAMO,IAAI,cAAeyG,GACzBhH,EAAMO,IAAI,aAAcyI,GAExBhJ,EAAMO,IAAI,MAAO2I,GAEjB,MAAMC,EAAQpJ,EAASc,IAAI,QAC3Bb,EAAMoJ,YAAY,OAAQD,GAC1BnJ,EAAMoJ,YAAY,SAAUD,GAE5B,MAAME,EAActJ,EAASc,IAAI,eACjCb,EAAMoJ,YAAY,cAAeC,E,CAGlC,IAAIC,EAAc,iBAAqBN,EAAeE,EAAa,GAEnE,MAAMzI,EAAQV,EAASc,IAAI,SAC3B,GAAIJ,IACHA,EAAM8I,WAAW,SAAUrN,GAC3BuE,EAAM8I,WAAW,cAAevC,GAChCvG,EAAMF,IAAI,aAAc+I,GAEK,WAAzB7I,EAAMI,IAAI,aAA0B,CACvC,IAAI2I,EAActN,EAASuE,EAAMI,IAAI,SAAU,GAC3CjE,EAAIV,EAAS,MAAUoN,GAEvBA,EAAc,IAAMA,GAAe,KACjC7I,EAAMoE,YAAepE,EAAMgJ,YAC/BrO,KAAKmG,SAAS3C,KAAK,CAAE6B,MAAOA,EAAO7D,EAAGA,IAEvC4M,IAAgB,EAChBA,GAAepO,KAAKsF,gBAAgBG,IAAI,cAAe,GACvDJ,EAAMF,IAAI,UAAW,MACrBE,EAAM+B,cAAc,QAAQ,KAGvB/B,EAAMoE,YAAepE,EAAMgJ,YAC/BrO,KAAKoG,SAAS5C,KAAK,CAAE6B,MAAOA,EAAO7D,EAAGA,IAEvC4M,GAAepO,KAAKsF,gBAAgBG,IAAI,eAAgB,GACxDJ,EAAMF,IAAI,UAAW,GACrBE,EAAM+B,cAAc,QAAQ,IAE7B/B,EAAMF,IAAI,IAAKiJ,GACf/I,EAAMF,IAAI,IAAKrE,EAAS,MAAUoN,G,CAGpCN,GAAgBE,EAChB9N,KAAKsI,YAAY3D,EAAS,G,EAK9B,CAEU,WAAA2D,CAAY3D,GACrB,MAAMgB,EAAOhB,EAASc,IAAI,QACpBJ,EAAQV,EAASc,IAAI,SACrBb,EAAQD,EAASc,IAAI,SACrBnD,EAAWqD,EAAKF,IAAI,WAAY,GACtC,GAAIE,GAAQN,GAAST,EAAO,CAC3B,MAAM9D,GAAU8D,EAAMa,IAAI,cAAe,GAAKb,EAAMa,IAAI,SAAU,IAAMb,EAAMa,IAAI,QAAS,GAAKnD,EAC1FgM,EAAajJ,EAAMI,IAAI,aAAc,GACrC8I,EAAM,MAAUD,GAChBE,EAAM,MAAUF,GAEhBhJ,EAAkBtF,KAAKsF,gBACvBmJ,EAAKnJ,EAAgBG,IAAI,cAAe,GACxCiJ,EAAKpJ,EAAgBG,IAAI,eAAgB,GAE/C,IAAIlE,EAAI,EACJC,EAAI,EAERD,EAAI8D,EAAM9D,IACVC,EAAI6D,EAAM7D,IACV,IAAImN,EAAuB,GAE3B,GAAQ,GAALpN,GAAe,GAALC,EAAO,CACnB,GAA6B,YAAzB6D,EAAMI,IAAI,YAA2B,CACxC,MAAM2I,EAAc/I,EAAMvE,SAAWuE,EAAMI,IAAI,gBAAiB,GAC1D6I,EAAajJ,EAAMI,IAAI,aAAc,GAC3ClE,EAAI6M,EAAc,MAAUE,GAC5B9M,EAAI4M,EAAc,MAAUE,E,CAG7B,IAAI/B,GAAMmC,EACNrJ,EAAMwI,WAAW,UACpBtB,EAAKkC,GAENE,EAAS,CAAC,CAAEpN,EAAGqD,EAAMrD,IAAMT,EAASyN,EAAK/M,EAAGoD,EAAMpD,IAAMV,EAAS0N,GAAO,CAAEjN,EAAGA,EAAIgL,EAAI/K,EAAGA,GAAK,CAAED,EAAGA,EAAGC,EAAGA,G,CAGzGmE,EAAKR,IAAI,SAAUwJ,E,CAErB,CAEO,eAAAC,CAAgBC,GAEtB,MAAMC,EAASD,EAAOpJ,IAAI,UAC1B,GAAIqJ,EAAQ,CACX,MACMlK,EADWkK,EAAOnK,SACDc,IAAI,SAE3B,GAAIb,EAAO,CACV,MAAMgH,EAAchH,EAAMa,IAAI,cAAe,GACvC3E,EAAS8D,EAAMa,IAAI,SAAU,GAM7BsJ,EALanK,EAAMa,IAAI,aAAc,GAC/Bb,EAAMa,IAAI,MAAO,GACXoJ,EAAOpJ,IAAI,YAAa,IAIpCtF,EAAIyL,GAAe9K,EAAS8K,GAHhBiD,EAAOpJ,IAAI,YAAa,IAK1CqJ,EAAOxO,OAAO,CAAEiB,EAAG,MAAUwN,GAAS5O,EAAGqB,EAAG,MAAUuN,GAAS5O,G,EAGlE,EAlPA,qC,gDAAkC,cAClC,sC,gDAA0CiE,EAAcH,WAAWC,OAAO,CAAC6I,EAAU5I,cCrC/E,MAAM6K,UAAoBC,EAAA,EAAjC,c,oBAMC,+C,iDAAsC,IAEtC,mC,gDAAyB,IACzB,mC,gDAAyB,IAEzB,mC,gDAAyB,IACzB,mC,gDAAyB,IAEzB,mC,gDAAyB,IACzB,mC,gDAAyB,IAEzB,mC,gDAAyB,IACzB,mC,gDAAyB,IAEzB,oC,gDAA0B,IAC1B,oC,gDAA0B,IAC1B,oC,gDAA0B,IAC1B,oC,gDAA0B,GAmG3B,CAjGW,SAAA3L,GACTxD,MAAMwD,YACNtD,KAAKmF,IAAI,QAAS+J,IACjBA,EAAQC,OAAOnP,KAAKoP,KAAMpP,KAAKqP,MAC/BH,EAAQI,OAAOtP,KAAKuP,KAAMvP,KAAKwP,MAC/BN,EAAQO,iBAAiBzP,KAAK0P,MAAO1P,KAAK2P,MAAO3P,KAAK4P,KAAM5P,KAAK6P,MACjEX,EAAQI,OAAOtP,KAAK8P,KAAM9P,KAAK+P,MAC/Bb,EAAQO,iBAAiBzP,KAAKgQ,MAAOhQ,KAAKiQ,MAAOjQ,KAAKoP,KAAMpP,KAAKqP,KAAK,GAExE,CAEO,QAAAa,CAASC,EAAmBC,GAClC,IAAI5E,EAAIxL,KAAKY,QACT8K,EAAI1L,KAAKa,SAEb,MAAMwP,EAAKrQ,KAAKyF,IAAI,WAAY,GAC1B6K,EAAKtQ,KAAKyF,IAAI,cAAe,GAEnC,GAA+B,YAA3BzF,KAAKyF,IAAI,eAA8B,CAC1C,IAAI8K,GAAOF,EAAK,EACZG,EAAMH,EAAK,EAKXI,EAAMF,IAFED,EAAK,EAEMC,GAAOH,EAG9B,MAAO,CAAE7O,EAAGkP,GAFFD,GAJAF,EAAK,EAIQE,GAAOJ,EAELK,GAAON,EAAW3O,EAAGkK,EAAI0E,E,CAE9C,CACJ,IAAIG,GAAOF,EAAK,EACZG,EAAMH,EAAK,EAKXI,EAAMF,IAFED,EAAK,EAEMC,GAAOJ,EAG9B,MAAO,CAAE5O,EAAGiK,EAAI2E,EAAW3O,EAAGiP,GAFpBD,GAJAF,EAAK,EAIQE,GAAOL,EAEaM,GAAOL,E,CAGpD,CAEO,QAAAM,GAEN,GAAI1Q,KAAK8H,QAAQ,aAAe9H,KAAK8H,QAAQ,gBAAkB9H,KAAK8H,QAAQ,mBAAqB9H,KAAK8H,QAAQ,gBAAkB9H,KAAK8H,QAAQ,UAAY9H,KAAK8H,QAAQ,UAAW,CAChL,MAAM0D,EAAIxL,KAAKY,QACT8K,EAAI1L,KAAKa,SACTwP,EAAKrQ,KAAKyF,IAAI,WAAY,GAC1B6K,EAAKtQ,KAAKyF,IAAI,cAAe,GACnCzF,KAAK2Q,QAAS,EAEd,IAAIC,EAAK5Q,KAAKyF,IAAI,iBAAkB,GAEL,YAA3BzF,KAAKyF,IAAI,gBACZzF,KAAKoP,MAAQiB,EAAK,EAClBrQ,KAAKqP,KAAO,EAEZrP,KAAKuP,KAAOc,EAAK,EACjBrQ,KAAKwP,KAAO,EAEZxP,KAAK4P,KAAOU,EAAK,EACjBtQ,KAAK6P,KAAOnE,EAEZ1L,KAAK8P,MAAQQ,EAAK,EAClBtQ,KAAK+P,KAAOrE,EAEZ1L,KAAK0P,MAAQ1P,KAAKuP,MAAQvP,KAAK4P,KAAO5P,KAAKuP,MAAQ,EAAIqB,EAAKlF,EAC5D1L,KAAK2P,MAAQ3P,KAAKwP,KAAO,GAAM9D,EAE/B1L,KAAKgQ,MAAQhQ,KAAKoP,MAAQpP,KAAK8P,KAAO9P,KAAKoP,MAAQ,EAAIwB,EAAKlF,EAC5D1L,KAAKiQ,MAAQjQ,KAAKqP,KAAO,GAAM3D,IAG/B1L,KAAKqP,MAAQgB,EAAK,EAClBrQ,KAAKoP,KAAO,EAEZpP,KAAKwP,KAAOa,EAAK,EACjBrQ,KAAKuP,KAAO,EAEZvP,KAAK6P,KAAOS,EAAK,EACjBtQ,KAAK4P,KAAOpE,EAEZxL,KAAK+P,MAAQO,EAAK,EAClBtQ,KAAK8P,KAAOtE,EAEZxL,KAAK2P,MAAQ3P,KAAKwP,MAAQxP,KAAK6P,KAAO7P,KAAKwP,MAAQ,EAAIoB,EAAKpF,EAC5DxL,KAAK0P,MAAQ1P,KAAKuP,KAAO,GAAM/D,EAE/BxL,KAAKiQ,MAAQjQ,KAAKqP,MAAQrP,KAAK+P,KAAO/P,KAAKqP,MAAQ,EAAIuB,EAAKpF,EAC5DxL,KAAKgQ,MAAQhQ,KAAKoP,KAAO,GAAM5D,E,CAGjC1L,MAAM4Q,UACP,EArHA,qC,gDAAkC,gBAClC,sC,gDAA0CzB,EAAA,EAAShL,WAAWC,OAAO,CAAC8K,EAAY7K,c,aC8D5E,MAAM0M,UAAqBzM,EAAlC,c,oBAOC,mC,gDAAiB,WAwCjB,oC,gDAA0DpE,KAAK8Q,eA6B/D,qC,gDAA2B,IAC3B,qC,gDAA2B,IAC3B,yC,gDAA+B,IAE/B,wC,iDAA+B,GAsbhC,CAzfW,WAAAvM,GACT,OAAO,IAAIyI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAMqO,EAAY7B,KAAKnN,KAAKC,MAAO,CAClCmN,UAAW,YAAiBpN,KAAK8E,OAAOuI,SAAS5H,IAAI,YAAa,IAAK,CAACzF,KAAK+Q,KAAM,SAAU,QAAS/Q,KAAKyF,IAAI,kBAC7G,CAACzF,KAAK8E,OAAOuI,YAElB,CAEU,WAAA7I,GACT,OAAO,IAAIwI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAMqQ,EAAA,EAAM7D,KAAKnN,KAAKC,MAAO,CAC5BmN,UAAW,YAAiBpN,KAAKuF,OAAO8H,SAAS5H,IAAI,YAAa,IAAK,CAACzF,KAAK+Q,KAAM,SAAU,QAAS/Q,KAAKyF,IAAI,kBAC7G,CAACzF,KAAKuF,OAAO8H,YAElB,CAEU,UAAA5I,GACT,OAAO,IAAIuI,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAM4M,EAAA,EAAKJ,KAAKnN,KAAKC,MAAO,CAC3BmN,UAAW,YAAiBpN,KAAK6F,MAAMwH,SAAS5H,IAAI,YAAa,IAAK,CAACzF,KAAK+Q,KAAM,SAAU,OAAQ/Q,KAAKyF,IAAI,kBAC3G,CAACzF,KAAK6F,MAAMwH,YAEjB,CAWU,UAAAyD,GACT,OAAO,IAAI9D,EAAA,EACVC,EAAA,GAAStM,IAAI,CAAC,IACd,IAAMqO,EAAY7B,KAAKnN,KAAKC,MAAO,CAClCmN,UAAW,YAAiBpN,KAAKiR,MAAM5D,SAAS5H,IAAI,YAAa,IAAK,CAACzF,KAAK+Q,KAAM,SAAU,OAAQ/Q,KAAKyF,IAAI,kBAC3G,CAACzF,KAAKiR,MAAM5D,YAEjB,CAKO,QAAA6D,CAASvM,GACf,MAAMwM,EAAOnR,KAAK6E,gBAAgBnB,SAASF,KAAKxD,KAAKiR,MAAMlM,QAI3D,OAHAoM,EAAKjM,aAAaP,GAClBA,EAASQ,IAAI,OAAQgM,GACrBnR,KAAKiR,MAAMzN,KAAK2N,GACTA,CACR,CAeU,SAAA7N,GACTxD,MAAMwD,YACN,MAAMuB,EAAkB7E,KAAK6E,gBAC7BA,EAAgBvE,OAAO,CAAEgB,YAAY,EAAMD,SAAU,WAAYT,OAAO,QAAQ,KAAMC,QAAQ,QAAQ,OACtGgE,EAAgBuM,UAAU,SAAS,KAClCpR,KAAKqR,eAAe,IAGrBxM,EAAgBuM,UAAU,UAAU,KACnCpR,KAAKqR,eAAe,IAGU,YAA3BrR,KAAKyF,IAAI,eACZzF,KAAKmF,IAAI,SAAUnF,KAAKC,MAAMqR,kBAG9BtR,KAAKmF,IAAI,SAAUnF,KAAKC,MAAMsR,eAEhC,CAEU,eAAA/I,CAAgB7D,GACzB7E,MAAM0I,gBAAgB7D,GAEtB,MAAMC,EAAQ5E,KAAK0E,UAAUC,GAE7BC,EAAMM,aAAaP,GAEnBA,EAASQ,IAAI,QAASP,GAEtB5E,KAAKkR,SAASvM,GACd,MAAMU,EAAQrF,KAAKoF,UAAUT,GAE7BU,EAAML,GAAG,KAAK,KACbhF,KAAKsI,YAAY3D,EAAS,IAG3BU,EAAML,GAAG,KAAK,KACbhF,KAAKsI,YAAY3D,EAAS,IAG3B3E,KAAK0F,SAASf,GAEdC,EAAM4I,OAAOxI,GAAG,mBAAmB,KAClCK,EAAM8F,WAAW,IAGlBvG,EAAM4I,OAAOxI,GAAG,iBAAiB,KAChC,MAAML,EAAWC,EAAMD,SACnBA,GACH3E,KAAKsI,YAAY3D,E,GAGpB,CAGO,eAAAiD,GACN5H,KAAKwR,WAAY,EACkC,GAA/CxR,KAAK0D,SAASiJ,QAAQ3M,KAAKsF,mBAC9BtF,KAAKwR,WAAY,GAGlB,IAAIC,EAAQ,EACR7K,EAAQ,EAgCZ,GA9BA,OAAY5G,KAAKqI,WAAY1D,IAC5B,MAAMsC,EAAQtC,EAASc,IAAI,SACvB,WAAewB,KAClBL,IACIK,EAAQ,EACXwK,GAAS1K,KAAKC,IAAIrC,EAASc,IAAI,eAAgBwB,GAASA,GAGpDjH,KAAKyF,IAAI,oBAAoB,IAI5Bd,EAAS8E,WAHb7C,IAOC6K,GAAS,E,IAOdzR,KAAK0R,OAAS,EAAI9K,EAAQ6K,EAC1BzR,KAAK2R,OAAS/K,EAEV5G,KAAK8H,QAAQ,gBAChB9H,KAAK4R,aAGF5R,KAAK0R,OAAS,IAAM1R,KAAKsG,cAAgBtG,KAAK6R,YAAa,CAE9D,MAAMhN,EAAkB7E,KAAK6E,gBAE7B,IAAI6G,EAEHA,EAD8B,YAA3B1L,KAAKyF,IAAI,eACRZ,EAAgB8G,cAGhB9G,EAAgB4G,aAGrBzL,KAAK8R,WAAa9R,KAAKyF,IAAI,gBAAiB,GAAKiG,EAEjD1L,KAAK0N,kBAEL,IAAIqE,EAAI,EACR,OAAY/R,KAAK6G,YAAalC,IAC7B3E,KAAK+H,kBAAkBpD,GAEvBA,EAASQ,IAAI,QAAS4M,GACtBA,IAEA,MAAMnN,EAAQD,EAASc,IAAI,SACrBE,EAAOhB,EAASc,IAAI,QACpBJ,EAAQV,EAASc,IAAI,SACrB0L,EAAOxM,EAASc,IAAI,QACpBsI,EAAQpJ,EAASc,IAAI,QACrBwI,EAActJ,EAASc,IAAI,eAEjCb,EAAMoJ,YAAY,OAAQD,GAC1BnJ,EAAMoJ,YAAY,SAAUD,GAC5BnJ,EAAMoJ,YAAY,cAAeC,GACjCkD,EAAKnD,YAAY,OAAQD,GACzBoD,EAAKnD,YAAY,SAAUD,GAE3B,MAAM9G,EAAQtC,EAASc,IAAI,SACvB,WAAewB,KACL,GAATA,GAAcjH,KAAKyF,IAAI,qBAC1Bb,EAAMuJ,WAAW,WAAW,GAC5BxI,EAAKwI,WAAW,WAAW,GAC3B9I,EAAM8I,WAAW,WAAW,KAG5BvJ,EAAMuJ,WAAW,WAAW,GAC5BxI,EAAKwI,WAAW,WAAW,GAC3B9I,EAAM8I,WAAW,WAAW,GAE5BnO,KAAKgS,cAAcrN,GAEf3E,KAAKiS,OAAOtN,GACfwM,EAAKhD,WAAW,WAAW,GAElBxJ,EAAS8E,YAClB0H,EAAKhD,WAAW,WAAW,I,IAMhCrO,MAAM8H,iBACP,CAEU,UAAAgK,GAET,MAAMjP,EAAc3C,KAAKyF,IAAI,eACvBH,EAAkBtF,KAAKsF,gBACvBmI,EAAiBzN,KAAKuF,OAAO8H,SAE/BrN,KAAKyF,IAAI,gBACZH,EAAgBH,IAAI,WAAY,YAChCG,EAAgBhF,OAAO,CAAEgB,YAAY,IAClB,YAAfqB,GACH3C,KAAKmF,IAAI,SAAUnF,KAAKC,MAAMqR,kBAC9B7D,EAAenN,OAAO,CAAE0C,QAAS,KAAMzB,EAAG,SAG1CvB,KAAKmF,IAAI,SAAUnF,KAAKC,MAAMsR,gBAC9B9D,EAAenN,OAAO,CAAE0C,QAAS,EAAGzB,EAAG,OAIxC+D,EAAgBhF,OAAO,CAAEgB,YAAY,EAAOD,SAAU,aACnC,YAAfsB,GACH2C,EAAgBhF,OAAO,CAAEiB,EAAG,OAC5BkM,EAAenN,OAAO,CAAE0C,QAAS,KAAKzB,EAAG,MAGzC+D,EAAgBhF,OAAO,CAAEkB,EAAG,OAC5BiM,EAAenN,OAAO,CAAE0C,QAAS,KAAKxB,EAAG,MAG3CxB,KAAKqR,eACN,CAEU,YAAAa,CAAavN,GACtB,IAAI+H,EAAQ/H,EAASc,IAAI,SACrB0M,EAAYxN,EAASc,IAAI,eAAgB,GAC7C,GAAIiH,EAAQ1M,KAAKqI,UAAUrE,OAAS,EAAG,CACtC,IAAIoO,EAAWpS,KAAKqI,UAAUqE,EAAQ,GAGtC,GAFAyF,EAAYC,EAAS3M,IAAI,eAAgB,GAErC2M,EAAS3I,YAAwC,GAAzB2I,EAAS3M,IAAI,UAAiBzF,KAAKyF,IAAI,oBAClE,OAAOzF,KAAKkS,aAAaE,E,CAG3B,OAAOD,CACR,CAEU,MAAAF,CAAOtN,GAChB,IAAI+H,EAAQ/H,EAASc,IAAI,SACzB,GAAIiH,GAAS1M,KAAKqI,UAAUrE,OAAS,EACpC,OAAO,EAGP,IAAK,IAAI+N,EAAIrF,EAAQ,EAAGqF,EAAI/R,KAAKqI,UAAUrE,OAAQ+N,IAClD,IAAK/R,KAAKqI,UAAU0J,GAAGtI,WACtB,OAAO,EAIV,OAAO,CACR,CAEU,aAAAuI,CAAcrN,GACvB,MAAMhC,EAAc3C,KAAKyF,IAAI,eAEvBb,EAAQD,EAASc,IAAI,SACrBJ,EAAQV,EAASc,IAAI,SACrB0L,EAAOxM,EAASc,IAAI,QAEpBZ,EAAkB7E,KAAK6E,gBAE7B,IAAImG,EAAWnG,EAAgB4G,aAC3Bd,EAAY9F,EAAgB8G,cAE5B0G,EAAUrH,EACK,cAAfrI,IACH0P,EAAU1H,GAGX,MAAMwH,EAAYnS,KAAKkS,aAAavN,GAC9BsC,EAAQtC,EAASc,IAAI,QAAS,GAC9B6M,EAAevL,KAAKC,IAAIrC,EAASc,IAAI,eAAgBwB,IACrDsL,EAAcvS,KAAKyF,IAAI,cAAe,GACtCgB,EAAYzG,KAAK6N,WAAW,YAAa,GAG/C,IAAI2E,EAAI,EACK,GAATvL,EACHuL,EAAIF,EAAevL,KAAKC,IAAIC,GAGxBtC,EAAS8E,aACZ+I,EAAI,MAIFxS,KAAK8R,YAAcnL,MACtB3G,KAAK8R,WAAa,GAGnB,IAAIW,EAAWH,EAAe7L,EAAY4L,EACtCK,GAAeJ,GAAgBA,EAAeH,GAAaI,GAAe9L,EAAY4L,EAE1FzN,EAAMtE,OAAO,CAAEmS,WAAUC,cAAa/P,gBACtCwO,EAAK7Q,OAAO,CAAEmS,SAAUC,EAAaA,aAAcJ,GAAgBA,EAAeH,IAAc1L,EAAY4L,EAAS1P,gBAErH,MAAMF,EAAgBzC,KAAKyF,IAAI,gBAAiB,GAC1C/C,EAAc1C,KAAKyF,IAAI,cAAe,GAE5C,GAAmB,YAAf9C,EAA2B,CAE9B,IAAIgQ,EAAaxB,EAAKtQ,SAAW2R,EAEjC7H,EAAYA,GAAajI,EAAcD,GAAiBkQ,EAExD/N,EAAMO,IAAI,IAAKnF,KAAK8R,YAEpB,IAAIjR,EAASkG,KAAKmF,IAAI,IAAQnF,KAAKoF,IAAI,EAAGxB,EAAY3K,KAAK2R,OAASa,EAAIxS,KAAK0R,OAASiB,IAEtF/N,EAAMtE,OAAO,CAAEO,SAAQU,EAAGyJ,EAAW,IACrC,IAAI4H,EAAS5S,KAAK8R,WAAajR,EAAS,EACxCwE,EAAMF,IAAI,IAAKyN,GAEf5S,KAAK8R,YAAcjR,EAAS8R,EAC5BxB,EAAK7Q,OAAO,CAAEkB,EAAGxB,KAAK8R,WAAaa,EAAYpR,EAAGyJ,EAAW,G,KAEzD,CACJ,IAAI2H,EAAaxB,EAAKvQ,QAAU4R,EAEhCxH,EAAWA,GAAYtI,EAAcD,GAAiBkQ,EAEtD/N,EAAMO,IAAI,IAAKnF,KAAK8R,YAEpB,IAAIlR,EAAQmG,KAAKmF,IAAI,IAAQnF,KAAKoF,IAAI,EAAGnB,EAAWhL,KAAK2R,OAASa,EAAIxS,KAAK0R,OAASiB,IAEpF/N,EAAMtE,OAAO,CAAEM,QAAOY,EAAGmJ,EAAY,IACrC,MAAMkI,EAAS7S,KAAK8R,WAAalR,EAAQ,EACzCyE,EAAMF,IAAI,IAAK0N,GAEf7S,KAAK8R,YAAclR,EAAQ+R,EAC3BxB,EAAK7Q,OAAO,CAAEiB,EAAGvB,KAAK8R,WAAaa,EAAYnR,EAAGmJ,EAAY,G,CAEhE,CASa,YAAAzB,CAAavE,EAA+C2C,G,uHAExE,OADA3C,EAASc,IAAI,QAAQkC,KAAKL,GACnB,EAAM4B,aAAY,UAACvE,EAAU2C,EACrC,G,CASa,YAAAoB,CAAa/D,EAA+C2C,G,uHAExE,OADA3C,EAASc,IAAI,QAAQ4B,KAAKC,GACnB,EAAMoB,aAAY,UAAC/D,EAAU2C,EACrC,G,CAEU,WAAAgB,CAAY3D,GACrB,GAAI3E,KAAKyF,IAAI,eAAgB,CAC5B,MAAME,EAAOhB,EAASc,IAAI,QACpBJ,EAAQV,EAASc,IAAI,SACrBb,EAAQD,EAASc,IAAI,SAE3B,GAAIE,GAAQf,GAASS,EAAO,CAE3B,MAAMC,EAAkBtF,KAAKsF,gBACvBT,EAAkB7E,KAAK6E,gBAC7B,IAAIiO,EAAenN,EAAKF,IAAI,WAAY,IAExC,MAAMsN,EAAMzN,EAAgB1E,QACtBoS,EAAM1N,EAAgBzE,SAEtB4N,EAAKnJ,EAAgBG,IAAI,cAAe,GACxCiJ,EAAKpJ,EAAgBG,IAAI,eAAgB,GACzCwN,EAAK3N,EAAgBG,IAAI,aAAc,GACvCyN,EAAK5N,EAAgBG,IAAI,gBAAiB,GAEhD,IAAI0N,EAAK,CAAE5R,EAAG,EAAGC,EAAG,GAChB4R,EAAK,CAAE7R,EAAG,EAAGC,EAAG,GAChB6R,EAAK,CAAE9R,EAAG,EAAGC,EAAG,GAEhBxB,KAAKwR,YACRsB,EAAe,EAAIA,GAGW,YAA3B9S,KAAKyF,IAAI,gBACZ0N,EAAKvO,EAAMsL,SAAS4C,EAAc,IAClCK,EAAG5R,GAAKqD,EAAMrD,IAAMsD,EAAgBtD,IACpC4R,EAAG3R,GAAKoD,EAAMpD,IAAMqD,EAAgBrD,IAEhCxB,KAAKwR,WACR4B,EAAG7R,EAAIwR,EACPK,EAAG5R,EAAI6D,EAAM7D,IAEb6R,EAAG9R,EAAIwR,EAAMtE,EACb4E,EAAG7R,EAAI4R,EAAG5R,IAGV4R,EAAG7R,EAAIsD,EAAgBtD,IAAMsD,EAAgBjE,QAC7CwS,EAAG5R,EAAI6D,EAAM7D,IAEb6R,EAAG9R,EAAI6R,EAAG7R,EAAIwR,EAAM1N,EAAMzE,QAAU8N,EACpC2E,EAAG7R,EAAI4R,EAAG5R,KAIX2R,EAAKvO,EAAMsL,SAAS,GAAK4C,GACzBK,EAAG5R,GAAKqD,EAAMrD,IAAMsD,EAAgBtD,IACpC4R,EAAG3R,GAAKoD,EAAMpD,IAAMqD,EAAgBrD,IAEhCxB,KAAKwR,WACR4B,EAAG5R,EAAIwR,EACPI,EAAG7R,EAAI8D,EAAM9D,IAEb8R,EAAG7R,EAAIwR,EAAMC,EACbI,EAAG9R,EAAI6R,EAAG7R,IAGV6R,EAAG5R,EAAIqD,EAAgBrD,IAAMqD,EAAgBhE,SAC7CuS,EAAG7R,EAAI8D,EAAM9D,IAEb8R,EAAG7R,EAAI4R,EAAG5R,EAAIwR,EAAM3N,EAAMxE,SAAWqS,EACrCG,EAAG9R,EAAI6R,EAAG7R,IAIZoE,EAAKR,IAAI,SAAU,CAACgO,EAAIC,EAAIC,G,EAG/B,CAKO,eAAAhK,CAAgB1E,GACtB7E,MAAMuJ,gBAAgB1E,GACtB,IAAIwM,EAAOxM,EAASc,IAAI,QACpB0L,IACHnR,KAAKiR,MAAM3H,YAAY6H,GACvBA,EAAK5H,UAEP,CAEO,eAAAqF,CAAgBC,GAEtB,MAAMC,EAASD,EAAOpJ,IAAI,UAC1B,GAAIqJ,EAAQ,CACX,MACMlK,EADWkK,EAAOnK,SACDc,IAAI,SAE3B,GAAIb,EAAO,CACV,MAAMhE,EAAQgE,EAAMhE,QACdC,EAAS+D,EAAM/D,SACfsP,EAAYtB,EAAOpJ,IAAI,YAAa,IACpC2K,EAAYvB,EAAOpJ,IAAI,YAAa,IAE1C,IAAI8G,EAAK,EACLD,EAAK,EACsB,cAA3BtM,KAAKyF,IAAI,eACZ6G,EAAKzL,EAAS,EAGd0L,EAAK3L,EAAQ,EAGdkO,EAAOxO,OAAO,CAAEiB,EAAGqD,EAAMrD,IAAMX,EAAQuP,EAAY5D,EAAI/K,EAAGoD,EAAMpD,IAAM8K,EAAKzL,EAASuP,G,EAGvF,EAhcA,qC,gDAAkC,iBAClC,sC,gDAA0ChM,EAAcH,WAAWC,OAAO,CAAC2M,EAAa1M,cCvHlF,MAAMmP,UAAsBzC,EAAnC,c,oBACC,mC,gDAAiB,YASjB,wC,wDA8HD,CA5HQ,gBAAA3K,GACNpG,MAAMoG,mBACNlG,KAAKuT,eAAYC,CAClB,CAEU,aAAAxB,CAAcrN,GACvB,MAAMhC,EAAc3C,KAAKyF,IAAI,eACvBZ,EAAkB7E,KAAK6E,gBAEvBD,EAAQD,EAASc,IAAI,SACrBJ,EAAQV,EAASc,IAAI,SACrB0L,EAAOxM,EAASc,IAAI,QACpBtC,EAAUnD,KAAKyF,IAAI,UAAW,QAE9Bc,EAAMvG,KAAK6N,WAAW,mBAAoB,GAEhD,GAAW,GAAPtH,EACH,OAGD,MAAM9D,EAAgBzC,KAAKyF,IAAI,gBAAiB,GAC1C/C,EAAc1C,KAAKyF,IAAI,cAAe,GAEtC4K,EAAKrQ,KAAKyF,IAAI,WAAY,GAC1B6K,EAAKtQ,KAAKyF,IAAI,cAAe,MAE7B6M,EAAevL,KAAKC,IAAIrC,EAASc,IAAI,eAAgB,IACrDwB,EAAQtC,EAASc,IAAI,QAAS,GAEpC,IAAIgO,EACAC,EAEAC,EAAgB9O,EAAgB8G,cAChCiI,EAAe/O,EAAgB4G,aAC/BoI,EAAY1C,EAAKvQ,QACjB+R,EAAaxB,EAAKtQ,SAEH,cAAf8B,KACFiR,EAAcD,GAAiB,CAACA,EAAeC,IAC/CC,EAAWlB,GAAc,CAACA,EAAYkB,IAGxC,MAAMC,EAASF,EAAe,EAE9B,IAAIpB,EAAI,EACK,GAATvL,EACHuL,EAAIF,EAAevL,KAAKC,IAAIC,GAGxBtC,EAAS8E,aACZ+I,EAAI,MAING,GAAcH,EAEdmB,EAAgBA,GAAiBjR,EAAcD,GAAiBkQ,GAAc3S,KAAK2R,OAAS3R,KAAK0R,OAAS,GAE1G,IAAIe,EAAW,kBAAuBpC,EAAIuD,GAErC,WAAe5T,KAAKuT,aACxBvT,KAAKuT,UAAYd,GAGlB,IAAIC,EAAc,kBAAuBpC,EAAIsD,GACzCG,EAAgB/T,KAAKuT,UAErBxE,EAAQhI,KAAKiN,MAAML,EAAelB,EAAWC,GAC7CuB,EAAIlN,KAAKmN,IAAInN,KAAKoN,GAAK,EAAIpF,GAK/B,GAJS,GAALkF,IACHA,EAAI,MAGU,QAAX9Q,EAAmB,CACtB,IACIiR,GADe3B,EAAWC,GAAe,EAAIiB,EACtBrB,EAAe/L,EAEtC8N,EAAItN,KAAKC,IAAI+M,EAAgBA,EAAgB,EAAIK,EAASH,GAE9DR,GAAeM,EAAgBhN,KAAKuN,KAAKD,IAAMJ,EAG9CP,EADGD,EAAc,GACG,EAAIW,EAASX,EAAcM,GAAiBN,EAG7CM,C,MAIpBN,EAAcE,EAAgBrB,EAAe/L,EAC7CmN,EAAmBK,EAAgBN,EAAcQ,EAGlD,IAAIM,EAAavU,KAAK8R,WAAa2B,EAAc,EAC7Ce,EAASV,EACTW,EAASzU,KAAK8R,WAEd4C,EAAQZ,EACRa,EAAQF,EAAShB,EAEF,YAAf9Q,GACH0C,EAAMF,IAAI,IAAKoP,GACXlP,EAAMI,IAAI,WAAa,GAC1BzF,KAAKoG,SAAS5C,KAAK,CAAE6B,MAAOA,EAAO7D,EAAG+S,IAEvC3P,EAAMO,IAAI,SAAUsO,KAGpBpO,EAAMF,IAAI,IAAKoP,GACXlP,EAAMI,IAAI,WAAa,GAC1BzF,KAAKqG,SAAS7C,KAAK,CAAE6B,MAAOA,EAAO7D,EAAG+S,KAEtCC,EAAQC,GAAU,CAACA,EAAQD,IAC3BE,EAAOC,GAAS,CAACA,EAAOD,GAEzB9P,EAAMO,IAAI,QAASsO,IAGpB7O,EAAMtE,OAAO,CAAEqC,cAAa+P,YAAagB,EAAkBjB,SAAUsB,EAAexS,EAAGiT,EAAQhT,EAAGiT,IAClGtD,EAAK7Q,OAAO,CAAEqC,cAAapB,EAAGmT,EAAOlT,EAAGmT,EAAOlC,SAAUiB,EAAkBhB,YAAagB,IAExF1T,KAAKuT,UAAYG,EACjB1T,KAAK8R,YAAc2B,EAAcd,CAClC,EApIA,qC,gDAAkC,kBAClC,sC,gDAA0C9B,EAAa5M,WAAWC,OAAO,CAACoP,EAAcnP,cC5BlF,MAAMyQ,UAA+BtB,EAA5C,c,oBACC,mC,gDAAiB,cAcjB,yC,gDAAuCrE,EAAA,EAAStO,IAAIX,KAAKC,MAAO,CAAEoB,SAAU,WAAYE,EAAG,KAAKC,EAAG,KAAKwB,QAAS,KAAKD,QAAS,SAE/H,6C,gDAA2C/C,KAAK6E,gBAAgBnB,SAASF,KAAKyL,EAAA,EAAStO,IAAIX,KAAKC,MAAO,CAAEmN,UAAW,CAAC,YAAa,cAAe/L,SAAU,WAAYE,EAAG,KAAKC,EAAG,KAAKwB,QAAS,KAAKD,QAAS,SAoD/M,CAlDW,SAAAO,GACTxD,MAAMwD,YACNtD,KAAKmF,IAAI,WAAY,MACrBnF,KAAKmF,IAAI,cAAe,MACxBnF,KAAKmF,IAAI,UAAW,UACpBnF,KAAK6E,gBAAgBM,IAAI,OAAQnF,KAAK6U,WACvC,CAEU,YAAAC,GACT,IAAIjQ,EAAkB7E,KAAK6E,gBAEvB2G,EAAI3G,EAAgB4G,aACpBC,EAAI7G,EAAgB8G,cAEpBkJ,EAAa7U,KAAK6U,WAClBE,EAAiB/U,KAAK+U,eACtB/S,EAAQ6S,EAAWpP,IAAI,QAAS,GAEpC,MAAM4E,EAASwK,EAAWG,cAE1B,IAAIC,EAAK5K,EAAOU,MAAQV,EAAOS,KAC3BoK,EAAK7K,EAAOI,OAASJ,EAAOG,IAE/BxI,EAD8B,cAA3BhC,KAAKyF,IAAI,eACJ+F,EAAIyJ,EAGJvJ,EAAIwJ,EAETlT,GAAS2E,KAAqBwO,KAATnT,IACxB6S,EAAW1P,IAAI,QAASnD,GACxB6S,EAAW1P,IAAI,IAAKqG,EAAI,GACxBqJ,EAAW1P,IAAI,IAAKuG,EAAI,GAExBqJ,EAAe5P,IAAI,QAASnD,GAC5B+S,EAAe5P,IAAI,IAAKqG,EAAI,GAC5BuJ,EAAe5P,IAAI,IAAKuG,EAAI,GAE9B,CAEO,gBAAAxF,GAEN,GADApG,MAAMoG,mBACFlG,KAAK8H,QAAQ,WAAY,CAC5B,MAAMsN,EAAUpV,KAAKyF,IAAI,WACzBzF,KAAK6U,WAAW1P,IAAI,UAAWiQ,GAC/BpV,KAAK+U,eAAe5P,IAAI,UAAWiQ,E,CAGpCpV,KAAK8U,cACN,EAhEA,qC,gDAAkC,2BAClC,sC,gDAA0CxB,EAAcrP,WAAWC,OAAO,CAAC0Q,EAAuBzQ,cCjB5F,MAAMkR,UAAoBjS,EACtB,SAAAE,GACTxD,MAAMwD,YAENtD,KAAK2D,gBAAgBrD,OAAO,CAAEgB,YAAW,EAAMgU,OAAQtV,KAAKC,MAAMqR,kBACnE,EAEA,qC,gDAAkC,gBAClC,sC,gDAA0ClO,EAAaa,WAAWC,OAAO,CAACmR,EAAYlR,a,uECvBhF,MAAMoR,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/percent/PercentDefaultTheme.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/percent/PercentChart.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/percent/PercentSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/pie/PieChart.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/pie/PieSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/funnel/FunnelSlice.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/funnel/FunnelSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/funnel/PyramidSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/funnel/PictorialStackedSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/funnel/SlicedChart.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/percent.js"], "sourcesContent": ["import { Theme } from \"../../core/Theme\";\r\nimport { percent, p50, p100 } from \"../../core/util/Percent\";\r\nimport { ColorSet } from \"../../core/util/ColorSet\";\r\nimport { setColor } from \"../../themes/DefaultTheme\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class PercentDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\r\n\t\tconst ic = this._root.interfaceColors;\r\n\t\tconst r = this.rule.bind(this);\r\n\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/percent\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"PercentSeries\").setAll({\r\n\t\t\tlegendLabelText: \"{category}\",\r\n\t\t\tlegendValueText: \"{valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tcolors: ColorSet.new(this._root, {}),\r\n\t\t\twidth: p100,\r\n\t\t\theight: p100\r\n\t\t});\r\n\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/pie\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"PieChart\").setAll({\r\n\t\t\tradius: percent(80),\r\n\t\t\tstartAngle: -90,\r\n\t\t\tendAngle: 270\r\n\t\t})\r\n\r\n\t\tr(\"PieSeries\").setAll({\r\n\t\t\talignLabels: true,\r\n\t\t\tstartAngle: -90,\r\n\t\t\tendAngle: 270\r\n\t\t});\r\n\r\n\t\tr(\"PieSeries\").states.create(\"hidden\", { endAngle: -90, opacity: 0 });\r\n\r\n\t\tr(\"Slice\", [\"pie\"]).setAll({\r\n\t\t\tposition: \"absolute\",\r\n\t\t\tisMeasured: false,\r\n\t\t\tx: 0,\r\n\t\t\ty: 0,\r\n\t\t\ttoggleKey: \"active\",\r\n\t\t\ttooltipText: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tstrokeWidth: 1,\r\n\t\t\tstrokeOpacity: 1,\r\n\t\t\trole: \"figure\",\r\n\t\t\tlineJoin:\"round\"\r\n\t\t});\r\n\r\n\t\tr(\"Slice\", [\"pie\"]).states.create(\"active\", { shiftRadius: 20, scale: 1 });\r\n\t\tr(\"Slice\", [\"pie\"]).states.create(\"hoverActive\", { scale: 1.04 });\r\n\t\tr(\"Slice\", [\"pie\"]).states.create(\"hover\", { scale: 1.04 });\r\n\r\n\t\tr(\"RadialLabel\", [\"pie\"]).setAll({\r\n\t\t\ttextType: \"aligned\",\r\n\t\t\tradius: 10,\r\n\t\t\ttext: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tpaddingTop: 5,\r\n\t\t\tpaddingBottom: 5,\r\n\t\t\tpopulateText: true\r\n\t\t});\r\n\r\n\t\tr(\"Tick\", [\"pie\"]).setAll({\r\n\t\t\tlocation: 1\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/funnel\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"SlicedChart\").setAll({\r\n\t\t\tpaddingLeft: 10,\r\n\t\t\tpaddingRight: 10,\r\n\t\t\tpaddingTop: 10,\r\n\t\t\tpaddingBottom: 10\r\n\t\t});\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/funnel: Funnel\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"FunnelSeries\").setAll({\r\n\t\t\tstartLocation: 0,\r\n\t\t\tendLocation: 1,\r\n\t\t\torientation: \"vertical\",\r\n\t\t\talignLabels: true,\r\n\t\t\tsequencedInterpolation: true\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\").setAll({\r\n\t\t\tinteractive: true,\r\n\t\t\texpandDistance: 0,\r\n\t\t\t//tooltipText: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\"\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\").states.create(\"hover\", { expandDistance: 0.15 })\r\n\r\n\t\tr(\"Label\", [\"funnel\"]).setAll({\r\n\t\t\tpopulateText: true,\r\n\t\t\ttext: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tcenterY: p50\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"funnel\", \"horizontal\"]).setAll({\r\n\t\t\tcenterX: 0,\r\n\t\t\tcenterY: p50,\r\n\t\t\trotation: -90\r\n\t\t});\r\n\r\n\t\t// Class: Label\r\n\t\tr(\"Label\", [\"funnel\", \"vertical\"]).setAll({\r\n\t\t\tcenterY: p50,\r\n\t\t\tcenterX: 0\r\n\t\t});\r\n\r\n\t\tr(\"Tick\", [\"funnel\"]).setAll({\r\n\t\t\tlocation: 1\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"funnel\", \"link\"]).setAll({\r\n\t\t\tfillOpacity: 0.5,\r\n\t\t\tstrokeOpacity: 0,\r\n\t\t\texpandDistance: -0.1\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"funnel\", \"link\", \"vertical\"]).setAll({\r\n\t\t\theight: 10,\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"funnel\", \"link\", \"horizontal\"]).setAll({\r\n\t\t\twidth: 10\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/funnel: Pyramid\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"PyramidSeries\").setAll({\r\n\t\t\tvalueIs: \"area\"\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pyramid\", \"link\"]).setAll({\r\n\t\t\tfillOpacity: 0.5\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pyramid\", \"link\", \"vertical\"]).setAll({\r\n\t\t\theight: 0\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pyramid\", \"link\", \"horizontal\"]).setAll({\r\n\t\t\twidth: 0\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pyramid\"]).setAll({\r\n\t\t\tinteractive: true,\r\n\t\t\texpandDistance: 0\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pyramid\"]).states.create(\"hover\", { expandDistance: 0.15 });\r\n\r\n\t\tr(\"Label\", [\"pyramid\"]).setAll({\r\n\t\t\tpopulateText: true,\r\n\t\t\ttext: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tcenterY: p50\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"pyramid\", \"horizontal\"]).setAll({\r\n\t\t\tcenterX: 0,\r\n\t\t\tcenterY: p50,\r\n\t\t\trotation: -90\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"pyramid\", \"vertical\"]).setAll({\r\n\t\t\tcenterY: p50,\r\n\t\t\tcenterX: 0\r\n\t\t});\r\n\r\n\t\tr(\"Tick\", [\"pyramid\"]).setAll({\r\n\t\t\tlocation: 1\r\n\t\t});\r\n\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/funnel: Pictorial\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\t// Class: FunnelSlice\r\n\t\tr(\"FunnelSlice\", [\"pictorial\"]).setAll({\r\n\t\t\tinteractive: true,\r\n\t\t\ttooltipText: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\"\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"pictorial\"]).setAll({\r\n\t\t\tpopulateText: true,\r\n\t\t\ttext: \"{category}: {valuePercentTotal.formatNumber('0.00p')}\",\r\n\t\t\tcenterY: p50\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"pictorial\", \"horizontal\"]).setAll({\r\n\t\t\tcenterX: 0,\r\n\t\t\tcenterY: p50,\r\n\t\t\trotation: -90\r\n\t\t});\r\n\r\n\t\tr(\"Label\", [\"pictorial\", \"vertical\"]).setAll({\r\n\t\t\tcenterY: p50,\r\n\t\t\tcenterX: 0\r\n\t\t});\r\n\r\n\t\tr(\"FunnelSlice\", [\"pictorial\", \"link\"]).setAll({\r\n\t\t\tfillOpacity: 0.5,\r\n\t\t\twidth: 0,\r\n\t\t\theight: 0\r\n\t\t});\r\n\r\n\t\tr(\"Tick\", [\"pictorial\"]).setAll({\r\n\t\t\tlocation: 0.5\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"Graphics\", [\"pictorial\", \"background\"]);\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tfillOpacity: 0.2\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"alternativeBackground\");\r\n\t\t}\r\n\r\n\t}\r\n}\r\n", "import type { PercentSeries } from \"./PercentSeries\";\nimport { SerialChart, ISerialChartPrivate, ISerialChartSettings } from \"../../core/render/SerialChart\";\nimport { PercentDefaultTheme } from \"./PercentDefaultTheme\";\n\nexport interface IPercentChartSettings extends ISerialChartSettings {\n}\n\nexport interface IPercentChartPrivate extends ISerialChartPrivate {\n}\n\n/**\n * Base class for [[PieChart]].\n *\n * Also used for percent-based series, like [[FunnelSeries]], [[PyramidSeries]], etc.\n *\n * @important\n */\nexport abstract class PercentChart extends SerialChart {\n\tpublic static className: string = \"PercentChart\";\n\tpublic static classNames: Array<string> = SerialChart.classNames.concat([PercentChart.className]);\n\n\tdeclare public _settings: IPercentChartSettings;\n\tdeclare public _privateSettings: IPercentChartPrivate;\n\tdeclare public _seriesType: PercentSeries;\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(PercentDefaultTheme.new(this._root));\n\n\t\tsuper._afterNew();\n\n\t\tthis.chartContainer.children.push(this.seriesContainer);\n\t\tthis.seriesContainer.children.push(this.bulletsContainer);\n\t}\n\n\tprotected _processSeries(series: this[\"_seriesType\"]) {\n\t\tsuper._processSeries(series);\n\t\tthis.seriesContainer.children.moveValue(this.bulletsContainer, this.seriesContainer.children.length - 1);\t\n\t}\t\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Graphics } from \"../../core/render/Graphics\";\nimport type { Label } from \"../../core/render/Label\";\nimport type { Tick } from \"../../core/render/Tick\";\nimport type { ListTemplate } from \"../../core/util/List\";\nimport type { ColorSet } from \"../../core/util/ColorSet\";\nimport type { ILegendDataItem } from \"../../core/render/Legend\";\nimport type { Color } from \"../../core/util/Color\";\nimport type { PercentChart } from \"./PercentChart\";\nimport type { PatternSet } from \"../../core/util/PatternSet\";\nimport type { Pattern } from \"../../core/render/patterns/Pattern\";\n\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate } from \"../../core/render/Series\";\nimport { Container } from \"../../core/render/Container\";\nimport { visualSettings } from \"../../core/render/Graphics\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\n\nexport interface IPercentSeriesDataItem extends ISeriesDataItem {\n\n\t/**\n\t * Percent of the series value total.\n\t */\n\tvaluePercentTotal: number;\n\n\t/**\n\t * Category.\n\t */\n\tcategory: string;\n\n\t/**\n\t * Slice visual element.\n\t */\n\tslice: Graphics;\n\n\t/**\n\t * Slice label.\n\t */\n\tlabel: Label;\n\n\t/**\n\t * Slice tick.\n\t */\n\ttick: Tick;\n\n\t/**\n\t * A related legend data item.\n\t */\n\tlegendDataItem: DataItem<ILegendDataItem>;\n\n\t/**\n\t * Fill color used for the slice and related elements, e.g. legend marker.\n\t */\n\tfill: Color;\n\n\t/**\n\t * Pattern used for the slice and related elements, e.g. legend marker.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/} for more info\n\t * @since 5.10.0\n\t */\n\tfillPattern: Pattern;\n\n}\n\n//type IPercentSeriesDataItemSettings = { [K in keyof IPercentSeriesDataItem]?: string; };\n\nexport interface IPercentSeriesSettings extends ISeriesSettings {\n\n\t/**\n\t * A [[ColorSet]] to use when asigning colors for slices.\n\t */\n\tcolors?: ColorSet;\n\n\t/**\n\t * A [[PatternSet]] to use when asigning patterns for slices.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/colors-gradients-and-patterns/patterns/#Pattern_sets} for more info\n\t * @since 5.10.0\n\t */\n\tpatterns?: PatternSet;\n\n\t/**\n\t * A field in data that holds category names.\n\t */\n\tcategoryField?: string;\n\n\t/**\n\t * Should slice labels be aligned in columns/rows?\n\t */\n\talignLabels?: boolean;\n\n\t/**\n\t * A field that holds color for slice fill.\n\t */\n\tfillField?: string;\n\n}\n\nexport interface IPercentSeriesPrivate extends ISeriesPrivate {\n\n\t/**\n\t * Calculate average value in series.\n\t */\n\tvalueAverage?: number;\n\n\t/**\n\t * Count of items in series.\n\t */\n\tvalueCount?: number;\n\n\t/**\n\t * Sum of values in series.\n\t */\n\tvalueSum?: number;\n\n\t/**\n\t * Sum of all absolute values in series.\n\t */\n\tvalueAbsoluteSum?: number;\n\n\t/**\n\t * Lowest value in series.\n\t */\n\tvalueLow?: number;\n\n\t/**\n\t * Highest value in series.\n\t */\n\tvalueHigh?: number;\n\n}\n\n/**\n * A base class for any percent chart series.\n */\nexport abstract class PercentSeries extends Series {\n\tpublic static className: string = \"PercentSeries\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([PercentSeries.className]);\n\n\tdeclare public _settings: IPercentSeriesSettings;\n\tdeclare public _privateSettings: IPercentSeriesPrivate;\n\tdeclare public _dataItemSettings: IPercentSeriesDataItem;\n\n\tdeclare public _sliceType: Graphics;\n\tdeclare public _labelType: Label;\n\tdeclare public _tickType: Tick;\n\n\tpublic readonly slicesContainer = this.children.push(Container.new(this._root, { position: \"absolute\", isMeasured: false }));\n\tpublic readonly labelsContainer = this.children.push(Container.new(this._root, { position: \"absolute\", isMeasured: false }));\n\tpublic readonly ticksContainer = this.children.push(Container.new(this._root, { position: \"absolute\", isMeasured: false }));\n\n\tprotected _lLabels: Array<{ label: Label, y: number }> = [];\n\tprotected _rLabels: Array<{ label: Label, y: number }> = [];\n\tprotected _hLabels: Array<{ label: Label, y: number }> = [];\n\n\t/**\n\t * A [[ListTemplate]] of all slices in series.\n\t *\n\t * `slices.template` can also be used to configure slices.\n\t */\n\tpublic readonly slices: ListTemplate<this[\"_sliceType\"]> = this._makeSlices();\n\n\tprotected abstract _makeSlices(): ListTemplate<this[\"_sliceType\"]>;\n\n\tpublic abstract chart: PercentChart | undefined;\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeSlice(dataItem: DataItem<this[\"_dataItemSettings\"]>): this[\"_sliceType\"] {\n\t\tconst slice = this.slicesContainer.children.push(this.slices.make());\n\n\t\tslice.on(\"fill\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\n\n\t\tslice.on(\"fillPattern\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\t\t\n\n\t\tslice.on(\"stroke\", () => {\n\t\t\tthis.updateLegendMarker(dataItem);\n\t\t})\n\n\t\tslice._setDataItem(dataItem);\n\t\tdataItem.set(\"slice\", slice);\n\t\tthis.slices.push(slice);\n\n\t\treturn slice;\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all slice labels in series.\n\t *\n\t * `labels.template` can also be used to configure slice labels.\n\t */\n\tpublic readonly labels: ListTemplate<this[\"_labelType\"]> = this._makeLabels();\n\n\tprotected abstract _makeLabels(): ListTemplate<this[\"_labelType\"]>;\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLabel(dataItem: DataItem<this[\"_dataItemSettings\"]>): this[\"_labelType\"] {\n\t\tconst label = this.labelsContainer.children.push(this.labels.make());\n\t\tlabel._setDataItem(dataItem);\n\t\tdataItem.set(\"label\", label);\n\t\tthis.labels.push(label);\n\t\treturn label;\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all slice ticks in series.\n\t *\n\t * `ticks.template` can also be used to configure slice ticks.\n\t */\n\tpublic readonly ticks: ListTemplate<this[\"_tickType\"]> = this._makeTicks();\n\n\tprotected abstract _makeTicks(): ListTemplate<this[\"_tickType\"]>;\n\n\n\tprotected _shouldMakeBullet(dataItem: DataItem<this[\"_dataItemSettings\"]>): boolean {\n\t\tif (dataItem.get(\"value\") != null) {\n\t\t\treturn true;\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeTick(dataItem: DataItem<this[\"_dataItemSettings\"]>): this[\"_tickType\"] {\n\t\tconst tick = this.ticksContainer.children.push(this.ticks.make());\n\t\ttick._setDataItem(dataItem);\n\t\tdataItem.set(\"tick\", tick);\n\t\tthis.ticks.push(tick);\n\t\treturn tick;\n\t}\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"category\", \"fill\");\n\t\tsuper._afterNew();\n\t}\n\n\tprotected _onDataClear() {\n\t\tconst colors = this.get(\"colors\");\n\t\tif (colors) {\n\t\t\tcolors.reset();\n\t\t}\n\t\tconst patterns = this.get(\"patterns\");\n\t\tif (patterns) {\n\t\t\tpatterns.reset();\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tthis._lLabels = [];\n\t\tthis._rLabels = [];\n\t\tthis._hLabels = [];\n\n\t\tif (this._valuesDirty) {\n\t\t\tlet sum = 0;\n\t\t\tlet absSum = 0;\n\t\t\tlet valueHigh = 0;\n\t\t\tlet valueLow = Infinity;\n\t\t\tlet count = 0;\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tlet valueWorking = dataItem.get(\"valueWorking\", 0);\n\t\t\t\tsum += valueWorking;\n\t\t\t\tabsSum += Math.abs(valueWorking);\n\t\t\t});\n\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tlet value = dataItem.get(\"valueWorking\", 0);\n\n\t\t\t\tif (value > valueHigh) {\n\t\t\t\t\tvalueHigh = value;\n\t\t\t\t}\n\n\t\t\t\tif (value < valueLow) {\n\t\t\t\t\tvalueLow = value;\n\t\t\t\t}\n\n\t\t\t\tcount++;\n\n\t\t\t\tlet percentTotal = value / absSum;\n\n\t\t\t\tif (absSum == 0) {\n\t\t\t\t\tpercentTotal = 0;\n\t\t\t\t}\n\n\t\t\t\tdataItem.setRaw(\"valuePercentTotal\", percentTotal * 100);\n\t\t\t});\n\n\t\t\tthis.setPrivateRaw(\"valueLow\", valueLow);\n\t\t\tthis.setPrivateRaw(\"valueHigh\", valueHigh);\n\t\t\tthis.setPrivateRaw(\"valueSum\", sum);\n\t\t\tthis.setPrivateRaw(\"valueAverage\", sum / count);\n\t\t\tthis.setPrivateRaw(\"valueAbsoluteSum\", absSum);\n\t\t}\n\t}\n\n\t/**\n\t * Shows hidden series.\n\t *\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Animation promise\n\t */\n\tpublic async show(duration?: number): Promise<void> {\n\t\tlet promises: Array<Promise<any>> = [];\n\t\tpromises.push(super.show(duration))\n\n\t\tpromises.push(this._sequencedShowHide(true, duration));\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Hide whole series.\n\t *\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Animation promise\n\t */\n\tpublic async hide(duration?: number): Promise<void> {\n\t\tlet promises: Array<Promise<any>> = [];\n\t\tpromises.push(super.hide(duration))\n\n\t\tpromises.push(this._sequencedShowHide(false, duration));\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic _updateChildren() {\n\t\tsuper._updateChildren();\n\t\tif (this._valuesDirty) {\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tdataItem.get(\"label\").text.markDirtyText();\n\t\t\t});\n\t\t}\n\n\t\tif (this.isDirty(\"legendLabelText\") || this.isDirty(\"legendValueText\")) {\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tthis.updateLegendValue(dataItem);\n\t\t\t});\n\t\t}\n\n\t\tthis._arrange();\n\t}\n\n\tprotected _arrange() {\n\t\tthis._arrangeDown(this._lLabels);\n\t\tthis._arrangeUp(this._lLabels);\n\n\t\tthis._arrangeDown(this._rLabels);\n\t\tthis._arrangeUp(this._rLabels);\n\n\t\tthis._arrangeLeft(this._hLabels);\n\t\tthis._arrangeRight(this._hLabels);\n\n\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\t}\n\n\n\tpublic _afterChanged() {\n\t\tsuper._afterChanged();\n\t\tthis._arrange();\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tif (dataItem.get(\"fill\") == null) {\n\t\t\tlet colors = this.get(\"colors\");\n\t\t\tif (colors) {\n\t\t\t\tdataItem.setRaw(\"fill\", colors.next());\n\t\t\t}\n\t\t}\n\n\t\tif (dataItem.get(\"fillPattern\") == null) {\n\t\t\tlet patterns = this.get(\"patterns\");\n\t\t\tif (patterns) {\n\t\t\t\tdataItem.setRaw(\"fillPattern\", patterns.next());\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Shows series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.showDataItem(dataItem, duration)];\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = this.get(\"stateAnimationDuration\", 0);\n\t\t}\n\n\t\tconst easing = this.get(\"stateAnimationEasing\");\n\n\t\tlet value = dataItem.get(\"value\");\n\n\t\tconst animation = dataItem.animate({ key: \"valueWorking\", to: value, duration: duration, easing: easing });\n\t\tif (animation) {\n\t\t\tpromises.push(animation.waitForStop());\n\t\t}\n\n\t\tconst tick = dataItem.get(\"tick\");\n\t\tif (tick) {\n\t\t\tpromises.push(tick.show(duration));\n\t\t}\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tpromises.push(label.show(duration));\n\t\t}\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tpromises.push(slice.show(duration));\n\t\t}\n\n\t\tif (slice.get(\"active\")) {\n\t\t\tslice.states.applyAnimate(\"active\");\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * Hides series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tconst promises = [super.hideDataItem(dataItem, duration)];\n\t\tconst hiddenState = this.states.create(\"hidden\", {})\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = hiddenState.get(\"stateAnimationDuration\", this.get(\"stateAnimationDuration\", 0));\n\t\t}\n\n\t\tconst easing = hiddenState.get(\"stateAnimationEasing\", this.get(\"stateAnimationEasing\"));\n\n\t\tconst animation = dataItem.animate({ key: \"valueWorking\", to: 0, duration: duration, easing: easing });\n\t\tif (animation) {\n\t\t\tpromises.push(animation.waitForStop());\n\t\t}\n\n\t\tconst tick = dataItem.get(\"tick\");\n\t\tif (tick) {\n\t\t\tpromises.push(tick.hide(duration));\n\t\t}\n\t\tconst label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tpromises.push(label.hide(duration));\n\t\t}\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tslice.hideTooltip();\n\n\t\tif (slice) {\n\t\t\tpromises.push(slice.hide(duration));\n\t\t}\n\n\t\tawait Promise.all(promises);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet label = dataItem.get(\"label\");\n\t\tif (label) {\n\t\t\tthis.labels.removeValue(label);\n\t\t\tlabel.dispose();\n\t\t}\n\t\tlet tick = dataItem.get(\"tick\");\n\t\tif (tick) {\n\t\t\tthis.ticks.removeValue(tick);\n\t\t\ttick.dispose();\n\t\t}\n\t\tlet slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tthis.slices.removeValue(slice);\n\t\t\tslice.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * Triggers hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic hoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice && !slice.isHidden()) {\n\t\t\tslice.hover();\n\t\t}\n\t}\n\n\t/**\n\t * Triggers un-hover on a series data item.\n\t *\n\t * @since 5.0.7\n\t * @param  dataItem  Target data item\n\t */\n\tpublic unhoverDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tif (slice) {\n\t\t\tslice.unhover();\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic updateLegendMarker(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tif (dataItem) {\n\t\t\tconst slice = dataItem.get(\"slice\");\n\n\t\t\tif (slice) {\n\t\t\t\tconst legendDataItem = dataItem.get(\"legendDataItem\");\n\t\t\t\tif (legendDataItem) {\n\t\t\t\t\tconst markerRectangle = legendDataItem.get(\"markerRectangle\");\n\t\t\t\t\t$array.each(visualSettings, (setting: any) => {\n\t\t\t\t\t\tif (slice.get(setting) != null) {\n\t\t\t\t\t\t\tmarkerRectangle.set(setting, slice.get(setting));\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _arrangeDown(labels?: Array<{ label: Label, y: number }>) {\n\t\tif (labels) {\n\n\t\t\tlet next = this._getNextDown();\n\n\t\t\tlabels.sort((a, b) => {\n\t\t\t\tif (a.y > b.y) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (a.y < b.y) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t$array.each(labels, (l) => {\n\t\t\t\tconst bounds = l.label.adjustedLocalBounds();\n\t\t\t\tlet labelTop = bounds.top;\n\t\t\t\tif (l.y + labelTop < next) {\n\t\t\t\t\tl.y = next - labelTop;\n\t\t\t\t}\n\t\t\t\tl.label.set(\"y\", l.y);\n\n\t\t\t\tnext = l.y + bounds.bottom;\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _getNextUp() {\n\t\treturn this.labelsContainer.maxHeight();\n\t}\n\n\tprotected _getNextDown() {\n\t\treturn 0;\n\t}\n\n\tprotected _arrangeUp(labels?: Array<{ label: Label, y: number }>) {\n\t\tif (labels) {\n\t\t\tlet next = this._getNextUp();\n\n\t\t\tlabels.sort((a, b) => {\n\t\t\t\tif (a.y < b.y) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (a.y > b.y) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t$array.each(labels, (l) => {\n\t\t\t\tconst bounds = l.label.adjustedLocalBounds();\n\t\t\t\tlet labelBottom = bounds.bottom;\n\t\t\t\tif (l.y + labelBottom > next) {\n\t\t\t\t\tl.y = next - labelBottom;\n\t\t\t\t}\n\t\t\t\tl.label.set(\"y\", l.y);\n\t\t\t\tnext = l.y + bounds.top;\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _arrangeRight(labels?: Array<{ label: Label, y: number }>) {\n\t\tif (labels) {\n\n\t\t\tlet next = 0;\n\n\t\t\tlabels.sort((a, b) => {\n\t\t\t\tif (a.y > b.y) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (a.y < b.y) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t$array.each(labels, (l) => {\n\t\t\t\tconst bounds = l.label.adjustedLocalBounds();\n\t\t\t\tlet labelLeft = bounds.left;\n\t\t\t\tif (l.y + labelLeft < next) {\n\t\t\t\t\tl.y = next - labelLeft;\n\t\t\t\t}\n\t\t\t\tl.label.set(\"x\", l.y);\n\n\t\t\t\tnext = l.y + bounds.right;\n\t\t\t})\n\t\t}\n\t}\n\n\tprotected _arrangeLeft(labels?: Array<{ label: Label, y: number }>) {\n\t\tif (labels) {\n\t\t\tlet next = this.labelsContainer.maxWidth();\n\n\t\t\tlabels.sort((a, b) => {\n\t\t\t\tif (a.y < b.y) {\n\t\t\t\t\treturn 1;\n\t\t\t\t}\n\t\t\t\telse if (a.y > b.y) {\n\t\t\t\t\treturn -1;\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn 0;\n\t\t\t\t}\n\t\t\t})\n\n\t\t\t$array.each(labels, (l) => {\n\t\t\t\tconst bounds = l.label.adjustedLocalBounds();\n\t\t\t\tlet labelRight = bounds.right;\n\t\t\t\tif (l.y + labelRight > next) {\n\t\t\t\t\tl.y = next - labelRight;\n\t\t\t\t}\n\t\t\t\tl.label.set(\"x\", l.y);\n\t\t\t\tnext = l.y + bounds.left;\n\t\t\t})\n\t\t}\n\t}\n\n\tpublic _updateSize() {\n\t\tsuper._updateSize();\n\t\tthis.markDirty();\n\t}\n\n\tprotected _updateTick(_dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t}\n\n\tprotected _dispose() {\n\t\tsuper._dispose();\n\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\tchart.series.removeValue(this);\n\t\t}\n\t}\n\n\n}\n", "import type { PieSeries } from \"./PieSeries\";\n\nimport { Percent } from \"../../core/util/Percent\";\nimport { PercentChart, IPercentChartPrivate, IPercentChartSettings } from \"../percent/PercentChart\";\nimport { p50 } from \"../../core/util/Percent\";\n\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $math from \"../../core/util/Math\";\n\n\nexport interface IPieChartSettings extends IPercentChartSettings {\n\n\t/**\n\t * Outer radius of the pie chart.\n\t *\n\t * Can be set in fixed pixel value, or relative to chart container size in\n\t * percent.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Pie_radius} for more info\n\t * @default 80%\n\t */\n\tradius?: number | Percent;\n\n\t/**\n\t * Inner radius of the pie chart. Setting to any non-zero value will result\n\t * in a donut chart.\n\t *\n\t * Can be set in fixed pixel value, or relative to chart container size in\n\t * percent.\n\t *\n\t * Setting to negative number will mean pixels from outer radius.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Pie_radius} for more info\n\t */\n\tinnerRadius?: number | Percent;\n\n\t/**\n\t * A start angle of the chart in degrees.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Start_end_angles} for more info\n\t * @default -90\n\t */\n\tstartAngle?: number;\n\n\t/**\n\t * An end angle of the chart in degrees.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/#Start_end_angles} for more info\n\t * @default 270\n\t */\n\tendAngle?: number;\n\n}\n\nexport interface IPieChartPrivate extends IPercentChartPrivate {\n\n\t/**\n\t * @ignore\n\t */\n\tirModifyer?: number;\n\n}\n\n/**\n * Creates a pie chart.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/} for more info\n * @important\n */\nexport class PieChart extends PercentChart {\n\n\tpublic static className: string = \"PieChart\";\n\tpublic static classNames: Array<string> = PercentChart.classNames.concat([PieChart.className]);\n\n\tdeclare public _settings: IPieChartSettings;\n\tdeclare public _privateSettings: IPieChartPrivate;\n\tdeclare public _seriesType: PieSeries;\n\n\tpublic _maxRadius: number = 1;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.seriesContainer.setAll({ x: p50, y: p50 });\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tconst chartContainer = this.chartContainer;\n\t\tconst w = chartContainer.innerWidth();\n\t\tconst h = chartContainer.innerHeight();\n\n\t\tconst startAngle = this.get(\"startAngle\", 0);\n\t\tconst endAngle = this.get(\"endAngle\", 0);\n\t\tconst innerRadius = this.get(\"innerRadius\");\n\n\t\tlet bounds = $math.getArcBounds(0, 0, startAngle, endAngle, 1);\n\n\t\tconst wr = w / (bounds.right - bounds.left);\n\t\tconst hr = h / (bounds.bottom - bounds.top);\n\n\t\tlet innerBounds = { left: 0, right: 0, top: 0, bottom: 0 };\n\n\t\tif (innerRadius instanceof Percent) {\n\t\t\tlet value = innerRadius.value;\n\t\t\tlet mr = Math.min(wr, hr);\n\t\t\tvalue = Math.max(mr * value, mr - Math.min(h, w)) / mr;\n\t\t\tinnerBounds = $math.getArcBounds(0, 0, startAngle, endAngle, value);\n\t\t\tthis.setPrivateRaw(\"irModifyer\", value / innerRadius.value);\n\t\t}\n\n\t\tbounds = $math.mergeBounds([bounds, innerBounds]);\n\n\t\tconst prevRadius = this._maxRadius;\n\t\tthis._maxRadius = Math.min(wr, hr);\n\n\t\tconst radius = $utils.relativeToValue(this.get(\"radius\", 0), this._maxRadius);\n\t\tthis.seriesContainer.setAll({\n\t\t\tdy: -radius * (bounds.bottom + bounds.top) / 2, dx: -radius * (bounds.right + bounds.left) / 2\n\t\t})\n\n\t\tif (this.isDirty(\"startAngle\") || this.isDirty(\"endAngle\") || prevRadius != this._maxRadius) {\n\t\t\tthis.series.each((series) => {\n\t\t\t\tseries._markDirtyKey(\"startAngle\");\n\t\t\t})\n\t\t}\n\n\t\tif(this.isDirty(\"innerRadius\") || this.isDirty(\"radius\")){\n\t\t\tthis.series.each((series) => {\n\t\t\t\tseries._markDirtyKey(\"innerRadius\");\n\t\t\t})\t\t\t\n\t\t}\n\t}\n\n\t/**\n\t * Returns outer radius in pixels.\n\t *\n\t * If optional series parameter is passed in, it will return outer radius\n\t * of that particular series.\n\t *\n\t * @param   series  Series\n\t * @return          Radius in pixels\n\t */\n\tpublic radius(series?: PieSeries): number {\n\n\t\tlet radius = $utils.relativeToValue(this.get(\"radius\", 0), this._maxRadius);\n\t\tlet innerRadius = $utils.relativeToValue(this.get(\"innerRadius\", 0), radius);\n\n\t\tif (series) {\n\t\t\tlet index = this.series.indexOf(series);\n\t\t\tlet length = this.series.length;\n\n\t\t\tlet seriesRadius = series.get(\"radius\");\n\t\t\tif (seriesRadius != null) {\n\t\t\t\treturn innerRadius + $utils.relativeToValue(seriesRadius, radius - innerRadius);\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn innerRadius + (radius - innerRadius) / length * (index + 1);\n\t\t\t}\n\t\t}\n\t\treturn radius;\n\t}\n\n\t/**\n\t * Returns inner radius in pixels.\n\t *\n\t * If optional series parameter is passed in, it will return inner radius\n\t * of that particular series.\n\t *\n\t * @param   series  Series\n\t * @return          Radius in pixels\n\t */\n\tpublic innerRadius(series?: PieSeries): number {\n\t\tconst radius = this.radius();\n\t\tlet innerRadius = $utils.relativeToValue(this.get(\"innerRadius\", 0), radius);\n\n\t\tif (innerRadius < 0) {\n\t\t\tinnerRadius = radius + innerRadius;\n\t\t}\n\n\t\tif (series) {\n\t\t\tlet index = this.series.indexOf(series);\n\t\t\tlet length = this.series.length;\n\n\t\t\tlet seriesInnerRadius = series.get(\"innerRadius\");\n\t\t\tif (seriesInnerRadius != null) {\n\t\t\t\treturn innerRadius + $utils.relativeToValue(seriesInnerRadius, radius - innerRadius);\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn innerRadius + (radius - innerRadius) / length * index;\n\t\t\t}\n\t\t}\n\t\treturn innerRadius;\n\t}\n\n\tpublic _updateSize() {\n\t\tsuper._updateSize();\n\t\tthis.markDirtyKey(\"radius\");\n\t}\t\t\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { <PERSON><PERSON><PERSON> } from \"./PieChart\";\n\nimport { PercentSeries, IPercentSeriesSettings, IPercentSeriesDataItem, IPercentSeriesPrivate } from \"../percent/PercentSeries\";\nimport { Template } from \"../../core/util/Template\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport { Slice } from \"../../core/render/Slice\";\nimport { Tick } from \"../../core/render/Tick\";\nimport { RadialLabel } from \"../../core/render/RadialLabel\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { p100, Percent } from \"../../core/util/Percent\";\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $math from \"../../core/util/Math\";\nimport * as $utils from \"../../core/util/Utils\";\n\nexport interface IPieSeriesDataItem extends IPercentSeriesDataItem {\n\tslice: Slice;\n\tlabel: RadialLabel;\n}\n\nexport interface IPieSeriesSettings extends IPercentSeriesSettings {\n\n\t/**\n\t * Radius of the series in pixels or percent.\n\t */\n\tradius?: Percent | number;\n\n\t/**\n\t * Radius of the series in pixels or percent.\n\t *\n\t * Setting to negative number will mean pixels from outer radius.\n\t */\n\tinnerRadius?: Percent | number;\n\n\t/**\n\t * Start angle of the series in degrees.\n\t *\n\t * @default -90\n\t */\n\tstartAngle?: number;\n\n\t/**\n\t * End angle of the series in degrees.\n\t *\n\t * @default 270\n\t */\n\tendAngle?: number;\n\n}\n\nexport interface IPieSeriesPrivate extends IPercentSeriesPrivate {\n\n\t/**\n\t * Actual radius of the series in pixels.\n\t */\n\tradius?: number;\n\n}\n\n/**\n * Creates a series for a [[PieChart]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/} for more info\n * @important\n */\nexport class PieSeries extends PercentSeries {\n\n\tdeclare public chart: PieChart | undefined;\n\n\tpublic static className: string = \"PieSeries\";\n\tpublic static classNames: Array<string> = PercentSeries.classNames.concat([PieSeries.className]);\n\n\tdeclare public _settings: IPieSeriesSettings;\n\tdeclare public _privateSettings: IPieSeriesPrivate;\n\tdeclare public _dataItemSettings: IPieSeriesDataItem;\n\n\tdeclare public _sliceType: Slice;\n\tdeclare public _labelType: RadialLabel;\n\n\tprotected _makeSlices(): ListTemplate<this[\"_sliceType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Slice._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.slices.template.get(\"themeTags\", []), [\"pie\", \"series\"])\n\t\t\t}, [this.slices.template]),\n\t\t);\n\t}\n\n\tprotected _makeLabels(): ListTemplate<this[\"_labelType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => RadialLabel._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [\"pie\", \"series\"])\n\t\t\t}, [this.labels.template]),\n\t\t);\n\t}\n\n\tprotected _makeTicks(): ListTemplate<this[\"_tickType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Tick._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.ticks.template.get(\"themeTags\", []), [\"pie\", \"series\"])\n\t\t\t}, [this.ticks.template]),\n\t\t);\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tconst slice = this.makeSlice(dataItem);\n\n\t\tslice.on(\"scale\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\t\tslice.on(\"shiftRadius\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\t\tslice.events.on(\"positionchanged\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\n\t\tconst label = this.makeLabel(dataItem);\n\n\t\tlabel.events.on(\"positionchanged\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\n\t\tthis.makeTick(dataItem);\n\n\t\tslice.events.on(\"positionchanged\", () => {\n\t\t\tlabel.markDirty();\n\t\t})\n\t}\n\n\tprotected _getNextUp() {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\treturn chart._maxRadius;\n\t\t}\n\t\treturn this.labelsContainer.maxHeight() / 2;\n\t}\n\n\tprotected _getNextDown() {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\treturn -chart._maxRadius;\n\t\t}\n\t\treturn -this.labelsContainer.maxHeight() / 2;\n\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\n\t\t\tif (this.isDirty(\"alignLabels\")) {\n\t\t\t\tlet labelsTemplate = this.labels.template;\n\n\t\t\t\tif (this.get(\"alignLabels\")) {\n\t\t\t\t\tlabelsTemplate.set(\"textType\", \"aligned\");\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tlet textType = labelsTemplate.get(\"textType\");\n\t\t\t\t\tif (textType == null || textType == \"aligned\") {\n\t\t\t\t\t\tlabelsTemplate.set(\"textType\", \"adjusted\");\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\n\t\t\tif (this._valuesDirty || this.isDirty(\"radius\") || this.isDirty(\"innerRadius\") || this.isDirty(\"startAngle\") || this.isDirty(\"endAngle\") || this.isDirty(\"alignLabels\")) {\n\t\t\t\tthis.markDirtyBounds();\n\t\t\t\tconst startAngle = this.get(\"startAngle\", chart.get(\"startAngle\", -90));\n\t\t\t\tconst endAngle = this.get(\"endAngle\", chart.get(\"endAngle\", 270));\n\t\t\t\tconst arc = endAngle - startAngle;\n\t\t\t\tlet currentAngle = startAngle;\n\n\t\t\t\tconst radius = chart.radius(this);\n\t\t\t\tthis.setPrivateRaw(\"radius\", radius);\n\t\t\t\tlet innerRadius = chart.innerRadius(this) * chart.getPrivate(\"irModifyer\", 1);\n\n\t\t\t\tif (innerRadius < 0) {\n\t\t\t\t\tinnerRadius = radius + innerRadius;\n\t\t\t\t}\n\n\t\t\t\t//if (radius > 0) {\n\t\t\t\t$array.each(this._dataItems, (dataItem) => {\n\n\t\t\t\t\tthis.updateLegendValue(dataItem);\n\n\t\t\t\t\tlet currentArc = arc * dataItem.get(\"valuePercentTotal\") / 100;\n\t\t\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\t\t\tif (slice) {\n\t\t\t\t\t\tslice.set(\"radius\", radius);\n\t\t\t\t\t\tslice.set(\"innerRadius\", innerRadius);\n\t\t\t\t\t\tslice.set(\"startAngle\", currentAngle);\n\n\t\t\t\t\t\tslice.set(\"arc\", currentArc);\n\n\t\t\t\t\t\tconst color = dataItem.get(\"fill\");\n\t\t\t\t\t\tslice._setDefault(\"fill\", color);\n\t\t\t\t\t\tslice._setDefault(\"stroke\", color);\n\n\t\t\t\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\t\t\t\t\t\tslice._setDefault(\"fillPattern\", fillPattern);\t\t\t\t\t\t\n\t\t\t\t\t}\n\n\t\t\t\t\tlet middleAngle = $math.normalizeAngle(currentAngle + currentArc / 2);\n\n\t\t\t\t\tconst label = dataItem.get(\"label\");\n\t\t\t\t\tif (label) {\n\t\t\t\t\t\tlabel.setPrivate(\"radius\", radius);\n\t\t\t\t\t\tlabel.setPrivate(\"innerRadius\", innerRadius);\n\t\t\t\t\t\tlabel.set(\"labelAngle\", middleAngle);\n\n\t\t\t\t\t\tif (label.get(\"textType\") == \"aligned\") {\n\t\t\t\t\t\t\tlet labelRadius = radius + label.get(\"radius\", 0);\n\t\t\t\t\t\t\tlet y = radius * $math.sin(middleAngle);\n\n\t\t\t\t\t\t\tif (middleAngle > 90 && middleAngle <= 270) {\n\t\t\t\t\t\t\t\tif (!label.isHidden() && !label.isHiding()) {\n\t\t\t\t\t\t\t\t\tthis._lLabels.push({ label: label, y: y });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlabelRadius *= -1;\n\t\t\t\t\t\t\t\tlabelRadius -= this.labelsContainer.get(\"paddingLeft\", 0);\n\t\t\t\t\t\t\t\tlabel.set(\"centerX\", p100);\n\t\t\t\t\t\t\t\tlabel.setPrivateRaw(\"left\", true);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tif (!label.isHidden() && !label.isHiding()) {\n\t\t\t\t\t\t\t\t\tthis._rLabels.push({ label: label, y: y });\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tlabelRadius += this.labelsContainer.get(\"paddingRight\", 0);\n\t\t\t\t\t\t\t\tlabel.set(\"centerX\", 0);\n\t\t\t\t\t\t\t\tlabel.setPrivateRaw(\"left\", false);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tlabel.set(\"x\", labelRadius);\n\t\t\t\t\t\t\tlabel.set(\"y\", radius * $math.sin(middleAngle));\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tcurrentAngle += currentArc;\n\t\t\t\t\tthis._updateTick(dataItem);\n\t\t\t\t})\n\t\t\t\t//}\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _updateTick(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst tick = dataItem.get(\"tick\");\n\t\tconst label = dataItem.get(\"label\");\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tconst location = tick.get(\"location\", 1);\n\t\tif (tick && label && slice) {\n\t\t\tconst radius = (slice.get(\"shiftRadius\", 0) + slice.get(\"radius\", 0)) * slice.get(\"scale\", 1) * location;\n\t\t\tconst labelAngle = label.get(\"labelAngle\", 0);\n\t\t\tconst cos = $math.cos(labelAngle);\n\t\t\tconst sin = $math.sin(labelAngle);\n\n\t\t\tconst labelsContainer = this.labelsContainer;\n\t\t\tconst pl = labelsContainer.get(\"paddingLeft\", 0);\n\t\t\tconst pr = labelsContainer.get(\"paddingRight\", 0);\n\n\t\t\tlet x = 0;\n\t\t\tlet y = 0;\n\n\t\t\tx = label.x();\n\t\t\ty = label.y();\n\t\t\tlet points:Array<IPoint> = [];\n\n\t\t\tif(x != 0 && y != 0){\n\t\t\t\tif (label.get(\"textType\") == \"circular\") {\n\t\t\t\t\tconst labelRadius = label.radius() - label.get(\"paddingBottom\", 0);\n\t\t\t\t\tconst labelAngle = label.get(\"labelAngle\", 0);\n\t\t\t\t\tx = labelRadius * $math.cos(labelAngle);\n\t\t\t\t\ty = labelRadius * $math.sin(labelAngle);\n\t\t\t\t}\n\n\t\t\t\tlet dx = -pr;\n\t\t\t\tif (label.getPrivate(\"left\")) {\n\t\t\t\t\tdx = pl;\n\t\t\t\t}\n\t\t\t\tpoints = [{ x: slice.x() + radius * cos, y: slice.y() + radius * sin }, { x: x + dx, y: y }, { x: x, y: y }];\t\t\t\t\n\t\t\t}\n\n\t\t\ttick.set(\"points\", points);\n\t\t}\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tconst slice = dataItem.get(\"slice\");\n\n\t\t\tif (slice) {\n\t\t\t\tconst innerRadius = slice.get(\"innerRadius\", 0);\n\t\t\t\tconst radius = slice.get(\"radius\", 0);\n\t\t\t\tconst startAngle = slice.get(\"startAngle\", 0);\n\t\t\t\tconst arc = slice.get(\"arc\", 0);\n\t\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\n\t\t\t\tconst angle = startAngle + arc * locationX;\n\t\t\t\tconst r = innerRadius + (radius - innerRadius) * locationY;\n\n\t\t\t\tsprite.setAll({ x: $math.cos(angle) * r, y: $math.sin(angle) * r });\n\t\t\t}\n\t\t}\n\t}\n}\n", "import { Graphics, IGraphicsSettings, IGraphicsPrivate } from \"../../core/render/Graphics\";\nimport type { IPoint } from \"../../core/util/IPoint\";\n\nexport interface IFunnelSliceSettings extends IGraphicsSettings {\n\n\t/**\n\t * Top width in pixels.\n\t */\n\ttopWidth?: number;\n\n\t/**\n\t * Bottom width in pixels.\n\t */\n\tbottomWidth?: number;\n\n\t/**\n\t * Orientation.\n\t */\n\torientation?: \"vertical\" | \"horizontal\";\n\n\t/**\n\t * A distance in pixels the slice should \"puff up\".\n\t *\n\t * Any non-zero value will make sides of the slide curved.\n\t */\n\texpandDistance?: number;\n\n}\n\nexport interface IFunnelSlicePrivate extends IGraphicsPrivate {\n}\n\n/**\n * Draws a slice for [[FunnelSeries]].\n */\nexport class FunnelSlice extends Graphics {\n\tdeclare public _settings: IFunnelSliceSettings;\n\tdeclare public _privateSettings: IFunnelSlicePrivate;\n\n\tpublic static className: string = \"FunnelSlice\";\n\tpublic static classNames: Array<string> = Graphics.classNames.concat([FunnelSlice.className]);\n\tprotected _projectionDirty: boolean = false;\n\n\tprotected _tlx: number = 0;\n\tprotected _tly: number = 0;\n\n\tprotected _trx: number = 0;\n\tprotected _try: number = 0;\n\n\tprotected _blx: number = 0;\n\tprotected _bly: number = 0;\n\n\tprotected _brx: number = 0;\n\tprotected _bry: number = 0;\n\n\tprotected _cprx: number = 0;\n\tprotected _cplx: number = 0;\n\tprotected _cpry: number = 0;\n\tprotected _cply: number = 0;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.set(\"draw\", (display) => {\n\t\t\tdisplay.moveTo(this._tlx, this._tly);\n\t\t\tdisplay.lineTo(this._trx, this._try);\n\t\t\tdisplay.quadraticCurveTo(this._cprx, this._cpry, this._brx, this._bry);\n\t\t\tdisplay.lineTo(this._blx, this._bly);\n\t\t\tdisplay.quadraticCurveTo(this._cplx, this._cply, this._tlx, this._tly);\n\t\t})\n\t}\n\n\tpublic getPoint(locationX: number, locationY: number): IPoint {\n\t\tlet w = this.width();\n\t\tlet h = this.height();\n\n\t\tconst tw = this.get(\"topWidth\", 0);\n\t\tconst bw = this.get(\"bottomWidth\", 0);\n\n\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\tlet tlx = -tw / 2;\n\t\t\tlet trx = tw / 2;\n\n\t\t\tlet brx = bw / 2;\n\t\t\tlet blx = - bw / 2;\n\n\t\t\tlet mlx = tlx + (blx - tlx) * locationY;\n\t\t\tlet mrx = trx + (brx - trx) * locationY;\n\n\t\t\treturn { x: mlx + (mrx - mlx) * locationX, y: h * locationY };\n\t\t}\n\t\telse {\n\t\t\tlet tlx = -tw / 2;\n\t\t\tlet trx = tw / 2;\n\n\t\t\tlet brx = bw / 2;\n\t\t\tlet blx = - bw / 2;\n\n\t\t\tlet mlx = tlx + (blx - tlx) * locationX;\n\t\t\tlet mrx = trx + (brx - trx) * locationX;\n\n\t\t\treturn { x: w * locationX, y: mlx + (mrx - mlx) * locationY};\n\t\t}\n\n\t}\n\n\tpublic _changed() {\n\n\t\tif (this.isDirty(\"topWidth\") || this.isDirty(\"bottomWidth\") || this.isDirty(\"expandDistance\") || this.isDirty(\"orientation\") || this.isDirty(\"width\") || this.isDirty(\"height\")) {\n\t\t\tconst w = this.width();\n\t\t\tconst h = this.height();\n\t\t\tconst tw = this.get(\"topWidth\", 0);\n\t\t\tconst bw = this.get(\"bottomWidth\", 0);\n\t\t\tthis._clear = true;\n\n\t\t\tlet ed = this.get(\"expandDistance\", 0);\n\n\t\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\t\tthis._tlx = -tw / 2;\n\t\t\t\tthis._tly = 0;\n\n\t\t\t\tthis._trx = tw / 2;\n\t\t\t\tthis._try = 0;\n\n\t\t\t\tthis._brx = bw / 2;\n\t\t\t\tthis._bry = h;\n\n\t\t\t\tthis._blx = -bw / 2;\n\t\t\t\tthis._bly = h;\n\n\t\t\t\tthis._cprx = this._trx + (this._brx - this._trx) / 2 + ed * h,\n\t\t\t\tthis._cpry = this._try + 0.5 * h;\n\n\t\t\t\tthis._cplx = this._tlx + (this._blx - this._tlx) / 2 - ed * h;\n\t\t\t\tthis._cply = this._tly + 0.5 * h;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis._tly = -tw / 2;\n\t\t\t\tthis._tlx = 0;\n\n\t\t\t\tthis._try = tw / 2;\n\t\t\t\tthis._trx = 0;\n\n\t\t\t\tthis._bry = bw / 2;\n\t\t\t\tthis._brx = w;\n\n\t\t\t\tthis._bly = -bw / 2;\n\t\t\t\tthis._blx = w;\n\n\t\t\t\tthis._cpry = this._try + (this._bry - this._try) / 2 + ed * w,\n\t\t\t\tthis._cprx = this._trx + 0.5 * w;\n\n\t\t\t\tthis._cply = this._tly + (this._bly - this._tly) / 2 - ed * w;\n\t\t\t\tthis._cplx = this._tlx + 0.5 * w;\n\t\t\t}\n\t\t}\n\t\tsuper._changed();\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Sliced<PERSON><PERSON> } from \"./SlicedChart\";\n\nimport { PercentSeries, IPercentSeriesSettings, IPercentSeriesDataItem, IPercentSeriesPrivate } from \"../percent/PercentSeries\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { FunnelSlice } from \"./FunnelSlice\";\nimport { Tick } from \"../../core/render/Tick\";\nimport { Label } from \"../../core/render/Label\";\nimport { percent, p50, p100 } from \"../../core/util/Percent\";\nimport type { Bullet } from \"../../core/render/Bullet\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $utils from \"../../core/util/Utils\";\n\n\nexport interface IFunnelSeriesDataItem extends IPercentSeriesDataItem {\n\n\t/**\n\t * A related slice element.\n\t */\n\tslice: FunnelSlice;\n\n\t/**\n\t * A related slice link element\n\t */\n\tlink: FunnelSlice;\n\n\t/**\n\t * Data item's index.\n\t */\n\tindex: number;\n\n}\n\nexport interface IFunnelSeriesSettings extends IPercentSeriesSettings {\n\n\t/**\n\t * Width of the bottom edge of the slice relative to the top edge of the next\n\t * slice.\n\t *\n\t * `1` - means the full width of the slice, resulting in a rectangle.\n\t * `0` - means using width of the next slice, resulting in a trapezoid.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/funnel-series/#Slice_bottom_width} for more info\n\t * @default 1\n\t */\n\tbottomRatio?: number;\n\n\t/**\n\t * Orientation of the series.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/#Series_orientation} for more info\n\t * @default \"vertical\"\n\t */\n\torientation: \"horizontal\" | \"vertical\";\n\n\t/**\n\t * If set to `true`, series will not create slices for data items with zero\n\t * value.\n\t */\n\tignoreZeroValues?: boolean;\n\n\t/**\n\t * Should labels be aligned into columns/rows?\n\t *\n\t * @default false\n\t */\n\talignLabels?: boolean;\n\n\t/**\n\t * Relative location within area available to series where it should start.\n\t *\n\t * `0` - beginning, `1` - end, or any intermediate value.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/funnel-series/#Start_end_locations} for more info\n\t * @default 0\n\t */\n\tstartLocation?: number;\n\n\t/**\n\t * Relative location within area available to series where it should start.\n\t *\n\t * `0` - beginning, `1` - end, or any intermediate value.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/funnel-series/#Start_end_locations} for more info\n\t * @default 0\n\t */\n\tendLocation?: number;\n\n}\n\nexport interface IFunnelSeriesPrivate extends IPercentSeriesPrivate {\n}\n\n/**\n * Creates a funnel series for use in a [[SlicedChart]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/funnel-series/} for more info\n * @important\n */\nexport class FunnelSeries extends PercentSeries {\n\n\t/**\n\t * A chart series is attached to.\n\t */\n\tdeclare public chart: SlicedChart | undefined;\n\n\tprotected _tag = \"funnel\";\n\n\tdeclare public _sliceType: FunnelSlice;\n\tdeclare public _labelType: Label;\n\tdeclare public _tickType: Tick;\n\n\tprotected _makeSlices(): ListTemplate<this[\"_sliceType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => FunnelSlice._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.slices.template.get(\"themeTags\", []), [this._tag, \"series\", \"slice\", this.get(\"orientation\")])\n\t\t\t}, [this.slices.template])\n\t\t);\n\t}\n\n\tprotected _makeLabels(): ListTemplate<this[\"_labelType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Label._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.labels.template.get(\"themeTags\", []), [this._tag, \"series\", \"label\", this.get(\"orientation\")])\n\t\t\t}, [this.labels.template])\n\t\t);\n\t}\n\n\tprotected _makeTicks(): ListTemplate<this[\"_tickType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => Tick._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.ticks.template.get(\"themeTags\", []), [this._tag, \"series\", \"tick\", this.get(\"orientation\")])\n\t\t\t}, [this.ticks.template])\n\t\t);\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all slice links in series.\n\t *\n\t * `links.template` can also be used to configure slice links.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/funnel-series/#Slice_links} for more info\n\t */\n\tpublic readonly links: ListTemplate<this[\"_sliceType\"]> = this._makeLinks();\n\n\tprotected _makeLinks(): ListTemplate<this[\"_sliceType\"]> {\n\t\treturn new ListTemplate(\n\t\t\tTemplate.new({}),\n\t\t\t() => FunnelSlice._new(this._root, {\n\t\t\t\tthemeTags: $utils.mergeTags(this.links.template.get(\"themeTags\", []), [this._tag, \"series\", \"link\", this.get(\"orientation\")])\n\t\t\t}, [this.links.template]),\n\t\t);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeLink(dataItem: DataItem<this[\"_dataItemSettings\"]>): this[\"_sliceType\"] {\n\t\tconst link = this.slicesContainer.children.push(this.links.make());\n\t\tlink._setDataItem(dataItem);\n\t\tdataItem.set(\"link\", link);\n\t\tthis.links.push(link);\n\t\treturn link;\n\t}\n\n\tpublic static className: string = \"FunnelSeries\";\n\tpublic static classNames: Array<string> = PercentSeries.classNames.concat([FunnelSeries.className]);\n\n\tdeclare public _settings: IFunnelSeriesSettings;\n\tdeclare public _privateSettings: IFunnelSeriesPrivate;\n\tdeclare public _dataItemSettings: IFunnelSeriesDataItem;\n\n\tprotected _total: number = 0;\n\tprotected _count: number = 0;\n\tprotected _nextCoord: number = 0;\n\n\tprotected _opposite: boolean = false;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tconst slicesContainer = this.slicesContainer;\n\t\tslicesContainer.setAll({ isMeasured: true, position: \"relative\", width: percent(100), height: percent(100) });\n\t\tslicesContainer.onPrivate(\"width\", () => {\n\t\t\tthis.markDirtySize();\n\t\t})\n\n\t\tslicesContainer.onPrivate(\"height\", () => {\n\t\t\tthis.markDirtySize();\n\t\t})\n\n\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\tthis.set(\"layout\", this._root.horizontalLayout);\n\t\t}\n\t\telse {\n\t\t\tthis.set(\"layout\", this._root.verticalLayout);\n\t\t}\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tconst slice = this.makeSlice(dataItem);\n\n\t\tslice._setDataItem(dataItem);\n\n\t\tdataItem.set(\"slice\", slice);\n\n\t\tthis.makeLink(dataItem);\n\t\tconst label = this.makeLabel(dataItem);\n\n\t\tlabel.on(\"x\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\n\t\tlabel.on(\"y\", () => {\n\t\t\tthis._updateTick(dataItem);\n\t\t})\n\n\t\tthis.makeTick(dataItem);\n\n\t\tslice.events.on(\"positionchanged\", () => {\n\t\t\tlabel.markDirty();\n\t\t})\n\n\t\tslice.events.on(\"boundschanged\", () => {\n\t\t\tconst dataItem = slice.dataItem;\n\t\t\tif (dataItem) {\n\t\t\t\tthis._updateTick(dataItem as any);\n\t\t\t}\n\t\t})\n\t}\n\n\n\tpublic _updateChildren() {\n\t\tthis._opposite = false;\n\t\tif (this.children.indexOf(this.labelsContainer) == 0) {\n\t\t\tthis._opposite = true;\n\t\t}\n\n\t\tlet total = 0;\n\t\tlet count = 0;\n\n\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\tconst value = dataItem.get(\"value\");\n\t\t\tif ($type.isNumber(value)) {\n\t\t\t\tcount++;\n\t\t\t\tif (value > 0) {\n\t\t\t\t\ttotal += Math.abs(dataItem.get(\"valueWorking\", value) / value);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tif (this.get(\"ignoreZeroValues\", false)) {\n\t\t\t\t\t\tcount--;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (dataItem.isHidden()) {\n\t\t\t\t\t\t\tcount--;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\ttotal += 1;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tthis._total = 1 / count * total;\n\t\tthis._count = count;\n\n\t\tif (this.isDirty(\"alignLabels\")) {\n\t\t\tthis._fixLayout();\n\t\t}\n\n\t\tif (this._total > 0 && (this._valuesDirty || this._sizeDirty)) {\n\n\t\t\tconst slicesContainer = this.slicesContainer;\n\n\t\t\tlet h: number;\n\t\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\t\th = slicesContainer.innerHeight();\n\t\t\t}\n\t\t\telse {\n\t\t\t\th = slicesContainer.innerWidth();\n\t\t\t}\n\n\t\t\tthis._nextCoord = this.get(\"startLocation\", 0) * h;\n\n\t\t\tthis.markDirtyBounds();\n\n\t\t\tlet i = 0;\n\t\t\t$array.each(this._dataItems, (dataItem) => {\n\t\t\t\tthis.updateLegendValue(dataItem);\n\n\t\t\t\tdataItem.set(\"index\", i);\n\t\t\t\ti++;\n\n\t\t\t\tconst slice = dataItem.get(\"slice\");\n\t\t\t\tconst tick = dataItem.get(\"tick\");\n\t\t\t\tconst label = dataItem.get(\"label\");\n\t\t\t\tconst link = dataItem.get(\"link\");\n\t\t\t\tconst color = dataItem.get(\"fill\");\n\t\t\t\tconst fillPattern = dataItem.get(\"fillPattern\");\n\n\t\t\t\tslice._setDefault(\"fill\", color);\n\t\t\t\tslice._setDefault(\"stroke\", color);\n\t\t\t\tslice._setDefault(\"fillPattern\", fillPattern);\n\t\t\t\tlink._setDefault(\"fill\", color);\n\t\t\t\tlink._setDefault(\"stroke\", color);\n\n\t\t\t\tconst value = dataItem.get(\"value\");\n\t\t\t\tif ($type.isNumber(value)) {\n\t\t\t\t\tif (value == 0 && this.get(\"ignoreZeroValues\")) {\n\t\t\t\t\t\tslice.setPrivate(\"visible\", false);\n\t\t\t\t\t\ttick.setPrivate(\"visible\", false);\n\t\t\t\t\t\tlabel.setPrivate(\"visible\", false);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tslice.setPrivate(\"visible\", true);\n\t\t\t\t\t\ttick.setPrivate(\"visible\", true);\n\t\t\t\t\t\tlabel.setPrivate(\"visible\", true);\n\n\t\t\t\t\t\tthis.decorateSlice(dataItem);\n\n\t\t\t\t\t\tif (this.isLast(dataItem)) {\n\t\t\t\t\t\t\tlink.setPrivate(\"visible\", false);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (!dataItem.isHidden()) {\n\t\t\t\t\t\t\tlink.setPrivate(\"visible\", true);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t\tsuper._updateChildren();\n\t}\n\n\tprotected _fixLayout() {\n\n\t\tconst orientation = this.get(\"orientation\");\n\t\tconst labelsContainer = this.labelsContainer;\n\t\tconst labelsTemplate = this.labels.template;\n\n\t\tif (this.get(\"alignLabels\")) {\n\t\t\tlabelsContainer.set(\"position\", \"relative\");\n\t\t\tlabelsContainer.setAll({ isMeasured: true });\n\t\t\tif (orientation == \"vertical\") {\n\t\t\t\tthis.set(\"layout\", this._root.horizontalLayout);\n\t\t\t\tlabelsTemplate.setAll({ centerX: p100, x: p100 });\n\t\t\t}\n\t\t\telse {\n\t\t\t\tthis.set(\"layout\", this._root.verticalLayout);\n\t\t\t\tlabelsTemplate.setAll({ centerX: 0, x: 0 });\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tlabelsContainer.setAll({ isMeasured: false, position: \"absolute\" });\n\t\t\tif (orientation == \"vertical\") {\n\t\t\t\tlabelsContainer.setAll({ x: p50 });\n\t\t\t\tlabelsTemplate.setAll({ centerX: p50, x: 0 });\n\t\t\t}\n\t\t\telse {\n\t\t\t\tlabelsContainer.setAll({ y: p50 });\n\t\t\t\tlabelsTemplate.setAll({ centerX: p50, y: 0 });\n\t\t\t}\n\t\t}\n\t\tthis.markDirtySize();\n\t}\n\n\tprotected getNextValue(dataItem: DataItem<this[\"_dataItemSettings\"]>): number {\n\t\tlet index = dataItem.get(\"index\");\n\t\tlet nextValue = dataItem.get(\"valueWorking\", 0);\n\t\tif (index < this.dataItems.length - 1) {\n\t\t\tlet nextItem = this.dataItems[index + 1];\n\t\t\tnextValue = nextItem.get(\"valueWorking\", 0);\n\n\t\t\tif (nextItem.isHidden() || (nextItem.get(\"value\") == 0 && this.get(\"ignoreZeroValues\"))) {\n\t\t\t\treturn this.getNextValue(nextItem);\n\t\t\t}\n\t\t}\n\t\treturn nextValue;\n\t}\n\n\tprotected isLast(dataItem: DataItem<this[\"_dataItemSettings\"]>): boolean {\n\t\tlet index = dataItem.get(\"index\");\n\t\tif (index == this.dataItems.length - 1) {\n\t\t\treturn true;\n\t\t}\n\t\telse {\n\t\t\tfor (let i = index + 1; i < this.dataItems.length; i++) {\n\t\t\t\tif (!this.dataItems[i].isHidden()) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn true;\n\t}\n\n\tprotected decorateSlice(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst orientation = this.get(\"orientation\");\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tconst label = dataItem.get(\"label\");\n\t\tconst link = dataItem.get(\"link\");\n\n\t\tconst slicesContainer = this.slicesContainer;\n\n\t\tlet maxWidth = slicesContainer.innerWidth();\n\t\tlet maxHeight = slicesContainer.innerHeight();\n\n\t\tlet maxSize = maxWidth;\n\t\tif (orientation == \"horizontal\") {\n\t\t\tmaxSize = maxHeight;\n\t\t}\n\n\t\tconst nextValue = this.getNextValue(dataItem);\n\t\tconst value = dataItem.get(\"value\", 0);\n\t\tconst workingValue = Math.abs(dataItem.get(\"valueWorking\", value));\n\t\tconst bottomRatio = this.get(\"bottomRatio\", 0);\n\t\tconst valueHigh = this.getPrivate(\"valueHigh\", 0);\n\n\n\t\tlet d = 1;\n\t\tif (value != 0) {\n\t\t\td = workingValue / Math.abs(value);\n\t\t}\n\t\telse {\n\t\t\tif (dataItem.isHidden()) {\n\t\t\t\td = 0.000001;\n\t\t\t}\n\t\t}\n\n\t\tif (this._nextCoord == Infinity) {\n\t\t\tthis._nextCoord = 0;\n\t\t}\n\n\t\tlet topWidth = workingValue / valueHigh * maxSize;\n\t\tlet bottomWidth = (workingValue - (workingValue - nextValue) * bottomRatio) / valueHigh * maxSize;\n\n\t\tslice.setAll({ topWidth, bottomWidth, orientation });\n\t\tlink.setAll({ topWidth: bottomWidth, bottomWidth: (workingValue - (workingValue - nextValue)) / valueHigh * maxSize, orientation });\n\n\t\tconst startLocation = this.get(\"startLocation\", 0);\n\t\tconst endLocation = this.get(\"endLocation\", 1);\n\n\t\tif (orientation == \"vertical\") {\n\n\t\t\tlet linkHeight = link.height() * d;\n\n\t\t\tmaxHeight = maxHeight * (endLocation - startLocation) + linkHeight;\n\n\t\t\tslice.set(\"y\", this._nextCoord);\n\n\t\t\tlet height = Math.min(100000, Math.max(0, maxHeight / this._count * d / this._total - linkHeight));\n\n\t\t\tslice.setAll({ height, x: maxWidth / 2 });\n\t\t\tlet labelY = this._nextCoord + height / 2;\n\t\t\tlabel.set(\"y\", labelY);\n\n\t\t\tthis._nextCoord += height + linkHeight;\n\t\t\tlink.setAll({ y: this._nextCoord - linkHeight, x: maxWidth / 2 });\n\t\t}\n\t\telse {\n\t\t\tlet linkHeight = link.width() * d;\n\n\t\t\tmaxWidth = maxWidth * (endLocation - startLocation) + linkHeight;\n\n\t\t\tslice.set(\"x\", this._nextCoord);\n\n\t\t\tlet width = Math.min(100000, Math.max(0, maxWidth / this._count * d / this._total - linkHeight));\n\n\t\t\tslice.setAll({ width, y: maxHeight / 2 });\n\t\t\tconst labelX = this._nextCoord + width / 2;\n\t\t\tlabel.set(\"x\", labelX);\n\n\t\t\tthis._nextCoord += width + linkHeight;\n\t\t\tlink.setAll({ x: this._nextCoord - linkHeight, y: maxHeight / 2 });\n\t\t}\n\t}\n\n\t/**\n\t * Hides series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async hideDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tdataItem.get(\"link\").hide(duration);\n\t\treturn super.hideDataItem(dataItem, duration)\n\t}\n\n\t/**\n\t * Shows series's data item.\n\t *\n\t * @param   dataItem  Data item\n\t * @param   duration  Animation duration in milliseconds\n\t * @return            Promise\n\t */\n\tpublic async showDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>, duration?: number): Promise<void> {\n\t\tdataItem.get(\"link\").show(duration);\n\t\treturn super.showDataItem(dataItem, duration)\n\t}\n\n\tprotected _updateTick(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tif (this.get(\"alignLabels\")) {\n\t\t\tconst tick = dataItem.get(\"tick\");\n\t\t\tconst label = dataItem.get(\"label\");\n\t\t\tconst slice = dataItem.get(\"slice\");\n\n\t\t\tif (tick && slice && label) {\n\n\t\t\t\tconst labelsContainer = this.labelsContainer;\n\t\t\t\tconst slicesContainer = this.slicesContainer;\n\t\t\t\tlet tickLocation = tick.get(\"location\", 0.5);\n\n\t\t\t\tconst lcw = labelsContainer.width();\n\t\t\t\tconst lch = labelsContainer.height();\n\n\t\t\t\tconst pl = labelsContainer.get(\"paddingLeft\", 0);\n\t\t\t\tconst pr = labelsContainer.get(\"paddingRight\", 0);\n\t\t\t\tconst pt = labelsContainer.get(\"paddingTop\", 0);\n\t\t\t\tconst pb = labelsContainer.get(\"paddingBottom\", 0);\n\n\t\t\t\tlet p0 = { x: 0, y: 0 };\n\t\t\t\tlet p1 = { x: 0, y: 0 };\n\t\t\t\tlet p2 = { x: 0, y: 0 };\n\n\t\t\t\tif (this._opposite) {\n\t\t\t\t\ttickLocation = 1 - tickLocation;\n\t\t\t\t}\n\n\t\t\t\tif (this.get(\"orientation\") == \"vertical\") {\n\t\t\t\t\tp0 = slice.getPoint(tickLocation, 0.5);\n\t\t\t\t\tp0.x += slice.x() + slicesContainer.x();\n\t\t\t\t\tp0.y += slice.y() + slicesContainer.y();\n\n\t\t\t\t\tif (this._opposite) {\n\t\t\t\t\t\tp1.x = lcw;\n\t\t\t\t\t\tp1.y = label.y();\n\n\t\t\t\t\t\tp2.x = lcw - pl;\n\t\t\t\t\t\tp2.y = p1.y;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tp1.x = slicesContainer.x() + slicesContainer.width();\n\t\t\t\t\t\tp1.y = label.y();\n\n\t\t\t\t\t\tp2.x = p1.x + lcw - label.width() - pr;\n\t\t\t\t\t\tp2.y = p1.y;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tp0 = slice.getPoint(0.5, tickLocation);\n\t\t\t\t\tp0.x += slice.x() + slicesContainer.x();\n\t\t\t\t\tp0.y += slice.y() + slicesContainer.y();\n\n\t\t\t\t\tif (this._opposite) {\n\t\t\t\t\t\tp1.y = lch;\n\t\t\t\t\t\tp1.x = label.x();\n\n\t\t\t\t\t\tp2.y = lch - pt;\n\t\t\t\t\t\tp2.x = p1.x;\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tp1.y = slicesContainer.y() + slicesContainer.height();\n\t\t\t\t\t\tp1.x = label.x();\n\n\t\t\t\t\t\tp2.y = p1.y + lch - label.height() - pb;\n\t\t\t\t\t\tp2.x = p1.x;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\ttick.set(\"points\", [p0, p1, p2]);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tlet link = dataItem.get(\"link\");\n\t\tif (link) {\n\t\t\tthis.links.removeValue(link);\n\t\t\tlink.dispose();\n\t\t}\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tconst slice = dataItem.get(\"slice\");\n\n\t\t\tif (slice) {\n\t\t\t\tconst width = slice.width();\n\t\t\t\tconst height = slice.height();\n\t\t\t\tconst locationX = bullet.get(\"locationX\", 0.5);\n\t\t\t\tconst locationY = bullet.get(\"locationY\", 0.5);\n\n\t\t\t\tlet dx = 0;\n\t\t\t\tlet dy = 0;\n\t\t\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\t\t\tdy = height / 2\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tdx = width / 2\n\t\t\t\t}\n\n\t\t\t\tsprite.setAll({ x: slice.x() + width * locationX - dx, y: slice.y() - dy + height * locationY });\n\t\t\t}\n\t\t}\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport { FunnelSeries, IFunnelSeriesSettings, IFunnelSeriesDataItem, IFunnelSeriesPrivate } from \"./FunnelSeries\";\nimport { Percent, p100 } from \"../../core/util/Percent\";\nimport * as $utils from \"../../core/util/Utils\";\nimport * as $type from \"../../core/util/Type\";\n\nexport interface IPyramidSeriesDataItem extends IFunnelSeriesDataItem {\n\n}\n\nexport interface IPyramidSeriesSettings extends IFunnelSeriesSettings {\n\n\t/**\n\t * The width of the tip of the pyramid.\n\t *\n\t * Can either be a fixed pixel value or percent relative to the space\n\t * available to the series.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pyramid-series/#Tip_and_base} for more info\n\t * @default 0\n\t */\n\ttopWidth?: number | Percent;\n\n\t/**\n\t * The width of the base of the pyramid.\n\t *\n\t * Can either be a fixed pixel value or percent relative to the space\n\t * available to the series.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pyramid-series/#Tip_and_base} for more info\n\t * @default 0\n\t */\n\tbottomWidth?: number | Percent;\n\n\t/**\n\t * Determines calculation mechanism for the slice area based on value.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pyramid-series/#Slice_size} for more info\n\t * @default \"area\"\n\t */\n\tvalueIs?: \"area\" | \"height\";\n\n}\n\nexport interface IPyramidSeriesPrivate extends IFunnelSeriesPrivate {\n}\n\n/**\n * Creates a pyramid series for use in a [[SlicedChart]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pyramid-series/} for more info\n * @important\n */\nexport class PyramidSeries extends FunnelSeries {\n\tprotected _tag = \"pyramid\";\n\n\tpublic static className: string = \"PyramidSeries\";\n\tpublic static classNames: Array<string> = FunnelSeries.classNames.concat([PyramidSeries.className]);\n\n\tdeclare public _settings: IPyramidSeriesSettings;\n\tdeclare public _privateSettings: IPyramidSeriesPrivate;\n\tdeclare public _dataItemSettings: IPyramidSeriesDataItem;\n\n\tprotected _nextSize: number | undefined;\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\t\tthis._nextSize = undefined;\n\t}\n\n\tprotected decorateSlice(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst orientation = this.get(\"orientation\");\n\t\tconst slicesContainer = this.slicesContainer;\n\n\t\tconst slice = dataItem.get(\"slice\");\n\t\tconst label = dataItem.get(\"label\");\n\t\tconst link = dataItem.get(\"link\");\n\t\tconst valueIs = this.get(\"valueIs\", \"area\");\n\n\t\tconst sum = this.getPrivate(\"valueAbsoluteSum\", 0);\n\n\t\tif (sum == 0) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst startLocation = this.get(\"startLocation\", 0);\n\t\tconst endLocation = this.get(\"endLocation\", 1);\n\n\t\tconst tw = this.get(\"topWidth\", 0);\n\t\tconst bw = this.get(\"bottomWidth\", p100);\n\n\t\tconst workingValue = Math.abs(dataItem.get(\"valueWorking\", 0));\n\t\tconst value = dataItem.get(\"value\", 0);\n\n\t\tlet sliceHeight: number;\n\t\tlet sliceBottomWidth: number;\n\n\t\tlet pyramidHeight = slicesContainer.innerHeight();\n\t\tlet pyramidWidth = slicesContainer.innerWidth();\n\t\tlet linkWidth = link.width();\n\t\tlet linkHeight = link.height();\n\n\t\tif (orientation == \"horizontal\") {\n\t\t\t[pyramidWidth, pyramidHeight] = [pyramidHeight, pyramidWidth];\n\t\t\t[linkWidth, linkHeight] = [linkHeight, linkWidth];\n\t\t}\n\n\t\tconst center = pyramidWidth / 2;\n\n\t\tlet d = 1;\n\t\tif (value != 0) {\n\t\t\td = workingValue / Math.abs(value);\n\t\t}\n\t\telse {\n\t\t\tif (dataItem.isHidden()) {\n\t\t\t\td = 0.000001;\n\t\t\t}\n\t\t}\n\n\t\tlinkHeight *= d;\n\n\t\tpyramidHeight = pyramidHeight * (endLocation - startLocation) - linkHeight * (this._count * this._total - 1);\n\n\t\tlet topWidth = $utils.relativeToValue(tw, pyramidWidth);\n\n\t\tif (!$type.isNumber(this._nextSize)) {\n\t\t\tthis._nextSize = topWidth;\n\t\t}\n\n\t\tlet bottomWidth = $utils.relativeToValue(bw, pyramidWidth);\n\t\tlet sliceTopWidth = this._nextSize;\n\n\t\tlet angle = Math.atan2(pyramidHeight, topWidth - bottomWidth);\n\t\tlet c = Math.tan(Math.PI / 2 - angle);\n\t\tif (c == 0) {\n\t\t\tc = 0.00000001;\n\t\t}\n\n\t\tif (valueIs == \"area\") {\n\t\t\tlet totalSquare = (topWidth + bottomWidth) / 2 * pyramidHeight;\n\t\t\tlet square = totalSquare * workingValue / sum;\n\n\t\t\tlet s = Math.abs(sliceTopWidth * sliceTopWidth - 2 * square * c);\n\n\t\t\tsliceHeight = (sliceTopWidth - Math.sqrt(s)) / c;\n\n\t\t\tif (sliceHeight > 0) {\n\t\t\t\tsliceBottomWidth = (2 * square - sliceHeight * sliceTopWidth) / sliceHeight;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tsliceBottomWidth = sliceTopWidth;\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tsliceHeight = pyramidHeight * workingValue / sum;\n\t\t\tsliceBottomWidth = sliceTopWidth - sliceHeight * c;\n\t\t}\n\n\t\tlet labelCoord = this._nextCoord + sliceHeight / 2;\n\t\tlet sliceX = center;\n\t\tlet sliceY = this._nextCoord;\n\n\t\tlet linkX = center;\n\t\tlet linkY = sliceY + sliceHeight;\n\n\t\tif (orientation == \"vertical\") {\n\t\t\tlabel.set(\"y\", labelCoord);\n\t\t\tif (label.get(\"opacity\") > 0) {\n\t\t\t\tthis._rLabels.push({ label: label, y: labelCoord });\n\t\t\t}\n\t\t\tslice.set(\"height\", sliceHeight);\n\t\t}\n\t\telse {\n\t\t\tlabel.set(\"x\", labelCoord);\n\t\t\tif (label.get(\"opacity\") > 0) {\n\t\t\t\tthis._hLabels.push({ label: label, y: labelCoord });\n\t\t\t}\n\t\t\t[sliceX, sliceY] = [sliceY, sliceX];\n\t\t\t[linkX, linkY] = [linkY, linkX];\n\n\t\t\tslice.set(\"width\", sliceHeight);\n\t\t}\n\n\t\tslice.setAll({ orientation, bottomWidth: sliceBottomWidth, topWidth: sliceTopWidth, x: sliceX, y: sliceY });\n\t\tlink.setAll({ orientation, x: linkX, y: linkY, topWidth: sliceBottomWidth, bottomWidth: sliceBottomWidth });\n\n\t\tthis._nextSize = sliceBottomWidth;\n\t\tthis._nextCoord += sliceHeight + linkHeight;\n\t}\n}\n", "import { PyramidSeries, IPyramidSeriesSettings, IPyramidSeriesDataItem, IPyramidSeriesPrivate } from \"./PyramidSeries\";\nimport { Graphics } from \"../../core/render/Graphics\";\nimport { p100, p50 } from \"../../core/util/Percent\"\n\n\nexport interface IPictorialStackedSeriesDataItem extends IPyramidSeriesDataItem {\n\n}\n\nexport interface IPictorialStackedSeriesSettings extends IPyramidSeriesSettings {\n\n\t/**\n\t * An SVG path that will define the shape of the pictorial series.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pictorial-stacked-series/#Shape_of_the_series} for more info\n\t */\n\tsvgPath?: string;\n\n}\n\nexport interface IPictorialStackedSeriesPrivate extends IPyramidSeriesPrivate {\n}\n\n/**\n * Creates a pictorial series for use in a [[SlicedChart]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/sliced-chart/pictorial-stacked-series/} for more info\n * @important\n */\nexport class PictorialStackedSeries extends PyramidSeries {\n\tprotected _tag = \"pictorial\";\n\n\tpublic static className: string = \"PictorialStackedSeries\";\n\tpublic static classNames: Array<string> = PyramidSeries.classNames.concat([PictorialStackedSeries.className]);\n\n\tdeclare public _settings: IPictorialStackedSeriesSettings;\n\tdeclare public _privateSettings: IPictorialStackedSeriesPrivate;\n\tdeclare public _dataItemSettings: IPictorialStackedSeriesDataItem;\n\n\t/**\n\t * A [[Graphics]] element to used as a mask (shape) for the series.\n\t *\n\t * This element is read-only. To modify the mask/shape, use the `svgPath` setting.\n\t */\n\tpublic readonly seriesMask: Graphics = Graphics.new(this._root, { position: \"absolute\", x: p50, y: p50, centerX: p50, centerY: p50 });\n\n\tpublic readonly seriesGraphics: Graphics = this.slicesContainer.children.push(Graphics.new(this._root, { themeTags: [\"pictorial\", \"background\"], position: \"absolute\", x: p50, y: p50, centerX: p50, centerY: p50 }));\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.set(\"topWidth\", p100);\n\t\tthis.set(\"bottomWidth\", p100);\n\t\tthis.set(\"valueIs\", \"height\");\n\t\tthis.slicesContainer.set(\"mask\", this.seriesMask);\n\t}\n\n\tprotected _updateScale() {\n\t\tlet slicesContainer = this.slicesContainer;\n\n\t\tlet w = slicesContainer.innerWidth();\n\t\tlet h = slicesContainer.innerHeight();\n\n\t\tlet seriesMask = this.seriesMask;\n\t\tlet seriesGraphics = this.seriesGraphics;\n\t\tlet scale = seriesMask.get(\"scale\", 1);\n\n\t\tconst bounds = seriesMask.localBounds();\n\n\t\tlet mw = bounds.right - bounds.left;\n\t\tlet mh = bounds.bottom - bounds.top;\n\t\tif (this.get(\"orientation\") == \"horizontal\") {\n\t\t\tscale = w / mw;\n\t\t}\n\t\telse {\n\t\t\tscale = h / mh;\n\t\t}\n\t\tif (scale != Infinity && scale != NaN) {\n\t\t\tseriesMask.set(\"scale\", scale);\n\t\t\tseriesMask.set(\"x\", w / 2);\n\t\t\tseriesMask.set(\"y\", h / 2);\n\n\t\t\tseriesGraphics.set(\"scale\", scale);\n\t\t\tseriesGraphics.set(\"x\", w / 2);\n\t\t\tseriesGraphics.set(\"y\", h / 2);\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\t\tif (this.isDirty(\"svgPath\")) {\n\t\t\tconst svgPath = this.get(\"svgPath\");\n\t\t\tthis.seriesMask.set(\"svgPath\", svgPath);\n\t\t\tthis.seriesGraphics.set(\"svgPath\", svgPath);\n\t\t}\n\n\t\tthis._updateScale();\n\t}\n\n}\n", "import { PercentChart, IPercentChartPrivate, IPercentChartSettings } from \"../percent/PercentChart\";\nimport type { PercentSeries } from \"../percent/PercentSeries\";\n\n\nexport interface ISlicedChartSettings extends IPercentChartSettings {\n}\n\nexport interface ISlicedChartPrivate extends IPercentChartPrivate {\n}\n\n/**\n * Creates a sliced chart for use with [[FunnelSeries]], [[PyramidSeries]], or [[PictorialStackedSeries]].\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/pie-chart/} for more info\n * @important\n */\nexport class SlicedChart extends PercentChart {\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\n\t\tthis.seriesContainer.setAll({ isMeasured:true, layout: this._root.horizontalLayout });\n\t}\n\n\tpublic static className: string = \"SlicedChart\";\n\tpublic static classNames: Array<string> = PercentChart.classNames.concat([SlicedChart.className]);\n\n\tdeclare public _settings: ISlicedChartSettings;\n\tdeclare public _privateSettings: ISlicedChartPrivate;\n\tdeclare public _seriesType: PercentSeries;\n}\n", "import * as m from \"./../../dist/es2015/percent.js\";\nexport const am5percent = m;"], "names": ["PercentDefaultTheme", "Theme", "setupDefaultRules", "super", "ic", "this", "_root", "interfaceColors", "r", "rule", "bind", "setAll", "legendLabelText", "legendValueText", "colors", "ColorSet", "new", "width", "height", "radius", "startAngle", "endAngle", "align<PERSON><PERSON><PERSON>", "states", "create", "opacity", "position", "isMeasured", "x", "y", "to<PERSON><PERSON><PERSON>", "tooltipText", "strokeWidth", "strokeOpacity", "role", "lineJoin", "shiftRadius", "scale", "textType", "text", "paddingTop", "paddingBottom", "populateText", "location", "paddingLeft", "paddingRight", "startLocation", "endLocation", "orientation", "sequencedInterpolation", "interactive", "expandDistance", "centerY", "centerX", "rotation", "fillOpacity", "valueIs", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_afterNew", "_defaultThemes", "push", "chartContainer", "children", "seriesContainer", "bulletsContainer", "_processSeries", "series", "moveValue", "length", "classNames", "concat", "className", "PercentSeries", "Series", "Container", "_makeSlices", "_make<PERSON><PERSON><PERSON>", "_makeTicks", "makeSlice", "dataItem", "slice", "slicesContainer", "slices", "make", "on", "updateLegendMarker", "_setDataItem", "set", "<PERSON><PERSON><PERSON><PERSON>", "label", "labelsContainer", "labels", "_should<PERSON><PERSON><PERSON><PERSON><PERSON>", "get", "makeTick", "tick", "ticksContainer", "ticks", "fields", "_onDataClear", "reset", "patterns", "_prepare<PERSON><PERSON><PERSON><PERSON>", "_l<PERSON><PERSON><PERSON>", "_r<PERSON><PERSON><PERSON>", "_h<PERSON><PERSON><PERSON>", "_valuesDirty", "sum", "absSum", "valueHigh", "valueLow", "Infinity", "count", "_dataItems", "valueWorking", "Math", "abs", "value", "percentTotal", "setRaw", "setPrivateRaw", "show", "duration", "promises", "_sequencedShowHide", "Promise", "all", "hide", "_update<PERSON><PERSON><PERSON>n", "markDirtyText", "isDirty", "updateLegendValue", "_arrange", "_arrangeDown", "_arrangeUp", "_arrangeLeft", "_arrangeRight", "dataItems", "_updateTick", "_afterChanged", "processDataItem", "next", "showDataItem", "easing", "animation", "animate", "key", "to", "waitForStop", "applyAnimate", "hideDataItem", "hiddenState", "hideTooltip", "disposeDataItem", "removeValue", "dispose", "hoverDataItem", "isHidden", "hover", "unhoverDataItem", "unhover", "legendDataItem", "markerRectangle", "setting", "_getNextDown", "sort", "a", "b", "l", "bounds", "adjustedLocalBounds", "labelTop", "top", "bottom", "_getNextUp", "maxHeight", "labelBottom", "labelLeft", "left", "right", "max<PERSON><PERSON><PERSON>", "labelRight", "_updateSize", "<PERSON><PERSON><PERSON><PERSON>", "_dataItem", "_dispose", "chart", "<PERSON><PERSON><PERSON>", "w", "innerWidth", "h", "innerHeight", "innerRadius", "wr", "hr", "innerBounds", "Percent", "mr", "min", "max", "prevRadius", "_maxRadius", "dy", "dx", "each", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "index", "indexOf", "seriesRadius", "seriesInnerRadius", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PieSeries", "List", "Template", "Slice", "_new", "themeTags", "template", "RadialLabel", "Tick", "events", "labelsTemplate", "markDirtyBounds", "arc", "currentAngle", "getPrivate", "currentArc", "color", "_setDefault", "fillPattern", "middleAngle", "setPrivate", "labelRadius", "isHiding", "labelAngle", "cos", "sin", "pl", "pr", "points", "_positionBullet", "bullet", "sprite", "angle", "FunnelSlice", "Graphics", "display", "moveTo", "_tlx", "_tly", "lineTo", "_trx", "_try", "quadraticCurveTo", "_cprx", "_cpry", "_brx", "_bry", "_blx", "_bly", "_cplx", "_cply", "getPoint", "locationX", "locationY", "tw", "bw", "tlx", "trx", "mlx", "_changed", "_clear", "ed", "FunnelSeries", "_makeLinks", "_tag", "Label", "links", "makeLink", "link", "onPrivate", "markDirtySize", "horizontalLayout", "verticalLayout", "_opposite", "total", "_total", "_count", "_fixLayout", "_sizeDirty", "_nextCoord", "i", "decorateSlice", "isLast", "getNextValue", "nextValue", "nextItem", "maxSize", "workingValue", "bottomRatio", "d", "topWidth", "bottomWidth", "linkHeight", "labelY", "labelX", "tickLocation", "lcw", "lch", "pt", "pb", "p0", "p1", "p2", "PyramidSeries", "_nextSize", "undefined", "sliceHeight", "sliceBottomWidth", "pyramidHeight", "pyramidWidth", "linkWidth", "center", "sliceTopWidth", "atan2", "c", "tan", "PI", "square", "s", "sqrt", "labelCoord", "sliceX", "sliceY", "linkX", "linkY", "PictorialStackedSeries", "seriesMask", "_updateScale", "seriesGraphics", "localBounds", "mw", "mh", "NaN", "svgPath", "SlicedChart", "layout", "am5percent"], "sourceRoot": ""}