{"version": 3, "file": "themes/Moonrise.js", "mappings": "kLAMO,MAAMA,UAAsBC,EAAA,EACxB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CACPC,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UAEfC,OAAO,GAET,ECxBM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/MoonriseTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Moonrise.js", "webpack://@amcharts/amcharts5/./src/themes/Moonrise.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class MoonriseTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [\n\t\t\t\tColor.fromHex(0x3a1302),\n\t\t\t\tColor.fromHex(0x601205),\n\t\t\t\tColor.fromHex(0x8a2b0d),\n\t\t\t\tColor.fromHex(0xc75e24),\n\t\t\t\tColor.fromHex(0xc79f59),\n\t\t\t\tColor.fromHex(0xa4956a),\n\t\t\t\tColor.fromHex(0x868569),\n\t\t\t\tColor.fromHex(0x756f61),\n\t\t\t\tColor.fromHex(0x586160),\n\t\t\t\tColor.fromHex(0x617983)\n\t\t\t],\n\t\t\treuse: true\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Moonrise.js\";\nexport const am5themes_Moonrise = m;", "import { MoonriseTheme } from \"../.internal/themes/MoonriseTheme\";\nexport default MoonriseTheme;"], "names": ["MoonriseTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "am5themes_Moonrise"], "sourceRoot": ""}