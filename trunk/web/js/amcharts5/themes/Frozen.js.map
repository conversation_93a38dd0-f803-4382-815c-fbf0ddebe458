{"version": 3, "file": "themes/Frozen.js", "mappings": "+KAMO,MAAMA,UAAoBC,EAAA,EACtB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CACPC,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,WAEfC,OAAO,GAET,ECxBM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/FrozenTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Frozen.js", "webpack://@amcharts/amcharts5/./src/themes/Frozen.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class FrozenTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [\n\t\t\t\tColor.fromHex(0xbec4f8),\n\t\t\t\tColor.fromHex(0xa5abee),\n\t\t\t\tColor.fromHex(0x6a6dde),\n\t\t\t\tColor.fromHex(0x4d42cf),\n\t\t\t\tColor.fromHex(0x713e8d),\n\t\t\t\tColor.fromHex(0xa160a0),\n\t\t\t\tColor.fromHex(0xeb6eb0),\n\t\t\t\tColor.fromHex(0xf597bb),\n\t\t\t\tColor.fromHex(0xfbb8c9),\n\t\t\t\tColor.fromHex(0xf8d4d8)\n\t\t\t],\n\t\t\treuse: true\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Frozen.js\";\nexport const am5themes_Frozen = m;", "import { FrozenTheme } from \"../.internal/themes/FrozenTheme\";\nexport default FrozenTheme;"], "names": ["FrozenTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "am5themes_Frozen"], "sourceRoot": ""}