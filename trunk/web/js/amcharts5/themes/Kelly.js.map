{"version": 3, "file": "themes/Kelly.js", "mappings": "+KAMO,MAAMA,UAAmBC,EAAA,EACrB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CACPC,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,OACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,OACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UAEfC,OAAO,GAET,ECpCM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/KellyTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Kelly.js", "webpack://@amcharts/amcharts5/./src/themes/Kelly.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class KellyTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [\n\t\t\t\tColor.fromHex(0xF3C300),\n\t\t\t\tColor.fromHex(0x875692),\n\t\t\t\tColor.fromHex(0xF38400),\n\t\t\t\tColor.fromHex(0xA1CAF1),\n\t\t\t\tColor.fromHex(0xBE0032),\n\t\t\t\tColor.fromHex(0xC2B280),\n\t\t\t\tColor.fromHex(0x848482),\n\t\t\t\tColor.fromHex(0x008856),\n\t\t\t\tColor.fromHex(0xE68FAC),\n\t\t\t\tColor.fromHex(0x0067A5),\n\t\t\t\tColor.fromHex(0xF99379),\n\t\t\t\tColor.fromHex(0x604E97),\n\t\t\t\tColor.fromHex(0xF6A600),\n\t\t\t\tColor.fromHex(0xB3446C),\n\t\t\t\tColor.fromHex(0xDCD300),\n\t\t\t\tColor.fromHex(0x882D17),\n\t\t\t\tColor.fromHex(0x8DB600),\n\t\t\t\tColor.fromHex(0x654522),\n\t\t\t\tColor.fromHex(0xE25822),\n\t\t\t\tColor.fromHex(0x2B3D26),\n\t\t\t\tColor.fromHex(0xF2F3F4),\n\t\t\t\tColor.fromHex(0x222222)\n\t\t\t],\n\t\t\treuse: true\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Kelly.js\";\nexport const am5themes_Kelly = m;", "import { KellyTheme } from \"../.internal/themes/KellyTheme\";\nexport default KellyTheme;"], "names": ["Kelly<PERSON><PERSON>e", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "am5them<PERSON>_<PERSON>"], "sourceRoot": ""}