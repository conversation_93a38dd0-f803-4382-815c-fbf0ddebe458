"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[3052],{6040:function(e,t,r){r.r(t),r.d(t,{am5themes_Dark:function(){return a}});var o=r(1112),l=r(3409);class n extends l.Q{setupDefaultRules(){super.setupDefaultRules(),this.rule("InterfaceColors").setAll({stroke:o.Il.fromHex(0),fill:o.Il.fromHex(2829099),primaryButton:o.<PERSON>.lighten(o.Il.fromHex(6788316),-.2),primaryButtonHover:o.Il.lighten(o.Il.fromHex(6779356),-.2),primaryButtonDown:o.Il.lighten(o.Il.fromHex(6872181),-.2),primaryButtonActive:o.Il.lighten(o.Il.fromHex(6872182),-.2),primaryButtonText:o.<PERSON>.fromHex(16777215),primaryButtonStroke:o.Il.lighten(o.Il.fromHex(6788316),-.2),secondaryButton:o.Il.fromHex(3881787),secondaryButtonHover:o.Il.lighten(o.Il.fromHex(3881787),.1),secondaryButtonDown:o.Il.lighten(o.Il.fromHex(3881787),.15),secondaryButtonActive:o.Il.lighten(o.Il.fromHex(3881787),.2),secondaryButtonText:o.Il.fromHex(12303291),secondaryButtonStroke:o.Il.lighten(o.Il.fromHex(3881787),-.2),grid:o.Il.fromHex(12303291),background:o.Il.fromHex(0),alternativeBackground:o.Il.fromHex(16777215),text:o.Il.fromHex(16777215),alternativeText:o.Il.fromHex(0),disabled:o.Il.fromHex(11382189),positive:o.Il.fromHex(5288704),negative:o.Il.fromHex(11730944)})}}const a=n}},function(e){var t=(6040,e(e.s=6040)),r=window;for(var o in t)r[o]=t[o];t.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})}]);
//# sourceMappingURL=Dark.js.map