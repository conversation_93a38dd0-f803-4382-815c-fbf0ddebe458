"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[6664],{5571:function(e,i,t){t.r(i),t.d(i,{am5themes_Micro:function(){return a}});var l=t(3409),s=t(6245);class d extends l.Q{setupDefaultRules(){super.setupDefaultRules(),this.rule("Chart").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("XYChart").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("SlicedChart").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("Tree").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("Pack").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("Flow").setAll({paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0}),this.rule("Button",["resize"]).setAll({visible:!1}),this.rule("Button",["zoom"]).setAll({scale:.5}),this.rule("Axis").setAll({visible:!1}),this.rule("Label",["axis"]).setAll({visible:!1}),this.rule("Grid").setAll({visible:!1}),this.rule("Tooltip",["axis"]).setAll({visible:!1}),this.rule("PieChart").setAll({radius:(0,s.aQ)(99)}),this.rule("Label",["pie"]).setAll({visible:!1}),this.rule("Tick",["pie"]).setAll({visible:!1})}}const a=d}},function(e){var i=(5571,e(e.s=5571)),t=window;for(var l in i)t[l]=i[l];i.__esModule&&Object.defineProperty(t,"__esModule",{value:!0})}]);
//# sourceMappingURL=Micro.js.map