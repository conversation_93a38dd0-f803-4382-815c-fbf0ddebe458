"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[8382],{4700:function(e,l,r){r.r(l),r.d(l,{am5themes_Kelly:function(){return x}});var o=r(1112),f=r(3409);class m extends f.Q{setupDefaultRules(){super.setupDefaultRules(),this.rule("ColorSet").setAll({colors:[o.Il.fromHex(15975168),o.<PERSON>.fromHex(8869522),o.<PERSON>.fromHex(15959040),o.<PERSON>.fromHex(10603249),o.<PERSON>.fromHex(12451890),o.Il.fromHex(12759680),o.<PERSON>.fromHex(8684674),o.Il.fromHex(34902),o.Il.fromHex(15110060),o.Il.fromHex(26533),o.Il.fromHex(16356217),o.<PERSON>.fromHex(6311575),o.<PERSON>.fromHex(16164352),o.<PERSON>.fromHex(11748460),o.<PERSON><PERSON>fromHex(14471936),o.<PERSON>.fromHex(8924439),o.<PERSON>.fromHex(9287168),o.Il.fromHex(6636834),o.Il.fromHex(14833698),o.Il.fromHex(2833702),o.Il.fromHex(15922164),o.Il.fromHex(2236962)],reuse:!0})}}const x=m}},function(e){var l=(4700,e(e.s=4700)),r=window;for(var o in l)r[o]=l[o];l.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})}]);
//# sourceMappingURL=Kelly.js.map