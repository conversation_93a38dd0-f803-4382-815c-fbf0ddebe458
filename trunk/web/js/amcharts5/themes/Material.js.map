{"version": 3, "file": "themes/Material.js", "mappings": "kLAMO,MAAMA,UAAsBC,EAAA,EACxB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,YAAYC,OAAO,CAC5BC,OAAQ,CACPC,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,QACdD,EAAA,GAAMC,QAAQ,OACdD,EAAA,GAAMC,QAAQ,OACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,SACdD,EAAA,GAAMC,QAAQ,UACdD,EAAA,GAAMC,QAAQ,UAEfC,OAAO,GAET,ECjCM,MAAMC,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/MaterialTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Material.js", "webpack://@amcharts/amcharts5/./src/themes/Material.ts"], "sourcesContent": ["import { Color } from \"../core/util/Color\";\nimport { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class MaterialTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"ColorSet\").setAll({\n\t\t\tcolors: [\n\t\t\t\tColor.fromHex(0xF44336),\n\t\t\t\tColor.fromHex(0xE91E63),\n\t\t\t\tColor.fromHex(0x9C27B0),\n\t\t\t\tColor.fromHex(0x673AB7),\n\t\t\t\tColor.fromHex(0x3F51B5),\n\t\t\t\tColor.fromHex(0x2196F3),\n\t\t\t\tColor.fromHex(0x03A9F4),\n\t\t\t\tColor.fromHex(0x00BCD4),\n\t\t\t\tColor.fromHex(0x009688),\n\t\t\t\tColor.fromHex(0x4CAF50),\n\t\t\t\tColor.fromHex(0x8BC34A),\n\t\t\t\tColor.fromHex(0xCDDC39),\n\t\t\t\tColor.fromHex(0xFFEB3B),\n\t\t\t\tColor.fromHex(0xFFC107),\n\t\t\t\tColor.fromHex(0xFF9800),\n\t\t\t\tColor.fromHex(0xFF5722),\n\t\t\t\tColor.fromHex(0x795548),\n\t\t\t\tColor.fromHex(0x9E9E9E),\n\t\t\t\tColor.fromHex(0x607D8B)\n\t\t\t],\n\t\t\treuse: true\n\t\t});\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Material.js\";\nexport const am5themes_Material = m;", "import { MaterialTheme } from \"../.internal/themes/MaterialTheme\";\nexport default MaterialTheme;"], "names": ["MaterialTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "colors", "Color", "fromHex", "reuse", "am5themes_Material"], "sourceRoot": ""}