{"version": 3, "file": "themes/Animated.js", "mappings": "wKAKO,MAAMA,UAAsBC,EAAA,EACxB,iBAAAC,GACTC,MAAMD,oBAENE,KAAKC,KAAK,aAAaC,OAAO,CAC7BC,sBAAuB,MAGxBH,KAAKC,KAAK,aAAaG,IAAI,oBAAqB,KAChDJ,KAAKC,KAAK,aAAaG,IAAI,oBAAqB,KAEhDJ,KAAKC,KAAK,WAAWG,IAAI,oBAAqB,KAE9CJ,KAAKC,KAAK,YAAYG,IAAI,oBAAqB,KAC/CJ,KAAKC,KAAK,YAAYG,IAAI,gBAAiB,KAE3CJ,KAAKC,KAAK,UAAUC,OAAO,CAC1BG,uBAAwB,MAGzBL,KAAKC,KAAK,UAAUK,OAAOC,OAAO,UAAW,CAAEF,uBAAwB,MAEvEL,KAAKC,KAAK,UAAW,CAAC,SAASC,OAAO,CACrCM,kBAAmB,MAGpBR,KAAKC,KAAK,aAAaG,IAAI,oBAAqB,KAChDJ,KAAKC,KAAK,WAAWG,IAAI,oBAAqB,KAC9CJ,KAAKC,KAAK,cAAcG,IAAI,oBAAqB,IAClD,ECjCM,MAAMK,ECAb,C", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/themes/AnimatedTheme.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/themes/Animated.js", "webpack://@amcharts/amcharts5/./src/themes/Animated.ts"], "sourcesContent": ["import { Theme } from \"../core/Theme\";\n\n/**\n * @ignore\n */\nexport class AnimatedTheme extends Theme {\n\tprotected setupDefaultRules() {\n\t\tsuper.setupDefaultRules();\n\n\t\tthis.rule(\"Component\").setAll({\n\t\t\tinterpolationDuration: 600\n\t\t});\n\n\t\tthis.rule(\"Hierarchy\").set(\"animationDuration\", 600);\n\t\tthis.rule(\"Scrollbar\").set(\"animationDuration\", 600);\n\n\t\tthis.rule(\"Tooltip\").set(\"animationDuration\", 300);\n\n\t\tthis.rule(\"MapChart\").set(\"animationDuration\", 1000);\n\t\tthis.rule(\"MapChart\").set(\"wheelDuration\", 300);\n\n\t\tthis.rule(\"Entity\").setAll({\n\t\t\tstateAnimationDuration: 600\n\t\t});\n\n\t\tthis.rule(\"Sprite\").states.create(\"default\", { stateAnimationDuration: 600 });\n\n\t\tthis.rule(\"Tooltip\", [\"axis\"]).setAll({\n\t\t\tanimationDuration: 200\n\t\t});\n\n\t\tthis.rule(\"WordCloud\").set(\"animationDuration\", 500);\n\t\tthis.rule(\"Polygon\").set(\"animationDuration\", 600);\n\t\tthis.rule(\"ArcDiagram\").set(\"animationDuration\", 600);\n\t}\n}\n", "import m from \"./../../../dist/es2015/themes/Animated.js\";\nexport const am5themes_Animated = m;", "import { AnimatedTheme } from \"../.internal/themes/AnimatedTheme\";\nexport default AnimatedTheme;"], "names": ["AnimatedTheme", "Theme", "setupDefaultRules", "super", "this", "rule", "setAll", "interpolationDuration", "set", "stateAnimationDuration", "states", "create", "animationDuration", "am5themes_Animated"], "sourceRoot": ""}