{"version": 3, "file": "locales/en_CA.js", "mappings": "wJACO,MAAMA,ECOb,CAEC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAGlB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,aAChB,kBAAqB,aACrB,aAAgB,UAChB,kBAAqB,yBACrB,WAAc,UACd,gBAAmB,yBACnB,UAAa,SACb,eAAkB,eAClB,WAAc,KACd,gBAAmB,eACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAGd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,KACX,QAAW,KAGX,EAAK,GACL,EAAK,GACL,GAAM,GACN,GAAM,GACN,OAAQ,GACR,OAAQ,GAGR,QAAW,GACX,SAAY,GACZ,MAAS,GACT,MAAS,GACT,IAAO,GACP,KAAQ,GACR,KAAQ,GACR,OAAU,GACV,UAAa,GACb,QAAW,GACX,SAAY,GACZ,SAAY,GACZ,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,aAAc,MACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,OAAU,GACV,OAAU,GACV,QAAW,GACX,UAAa,GACb,SAAY,GACZ,OAAU,GACV,SAAY,GACZ,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GAGP,SAAY,SAASC,GACpB,IAAIC,EAAM,KACV,GAAKD,EAAM,IAAQA,EAAM,GACxB,OAAQA,EAAM,IACb,KAAK,EACJC,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KAIT,OAAOA,CACR,EAGA,KAAQ,GACR,KAAQ,GACR,WAAY,GACZ,OAAU,GACV,wBAAyB,GACzB,QAAW,GACX,KAAQ,GAGR,MAAS,GACT,eAAgB,GAChB,YAAa,GACb,YAAa,GACb,cAAe,GACf,cAAe,GACf,iBAAkB,GAClB,gBAAiB,GACjB,eAAgB,GAChB,gBAAiB,GAGjB,OAAU,GACV,qBAAsB,GACtB,gBAAiB,GACjB,cAAe,GACf,mBAAoB,GACpB,aAAc,GAGd,IAAO,GACP,yBAA0B,GAC1B,0BAA2B,GAC3B,oCAAqC,GACrC,8DAA+D,GAG/D,OAAU,GACV,MAAS,GACT,KAAQ,GACR,MAAS,GACT,sBAAuB,GACvB,wBAAyB,GACzB,+BAAgC,GAChC,oCAAqC,GACrC,wBAAyB,GACzB,+EAAgF,GAChF,aAAc,GACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAGR,8EAA+E,GAC/E,8CAA+C,GAC/C,mDAAoD,GACpD,oDAAqD,GACrD,wEAAyE,GACzE,2CAA4C,GAC5C,iDAAkD,GAClD,iDAAkD,GAClD,gBAAiB,GACjB,UAAW,GACX,QAAS,GAGT,mCAAoC,GACpC,yBAA0B,GAC1B,0BAA2B,GAC3B,eAAgB,G", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/en_CA.js", "webpack://@amcharts/amcharts5/./src/locales/en_CA.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/en_CA.js\";\nexport const am5locales_en_CA = m;", "/**\n * amCharts 5 locale\n * \n * Locale: en_CA\n * Language: Canadian English\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \".\",\n\t\"_thousandSeparator\": \",\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"hh:mm:ss a\",\n\t\"_date_second_full\": \"hh:mm:ss a\",\n\t\"_date_minute\": \"hh:mm a\",\n\t\"_date_minute_full\": \"hh:mm a - MMM dd, yyyy\",\n\t\"_date_hour\": \"hh:mm a\",\n\t\"_date_hour_full\": \"hh:mm a - MMM dd, yyyy\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_day_full\": \"MMM dd, yyyy\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"MMM dd, yyyy\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"AD\",\n\t\"_era_bc\": \"BC\",\n\n\t// Period\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Dates\n\t\"January\": \"\",\n\t\"February\": \"\",\n\t\"March\": \"\",\n\t\"April\": \"\",\n\t\"May\": \"\",\n\t\"June\": \"\",\n\t\"July\": \"\",\n\t\"August\": \"\",\n\t\"September\": \"\",\n\t\"October\": \"\",\n\t\"November\": \"\",\n\t\"December\": \"\",\n\t\"Jan\": \"\",\n\t\"Feb\": \"\",\n\t\"Mar\": \"\",\n\t\"Apr\": \"\",\n\t\"May(short)\": \"May\",\n\t\"Jun\": \"\",\n\t\"Jul\": \"\",\n\t\"Aug\": \"\",\n\t\"Sep\": \"\",\n\t\"Oct\": \"\",\n\t\"Nov\": \"\",\n\t\"Dec\": \"\",\n\t\"Sunday\": \"\",\n\t\"Monday\": \"\",\n\t\"Tuesday\": \"\",\n\t\"Wednesday\": \"\",\n\t\"Thursday\": \"\",\n\t\"Friday\": \"\",\n\t\"Saturday\": \"\",\n\t\"Sun\": \"\",\n\t\"Mon\": \"\",\n\t\"Tue\": \"\",\n\t\"Wed\": \"\",\n\t\"Thu\": \"\",\n\t\"Fri\": \"\",\n\t\"Sat\": \"\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(day: number): string {\n\t\tlet res = \"th\";\n\t\tif ((day < 11) || (day > 13)) {\n\t\t\tswitch (day % 10) {\n\t\t\t\tcase 1:\n\t\t\t\t\tres = \"st\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tres = \"nd\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tres = \"rd\"\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Chart elements\n\t\"Play\": \"\",\n\t\"Stop\": \"\",\n\t\"Zoom Out\": \"\",\n\t\"Legend\": \"\",\n\t\"Press ENTER to toggle\": \"\",\n\t\"Loading\": \"\",\n\t\"Home\": \"\",\n\n\t// Chart types\n\t\"Chart\": \"\",\n\t\"Serial chart\": \"\",\n\t\"X/Y chart\": \"\",\n\t\"Pie chart\": \"\",\n\t\"Gauge chart\": \"\",\n\t\"Radar chart\": \"\",\n\t\"Sankey diagram\": \"\",\n\t\"Chord diagram\": \"\",\n\t\"Flow diagram\": \"\",\n\t\"TreeMap chart\": \"\",\n\n\t// Series types\n\t\"Series\": \"\",\n\t\"Candlestick Series\": \"\",\n\t\"Column Series\": \"\",\n\t\"Line Series\": \"\",\n\t\"Pie Slice Series\": \"\",\n\t\"X/Y Series\": \"\",\n\n\t// Map-related\n\t\"Map\": \"\",\n\t\"Press ENTER to zoom in\": \"\",\n\t\"Press ENTER to zoom out\": \"\",\n\t\"Use arrow keys to zoom in and out\": \"\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"\",\n\n\t// Export-related\n\t\"Export\": \"\",\n\t\"Image\": \"\",\n\t\"Data\": \"\",\n\t\"Print\": \"\",\n\t\"Press ENTER to open\": \"\",\n\t\"Press ENTER to print.\": \"\",\n\t\"Press ENTER to export as %1.\": \"\",\n\t\"(Press ESC to close this message)\": \"\",\n\t\"Image Export Complete\": \"\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n\t\"Saved from\": \"\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n\t\"Use left and right arrows to move selection\": \"\",\n\t\"Use left and right arrows to move left selection\": \"\",\n\t\"Use left and right arrows to move right selection\": \"\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n\t\"Use up and down arrows to move selection\": \"\",\n\t\"Use up and down arrows to move lower selection\": \"\",\n\t\"Use up and down arrows to move upper selection\": \"\",\n\t\"From %1 to %2\": \"\",\n\t\"From %1\": \"\",\n\t\"To %1\": \"\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"\",\n\t\"Error parsing file: %1\": \"\",\n\t\"Unable to load file: %1\": \"\",\n\t\"Invalid date\": \"\",\n};\n"], "names": ["am5locales_en_CA", "day", "res"], "sourceRoot": ""}