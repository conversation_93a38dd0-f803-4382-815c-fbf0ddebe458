{"version": 3, "file": "locales/el_GR.js", "mappings": "wJACO,MAAMA,ECuDb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAUlB,qBAAwB,IACxB,qBAAwB,IACxB,qBAAwB,IACxB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IAEzB,uBAA0B,IAC1B,uBAA0B,IAC1B,uBAA0B,IAC1B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAE3B,eAAkB,IAClB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KAWnB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,uBACrB,WAAc,QACd,gBAAmB,uBACnB,UAAa,SACb,eAAkB,eAClB,WAAc,KACd,gBAAmB,eACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAqBd,sBAAyB,MACzB,6BAAgC,SAChC,6BAAgC,YAChC,2BAA8B,eAC9B,0BAA6B,iBAC7B,2BAA8B,iBAC9B,4BAA+B,uBAC/B,2BAA8B,6BAE9B,iBAAoB,KACpB,wBAA2B,QAC3B,sBAAyB,WACzB,qBAAwB,gBACxB,sBAAyB,gBACzB,uBAA0B,sBAC1B,sBAAyB,4BAEzB,iBAAoB,KACpB,sBAAyB,QACzB,qBAAwB,aACxB,sBAAyB,aACzB,uBAA0B,mBAC1B,sBAAyB,yBAEzB,eAAkB,QAClB,mBAAsB,aACtB,oBAAuB,aACvB,qBAAwB,mBACxB,oBAAuB,yBAEvB,cAAiB,OACjB,mBAAsB,OACtB,oBAAuB,aACvB,mBAAsB,mBAEtB,eAAkB,OAClB,qBAAwB,OACxB,oBAAuB,OAEvB,gBAAmB,OACnB,qBAAwB,aAExB,eAAkB,OAGlB,QAAW,OACX,QAAW,OAUX,EAAK,KACL,EAAK,KACL,GAAM,OACN,GAAM,OACN,OAAQ,OACR,OAAQ,OAoBR,QAAW,aACX,SAAY,cACZ,MAAS,UACT,MAAS,WACT,IAAO,QACP,KAAQ,UACR,KAAQ,UACR,OAAU,YACV,UAAa,cACb,QAAW,YACX,SAAY,YACZ,SAAY,aACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,aAAc,MACd,IAAO,OACP,IAAO,OACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAGP,OAAU,UACV,OAAU,UACV,QAAW,QACX,UAAa,UACb,SAAY,SACZ,OAAU,YACV,SAAY,UACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAWP,SAAY,SAASC,GACpB,IAAIC,EAAM,KACV,GAAKD,EAAM,IAAQA,EAAM,GACxB,OAAQA,EAAM,IACb,KAAK,EACJC,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KACN,MACD,KAAK,EACJA,EAAM,KAIT,OAAOA,CACR,EAIA,WAAY,UAGZ,KAAQ,cACR,KAAQ,QAGR,OAAU,WAGV,wBAAyB,GAGzB,QAAW,UAIX,KAAQ,SAKR,MAAS,GACT,eAAgB,GAChB,YAAa,GACb,YAAa,GACb,cAAe,GACf,cAAe,GACf,iBAAkB,GAClB,eAAgB,GAChB,gBAAiB,GACjB,gBAAiB,GACjB,eAAgB,GAKhB,OAAU,GACV,qBAAsB,GACtB,cAAe,GACf,gBAAiB,GACjB,cAAe,GACf,mBAAoB,GACpB,gBAAiB,GACjB,iBAAkB,GAClB,aAAc,GAGd,IAAO,GACP,yBAA0B,GAC1B,0BAA2B,GAC3B,oCAAqC,GACrC,8DAA+D,GAY/D,OAAU,WACV,MAAS,QACT,KAAQ,WACR,MAAS,WACT,sBAAuB,GACvB,wBAAyB,GACzB,+BAAgC,GAChC,oCAAqC,GACrC,wBAAyB,GACzB,+EAAgF,GAChF,aAAc,GACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAYR,8EAA+E,GAC/E,8CAA+C,GAC/C,mDAAoD,GACpD,oDAAqD,GACrD,wEAAyE,GACzE,2CAA4C,GAC5C,iDAAkD,GAClD,iDAAkD,GAClD,gBAAiB,gBACjB,UAAW,SACX,QAAS,SAGT,mCAAoC,GACpC,yBAA0B,GAC1B,0BAA2B,GAC3B,eAAgB,G", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/el_GR.js", "webpack://@amcharts/amcharts5/./src/locales/el_GR.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/el_GR.js\";\nexport const am5locales_el_GR = m;", "/**\n * amCharts 5 locale\n *\n * Locale: el_GR\n * Language: Greek\n * Author: <PERSON><PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Suffixes for numbers\n\t// When formatting numbers, big or small numers might be reformatted to\n\t// shorter version, by applying a suffix.\n\t//\n\t// For example, 1000000 might become \"1m\".\n\t// Or 1024 might become \"1KB\" if we're formatting byte numbers.\n\t//\n\t// This section defines such suffixes for all such cases.\n\t\"_big_number_suffix_3\": \"k\",\n\t\"_big_number_suffix_6\": \"M\",\n\t\"_big_number_suffix_9\": \"G\",\n\t\"_big_number_suffix_12\": \"T\",\n\t\"_big_number_suffix_15\": \"P\",\n\t\"_big_number_suffix_18\": \"E\",\n\t\"_big_number_suffix_21\": \"Z\",\n\t\"_big_number_suffix_24\": \"Y\",\n\n\t\"_small_number_suffix_3\": \"m\",\n\t\"_small_number_suffix_6\": \"μ\",\n\t\"_small_number_suffix_9\": \"n\",\n\t\"_small_number_suffix_12\": \"p\",\n\t\"_small_number_suffix_15\": \"f\",\n\t\"_small_number_suffix_18\": \"a\",\n\t\"_small_number_suffix_21\": \"z\",\n\t\"_small_number_suffix_24\": \"y\",\n\n\t\"_byte_suffix_B\": \"B\",\n\t\"_byte_suffix_KB\": \"KB\",\n\t\"_byte_suffix_MB\": \"MB\",\n\t\"_byte_suffix_GB\": \"GB\",\n\t\"_byte_suffix_TB\": \"TB\",\n\t\"_byte_suffix_PB\": \"PB\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_day_full\": \"MMM dd, yyyy\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"MMM dd, yyyy\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Notice how each duration unit comes in several versions. This is to ensure\n\t// that each base unit is shown correctly.\n\t//\n\t// For example, if we have baseUnit set to \"second\", meaning our duration is\n\t// in seconds.\n\t//\n\t// If we pass in `50` to formatter, it will know that we have just 50 seconds\n\t// (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\n\t// and the formatted result will be in like `\"50\"`.\n\t//\n\t// If we pass in `70`, which is more than a minute, the formatter will switch\n\t// to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\n\t// text.\n\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_millisecond_second\": \"ss.SSS\",\n\t\"_duration_millisecond_minute\": \"mm:ss SSS\",\n\t\"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\n\t\"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\n\t\"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\n\n\t\"_duration_second\": \"ss\",\n\t\"_duration_second_minute\": \"mm:ss\",\n\t\"_duration_second_hour\": \"hh:mm:ss\",\n\t\"_duration_second_day\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_week\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\n\t\"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\n\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_minute_hour\": \"hh:mm\",\n\t\"_duration_minute_day\": \"d'd' hh:mm\",\n\t\"_duration_minute_week\": \"d'd' hh:mm\",\n\t\"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\n\t\"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\n\n\t\"_duration_hour\": \"hh'h'\",\n\t\"_duration_hour_day\": \"d'd' hh'h'\",\n\t\"_duration_hour_week\": \"d'd' hh'h'\",\n\t\"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\n\t\"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\n\n\t\"_duration_day\": \"d'd'\",\n\t\"_duration_day_week\": \"d'd'\",\n\t\"_duration_day_month\": \"M'm' dd'd'\",\n\t\"_duration_day_year\": \"y'y' MM'm' dd'd'\",\n\n\t\"_duration_week\": \"w'w'\",\n\t\"_duration_week_month\": \"w'w'\",\n\t\"_duration_week_year\": \"w'w'\",\n\n\t\"_duration_month\": \"M'm'\",\n\t\"_duration_month_year\": \"y'y' MM'm'\",\n\n\t\"_duration_year\": \"y'y'\",\n\n\t// Era translations\n\t\"_era_ad\": \"μ.Χ.\",\n\t\"_era_bc\": \"π.Χ.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"πμ\",\n\t\"P\": \"μμ\",\n\t\"AM\": \"π.μ.\",\n\t\"PM\": \"μ.μ.\",\n\t\"A.M.\": \"π.μ.\",\n\t\"P.M.\": \"μ.μ.\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t//\n\t// Should month names and weekdays be capitalized or not?\n\t//\n\t// Rule of thumb is this: if the names should always be capitalized,\n\t// regardless of name position within date (\"January\", \"21st January 2018\",\n\t// etc.) use capitalized names. Otherwise enter all lowercase.\n\t//\n\t// The date formatter will automatically capitalize names if they are the\n\t// first (or only) word in resulting date.\n\t\"January\": \"Ιανουαρίου\",\n\t\"February\": \"Φεβρουαρίου\",\n\t\"March\": \"Μαρτίου\",\n\t\"April\": \"Απριλίου\",\n\t\"May\": \"Μαΐου\",\n\t\"June\": \"Ιουνίου\",\n\t\"July\": \"Ιουλίου\",\n\t\"August\": \"Αυγούστου\",\n\t\"September\": \"Σεπτεμβρίου\",\n\t\"October\": \"Οκτωβρίου\",\n\t\"November\": \"Νοεμβρίου\",\n\t\"December\": \"Δεκεμβρίου\",\n\t\"Jan\": \"Ιαν\",\n\t\"Feb\": \"Φεβ\",\n\t\"Mar\": \"Μαρ\",\n\t\"Apr\": \"Απρ\",\n\t\"May(short)\": \"Μαΐ\",\n\t\"Jun\": \"Ιουν\",\n\t\"Jul\": \"Ιουλ\",\n\t\"Aug\": \"Αυγ\",\n\t\"Sep\": \"Σεπ\",\n\t\"Oct\": \"Οκτ\",\n\t\"Nov\": \"Νοε\",\n\t\"Dec\": \"Δεκ\",\n\n\t// Weekdays.\n\t\"Sunday\": \"Κυριακή\",\n\t\"Monday\": \"Δευτέρα\",\n\t\"Tuesday\": \"Τρίτη\",\n\t\"Wednesday\": \"Τετάρτη\",\n\t\"Thursday\": \"Πέμπτη\",\n\t\"Friday\": \"Παρασκευή\",\n\t\"Saturday\": \"Σάββατο\",\n\t\"Sun\": \"Κυρ\",\n\t\"Mon\": \"Δευ\",\n\t\"Tue\": \"Τρί\",\n\t\"Wed\": \"Τετ\",\n\t\"Thu\": \"Πέμ\",\n\t\"Fri\": \"Παρ\",\n\t\"Sat\": \"Σάβ\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\tlet res = \"th\";\n\t\tif ((day < 11) || (day > 13)) {\n\t\t\tswitch (day % 10) {\n\t\t\t\tcase 1:\n\t\t\t\t\tres = \"st\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tres = \"nd\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tres = \"rd\"\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Εστίαση\",\n\n\t// Timeline buttons\n\t\"Play\": \"Αναπαραγωγή\",\n\t\"Stop\": \"Στάση\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Υπόμνημα\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Φόρτωση\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Αρχική\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"\",\n\t\"Serial chart\": \"\",\n\t\"X/Y chart\": \"\",\n\t\"Pie chart\": \"\",\n\t\"Gauge chart\": \"\",\n\t\"Radar chart\": \"\",\n\t\"Sankey diagram\": \"\",\n\t\"Flow diagram\": \"\",\n\t\"Chord diagram\": \"\",\n\t\"TreeMap chart\": \"\",\n\t\"Sliced chart\": \"\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"\",\n\t\"Candlestick Series\": \"\",\n\t\"OHLC Series\": \"\",\n\t\"Column Series\": \"\",\n\t\"Line Series\": \"\",\n\t\"Pie Slice Series\": \"\",\n\t\"Funnel Series\": \"\",\n\t\"Pyramid Series\": \"\",\n\t\"X/Y Series\": \"\",\n\n\t// Map-related stuff.\n\t\"Map\": \"\",\n\t\"Press ENTER to zoom in\": \"\",\n\t\"Press ENTER to zoom out\": \"\",\n\t\"Use arrow keys to zoom in and out\": \"\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Εκτύπωση\",\n\t\"Image\": \"Image\",\n\t\"Data\": \"Δεδομένα\",\n\t\"Print\": \"Εκτύπωση\",\n\t\"Press ENTER to open\": \"\",\n\t\"Press ENTER to print.\": \"\",\n\t\"Press ENTER to export as %1.\": \"\",\n\t\"(Press ESC to close this message)\": \"\",\n\t\"Image Export Complete\": \"\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"\",\n\t\"Saved from\": \"\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"\",\n\t\"Use left and right arrows to move selection\": \"\",\n\t\"Use left and right arrows to move left selection\": \"\",\n\t\"Use left and right arrows to move right selection\": \"\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"\",\n\t\"Use up and down arrows to move selection\": \"\",\n\t\"Use up and down arrows to move lower selection\": \"\",\n\t\"Use up and down arrows to move upper selection\": \"\",\n\t\"From %1 to %2\": \"Από %1 έως %2\",\n\t\"From %1\": \"Από %1\",\n\t\"To %1\": \"Έως %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"\",\n\t\"Error parsing file: %1\": \"\",\n\t\"Unable to load file: %1\": \"\",\n\t\"Invalid date\": \"\",\n};\n"], "names": ["am5locales_el_GR", "day", "res"], "sourceRoot": ""}