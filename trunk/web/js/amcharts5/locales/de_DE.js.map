{"version": 3, "file": "locales/de_DE.js", "mappings": "uJACO,MAAMA,ECuDb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAUlB,qBAAwB,IACxB,qBAAwB,MACxB,qBAAwB,MACxB,sBAAyB,MACzB,sBAAyB,MACzB,sBAAyB,QACzB,sBAAyB,MACzB,sBAAyB,IAEzB,uBAA0B,IAC1B,uBAA0B,IAC1B,uBAA0B,IAC1B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAE3B,eAAkB,IAClB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KAWnB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,wBACrB,WAAc,QACd,gBAAmB,wBACnB,UAAa,UACb,eAAkB,gBAClB,WAAc,KACd,gBAAmB,gBACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OASd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,UACX,QAAW,UAUX,EAAK,GACL,EAAK,GACL,GAAM,GACN,GAAM,GACN,OAAQ,GACR,OAAQ,GAaR,QAAW,SACX,SAAY,UACZ,MAAS,OACT,MAAS,QACT,IAAO,MACP,KAAQ,OACR,KAAQ,OACR,OAAU,SACV,UAAa,YACb,QAAW,UACX,SAAY,WACZ,SAAY,WACZ,IAAO,OACP,IAAO,QACP,IAAO,OACP,IAAO,OACP,aAAc,MACd,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,QACP,IAAO,OACP,IAAO,OACP,IAAO,OAIP,OAAU,UACV,OAAU,SACV,QAAW,WACX,UAAa,WACb,SAAY,aACZ,OAAU,UACV,SAAY,UACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAWP,SAAY,SAASC,GACnB,OAAOA,EAAM,GACf,EAIA,WAAY,eAGZ,KAAQ,YACR,KAAQ,OAGR,OAAU,UAGV,wBAAyB,oDAGzB,QAAW,eAIX,KAAQ,OAKR,MAAS,WACT,eAAgB,iBAChB,YAAa,eACb,YAAa,gBACb,cAAe,eACf,cAAe,eACf,iBAAkB,kBAClB,gBAAiB,GACjB,eAAgB,gBAChB,gBAAiB,eAKjB,OAAU,QACV,qBAAsB,iBACtB,gBAAiB,iBACjB,cAAe,iBACf,mBAAoB,gBACpB,aAAc,gBAGd,IAAO,QACP,yBAA0B,gCAC1B,0BAA2B,gCAC3B,oCAAqC,qCACrC,8DAA+D,2CAY/D,OAAU,SACV,MAAS,OACT,KAAQ,QACR,MAAS,UACT,sBAAuB,gDACvB,wBAAyB,kDACzB,+BAAgC,8DAChC,oCAAqC,8CACrC,wBAAyB,sBACzB,+EAAgF,6EAChF,aAAc,kBACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAYR,8EAA+E,mGAC/E,8CAA+C,mEAC/C,mDAAoD,yEACpD,oDAAqD,0EACrD,wEAAyE,+GACzE,2CAA4C,wEAC5C,iDAAkD,+EAClD,iDAAkD,8EAClD,gBAAiB,gBACjB,UAAW,SACX,QAAS,SAGT,mCAAoC,qCACpC,yBAA0B,kCAC1B,0BAA2B,uCAC3B,eAAgB,a", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/de_DE.js", "webpack://@amcharts/amcharts5/./src/locales/de_DE.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/de_DE.js\";\nexport const am5locales_de_DE = m;", "/**\n * amCharts 5 locale\n *\n * Locale: de\n * Language: German\n * Author: <PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Suffixes for numbers\n\t// When formatting numbers, big or small numers might be reformatted to\n\t// shorter version, by applying a suffix.\n\t//\n\t// For example, 1000000 might become \"1m\".\n\t// Or 1024 might become \"1KB\" if we're formatting byte numbers.\n\t//\n\t// This section defines such suffixes for all such cases.\n\t\"_big_number_suffix_3\": \"K\",\n\t\"_big_number_suffix_6\": \"Mio\",\n\t\"_big_number_suffix_9\": \"Mrd\",\n\t\"_big_number_suffix_12\": \"Bio\",\n\t\"_big_number_suffix_15\": \"Brd\",\n\t\"_big_number_suffix_18\": \"Trill\",\n\t\"_big_number_suffix_21\": \"Trd\",\n\t\"_big_number_suffix_24\": \"Y\",\n\n\t\"_small_number_suffix_3\": \"m\",\n\t\"_small_number_suffix_6\": \"μ\",\n\t\"_small_number_suffix_9\": \"n\",\n\t\"_small_number_suffix_12\": \"p\",\n\t\"_small_number_suffix_15\": \"f\",\n\t\"_small_number_suffix_18\": \"a\",\n\t\"_small_number_suffix_21\": \"z\",\n\t\"_small_number_suffix_24\": \"y\",\n\n\t\"_byte_suffix_B\": \"B\",\n\t\"_byte_suffix_KB\": \"KB\",\n\t\"_byte_suffix_MB\": \"MB\",\n\t\"_byte_suffix_GB\": \"GB\",\n\t\"_byte_suffix_TB\": \"TB\",\n\t\"_byte_suffix_PB\": \"PB\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - dd. MMM, yyyy\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - dd. MMM, yyyy\",\n\t\"_date_day\": \"dd. MMM\",\n\t\"_date_day_full\": \"dd. MMM, yyyy\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"dd. MMM, yyyy\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"v. Chr.\",\n\t\"_era_bc\": \"n. Chr.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t//\n\t// Short versions follow the guidelines of the the german \"Duden\" (https://de.wikipedia.org/wiki/Monat#Kurzformen)\n\t\"January\": \"Januar\",\n\t\"February\": \"Februar\",\n\t\"March\": \"März\",\n\t\"April\": \"April\",\n\t\"May\": \"Mai\",\n\t\"June\": \"Juni\",\n\t\"July\": \"Juli\",\n\t\"August\": \"August\",\n\t\"September\": \"September\",\n\t\"October\": \"Oktober\",\n\t\"November\": \"November\",\n\t\"December\": \"Dezember\",\n\t\"Jan\": \"Jan.\",\n\t\"Feb\": \"Febr.\",\n\t\"Mar\": \"März\",\n\t\"Apr\": \"Apr.\",\n\t\"May(short)\": \"Mai\",\n\t\"Jun\": \"Juni\",\n\t\"Jul\": \"Juli\",\n\t\"Aug\": \"Aug.\",\n\t\"Sep\": \"Sept.\",\n\t\"Oct\": \"Okt.\",\n\t\"Nov\": \"Nov.\",\n\t\"Dec\": \"Dez.\",\n\n\t// Weekdays.\n\t// Short versions follow the guidelines of the the german \"Duden\"\n\t\"Sunday\": \"Sonntag\",\n\t\"Monday\": \"Montag\",\n\t\"Tuesday\": \"Dienstag\",\n\t\"Wednesday\": \"Mittwoch\",\n\t\"Thursday\": \"Donnerstag\",\n\t\"Friday\": \"Freitag\",\n\t\"Saturday\": \"Samstag\",\n\t\"Sun\": \"So.\",\n\t\"Mon\": \"Mo.\",\n\t\"Tue\": \"Di.\",\n\t\"Wed\": \"Mi.\",\n\t\"Thu\": \"Do.\",\n\t\"Fri\": \"Fr.\",\n\t\"Sat\": \"Sa.\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\t\treturn day + '.';\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Herauszoomen\",\n\n\t// Timeline buttons\n\t\"Play\": \"Abspielen\",\n\t\"Stop\": \"Stop\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legende\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"Klicken, tippen oder ENTER drücken zum Umschalten\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Wird geladen\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Home\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Diagramm\",\n\t\"Serial chart\": \"Seriendiagramm\",\n\t\"X/Y chart\": \"X-Y-Diagramm\",\n\t\"Pie chart\": \"Kreisdiagramm\",\n\t\"Gauge chart\": \"Messdiagramm\",\n\t\"Radar chart\": \"Netzdiagramm\",\n\t\"Sankey diagram\": \"Sankey-Diagramm\",\n\t\"Chord diagram\": \"\",\n\t\"Flow diagram\": \"Flussdiagramm\",\n\t\"TreeMap chart\": \"Baumdiagramm\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Serie\",\n\t\"Candlestick Series\": \"Kerzendiagramm\",\n\t\"Column Series\": \"Balkendiagramm\",\n\t\"Line Series\": \"Liniendiagramm\",\n\t\"Pie Slice Series\": \"Kreisdiagramm\",\n\t\"X/Y Series\": \"Punktdiagramm\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Karte\",\n\t\"Press ENTER to zoom in\": \"Drücke ENTER zum Hereinzoomen\",\n\t\"Press ENTER to zoom out\": \"Drücke ENTER zum Herauszoomen\",\n\t\"Use arrow keys to zoom in and out\": \"Benutze die Pfeiltasten zum Zoomen\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Benutze Plus- und Minustasten zum Zoomen\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Export\",\n\t\"Image\": \"Bild\",\n\t\"Data\": \"Daten\",\n\t\"Print\": \"Drucken\",\n\t\"Press ENTER to open\": \"Zum Öffnen klicken, tippen oder ENTER drücken\",\n\t\"Press ENTER to print.\": \"Zum Drucken klicken, tippen oder ENTER drücken.\",\n\t\"Press ENTER to export as %1.\": \"Klicken, tippen oder ENTER drücken um als %1 zu exportieren\",\n\t\"(Press ESC to close this message)\": \"ESC drücken um diese Nachricht zu schließen\",\n\t\"Image Export Complete\": \"Bildexport komplett\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Der Export dauert länger als geplant. Vielleicht ist etwas schiefgelaufen.\",\n\t\"Saved from\": \"Gespeichert von\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder linke und rechte Pfeiltaste um die Auswahl zu ändern\",\n\t\"Use left and right arrows to move selection\": \"Linke und rechte Pfeiltaste nutzen um die Auswahl zu verschieben\",\n\t\"Use left and right arrows to move left selection\": \"Linke und rechte Pfeiltaste nutzen um die linke Auswahl zu verschieben\",\n\t\"Use left and right arrows to move right selection\": \"Linke und rechte Pfeiltaste nutzen um die rechte Auswahl zu verschieben\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"TAB nutzen, um Ankerpunkte auszuwählen oder Pfeiltaste nach oben und unten drücken, um die Auswahl zu ändern\",\n\t\"Use up and down arrows to move selection\": \"Pfeiltaste nach oben und unten drücken, um die Auswahl zu verschieben\",\n\t\"Use up and down arrows to move lower selection\": \"Pfeiltaste nach oben und unten drücken, um die untere Auswahl zu verschieben\",\n\t\"Use up and down arrows to move upper selection\": \"Pfeiltaste nach oben und unten drücken, um die obere Auswahl zu verschieben\",\n\t\"From %1 to %2\": \"Von %1 bis %2\",\n\t\"From %1\": \"Von %1\",\n\t\"To %1\": \"Bis %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Kein Parser für Datei %1 verfügbar\",\n\t\"Error parsing file: %1\": \"Fehler beim Parsen von Datei %1\",\n\t\"Unable to load file: %1\": \"Datei %1 konnte nicht geladen werden\",\n\t\"Invalid date\": \"Kein Datum\",\n};\n"], "names": ["am5locales_de_DE", "day"], "sourceRoot": ""}