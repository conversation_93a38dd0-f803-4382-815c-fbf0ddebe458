"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[660],{635:function(e,r,a){a.r(r),a.d(r,{am5locales_sv_SE:function(){return t}});const t={_decimalSeparator:",",_thousandSeparator:" ",_percentPrefix:null,_percentSuffix:"%",_date_millisecond:"mm:ss SSS",_date_millisecond_full:"HH:mm:ss SSS",_date_second:"HH:mm:ss",_date_second_full:"HH:mm:ss",_date_minute:"HH:mm",_date_minute_full:"HH:mm - yyyy-MM-dd",_date_hour:"HH:mm",_date_hour_full:"HH:mm - yyyy-MM-dd",_date_day:"yyyy-MM-dd",_date_day_full:"yyyy-MM-dd",_date_week:"ww",_date_week_full:"yyyy-MM-dd",_date_month:"MMM",_date_month_full:"MMM, yyyy",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_second:"ss",_duration_minute:"mm",_duration_hour:"hh",_duration_day:"dd",_duration_week:"ww",_duration_month:"MM",_duration_year:"yyyy",_era_ad:"e.Kr.",_era_bc:"f.Kr.",A:"fm",P:"em",AM:"fm",PM:"em","A.M.":"f.m.","P.M.":"e.m.",January:"januari",February:"februari",March:"mars",April:"april",May:"maj",June:"juni",July:"juli",August:"augusti",September:"september",October:"oktober",November:"november",December:"december",Jan:"jan.",Feb:"feb.",Mar:"mars",Apr:"apr.","May(short)":"maj",Jun:"juni",Jul:"juli",Aug:"aug.",Sep:"sep.",Oct:"okt.",Nov:"nov.",Dec:"dec.",Sunday:"söndag",Monday:"måndag",Tuesday:"tisdag",Wednesday:"onsdag",Thursday:"torsdag",Friday:"fredag",Saturday:"lördag",Sun:"sön",Mon:"mån",Tue:"tis",Wed:"ons",Thu:"tor",Fri:"fre",Sat:"lör",_dateOrd:function(e){return""},"Zoom Out":"Zooma ut",Play:"Spela",Stop:"Stoppa",Legend:"Teckenförklaring","Press ENTER to toggle":"Klicka eller tryck ENTER för att ändra",Loading:"Läser in",Home:"Hem",Chart:"Diagram","Serial chart":"Seriediagram","X/Y chart":"XY-diagram","Pie chart":"Tårtdiagram","Gauge chart":"Instrumentdiagram","Radar chart":"Radardiagram","Sankey diagram":"Sankeydiagram","Chord diagram":"Strängdiagram","Flow diagram":"Flödesschema","TreeMap chart":"Träddiagram ",Series:"Serier","Candlestick Series":"Candlestick-serier","Column Series":"Kolumnserier","Line Series":"Linjeserier","Pie Slice Series":"Tårtserier","X/Y Series":"X/Y-serier",Map:"Karta","Press ENTER to zoom in":"Tryck RETUR för att zooma in","Press ENTER to zoom out":"Tryck RETUR för att zooma ut","Use arrow keys to zoom in and out":"Använd pil-knapparna för att zooma in och ut","Use plus and minus keys on your keyboard to zoom in and out":"Använd plus- och minus-knapparna för att zooma in och ut",Export:"Exportera",Image:"Bild",Data:"Data",Print:"Skriv ut","Press ENTER to open":"Klicka eller tryck ENTER för att öppna","Press ENTER to print.":"Klicka eller tryck ENTER för att skriva ut.","Press ENTER to export as %1.":"Klicka eller tryck ENTER för att exportera till %1.","(Press ESC to close this message)":"(Tryck ESC för att stänga)","Image Export Complete":"Bildexport klar","Export operation took longer than expected. Something might have gone wrong.":"","Saved from":"Sparad från",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"",HTML:"","Use TAB to select grip buttons or left and right arrows to change selection":"","Use left and right arrows to move selection":"Använd vänster och höger pilknappar för att flytta urvalet","Use left and right arrows to move left selection":"Använd vänster och höger pilknappar för att flytta vänsterurval","Use left and right arrows to move right selection":"Använd vänster och höger pilknappar för att flytta högerurval","Use TAB select grip buttons or up and down arrows to change selection":"","Use up and down arrows to move selection":"Använd upp och ner pilknappar för att flytta urvalet","Use up and down arrows to move lower selection":"Använd upp och ner pilknappar för att flytta nedre urvalet","Use up and down arrows to move upper selection":"Använd upp och ner pilknappar för att flytta övre urvalet","From %1 to %2":"Från %1 till %2","From %1":"Från %1","To %1":"Till %1","No parser available for file: %1":"","Error parsing file: %1":"","Unable to load file: %1":"","Invalid date":"Ogiltigt datum"}}},function(e){var r=(635,e(e.s=635)),a=window;for(var t in r)a[t]=r[t];r.__esModule&&Object.defineProperty(a,"__esModule",{value:!0})}]);
//# sourceMappingURL=sv_SE.js.map