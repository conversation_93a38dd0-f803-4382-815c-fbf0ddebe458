{"version": 3, "file": "locales/ru_RU.js", "mappings": "wJACO,MAAMA,ECOb,CAEC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAGlB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,iBACrB,WAAc,QACd,gBAAmB,iBACnB,UAAa,SACb,eAAkB,SAClB,WAAc,KACd,gBAAmB,SACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAGd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,OACX,QAAW,UAGX,EAAK,IACL,EAAK,IACL,GAAM,OACN,GAAM,SACN,OAAQ,aACR,OAAQ,gBAGR,QAAW,SACX,SAAY,UACZ,MAAS,QACT,MAAS,SACT,IAAO,MACP,KAAQ,OACR,KAAQ,OACR,OAAU,UACV,UAAa,WACb,QAAW,UACX,SAAY,SACZ,SAAY,UACZ,IAAO,OACP,IAAO,QACP,IAAO,OACP,IAAO,OACP,aAAc,MACd,IAAO,OACP,IAAO,OACP,IAAO,OACP,IAAO,QACP,IAAO,OACP,IAAO,QACP,IAAO,OACP,OAAU,cACV,OAAU,cACV,QAAW,UACX,UAAa,QACb,SAAY,UACZ,OAAU,UACV,SAAY,UACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAGP,SAAY,SAASC,GACpB,MAAO,KACR,EAGA,WAAY,YACZ,KAAQ,QACR,KAAQ,OACR,OAAU,UACV,wBAAyB,0DACzB,QAAW,gBACX,KAAQ,SAGR,MAAS,SACT,eAAgB,qBAChB,YAAa,gBACb,YAAa,qBACb,cAAe,mBACf,cAAe,wBACf,iBAAkB,kBAClB,gBAAiB,kBACjB,eAAgB,iBAChB,gBAAiB,0BAGjB,OAAU,QACV,qBAAsB,mBACtB,gBAAiB,mBACjB,cAAe,iBACf,mBAAoB,iBACpB,aAAc,YAGd,IAAO,QACP,yBAA0B,+BAC1B,0BAA2B,+BAC3B,oCAAqC,0DACrC,8DAA+D,6EAG/D,OAAU,iBACV,MAAS,cACT,KAAQ,SACR,MAAS,WACT,sBAAuB,qDACvB,wBAAyB,yDACzB,+BAAgC,mEAChC,2EAA4E,2GAC5E,uFAAwF,mHACxF,oCAAqC,4CACrC,wBAAyB,+BACzB,+EAAgF,kFAChF,aAAc,eACd,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,KAAQ,OACR,IAAO,MACP,KAAQ,OACR,KAAQ,GAGR,8EAA+E,+GAC/E,8CAA+C,gEAC/C,mDAAoD,sEACpD,oDAAqD,uEACrD,wEAAyE,2FACzE,2CAA4C,8DAC5C,iDAAkD,qEAClD,iDAAkD,sEAClD,gBAAiB,cACjB,UAAW,QACX,QAAS,QAGT,mCAAoC,gCACpC,yBAA0B,+BAC1B,0BAA2B,gCAC3B,eAAgB,oB", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/ru_RU.js", "webpack://@amcharts/amcharts5/./src/locales/ru_RU.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/ru_RU.js\";\nexport const am5locales_ru_RU = m;", "/**\n * amCharts 5 locale\n *\n * Locale: ru_RU\n * Language: Russian\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// number formatter related\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \" \",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Default date formats for various periods\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - dd MMM\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - dd MMM\",\n\t\"_date_day\": \"dd MMM\",\n\t\"_date_day_full\": \"dd MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"dd MMM\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"M<PERSON>, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era\n\t\"_era_ad\": \"н.э.\",\n\t\"_era_bc\": \"до н.э.\",\n\n\t// Period\n\t\"A\": \"У\",\n\t\"P\": \"В\",\n\t\"AM\": \"утра\",\n\t\"PM\": \"вечера\",\n\t\"A.M.\": \"до полудня\",\n\t\"P.M.\": \"после полудня\",\n\n\t// Dates\n\t\"January\": \"января\",\n\t\"February\": \"февраля\",\n\t\"March\": \"марта\",\n\t\"April\": \"апреля\",\n\t\"May\": \"мая\",\n\t\"June\": \"июня\",\n\t\"July\": \"июля\",\n\t\"August\": \"августа\",\n\t\"September\": \"сентября\",\n\t\"October\": \"октября\",\n\t\"November\": \"ноября\",\n\t\"December\": \"декабря\",\n\t\"Jan\": \"янв.\",\n\t\"Feb\": \"февр.\",\n\t\"Mar\": \"март\",\n\t\"Apr\": \"апр.\",\n\t\"May(short)\": \"май\",\n\t\"Jun\": \"июнь\",\n\t\"Jul\": \"июль\",\n\t\"Aug\": \"авг.\",\n\t\"Sep\": \"сент.\",\n\t\"Oct\": \"окт.\",\n\t\"Nov\": \"нояб.\",\n\t\"Dec\": \"дек.\",\n\t\"Sunday\": \"воскресенье\",\n\t\"Monday\": \"понедельник\",\n\t\"Tuesday\": \"вторник\",\n\t\"Wednesday\": \"среда\",\n\t\"Thursday\": \"четверг\",\n\t\"Friday\": \"пятница\",\n\t\"Saturday\": \"суббота\",\n\t\"Sun\": \"вс.\",\n\t\"Mon\": \"пн.\",\n\t\"Tue\": \"вт.\",\n\t\"Wed\": \"ср.\",\n\t\"Thu\": \"чт.\",\n\t\"Fri\": \"пт.\",\n\t\"Sat\": \"сб.\",\n\n\t// ordinal function\n\t\"_dateOrd\": function(_day: number): string {\n\t\treturn '-ое';\n\t},\n\n\t// Chart elements\n\t\"Zoom Out\": \"Уменьшить\",\n\t\"Play\": \"Старт\",\n\t\"Stop\": \"Стоп\",\n\t\"Legend\": \"Легенда\",\n\t\"Press ENTER to toggle\": \"Щелкните, коснитесь или нажмите ВВОД, чтобы переключить\",\n\t\"Loading\": \"Идет загрузка\",\n\t\"Home\": \"Начало\",\n\n\t// Chart types\n\t\"Chart\": \"График\",\n\t\"Serial chart\": \"Серийная диаграмма\",\n\t\"X/Y chart\": \"Диаграмма X/Y\",\n\t\"Pie chart\": \"Круговая диаграмма\",\n\t\"Gauge chart\": \"Датчик-диаграмма\",\n\t\"Radar chart\": \"Лепестковая диаграмма\",\n\t\"Sankey diagram\": \"Диаграмма Сэнки\",\n\t\"Chord diagram\": \"Диаграмма Chord\",\n\t\"Flow diagram\": \"Диаграмма флоу\",\n\t\"TreeMap chart\": \"Иерархическая диаграмма\",\n\n\t// Series types\n\t\"Series\": \"Серия\",\n\t\"Candlestick Series\": \"Серия-подсвечник\",\n\t\"Column Series\": \"Столбчатая серия\",\n\t\"Line Series\": \"Линейная серия\",\n\t\"Pie Slice Series\": \"Круговая серия\",\n\t\"X/Y Series\": \"X/Y серия\",\n\n\t// Map-related\n\t\"Map\": \"Карта\",\n\t\"Press ENTER to zoom in\": \"Нажмите ВВОД чтобу увеличить\",\n\t\"Press ENTER to zoom out\": \"Нажмите ВВОД чтобы уменьшить\",\n\t\"Use arrow keys to zoom in and out\": \"Используйте клавиши-стрелки чтобы увеличить и уменьшить\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Используйте клавиши плюс и минус на клавиатуре чтобы увеличить и уменьшить\",\n\n\t// Export-related\n\t\"Export\": \"Экспортировать\",\n\t\"Image\": \"Изображение\",\n\t\"Data\": \"Данные\",\n\t\"Print\": \"Печатать\",\n\t\"Press ENTER to open\": \"Щелкните, коснитесь или нажмите ВВОД чтобы открыть\",\n\t\"Press ENTER to print.\": \"Щелкните, коснитесь или нажмите ВВОД чтобы распечатать\",\n\t\"Press ENTER to export as %1.\": \"Щелкните, коснитесь или нажмите ВВОД чтобы экспортировать как %1\",\n\t'To save the image, right-click this link and choose \"Save picture as...\"': 'Чтобы сохранить изображение, щелкните правой кнопкой на ссылке и выберите \"Сохранить изображение как...\"',\n\t'To save the image, right-click thumbnail on the left and choose \"Save picture as...\"': 'Чтобы сохранить изображение, щелкните правой кнопкой на картинке слева и выберите \"Сохранить изображение как...\"',\n\t\"(Press ESC to close this message)\": \"(Нажмите ESC чтобы закрыть это сообщение)\",\n\t\"Image Export Complete\": \"Экспорт изображения завершен\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Экспортирование заняло дольше, чем планировалось. Возможно что-то пошло не так.\",\n\t\"Saved from\": \"Сохранено из\",\n\t\"PNG\": \"PNG\",\n\t\"JPG\": \"JPG\",\n\t\"GIF\": \"GIF\",\n\t\"SVG\": \"SVG\",\n\t\"PDF\": \"PDF\",\n\t\"JSON\": \"JSON\",\n\t\"CSV\": \"CSV\",\n\t\"XLSX\": \"XLSX\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Используйте клавишу TAB, чтобы выбрать рукоятки или клавиши стрелок влево и вправо, чтобы изменить выделение\",\n\t\"Use left and right arrows to move selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть выделение\",\n\t\"Use left and right arrows to move left selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть левое выделение\",\n\t\"Use left and right arrows to move right selection\": \"Используйте стрелки влево-вправо, чтобы передвинуть правое выделение\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Используйте TAB, чтобы выбрать рукоятки или клавиши вверх-вниз, чтобы изменить выделение\",\n\t\"Use up and down arrows to move selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть выделение\",\n\t\"Use up and down arrows to move lower selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть нижнее выделение\",\n\t\"Use up and down arrows to move upper selection\": \"Используйте стрелки вверх-вниз, чтобы передвинуть верхнее выделение\",\n\t\"From %1 to %2\": \"От %1 до %2\",\n\t\"From %1\": \"От %1\",\n\t\"To %1\": \"До %1\",\n\n\t// Data loader-related\n\t\"No parser available for file: %1\": \"Нет анализатора для файла: %1\",\n\t\"Error parsing file: %1\": \"Ошибка при разборе файла: %1\",\n\t\"Unable to load file: %1\": \"Не удалось загрузить файл: %1\",\n\t\"Invalid date\": \"Некорректная дата\",\n};\n"], "names": ["am5locales_ru_RU", "_day"], "sourceRoot": ""}