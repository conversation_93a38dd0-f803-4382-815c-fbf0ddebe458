"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[7633],{1981:function(e,a,_){_.r(a),_.d(a,{am5locales_id_ID:function(){return r}});const r={_decimalSeparator:",",_thousandSeparator:".",_percentPrefix:null,_percentSuffix:"%",_big_number_suffix_3:"k",_big_number_suffix_6:"M",_big_number_suffix_9:"G",_big_number_suffix_12:"T",_big_number_suffix_15:"P",_big_number_suffix_18:"E",_big_number_suffix_21:"Z",_big_number_suffix_24:"Y",_small_number_suffix_3:"m",_small_number_suffix_6:"μ",_small_number_suffix_9:"n",_small_number_suffix_12:"p",_small_number_suffix_15:"f",_small_number_suffix_18:"a",_small_number_suffix_21:"z",_small_number_suffix_24:"y",_byte_suffix_B:"B",_byte_suffix_KB:"KB",_byte_suffix_MB:"MB",_byte_suffix_GB:"GB",_byte_suffix_TB:"TB",_byte_suffix_PB:"PB",_date_millisecond:"mm:ss SSS",_date_millisecond_full:"HH:mm:ss SSS",_date_second:"HH:mm:ss",_date_second_full:"HH:mm:ss",_date_minute:"HH:mm",_date_minute_full:"HH:mm - MMM dd, yyyy",_date_hour:"HH:mm",_date_hour_full:"HH:mm - MMM dd, yyyy",_date_day:"MMM dd",_date_day_full:"MMM dd, yyyy",_date_week:"ww",_date_week_full:"MMM dd, yyyy",_date_month:"MMM",_date_month_full:"MMM, yyyy",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_millisecond_second:"ss.SSS",_duration_millisecond_minute:"mm:ss SSS",_duration_millisecond_hour:"hh:mm:ss SSS",_duration_millisecond_day:"d'd' mm:ss SSS",_duration_millisecond_week:"d'd' mm:ss SSS",_duration_millisecond_month:"M'm' dd'd' mm:ss SSS",_duration_millisecond_year:"y'y' MM'm' dd'd' mm:ss SSS",_duration_second:"ss",_duration_second_minute:"mm:ss",_duration_second_hour:"hh:mm:ss",_duration_second_day:"d'd' hh:mm:ss",_duration_second_week:"d'd' hh:mm:ss",_duration_second_month:"M'm' dd'd' hh:mm:ss",_duration_second_year:"y'y' MM'm' dd'd' hh:mm:ss",_duration_minute:"mm",_duration_minute_hour:"hh:mm",_duration_minute_day:"d'd' hh:mm",_duration_minute_week:"d'd' hh:mm",_duration_minute_month:"M'm' dd'd' hh:mm",_duration_minute_year:"y'y' MM'm' dd'd' hh:mm",_duration_hour:"hh'h'",_duration_hour_day:"d'd' hh'h'",_duration_hour_week:"d'd' hh'h'",_duration_hour_month:"M'm' dd'd' hh'h'",_duration_hour_year:"y'y' MM'm' dd'd' hh'h'",_duration_day:"d'd'",_duration_day_week:"d'd'",_duration_day_month:"M'm' dd'd'",_duration_day_year:"y'y' MM'm' dd'd'",_duration_week:"w'w'",_duration_week_month:"w'w'",_duration_week_year:"w'w'",_duration_month:"M'm'",_duration_month_year:"y'y' MM'm'",_duration_year:"y'y'",_era_ad:"M",_era_bc:"SM",A:"AM",P:"PM",AM:"AM",PM:"PM","A.M.":"AM","P.M.":"PM",January:"Januari",February:"Februari",March:"Maret",April:"April",May:"Mei",June:"Juni",July:"Juli",August:"Agustus",September:"September",October:"Oktober",November:"November",December:"Desember",Jan:"Jan",Feb:"Feb",Mar:"Mar",Apr:"Apr","May(short)":"Mei",Jun:"Jun",Jul:"Jul",Aug:"Agu",Sep:"Sep",Oct:"Okt",Nov:"Nov",Dec:"Des",Sunday:"Minggu",Monday:"Senin",Tuesday:"Selasa",Wednesday:"Rabu",Thursday:"Kamis",Friday:"Jumat",Saturday:"Sabtu",Sun:"Min",Mon:"Sen",Tue:"Sel",Wed:"Rab",Thu:"Kam",Fri:"Jum",Sat:"Sab",_dateOrd:function(e){let a="th";if(e<11||e>13)switch(e%10){case 1:a="st";break;case 2:a="nd";break;case 3:a="rd"}return a},"Zoom Out":"Perkecil",Play:"Putar",Stop:"Hentikan",Legend:"Legenda","Press ENTER to toggle":"Klik, ketuk atau tekan ENTER untuk beralih",Loading:"Memuat",Home:"Beranda",Chart:"","Serial chart":"","X/Y chart":"","Pie chart":"","Gauge chart":"","Radar chart":"","Sankey diagram":"","Flow diagram":"","Chord diagram":"","TreeMap chart":"","Sliced chart":"",Series:"","Candlestick Series":"","OHLC Series":"","Column Series":"","Line Series":"","Pie Slice Series":"","Funnel Series":"","Pyramid Series":"","X/Y Series":"",Map:"Peta","Press ENTER to zoom in":"Tekan ENTER untuk memperbesar","Press ENTER to zoom out":"Tekan ENTER untuk memperkecil","Use arrow keys to zoom in and out":"Gunakan tombol panah untuk memperbesar dan memperkecil","Use plus and minus keys on your keyboard to zoom in and out":"Gunakan tombol plus dan minus pada keyboard Anda untuk memperbesar dan memperkecil",Export:"Cetak",Image:"Gambar",Data:"Data",Print:"Cetak","Press ENTER to open":"Klik, ketuk atau tekan ENTER untuk membuka","Press ENTER to print.":"Klik, ketuk atau tekan ENTER untuk mencetak","Press ENTER to export as %1.":"Klik, ketuk atau tekan ENTER untuk mengekspor sebagai %1","(Press ESC to close this message)":"Tekan ESC untuk menutup pesan ini","Image Export Complete":"Ekspor gambar selesai","Export operation took longer than expected. Something might have gone wrong.":"","Saved from":"",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"",HTML:"","Use TAB to select grip buttons or left and right arrows to change selection":"","Use left and right arrows to move selection":"","Use left and right arrows to move left selection":"","Use left and right arrows to move right selection":"","Use TAB select grip buttons or up and down arrows to change selection":"","Use up and down arrows to move selection":"","Use up and down arrows to move lower selection":"","Use up and down arrows to move upper selection":"","From %1 to %2":"Dari %1 ke %2","From %1":"Dari %1","To %1":"Ke %1","No parser available for file: %1":"","Error parsing file: %1":"","Unable to load file: %1":"","Invalid date":""}}},function(e){var a=(1981,e(e.s=1981)),_=window;for(var r in a)_[r]=a[r];a.__esModule&&Object.defineProperty(_,"__esModule",{value:!0})}]);
//# sourceMappingURL=id_ID.js.map