{"version": 3, "file": "locales/sl_SL.js", "mappings": "wJACO,MAAMA,ECuDb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAUlB,qBAAwB,IACxB,qBAAwB,IACxB,qBAAwB,IACxB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IAEzB,uBAA0B,IAC1B,uBAA0B,IAC1B,uBAA0B,IAC1B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAE3B,eAAkB,IAClB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KAWnB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,uBACrB,WAAc,QACd,gBAAmB,uBACnB,UAAa,SACb,eAAkB,eAClB,WAAc,KACd,gBAAmB,eACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAuBd,sBAAyB,MACzB,6BAAgC,SAChC,6BAAgC,YAChC,2BAA8B,eAC9B,0BAA6B,iBAC7B,2BAA8B,iBAC9B,4BAA+B,uBAC/B,2BAA8B,6BAE9B,iBAAoB,KACpB,wBAA2B,QAC3B,sBAAyB,WACzB,qBAAwB,gBACxB,sBAAyB,gBACzB,uBAA0B,sBAC1B,sBAAyB,4BAEzB,iBAAoB,KACpB,sBAAyB,QACzB,qBAAwB,aACxB,sBAAyB,aACzB,uBAA0B,mBAC1B,sBAAyB,yBAEzB,eAAkB,QAClB,mBAAsB,aACtB,oBAAuB,aACvB,qBAAwB,mBACxB,oBAAuB,yBAEvB,cAAiB,OACjB,mBAAsB,OACtB,oBAAuB,aACvB,mBAAsB,mBAEtB,eAAkB,OAClB,qBAAwB,OACxB,oBAAuB,OAEvB,gBAAmB,OACnB,qBAAwB,aAExB,eAAkB,OAGlB,QAAW,SACX,QAAW,aAUX,EAAK,IACL,EAAK,IACL,GAAM,KACN,GAAM,KACN,OAAQ,OACR,OAAQ,OAoBR,QAAW,SACX,SAAY,UACZ,MAAS,QACT,MAAS,QACT,IAAO,MACP,KAAQ,QACR,KAAQ,QACR,OAAU,SACV,UAAa,YACb,QAAW,UACX,SAAY,WACZ,SAAY,WACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,aAAc,MACd,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAGP,OAAU,UACV,OAAU,aACV,QAAW,QACX,UAAa,QACb,SAAY,UACZ,OAAU,QACV,SAAY,SACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAWP,SAAY,SAASC,GACpB,MAAO,GACR,EAIA,WAAY,iBAGZ,KAAQ,SACR,KAAQ,SAGR,OAAU,UAGV,wBAAyB,8CAGzB,QAAW,UAIX,KAAQ,QAKR,MAAS,OACT,eAAgB,gBAChB,YAAa,WACb,YAAa,cACb,cAAe,eACf,cAAe,aACf,iBAAkB,iBAClB,eAAgB,gBAChB,gBAAiB,oBACjB,gBAAiB,eACjB,eAAgB,cAKhB,OAAU,SACV,qBAAsB,gBACtB,cAAe,cACf,gBAAiB,qBACjB,cAAe,eACf,mBAAoB,gBACpB,gBAAiB,eACjB,iBAAkB,mBAClB,aAAc,aAGd,IAAO,OACP,yBAA0B,kCAC1B,0BAA2B,iCAC3B,oCAAqC,wDACrC,8DAA+D,6EAY/D,OAAU,SACV,MAAS,QACT,KAAQ,UACR,MAAS,UACT,sBAAuB,8CACvB,wBAAyB,gDACzB,+BAAgC,sDAChC,oCAAqC,wCACrC,wBAAyB,qBACzB,+EAAgF,yEAChF,aAAc,eACd,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,KAAQ,OACR,IAAO,MACP,KAAQ,OACR,KAAQ,GAYR,8EAA+E,yFAC/E,8CAA+C,yDAC/C,mDAAoD,2DACpD,oDAAqD,4DACrD,wEAAyE,sFACzE,2CAA4C,mDAC5C,iDAAkD,2DAClD,iDAAkD,2DAClD,gBAAiB,cACjB,UAAW,QACX,QAAS,QAGT,mCAAoC,gDACpC,yBAA0B,mCAC1B,0BAA2B,kCAC3B,eAAgB,mB", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/sl_SL.js", "webpack://@amcharts/amcharts5/./src/locales/sl_SL.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/sl_SL.js\";\nexport const am5locales_sl_SL = m;", "/**\n * amCharts 5 locale\n *\n * Locale: sl-SI\n * Language: Slovenian\n * Author: <PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n *\n * ---\n * Edit but leave the header section above this line. You can remove any\n * subsequent comment sections.\n * ---\n *\n * Use this file as a template to create translations. Leave the key part in\n * English intact. Fill the value with a translation.\n *\n * Empty string means no translation, so default \"International English\"\n * will be used.\n *\n * If you need the translation to literally be an empty string, use `null`\n * instead.\n *\n * IMPORTANT:\n * When translating make good effort to keep the translation length\n * at least the same chartcount as the English, especially for short prompts.\n *\n * Having significantly longer prompts may distort the actual charts.\n *\n * NOTE:\n * Some prompts - like months or weekdays - come in two versions: full and\n * shortened.\n *\n * If there's no official shortened version of these in your language, and it\n * would not be possible to invent such short versions that don't seem weird\n * to native speakers of that language, fill those with the same as full\n * version.\n *\n * PLACEHOLDERS:\n * Some prompts have placeholders like \"%1\". Those will be replaced by actual\n * values during translation and should be retained in the translated prompts.\n *\n * Placeholder positions may be changed to better suit structure of the\n * sentence.\n *\n * For example \"From %1 to %2\", when actually used will replace \"%1\" with an\n * actual value representing range start, and \"%2\" will be replaced by end\n * value.\n *\n * E.g. in a Scrollbar for Value axis \"From %1 to %2\" will become\n * \"From 100 to 200\". You may translate \"From\" and \"to\", as well as re-arrange\n * the order of the prompt itself, but make sure the \"%1\" and \"%2\" remain, in\n * places where they will make sense.\n *\n * Save the file as language_LOCALE, i.e. `en_GB.ts`, `fr_FR.ts`, etc.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Suffixes for numbers\n\t// When formatting numbers, big or small numers might be reformatted to\n\t// shorter version, by applying a suffix.\n\t//\n\t// For example, 1000000 might become \"1m\".\n\t// Or 1024 might become \"1KB\" if we're formatting byte numbers.\n\t//\n\t// This section defines such suffixes for all such cases.\n\t\"_big_number_suffix_3\": \"k\",\n\t\"_big_number_suffix_6\": \"M\",\n\t\"_big_number_suffix_9\": \"G\",\n\t\"_big_number_suffix_12\": \"T\",\n\t\"_big_number_suffix_15\": \"P\",\n\t\"_big_number_suffix_18\": \"E\",\n\t\"_big_number_suffix_21\": \"Z\",\n\t\"_big_number_suffix_24\": \"Y\",\n\n\t\"_small_number_suffix_3\": \"m\",\n\t\"_small_number_suffix_6\": \"μ\",\n\t\"_small_number_suffix_9\": \"n\",\n\t\"_small_number_suffix_12\": \"p\",\n\t\"_small_number_suffix_15\": \"f\",\n\t\"_small_number_suffix_18\": \"a\",\n\t\"_small_number_suffix_21\": \"z\",\n\t\"_small_number_suffix_24\": \"y\",\n\n\t\"_byte_suffix_B\": \"B\",\n\t\"_byte_suffix_KB\": \"KB\",\n\t\"_byte_suffix_MB\": \"MB\",\n\t\"_byte_suffix_GB\": \"GB\",\n\t\"_byte_suffix_TB\": \"TB\",\n\t\"_byte_suffix_PB\": \"PB\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_day_full\": \"MMM dd, yyyy\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"MMM dd, yyyy\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Notice how each duration unit comes in several versions. This is to ensure\n\t// that each base unit is shown correctly.\n\t//\n\t// For example, if we have baseUnit set to \"second\", meaning our duration is\n\t// in seconds.\n\t//\n\t// If we pass in `50` to formatter, it will know that we have just 50 seconds\n\t// (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\n\t// and the formatted result will be in like `\"50\"`.\n\t//\n\t// If we pass in `70`, which is more than a minute, the formatter will switch\n\t// to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\n\t// text.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_millisecond_second\": \"ss.SSS\",\n\t\"_duration_millisecond_minute\": \"mm:ss SSS\",\n\t\"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\n\t\"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\n\t\"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\n\n\t\"_duration_second\": \"ss\",\n\t\"_duration_second_minute\": \"mm:ss\",\n\t\"_duration_second_hour\": \"hh:mm:ss\",\n\t\"_duration_second_day\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_week\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\n\t\"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\n\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_minute_hour\": \"hh:mm\",\n\t\"_duration_minute_day\": \"d'd' hh:mm\",\n\t\"_duration_minute_week\": \"d'd' hh:mm\",\n\t\"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\n\t\"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\n\n\t\"_duration_hour\": \"hh'h'\",\n\t\"_duration_hour_day\": \"d'd' hh'h'\",\n\t\"_duration_hour_week\": \"d'd' hh'h'\",\n\t\"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\n\t\"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\n\n\t\"_duration_day\": \"d'd'\",\n\t\"_duration_day_week\": \"d'd'\",\n\t\"_duration_day_month\": \"M'm' dd'd'\",\n\t\"_duration_day_year\": \"y'y' MM'm' dd'd'\",\n\n\t\"_duration_week\": \"w'w'\",\n\t\"_duration_week_month\": \"w'w'\",\n\t\"_duration_week_year\": \"w'w'\",\n\n\t\"_duration_month\": \"M'm'\",\n\t\"_duration_month_year\": \"y'y' MM'm'\",\n\n\t\"_duration_year\": \"y'y'\",\n\n\t// Era translations\n\t\"_era_ad\": \"n. št.\",\n\t\"_era_bc\": \"pr. n. št.\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"A\",\n\t\"P\": \"P\",\n\t\"AM\": \"AM\",\n\t\"PM\": \"PM\",\n\t\"A.M.\": \"A.M.\",\n\t\"P.M.\": \"P.M.\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t//\n\t// Should month names and weekdays be capitalized or not?\n\t//\n\t// Rule of thumb is this: if the names should always be capitalized,\n\t// regardless of name position within date (\"January\", \"21st January 2018\",\n\t// etc.) use capitalized names. Otherwise enter all lowercase.\n\t//\n\t// The date formatter will automatically capitalize names if they are the\n\t// first (or only) word in resulting date.\n\t\"January\": \"Januar\",\n\t\"February\": \"Februar\",\n\t\"March\": \"Marec\",\n\t\"April\": \"April\",\n\t\"May\": \"Maj\",\n\t\"June\": \"Junij\",\n\t\"July\": \"Julij\",\n\t\"August\": \"Avgust\",\n\t\"September\": \"September\",\n\t\"October\": \"Oktober\",\n\t\"November\": \"November\",\n\t\"December\": \"December\",\n\t\"Jan\": \"Jan\",\n\t\"Feb\": \"Feb\",\n\t\"Mar\": \"Mar\",\n\t\"Apr\": \"Apr\",\n\t\"May(short)\": \"Maj\",\n\t\"Jun\": \"Jun\",\n\t\"Jul\": \"Jul\",\n\t\"Aug\": \"Avg\",\n\t\"Sep\": \"Sep\",\n\t\"Oct\": \"Okt\",\n\t\"Nov\": \"Nov\",\n\t\"Dec\": \"Dec\",\n\n\t// Weekdays.\n\t\"Sunday\": \"Nedelja\",\n\t\"Monday\": \"Ponedeljek\",\n\t\"Tuesday\": \"Torek\",\n\t\"Wednesday\": \"Sreda\",\n\t\"Thursday\": \"Četrtek\",\n\t\"Friday\": \"Petek\",\n\t\"Saturday\": \"Sobota\",\n\t\"Sun\": \"Ned\",\n\t\"Mon\": \"Pon\",\n\t\"Tue\": \"Tor\",\n\t\"Wed\": \"Sre\",\n\t\"Thu\": \"Čet\",\n\t\"Fri\": \"Pet\",\n\t\"Sat\": \"Sob\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(_day: number): string {\n\t\treturn \".\";\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Oddalji pogled\",\n\n\t// Timeline buttons\n\t\"Play\": \"Zaženi\",\n\t\"Stop\": \"Ustavi\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legenda\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"Klikni, tapni ali pritisni ENTER za preklop\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Nalagam\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Domov\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Graf\",\n\t\"Serial chart\": \"Serijski graf\",\n\t\"X/Y chart\": \"X/Y graf\",\n\t\"Pie chart\": \"Tortni graf\",\n\t\"Gauge chart\": \"Stevčni graf\",\n\t\"Radar chart\": \"Radar graf\",\n\t\"Sankey diagram\": \"Sankey diagram\",\n\t\"Flow diagram\": \"Prikaz poteka\",\n\t\"Chord diagram\": \"Kolobarni diagram\",\n\t\"TreeMap chart\": \"Drevesi graf\",\n\t\"Sliced chart\": \"Sliced graf\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Serija\",\n\t\"Candlestick Series\": \"Svečna serija\",\n\t\"OHLC Series\": \"OHLC serija\",\n\t\"Column Series\": \"Stolpičasta serija\",\n\t\"Line Series\": \"Črtna serija\",\n\t\"Pie Slice Series\": \"Tortna serija\",\n\t\"Funnel Series\": \"Lijak serija\",\n\t\"Pyramid Series\": \"Piramidna serija\",\n\t\"X/Y Series\": \"X/Y serija\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Mapa\",\n\t\"Press ENTER to zoom in\": \"Pritisni ENTER za približevanje\",\n\t\"Press ENTER to zoom out\": \"Pritisni ENTER za oddaljevanje\",\n\t\"Use arrow keys to zoom in and out\": \"Uporabi smerne tiple za približevanje in oddaljevanje\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Uporabi plus in minus tipke na tipkovnici za približevanje in oddaljevanje\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Izvozi\",\n\t\"Image\": \"Slika\",\n\t\"Data\": \"Podatki\",\n\t\"Print\": \"Natisni\",\n\t\"Press ENTER to open\": \"Klikni, tapni ali pritisni ENTER da odpreš.\",\n\t\"Press ENTER to print.\": \"Klikni, tapni ali pritisni ENTER za tiskanje.\",\n\t\"Press ENTER to export as %1.\": \"Klikni, tapni ali pritisni ENTER da izvoziš kot %1.\",\n\t\"(Press ESC to close this message)\": \"(Pritisni ESC da zapreš to sporočilo)\",\n\t\"Image Export Complete\": \"Izvoz slike končan\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"Operacija izvoza je trajala dlje kot pričakovano. Nekaj je šlo narobe.\",\n\t\"Saved from\": \"Shranjeno od\",\n\t\"PNG\": \"PNG\",\n\t\"JPG\": \"JPG\",\n\t\"GIF\": \"GIF\",\n\t\"SVG\": \"SVG\",\n\t\"PDF\": \"PDF\",\n\t\"JSON\": \"JSON\",\n\t\"CSV\": \"CSV\",\n\t\"XLSX\": \"XLSX\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Uporabi TAB za izbiro drsnih gumbov ali levo in desno smerno tipko da spremeniš izbiro\",\n\t\"Use left and right arrows to move selection\": \"Uporabi levo in desno smerno tipko za premik izbranega\",\n\t\"Use left and right arrows to move left selection\": \"Uporabi levo in desno smerno tipko za premik leve izbire\",\n\t\"Use left and right arrows to move right selection\": \"Uporabi levo in desno smerno tipko za premik desne izbire\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Uporabi TAB za izbiro drsnih gumbov ali gor in dol smerno tipko da spremeniš izbiro\",\n\t\"Use up and down arrows to move selection\": \"Uporabi gor in dol smerne tipke za premik izbire\",\n\t\"Use up and down arrows to move lower selection\": \"Uporabi gor in dol smerne tipke za premik spodnje izbire\",\n\t\"Use up and down arrows to move upper selection\": \"Uporabi gor in dol smerne tipke za premik zgornje izbire\",\n\t\"From %1 to %2\": \"Od %1 do %2\",\n\t\"From %1\": \"Od %1\",\n\t\"To %1\": \"Do %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Nobenega parserja ni na voljo za datoteko: %1\",\n\t\"Error parsing file: %1\": \"Napaka pri parsanju datoteke: %1\",\n\t\"Unable to load file: %1\": \"Ni mogoče naložiti datoteke: %1\",\n\t\"Invalid date\": \"Neveljaven datum\",\n};\n"], "names": ["am5locales_sl_SL", "_day"], "sourceRoot": ""}