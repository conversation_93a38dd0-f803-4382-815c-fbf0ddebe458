{"version": 3, "file": "locales/pt_BR.js", "mappings": "wJACO,MAAMA,ECOb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAWlB,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,iBACrB,WAAc,QACd,gBAAmB,iBACnB,UAAa,SACb,eAAkB,SAClB,WAAc,KACd,gBAAmB,SACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OASd,sBAAyB,MACzB,iBAAoB,KACpB,iBAAoB,KACpB,eAAkB,KAClB,cAAiB,KACjB,eAAkB,KAClB,gBAAmB,KACnB,eAAkB,OAGlB,QAAW,KACX,QAAW,KAUX,EAAK,GACL,EAAK,GACL,GAAM,GACN,GAAM,GACN,OAAQ,GACR,OAAQ,GAWR,QAAW,UACX,SAAY,YACZ,MAAS,QACT,MAAS,QACT,IAAO,OACP,KAAQ,QACR,KAAQ,QACR,OAAU,SACV,UAAa,WACb,QAAW,UACX,SAAY,WACZ,SAAY,WACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,aAAc,MACd,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAGP,OAAU,UACV,OAAU,gBACV,QAAW,cACX,UAAa,eACb,SAAY,eACZ,OAAU,cACV,SAAY,SACZ,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MACP,IAAO,MAWP,SAAY,SAASC,GACpB,MAAO,GACR,EAIA,WAAY,eAGZ,KAAQ,OACR,KAAQ,QAGR,OAAU,UAGV,wBAAyB,iDAGzB,QAAW,aAIX,KAAQ,SAKR,MAAS,UACT,eAAgB,iBAChB,YAAa,aACb,YAAa,mBACb,cAAe,oBACf,cAAe,mBACf,iBAAkB,kBAClB,gBAAiB,gBACjB,eAAgB,gBAChB,gBAAiB,4BAKjB,OAAU,SACV,qBAAsB,wBACtB,gBAAiB,oBACjB,cAAe,mBACf,mBAAoB,4BACpB,aAAc,eAGd,IAAO,OACP,yBAA0B,uCAC1B,0BAA2B,uCAC3B,oCAAqC,gDACrC,8DAA+D,8EAY/D,OAAU,WACV,MAAS,SACT,KAAQ,QACR,MAAS,WACT,sBAAuB,8CACvB,wBAAyB,iDACzB,+BAAgC,0DAChC,oCAAqC,4CACrC,wBAAyB,wCACzB,+EAAgF,oFAChF,aAAc,WACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAYR,8EAA+E,6FAC/E,8CAA+C,+DAC/C,mDAAoD,2EACpD,oDAAqD,0EACrD,wEAAyE,0FACzE,2CAA4C,4DAC5C,iDAAkD,qEAClD,iDAAkD,oEAClD,gBAAiB,eACjB,UAAW,QACX,QAAS,SAGT,mCAAoC,gDACpC,yBAA0B,gCAC1B,0BAA2B,uCAC3B,eAAgB,gB", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/pt_BR.js", "webpack://@amcharts/amcharts5/./src/locales/pt_BR.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/pt_BR.js\";\nexport const am5locales_pt_BR = m;", "/**\n * amCharts 5 locale\n *\n * Locale: pt_BR\n * Language: Brazilian Portuguese\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \",\",\n\t\"_thousandSeparator\": \".\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - dd MMM\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - dd MMM\",\n\t\"_date_day\": \"dd MMM\",\n\t\"_date_day_full\": \"dd MMM\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"dd MMM\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Available codes here:\n\t// https://www.amcharts.com/docs/v4/concepts/formatters/formatting-duration/#Available_Codes\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_second\": \"ss\",\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_hour\": \"hh\",\n\t\"_duration_day\": \"dd\",\n\t\"_duration_week\": \"ww\",\n\t\"_duration_month\": \"MM\",\n\t\"_duration_year\": \"yyyy\",\n\n\t// Era translations\n\t\"_era_ad\": \"DC\",\n\t\"_era_bc\": \"AC\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"\",\n\t\"P\": \"\",\n\t\"AM\": \"\",\n\t\"PM\": \"\",\n\t\"A.M.\": \"\",\n\t\"P.M.\": \"\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t\"January\": \"Janeiro\",\n\t\"February\": \"Fevereiro\",\n\t\"March\": \"Março\",\n\t\"April\": \"Abril\",\n\t\"May\": \"Maio\",\n\t\"June\": \"Junho\",\n\t\"July\": \"Julho\",\n\t\"August\": \"Agosto\",\n\t\"September\": \"Setembro\",\n\t\"October\": \"Outubro\",\n\t\"November\": \"Novembro\",\n\t\"December\": \"Dezembro\",\n\t\"Jan\": \"Jan\",\n\t\"Feb\": \"Fev\",\n\t\"Mar\": \"Mar\",\n\t\"Apr\": \"Abr\",\n\t\"May(short)\": \"Mai\",\n\t\"Jun\": \"Jun\",\n\t\"Jul\": \"Jul\",\n\t\"Aug\": \"Ago\",\n\t\"Sep\": \"Set\",\n\t\"Oct\": \"Out\",\n\t\"Nov\": \"Nov\",\n\t\"Dec\": \"Dez\",\n\n\t// Weekdays.\n\t\"Sunday\": \"Domingo\",\n\t\"Monday\": \"Segunda-feira\",\n\t\"Tuesday\": \"Terça-feira\",\n\t\"Wednesday\": \"Quarta-feira\",\n\t\"Thursday\": \"Quinta-feira\",\n\t\"Friday\": \"Sexta-feira\",\n\t\"Saturday\": \"Sábado\",\n\t\"Sun\": \"Dom\",\n\t\"Mon\": \"Seg\",\n\t\"Tue\": \"Ter\",\n\t\"Wed\": \"Qua\",\n\t\"Thu\": \"Qui\",\n\t\"Fri\": \"Sex\",\n\t\"Sat\": \"Sáb\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(_day: number): string {\n\t\treturn \"º\";\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"Reduzir Zoom\",\n\n\t// Timeline buttons\n\t\"Play\": \"Play\",\n\t\"Stop\": \"Parar\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"Legenda\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"Clique, toque ou pressione ENTER para alternar\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"Carregando\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"Início\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"Gráfico\",\n\t\"Serial chart\": \"Gráfico Serial\",\n\t\"X/Y chart\": \"Gráfico XY\",\n\t\"Pie chart\": \"Gráfico de Pizza\",\n\t\"Gauge chart\": \"Gráfico Indicador\",\n\t\"Radar chart\": \"Gráfico de Radar\",\n\t\"Sankey diagram\": \"Diagrama Sankey\",\n\t\"Chord diagram\": \"Diagram Chord\",\n\t\"Flow diagram\": \"Diagrama Flow\",\n\t\"TreeMap chart\": \"Gráfico de Mapa de Árvore\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"Séries\",\n\t\"Candlestick Series\": \"Séries do Candlestick\",\n\t\"Column Series\": \"Séries de Colunas\",\n\t\"Line Series\": \"Séries de Linhas\",\n\t\"Pie Slice Series\": \"Séries de Fatias de Pizza\",\n\t\"X/Y Series\": \"Séries de XY\",\n\n\t// Map-related stuff.\n\t\"Map\": \"Mapa\",\n\t\"Press ENTER to zoom in\": \"Pressione ENTER para aumentar o zoom\",\n\t\"Press ENTER to zoom out\": \"Pressione ENTER para diminuir o zoom\",\n\t\"Use arrow keys to zoom in and out\": \"Use as setas para diminuir ou aumentar o zoom\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"Use as teclas mais ou menos no seu teclado para diminuir ou aumentar o zoom\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"Exportar\",\n\t\"Image\": \"Imagem\",\n\t\"Data\": \"Dados\",\n\t\"Print\": \"Imprimir\",\n\t\"Press ENTER to open\": \"Clique, toque ou pressione ENTER para abrir\",\n\t\"Press ENTER to print.\": \"Clique, toque ou pressione ENTER para imprimir\",\n\t\"Press ENTER to export as %1.\": \"Clique, toque ou pressione ENTER para exportar como %1.\",\n\t\"(Press ESC to close this message)\": \"(Pressione ESC para fechar esta mensagem)\",\n\t\"Image Export Complete\": \"A exportação da imagem foi completada\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"A exportação da imagem demorou mais do que o experado. Algo deve ter dado errado.\",\n\t\"Saved from\": \"Salvo de\",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"Use TAB para selecionar os botões ou setas para a direita ou esquerda para mudar a seleção\",\n\t\"Use left and right arrows to move selection\": \"Use as setas para a esquerda ou direita para mover a seleção\",\n\t\"Use left and right arrows to move left selection\": \"Use as setas para a esquerda ou direita para mover a seleção da esquerda\",\n\t\"Use left and right arrows to move right selection\": \"Use as setas para a esquerda ou direita para mover a seleção da direita\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"Use TAB para selecionar os botões ou setas para cima ou para baixo para mudar a seleção\",\n\t\"Use up and down arrows to move selection\": \"Use as setas para cima ou para baixo para mover a seleção\",\n\t\"Use up and down arrows to move lower selection\": \"Use as setas para cima ou para baixo para mover a seleção de baixo\",\n\t\"Use up and down arrows to move upper selection\": \"Use as setas para cima ou para baixo para mover a seleção de cima\",\n\t\"From %1 to %2\": \"De %1 até %2\",\n\t\"From %1\": \"De %1\",\n\t\"To %1\": \"Até %1\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"Não há um interpretador para este arquivo: %1\",\n\t\"Error parsing file: %1\": \"Erro analizando o arquivo: %1\",\n\t\"Unable to load file: %1\": \"O arquivo não pôde ser carregado: %1\",\n\t\"Invalid date\": \"Data inválida\",\n};\n"], "names": ["am5locales_pt_BR", "_day"], "sourceRoot": ""}