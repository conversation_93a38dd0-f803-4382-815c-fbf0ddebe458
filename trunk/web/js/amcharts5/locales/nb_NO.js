"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[2820],{8462:function(e,_,r){r.r(_),r.d(_,{am5locales_nb_NO:function(){return a}});const a={_decimalSeparator:",",_thousandSeparator:" ",_percentPrefix:null,_percentSuffix:"%",_big_number_suffix_3:"k",_big_number_suffix_6:"M",_big_number_suffix_9:"G",_big_number_suffix_12:"T",_big_number_suffix_15:"P",_big_number_suffix_18:"E",_big_number_suffix_21:"Z",_big_number_suffix_24:"Y",_small_number_suffix_3:"m",_small_number_suffix_6:"μ",_small_number_suffix_9:"n",_small_number_suffix_12:"p",_small_number_suffix_15:"f",_small_number_suffix_18:"a",_small_number_suffix_21:"z",_small_number_suffix_24:"y",_byte_suffix_B:"B",_byte_suffix_KB:"KB",_byte_suffix_MB:"MB",_byte_suffix_GB:"GB",_byte_suffix_TB:"TB",_byte_suffix_PB:"PB",_date_millisecond:"mm:ss SSS",_date_millisecond_full:"HH:mm:ss SSS",_date_second:"HH:mm:ss",_date_second_full:"HH:mm:ss",_date_minute:"HH:mm",_date_minute_full:"HH:mm - dd. MMM yyyy",_date_hour:"HH:mm",_date_hour_full:"HH:mm - dd. MMM yyyy",_date_day:"dd. MMM",_date_day_full:"dd. MMM yyyy",_date_week:"ww",_date_week_full:"dd. MMM yyyy",_date_month:"MMM",_date_month_full:"MMM yyyy",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_millisecond_second:"ss.SSS",_duration_millisecond_minute:"mm:ss SSS",_duration_millisecond_hour:"hh:mm:ss SSS",_duration_millisecond_day:"d'd' mm:ss SSS",_duration_millisecond_week:"d'd' mm:ss SSS",_duration_millisecond_month:"M'm' dd'd' mm:ss SSS",_duration_millisecond_year:"y'y' MM'm' dd'd' mm:ss SSS",_duration_second:"ss",_duration_second_minute:"mm:ss",_duration_second_hour:"hh:mm:ss",_duration_second_day:"d'd' hh:mm:ss",_duration_second_week:"d'd' hh:mm:ss",_duration_second_month:"M'm' dd'd' hh:mm:ss",_duration_second_year:"y'y' MM'm' dd'd' hh:mm:ss",_duration_minute:"mm",_duration_minute_hour:"hh:mm",_duration_minute_day:"d'd' hh:mm",_duration_minute_week:"d'd' hh:mm",_duration_minute_month:"M'm' dd'd' hh:mm",_duration_minute_year:"y'y' MM'm' dd'd' hh:mm",_duration_hour:"hh'h'",_duration_hour_day:"d'd' hh'h'",_duration_hour_week:"d'd' hh'h'",_duration_hour_month:"M'm' dd'd' hh'h'",_duration_hour_year:"y'y' MM'm' dd'd' hh'h'",_duration_day:"d'd'",_duration_day_week:"d'd'",_duration_day_month:"M'm' dd'd'",_duration_day_year:"y'y' MM'm' dd'd'",_duration_week:"w'w'",_duration_week_month:"w'w'",_duration_week_year:"w'w'",_duration_month:"M'm'",_duration_month_year:"y'y' MM'm'",_duration_year:"y'y'",_era_ad:"e.Kr.",_era_bc:"f.Kr.",A:"a",P:"p",AM:"a.m.",PM:"p.m.","A.M.":"a.m.","P.M.":"p.m.",January:"januar",February:"februar",March:"mars",April:"april",May:"mai",June:"juni",July:"juli",August:"august",September:"september",October:"oktober",November:"november",December:"desember",Jan:"jan.",Feb:"feb.",Mar:"mar.",Apr:"apr.","May(short)":"mai",Jun:"jun.",Jul:"jul.",Aug:"aug.",Sep:"sep.",Oct:"okt.",Nov:"nov.",Dec:"des.",Sunday:"søndag",Monday:"mandag",Tuesday:"tirsdag",Wednesday:"onsdag",Thursday:"torsdag",Friday:"fredag",Saturday:"lørdag",Sun:"søn.",Mon:"man.",Tue:"tir.",Wed:"ons.",Thu:"tor.",Fri:"fre.",Sat:"lør.",_dateOrd:function(e){let _="th";if(e<11||e>13)switch(e%10){case 1:_="st";break;case 2:_="nd";break;case 3:_="rd"}return _},"Zoom Out":"Zoom",Play:"Spill av",Stop:"Stopp",Legend:"Tegnforklaring","Press ENTER to toggle":"",Loading:"Laster inn",Home:"Hjem",Chart:"","Serial chart":"","X/Y chart":"","Pie chart":"","Gauge chart":"","Radar chart":"","Sankey diagram":"","Flow diagram":"","Chord diagram":"","TreeMap chart":"","Sliced chart":"",Series:"","Candlestick Series":"","OHLC Series":"","Column Series":"","Line Series":"","Pie Slice Series":"","Funnel Series":"","Pyramid Series":"","X/Y Series":"",Map:"","Press ENTER to zoom in":"","Press ENTER to zoom out":"","Use arrow keys to zoom in and out":"","Use plus and minus keys on your keyboard to zoom in and out":"",Export:"Skriv ut",Image:"Bilde",Data:"Data",Print:"Skriv ut","Press ENTER to open":"","Press ENTER to print.":"","Press ENTER to export as %1.":"","(Press ESC to close this message)":"","Image Export Complete":"","Export operation took longer than expected. Something might have gone wrong.":"","Saved from":"",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"",HTML:"","Use TAB to select grip buttons or left and right arrows to change selection":"","Use left and right arrows to move selection":"","Use left and right arrows to move left selection":"","Use left and right arrows to move right selection":"","Use TAB select grip buttons or up and down arrows to change selection":"","Use up and down arrows to move selection":"","Use up and down arrows to move lower selection":"","Use up and down arrows to move upper selection":"","From %1 to %2":"Fra %1 til %2","From %1":"Fra %1","To %1":"Til %1","No parser available for file: %1":"","Error parsing file: %1":"","Unable to load file: %1":"","Invalid date":""}}},function(e){var _=(8462,e(e.s=8462)),r=window;for(var a in _)r[a]=_[a];_.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})}]);
//# sourceMappingURL=nb_NO.js.map