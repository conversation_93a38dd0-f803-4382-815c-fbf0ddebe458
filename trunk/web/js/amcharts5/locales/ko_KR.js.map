{"version": 3, "file": "locales/ko_KR.js", "mappings": "wJACO,MAAMA,ECQb,CAKC,kBAAqB,IACrB,mBAAsB,IAGtB,eAAkB,KAClB,eAAkB,IAUlB,qBAAwB,IACxB,qBAAwB,IACxB,qBAAwB,IACxB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IACzB,sBAAyB,IAEzB,uBAA0B,IAC1B,uBAA0B,IAC1B,uBAA0B,IAC1B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAC3B,wBAA2B,IAE3B,eAAkB,IAClB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KACnB,gBAAmB,KAanB,MAAS,aACT,kBAAqB,YACrB,uBAA0B,eAC1B,aAAgB,WAChB,kBAAqB,WACrB,aAAgB,QAChB,kBAAqB,uBACrB,WAAc,QACd,gBAAmB,uBACnB,UAAa,SACb,eAAkB,eAClB,WAAc,KACd,gBAAmB,eACnB,YAAe,MACf,iBAAoB,YACpB,WAAc,OAqBd,sBAAyB,MACzB,6BAAgC,SAChC,6BAAgC,YAChC,2BAA8B,eAC9B,0BAA6B,iBAC7B,2BAA8B,iBAC9B,4BAA+B,uBAC/B,2BAA8B,6BAE9B,iBAAoB,KACpB,wBAA2B,QAC3B,sBAAyB,WACzB,qBAAwB,gBACxB,sBAAyB,gBACzB,uBAA0B,sBAC1B,sBAAyB,4BAEzB,iBAAoB,KACpB,sBAAyB,QACzB,qBAAwB,aACxB,sBAAyB,aACzB,uBAA0B,mBAC1B,sBAAyB,yBAEzB,eAAkB,QAClB,mBAAsB,aACtB,oBAAuB,aACvB,qBAAwB,mBACxB,oBAAuB,yBAEvB,cAAiB,OACjB,mBAAsB,OACtB,oBAAuB,aACvB,mBAAsB,mBAEtB,eAAkB,OAClB,qBAAwB,OACxB,oBAAuB,OAEvB,gBAAmB,OACnB,qBAAwB,aAExB,eAAkB,OAGlB,QAAW,KACX,QAAW,KAUX,EAAK,KACL,EAAK,KACL,GAAM,KACN,GAAM,KACN,OAAQ,KACR,OAAQ,KAoBR,QAAW,KACX,SAAY,KACZ,MAAS,KACT,MAAS,KACT,IAAO,KACP,KAAQ,KACR,KAAQ,KACR,OAAU,KACV,UAAa,KACb,QAAW,MACX,SAAY,MACZ,SAAY,MACZ,IAAO,KACP,IAAO,KACP,IAAO,KACP,IAAO,KACP,aAAc,KACd,IAAO,KACP,IAAO,KACP,IAAO,KACP,IAAO,KACP,IAAO,MACP,IAAO,MACP,IAAO,MAGP,OAAU,MACV,OAAU,MACV,QAAW,MACX,UAAa,MACb,SAAY,MACZ,OAAU,MACV,SAAY,MACZ,IAAO,IACP,IAAO,IACP,IAAO,IACP,IAAO,IACP,IAAO,IACP,IAAO,IACP,IAAO,IAWP,SAAY,SAASC,GACpB,IAAIC,EAAM,IACV,GAAKD,EAAM,IAAQA,EAAM,GACxB,OAAQA,EAAM,IACb,KAAK,EAGL,KAAK,EAGL,KAAK,EACJC,EAAM,IAIT,OAAOA,CACR,EAIA,WAAY,KAGZ,KAAQ,KACR,KAAQ,KAGR,OAAU,KAGV,wBAAyB,6BAGzB,QAAW,SAIX,KAAQ,IAKR,MAAS,KACT,eAAgB,SAChB,YAAa,SACb,YAAa,QACb,cAAe,SACf,cAAe,SACf,iBAAkB,WAClB,eAAgB,YAChB,gBAAiB,WACjB,gBAAiB,SACjB,sBAAuB,aACvB,eAAgB,UAKhB,OAAU,MACV,qBAAsB,WACtB,cAAe,WACf,gBAAiB,SACjB,cAAe,SACf,mBAAoB,cACpB,gBAAiB,SACjB,iBAAkB,WAClB,aAAc,UAGd,IAAO,IACP,yBAA0B,kBAC1B,0BAA2B,kBAC3B,oCAAqC,0BACrC,8DAA+D,iCAY/D,OAAU,OACV,MAAS,MACT,KAAQ,MACR,MAAS,KACT,sBAAuB,0BACvB,wBAAyB,4BACzB,+BAAgC,kCAChC,oCAAqC,0BACrC,wBAAyB,cACzB,+EAAgF,sCAChF,aAAc,eACd,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,IAAO,GACP,KAAQ,GACR,IAAO,GACP,KAAQ,GACR,KAAQ,GAYR,8EAA+E,sCAC/E,8CAA+C,8BAC/C,mDAAoD,iCACpD,oDAAqD,kCACrD,wEAAyE,sCACzE,2CAA4C,8BAC5C,iDAAkD,iCAClD,iDAAkD,iCAClD,gBAAiB,cACjB,UAAW,QACX,QAAS,QAGT,mCAAoC,gBACpC,yBAA0B,eAC1B,0BAA2B,gBAC3B,eAAgB,a", "sources": ["webpack://@amcharts/amcharts5/./tmp/webpack/locales/ko_KR.js", "webpack://@amcharts/amcharts5/./src/locales/ko_KR.ts"], "sourcesContent": ["import m from \"./../../../dist/es2015/locales/ko_KR.js\";\nexport const am5locales_ko_KR = m;", "/**\n * amCharts 5 locale\n *\n * Locale: ko_KR\n * Language: Korean\n * Author: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>\n *\n * Follow instructions in [on this page](https://www.amcharts.com/docs/v5/concepts/locales/creating-translations/) to make corrections or add new translations.\n */\nexport default {\n\t// Number formatting options.\n\t//\n\t// Please check with the local standards which separator is accepted to be\n\t// used for separating decimals, and which for thousands.\n\t\"_decimalSeparator\": \".\",\n\t\"_thousandSeparator\": \",\",\n\n\t// Position of the percent sign in numbers\n\t\"_percentPrefix\": null,\n\t\"_percentSuffix\": \"%\",\n\n\t// Suffixes for numbers\n\t// When formatting numbers, big or small numers might be reformatted to\n\t// shorter version, by applying a suffix.\n\t//\n\t// For example, 1000000 might become \"1m\".\n\t// Or 1024 might become \"1KB\" if we're formatting byte numbers.\n\t//\n\t// This section defines such suffixes for all such cases.\n\t\"_big_number_suffix_3\": \"k\",\n\t\"_big_number_suffix_6\": \"M\",\n\t\"_big_number_suffix_9\": \"G\",\n\t\"_big_number_suffix_12\": \"T\",\n\t\"_big_number_suffix_15\": \"P\",\n\t\"_big_number_suffix_18\": \"E\",\n\t\"_big_number_suffix_21\": \"Z\",\n\t\"_big_number_suffix_24\": \"Y\",\n\n\t\"_small_number_suffix_3\": \"m\",\n\t\"_small_number_suffix_6\": \"μ\",\n\t\"_small_number_suffix_9\": \"n\",\n\t\"_small_number_suffix_12\": \"p\",\n\t\"_small_number_suffix_15\": \"f\",\n\t\"_small_number_suffix_18\": \"a\",\n\t\"_small_number_suffix_21\": \"z\",\n\t\"_small_number_suffix_24\": \"y\",\n\n\t\"_byte_suffix_B\": \"B\",\n\t\"_byte_suffix_KB\": \"KB\",\n\t\"_byte_suffix_MB\": \"MB\",\n\t\"_byte_suffix_GB\": \"GB\",\n\t\"_byte_suffix_TB\": \"TB\",\n\t\"_byte_suffix_PB\": \"PB\",\n\n\t// Default date formats for various periods.\n\t//\n\t// This should reflect official or de facto formatting universally accepted\n\t// in the country translation is being made for\n\t// Available format codes here:\n\t// https://www.amcharts.com/docs/v5/concepts/formatters/formatting-dates/#Format_codes\n\t//\n\t// This will be used when formatting date/time for particular granularity,\n\t// e.g. \"_date_hour\" will be shown whenever we need to show time as hours.\n\t// \n\t// \"date\" is used as in default date format when showing standalone dates.\n\t\"_date\": \"yyyy-MM-dd\",\n\t\"_date_millisecond\": \"mm:ss SSS\",\n\t\"_date_millisecond_full\": \"HH:mm:ss SSS\",\n\t\"_date_second\": \"HH:mm:ss\",\n\t\"_date_second_full\": \"HH:mm:ss\",\n\t\"_date_minute\": \"HH:mm\",\n\t\"_date_minute_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_hour\": \"HH:mm\",\n\t\"_date_hour_full\": \"HH:mm - MMM dd, yyyy\",\n\t\"_date_day\": \"MMM dd\",\n\t\"_date_day_full\": \"MMM dd, yyyy\",\n\t\"_date_week\": \"ww\",\n\t\"_date_week_full\": \"MMM dd, yyyy\",\n\t\"_date_month\": \"MMM\",\n\t\"_date_month_full\": \"MMM, yyyy\",\n\t\"_date_year\": \"yyyy\",\n\n\t// Default duration formats for various base units.\n\t//\n\t// This will be used by DurationFormatter to format numeric values into\n\t// duration.\n\t//\n\t// Notice how each duration unit comes in several versions. This is to ensure\n\t// that each base unit is shown correctly.\n\t//\n\t// For example, if we have baseUnit set to \"second\", meaning our duration is\n\t// in seconds.\n\t//\n\t// If we pass in `50` to formatter, it will know that we have just 50 seconds\n\t// (less than a minute) so it will use format in `\"_duration_second\"` (\"ss\"),\n\t// and the formatted result will be in like `\"50\"`.\n\t//\n\t// If we pass in `70`, which is more than a minute, the formatter will switch\n\t// to `\"_duration_second_minute\"` (\"mm:ss\"), resulting in \"01:10\" formatted\n\t// text.\n\n\t\"_duration_millisecond\": \"SSS\",\n\t\"_duration_millisecond_second\": \"ss.SSS\",\n\t\"_duration_millisecond_minute\": \"mm:ss SSS\",\n\t\"_duration_millisecond_hour\": \"hh:mm:ss SSS\",\n\t\"_duration_millisecond_day\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_week\": \"d'd' mm:ss SSS\",\n\t\"_duration_millisecond_month\": \"M'm' dd'd' mm:ss SSS\",\n\t\"_duration_millisecond_year\": \"y'y' MM'm' dd'd' mm:ss SSS\",\n\n\t\"_duration_second\": \"ss\",\n\t\"_duration_second_minute\": \"mm:ss\",\n\t\"_duration_second_hour\": \"hh:mm:ss\",\n\t\"_duration_second_day\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_week\": \"d'd' hh:mm:ss\",\n\t\"_duration_second_month\": \"M'm' dd'd' hh:mm:ss\",\n\t\"_duration_second_year\": \"y'y' MM'm' dd'd' hh:mm:ss\",\n\n\t\"_duration_minute\": \"mm\",\n\t\"_duration_minute_hour\": \"hh:mm\",\n\t\"_duration_minute_day\": \"d'd' hh:mm\",\n\t\"_duration_minute_week\": \"d'd' hh:mm\",\n\t\"_duration_minute_month\": \"M'm' dd'd' hh:mm\",\n\t\"_duration_minute_year\": \"y'y' MM'm' dd'd' hh:mm\",\n\n\t\"_duration_hour\": \"hh'h'\",\n\t\"_duration_hour_day\": \"d'd' hh'h'\",\n\t\"_duration_hour_week\": \"d'd' hh'h'\",\n\t\"_duration_hour_month\": \"M'm' dd'd' hh'h'\",\n\t\"_duration_hour_year\": \"y'y' MM'm' dd'd' hh'h'\",\n\n\t\"_duration_day\": \"d'd'\",\n\t\"_duration_day_week\": \"d'd'\",\n\t\"_duration_day_month\": \"M'm' dd'd'\",\n\t\"_duration_day_year\": \"y'y' MM'm' dd'd'\",\n\n\t\"_duration_week\": \"w'w'\",\n\t\"_duration_week_month\": \"w'w'\",\n\t\"_duration_week_year\": \"w'w'\",\n\n\t\"_duration_month\": \"M'm'\",\n\t\"_duration_month_year\": \"y'y' MM'm'\",\n\n\t\"_duration_year\": \"y'y'\",\n\n\t// Era translations\n\t\"_era_ad\": \"AD\",\n\t\"_era_bc\": \"BC\",\n\n\t// Day part, used in 12-hour formats, e.g. 5 P.M.\n\t// Please note that these come in 3 variants:\n\t// * one letter (e.g. \"A\")\n\t// * two letters (e.g. \"AM\")\n\t// * two letters with dots (e.g. \"A.M.\")\n\t//\n\t// All three need to to be translated even if they are all the same. Some\n\t// users might use one, some the other.\n\t\"A\": \"AM\",\n\t\"P\": \"PM\",\n\t\"AM\": \"AM\",\n\t\"PM\": \"PM\",\n\t\"A.M.\": \"오전\",\n\t\"P.M.\": \"오후\",\n\n\t// Date-related stuff.\n\t//\n\t// When translating months, if there's a difference, use the form which is\n\t// best for a full date, e.g. as you would use it in \"2018 January 1\".\n\t//\n\t// Note that May is listed twice. This is because in English May is the same\n\t// in both long and short forms, while in other languages it may not be the\n\t// case. Translate \"May\" to full word, while \"May(short)\" to shortened\n\t// version.\n\t//\n\t// Should month names and weekdays be capitalized or not?\n\t//\n\t// Rule of thumb is this: if the names should always be capitalized,\n\t// regardless of name position within date (\"January\", \"21st January 2018\",\n\t// etc.) use capitalized names. Otherwise enter all lowercase.\n\t//\n\t// The date formatter will automatically capitalize names if they are the\n\t// first (or only) word in resulting date.\n\t\"January\": \"1월\",\n\t\"February\": \"2월\",\n\t\"March\": \"3월\",\n\t\"April\": \"4월\",\n\t\"May\": \"5월\",\n\t\"June\": \"6월\",\n\t\"July\": \"7월\",\n\t\"August\": \"8월\",\n\t\"September\": \"9월\",\n\t\"October\": \"10월\",\n\t\"November\": \"11월\",\n\t\"December\": \"12월\",\n\t\"Jan\": \"1월\",\n\t\"Feb\": \"2월\",\n\t\"Mar\": \"3월\",\n\t\"Apr\": \"4월\",\n\t\"May(short)\": \"5월\",\n\t\"Jun\": \"6월\",\n\t\"Jul\": \"7월\",\n\t\"Aug\": \"8월\",\n\t\"Sep\": \"9월\",\n\t\"Oct\": \"10월\",\n\t\"Nov\": \"11월\",\n\t\"Dec\": \"12월\",\n\n\t// Weekdays.\n\t\"Sunday\": \"일요일\",\n\t\"Monday\": \"월요일\",\n\t\"Tuesday\": \"화요일\",\n\t\"Wednesday\": \"수요일\",\n\t\"Thursday\": \"목요일\",\n\t\"Friday\": \"금요일\",\n\t\"Saturday\": \"토요일\",\n\t\"Sun\": \"일\",\n\t\"Mon\": \"월\",\n\t\"Tue\": \"화\",\n\t\"Wed\": \"수\",\n\t\"Thu\": \"목\",\n\t\"Fri\": \"금\",\n\t\"Sat\": \"토\",\n\n\t// Date ordinal function.\n\t//\n\t// This is used when adding number ordinal when formatting days in dates.\n\t//\n\t// E.g. \"January 1st\", \"February 2nd\".\n\t//\n\t// The function accepts day number, and returns a string to be added to the\n\t// day, like in default English translation, if we pass in 2, we will receive\n\t// \"nd\" back.\n\t\"_dateOrd\": function(day: number): string {\n\t\tlet res = \"일\";\n\t\tif ((day < 11) || (day > 13)) {\n\t\t\tswitch (day % 10) {\n\t\t\t\tcase 1:\n\t\t\t\t\tres = \"일\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 2:\n\t\t\t\t\tres = \"일\";\n\t\t\t\t\tbreak;\n\t\t\t\tcase 3:\n\t\t\t\t\tres = \"일\"\n\t\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn res;\n\t},\n\n\t// Various chart controls.\n\t// Shown as a tooltip on zoom out button.\n\t\"Zoom Out\": \"축소\",\n\n\t// Timeline buttons\n\t\"Play\": \"시작\",\n\t\"Stop\": \"정지\",\n\n\t// Chart's Legend screen reader title.\n\t\"Legend\": \"범례\",\n\n\t// Legend's item screen reader indicator.\n\t\"Press ENTER to toggle\": \"켜고 끄려면 클릭, 탭 혹은 엔터를 눌러주세요.\",\n\n\t// Shown when the chart is busy loading something.\n\t\"Loading\": \"불러오는 중\",\n\n\t// Shown as the first button in the breadcrumb navigation, e.g.:\n\t// Home > First level > ...\n\t\"Home\": \"홈\",\n\n\t// Chart types.\n\t// Those are used as default screen reader titles for the main chart element\n\t// unless developer has set some more descriptive title.\n\t\"Chart\": \"차트\",\n\t\"Serial chart\": \"시리얼 차트\",\n\t\"X/Y chart\": \"X/Y 차트\",\n\t\"Pie chart\": \"파이 차트\",\n\t\"Gauge chart\": \"게이지 차트\",\n\t\"Radar chart\": \"레이더 차트\",\n\t\"Sankey diagram\": \"생키 다이어그램\",\n\t\"Flow diagram\": \"플로우 다이어그램\",\n\t\"Chord diagram\": \"코드 다이어그램\",\n\t\"TreeMap chart\": \"트리맵 차트\",\n\t\"Force directed tree\": \"포스 디렉티드 트리\",\n\t\"Sliced chart\": \"슬라이스 차트\",\n\n\t// Series types.\n\t// Used to name series by type for screen readers if they do not have their\n\t// name set.\n\t\"Series\": \"시리즈\",\n\t\"Candlestick Series\": \"캔들스틱 시리즈\",\n\t\"OHLC Series\": \"OHLC 시리즈\",\n\t\"Column Series\": \"컬럼 시리즈\",\n\t\"Line Series\": \"라인 시리즈\",\n\t\"Pie Slice Series\": \"파이 슬라이스 시리즈\",\n\t\"Funnel Series\": \"퍼널 시리즈\",\n\t\"Pyramid Series\": \"피라미드 시리즈\",\n\t\"X/Y Series\": \"X/Y 시리즈\",\n\n\t// Map-related stuff.\n\t\"Map\": \"맵\",\n\t\"Press ENTER to zoom in\": \"확대하려면 엔터를 누르세요.\",\n\t\"Press ENTER to zoom out\": \"축소하려면 엔터를 누르세요.\",\n\t\"Use arrow keys to zoom in and out\": \"확대 혹은 축소하려면 방향키를 이용하세요.\",\n\t\"Use plus and minus keys on your keyboard to zoom in and out\": \"확대 혹은 축소하려면 키보드의 +/- 키를 이용하세요.\",\n\n\t// Export-related stuff.\n\t// These prompts are used in Export menu labels.\n\t//\n\t// \"Export\" is the top-level menu item.\n\t//\n\t// \"Image\", \"Data\", \"Print\" as second-level indicating type of export\n\t// operation.\n\t//\n\t// Leave actual format untranslated, unless you absolutely know that they\n\t// would convey more meaning in some other way.\n\t\"Export\": \"내보내기\",\n\t\"Image\": \"이미지\",\n\t\"Data\": \"데이터\",\n\t\"Print\": \"인쇄\",\n\t\"Press ENTER to open\": \"열려면, 클릭, 탭 또는 엔터를 누르세요.\",\n\t\"Press ENTER to print.\": \"출력하려면, 클릭, 탭 또는 엔터를 누르세요.\",\n\t\"Press ENTER to export as %1.\": \"%1(으)로 내보내려면 클릭, 탭 또는 엔터를 누르세요.\",\n\t\"(Press ESC to close this message)\": \"(이 메시지를 끄려면 ESC를 누르세요.)\",\n\t\"Image Export Complete\": \"이미지 내보내기 완료\",\n\t\"Export operation took longer than expected. Something might have gone wrong.\": \"내보내기가 지연되고 있습니다. 문제가 없는지 확인이 필요합니다.\",\n\t\"Saved from\": \"다음으로부터 저장됨: \",\n\t\"PNG\": \"\",\n\t\"JPG\": \"\",\n\t\"GIF\": \"\",\n\t\"SVG\": \"\",\n\t\"PDF\": \"\",\n\t\"JSON\": \"\",\n\t\"CSV\": \"\",\n\t\"XLSX\": \"\",\n\t\"HTML\": \"\",\n\n\t// Scrollbar-related stuff.\n\t//\n\t// Scrollbar is a control which can zoom and pan the axes on the chart.\n\t//\n\t// Each scrollbar has two grips: left or right (for horizontal scrollbar) or\n\t// upper and lower (for vertical one).\n\t//\n\t// Prompts change in relation to whether Scrollbar is vertical or horizontal.\n\t//\n\t// The final section is used to indicate the current range of selection.\n\t\"Use TAB to select grip buttons or left and right arrows to change selection\": \"선택 범위를 변경하려면 선택 버튼이나 좌우 화살표를 이용하세요.\",\n\t\"Use left and right arrows to move selection\": \"선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\n\t\"Use left and right arrows to move left selection\": \"왼쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\n\t\"Use left and right arrows to move right selection\": \"오른쪽 선택 범위를 움직이려면 좌우 화살표를 이용하세요.\",\n\t\"Use TAB select grip buttons or up and down arrows to change selection\": \"선택 범위를 변경하려면 선택 버튼이나 상하 화살표를 이용하세요.\",\n\t\"Use up and down arrows to move selection\": \"선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\n\t\"Use up and down arrows to move lower selection\": \"하단 선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\n\t\"Use up and down arrows to move upper selection\": \"상단 선택 범위를 움직이려면 상하 화살표를 이용하세요.\",\n\t\"From %1 to %2\": \"%1 부터 %2 까지\",\n\t\"From %1\": \"%1 부터\",\n\t\"To %1\": \"%1 까지\",\n\n\t// Data loader-related.\n\t\"No parser available for file: %1\": \"파일 파싱 불가능: %1\",\n\t\"Error parsing file: %1\": \"파일 파싱 오류: %1\",\n\t\"Unable to load file: %1\": \"파일 로드 불가능: %1\",\n\t\"Invalid date\": \"날짜 올바르지 않음\",\n};"], "names": ["am5locales_ko_KR", "day", "res"], "sourceRoot": ""}