"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[5425],{4338:function(e,a,o){o.r(a),o.d(a,{am5locales_pt_BR:function(){return r}});const r={_decimalSeparator:",",_thousandSeparator:".",_percentPrefix:null,_percentSuffix:"%",_date_millisecond:"mm:ss SSS",_date_millisecond_full:"HH:mm:ss SSS",_date_second:"HH:mm:ss",_date_second_full:"HH:mm:ss",_date_minute:"HH:mm",_date_minute_full:"HH:mm - dd MMM",_date_hour:"HH:mm",_date_hour_full:"HH:mm - dd MMM",_date_day:"dd MMM",_date_day_full:"dd MMM",_date_week:"ww",_date_week_full:"dd MMM",_date_month:"MMM",_date_month_full:"MMM, yyyy",_date_year:"yyyy",_duration_millisecond:"SSS",_duration_second:"ss",_duration_minute:"mm",_duration_hour:"hh",_duration_day:"dd",_duration_week:"ww",_duration_month:"MM",_duration_year:"yyyy",_era_ad:"DC",_era_bc:"AC",A:"",P:"",AM:"",PM:"","A.M.":"","P.M.":"",January:"Janeiro",February:"Fevereiro",March:"Março",April:"Abril",May:"Maio",June:"Junho",July:"Julho",August:"Agosto",September:"Setembro",October:"Outubro",November:"Novembro",December:"Dezembro",Jan:"Jan",Feb:"Fev",Mar:"Mar",Apr:"Abr","May(short)":"Mai",Jun:"Jun",Jul:"Jul",Aug:"Ago",Sep:"Set",Oct:"Out",Nov:"Nov",Dec:"Dez",Sunday:"Domingo",Monday:"Segunda-feira",Tuesday:"Terça-feira",Wednesday:"Quarta-feira",Thursday:"Quinta-feira",Friday:"Sexta-feira",Saturday:"Sábado",Sun:"Dom",Mon:"Seg",Tue:"Ter",Wed:"Qua",Thu:"Qui",Fri:"Sex",Sat:"Sáb",_dateOrd:function(e){return"º"},"Zoom Out":"Reduzir Zoom",Play:"Play",Stop:"Parar",Legend:"Legenda","Press ENTER to toggle":"Clique, toque ou pressione ENTER para alternar",Loading:"Carregando",Home:"Início",Chart:"Gráfico","Serial chart":"Gráfico Serial","X/Y chart":"Gráfico XY","Pie chart":"Gráfico de Pizza","Gauge chart":"Gráfico Indicador","Radar chart":"Gráfico de Radar","Sankey diagram":"Diagrama Sankey","Chord diagram":"Diagram Chord","Flow diagram":"Diagrama Flow","TreeMap chart":"Gráfico de Mapa de Árvore",Series:"Séries","Candlestick Series":"Séries do Candlestick","Column Series":"Séries de Colunas","Line Series":"Séries de Linhas","Pie Slice Series":"Séries de Fatias de Pizza","X/Y Series":"Séries de XY",Map:"Mapa","Press ENTER to zoom in":"Pressione ENTER para aumentar o zoom","Press ENTER to zoom out":"Pressione ENTER para diminuir o zoom","Use arrow keys to zoom in and out":"Use as setas para diminuir ou aumentar o zoom","Use plus and minus keys on your keyboard to zoom in and out":"Use as teclas mais ou menos no seu teclado para diminuir ou aumentar o zoom",Export:"Exportar",Image:"Imagem",Data:"Dados",Print:"Imprimir","Press ENTER to open":"Clique, toque ou pressione ENTER para abrir","Press ENTER to print.":"Clique, toque ou pressione ENTER para imprimir","Press ENTER to export as %1.":"Clique, toque ou pressione ENTER para exportar como %1.","(Press ESC to close this message)":"(Pressione ESC para fechar esta mensagem)","Image Export Complete":"A exportação da imagem foi completada","Export operation took longer than expected. Something might have gone wrong.":"A exportação da imagem demorou mais do que o experado. Algo deve ter dado errado.","Saved from":"Salvo de",PNG:"",JPG:"",GIF:"",SVG:"",PDF:"",JSON:"",CSV:"",XLSX:"",HTML:"","Use TAB to select grip buttons or left and right arrows to change selection":"Use TAB para selecionar os botões ou setas para a direita ou esquerda para mudar a seleção","Use left and right arrows to move selection":"Use as setas para a esquerda ou direita para mover a seleção","Use left and right arrows to move left selection":"Use as setas para a esquerda ou direita para mover a seleção da esquerda","Use left and right arrows to move right selection":"Use as setas para a esquerda ou direita para mover a seleção da direita","Use TAB select grip buttons or up and down arrows to change selection":"Use TAB para selecionar os botões ou setas para cima ou para baixo para mudar a seleção","Use up and down arrows to move selection":"Use as setas para cima ou para baixo para mover a seleção","Use up and down arrows to move lower selection":"Use as setas para cima ou para baixo para mover a seleção de baixo","Use up and down arrows to move upper selection":"Use as setas para cima ou para baixo para mover a seleção de cima","From %1 to %2":"De %1 até %2","From %1":"De %1","To %1":"Até %1","No parser available for file: %1":"Não há um interpretador para este arquivo: %1","Error parsing file: %1":"Erro analizando o arquivo: %1","Unable to load file: %1":"O arquivo não pôde ser carregado: %1","Invalid date":"Data inválida"}}},function(e){var a=(4338,e(e.s=4338)),o=window;for(var r in a)o[r]=a[r];a.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})}]);
//# sourceMappingURL=pt_BR.js.map