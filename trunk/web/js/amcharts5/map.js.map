{"version": 3, "file": "map.js", "mappings": "88BAqGO,MAAeA,UAAkBC,EAAA,EAAxC,c,oBAUC,qC,gDAAwD,KAExD,0C,gDAA8C,KAC9C,6C,iDAAoC,IAEpC,wC,gDAA2D,KAC3D,2C,gDAA8D,IAiS/D,CA/RW,SAAAC,GACTC,KAAKC,OAAOC,KAAK,WAAY,gBAC7BF,KAAKG,eAAe,gBAAiB,YACrCH,KAAKG,eAAe,oBAAqB,gBACzCH,KAAKG,eAAe,UAAW,MAE/BH,KAAKI,GAAG,WAAYC,IACnB,IAAIC,EAAWN,KAAKO,cAAcF,QAC9BC,GAAYA,GAAYD,GAC3BL,KAAKQ,KAAKC,O,IAIZC,MAAMX,WACP,CAEU,cAAAY,GACT,MAAMN,EAAUL,KAAKY,IAAI,WACzB,IAAIN,EAAWN,KAAKO,cAAcF,QAE9BC,GAAYA,GAAYD,IAC3BL,KAAKO,cAAcF,aAAUQ,EAC7Bb,KAAKc,gBAAiB,GAGlBd,KAAKc,iBACTd,KAAKe,gBACLf,KAAKc,gBAAiB,EAExB,CAEO,gBAAAE,GAON,GANAN,MAAMM,mBAEFhB,KAAKiB,cACRjB,KAAKW,iBAGFX,KAAKY,IAAI,aAAeZ,KAAKkB,QAAQ,YAAclB,KAAKkB,QAAQ,YAAclB,KAAKkB,QAAQ,YAAa,CAE3GlB,KAAKW,iBAEL,MAAMQ,EAAQnB,KAAKmB,MAEbC,EAAUpB,KAAKY,IAAI,WAErBQ,IACCD,IACHA,EAAME,gBAAkB,MAEzB,OAAYD,GAAUE,IACrB,MAAMC,EAAWvB,KAAKwB,gBAAgBF,GAClCC,GACHvB,KAAKyB,iBAAiBF,E,KAKpBH,GAA6B,GAAlBA,EAAQM,SACvB,OAAY1B,KAAK2B,WAAYJ,IAC5BvB,KAAK4B,mBAAmBL,EAAS,IAElCvB,KAAK2B,UAAY,IAGlB,MAAME,EAAU7B,KAAKY,IAAI,WACrBiB,IACCV,IACHA,EAAME,gBAAkB,MAEzB,OAAYrB,KAAK8B,WAAYP,IAC5B,MAAMD,EAAKC,EAASX,IAAI,MACpBU,IAA8B,GAAxBO,EAAQE,QAAQT,GACzBtB,KAAKgC,oBAAoBT,GAGzBvB,KAAKiC,sBAAsBV,E,KAKzBM,IACJ,OAAY7B,KAAKkC,cAAeX,IAC/BvB,KAAKiC,sBAAsBV,EAAS,IAErCvB,KAAKkC,aAAe,G,CAIvB,CAEU,gBAAAT,CAAiBF,GAC1BvB,KAAKmC,gBAAgBZ,EAASX,IAAI,aAClC,OAAYZ,KAAK2B,UAAWJ,EAC7B,CAEU,kBAAAK,CAAmBL,GAC5BvB,KAAKoC,aAAab,EAASX,IAAI,YAAaZ,KAC7C,CAEU,mBAAAgC,CAAoBT,GAC7BvB,KAAKmC,gBAAgBZ,EAASX,IAAI,aAClC,OAAYZ,KAAKkC,aAAcX,EAChC,CAEU,qBAAAU,CAAsBV,GAC/BvB,KAAKoC,aAAab,EAASX,IAAI,YAAaZ,KAC7C,CAEU,YAAAqC,CAAaf,EAAYgB,EAAgCC,GAClE,GAAID,EAAU,CACb,GAAuB,GAAnBA,EAASZ,OACZ,OAAO,EAGP,IAA6B,GAAzBY,EAASP,QAAQT,GACpB,OAAO,C,CAKV,QAAIiB,GAAYA,EAASb,OAAS,IACJ,GAAzBa,EAASR,QAAQT,GAKvB,CAEU,aAAAP,GAET,MAAMV,EAAUL,KAAKY,IAAI,WACzB,GAAIP,EAAS,CAEZ,IAAImC,EAEgB,qBAAhBnC,EAAQoC,KACXD,EAAWnC,EAAQmC,SAEK,WAAhBnC,EAAQoC,KAChBD,EAAW,CAACnC,IAE0G,GAA9G,CAAC,QAAS,aAAc,UAAW,aAAc,kBAAmB,gBAAgB0B,QAAQ1B,EAAQoC,MAC5GD,EAAW,CAAC,CAAEE,SAAUrC,IAGxBsC,QAAQC,IAAI,4BAGb,MAAMC,EAAe7C,KAAKY,IAAI,gBAC9B,GAAI4B,EAAU,CAEb,MAAMM,EAAU9C,KAAKY,IAAI,UAAW,MAEpC,IAAK,IAAImC,EAAI,EAAGC,EAAMR,EAASd,OAAQqB,EAAIC,EAAKD,IAAK,CACpD,IAAIE,EAAeT,EAASO,GACxBL,EAAgBO,EAAQP,SAE5B,GAAIA,EAAU,CACb,IAAID,EAAOC,EAASD,KAChBnB,EAAa2B,EAAQH,GAMzB,GAJID,GAAgBA,EAAavB,KAChC2B,EAAQC,WAAWC,KAAON,EAAavB,KAGN,GAA9BtB,KAAKoD,OAAOrB,QAAQU,GAAa,CAKpC,IAAIlB,EASA8B,EAPM,MAAN/B,IAEHC,EAAW,OAAYvB,KAAK8B,WAAYwB,GAChCA,EAAM1C,IAAI,OAASU,KAMxBC,IACH8B,EAAa9B,EAASgC,aAIlBhC,EAQC8B,EAAWX,WACfW,EAAWX,SAAWA,EACtBW,EAAWG,aAAef,EAC1BlB,EAASkC,IAAI,WAAYf,GACzBnB,EAASkC,IAAI,eAAgBhB,GAC7BzC,KAAK0D,gBAAgBnC,KAZtB8B,EAAa,CAAEX,SAAUA,EAAUc,aAAcf,EAAMkB,iBAAiB,GACxEN,EAAWP,GAAWxB,EACtBtB,KAAKQ,KAAKN,KAAKmD,IAehB,qBAA2BJ,EAAQC,WAAYG,E,IAMnD,MAAMZ,EAAO,mBACTzC,KAAK4D,OAAOC,UAAUpB,IACzBzC,KAAK4D,OAAOE,SAASrB,EAAM,CAAEA,KAAMA,EAAMsB,OAAQ/D,M,CAGpD,CAOO,sBAAAgE,CAAuBC,GAC7BjE,KAAKkE,SAASC,UAAUnE,KAAKoE,iBAC9B,CAEO,uBAAAC,GAEP,CAKO,UAAAC,GACN,MAAMnD,EAAQnB,KAAKmB,MACnB,GAAIA,EACH,OAAOA,EAAMP,IAAI,aAEnB,CAKO,OAAA2D,GACN,MAAMpD,EAAQnB,KAAKmB,MACnB,GAAIA,EACH,OAAOA,EAAMqD,WAAW,UAE1B,CAEU,YAAApC,CAAaM,EAAe+B,GACrC,GAAI/B,GAAY+B,EAAO7D,IAAI,iBAAiB,GAAO,CAClDZ,KAAK0E,YAAYxE,KAAKwC,GAEtB,MAAMvB,EAAQnB,KAAKmB,MACfA,GACHA,EAAMwD,qB,CAGT,CAEU,eAAAxC,CAAgBO,GACzB,GAAIA,EAAU,CACb,SAAc1C,KAAK0E,YAAahC,GAEhC,MAAMvB,EAAQnB,KAAKmB,MACfA,GACHA,EAAMwD,qB,CAGT,CAEU,QAAAC,GACTlE,MAAMkE,WAEN,MAAMzD,EAAQnB,KAAKmB,MACfA,GACHA,EAAMsD,OAAOI,YAAY7E,KAE3B,CAEU,YAAA8E,GACTpE,MAAMoE,eACN9E,KAAKc,gBAAiB,EACtBd,KAAK+E,cAAc,UACpB,EA/SA,qC,gDAAkC,cAClC,sC,gDAA0CjF,EAAA,EAAOkF,WAAWC,OAAO,CAACpF,EAAUqF,c,kCCtGxE,MAAMC,EACX,WAAAC,GACEpF,KAAKqF,UAAY,IAAIC,aAAa,IAClCtF,KAAKuF,GAAK,CACZ,CACA,GAAAC,CAAIC,GACF,MAAMC,EAAI1F,KAAKqF,UACf,IAAItC,EAAI,EACR,IAAK,IAAI4C,EAAI,EAAGA,EAAI3F,KAAKuF,IAAMI,EAAI,GAAIA,IAAK,CAC1C,MAAMC,EAAIF,EAAEC,GACVE,EAAKJ,EAAIG,EACTE,EAAKC,KAAKC,IAAIP,GAAKM,KAAKC,IAAIJ,GAAKH,GAAKI,EAAKD,GAAKA,GAAKC,EAAKJ,GACxDK,IAAIJ,EAAE3C,KAAO+C,GACjBL,EAAII,CACN,CAGA,OAFAH,EAAE3C,GAAK0C,EACPzF,KAAKuF,GAAKxC,EAAI,EACP/C,IACT,CACA,OAAAiG,GACE,MAAMP,EAAI1F,KAAKqF,UACf,IAAiBI,EAAGG,EAAGE,EAAnBI,EAAIlG,KAAKuF,GAAcM,EAAK,EAChC,GAAIK,EAAI,EAAG,CAET,IADAL,EAAKH,IAAIQ,GACFA,EAAI,IACTT,EAAII,EACJD,EAAIF,IAAIQ,GACRL,EAAKJ,EAAIG,EACTE,EAAKF,GAAKC,EAAKJ,IACXK,KAEFI,EAAI,IAAOJ,EAAK,GAAKJ,EAAEQ,EAAI,GAAK,GAAOJ,EAAK,GAAKJ,EAAEQ,EAAI,GAAK,KAC9DN,EAAS,EAALE,EACJL,EAAII,EAAKD,EACLA,GAAKH,EAAII,IAAIA,EAAKJ,GAE1B,CACA,OAAOI,CACT,ECvCK,IAAIM,EAAU,KACVC,EAAW,MACXC,EAAKN,KAAKO,GACVC,EAASF,EAAK,EACdG,EAAYH,EAAK,EACjBI,EAAW,EAALJ,EAENK,EAAU,IAAML,EAChBM,EAAUN,EAAK,IAEfL,EAAMD,KAAKC,IACXY,EAAOb,KAAKa,KACZC,EAAQd,KAAKc,MACb,EAAMd,KAAKe,IACXC,EAAOhB,KAAKgB,KACZC,EAAMjB,KAAKiB,IAEXC,GADQlB,KAAKmB,MACLnB,KAAKkB,OACbrE,EAAMmD,KAAKnD,IAEX,GADMmD,KAAKoB,IACLpB,KAAKqB,KACXC,EAAOtB,KAAKsB,MAAQ,SAAS5B,GAAK,OAAOA,EAAI,EAAI,EAAIA,EAAI,GAAK,EAAI,CAAG,EACrE6B,EAAOvB,KAAKuB,KACZC,EAAMxB,KAAKwB,IAMf,SAASC,EAAK/B,GACnB,OAAOA,EAAI,EAAIc,EAASd,GAAK,GAAKc,EAASR,KAAKyB,KAAK/B,EACvD,CAEO,SAASgC,EAAShC,GACvB,OAAQA,EAAI,EAAIA,EAAI,IAAMA,CAC5B,CCnCe,SAASiC,IAAQ,CCAhC,SAASC,EAAejF,EAAUkF,GAC5BlF,GAAYmF,EAAmBC,eAAepF,EAASD,OACzDoF,EAAmBnF,EAASD,MAAMC,EAAUkF,EAEhD,CAEA,ICDIG,EACAC,EACAC,EACAC,EDFAC,EAAmB,CACrBC,QAAS,SAASC,EAAQT,GACxBD,EAAeU,EAAO3F,SAAUkF,EAClC,EACAU,kBAAmB,SAASD,EAAQT,GAElC,IADA,IAAIpF,EAAW6F,EAAO7F,SAAUO,GAAK,EAAGmD,EAAI1D,EAASd,SAC5CqB,EAAImD,GAAGyB,EAAenF,EAASO,GAAGL,SAAUkF,EACvD,GAGEC,EAAqB,CACvBU,OAAQ,SAASF,EAAQT,GACvBA,EAAOY,QACT,EACAC,MAAO,SAASJ,EAAQT,GACtBS,EAASA,EAAOK,YAChBd,EAAOe,MAAMN,EAAO,GAAIA,EAAO,GAAIA,EAAO,GAC5C,EACAO,WAAY,SAASP,EAAQT,GAE3B,IADA,IAAIc,EAAcL,EAAOK,YAAa3F,GAAK,EAAGmD,EAAIwC,EAAYhH,SACrDqB,EAAImD,GAAGmC,EAASK,EAAY3F,GAAI6E,EAAOe,MAAMN,EAAO,GAAIA,EAAO,GAAIA,EAAO,GACrF,EACAQ,WAAY,SAASR,EAAQT,GAC3BkB,EAAWT,EAAOK,YAAad,EAAQ,EACzC,EACAmB,gBAAiB,SAASV,EAAQT,GAEhC,IADA,IAAIc,EAAcL,EAAOK,YAAa3F,GAAK,EAAGmD,EAAIwC,EAAYhH,SACrDqB,EAAImD,GAAG4C,EAAWJ,EAAY3F,GAAI6E,EAAQ,EACrD,EACAoB,QAAS,SAASX,EAAQT,GACxBqB,EAAcZ,EAAOK,YAAad,EACpC,EACAsB,aAAc,SAASb,EAAQT,GAE7B,IADA,IAAIc,EAAcL,EAAOK,YAAa3F,GAAK,EAAGmD,EAAIwC,EAAYhH,SACrDqB,EAAImD,GAAG+C,EAAcP,EAAY3F,GAAI6E,EAChD,EACAuB,mBAAoB,SAASd,EAAQT,GAEnC,IADA,IAAIwB,EAAaf,EAAOe,WAAYrG,GAAK,EAAGmD,EAAIkD,EAAW1H,SAClDqB,EAAImD,GAAGyB,EAAeyB,EAAWrG,GAAI6E,EAChD,GAGF,SAASkB,EAAWJ,EAAad,EAAQyB,GACvC,IAA6CC,EAAzCvG,GAAK,EAAGmD,EAAIwC,EAAYhH,OAAS2H,EAErC,IADAzB,EAAO2B,cACExG,EAAImD,GAAGoD,EAAaZ,EAAY3F,GAAI6E,EAAOe,MAAMW,EAAW,GAAIA,EAAW,GAAIA,EAAW,IACnG1B,EAAO4B,SACT,CAEA,SAASP,EAAcP,EAAad,GAClC,IAAI7E,GAAK,EAAGmD,EAAIwC,EAAYhH,OAE5B,IADAkG,EAAO6B,iBACE1G,EAAImD,GAAG4C,EAAWJ,EAAY3F,GAAI6E,EAAQ,GACnDA,EAAO8B,YACT,CAEe,WAASrB,EAAQT,GAC1BS,GAAUF,EAAiBL,eAAeO,EAAO5F,MACnD0F,EAAiBE,EAAO5F,MAAM4F,EAAQT,GAEtCD,EAAeU,EAAQT,EAE3B,CC1DA,IAAI+B,EAAe,CACjBnB,OAAQd,EACRiB,MAAOjB,EACP6B,UAMF,WACEI,EAAahB,MAAQiB,EACrBD,EAAaH,QAAUK,CACzB,EAREL,QAAS9B,EACT+B,aAAc/B,EACdgC,WAAYhC,GAQd,SAASmC,IACPF,EAAahB,MAAQgB,EAAaH,QAAU9B,CAC9C,CAEA,SAASkC,EAAiBE,EAAQC,GAEhC/B,EADA8B,GAAUnD,EACQsB,EAAU,EADT8B,GAAOpD,GACYuB,EAAU,EAAI6B,GACpDJ,EAAahB,MAAQqB,CACvB,CAEA,SAASA,EAAYF,EAAQC,GAC3BD,GAAUnD,EACV,IAAIsD,EAAS,EADMF,GAAOpD,GAEtBuD,EAAS,EAAIH,GACbI,EAAQnE,EAAI8D,EAAS9B,GACrBoC,EAAW,EAAID,GAEf1E,EAAIyE,EADO,EAAIC,GAEfvE,EAAIsC,EAAU+B,EAAShC,EAAUiC,EAASE,EAC1CC,EAAIpC,EAAUgC,EAAS/B,EAAUgC,EAASE,EAC9CrC,EAAUvC,IAAIqB,EAAMS,EAAK7B,EAAIA,EAAIG,EAAIA,GAAIyE,IACzCrC,EAAU8B,EAAQ7B,EAAUgC,EAAQ/B,EAAUgC,CAChD,CAEe,WAAS7B,GAGtB,OAFAN,EAAY,IAAI5C,EAChByC,EAAOS,EAAQsB,IACP5B,CACV,CClDA,IAAIW,EAAc,CAAC,KAAM,MACrBL,EAAS,CAAC5F,KAAM,aAAciG,YAAaA,GAEhC,WAAS4B,EAAGC,GAGzB,OAFA7B,EAAY,GAAK4B,EACjB5B,EAAY,GAAK6B,EACV,EAAOlC,EAChB,CCkCO,MAAMmC,UAAgBC,EAAA,EAA7B,c,oBAQC,+C,iDAAsC,GAqNvC,CAnNQ,cAAAC,GAGN,GAFAhK,MAAMgK,iBAEF1K,KAAK2K,kBAAoB3K,KAAKkB,QAAQ,aAAelB,KAAKkB,QAAQ,aAAc,CACnF,MAAMwB,EAAW1C,KAAKY,IAAI,YAC1B,GAAI8B,EAAU,CACb,MAAM+B,EAASzE,KAAKwE,WAAW,UAC/B,GAAIC,EAAQ,CACX,MAAMtD,EAAQsD,EAAOtD,MACrB,GAAIA,EAAO,CACV,MAAMmD,EAAanD,EAAMP,IAAI,cAC7B,IAAIgK,EAA2B,KAE3BtG,GAAcA,EAAWsG,YAC5BA,EAAYtG,EAAWsG,YACvBtG,EAAWuG,UAAU7K,KAAKY,IAAI,YAAa,MAE5C,MAAMW,EAAWvB,KAAKuB,SAChBgD,EAAUpD,EAAMqD,WAAW,WACjC,GAAID,GAAWhD,EAEd,GADAvB,KAAK8K,QAAS,EAC0C,YAApDvJ,EAASX,IAAI,WAAY6D,EAAO7D,IAAI,aAA4B,CAEnE,MAAM8B,EAAW1C,KAAKY,IAAI,YAE1B,GAAI8B,EAAU,CACb,IAAIgG,EAAchG,EAASgG,YAC3B,GAAIA,EAAa,CAEhB,IAAIqC,EAEiB,cAAjBrI,EAASD,KACZsI,EAAW,CAACrC,GAEa,mBAAjBhG,EAASD,OACjBsI,EAAWrC,GAGZ1I,KAAKyD,IAAI,QAASuH,IACjB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAASrJ,OAAQuJ,IAAK,CACzC,IAAIC,EAAUH,EAASE,GACvB,GAAIC,EAAQxJ,OAAS,EAAG,CACvB,MAAMyJ,EAAMD,EAAQ,GACdE,EAAKjK,EAAMkK,QAAQ,CAAEC,UAAWH,EAAI,GAAII,SAAUJ,EAAI,KAC5DH,EAAQQ,OAAOJ,EAAG3F,EAAG2F,EAAGxF,GAExB,IAAK,IAAIF,EAAI,EAAGA,EAAIwF,EAAQxJ,OAAQgE,IAAK,CACxC,MAAM+F,EAAKP,EAAQxF,GACbgG,EAAKvK,EAAMkK,QAAQ,CAAEC,UAAWG,EAAG,GAAIF,SAAUE,EAAG,KAC1DT,EAAQQ,OAAOE,EAAGjG,EAAGiG,EAAG9F,E,cAS9B5F,KAAKyD,IAAI,QAASkI,IACbrH,IAAyC,IAA3BG,EAAO7D,IAAI,aAC5B0D,EAAWsG,UAAU,KAGtBrG,EAAQqH,QAAQ5L,KAAK2L,UACrBpH,EAAQ7B,GACR6B,EAAQqH,QAAQ,MAEZtH,GAAcA,EAAWsG,WAC5BtG,EAAWsG,UAAUA,E,MAQ5B,MAAMnI,EAAO,cACTzC,KAAK4D,OAAOC,UAAUpB,IACzBzC,KAAK4D,OAAOE,SAASrB,EAAM,CAAEA,KAAMA,EAAMsB,OAAQ/D,M,CAGpD,CAKO,mBAAA6L,GACN7L,KAAK8L,YACL9L,KAAK2K,kBAAmB,CACzB,CAEO,WAAAoB,GACNrL,MAAMqL,cACN/L,KAAK2K,kBAAmB,CACzB,CAEO,gBAAAqB,GACN,IAAIC,EAAWjM,KAAKY,IAAI,YACpBsL,EAAWlM,KAAKY,IAAI,YAEpB6E,EAAI,EACJG,EAAI,EAUR,GARI,WAAeqG,KAClBxG,EAAIwG,GAGD,WAAeC,KAClBtG,EAAIsG,GAGDD,aAAoBE,EAAA,GAAS,CAChC,MAAMC,EAAWpM,KAAKqM,mBAAmBJ,EAAS3I,OAC5CmB,EAASzE,KAAKwE,WAAW,UAC/B,GAAIC,EAAQ,CACX,MAAMtD,EAAQsD,EAAOtD,MACrB,GAAIA,EAAO,CACV,MAAMwH,EAAQxH,EAAMkK,QAAQe,GAC5B3G,EAAIkD,EAAMlD,EACVG,EAAI+C,EAAM/C,C,GAKb,MAAO,CAAEH,IAAGG,IACb,CAQO,kBAAAyG,CAAmBC,GAEzB,MAAM5J,EAAW1C,KAAKY,IAAI,YACpB6D,EAASzE,KAAKwE,WAAW,UACzBrD,EAAQsD,EAAOtD,MACfI,EAAWvB,KAAKuB,SAEtB,GAAImB,GAAY+B,GAAUtD,GAASI,EAAU,CAC5C,MAAMgL,EAAWhL,EAASX,IAAI,WAAY6D,EAAO7D,IAAI,aACrD,IAGI4L,EAGAC,EACAC,EAPAC,EAAwB,EAAUjK,GAClCkK,EAA0B,EAG1BC,EAAoB,EACpBC,EAAoB,EAIpBpE,EAAchG,EAASgG,YAC3B,GAAIA,EAAa,CAChB,IAAIqC,EAEiB,cAAjBrI,EAASD,KACZsI,EAAW,CAACrC,GAEa,mBAAjBhG,EAASD,OACjBsI,EAAWrC,GAGZ,IAAK,IAAIuC,EAAI,EAAGA,EAAIF,EAASrJ,OAAQuJ,IAAK,CACzC,IAAIC,EAAUH,EAASE,GACvB,GAAIC,EAAQxJ,OAAS,GACpB,IAAK,IAAIgE,EAAI,EAAGA,EAAIwF,EAAQxJ,OAAQgE,IASnC,GARA+G,EAASvB,EAAQxF,EAAI,GACrBgH,EAASxB,EAAQxF,GAEjBmH,EAAYD,EAAkBD,EAC9BH,EAAa,EAAYC,EAAQC,GACjCE,GAAmBJ,EACnBM,EAAYF,EAAkBD,EAE1BE,GAAaP,GAAYQ,EAAYR,EAAU,CAClDrB,EAAIF,EAASrJ,OACb,K,OAIwB,GAAlBwJ,EAAQxJ,SAChB+K,EAASvB,EAAQ,GACjBwB,EAASxB,EAAQ,GACjB2B,EAAY,EACZC,EAAY,E,CAId,GAAIL,GAAUC,EAAQ,CACrB,IACIK,EADAC,GAAsBV,EAAWO,IAAcC,EAAYD,GAG/D,GAAgB,YAAZN,EAAwB,CAC3B,IAAInB,EAAKjK,EAAMkK,QAAQ,CAAEC,UAAWmB,EAAO,GAAIlB,SAAUkB,EAAO,KAC5DQ,EAAK9L,EAAMkK,QAAQ,CAAEC,UAAWoB,EAAO,GAAInB,SAAUmB,EAAO,KAE5DjH,EAAI2F,EAAG3F,GAAKwH,EAAGxH,EAAI2F,EAAG3F,GAAKuH,EAC3BpH,EAAIwF,EAAGxF,GAAKqH,EAAGrH,EAAIwF,EAAGxF,GAAKoH,EAE/B,OAAO7L,EAAM+L,OAAO,CAAEzH,EAAGA,EAAGG,EAAGA,G,CAI/B,OADAmH,EC7PS,SAASzC,EAAGC,GACzB,IAAI4C,EAAK7C,EAAE,GAAK3D,EACZyG,EAAK9C,EAAE,GAAK3D,EACZ0G,EAAK9C,EAAE,GAAK5D,EACZ2G,EAAK/C,EAAE,GAAK5D,EACZ4G,EAAM,EAAIH,GACVI,EAAM,EAAIJ,GACVK,EAAM,EAAIH,GACVI,EAAM,EAAIJ,GACVK,EAAMJ,EAAM,EAAIJ,GAChBS,EAAML,EAAM,EAAIJ,GAChBU,EAAMJ,EAAM,EAAIJ,GAChBS,EAAML,EAAM,EAAIJ,GAChBU,EAAI,EAAIvG,EAAKF,EAAKG,EAAS6F,EAAKF,GAAMG,EAAME,EAAMhG,EAAS4F,EAAKF,KAChEa,EAAI,EAAID,GAERE,EAAcF,EAAI,SAASG,GAC7B,IAAIC,EAAI,EAAID,GAAKH,GAAKC,EAClBI,EAAI,EAAIL,EAAIG,GAAKF,EACjBvI,EAAI2I,EAAIT,EAAMQ,EAAIN,EAClBjI,EAAIwI,EAAIR,EAAMO,EAAIL,EAClBzD,EAAI+D,EAAIZ,EAAMW,EAAIT,EACtB,MAAO,CACL7G,EAAMjB,EAAGH,GAAKiB,EACdG,EAAMwD,EAAG/C,EAAK7B,EAAIA,EAAIG,EAAIA,IAAMc,EAEpC,EAAI,WACF,MAAO,CAACyG,EAAKzG,EAAS0G,EAAK1G,EAC7B,EAIA,OAFAuH,EAAYI,SAAWN,EAEhBE,CACT,CD4NiB,CAAexB,EAAQC,EAAvB,CAA+BM,GACnC,CAAE1B,UAAWyB,EAAS,GAAIxB,SAAUwB,EAAS,G,GAMxD,MAAO,CAAEzB,UAAW,EAAGC,SAAU,EAClC,EAtNA,qC,gDAAkC,YAClC,sC,gDAA0Cd,EAAA,EAASzF,WAAWC,OAAO,CAACuF,EAAQtF,c,yBEiCxE,MAAMoJ,WAAsBzO,EAAnC,c,oBAyBC,uC,gDAAkD,IAAI0O,EAAA,EACrDC,GAAA,GAASC,IAAI,CAAC,IACd,IAAMjE,EAAQkE,KAAK1O,KAAK2O,MAAO,CAAC,EAAG,CAAC3O,KAAK4O,SAASC,eAUnD,qC,gDAAwD,CAAC,aAAc,oBAkKxE,CArMW,SAAA9O,GACTC,KAAKC,OAAOC,KAAK,YACjBF,KAAKG,eAAe,gBAAiB,YACrCO,MAAMX,WACP,CAKO,WAAA+O,CAAYvN,GAClB,MAAMwN,EAAU/O,KAAKkE,SAAShE,KAAKF,KAAK4O,SAASI,QAGjD,OAFAD,EAAQE,aAAa1N,GACrBvB,KAAK4O,SAAS1O,KAAK6O,GACZA,CACR,CA0BO,mBAAAlD,GACN,OAAY7L,KAAK8B,WAAYP,IAC5B,IAAIwN,EAAUxN,EAASX,IAAI,WACvBmO,GACHA,EAAQlD,qB,GAGX,CAEO,gBAAA7K,GACNN,MAAMM,mBAEFhB,KAAKkB,QAAQ,WAChBlB,KAAK4O,SAASC,SAASpL,IAAI,SAAUzD,KAAKY,IAAI,UAEhD,CAEU,eAAA8C,CAAgBnC,GACzBb,MAAMgD,gBAAgBnC,GAEtB,IAAIwN,EAAUxN,EAASX,IAAI,WACtBmO,IACJA,EAAU/O,KAAK8O,YAAYvN,IAG5BvB,KAAKkP,uBAAuB3N,GAC5BA,EAASnB,GAAG,mBAAmB,KAC9BJ,KAAKkP,uBAAuB3N,EAAS,IAGtCA,EAASkC,IAAI,UAAWsL,GAExB/O,KAAKoC,aAAab,EAASX,IAAI,YAAaZ,MAE5C+O,EAAQI,WAAW,SAAUnP,KAC9B,CAEU,sBAAAkP,CAAuB3N,GAEhC,MAAM6N,EAAkB7N,EAASX,IAAI,mBACjCwO,IACH,OAAYA,GAAkBzG,IAE7BA,EAAMvI,GAAG,YAAY,KACpBJ,KAAKqP,gBAAgB9N,EAAS,IAG/BoH,EAAMvI,GAAG,aAAa,KACrBJ,KAAKqP,gBAAgB9N,EAAS,IAG/BoH,EAAMvI,GAAG,YAAY,KACpBJ,KAAKqP,gBAAgB9N,EAAS,GAC7B,IAGHvB,KAAKqP,gBAAgB9N,GAEvB,CAOO,eAAA8N,CAAgB9N,GAEtB,GADAb,MAAM2O,kBACF9N,EAAU,CACb,MAAMwN,EAAUxN,EAASX,IAAI,WAC7B,GAAImO,EAAS,CACZ,MAAMK,EAAkB7N,EAASX,IAAI,mBACrC,GAAIwO,EAAiB,CACpB,IAAI1G,EAAoC,GACxC,OAAY0G,GAAkBzG,IAC7B,MAAM2C,EAAY3C,EAAM/H,IAAI,aACtB2K,EAAW5C,EAAM/H,IAAI,YAC3B,GAAiB,MAAb0K,GAAiC,MAAZC,EACxB7C,EAAYxI,KAAK,CAACoL,EAAWC,QAEzB,CACJ,MAAM7I,EAAWiG,EAAM/H,IAAI,YAC3B,GAAI8B,EAAU,CACb,MAAM4M,EAAS5M,EAASgG,YACpB4G,GACH5G,EAAYxI,KAAK,CAACoP,EAAO,GAAWA,EAAO,I,MAM/C,IAAI5M,EAAgB,CAAED,KAAM,aAAciG,YAAaA,GAEvDnH,EAASgO,OAAO,WAAY7M,GAC5BqM,EAAQtL,IAAI,WAAYf,E,MAGxBqM,EAAQtL,IAAI,WAAYlC,EAASX,IAAI,Y,EAIzC,CAKO,eAAA4O,CAAgBjO,GACtBb,MAAM8O,gBAAgBjO,GACtB,MAAMwN,EAAUxN,EAASX,IAAI,WACzBmO,IACH/O,KAAK4O,SAAS/J,YAAYkK,GAC1BA,EAAQU,UAEV,CAKU,gBAAAhO,CAAiBF,GAC1Bb,MAAMe,iBAAiBF,GACvB,MAAMwN,EAAUxN,EAASX,IAAI,WACzBmO,GACHA,EAAQI,WAAW,WAAW,EAEhC,CAKU,kBAAAvN,CAAmBL,GAC5Bb,MAAMkB,mBAAmBL,GACzB,MAAMwN,EAAUxN,EAASX,IAAI,WACzBmO,GACHA,EAAQI,WAAW,WAAW,EAEhC,CAKU,mBAAAnN,CAAoBT,GAC7Bb,MAAMsB,oBAAoBT,GAC1B,MAAMwN,EAAUxN,EAASX,IAAI,WACzBmO,GACHA,EAAQI,WAAW,WAAW,EAEhC,CAKU,qBAAAlN,CAAsBV,GAC/Bb,MAAMuB,sBAAsBV,GAC5B,MAAMwN,EAAUxN,EAASX,IAAI,WACzBmO,GACHA,EAAQI,WAAW,WAAW,EAEhC,ECzRc,SAASO,GAAMC,EAAOC,EAAMC,GACzCF,GAASA,EAAOC,GAAQA,EAAMC,GAAQ3J,EAAI4J,UAAUpO,QAAU,GAAKkO,EAAOD,EAAOA,EAAQ,EAAG,GAAKzJ,EAAI,EAAI,GAAK2J,EAM9G,IAJA,IAAI9M,GAAK,EACLmD,EAAoD,EAAhDH,KAAKgK,IAAI,EAAGhK,KAAKgB,MAAM6I,EAAOD,GAASE,IAC3CH,EAAQ,IAAIM,MAAM9J,KAEbnD,EAAImD,GACXwJ,EAAM3M,GAAK4M,EAAQ5M,EAAI8M,EAGzB,OAAOH,CACT,CCTA,SAASO,GAAW7C,EAAIE,EAAI4C,GAC1B,IAAItK,EAAI8J,GAAMtC,EAAIE,EAAKnH,EAAS+J,GAAIjL,OAAOqI,GAC3C,OAAO,SAAS7H,GAAK,OAAOG,EAAEuK,KAAI,SAASvK,GAAK,MAAO,CAACH,EAAGG,EAAI,GAAI,CACrE,CAEA,SAASwK,GAAWjD,EAAIE,EAAIgD,GAC1B,IAAI5K,EAAIiK,GAAMvC,EAAIE,EAAKlH,EAASkK,GAAIpL,OAAOoI,GAC3C,OAAO,SAASzH,GAAK,OAAOH,EAAE0K,KAAI,SAAS1K,GAAK,MAAO,CAACA,EAAGG,EAAI,GAAI,CACrE,CFsGC,sC,gDAAkC,kBAClC,uC,gDAA0C/F,EAAUmF,WAAWC,OAAO,CAACqJ,GAAcpJ,cGvF/E,MAAMoL,WAAwBhC,GAArC,c,oBAQC,wC,gDAA2DtO,KAAKuQ,aAAa,CAAC,IAiD/E,CA/CW,SAAAxQ,GACTW,MAAMX,YACNC,KAAK8B,UAAU5B,KAAKF,KAAKwQ,WACzBxQ,KAAKyQ,WACN,CAEO,eAAAC,GAON,GANAhQ,MAAMgQ,kBAEF1Q,KAAKkB,QAAQ,SAChBlB,KAAKyQ,YAGFzQ,KAAKkB,QAAQ,eAEZlB,KAAKY,IAAI,cAAe,CAC3B,MAAMO,EAAQnB,KAAKmB,MACfA,GACHA,EAAMyC,OAAOxD,GAAG,oBAAoB,KACnCJ,KAAKyQ,WAAW,IAGlBzQ,KAAKyQ,W,CAGR,CAEU,SAAAA,GACT,IAAIE,EDpDS,WACb,IAAItD,EAAIF,EAAIyD,EAAIC,EACZvD,EAAIF,EAAI0D,EAAIC,EAEZtL,EAAGG,EAAGoL,EAAGC,EADTZ,EAAK,GAAIH,EAAKG,EAAIa,EAAK,GAAIC,EAAK,IAEhCtG,EAAY,IAEhB,SAAS8F,IACP,MAAO,CAAClO,KAAM,kBAAmBiG,YAAa0I,IAChD,CAEA,SAASA,IACP,OAAO1B,GAAM3I,EAAK8J,EAAKK,GAAMA,EAAIN,EAAIM,GAAIf,IAAIa,GACxC/L,OAAOyK,GAAM3I,EAAKgK,EAAKI,GAAMA,EAAIL,EAAIK,GAAIhB,IAAIc,IAC7ChM,OAAOyK,GAAM3I,EAAKoG,EAAKkD,GAAMA,EAAIhD,EAAIgD,GAAIgB,QAAO,SAAS5L,GAAK,OAAOO,EAAIP,EAAIyL,GAAM/K,CAAS,IAAGgK,IAAI1K,IACnGR,OAAOyK,GAAM3I,EAAKqG,EAAK8C,GAAMA,EAAI5C,EAAI4C,GAAImB,QAAO,SAASzL,GAAK,OAAOI,EAAIJ,EAAIuL,GAAMhL,CAAS,IAAGgK,IAAIvK,GAC1G,CAoEA,OAlEA+K,EAAUS,MAAQ,WAChB,OAAOA,IAAQjB,KAAI,SAASzH,GAAe,MAAO,CAACjG,KAAM,aAAciG,YAAaA,EAAc,GACpG,EAEAiI,EAAUW,QAAU,WAClB,MAAO,CACL7O,KAAM,UACNiG,YAAa,CACXsI,EAAEH,GAAI5L,OACNgM,EAAEH,GAAIS,MAAM,GACZP,EAAEJ,GAAIY,UAAUD,MAAM,GACtBN,EAAEF,GAAIS,UAAUD,MAAM,KAG5B,EAEAZ,EAAUc,OAAS,SAASC,GAC1B,OAAK5B,UAAUpO,OACRiP,EAAUgB,YAAYD,GAAGE,YAAYF,GADdf,EAAUiB,aAE1C,EAEAjB,EAAUgB,YAAc,SAASD,GAC/B,OAAK5B,UAAUpO,QACfmP,GAAMa,EAAE,GAAG,GAAId,GAAMc,EAAE,GAAG,GAC1BX,GAAMW,EAAE,GAAG,GAAIZ,GAAMY,EAAE,GAAG,GACtBb,EAAKD,IAAIc,EAAIb,EAAIA,EAAKD,EAAIA,EAAKc,GAC/BX,EAAKD,IAAIY,EAAIX,EAAIA,EAAKD,EAAIA,EAAKY,GAC5Bf,EAAU9F,UAAUA,IALG,CAAC,CAACgG,EAAIE,GAAK,CAACH,EAAIE,GAMhD,EAEAH,EAAUiB,YAAc,SAASF,GAC/B,OAAK5B,UAAUpO,QACfyL,GAAMuE,EAAE,GAAG,GAAIrE,GAAMqE,EAAE,GAAG,GAC1BtE,GAAMsE,EAAE,GAAG,GAAIpE,GAAMoE,EAAE,GAAG,GACtBvE,EAAKE,IAAIqE,EAAIvE,EAAIA,EAAKE,EAAIA,EAAKqE,GAC/BtE,EAAKE,IAAIoE,EAAItE,EAAIA,EAAKE,EAAIA,EAAKoE,GAC5Bf,EAAU9F,UAAUA,IALG,CAAC,CAACsC,EAAIC,GAAK,CAACC,EAAIC,GAMhD,EAEAqD,EAAUd,KAAO,SAAS6B,GACxB,OAAK5B,UAAUpO,OACRiP,EAAUkB,UAAUH,GAAGI,UAAUJ,GADVf,EAAUmB,WAE1C,EAEAnB,EAAUkB,UAAY,SAASH,GAC7B,OAAK5B,UAAUpO,QACfwP,GAAMQ,EAAE,GAAIP,GAAMO,EAAE,GACbf,GAFuB,CAACO,EAAIC,EAGrC,EAEAR,EAAUmB,UAAY,SAASJ,GAC7B,OAAK5B,UAAUpO,QACf2O,GAAMqB,EAAE,GAAIxB,GAAMwB,EAAE,GACbf,GAFuB,CAACN,EAAIH,EAGrC,EAEAS,EAAU9F,UAAY,SAAS6G,GAC7B,OAAK5B,UAAUpO,QACfmJ,GAAa6G,EACbjM,EAAIwK,GAAW7C,EAAIE,EAAI,IACvB1H,EAAIwK,GAAWjD,EAAIE,EAAIxC,GACvBmG,EAAIf,GAAWc,EAAID,EAAI,IACvBG,EAAIb,GAAWS,EAAID,EAAI/F,GAChB8F,GANuB9F,CAOhC,EAEO8F,EACFgB,YAAY,CAAC,EAAE,KAAM,GAAKxL,GAAU,CAAC,IAAK,GAAKA,KAC/CyL,YAAY,CAAC,EAAE,KAAM,GAAKzL,GAAU,CAAC,IAAK,GAAKA,IACtD,CCnCkB,GAEhB,GAAIwK,EAAW,CACd,GAAI3Q,KAAKY,IAAI,cAAe,CAC3B,MAAMO,EAAQnB,KAAKmB,MACnB,GAAIA,EAAO,CACV,MAAM4Q,EAAY5Q,EAAM4Q,YACpBA,GACHpB,EAAUc,OAAO,CAAC,CAACM,EAAUC,KAAMD,EAAUE,QAAS,CAACF,EAAUG,MAAOH,EAAUI,M,EAKrF,MAAMtC,EAAO7P,KAAKY,IAAI,OAAQ,IAC9B+P,EAAUmB,UAAU,CAAC,IAAK,MAC1BnB,EAAUkB,UAAU,CAAChC,EAAMA,IAC3B7P,KAAKwQ,UAAU/M,IAAI,WAAYkN,I,CAEjC,EAtDA,sC,gDAAkC,oBAClC,uC,gDAA0CrC,GAActJ,WAAWC,OAAO,CAACqL,GAAgBpL,c,0BC9B7E,YAASoF,EAAGC,GAEzB,SAAS6H,EAAQ3M,EAAGG,GAClB,OAAOH,EAAI6E,EAAE7E,EAAGG,GAAI2E,EAAE9E,EAAE,GAAIA,EAAE,GAChC,CAMA,OAJI6E,EAAE4C,QAAU3C,EAAE2C,SAAQkF,EAAQlF,OAAS,SAASzH,EAAGG,GACrD,OAAOH,EAAI8E,EAAE2C,OAAOzH,EAAGG,KAAS0E,EAAE4C,OAAOzH,EAAE,GAAIA,EAAE,GACnD,GAEO2M,CACT,CCRA,SAASC,GAAiBvI,EAAQC,GAEhC,OADI/D,EAAI8D,GAAUzD,IAAIyD,GAAU/D,KAAKuM,MAAMxI,EAASrD,GAAOA,GACpD,CAACqD,EAAQC,EAClB,CAIO,SAASwI,GAAcC,EAAaC,EAAUC,GACnD,OAAQF,GAAe/L,GAAQgM,GAAYC,EAAaN,GAAQO,GAAeH,GAAcI,GAAiBH,EAAUC,IACpHC,GAAeH,GACdC,GAAYC,EAAaE,GAAiBH,EAAUC,GACrDL,EACN,CAEA,SAASQ,GAAsBL,GAC7B,OAAO,SAAS1I,EAAQC,GAGtB,OADI/D,EADJ8D,GAAU0I,GACQnM,IAAIyD,GAAU/D,KAAKuM,MAAMxI,EAASrD,GAAOA,GACpD,CAACqD,EAAQC,EAClB,CACF,CAEA,SAAS4I,GAAeH,GACtB,IAAIM,EAAWD,GAAsBL,GAErC,OADAM,EAAS5F,OAAS2F,IAAuBL,GAClCM,CACT,CAEA,SAASF,GAAiBH,EAAUC,GAClC,IAAIK,EAAc,EAAIN,GAClBO,EAAc,EAAIP,GAClBQ,EAAgB,EAAIP,GACpBQ,EAAgB,EAAIR,GAExB,SAASI,EAAShJ,EAAQC,GACxB,IAAIG,EAAS,EAAIH,GACbtE,EAAI,EAAIqE,GAAUI,EAClBtE,EAAI,EAAIkE,GAAUI,EAClBG,EAAI,EAAIN,GACRiE,EAAI3D,EAAI0I,EAActN,EAAIuN,EAC9B,MAAO,CACLnM,EAAMjB,EAAIqN,EAAgBjF,EAAIkF,EAAezN,EAAIsN,EAAc1I,EAAI2I,GACnExL,EAAKwG,EAAIiF,EAAgBrN,EAAIsN,GAEjC,CAcA,OAZAJ,EAAS5F,OAAS,SAASpD,EAAQC,GACjC,IAAIG,EAAS,EAAIH,GACbtE,EAAI,EAAIqE,GAAUI,EAClBtE,EAAI,EAAIkE,GAAUI,EAClBG,EAAI,EAAIN,GACRiE,EAAI3D,EAAI4I,EAAgBrN,EAAIsN,EAChC,MAAO,CACLrM,EAAMjB,EAAIqN,EAAgB5I,EAAI6I,EAAezN,EAAIsN,EAAc/E,EAAIgF,GACnExL,EAAKwG,EAAI+E,EAActN,EAAIuN,GAE/B,EAEOF,CACT,CC5De,cACb,IACIK,EADA/B,EAAQ,GAEZ,MAAO,CACLzI,MAAO,SAASlD,EAAGG,EAAGwN,GACpBD,EAAKjT,KAAK,CAACuF,EAAGG,EAAGwN,GACnB,EACA7J,UAAW,WACT6H,EAAMlR,KAAKiT,EAAO,GACpB,EACA3J,QAAS9B,EACT2L,OAAQ,WACFjC,EAAM1P,OAAS,GAAG0P,EAAMlR,KAAKkR,EAAMkC,MAAMrO,OAAOmM,EAAMmC,SAC5D,EACAC,OAAQ,WACN,IAAIA,EAASpC,EAGb,OAFAA,EAAQ,GACR+B,EAAO,KACAK,CACT,EAEJ,CCrBe,YAASlJ,EAAGC,GACzB,OAAOvE,EAAIsE,EAAE,GAAKC,EAAE,IAAMpE,GAAWH,EAAIsE,EAAE,GAAKC,EAAE,IAAMpE,CAC1D,CCDA,SAASsN,GAAa9K,EAAO+K,EAAQC,EAAOC,GAC1C5T,KAAKyF,EAAIkD,EACT3I,KAAKqK,EAAIqJ,EACT1T,KAAK6T,EAAIF,EACT3T,KAAK8T,EAAIF,EACT5T,KAAK+T,GAAI,EACT/T,KAAKkG,EAAIlG,KAAK0F,EAAI,IACpB,CAKe,YAASqF,EAAUiJ,EAAqBC,EAAahG,EAAarG,GAC/E,IAEI7E,EACAmD,EAHAgO,EAAU,GACVC,EAAO,GAyBX,GArBApJ,EAASqJ,SAAQ,SAASlJ,GACxB,MAAKhF,EAAIgF,EAAQxJ,OAAS,IAAM,GAAhC,CACA,IAAIwE,EAAqCT,EAAlC2F,EAAKF,EAAQ,GAAI+B,EAAK/B,EAAQhF,GAErC,GAAImO,GAAWjJ,EAAI6B,GAAK,CACtB,IAAK7B,EAAG,KAAO6B,EAAG,GAAI,CAEpB,IADArF,EAAO2B,YACFxG,EAAI,EAAGA,EAAImD,IAAKnD,EAAG6E,EAAOe,OAAOyC,EAAKF,EAAQnI,IAAI,GAAIqI,EAAG,IAE9D,YADAxD,EAAO4B,SAET,CAEAyD,EAAG,IAAM,EAAI9G,CACf,CAEA+N,EAAQhU,KAAKuF,EAAI,IAAIgO,GAAarI,EAAIF,EAAS,MAAM,IACrDiJ,EAAKjU,KAAKuF,EAAEoO,EAAI,IAAIJ,GAAarI,EAAI,KAAM3F,GAAG,IAC9CyO,EAAQhU,KAAKuF,EAAI,IAAIgO,GAAaxG,EAAI/B,EAAS,MAAM,IACrDiJ,EAAKjU,KAAKuF,EAAEoO,EAAI,IAAIJ,GAAaxG,EAAI,KAAMxH,GAAG,GAjBL,CAkB3C,IAEKyO,EAAQxS,OAAb,CAMA,IAJAyS,EAAKG,KAAKN,GACV,GAAKE,GACL,GAAKC,GAEApR,EAAI,EAAGmD,EAAIiO,EAAKzS,OAAQqB,EAAImD,IAAKnD,EACpCoR,EAAKpR,GAAG+Q,EAAIG,GAAeA,EAO7B,IAJA,IACIP,EACA/K,EAFAgH,EAAQuE,EAAQ,KAIV,CAIR,IAFA,IAAIK,EAAU5E,EACV6E,GAAY,EACTD,EAAQR,OAAQQ,EAAUA,EAAQrO,KAAOyJ,EAAO,OACvD+D,EAASa,EAAQlK,EACjBzC,EAAO2B,YACP,EAAG,CAED,GADAgL,EAAQR,EAAIQ,EAAQV,EAAEE,GAAI,EACtBQ,EAAQT,EAAG,CACb,GAAIU,EACF,IAAKzR,EAAI,EAAGmD,EAAIwN,EAAOhS,OAAQqB,EAAImD,IAAKnD,EAAG6E,EAAOe,OAAOA,EAAQ+K,EAAO3Q,IAAI,GAAI4F,EAAM,SAEtFsF,EAAYsG,EAAQ9O,EAAG8O,EAAQrO,EAAET,EAAG,EAAGmC,GAEzC2M,EAAUA,EAAQrO,CACpB,KAAO,CACL,GAAIsO,EAEF,IADAd,EAASa,EAAQ7O,EAAE2E,EACdtH,EAAI2Q,EAAOhS,OAAS,EAAGqB,GAAK,IAAKA,EAAG6E,EAAOe,OAAOA,EAAQ+K,EAAO3Q,IAAI,GAAI4F,EAAM,SAEpFsF,EAAYsG,EAAQ9O,EAAG8O,EAAQ7O,EAAED,GAAI,EAAGmC,GAE1C2M,EAAUA,EAAQ7O,CACpB,CAEAgO,GADAa,EAAUA,EAAQV,GACDxJ,EACjBmK,GAAaA,CACf,QAAUD,EAAQR,GAClBnM,EAAO4B,SACT,CA5C2B,CA6C7B,CAEA,SAAS,GAAKiL,GACZ,GAAMvO,EAAIuO,EAAM/S,OAAhB,CAKA,IAJA,IAAIwE,EAGAqE,EAFAxH,EAAI,EACJuH,EAAImK,EAAM,KAEL1R,EAAImD,GACXoE,EAAEpE,EAAIqE,EAAIkK,EAAM1R,GAChBwH,EAAE7E,EAAI4E,EACNA,EAAIC,EAEND,EAAEpE,EAAIqE,EAAIkK,EAAM,GAChBlK,EAAE7E,EAAI4E,CAXyB,CAYjC,CCpGO,SAASoK,GAAUC,GACxB,MAAO,CAAC9N,EAAM8N,EAAU,GAAIA,EAAU,IAAKnN,EAAKmN,EAAU,IAC5D,CAEO,SAASA,GAAUD,GACxB,IAAI5K,EAAS4K,EAAU,GAAI3K,EAAM2K,EAAU,GAAIxK,EAAS,EAAIH,GAC5D,MAAO,CAACG,EAAS,EAAIJ,GAASI,EAAS,EAAIJ,GAAS,EAAIC,GAC1D,CAEO,SAAS6K,GAAatK,EAAGC,GAC9B,OAAOD,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,EAC9C,CAEO,SAASsK,GAAevK,EAAGC,GAChC,MAAO,CAACD,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,GAAID,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,GAAID,EAAE,GAAKC,EAAE,GAAKD,EAAE,GAAKC,EAAE,GACvF,CAGO,SAASuK,GAAoBxK,EAAGC,GACrCD,EAAE,IAAMC,EAAE,GAAID,EAAE,IAAMC,EAAE,GAAID,EAAE,IAAMC,EAAE,EACxC,CAEO,SAASwK,GAAeC,EAAQhH,GACrC,MAAO,CAACgH,EAAO,GAAKhH,EAAGgH,EAAO,GAAKhH,EAAGgH,EAAO,GAAKhH,EACpD,CAGO,SAASiH,GAA0BlH,GACxC,IAAImH,EAAI5N,EAAKyG,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAKA,EAAE,GAAKA,EAAE,IAClDA,EAAE,IAAMmH,EAAGnH,EAAE,IAAMmH,EAAGnH,EAAE,IAAMmH,CAChC,CC5BA,SAAS5J,GAAU3C,GACjB,OAAO3C,EAAI2C,EAAM,KAAOtC,EAAKsC,EAAM,GAAKtB,EAAKsB,EAAM,MAAQ3C,EAAI2C,EAAM,IAAMtC,GAAMI,EAAMJ,EACzF,CCAe,SAAS8O,GAAMC,GAC5B,OAAOpF,MAAMqF,KAPf,UAAkBD,GAChB,IAAK,MAAMX,KAASW,QACXX,CAEX,CAGoBa,CAAQF,GAC5B,CCFe,YAASG,EAAcC,EAAUvH,EAAa0B,GAC3D,OAAO,SAAS8F,GACd,IAIIC,EACA3K,EACA4K,EANAxC,EAAOqC,EAASC,GAChBG,EAAa,KACbC,EAAWL,EAASI,GACpBE,GAAiB,EAKjB3B,EAAO,CACTxL,MAAOA,EACPY,UAAWA,EACXC,QAASA,EACTC,aAAc,WACZ0K,EAAKxL,MAAQoN,EACb5B,EAAK5K,UAAYyM,EACjB7B,EAAK3K,QAAUyM,EACflL,EAAW,GACX2K,EAAU,EACZ,EACAhM,WAAY,WACVyK,EAAKxL,MAAQA,EACbwL,EAAK5K,UAAYA,EACjB4K,EAAK3K,QAAUA,EACfuB,EAAWoK,GAAMpK,GACjB,IAAIkJ,EFxBG,SAASyB,EAAS/M,GAC/B,IAAImB,EAASwB,GAAU3C,GACnBoB,EAAMpB,EAAM,GACZsB,EAAS,EAAIF,GACbmM,EAAS,CAAC,EAAIpM,IAAU,EAAIA,GAAS,GACrCqM,EAAQ,EACRC,EAAU,EAEVC,EAAM,IAAIlR,EAEC,IAAX8E,EAAcF,EAAMxD,EAASJ,GACZ,IAAZ8D,IAAeF,GAAOxD,EAASJ,GAExC,IAAK,IAAIpD,EAAI,EAAGmD,EAAIwP,EAAQhU,OAAQqB,EAAImD,IAAKnD,EAC3C,GAAMqQ,GAAKuC,EAAOD,EAAQ3S,IAAIrB,OAS9B,IARA,IAAIiU,EACAvC,EACAkD,EAASX,EAAKvC,EAAI,GAClBpL,EAAUsD,GAAUgL,GACpBC,EAAOD,EAAO,GAAK,EAAI9P,EACvByB,EAAU,EAAIsO,GACdrO,EAAU,EAAIqO,GAET5Q,EAAI,EAAGA,EAAIyN,IAAKzN,EAAGqC,EAAUwO,EAASvO,EAAUwO,EAASvO,EAAUwO,EAASJ,EAASK,EAAQ,CACpG,IAAIA,EAAShB,EAAKhQ,GACd6Q,EAAUlL,GAAUqL,GACpBC,EAAOD,EAAO,GAAK,EAAInQ,EACvBiQ,EAAU,EAAIG,GACdF,EAAU,EAAIE,GACdzM,EAAQqM,EAAUxO,EAClBX,EAAO8C,GAAS,EAAI,GAAK,EACzB0M,EAAWxP,EAAO8C,EAClB2M,EAAeD,EAAWxQ,EAC1B2H,EAAI/F,EAAUwO,EAOlB,GALAJ,EAAI7Q,IAAIqB,EAAMmH,EAAI3G,EAAO,EAAIwP,GAAW3O,EAAUwO,EAAU1I,EAAI,EAAI6I,KACpEV,GAASW,EAAe3M,EAAQ9C,EAAOZ,EAAM0D,EAIzC2M,EAAe9O,GAAW8B,EAAS0M,GAAW1M,EAAQ,CACxD,IAAIiN,EAAMlC,GAAeF,GAAU2B,GAAS3B,GAAUgC,IACtD1B,GAA0B8B,GAC1B,IAAIC,EAAenC,GAAeqB,EAAQa,GAC1C9B,GAA0B+B,GAC1B,IAAIC,GAAUH,EAAe3M,GAAS,GAAK,EAAI,GAAK3C,EAAKwP,EAAa,KAClEjN,EAAMkN,GAAUlN,IAAQkN,IAAWF,EAAI,IAAMA,EAAI,OACnDX,GAAWU,EAAe3M,GAAS,EAAI,GAAK,EAEhD,CACF,CAcF,OAAQgM,GAAShQ,GAAWgQ,EAAQhQ,GAAWkQ,GAAOjQ,GAAuB,EAAVgQ,CACrE,CEzC0Bc,CAAgBxB,EAAS/F,GACvC5E,EAASrJ,QACNoU,IAAgBL,EAAKhM,eAAgBqM,GAAiB,GAC3D,GAAW/K,EAAUiJ,GAAqBC,EAAahG,EAAawH,IAC3DxB,IACJ6B,IAAgBL,EAAKhM,eAAgBqM,GAAiB,GAC3DL,EAAKlM,YACL0E,EAAY,KAAM,KAAM,EAAGwH,GAC3BA,EAAKjM,WAEHsM,IAAgBL,EAAK/L,aAAcoM,GAAiB,GACxD/K,EAAW2K,EAAU,IACvB,EACAlN,OAAQ,WACNiN,EAAKhM,eACLgM,EAAKlM,YACL0E,EAAY,KAAM,KAAM,EAAGwH,GAC3BA,EAAKjM,UACLiM,EAAK/L,YACP,GAGF,SAASf,EAAMmB,EAAQC,GACjBwL,EAAazL,EAAQC,IAAM0L,EAAK9M,MAAMmB,EAAQC,EACpD,CAEA,SAASoN,EAAUrN,EAAQC,GACzBoJ,EAAKxK,MAAMmB,EAAQC,EACrB,CAEA,SAASR,IACP4K,EAAKxL,MAAQwO,EACbhE,EAAK5J,WACP,CAEA,SAASC,IACP2K,EAAKxL,MAAQA,EACbwK,EAAK3J,SACP,CAEA,SAASuM,EAAUjM,EAAQC,GACzB4L,EAAKzV,KAAK,CAAC4J,EAAQC,IACnB8L,EAASlN,MAAMmB,EAAQC,EACzB,CAEA,SAASiM,IACPH,EAAStM,YACToM,EAAO,EACT,CAEA,SAASM,IACPF,EAAUJ,EAAK,GAAG,GAAIA,EAAK,GAAG,IAC9BE,EAASrM,UAET,IAEIzG,EAA4BqQ,EAC5BlI,EACAvC,EAJAyO,EAAQvB,EAASuB,QACjBC,EAAezB,EAAWpC,SACvBtN,EAAImR,EAAa3V,OAQxB,GAJAiU,EAAKrC,MACLoC,EAAQxV,KAAKyV,GACbA,EAAO,KAEFzP,EAGL,GAAY,EAARkR,GAEF,IAAKhE,GADLlI,EAAUmM,EAAa,IACN3V,OAAS,GAAK,EAAG,CAGhC,IAFKoU,IAAgBL,EAAKhM,eAAgBqM,GAAiB,GAC3DL,EAAKlM,YACAxG,EAAI,EAAGA,EAAIqQ,IAAKrQ,EAAG0S,EAAK9M,OAAOA,EAAQuC,EAAQnI,IAAI,GAAI4F,EAAM,IAClE8M,EAAKjM,SACP,OAMEtD,EAAI,GAAa,EAARkR,GAAWC,EAAanX,KAAKmX,EAAa/D,MAAMrO,OAAOoS,EAAa9D,UAEjFxI,EAAS7K,KAAKmX,EAAahG,OAAOiG,IACpC,CAEA,OAAOnD,CACT,CACF,CAEA,SAASmD,GAAapM,GACpB,OAAOA,EAAQxJ,OAAS,CAC1B,CAIA,SAASsS,GAAoB1J,EAAGC,GAC9B,QAASD,EAAIA,EAAE7E,GAAG,GAAK,EAAI6E,EAAE,GAAK/D,EAASJ,EAAUI,EAAS+D,EAAE,MACvDC,EAAIA,EAAE9E,GAAG,GAAK,EAAI8E,EAAE,GAAKhE,EAASJ,EAAUI,EAASgE,EAAE,GAClE,CP1HA8H,GAAiBnF,OAASmF,GQL1B,OAAe8B,IACb,WAAa,OAAO,CAAM,IAS5B,SAA8BvM,GAC5B,IAGIwP,EAHApP,EAAUuP,IACVhB,EAAOgB,IACPC,EAAQD,IAGZ,MAAO,CACLhO,UAAW,WACT3B,EAAO2B,YACP6N,EAAQ,CACV,EACAzO,MAAO,SAAS6N,EAASI,GACvB,IAAIa,EAAQjB,EAAU,EAAInQ,GAAMA,EAC5B8D,EAAQnE,EAAIwQ,EAAUxO,GACtBhC,EAAImE,EAAQ9D,GAAMF,GACpByB,EAAOe,MAAMX,EAASuO,GAAQA,EAAOK,GAAQ,EAAI,EAAIrQ,GAAUA,GAC/DqB,EAAOe,MAAM6O,EAAOjB,GACpB3O,EAAO4B,UACP5B,EAAO2B,YACP3B,EAAOe,MAAM8O,EAAOlB,GACpB3O,EAAOe,MAAM6N,EAASD,GACtBa,EAAQ,GACCI,IAAUC,GAAStN,GAAS9D,IACjCL,EAAIgC,EAAUwP,GAASrR,IAAS6B,GAAWwP,EAAQrR,GACnDH,EAAIwQ,EAAUiB,GAAStR,IAASqQ,GAAWiB,EAAQtR,GACvDoQ,EAoBR,SAAmCvO,EAASuO,EAAMC,EAASI,GACzD,IAAI1O,EACAwO,EACAgB,EAAoB,EAAI1P,EAAUwO,GACtC,OAAOxQ,EAAI0R,GAAqBvR,EAC1BS,GAAM,EAAI2P,IAASG,EAAU,EAAIE,IAAS,EAAIJ,GAC1C,EAAII,IAAS1O,EAAU,EAAIqO,IAAS,EAAIvO,KACvCE,EAAUwO,EAAUgB,KACxBnB,EAAOK,GAAQ,CACxB,CA7Bee,CAA0B3P,EAASuO,EAAMC,EAASI,GACzDhP,EAAOe,MAAM6O,EAAOjB,GACpB3O,EAAO4B,UACP5B,EAAO2B,YACP3B,EAAOe,MAAM8O,EAAOlB,GACpBa,EAAQ,GAEVxP,EAAOe,MAAMX,EAAUwO,EAASD,EAAOK,GACvCY,EAAQC,CACV,EACAjO,QAAS,WACP5B,EAAO4B,UACPxB,EAAUuO,EAAOgB,GACnB,EACAH,MAAO,WACL,OAAO,EAAIA,CACb,EAEJ,IAaA,SAAqC/B,EAAMuC,EAAIC,EAAWjQ,GACxD,IAAImC,EACJ,GAAY,MAARsL,EACFtL,EAAM8N,EAAYtR,EAClBqB,EAAOe,OAAOtC,EAAI0D,GAClBnC,EAAOe,MAAM,EAAGoB,GAChBnC,EAAOe,MAAMtC,EAAI0D,GACjBnC,EAAOe,MAAMtC,EAAI,GACjBuB,EAAOe,MAAMtC,GAAK0D,GAClBnC,EAAOe,MAAM,GAAIoB,GACjBnC,EAAOe,OAAOtC,GAAK0D,GACnBnC,EAAOe,OAAOtC,EAAI,GAClBuB,EAAOe,OAAOtC,EAAI0D,QACb,GAAI/D,EAAIqP,EAAK,GAAKuC,EAAG,IAAMzR,EAAS,CACzC,IAAI2D,EAASuL,EAAK,GAAKuC,EAAG,GAAKvR,GAAMA,EACrC0D,EAAM8N,EAAY/N,EAAS,EAC3BlC,EAAOe,OAAOmB,EAAQC,GACtBnC,EAAOe,MAAM,EAAGoB,GAChBnC,EAAOe,MAAMmB,EAAQC,EACvB,MACEnC,EAAOe,MAAMiP,EAAG,GAAIA,EAAG,GAE3B,GApFE,EAAEvR,GAAKE,ICPM,YAASd,GACtB,OAAO,WACL,OAAOA,CACT,CACF,CCEO,SAASqS,GAAalQ,EAAQmQ,EAAQ5N,EAAO0N,EAAWG,EAAIC,GACjE,GAAK9N,EAAL,CACA,IAAI+N,EAAY,EAAIH,GAChBI,EAAY,EAAIJ,GAChBlI,EAAOgI,EAAY1N,EACb,MAAN6N,GACFA,EAAKD,EAASF,EAAYpR,EAC1BwR,EAAKF,EAASlI,EAAO,IAErBmI,EAAKI,GAAaF,EAAWF,GAC7BC,EAAKG,GAAaF,EAAWD,IACzBJ,EAAY,EAAIG,EAAKC,EAAKD,EAAKC,KAAID,GAAMH,EAAYpR,IAE3D,IAAK,IAAIkC,EAAOuF,EAAI8J,EAAIH,EAAY,EAAI3J,EAAI+J,EAAK/J,EAAI+J,EAAI/J,GAAK2B,EAC5DlH,EAAQ+L,GAAU,CAACwD,GAAYC,EAAY,EAAIjK,IAAKiK,EAAY,EAAIjK,KACpEtG,EAAOe,MAAMA,EAAM,GAAIA,EAAM,GAdb,CAgBpB,CAGA,SAASyP,GAAaF,EAAWvP,IAC/BA,EAAQgM,GAAUhM,IAAc,IAAMuP,EACtCjD,GAA0BtM,GAC1B,ItBJmBlD,EsBIfsS,GtBJetS,GsBIAkD,EAAM,ItBHd,EAAI,EAAIlD,GAAK,EAAIY,EAAKN,KAAKsS,KAAK5S,GsBI3C,SAAUkD,EAAM,GAAK,GAAKoP,EAASA,GAAUtR,EAAMN,GAAWM,CAChE,CCzBA,IAAI6R,GAAU,IAAKC,IAAWD,GCN9B,OAAe7S,GAAKA,ECMb,SAAS+S,GAAYC,GAC1B,OAAO,SAAS7Q,GACd,IAAIqD,EAAI,IAAIyN,GACZ,IAAK,IAAIC,KAAOF,EAASxN,EAAE0N,GAAOF,EAAQE,GAE1C,OADA1N,EAAErD,OAASA,EACJqD,CACT,CACF,CAEA,SAASyN,KAAmB,CAE5BA,GAAgBE,UAAY,CAC1BxT,YAAasT,GACb/P,MAAO,SAASlD,EAAGG,GAAK5F,KAAK4H,OAAOe,MAAMlD,EAAGG,EAAI,EACjD4C,OAAQ,WAAaxI,KAAK4H,OAAOY,QAAU,EAC3Ce,UAAW,WAAavJ,KAAK4H,OAAO2B,WAAa,EACjDC,QAAS,WAAaxJ,KAAK4H,OAAO4B,SAAW,EAC7CC,aAAc,WAAazJ,KAAK4H,OAAO6B,cAAgB,EACvDC,WAAY,WAAa1J,KAAK4H,OAAO8B,YAAc,GCtBrD,IAAIyD,GAAK0L,IACLzL,GAAKD,GACLE,IAAMF,GACNG,GAAKD,GAELyL,GAAe,CACjBnQ,MAYF,SAAqBlD,EAAGG,GAClBH,EAAI0H,KAAIA,GAAK1H,GACbA,EAAI4H,KAAIA,GAAK5H,GACbG,EAAIwH,KAAIA,GAAKxH,GACbA,EAAI0H,KAAIA,GAAK1H,EACnB,EAhBE2D,UAAW7B,EACX8B,QAAS9B,EACT+B,aAAc/B,EACdgC,WAAYhC,EACZ8L,OAAQ,WACN,IAAIuF,EAAS,CAAC,CAAC5L,GAAIC,IAAK,CAACC,GAAIC,KAE7B,OADAD,GAAKC,KAAOF,GAAKD,GAAK0L,KACfE,CACT,GAUF,MCxBA,SAASC,GAAI1U,EAAY2U,EAAW5Q,GAClC,IAAI8L,EAAO7P,EAAW4U,YAAc5U,EAAW4U,aAM/C,OALA5U,EAAW6U,MAAM,KAAKC,UAAU,CAAC,EAAG,IACxB,MAARjF,GAAc7P,EAAW4U,WAAW,MACxC,EAAU7Q,EAAQ/D,EAAWsD,OAAOmR,KACpCE,EAAUF,GAAavF,UACX,MAARW,GAAc7P,EAAW4U,WAAW/E,GACjC7P,CACT,CAEO,SAAS+U,GAAU/U,EAAYmN,EAAQpJ,GAC5C,OAAO2Q,GAAI1U,GAAY,SAASiG,GAC9B,IAAI+O,EAAI7H,EAAO,GAAG,GAAKA,EAAO,GAAG,GAC7B8H,EAAI9H,EAAO,GAAG,GAAKA,EAAO,GAAG,GAC7BzD,EAAIjI,KAAKyT,IAAIF,GAAK/O,EAAE,GAAG,GAAKA,EAAE,GAAG,IAAKgP,GAAKhP,EAAE,GAAG,GAAKA,EAAE,GAAG,KAC1D9E,GAAKgM,EAAO,GAAG,IAAM6H,EAAItL,GAAKzD,EAAE,GAAG,GAAKA,EAAE,GAAG,KAAO,EACpD3E,GAAK6L,EAAO,GAAG,IAAM8H,EAAIvL,GAAKzD,EAAE,GAAG,GAAKA,EAAE,GAAG,KAAO,EACxDjG,EAAW6U,MAAM,IAAMnL,GAAGoL,UAAU,CAAC3T,EAAGG,GAC1C,GAAGyC,EACL,CAEO,SAASoR,GAAQnV,EAAYoV,EAAMrR,GACxC,OAAOgR,GAAU/U,EAAY,CAAC,CAAC,EAAG,GAAIoV,GAAOrR,EAC/C,CAEO,SAASsR,GAASrV,EAAYsV,EAAOvR,GAC1C,OAAO2Q,GAAI1U,GAAY,SAASiG,GAC9B,IAAI+O,GAAKM,EACL5L,EAAIsL,GAAK/O,EAAE,GAAG,GAAKA,EAAE,GAAG,IACxB9E,GAAK6T,EAAItL,GAAKzD,EAAE,GAAG,GAAKA,EAAE,GAAG,KAAO,EACpC3E,GAAKoI,EAAIzD,EAAE,GAAG,GAClBjG,EAAW6U,MAAM,IAAMnL,GAAGoL,UAAU,CAAC3T,EAAGG,GAC1C,GAAGyC,EACL,CAEO,SAASwR,GAAUvV,EAAYwV,EAAQzR,GAC5C,OAAO2Q,GAAI1U,GAAY,SAASiG,GAC9B,IAAIgP,GAAKO,EACL9L,EAAIuL,GAAKhP,EAAE,GAAG,GAAKA,EAAE,GAAG,IACxB9E,GAAKuI,EAAIzD,EAAE,GAAG,GACd3E,GAAK2T,EAAIvL,GAAKzD,EAAE,GAAG,GAAKA,EAAE,GAAG,KAAO,EACxCjG,EAAW6U,MAAM,IAAMnL,GAAGoL,UAAU,CAAC3T,EAAGG,GAC1C,GAAGyC,EACL,CC1CA,IAAI0R,GAAW,GACXC,GAAiB,EAAI,GAAKrT,GAEf,YAASsT,EAASC,GAC/B,OAAQA,EAYV,SAAkBD,EAASC,GAEzB,SAASC,EAAehN,EAAIC,EAAIpF,EAASoS,EAAIC,EAAIC,EAAIjN,EAAIC,EAAIkJ,EAAS+D,EAAIC,EAAIC,EAAIC,EAAO9S,GACvF,IAAIyI,EAAKhD,EAAKF,EACV+C,EAAK5C,EAAKF,EACVuN,EAAKtK,EAAKA,EAAKH,EAAKA,EACxB,GAAIyK,EAAK,EAAIT,GAAUQ,IAAS,CAC9B,IAAIpQ,EAAI8P,EAAKG,EACThQ,EAAI8P,EAAKG,EACTI,EAAIN,EAAKG,EACTrH,EAAI9L,EAAKgD,EAAIA,EAAIC,EAAIA,EAAIqQ,EAAIA,GAC7BC,EAAOrT,EAAKoT,GAAKxH,GACjB0H,EAAU9U,EAAIA,EAAI4U,GAAK,GAAKzU,GAAWH,EAAIgC,EAAUwO,GAAWrQ,GAAW6B,EAAUwO,GAAW,EAAI3P,EAAM0D,EAAGD,GAC7G5E,EAAIuU,EAAQa,EAASD,GACrBE,EAAKrV,EAAE,GACPsV,EAAKtV,EAAE,GACPuV,EAAMF,EAAK5N,EACX+N,EAAMF,EAAK5N,EACX+N,EAAKjL,EAAK+K,EAAM5K,EAAK6K,GACrBC,EAAKA,EAAKR,EAAKT,GACZlU,GAAKqK,EAAK4K,EAAM/K,EAAKgL,GAAOP,EAAK,IAAO,IACxCP,EAAKG,EAAKF,EAAKG,EAAKF,EAAKG,EAAKT,MACnCG,EAAehN,EAAIC,EAAIpF,EAASoS,EAAIC,EAAIC,EAAIS,EAAIC,EAAIF,EAASxQ,GAAK8I,EAAG7I,GAAK6I,EAAGwH,EAAGF,EAAO9S,GACvFA,EAAOe,MAAMoS,EAAIC,GACjBb,EAAeY,EAAIC,EAAIF,EAASxQ,EAAGC,EAAGqQ,EAAGvN,EAAIC,EAAIkJ,EAAS+D,EAAIC,EAAIC,EAAIC,EAAO9S,GAEjF,CACF,CACA,OAAO,SAASA,GACd,IAAIwT,EAAUC,EAAKC,EAAKC,EAAKC,EAAKC,EAC9BzT,EAASmF,EAAIC,EAAIgN,EAAIC,EAAIC,EAEzBoB,EAAiB,CACnB/S,MAAOA,EACPY,UAAWA,EACXC,QAASA,EACTC,aAAc,WAAa7B,EAAO6B,eAAgBiS,EAAenS,UAAYyM,CAAW,EACxFtM,WAAY,WAAa9B,EAAO8B,aAAcgS,EAAenS,UAAYA,CAAW,GAGtF,SAASZ,EAAMlD,EAAGG,GAChBH,EAAIwU,EAAQxU,EAAGG,GACfgC,EAAOe,MAAMlD,EAAE,GAAIA,EAAE,GACvB,CAEA,SAAS8D,IACP4D,EAAKoK,IACLmE,EAAe/S,MAAQgT,EACvB/T,EAAO2B,WACT,CAEA,SAASoS,EAAU7R,EAAQC,GACzB,IAAI6Q,EAAIjG,GAAU,CAAC7K,EAAQC,IAAOrE,EAAIuU,EAAQnQ,EAAQC,GACtDoQ,EAAehN,EAAIC,EAAIpF,EAASoS,EAAIC,EAAIC,EAAInN,EAAKzH,EAAE,GAAI0H,EAAK1H,EAAE,GAAIsC,EAAU8B,EAAQsQ,EAAKQ,EAAE,GAAIP,EAAKO,EAAE,GAAIN,EAAKM,EAAE,GAAIb,GAAUnS,GAC/HA,EAAOe,MAAMwE,EAAIC,EACnB,CAEA,SAAS5D,IACPkS,EAAe/S,MAAQA,EACvBf,EAAO4B,SACT,CAEA,SAASwM,IACPzM,IACAmS,EAAe/S,MAAQiT,EACvBF,EAAelS,QAAUyM,CAC3B,CAEA,SAAS2F,EAAU9R,EAAQC,GACzB4R,EAAUP,EAAWtR,EAAQC,GAAMsR,EAAMlO,EAAImO,EAAMlO,EAAImO,EAAMnB,EAAIoB,EAAMnB,EAAIoB,EAAMnB,EACjFoB,EAAe/S,MAAQgT,CACzB,CAEA,SAAS1F,IACPkE,EAAehN,EAAIC,EAAIpF,EAASoS,EAAIC,EAAIC,EAAIe,EAAKC,EAAKF,EAAUG,EAAKC,EAAKC,EAAK1B,GAAUnS,GACzF8T,EAAelS,QAAUA,EACzBA,GACF,CAEA,OAAOkS,CACT,CACF,CA7FmB,CAASzB,EAASC,GAGrC,SAAsBD,GACpB,OAAOzB,GAAY,CACjB7P,MAAO,SAASlD,EAAGG,GACjBH,EAAIwU,EAAQxU,EAAGG,GACf5F,KAAK4H,OAAOe,MAAMlD,EAAE,GAAIA,EAAE,GAC5B,GAEJ,CAV+CoW,CAAa5B,EAC5D,CCEA,IAAI6B,GAAmBtD,GAAY,CACjC7P,MAAO,SAASlD,EAAGG,GACjB5F,KAAK4H,OAAOe,MAAMlD,EAAIkB,EAASf,EAAIe,EACrC,IAuBF,SAASoV,GAAqB/N,EAAGqC,EAAIH,EAAI8L,EAAIC,EAAIC,GAC/C,IAAKA,EAAO,OAZd,SAAwBlO,EAAGqC,EAAIH,EAAI8L,EAAIC,GACrC,SAASE,EAAU1W,EAAGG,GAEpB,MAAO,CAACyK,EAAKrC,GADbvI,GAAKuW,GACe9L,EAAKlC,GADhBpI,GAAKqW,GAEhB,CAIA,OAHAE,EAAUjP,OAAS,SAASzH,EAAGG,GAC7B,MAAO,EAAEH,EAAI4K,GAAMrC,EAAIgO,GAAK9L,EAAKtK,GAAKoI,EAAIiO,EAC5C,EACOE,CACT,CAGqBC,CAAepO,EAAGqC,EAAIH,EAAI8L,EAAIC,GACjD,IAAII,EAAW,EAAIH,GACfI,EAAW,EAAIJ,GACf5R,EAAI+R,EAAWrO,EACfzD,EAAI+R,EAAWtO,EACfuO,EAAKF,EAAWrO,EAChBwO,EAAKF,EAAWtO,EAChByO,GAAMH,EAAWpM,EAAKmM,EAAWhM,GAAMrC,EACvC0O,GAAMJ,EAAWjM,EAAKgM,EAAWnM,GAAMlC,EAC3C,SAASmO,EAAU1W,EAAGG,GAEpB,MAAO,CAAC0E,GADR7E,GAAKuW,GACWzR,GADP3E,GAAKqW,GACU5L,EAAIH,EAAK3F,EAAI9E,EAAI6E,EAAI1E,EAC/C,CAIA,OAHAuW,EAAUjP,OAAS,SAASzH,EAAGG,GAC7B,MAAO,CAACoW,GAAMO,EAAK9W,EAAI+W,EAAK5W,EAAI6W,GAAKR,GAAMS,EAAKF,EAAK/W,EAAI8W,EAAK3W,GAChE,EACOuW,CACT,CAEe,SAAS7X,GAAW2V,GACjC,OAAO0C,IAAkB,WAAa,OAAO1C,CAAS,GAA/C0C,EACT,CAEO,SAASA,GAAkBC,GAChC,IAAI3C,EAI+C4C,EAKpCzP,EAAIC,EAAIC,EAEnBwP,EACAC,EACAC,EACAC,EACAC,EAdAlP,EAAI,IACJvI,EAAI,IAAKG,EAAI,IACbkE,EAAS,EAAGC,EAAM,EAClByI,EAAc,EAAGC,EAAW,EAAGC,EAAa,EAC5CwJ,EAAQ,EACRF,EAAK,EACLC,EAAK,EACLkB,EAAQ,KAAMC,EAAU,GACxBjQ,EAAK,KAAkBkQ,EAAWC,GAClCpD,EAAS,GAOb,SAAS5V,EAAWqE,GAClB,OAAOqU,EAAuBrU,EAAM,GAAKhC,EAASgC,EAAM,GAAKhC,EAC/D,CAEA,SAASuG,EAAOvE,GAEd,OADAA,EAAQqU,EAAuB9P,OAAOvE,EAAM,GAAIA,EAAM,MACtC,CAACA,EAAM,GAAKjC,EAASiC,EAAM,GAAKjC,EAClD,CAsEA,SAAS6W,IACP,IAAIC,EAASzB,GAAqB/N,EAAG,EAAG,EAAGgO,EAAIC,EAAIC,GAAOuB,MAAM,KAAMxD,EAAQnQ,EAAQC,IAClFoS,EAAYJ,GAAqB/N,EAAGvI,EAAI+X,EAAO,GAAI5X,EAAI4X,EAAO,GAAIxB,EAAIC,EAAIC,GAK9E,OAJAW,EAAStK,GAAcC,EAAaC,EAAUC,GAC9CqK,EAAmB3K,GAAQ6H,EAASkC,GACpCa,EAAyB5K,GAAQyK,EAAQE,GACzCD,EAAkBY,GAASX,EAAkB7C,GACtCyD,GACT,CAEA,SAASA,IAEP,OADAV,EAAQC,EAAc,KACf5Y,CACT,CAEA,OAnFAA,EAAWsD,OAAS,SAASA,GAC3B,OAAOqV,GAASC,IAAgBtV,EAASqV,EAAQA,EAAQnB,GAxE7D,SAAyBe,GACvB,OAAOrE,GAAY,CACjB7P,MAAO,SAASlD,EAAGG,GACjB,IAAIgY,EAAIf,EAAOpX,EAAGG,GAClB,OAAO5F,KAAK4H,OAAOe,MAAMiV,EAAE,GAAIA,EAAE,GACnC,GAEJ,CAiE8EC,CAAgBhB,EAAhBgB,CAAwBT,EAAQN,EAAgBO,EAASH,EAActV,MACnJ,EAEAtD,EAAW8Y,QAAU,SAAS1L,GAC5B,OAAO5B,UAAUpO,QAAU0b,EAAU1L,EAAGyL,OAAQtc,EAAW8c,KAAWP,CACxE,EAEA9Y,EAAW+Y,SAAW,SAAS3L,GAC7B,OAAO5B,UAAUpO,QAAU2b,EAAW3L,EAAGvE,EAAKC,EAAKC,EAAKC,EAAK,KAAMqQ,KAAWN,CAChF,EAEA/Y,EAAWsG,UAAY,SAAS8G,GAC9B,OAAO5B,UAAUpO,QAAU0b,GAAW1L,EC/F3B,SAASqG,GACtB,IAAI+F,EAAK,EAAI/F,GACT5N,EAAQ,EAAIxD,EACZoX,EAAcD,EAAK,EACnBE,EAAgBhY,EAAI8X,GAAM3X,EAM9B,SAAS8X,EAAQnU,EAAQC,GACvB,OAAO,EAAID,GAAU,EAAIC,GAAO+T,CAClC,CAiFA,SAASI,EAAU5T,EAAGC,EAAG4T,GACvB,IAKIC,EAAK,CAAC,EAAG,EAAG,GACZC,EAAKxJ,GANAF,GAAUrK,GACVqK,GAAUpK,IAMf+T,EAAO1J,GAAayJ,EAAIA,GACxBE,EAAOF,EAAG,GACVG,EAAcF,EAAOC,EAAOA,EAGhC,IAAKC,EAAa,OAAQL,GAAO7T,EAEjC,IAAImQ,EAAMqD,EAAKQ,EAAOE,EAClBC,GAAMX,EAAKS,EAAOC,EAClBE,EAAQ7J,GAAeuJ,EAAIC,GAC3BjQ,EAAI2G,GAAeqJ,EAAI3D,GAE3B3F,GAAoB1G,EADZ2G,GAAesJ,EAAII,IAI3B,IAAIE,EAAID,EACJpF,EAAI1E,GAAaxG,EAAGuQ,GACpBC,EAAKhK,GAAa+J,EAAGA,GACrBE,EAAKvF,EAAIA,EAAIsF,GAAMhK,GAAaxG,EAAGA,GAAK,GAE5C,KAAIyQ,EAAK,GAAT,CAEA,IAAI3Q,EAAI5G,EAAKuX,GACTC,EAAI/J,GAAe4J,IAAKrF,EAAIpL,GAAK0Q,GAIrC,GAHA9J,GAAoBgK,EAAG1Q,GACvB0Q,EAAIpK,GAAUoK,IAETX,EAAK,OAAOW,EAGjB,IAIIzU,EAJArC,EAAUsC,EAAE,GACZkM,EAAUjM,EAAE,GACZgM,EAAOjM,EAAE,GACTsM,EAAOrM,EAAE,GAGTiM,EAAUxO,IAASqC,EAAIrC,EAASA,EAAUwO,EAASA,EAAUnM,GAEjE,IAAIF,EAAQqM,EAAUxO,EAClB+W,EAAQ/Y,EAAImE,EAAQ9D,GAAMF,EAM9B,IAHK4Y,GAASnI,EAAOL,IAAMlM,EAAIkM,EAAMA,EAAOK,EAAMA,EAAOvM,GAF1C0U,GAAS5U,EAAQhE,EAM1B4Y,EACExI,EAAOK,EAAO,EAAIkI,EAAE,IAAM9Y,EAAI8Y,EAAE,GAAK9W,GAAW7B,EAAUoQ,EAAOK,GACjEL,GAAQuI,EAAE,IAAMA,EAAE,IAAMlI,EAC1BzM,EAAQ9D,GAAM2B,GAAW8W,EAAE,IAAMA,EAAE,IAAMtI,GAAU,CACvD,IAAIwI,EAAKjK,GAAe4J,IAAKrF,EAAIpL,GAAK0Q,GAEtC,OADA9J,GAAoBkK,EAAI5Q,GACjB,CAAC0Q,EAAGpK,GAAUsK,GACvB,CAjCkB,CAkCpB,CAIA,SAASC,EAAKnV,EAAQC,GACpB,IAAI6T,EAAIG,EAAchG,EAAS1R,EAAK0R,EAChCkH,EAAO,EAKX,OAJInV,GAAU8T,EAAGqB,GAAQ,EAChBnV,EAAS8T,IAAGqB,GAAQ,GACzBlV,GAAO6T,EAAGqB,GAAQ,EACblV,EAAM6T,IAAGqB,GAAQ,GACnBA,CACT,CAEA,OAAO9K,GAAK8J,GAvJZ,SAAkBrW,GAChB,IAAI0O,EACAgE,EACA4E,EACAC,EACA/H,EACJ,MAAO,CACL7N,UAAW,WACT4V,EAAMD,GAAK,EACX9H,EAAQ,CACV,EACAzO,MAAO,SAASmB,EAAQC,GACtB,IACIqV,EADAzI,EAAS,CAAC7M,EAAQC,GAElBgK,EAAIkK,EAAQnU,EAAQC,GACpB6Q,EAAImD,EACAhK,EAAI,EAAIkL,EAAKnV,EAAQC,GACrBgK,EAAIkL,EAAKnV,GAAUA,EAAS,EAAIzD,GAAMA,GAAK0D,GAAO,EAO1D,IANKuM,IAAW6I,EAAMD,EAAKnL,IAAInM,EAAO2B,YAClCwK,IAAMmL,MACRE,EAASlB,EAAU5H,EAAQK,KACZtC,GAAWiC,EAAQ8I,IAAW/K,GAAWsC,EAAQyI,MAC9DzI,EAAO,GAAK,GAEZ5C,IAAMmL,EACR9H,EAAQ,EACJrD,GAEFnM,EAAO2B,YACP6V,EAASlB,EAAUvH,EAAQL,GAC3B1O,EAAOe,MAAMyW,EAAO,GAAIA,EAAO,MAG/BA,EAASlB,EAAU5H,EAAQK,GAC3B/O,EAAOe,MAAMyW,EAAO,GAAIA,EAAO,GAAI,GACnCxX,EAAO4B,WAET8M,EAAS8I,OACJ,GAAIpB,GAAiB1H,GAAUyH,EAAchK,EAAG,CACrD,IAAI7F,EAGE0M,EAAIN,KAAQpM,EAAIgQ,EAAUvH,EAAQL,GAAQ,MAC9Cc,EAAQ,EACJ2G,GACFnW,EAAO2B,YACP3B,EAAOe,MAAMuF,EAAE,GAAG,GAAIA,EAAE,GAAG,IAC3BtG,EAAOe,MAAMuF,EAAE,GAAG,GAAIA,EAAE,GAAG,IAC3BtG,EAAO4B,YAEP5B,EAAOe,MAAMuF,EAAE,GAAG,GAAIA,EAAE,GAAG,IAC3BtG,EAAO4B,UACP5B,EAAO2B,YACP3B,EAAOe,MAAMuF,EAAE,GAAG,GAAIA,EAAE,GAAG,GAAI,IAGrC,EACI6F,GAAOuC,GAAWjC,GAAWiC,EAAQK,IACvC/O,EAAOe,MAAMgO,EAAO,GAAIA,EAAO,IAEjCL,EAASK,EAAQuI,EAAKnL,EAAGuG,EAAKM,CAChC,EACApR,QAAS,WACH0V,GAAItX,EAAO4B,UACf8M,EAAS,IACX,EAGAc,MAAO,WACL,OAAOA,GAAU+H,GAAOD,IAAO,CACjC,EAEJ,IApFA,SAAqB7J,EAAMuC,EAAIC,EAAWjQ,GACxCkQ,GAAalQ,EAAQmQ,EAAQ5N,EAAO0N,EAAWxC,EAAMuC,EACvD,GAiK4CmG,EAAc,CAAC,GAAIhG,GAAU,EAAE1R,EAAI0R,EAAS1R,GAC1F,CD3E8C,CAAW8W,EAAQzL,EAAI/K,IAAYwW,EAAQ,KAAM,IAAmBQ,KAAWR,EAAQzW,CACnI,EAEApC,EAAW4U,WAAa,SAASxH,GAC/B,OAAO5B,UAAUpO,QAAU2b,EAAgB,MAAL3L,GAAavE,EAAKC,EAAKC,EAAKC,EAAK,KAAMgQ,IN9FlE,SAAuBnQ,EAAIC,EAAIC,EAAIC,GAEhD,SAAS2Q,EAAQxY,EAAGG,GAClB,OAAOuH,GAAM1H,GAAKA,GAAK4H,GAAMD,GAAMxH,GAAKA,GAAK0H,CAC/C,CAEA,SAASW,EAAYoH,EAAMuC,EAAIC,EAAWjQ,GACxC,IAAI0C,EAAI,EAAGiQ,EAAK,EAChB,GAAY,MAARlF,IACI/K,EAAI+U,EAAOhK,EAAMwC,OAAiB0C,EAAK8E,EAAOzH,EAAIC,KACnDyH,EAAajK,EAAMuC,GAAM,EAAIC,EAAY,EAC9C,GAAGjQ,EAAOe,MAAY,IAAN2B,GAAiB,IAANA,EAAU6C,EAAKE,EAAI/C,EAAI,EAAIgD,EAAKF,UACnD9C,GAAKA,EAAIuN,EAAY,GAAK,KAAO0C,QAEzC3S,EAAOe,MAAMiP,EAAG,GAAIA,EAAG,GAE3B,CAEA,SAASyH,EAAO3Z,EAAGmS,GACjB,OAAO7R,EAAIN,EAAE,GAAKyH,GAAMhH,EAAU0R,EAAY,EAAI,EAAI,EAChD7R,EAAIN,EAAE,GAAK2H,GAAMlH,EAAU0R,EAAY,EAAI,EAAI,EAC/C7R,EAAIN,EAAE,GAAK0H,GAAMjH,EAAU0R,EAAY,EAAI,EAAI,EAC/CA,EAAY,EAAI,EAAI,CAC5B,CAEA,SAAS7D,EAAoB1J,EAAGC,GAC9B,OAAO+U,EAAahV,EAAE7E,EAAG8E,EAAE9E,EAC7B,CAEA,SAAS6Z,EAAahV,EAAGC,GACvB,IAAIgV,EAAKF,EAAO/U,EAAG,GACfkV,EAAKH,EAAO9U,EAAG,GACnB,OAAOgV,IAAOC,EAAKD,EAAKC,EACX,IAAPD,EAAWhV,EAAE,GAAKD,EAAE,GACb,IAAPiV,EAAWjV,EAAE,GAAKC,EAAE,GACb,IAAPgV,EAAWjV,EAAE,GAAKC,EAAE,GACpBA,EAAE,GAAKD,EAAE,EACjB,CAEA,OAAO,SAAS1C,GACd,IAEImD,EACA2K,EACAC,EACA8J,EAAKC,EAAKC,EACVC,EAAIC,EAAIC,EACRC,EACA3I,EARA4I,EAAepY,EACfqY,EAAe,KASfC,EAAa,CACfvX,MAAOA,EACPY,UAgDF,WACE2W,EAAWvX,MAAQgT,EACfjG,GAASA,EAAQxV,KAAKyV,EAAO,IACjCoK,GAAQ,EACRD,GAAK,EACLF,EAAKC,EAAKtI,GACZ,EArDE/N,QA0DF,WACMuB,IACF4Q,EAAU8D,EAAKC,GACXC,GAAOG,GAAIG,EAAa5M,SAC5BtI,EAAS7K,KAAK+f,EAAazM,WAE7B0M,EAAWvX,MAAQA,EACfmX,GAAIE,EAAaxW,SACvB,EAjEEC,aAuBF,WACEuW,EAAeC,EAAclV,EAAW,GAAI2K,EAAU,GAAI0B,GAAQ,CACpE,EAxBE1N,WA0BF,WACE,IAAIuK,EApBN,WAGE,IAFA,IAAImC,EAAU,EAELrT,EAAI,EAAGmD,EAAIwP,EAAQhU,OAAQqB,EAAImD,IAAKnD,EAC3C,IAAK,IAAgEqX,EAAIG,EAAhE5E,EAAOD,EAAQ3S,GAAI4C,EAAI,EAAGyN,EAAIuC,EAAKjU,OAAQiH,EAAQgN,EAAK,GAAY0E,EAAK1R,EAAM,GAAI6R,EAAK7R,EAAM,GAAIhD,EAAIyN,IAAKzN,EAClHyU,EAAKC,EAAIE,EAAKC,EAAqBH,GAAjB1R,EAAQgN,EAAKhQ,IAAe,GAAI6U,EAAK7R,EAAM,GACzD4R,GAAMjN,EAAUkN,EAAKlN,IAAO+M,EAAKD,IAAO9M,EAAKiN,IAAOC,EAAKD,IAAOpN,EAAKiN,MAAOhE,EACrEoE,GAAMlN,IAAO+M,EAAKD,IAAO9M,EAAKiN,IAAOC,EAAKD,IAAOpN,EAAKiN,MAAOhE,EAI5E,OAAOA,CACT,CAQoB+J,GACdC,EAAchJ,GAASnD,EACvBgK,GAAWlT,EAAWoK,GAAMpK,IAAWrJ,QACvC0e,GAAenC,KACjBrW,EAAO6B,eACH2W,IACFxY,EAAO2B,YACP0E,EAAY,KAAM,KAAM,EAAGrG,GAC3BA,EAAO4B,WAELyU,GACF,GAAWlT,EAAUiJ,EAAqBC,EAAahG,EAAarG,GAEtEA,EAAO8B,cAETsW,EAAepY,EAAQmD,EAAW2K,EAAUC,EAAO,IACrD,GAxCA,SAAShN,EAAMlD,EAAGG,GACZqY,EAAQxY,EAAGG,IAAIoa,EAAarX,MAAMlD,EAAGG,EAC3C,CA6DA,SAAS+V,EAAUlW,EAAGG,GACpB,IAAImO,EAAIkK,EAAQxY,EAAGG,GAEnB,GADI8P,GAASC,EAAKzV,KAAK,CAACuF,EAAGG,IACvBma,EACFN,EAAMha,EAAGia,EAAM9Z,EAAG+Z,EAAM5L,EACxBgM,GAAQ,EACJhM,IACFiM,EAAazW,YACbyW,EAAarX,MAAMlD,EAAGG,SAGxB,GAAImO,GAAK+L,EAAIE,EAAarX,MAAMlD,EAAGG,OAC9B,CACH,IAAI0E,EAAI,CAACsV,EAAK7Z,KAAKgK,IAAIwI,GAASxS,KAAKyT,IAAIlB,GAASsH,IAAMC,EAAK9Z,KAAKgK,IAAIwI,GAASxS,KAAKyT,IAAIlB,GAASuH,KAC7FtV,EAAI,CAAC9E,EAAIM,KAAKgK,IAAIwI,GAASxS,KAAKyT,IAAIlB,GAAS7S,IAAKG,EAAIG,KAAKgK,IAAIwI,GAASxS,KAAKyT,IAAIlB,GAAS1S,MQlJzF,SAAS0E,EAAGC,EAAG4C,EAAIC,EAAIC,EAAIC,GACxC,IAQIsQ,EARAyC,EAAK/V,EAAE,GACPgW,EAAKhW,EAAE,GAGP0N,EAAK,EACLC,EAAK,EACL5H,EAJK9F,EAAE,GAIG8V,EACVnQ,EAJK3F,EAAE,GAIG+V,EAId,GADA1C,EAAIzQ,EAAKkT,EACJhQ,KAAMuN,EAAI,GAAf,CAEA,GADAA,GAAKvN,EACDA,EAAK,EAAG,CACV,GAAIuN,EAAI5F,EAAI,OACR4F,EAAI3F,IAAIA,EAAK2F,EACnB,MAAO,GAAIvN,EAAK,EAAG,CACjB,GAAIuN,EAAI3F,EAAI,OACR2F,EAAI5F,IAAIA,EAAK4F,EACnB,CAGA,GADAA,EAAIvQ,EAAKgT,EACJhQ,KAAMuN,EAAI,GAAf,CAEA,GADAA,GAAKvN,EACDA,EAAK,EAAG,CACV,GAAIuN,EAAI3F,EAAI,OACR2F,EAAI5F,IAAIA,EAAK4F,EACnB,MAAO,GAAIvN,EAAK,EAAG,CACjB,GAAIuN,EAAI5F,EAAI,OACR4F,EAAI3F,IAAIA,EAAK2F,EACnB,CAGA,GADAA,EAAIxQ,EAAKkT,EACJpQ,KAAM0N,EAAI,GAAf,CAEA,GADAA,GAAK1N,EACDA,EAAK,EAAG,CACV,GAAI0N,EAAI5F,EAAI,OACR4F,EAAI3F,IAAIA,EAAK2F,EACnB,MAAO,GAAI1N,EAAK,EAAG,CACjB,GAAI0N,EAAI3F,EAAI,OACR2F,EAAI5F,IAAIA,EAAK4F,EACnB,CAGA,GADAA,EAAItQ,EAAKgT,EACJpQ,KAAM0N,EAAI,GAAf,CAEA,GADAA,GAAK1N,EACDA,EAAK,EAAG,CACV,GAAI0N,EAAI3F,EAAI,OACR2F,EAAI5F,IAAIA,EAAK4F,EACnB,MAAO,GAAI1N,EAAK,EAAG,CACjB,GAAI0N,EAAI5F,EAAI,OACR4F,EAAI3F,IAAIA,EAAK2F,EACnB,CAIA,OAFI5F,EAAK,IAAG1N,EAAE,GAAK+V,EAAKrI,EAAK3H,EAAI/F,EAAE,GAAKgW,EAAKtI,EAAK9H,GAC9C+H,EAAK,IAAG1N,EAAE,GAAK8V,EAAKpI,EAAK5H,EAAI9F,EAAE,GAAK+V,EAAKrI,EAAK/H,IAC3C,CAZiB,CAXA,CAXA,CAXA,CA8C1B,CRyFc,CAAS5F,EAAGC,EAAG4C,EAAIC,EAAIC,EAAIC,GAQpByG,IACTiM,EAAazW,YACbyW,EAAarX,MAAMlD,EAAGG,GACtBwR,GAAQ,IAVH0I,IACHE,EAAazW,YACbyW,EAAarX,MAAM2B,EAAE,GAAIA,EAAE,KAE7B0V,EAAarX,MAAM4B,EAAE,GAAIA,EAAE,IACtBwJ,GAAGiM,EAAaxW,UACrB4N,GAAQ,EAMZ,CAEFwI,EAAKna,EAAGoa,EAAKja,EAAGka,EAAK/L,CACvB,CAEA,OAAOmM,CACT,CACF,CM9D6FK,CAAcpT,GAAMuE,EAAE,GAAG,GAAItE,GAAMsE,EAAE,GAAG,GAAIrE,GAAMqE,EAAE,GAAG,GAAIpE,GAAMoE,EAAE,GAAG,IAAKiM,KAAiB,MAANxQ,EAAa,KAAO,CAAC,CAACA,EAAIC,GAAK,CAACC,EAAIC,GACrN,EAEAhJ,EAAW6U,MAAQ,SAASzH,GAC1B,OAAO5B,UAAUpO,QAAUsM,GAAK0D,EAAG6L,KAAcvP,CACnD,EAEA1J,EAAW8U,UAAY,SAAS1H,GAC9B,OAAO5B,UAAUpO,QAAU+D,GAAKiM,EAAE,GAAI9L,GAAK8L,EAAE,GAAI6L,KAAc,CAAC9X,EAAGG,EACrE,EAEAtB,EAAWkZ,OAAS,SAAS9L,GAC3B,OAAO5B,UAAUpO,QAAUoI,EAAS4H,EAAE,GAAK,IAAM/K,EAASoD,EAAM2H,EAAE,GAAK,IAAM/K,EAAS4W,KAAc,CAACzT,EAASpD,EAASqD,EAAMrD,EAC/H,EAEApC,EAAWuY,OAAS,SAASnL,GAC3B,OAAO5B,UAAUpO,QAAU8Q,EAAcd,EAAE,GAAK,IAAM/K,EAAS8L,EAAWf,EAAE,GAAK,IAAM/K,EAAS+L,EAAahB,EAAEhQ,OAAS,EAAIgQ,EAAE,GAAK,IAAM/K,EAAU,EAAG4W,KAAc,CAAC/K,EAAc9L,EAAS+L,EAAW/L,EAASgM,EAAahM,EAC/N,EAEApC,EAAW6R,MAAQ,SAASzE,GAC1B,OAAO5B,UAAUpO,QAAUwa,EAAQxK,EAAI,IAAM/K,EAAS4W,KAAcrB,EAAQxV,CAC9E,EAEApC,EAAWkc,SAAW,SAAS9O,GAC7B,OAAO5B,UAAUpO,QAAUsa,EAAKtK,GAAK,EAAI,EAAG6L,KAAcvB,EAAK,CACjE,EAEA1X,EAAWmc,SAAW,SAAS/O,GAC7B,OAAO5B,UAAUpO,QAAUua,EAAKvK,GAAK,EAAI,EAAG6L,KAActB,EAAK,CACjE,EAEA3X,EAAWuG,UAAY,SAAS6G,GAC9B,OAAO5B,UAAUpO,QAAUob,EAAkBY,GAASX,EAAkB7C,EAASxI,EAAIA,GAAIiM,KAAWrW,EAAK4S,EAC3G,EAEA5V,EAAW+U,UAAY,SAAS5H,EAAQpJ,GACtC,OAAOgR,GAAU/U,EAAYmN,EAAQpJ,EACvC,EAEA/D,EAAWmV,QAAU,SAASC,EAAMrR,GAClC,OAAOoR,GAAQnV,EAAYoV,EAAMrR,EACnC,EAEA/D,EAAWqV,SAAW,SAASC,EAAOvR,GACpC,OAAOsR,GAASrV,EAAYsV,EAAOvR,EACrC,EAEA/D,EAAWuV,UAAY,SAASC,EAAQzR,GACtC,OAAOwR,GAAUvV,EAAYwV,EAAQzR,EACvC,EAiBO,WAGL,OAFA4R,EAAU2C,EAAUa,MAAMzd,KAAM8P,WAChCxL,EAAW4I,OAAS+M,EAAQ/M,QAAUA,EAC/BqQ,GACT,CACF,CG5KO,SAASmD,GAAY5W,EAAQC,GAClC,MAAO,CAACD,EAAQlH,EAAI2E,GAAKhB,EAASwD,GAAO,IAC3C,CAMe,cACb,OAIK,SAA4BkQ,GACjC,IAKe7M,EAAIC,EAAIC,EALnB8F,EAAI9O,GAAW2V,GACfuD,EAASpK,EAAEoK,OACXrE,EAAQ/F,EAAE+F,MACVC,EAAYhG,EAAEgG,UACdF,EAAa9F,EAAE8F,WACf/L,EAAK,KAkBT,SAASwT,IACP,IAAI3S,EAAI3H,EAAK8S,IACTjL,EAAIkF,EpBqBG,SAASyJ,GAGtB,SAAS+D,EAAQlY,GAEf,OADAA,EAAcmU,EAAOnU,EAAY,GAAK/B,EAAS+B,EAAY,GAAK/B,IAC7C,IAAMD,EAASgC,EAAY,IAAMhC,EAASgC,CAC/D,CAOA,OAZAmU,EAAStK,GAAcsK,EAAO,GAAKlW,EAASkW,EAAO,GAAKlW,EAASkW,EAAOnb,OAAS,EAAImb,EAAO,GAAKlW,EAAU,GAO3Gia,EAAQ1T,OAAS,SAASxE,GAExB,OADAA,EAAcmU,EAAO3P,OAAOxE,EAAY,GAAK/B,EAAS+B,EAAY,GAAK/B,IACpD,IAAMD,EAASgC,EAAY,IAAMhC,EAASgC,CAC/D,EAEOkY,CACT,CoBnCc9N,CAASM,EAAEyJ,UAAU3P,OAAO,CAAC,EAAG,KAC1C,OAAOgM,EAAiB,MAAN/L,EACZ,CAAC,CAACe,EAAE,GAAKF,EAAGE,EAAE,GAAKF,GAAI,CAACE,EAAE,GAAKF,EAAGE,EAAE,GAAKF,IAAMiM,IAAYyG,GAC3D,CAAC,CAAC3a,KAAKgK,IAAI7B,EAAE,GAAKF,EAAGb,GAAKC,GAAK,CAACrH,KAAKyT,IAAItL,EAAE,GAAKF,EAAGX,GAAKC,IACxD,CAAC,CAACH,EAAIpH,KAAKgK,IAAI7B,EAAE,GAAKF,EAAGZ,IAAM,CAACC,EAAItH,KAAKyT,IAAItL,EAAE,GAAKF,EAAGV,KAC/D,CAEA,OAzBA8F,EAAE+F,MAAQ,SAASzH,GACjB,OAAO5B,UAAUpO,QAAUyX,EAAMzH,GAAIiP,KAAYxH,GACnD,EAEA/F,EAAEgG,UAAY,SAAS1H,GACrB,OAAO5B,UAAUpO,QAAU0X,EAAU1H,GAAIiP,KAAYvH,GACvD,EAEAhG,EAAEoK,OAAS,SAAS9L,GAClB,OAAO5B,UAAUpO,QAAU8b,EAAO9L,GAAIiP,KAAYnD,GACpD,EAEApK,EAAE8F,WAAa,SAASxH,GACtB,OAAO5B,UAAUpO,QAAgB,MAALgQ,EAAYvE,EAAKC,EAAKC,EAAKC,EAAK,MAAQH,GAAMuE,EAAE,GAAG,GAAItE,GAAMsE,EAAE,GAAG,GAAIrE,GAAMqE,EAAE,GAAG,GAAIpE,GAAMoE,EAAE,GAAG,IAAMiP,KAAkB,MAANxT,EAAa,KAAO,CAAC,CAACA,EAAIC,GAAK,CAACC,EAAIC,GACpL,EAWOqT,GACT,CAtCSE,CAAmBH,IACrBvH,MAAM,IAAM1S,EACnB,CAPAia,GAAYxT,OAAS,SAASzH,EAAGG,GAC/B,MAAO,CAACH,EAAG,EAAImB,EAAKI,EAAIpB,IAAMW,EAChC,E,eCCO,MAAMua,WAA6BC,GAAA,EAC/B,iBAAAC,GACTtgB,MAAMsgB,oBAEN,MAAMC,EAAKjhB,KAAK2O,MAAMuS,gBAChBtD,EAAI5d,KAAKmhB,KAAKC,KAAKphB,MAQzB4d,EAAE,YAAYyD,OAAO,CACpB/c,WAAY,KACZgd,KAAM,aACNC,KAAM,aACNC,WAAW,EACXC,SAAU,EACVC,UAAW,EACXC,UAAW,EACXC,UAAW,EACXC,UAAW,EACXC,aAAc,GACdC,aAAc,EACdC,OAAQ,OACRC,OAAQ,OACRC,gBAAiB,OAAU,UAC3BC,YAAa,OAAU,UACvBC,cAAe,EACfC,iBAAkB,EAClBC,UAAW,GACXC,oBAAoB,IAGrB,CACC,MAAMpB,EAAOvD,EAAE,WAEfuD,EAAKE,OAAO,CACXxW,UAAW,GACX2X,KAAM,YAGP,QAASrB,EAAM,SAAUF,EAAI,O,CAG9BrD,EAAE,oBAAoByD,OAAO,CAC5BoB,eAAe,IAIhB7E,EAAE,kBAAkByD,OAAO,CAC1BoB,eAAe,EACfC,WAAW,EACXC,UAAU,EACVC,WAAW,IAGZhF,EAAE,wBAAwByD,OAAO,CAChCwB,YAAa,GACbC,gBAAiB,EACjBC,cAAe,EACfC,gBAAiB,MAGlBpF,EAAE,iBAAiByD,OAAO,CACzBoB,eAAe,IAGhB,CACC,MAAMtB,EAAOvD,EAAE,cAEfuD,EAAKE,OAAO,CACXxW,UAAW,GACXoY,YAAY,EACZT,KAAM,SACNU,YAAa,EACb5W,SAAU,WACV6W,YAAa,GACbC,cAAe,KAGhB,QAASjC,EAAM,OAAQF,EAAI,kBAC3B,QAASE,EAAM,SAAUF,EAAI,a,CAI9BrD,EAAE,SAAU,CAAC,YAAa,SAASyD,OAAO,CACzCpD,SAAS,IASVL,EAAE,mBAAmByD,OAAO,CAC3BxR,KAAM,IAER,E,ICzGGwL,GACAC,GACA,GACA,G,sBALA+H,GAAU,IAAIle,EACdme,GAAc,IAAIne,EAMlBoe,GAAa,CACf5a,MAAOjB,EACP6B,UAAW7B,EACX8B,QAAS9B,EACT+B,aAAc,WACZ8Z,GAAWha,UAAYia,GACvBD,GAAW/Z,QAAUia,EACvB,EACA/Z,WAAY,WACV6Z,GAAWha,UAAYga,GAAW/Z,QAAU+Z,GAAW5a,MAAQjB,EAC/D2b,GAAQ7d,IAAIQ,EAAIsd,KAChBA,GAAc,IAAIne,CACpB,EACAqO,OAAQ,WACN,IAAIkQ,EAAOL,GAAU,EAErB,OADAA,GAAU,IAAIle,EACPue,CACT,GAGF,SAASF,KACPD,GAAW5a,MAAQgb,EACrB,CAEA,SAASA,GAAele,EAAGG,GACzB2d,GAAW5a,MAAQib,GACnBvI,GAAM,GAAK5V,EAAG6V,GAAM,GAAK1V,CAC3B,CAEA,SAASge,GAAUne,EAAGG,GACpB0d,GAAY9d,IAAI,GAAKC,EAAI,GAAKG,GAC9B,GAAKH,EAAG,GAAKG,CACf,CAEA,SAAS6d,KACPG,GAAUvI,GAAKC,GACjB,CAEA,ICpCI,GACA,GACA,GACA,GDiCJ,MC7CIzK,GAAK,EACLE,GAAK,EACL8S,GAAK,EACLjT,GAAK,EACLE,GAAK,EACLgT,GAAK,EACLC,GAAK,EACLC,GAAK,EACLC,GAAK,EAMLC,GAAiB,CACnBvb,MAAOwb,GACP5a,UAAW6a,GACX5a,QAAS6a,GACT5a,aAAc,WACZya,GAAe3a,UAAY+a,GAC3BJ,GAAe1a,QAAU+a,EAC3B,EACA7a,WAAY,WACVwa,GAAevb,MAAQwb,GACvBD,GAAe3a,UAAY6a,GAC3BF,GAAe1a,QAAU6a,EAC3B,EACA7Q,OAAQ,WACN,IAAIgR,EAAWP,GAAK,CAACF,GAAKE,GAAID,GAAKC,IAC7BH,GAAK,CAAClT,GAAKkT,GAAIhT,GAAKgT,IACpBD,GAAK,CAAChT,GAAKgT,GAAI9S,GAAK8S,IACpB,CAACtM,IAAKA,KAIZ,OAHA1G,GAAKE,GAAK8S,GACVjT,GAAKE,GAAKgT,GACVC,GAAKC,GAAKC,GAAK,EACRO,CACT,GAGF,SAASL,GAAc1e,EAAGG,GACxBiL,IAAMpL,EACNsL,IAAMnL,IACJie,EACJ,CAEA,SAASO,KACPF,GAAevb,MAAQ8b,EACzB,CAEA,SAASA,GAAuBhf,EAAGG,GACjCse,GAAevb,MAAQ+b,GACvBP,GAAc,GAAK1e,EAAG,GAAKG,EAC7B,CAEA,SAAS8e,GAAkBjf,EAAGG,GAC5B,IAAIyK,EAAK5K,EAAI,GAAIyK,EAAKtK,EAAI,GAAIyE,EAAI/C,EAAK+I,EAAKA,EAAKH,EAAKA,GACtDU,IAAMvG,GAAK,GAAK5E,GAAK,EACrBqL,IAAMzG,GAAK,GAAKzE,GAAK,EACrBke,IAAMzZ,EACN8Z,GAAc,GAAK1e,EAAG,GAAKG,EAC7B,CAEA,SAASye,KACPH,GAAevb,MAAQwb,EACzB,CAEA,SAASG,KACPJ,GAAevb,MAAQgc,EACzB,CAEA,SAASJ,KACPK,GAAkB,GAAK,GACzB,CAEA,SAASD,GAAuBlf,EAAGG,GACjCse,GAAevb,MAAQic,GACvBT,GAAc,GAAM,GAAK1e,EAAG,GAAM,GAAKG,EACzC,CAEA,SAASgf,GAAkBnf,EAAGG,GAC5B,IAAIyK,EAAK5K,EAAI,GACTyK,EAAKtK,EAAI,GACTyE,EAAI/C,EAAK+I,EAAKA,EAAKH,EAAKA,GAE5BU,IAAMvG,GAAK,GAAK5E,GAAK,EACrBqL,IAAMzG,GAAK,GAAKzE,GAAK,EACrBke,IAAMzZ,EAGN0Z,KADA1Z,EAAI,GAAK5E,EAAI,GAAKG,IACP,GAAKH,GAChBue,IAAM3Z,GAAK,GAAKzE,GAChBqe,IAAU,EAAJ5Z,EACN8Z,GAAc,GAAK1e,EAAG,GAAKG,EAC7B,CAEA,UChGe,SAASif,GAAYjZ,GAClC5L,KAAK8kB,SAAWlZ,CAClB,CAEAiZ,GAAYjM,UAAY,CACtBmM,QAAS,IACTC,YAAa,SAAStT,GACpB,OAAO1R,KAAK+kB,QAAUrT,EAAG1R,IAC3B,EACAyJ,aAAc,WACZzJ,KAAKilB,MAAQ,CACf,EACAvb,WAAY,WACV1J,KAAKilB,MAAQ1N,GACf,EACAhO,UAAW,WACTvJ,KAAKklB,OAAS,CAChB,EACA1b,QAAS,WACY,IAAfxJ,KAAKilB,OAAajlB,KAAK8kB,SAASK,YACpCnlB,KAAKklB,OAAS3N,GAChB,EACA5O,MAAO,SAASlD,EAAGG,GACjB,OAAQ5F,KAAKklB,QACX,KAAK,EACHllB,KAAK8kB,SAASM,OAAO3f,EAAGG,GACxB5F,KAAKklB,OAAS,EACd,MAEF,KAAK,EACHllB,KAAK8kB,SAAStZ,OAAO/F,EAAGG,GACxB,MAEF,QACE5F,KAAK8kB,SAASM,OAAO3f,EAAIzF,KAAK+kB,QAASnf,GACvC5F,KAAK8kB,SAAS/N,IAAItR,EAAGG,EAAG5F,KAAK+kB,QAAS,EAAGte,GAI/C,EACA+M,OAAQ9L,GCvCV,IACI2d,GACA,GACA,GACA,GACA,GALA,GAAY,IAAIlgB,EAOhB,GAAe,CACjBwD,MAAOjB,EACP6B,UAAW,WACT,GAAaZ,MAAQ,EACvB,EACAa,QAAS,WACH6b,IAAY,GAAY,GAAK,IACjC,GAAa1c,MAAQjB,CACvB,EACA+B,aAAc,WACZ4b,IAAa,CACf,EACA3b,WAAY,WACV2b,GAAa,IACf,EACA7R,OAAQ,WACN,IAAI9R,GAAU,GAEd,OADA,GAAY,IAAIyD,EACTzD,CACT,GAGF,SAAS,GAAiB+D,EAAGG,GAC3B,GAAa+C,MAAQ,GACrB,GAAM,GAAKlD,EAAG,GAAM,GAAKG,CAC3B,CAEA,SAAS,GAAYH,EAAGG,GACtB,IAAMH,EAAG,IAAMG,EACf,GAAUJ,IAAI8B,EAAK,GAAK,GAAK,GAAK,KAClC,GAAK7B,EAAG,GAAKG,CACf,CAEA,UC3CA,IAAI0f,GAAaC,GAAaC,GAAaC,GAE5B,MAAMC,GACnB,WAAAtgB,CAAYugB,GACV3lB,KAAK4lB,QAAoB,MAAVD,EAAiBE,GAgEpC,SAAqBF,GACnB,MAAM5X,EAAIhI,KAAKmB,MAAMye,GACrB,KAAM5X,GAAK,GAAI,MAAM,IAAI+X,WAAW,mBAAmBH,KACvD,GAAI5X,EAAI,GAAI,OAAO8X,GACnB,GAAI9X,IAAMuX,GAAa,CACrB,MAAMtX,EAAI,IAAMD,EAChBuX,GAAcvX,EACdwX,GAAc,SAAgBQ,GAC5B,IAAIhjB,EAAI,EACR/C,KAAK0R,GAAKqU,EAAQ,GAClB,IAAK,MAAMpgB,EAAIogB,EAAQrkB,OAAQqB,EAAI4C,IAAK5C,EACtC/C,KAAK0R,GAAK3L,KAAKuM,MAAMxC,UAAU/M,GAAKiL,GAAKA,EAAI+X,EAAQhjB,EAEzD,CACF,CACA,OAAOwiB,EACT,CAhF6CS,CAAYL,GACrD3lB,KAAK+kB,QAAU,IACf/kB,KAAK0R,EAAI,EACX,CACA,WAAAsT,CAAYtT,GAEV,OADA1R,KAAK+kB,SAAWrT,EACT1R,IACT,CACA,YAAAyJ,GACEzJ,KAAKilB,MAAQ,CACf,CACA,UAAAvb,GACE1J,KAAKilB,MAAQ1N,GACf,CACA,SAAAhO,GACEvJ,KAAKklB,OAAS,CAChB,CACA,OAAA1b,GACqB,IAAfxJ,KAAKilB,QAAajlB,KAAK0R,GAAK,KAChC1R,KAAKklB,OAAS3N,GAChB,CACA,KAAA5O,CAAMlD,EAAGG,GACP,OAAQ5F,KAAKklB,QACX,KAAK,EACHllB,KAAK4lB,OAAO,IAAIngB,KAAKG,IACrB5F,KAAKklB,OAAS,EACd,MAEF,KAAK,EACHllB,KAAK4lB,OAAO,IAAIngB,KAAKG,IACrB,MAEF,QAEE,GADA5F,KAAK4lB,OAAO,IAAIngB,KAAKG,IACjB5F,KAAK+kB,UAAYS,IAAexlB,KAAK4lB,UAAYL,GAAa,CAChE,MAAM3H,EAAI5d,KAAK+kB,QACT9Z,EAAIjL,KAAK0R,EACf1R,KAAK0R,EAAI,GACT1R,KAAK4lB,OAAO,MAAMhI,KAAKA,KAAKA,cAAc,EAAIA,KAAKA,KAAKA,aAAa,EAAIA,KACzE4H,GAAc5H,EACd2H,GAAcvlB,KAAK4lB,QACnBH,GAAczlB,KAAK0R,EACnB1R,KAAK0R,EAAIzG,CACX,CACAjL,KAAK0R,GAAK+T,GAIhB,CACA,MAAAjS,GACE,MAAMA,EAASxT,KAAK0R,EAEpB,OADA1R,KAAK0R,EAAI,GACF8B,EAAO9R,OAAS8R,EAAS,IAClC,EAGF,SAASqS,GAAOE,GACd,IAAIhjB,EAAI,EACR/C,KAAK0R,GAAKqU,EAAQ,GAClB,IAAK,MAAMpgB,EAAIogB,EAAQrkB,OAAQqB,EAAI4C,IAAK5C,EACtC/C,KAAK0R,GAAK5B,UAAU/M,GAAKgjB,EAAQhjB,EAErC,C,IC9DIkjB,GAAIC,GACJ,GAAI,GAAI,GACR,GAAI,GAAI,GACR,GAAI,GAAI,GACR9K,GAAU+K,GACV,GAAI,GAAIC,G,gCAER,GAAiB,CACnB5d,OAAQd,EACRiB,MAAO,GACPY,UAAW,GACXC,QAAS,GACTC,aAAc,WACZ,GAAeF,UAAY,GAC3B,GAAeC,QAAU,EAC3B,EACAE,WAAY,WACV,GAAeH,UAAY,GAC3B,GAAeC,QAAU,EAC3B,GAIF,SAAS,GAAcM,EAAQC,GAC7BD,GAAUnD,EACV,IAAIuD,EAAS,EADMH,GAAOpD,GAE1B0f,GAAuBnc,EAAS,EAAIJ,GAASI,EAAS,EAAIJ,GAAS,EAAIC,GACzE,CAEA,SAASsc,GAAuB5gB,EAAGG,EAAGyE,KAClC4b,GACF,KAAOxgB,EAAI,IAAMwgB,GACjB,KAAOrgB,EAAI,IAAMqgB,GACjB,KAAO5b,EAAI,IAAM4b,EACnB,CAEA,SAAS,KACP,GAAetd,MAAQ2d,EACzB,CAEA,SAASA,GAAuBxc,EAAQC,GACtCD,GAAUnD,EACV,IAAIuD,EAAS,EADMH,GAAOpD,GAE1B,GAAKuD,EAAS,EAAIJ,GAClB,GAAKI,EAAS,EAAIJ,GAClBsc,GAAK,EAAIrc,GACT,GAAepB,MAAQ4d,GACvBF,GAAuB,GAAI,GAAID,GACjC,CAEA,SAASG,GAAkBzc,EAAQC,GACjCD,GAAUnD,EACV,IAAIuD,EAAS,EADMH,GAAOpD,GAEtBlB,EAAIyE,EAAS,EAAIJ,GACjBlE,EAAIsE,EAAS,EAAIJ,GACjBO,EAAI,EAAIN,GACRuP,EAAIzS,EAAMS,GAAMgS,EAAI,GAAKjP,EAAI+b,GAAKxgB,GAAK0T,GAAKA,EAAI8M,GAAK3gB,EAAI,GAAK4E,GAAKiP,GAAKA,EAAI,GAAK1T,EAAI,GAAKH,GAAK6T,GAAI,GAAK7T,EAAI,GAAKG,EAAIwgB,GAAK/b,GAC9H6b,IAAM5M,EACN,IAAMA,GAAK,IAAM,GAAK7T,IACtB,IAAM6T,GAAK,IAAM,GAAK1T,IACtB,IAAM0T,GAAK8M,IAAMA,GAAK/b,IACtBgc,GAAuB,GAAI,GAAID,GACjC,CAEA,SAAS,KACP,GAAezd,MAAQ,EACzB,CAIA,SAAS,KACP,GAAeA,MAAQ6d,EACzB,CAEA,SAAS,KACPC,GAAkBrL,GAAU+K,IAC5B,GAAexd,MAAQ,EACzB,CAEA,SAAS6d,GAAuB1c,EAAQC,GACtCqR,GAAWtR,EAAQqc,GAAQpc,EAC3BD,GAAUnD,EAASoD,GAAOpD,EAC1B,GAAegC,MAAQ8d,GACvB,IAAIvc,EAAS,EAAIH,GACjB,GAAKG,EAAS,EAAIJ,GAClB,GAAKI,EAAS,EAAIJ,GAClBsc,GAAK,EAAIrc,GACTsc,GAAuB,GAAI,GAAID,GACjC,CAEA,SAASK,GAAkB3c,EAAQC,GACjCD,GAAUnD,EACV,IAAIuD,EAAS,EADMH,GAAOpD,GAEtBlB,EAAIyE,EAAS,EAAIJ,GACjBlE,EAAIsE,EAAS,EAAIJ,GACjBO,EAAI,EAAIN,GACR2c,EAAK,GAAKrc,EAAI+b,GAAKxgB,EACnB+gB,EAAKP,GAAK3gB,EAAI,GAAK4E,EACnBuc,EAAK,GAAKhhB,EAAI,GAAKH,EACnB2N,EAAInM,EAAMyf,EAAIC,EAAIC,GAClBtN,EAAI9R,EAAK4L,GACTW,EAAIX,IAAMkG,EAAIlG,EAClB,GAAG5N,IAAIuO,EAAI2S,GACX,GAAGlhB,IAAIuO,EAAI4S,GACX,GAAGnhB,IAAIuO,EAAI6S,GACXV,IAAM5M,EACN,IAAMA,GAAK,IAAM,GAAK7T,IACtB,IAAM6T,GAAK,IAAM,GAAK1T,IACtB,IAAM0T,GAAK8M,IAAMA,GAAK/b,IACtBgc,GAAuB,GAAI,GAAID,GACjC,CC9GO,IAKH,GACA,GACA,GACA,GACA,GCRA,GAAS7P,GAAMC,GAASI,GACxBkE,GACA,GAAU,GACV1P,GACAyb,GACAC,GACA,GDPO,GAAc,IAAI3hB,EAIzB,GAAU,IAAIA,EAOP,GAAa,CACtBwD,MAAOjB,EACP6B,UAAW7B,EACX8B,QAAS9B,EACT+B,aAAc,WACZ,GAAc,IAAItE,EAClB,GAAWoE,UAAY,GACvB,GAAWC,QAAU,EACvB,EACAE,WAAY,WACV,IAAIqd,GAAY,GAChB,GAAQvhB,IAAIuhB,EAAW,EAAItgB,EAAMsgB,EAAWA,GAC5C/mB,KAAKuJ,UAAYvJ,KAAKwJ,QAAUxJ,KAAK2I,MAAQjB,CAC/C,EACAc,OAAQ,WACN,GAAQhD,IAAIiB,EACd,GAGF,SAAS,KACP,GAAWkC,MAAQ,EACrB,CAEA,SAAS,KACP,GAAU,GAAU,GACtB,CAEA,SAAS,GAAemB,EAAQC,GAC9B,GAAWpB,MAAQ,GACnB,GAAWmB,EAAQ,GAAQC,EAE3B,GADAD,GAAUnD,EACQ,GAAU,EAAIoD,GADbA,GAAOpD,GACkB,EAAIH,GAAY,GAAU,EAAIuD,EAC5E,CAEA,SAAS,GAAUD,EAAQC,GAOzB,IAAIid,GANJld,GAAUnD,GAMa,GACnBsgB,EAAWD,GAAW,EAAI,GAAK,EAC/BE,EAAWD,EAAWD,EACtB9c,EAAS,EARbH,GADmBA,GAAOpD,GACd,EAAIH,GASZyD,EAAS,EAAIF,GACbiE,EAAI,GAAU/D,EACd0U,EAAI,GAAUzU,EAAS8D,EAAI,EAAIkZ,GAC/BnT,EAAI/F,EAAIiZ,EAAW,EAAIC,GAC3B,GAAY1hB,IAAIqB,EAAMkN,EAAG4K,IAGzB,GAAU7U,EAAQ,GAAUI,EAAQ,GAAUD,CAChD,CAEe,YAAS5B,GAGtB,OAFA,GAAU,IAAIlD,EACdyC,EAAOS,EAAQ,IACE,EAAV,EACT,CC7DA,IAAI,GAAe,CACjBM,MAAO,GACPY,UAAW4d,GACX3d,QAAS4d,GACT3d,aAAc,WACZ,GAAad,MAAQ0e,GACrB,GAAa9d,UAAY+d,GACzB,GAAa9d,QAAU+d,GACvBV,GAAW,IAAI1hB,EACf,GAAWsE,cACb,EACAC,WAAY,WACV,GAAWA,aACX,GAAaf,MAAQ,GACrB,GAAaY,UAAY4d,GACzB,GAAa3d,QAAU4d,GACnB,GAAc,GAAG,KAAY5Q,GAAU,KAAMD,KAASK,GAAO,KACxDiQ,GAAW1gB,EAASyQ,GAAO,GAC3BiQ,IAAY1gB,IAASoQ,IAAQ,IACtC,GAAM,GAAK,GAAS,GAAM,GAAKC,EACjC,EACAhO,OAAQ,WACN,KAAYgO,GAAU,KAAMD,KAASK,GAAO,GAC9C,GAGF,SAAS,GAAY9M,EAAQC,GAC3B+c,GAAO5mB,KAAK,GAAQ,CAAC,GAAU4J,EAAQ0M,GAAU1M,IAC7CC,EAAMwM,KAAMA,GAAOxM,GACnBA,EAAM6M,KAAMA,GAAO7M,EACzB,CAEA,SAAS4R,GAAU7R,EAAQC,GACzB,IAAIrE,EAAIiP,GAAU,CAAC7K,EAASnD,EAASoD,EAAMpD,IAC3C,GAAIyE,GAAI,CACN,IAAI8K,EAASrB,GAAezJ,GAAI1F,GAE5B8hB,EAAa3S,GADA,CAACqB,EAAO,IAAKA,EAAO,GAAI,GACGA,GAC5CjB,GAA0BuS,GAC1BA,EAAa9S,GAAU8S,GACvB,IAGIC,EAHAtd,EAAQL,EAASgR,GACjBzT,EAAO8C,EAAQ,EAAI,GAAK,EACxBud,EAAUF,EAAW,GAAK9gB,EAAUW,EAEpCyP,EAAe9Q,EAAImE,GAAS,IAC5B2M,GAAgBzP,EAAOyT,GAAU4M,GAAWA,EAAUrgB,EAAOyC,IAC/D2d,EAAOD,EAAW,GAAK9gB,GACZkQ,KAAMA,GAAO6Q,GACwB3Q,GAAgBzP,EAAOyT,IAA9D4M,GAAWA,EAAU,KAAO,IAAM,MAAiDA,EAAUrgB,EAAOyC,IAC7G2d,GAAQD,EAAW,GAAK9gB,GACb6P,KAAMA,GAAOkR,IAEpB1d,EAAMwM,KAAMA,GAAOxM,GACnBA,EAAM6M,KAAMA,GAAO7M,IAErB+M,EACEhN,EAASgR,GACP3E,GAAM,GAASrM,GAAUqM,GAAM,GAASK,MAAUA,GAAU1M,GAE5DqM,GAAMrM,EAAQ0M,IAAWL,GAAM,GAASK,MAAU,GAAU1M,GAG9D0M,IAAW,IACT1M,EAAS,KAAS,GAAUA,GAC5BA,EAAS0M,KAASA,GAAU1M,IAE5BA,EAASgR,GACP3E,GAAM,GAASrM,GAAUqM,GAAM,GAASK,MAAUA,GAAU1M,GAE5DqM,GAAMrM,EAAQ0M,IAAWL,GAAM,GAASK,MAAU,GAAU1M,EAIxE,MACEgd,GAAO5mB,KAAK,GAAQ,CAAC,GAAU4J,EAAQ0M,GAAU1M,IAE/CC,EAAMwM,KAAMA,GAAOxM,GACnBA,EAAM6M,KAAMA,GAAO7M,GACvBqB,GAAK1F,EAAGoV,GAAUhR,CACpB,CAEA,SAASqd,KACP,GAAaxe,MAAQgT,EACvB,CAEA,SAASyL,KACP,GAAM,GAAK,GAAS,GAAM,GAAK5Q,GAC/B,GAAa7N,MAAQ,GACrByC,GAAK,IACP,CAEA,SAASic,GAAgBvd,EAAQC,GAC/B,GAAIqB,GAAI,CACN,IAAIjB,EAAQL,EAASgR,GACrB+L,GAASrhB,IAAIQ,EAAImE,GAAS,IAAMA,GAASA,EAAQ,EAAI,KAAO,KAAOA,EACrE,MACE,GAAWL,EAAQ,GAAQC,EAE7B,GAAWpB,MAAMmB,EAAQC,GACzB4R,GAAU7R,EAAQC,EACpB,CAEA,SAASud,KACP,GAAW/d,WACb,CAEA,SAASge,KACPF,GAAgB,GAAU,IAC1B,GAAW7d,UACPxD,EAAI6gB,IAAY1gB,IAAS,KAAYqQ,GAAU,MACnD,GAAM,GAAK,GAAS,GAAM,GAAKA,GAC/BpL,GAAK,IACP,CAKA,SAAS+K,GAAMnO,EAASwO,GACtB,OAAQA,GAAWxO,GAAW,EAAIwO,EAAU,IAAMA,CACpD,CAEA,SAASmR,GAAard,EAAGC,GACvB,OAAOD,EAAE,GAAKC,EAAE,EAClB,CAEA,SAASqd,GAAclY,EAAOjK,GAC5B,OAAOiK,EAAM,IAAMA,EAAM,GAAKA,EAAM,IAAMjK,GAAKA,GAAKiK,EAAM,GAAKjK,EAAIiK,EAAM,IAAMA,EAAM,GAAKjK,CAC5F,CCjIO,SAASoiB,GAAazb,EAAqB2L,GACjD,OpBoBc,WACb,IAGIpC,EACAkH,EAJAW,EAASsK,GAAS,CAAC,EAAG,IACtB/P,EAAS+P,GAAS,IAClBjd,EAAYid,GAAS,GAGrBlgB,EAAS,CAACe,MAEd,SAAelD,EAAGG,GAChB+P,EAAKzV,KAAKuF,EAAIoX,EAAOpX,EAAGG,IACxBH,EAAE,IAAMiB,EAASjB,EAAE,IAAMiB,CAC3B,GAEA,SAASqhB,IACP,IAAInN,EAAI4C,EAAOC,MAAMzd,KAAM8P,WACvB8N,EAAI7F,EAAO0F,MAAMzd,KAAM8P,WAAanJ,EACpCjB,EAAImF,EAAU4S,MAAMzd,KAAM8P,WAAanJ,EAM3C,OALAgP,EAAO,GACPkH,EAAStK,IAAeqI,EAAE,GAAKjU,GAAUiU,EAAE,GAAKjU,EAAS,GAAGuG,OAC5D4K,GAAalQ,EAAQgW,EAAGlY,EAAG,GAC3BkV,EAAI,CAACnY,KAAM,UAAWiG,YAAa,CAACiN,IACpCA,EAAOkH,EAAS,KACTjC,CACT,CAcA,OAZAmN,EAAOvK,OAAS,SAAS9L,GACvB,OAAO5B,UAAUpO,QAAU8b,EAAsB,mBAAN9L,EAAmBA,EAAIoW,GAAS,EAAEpW,EAAE,IAAKA,EAAE,KAAMqW,GAAUvK,CACxG,EAEAuK,EAAOhQ,OAAS,SAASrG,GACvB,OAAO5B,UAAUpO,QAAUqW,EAAsB,mBAANrG,EAAmBA,EAAIoW,IAAUpW,GAAIqW,GAAUhQ,CAC5F,EAEAgQ,EAAOld,UAAY,SAAS6G,GAC1B,OAAO5B,UAAUpO,QAAUmJ,EAAyB,mBAAN6G,EAAmBA,EAAIoW,IAAUpW,GAAIqW,GAAUld,CAC/F,EAEOkd,CACT,CoB1DQ,GAAYvK,OAAO,CAACpR,EAASd,UAAWc,EAASb,WAAWwM,OAAOA,EAAnE,EACR,CAKO,SAASiQ,GAAetlB,GAC9B,MAAM8hB,EHiGQ,SAASnc,GACtB4d,GAAKC,GACL,GAAK,GAAK,GACV,GAAK,GAAK,GAAK,EACf,GAAK,IAAI/gB,EACT,GAAK,IAAIA,EACT,GAAK,IAAIA,EACTyC,EAAOS,EAAQ,IAEf,IAAI5C,GAAK,GACLG,GAAK,GACLyE,GAAK,GACL+I,EAAInM,EAAMxB,EAAGG,EAAGyE,GAGpB,OAAI+I,EAAIhN,IACNX,EAAI,GAAIG,EAAI,GAAIyE,EAAI,GAEhB6b,GAAK/f,IAASV,EAAI,GAAIG,EAAI,GAAIyE,EAAI,KACtC+I,EAAInM,EAAMxB,EAAGG,EAAGyE,IAERjE,GAAiB,CAACmR,IAAKA,KAG1B,CAAC1Q,EAAMjB,EAAGH,GAAKiB,EAASc,EAAK6C,EAAI+I,GAAK1M,EAC/C,CG1HkB,CAAYhE,GAC7B,MAAO,CAAE4I,UAAWkZ,EAAS,GAAIjZ,SAAUiZ,EAAS,GACrD,CAKO,SAASyD,GAAWvlB,GAC1B,OAAO,GAAQA,EAChB,CAKO,SAASwlB,GAAaxlB,GAC5B,MAAMqW,ED4GQ,SAAS9V,GACtB,IAAIF,EAAGmD,EAAGoE,EAAGC,EAAG4d,EAAQC,EAAUje,EAOlC,GALAyM,GAAOJ,KAAY,GAAUD,GAAOsC,KACpCiO,GAAS,GACTlf,EAAO3E,EAAS,IAGZiD,EAAI4gB,GAAOplB,OAAQ,CAIrB,IAHAolB,GAAOxS,KAAKqT,IAGP5kB,EAAI,EAAkBolB,EAAS,CAAxB7d,EAAIwc,GAAO,IAAkB/jB,EAAImD,IAAKnD,EAE5C6kB,GAActd,GADlBC,EAAIuc,GAAO/jB,IACY,KAAO6kB,GAActd,EAAGC,EAAE,KAC3C4L,GAAM7L,EAAE,GAAIC,EAAE,IAAM4L,GAAM7L,EAAE,GAAIA,EAAE,MAAKA,EAAE,GAAKC,EAAE,IAChD4L,GAAM5L,EAAE,GAAID,EAAE,IAAM6L,GAAM7L,EAAE,GAAIA,EAAE,MAAKA,EAAE,GAAKC,EAAE,KAEpD4d,EAAOjoB,KAAKoK,EAAIC,GAMpB,IAAK6d,GAAW,IAAkCrlB,EAAI,EAAGuH,EAAI6d,EAAlCjiB,EAAIiiB,EAAOzmB,OAAS,GAAyBqB,GAAKmD,EAAGoE,EAAIC,IAAKxH,EACvFwH,EAAI4d,EAAOplB,IACNoH,EAAQgM,GAAM7L,EAAE,GAAIC,EAAE,KAAO6d,IAAUA,EAAWje,EAAO,GAAUI,EAAE,GAAIiM,GAAUlM,EAAE,GAE9F,CAIA,OAFAwc,GAAS,GAAQ,KAEV,KAAYjO,KAAYtC,KAASsC,IAClC,CAAC,CAACtB,IAAKA,KAAM,CAACA,IAAKA,MACnB,CAAC,CAAC,GAAShB,IAAO,CAACC,GAASI,IACpC,CC/IgB,CAAUlU,GAEzB,GAAIqW,EAAQ,CACX,MAAMhH,EAAY,CAAEC,KAAM+G,EAAO,GAAG,GAAI7G,MAAO6G,EAAO,GAAG,GAAI5G,IAAK4G,EAAO,GAAG,GAAI9G,OAAQ8G,EAAO,GAAG,IAKlG,OAJIhH,EAAUG,MAAQH,EAAUC,OAC/BD,EAAUG,MAAQ,IAClBH,EAAUC,MAAQ,KAEZD,C,CAER,MAAO,CAAEC,KAAM,EAAGE,MAAO,EAAGC,IAAK,EAAGF,OAAQ,EAC7C,CAYO,SAASoW,GAAgBC,EAAeC,EAAcC,EAAeC,GAE3E,IAAIC,EAAsD,GAEtDD,IAAS,MACZA,GAAQ,UAELD,IAAU,KACbA,GAAS,SAENF,GAAS,KACZA,EAAQ,SAELC,GAAQ,MACXA,EAAO,UAIR,IAAII,EAAW5iB,KAAKyT,IAAI,IAAK+O,EAAOE,GAAQ1iB,KAAKgB,MAAMwhB,EAAOE,GAAQ,KAClEG,GAAWN,EAAQE,GAASziB,KAAKgB,MAAMuhB,EAAQE,GAAS,IAE5D,IAAK,IAAIK,EAAKJ,EAAMI,EAAKN,EAAMM,GAAUF,EAAU,CAClD,IAAIG,EAAmC,GACvCJ,EAAaxoB,KAAK,CAAC4oB,IAEfD,EAAKF,EAAWJ,IACnBI,EAAWJ,EAAOM,GAGnB,IAAK,IAAIE,EAAKF,EAAIE,GAAMF,EAAKF,EAAUI,GAAU,EAChDD,EAAQ5oB,KAAK,CAAC6oB,EAAIT,IAGnB,IAAK,IAAIU,EAAKV,EAAOU,GAAMR,EAAOQ,GAAUJ,EAC3CE,EAAQ5oB,KAAK,CAAC2oB,EAAKF,EAAUK,IAG9B,IAAK,IAAID,EAAKF,EAAKF,EAAUI,GAAMF,EAAIE,GAAU,EAChDD,EAAQ5oB,KAAK,CAAC6oB,EAAIP,IAGnB,IAAK,IAAIQ,EAAKR,EAAOQ,GAAMV,EAAOU,GAAUJ,EAC3CE,EAAQ5oB,KAAK,CAAC2oB,EAAIG,G,CAIpB,MAAO,CAAEvmB,KAAM,eAAgBiG,YAAaggB,EAC7C,CAQO,SAASO,GAAkB7c,GACjC,IAAId,EAAY4d,GAAe9c,EAASd,WACpCC,EAAWxF,KAAKyB,KAAKzB,KAAKqB,IAAKgF,EAASb,SAAW,aAAmB,WAEtE4d,EAAcD,GAAe9c,EAASb,UAS1C,OAPIxF,KAAKC,IAAImjB,GAAe,KAC3B7d,EAAY4d,GAAe5d,EAAY,MAGxCc,EAASd,UAAYA,EACrBc,EAASb,SAAWA,EAEba,CACR,CAKO,SAAS8c,GAAe/S,GAU9B,OATAA,GAAgB,KAEJ,MACXA,GAAS,KAENA,GAAS,MACZA,GAAS,KAGHA,CACR,C,eCoHO,MAAMiT,WAAiBC,GAAA,EAA9B,c,oBASC,8C,yDACA,8C,yDACA,6C,yDACA,6C,yDACA,6C,yDACA,oC,gDAA0B,IAC1B,oC,gDAA0B,IAE1B,0C,gDAAqD,CAAC,IACtD,6C,gDAAmC,IACnC,kD,gDAAwC,IAExC,+C,iDAAsC,IACtC,iD,gDAA2D,CAAE5mB,KAAM,qBAAsB2G,WAAY,MAErG,8C,gDAAkD,OAElD,kC,yDACA,mC,yDACA,mC,yDACA,mC,yDACA,mC,yDAEA,yC,gDAAuB,CAAC,CAAC,EAAG,GAAI,CAAC,EAAG,MAEpC,2C,gDAAoC,CAAEkC,UAAW,EAAGC,SAAU,KAC9D,yC,gDAAqF,CAAEyG,KAAM,EAAGE,MAAO,EAAGC,IAAK,EAAGF,OAAQ,KAC1H,6C,gDAAyF,CAAED,KAAM,EAAGE,MAAO,EAAGC,IAAK,EAAGF,OAAQ,KAE9H,8C,iDAAqC,IAErC,uC,yDAEA,kC,yDACA,kC,yDAEA,yC,iDAAgC,IAEhC,uC,gDAA6B,IAC7B,uC,gDAA6B,GA06B9B,CAx6BW,YAAAqX,GACT,MAAMhlB,EAAatE,KAAKY,IAAI,cACtB2oB,EC9SO,SAASjlB,EAAYsH,GAClC,IAEI4d,EACAC,EAHA9D,EAAS,EACTX,EAAc,IAIlB,SAASuE,EAAKlhB,GAKZ,OAJIA,IACyB,mBAAhB2c,GAA4ByE,EAAczE,aAAaA,EAAYvH,MAAMzd,KAAM8P,YAC1FlI,EAAOS,EAAQmhB,EAAiBC,KAE3BA,EAAcjW,QACvB,CAqDA,OAnDA+V,EAAK7F,KAAO,SAASrb,GAEnB,OADAT,EAAOS,EAAQmhB,EAAiB,KACzB,GAAShW,QAClB,EAEA+V,EAAKG,QAAU,SAASrhB,GAEtB,OADAT,EAAOS,EAAQmhB,EAAiB,KACzB,GAAYhW,QACrB,EAEA+V,EAAKxQ,OAAS,SAAS1Q,GAErB,OADAT,EAAOS,EAAQmhB,EAAiB,KACzB,GAAWhW,QACpB,EAEA+V,EAAK/E,SAAW,SAASnc,GAEvB,OADAT,EAAOS,EAAQmhB,EAAiB,KACzB,GAAahW,QACtB,EAEA+V,EAAKjlB,WAAa,SAASoN,GACzB,OAAK5B,UAAUpO,QACf8nB,EAAwB,MAAL9X,GAAapN,EAAa,KAAMgZ,KAAahZ,EAAaoN,GAAG9J,OACzE2hB,GAFuBjlB,CAGhC,EAEAilB,EAAK3d,QAAU,SAAS8F,GACtB,OAAK5B,UAAUpO,QACf+nB,EAAqB,MAAL/X,GAAa9F,EAAU,KAAM,IAAI8Z,GAAWC,IAAW,IAAId,GAAYjZ,EAAU8F,GACtE,mBAAhBsT,GAA4ByE,EAAczE,YAAYA,GAC1DuE,GAHuB3d,CAIhC,EAEA2d,EAAKvE,YAAc,SAAStT,GAC1B,OAAK5B,UAAUpO,QACfsjB,EAA2B,mBAANtT,EAAmBA,GAAK+X,EAAczE,aAAatT,IAAKA,GACtE6X,GAFuBvE,CAGhC,EAEAuE,EAAK5D,OAAS,SAASjU,GACrB,IAAK5B,UAAUpO,OAAQ,OAAOikB,EAC9B,GAAS,MAALjU,EAAWiU,EAAS,SACnB,CACH,MAAM5X,EAAIhI,KAAKmB,MAAMwK,GACrB,KAAM3D,GAAK,GAAI,MAAM,IAAI+X,WAAW,mBAAmBpU,KACvDiU,EAAS5X,CACX,CAEA,OADgB,OAAZnC,IAAkB6d,EAAgB,IAAI/D,GAAWC,IAC9C4D,CACT,EAEOA,EAAKjlB,WAAWA,GAAYqhB,OAAOA,GAAQ/Z,QAAQA,EAC5D,CD4Oe,GACb2d,EAAKjlB,WAAWA,GAChBtE,KAAK2pB,cAAc,UAAWJ,EAC/B,CASO,QAAAnd,GACN,OAAOpM,KAAKkN,OAAOlN,KAAK4pB,gBAAgBC,SAAS,CAAEpkB,EAAGzF,KAAK4Z,QAAU,EAAGhU,EAAG5F,KAAK8Z,SAAW,IAC5F,CAKO,WAAAgQ,GACN,OAAO9pB,KAAK+pB,YACb,CAKO,SAAAhY,GACN,OAAO/R,KAAKgqB,UACb,CAEU,eAAAC,GAET,MAAMhI,EAASjiB,KAAKY,IAAI,UAClBohB,EAAShiB,KAAKY,IAAI,UAClBspB,EAAiBlqB,KAAKkqB,eAEd,QAAVjI,GAA8B,QAAVD,GACnBhiB,KAAKmqB,UACRnqB,KAAKmqB,SAAS1a,UAGfzP,KAAKmqB,SAAWD,EAAetmB,OAAOxD,GAAG,SAAUgqB,IAClD,MAAMjI,EAAcniB,KAAKY,IAAI,eACvByhB,EAAmBriB,KAAKY,IAAI,mBAAoB,GAChDwhB,EAAgBpiB,KAAKY,IAAI,gBAAiB,GAE1CypB,EAAaD,EAAME,cAIzB,IAAI,gBAAoBD,EAAYrqB,MAInC,OAHAqqB,EAAWE,iBAMZ,MAAM5hB,EAAQuhB,EAAeve,SAAS6e,QAAQJ,EAAMzhB,OAErC,QAAVqZ,EACJhiB,KAAKyqB,iBAAiBJ,EAAWK,OAAQ/hB,GAEvB,WAAVqZ,EACRhiB,KAAK2qB,oBAAoBN,EAAWK,OAAS,EAAIrI,EAAkBD,EAAeD,GAEhE,WAAVH,GACRhiB,KAAK4qB,oBAAoBP,EAAWK,OAAS,EAAIrI,EAAkBD,EAAeD,GAGpE,QAAVF,EACJjiB,KAAKyqB,iBAAiBJ,EAAWQ,OAAQliB,GAEvB,WAAVsZ,EACRjiB,KAAK2qB,oBAAoBN,EAAWQ,OAAS,EAAIxI,EAAkBD,EAAeD,GAEhE,WAAVF,GACRjiB,KAAK4qB,oBAAoBP,EAAWQ,OAAS,EAAIxI,EAAkBD,EAAeD,E,IAKpFniB,KAAK8qB,WAAW5qB,KAAKF,KAAKmqB,WAGtBnqB,KAAKmqB,UACRnqB,KAAKmqB,SAAS1a,SAGjB,CAEO,gBAAAzO,GACNN,MAAMM,mBAEN,MAAMsD,EAAatE,KAAKY,IAAI,cACtB0Y,EAAItZ,KAAK+qB,aACTxR,EAAIvZ,KAAKgrB,cAETC,EAAqBjrB,KAAKkrB,mBAAmB9hB,WAEnD,GAAIpJ,KAAKkB,QAAQ,cAAe,CAC/BlB,KAAKspB,eACLtpB,KAAK6L,sBACL7L,KAAKmrB,UAEL7mB,EAAW6U,MAAMnZ,KAAKwE,WAAW,YAAcxE,KAAKY,IAAI,YAAa,IACjE0D,EAAWuY,QACdvY,EAAWuY,OAAO,CAAC7c,KAAKY,IAAI,YAAa,GAAIZ,KAAKY,IAAI,YAAa,GAAIZ,KAAKY,IAAI,YAAa,KAG9F,IAAIwqB,EAAOprB,KAAKO,cAAc+D,WAC9B,GAAI8mB,GAAQA,GAAQ9mB,EAAY,CAC/B,IAAI+mB,EAAK/R,EAAI,EACTgS,EAAK/R,EAAI,EACb,GAAI6R,EAAKle,OAAQ,CAChB,IAAIqe,EAAiBH,EAAKle,OAAO,CAACme,EAAIC,IAEtC,GAAIC,EAAgB,CAEnB,IAAIC,EAAKlnB,EAAWinB,GACpB,GAAIC,EAAI,CACP,IAAIpS,EAAY9U,EAAW8U,YAEvBqS,EAAKJ,GAAOG,EAAG,GAAKpS,EAAU,IAC9BsS,EAAKJ,GAAOE,EAAG,GAAKpS,EAAU,IAElC9U,EAAW8U,UAAU,CAACqS,EAAIC,IAE1B1rB,KAAKuP,OAAO,aAAckc,GAC1BzrB,KAAKuP,OAAO,aAAcmc,E,KAqB/B,IAdI1rB,KAAKkB,QAAQ,WAAalB,KAAKkB,QAAQ,YAC1ClB,KAAKiqB,kBAEFjqB,KAAK2rB,mBACR3rB,KAAKkrB,mBAAmB9hB,WAAa,GAErCpJ,KAAKyE,OAAOmnB,MAAMnnB,IACjB,UAAezE,KAAKkrB,mBAAmB9hB,WAAY3E,EAAOC,YAAY,IAIvE1E,KAAKmrB,WAG2B,GAA7BF,EAAmBvpB,SAAgB4X,GAAKtZ,KAAK6rB,KAAOtS,GAAKvZ,KAAK8rB,KAAO9rB,KAAK2rB,mBACzErS,EAAI,GAAKC,EAAI,EAAG,CACnB,IAAI8R,EAAK/R,EAAI,EACTgS,EAAK/R,EAAI,EAEbjV,EAAWmV,QAAQ,CAACH,EAAGC,GAAIvZ,KAAKkrB,oBAChC,MAAMa,EAAWznB,EAAW6U,QAK5B,GAHAnZ,KAAK2pB,cAAc,WAAYoC,GAC/BznB,EAAW6U,MAAM4S,EAAW/rB,KAAKY,IAAI,YAAa,IAE9CZ,KAAKqB,gBAAiB,CACzB,IAAImqB,EAAKlnB,EAAWtE,KAAKqB,iBACzB,GAAImqB,EAAI,CACP,IAAIpS,EAAY9U,EAAW8U,YAEvBqS,EAAKJ,GAAOG,EAAG,GAAKpS,EAAU,IAC9BsS,EAAKJ,GAAOE,EAAG,GAAKpS,EAAU,IAElC9U,EAAW8U,UAAU,CAACqS,EAAIC,IAE1B1rB,KAAKuP,OAAO,aAAckc,GAC1BzrB,KAAKuP,OAAO,aAAcmc,GAE1B1rB,KAAKgsB,SAAW5S,EAAU,GAC1BpZ,KAAKisB,SAAW7S,EAAU,E,EAI5BpZ,KAAK6L,sBAEL,MAAMtH,EAAUvE,KAAKwE,WAAW,WAChCxE,KAAKksB,WAAa3nB,EAAQwU,OAAO/Y,KAAKkrB,mB,CAOxC,GAHAlrB,KAAK6rB,IAAMvS,EACXtZ,KAAK8rB,IAAMvS,EAEPvZ,KAAKkB,QAAQ,eAAgB,CAChC,MAAMZ,EAAWN,KAAKO,cAAc4rB,YAC9BA,EAAcnsB,KAAKY,IAAI,eACzBurB,IAAgB7rB,IACnBN,KAAKosB,iBAAiB,eAClB9rB,GACHA,EAASmP,UAEN0c,IACHA,EAAYhd,WAAW,QAASnP,MAChCA,KAAKkE,SAAShE,KAAKisB,IAGpBnsB,KAAKuP,OAAO,cAAe4c,G,CAI7B,GAAInsB,KAAKkB,QAAQ,aAAc,CAC9BoD,EAAW6U,MAAMnZ,KAAKwE,WAAW,YAAcxE,KAAKY,IAAI,YAAa,IACrEZ,KAAK6L,sBAEL7L,KAAKyE,OAAOmnB,MAAMnnB,IACbA,EAAO4nB,OAAuB,mBAC7B5nB,EAAO7D,IAAI,cACd,OAAY6D,EAAO3C,WAAYP,IAC9B,MAAM+qB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAO/oB,IAAI,QAASzD,KAAKY,IAAI,a,UASpC,MAAMurB,EAAcnsB,KAAKY,IAAI,eAC7B,GAAIurB,EAAa,CAChB,MAAMzK,EAAY1hB,KAAKY,IAAI,YAAa,GAEpC8gB,GAAa1hB,KAAKY,IAAI,eAAgB,GACzCZ,KAAKysB,KAAK7oB,OAAO8oB,KAAK,cAAc,KACnCP,EAAYQ,YAAYlpB,IAAI,YAAY,EAAK,IAI9C0oB,EAAYQ,YAAYlpB,IAAI,YAAY,GAGrCie,GAAa1hB,KAAKY,IAAI,eAAgB,IACzCurB,EAAYS,WAAWnpB,IAAI,YAAY,GAGvC0oB,EAAYS,WAAWnpB,IAAI,YAAY,E,GAKtCzD,KAAKkB,QAAQ,eAAiBlB,KAAKkB,QAAQ,iBAC9CoD,EAAW8U,UAAU,CAACpZ,KAAKY,IAAI,aAAcZ,KAAK4Z,QAAU,GAAI5Z,KAAKY,IAAI,aAAcZ,KAAK8Z,SAAW,KACvG9Z,KAAK6L,uBAGFvH,EAAWuY,SACV7c,KAAKkB,QAAQ,cAAgBlB,KAAKkB,QAAQ,cAAgBlB,KAAKkB,QAAQ,gBAC1EoD,EAAWuY,OAAO,CAAC7c,KAAKY,IAAI,YAAa,GAAIZ,KAAKY,IAAI,YAAa,GAAIZ,KAAKY,IAAI,YAAa,KAC7FZ,KAAK6L,wBAIH7L,KAAKkB,QAAQ,cAAgBlB,KAAKY,IAAI,SAAWZ,KAAKY,IAAI,UAC7DZ,KAAK6sB,aAEP,CAGU,OAAA1B,GACT,MAAM7mB,EAAatE,KAAKY,IAAI,cAE5B,IAAI0Y,EAAItZ,KAAK+qB,aACTxR,EAAIvZ,KAAKgrB,cAEb,GAAI1R,EAAI,GAAKC,EAAI,EAAG,CACnBjV,EAAWmV,QAAQ,CAACH,EAAGC,GAAIvZ,KAAKkrB,oBAChClrB,KAAK2pB,cAAc,WAAYrlB,EAAW6U,SAE1C,MAAMC,EAAY9U,EAAW8U,YAE7BpZ,KAAKuP,OAAO,aAAc6J,EAAU,IACpCpZ,KAAKuP,OAAO,aAAc6J,EAAU,IAEpCpZ,KAAKgsB,SAAW5S,EAAU,GAC1BpZ,KAAKisB,SAAW7S,EAAU,GAE1B,MAAM7U,EAAUvE,KAAKwE,WAAW,WAChCxE,KAAKksB,WAAa3nB,EAAQwU,OAAO/Y,KAAKkrB,oBAEtClrB,KAAK+pB,aAAe,GAAyB/pB,KAAKkrB,oBAElD,MAAMnS,EAAS,GAAuB/Y,KAAKkrB,oBAG3C,GAFAlrB,KAAKgqB,WAAajR,EAEd/Y,KAAKkrB,mBAAmB9hB,WAAW1H,OAAS,EAAG,CAElDqX,EAAO/G,KAAO,SAAYhS,KAAKgqB,WAAWhY,KAAM,GAChD+G,EAAO7G,MAAQ,SAAYlS,KAAKgqB,WAAW9X,MAAO,GAClD6G,EAAO5G,IAAM,SAAYnS,KAAKgqB,WAAW7X,IAAK,GAC9C4G,EAAO9G,OAAS,SAAYjS,KAAKgqB,WAAW/X,OAAQ,GAEpD,MAAM6a,EAAgB9sB,KAAK+sB,eAEvBD,IAAkB,cAAkB/T,EAAQ+T,KAC/C9sB,KAAKgtB,iBAAkB,EACvBhtB,KAAK+sB,eAAiBhU,E,CAIxB/Y,KAAKitB,YAAa,C,CAEpB,CAMO,YAAAC,GACN,IAAIA,EAAeltB,KAAKY,IAAI,gBAC5B,IAAKssB,EAAc,CAClB,MACMnU,EADU/Y,KAAKwE,WAAW,WACTuU,OAAO/Y,KAAKkrB,oBAE7BlZ,EAAO+G,EAAO,GAAG,GACjB5G,EAAM4G,EAAO,GAAG,GAEhB7G,EAAQ6G,EAAO,GAAG,GAClB9G,EAAS8G,EAAO,GAAG,GAEzBmU,EAAeltB,KAAKkN,OAAO,CAAEzH,EAAGuM,GAAQE,EAAQF,GAAQ,EAAGpM,EAAGuM,GAAOF,EAASE,GAAO,G,CAEtF,OAAO+a,CACR,CAQO,MAAAC,CAAOC,GACbptB,KAAKqtB,eAAertB,KAAKktB,eAAgBltB,KAAKY,IAAI,gBAAiB,IAAI,EAAMwsB,EAAUptB,KAAKY,IAAI,iBAAkBZ,KAAKY,IAAI,iBAC5H,CAEO,eAAA8P,GACN,MAAMpM,EAAatE,KAAKY,IAAI,cAC5B,GAAI0D,EAAW4I,OAAQ,CACtB,IAAIoM,EAAItZ,KAAK+qB,aACTxR,EAAIvZ,KAAKgrB,cACT1R,EAAI,GAAKC,EAAI,IAChBvZ,KAAKqB,gBAAkBiD,EAAW4I,OAAO,CAAClN,KAAK+qB,aAAe,EAAG/qB,KAAKgrB,cAAgB,I,CAGxFtqB,MAAMgQ,iBACP,CAEO,aAAA4c,GAEN,GADA5sB,MAAM4sB,gBACFttB,KAAKgtB,gBAAiB,CACzBhtB,KAAKgtB,iBAAkB,EACvB,MAAMvqB,EAAO,mBACTzC,KAAK4D,OAAOC,UAAUpB,IACzBzC,KAAK4D,OAAOE,SAASrB,EAAM,CAAEA,KAAMA,EAAMsB,OAAQ/D,M,CAGpD,CAEU,WAAA6sB,GACJ7sB,KAAKkqB,eAAeve,SAAS4hB,cACjCvtB,KAAKkqB,eAAeve,SAAS4hB,eAAevtB,KAAKY,IAAI,cAAgBZ,KAAKY,IAAI,SAAWZ,KAAKY,IAAI,SAEpG,CAMO,mBAAA+D,GACN3E,KAAK2rB,kBAAmB,EACxB3rB,KAAK8L,WACN,CAKO,mBAAAD,GACN7L,KAAKyE,OAAOmnB,MAAMnnB,IACjBA,EAAOoH,qBAAqB,GAE9B,CAEU,SAAA9L,GACTC,KAAKwtB,eAAettB,KAAK4gB,GAAqBrS,IAAIzO,KAAK2O,QACvD3O,KAAKytB,UAAUC,UAAY,aAAiB1tB,KAAKytB,UAAUC,UAAW,CAAC,QAEvE1tB,KAAKkE,SAAShE,KAAKF,KAAKoE,kBAExB1D,MAAMX,YAENC,KAAKspB,eAELtpB,KAAKkqB,eAAehmB,SAAShE,KAAKF,KAAK4pB,iBAET,MAA1B5pB,KAAKY,IAAI,eACZZ,KAAKyD,IAAI,aAAczD,KAAK4Z,QAAU,GAET,MAA1B5Z,KAAKY,IAAI,eACZZ,KAAKyD,IAAI,aAAczD,KAAK8Z,SAAW,GAKxC9Z,KAAKkqB,eAAezmB,IAAI,eAAe,GACvCzD,KAAKkqB,eAAezmB,IAAI,uBAAuB,GAC/CzD,KAAKkqB,eAAezmB,IAAI,aAAckqB,GAAA,EAAUlf,IAAIzO,KAAK2O,MAAO,CAC/D+e,UAAW,CAAC,MAAO,cACnBE,KAAMC,GAAA,GAAMC,QAAQ,GACpB5K,YAAa,KAGdljB,KAAK8qB,WAAW5qB,KAAKF,KAAKkqB,eAAetmB,OAAOxD,GAAG,eAAgBgqB,IAClEpqB,KAAK+tB,iBAAiB3D,EAAM,KAG7BpqB,KAAK8qB,WAAW5qB,KAAKF,KAAKkqB,eAAetmB,OAAOxD,GAAG,mBAAoBgqB,IACtEpqB,KAAKguB,eAAe5D,EAAM,KAG3BpqB,KAAK8qB,WAAW5qB,KAAKF,KAAKkqB,eAAetmB,OAAOxD,GAAG,qBAAsBgqB,IACxEpqB,KAAKiuB,iBAAiB7D,EAAM,KAG7B,IAAI8D,GAAU,EACd,IAAK,IAAInrB,EAAI,EAAGA,EAAI,MAASorB,SAASzsB,OAAQqB,IACzC,MAASorB,SAASprB,GAAGqrB,MAAM,iBAC9BF,GAAU,GAGPA,EAIJluB,KAAK2O,MAAM0f,kBAHXruB,KAAK2O,MAAM2f,gBAMZtuB,KAAK6sB,aAEN,CAEU,gBAAAkB,CAAiB3D,GAE1BpqB,KAAKuuB,eAAiBvuB,KAAKY,IAAI,YAAa,GAC5C,MAAM4tB,EAAaxuB,KAAKkqB,eAAeuE,YAEvC,IAAIC,EAAQ,OAAaF,GAAY9sB,OACrC,GAAa,GAATgtB,EAAY,CAEf,IAAIC,EAAYH,EAAW,GACtBG,IACJA,EAAYH,EAAW,IAGpBG,GAAcA,EAAUlpB,GAAK2kB,EAAMzhB,MAAMlD,GAAKkpB,EAAU/oB,GAAKwkB,EAAMzhB,MAAM/C,IAC5E8oB,EAAQ,E,CAIV,GAAIA,EAAQ,EAAG,CACd1uB,KAAK4uB,gBAAkB5uB,KAAKY,IAAI,cAChCZ,KAAK6uB,gBAAkB7uB,KAAKY,IAAI,cAChCZ,KAAK8uB,eAAiB9uB,KAAKY,IAAI,aAC/BZ,KAAK+uB,eAAiB/uB,KAAKY,IAAI,aAC/BZ,KAAKgvB,eAAiBhvB,KAAKY,IAAI,aAE/B,MAAMquB,EAASjvB,KAAKkqB,eAAegF,kBACnC,GAAID,EAAQ,CACX,IAAIE,EAAYnvB,KAAKovB,YAAYH,GAC7BE,IACHnvB,KAAKkqB,eAAeuE,YAAYQ,GAAUE,E,OAIxC,GAAa,GAATT,EAAY,CACpB,IAAIW,EAAKrvB,KAAKkqB,eAAetpB,IAAI,cAKjC,GAJIyuB,GACHA,EAAGzrB,OAAO0rB,WAAW,SAGlBtvB,KAAKY,IAAI,SAAWZ,KAAKY,IAAI,QAAS,CAErCZ,KAAKuvB,KACRvvB,KAAKuvB,IAAI3f,OAEN5P,KAAKwvB,MACRxvB,KAAKwvB,KAAK5f,OAEP5P,KAAKyvB,MACRzvB,KAAKyvB,KAAK7f,OAEP5P,KAAK0vB,MACR1vB,KAAK0vB,KAAK9f,OAEP5P,KAAK2vB,MACR3vB,KAAK2vB,KAAK/f,OAGX,MAAM+e,EAAY3uB,KAAKkqB,eAAeve,SAAS6e,QAAQJ,EAAMzhB,OAC7D3I,KAAK4uB,gBAAkB5uB,KAAKY,IAAI,cAChCZ,KAAK6uB,gBAAkB7uB,KAAKY,IAAI,cAChCZ,KAAK8uB,eAAiB9uB,KAAKY,IAAI,aAC/BZ,KAAK+uB,eAAiB/uB,KAAKY,IAAI,aAC/BZ,KAAKgvB,eAAiBhvB,KAAKY,IAAI,aAE/B,IAAI0D,EAAatE,KAAKY,IAAI,cAE1B,GAAI0D,EAAW4I,OAAQ,CACtB,IAAI0iB,EAAKtrB,EAAW4I,OAAO,CAACyhB,EAAUlpB,EAAGkpB,EAAU/oB,IAC/CiqB,EAAKvrB,EAAW4I,OAAO,CAACyhB,EAAUlpB,EAAI,EAAGkpB,EAAU/oB,EAAI,IACvDgqB,GAAMC,IACT7vB,KAAK8vB,MAAQ/pB,KAAKC,IAAI6pB,EAAG,GAAKD,EAAG,IACjC5vB,KAAK+vB,MAAQhqB,KAAKC,IAAI6pB,EAAG,GAAKD,EAAG,I,GAKtC,CASO,MAAA1iB,CAAOvE,GACb,IAAIrE,EAAatE,KAAKY,IAAI,cAE1B,GAAI0D,EAAW4I,OAAQ,CACtB,MAAM6b,EAAKzkB,EAAW4I,OAAO,CAACvE,EAAMlD,EAAGkD,EAAM/C,IAC7C,GAAImjB,EACH,MAAO,CAAEzd,UAAWyd,EAAG,GAAIxd,SAAUwd,EAAG,G,CAI1C,MAAO,CAAEzd,UAAW,EAAGC,SAAU,EAClC,CAWO,OAAAF,CAAQ1C,EAAkBgZ,EAAoBC,GACpD,IACI4J,EADAlnB,EAAatE,KAAKY,IAAI,cAQ1B,GALK0D,EAAWuY,SACf8E,OAAY9gB,EACZ+gB,OAAY/gB,GAGI,MAAb8gB,GAAkC,MAAbC,EAAmB,CAC1B,MAAbD,IACHA,EAAY,GAEI,MAAbC,IACHA,EAAY,GAEb,IAAI9O,EAAWxO,EAAWuY,SAC1BvY,EAAWuY,OAAO,CAAC8E,EAAWC,EAAW,IACzC4J,EAAKlnB,EAAW,CAACqE,EAAM2C,UAAW3C,EAAM4C,WACxCjH,EAAWuY,OAAO/J,E,MAGlB0Y,EAAKlnB,EAAW,CAACqE,EAAM2C,UAAW3C,EAAM4C,WAGzC,OAAIigB,EACI,CAAE/lB,EAAG+lB,EAAG,GAAI5lB,EAAG4lB,EAAG,IAGnB,CAAE/lB,EAAG,EAAGG,EAAG,EACnB,CAEU,cAAAooB,CAAegC,GACxBhwB,KAAKkqB,eAAeuE,YAAc,CAAC,CACpC,CAEU,YAAAwB,GACT,MAAM/F,EAAiBlqB,KAAKkqB,eAC5B,IAAInnB,EAAI,EACJyrB,EAA4B,GAC5B0B,EAA4B,GAWhC,GATA,OAAahG,EAAeuE,aAAa,CAACzgB,EAAGrF,KAC5C6lB,EAAWzrB,GAAK4F,EAChB,IAAIwmB,EAAYnvB,KAAKovB,YAAYphB,GAC7BmhB,IACHe,EAAWntB,GAAKosB,GAEjBpsB,GAAG,IAGAyrB,EAAW9sB,OAAS,GAAKwuB,EAAWxuB,OAAS,EAAG,CACnD,MAAMsJ,EAAUkf,EAAeve,SAE/B,IAAIwkB,EAAa3B,EAAW,GACxB4B,EAAa5B,EAAW,GAExB6B,EAAaH,EAAW,GACxBI,EAAaJ,EAAW,GAE5B,GAAIC,GAAcC,GAAcC,GAAcC,EAAY,CAEzDH,EAAanlB,EAAQwf,QAAQ2F,GAC7BC,EAAaplB,EAAQwf,QAAQ4F,GAE7BC,EAAarlB,EAAQwf,QAAQ6F,GAC7BC,EAAatlB,EAAQwf,QAAQ8F,GAE7B,IAAIC,EAAkBxqB,KAAKkB,MAAMmpB,EAAW3qB,EAAI0qB,EAAW1qB,EAAG2qB,EAAWxqB,EAAIuqB,EAAWvqB,GAGpF4qB,EAFkBzqB,KAAKkB,MAAMqpB,EAAW7qB,EAAI4qB,EAAW5qB,EAAG6qB,EAAW1qB,EAAIyqB,EAAWzqB,GAE1D2qB,EAAkBvwB,KAAKuuB,eACrDiC,EAAQ,cAAiBA,EAAOxwB,KAAKY,IAAI,eAAgB,GAAIZ,KAAKY,IAAI,eAAgB,KAEtF,IAAI6vB,EAAa,CAAEhrB,EAAG4qB,EAAW5qB,GAAK6qB,EAAW7qB,EAAI4qB,EAAW5qB,GAAK,EAAGG,EAAGyqB,EAAWzqB,GAAK0qB,EAAW1qB,EAAIyqB,EAAWzqB,GAAK,GACtH8qB,EAAa,CAAEjrB,EAAG0qB,EAAW1qB,GAAK2qB,EAAW3qB,EAAI0qB,EAAW1qB,GAAK,EAAGG,EAAGuqB,EAAWvqB,GAAKwqB,EAAWxqB,EAAIuqB,EAAWvqB,GAAK,GAEtH+qB,EAAK3wB,KAAK4uB,iBAAmB,EAC7BgC,EAAK5wB,KAAK6uB,iBAAmB,EAE7BnN,EAAY1hB,KAAKuuB,eAEjB9C,EAAKgF,EAAWhrB,IAAOkrB,EAAKD,EAAWjrB,GAAKic,EAAY8O,EACxD9E,EAAK+E,EAAW7qB,IAAOgrB,EAAKF,EAAW9qB,GAAK8b,EAAY8O,EAE5DxwB,KAAKyD,IAAI,YAAa+sB,GACtBxwB,KAAKyD,IAAI,aAAcgoB,GACvBzrB,KAAKyD,IAAI,aAAcioB,E,EAG1B,CAEU,gBAAAuC,CAAiB7D,GAC1B,MAAMF,EAAiBlqB,KAAKkqB,eAC5B,IAAIyE,EAAYzE,EAAe2G,gBAC/B,MAAMC,EAAc5G,EAAegF,kBAG7B6B,EAFgB3G,EAAME,cAEIyG,UAEhC,GAAI/wB,KAAKY,IAAI,cACRmwB,IACH/wB,KAAKovB,YAAY2B,GAAa3G,EAAMzhB,MAEhC,OAAauhB,EAAeuE,aAAa/sB,OAAS,GACrD1B,KAAKiwB,oBAMR,KAAIa,IAAeC,GAAaA,GAAaD,IAIxCnC,EAAW,CACd,MAAMrN,EAAOthB,KAAKY,IAAI,QAChB2gB,EAAOvhB,KAAKY,IAAI,QACtB,GAAY,QAAR0gB,GAA0B,QAARC,EAAgB,CACrC,MAAMvW,EAAUkf,EAAeve,SAC/B,IAAIqlB,EAAQhmB,EAAQwf,QAAQJ,EAAMzhB,OAClCgmB,EAAY3jB,EAAQwf,QAAQmE,GAE5B,IAAIlpB,EAAIzF,KAAK4uB,gBACThpB,EAAI5F,KAAK6uB,gBAEb,GAAI9oB,KAAKkB,MAAM0nB,EAAUlpB,EAAIurB,EAAMvrB,EAAGkpB,EAAU/oB,EAAIorB,EAAMprB,GAAK,EAAG,CACjE,IAAIypB,EAAKnF,EAAetpB,IAAI,cAK5B,GAJIyuB,GACHA,EAAGzrB,OAAOqtB,YAAY,SAGnB,WAAexrB,IAAM,WAAeG,GAAI,CAC3C,IAAItB,EAAatE,KAAKY,IAAI,cAC1B,MAAM8gB,EAAY1hB,KAAKY,IAAI,YAAa,GAElC0hB,EAAYtiB,KAAKY,IAAI,YAAa,IAClCmY,EAAS/Y,KAAKksB,WAEd5S,EAAItZ,KAAK4Z,QACTL,EAAIvZ,KAAK8Z,SAEToX,EAAKnY,EAAO,GAAG,GAAKA,EAAO,GAAG,GAC9BuS,EAAKvS,EAAO,GAAG,GAAKA,EAAO,GAAG,GAEpC,GAAY,cAARuI,EAAsB,CACzB7b,GAAKurB,EAAMvrB,EAAIkpB,EAAUlpB,EAEzB,MAAMihB,EAAKpN,EAAI,GAAKA,EAAI,EAAItZ,KAAKgsB,UAAYtK,EAC7Cjc,EAAIM,KAAKyT,IAAI/T,EAAGihB,EAAKwK,EAAK5O,EAAYZ,GACtCjc,EAAIM,KAAKgK,IAAItK,EAAGihB,EAAKwK,EAAK5O,EAAYZ,E,CAGvC,GAAY,cAARH,EAAsB,CACzB3b,GAAKorB,EAAMprB,EAAI+oB,EAAU/oB,EACzB,MAAM+gB,EAAKpN,EAAI,GAAKA,EAAI,EAAIvZ,KAAKisB,UAAYvK,EAC7C9b,EAAIG,KAAKyT,IAAI5T,EAAG+gB,EAAK2E,EAAKhJ,EAAYZ,GACtC9b,EAAIG,KAAKgK,IAAInK,EAAG+gB,EAAK2E,EAAKhJ,EAAYZ,E,CAMvC,GAHA1hB,KAAKyD,IAAI,aAAcgC,GACvBzF,KAAKyD,IAAI,aAAcmC,GAEnBtB,EAAW4I,OAAQ,CACtB,IAAIikB,EAAe7sB,EAAW4I,OAAO,CAACyhB,EAAUlpB,EAAGkpB,EAAU/oB,IACzDmH,UAAYokB,IACH,WAAR7P,GACHthB,KAAKyD,IAAI,YAAazD,KAAK8uB,gBAAmBH,EAAUlpB,EAAIurB,EAAMvrB,GAAKzF,KAAK8vB,OAEjE,WAARvO,GACHvhB,KAAKyD,IAAI,YAAazD,KAAK+uB,gBAAmBJ,EAAU/oB,EAAIorB,EAAMprB,GAAK5F,KAAK+vB,O,KASrF,CAEU,mBAAApF,CAAoBxgB,EAAeijB,EAAkBgE,GAC9DpxB,KAAK2vB,KAAO3vB,KAAKqxB,QAAQ,CAAE1Y,IAAK,YAAaf,GAAI5X,KAAKY,IAAI,YAAa,GAAKuJ,EAAOijB,SAAUA,EAAUgE,OAAQA,GAChH,CAEU,mBAAAxG,CAAoBzgB,EAAeijB,EAAkBgE,GAC9DpxB,KAAK0vB,KAAO1vB,KAAKqxB,QAAQ,CAAE1Y,IAAK,YAAaf,GAAI5X,KAAKY,IAAI,YAAa,GAAKuJ,EAAOijB,SAAUA,EAAUgE,OAAQA,GAChH,CAEU,gBAAA3G,CAAiBtgB,EAAexB,GACzC,IAAIkH,EAAO7P,KAAKY,IAAI,WAAY,GAC5B8gB,EAAY1hB,KAAKY,IAAI,YAAa,GAClC0wB,EAAe5P,EACfvX,EAAQ,EACXmnB,EAAe5P,EAAY7R,EAEnB1F,EAAQ,IAChBmnB,EAAe5P,EAAY7R,GAGxByhB,GAAgB5P,GACnB1hB,KAAKuxB,YAAY5oB,EAAO2oB,EAE1B,CAUO,eAAAE,CAAgBzf,EAAyEqb,EAAmBzL,EAAoBC,GAClI7P,EAAUG,MAAQH,EAAUC,OAC/BD,EAAUG,MAAQ,IAClBH,EAAUC,MAAQ,KAGnB,MACMyf,EADUzxB,KAAKwE,WAAW,WACNuU,OAAO/Y,KAAKkrB,oBAEtC,IAAI9f,EAAKpL,KAAKqL,QAAQ,CAAEC,UAAWyG,EAAUC,KAAMzG,SAAUwG,EAAUI,KAAOwP,EAAWC,GACrF3U,EAAKjN,KAAKqL,QAAQ,CAAEC,UAAWyG,EAAUG,MAAO3G,SAAUwG,EAAUE,QAAU0P,EAAWC,GAEzFxW,EAAGxF,EAAI6rB,EAAU,GAAG,KACvBrmB,EAAGxF,EAAI6rB,EAAU,GAAG,IAGjBxkB,EAAGrH,EAAI6rB,EAAU,GAAG,KACvBxkB,EAAGrH,EAAI6rB,EAAU,GAAG,IAGrB,IAAIC,EAAK1xB,KAAKY,IAAI,YAAa,GAE3BmY,EAAiB3N,EAAG3F,EAApBsT,EAA8B9L,EAAGxH,EAAjCsT,EAAyC3N,EAAGxF,EAA5CmT,EAAuD9L,EAAGrH,EAE1DgkB,EAAkB5pB,KAAK4pB,gBAEvBlI,EAAY,GAAK3b,KAAKyT,IAAIoQ,EAAgBmB,cAAgBhS,EAAeA,GAAe2Y,EAAI9H,EAAgBoB,eAAiBjS,EAAgBA,GAAc2Y,GAC3JjsB,EAAIsT,GAAeA,EAAeA,GAAe,EACjDnT,EAAImT,GAAcA,EAAgBA,GAAc,EAEhD3M,EAAWpM,KAAKkN,OAAO,CAAEzH,IAAGG,MAMhC,OAJiB,MAAb+b,GAAkC,MAAbC,GACxB5hB,KAAK6c,OAAO8E,EAAWC,GAGjB5hB,KAAKqtB,eAAejhB,EAAUsV,GAAW,EAAM0L,EACvD,CAUO,WAAAmE,CAAY5oB,EAAe6nB,EAAehT,EAAkB4P,GAC9DoD,IACHA,EAAQ,cAAiBA,EAAOxwB,KAAKY,IAAI,eAAgB,GAAIZ,KAAKY,IAAI,eAAgB,MAGlF,WAAewsB,KACnBA,EAAWptB,KAAKY,IAAI,oBAAqB,IAE1C,MAAMwwB,EAASpxB,KAAKY,IAAI,mBAClB8gB,EAAY1hB,KAAKY,IAAI,YAAa,GAEpCZ,KAAKY,IAAI,uBAAyB4vB,GAASxwB,KAAKY,IAAI,gBAAiB,KACxE+H,EAAQ3I,KAAKqL,QAAQrL,KAAKktB,eAAgBltB,KAAKY,IAAI,iBAAkBZ,KAAKY,IAAI,kBAC9E4c,GAAS,GAGV,IAAI/X,EAAIkD,EAAMlD,EACVG,EAAI+C,EAAM/C,EAEV+qB,EAAK3wB,KAAKY,IAAI,aAAc,GAC5BgwB,EAAK5wB,KAAKY,IAAI,aAAc,GAE5B8lB,EAAKjhB,EACLkhB,EAAK/gB,EAEL4X,IACHkJ,EAAK1mB,KAAK4Z,QAAU,EACpB+M,EAAK3mB,KAAK8Z,SAAW,GAGtB,IAAI2R,EAAK/E,GAAOjhB,EAAIkrB,GAAMjP,EAAY8O,EAClC9E,EAAK/E,GAAO/gB,EAAIgrB,GAAMlP,EAAY8O,EAWtC,OARAxwB,KAAKwvB,KAAOxvB,KAAKqxB,QAAQ,CAAE1Y,IAAK,aAAcf,GAAI6T,EAAI2B,SAAUA,EAAUgE,OAAQA,IAClFpxB,KAAKyvB,KAAOzvB,KAAKqxB,QAAQ,CAAE1Y,IAAK,aAAcf,GAAI8T,EAAI0B,SAAUA,EAAUgE,OAAQA,IAClFpxB,KAAKuvB,IAAMvvB,KAAKqxB,QAAQ,CAAE1Y,IAAK,YAAaf,GAAI4Y,EAAOpD,SAAUA,EAAUgE,OAAQA,IAE/E1P,GAAa8O,GAChBxwB,KAAK2O,MAAMgjB,YAAY3xB,KAAK4xB,GAAG,2BAA4B5xB,KAAK2O,MAAMkjB,OAAQ,iBAAqBrB,KAG7FxwB,KAAKuvB,GACb,CAaO,cAAAlC,CAAejhB,EAAqBokB,EAAehT,EAAkB4P,EAAmBzL,EAAoBC,GAElH,IAAI4J,EAAKxrB,KAAKqL,QAAQe,EAAUuV,EAAWC,GAM3C,GAJiB,MAAbD,GAAkC,MAAbC,GACxB5hB,KAAK6c,OAAO8E,EAAWC,EAAWwL,GAG/B5B,EACH,OAAOxrB,KAAKuxB,YAAY/F,EAAIgF,EAAOhT,EAAQ4P,EAE7C,CAEO,MAAAvQ,CAAO8E,EAAoBC,EAAoBwL,GAErD,GADmBptB,KAAKY,IAAI,cACZic,OAEX,CACC,WAAeuQ,KACnBA,EAAWptB,KAAKY,IAAI,oBAAqB,IAG1C,MAAMwwB,EAASpxB,KAAKY,IAAI,mBACP,MAAb+gB,GACH3hB,KAAKqxB,QAAQ,CAAE1Y,IAAK,YAAaf,GAAI+J,EAAWyL,SAAUA,EAAUgE,OAAQA,IAE5D,MAAbxP,GACH5hB,KAAKqxB,QAAQ,CAAE1Y,IAAK,YAAaf,GAAIgK,EAAWwL,SAAUA,EAAUgE,OAAQA,G,CAG/E,CAKO,MAAAU,GACN,OAAO9xB,KAAKuxB,YAAY,CAAE9rB,EAAGzF,KAAK4Z,QAAU,EAAGhU,EAAG5F,KAAK8Z,SAAW,GAAK9Z,KAAKY,IAAI,YAAa,GAAKZ,KAAKY,IAAI,WAAY,GACxH,CAKO,OAAAmxB,GACN,OAAO/xB,KAAKuxB,YAAY,CAAE9rB,EAAGzF,KAAK4Z,QAAU,EAAGhU,EAAG5F,KAAK8Z,SAAW,GAAK9Z,KAAKY,IAAI,YAAa,GAAKZ,KAAKY,IAAI,WAAY,GACxH,CAEO,WAAAmL,GACNrL,MAAMqL,cACN/L,KAAK2rB,kBAAmB,EACxB3rB,KAAKitB,YAAa,CACnB,CAKO,OAAA+E,CAAQzwB,GACd,MAAMgD,EAAUvE,KAAKwE,WAAW,WAC1B9B,EAAWnB,EAASX,IAAI,YAC9B,OAAI8B,EACI6B,EAAQmf,KAAKhhB,GAEd,CACR,EAx9BA,sC,gDAAkC,aAClC,uC,gDAA0C2mB,GAAA,EAAYrkB,WAAWC,OAAO,CAACmkB,GAASlkB,cEvG5E,MAAM+sB,WAAuBpyB,EAApC,c,oBASC,qC,gDAAwD,CAAC,QAAS,gBAElE,6C,wDAmZD,CAjZW,SAAAE,GACTC,KAAKC,OAAOC,KAAK,YAAa,SAAU,YAAa,WAAY,SACjEQ,MAAMX,WACP,CAKO,mBAAA8L,GACN7L,KAAK8L,WACN,CAOO,eAAAuD,CAAgB9N,GACtBb,MAAM2O,kBAEF9N,GACHvB,KAAKkyB,iBAAiB3wB,EAExB,CAEU,eAAAmC,CAAgBnC,GACzBb,MAAMgD,gBAAgBnC,GACtB,IAAImB,EAAWnB,EAASX,IAAI,YAC5B,GAAK8B,GAKJ,GAAqB,SAAjBA,EAASD,KAAiB,CAC7B,MAAMiG,EAAchG,EAASgG,YACzBA,IACHnH,EAASkC,IAAI,YAAaiF,EAAY,IACtCnH,EAASkC,IAAI,WAAYiF,EAAY,I,MAGlC,GAAqB,cAAjBhG,EAASD,KAAsB,CACvC,MAAMiG,EAAchG,EAASgG,YACzBA,GAAeA,EAAY,KAC9BnH,EAASkC,IAAI,YAAaiF,EAAY,GAAG,IACzCnH,EAASkC,IAAI,WAAYiF,EAAY,GAAG,I,OAf1ChG,EAAW,CAAED,KAAM,QAASiG,YAAa,CAACnH,EAASX,IAAI,YAAa,GAAIW,EAASX,IAAI,WAAY,KACjGW,EAASkC,IAAI,WAAYf,GAmB1B1C,KAAKoC,aAAaM,EAAU1C,KAC7B,CAEU,YAAAmyB,CAAa5wB,GACtBA,EAAS+qB,QAAU,GAEnBtsB,KAAKssB,QAAQV,MAAMwG,IAClB,MAAM1vB,EAAWnB,EAASX,IAAI,YAE9B,GAAI8B,EACH,GAAqB,SAAjBA,EAASD,KACZzC,KAAKqyB,iBAAiBryB,KAAKsyB,YAAY/wB,EAAU6wB,SAE7C,GAAI1vB,EAASD,KAAO,aAAc,CACtC,IAAIM,EAAI,EACR,OAAYL,EAASgG,aAAa,KACjC1I,KAAKqyB,iBAAiBryB,KAAKsyB,YAAY/wB,EAAU6wB,EAAgBrvB,IACjEA,GAAG,G,IAKR,CAEU,gBAAAsvB,CAAiB9F,GAC1B,GAAIA,EAAQ,CACX,MAAMC,EAASD,EAAO3rB,IAAI,UACpBO,EAAQnB,KAAKmB,MACnB,GAAIqrB,GAAUrrB,EAAO,CACpB,MAAMI,EAAWirB,EAAOjrB,SACpBA,IACCA,EAASX,IAAI,SACZ4rB,EAAO+F,QAAUpxB,EAAMiD,kBAC1BjD,EAAMiD,iBAAiBF,SAASC,UAAUqoB,GAIvCA,EAAO+F,QAAUvyB,KAAKoE,kBACzBpE,KAAKoE,iBAAiBF,SAASC,UAAUqoB,G,EAM/C,CAEO,eAAAgG,CAAgBjG,GACtB,MAAMC,EAASD,EAAO3rB,IAAI,UAC1B,GAAI4rB,EAAQ,CACX,MAAMjrB,EAAWirB,EAAOjrB,SACxB,GAAIA,GAAYA,EAASX,IAAI,SAC5B,OAGD,MAAM2K,EAAWhK,EAASX,IAAI,YACxB0K,EAAY/J,EAASX,IAAI,aACzB6xB,EAAelxB,EAASX,IAAI,gBAC5B8xB,EAAQnxB,EAASX,IAAI,SACrBO,EAAQnB,KAAKmB,MACnB,IAAIgS,EACJ,GAAIsf,EACHtf,EAAOsf,EAAa7xB,IAAI,eAEpB,CACJ,MAAM+xB,EAASpxB,EAASX,IAAI,UAExB+xB,GAAUxxB,GACbA,EAAMsD,OAAOmnB,MAAMnnB,IAClB,GAAIA,EAAO4nB,OAAsB,iBAAkB,CAClD,IAAIuG,EAASnuB,EAAOjD,gBAAgBmxB,GAChCC,IACHrxB,EAASkC,IAAI,eAAgBmvB,GAC7Bzf,EAAOyf,EAAOhyB,IAAI,W,KAOnBZ,KAAK6yB,gBACR7yB,KAAK6yB,eAAepjB,UAGjB0D,IACHnT,KAAK6yB,eAAiB1f,EAAKvP,OAAOxD,GAAG,eAAe,KACnDJ,KAAKkyB,iBAAiB3wB,EAAS,KAIjC,MAAMuxB,EAAkBvxB,EAASX,IAAI,mBACrC,IAAI8U,EACJ,GAAIod,EACHpd,EAAUod,EAAgBlyB,IAAI,kBAE1B,CACJ,MAAMmyB,EAAYxxB,EAASX,IAAI,aAE3BmyB,GAAa5xB,GAChBA,EAAMsD,OAAOmnB,MAAMnnB,IAClB,GAAIA,EAAO4nB,OAAyB,oBAAqB,CACxD,IAAI2G,EAAYvuB,EAAOjD,gBAAgBuxB,GACnCC,IACHzxB,EAASkC,IAAI,kBAAmBuvB,GAChCtd,EAAUsd,EAAUpyB,IAAI,c,KAO7B,MAAMqyB,EAAiB1xB,EAASX,IAAI,kBACpC,IAAI8H,EAEAyN,EAEJ,GAAIT,EAAS,CACZ,IAAItJ,EAAWsJ,EAAQwd,iBACvBxqB,EAAc,CAAC0D,EAASd,UAAWc,EAASb,UAC5ChK,EAASgO,OAAO,YAAanD,EAASd,WACtC/J,EAASgO,OAAO,WAAYnD,EAASb,S,MAEjC,GAAI4H,GAAQ,WAAe8f,GAAiB,CAChD,IAAI7mB,EAAW+G,EAAK9G,mBAAmB4mB,GAGvC,GAFAvqB,EAAc,CAAC0D,EAASd,UAAWc,EAASb,UAExChK,EAASX,IAAI,aAAc2rB,EAAO3rB,IAAI,gBAAkBO,EAAO,CAClE,MAAMgyB,EAAYhgB,EAAK9G,mBAAmB4mB,EAAiB,MACrDG,EAAYjgB,EAAK9G,mBAAmB4mB,EAAiB,MAErD3c,EAASnV,EAAMkK,QAAQ8nB,GACvBxc,EAASxV,EAAMkK,QAAQ+nB,GAG7Bjd,EAAQ,YAAeG,EAAQK,E,CAGhCpV,EAASgO,OAAO,YAAanD,EAASd,WACtC/J,EAASgO,OAAO,WAAYnD,EAASb,S,MAEjC,GAAI,WAAeD,IAAc,WAAeC,GACpD7C,EAAc,CAAC4C,EAAWC,OAEtB,CACJ,MAAM7I,EAAWnB,EAASX,IAAI,YAC9B,GAAI8B,EACH,GAAqB,SAAjBA,EAASD,KACZzC,KAAKqzB,oBAAoB9G,EAAQ7pB,EAAUA,EAASgG,YAAiCyN,QAEjF,GAAqB,cAAjBzT,EAASD,KAAsB,CACvC,IAAI6wB,EAAQ/G,EAAOgH,QAAU,EAC7B7qB,EAAchG,EAASgG,YAAY4qB,E,GAKjCZ,GAAShqB,GACb1I,KAAKqzB,oBAAoB9G,EAAQ,CAAE9pB,KAAM,QAASiG,YAAaA,GAAeA,EAAayN,E,CAG9F,CAEU,mBAAAkd,CAAoB9G,EAAgB7pB,EAA4BgG,EAA+ByN,GACxG,MAAMqW,EAASD,EAAO3rB,IAAI,UACpBO,EAAQnB,KAAKmB,MACnB,GAAIA,EAAO,CACV,MAAMmD,EAAanD,EAAMP,IAAI,cACvB2D,EAAUpD,EAAMqD,WAAW,WAC3BjD,EAA8CirB,EAAOjrB,SAErDiqB,EAAKlnB,EAAWoE,GAEtB,GAAI8iB,EAAI,CACP,MAAM7iB,EAAQ,CAAElD,EAAG+lB,EAAG,GAAI5lB,EAAG4lB,EAAG,IAChCgB,EAAOnL,OAAO1Y,GACdpH,EAASgO,OAAO,QAAS5G,E,CAG1B,IAAIsV,GAAU,EACV1Z,EAAQ7B,GACP1C,KAAKY,IAAI,eACZqd,GAAU,GAIPje,KAAKY,IAAI,cACZqd,GAAU,GAGZuO,EAAOrd,WAAW,UAAW8O,GAC7B1c,EAASkC,IAAI,WAAYwa,GAErB1c,GAAqB,MAAT4U,GAAiB5U,EAASX,IAAI,aAAc2rB,EAAO3rB,IAAI,gBACtE4rB,EAAO/oB,IAAI,WAAY0S,EAAQ5U,EAASX,IAAI,kBAAmB2rB,EAAO3rB,IAAI,kBAAmB,I,CAGhG,CAUO,cAAA4yB,CAAejyB,EAA6CmgB,EAAmB7E,GACrF,MAAM1b,EAAQnB,KAAKmB,MACnB,GAAIA,EAAO,CACV,MAAMmK,EAAY/J,EAASX,IAAI,YAAa,GACtC2K,EAAWhK,EAASX,IAAI,WAAY,GAC1C,OAAIic,EACI1b,EAAMksB,eAAe,CAAE/hB,UAAWA,EAAWC,SAAUA,GAAYmW,GAAW,OAAM7gB,GAAYyK,GAAYC,GAE7GpK,EAAMksB,eAAe,CAAE/hB,UAAWA,EAAWC,SAAUA,GAAYmW,GAAW,E,CAEvF,CAWO,eAAA+R,CAAgB3xB,EAAqD+a,GAE3E,IAAI7K,EAAsB,KACtBE,EAAuB,KACvBC,EAAqB,KACrBF,EAAwB,KAmB5B,GAjBA,OAAYnQ,GAAYP,IACvB,MAAM+J,EAAY/J,EAASX,IAAI,YAAa,GACtC2K,EAAWhK,EAASX,IAAI,WAAY,IAE9B,MAARoR,GAAgBA,EAAO1G,KAC1B0G,EAAO1G,IAEK,MAAT4G,GAAiBA,EAAQ5G,KAC5B4G,EAAQ5G,IAEE,MAAP6G,GAAeA,EAAM5G,KACxB4G,EAAM5G,IAEO,MAAV0G,GAAkBA,EAAS1G,KAC9B0G,EAAS1G,E,IAGC,MAARyG,GAAyB,MAATE,GAAwB,MAAPC,GAAyB,MAAVF,EAAgB,CACnE,MAAM9Q,EAAQnB,KAAKmB,MACnB,GAAIA,EACH,OAAI0b,EACI1b,EAAMqwB,gBAAgB,CAAExf,OAAME,QAAOC,MAAKF,eAAUpR,IAAamR,GAAQE,EAAQF,GAAQ,KAAMG,GAAOA,EAAMF,GAAU,IAEvH9Q,EAAMqwB,gBAAgB,CAAExf,OAAME,QAAOC,MAAKF,U,CAGpD,CAMO,eAAAzC,CAAgBjO,GACtB,MAAMJ,EAAQnB,KAAKmB,MACfA,GACHA,EAAMsD,OAAOmnB,MAAMnnB,IACdA,EAAO4nB,OAAsB,kBAChC,OAAY5nB,EAAO3C,WAAY4xB,IAC9B,MAAMtkB,EAAkBskB,EAAG9yB,IAAI,mBAC3BwO,GACH,OAAYA,GAAkBzG,IACzBA,GAASpH,IACZ,SAAc6N,EAAiBzG,GAC/BlE,EAAO4K,gBAAgBqkB,G,UAS9BhzB,MAAM8O,gBAAgBjO,EACvB,CAKU,gBAAAE,CAAiBF,GAC1Bb,MAAMe,iBAAiBF,GACvB,MAAM+qB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAOrd,WAAW,WAAW,E,GAIjC,CAMU,kBAAAvN,CAAmBL,GAC5Bb,MAAMkB,mBAAmBL,GACzB,MAAM+qB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAOrd,WAAW,WAAW,E,GAIjC,CAKU,mBAAAnN,CAAoBT,GAC7Bb,MAAMsB,oBAAoBT,GAC1B,MAAM+qB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAOrd,WAAW,WAAW,E,GAIjC,CAKU,qBAAAlN,CAAsBV,GAC/Bb,MAAMuB,sBAAsBV,GAC5B,MAAM+qB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAOrd,WAAW,WAAW,E,GAIjC,EA3ZA,sC,gDAAkC,mBAClC,uC,gDAA0CtP,EAAUmF,WAAWC,OAAO,CAACgtB,GAAe/sB,c,0BCpIhF,MAAMyuB,WAAmBlpB,EAAA,EAAhC,c,oBAOC,+C,iDAAsC,IAUtC,qC,wDAkHD,CA1HW,SAAA1K,GACTW,MAAMX,YACNC,KAAKmP,WAAW,eAAe,EAChC,CAOO,cAAAzE,GAGN,GAFAhK,MAAMgK,iBAEF1K,KAAK2K,kBAAoB3K,KAAKkB,QAAQ,aAAelB,KAAKkB,QAAQ,aAAc,CACnF,MAAMwB,EAAW1C,KAAKY,IAAI,YAE1B,GAAI8B,EAAU,CACb,MAAM+B,EAASzE,KAAKyE,OACpB,GAAIA,EAAQ,CACX,MAAMH,EAAaG,EAAOH,aACtBA,GACHA,EAAWuG,UAAU7K,KAAKY,IAAI,YAAa,KAG5C,MAAM2D,EAAUE,EAAOF,UAEnBA,IACHvE,KAAK8K,QAAS,EAEd9K,KAAKyD,IAAI,QAASkI,IACjBpH,EAAQqH,QAAQ5L,KAAK2L,UACrBpH,EAAQ7B,GACR6B,EAAQqH,QAAQ,KAAK,IAGlB5L,KAAK4zB,WACR5zB,KAAK6zB,c,GAMX,CAKO,mBAAAhoB,GACN7L,KAAK8L,YACL9L,KAAK2K,kBAAmB,CACzB,CAEO,WAAAoB,GACNrL,MAAMqL,cACN/L,KAAK2K,kBAAmB,CACzB,CAOO,WAAAmf,GACN,MAAMpnB,EAAW1C,KAAKY,IAAI,YAC1B,OAAI8B,EACI,GAAyBA,GAGzB,CAAE6I,SAAU,EAAGD,UAAW,EAEnC,CAOO,cAAA4nB,GAEN,IAAIY,EAAc,EACdprB,EAA4B,GAChC,MAAMhG,EAAW1C,KAAKY,IAAI,YAE1B,GAAI8B,EAAU,CACb,GAAqB,WAAjBA,EAASD,KACZiG,EAAchG,EAASgG,iBAEnB,GAAqB,gBAAjBhG,EAASD,KACjB,IAAK,IAAIM,EAAI,EAAGA,EAAIL,EAASgG,YAAYhH,OAAQqB,IAAK,CACrD,IAAIuM,EAAS5M,EAASgG,YAAY3F,GAC9B2gB,EAAO,GAAQ,CAAEjhB,KAAM,UAAWiG,YAAa4G,IAC/CoU,EAAOoQ,IACVprB,EAAc4G,EACdwkB,EAAcpQ,E,CAIjB,GAAIhb,EAAa,CAChB,IAAI8U,EAAS,KAAW9U,GACxB,MAAO,CAAE4C,UAAWkS,EAAO,GAAIjS,SAAUiS,EAAO,G,EAGlD,MAAO,CAAElS,UAAW,EAAGC,SAAU,EAClC,CAGO,gBAAAS,GACN,MAAMvH,EAASzE,KAAKyE,OAEpB,GAAIA,EAAQ,CACX,MAAMH,EAAaG,EAAOH,aAC1B,GAAIA,EAAY,CACf,MAAM8H,EAAWpM,KAAKkzB,iBAChB1H,EAAKlnB,EAAW,CAAC8H,EAASd,UAAWc,EAASb,WAEpD,GAAIigB,EACH,MAAO,CAAE/lB,EAAG+lB,EAAG,GAAI5lB,EAAG4lB,EAAG,G,EAI5B,MAAO,CAAE/lB,EAAG,EAAGG,EAAG,EACnB,EA7HA,sC,gDAAkC,eAClC,uC,gDAA0C6E,EAAA,EAASzF,WAAWC,OAAO,CAAC0uB,GAAWzuB,cCiB3E,MAAM6uB,WAAyBl0B,EAAtC,c,oBAmBC,0C,gDAAwD,IAAI0O,EAAA,EAC3DC,GAAA,GAASC,IAAI,CAAC,IACd,IAAMklB,GAAWjlB,KAAK1O,KAAK2O,MAAO,CAAC,EAAG,CAAC3O,KAAKg0B,YAAYnlB,eAUzD,qC,gDAAwD,CAAC,UAAW,iBAiPrE,CA3QQ,cAAAolB,CAAe1yB,GACrB,MAAM2yB,EAAal0B,KAAKkE,SAAShE,KAAKF,KAAKg0B,YAAYhlB,QAGvD,OAFAklB,EAAWjlB,aAAa1N,GACxBvB,KAAKg0B,YAAY9zB,KAAKg0B,GACfA,CACR,CA0BO,mBAAAroB,GACN,OAAY7L,KAAK8B,WAAYP,IAC5B,IAAI2yB,EAAa3yB,EAASX,IAAI,cAC1BszB,GACHA,EAAWroB,qB,GAGd,CAEO,gBAAA7K,GACNN,MAAMM,mBAEFhB,KAAKkB,QAAQ,SAChBlB,KAAKg0B,YAAYnlB,SAASpL,IAAI,OAAQzD,KAAKY,IAAI,SAE5CZ,KAAKkB,QAAQ,WAChBlB,KAAKg0B,YAAYnlB,SAASpL,IAAI,SAAUzD,KAAKY,IAAI,UAEnD,CAEU,eAAA8C,CAAgBnC,GACzBb,MAAMgD,gBAAgBnC,GAEtB,IAAI2yB,EAAa3yB,EAASX,IAAI,cACzBszB,IACJA,EAAal0B,KAAKi0B,eAAe1yB,IAGlCA,EAASkC,IAAI,aAAcywB,GAC3B,IAAIxxB,EAAWnB,EAASX,IAAI,YAE5B,GAAI8B,EAAU,CACb,GAAI1C,KAAKY,IAAI,mBACQ8B,EAASgG,YAE5B,IAAK,IAAIjD,EAAI,EAAGA,EAAI/C,EAASgG,YAAYhH,OAAQ+D,IAChD,GAAqB,gBAAjB/C,EAASD,KACZ,IAAK,IAAImD,EAAI,EAAGA,EAAIlD,EAASgG,YAAYjD,GAAG/D,OAAQkE,IACnDlD,EAASgG,YAAYjD,GAAGG,GAAG4L,eAI5B9O,EAASgG,YAAYjD,GAAG+L,UAK5B0iB,EAAWzwB,IAAI,WAAYf,E,CAG5BwxB,EAAWzvB,OAASzE,KAEpBA,KAAKoC,aAAab,EAASX,IAAI,YAAaZ,KAC7C,CAKO,eAAAwP,CAAgBjO,GACtBb,MAAM8O,gBAAgBjO,GACtB,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,IACHl0B,KAAKg0B,YAAYnvB,YAAYqvB,GAC7BA,EAAWzkB,WAEZzP,KAAKmC,gBAAgBZ,EAASX,IAAI,YACnC,CAKU,gBAAAa,CAAiBF,GAC1Bb,MAAMe,iBAAiBF,GACvB,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,GACHA,EAAW/kB,WAAW,WAAW,EAEnC,CAKU,kBAAAvN,CAAmBL,GAC5Bb,MAAMkB,mBAAmBL,GACzB,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,GACHA,EAAW/kB,WAAW,WAAW,EAEnC,CAKU,mBAAAnN,CAAoBT,GAC7Bb,MAAMsB,oBAAoBT,GAC1B,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,GACHA,EAAW/kB,WAAW,WAAW,EAEnC,CAKU,qBAAAlN,CAAsBV,GAC/Bb,MAAMuB,sBAAsBV,GAC5B,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,GACHA,EAAW/kB,WAAW,WAAW,EAEnC,CAOO,eAAAE,CAAgB9N,GAEtB,GADAb,MAAM2O,kBACF9N,EAAU,CACb,MAAM2yB,EAAa3yB,EAASX,IAAI,cAC5BszB,GACHA,EAAWzwB,IAAI,WAAYlC,EAASX,IAAI,Y,CAG3C,CASO,cAAA4yB,CAAejyB,EAA+Csb,GACpE,MAAMnH,EAAUnU,EAASX,IAAI,cAC7B,GAAI8U,EAAS,CACZ,MAAMhT,EAAWgT,EAAQ9U,IAAI,YACvBO,EAAQnB,KAAKmB,MAEnB,GAAIuB,GAAYvB,EAAO,CAEtB,GAAI0b,EAAQ,CACX,MAAM2H,EAAW,GAAyB9hB,GAE1C,OADAvB,EAAM0b,QAAQ2H,EAASlZ,WAAYkZ,EAASjZ,UACrCpK,EAAMqwB,gBAAgB,GAAuB9uB,QAAW7B,GAAY2jB,EAASlZ,WAAYkZ,EAASjZ,S,CAG1G,OAAOpK,EAAMqwB,gBAAgB,GAAuB9uB,G,EAGvD,CAUO,eAAA+wB,CAAgB3xB,EAAuD+a,GAC7E,IAAI7K,EACAE,EACAC,EACAF,EA+BJ,GA7BA,OAAYnQ,GAAYP,IAEvB,MAAMmU,EAAUnU,EAASX,IAAI,cAC7B,GAAI8U,EAAS,CACZ,MAAMhT,EAAWgT,EAAQ9U,IAAI,YAC7B,GAAI8B,EAAU,CACb,IAAIqW,EAAS,GAAuBrW,GAExB,MAARsP,IACHA,EAAO+G,EAAO/G,MAEF,MAATE,IACHA,EAAQ6G,EAAO7G,OAEL,MAAPC,IACHA,EAAM4G,EAAO5G,KAEA,MAAVF,IACHA,EAAS8G,EAAO9G,QAGjBD,EAAOjM,KAAKyT,IAAIT,EAAO/G,KAAMA,GAC7BE,EAAQnM,KAAKgK,IAAIgJ,EAAO7G,MAAOA,GAC/BC,EAAMpM,KAAKgK,IAAIgJ,EAAO5G,IAAKA,GAC3BF,EAASlM,KAAKyT,IAAIT,EAAO9G,OAAQA,E,MAKxB,MAARD,GAAyB,MAATE,GAAwB,MAAPC,GAAyB,MAAVF,EAAgB,CACnE,MAAM9Q,EAAQnB,KAAKmB,MACnB,GAAIA,EAAO,CACV,GAAI0b,EAAQ,CACX,MAAMsX,EAAKniB,GAAQE,EAAQF,GAAQ,EAC7BoiB,EAAKniB,GAAUE,EAAMF,GAAU,EAGrC,OADA9Q,EAAM0b,QAAQsX,GAAKC,GACZjzB,EAAMqwB,gBAAgB,CAAExf,OAAME,QAAOC,MAAKF,eAAUpR,GAAYszB,GAAKC,E,CAG7E,OAAOjzB,EAAMqwB,gBAAgB,CAAExf,OAAME,QAAOC,MAAKF,U,EAGpD,CASO,iBAAAoiB,CAAkB1rB,GACxB,IAAI2rB,EACJ,MACMC,EADWv0B,KAAK2L,SAAS6oB,UACSC,iBAAiB9rB,GACzD,GAAI4rB,EAMH,OALAv0B,KAAKg0B,YAAYpI,MAAK,SAASlW,GAC1BA,EAAQ/J,UAAY4oB,IACvBD,EAAQ5e,EAEV,IACO4e,CAET,CAEO,oBAAAI,CAAqB/rB,GAC3B,OAAO3I,KAAKq0B,kBAAkBr0B,KAAKmB,MAAOkK,QAAQ1C,GACnD,EAvPA,sC,gDAAkC,qBAClC,uC,gDAA0C9I,EAAUmF,WAAWC,OAAO,CAAC8uB,GAAiB7uB,c,+CCuElF,MAAMyvB,WAA6B1C,GAA1C,c,oBASC,wC,gDAA2DjyB,KAAKuQ,aAAa,CAAC,KAC9E,4C,gDAAkC,IAClC,wC,gDAAyE,KACzE,iD,gDAAiE,KAEjE,4C,gDAAkC,IAClC,wC,gDAAyE,KAEzE,0C,gDAAqB,SAErB,sC,gDAAqD,KAErD,yC,yDAEA,0C,gDAAgC,GA2YjC,CAzYW,SAAAxQ,GACTC,KAAKC,OAAOC,KAAK,WACjBF,KAAKG,eAAe,eAAgB,WAEpCO,MAAMX,WACP,CAEO,eAAA2Q,GACNhQ,MAAMgQ,kBAEF1Q,KAAKkB,QAAQ,mBAChBlB,KAAK40B,QAAU,gBAAmB,EAAG,EAAG,IAAK,IAAK,EAAG,EAAG,EAAG,EAAG,IAG/D,MAAMzzB,EAAQnB,KAAKmB,MAEnB,GAAIA,EAAO,CAEV,MAAMugB,EAAYvgB,EAAMP,IAAI,YAAa,GAEzC,GAAI8gB,GAAa1hB,KAAK60B,YAAa,CAClC,MAAMC,EAAe90B,KAAKY,IAAI,eAAgB,GAC1Ck0B,EACC90B,KAAK+0B,YACR/0B,KAAK+0B,WAAWtlB,UAEhBzP,KAAK+0B,WAAa/0B,KAAKg1B,YAAW,KACjCh1B,KAAKi1B,eAAe,GAClBH,KAIH90B,KAAKi1B,gBACLj1B,KAAK+0B,WAAa/0B,KAAKg1B,YAAW,QAAW,IAI9Ch1B,KAAKi1B,gBAGNj1B,KAAK60B,YAAcnT,C,CAGpB,OAAY1hB,KAAKk1B,oBAAqB3zB,IACrC,MAAMgrB,EAAShrB,EAASX,IAAI,UACtB0K,EAAY/J,EAASX,IAAI,YAAa,GACtC2K,EAAWhK,EAASX,IAAI,WAAY,GAC1CZ,KAAKqzB,oBAAoB9G,EAAQ,CAAE9pB,KAAM,QAASiG,YAAa,CAAC4C,EAAWC,IAAa,CAACD,EAAWC,GAAU,G,CAGjH,CAGU,aAAA0pB,GACT,MAAME,EAA8E,CAAC,EAErF,OAAYn1B,KAAK8B,WAAYP,IAC5B,MAAM6zB,EAAU7zB,EAASX,IAAI,UAAW,YAEnCu0B,EAAOC,KACXD,EAAOC,GAAW,IAEnBD,EAAOC,GAASl1B,KAAKqB,EAAS,IAG/BvB,KAAKq1B,eAAiB,EACtBr1B,KAAKs1B,UAAY,GACjBt1B,KAAKu1B,eAAiB,EACtBv1B,KAAKw1B,UAAY,GAEjB,OAAYx1B,KAAKk1B,oBAAqB3zB,IACrCA,EAASgO,OAAO,gBAAY1O,EAAU,IAGvC,OAAYb,KAAK8B,WAAYP,IAC5BA,EAASgO,OAAO,eAAW1O,EAAU,IAGtC,OAAas0B,GAAQ,CAACM,EAAMC,KAC3B11B,KAAK21B,cAAcD,EAAM,IAI1B,OAAaP,GAAQ,CAACM,EAAMC,KAC3B11B,KAAK41B,cAAcF,EAAM,IAG1B,OAAY11B,KAAK8B,WAAYP,IAC5B,IAAKA,EAASX,IAAI,WAAY,CAC7B,MAAM0rB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAO/oB,IAAI,eAAe,E,OAMhC,CAcO,aAAAoyB,CAAct0B,EAAwCsb,GAC5D7c,KAAKyzB,gBAAgBlyB,EAASX,IAAI,WAAY,IAAKic,EACpD,CAEU,aAAA+Y,CAAc9zB,GACvB,MAAMX,EAAQnB,KAAKmB,MACnB,GAAIA,GAASA,EAAMP,IAAI,YAAa,IAAMO,EAAMP,IAAI,eAAgB,KAAOZ,KAAKY,IAAI,kBAAmB,WAetG,IAVAkB,EAAUwS,MAAK,CAAChK,EAAGC,KAClB,MAAMkC,EAASnC,EAAE1J,IAAI,SACf8L,EAASnC,EAAE3J,IAAI,SACrB,OAAI6L,GAAUC,EACN3G,KAAKkB,MAAMwF,EAAOhH,EAAIiH,EAAOjH,EAAGgH,EAAO7G,EAAI8G,EAAO9G,GAGnD,CAAC,IAGF9D,EAAUJ,OAAS,GAAG,CAC5B1B,KAAKu1B,gBACLv1B,KAAKw1B,UAAUx1B,KAAKu1B,eAAiB,GACrC,MAAMO,EAAU91B,KAAKw1B,UAAUx1B,KAAKu1B,eAC9Bh0B,EAAWO,EAAU,GAE3Bg0B,EAAQ51B,KAAKqB,GACb,cAAmBO,EAAWP,GAE9BvB,KAAK+1B,iBAAiBx0B,EAAUO,E,CAIlC,IAAIiB,EAAI,EAER,MAAMizB,EAAeh2B,KAAKY,IAAI,mBAC1Bo1B,GACH,OAAYh2B,KAAKw1B,WAAYM,IAC5B,IAAIG,EAAO,EACPC,EAAO,EAEPlzB,EAAM8yB,EAAQp0B,OAElB,GAAIsB,EAAM,EAAG,CAEZ,IA2BIoyB,EA3BAe,EAAoBn2B,KAAKk1B,mBAAmBnyB,GAChD,IAAKozB,EAAmB,CACvBA,EAAoB,IAAI,KAASn2B,UAAMa,EAAW,CAAC,GAEnD,MAAM0rB,EAAS4J,EAAkB1yB,IAAI,SAAiBuyB,EAAah2B,KAAK2O,MAAO3O,KAAMm2B,IAErF,GAAI5J,EAAQ,CACX,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,IACHxsB,KAAKoE,iBAAiBF,SAAShE,KAAKssB,GACpCA,EAAOvd,aAAaknB,GAEpBn2B,KAAKysB,KAAK7oB,OAAO8oB,KAAK,cAAc,KAC/BF,aAAkB4J,GAAA,GACrB5J,EAAO6J,cAAcC,IAChBA,aAAiBC,GAAA,GACpBD,EAAMjnB,iB,QAQZrP,KAAKk1B,mBAAmBh1B,KAAKi2B,E,CAK9B,OAAYL,GAAUv0B,IACrBA,EAASgO,OAAO,UAAW4mB,GAE3B,MAAMxtB,EAAQpH,EAASX,IAAI,SACvB+H,IACHstB,GAAQttB,EAAMlD,EACdywB,GAAQvtB,EAAM/C,GAGf,MAAM0mB,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAO/oB,IAAI,eAAe,E,IAI7B2xB,EAAU7zB,EAASX,IAAI,UAAU,IAGlC,IAAI41B,EAAWP,EAAOjzB,EAClByzB,EAAWP,EAAOlzB,EAEtBmzB,EAAkB5mB,OAAO,WAAmBumB,GAC5CK,EAAkB5mB,OAAO,UAAW6lB,GAEpC,MAAMsB,EAAUP,EAAkBv1B,IAAI,SACtCu1B,EAAkB5mB,OAAO,QAAgBvM,GAEzC,MAAMupB,EAAS4J,EAAkBv1B,IAAI,UACrC,GAAI2rB,EAAQ,CAEX,IAAIngB,EAAWpM,KAAKmB,MAAO+L,OAAO,CAAEzH,EAAG+wB,EAAU5wB,EAAG6wB,IAChDrqB,GACH+pB,EAAkB9U,OAAO,CACxB/V,UAAWc,EAASd,UACpBC,SAAUa,EAASb,WAIrBvL,KAAKkyB,iBAAiBiE,GAEtB,MAAM3J,EAASD,EAAO3rB,IAAI,UACtB4rB,IACHA,EAAO/oB,IAAI,eAAe,GAGtBizB,GAAW1zB,GACVwpB,aAAkB4J,GAAA,GACrB5J,EAAO6J,cAAcC,IAChBA,aAAiBK,GAAA,GACpBL,EAAMM,KAAKC,e,KAOjB9zB,G,KAKH,OAAY/C,KAAKk1B,oBAAqB3zB,IACrC,IAAI2C,EAAW3C,EAASX,IAAI,YAC5B,IAAKsD,GAA+B,GAAnBA,EAASxC,OAAa,CACtC,MAAM6qB,EAAShrB,EAASX,IAAI,UAC5B,GAAI2rB,EAAQ,CACX,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAO/oB,IAAI,eAAe,E,KAK/B,CAEU,YAAAqB,GACTpE,MAAMoE,eAEN,OAAY9E,KAAKk1B,oBAAqB3zB,IACrC,MAAMgrB,EAAShrB,EAASX,IAAI,UAC5B,GAAI2rB,EAAQ,CACX,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAO/c,S,KAIVzP,KAAKk1B,mBAAqB,EAE3B,CAEU,gBAAAa,CAAiBx0B,EAAmDO,GAC7E,MAAM6G,EAAQpH,EAASX,IAAI,SAC3B,GAAI+H,EAAO,CACV,IAAIka,EAAc7iB,KAAKY,IAAI,cAAe,IAC1C,MAAMk1B,EAAU91B,KAAKw1B,UAAUx1B,KAAKu1B,eAEpC,IAAK,IAAIxyB,EAAIjB,EAAUJ,OAAS,EAAGqB,GAAK,EAAGA,IAAK,CAC/C,MAAM2wB,EAAK5xB,EAAUiB,GACrB,GAAI2wB,IAAOA,EAAG9yB,IAAI,WAAY,CAC7B,MAAMk2B,EAAUpD,EAAG9yB,IAAI,SACnBk2B,GACC/wB,KAAKkB,MAAM6vB,EAAQrxB,EAAIkD,EAAMlD,EAAGqxB,EAAQlxB,EAAI+C,EAAM/C,GAAKid,IAC1DiT,EAAQ51B,KAAKwzB,GACb,cAAmB5xB,EAAW4xB,GAC9B1zB,KAAK+1B,iBAAiBrC,EAAI5xB,G,GAMhC,CAEU,aAAA6zB,CAAc7zB,GACvB,MAAMX,EAAQnB,KAAKmB,MACnB,GAAIA,GAASA,EAAMP,IAAI,YAAa,IAAMO,EAAMP,IAAI,eAAgB,KAAOZ,KAAKY,IAAI,kBAAmB,KAAO,CAC7G,KAAOkB,EAAUJ,OAAS,GAAG,CAC5B1B,KAAKq1B,gBACLr1B,KAAKs1B,UAAUt1B,KAAKq1B,eAAiB,GACrC,MAAM0B,EAAU/2B,KAAKs1B,UAAUt1B,KAAKq1B,eAC9B9zB,EAAWO,EAAU,GAE3Bi1B,EAAQ72B,KAAKqB,GACb,SAAcO,EAAWP,GAEzBvB,KAAKg3B,iBAAiBz1B,EAAUO,E,CAGjC,OAAY9B,KAAKs1B,WAAYyB,IAG5B,GAFUA,EAAQr1B,OAER,EAAG,CACZ,IAAIu1B,EAAmE,GACnEhsB,EAAI,EACJ8M,EAAS/X,KAAKY,IAAI,gBAAiB,GACvC,OAAYm2B,GAAUx1B,IACrB,IAAI21B,EAAcl3B,KAAK40B,QAAQ3pB,GAC3BksB,GAAa,EAEjB,GAAIF,EAAgBv1B,OAAS,EAC5B,KAAOy1B,GACN,OAAYF,GAAkBG,IAE7B,IADAD,GAAa,EACN,kBAAqB,CAAE1xB,EAAGyxB,EAAYzxB,EAAGG,EAAGsxB,EAAYtxB,EAAGmS,OAAQA,GAAUqf,IACnFnsB,IAEuBpK,MAAnBb,KAAK40B,QAAQ3pB,GAChBksB,GAAa,GAGbA,GAAa,EACbD,EAAcl3B,KAAK40B,QAAQ3pB,G,IAOhC,MAAMoF,EAAK6mB,EAAYzxB,EACjByK,EAAKgnB,EAAYtxB,EAEvBqxB,EAAgB/2B,KAAK,CAAEuF,EAAG4K,EAAIzK,EAAGsK,EAAI6H,OAAQA,IAE7CxW,EAASkC,IAAI,KAAM4M,GACnB9O,EAASkC,IAAI,KAAMyM,GAEnB,MAAMoc,EAAU/qB,EAAS+qB,QACrBA,GACH,OAAYA,GAAUC,IACrB,MAAMC,EAASD,EAAO3rB,IAAI,UACtB4rB,GACHA,EAAOnL,OAAO,CAAEhR,GAAIA,EAAIH,GAAIA,G,WAQpC,CAEU,gBAAA8mB,CAAiBz1B,EAAmDO,GAC7E,MAAM6G,EAAQpH,EAASX,IAAI,SAC3B,GAAI+H,EAAO,CACV,MAAMma,EAAkB9iB,KAAKY,IAAI,kBAAmB,GAC9Cm2B,EAAU/2B,KAAKs1B,UAAUt1B,KAAKq1B,eACpC,OAAYvzB,GAAY4xB,IACvB,GAAIA,IAAOA,EAAG9yB,IAAI,WAAY,CAC7B,MAAMk2B,EAAUpD,EAAG9yB,IAAI,SAEnBk2B,GACC/wB,KAAKkB,MAAM6vB,EAAQrxB,EAAIkD,EAAMlD,EAAGqxB,EAAQlxB,EAAI+C,EAAM/C,GAAKkd,IAC1DiU,EAAQ72B,KAAKwzB,GACb,cAAmB5xB,EAAW4xB,GAC9B1zB,KAAKg3B,iBAAiBtD,EAAI5xB,G,KAMhC,EA/ZA,sC,gDAAkC,yBAClC,uC,gDAA0CmwB,GAAejtB,WAAWC,OAAO,CAAC0vB,GAAqBzvB,c,eCnI3F,MAAMmyB,WAAoBC,GAAA,EAOtB,SAAAv3B,GACTW,MAAMX,YACNC,KAAKu3B,OAAO,YACb,CAEO,gBAAAv2B,GACNN,MAAMM,mBAEFhB,KAAKw3B,eAAe,UACvBx3B,KAAKyD,IAAI,SAAUzD,KAAKwE,WAAW,SAErC,EClCM,SAASizB,GAAgBhyB,EAAGG,GACjC,MAAO,CAAC,EAAIA,GAAK,EAAIH,GAAI,EAAIG,GAC/B,CAIe,cACb,OAAOtB,GAAWmzB,IACbte,MAAM,OACNvO,UAAU,GAAKzE,EACtB,CCZO,SAASuxB,GAAmB5tB,EAAQC,GACzC,MAAO,CAACD,EAAQC,EAClB,CAIe,cACb,OAAOzF,GAAWozB,IACbve,MAAM,OACb,CCPO,SAASwe,GAAkBvqB,EAAIE,GACpC,IAAIE,EAAM,EAAIJ,GAAKlH,GAAKsH,EAAM,EAAIF,IAAO,EAGzC,GAAItH,EAAIE,GAAKC,EAAS,OCNjB,SAAiCoQ,GACtC,IAAIrO,EAAU,EAAIqO,GAElB,SAASqK,EAAQ9W,EAAQC,GACvB,MAAO,CAACD,EAAS5B,EAAS,EAAI6B,GAAO7B,EACvC,CAMA,OAJA0Y,EAAQ1T,OAAS,SAASzH,EAAGG,GAC3B,MAAO,CAACH,EAAIyC,EAASV,EAAK5B,EAAIsC,GAChC,EAEO0Y,CACT,CDN+BgX,CAAwBxqB,GAErD,IAAIwN,EAAI,EAAIpN,GAAO,EAAItH,EAAIsH,GAAMqqB,EAAKvwB,EAAKsT,GAAK1U,EAEhD,SAAS+T,EAAQxU,EAAGG,GAClB,IAAIgY,EAAItW,EAAKsT,EAAI,EAAI1U,EAAI,EAAIN,IAAMM,EACnC,MAAO,CAAC0X,EAAI,EAAInY,GAAKS,GAAI2xB,EAAKja,EAAI,EAAInY,GACxC,CAUA,OARAwU,EAAQ/M,OAAS,SAASzH,EAAGG,GAC3B,IAAIkyB,EAAMD,EAAKjyB,EACXsP,EAAIrO,EAAMpB,EAAGO,EAAI8xB,IAAQzwB,EAAKywB,GAGlC,OAFIA,EAAM5xB,EAAI,IACZgP,GAAK7O,EAAKgB,EAAK5B,GAAK4B,EAAKywB,IACpB,CAAC5iB,EAAIhP,EAAGsB,GAAMoT,GAAKnV,EAAIA,EAAIqyB,EAAMA,GAAO5xB,EAAIA,IAAM,EAAIA,IAC/D,EAEO+T,CACT,CAEe,cACb,OE1BK,SAAyB2C,GAC9B,IAAIrG,EAAO,EACPK,EAAOvQ,EAAK,EACZ+M,EAAIuJ,GAAkBC,GACtBlX,EAAI0N,EAAEmD,EAAMK,GAMhB,OAJAlR,EAAEqyB,UAAY,SAASrmB,GACrB,OAAO5B,UAAUpO,OAAS0R,EAAEmD,EAAO7E,EAAE,GAAK/K,EAASiQ,EAAOlF,EAAE,GAAK/K,GAAW,CAAC4P,EAAO7P,EAASkQ,EAAOlQ,EACtG,EAEOhB,CACT,CFeSsyB,CAAgBL,IAClBxe,MAAM,SACNqE,OAAO,CAAC,EAAG,SAClB,CGRe,cACb,IAAIP,EACAC,EACoB+a,EAC+DC,EACDC,EAClFxvB,EAHAyvB,ECxBGC,KACFN,UAAU,CAAC,KAAM,OACjB5e,MAAM,MACNC,UAAU,CAAC,IAAK,MAChByD,OAAO,CAAC,GAAI,IACZW,OAAO,EAAE,GAAK,ODoBf8a,EAASD,KAAiBxb,OAAO,CAAC,IAAK,IAAIW,OAAO,EAAE,EAAG,OAAOua,UAAU,CAAC,GAAI,KAC7EQ,EAASF,KAAiBxb,OAAO,CAAC,IAAK,IAAIW,OAAO,EAAE,EAAG,OAAOua,UAAU,CAAC,EAAG,KACrES,EAAc,CAAC7vB,MAAO,SAASlD,EAAGG,GAAK+C,EAAQ,CAAClD,EAAGG,EAAI,GAElE,SAAS6yB,EAAU/vB,GACjB,IAAIjD,EAAIiD,EAAY,GAAI9C,EAAI8C,EAAY,GACxC,OAAOC,EAAQ,KACVsvB,EAAatvB,MAAMlD,EAAGG,GAAI+C,IACvBuvB,EAAYvvB,MAAMlD,EAAGG,GAAI+C,KACzBwvB,EAAYxvB,MAAMlD,EAAGG,GAAI+C,EACnC,CAkEA,SAASgV,IAEP,OADAV,EAAQC,EAAc,KACfub,CACT,CAEA,OArEAA,EAAUvrB,OAAS,SAASxE,GAC1B,IAAIsF,EAAIoqB,EAAQjf,QACZjL,EAAIkqB,EAAQhf,YACZ3T,GAAKiD,EAAY,GAAKwF,EAAE,IAAMF,EAC9BpI,GAAK8C,EAAY,GAAKwF,EAAE,IAAMF,EAClC,OAAQpI,GAAK,KAASA,EAAI,MAASH,IAAM,MAASA,GAAK,KAAQ6yB,EACzD1yB,GAAK,MAASA,EAAI,MAASH,IAAM,MAASA,GAAK,KAAQ8yB,EACvDH,GAASlrB,OAAOxE,EACxB,EAEA+vB,EAAU7wB,OAAS,SAASA,GAC1B,OAAOqV,GAASC,IAAgBtV,EAASqV,GA5C1Byb,EA4CoD,CAACN,EAAQxwB,OAAOsV,EAActV,GAAS0wB,EAAO1wB,OAAOA,GAAS2wB,EAAO3wB,OAAOA,IA3C7I1B,EAAIwyB,EAAQh3B,OA2CmCub,EA1C5C,CACLtU,MAAO,SAASlD,EAAGG,GAAiB,IAAZ,IAAI7C,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAG4F,MAAMlD,EAAGG,EAAI,EAC5E4C,OAAQ,WAAyB,IAAZ,IAAIzF,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAGyF,QAAU,EACtEe,UAAW,WAAyB,IAAZ,IAAIxG,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAGwG,WAAa,EAC5EC,QAAS,WAAyB,IAAZ,IAAIzG,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAGyG,SAAW,EACxEC,aAAc,WAAyB,IAAZ,IAAI1G,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAG0G,cAAgB,EAClFC,WAAY,WAAyB,IAAZ,IAAI3G,GAAK,IAAYA,EAAImD,GAAGwyB,EAAQ31B,GAAG2G,YAAc,IARlF,IAAmBgvB,EACbxyB,CA4CJ,EAEAuyB,EAAU5tB,UAAY,SAAS6G,GAC7B,OAAK5B,UAAUpO,QACf02B,EAAQvtB,UAAU6G,GAAI4mB,EAAOztB,UAAU6G,GAAI6mB,EAAO1tB,UAAU6G,GACrDiM,KAFuBya,EAAQvtB,WAGxC,EAEA4tB,EAAUtf,MAAQ,SAASzH,GACzB,OAAK5B,UAAUpO,QACf02B,EAAQjf,MAAMzH,GAAI4mB,EAAOnf,MAAU,IAAJzH,GAAW6mB,EAAOpf,MAAMzH,GAChD+mB,EAAUrf,UAAUgf,EAAQhf,cAFLgf,EAAQjf,OAGxC,EAEAsf,EAAUrf,UAAY,SAAS1H,GAC7B,IAAK5B,UAAUpO,OAAQ,OAAO02B,EAAQhf,YACtC,IAAIpL,EAAIoqB,EAAQjf,QAAS1T,GAAKiM,EAAE,GAAI9L,GAAK8L,EAAE,GAiB3C,OAfAumB,EAAeG,EACVhf,UAAU1H,GACVwH,WAAW,CAAC,CAACzT,EAAI,KAAQuI,EAAGpI,EAAI,KAAQoI,GAAI,CAACvI,EAAI,KAAQuI,EAAGpI,EAAI,KAAQoI,KACxEpG,OAAO4wB,GAEZN,EAAcI,EACTlf,UAAU,CAAC3T,EAAI,KAAQuI,EAAGpI,EAAI,KAAQoI,IACtCkL,WAAW,CAAC,CAACzT,EAAI,KAAQuI,EAAI7H,EAASP,EAAI,IAAQoI,EAAI7H,GAAU,CAACV,EAAI,KAAQuI,EAAI7H,EAASP,EAAI,KAAQoI,EAAI7H,KAC1GyB,OAAO4wB,GAEZL,EAAcI,EACTnf,UAAU,CAAC3T,EAAI,KAAQuI,EAAGpI,EAAI,KAAQoI,IACtCkL,WAAW,CAAC,CAACzT,EAAI,KAAQuI,EAAI7H,EAASP,EAAI,KAAQoI,EAAI7H,GAAU,CAACV,EAAI,KAAQuI,EAAI7H,EAASP,EAAI,KAAQoI,EAAI7H,KAC1GyB,OAAO4wB,GAEL7a,GACT,EAEA8a,EAAUpf,UAAY,SAAS5H,EAAQpJ,GACrC,OAAOgR,GAAUof,EAAWhnB,EAAQpJ,EACtC,EAEAowB,EAAUhf,QAAU,SAASC,EAAMrR,GACjC,OAAOoR,GAAQgf,EAAW/e,EAAMrR,EAClC,EAEAowB,EAAU9e,SAAW,SAASC,EAAOvR,GACnC,OAAOsR,GAAS8e,EAAW7e,EAAOvR,EACpC,EAEAowB,EAAU5e,UAAY,SAASC,EAAQzR,GACrC,OAAOwR,GAAU4e,EAAW3e,EAAQzR,EACtC,EAOOowB,EAAUtf,MAAM,KACzB,CNzFC,sC,gDAAkC,gBAClC,uC,gDAA0Cme,GAAA,EAAUtyB,WAAWC,OAAO,CAACoyB,GAAYnyB,cCdpFuyB,GAAgBvqB,OOOT,SAAyBiJ,GAC9B,OAAO,SAAS1Q,EAAGG,GACjB,IAAIyE,EAAI/C,EAAK7B,EAAIA,EAAIG,EAAIA,GACrBgV,EAAIzE,EAAM9L,GACVsuB,EAAK,EAAI/d,GACTge,EAAK,EAAIhe,GACb,MAAO,CACL/T,EAAMpB,EAAIkzB,EAAItuB,EAAIuuB,GAClBpxB,EAAK6C,GAAKzE,EAAI+yB,EAAKtuB,GAEvB,CACF,CPlByBwuB,CAAgBrxB,GCFzCkwB,GAAmBxqB,OAASwqB,GOH5B,IAAIoB,GAAK,SACLC,IAAM,QACNC,GAAK,OACLC,GAAK,QACLC,GAAI5xB,EAAK,GAAK,EAGX,SAAS6xB,GAAcrvB,EAAQC,GACpC,IAAImL,EAAI1N,EAAK0xB,GAAI,EAAInvB,IAAOqvB,EAAKlkB,EAAIA,EAAGmkB,EAAKD,EAAKA,EAAKA,EACvD,MAAO,CACLtvB,EAAS,EAAIoL,IAAMgkB,IAAKJ,GAAK,EAAIC,GAAKK,EAAKC,GAAM,EAAIL,GAAK,EAAIC,GAAKG,KACnElkB,GAAK4jB,GAAKC,GAAKK,EAAKC,GAAML,GAAKC,GAAKG,IAExC,CAgBe,cACb,OAAO90B,GAAW60B,IACbhgB,MAAM,QACb,CChCO,SAASmgB,GAAiBxvB,EAAQC,GACvC,IAAI8Q,EAAO9Q,EAAMA,EAAKwvB,EAAO1e,EAAOA,EACpC,MAAO,CACL/Q,GAAU,MAAS,QAAW+Q,EAAO0e,GAAoBA,GAAQ,QAAW1e,EAAO,QAAW0e,GAAhD,UAC9CxvB,GAAO,SAAW8Q,GAAQ,QAAW0e,GAAoB,QAAW1e,EAAtB,QAA6B,QAAW0e,KAE1F,CAee,cACb,OAAOj1B,GAAWg1B,IACbngB,MAAM,QACb,CDTAggB,GAAcjsB,OAAS,SAASzH,EAAGG,GAEjC,IADA,IACgBuE,EADZ+K,EAAItP,EAAGwzB,EAAKlkB,EAAIA,EAAGmkB,EAAKD,EAAKA,EAAKA,EAC7Br2B,EAAI,EAAmBA,EAZjB,KAesBs2B,GAAZD,GAAvBlkB,GAAK/K,GAFA+K,GAAK4jB,GAAKC,GAAKK,EAAKC,GAAML,GAAKC,GAAKG,IAAOxzB,IAC1CkzB,GAAK,EAAIC,GAAKK,EAAKC,GAAM,EAAIL,GAAK,EAAIC,GAAKG,KACjBlkB,GAAakkB,EAAKA,IAC9CpzB,EAAImE,GAAS/D,MAJ+BrD,GAMlD,MAAO,CACLm2B,GAAIzzB,GAAKqzB,GAAK,EAAIC,GAAKK,EAAKC,GAAM,EAAIL,GAAK,EAAIC,GAAKG,IAAO,EAAIlkB,GAC/D1N,EAAK,EAAI0N,GAAKgkB,IAElB,ECnBAI,GAAiBpsB,OAAS,SAASzH,EAAGG,GACpC,IAAqBuE,EAAjBJ,EAAMnE,EAAG7C,EAAI,GACjB,EAAG,CACD,IAAI8X,EAAO9Q,EAAMA,EAAKwvB,EAAO1e,EAAOA,EACpC9Q,GAAOI,GAASJ,GAAO,SAAW8Q,GAAQ,QAAW0e,GAAoB,QAAW1e,EAAtB,QAA6B,QAAW0e,KAAU3zB,IAC3G,SAAWiV,GAAQ,QAAe0e,GAAwB,QAAe1e,EAA/B,QAAsC,QAAW,GAAK0e,IACvG,OAASvzB,EAAImE,GAAShE,KAAapD,EAAI,GACvC,MAAO,CACL0C,GAAK,OAAUoV,EAAO9Q,EAAMA,IAAoB8Q,GAAoBA,EAAOA,EAAOA,GAAQ,QAAW,QAAWA,GAAvD,SAApB,UACrC9Q,EAEJ,C,uBCpBA,IAAIyvB,EAAQ,EAAQ,MAOpB,SAASC,EAAU/jB,EAAS7K,EAAW6uB,GAInC,IAAIC,EAAMC,EAAMC,EAAMC,EAHtBjvB,EAAYA,GAAa,EAIzB,IAAK,IAAI9H,EAAI,EAAGA,EAAI2S,EAAQ,GAAGhU,OAAQqB,IAAK,CACxC,IAAI2C,EAAIgQ,EAAQ,GAAG3S,KACdA,GAAK2C,EAAE,GAAKi0B,KAAMA,EAAOj0B,EAAE,MAC3B3C,GAAK2C,EAAE,GAAKk0B,KAAMA,EAAOl0B,EAAE,MAC3B3C,GAAK2C,EAAE,GAAKm0B,KAAMA,EAAOn0B,EAAE,MAC3B3C,GAAK2C,EAAE,GAAKo0B,KAAMA,EAAOp0B,EAAE,GACpC,CAEA,IAAIkU,EAAQigB,EAAOF,EACf7f,EAASggB,EAAOF,EAChBG,EAAWh0B,KAAKyT,IAAII,EAAOE,GAC3BP,EAAIwgB,EAAW,EAEnB,GAAiB,IAAbA,EAAgB,CAChB,IAAIC,EAAkC,CAACL,EAAMC,GAE7C,OADAI,EAAgC3rB,SAAW,EACpC2rB,CACX,CAMA,IAHA,IAAIC,EAAY,IAAIT,OAAM34B,EAAWq5B,GAG5Bz0B,EAAIk0B,EAAMl0B,EAAIo0B,EAAMp0B,GAAKs0B,EAC9B,IAAK,IAAIn0B,EAAIg0B,EAAMh0B,EAAIk0B,EAAMl0B,GAAKm0B,EAC9BE,EAAU/5B,KAAK,IAAIi6B,EAAK10B,EAAI8T,EAAG3T,EAAI2T,EAAGA,EAAG7D,IAKjD,IAAI0kB,EA2ER,SAAyB1kB,GAMrB,IALA,IAAIgO,EAAO,EACPje,EAAI,EACJG,EAAI,EACJ8N,EAASgC,EAAQ,GAEZ3S,EAAI,EAAGC,EAAM0Q,EAAOhS,OAAQiE,EAAI3C,EAAM,EAAGD,EAAIC,EAAK2C,EAAI5C,IAAK,CAChE,IAAIuH,EAAIoJ,EAAO3Q,GACXwH,EAAImJ,EAAO/N,GACX00B,EAAI/vB,EAAE,GAAKC,EAAE,GAAKA,EAAE,GAAKD,EAAE,GAC/B7E,IAAM6E,EAAE,GAAKC,EAAE,IAAM8vB,EACrBz0B,IAAM0E,EAAE,GAAKC,EAAE,IAAM8vB,EACrB3W,GAAY,EAAJ2W,CACZ,CACA,OAAa,IAAT3W,EAAmB,IAAIyW,EAAKzmB,EAAO,GAAG,GAAIA,EAAO,GAAG,GAAI,EAAGgC,GACxD,IAAIykB,EAAK10B,EAAIie,EAAM9d,EAAI8d,EAAM,EAAGhO,EAC3C,CA3FmB4kB,CAAgB5kB,GAG3B6kB,EAAW,IAAIJ,EAAKR,EAAO/f,EAAQ,EAAGggB,EAAO9f,EAAS,EAAG,EAAGpE,GAC5D6kB,EAASxsB,EAAIqsB,EAASrsB,IAAGqsB,EAAWG,GAIxC,IAFA,IAAIC,EAAYP,EAAUv4B,OAEnBu4B,EAAUv4B,QAAQ,CAErB,IAAI+4B,EAAOR,EAAU3mB,MAGjBmnB,EAAK1sB,EAAIqsB,EAASrsB,IAClBqsB,EAAWK,EACPf,GAAO/2B,QAAQC,IAAI,gCAAiCmD,KAAKuM,MAAM,IAAMmoB,EAAK1sB,GAAK,IAAKysB,IAIxFC,EAAK1qB,IAAMqqB,EAASrsB,GAAKlD,IAG7B0O,EAAIkhB,EAAKlhB,EAAI,EACb0gB,EAAU/5B,KAAK,IAAIi6B,EAAKM,EAAKh1B,EAAI8T,EAAGkhB,EAAK70B,EAAI2T,EAAGA,EAAG7D,IACnDukB,EAAU/5B,KAAK,IAAIi6B,EAAKM,EAAKh1B,EAAI8T,EAAGkhB,EAAK70B,EAAI2T,EAAGA,EAAG7D,IACnDukB,EAAU/5B,KAAK,IAAIi6B,EAAKM,EAAKh1B,EAAI8T,EAAGkhB,EAAK70B,EAAI2T,EAAGA,EAAG7D,IACnDukB,EAAU/5B,KAAK,IAAIi6B,EAAKM,EAAKh1B,EAAI8T,EAAGkhB,EAAK70B,EAAI2T,EAAGA,EAAG7D,IACnD8kB,GAAa,EACjB,CAEId,IACA/2B,QAAQC,IAAI,eAAiB43B,GAC7B73B,QAAQC,IAAI,kBAAoBw3B,EAASrsB,IAG7C,IAAI2sB,EAAwB,CAACN,EAAS30B,EAAG20B,EAASx0B,GAElD,OADA80B,EAAsBrsB,SAAW+rB,EAASrsB,EACnC2sB,CACX,CAEA,SAASR,EAAW5vB,EAAGC,GACnB,OAAOA,EAAEwF,IAAMzF,EAAEyF,GACrB,CAEA,SAASoqB,EAAK10B,EAAGG,EAAG2T,EAAG7D,GACnB1V,KAAKyF,EAAIA,EACTzF,KAAK4F,EAAIA,EACT5F,KAAKuZ,EAAIA,EACTvZ,KAAK+N,EAKT,SAA4BtI,EAAGG,EAAG8P,GAI9B,IAHA,IAAIilB,GAAS,EACTC,EAAY/hB,IAEP7K,EAAI,EAAGA,EAAI0H,EAAQhU,OAAQsM,IAGhC,IAFA,IAAI2H,EAAOD,EAAQ1H,GAEVjL,EAAI,EAAGC,EAAM2S,EAAKjU,OAAQiE,EAAI3C,EAAM,EAAGD,EAAIC,EAAK2C,EAAI5C,IAAK,CAC9D,IAAIuH,EAAIqL,EAAK5S,GACTwH,EAAIoL,EAAKhQ,GAER2E,EAAE,GAAK1E,GAAM2E,EAAE,GAAK3E,GACpBH,GAAK8E,EAAE,GAAKD,EAAE,KAAO1E,EAAI0E,EAAE,KAAOC,EAAE,GAAKD,EAAE,IAAMA,EAAE,KAAKqwB,GAAUA,GAEvEC,EAAY70B,KAAKyT,IAAIohB,EAAWC,EAAap1B,EAAGG,EAAG0E,EAAGC,GAC1D,CAGJ,OAAqB,IAAdqwB,EAAkB,GAAKD,EAAS,GAAK,GAAK50B,KAAKuB,KAAKszB,EAC/D,CAxBaE,CAAmBr1B,EAAGG,EAAG8P,GAClC1V,KAAK+P,IAAM/P,KAAK+N,EAAI/N,KAAKuZ,EAAIxT,KAAKg1B,KACtC,CA4CA,SAASF,EAAaG,EAAIC,EAAI3wB,EAAGC,GAE7B,IAAI9E,EAAI6E,EAAE,GACN1E,EAAI0E,EAAE,GACN+F,EAAK9F,EAAE,GAAK9E,EACZyK,EAAK3F,EAAE,GAAK3E,EAEhB,GAAW,IAAPyK,GAAmB,IAAPH,EAAU,CAEtB,IAAIhC,IAAM8sB,EAAKv1B,GAAK4K,GAAM4qB,EAAKr1B,GAAKsK,IAAOG,EAAKA,EAAKH,EAAKA,GAEtDhC,EAAI,GACJzI,EAAI8E,EAAE,GACN3E,EAAI2E,EAAE,IAEC2D,EAAI,IACXzI,GAAK4K,EAAKnC,EACVtI,GAAKsK,EAAKhC,EAElB,CAKA,OAHAmC,EAAK2qB,EAAKv1B,GAGE4K,GAFZH,EAAK+qB,EAAKr1B,GAEYsK,CAC1B,CA/JIspB,EAAM0B,UAAS1B,EAAQA,EAAM0B,SAEjCC,EAAOC,QAAU3B,EACjB0B,EAAOC,QAAP,QAAyB3B,C,oECNV,MAAM4B,EACjB,WAAAj2B,CAAY5E,EAAO,GAAI86B,EAAUC,GAK7B,GAJAv7B,KAAKQ,KAAOA,EACZR,KAAK0B,OAAS1B,KAAKQ,KAAKkB,OACxB1B,KAAKs7B,QAAUA,EAEXt7B,KAAK0B,OAAS,EACd,IAAK,IAAIqB,GAAK/C,KAAK0B,QAAU,GAAK,EAAGqB,GAAK,EAAGA,IAAK/C,KAAKw7B,MAAMz4B,EAErE,CAEA,IAAA7C,CAAKu7B,GACDz7B,KAAKQ,KAAKN,KAAKu7B,GACfz7B,KAAK0B,SACL1B,KAAK07B,IAAI17B,KAAK0B,OAAS,EAC3B,CAEA,GAAA4R,GACI,GAAoB,IAAhBtT,KAAK0B,OAAc,OAEvB,MAAMyQ,EAAMnS,KAAKQ,KAAK,GAChByR,EAASjS,KAAKQ,KAAK8S,MAQzB,OAPAtT,KAAK0B,SAED1B,KAAK0B,OAAS,IACd1B,KAAKQ,KAAK,GAAKyR,EACfjS,KAAKw7B,MAAM,IAGRrpB,CACX,CAEA,IAAAwpB,GACI,OAAO37B,KAAKQ,KAAK,EACrB,CAEA,GAAAk7B,CAAIE,GACA,MAAM,KAACp7B,EAAI,QAAE86B,GAAWt7B,KAClBy7B,EAAOj7B,EAAKo7B,GAElB,KAAOA,EAAM,GAAG,CACZ,MAAMrJ,EAAUqJ,EAAM,GAAM,EACtBrnB,EAAU/T,EAAK+xB,GACrB,GAAI+I,EAAQG,EAAMlnB,IAAY,EAAG,MACjC/T,EAAKo7B,GAAOrnB,EACZqnB,EAAMrJ,CACV,CAEA/xB,EAAKo7B,GAAOH,CAChB,CAEA,KAAAD,CAAMI,GACF,MAAM,KAACp7B,EAAI,QAAE86B,GAAWt7B,KAClB67B,EAAa77B,KAAK0B,QAAU,EAC5B+5B,EAAOj7B,EAAKo7B,GAElB,KAAOA,EAAMC,GAAY,CACrB,IAAI7pB,EAAoB,GAAZ4pB,GAAO,GACfE,EAAOt7B,EAAKwR,GAChB,MAAME,EAAQF,EAAO,EAMrB,GAJIE,EAAQlS,KAAK0B,QAAU45B,EAAQ96B,EAAK0R,GAAQ4pB,GAAQ,IACpD9pB,EAAOE,EACP4pB,EAAOt7B,EAAK0R,IAEZopB,EAAQQ,EAAML,IAAS,EAAG,MAE9Bj7B,EAAKo7B,GAAOE,EACZF,EAAM5pB,CACV,CAEAxR,EAAKo7B,GAAOH,CAChB,EAGJ,SAASF,EAAejxB,EAAGC,GACvB,OAAOD,EAAIC,GAAK,EAAID,EAAIC,EAAI,EAAI,CACpC,C,mEC7EO,MAAMwxB,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapSeries.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-array/src/fsum.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/math.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/noop.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/stream.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/length.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/distance.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapLine.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/interpolate.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapLineSeries.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-array/src/range.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/graticule.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/GraticuleSeries.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/compose.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/rotation.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/buffer.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/pointEqual.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/rejoin.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/cartesian.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/polygonContains.js", "webpack://@amcharts/amcharts5/./node_modules/d3-array/src/merge.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/index.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/antimeridian.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/constant.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/circle.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/rectangle.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/identity.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/transform.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/bounds.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/fit.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/resample.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/index.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/circle.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/clip/line.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/mercator.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapChartDefaultTheme.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/area.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/centroid.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/context.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/measure.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/string.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/centroid.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/area.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/bounds.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapUtils.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapChart.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/path/index.js", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapPointSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapPolygon.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/MapPolygonSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/ClusteredPointSeries.ts", "webpack://@amcharts/amcharts5/./src/.internal/charts/map/ZoomControl.ts", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/orthographic.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/equirectangular.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/conicEqualArea.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/cylindricalEqualArea.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/conic.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/albersUsa.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/albers.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/azimuthal.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/equalEarth.js", "webpack://@amcharts/amcharts5/./node_modules/d3-geo/src/projection/naturalEarth1.js", "webpack://@amcharts/amcharts5/./node_modules/polylabel/polylabel.js", "webpack://@amcharts/amcharts5/./node_modules/tinyqueue/index.js", "webpack://@amcharts/amcharts5/./tmp/webpack/map.js"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON> } from \"./MapChart\";\nimport type { GeoProjection, GeoPath } from \"d3-geo\";\nimport type { DataItem } from \"../../core/render/Component\";\n\nimport { Series, ISeriesSettings, ISeriesDataItem, ISeriesPrivate, ISeriesEvents } from \"../../core/render/Series\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $object from \"../../core/util/Object\";\n\nexport interface IMapSeriesDataItem extends ISeriesDataItem {\n\tgeometry?: GeoJSON.Geometry;\n\tgeometryType?: GeoJSON.GeoJsonGeometryTypes;\n\tvalue?: number;\n}\n\nexport interface IMapSeriesSettings extends ISeriesSettings {\n\n\t/**\n\t * All map series will determine the actual bounds shown in the [[MapChart]].\n\t *\n\t * If we need a series to be ignored while calculating the bounds, we can set\n\t * this to `false`.\n\t *\n\t * Especially useful for background series.\n\t *\n\t * @default true\n\t * @since 5.2.36\n\t */\n\taffectsBounds?: boolean;\n\n\t/**\n\t * Map data in GeoJSON format.\n\t */\n\tgeoJSON?: GeoJSON.GeoJSON;\n\n\t/**\n\t * An array of map object ids from geodata to include in the map.\n\t *\n\t * If set, only those objects listed in `include` will be shown.\n\t */\n\tinclude?: Array<string>;\n\n\t/**\n\t * An array of map object ids from geodata to omit when showing the map.\n\t */\n\texclude?: Array<string>;\n\n\t/**\n\t * A field in series `data` that will hold map object's numeric value.\n\t *\n\t * It can be used in a number of places, e.g. tooltips, heat rules, etc.\n\t */\n\tvalueField?: string;\n\n\t/**\n\t * @ignore\n\t */\n\tgeometryField?: string;\n\n\t/**\n\t * @ignore\n\t */\n\tgeometryTypeField?: string;\n\n\t/**\n\t * Names of geodata items, such as countries, to replace by from loaded\n\t * geodata.\n\t *\n\t * Can be used to override built-in English names for countries.\n\t *\n\t * ```TypeScript\n\t * import am5geodata_lang_ES from '@amcharts5-geodata/lang/es';\n\t * // ...\n\t * map.geodataNames = am5geodata_lang_ES;\n\t * ```\n\t * ```JavaScript\n\t * map.geodataNames = am5geodata_lang_ES;\n\t * ```\n\t *\n\t * @since 5.1.13\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-translations/} for more info\n\t */\n\tgeodataNames?: { [index: string]: string };\n\n}\n\nexport interface IMapSeriesPrivate extends ISeriesPrivate {\n}\n\nexport interface IMapSeriesEvents extends ISeriesEvents {\n\n\t/**\n\t * Invoked when geodata is finished loading and processed.\n\t */\n\tgeodataprocessed: {};\n\n}\n\n/**\n * Base class for map series.\n */\nexport abstract class MapSeries extends Series {\n\tpublic static className: string = \"MapSeries\";\n\tpublic static classNames: Array<string> = Series.classNames.concat([MapSeries.className]);\n\n\tdeclare public chart: MapChart | undefined;\n\tdeclare public _settings: IMapSeriesSettings;\n\tdeclare public _privateSettings: IMapSeriesPrivate;\n\tdeclare public _dataItemSettings: IMapSeriesDataItem;\n\tdeclare public _events: IMapSeriesEvents;\n\n\tprotected _types: Array<GeoJSON.GeoJsonGeometryTypes> = [];\n\n\tpublic _geometries: Array<GeoJSON.Geometry> = [];\n\tprotected _geoJSONparsed: boolean = false;\n\n\tprotected _excluded: Array<DataItem<IMapSeriesDataItem>> = [];\n\tprotected _notIncluded: Array<DataItem<IMapSeriesDataItem>> = [];\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"geometry\", \"geometryType\");\n\t\tthis._setRawDefault(\"geometryField\", \"geometry\");\n\t\tthis._setRawDefault(\"geometryTypeField\", \"geometryType\");\n\t\tthis._setRawDefault(\"idField\", \"id\");\n\n\t\tthis.on(\"geoJSON\", (geoJSON) => {\n\t\t\tlet previous = this._prevSettings.geoJSON;\n\t\t\tif (previous && previous != geoJSON) {\n\t\t\t\tthis.data.clear();\n\t\t\t}\n\t\t})\n\n\t\tsuper._afterNew();\n\t}\n\n\tprotected _handleDirties() {\n\t\tconst geoJSON = this.get(\"geoJSON\");\n\t\tlet previous = this._prevSettings.geoJSON;\n\n\t\tif (previous && previous != geoJSON) {\n\t\t\tthis._prevSettings.geoJSON = undefined;\n\t\t\tthis._geoJSONparsed = false;\n\t\t}\n\n\t\tif (!this._geoJSONparsed) {\n\t\t\tthis._parseGeoJSON();\n\t\t\tthis._geoJSONparsed = true;\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this._valuesDirty) {\n\t\t\tthis._handleDirties();\n\t\t}\n\n\t\tif (this.get(\"geoJSON\") && (this.isDirty(\"geoJSON\") || this.isDirty(\"include\") || this.isDirty(\"exclude\"))) {\n\n\t\t\tthis._handleDirties();\n\n\t\t\tconst chart = this.chart;\n\n\t\t\tconst exclude = this.get(\"exclude\");\n\n\t\t\tif (exclude) {\n\t\t\t\tif (chart) {\n\t\t\t\t\tchart._centerLocation = null;\n\t\t\t\t}\n\t\t\t\t$array.each(exclude, (id) => {\n\t\t\t\t\tconst dataItem = this.getDataItemById(id);\n\t\t\t\t\tif (dataItem) {\n\t\t\t\t\t\tthis._excludeDataItem(dataItem)\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tif (!exclude || exclude.length == 0) {\n\t\t\t\t$array.each(this._excluded, (dataItem) => {\n\t\t\t\t\tthis._unexcludeDataItem(dataItem)\n\t\t\t\t})\n\t\t\t\tthis._excluded = [];\n\t\t\t}\n\n\t\t\tconst include = this.get(\"include\");\n\t\t\tif (include) {\n\t\t\t\tif (chart) {\n\t\t\t\t\tchart._centerLocation = null;\n\t\t\t\t}\n\t\t\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\t\t\tconst id = dataItem.get(\"id\");\n\t\t\t\t\tif (id && include.indexOf(id) == -1) {\n\t\t\t\t\t\tthis._notIncludeDataItem(dataItem);\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tthis._unNotIncludeDataItem(dataItem);\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tif (!include) {\n\t\t\t\t$array.each(this._notIncluded, (dataItem) => {\n\t\t\t\t\tthis._unNotIncludeDataItem(dataItem);\n\t\t\t\t})\n\t\t\t\tthis._notIncluded = [];\n\t\t\t}\n\n\t\t}\n\t}\n\n\tprotected _excludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tthis._removeGeometry(dataItem.get(\"geometry\"));\n\t\t$array.move(this._excluded, dataItem);\n\t}\n\n\tprotected _unexcludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tthis._addGeometry(dataItem.get(\"geometry\"), this);\n\t}\n\n\tprotected _notIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tthis._removeGeometry(dataItem.get(\"geometry\"));\n\t\t$array.move(this._notIncluded, dataItem);\n\t}\n\n\tprotected _unNotIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tthis._addGeometry(dataItem.get(\"geometry\"), this);\n\t}\n\n\tprotected checkInclude(id: string, includes: string[] | undefined, excludes?: string[] | undefined): boolean {\n\t\tif (includes) {\n\t\t\tif (includes.length == 0) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (includes.indexOf(id) == -1) {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (excludes && excludes.length > 0) {\n\t\t\tif (excludes.indexOf(id) != -1) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t}\n\t\treturn true;\n\t}\n\n\tprotected _parseGeoJSON() {\n\n\t\tconst geoJSON = this.get(\"geoJSON\");\n\t\tif (geoJSON) {\n\n\t\t\tlet features!: any[];\n\n\t\t\tif (geoJSON.type == \"FeatureCollection\") {\n\t\t\t\tfeatures = geoJSON.features;\n\t\t\t}\n\t\t\telse if (geoJSON.type == \"Feature\") {\n\t\t\t\tfeatures = [geoJSON];\n\t\t\t}\n\t\t\telse if ([\"Point\", \"LineString\", \"Polygon\", \"MultiPoint\", \"MultiLineString\", \"MultiPolygon\"].indexOf(geoJSON.type) != -1) {\n\t\t\t\tfeatures = [{ geometry: geoJSON }];\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconsole.log(\"nothing found in geoJSON\");\n\t\t\t}\n\n\t\t\tconst geodataNames = this.get(\"geodataNames\");\n\t\t\tif (features) {\n\n\t\t\t\tconst idField = this.get(\"idField\", \"id\");\n\n\t\t\t\tfor (let i = 0, len = features.length; i < len; i++) {\n\t\t\t\t\tlet feature: any = features[i];\n\t\t\t\t\tlet geometry: any = feature.geometry;\n\n\t\t\t\t\tif (geometry) {\n\t\t\t\t\t\tlet type = geometry.type;\n\t\t\t\t\t\tlet id: string = feature[idField];\n\n\t\t\t\t\t\tif (geodataNames && geodataNames[id]) {\n\t\t\t\t\t\t\tfeature.properties.name = geodataNames[id];\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (this._types.indexOf(type) != -1) {\n\t\t\t\t\t\t\t//if (!this.checkInclude(id, this.get(\"include\"), this.get(\"exclude\"))) {\n\t\t\t\t\t\t\t//\tcontinue;\n\t\t\t\t\t\t\t//}\n\n\t\t\t\t\t\t\tlet dataItem: any;\n\n\t\t\t\t\t\t\tif (id != null) {\n\t\t\t\t\t\t\t\t// find data object in user-provided data\n\t\t\t\t\t\t\t\tdataItem = $array.find(this.dataItems, (value: any) => {\n\t\t\t\t\t\t\t\t\treturn value.get(\"id\") == id;\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tlet dataObject: any;\n\n\t\t\t\t\t\t\tif (dataItem) {\n\t\t\t\t\t\t\t\tdataObject = dataItem.dataContext;\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// create one if not found\n\t\t\t\t\t\t\tif (!dataItem) {\n\t\t\t\t\t\t\t\tdataObject = { geometry: geometry, geometryType: type, madeFromGeoData: true };\n\t\t\t\t\t\t\t\tdataObject[idField] = id;\n\t\t\t\t\t\t\t\tthis.data.push(dataObject);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t// in case found\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\t// if user-provided object doesn't have points data provided in any way:\n\t\t\t\t\t\t\t\tif (!dataObject.geometry) {\n\t\t\t\t\t\t\t\t\tdataObject.geometry = geometry;\n\t\t\t\t\t\t\t\t\tdataObject.geometryType = type;\n\t\t\t\t\t\t\t\t\tdataItem.set(\"geometry\", geometry);\n\t\t\t\t\t\t\t\t\tdataItem.set(\"geometryType\", type);\n\t\t\t\t\t\t\t\t\tthis.processDataItem(dataItem);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t// copy properties data to datacontext\n\t\t\t\t\t\t\t$object.softCopyProperties(feature.properties, dataObject);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst type = \"geodataprocessed\";\n\t\t\tif (this.events.isEnabled(type)) {\n\t\t\t\tthis.events.dispatch(type, { type: type, target: this });\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic abstract markDirtyProjection(): void\n\n\tpublic _placeBulletsContainer(_chart: MapChart) {\n\t\tthis.children.moveValue(this.bulletsContainer);\n\t}\n\n\tpublic _removeBulletsContainer() {\n\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic projection(): GeoProjection | undefined {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\treturn chart.get(\"projection\");\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic geoPath(): GeoPath | undefined {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\treturn chart.getPrivate(\"geoPath\");\n\t\t}\n\t}\n\n\tprotected _addGeometry(geometry: any, series: MapSeries) {\n\t\tif (geometry && series.get(\"affectsBounds\", true)) {\n\t\t\tthis._geometries.push(geometry);\n\n\t\t\tconst chart = this.chart;\n\t\t\tif (chart) {\n\t\t\t\tchart.markDirtyGeometries();\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _removeGeometry(geometry: any) {\n\t\tif (geometry) {\n\t\t\t$array.remove(this._geometries, geometry);\n\n\t\t\tconst chart = this.chart;\n\t\t\tif (chart) {\n\t\t\t\tchart.markDirtyGeometries();\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _dispose() {\n\t\tsuper._dispose();\n\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\tchart.series.removeValue(this);\n\t\t}\n\t}\n\n\tprotected _onDataClear() {\n\t\tsuper._onDataClear();\n\t\tthis._geoJSONparsed = false;\n\t\tthis._markDirtyKey(\"exclude\");\n\t}\t\n}\n", "// https://github.com/python/cpython/blob/a74eea238f5baba15797e2e8b570d153bc8690a7/Modules/mathmodule.c#L1423\nexport class Adder {\n  constructor() {\n    this._partials = new Float64Array(32);\n    this._n = 0;\n  }\n  add(x) {\n    const p = this._partials;\n    let i = 0;\n    for (let j = 0; j < this._n && j < 32; j++) {\n      const y = p[j],\n        hi = x + y,\n        lo = Math.abs(x) < Math.abs(y) ? x - (hi - y) : y - (hi - x);\n      if (lo) p[i++] = lo;\n      x = hi;\n    }\n    p[i] = x;\n    this._n = i + 1;\n    return this;\n  }\n  valueOf() {\n    const p = this._partials;\n    let n = this._n, x, y, lo, hi = 0;\n    if (n > 0) {\n      hi = p[--n];\n      while (n > 0) {\n        x = hi;\n        y = p[--n];\n        hi = x + y;\n        lo = y - (hi - x);\n        if (lo) break;\n      }\n      if (n > 0 && ((lo < 0 && p[n - 1] < 0) || (lo > 0 && p[n - 1] > 0))) {\n        y = lo * 2;\n        x = hi + y;\n        if (y == x - hi) hi = x;\n      }\n    }\n    return hi;\n  }\n}\n\nexport function fsum(values, valueof) {\n  const adder = new Adder();\n  if (valueof === undefined) {\n    for (let value of values) {\n      if (value = +value) {\n        adder.add(value);\n      }\n    }\n  } else {\n    let index = -1;\n    for (let value of values) {\n      if (value = +valueof(value, ++index, values)) {\n        adder.add(value);\n      }\n    }\n  }\n  return +adder;\n}\n\nexport function fcumsum(values, valueof) {\n  const adder = new Adder();\n  let index = -1;\n  return Float64Array.from(values, valueof === undefined\n      ? v => adder.add(+v || 0)\n      : v => adder.add(+valueof(v, ++index, values) || 0)\n  );\n}\n", "export var epsilon = 1e-6;\nexport var epsilon2 = 1e-12;\nexport var pi = Math.PI;\nexport var halfPi = pi / 2;\nexport var quarterPi = pi / 4;\nexport var tau = pi * 2;\n\nexport var degrees = 180 / pi;\nexport var radians = pi / 180;\n\nexport var abs = Math.abs;\nexport var atan = Math.atan;\nexport var atan2 = Math.atan2;\nexport var cos = Math.cos;\nexport var ceil = Math.ceil;\nexport var exp = Math.exp;\nexport var floor = Math.floor;\nexport var hypot = Math.hypot;\nexport var log = Math.log;\nexport var pow = Math.pow;\nexport var sin = Math.sin;\nexport var sign = Math.sign || function(x) { return x > 0 ? 1 : x < 0 ? -1 : 0; };\nexport var sqrt = Math.sqrt;\nexport var tan = Math.tan;\n\nexport function acos(x) {\n  return x > 1 ? 0 : x < -1 ? pi : Math.acos(x);\n}\n\nexport function asin(x) {\n  return x > 1 ? halfPi : x < -1 ? -halfPi : Math.asin(x);\n}\n\nexport function haversin(x) {\n  return (x = sin(x / 2)) * x;\n}\n", "export default function noop() {}\n", "function streamGeometry(geometry, stream) {\n  if (geometry && streamGeometryType.hasOwnProperty(geometry.type)) {\n    streamGeometryType[geometry.type](geometry, stream);\n  }\n}\n\nvar streamObjectType = {\n  Feature: function(object, stream) {\n    streamGeometry(object.geometry, stream);\n  },\n  FeatureCollection: function(object, stream) {\n    var features = object.features, i = -1, n = features.length;\n    while (++i < n) streamGeometry(features[i].geometry, stream);\n  }\n};\n\nvar streamGeometryType = {\n  Sphere: function(object, stream) {\n    stream.sphere();\n  },\n  Point: function(object, stream) {\n    object = object.coordinates;\n    stream.point(object[0], object[1], object[2]);\n  },\n  MultiPoint: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) object = coordinates[i], stream.point(object[0], object[1], object[2]);\n  },\n  LineString: function(object, stream) {\n    streamLine(object.coordinates, stream, 0);\n  },\n  MultiLineString: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamLine(coordinates[i], stream, 0);\n  },\n  Polygon: function(object, stream) {\n    streamPolygon(object.coordinates, stream);\n  },\n  MultiPolygon: function(object, stream) {\n    var coordinates = object.coordinates, i = -1, n = coordinates.length;\n    while (++i < n) streamPolygon(coordinates[i], stream);\n  },\n  GeometryCollection: function(object, stream) {\n    var geometries = object.geometries, i = -1, n = geometries.length;\n    while (++i < n) streamGeometry(geometries[i], stream);\n  }\n};\n\nfunction streamLine(coordinates, stream, closed) {\n  var i = -1, n = coordinates.length - closed, coordinate;\n  stream.lineStart();\n  while (++i < n) coordinate = coordinates[i], stream.point(coordinate[0], coordinate[1], coordinate[2]);\n  stream.lineEnd();\n}\n\nfunction streamPolygon(coordinates, stream) {\n  var i = -1, n = coordinates.length;\n  stream.polygonStart();\n  while (++i < n) streamLine(coordinates[i], stream, 1);\n  stream.polygonEnd();\n}\n\nexport default function(object, stream) {\n  if (object && streamObjectType.hasOwnProperty(object.type)) {\n    streamObjectType[object.type](object, stream);\n  } else {\n    streamGeometry(object, stream);\n  }\n}\n", "import {Adder} from \"d3-array\";\nimport {abs, atan2, cos, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar lengthSum,\n    lambda0,\n    sinPhi0,\n    cosPhi0;\n\nvar lengthStream = {\n  sphere: noop,\n  point: noop,\n  lineStart: lengthLineStart,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop\n};\n\nfunction lengthLineStart() {\n  lengthStream.point = lengthPointFirst;\n  lengthStream.lineEnd = lengthLineEnd;\n}\n\nfunction lengthLineEnd() {\n  lengthStream.point = lengthStream.lineEnd = noop;\n}\n\nfunction lengthPointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, sinPhi0 = sin(phi), cosPhi0 = cos(phi);\n  lengthStream.point = lengthPoint;\n}\n\nfunction lengthPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var sinPhi = sin(phi),\n      cosPhi = cos(phi),\n      delta = abs(lambda - lambda0),\n      cosDelta = cos(delta),\n      sinDelta = sin(delta),\n      x = cosPhi * sinDelta,\n      y = cosPhi0 * sinPhi - sinPhi0 * cosPhi * cosDelta,\n      z = sinPhi0 * sinPhi + cosPhi0 * cosPhi * cosDelta;\n  lengthSum.add(atan2(sqrt(x * x + y * y), z));\n  lambda0 = lambda, sinPhi0 = sinPhi, cosPhi0 = cosPhi;\n}\n\nexport default function(object) {\n  lengthSum = new Adder();\n  stream(object, lengthStream);\n  return +lengthSum;\n}\n", "import length from \"./length.js\";\n\nvar coordinates = [null, null],\n    object = {type: \"LineString\", coordinates: coordinates};\n\nexport default function(a, b) {\n  coordinates[0] = a;\n  coordinates[1] = b;\n  return length(object);\n}\n", "import type { MapLineSeries, IMapLineSeriesDataItem } from \"./MapLineSeries\";\nimport type { IGeoPoint } from \"../../core/util/IGeoPoint\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport * as $type from \"../../core/util/Type\";\nimport { Percent } from \"../../core/util/Percent\";\n\nimport { Graphics, IGraphicsSettings, IGraphicsPrivate, IGraphicsEvents } from \"../../core/render/Graphics\";\nimport { geoLength, geoInterpolate, geoDistance } from \"d3-geo\";\n\nexport interface IMapLineSettings extends IGraphicsSettings {\n\n\t/**\n\t * A GeoJSON representation of the polygons geometry.\n\t */\n\tgeometry?: GeoJSON.LineString | GeoJSON.MultiLineString;\n\n\t/**\n\t * @todo needs description\n\t * @default 0.5\n\t */\n\tprecision?: number;\n}\n\nexport interface IMapLinePrivate extends IGraphicsPrivate {\n\n\t/**\n\t * @ignore\n\t */\n\tseries: MapLineSeries;\n}\n\nexport interface IMapLineEvents extends IGraphicsEvents {\n\n\t/**\n\t * Invoked when line is redrawn\n\t */\n\tlinechanged: {};\n}\n\n/**\n * A line object in a [[MapLineSeries]].\n */\nexport class MapLine extends Graphics {\n\n\tdeclare public _settings: IMapLineSettings;\n\tdeclare public _privateSettings: IMapLinePrivate;\n\tdeclare public _events: IMapLineEvents;\n\n\tpublic static className: string = \"MapLine\";\n\tpublic static classNames: Array<string> = Graphics.classNames.concat([MapLine.className]);\n\tprotected _projectionDirty: boolean = false;\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this._projectionDirty || this.isDirty(\"geometry\") || this.isDirty(\"precision\")) {\n\t\t\tconst geometry = this.get(\"geometry\")!;\n\t\t\tif (geometry) {\n\t\t\t\tconst series = this.getPrivate(\"series\");\n\t\t\t\tif (series) {\n\t\t\t\t\tconst chart = series.chart;\n\t\t\t\t\tif (chart) {\n\t\t\t\t\t\tconst projection = chart.get(\"projection\");\n\t\t\t\t\t\tlet clipAngle: number | null = null;\n\n\t\t\t\t\t\tif (projection && projection.clipAngle) {\n\t\t\t\t\t\t\tclipAngle = projection.clipAngle();\n\t\t\t\t\t\t\tprojection.precision(this.get(\"precision\", 0.5));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tconst dataItem = this.dataItem as DataItem<IMapLineSeriesDataItem>;\n\t\t\t\t\t\tconst geoPath = chart.getPrivate(\"geoPath\");\n\t\t\t\t\t\tif (geoPath && dataItem) {\n\t\t\t\t\t\t\tthis._clear = true;\n\t\t\t\t\t\t\tif (dataItem.get(\"lineType\", series.get(\"lineType\")) == \"straight\") {\n\n\t\t\t\t\t\t\t\tconst geometry = this.get(\"geometry\")!;\n\n\t\t\t\t\t\t\t\tif (geometry) {\n\t\t\t\t\t\t\t\t\tlet coordinates = geometry.coordinates;\n\t\t\t\t\t\t\t\t\tif (coordinates) {\n\n\t\t\t\t\t\t\t\t\t\tlet segments!: number[][][];\n\n\t\t\t\t\t\t\t\t\t\tif (geometry.type == \"LineString\") {\n\t\t\t\t\t\t\t\t\t\t\tsegments = [coordinates] as number[][][];\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\telse if (geometry.type == \"MultiLineString\") {\n\t\t\t\t\t\t\t\t\t\t\tsegments = coordinates as number[][][];\n\t\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\t\tthis.set(\"draw\", (display) => {\n\t\t\t\t\t\t\t\t\t\t\tfor (let s = 0; s < segments.length; s++) {\n\t\t\t\t\t\t\t\t\t\t\t\tlet segment = segments[s];\n\t\t\t\t\t\t\t\t\t\t\t\tif (segment.length > 0) {\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst gp0 = segment[0];\n\t\t\t\t\t\t\t\t\t\t\t\t\tconst p0 = chart.convert({ longitude: gp0[0], latitude: gp0[1] })\n\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay.lineTo(p0.x, p0.y);\n\n\t\t\t\t\t\t\t\t\t\t\t\t\tfor (let p = 0; p < segment.length; p++) {\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst gp = segment[p];\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tconst pn = chart.convert({ longitude: gp[0], latitude: gp[1] })\n\t\t\t\t\t\t\t\t\t\t\t\t\t\tdisplay.lineTo(pn.x, pn.y);\n\t\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\telse {\n\t\t\t\t\t\t\t\tthis.set(\"draw\", (_display) => {\n\t\t\t\t\t\t\t\t\tif (projection && series.get(\"clipBack\") === false) {\n\t\t\t\t\t\t\t\t\t\tprojection.clipAngle(180);\n\t\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\t\tgeoPath.context(this._display as any);\n\t\t\t\t\t\t\t\t\tgeoPath(geometry);\n\t\t\t\t\t\t\t\t\tgeoPath.context(null);\n\n\t\t\t\t\t\t\t\t\tif (projection && projection.clipAngle) {\n\t\t\t\t\t\t\t\t\t\tprojection.clipAngle(clipAngle as any);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tconst type = \"linechanged\";\n\t\t\tif (this.events.isEnabled(type)) {\n\t\t\t\tthis.events.dispatch(type, { type: type, target: this });\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\tthis.markDirty();\n\t\tthis._projectionDirty = true;\n\t}\n\n\tpublic _clearDirty() {\n\t\tsuper._clearDirty();\n\t\tthis._projectionDirty = false;\n\t}\n\n\tpublic _getTooltipPoint(): IPoint {\n\t\tlet tooltipX = this.get(\"tooltipX\");\n\t\tlet tooltipY = this.get(\"tooltipY\");\n\n\t\tlet x = 0;\n\t\tlet y = 0;\n\n\t\tif ($type.isNumber(tooltipX)) {\n\t\t\tx = tooltipX;\n\t\t}\n\n\t\tif ($type.isNumber(tooltipY)) {\n\t\t\ty = tooltipY;\n\t\t}\n\n\t\tif (tooltipX instanceof Percent) {\n\t\t\tconst geoPoint = this.positionToGeoPoint(tooltipX.value)\n\t\t\tconst series = this.getPrivate(\"series\");\n\t\t\tif (series) {\n\t\t\t\tconst chart = series.chart;\n\t\t\t\tif (chart) {\n\t\t\t\t\tconst point = chart.convert(geoPoint);\n\t\t\t\t\tx = point.x;\n\t\t\t\t\ty = point.y;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\t\n\t\treturn { x, y };\n\t}\n\n\t/**\n\t * Converts relative position along the line (0-1) into pixel coordinates.\n\t *\n\t * @param position  Position (0-1)\n\t * @return Coordinates\n\t */\n\tpublic positionToGeoPoint(position: number): IGeoPoint {\n\n\t\tconst geometry = this.get(\"geometry\")!;\n\t\tconst series = this.getPrivate(\"series\");\n\t\tconst chart = series.chart;\n\t\tconst dataItem = this.dataItem as DataItem<IMapLineSeriesDataItem>;\n\n\t\tif (geometry && series && chart && dataItem) {\n\t\t\tconst lineType = dataItem.get(\"lineType\", series.get(\"lineType\"));\n\t\t\tlet totalDistance: number = geoLength(geometry);\n\t\t\tlet currentDistance: number = 0;\n\n\t\t\tlet distanceAB: number;\n\t\t\tlet positionA: number = 0;\n\t\t\tlet positionB: number = 0;\n\t\t\tlet pointA!: [number, number];\n\t\t\tlet pointB!: [number, number];\n\n\t\t\tlet coordinates = geometry.coordinates;\n\t\t\tif (coordinates) {\n\t\t\t\tlet segments!: number[][][];\n\n\t\t\t\tif (geometry.type == \"LineString\") {\n\t\t\t\t\tsegments = [coordinates] as number[][][];\n\t\t\t\t}\n\t\t\t\telse if (geometry.type == \"MultiLineString\") {\n\t\t\t\t\tsegments = coordinates as number[][][];\n\t\t\t\t}\n\n\t\t\t\tfor (let s = 0; s < segments.length; s++) {\n\t\t\t\t\tlet segment = segments[s];\n\t\t\t\t\tif (segment.length > 1) {\n\t\t\t\t\t\tfor (let p = 1; p < segment.length; p++) {\n\t\t\t\t\t\t\tpointA = segment[p - 1] as [number, number];\n\t\t\t\t\t\t\tpointB = segment[p] as [number, number];\n\n\t\t\t\t\t\t\tpositionA = currentDistance / totalDistance;\n\t\t\t\t\t\t\tdistanceAB = geoDistance(pointA, pointB);\n\t\t\t\t\t\t\tcurrentDistance += distanceAB;\n\t\t\t\t\t\t\tpositionB = currentDistance / totalDistance;\n\n\t\t\t\t\t\t\tif (positionA <= position && positionB > position) {\n\t\t\t\t\t\t\t\ts = segments.length;\n\t\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse if (segment.length == 1) {\n\t\t\t\t\t\tpointA = segment[0] as [number, number];\n\t\t\t\t\t\tpointB = segment[0] as [number, number];\n\t\t\t\t\t\tpositionA = 0;\n\t\t\t\t\t\tpositionB = 1;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (pointA && pointB) {\n\t\t\t\t\tlet positionAB: number = (position - positionA) / (positionB - positionA);\n\t\t\t\t\tlet location: number[];\n\n\t\t\t\t\tif (lineType == \"straight\") {\n\t\t\t\t\t\tlet p0 = chart.convert({ longitude: pointA[0], latitude: pointA[1] });\n\t\t\t\t\t\tlet p1 = chart.convert({ longitude: pointB[0], latitude: pointB[1] });\n\n\t\t\t\t\t\tlet x = p0.x + (p1.x - p0.x) * positionAB;\n\t\t\t\t\t\tlet y = p0.y + (p1.y - p0.y) * positionAB;\n\n\t\t\t\t\t\treturn chart.invert({ x: x, y: y });\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tlocation = geoInterpolate(pointA, pointB)(positionAB);\n\t\t\t\t\t\treturn { longitude: location[0], latitude: location[1] }\n\t\t\t\t\t}\n\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn { longitude: 0, latitude: 0 };\n\t}\n}\n", "import {asin, atan2, cos, degrees, haversin, radians, sin, sqrt} from \"./math.js\";\n\nexport default function(a, b) {\n  var x0 = a[0] * radians,\n      y0 = a[1] * radians,\n      x1 = b[0] * radians,\n      y1 = b[1] * radians,\n      cy0 = cos(y0),\n      sy0 = sin(y0),\n      cy1 = cos(y1),\n      sy1 = sin(y1),\n      kx0 = cy0 * cos(x0),\n      ky0 = cy0 * sin(x0),\n      kx1 = cy1 * cos(x1),\n      ky1 = cy1 * sin(x1),\n      d = 2 * asin(sqrt(haversin(y1 - y0) + cy0 * cy1 * haversin(x1 - x0))),\n      k = sin(d);\n\n  var interpolate = d ? function(t) {\n    var B = sin(t *= d) / k,\n        A = sin(d - t) / k,\n        x = A * kx0 + B * kx1,\n        y = A * ky0 + B * ky1,\n        z = A * sy0 + B * sy1;\n    return [\n      atan2(y, x) * degrees,\n      atan2(z, sqrt(x * x + y * y)) * degrees\n    ];\n  } : function() {\n    return [x0 * degrees, y0 * degrees];\n  };\n\n  interpolate.distance = d;\n\n  return interpolate;\n}\n", "import type { IMapPointSeriesDataItem } from \"./MapPointSeries\";\nimport type { DataItem } from \"../../core/render/Component\";\n\nimport { MapSeries, IMapSeriesSettings, IMapSeriesDataItem, IMapSeriesPrivate } from \"./MapSeries\";\nimport { MapLine } from \"./MapLine\";\nimport { ListTemplate } from \"../../core/util/List\";\nimport { Template } from \"../../core/util/Template\";\n\nimport * as $array from \"../../core/util/Array\";\n\n/**\n * @ignore\n */\nexport interface IMapLineSeriesPrivate extends IMapSeriesPrivate {\n}\n\nexport interface IMapLineSeriesDataItem extends IMapSeriesDataItem {\n\n\t/**\n\t * Related [[MapLine]] object.\n\t */\n\tmapLine?: MapLine;\n\n\t/**\n\t * GeoJSON geometry of the line.\n\t */\n\tgeometry?: GeoJSON.LineString | GeoJSON.MultiLineString;\n\n\t/**\n\t * An array of data items from [[MapPointSeries]] to use as line end-points. Note, fixed points can not be used here.\n\t */\n\tpointsToConnect?: Array<DataItem<IMapPointSeriesDataItem>>;\n\n\t/**\n\t * A line type.\n\t *\n\t * * `\"curved\"` (default) - connects points using shortest distance, which will result in curved lines based on map projection.\n\t * * `\"straight\"` - connects points using visually straight lines, and will not cross the -180/180 longitude.\n\t * \n\t * @default \"curved\"\n\t * @since 5.2.32\n\t */\n\tlineType?: \"curved\" | \"straight\"\n\n}\n\nexport interface IMapLineSeriesSettings extends IMapSeriesSettings {\n\n\t/**\n\t * If set to `true` will hide line segments that are in the invisible range\n\t * of the map.\n\t *\n\t * For example on the side of the globe facing away from the viewer when\n\t * used with Orthographic projection.\n\t *\n\t * NOTE: not all projections have invisible side.\n\t */\n\tclipBack?: boolean;\n\n\t/**\n\t * A line type.\n\t *\n\t * * `\"curved\"` (default) - connects points using shortest distance, which will result in curved lines based on map projection.\n\t * * `\"straight\"` - connects points using visually straight lines, and will not cross the -180/180 longitude.\n\t * \n\t * @default \"curved\"\n\t * @since 5.2.24\n\t */\n\tlineType?: \"curved\" | \"straight\"\n\n\n\t/**\n\t * @ignore\n\t */\n\tlineTypeField?: string;\n}\n\n/**\n * Creates a map series for displaying lines on the map.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-line-series/} for more info\n * @important\n */\nexport class MapLineSeries extends MapSeries {\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"lineType\");\n\t\tthis._setRawDefault(\"lineTypeField\", \"lineType\");\n\t\tsuper._afterNew();\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeMapLine(dataItem: DataItem<this[\"_dataItemSettings\"]>): MapLine {\n\t\tconst mapLine = this.children.push(this.mapLines.make());\n\t\tmapLine._setDataItem(dataItem);\n\t\tthis.mapLines.push(mapLine);\n\t\treturn mapLine;\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all lines in series.\n\t *\n\t * `mapLines.template` can also be used to configure lines.\n\t *\n\t * @default new ListTemplate<MapLine>\n\t */\n\tpublic readonly mapLines: ListTemplate<MapLine> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => MapLine._new(this._root, {}, [this.mapLines.template])\n\t);\n\n\tpublic static className: string = \"MapLineSeries\";\n\tpublic static classNames: Array<string> = MapSeries.classNames.concat([MapLineSeries.className]);\n\n\tdeclare public _settings: IMapLineSeriesSettings;\n\tdeclare public _privateSettings: IMapLineSeriesPrivate;\n\tdeclare public _dataItemSettings: IMapLineSeriesDataItem;\n\n\tprotected _types: Array<GeoJSON.GeoJsonGeometryTypes> = [\"LineString\", \"MultiLineString\"];\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\tlet mapLine = dataItem.get(\"mapLine\");\n\t\t\tif (mapLine) {\n\t\t\t\tmapLine.markDirtyProjection();\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"stroke\")) {\n\t\t\tthis.mapLines.template.set(\"stroke\", this.get(\"stroke\"));\n\t\t}\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tlet mapLine = dataItem.get(\"mapLine\");\n\t\tif (!mapLine) {\n\t\t\tmapLine = this.makeMapLine(dataItem);\n\t\t}\n\n\t\tthis._handlePointsToConnect(dataItem);\n\t\tdataItem.on(\"pointsToConnect\", () => {\n\t\t\tthis._handlePointsToConnect(dataItem);\n\t\t})\n\n\t\tdataItem.set(\"mapLine\", mapLine);\n\n\t\tthis._addGeometry(dataItem.get(\"geometry\"), this);\n\n\t\tmapLine.setPrivate(\"series\", this);\n\t}\n\n\tprotected _handlePointsToConnect(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\n\t\tconst pointsToConnect = dataItem.get(\"pointsToConnect\");\n\t\tif (pointsToConnect) {\n\t\t\t$array.each(pointsToConnect, (point) => {\n\n\t\t\t\tpoint.on(\"geometry\", () => {\n\t\t\t\t\tthis.markDirtyValues(dataItem);\n\t\t\t\t})\n\n\t\t\t\tpoint.on(\"longitude\", () => {\n\t\t\t\t\tthis.markDirtyValues(dataItem);\n\t\t\t\t})\n\n\t\t\t\tpoint.on(\"latitude\", () => {\n\t\t\t\t\tthis.markDirtyValues(dataItem);\n\t\t\t\t})\n\t\t\t})\n\n\t\t\tthis.markDirtyValues(dataItem);\n\t\t}\n\t}\n\n\t/**\n\t * Forces a repaint of the element which relies on data.\n\t *\n\t * @since 5.0.21\n\t */\n\tpublic markDirtyValues(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.markDirtyValues();\n\t\tif (dataItem) {\n\t\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\t\tif (mapLine) {\n\t\t\t\tconst pointsToConnect = dataItem.get(\"pointsToConnect\");\n\t\t\t\tif (pointsToConnect) {\n\t\t\t\t\tlet coordinates: Array<Array<number>> = [];\n\t\t\t\t\t$array.each(pointsToConnect, (point) => {\n\t\t\t\t\t\tconst longitude = point.get(\"longitude\");\n\t\t\t\t\t\tconst latitude = point.get(\"latitude\");\n\t\t\t\t\t\tif (longitude != null && latitude != null) {\n\t\t\t\t\t\t\tcoordinates.push([longitude, latitude]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tconst geometry = point.get(\"geometry\");\n\t\t\t\t\t\t\tif (geometry) {\n\t\t\t\t\t\t\t\tconst coords = geometry.coordinates;\n\t\t\t\t\t\t\t\tif (coords) {\n\t\t\t\t\t\t\t\t\tcoordinates.push([coords[0] as any, coords[1] as any]);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tlet geometry: any = { type: \"LineString\", coordinates: coordinates };\n\n\t\t\t\t\tdataItem.setRaw(\"geometry\", geometry);\n\t\t\t\t\tmapLine.set(\"geometry\", geometry);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tmapLine.set(\"geometry\", dataItem.get(\"geometry\"));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\tif (mapLine) {\n\t\t\tthis.mapLines.removeValue(mapLine);\n\t\t\tmapLine.dispose();\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _excludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._excludeDataItem(dataItem);\n\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\tif (mapLine) {\n\t\t\tmapLine.setPrivate(\"visible\", false);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unexcludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unexcludeDataItem(dataItem);\n\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\tif (mapLine) {\n\t\t\tmapLine.setPrivate(\"visible\", true);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _notIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._notIncludeDataItem(dataItem);\n\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\tif (mapLine) {\n\t\t\tmapLine.setPrivate(\"visible\", false);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unNotIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unNotIncludeDataItem(dataItem);\n\t\tconst mapLine = dataItem.get(\"mapLine\");\n\t\tif (mapLine) {\n\t\t\tmapLine.setPrivate(\"visible\", true);\n\t\t}\n\t}\n}\n", "export default function range(start, stop, step) {\n  start = +start, stop = +stop, step = (n = arguments.length) < 2 ? (stop = start, start = 0, 1) : n < 3 ? 1 : +step;\n\n  var i = -1,\n      n = Math.max(0, Math.ceil((stop - start) / step)) | 0,\n      range = new Array(n);\n\n  while (++i < n) {\n    range[i] = start + i * step;\n  }\n\n  return range;\n}\n", "import {range} from \"d3-array\";\nimport {abs, ceil, epsilon} from \"./math.js\";\n\nfunction graticuleX(y0, y1, dy) {\n  var y = range(y0, y1 - epsilon, dy).concat(y1);\n  return function(x) { return y.map(function(y) { return [x, y]; }); };\n}\n\nfunction graticuleY(x0, x1, dx) {\n  var x = range(x0, x1 - epsilon, dx).concat(x1);\n  return function(y) { return x.map(function(x) { return [x, y]; }); };\n}\n\nexport default function graticule() {\n  var x1, x0, X1, X0,\n      y1, y0, Y1, Y0,\n      dx = 10, dy = dx, DX = 90, DY = 360,\n      x, y, X, Y,\n      precision = 2.5;\n\n  function graticule() {\n    return {type: \"MultiLineString\", coordinates: lines()};\n  }\n\n  function lines() {\n    return range(ceil(X0 / DX) * DX, X1, DX).map(X)\n        .concat(range(ceil(Y0 / DY) * DY, Y1, DY).map(Y))\n        .concat(range(ceil(x0 / dx) * dx, x1, dx).filter(function(x) { return abs(x % DX) > epsilon; }).map(x))\n        .concat(range(ceil(y0 / dy) * dy, y1, dy).filter(function(y) { return abs(y % DY) > epsilon; }).map(y));\n  }\n\n  graticule.lines = function() {\n    return lines().map(function(coordinates) { return {type: \"LineString\", coordinates: coordinates}; });\n  };\n\n  graticule.outline = function() {\n    return {\n      type: \"Polygon\",\n      coordinates: [\n        X(X0).concat(\n        Y(Y1).slice(1),\n        X(X1).reverse().slice(1),\n        Y(Y0).reverse().slice(1))\n      ]\n    };\n  };\n\n  graticule.extent = function(_) {\n    if (!arguments.length) return graticule.extentMinor();\n    return graticule.extentMajor(_).extentMinor(_);\n  };\n\n  graticule.extentMajor = function(_) {\n    if (!arguments.length) return [[X0, Y0], [X1, Y1]];\n    X0 = +_[0][0], X1 = +_[1][0];\n    Y0 = +_[0][1], Y1 = +_[1][1];\n    if (X0 > X1) _ = X0, X0 = X1, X1 = _;\n    if (Y0 > Y1) _ = Y0, Y0 = Y1, Y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.extentMinor = function(_) {\n    if (!arguments.length) return [[x0, y0], [x1, y1]];\n    x0 = +_[0][0], x1 = +_[1][0];\n    y0 = +_[0][1], y1 = +_[1][1];\n    if (x0 > x1) _ = x0, x0 = x1, x1 = _;\n    if (y0 > y1) _ = y0, y0 = y1, y1 = _;\n    return graticule.precision(precision);\n  };\n\n  graticule.step = function(_) {\n    if (!arguments.length) return graticule.stepMinor();\n    return graticule.stepMajor(_).stepMinor(_);\n  };\n\n  graticule.stepMajor = function(_) {\n    if (!arguments.length) return [DX, DY];\n    DX = +_[0], DY = +_[1];\n    return graticule;\n  };\n\n  graticule.stepMinor = function(_) {\n    if (!arguments.length) return [dx, dy];\n    dx = +_[0], dy = +_[1];\n    return graticule;\n  };\n\n  graticule.precision = function(_) {\n    if (!arguments.length) return precision;\n    precision = +_;\n    x = graticuleX(y0, y1, 90);\n    y = graticuleY(x0, x1, precision);\n    X = graticuleX(Y0, Y1, 90);\n    Y = graticuleY(X0, X1, precision);\n    return graticule;\n  };\n\n  return graticule\n      .extentMajor([[-180, -90 + epsilon], [180, 90 - epsilon]])\n      .extentMinor([[-180, -80 - epsilon], [180, 80 + epsilon]]);\n}\n\nexport function graticule10() {\n  return graticule()();\n}\n", "import { MapLineSeries, IMapLineSeriesSettings, IMapLineSeriesPrivate, IMapLineSeriesDataItem } from \"./MapLineSeries\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport { geoGraticule } from \"d3-geo\";\n\nexport interface IGraticuleSeriesDataItem extends IMapLineSeriesDataItem {\n}\n\nexport interface IGraticuleSeriesPrivate extends IMapLineSeriesPrivate {\n}\n\nexport interface IGraticuleSeriesSettings extends IMapLineSeriesSettings {\n\tclipExtent?: boolean;\n\n\t/**\n\t * Place a grid line every Xth latitude/longitude.\n\t *\n\t * @default 10\n\t */\n\tstep?: number;\n}\n\n/**\n * A [[MapChart]] series to draw a map grid.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/graticule-series/} for more info\n * @important\n */\nexport class GraticuleSeries extends MapLineSeries {\n\n\tpublic static className: string = \"GraticuleSeries\";\n\tpublic static classNames: Array<string> = MapLineSeries.classNames.concat([GraticuleSeries.className]);\n\n\tdeclare public _settings: IGraticuleSeriesSettings;\n\tdeclare public _privateSettings: IGraticuleSeriesPrivate;\n\n\tprotected _dataItem: DataItem<this[\"_dataItemSettings\"]> = this.makeDataItem({});\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.dataItems.push(this._dataItem);\n\t\tthis._generate();\n\t}\n\n\tpublic _updateChildren() {\n\t\tsuper._updateChildren();\n\n\t\tif (this.isDirty(\"step\")) {\n\t\t\tthis._generate();\n\t\t}\n\n\t\tif (this.isDirty(\"clipExtent\")) {\n\n\t\t\tif (this.get(\"clipExtent\")) {\n\t\t\t\tconst chart = this.chart;\n\t\t\t\tif (chart) {\n\t\t\t\t\tchart.events.on(\"geoboundschanged\", () => {\n\t\t\t\t\t\tthis._generate();\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tthis._generate();\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _generate() {\n\t\tlet graticule = geoGraticule();\n\n\t\tif (graticule) {\n\t\t\tif (this.get(\"clipExtent\")) {\n\t\t\t\tconst chart = this.chart;\n\t\t\t\tif (chart) {\n\t\t\t\t\tconst geoBounds = chart.geoBounds();\n\t\t\t\t\tif (geoBounds) {\n\t\t\t\t\t\tgraticule.extent([[geoBounds.left, geoBounds.bottom], [geoBounds.right, geoBounds.top]]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst step = this.get(\"step\", 10);\n\t\t\tgraticule.stepMinor([360, 360]);\n\t\t\tgraticule.stepMajor([step, step]);\n\t\t\tthis._dataItem.set(\"geometry\", graticule());\n\t\t}\n\t}\n}\n", "export default function(a, b) {\n\n  function compose(x, y) {\n    return x = a(x, y), b(x[0], x[1]);\n  }\n\n  if (a.invert && b.invert) compose.invert = function(x, y) {\n    return x = b.invert(x, y), x && a.invert(x[0], x[1]);\n  };\n\n  return compose;\n}\n", "import compose from \"./compose.js\";\nimport {abs, asin, atan2, cos, degrees, pi, radians, sin, tau} from \"./math.js\";\n\nfunction rotationIdentity(lambda, phi) {\n  if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n  return [lambda, phi];\n}\n\nrotationIdentity.invert = rotationIdentity;\n\nexport function rotateRadians(deltaLambda, deltaPhi, deltaGamma) {\n  return (deltaLambda %= tau) ? (deltaPhi || deltaGamma ? compose(rotationLambda(deltaLambda), rotationPhiGamma(deltaPhi, deltaGamma))\n    : rotationLambda(deltaLambda))\n    : (deltaPhi || deltaGamma ? rotationPhiGamma(deltaPhi, deltaGamma)\n    : rotationIdentity);\n}\n\nfunction forwardRotationLambda(deltaLambda) {\n  return function(lambda, phi) {\n    lambda += deltaLambda;\n    if (abs(lambda) > pi) lambda -= Math.round(lambda / tau) * tau;\n    return [lambda, phi];\n  };\n}\n\nfunction rotationLambda(deltaLambda) {\n  var rotation = forwardRotationLambda(deltaLambda);\n  rotation.invert = forwardRotationLambda(-deltaLambda);\n  return rotation;\n}\n\nfunction rotationPhiGamma(deltaPhi, deltaGamma) {\n  var cosDeltaPhi = cos(deltaPhi),\n      sinDeltaPhi = sin(deltaPhi),\n      cosDeltaGamma = cos(deltaGamma),\n      sinDeltaGamma = sin(deltaGamma);\n\n  function rotation(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaPhi + x * sinDeltaPhi;\n    return [\n      atan2(y * cosDeltaGamma - k * sinDeltaGamma, x * cosDeltaPhi - z * sinDeltaPhi),\n      asin(k * cosDeltaGamma + y * sinDeltaGamma)\n    ];\n  }\n\n  rotation.invert = function(lambda, phi) {\n    var cosPhi = cos(phi),\n        x = cos(lambda) * cosPhi,\n        y = sin(lambda) * cosPhi,\n        z = sin(phi),\n        k = z * cosDeltaGamma - y * sinDeltaGamma;\n    return [\n      atan2(y * cosDeltaGamma + z * sinDeltaGamma, x * cosDeltaPhi + k * sinDeltaPhi),\n      asin(k * cosDeltaPhi - x * sinDeltaPhi)\n    ];\n  };\n\n  return rotation;\n}\n\nexport default function(rotate) {\n  rotate = rotateRadians(rotate[0] * radians, rotate[1] * radians, rotate.length > 2 ? rotate[2] * radians : 0);\n\n  function forward(coordinates) {\n    coordinates = rotate(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  }\n\n  forward.invert = function(coordinates) {\n    coordinates = rotate.invert(coordinates[0] * radians, coordinates[1] * radians);\n    return coordinates[0] *= degrees, coordinates[1] *= degrees, coordinates;\n  };\n\n  return forward;\n}\n", "import noop from \"../noop.js\";\n\nexport default function() {\n  var lines = [],\n      line;\n  return {\n    point: function(x, y, m) {\n      line.push([x, y, m]);\n    },\n    lineStart: function() {\n      lines.push(line = []);\n    },\n    lineEnd: noop,\n    rejoin: function() {\n      if (lines.length > 1) lines.push(lines.pop().concat(lines.shift()));\n    },\n    result: function() {\n      var result = lines;\n      lines = [];\n      line = null;\n      return result;\n    }\n  };\n}\n", "import {abs, epsilon} from \"./math.js\";\n\nexport default function(a, b) {\n  return abs(a[0] - b[0]) < epsilon && abs(a[1] - b[1]) < epsilon;\n}\n", "import pointEqual from \"../pointEqual.js\";\nimport {epsilon} from \"../math.js\";\n\nfunction Intersection(point, points, other, entry) {\n  this.x = point;\n  this.z = points;\n  this.o = other; // another intersection\n  this.e = entry; // is an entry?\n  this.v = false; // visited\n  this.n = this.p = null; // next & previous\n}\n\n// A generalized polygon clipping algorithm: given a polygon that has been cut\n// into its visible line segments, and rejoins the segments by interpolating\n// along the clip edge.\nexport default function(segments, compareIntersection, startInside, interpolate, stream) {\n  var subject = [],\n      clip = [],\n      i,\n      n;\n\n  segments.forEach(function(segment) {\n    if ((n = segment.length - 1) <= 0) return;\n    var n, p0 = segment[0], p1 = segment[n], x;\n\n    if (pointEqual(p0, p1)) {\n      if (!p0[2] && !p1[2]) {\n        stream.lineStart();\n        for (i = 0; i < n; ++i) stream.point((p0 = segment[i])[0], p0[1]);\n        stream.lineEnd();\n        return;\n      }\n      // handle degenerate cases by moving the point\n      p1[0] += 2 * epsilon;\n    }\n\n    subject.push(x = new Intersection(p0, segment, null, true));\n    clip.push(x.o = new Intersection(p0, null, x, false));\n    subject.push(x = new Intersection(p1, segment, null, false));\n    clip.push(x.o = new Intersection(p1, null, x, true));\n  });\n\n  if (!subject.length) return;\n\n  clip.sort(compareIntersection);\n  link(subject);\n  link(clip);\n\n  for (i = 0, n = clip.length; i < n; ++i) {\n    clip[i].e = startInside = !startInside;\n  }\n\n  var start = subject[0],\n      points,\n      point;\n\n  while (1) {\n    // Find first unvisited intersection.\n    var current = start,\n        isSubject = true;\n    while (current.v) if ((current = current.n) === start) return;\n    points = current.z;\n    stream.lineStart();\n    do {\n      current.v = current.o.v = true;\n      if (current.e) {\n        if (isSubject) {\n          for (i = 0, n = points.length; i < n; ++i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.n.x, 1, stream);\n        }\n        current = current.n;\n      } else {\n        if (isSubject) {\n          points = current.p.z;\n          for (i = points.length - 1; i >= 0; --i) stream.point((point = points[i])[0], point[1]);\n        } else {\n          interpolate(current.x, current.p.x, -1, stream);\n        }\n        current = current.p;\n      }\n      current = current.o;\n      points = current.z;\n      isSubject = !isSubject;\n    } while (!current.v);\n    stream.lineEnd();\n  }\n}\n\nfunction link(array) {\n  if (!(n = array.length)) return;\n  var n,\n      i = 0,\n      a = array[0],\n      b;\n  while (++i < n) {\n    a.n = b = array[i];\n    b.p = a;\n    a = b;\n  }\n  a.n = b = array[0];\n  b.p = a;\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"./math.js\";\n\nexport function spherical(cartesian) {\n  return [atan2(cartesian[1], cartesian[0]), asin(cartesian[2])];\n}\n\nexport function cartesian(spherical) {\n  var lambda = spherical[0], phi = spherical[1], cosPhi = cos(phi);\n  return [cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi)];\n}\n\nexport function cartesianDot(a, b) {\n  return a[0] * b[0] + a[1] * b[1] + a[2] * b[2];\n}\n\nexport function cartesianCross(a, b) {\n  return [a[1] * b[2] - a[2] * b[1], a[2] * b[0] - a[0] * b[2], a[0] * b[1] - a[1] * b[0]];\n}\n\n// TODO return a\nexport function cartesianAddInPlace(a, b) {\n  a[0] += b[0], a[1] += b[1], a[2] += b[2];\n}\n\nexport function cartesianScale(vector, k) {\n  return [vector[0] * k, vector[1] * k, vector[2] * k];\n}\n\n// TODO return d\nexport function cartesianNormalizeInPlace(d) {\n  var l = sqrt(d[0] * d[0] + d[1] * d[1] + d[2] * d[2]);\n  d[0] /= l, d[1] /= l, d[2] /= l;\n}\n", "import {<PERSON>der} from \"d3-array\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace} from \"./cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, epsilon2, halfPi, pi, quarterPi, sign, sin, tau} from \"./math.js\";\n\nfunction longitude(point) {\n  return abs(point[0]) <= pi ? point[0] : sign(point[0]) * ((abs(point[0]) + pi) % tau - pi);\n}\n\nexport default function(polygon, point) {\n  var lambda = longitude(point),\n      phi = point[1],\n      sinPhi = sin(phi),\n      normal = [sin(lambda), -cos(lambda), 0],\n      angle = 0,\n      winding = 0;\n\n  var sum = new Adder();\n\n  if (sinPhi === 1) phi = halfPi + epsilon;\n  else if (sinPhi === -1) phi = -halfPi - epsilon;\n\n  for (var i = 0, n = polygon.length; i < n; ++i) {\n    if (!(m = (ring = polygon[i]).length)) continue;\n    var ring,\n        m,\n        point0 = ring[m - 1],\n        lambda0 = longitude(point0),\n        phi0 = point0[1] / 2 + quarterPi,\n        sinPhi0 = sin(phi0),\n        cosPhi0 = cos(phi0);\n\n    for (var j = 0; j < m; ++j, lambda0 = lambda1, sinPhi0 = sinPhi1, cosPhi0 = cosPhi1, point0 = point1) {\n      var point1 = ring[j],\n          lambda1 = longitude(point1),\n          phi1 = point1[1] / 2 + quarterPi,\n          sinPhi1 = sin(phi1),\n          cosPhi1 = cos(phi1),\n          delta = lambda1 - lambda0,\n          sign = delta >= 0 ? 1 : -1,\n          absDelta = sign * delta,\n          antimeridian = absDelta > pi,\n          k = sinPhi0 * sinPhi1;\n\n      sum.add(atan2(k * sign * sin(absDelta), cosPhi0 * cosPhi1 + k * cos(absDelta)));\n      angle += antimeridian ? delta + sign * tau : delta;\n\n      // Are the longitudes either side of the point’s meridian (lambda),\n      // and are the latitudes smaller than the parallel (phi)?\n      if (antimeridian ^ lambda0 >= lambda ^ lambda1 >= lambda) {\n        var arc = cartesianCross(cartesian(point0), cartesian(point1));\n        cartesianNormalizeInPlace(arc);\n        var intersection = cartesianCross(normal, arc);\n        cartesianNormalizeInPlace(intersection);\n        var phiArc = (antimeridian ^ delta >= 0 ? -1 : 1) * asin(intersection[2]);\n        if (phi > phiArc || phi === phiArc && (arc[0] || arc[1])) {\n          winding += antimeridian ^ delta >= 0 ? 1 : -1;\n        }\n      }\n    }\n  }\n\n  // First, determine whether the South pole is inside or outside:\n  //\n  // It is inside if:\n  // * the polygon winds around it in a clockwise direction.\n  // * the polygon does not (cumulatively) wind around it, but has a negative\n  //   (counter-clockwise) area.\n  //\n  // Second, count the (signed) number of times a segment crosses a lambda\n  // from the point to the South pole.  If it is zero, then the point is the\n  // same side as the South pole.\n\n  return (angle < -epsilon || angle < epsilon && sum < -epsilon2) ^ (winding & 1);\n}\n", "function* flatten(arrays) {\n  for (const array of arrays) {\n    yield* array;\n  }\n}\n\nexport default function merge(arrays) {\n  return Array.from(flatten(arrays));\n}\n", "import clipBuffer from \"./buffer.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {epsilon, halfPi} from \"../math.js\";\nimport polygonContains from \"../polygonContains.js\";\nimport {merge} from \"d3-array\";\n\nexport default function(pointVisible, clipLine, interpolate, start) {\n  return function(sink) {\n    var line = clipLine(sink),\n        ringBuffer = clipBuffer(),\n        ringSink = clipLine(ringBuffer),\n        polygonStarted = false,\n        polygon,\n        segments,\n        ring;\n\n    var clip = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() {\n        clip.point = pointRing;\n        clip.lineStart = ringStart;\n        clip.lineEnd = ringEnd;\n        segments = [];\n        polygon = [];\n      },\n      polygonEnd: function() {\n        clip.point = point;\n        clip.lineStart = lineStart;\n        clip.lineEnd = lineEnd;\n        segments = merge(segments);\n        var startInside = polygonContains(polygon, start);\n        if (segments.length) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          clipRejoin(segments, compareIntersection, startInside, interpolate, sink);\n        } else if (startInside) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          interpolate(null, null, 1, sink);\n          sink.lineEnd();\n        }\n        if (polygonStarted) sink.polygonEnd(), polygonStarted = false;\n        segments = polygon = null;\n      },\n      sphere: function() {\n        sink.polygonStart();\n        sink.lineStart();\n        interpolate(null, null, 1, sink);\n        sink.lineEnd();\n        sink.polygonEnd();\n      }\n    };\n\n    function point(lambda, phi) {\n      if (pointVisible(lambda, phi)) sink.point(lambda, phi);\n    }\n\n    function pointLine(lambda, phi) {\n      line.point(lambda, phi);\n    }\n\n    function lineStart() {\n      clip.point = pointLine;\n      line.lineStart();\n    }\n\n    function lineEnd() {\n      clip.point = point;\n      line.lineEnd();\n    }\n\n    function pointRing(lambda, phi) {\n      ring.push([lambda, phi]);\n      ringSink.point(lambda, phi);\n    }\n\n    function ringStart() {\n      ringSink.lineStart();\n      ring = [];\n    }\n\n    function ringEnd() {\n      pointRing(ring[0][0], ring[0][1]);\n      ringSink.lineEnd();\n\n      var clean = ringSink.clean(),\n          ringSegments = ringBuffer.result(),\n          i, n = ringSegments.length, m,\n          segment,\n          point;\n\n      ring.pop();\n      polygon.push(ring);\n      ring = null;\n\n      if (!n) return;\n\n      // No intersections.\n      if (clean & 1) {\n        segment = ringSegments[0];\n        if ((m = segment.length - 1) > 0) {\n          if (!polygonStarted) sink.polygonStart(), polygonStarted = true;\n          sink.lineStart();\n          for (i = 0; i < m; ++i) sink.point((point = segment[i])[0], point[1]);\n          sink.lineEnd();\n        }\n        return;\n      }\n\n      // Rejoin connected segments.\n      // TODO reuse ringBuffer.rejoin()?\n      if (n > 1 && clean & 2) ringSegments.push(ringSegments.pop().concat(ringSegments.shift()));\n\n      segments.push(ringSegments.filter(validSegment));\n    }\n\n    return clip;\n  };\n}\n\nfunction validSegment(segment) {\n  return segment.length > 1;\n}\n\n// Intersections are sorted along the clip edge. For both antimeridian cutting\n// and circle clipping, the same comparison is used.\nfunction compareIntersection(a, b) {\n  return ((a = a.x)[0] < 0 ? a[1] - halfPi - epsilon : halfPi - a[1])\n       - ((b = b.x)[0] < 0 ? b[1] - halfPi - epsilon : halfPi - b[1]);\n}\n", "import clip from \"./index.js\";\nimport {abs, atan, cos, epsilon, halfPi, pi, sin} from \"../math.js\";\n\nexport default clip(\n  function() { return true; },\n  clipAntimeridianLine,\n  clipAntimeridianInterpolate,\n  [-pi, -halfPi]\n);\n\n// Takes a line and cuts into visible segments. Return values: 0 - there were\n// intersections or the line was empty; 1 - no intersections; 2 - there were\n// intersections, and the first and last segments should be rejoined.\nfunction clipAntimeridianLine(stream) {\n  var lambda0 = NaN,\n      phi0 = NaN,\n      sign0 = NaN,\n      clean; // no intersections\n\n  return {\n    lineStart: function() {\n      stream.lineStart();\n      clean = 1;\n    },\n    point: function(lambda1, phi1) {\n      var sign1 = lambda1 > 0 ? pi : -pi,\n          delta = abs(lambda1 - lambda0);\n      if (abs(delta - pi) < epsilon) { // line crosses a pole\n        stream.point(lambda0, phi0 = (phi0 + phi1) / 2 > 0 ? halfPi : -halfPi);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        stream.point(lambda1, phi0);\n        clean = 0;\n      } else if (sign0 !== sign1 && delta >= pi) { // line crosses antimeridian\n        if (abs(lambda0 - sign0) < epsilon) lambda0 -= sign0 * epsilon; // handle degeneracies\n        if (abs(lambda1 - sign1) < epsilon) lambda1 -= sign1 * epsilon;\n        phi0 = clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1);\n        stream.point(sign0, phi0);\n        stream.lineEnd();\n        stream.lineStart();\n        stream.point(sign1, phi0);\n        clean = 0;\n      }\n      stream.point(lambda0 = lambda1, phi0 = phi1);\n      sign0 = sign1;\n    },\n    lineEnd: function() {\n      stream.lineEnd();\n      lambda0 = phi0 = NaN;\n    },\n    clean: function() {\n      return 2 - clean; // if intersections, rejoin first and last segments\n    }\n  };\n}\n\nfunction clipAntimeridianIntersect(lambda0, phi0, lambda1, phi1) {\n  var cosPhi0,\n      cosPhi1,\n      sinLambda0Lambda1 = sin(lambda0 - lambda1);\n  return abs(sinLambda0Lambda1) > epsilon\n      ? atan((sin(phi0) * (cosPhi1 = cos(phi1)) * sin(lambda1)\n          - sin(phi1) * (cosPhi0 = cos(phi0)) * sin(lambda0))\n          / (cosPhi0 * cosPhi1 * sinLambda0Lambda1))\n      : (phi0 + phi1) / 2;\n}\n\nfunction clipAntimeridianInterpolate(from, to, direction, stream) {\n  var phi;\n  if (from == null) {\n    phi = direction * halfPi;\n    stream.point(-pi, phi);\n    stream.point(0, phi);\n    stream.point(pi, phi);\n    stream.point(pi, 0);\n    stream.point(pi, -phi);\n    stream.point(0, -phi);\n    stream.point(-pi, -phi);\n    stream.point(-pi, 0);\n    stream.point(-pi, phi);\n  } else if (abs(from[0] - to[0]) > epsilon) {\n    var lambda = from[0] < to[0] ? pi : -pi;\n    phi = direction * lambda / 2;\n    stream.point(-lambda, phi);\n    stream.point(0, phi);\n    stream.point(lambda, phi);\n  } else {\n    stream.point(to[0], to[1]);\n  }\n}\n", "export default function(x) {\n  return function() {\n    return x;\n  };\n}\n", "import {cartesian, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport constant from \"./constant.js\";\nimport {acos, cos, degrees, epsilon, radians, sin, tau} from \"./math.js\";\nimport {rotateRadians} from \"./rotation.js\";\n\n// Generates a circle centered at [0°, 0°], with a given radius and precision.\nexport function circleStream(stream, radius, delta, direction, t0, t1) {\n  if (!delta) return;\n  var cosRadius = cos(radius),\n      sinRadius = sin(radius),\n      step = direction * delta;\n  if (t0 == null) {\n    t0 = radius + direction * tau;\n    t1 = radius - step / 2;\n  } else {\n    t0 = circleRadius(cosRadius, t0);\n    t1 = circleRadius(cosRadius, t1);\n    if (direction > 0 ? t0 < t1 : t0 > t1) t0 += direction * tau;\n  }\n  for (var point, t = t0; direction > 0 ? t > t1 : t < t1; t -= step) {\n    point = spherical([cosRadius, -sinRadius * cos(t), -sinRadius * sin(t)]);\n    stream.point(point[0], point[1]);\n  }\n}\n\n// Returns the signed angle of a cartesian point relative to [cosRadius, 0, 0].\nfunction circleRadius(cosRadius, point) {\n  point = cartesian(point), point[0] -= cosRadius;\n  cartesianNormalizeInPlace(point);\n  var radius = acos(-point[1]);\n  return ((-point[2] < 0 ? -radius : radius) + tau - epsilon) % tau;\n}\n\nexport default function() {\n  var center = constant([0, 0]),\n      radius = constant(90),\n      precision = constant(6),\n      ring,\n      rotate,\n      stream = {point: point};\n\n  function point(x, y) {\n    ring.push(x = rotate(x, y));\n    x[0] *= degrees, x[1] *= degrees;\n  }\n\n  function circle() {\n    var c = center.apply(this, arguments),\n        r = radius.apply(this, arguments) * radians,\n        p = precision.apply(this, arguments) * radians;\n    ring = [];\n    rotate = rotateRadians(-c[0] * radians, -c[1] * radians, 0).invert;\n    circleStream(stream, r, p, 1);\n    c = {type: \"Polygon\", coordinates: [ring]};\n    ring = rotate = null;\n    return c;\n  }\n\n  circle.center = function(_) {\n    return arguments.length ? (center = typeof _ === \"function\" ? _ : constant([+_[0], +_[1]]), circle) : center;\n  };\n\n  circle.radius = function(_) {\n    return arguments.length ? (radius = typeof _ === \"function\" ? _ : constant(+_), circle) : radius;\n  };\n\n  circle.precision = function(_) {\n    return arguments.length ? (precision = typeof _ === \"function\" ? _ : constant(+_), circle) : precision;\n  };\n\n  return circle;\n}\n", "import {abs, epsilon} from \"../math.js\";\nimport clipBuffer from \"./buffer.js\";\nimport clipLine from \"./line.js\";\nimport clipRejoin from \"./rejoin.js\";\nimport {merge} from \"d3-array\";\n\nvar clipMax = 1e9, clipMin = -clipMax;\n\n// TODO Use d3-polygon’s polygonContains here for the ring check?\n// TODO Eliminate duplicate buffering in clipBuffer and polygon.push?\n\nexport default function clipRectangle(x0, y0, x1, y1) {\n\n  function visible(x, y) {\n    return x0 <= x && x <= x1 && y0 <= y && y <= y1;\n  }\n\n  function interpolate(from, to, direction, stream) {\n    var a = 0, a1 = 0;\n    if (from == null\n        || (a = corner(from, direction)) !== (a1 = corner(to, direction))\n        || comparePoint(from, to) < 0 ^ direction > 0) {\n      do stream.point(a === 0 || a === 3 ? x0 : x1, a > 1 ? y1 : y0);\n      while ((a = (a + direction + 4) % 4) !== a1);\n    } else {\n      stream.point(to[0], to[1]);\n    }\n  }\n\n  function corner(p, direction) {\n    return abs(p[0] - x0) < epsilon ? direction > 0 ? 0 : 3\n        : abs(p[0] - x1) < epsilon ? direction > 0 ? 2 : 1\n        : abs(p[1] - y0) < epsilon ? direction > 0 ? 1 : 0\n        : direction > 0 ? 3 : 2; // abs(p[1] - y1) < epsilon\n  }\n\n  function compareIntersection(a, b) {\n    return comparePoint(a.x, b.x);\n  }\n\n  function comparePoint(a, b) {\n    var ca = corner(a, 1),\n        cb = corner(b, 1);\n    return ca !== cb ? ca - cb\n        : ca === 0 ? b[1] - a[1]\n        : ca === 1 ? a[0] - b[0]\n        : ca === 2 ? a[1] - b[1]\n        : b[0] - a[0];\n  }\n\n  return function(stream) {\n    var activeStream = stream,\n        bufferStream = clipBuffer(),\n        segments,\n        polygon,\n        ring,\n        x__, y__, v__, // first point\n        x_, y_, v_, // previous point\n        first,\n        clean;\n\n    var clipStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: polygonStart,\n      polygonEnd: polygonEnd\n    };\n\n    function point(x, y) {\n      if (visible(x, y)) activeStream.point(x, y);\n    }\n\n    function polygonInside() {\n      var winding = 0;\n\n      for (var i = 0, n = polygon.length; i < n; ++i) {\n        for (var ring = polygon[i], j = 1, m = ring.length, point = ring[0], a0, a1, b0 = point[0], b1 = point[1]; j < m; ++j) {\n          a0 = b0, a1 = b1, point = ring[j], b0 = point[0], b1 = point[1];\n          if (a1 <= y1) { if (b1 > y1 && (b0 - a0) * (y1 - a1) > (b1 - a1) * (x0 - a0)) ++winding; }\n          else { if (b1 <= y1 && (b0 - a0) * (y1 - a1) < (b1 - a1) * (x0 - a0)) --winding; }\n        }\n      }\n\n      return winding;\n    }\n\n    // Buffer geometry within a polygon and then clip it en masse.\n    function polygonStart() {\n      activeStream = bufferStream, segments = [], polygon = [], clean = true;\n    }\n\n    function polygonEnd() {\n      var startInside = polygonInside(),\n          cleanInside = clean && startInside,\n          visible = (segments = merge(segments)).length;\n      if (cleanInside || visible) {\n        stream.polygonStart();\n        if (cleanInside) {\n          stream.lineStart();\n          interpolate(null, null, 1, stream);\n          stream.lineEnd();\n        }\n        if (visible) {\n          clipRejoin(segments, compareIntersection, startInside, interpolate, stream);\n        }\n        stream.polygonEnd();\n      }\n      activeStream = stream, segments = polygon = ring = null;\n    }\n\n    function lineStart() {\n      clipStream.point = linePoint;\n      if (polygon) polygon.push(ring = []);\n      first = true;\n      v_ = false;\n      x_ = y_ = NaN;\n    }\n\n    // TODO rather than special-case polygons, simply handle them separately.\n    // Ideally, coincident intersection points should be jittered to avoid\n    // clipping issues.\n    function lineEnd() {\n      if (segments) {\n        linePoint(x__, y__);\n        if (v__ && v_) bufferStream.rejoin();\n        segments.push(bufferStream.result());\n      }\n      clipStream.point = point;\n      if (v_) activeStream.lineEnd();\n    }\n\n    function linePoint(x, y) {\n      var v = visible(x, y);\n      if (polygon) ring.push([x, y]);\n      if (first) {\n        x__ = x, y__ = y, v__ = v;\n        first = false;\n        if (v) {\n          activeStream.lineStart();\n          activeStream.point(x, y);\n        }\n      } else {\n        if (v && v_) activeStream.point(x, y);\n        else {\n          var a = [x_ = Math.max(clipMin, Math.min(clipMax, x_)), y_ = Math.max(clipMin, Math.min(clipMax, y_))],\n              b = [x = Math.max(clipMin, Math.min(clipMax, x)), y = Math.max(clipMin, Math.min(clipMax, y))];\n          if (clipLine(a, b, x0, y0, x1, y1)) {\n            if (!v_) {\n              activeStream.lineStart();\n              activeStream.point(a[0], a[1]);\n            }\n            activeStream.point(b[0], b[1]);\n            if (!v) activeStream.lineEnd();\n            clean = false;\n          } else if (v) {\n            activeStream.lineStart();\n            activeStream.point(x, y);\n            clean = false;\n          }\n        }\n      }\n      x_ = x, y_ = y, v_ = v;\n    }\n\n    return clipStream;\n  };\n}\n", "export default x => x;\n", "export default function(methods) {\n  return {\n    stream: transformer(methods)\n  };\n}\n\nexport function transformer(methods) {\n  return function(stream) {\n    var s = new TransformStream;\n    for (var key in methods) s[key] = methods[key];\n    s.stream = stream;\n    return s;\n  };\n}\n\nfunction TransformStream() {}\n\nTransformStream.prototype = {\n  constructor: TransformStream,\n  point: function(x, y) { this.stream.point(x, y); },\n  sphere: function() { this.stream.sphere(); },\n  lineStart: function() { this.stream.lineStart(); },\n  lineEnd: function() { this.stream.lineEnd(); },\n  polygonStart: function() { this.stream.polygonStart(); },\n  polygonEnd: function() { this.stream.polygonEnd(); }\n};\n", "import noop from \"../noop.js\";\n\nvar x0 = Infinity,\n    y0 = x0,\n    x1 = -x0,\n    y1 = x1;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: noop,\n  polygonEnd: noop,\n  result: function() {\n    var bounds = [[x0, y0], [x1, y1]];\n    x1 = y1 = -(y0 = x0 = Infinity);\n    return bounds;\n  }\n};\n\nfunction boundsPoint(x, y) {\n  if (x < x0) x0 = x;\n  if (x > x1) x1 = x;\n  if (y < y0) y0 = y;\n  if (y > y1) y1 = y;\n}\n\nexport default boundsStream;\n", "import {default as geoStream} from \"../stream.js\";\nimport boundsStream from \"../path/bounds.js\";\n\nfunction fit(projection, fitBounds, object) {\n  var clip = projection.clipExtent && projection.clipExtent();\n  projection.scale(150).translate([0, 0]);\n  if (clip != null) projection.clipExtent(null);\n  geoStream(object, projection.stream(boundsStream));\n  fitBounds(boundsStream.result());\n  if (clip != null) projection.clipExtent(clip);\n  return projection;\n}\n\nexport function fitExtent(projection, extent, object) {\n  return fit(projection, function(b) {\n    var w = extent[1][0] - extent[0][0],\n        h = extent[1][1] - extent[0][1],\n        k = Math.min(w / (b[1][0] - b[0][0]), h / (b[1][1] - b[0][1])),\n        x = +extent[0][0] + (w - k * (b[1][0] + b[0][0])) / 2,\n        y = +extent[0][1] + (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitSize(projection, size, object) {\n  return fitExtent(projection, [[0, 0], size], object);\n}\n\nexport function fitWidth(projection, width, object) {\n  return fit(projection, function(b) {\n    var w = +width,\n        k = w / (b[1][0] - b[0][0]),\n        x = (w - k * (b[1][0] + b[0][0])) / 2,\n        y = -k * b[0][1];\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n\nexport function fitHeight(projection, height, object) {\n  return fit(projection, function(b) {\n    var h = +height,\n        k = h / (b[1][1] - b[0][1]),\n        x = -k * b[0][0],\n        y = (h - k * (b[1][1] + b[0][1])) / 2;\n    projection.scale(150 * k).translate([x, y]);\n  }, object);\n}\n", "import {cartesian} from \"../cartesian.js\";\nimport {abs, asin, atan2, cos, epsilon, radians, sqrt} from \"../math.js\";\nimport {transformer} from \"../transform.js\";\n\nvar maxDepth = 16, // maximum depth of subdivision\n    cosMinDistance = cos(30 * radians); // cos(minimum angular distance)\n\nexport default function(project, delta2) {\n  return +delta2 ? resample(project, delta2) : resampleNone(project);\n}\n\nfunction resampleNone(project) {\n  return transformer({\n    point: function(x, y) {\n      x = project(x, y);\n      this.stream.point(x[0], x[1]);\n    }\n  });\n}\n\nfunction resample(project, delta2) {\n\n  function resampleLineTo(x0, y0, lambda0, a0, b0, c0, x1, y1, lambda1, a1, b1, c1, depth, stream) {\n    var dx = x1 - x0,\n        dy = y1 - y0,\n        d2 = dx * dx + dy * dy;\n    if (d2 > 4 * delta2 && depth--) {\n      var a = a0 + a1,\n          b = b0 + b1,\n          c = c0 + c1,\n          m = sqrt(a * a + b * b + c * c),\n          phi2 = asin(c /= m),\n          lambda2 = abs(abs(c) - 1) < epsilon || abs(lambda0 - lambda1) < epsilon ? (lambda0 + lambda1) / 2 : atan2(b, a),\n          p = project(lambda2, phi2),\n          x2 = p[0],\n          y2 = p[1],\n          dx2 = x2 - x0,\n          dy2 = y2 - y0,\n          dz = dy * dx2 - dx * dy2;\n      if (dz * dz / d2 > delta2 // perpendicular projected distance\n          || abs((dx * dx2 + dy * dy2) / d2 - 0.5) > 0.3 // midpoint close to an end\n          || a0 * a1 + b0 * b1 + c0 * c1 < cosMinDistance) { // angular distance\n        resampleLineTo(x0, y0, lambda0, a0, b0, c0, x2, y2, lambda2, a /= m, b /= m, c, depth, stream);\n        stream.point(x2, y2);\n        resampleLineTo(x2, y2, lambda2, a, b, c, x1, y1, lambda1, a1, b1, c1, depth, stream);\n      }\n    }\n  }\n  return function(stream) {\n    var lambda00, x00, y00, a00, b00, c00, // first point\n        lambda0, x0, y0, a0, b0, c0; // previous point\n\n    var resampleStream = {\n      point: point,\n      lineStart: lineStart,\n      lineEnd: lineEnd,\n      polygonStart: function() { stream.polygonStart(); resampleStream.lineStart = ringStart; },\n      polygonEnd: function() { stream.polygonEnd(); resampleStream.lineStart = lineStart; }\n    };\n\n    function point(x, y) {\n      x = project(x, y);\n      stream.point(x[0], x[1]);\n    }\n\n    function lineStart() {\n      x0 = NaN;\n      resampleStream.point = linePoint;\n      stream.lineStart();\n    }\n\n    function linePoint(lambda, phi) {\n      var c = cartesian([lambda, phi]), p = project(lambda, phi);\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x0 = p[0], y0 = p[1], lambda0 = lambda, a0 = c[0], b0 = c[1], c0 = c[2], maxDepth, stream);\n      stream.point(x0, y0);\n    }\n\n    function lineEnd() {\n      resampleStream.point = point;\n      stream.lineEnd();\n    }\n\n    function ringStart() {\n      lineStart();\n      resampleStream.point = ringPoint;\n      resampleStream.lineEnd = ringEnd;\n    }\n\n    function ringPoint(lambda, phi) {\n      linePoint(lambda00 = lambda, phi), x00 = x0, y00 = y0, a00 = a0, b00 = b0, c00 = c0;\n      resampleStream.point = linePoint;\n    }\n\n    function ringEnd() {\n      resampleLineTo(x0, y0, lambda0, a0, b0, c0, x00, y00, lambda00, a00, b00, c00, maxDepth, stream);\n      resampleStream.lineEnd = lineEnd;\n      lineEnd();\n    }\n\n    return resampleStream;\n  };\n}\n", "import clipAntimeridian from \"../clip/antimeridian.js\";\nimport clipCircle from \"../clip/circle.js\";\nimport clipRectangle from \"../clip/rectangle.js\";\nimport compose from \"../compose.js\";\nimport identity from \"../identity.js\";\nimport {cos, degrees, radians, sin, sqrt} from \"../math.js\";\nimport {rotateRadians} from \"../rotation.js\";\nimport {transformer} from \"../transform.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\nimport resample from \"./resample.js\";\n\nvar transformRadians = transformer({\n  point: function(x, y) {\n    this.stream.point(x * radians, y * radians);\n  }\n});\n\nfunction transformRotate(rotate) {\n  return transformer({\n    point: function(x, y) {\n      var r = rotate(x, y);\n      return this.stream.point(r[0], r[1]);\n    }\n  });\n}\n\nfunction scaleTranslate(k, dx, dy, sx, sy) {\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [dx + k * x, dy - k * y];\n  }\n  transform.invert = function(x, y) {\n    return [(x - dx) / k * sx, (dy - y) / k * sy];\n  };\n  return transform;\n}\n\nfunction scaleTranslateRotate(k, dx, dy, sx, sy, alpha) {\n  if (!alpha) return scaleTranslate(k, dx, dy, sx, sy);\n  var cosAlpha = cos(alpha),\n      sinAlpha = sin(alpha),\n      a = cosAlpha * k,\n      b = sinAlpha * k,\n      ai = cosAlpha / k,\n      bi = sinAlpha / k,\n      ci = (sinAlpha * dy - cosAlpha * dx) / k,\n      fi = (sinAlpha * dx + cosAlpha * dy) / k;\n  function transform(x, y) {\n    x *= sx; y *= sy;\n    return [a * x - b * y + dx, dy - b * x - a * y];\n  }\n  transform.invert = function(x, y) {\n    return [sx * (ai * x - bi * y + ci), sy * (fi - bi * x - ai * y)];\n  };\n  return transform;\n}\n\nexport default function projection(project) {\n  return projectionMutator(function() { return project; })();\n}\n\nexport function projectionMutator(projectAt) {\n  var project,\n      k = 150, // scale\n      x = 480, y = 250, // translate\n      lambda = 0, phi = 0, // center\n      deltaLambda = 0, deltaPhi = 0, deltaGamma = 0, rotate, // pre-rotate\n      alpha = 0, // post-rotate angle\n      sx = 1, // reflectX\n      sy = 1, // reflectX\n      theta = null, preclip = clipAntimeridian, // pre-clip angle\n      x0 = null, y0, x1, y1, postclip = identity, // post-clip extent\n      delta2 = 0.5, // precision\n      projectResample,\n      projectTransform,\n      projectRotateTransform,\n      cache,\n      cacheStream;\n\n  function projection(point) {\n    return projectRotateTransform(point[0] * radians, point[1] * radians);\n  }\n\n  function invert(point) {\n    point = projectRotateTransform.invert(point[0], point[1]);\n    return point && [point[0] * degrees, point[1] * degrees];\n  }\n\n  projection.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = transformRadians(transformRotate(rotate)(preclip(projectResample(postclip(cacheStream = stream)))));\n  };\n\n  projection.preclip = function(_) {\n    return arguments.length ? (preclip = _, theta = undefined, reset()) : preclip;\n  };\n\n  projection.postclip = function(_) {\n    return arguments.length ? (postclip = _, x0 = y0 = x1 = y1 = null, reset()) : postclip;\n  };\n\n  projection.clipAngle = function(_) {\n    return arguments.length ? (preclip = +_ ? clipCircle(theta = _ * radians) : (theta = null, clipAntimeridian), reset()) : theta * degrees;\n  };\n\n  projection.clipExtent = function(_) {\n    return arguments.length ? (postclip = _ == null ? (x0 = y0 = x1 = y1 = null, identity) : clipRectangle(x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1]), reset()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  projection.scale = function(_) {\n    return arguments.length ? (k = +_, recenter()) : k;\n  };\n\n  projection.translate = function(_) {\n    return arguments.length ? (x = +_[0], y = +_[1], recenter()) : [x, y];\n  };\n\n  projection.center = function(_) {\n    return arguments.length ? (lambda = _[0] % 360 * radians, phi = _[1] % 360 * radians, recenter()) : [lambda * degrees, phi * degrees];\n  };\n\n  projection.rotate = function(_) {\n    return arguments.length ? (deltaLambda = _[0] % 360 * radians, deltaPhi = _[1] % 360 * radians, deltaGamma = _.length > 2 ? _[2] % 360 * radians : 0, recenter()) : [deltaLambda * degrees, deltaPhi * degrees, deltaGamma * degrees];\n  };\n\n  projection.angle = function(_) {\n    return arguments.length ? (alpha = _ % 360 * radians, recenter()) : alpha * degrees;\n  };\n\n  projection.reflectX = function(_) {\n    return arguments.length ? (sx = _ ? -1 : 1, recenter()) : sx < 0;\n  };\n\n  projection.reflectY = function(_) {\n    return arguments.length ? (sy = _ ? -1 : 1, recenter()) : sy < 0;\n  };\n\n  projection.precision = function(_) {\n    return arguments.length ? (projectResample = resample(projectTransform, delta2 = _ * _), reset()) : sqrt(delta2);\n  };\n\n  projection.fitExtent = function(extent, object) {\n    return fitExtent(projection, extent, object);\n  };\n\n  projection.fitSize = function(size, object) {\n    return fitSize(projection, size, object);\n  };\n\n  projection.fitWidth = function(width, object) {\n    return fitWidth(projection, width, object);\n  };\n\n  projection.fitHeight = function(height, object) {\n    return fitHeight(projection, height, object);\n  };\n\n  function recenter() {\n    var center = scaleTranslateRotate(k, 0, 0, sx, sy, alpha).apply(null, project(lambda, phi)),\n        transform = scaleTranslateRotate(k, x - center[0], y - center[1], sx, sy, alpha);\n    rotate = rotateRadians(deltaLambda, deltaPhi, deltaGamma);\n    projectTransform = compose(project, transform);\n    projectRotateTransform = compose(rotate, projectTransform);\n    projectResample = resample(projectTransform, delta2);\n    return reset();\n  }\n\n  function reset() {\n    cache = cacheStream = null;\n    return projection;\n  }\n\n  return function() {\n    project = projectAt.apply(this, arguments);\n    projection.invert = project.invert && invert;\n    return recenter();\n  };\n}\n", "import {cartesian, cartesianAddInPlace, cartesianCross, cartesianDot, cartesianScale, spherical} from \"../cartesian.js\";\nimport {circleStream} from \"../circle.js\";\nimport {abs, cos, epsilon, pi, radians, sqrt} from \"../math.js\";\nimport pointEqual from \"../pointEqual.js\";\nimport clip from \"./index.js\";\n\nexport default function(radius) {\n  var cr = cos(radius),\n      delta = 6 * radians,\n      smallRadius = cr > 0,\n      notHemisphere = abs(cr) > epsilon; // TODO optimise for this common case\n\n  function interpolate(from, to, direction, stream) {\n    circleStream(stream, radius, delta, direction, from, to);\n  }\n\n  function visible(lambda, phi) {\n    return cos(lambda) * cos(phi) > cr;\n  }\n\n  // Takes a line and cuts into visible segments. Return values used for polygon\n  // clipping: 0 - there were intersections or the line was empty; 1 - no\n  // intersections 2 - there were intersections, and the first and last segments\n  // should be rejoined.\n  function clipLine(stream) {\n    var point0, // previous point\n        c0, // code for previous point\n        v0, // visibility of previous point\n        v00, // visibility of first point\n        clean; // no intersections\n    return {\n      lineStart: function() {\n        v00 = v0 = false;\n        clean = 1;\n      },\n      point: function(lambda, phi) {\n        var point1 = [lambda, phi],\n            point2,\n            v = visible(lambda, phi),\n            c = smallRadius\n              ? v ? 0 : code(lambda, phi)\n              : v ? code(lambda + (lambda < 0 ? pi : -pi), phi) : 0;\n        if (!point0 && (v00 = v0 = v)) stream.lineStart();\n        if (v !== v0) {\n          point2 = intersect(point0, point1);\n          if (!point2 || pointEqual(point0, point2) || pointEqual(point1, point2))\n            point1[2] = 1;\n        }\n        if (v !== v0) {\n          clean = 0;\n          if (v) {\n            // outside going in\n            stream.lineStart();\n            point2 = intersect(point1, point0);\n            stream.point(point2[0], point2[1]);\n          } else {\n            // inside going out\n            point2 = intersect(point0, point1);\n            stream.point(point2[0], point2[1], 2);\n            stream.lineEnd();\n          }\n          point0 = point2;\n        } else if (notHemisphere && point0 && smallRadius ^ v) {\n          var t;\n          // If the codes for two points are different, or are both zero,\n          // and there this segment intersects with the small circle.\n          if (!(c & c0) && (t = intersect(point1, point0, true))) {\n            clean = 0;\n            if (smallRadius) {\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1]);\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n            } else {\n              stream.point(t[1][0], t[1][1]);\n              stream.lineEnd();\n              stream.lineStart();\n              stream.point(t[0][0], t[0][1], 3);\n            }\n          }\n        }\n        if (v && (!point0 || !pointEqual(point0, point1))) {\n          stream.point(point1[0], point1[1]);\n        }\n        point0 = point1, v0 = v, c0 = c;\n      },\n      lineEnd: function() {\n        if (v0) stream.lineEnd();\n        point0 = null;\n      },\n      // Rejoin first and last segments if there were intersections and the first\n      // and last points were visible.\n      clean: function() {\n        return clean | ((v00 && v0) << 1);\n      }\n    };\n  }\n\n  // Intersects the great circle between a and b with the clip circle.\n  function intersect(a, b, two) {\n    var pa = cartesian(a),\n        pb = cartesian(b);\n\n    // We have two planes, n1.p = d1 and n2.p = d2.\n    // Find intersection line p(t) = c1 n1 + c2 n2 + t (n1 ⨯ n2).\n    var n1 = [1, 0, 0], // normal\n        n2 = cartesianCross(pa, pb),\n        n2n2 = cartesianDot(n2, n2),\n        n1n2 = n2[0], // cartesianDot(n1, n2),\n        determinant = n2n2 - n1n2 * n1n2;\n\n    // Two polar points.\n    if (!determinant) return !two && a;\n\n    var c1 =  cr * n2n2 / determinant,\n        c2 = -cr * n1n2 / determinant,\n        n1xn2 = cartesianCross(n1, n2),\n        A = cartesianScale(n1, c1),\n        B = cartesianScale(n2, c2);\n    cartesianAddInPlace(A, B);\n\n    // Solve |p(t)|^2 = 1.\n    var u = n1xn2,\n        w = cartesianDot(A, u),\n        uu = cartesianDot(u, u),\n        t2 = w * w - uu * (cartesianDot(A, A) - 1);\n\n    if (t2 < 0) return;\n\n    var t = sqrt(t2),\n        q = cartesianScale(u, (-w - t) / uu);\n    cartesianAddInPlace(q, A);\n    q = spherical(q);\n\n    if (!two) return q;\n\n    // Two intersection points.\n    var lambda0 = a[0],\n        lambda1 = b[0],\n        phi0 = a[1],\n        phi1 = b[1],\n        z;\n\n    if (lambda1 < lambda0) z = lambda0, lambda0 = lambda1, lambda1 = z;\n\n    var delta = lambda1 - lambda0,\n        polar = abs(delta - pi) < epsilon,\n        meridian = polar || delta < epsilon;\n\n    if (!polar && phi1 < phi0) z = phi0, phi0 = phi1, phi1 = z;\n\n    // Check that the first point is between a and b.\n    if (meridian\n        ? polar\n          ? phi0 + phi1 > 0 ^ q[1] < (abs(q[0] - lambda0) < epsilon ? phi0 : phi1)\n          : phi0 <= q[1] && q[1] <= phi1\n        : delta > pi ^ (lambda0 <= q[0] && q[0] <= lambda1)) {\n      var q1 = cartesianScale(u, (-w + t) / uu);\n      cartesianAddInPlace(q1, A);\n      return [q, spherical(q1)];\n    }\n  }\n\n  // Generates a 4-bit vector representing the location of a point relative to\n  // the small circle's bounding box.\n  function code(lambda, phi) {\n    var r = smallRadius ? radius : pi - radius,\n        code = 0;\n    if (lambda < -r) code |= 1; // left\n    else if (lambda > r) code |= 2; // right\n    if (phi < -r) code |= 4; // below\n    else if (phi > r) code |= 8; // above\n    return code;\n  }\n\n  return clip(visible, clipLine, interpolate, smallRadius ? [0, -radius] : [-pi, radius - pi]);\n}\n", "export default function(a, b, x0, y0, x1, y1) {\n  var ax = a[0],\n      ay = a[1],\n      bx = b[0],\n      by = b[1],\n      t0 = 0,\n      t1 = 1,\n      dx = bx - ax,\n      dy = by - ay,\n      r;\n\n  r = x0 - ax;\n  if (!dx && r > 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dx > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = x1 - ax;\n  if (!dx && r < 0) return;\n  r /= dx;\n  if (dx < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dx > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  r = y0 - ay;\n  if (!dy && r > 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  } else if (dy > 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  }\n\n  r = y1 - ay;\n  if (!dy && r < 0) return;\n  r /= dy;\n  if (dy < 0) {\n    if (r > t1) return;\n    if (r > t0) t0 = r;\n  } else if (dy > 0) {\n    if (r < t0) return;\n    if (r < t1) t1 = r;\n  }\n\n  if (t0 > 0) a[0] = ax + t0 * dx, a[1] = ay + t0 * dy;\n  if (t1 < 1) b[0] = ax + t1 * dx, b[1] = ay + t1 * dy;\n  return true;\n}\n", "import {atan, exp, halfPi, log, pi, tan, tau} from \"../math.js\";\nimport rotation from \"../rotation.js\";\nimport projection from \"./index.js\";\n\nexport function mercatorRaw(lambda, phi) {\n  return [lambda, log(tan((halfPi + phi) / 2))];\n}\n\nmercatorRaw.invert = function(x, y) {\n  return [x, 2 * atan(exp(y)) - halfPi];\n};\n\nexport default function() {\n  return mercatorProjection(mercatorRaw)\n      .scale(961 / tau);\n}\n\nexport function mercatorProjection(project) {\n  var m = projection(project),\n      center = m.center,\n      scale = m.scale,\n      translate = m.translate,\n      clipExtent = m.clipExtent,\n      x0 = null, y0, x1, y1; // clip extent\n\n  m.scale = function(_) {\n    return arguments.length ? (scale(_), reclip()) : scale();\n  };\n\n  m.translate = function(_) {\n    return arguments.length ? (translate(_), reclip()) : translate();\n  };\n\n  m.center = function(_) {\n    return arguments.length ? (center(_), reclip()) : center();\n  };\n\n  m.clipExtent = function(_) {\n    return arguments.length ? ((_ == null ? x0 = y0 = x1 = y1 = null : (x0 = +_[0][0], y0 = +_[0][1], x1 = +_[1][0], y1 = +_[1][1])), reclip()) : x0 == null ? null : [[x0, y0], [x1, y1]];\n  };\n\n  function reclip() {\n    var k = pi * scale(),\n        t = m(rotation(m.rotate()).invert([0, 0]));\n    return clipExtent(x0 == null\n        ? [[t[0] - k, t[1] - k], [t[0] + k, t[1] + k]] : project === mercatorRaw\n        ? [[Math.max(t[0] - k, x0), y0], [Math.min(t[0] + k, x1), y1]]\n        : [[x0, Math.max(t[1] - k, y0)], [x1, Math.min(t[1] + k, y1)]]);\n  }\n\n  return reclip();\n}\n", "import { Theme } from \"../../core/Theme\";\r\nimport { setColor } from \"../../themes/DefaultTheme\";\r\n\r\nimport { geoMercator } from \"d3-geo\";\r\n\r\nimport * as $ease from \"../../core/util/Ease\";\r\n\r\n\r\n/**\r\n * @ignore\r\n */\r\nexport class MapChartDefaultTheme extends Theme {\r\n\tprotected setupDefaultRules() {\r\n\t\tsuper.setupDefaultRules();\r\n\r\n\t\tconst ic = this._root.interfaceColors;\r\n\t\tconst r = this.rule.bind(this);\r\n\r\n\t\t/**\r\n\t\t * ========================================================================\r\n\t\t * charts/map\r\n\t\t * ========================================================================\r\n\t\t */\r\n\r\n\t\tr(\"MapChart\").setAll({\r\n\t\t\tprojection: geoMercator(),\r\n\t\t\tpanX: \"translateX\",\r\n\t\t\tpanY: \"translateY\",\r\n\t\t\tpinchZoom: true,\r\n\t\t\tzoomStep: 2,\r\n\t\t\tzoomLevel: 1,\r\n\t\t\trotationX: 0,\r\n\t\t\trotationY: 0,\r\n\t\t\trotationZ: 0,\r\n\t\t\tmaxZoomLevel: 32,\r\n\t\t\tminZoomLevel: 1,\r\n\t\t\twheelY: \"zoom\",\r\n\t\t\twheelX: \"none\",\r\n\t\t\tanimationEasing: $ease.out($ease.cubic),\r\n\t\t\twheelEasing: $ease.out($ease.cubic),\r\n\t\t\twheelDuration: 0,\r\n\t\t\twheelSensitivity: 1,\r\n\t\t\tmaxPanOut: 0.4,\r\n\t\t\tcenterMapOnZoomOut: true\r\n\t\t});\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"MapLine\");\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tprecision: 0.5,\r\n\t\t\t\trole: \"figure\",\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"grid\");\r\n\t\t}\r\n\r\n\t\tr(\"MapPolygonSeries\").setAll({\r\n\t\t\taffectsBounds: true\r\n\t\t})\r\n\r\n\r\n\t\tr(\"MapPointSeries\").setAll({\r\n\t\t\taffectsBounds: false,\r\n\t\t\tclipFront: false,\r\n\t\t\tclipBack: true,\r\n\t\t\tautoScale: false\r\n\t\t})\r\n\r\n\t\tr(\"ClusteredPointSeries\").setAll({\r\n\t\t\tminDistance: 20,\r\n\t\t\tscatterDistance: 3,\r\n\t\t\tscatterRadius: 8,\r\n\t\t\tstopClusterZoom: 0.95\r\n\t\t})\r\n\r\n\t\tr(\"MapLineSeries\").setAll({\r\n\t\t\taffectsBounds: false\r\n\t\t})\r\n\r\n\t\t{\r\n\t\t\tconst rule = r(\"MapPolygon\");\r\n\r\n\t\t\trule.setAll({\r\n\t\t\t\tprecision: 0.5,\r\n\t\t\t\tisMeasured: false,\r\n\t\t\t\trole: \"figure\",\r\n\t\t\t\tfillOpacity: 1,\r\n\t\t\t\tposition: \"absolute\",\r\n\t\t\t\tstrokeWidth: 0.2,\r\n\t\t\t\tstrokeOpacity: 1\r\n\t\t\t});\r\n\r\n\t\t\tsetColor(rule, \"fill\", ic, \"primaryButton\");\r\n\t\t\tsetColor(rule, \"stroke\", ic, \"background\");\r\n\t\t}\r\n\r\n\r\n\t\tr(\"Button\", [\"zoomtools\", \"home\"]).setAll({\r\n\t\t\tvisible: false\r\n\t\t});\r\n\r\n\t\t/**\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t * charts/map: Series\r\n\t\t * ------------------------------------------------------------------------\r\n\t\t */\r\n\r\n\t\tr(\"GraticuleSeries\").setAll({\r\n\t\t\tstep: 10\r\n\t\t});\r\n\t}\r\n}\r\n", "import {Adder} from \"d3-array\";\nimport {abs} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar areaSum = new Adder(),\n    areaRingSum = new Adder(),\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    areaStream.lineStart = areaStream.lineEnd = areaStream.point = noop;\n    areaSum.add(abs(areaRingSum));\n    areaRingSum = new Adder();\n  },\n  result: function() {\n    var area = areaSum / 2;\n    areaSum = new Adder();\n    return area;\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaPointFirst(x, y) {\n  areaStream.point = areaPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction areaPoint(x, y) {\n  areaRingSum.add(y0 * x - x0 * y);\n  x0 = x, y0 = y;\n}\n\nfunction areaRingEnd() {\n  areaPoint(x00, y00);\n}\n\nexport default areaStream;\n", "import {sqrt} from \"../math.js\";\n\n// TODO Enforce positive area for exterior, negative area for interior?\n\nvar X0 = 0,\n    Y0 = 0,\n    Z0 = 0,\n    X1 = 0,\n    Y1 = 0,\n    Z1 = 0,\n    X2 = 0,\n    Y2 = 0,\n    Z2 = 0,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar centroidStream = {\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.point = centroidPoint;\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  },\n  result: function() {\n    var centroid = Z2 ? [X2 / Z2, Y2 / Z2]\n        : Z1 ? [X1 / Z1, Y1 / Z1]\n        : Z0 ? [X0 / Z0, Y0 / Z0]\n        : [NaN, NaN];\n    X0 = Y0 = Z0 =\n    X1 = Y1 = Z1 =\n    X2 = Y2 = Z2 = 0;\n    return centroid;\n  }\n};\n\nfunction centroidPoint(x, y) {\n  X0 += x;\n  Y0 += y;\n  ++Z0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidPointFirstLine;\n}\n\nfunction centroidPointFirstLine(x, y) {\n  centroidStream.point = centroidPointLine;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidPointLine(x, y) {\n  var dx = x - x0, dy = y - y0, z = sqrt(dx * dx + dy * dy);\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingStart() {\n  centroidStream.point = centroidPointFirstRing;\n}\n\nfunction centroidRingEnd() {\n  centroidPointRing(x00, y00);\n}\n\nfunction centroidPointFirstRing(x, y) {\n  centroidStream.point = centroidPointRing;\n  centroidPoint(x00 = x0 = x, y00 = y0 = y);\n}\n\nfunction centroidPointRing(x, y) {\n  var dx = x - x0,\n      dy = y - y0,\n      z = sqrt(dx * dx + dy * dy);\n\n  X1 += z * (x0 + x) / 2;\n  Y1 += z * (y0 + y) / 2;\n  Z1 += z;\n\n  z = y0 * x - x0 * y;\n  X2 += z * (x0 + x);\n  Y2 += z * (y0 + y);\n  Z2 += z * 3;\n  centroidPoint(x0 = x, y0 = y);\n}\n\nexport default centroidStream;\n", "import {tau} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nexport default function PathContext(context) {\n  this._context = context;\n}\n\nPathContext.prototype = {\n  _radius: 4.5,\n  pointRadius: function(_) {\n    return this._radius = _, this;\n  },\n  polygonStart: function() {\n    this._line = 0;\n  },\n  polygonEnd: function() {\n    this._line = NaN;\n  },\n  lineStart: function() {\n    this._point = 0;\n  },\n  lineEnd: function() {\n    if (this._line === 0) this._context.closePath();\n    this._point = NaN;\n  },\n  point: function(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._context.moveTo(x, y);\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._context.lineTo(x, y);\n        break;\n      }\n      default: {\n        this._context.moveTo(x + this._radius, y);\n        this._context.arc(x, y, this._radius, 0, tau);\n        break;\n      }\n    }\n  },\n  result: noop\n};\n", "import {Adder} from \"d3-array\";\nimport {sqrt} from \"../math.js\";\nimport noop from \"../noop.js\";\n\nvar lengthSum = new Adder(),\n    lengthRing,\n    x00,\n    y00,\n    x0,\n    y0;\n\nvar lengthStream = {\n  point: noop,\n  lineStart: function() {\n    lengthStream.point = lengthPointFirst;\n  },\n  lineEnd: function() {\n    if (lengthRing) lengthPoint(x00, y00);\n    lengthStream.point = noop;\n  },\n  polygonStart: function() {\n    lengthRing = true;\n  },\n  polygonEnd: function() {\n    lengthRing = null;\n  },\n  result: function() {\n    var length = +lengthSum;\n    lengthSum = new Adder();\n    return length;\n  }\n};\n\nfunction lengthPointFirst(x, y) {\n  lengthStream.point = lengthPoint;\n  x00 = x0 = x, y00 = y0 = y;\n}\n\nfunction lengthPoint(x, y) {\n  x0 -= x, y0 -= y;\n  lengthSum.add(sqrt(x0 * x0 + y0 * y0));\n  x0 = x, y0 = y;\n}\n\nexport default lengthStream;\n", "// Simple caching for constant-radius points.\nlet cacheDigits, cacheAppend, cacheRadius, cacheCircle;\n\nexport default class PathString {\n  constructor(digits) {\n    this._append = digits == null ? append : appendRound(digits);\n    this._radius = 4.5;\n    this._ = \"\";\n  }\n  pointRadius(_) {\n    this._radius = +_;\n    return this;\n  }\n  polygonStart() {\n    this._line = 0;\n  }\n  polygonEnd() {\n    this._line = NaN;\n  }\n  lineStart() {\n    this._point = 0;\n  }\n  lineEnd() {\n    if (this._line === 0) this._ += \"Z\";\n    this._point = NaN;\n  }\n  point(x, y) {\n    switch (this._point) {\n      case 0: {\n        this._append`M${x},${y}`;\n        this._point = 1;\n        break;\n      }\n      case 1: {\n        this._append`L${x},${y}`;\n        break;\n      }\n      default: {\n        this._append`M${x},${y}`;\n        if (this._radius !== cacheRadius || this._append !== cacheAppend) {\n          const r = this._radius;\n          const s = this._;\n          this._ = \"\"; // stash the old string so we can cache the circle path fragment\n          this._append`m0,${r}a${r},${r} 0 1,1 0,${-2 * r}a${r},${r} 0 1,1 0,${2 * r}z`;\n          cacheRadius = r;\n          cacheAppend = this._append;\n          cacheCircle = this._;\n          this._ = s;\n        }\n        this._ += cacheCircle;\n        break;\n      }\n    }\n  }\n  result() {\n    const result = this._;\n    this._ = \"\";\n    return result.length ? result : null;\n  }\n}\n\nfunction append(strings) {\n  let i = 1;\n  this._ += strings[0];\n  for (const j = strings.length; i < j; ++i) {\n    this._ += arguments[i] + strings[i];\n  }\n}\n\nfunction appendRound(digits) {\n  const d = Math.floor(digits);\n  if (!(d >= 0)) throw new RangeError(`invalid digits: ${digits}`);\n  if (d > 15) return append;\n  if (d !== cacheDigits) {\n    const k = 10 ** d;\n    cacheDigits = d;\n    cacheAppend = function append(strings) {\n      let i = 1;\n      this._ += strings[0];\n      for (const j = strings.length; i < j; ++i) {\n        this._ += Math.round(arguments[i] * k) / k + strings[i];\n      }\n    };\n  }\n  return cacheAppend;\n}\n", "import {Adder} from \"d3-array\";\nimport {asin, atan2, cos, degrees, epsilon, epsilon2, hypot, radians, sin, sqrt} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nvar W0, W1,\n    X0, Y0, Z0,\n    X1, Y1, Z1,\n    X2, Y2, Z2,\n    lambda00, phi00, // first point\n    x0, y0, z0; // previous point\n\nvar centroidStream = {\n  sphere: noop,\n  point: centroidPoint,\n  lineStart: centroidLineStart,\n  lineEnd: centroidLineEnd,\n  polygonStart: function() {\n    centroidStream.lineStart = centroidRingStart;\n    centroidStream.lineEnd = centroidRingEnd;\n  },\n  polygonEnd: function() {\n    centroidStream.lineStart = centroidLineStart;\n    centroidStream.lineEnd = centroidLineEnd;\n  }\n};\n\n// Arithmetic mean of Cartesian vectors.\nfunction centroidPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  centroidPointCartesian(cosPhi * cos(lambda), cosPhi * sin(lambda), sin(phi));\n}\n\nfunction centroidPointCartesian(x, y, z) {\n  ++W0;\n  X0 += (x - X0) / W0;\n  Y0 += (y - Y0) / W0;\n  Z0 += (z - Z0) / W0;\n}\n\nfunction centroidLineStart() {\n  centroidStream.point = centroidLinePointFirst;\n}\n\nfunction centroidLinePointFirst(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidStream.point = centroidLinePoint;\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLinePoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      w = atan2(sqrt((w = y0 * z - z0 * y) * w + (w = z0 * x - x0 * z) * w + (w = x0 * y - y0 * x) * w), x0 * x + y0 * y + z0 * z);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidLineEnd() {\n  centroidStream.point = centroidPoint;\n}\n\n// See J. E. Brock, The Inertia Tensor for a Spherical Triangle,\n// J. Applied Mechanics 42, 239 (1975).\nfunction centroidRingStart() {\n  centroidStream.point = centroidRingPointFirst;\n}\n\nfunction centroidRingEnd() {\n  centroidRingPoint(lambda00, phi00);\n  centroidStream.point = centroidPoint;\n}\n\nfunction centroidRingPointFirst(lambda, phi) {\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  centroidStream.point = centroidRingPoint;\n  var cosPhi = cos(phi);\n  x0 = cosPhi * cos(lambda);\n  y0 = cosPhi * sin(lambda);\n  z0 = sin(phi);\n  centroidPointCartesian(x0, y0, z0);\n}\n\nfunction centroidRingPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  var cosPhi = cos(phi),\n      x = cosPhi * cos(lambda),\n      y = cosPhi * sin(lambda),\n      z = sin(phi),\n      cx = y0 * z - z0 * y,\n      cy = z0 * x - x0 * z,\n      cz = x0 * y - y0 * x,\n      m = hypot(cx, cy, cz),\n      w = asin(m), // line weight = angle\n      v = m && -w / m; // area weight multiplier\n  X2.add(v * cx);\n  Y2.add(v * cy);\n  Z2.add(v * cz);\n  W1 += w;\n  X1 += w * (x0 + (x0 = x));\n  Y1 += w * (y0 + (y0 = y));\n  Z1 += w * (z0 + (z0 = z));\n  centroidPointCartesian(x0, y0, z0);\n}\n\nexport default function(object) {\n  W0 = W1 =\n  X0 = Y0 = Z0 =\n  X1 = Y1 = Z1 = 0;\n  X2 = new Adder();\n  Y2 = new Adder();\n  Z2 = new Adder();\n  stream(object, centroidStream);\n\n  var x = +X2,\n      y = +Y2,\n      z = +Z2,\n      m = hypot(x, y, z);\n\n  // If the area-weighted ccentroid is undefined, fall back to length-weighted ccentroid.\n  if (m < epsilon2) {\n    x = X1, y = Y1, z = Z1;\n    // If the feature has zero length, fall back to arithmetic mean of point vectors.\n    if (W1 < epsilon) x = X0, y = Y0, z = Z0;\n    m = hypot(x, y, z);\n    // If the feature still has an undefined ccentroid, then return.\n    if (m < epsilon2) return [NaN, NaN];\n  }\n\n  return [atan2(y, x) * degrees, asin(z / m) * degrees];\n}\n", "import {Adder} from \"d3-array\";\nimport {atan2, cos, quarterPi, radians, sin, tau} from \"./math.js\";\nimport noop from \"./noop.js\";\nimport stream from \"./stream.js\";\n\nexport var areaRingSum = new Adder();\n\n// hello?\n\nvar areaSum = new Adder(),\n    lambda00,\n    phi00,\n    lambda0,\n    cosPhi0,\n    sinPhi0;\n\nexport var areaStream = {\n  point: noop,\n  lineStart: noop,\n  lineEnd: noop,\n  polygonStart: function() {\n    areaRingSum = new Adder();\n    areaStream.lineStart = areaRingStart;\n    areaStream.lineEnd = areaRingEnd;\n  },\n  polygonEnd: function() {\n    var areaRing = +areaRingSum;\n    areaSum.add(areaRing < 0 ? tau + areaRing : areaRing);\n    this.lineStart = this.lineEnd = this.point = noop;\n  },\n  sphere: function() {\n    areaSum.add(tau);\n  }\n};\n\nfunction areaRingStart() {\n  areaStream.point = areaPointFirst;\n}\n\nfunction areaRingEnd() {\n  areaPoint(lambda00, phi00);\n}\n\nfunction areaPointFirst(lambda, phi) {\n  areaStream.point = areaPoint;\n  lambda00 = lambda, phi00 = phi;\n  lambda *= radians, phi *= radians;\n  lambda0 = lambda, cosPhi0 = cos(phi = phi / 2 + quarterPi), sinPhi0 = sin(phi);\n}\n\nfunction areaPoint(lambda, phi) {\n  lambda *= radians, phi *= radians;\n  phi = phi / 2 + quarterPi; // half the angular distance from south pole\n\n  // Spherical excess E for a spherical triangle with vertices: south pole,\n  // previous point, current point.  Uses a formula derived from Cagnoli’s\n  // theorem.  See Todhunter, Spherical Trig. (1871), Sec. 103, Eq. (2).\n  var dLambda = lambda - lambda0,\n      sdLambda = dLambda >= 0 ? 1 : -1,\n      adLambda = sdLambda * dLambda,\n      cosPhi = cos(phi),\n      sinPhi = sin(phi),\n      k = sinPhi0 * sinPhi,\n      u = cosPhi0 * cosPhi + k * cos(adLambda),\n      v = k * sdLambda * sin(adLambda);\n  areaRingSum.add(atan2(v, u));\n\n  // Advance the previous points.\n  lambda0 = lambda, cosPhi0 = cosPhi, sinPhi0 = sinPhi;\n}\n\nexport default function(object) {\n  areaSum = new Adder();\n  stream(object, areaStream);\n  return areaSum * 2;\n}\n", "import {Adder} from \"d3-array\";\nimport {areaStream, areaRingSum} from \"./area.js\";\nimport {cartesian, cartesianCross, cartesianNormalizeInPlace, spherical} from \"./cartesian.js\";\nimport {abs, degrees, epsilon, radians} from \"./math.js\";\nimport stream from \"./stream.js\";\n\nvar lambda0, phi0, lambda1, phi1, // bounds\n    lambda2, // previous lambda-coordinate\n    lambda00, phi00, // first point\n    p0, // previous 3D point\n    deltaSum,\n    ranges,\n    range;\n\nvar boundsStream = {\n  point: boundsPoint,\n  lineStart: boundsLineStart,\n  lineEnd: boundsLineEnd,\n  polygonStart: function() {\n    boundsStream.point = boundsRingPoint;\n    boundsStream.lineStart = boundsRingStart;\n    boundsStream.lineEnd = boundsRingEnd;\n    deltaSum = new Adder();\n    areaStream.polygonStart();\n  },\n  polygonEnd: function() {\n    areaStream.polygonEnd();\n    boundsStream.point = boundsPoint;\n    boundsStream.lineStart = boundsLineStart;\n    boundsStream.lineEnd = boundsLineEnd;\n    if (areaRingSum < 0) lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n    else if (deltaSum > epsilon) phi1 = 90;\n    else if (deltaSum < -epsilon) phi0 = -90;\n    range[0] = lambda0, range[1] = lambda1;\n  },\n  sphere: function() {\n    lambda0 = -(lambda1 = 180), phi0 = -(phi1 = 90);\n  }\n};\n\nfunction boundsPoint(lambda, phi) {\n  ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n}\n\nfunction linePoint(lambda, phi) {\n  var p = cartesian([lambda * radians, phi * radians]);\n  if (p0) {\n    var normal = cartesianCross(p0, p),\n        equatorial = [normal[1], -normal[0], 0],\n        inflection = cartesianCross(equatorial, normal);\n    cartesianNormalizeInPlace(inflection);\n    inflection = spherical(inflection);\n    var delta = lambda - lambda2,\n        sign = delta > 0 ? 1 : -1,\n        lambdai = inflection[0] * degrees * sign,\n        phii,\n        antimeridian = abs(delta) > 180;\n    if (antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = inflection[1] * degrees;\n      if (phii > phi1) phi1 = phii;\n    } else if (lambdai = (lambdai + 360) % 360 - 180, antimeridian ^ (sign * lambda2 < lambdai && lambdai < sign * lambda)) {\n      phii = -inflection[1] * degrees;\n      if (phii < phi0) phi0 = phii;\n    } else {\n      if (phi < phi0) phi0 = phi;\n      if (phi > phi1) phi1 = phi;\n    }\n    if (antimeridian) {\n      if (lambda < lambda2) {\n        if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n      } else {\n        if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n      }\n    } else {\n      if (lambda1 >= lambda0) {\n        if (lambda < lambda0) lambda0 = lambda;\n        if (lambda > lambda1) lambda1 = lambda;\n      } else {\n        if (lambda > lambda2) {\n          if (angle(lambda0, lambda) > angle(lambda0, lambda1)) lambda1 = lambda;\n        } else {\n          if (angle(lambda, lambda1) > angle(lambda0, lambda1)) lambda0 = lambda;\n        }\n      }\n    }\n  } else {\n    ranges.push(range = [lambda0 = lambda, lambda1 = lambda]);\n  }\n  if (phi < phi0) phi0 = phi;\n  if (phi > phi1) phi1 = phi;\n  p0 = p, lambda2 = lambda;\n}\n\nfunction boundsLineStart() {\n  boundsStream.point = linePoint;\n}\n\nfunction boundsLineEnd() {\n  range[0] = lambda0, range[1] = lambda1;\n  boundsStream.point = boundsPoint;\n  p0 = null;\n}\n\nfunction boundsRingPoint(lambda, phi) {\n  if (p0) {\n    var delta = lambda - lambda2;\n    deltaSum.add(abs(delta) > 180 ? delta + (delta > 0 ? 360 : -360) : delta);\n  } else {\n    lambda00 = lambda, phi00 = phi;\n  }\n  areaStream.point(lambda, phi);\n  linePoint(lambda, phi);\n}\n\nfunction boundsRingStart() {\n  areaStream.lineStart();\n}\n\nfunction boundsRingEnd() {\n  boundsRingPoint(lambda00, phi00);\n  areaStream.lineEnd();\n  if (abs(deltaSum) > epsilon) lambda0 = -(lambda1 = 180);\n  range[0] = lambda0, range[1] = lambda1;\n  p0 = null;\n}\n\n// Finds the left-right distance between two longitudes.\n// This is almost the same as (lambda1 - lambda0 + 360°) % 360°, except that we want\n// the distance between ±180° to be 360°.\nfunction angle(lambda0, lambda1) {\n  return (lambda1 -= lambda0) < 0 ? lambda1 + 360 : lambda1;\n}\n\nfunction rangeCompare(a, b) {\n  return a[0] - b[0];\n}\n\nfunction rangeContains(range, x) {\n  return range[0] <= range[1] ? range[0] <= x && x <= range[1] : x < range[0] || range[1] < x;\n}\n\nexport default function(feature) {\n  var i, n, a, b, merged, deltaMax, delta;\n\n  phi1 = lambda1 = -(lambda0 = phi0 = Infinity);\n  ranges = [];\n  stream(feature, boundsStream);\n\n  // First, sort ranges by their minimum longitudes.\n  if (n = ranges.length) {\n    ranges.sort(rangeCompare);\n\n    // Then, merge any ranges that overlap.\n    for (i = 1, a = ranges[0], merged = [a]; i < n; ++i) {\n      b = ranges[i];\n      if (rangeContains(a, b[0]) || rangeContains(a, b[1])) {\n        if (angle(a[0], b[1]) > angle(a[0], a[1])) a[1] = b[1];\n        if (angle(b[0], a[1]) > angle(a[0], a[1])) a[0] = b[0];\n      } else {\n        merged.push(a = b);\n      }\n    }\n\n    // Finally, find the largest gap between the merged ranges.\n    // The final bounding box will be the inverse of this gap.\n    for (deltaMax = -Infinity, n = merged.length - 1, i = 0, a = merged[n]; i <= n; a = b, ++i) {\n      b = merged[i];\n      if ((delta = angle(a[1], b[0])) > deltaMax) deltaMax = delta, lambda0 = b[0], lambda1 = a[1];\n    }\n  }\n\n  ranges = range = null;\n\n  return lambda0 === Infinity || phi0 === Infinity\n      ? [[NaN, NaN], [NaN, NaN]]\n      : [[lambda0, phi0], [lambda1, phi1]];\n}\n", "import type { IGeoPoint } from \"../../core/util/IGeoPoint\";\nimport * as $math from \"../../core/util/Math\";\nimport { geoCircle, geoCentroid, geoBounds, geoArea } from \"d3-geo\";\n\n/**\n * Returns a GeoJSON representation of a circle, suitable for use as `geometry` value\n * in a [[MapPolygon]] in a [[MapPolygonSeries]].\n * \n * @param   geoPoint  Coordinates\n * @param   radius    Circle radius in degrees\n * @return            Polygon geometry\n */\nexport function getGeoCircle(geoPoint: IGeoPoint, radius: number): GeoJSON.Polygon {\n\treturn geoCircle().center([geoPoint.longitude, geoPoint.latitude]).radius(radius)();\n}\n\n/**\n * Returns geo centroid of a geometry\n */\nexport function getGeoCentroid(geometry: GeoJSON.GeometryObject): IGeoPoint {\n\tconst centroid = geoCentroid(geometry);\n\treturn { longitude: centroid[0], latitude: centroid[1] };\n}\n\n/**\n * Returns geo area of a geometry\n */\nexport function getGeoArea(geometry: GeoJSON.GeometryObject): number {\n\treturn geoArea(geometry);\n}\n\n/**\n * Returns bounds of a geometry\n */\nexport function getGeoBounds(geometry: GeoJSON.GeometryObject): { left: number, right: number, top: number, bottom: number } {\n\tconst bounds = geoBounds(geometry);\n\n\tif (bounds) {\n\t\tconst geoBounds = { left: bounds[0][0], right: bounds[1][0], top: bounds[1][1], bottom: bounds[0][1] };\n\t\tif (geoBounds.right < geoBounds.left) {\n\t\t\tgeoBounds.right = 180;\n\t\t\tgeoBounds.left = -180;\n\t\t}\n\t\treturn geoBounds;\n\t}\n\treturn { left: 0, right: 0, top: 0, bottom: 0 };\n}\n\n/**\n * Returns a GeoJSON representation of a rectangle, suitable for use\n * as `geometry` value in a [[MapPolygon]] in a [[MapPolygonSeries]].\n * \n * @param   north  North latitude\n * @param   east   East longitude\n * @param   south  South latitude\n * @param   west   West longitude\n * @return         polygon geometry\n */\nexport function getGeoRectangle(north: number, east: number, south: number, west: number): GeoJSON.MultiPolygon {\n\n\tlet multiPolygon: Array<Array<Array<[number, number]>>> = [];\n\n\tif (west <= -180) {\n\t\twest = -179.9999;\n\t}\n\tif (south <= -90) {\n\t\tsouth = -89.9999;\n\t}\n\tif (north >= 90) {\n\t\tnorth = 89.9999;\n\t}\n\tif (east >= 180) {\n\t\teast = 179.9999;\n\t}\n\n\n\tlet stepLong = Math.min(90, (east - west) / Math.ceil((east - west) / 90));\n\tlet stepLat = (north - south) / Math.ceil((north - south) / 90);\n\n\tfor (let ln = west; ln < east; ln = ln + stepLong) {\n\t\tlet surface: Array<[number, number]> = [];\n\t\tmultiPolygon.push([surface]);\n\n\t\tif (ln + stepLong > east) {\n\t\t\tstepLong = east - ln;\n\t\t}\n\n\t\tfor (let ll = ln; ll <= ln + stepLong; ll = ll + 5) {\n\t\t\tsurface.push([ll, north]);\n\t\t}\n\n\t\tfor (let lt = north; lt >= south; lt = lt - stepLat) {\n\t\t\tsurface.push([ln + stepLong, lt]);\n\t\t}\n\n\t\tfor (let ll = ln + stepLong; ll >= ln; ll = ll - 5) {\n\t\t\tsurface.push([ll, south]);\n\t\t}\n\n\t\tfor (let lt = south; lt <= north; lt = lt + stepLat) {\n\t\t\tsurface.push([ln, lt]);\n\t\t}\n\t}\n\n\treturn { type: \"MultiPolygon\", coordinates: multiPolygon };\n}\n\n/**\n * Update longitudes and latitudes that wrap around -180/180 and -90/90 values.\n * \n * @param   geoPoint  Input coordinates\n * @return            Updated coordinates\n */\nexport function normalizeGeoPoint(geoPoint: IGeoPoint): IGeoPoint {\n\tlet longitude = wrapAngleTo180(geoPoint.longitude);\n\tlet latitude = Math.asin(Math.sin((geoPoint.latitude * $math.RADIANS))) * $math.DEGREES;\n\n\tlet latitude180 = wrapAngleTo180(geoPoint.latitude);\n\n\tif (Math.abs(latitude180) > 90) {\n\t\tlongitude = wrapAngleTo180(longitude + 180);\n\t}\n\n\tgeoPoint.longitude = longitude;\n\tgeoPoint.latitude = latitude;\n\n\treturn geoPoint;\n}\n\n/**\n * @ignore\n */\nexport function wrapAngleTo180(angle: number): number {\n\tangle = angle % 360;\n\n\tif (angle > 180) {\n\t\tangle -= 360;\n\t}\n\tif (angle < -180) {\n\t\tangle += 360;\n\t}\n\n\treturn angle;\n}", "import type { MapSeries } from \"./MapSeries\";\nimport type { MapPointSeries } from \"./MapPointSeries\";\nimport type { GeoProjection, GeoPath } from \"d3-geo\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport type { IGeoPoint } from \"../../core/util/IGeoPoint\";\nimport type { Time } from \"../../core/util/Animation\";\nimport type { ZoomControl } from \"./ZoomControl\";\nimport type { Animation } from \"../../core/util/Entity\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { IMapPolygonSeriesDataItem } from \"./MapPolygonSeries\";\n\nimport { MapChartDefaultTheme } from \"./MapChartDefaultTheme\";\nimport { SerialChart, ISerialChartPrivate, ISerialChartSettings, ISerialChartEvents } from \"../../core/render/SerialChart\";\nimport { Rectangle } from \"../../core/render/Rectangle\";\nimport { geoPath } from \"d3-geo\";\nimport { Color } from \"../../core/util/Color\";\nimport { registry } from \"../../core/Registry\";\n\nimport * as $math from \"../../core/util/Math\";\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $mapUtils from \"./MapUtils\";\nimport * as $object from \"../../core/util/Object\";\nimport * as $utils from \"../../core/util/Utils\";\n\nimport type { IDisposer } from \"../../core/util/Disposer\";\nimport type { ISpritePointerEvent } from \"../../core/render/Sprite\";\n\n\nexport interface IMapChartSettings extends ISerialChartSettings {\n\n\t/**\n\t * A projection to use when plotting the map.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/#Projections} for more info\n\t */\n\tprojection?: GeoProjection;\n\n\t/**\n\t * Current zoom level.\n\t */\n\tzoomLevel?: number;\n\n\t/**\n\t * current x position of a map\n\t */\n\ttranslateX?: number;\n\n\t/**\n\t * current y position of a map\n\t */\n\ttranslateY?: number;\n\n\t/**\n\t * Vertical centering of the map.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/#Centering_the_map} for more info\n\t */\n\trotationY?: number;\n\n\t/**\n\t * Horizontal centering of the map.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/#Centering_the_map} for more info\n\t */\n\trotationX?: number;\n\n\t/**\n\t * Depth centering of the map.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/#Centering_the_map} for more info\n\t */\n\trotationZ?: number;\n\n\t/**\n\t * Highest zoom level map is allowed to zoom in to.\n\t *\n\t * @deault 32\n\t */\n\tmaxZoomLevel?: number;\n\n\t/**\n\t * Lowest zoom level map is allowed to zoom in to.\n\t *\n\t * @deault 1\n\t */\n\tminZoomLevel?: number;\n\n\t/**\n\t * Increment zoom level by `zoomStep` when user zooms in via [[ZoomControl]] or\n\t * API.\n\t *\n\t * @default 2\n\t */\n\tzoomStep?: number;\n\n\t/**\n\t * Defines what happens when map is being dragged horizontally.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Panning} for more info\n\t * @default \"translateX\"\n\t */\n\tpanX?: \"none\" | \"rotateX\" | \"translateX\";\n\n\t/**\n\t * Defines what happens when map is being dragged vertically.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Panning} for more info\n\t * @default \"translateY\"\n\t */\n\tpanY?: \"none\" | \"rotateY\" | \"translateY\";\n\n\t/**\n\t * Enables pinch-zooming of the map on multi-touch devices.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Pinch_zoom} for more info\n\t * @default true\n\t */\n\tpinchZoom?: boolean;\n\n\t/**\n\t * Defines what happens when horizontal mouse wheel (only some mouses do have such a wheel)\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Mouse_wheel_behavior} for more info\n\t * @default \"none\"\n\t */\n\twheelX?: \"none\" | \"zoom\" | \"rotateX\" | \"rotateY\";\n\n\t/**\n\t * Defines what happens when mouse wheel is turned.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Mouse_wheel_behavior} for more info\n\t * @default \"zoom\"\n\t */\n\twheelY?: \"none\" | \"zoom\" | \"rotateX\" | \"rotateY\";\n\n\t/**\n\t * Sensitivity of a mouse wheel.\n\t *\n\t * NOTE: this setting is ignored when `wheelX` or `wheelY` is set to `\"zoom\"`.\n\t *\n\t * @default 1\n\t */\n\twheelSensitivity?: number;\n\n\t/**\n\t * Duration of mouse-wheel action animation, in milliseconds.\n\t *\n\t * NOTE: this setting is ignored when `wheelX` or `wheelY` is set to `\"zoom\"`.\n\t */\n\twheelDuration?: number;\n\n\t/**\n\t * An easing function to use for mouse wheel action animations.\n\t *\n\t * NOTE: this setting is ignored when `wheelX` or `wheelY` is set to `\"zoom\"`.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/animations/#Easing_functions} for more info\n\t * @default am5.ease.out($ease.cubic)\n\t */\n\twheelEasing?: (t: Time) => Time;\n\n\t/**\n\t * Duration of zoom/pan animations, in milliseconds.\n\t */\n\tanimationDuration?: number;\n\n\t/**\n\t * An easing function to use for zoom/pan animations.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/concepts/animations/#Easing_functions} for more info\n\t * @default am5.ease.out($ease.cubic)\n\t */\n\tanimationEasing?: (t: Time) => Time;\n\n\n\t/**\n\t * A [[ZoomControl]] instance.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/#Zoom_control} for more info\n\t */\n\tzoomControl?: ZoomControl;\n\n\t/**\n\t * Initial/home zoom level.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Initial_position_and_zoom} for more info\n\t */\n\thomeZoomLevel?: number;\n\n\t/**\n\t * Initial/home rotationX.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Initial_position_and_zoom} for more info\n\t */\n\thomeRotationX?: number;\n\n\t/**\n\t * Initial/home rotationY.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Initial_position_and_zoom} for more info\n\t */\n\thomeRotationY?: number;\n\n\t/**\n\t * Initial coordinates to center map on load or `goHome()` call.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Initial_position_and_zoom} for more info\n\t */\n\thomeGeoPoint?: IGeoPoint;\n\n\t/**\n\t * How much of a map can go outside the viewport.\n\t *\n\t * @default 0.4\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Panning_outside_viewport} for more info\n\t */\n\tmaxPanOut?: number;\n\n\t/**\n\t * Setting `true` means that the map will automatically center itself (or go\n\t * to `homeGeoPoint` if set) when fully zoomed out.\n\t *\n\t * `false` would mean that zoom out will be centered around the mouse\n\t * cursor (when zooming using wheel), or current map position.\n\t * \n\t * @default true\n\t * @since 5.2.1\n\t */\n\tcenterMapOnZoomOut?: boolean;\n\n}\n\nexport interface IMapChartPrivate extends ISerialChartPrivate {\n\n\t/**\n\t * @ignore\n\t */\n\tgeoPath: GeoPath;\n\n\t/**\n\t * @ignore\n\t */\n\tmapScale: number;\n\n}\n\n\nexport interface IMapChartEvents extends ISerialChartEvents {\n\n\t/**\n\t * Invoked when geo bounds of the map change, usually after map is\n\t * initialized.\n\t */\n\tgeoboundschanged: {};\n\n}\n\n\nexport class MapChart extends SerialChart {\n\tpublic static className: string = \"MapChart\";\n\tpublic static classNames: Array<string> = SerialChart.classNames.concat([MapChart.className]);\n\n\tdeclare public _settings: IMapChartSettings;\n\tdeclare public _privateSettings: IMapChartPrivate;\n\tdeclare public _seriesType: MapSeries;\n\tdeclare public _events: IMapChartEvents;\n\n\tprotected _downTranslateX: number | undefined;\n\tprotected _downTranslateY: number | undefined;\n\tprotected _downRotationX: number | undefined;\n\tprotected _downRotationY: number | undefined;\n\tprotected _downRotationZ: number | undefined;\n\tprotected _pLat: number = 0;\n\tprotected _pLon: number = 0;\n\n\tprotected _movePoints: { [index: number]: IPoint } = {};\n\tprotected _downZoomLevel: number = 1;\n\tprotected _doubleDownDistance: number = 0;\n\n\tprotected _dirtyGeometries: boolean = false;\n\tprotected _geometryColection: GeoJSON.GeometryCollection = { type: \"GeometryCollection\", geometries: [] };\n\n\tpublic _centerLocation: [number, number] | null = null;\n\n\tprotected _za?: Animation<this[\"_settings\"][\"zoomLevel\"]>;\n\tprotected _rxa?: Animation<this[\"_settings\"][\"rotationX\"]>;\n\tprotected _rya?: Animation<this[\"_settings\"][\"rotationY\"]>;\n\tprotected _txa?: Animation<this[\"_settings\"][\"translateX\"]>;\n\tprotected _tya?: Animation<this[\"_settings\"][\"translateY\"]>;\n\n\tprotected _mapBounds = [[0, 0], [0, 0]];\n\n\tprotected _geoCentroid: IGeoPoint = { longitude: 0, latitude: 0 };\n\tprotected _geoBounds: { left: number, right: number, top: number, bottom: number } = { left: 0, right: 0, top: 0, bottom: 0 };\n\tprotected _prevGeoBounds: { left: number, right: number, top: number, bottom: number } = { left: 0, right: 0, top: 0, bottom: 0 };\n\n\tprotected _dispatchBounds: boolean = false;\n\n\tprotected _wheelDp: IDisposer | undefined;\n\n\tprotected _pw?: number;\n\tprotected _ph?: number;\n\n\tprotected _mapFitted: boolean = false;\n\n\tprotected _centerX: number = 0;\n\tprotected _centerY: number = 0;\n\n\tprotected _makeGeoPath() {\n\t\tconst projection = this.get(\"projection\")!;\n\t\tconst path = geoPath();\n\t\tpath.projection(projection);\n\t\tthis.setPrivateRaw(\"geoPath\", path);\n\t}\n\n\t/**\n\t * Returns a geoPoint of the current zoom position.\n\t * \n\t * You can later use it to restore zoom position, e.g.: `chart.zoomToGeoPoint(geoPoint, zoomLevel, true)`.\n\t *\n\t * @since 5.2.19\n\t */\n\tpublic geoPoint() {\n\t\treturn this.invert(this.seriesContainer.toGlobal({ x: this.width() / 2, y: this.height() / 2 }));\n\t}\n\n\t/**\n\t * Returns coordinates to geographical center of the map.\n\t */\n\tpublic geoCentroid() {\n\t\treturn this._geoCentroid;\n\t}\n\n\t/**\n\t * Returns geographical bounds of the map.\n\t */\n\tpublic geoBounds() {\n\t\treturn this._geoBounds;\n\t}\n\n\tprotected _handleSetWheel() {\n\n\t\tconst wheelX = this.get(\"wheelX\");\n\t\tconst wheelY = this.get(\"wheelY\");\n\t\tconst chartContainer = this.chartContainer;\n\n\t\tif (wheelX != \"none\" || wheelY != \"none\") {\n\t\t\tif (this._wheelDp) {\n\t\t\t\tthis._wheelDp.dispose();\n\t\t\t}\n\n\t\t\tthis._wheelDp = chartContainer.events.on(\"wheel\", (event) => {\n\t\t\t\tconst wheelEasing = this.get(\"wheelEasing\")!;\n\t\t\t\tconst wheelSensitivity = this.get(\"wheelSensitivity\", 1);\n\t\t\t\tconst wheelDuration = this.get(\"wheelDuration\", 0);\n\n\t\t\t\tconst wheelEvent = event.originalEvent;\n\n\t\t\t\t// Ignore wheel event if it is happening on a non-chart element, e.g. if\n\t\t\t\t// some page element is over the chart.\n\t\t\t\tif ($utils.isLocalEvent(wheelEvent, this)) {\n\t\t\t\t\twheelEvent.preventDefault();\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconst point = chartContainer._display.toLocal(event.point);\n\n\t\t\t\tif ((wheelY == \"zoom\")) {\n\t\t\t\t\tthis._handleWheelZoom(wheelEvent.deltaY, point);\n\t\t\t\t}\n\t\t\t\telse if (wheelY == \"rotateY\") {\n\t\t\t\t\tthis._handleWheelRotateY(wheelEvent.deltaY / 5 * wheelSensitivity, wheelDuration, wheelEasing);\n\t\t\t\t}\n\t\t\t\telse if (wheelY == \"rotateX\") {\n\t\t\t\t\tthis._handleWheelRotateX(wheelEvent.deltaY / 5 * wheelSensitivity, wheelDuration, wheelEasing);\n\t\t\t\t}\n\n\t\t\t\tif ((wheelX == \"zoom\")) {\n\t\t\t\t\tthis._handleWheelZoom(wheelEvent.deltaX, point);\n\t\t\t\t}\n\t\t\t\telse if (wheelX == \"rotateY\") {\n\t\t\t\t\tthis._handleWheelRotateY(wheelEvent.deltaX / 5 * wheelSensitivity, wheelDuration, wheelEasing);\n\t\t\t\t}\n\t\t\t\telse if (wheelX == \"rotateX\") {\n\t\t\t\t\tthis._handleWheelRotateX(wheelEvent.deltaX / 5 * wheelSensitivity, wheelDuration, wheelEasing);\n\t\t\t\t}\n\n\t\t\t});\n\n\t\t\tthis._disposers.push(this._wheelDp);\n\t\t}\n\t\telse {\n\t\t\tif (this._wheelDp) {\n\t\t\t\tthis._wheelDp.dispose();\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tconst projection = this.get(\"projection\")!;\n\t\tconst w = this.innerWidth();\n\t\tconst h = this.innerHeight();\n\n\t\tconst previousGeometries = this._geometryColection.geometries;\n\n\t\tif (this.isDirty(\"projection\")) {\n\t\t\tthis._makeGeoPath();\n\t\t\tthis.markDirtyProjection();\n\t\t\tthis._fitMap();\n\n\t\t\tprojection.scale(this.getPrivate(\"mapScale\") * this.get(\"zoomLevel\", 1));\n\t\t\tif (projection.rotate) {\n\t\t\t\tprojection.rotate([this.get(\"rotationX\", 0), this.get(\"rotationY\", 0), this.get(\"rotationZ\", 0)])\n\t\t\t}\n\n\t\t\tlet prev = this._prevSettings.projection;\n\t\t\tif (prev && prev != projection) {\n\t\t\t\tlet hw = w / 2;\n\t\t\t\tlet hh = h / 2;\n\t\t\t\tif (prev.invert) {\n\t\t\t\t\tlet centerLocation = prev.invert([hw, hh]);\n\n\t\t\t\t\tif (centerLocation) {\n\n\t\t\t\t\t\tlet xy = projection(centerLocation);\n\t\t\t\t\t\tif (xy) {\n\t\t\t\t\t\t\tlet translate = projection.translate();\n\n\t\t\t\t\t\t\tlet xx = hw - ((xy[0] - translate[0]));\n\t\t\t\t\t\t\tlet yy = hh - ((xy[1] - translate[1]));\n\n\t\t\t\t\t\t\tprojection.translate([xx, yy])\n\n\t\t\t\t\t\t\tthis.setRaw(\"translateX\", xx);\n\t\t\t\t\t\t\tthis.setRaw(\"translateY\", yy);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"wheelX\") || this.isDirty(\"wheelY\")) {\n\t\t\tthis._handleSetWheel();\n\t\t}\n\t\tif (this._dirtyGeometries) {\n\t\t\tthis._geometryColection.geometries = [];\n\n\t\t\tthis.series.each((series) => {\n\t\t\t\t$array.pushAll(this._geometryColection.geometries, series._geometries);\n\t\t\t})\n\n\n\t\t\tthis._fitMap();\n\t\t}\n\n\t\tif (previousGeometries.length != 0 && (w != this._pw || h != this._ph || this._dirtyGeometries)) {\n\t\t\tif (w > 0 && h > 0) {\n\t\t\t\tlet hw = w / 2;\n\t\t\t\tlet hh = h / 2;\n\n\t\t\t\tprojection.fitSize([w, h], this._geometryColection);\n\t\t\t\tconst newScale = projection.scale();\n\n\t\t\t\tthis.setPrivateRaw(\"mapScale\", newScale);\n\t\t\t\tprojection.scale(newScale * this.get(\"zoomLevel\", 1));\n\n\t\t\t\tif (this._centerLocation) {\n\t\t\t\t\tlet xy = projection(this._centerLocation);\n\t\t\t\t\tif (xy) {\n\t\t\t\t\t\tlet translate = projection.translate();\n\n\t\t\t\t\t\tlet xx = hw - ((xy[0] - translate[0]));\n\t\t\t\t\t\tlet yy = hh - ((xy[1] - translate[1]));\n\n\t\t\t\t\t\tprojection.translate([xx, yy])\n\n\t\t\t\t\t\tthis.setRaw(\"translateX\", xx);\n\t\t\t\t\t\tthis.setRaw(\"translateY\", yy);\n\n\t\t\t\t\t\tthis._centerX = translate[0];\n\t\t\t\t\t\tthis._centerY = translate[1];\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tthis.markDirtyProjection();\n\n\t\t\t\tconst geoPath = this.getPrivate(\"geoPath\");\n\t\t\t\tthis._mapBounds = geoPath.bounds(this._geometryColection);\n\t\t\t}\n\t\t}\n\n\t\tthis._pw = w;\n\t\tthis._ph = h;\n\n\t\tif (this.isDirty(\"zoomControl\")) {\n\t\t\tconst previous = this._prevSettings.zoomControl;\n\t\t\tconst zoomControl = this.get(\"zoomControl\")!;\n\t\t\tif (zoomControl !== previous) {\n\t\t\t\tthis._disposeProperty(\"zoomControl\");\n\t\t\t\tif (previous) {\n\t\t\t\t\tprevious.dispose();\n\t\t\t\t}\n\t\t\t\tif (zoomControl) {\n\t\t\t\t\tzoomControl.setPrivate(\"chart\", this);\n\t\t\t\t\tthis.children.push(zoomControl);\n\t\t\t\t}\n\n\t\t\t\tthis.setRaw(\"zoomControl\", zoomControl);\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"zoomLevel\")) {\n\t\t\tprojection.scale(this.getPrivate(\"mapScale\") * this.get(\"zoomLevel\", 1));\n\t\t\tthis.markDirtyProjection();\n\n\t\t\tthis.series.each((series) => {\n\t\t\t\tif (series.isType<MapPointSeries>(\"MapPointSeries\")) {\n\t\t\t\t\tif (series.get(\"autoScale\")) {\n\t\t\t\t\t\t$array.each(series.dataItems, (dataItem) => {\n\t\t\t\t\t\t\tconst bullets = dataItem.bullets;\n\t\t\t\t\t\t\tif (bullets) {\n\t\t\t\t\t\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\t\t\t\t\t\tif (sprite) {\n\t\t\t\t\t\t\t\t\t\tsprite.set(\"scale\", this.get(\"zoomLevel\"));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\n\t\t\tconst zoomControl = this.get(\"zoomControl\");\n\t\t\tif (zoomControl) {\n\t\t\t\tconst zoomLevel = this.get(\"zoomLevel\", 1);\n\n\t\t\t\tif (zoomLevel == this.get(\"minZoomLevel\", 1)) {\n\t\t\t\t\tthis.root.events.once(\"frameended\", () =>{\n\t\t\t\t\t\tzoomControl.minusButton.set(\"disabled\", true);\n\t\t\t\t\t})\t\t\t\t\t\t\t\t\t\t\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tzoomControl.minusButton.set(\"disabled\", false);\n\t\t\t\t}\n\n\t\t\t\tif (zoomLevel == this.get(\"maxZoomLevel\", 32)) {\n\t\t\t\t\tzoomControl.plusButton.set(\"disabled\", true);\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tzoomControl.plusButton.set(\"disabled\", false);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"translateX\") || this.isDirty(\"translateY\")) {\n\t\t\tprojection.translate([this.get(\"translateX\", this.width() / 2), this.get(\"translateY\", this.height() / 2)])\n\t\t\tthis.markDirtyProjection();\n\t\t}\n\n\t\tif (projection.rotate) {\n\t\t\tif (this.isDirty(\"rotationX\") || this.isDirty(\"rotationY\") || this.isDirty(\"rotationZ\")) {\n\t\t\t\tprojection.rotate([this.get(\"rotationX\", 0), this.get(\"rotationY\", 0), this.get(\"rotationZ\", 0)])\n\t\t\t\tthis.markDirtyProjection();\n\t\t\t}\n\t\t}\n\n\t\tif (this.isDirty(\"pinchZoom\") || this.get(\"panX\") || this.get(\"panY\")) {\n\t\t\tthis._setUpTouch();\n\t\t}\n\t}\n\n\n\tprotected _fitMap() {\n\t\tconst projection = this.get(\"projection\")!;\n\n\t\tlet w = this.innerWidth();\n\t\tlet h = this.innerHeight();\n\n\t\tif (w > 0 && h > 0) {\n\t\t\tprojection.fitSize([w, h], this._geometryColection);\n\t\t\tthis.setPrivateRaw(\"mapScale\", projection.scale());\n\n\t\t\tconst translate = projection.translate();\n\n\t\t\tthis.setRaw(\"translateX\", translate[0]);\n\t\t\tthis.setRaw(\"translateY\", translate[1]);\n\n\t\t\tthis._centerX = translate[0];\n\t\t\tthis._centerY = translate[1];\n\n\t\t\tconst geoPath = this.getPrivate(\"geoPath\");\n\t\t\tthis._mapBounds = geoPath.bounds(this._geometryColection);\n\n\t\t\tthis._geoCentroid = $mapUtils.getGeoCentroid(this._geometryColection);\n\n\t\t\tconst bounds = $mapUtils.getGeoBounds(this._geometryColection);\n\t\t\tthis._geoBounds = bounds;\n\n\t\t\tif (this._geometryColection.geometries.length > 0) {\n\n\t\t\t\tbounds.left = $math.round(this._geoBounds.left, 3);\n\t\t\t\tbounds.right = $math.round(this._geoBounds.right, 3);\n\t\t\t\tbounds.top = $math.round(this._geoBounds.top, 3);\n\t\t\t\tbounds.bottom = $math.round(this._geoBounds.bottom, 3);\n\n\t\t\t\tconst prevGeoBounds = this._prevGeoBounds;\n\n\t\t\t\tif (prevGeoBounds && !$utils.sameBounds(bounds, prevGeoBounds)) {\n\t\t\t\t\tthis._dispatchBounds = true;\n\t\t\t\t\tthis._prevGeoBounds = bounds;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._mapFitted = true;\n\t\t}\n\t}\n\n\t/**\n\t * Returns geographical coordinates for calculated or manual center of the\n\t * map.\n\t */\n\tpublic homeGeoPoint(): IGeoPoint {\n\t\tlet homeGeoPoint = this.get(\"homeGeoPoint\");\n\t\tif (!homeGeoPoint) {\n\t\t\tconst geoPath = this.getPrivate(\"geoPath\");\n\t\t\tconst bounds = geoPath.bounds(this._geometryColection);\n\n\t\t\tconst left = bounds[0][0];\n\t\t\tconst top = bounds[0][1];\n\n\t\t\tconst right = bounds[1][0];\n\t\t\tconst bottom = bounds[1][1];\n\n\t\t\thomeGeoPoint = this.invert({ x: left + (right - left) / 2, y: top + (bottom - top) / 2 });\n\t\t}\n\t\treturn homeGeoPoint;\n\t}\n\n\t/**\n\t * Repositions the map to the \"home\" zoom level and center coordinates.\n\t *\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Resetting_position_level} for more info\n\t * @param  duration  Animation duration in milliseconds\n\t */\n\tpublic goHome(duration?: number) {\n\t\tthis.zoomToGeoPoint(this.homeGeoPoint(), this.get(\"homeZoomLevel\", 1), true, duration, this.get(\"homeRotationX\"), this.get(\"homeRotationY\"));\n\t}\n\n\tpublic _updateChildren() {\n\t\tconst projection = this.get(\"projection\")!;\n\t\tif (projection.invert) {\n\t\t\tlet w = this.innerWidth();\n\t\t\tlet h = this.innerHeight();\n\t\t\tif (w > 0 && h > 0) {\n\t\t\t\tthis._centerLocation = projection.invert([this.innerWidth() / 2, this.innerHeight() / 2]);\n\t\t\t}\n\t\t}\n\t\tsuper._updateChildren();\n\t}\n\n\tpublic _afterChanged() {\n\t\tsuper._afterChanged();\n\t\tif (this._dispatchBounds) {\n\t\t\tthis._dispatchBounds = false;\n\t\t\tconst type = \"geoboundschanged\";\n\t\t\tif (this.events.isEnabled(type)) {\n\t\t\t\tthis.events.dispatch(type, { type: type, target: this });\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _setUpTouch(): void {\n\t\tif (!this.chartContainer._display.cancelTouch) {\n\t\t\tthis.chartContainer._display.cancelTouch = (this.get(\"pinchZoom\") || this.get(\"panX\") || this.get(\"panY\")) ? true : false;\n\t\t}\n\t}\n\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyGeometries() {\n\t\tthis._dirtyGeometries = true;\n\t\tthis.markDirty();\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\tthis.series.each((series) => {\n\t\t\tseries.markDirtyProjection();\n\t\t})\n\t}\n\n\tprotected _afterNew() {\n\t\tthis._defaultThemes.push(MapChartDefaultTheme.new(this._root));\n\t\tthis._settings.themeTags = $utils.mergeTags(this._settings.themeTags, [\"map\"]);\n\n\t\tthis.children.push(this.bulletsContainer);\n\n\t\tsuper._afterNew();\n\n\t\tthis._makeGeoPath();\n\n\t\tthis.chartContainer.children.push(this.seriesContainer);\n\n\t\tif (this.get(\"translateX\") == null) {\n\t\t\tthis.set(\"translateX\", this.width() / 2);\n\t\t}\n\t\tif (this.get(\"translateY\") == null) {\n\t\t\tthis.set(\"translateY\", this.height() / 2);\n\t\t}\n\n\t\t// Setting trasnparent background so that full body of the plot container\n\t\t// is interactive\n\t\tthis.chartContainer.set(\"interactive\", true);\n\t\tthis.chartContainer.set(\"interactiveChildren\", false);\n\t\tthis.chartContainer.set(\"background\", Rectangle.new(this._root, {\n\t\t\tthemeTags: [\"map\", \"background\"],\n\t\t\tfill: Color.fromHex(0x000000),\n\t\t\tfillOpacity: 0\n\t\t}));\n\n\t\tthis._disposers.push(this.chartContainer.events.on(\"pointerdown\", (event) => {\n\t\t\tthis._handleChartDown(event);\n\t\t}));\n\n\t\tthis._disposers.push(this.chartContainer.events.on(\"globalpointerup\", (event) => {\n\t\t\tthis._handleChartUp(event);\n\t\t}));\n\n\t\tthis._disposers.push(this.chartContainer.events.on(\"globalpointermove\", (event) => {\n\t\t\tthis._handleChartMove(event);\n\t\t}));\n\n\t\tlet license = false;\n\t\tfor (let i = 0; i < registry.licenses.length; i++) {\n\t\t\tif (registry.licenses[i].match(/^AM5M.{5,}/i)) {\n\t\t\t\tlicense = true;\n\t\t\t}\n\t\t}\n\t\tif (!license) {\n\t\t\tthis._root._showBranding();\n\t\t}\n\t\telse {\n\t\t\tthis._root._licenseApplied();\n\t\t}\n\n\t\tthis._setUpTouch();\n\n\t}\n\n\tprotected _handleChartDown(event: ISpritePointerEvent) {\n\n\t\tthis._downZoomLevel = this.get(\"zoomLevel\", 1);\n\t\tconst downPoints = this.chartContainer._downPoints;\n\n\t\tlet count = $object.keys(downPoints).length;\n\t\tif (count == 1) {\n\t\t\t// workaround to solve a problem when events are added to some children of chart container (rotation stops working)\n\t\t\tlet downPoint = downPoints[1];\n\t\t\tif (!downPoint) {\n\t\t\t\tdownPoint = downPoints[0];\n\t\t\t}\n\n\t\t\tif (downPoint && (downPoint.x == event.point.x && downPoint.y == event.point.y)) {\n\t\t\t\tcount = 0;\n\t\t\t}\n\t\t}\n\n\t\tif (count > 0) {\n\t\t\tthis._downTranslateX = this.get(\"translateX\");\n\t\t\tthis._downTranslateY = this.get(\"translateY\");\n\t\t\tthis._downRotationX = this.get(\"rotationX\");\n\t\t\tthis._downRotationY = this.get(\"rotationY\");\n\t\t\tthis._downRotationZ = this.get(\"rotationZ\");\n\n\t\t\tconst downId = this.chartContainer._getDownPointId();\n\t\t\tif (downId) {\n\t\t\t\tlet movePoint = this._movePoints[downId];\n\t\t\t\tif (movePoint) {\n\t\t\t\t\tthis.chartContainer._downPoints[downId] = movePoint;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\telse if (count == 0) {\n\t\t\tlet bg = this.chartContainer.get(\"background\");\n\t\t\tif (bg) {\n\t\t\t\tbg.events.enableType(\"click\");\n\t\t\t}\n\n\t\t\tif (this.get(\"panX\") || this.get(\"panY\")) {\n\n\t\t\t\tif (this._za) {\n\t\t\t\t\tthis._za.stop();\n\t\t\t\t}\n\t\t\t\tif (this._txa) {\n\t\t\t\t\tthis._txa.stop();\n\t\t\t\t}\n\t\t\t\tif (this._tya) {\n\t\t\t\t\tthis._tya.stop();\n\t\t\t\t}\n\t\t\t\tif (this._rxa) {\n\t\t\t\t\tthis._rxa.stop();\n\t\t\t\t}\n\t\t\t\tif (this._rya) {\n\t\t\t\t\tthis._rya.stop();\n\t\t\t\t}\n\n\t\t\t\tconst downPoint = this.chartContainer._display.toLocal(event.point);\n\t\t\t\tthis._downTranslateX = this.get(\"translateX\");\n\t\t\t\tthis._downTranslateY = this.get(\"translateY\");\n\t\t\t\tthis._downRotationX = this.get(\"rotationX\");\n\t\t\t\tthis._downRotationY = this.get(\"rotationY\");\n\t\t\t\tthis._downRotationZ = this.get(\"rotationZ\");\n\n\t\t\t\tlet projection = this.get(\"projection\")!;\n\n\t\t\t\tif (projection.invert) {\n\t\t\t\t\tlet l0 = projection.invert([downPoint.x, downPoint.y]);\n\t\t\t\t\tlet l1 = projection.invert([downPoint.x + 1, downPoint.y + 1]);\n\t\t\t\t\tif (l0 && l1) {\n\t\t\t\t\t\tthis._pLon = Math.abs(l1[0] - l0[0]);\n\t\t\t\t\t\tthis._pLat = Math.abs(l1[1] - l0[1]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Converts screen coordinates (X and Y) within chart to latitude and\n\t * longitude.\n\t * \n\t * @param  point  Screen coordinates\n\t * @return        Geographical coordinates\n\t */\n\tpublic invert(point: IPoint): IGeoPoint {\n\t\tlet projection = this.get(\"projection\")!;\n\n\t\tif (projection.invert) {\n\t\t\tconst ll = projection.invert([point.x, point.y]);\n\t\t\tif (ll) {\n\t\t\t\treturn { longitude: ll[0], latitude: ll[1] };\n\t\t\t}\n\t\t}\n\n\t\treturn { longitude: 0, latitude: 0 };\n\t}\n\n\t/**\n\t * Converts latitude/longitude to screen coordinates (X and Y).\n\t * \n\t * @param  point  Geographical coordinates\n\t * @param  rotationX  X rotation of a map if different from current\n\t * @param  rotationY  Y rotation of a map if different from current\n\t * \n\t * @return Screen coordinates\n\t */\n\tpublic convert(point: IGeoPoint, rotationX?: number, rotationY?: number): IPoint {\n\t\tlet projection = this.get(\"projection\")!;\n\t\tlet xy;\n\n\t\tif (!projection.rotate) {\n\t\t\trotationX = undefined;\n\t\t\trotationY = undefined;\n\t\t}\n\n\t\tif (rotationX != null || rotationY != null) {\n\t\t\tif (rotationX == null) {\n\t\t\t\trotationX = 0;\n\t\t\t}\n\t\t\tif (rotationY == null) {\n\t\t\t\trotationY = 0;\n\t\t\t}\n\t\t\tlet rotation = projection.rotate();\n\t\t\tprojection.rotate([rotationX, rotationY, 0]);\n\t\t\txy = projection([point.longitude, point.latitude]);\n\t\t\tprojection.rotate(rotation);\n\t\t}\n\t\telse {\n\t\t\txy = projection([point.longitude, point.latitude]);\n\t\t}\n\n\t\tif (xy) {\n\t\t\treturn { x: xy[0], y: xy[1] };\n\t\t}\n\n\t\treturn { x: 0, y: 0 };\n\t}\n\n\tprotected _handleChartUp(_event: ISpritePointerEvent) {\n\t\tthis.chartContainer._downPoints = {}\n\t}\n\n\tprotected _handlePinch() {\n\t\tconst chartContainer = this.chartContainer;\n\t\tlet i = 0;\n\t\tlet downPoints: Array<IPoint> = [];\n\t\tlet movePoints: Array<IPoint> = [];\n\n\t\t$object.each(chartContainer._downPoints, (k, point) => {\n\t\t\tdownPoints[i] = point;\n\t\t\tlet movePoint = this._movePoints[k];\n\t\t\tif (movePoint) {\n\t\t\t\tmovePoints[i] = movePoint;\n\t\t\t}\n\t\t\ti++;\n\t\t});\n\n\t\tif (downPoints.length > 1 && movePoints.length > 1) {\n\t\t\tconst display = chartContainer._display;\n\n\t\t\tlet downPoint0 = downPoints[0];\n\t\t\tlet downPoint1 = downPoints[1];\n\n\t\t\tlet movePoint0 = movePoints[0];\n\t\t\tlet movePoint1 = movePoints[1];\n\n\t\t\tif (downPoint0 && downPoint1 && movePoint0 && movePoint1) {\n\n\t\t\t\tdownPoint0 = display.toLocal(downPoint0);\n\t\t\t\tdownPoint1 = display.toLocal(downPoint1);\n\n\t\t\t\tmovePoint0 = display.toLocal(movePoint0);\n\t\t\t\tmovePoint1 = display.toLocal(movePoint1);\n\n\t\t\t\tlet initialDistance = Math.hypot(downPoint1.x - downPoint0.x, downPoint1.y - downPoint0.y);\n\t\t\t\tlet currentDistance = Math.hypot(movePoint1.x - movePoint0.x, movePoint1.y - movePoint0.y);\n\n\t\t\t\tlet level = currentDistance / initialDistance * this._downZoomLevel;\n\t\t\t\tlevel = $math.fitToRange(level, this.get(\"minZoomLevel\", 1), this.get(\"maxZoomLevel\", 32));\n\n\t\t\t\tlet moveCenter = { x: movePoint0.x + (movePoint1.x - movePoint0.x) / 2, y: movePoint0.y + (movePoint1.y - movePoint0.y) / 2 };\n\t\t\t\tlet downCenter = { x: downPoint0.x + (downPoint1.x - downPoint0.x) / 2, y: downPoint0.y + (downPoint1.y - downPoint0.y) / 2 };\n\n\t\t\t\tlet tx = this._downTranslateX || 0;\n\t\t\t\tlet ty = this._downTranslateY || 0;\n\n\t\t\t\tlet zoomLevel = this._downZoomLevel;\n\n\t\t\t\tlet xx = moveCenter.x - (- tx + downCenter.x) / zoomLevel * level;\n\t\t\t\tlet yy = moveCenter.y - (- ty + downCenter.y) / zoomLevel * level;\n\n\t\t\t\tthis.set(\"zoomLevel\", level);\n\t\t\t\tthis.set(\"translateX\", xx);\n\t\t\t\tthis.set(\"translateY\", yy);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _handleChartMove(event: ISpritePointerEvent) {\n\t\tconst chartContainer = this.chartContainer;\n\t\tlet downPoint = chartContainer._getDownPoint();\n\t\tconst downPointId = chartContainer._getDownPointId();\n\t\tconst originalEvent = event.originalEvent as any;\n\n\t\tconst pointerId = originalEvent.pointerId;\n\n\t\tif (this.get(\"pinchZoom\")) {\n\t\t\tif (pointerId) {\n\t\t\t\tthis._movePoints[pointerId] = event.point;\n\n\t\t\t\tif ($object.keys(chartContainer._downPoints).length > 1) {\n\t\t\t\t\tthis._handlePinch();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (downPointId && pointerId && pointerId != downPointId) {\n\t\t\treturn;\n\t\t}\n\t\telse {\n\t\t\tif (downPoint) {\n\t\t\t\tconst panX = this.get(\"panX\");\n\t\t\t\tconst panY = this.get(\"panY\");\n\t\t\t\tif (panX != \"none\" || panY != \"none\") {\n\t\t\t\t\tconst display = chartContainer._display;\n\t\t\t\t\tlet local = display.toLocal(event.point);\n\t\t\t\t\tdownPoint = display.toLocal(downPoint);\n\n\t\t\t\t\tlet x = this._downTranslateX;\n\t\t\t\t\tlet y = this._downTranslateY;\n\n\t\t\t\t\tif (Math.hypot(downPoint.x - local.x, downPoint.y - local.y) > 5) {\n\t\t\t\t\t\tlet bg = chartContainer.get(\"background\");\n\t\t\t\t\t\tif (bg) {\n\t\t\t\t\t\t\tbg.events.disableType(\"click\");\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif ($type.isNumber(x) && $type.isNumber(y)) {\n\t\t\t\t\t\t\tlet projection = this.get(\"projection\")!;\n\t\t\t\t\t\t\tconst zoomLevel = this.get(\"zoomLevel\", 1);\n\n\t\t\t\t\t\t\tconst maxPanOut = this.get(\"maxPanOut\", 0.4);\n\t\t\t\t\t\t\tconst bounds = this._mapBounds;\n\n\t\t\t\t\t\t\tconst w = this.width();\n\t\t\t\t\t\t\tconst h = this.height();\n\n\t\t\t\t\t\t\tconst ww = bounds[1][0] - bounds[0][0];\n\t\t\t\t\t\t\tconst hh = bounds[1][1] - bounds[0][1];\n\n\t\t\t\t\t\t\tif (panX == \"translateX\") {\n\t\t\t\t\t\t\t\tx += local.x - downPoint.x;\n\n\t\t\t\t\t\t\t\tconst cx = w / 2 - (w / 2 - this._centerX) * zoomLevel;\n\t\t\t\t\t\t\t\tx = Math.min(x, cx + ww * maxPanOut * zoomLevel);\n\t\t\t\t\t\t\t\tx = Math.max(x, cx - ww * maxPanOut * zoomLevel);\n\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif (panY == \"translateY\") {\n\t\t\t\t\t\t\t\ty += local.y - downPoint.y;\n\t\t\t\t\t\t\t\tconst cy = h / 2 - (h / 2 - this._centerY) * zoomLevel;\n\t\t\t\t\t\t\t\ty = Math.min(y, cy + hh * maxPanOut * zoomLevel);\n\t\t\t\t\t\t\t\ty = Math.max(y, cy - hh * maxPanOut * zoomLevel);\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthis.set(\"translateX\", x);\n\t\t\t\t\t\t\tthis.set(\"translateY\", y);\n\n\t\t\t\t\t\t\tif (projection.invert) {\n\t\t\t\t\t\t\t\tlet downLocation = projection.invert([downPoint.x, downPoint.y]);\n\t\t\t\t\t\t\t\tif (location && downLocation) {\n\t\t\t\t\t\t\t\t\tif (panX == \"rotateX\") {\n\t\t\t\t\t\t\t\t\t\tthis.set(\"rotationX\", this._downRotationX! - (downPoint.x - local.x) * this._pLon);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\tif (panY == \"rotateY\") {\n\t\t\t\t\t\t\t\t\t\tthis.set(\"rotationY\", this._downRotationY! + (downPoint.y - local.y) * this._pLat);\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _handleWheelRotateY(delta: number, duration: number, easing: (t: Time) => Time) {\n\t\tthis._rya = this.animate({ key: \"rotationY\", to: this.get(\"rotationY\", 0) - delta, duration: duration, easing: easing });\n\t}\n\n\tprotected _handleWheelRotateX(delta: number, duration: number, easing: (t: Time) => Time) {\n\t\tthis._rxa = this.animate({ key: \"rotationX\", to: this.get(\"rotationX\", 0) - delta, duration: duration, easing: easing });\n\t}\n\n\tprotected _handleWheelZoom(delta: number, point: IPoint) {\n\t\tlet step = this.get(\"zoomStep\", 2);\n\t\tlet zoomLevel = this.get(\"zoomLevel\", 1);\n\t\tlet newZoomLevel = zoomLevel;\n\t\tif (delta > 0) {\n\t\t\tnewZoomLevel = zoomLevel / step;\n\t\t}\n\t\telse if (delta < 0) {\n\t\t\tnewZoomLevel = zoomLevel * step;\n\t\t}\n\n\t\tif (newZoomLevel != zoomLevel) {\n\t\t\tthis.zoomToPoint(point, newZoomLevel)\n\t\t}\n\t}\n\n\t/**\n\t * Zoom the map to geographical bounds.\n\t *\n\t * @param  geoBounds  Bounds\n\t * @param  duration   Animation duration in milliseconds\n\t * @param  rotationX  X rotation of a map at the end of zoom\n\t * @param  rotationY  Y rotation of a map at the end of zoom\n\t */\n\tpublic zoomToGeoBounds(geoBounds: { left: number, right: number, top: number, bottom: number }, duration?: number, rotationX?: number, rotationY?: number): Animation<this[\"_settings\"][\"zoomLevel\"]> | undefined {\n\t\tif (geoBounds.right < geoBounds.left) {\n\t\t\tgeoBounds.right = 180;\n\t\t\tgeoBounds.left = -180;\n\t\t}\n\n\t\tconst geoPath = this.getPrivate(\"geoPath\");\n\t\tconst mapBounds = geoPath.bounds(this._geometryColection);\n\n\t\tlet p0 = this.convert({ longitude: geoBounds.left, latitude: geoBounds.top }, rotationX, rotationY);\n\t\tlet p1 = this.convert({ longitude: geoBounds.right, latitude: geoBounds.bottom }, rotationX, rotationY);\n\n\t\tif (p0.y < mapBounds[0][1]) {\n\t\t\tp0.y = mapBounds[0][1];\n\t\t}\n\n\t\tif (p1.y > mapBounds[1][1]) {\n\t\t\tp1.y = mapBounds[1][1];\n\t\t}\n\n\t\tlet zl = this.get(\"zoomLevel\", 1);\n\n\t\tlet bounds = { left: p0.x, right: p1.x, top: p0.y, bottom: p1.y };\n\n\t\tlet seriesContainer = this.seriesContainer;\n\n\t\tlet zoomLevel = .9 * Math.min(seriesContainer.innerWidth() / (bounds.right - bounds.left) * zl, seriesContainer.innerHeight() / (bounds.bottom - bounds.top) * zl);\n\t\tlet x = bounds.left + (bounds.right - bounds.left) / 2;\n\t\tlet y = bounds.top + (bounds.bottom - bounds.top) / 2;\n\n\t\tlet geoPoint = this.invert({ x, y });\n\n\t\tif (rotationX != null || rotationY != null) {\n\t\t\tthis.rotate(rotationX, rotationY);\n\t\t}\n\n\t\treturn this.zoomToGeoPoint(geoPoint, zoomLevel, true, duration);\n\t}\n\n\t/**\n\t * Zooms the map to specific screen point.\n\t *\n\t * @param  point    Point\n\t * @param  level    Zoom level\n\t * @param  center   Center the map\n\t * @param  duration Duration of the animation in milliseconds\n\t */\n\tpublic zoomToPoint(point: IPoint, level: number, center?: boolean, duration?: number): Animation<this[\"_settings\"][\"zoomLevel\"]> | undefined {\n\t\tif (level) {\n\t\t\tlevel = $math.fitToRange(level, this.get(\"minZoomLevel\", 1), this.get(\"maxZoomLevel\", 32));\n\t\t}\n\n\t\tif (!$type.isNumber(duration)) {\n\t\t\tduration = this.get(\"animationDuration\", 0);\n\t\t}\n\t\tconst easing = this.get(\"animationEasing\");\n\t\tconst zoomLevel = this.get(\"zoomLevel\", 1);\n\n\t\tif (this.get(\"centerMapOnZoomOut\") && level == this.get(\"homeZoomLevel\", 1)) {\n\t\t\tpoint = this.convert(this.homeGeoPoint(), this.get(\"homeRotationX\"), this.get(\"homeRotationY\"));\n\t\t\tcenter = true;\n\t\t}\n\n\t\tlet x = point.x;\n\t\tlet y = point.y;\n\n\t\tlet tx = this.get(\"translateX\", 0);\n\t\tlet ty = this.get(\"translateY\", 0);\n\n\t\tlet cx = x;\n\t\tlet cy = y;\n\n\t\tif (center) {\n\t\t\tcx = this.width() / 2;\n\t\t\tcy = this.height() / 2;\n\t\t}\n\n\t\tlet xx = cx - ((x - tx) / zoomLevel * level);\n\t\tlet yy = cy - ((y - ty) / zoomLevel * level);\n\n\n\t\tthis._txa = this.animate({ key: \"translateX\", to: xx, duration: duration, easing: easing });\n\t\tthis._tya = this.animate({ key: \"translateY\", to: yy, duration: duration, easing: easing });\n\t\tthis._za = this.animate({ key: \"zoomLevel\", to: level, duration: duration, easing: easing });\n\n\t\tif (zoomLevel != level) {\n\t\t\tthis._root.readerAlert(this._t(\"Zoom level changed to %1\", this._root.locale, $type.numberToString(level)));\n\t\t}\n\n\t\treturn this._za;\n\t}\n\n\t/**\n\t * Zooms the map to specific geographical point.\n\t *\n\t * @param  geoPoint  Point\n\t * @param  level     Zoom level\n\t * @param  center    Center the map\n\t * @param  duration  Duration of the animation in milliseconds\n\t * @param  rotationX  X rotation of a map at the end of zoom\n\t * @param  rotationY  Y rotation of a map at the end of zoom\n\t * \n\t */\n\tpublic zoomToGeoPoint(geoPoint: IGeoPoint, level: number, center?: boolean, duration?: number, rotationX?: number, rotationY?: number): Animation<this[\"_settings\"][\"zoomLevel\"]> | undefined {\n\n\t\tlet xy = this.convert(geoPoint, rotationX, rotationY);\n\n\t\tif (rotationX != null || rotationY != null) {\n\t\t\tthis.rotate(rotationX, rotationY, duration);\n\t\t}\n\n\t\tif (xy) {\n\t\t\treturn this.zoomToPoint(xy, level, center, duration);\n\t\t}\n\t}\n\n\tpublic rotate(rotationX?: number, rotationY?: number, duration?: number) {\n\t\tconst projection = this.get(\"projection\")!;\n\t\tif (!projection.rotate) {\n\t\t}\n\t\telse {\n\t\t\tif (!$type.isNumber(duration)) {\n\t\t\t\tduration = this.get(\"animationDuration\", 0);\n\t\t\t}\n\n\t\t\tconst easing = this.get(\"animationEasing\");\n\t\t\tif (rotationX != null) {\n\t\t\t\tthis.animate({ key: \"rotationX\", to: rotationX, duration: duration, easing: easing });\n\t\t\t}\n\t\t\tif (rotationY != null) {\n\t\t\t\tthis.animate({ key: \"rotationY\", to: rotationY, duration: duration, easing: easing });\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Zooms the map in.\n\t */\n\tpublic zoomIn(): Animation<this[\"_settings\"][\"zoomLevel\"]> | undefined {\n\t\treturn this.zoomToPoint({ x: this.width() / 2, y: this.height() / 2 }, this.get(\"zoomLevel\", 1) * this.get(\"zoomStep\", 2));\n\t}\n\n\t/**\n\t * Zooms the map out.\n\t */\n\tpublic zoomOut(): Animation<this[\"_settings\"][\"zoomLevel\"]> | undefined {\n\t\treturn this.zoomToPoint({ x: this.width() / 2, y: this.height() / 2 }, this.get(\"zoomLevel\", 1) / this.get(\"zoomStep\", 2));\n\t}\n\n\tpublic _clearDirty() {\n\t\tsuper._clearDirty();\n\t\tthis._dirtyGeometries = false;\n\t\tthis._mapFitted = false;\n\t}\n\n\t/**\n\t * Returns area of a mapPolygon in square pixels.\n\t */\n\tpublic getArea(dataItem: DataItem<IMapPolygonSeriesDataItem>): number {\n\t\tconst geoPath = this.getPrivate(\"geoPath\");\n\t\tconst geometry = dataItem.get(\"geometry\");\n\t\tif (geometry) {\n\t\t\treturn geoPath.area(geometry);\n\t\t}\n\t\treturn 0;\n\t}\n}\n", "import identity from \"../identity.js\";\nimport stream from \"../stream.js\";\nimport pathArea from \"./area.js\";\nimport pathBounds from \"./bounds.js\";\nimport pathCentroid from \"./centroid.js\";\nimport PathContext from \"./context.js\";\nimport pathMeasure from \"./measure.js\";\nimport PathString from \"./string.js\";\n\nexport default function(projection, context) {\n  let digits = 3,\n      pointRadius = 4.5,\n      projectionStream,\n      contextStream;\n\n  function path(object) {\n    if (object) {\n      if (typeof pointRadius === \"function\") contextStream.pointRadius(+pointRadius.apply(this, arguments));\n      stream(object, projectionStream(contextStream));\n    }\n    return contextStream.result();\n  }\n\n  path.area = function(object) {\n    stream(object, projectionStream(pathArea));\n    return pathArea.result();\n  };\n\n  path.measure = function(object) {\n    stream(object, projectionStream(pathMeasure));\n    return pathMeasure.result();\n  };\n\n  path.bounds = function(object) {\n    stream(object, projectionStream(pathBounds));\n    return pathBounds.result();\n  };\n\n  path.centroid = function(object) {\n    stream(object, projectionStream(pathCentroid));\n    return pathCentroid.result();\n  };\n\n  path.projection = function(_) {\n    if (!arguments.length) return projection;\n    projectionStream = _ == null ? (projection = null, identity) : (projection = _).stream;\n    return path;\n  };\n\n  path.context = function(_) {\n    if (!arguments.length) return context;\n    contextStream = _ == null ? (context = null, new PathString(digits)) : new PathContext(context = _);\n    if (typeof pointRadius !== \"function\") contextStream.pointRadius(pointRadius);\n    return path;\n  };\n\n  path.pointRadius = function(_) {\n    if (!arguments.length) return pointRadius;\n    pointRadius = typeof _ === \"function\" ? _ : (contextStream.pointRadius(+_), +_);\n    return path;\n  };\n\n  path.digits = function(_) {\n    if (!arguments.length) return digits;\n    if (_ == null) digits = null;\n    else {\n      const d = Math.floor(_);\n      if (!(d >= 0)) throw new RangeError(`invalid digits: ${_}`);\n      digits = d;\n    }\n    if (context === null) contextStream = new PathString(digits);\n    return path;\n  };\n\n  return path.projection(projection).digits(digits).context(context);\n}\n", "import type { IMapLineSeriesDataItem, MapLineSeries } from \"./MapLineSeries\";\nimport type { IMapPolygonSeriesDataItem, MapPolygonSeries } from \"./MapPolygonSeries\";\nimport type { Bullet } from \"../../core/render/Bullet\";\nimport type { DataItem } from \"../../core/render/Component\";\nimport type { MapLine } from \"./MapLine\";\nimport type { MapPolygon } from \"./MapPolygon\";\n\nimport { MapSeries, IMapSeriesSettings, IMapSeriesDataItem, IMapSeriesPrivate } from \"./MapSeries\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $type from \"../../core/util/Type\";\nimport * as $math from \"../../core/util/Math\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport type { Animation } from \"../../core/util/Entity\";\nimport type { IDisposer } from \"../../core/util/Disposer\";\n\nexport interface IMapPointSeriesPrivate extends IMapSeriesPrivate {\n}\n\nexport interface IMapPointSeriesDataItem extends IMapSeriesDataItem {\n\n\t/**\n\t * GeoJSON geometry of the point.\n\t */\n\tgeometry?: GeoJSON.Point | GeoJSON.MultiPoint;\n\n\t/**\n\t * Longitude.\n\t */\n\tlongitude?: number;\n\n\t/**\n\t * Latitude.\n\t */\n\tlatitude?: number;\n\n\t/**\n\t * Relative position (0-1) on the [[MapLine]] to place point on.\n\t */\n\tpositionOnLine?: number;\n\n\t/**\n\t * Automatically rotate the point bullet to face the direction of the line\n\t * it is attached to.\n\t */\n\tautoRotate?: boolean;\n\n\t/**\n\t * The angle will be added to the automatically-calculated angle.\n\t *\n\t * Can be used to reverse the direction.\n\t */\n\tautoRotateAngle?: number;\n\n\t/**\n\t * A data item from a [[MapLineSeries]] the point is attached to.\n\t */\n\tlineDataItem?: DataItem<IMapLineSeriesDataItem>;\n\n\t/**\n\t * An ID of a [[MapLine]] the point is attached to.\n\t */\n\tlineId?: string;\n\n\t/**\n\t * A data item from a [[MapPolygonSeries]] to use for positioning of the\n\t * point.\n\t */\n\tpolygonDataItem?: DataItem<IMapPolygonSeriesDataItem>;\n\n\t/**\n\t * An ID of the [[MapPolygon]] to use for centering the point.\n\t */\n\tpolygonId?: string;\n\n\t/**\n\t * If set to `true`, the point will be drawn according to its `x` and `y`\n\t * coordinates, not its latitude and longitude.\n\t * \n\t * Fixed points will not move together with map, and can not be used to\n\t * connect points on a `MapLineSeries`.\n\t * \n\t * @default false\n\t * @since 5.2.34\n\t */\n\tfixed?: boolean;\n\n\t/**\n\t * Point (in pixels) of a data item\n\t */\n\tpoint?: IPoint;\n\n\t/**\n\t * @ignore\n\t */\n\tclipped?: boolean;\n}\n\nexport interface IMapPointSeriesSettings extends IMapSeriesSettings {\n\n\t/**\n\t * A field in data that holds an ID of the related polygon.\n\t *\n\t * If set, the point will be positioned in the visual center of the target\n\t * polygon.\n\t */\n\tpolygonIdField?: string;\n\n\t/**\n\t * If set to `true` will hide all points that are in the visible range of\n\t * the map.\n\t */\n\tclipFront?: boolean;\n\n\t/**\n\t * If set to `true` will hide all points that are in the invisible range of\n\t * the map.\n\t *\n\t * For example on the side of the globe facing away from the viewer when\n\t * used with Orthographic projection.\n\t *\n\t * NOTE: not all projections have invisible side.\n\t *\n\t * @default true\n\t */\n\tclipBack?: boolean;\n\n\t/**\n\t * A field in data that holds point's longitude.\n\t */\n\tlatitudeField?: string;\n\n\t/**\n\t * A field in data that holds point's longitude.\n\t */\n\tlongitudeField?: string;\n\n\t/**\n\t * A field in data that holds information if this point is fixed or moves with a map.\n\t */\n\tfixedField?: string;\n\n\t/**\n\t * If set to `true`, bullets will resize when zooming the [[MapChart]].\n\t * \n\t * @since 5.2.8\n\t * @default false\n\t */\n\tautoScale?: boolean\n\n};\n\n/**\n * Creates a map series for displaying markers on the map.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-point-series/} for more info\n * @important\n */\nexport class MapPointSeries extends MapSeries {\n\n\tpublic static className: string = \"MapPointSeries\";\n\tpublic static classNames: Array<string> = MapSeries.classNames.concat([MapPointSeries.className]);\n\n\tdeclare public _settings: IMapPointSeriesSettings;\n\tdeclare public _privateSettings: IMapPointSeriesPrivate;\n\tdeclare public _dataItemSettings: IMapPointSeriesDataItem;\n\n\tprotected _types: Array<GeoJSON.GeoJsonGeometryTypes> = [\"Point\", \"MultiPoint\"];\n\n\tprotected _lineChangedDp?: IDisposer;\n\n\tprotected _afterNew() {\n\t\tthis.fields.push(\"polygonId\", \"lineId\", \"longitude\", \"latitude\", \"fixed\");\n\t\tsuper._afterNew();\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\tthis.markDirty();\n\t}\n\n\t/**\n\t * Forces a repaint of the element which relies on data.\n\t *\n\t * @since 5.0.21\n\t */\n\tpublic markDirtyValues(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.markDirtyValues();\n\n\t\tif (dataItem) {\n\t\t\tthis._positionBullets(dataItem);\n\t\t}\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\t\tlet geometry = dataItem.get(\"geometry\");\n\t\tif (!geometry) {\n\t\t\tgeometry = { type: \"Point\", coordinates: [dataItem.get(\"longitude\", 0), dataItem.get(\"latitude\", 0)] };\n\t\t\tdataItem.set(\"geometry\", geometry);\n\t\t}\n\t\telse {\n\t\t\tif (geometry.type == \"Point\") {\n\t\t\t\tconst coordinates = geometry.coordinates;\n\t\t\t\tif (coordinates) {\n\t\t\t\t\tdataItem.set(\"longitude\", coordinates[0]);\n\t\t\t\t\tdataItem.set(\"latitude\", coordinates[1]);\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (geometry.type == \"MultiPoint\") {\n\t\t\t\tconst coordinates = geometry.coordinates;\n\t\t\t\tif (coordinates && coordinates[0]) {\n\t\t\t\t\tdataItem.set(\"longitude\", coordinates[0][0]);\n\t\t\t\t\tdataItem.set(\"latitude\", coordinates[0][1]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis._addGeometry(geometry, this);\n\t}\n\n\tprotected _makeBullets(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tdataItem.bullets = [];\n\n\t\tthis.bullets.each((bulletFunction) => {\n\t\t\tconst geometry = dataItem.get(\"geometry\");\n\n\t\t\tif (geometry) {\n\t\t\t\tif (geometry.type == \"Point\") {\n\t\t\t\t\tthis._setBulletParent(this._makeBullet(dataItem, bulletFunction));\n\t\t\t\t}\n\t\t\t\telse if (geometry.type = \"MultiPoint\") {\n\t\t\t\t\tlet i = 0;\n\t\t\t\t\t$array.each(geometry.coordinates, () => {\n\t\t\t\t\t\tthis._setBulletParent(this._makeBullet(dataItem, bulletFunction, i));\n\t\t\t\t\t\ti++;\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t}\n\n\tprotected _setBulletParent(bullet?: Bullet) {\n\t\tif (bullet) {\n\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\tconst chart = this.chart;\n\t\t\tif (sprite && chart) {\n\t\t\t\tconst dataItem = sprite.dataItem as DataItem<IMapPointSeriesDataItem>;\n\t\t\t\tif (dataItem) {\n\t\t\t\t\tif (dataItem.get(\"fixed\")) {\n\t\t\t\t\t\tif (sprite.parent != chart.bulletsContainer) {\n\t\t\t\t\t\t\tchart.bulletsContainer.children.moveValue(sprite);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tif (sprite.parent != this.bulletsContainer) {\n\t\t\t\t\t\t\tthis.bulletsContainer.children.moveValue(sprite);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tpublic _positionBullet(bullet: Bullet) {\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tif (sprite) {\n\t\t\tconst dataItem = sprite.dataItem as DataItem<this[\"_dataItemSettings\"]>;\n\t\t\tif (dataItem && dataItem.get(\"fixed\")) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tconst latitude = dataItem.get(\"latitude\");\n\t\t\tconst longitude = dataItem.get(\"longitude\");\n\t\t\tconst lineDataItem = dataItem.get(\"lineDataItem\");\n\t\t\tconst fixed = dataItem.get(\"fixed\");\n\t\t\tconst chart = this.chart;\n\t\t\tlet line: MapLine | undefined;\n\t\t\tif (lineDataItem) {\n\t\t\t\tline = lineDataItem.get(\"mapLine\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst lineId = dataItem.get(\"lineId\");\n\n\t\t\t\tif (lineId && chart) {\n\t\t\t\t\tchart.series.each((series) => {\n\t\t\t\t\t\tif (series.isType<MapLineSeries>(\"MapLineSeries\")) {\n\t\t\t\t\t\t\tlet lineDI = series.getDataItemById(lineId);\n\t\t\t\t\t\t\tif (lineDI) {\n\t\t\t\t\t\t\t\tdataItem.set(\"lineDataItem\", lineDI);\n\t\t\t\t\t\t\t\tline = lineDI.get(\"mapLine\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (this._lineChangedDp) {\n\t\t\t\tthis._lineChangedDp.dispose();\n\t\t\t}\n\n\t\t\tif (line) {\n\t\t\t\tthis._lineChangedDp = line.events.on(\"linechanged\", () => {\n\t\t\t\t\tthis._positionBullets(dataItem);\n\t\t\t\t})\n\t\t\t}\n\n\t\t\tconst polygonDataItem = dataItem.get(\"polygonDataItem\");\n\t\t\tlet polygon: MapPolygon | undefined;\n\t\t\tif (polygonDataItem) {\n\t\t\t\tpolygon = polygonDataItem.get(\"mapPolygon\");\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst polygonId = dataItem.get(\"polygonId\");\n\n\t\t\t\tif (polygonId && chart) {\n\t\t\t\t\tchart.series.each((series) => {\n\t\t\t\t\t\tif (series.isType<MapPolygonSeries>(\"MapPolygonSeries\")) {\n\t\t\t\t\t\t\tlet polygonDI = series.getDataItemById(polygonId);\n\t\t\t\t\t\t\tif (polygonDI) {\n\t\t\t\t\t\t\t\tdataItem.set(\"polygonDataItem\", polygonDI);\n\t\t\t\t\t\t\t\tpolygon = polygonDI.get(\"mapPolygon\");\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tconst positionOnLine = dataItem.get(\"positionOnLine\");\n\t\t\tlet coordinates: [number, number] | undefined;\n\n\t\t\tlet angle: number | undefined;\n\n\t\t\tif (polygon) {\n\t\t\t\tlet geoPoint = polygon.visualCentroid();\n\t\t\t\tcoordinates = [geoPoint.longitude, geoPoint.latitude];\n\t\t\t\tdataItem.setRaw(\"longitude\", geoPoint.longitude);\n\t\t\t\tdataItem.setRaw(\"latitude\", geoPoint.latitude);\n\t\t\t}\n\t\t\telse if (line && $type.isNumber(positionOnLine)) {\n\t\t\t\tlet geoPoint = line.positionToGeoPoint(positionOnLine);\n\t\t\t\tcoordinates = [geoPoint.longitude, geoPoint.latitude];\n\n\t\t\t\tif (dataItem.get(\"autoRotate\", bullet.get(\"autoRotate\")) && chart) {\n\t\t\t\t\tconst geoPoint0 = line.positionToGeoPoint(positionOnLine - 0.002);\n\t\t\t\t\tconst geoPoint1 = line.positionToGeoPoint(positionOnLine + 0.002);\n\n\t\t\t\t\tconst point0 = chart.convert(geoPoint0);\n\t\t\t\t\tconst point1 = chart.convert(geoPoint1);\n\n\t\t\t\t\t//dataItem.set(\"autoRotateAngle\", $math.getAngle(point0, point1));\n\t\t\t\t\tangle = $math.getAngle(point0, point1);\n\t\t\t\t}\n\n\t\t\t\tdataItem.setRaw(\"longitude\", geoPoint.longitude);\n\t\t\t\tdataItem.setRaw(\"latitude\", geoPoint.latitude);\n\t\t\t}\n\t\t\telse if ($type.isNumber(longitude) && $type.isNumber(latitude)) {\n\t\t\t\tcoordinates = [longitude, latitude];\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst geometry = dataItem.get(\"geometry\")!;\n\t\t\t\tif (geometry) {\n\t\t\t\t\tif (geometry.type == \"Point\") {\n\t\t\t\t\t\tthis._positionBulletReal(bullet, geometry, geometry.coordinates as [number, number], angle);\n\t\t\t\t\t}\n\t\t\t\t\telse if (geometry.type == \"MultiPoint\") {\n\t\t\t\t\t\tlet index = bullet._index || 0;\n\t\t\t\t\t\tcoordinates = geometry.coordinates[index] as [number, number];\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (!fixed && coordinates) {\n\t\t\t\tthis._positionBulletReal(bullet, { type: \"Point\", coordinates: coordinates }, coordinates, angle);\n\t\t\t}\n\t\t}\n\t}\n\n\tprotected _positionBulletReal(bullet: Bullet, geometry: GeoJSON.Geometry, coordinates: [number, number], angle?: number) {\n\t\tconst sprite = bullet.get(\"sprite\");\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\tconst projection = chart.get(\"projection\")!;\n\t\t\tconst geoPath = chart.getPrivate(\"geoPath\");\n\t\t\tconst dataItem: DataItem<IMapPointSeriesDataItem> = sprite.dataItem as DataItem<IMapPointSeriesDataItem>;\n\n\t\t\tconst xy = projection(coordinates as any);\n\n\t\t\tif (xy) {\n\t\t\t\tconst point = { x: xy[0], y: xy[1] };\n\t\t\t\tsprite.setAll(point);\n\t\t\t\tdataItem.setRaw(\"point\", point);\n\t\t\t}\n\n\t\t\tlet visible = true;\n\t\t\tif (geoPath(geometry)) {\n\t\t\t\tif (this.get(\"clipFront\")) {\n\t\t\t\t\tvisible = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tif (this.get(\"clipBack\")) {\n\t\t\t\t\tvisible = false;\n\t\t\t\t}\n\t\t\t}\n\t\t\tsprite.setPrivate(\"visible\", visible);\n\t\t\tdataItem.set(\"clipped\", !visible);\n\n\t\t\tif (dataItem && angle != null && dataItem.get(\"autoRotate\", bullet.get(\"autoRotate\"))) {\n\t\t\t\tsprite.set(\"rotation\", angle + dataItem.get(\"autoRotateAngle\", bullet.get(\"autoRotateAngle\", 0)));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Centers the map to specific series' data item and zooms to the level\n\t * specified in the parameters.\n\t *\n\t * @param  dataItem   Map point\n\t * @param  zoomLevel  Zoom level\n\t * @param  rotate If it's true, the map will rotate so that this point would be in the center. Mostly usefull with geoOrthographic projection.\n\t */\n\tpublic zoomToDataItem(dataItem: DataItem<IMapPointSeriesDataItem>, zoomLevel: number, rotate?: boolean): Animation<any> | undefined {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\tconst longitude = dataItem.get(\"longitude\", 0);\n\t\t\tconst latitude = dataItem.get(\"latitude\", 0);\n\t\t\tif (rotate) {\n\t\t\t\treturn chart.zoomToGeoPoint({ longitude: longitude, latitude: latitude }, zoomLevel, true, undefined, -longitude, -latitude);\n\t\t\t}\n\t\t\treturn chart.zoomToGeoPoint({ longitude: longitude, latitude: latitude }, zoomLevel, true);\n\t\t}\n\t}\n\n\n\t/**\n\t * Zooms the map in so that all points in the array are visible.\n\t *\n\t * @param   dataItems  An array of data items of points to zoom to\n\t * @param   rotate     Rotate the map so it is centered on the selected items\n\t * @return             Animation\n\t * @since 5.5.6\n\t */\n\tpublic zoomToDataItems(dataItems: Array<DataItem<IMapPointSeriesDataItem>>, rotate?: boolean): Animation<any> | undefined {\n\n\t\tlet left: number | null = null;\n\t\tlet right: number | null = null;\n\t\tlet top: number | null = null;\n\t\tlet bottom: number | null = null;\n\n\t\t$array.each(dataItems, (dataItem) => {\n\t\t\tconst longitude = dataItem.get(\"longitude\", 0);\n\t\t\tconst latitude = dataItem.get(\"latitude\", 0);\n\n\t\t\tif (left == null || left > longitude) {\n\t\t\t\tleft = longitude;\n\t\t\t}\n\t\t\tif (right == null || right < longitude) {\n\t\t\t\tright = longitude;\n\t\t\t}\n\t\t\tif (top == null || top < latitude) {\n\t\t\t\ttop = latitude;\n\t\t\t}\n\t\t\tif (bottom == null || bottom > latitude) {\n\t\t\t\tbottom = latitude;\n\t\t\t}\n\t\t})\n\t\tif (left != null && right != null && top != null && bottom != null) {\n\t\t\tconst chart = this.chart;\n\t\t\tif (chart) {\n\t\t\t\tif (rotate) {\n\t\t\t\t\treturn chart.zoomToGeoBounds({ left, right, top, bottom }, undefined, -(left + (right - left) / 2), -(top + (top - bottom) / 2));\n\t\t\t\t}\n\t\t\t\treturn chart.zoomToGeoBounds({ left, right, top, bottom });\n\t\t\t}\n\t\t}\n\t}\n\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tconst chart = this.chart;\n\t\tif (chart) {\n\t\t\tchart.series.each((series) => {\n\t\t\t\tif (series.isType<MapLineSeries>(\"MapLineSeries\")) {\n\t\t\t\t\t$array.each(series.dataItems, (di) => {\n\t\t\t\t\t\tconst pointsToConnect = di.get(\"pointsToConnect\");\n\t\t\t\t\t\tif (pointsToConnect) {\n\t\t\t\t\t\t\t$array.each(pointsToConnect, (point) => {\n\t\t\t\t\t\t\t\tif (point == dataItem) {\n\t\t\t\t\t\t\t\t\t$array.remove(pointsToConnect, point);\n\t\t\t\t\t\t\t\t\tseries.markDirtyValues(di);\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\n\t\tsuper.disposeDataItem(dataItem);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _excludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._excludeDataItem(dataItem);\n\t\tconst bullets = dataItem.bullets;\n\t\tif (bullets) {\n\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tsprite.setPrivate(\"visible\", false);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unexcludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unexcludeDataItem(dataItem);\n\t\tconst bullets = dataItem.bullets;\n\t\tif (bullets) {\n\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tsprite.setPrivate(\"visible\", true);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _notIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._notIncludeDataItem(dataItem);\n\t\tconst bullets = dataItem.bullets;\n\t\tif (bullets) {\n\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tsprite.setPrivate(\"visible\", false);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unNotIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unNotIncludeDataItem(dataItem);\n\t\tconst bullets = dataItem.bullets;\n\t\tif (bullets) {\n\t\t\t$array.each(bullets, (bullet) => {\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\n\t\t\t\tif (sprite) {\n\t\t\t\t\tsprite.setPrivate(\"visible\", true);\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n}\n", "import { Graphics, IGraphicsSettings, IGraphicsPrivate } from \"../../core/render/Graphics\";\nimport type { MapPolygonSeries } from \"./MapPolygonSeries\";\nimport type { IGeoPoint } from \"../../core/util/IGeoPoint\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport * as $mapUtils from \"./MapUtils\";\nimport $polylabel from \"polylabel\";\nimport { geoArea } from \"d3-geo\";\n\nexport interface IMapPolygonSettings extends IGraphicsSettings {\n\n\t/**\n\t * A GeoJSON representation of the polygons geometry.\n\t */\n\tgeometry?: GeoJSON.MultiPolygon | GeoJSON.Polygon;\n\n\t/**\n\t * @todo needs description\n\t * @default 0.5\n\t */\n\tprecision?: number;\n\n}\n\nexport interface IMapPolygonPrivate extends IGraphicsPrivate {\n}\n\n/**\n * A polygon in a [[MapPolygonSeries]].\n */\nexport class MapPolygon extends Graphics {\n\n\tdeclare public _settings: IMapPolygonSettings;\n\tdeclare public _privateSettings: IMapPolygonPrivate;\n\n\tpublic static className: string = \"MapPolygon\";\n\tpublic static classNames: Array<string> = Graphics.classNames.concat([MapPolygon.className]);\n\tprotected _projectionDirty: boolean = false;\n\n\tprotected _afterNew(): void {\n\t\tsuper._afterNew();\n\t\tthis.setPrivate(\"trustBounds\", true);\n\t}\n\n\t/**\n\t * A [[MapPolygonSeries]] polygon belongs to.\n\t */\n\tpublic series: MapPolygonSeries | undefined;\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this._projectionDirty || this.isDirty(\"geometry\") || this.isDirty(\"precision\")) {\n\t\t\tconst geometry = this.get(\"geometry\")!;\n\n\t\t\tif (geometry) {\n\t\t\t\tconst series = this.series;\n\t\t\t\tif (series) {\n\t\t\t\t\tconst projection = series.projection();\n\t\t\t\t\tif (projection) {\n\t\t\t\t\t\tprojection.precision(this.get(\"precision\", 0.5));\n\t\t\t\t\t}\n\n\t\t\t\t\tconst geoPath = series.geoPath();\n\n\t\t\t\t\tif (geoPath) {\n\t\t\t\t\t\tthis._clear = true;\n\n\t\t\t\t\t\tthis.set(\"draw\", (_display) => {\n\t\t\t\t\t\t\tgeoPath.context(this._display as any);\n\t\t\t\t\t\t\tgeoPath(geometry);\n\t\t\t\t\t\t\tgeoPath.context(null);\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\tif (this.isHover()) {\n\t\t\t\t\t\t\tthis.showTooltip();\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\tthis.markDirty();\n\t\tthis._projectionDirty = true;\n\t}\n\n\tpublic _clearDirty() {\n\t\tsuper._clearDirty();\n\t\tthis._projectionDirty = false;\n\t}\n\n\t/**\n\t * Returns latitude/longitude of the geometrical center of the polygon.\n\t *\n\t * @return Center\n\t */\n\tpublic geoCentroid(): IGeoPoint {\n\t\tconst geometry = this.get(\"geometry\")!;\n\t\tif (geometry) {\n\t\t\treturn $mapUtils.getGeoCentroid(geometry);\n\t\t}\n\t\telse {\n\t\t\treturn { latitude: 0, longitude: 0 };\n\t\t}\n\t}\n\n\t/**\n\t * Returns latitude/longitude of the visual center of the polygon.\n\t *\n\t * @return Center\n\t */\n\tpublic visualCentroid(): IGeoPoint {\n\n\t\tlet biggestArea = 0;\n\t\tlet coordinates: number[][][] = [];\n\t\tconst geometry = this.get(\"geometry\")!;\n\n\t\tif (geometry) {\n\t\t\tif (geometry.type == \"Polygon\") {\n\t\t\t\tcoordinates = geometry.coordinates as number[][][];\n\t\t\t}\n\t\t\telse if (geometry.type == \"MultiPolygon\") {\n\t\t\t\tfor (let i = 0; i < geometry.coordinates.length; i++) {\n\t\t\t\t\tlet coords = geometry.coordinates[i] as number[][][];\n\t\t\t\t\tlet area = geoArea({ type: \"Polygon\", coordinates: coords });\n\t\t\t\t\tif (area > biggestArea) {\n\t\t\t\t\t\tcoordinates = coords;\n\t\t\t\t\t\tbiggestArea = area;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (coordinates) {\n\t\t\t\tlet center = $polylabel(coordinates as number[][][]);\n\t\t\t\treturn { longitude: center[0], latitude: center[1] };\n\t\t\t}\n\t\t}\n\t\treturn { longitude: 0, latitude: 0 };\n\t}\n\n\n\tpublic _getTooltipPoint(): IPoint {\n\t\tconst series = this.series;\n\n\t\tif (series) {\n\t\t\tconst projection = series.projection();\n\t\t\tif (projection) {\n\t\t\t\tconst geoPoint = this.visualCentroid();\n\t\t\t\tconst xy = projection([geoPoint.longitude, geoPoint.latitude]);\n\n\t\t\t\tif (xy) {\n\t\t\t\t\treturn { x: xy[0], y: xy[1] }\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn { x: 0, y: 0 };\n\t}\n}\n", "import type { DataItem } from \"../../core/render/Component\";\nimport type { Animation } from \"../../core/util/Entity\";\nimport type { IPoint } from \"../../core/util/IPoint\";\nimport type { IGeoPoint } from \"../../core/util/IGeoPoint\";\n\nimport { MapSeries, IMapSeriesSettings, IMapSeriesDataItem, IMapSeriesPrivate } from \"./MapSeries\";\nimport { MapPolygon } from \"./MapPolygon\";\nimport { Template } from \"../../core/util/Template\";\nimport { ListTemplate } from \"../../core/util/List\";\n\nimport * as $array from \"../../core/util/Array\";\nimport * as $mapUtils from \"./MapUtils\";\n\n\n\nexport interface IMapPolygonSeriesPrivate extends IMapSeriesPrivate {\n}\n\nexport interface IMapPolygonSeriesDataItem extends IMapSeriesDataItem {\n\n\t/**\n\t * Related [[MapPolygon]] object.\n\t */\n\tmapPolygon: MapPolygon;\n\n\t/**\n\t * GeoJSON geometry of the polygon.\n\t */\n\tgeometry?: GeoJSON.Polygon | GeoJSON.MultiPolygon;\n}\n\nexport interface IMapPolygonSeriesSettings extends IMapSeriesSettings {\n\n\t/**\n\t * If set to `true`, the order of coordinates in GeoJSON will be flipped.\n\t *\n\t * Some GeoJSON software produces those in reverse order, so if your custom\n\t * map appears garbled, try this setting.\n\t * \n\t * @default false\n\t * @since 5.2.42\n\t */\n\treverseGeodata?: boolean;\n\n}\n\n/**\n * Creates a map series for displaying polygons.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-polygon-series/} for more info\n * @important\n */\nexport class MapPolygonSeries extends MapSeries {\n\n\t/**\n\t * @ignore\n\t */\n\tpublic makeMapPolygon(dataItem: DataItem<this[\"_dataItemSettings\"]>): MapPolygon {\n\t\tconst mapPolygon = this.children.push(this.mapPolygons.make());\n\t\tmapPolygon._setDataItem(dataItem);\n\t\tthis.mapPolygons.push(mapPolygon);\n\t\treturn mapPolygon;\n\t}\n\n\t/**\n\t * A [[ListTemplate]] of all polygons in series.\n\t *\n\t * `mapPolygons.template` can also be used to configure polygons.\n\t *\n\t * @default new ListTemplate<MapPolygon>\n\t */\n\tpublic readonly mapPolygons: ListTemplate<MapPolygon> = new ListTemplate(\n\t\tTemplate.new({}),\n\t\t() => MapPolygon._new(this._root, {}, [this.mapPolygons.template])\n\t);\n\n\tpublic static className: string = \"MapPolygonSeries\";\n\tpublic static classNames: Array<string> = MapSeries.classNames.concat([MapPolygonSeries.className]);\n\n\tdeclare public _settings: IMapPolygonSeriesSettings;\n\tdeclare public _privateSettings: IMapPolygonSeriesPrivate;\n\tdeclare public _dataItemSettings: IMapPolygonSeriesDataItem;\n\n\tprotected _types: Array<GeoJSON.GeoJsonGeometryTypes> = [\"Polygon\", \"MultiPolygon\"];\n\n\t/**\n\t * @ignore\n\t */\n\tpublic markDirtyProjection() {\n\t\t$array.each(this.dataItems, (dataItem) => {\n\t\t\tlet mapPolygon = dataItem.get(\"mapPolygon\");\n\t\t\tif (mapPolygon) {\n\t\t\t\tmapPolygon.markDirtyProjection();\n\t\t\t}\n\t\t})\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isDirty(\"fill\")) {\n\t\t\tthis.mapPolygons.template.set(\"fill\", this.get(\"fill\"));\n\t\t}\n\t\tif (this.isDirty(\"stroke\")) {\n\t\t\tthis.mapPolygons.template.set(\"stroke\", this.get(\"stroke\"));\n\t\t}\n\t}\n\n\tprotected processDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.processDataItem(dataItem);\n\n\t\tlet mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (!mapPolygon) {\n\t\t\tmapPolygon = this.makeMapPolygon(dataItem);\n\t\t}\n\n\t\tdataItem.set(\"mapPolygon\", mapPolygon);\n\t\tlet geometry = dataItem.get(\"geometry\")!;\n\n\t\tif (geometry) {\n\t\t\tif (this.get(\"reverseGeodata\")) {\n\t\t\t\tconst coordinates = geometry.coordinates;\n\t\t\t\tif (coordinates) {\n\t\t\t\t\tfor (let x = 0; x < geometry.coordinates.length; x++) {\n\t\t\t\t\t\tif (geometry.type == \"MultiPolygon\") {\n\t\t\t\t\t\t\tfor (let y = 0; y < geometry.coordinates[x].length; y++) {\n\t\t\t\t\t\t\t\tgeometry.coordinates[x][y].reverse()\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse {\n\t\t\t\t\t\t\tgeometry.coordinates[x].reverse()\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tmapPolygon.set(\"geometry\", geometry);\n\t\t}\n\n\t\tmapPolygon.series = this;\n\n\t\tthis._addGeometry(dataItem.get(\"geometry\"), this);\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tpublic disposeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.disposeDataItem(dataItem);\n\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (mapPolygon) {\n\t\t\tthis.mapPolygons.removeValue(mapPolygon);\n\t\t\tmapPolygon.dispose();\n\t\t}\n\t\tthis._removeGeometry(dataItem.get(\"geometry\"));\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _excludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._excludeDataItem(dataItem);\n\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (mapPolygon) {\n\t\t\tmapPolygon.setPrivate(\"visible\", false);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unexcludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unexcludeDataItem(dataItem);\n\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (mapPolygon) {\n\t\t\tmapPolygon.setPrivate(\"visible\", true);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _notIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._notIncludeDataItem(dataItem);\n\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (mapPolygon) {\n\t\t\tmapPolygon.setPrivate(\"visible\", false);\n\t\t}\n\t}\n\n\t/**\n\t * @ignore\n\t */\n\tprotected _unNotIncludeDataItem(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper._unNotIncludeDataItem(dataItem);\n\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\tif (mapPolygon) {\n\t\t\tmapPolygon.setPrivate(\"visible\", true);\n\t\t}\n\t}\n\n\t/**\n\t * Forces a repaint of the element which relies on data.\n\t *\n\t * @since 5.0.21\n\t */\n\tpublic markDirtyValues(dataItem: DataItem<this[\"_dataItemSettings\"]>) {\n\t\tsuper.markDirtyValues();\n\t\tif (dataItem) {\n\t\t\tconst mapPolygon = dataItem.get(\"mapPolygon\");\n\t\t\tif (mapPolygon) {\n\t\t\t\tmapPolygon.set(\"geometry\", dataItem.get(\"geometry\"));\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Centers and zooms in on the specific polygon.\n\t *\n\t * @param  dataItem  Target data item\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Zooming_to_clicked_object} for more info\n\t * @param  rotate If it's true, the map will rotate so that this polygon would be in the center. Mostly usefull with geoOrthographic projection.\n\t */\n\tpublic zoomToDataItem(dataItem: DataItem<IMapPolygonSeriesDataItem>, rotate?: boolean): Animation<any> | undefined {\n\t\tconst polygon = dataItem.get(\"mapPolygon\");\n\t\tif (polygon) {\n\t\t\tconst geometry = polygon.get(\"geometry\");\n\t\t\tconst chart = this.chart;\n\n\t\t\tif (geometry && chart) {\n\n\t\t\t\tif (rotate) {\n\t\t\t\t\tconst centroid = $mapUtils.getGeoCentroid(geometry);\n\t\t\t\t\tchart.rotate(-centroid.longitude, -centroid.latitude);\n\t\t\t\t\treturn chart.zoomToGeoBounds($mapUtils.getGeoBounds(geometry), undefined, -centroid.longitude, -centroid.latitude);\n\t\t\t\t}\n\n\t\t\t\treturn chart.zoomToGeoBounds($mapUtils.getGeoBounds(geometry),);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Zooms the map in so that all polygons in the array are visible.\n\t *\n\t * @param   dataItems  An array of data items to zoom to\n\t * @param   rotate     Rotate the map so it is centered on the selected items\n\t * @return             Animation\n\t * @since 5.9.0\n\t */\n\tpublic zoomToDataItems(dataItems: Array<DataItem<IMapPolygonSeriesDataItem>>, rotate?: boolean): Animation<any> | undefined {\n\t\tlet left!: number;\n\t\tlet right!: number;\n\t\tlet top!: number;\n\t\tlet bottom!: number;\n\n\t\t$array.each(dataItems, (dataItem) => {\n\n\t\t\tconst polygon = dataItem.get(\"mapPolygon\");\n\t\t\tif (polygon) {\n\t\t\t\tconst geometry = polygon.get(\"geometry\");\n\t\t\t\tif (geometry) {\n\t\t\t\t\tlet bounds = $mapUtils.getGeoBounds(geometry);\n\n\t\t\t\t\tif (left == null) {\n\t\t\t\t\t\tleft = bounds.left;\n\t\t\t\t\t}\n\t\t\t\t\tif (right == null) {\n\t\t\t\t\t\tright = bounds.right;\n\t\t\t\t\t}\n\t\t\t\t\tif (top == null) {\n\t\t\t\t\t\ttop = bounds.top;\n\t\t\t\t\t}\n\t\t\t\t\tif (bottom == null) {\n\t\t\t\t\t\tbottom = bounds.bottom;\n\t\t\t\t\t}\n\n\t\t\t\t\tleft = Math.min(bounds.left, left);\n\t\t\t\t\tright = Math.max(bounds.right, right);\n\t\t\t\t\ttop = Math.max(bounds.top, top);\n\t\t\t\t\tbottom = Math.min(bounds.bottom, bottom);\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tif (left != null && right != null && top != null && bottom != null) {\n\t\t\tconst chart = this.chart;\n\t\t\tif (chart) {\n\t\t\t\tif (rotate) {\n\t\t\t\t\tconst rx = left + (right - left) / 2;\n\t\t\t\t\tconst ry = bottom + (top - bottom) / 2;\n\n\t\t\t\t\tchart.rotate(-rx, -ry);\n\t\t\t\t\treturn chart.zoomToGeoBounds({ left, right, top, bottom }, undefined, -rx, -ry);\n\t\t\t\t}\n\n\t\t\t\treturn chart.zoomToGeoBounds({ left, right, top, bottom });\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Returns a [[MapPolygon]] that is under specific X/Y point.\n\t *\n\t * @since 5.9.8\n\t * @param   point  X/Y\n\t * @return         Polygon\n\t */\n\tpublic getPolygonByPoint(point: IPoint): MapPolygon | undefined {\n\t\tlet found: MapPolygon | undefined;\n\t\tconst renderer = this._display._renderer;\n\t\tconst displayObject = (renderer as any).getObjectAtPoint(point);\n\t\tif (displayObject) {\n\t\t\tthis.mapPolygons.each(function(polygon) {\n\t\t\t\tif (polygon._display == displayObject) {\n\t\t\t\t\tfound = polygon;\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn found;\n\t\t}\n\t}\n\t\n\tpublic getPolygonByGeoPoint(point: IGeoPoint): MapPolygon | undefined {\n\t\treturn this.getPolygonByPoint(this.chart!.convert(point));\n\t}\n}", "import { MapPointSeries, IMapPointSeriesSettings, IMapPointSeriesPrivate, IMapPointSeriesDataItem } from \"./MapPointSeries\";\r\nimport { Component, DataItem, IComponentDataItem } from \"../../core/render/Component\";\r\nimport type { Root } from \"../../core/Root\";\r\nimport type { Bullet } from \"../../core/render/Bullet\";\r\nimport { Container } from \"../../core/render/Container\";\r\nimport { Label } from \"../../core/render/Label\";\r\nimport type { IDisposer } from \"../../core/util/Disposer\";\r\n\r\nimport * as $array from \"../../core/util/Array\";\r\nimport * as $object from \"../../core/util/Object\";\r\nimport * as d3hierarchy from \"d3-hierarchy\";\r\nimport * as $math from \"../../core/util/Math\";\r\n\r\n\r\nexport interface IClusteredDataItem extends IComponentDataItem {\r\n\t/**\r\n\t * All the data items of this cluster\r\n\t */\r\n\tchildren?: Array<DataItem<IMapPointSeriesDataItem>>;\r\n\r\n\t/**\r\n\t * Bullet of clustered data item\r\n\t */\r\n\tbullet?: Bullet;\r\n\r\n\t/**\r\n\t * An ID of a group.\r\n\t */\r\n\tgroupId?: string\r\n\r\n\t/**\r\n\t * Longitude.\r\n\t */\r\n\tlongitude?: number;\r\n\r\n\t/**\r\n\t * Latitude.\r\n\t */\r\n\tlatitude?: number;\r\n}\r\n\r\nexport interface IClusteredPointSeriesDataItem extends IMapPointSeriesDataItem {\r\n\t/**\r\n\t * An ID of a bullet's group.\r\n\t */\r\n\tgroupId?: string\r\n\r\n\t/**\r\n\t * Clustered data item (if available)\r\n\t * @readonly\r\n\t */\r\n\tcluster?: DataItem<IClusteredDataItem>;\r\n\r\n\t/**\r\n\t * How much bullet was moved from its original position\r\n\t */\r\n\tdx?: number;\r\n\r\n\t/**\r\n\t * How much bullet was moved from its original position\r\n\t */\r\n\tdy?: number;\r\n}\r\n\r\nexport interface IClusteredPointSeriesPrivate extends IMapPointSeriesPrivate {\r\n\r\n}\r\n\r\nexport interface IClusteredPointSeriesSettings extends IMapPointSeriesSettings {\r\n\t/**\r\n\t * Series data can contain a field with an ID of a virtual group the bullet\r\n\t * belongs to.\r\n\t *\r\n\t * For example, we migth want bullets to group with other bullets from the\r\n\t * same continent.\r\n\t *\r\n\t * `groupIdField` specifies which field in source data holds group IDs.\r\n\t *\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Group_segregation} for more info\r\n\t * @default groupID\r\n\t */\r\n\tgroupIdField?: string;\r\n\r\n\t/**\r\n\t * Bullets that are closer than X pixels apart, will be automatically grouped.\r\n\t *\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Minimal_distance} for more info\r\n\t * @default 20\r\n\t */\r\n\tminDistance?: number;\r\n\r\n\t/**\r\n\t * Set this to a [[Bullet]] instance which will be used to show groups.\r\n\t *\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Group_bullet} for more info\r\n\t */\r\n\tclusteredBullet?: (root: Root, series: ClusteredPointSeries, dataItem: DataItem<IClusteredDataItem>) => Bullet | undefined;\r\n\r\n\t/**\r\n\t * If bullets are closer to each other than `scatterDistance`, they will be\r\n\t * scattered so that all are visible.\r\n\t *\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Scatter_settings} for more info\r\n\t * @default 5\r\n\t * @since 5.5.7\r\n\t */\r\n\tscatterDistance?: number;\r\n\r\n\t/**\r\n\t * Presumed radius of a each bullet when scattering them.\r\n\t * \r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Scatter_settings} for more info\r\n\t * @default 8\r\n\t * @since 5.5.7\r\n\t */\r\n\tscatterRadius?: number;\r\n\r\n\t/**\r\n\t * If a map is zoomed to a maxZoomLevel * stopClusterZoom, clusters will be\r\n\t * disabled.\r\n\t * \r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Scatter_settings} for more info\r\n\t * @default 0.95\r\n\t * @since 5.5.7\r\n\t */\r\n\tstopClusterZoom?: number\r\n\r\n\r\n\t/**\r\n\t * Delay in milliseconds before clustering is made.\r\n\t * \r\n\t * This is useful if you have many data items and want to avoid re-clustering\r\n\t * on every zoom/position change.\r\n\t * \r\n\t * @default 0\r\n\t * @since 5.9.11\r\n\t */\r\n\tclusterDelay?: number;\r\n}\r\n\r\n/**\r\n * A version of [[MapPointSeries]] which can automatically group closely located\r\n * bullets into groups.\r\n * \r\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/} for more info\r\n * @since 5.5.6\r\n * @important\r\n */\r\nexport class ClusteredPointSeries extends MapPointSeries {\r\n\r\n\tpublic static className: string = \"ClusteredPointSeries\";\r\n\tpublic static classNames: Array<string> = MapPointSeries.classNames.concat([ClusteredPointSeries.className]);\r\n\r\n\tdeclare public _settings: IClusteredPointSeriesSettings;\r\n\tdeclare public _privateSettings: IClusteredPointSeriesPrivate;\r\n\tdeclare public _dataItemSettings: IClusteredPointSeriesDataItem;\r\n\r\n\tprotected _dataItem: DataItem<this[\"_dataItemSettings\"]> = this.makeDataItem({});\r\n\tprotected _clusterIndex: number = 0;\r\n\tprotected _clusters: Array<Array<DataItem<this[\"_dataItemSettings\"]>>> = [];\r\n\tpublic clusteredDataItems: Array<DataItem<IClusteredDataItem>> = [];\r\n\r\n\tprotected _scatterIndex: number = 0;\r\n\tprotected _scatters: Array<Array<DataItem<this[\"_dataItemSettings\"]>>> = [];\r\n\r\n\tpublic _packLayout = d3hierarchy.pack();\r\n\r\n\tprotected _spiral: Array<{ x: number, y: number }> = [];\r\n\r\n\tprotected _clusterDP?: IDisposer;\r\n\r\n\tprotected _previousZL: number = 0;\r\n\r\n\tprotected _afterNew() {\r\n\t\tthis.fields.push(\"groupId\");\r\n\t\tthis._setRawDefault(\"groupIdField\", \"groupId\");\r\n\r\n\t\tsuper._afterNew();\r\n\t}\r\n\r\n\tpublic _updateChildren() {\r\n\t\tsuper._updateChildren();\r\n\r\n\t\tif (this.isDirty(\"scatterRadius\")) {\r\n\t\t\tthis._spiral = $math.spiralPoints(0, 0, 300, 300, 0, 3, 3, 0, 0)\r\n\t\t}\r\n\r\n\t\tconst chart = this.chart;\r\n\r\n\t\tif (chart) {\r\n\r\n\t\t\tconst zoomLevel = chart.get(\"zoomLevel\", 1);\r\n\r\n\t\t\tif (zoomLevel != this._previousZL) {\r\n\t\t\t\tconst clusterDelay = this.get(\"clusterDelay\", 0);\r\n\t\t\t\tif (clusterDelay) {\r\n\t\t\t\t\tif (this._clusterDP) {\r\n\t\t\t\t\t\tthis._clusterDP.dispose();\r\n\r\n\t\t\t\t\t\tthis._clusterDP = this.setTimeout(() => {\r\n\t\t\t\t\t\t\tthis._doTheCluster();\r\n\t\t\t\t\t\t}, clusterDelay)\r\n\t\t\t\t\t}\r\n\t\t\t\t\telse {\r\n\t\t\t\t\t\t// first time without delay\r\n\t\t\t\t\t\tthis._doTheCluster();\r\n\t\t\t\t\t\tthis._clusterDP = this.setTimeout(() => { }, 0);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tthis._doTheCluster();\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis._previousZL = zoomLevel;\r\n\t\t\t}\r\n\r\n\t\t\t$array.each(this.clusteredDataItems, (dataItem) => {\r\n\t\t\t\tconst bullet = dataItem.get(\"bullet\" as any);\r\n\t\t\t\tconst longitude = dataItem.get(\"longitude\", 0);\r\n\t\t\t\tconst latitude = dataItem.get(\"latitude\", 0);\r\n\t\t\t\tthis._positionBulletReal(bullet, { type: \"Point\", coordinates: [longitude, latitude] }, [longitude, latitude]);\r\n\t\t\t})\t\t\t\r\n\t\t}\r\n\t}\r\n\r\n\r\n\tprotected _doTheCluster() {\r\n\t\tconst groups: { [index: string]: Array<DataItem<IClusteredPointSeriesDataItem>> } = {};\r\n\t\t// distribute to groups\r\n\t\t$array.each(this.dataItems, (dataItem) => {\r\n\t\t\tconst groupId = dataItem.get(\"groupId\", \"_default\");\r\n\r\n\t\t\tif (!groups[groupId]) {\r\n\t\t\t\tgroups[groupId] = [];\r\n\t\t\t}\r\n\t\t\tgroups[groupId].push(dataItem);\r\n\t\t})\r\n\r\n\t\tthis._scatterIndex = -1;\r\n\t\tthis._scatters = [];\r\n\t\tthis._clusterIndex = -1;\r\n\t\tthis._clusters = [];\r\n\r\n\t\t$array.each(this.clusteredDataItems, (dataItem) => {\r\n\t\t\tdataItem.setRaw(\"children\", undefined);\r\n\t\t})\r\n\r\n\t\t$array.each(this.dataItems, (dataItem) => {\r\n\t\t\tdataItem.setRaw(\"cluster\", undefined);\r\n\t\t})\r\n\r\n\t\t$object.each(groups, (_key, group) => {\r\n\t\t\tthis._scatterGroup(group);\r\n\t\t})\r\n\r\n\r\n\t\t$object.each(groups, (_key, group) => {\r\n\t\t\tthis._clusterGroup(group);\r\n\t\t})\r\n\r\n\t\t$array.each(this.dataItems, (dataItem) => {\r\n\t\t\tif (!dataItem.get(\"cluster\")) {\r\n\t\t\t\tconst bullets = dataItem.bullets;\r\n\t\t\t\tif (bullets) {\r\n\t\t\t\t\t$array.each(bullets, (bullet) => {\r\n\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\t\tsprite.set(\"forceHidden\", false);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\r\n\t/**\r\n\t * Zooms to the area so that all clustered data items of a cluster would be\r\n\t * visible.\r\n\t *\r\n\t * Pass in `true` as a second parameter to rotate that map so that the group\r\n\t * is in the center. This is especially useful in the maps that use\r\n\t * Orthographic (globe) projection.\r\n\t *\r\n\t * @param  dataItem  Group data item\r\n\t * @param  rotate    Rotate the map so that group is in the center?\r\n\t * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/clustered-point-series/#Drill_down} for more info\r\n\t */\r\n\tpublic zoomToCluster(dataItem: DataItem<IClusteredDataItem>, rotate?: boolean) {\r\n\t\tthis.zoomToDataItems(dataItem.get(\"children\", []), rotate);\r\n\t}\r\n\r\n\tprotected _clusterGroup(dataItems: Array<DataItem<IClusteredPointSeriesDataItem>>) {\r\n\t\tconst chart = this.chart;\r\n\t\tif (chart && chart.get(\"zoomLevel\", 1) >= chart.get(\"maxZoomLevel\", 100) * this.get(\"stopClusterZoom\", 0.95)) {\r\n\t\t\t// void\r\n\t\t}\r\n\t\telse {\r\n\r\n\t\t\tdataItems.sort((a, b) => {\r\n\t\t\t\tconst pointA = a.get(\"point\");\r\n\t\t\t\tconst pointB = b.get(\"point\");\r\n\t\t\t\tif (pointA && pointB) {\r\n\t\t\t\t\treturn Math.hypot(pointA.x - pointB.x, pointA.y - pointB.y);\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn 0;\r\n\t\t\t})\r\n\r\n\t\t\twhile (dataItems.length > 0) {\r\n\t\t\t\tthis._clusterIndex++;\r\n\t\t\t\tthis._clusters[this._clusterIndex] = [];\r\n\t\t\t\tconst cluster = this._clusters[this._clusterIndex];\r\n\t\t\t\tconst dataItem = dataItems[0];\r\n\r\n\t\t\t\tcluster.push(dataItem);\r\n\t\t\t\t$array.removeFirst(dataItems, dataItem);\r\n\r\n\t\t\t\tthis._clusterDataItem(dataItem, dataItems);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\tlet i = 0;\r\n\r\n\t\tconst bulletMethod = this.get(\"clusteredBullet\");\r\n\t\tif (bulletMethod) {\r\n\t\t\t$array.each(this._clusters, (cluster) => {\r\n\t\t\t\tlet sumX = 0;\r\n\t\t\t\tlet sumY = 0;\r\n\r\n\t\t\t\tlet len = cluster.length;\r\n\r\n\t\t\t\tif (len > 1) {\r\n\r\n\t\t\t\t\tlet clusteredDataItem = this.clusteredDataItems[i];\r\n\t\t\t\t\tif (!clusteredDataItem) {\r\n\t\t\t\t\t\tclusteredDataItem = new DataItem(this, undefined, {});\r\n\r\n\t\t\t\t\t\tconst bullet = clusteredDataItem.set(\"bullet\" as any, bulletMethod(this._root, this, clusteredDataItem));\r\n\r\n\t\t\t\t\t\tif (bullet) {\r\n\t\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\t\t\tthis.bulletsContainer.children.push(sprite);\r\n\t\t\t\t\t\t\t\tsprite._setDataItem(clusteredDataItem);\r\n\r\n\t\t\t\t\t\t\t\tthis.root.events.once(\"frameended\", () => {\r\n\t\t\t\t\t\t\t\t\tif (sprite instanceof Container) {\r\n\t\t\t\t\t\t\t\t\t\tsprite.walkChildren((child) => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (child instanceof Component) {\r\n\t\t\t\t\t\t\t\t\t\t\t\tchild.markDirtyValues();\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis.clusteredDataItems.push(clusteredDataItem)\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tlet groupId;\r\n\r\n\t\t\t\t\t$array.each(cluster, (dataItem) => {\r\n\t\t\t\t\t\tdataItem.setRaw(\"cluster\", clusteredDataItem);\r\n\r\n\t\t\t\t\t\tconst point = dataItem.get(\"point\");\r\n\t\t\t\t\t\tif (point) {\r\n\t\t\t\t\t\t\tsumX += point.x;\r\n\t\t\t\t\t\t\tsumY += point.y;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconst bullets = dataItem.bullets;\r\n\t\t\t\t\t\tif (bullets) {\r\n\t\t\t\t\t\t\t$array.each(bullets, (bullet) => {\r\n\t\t\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\t\t\t\tsprite.set(\"forceHidden\", true);\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tgroupId = dataItem.get(\"groupId\");\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t\tlet averageX = sumX / len;\r\n\t\t\t\t\tlet averageY = sumY / len;\r\n\r\n\t\t\t\t\tclusteredDataItem.setRaw(\"children\" as any, cluster);\r\n\t\t\t\t\tclusteredDataItem.setRaw(\"groupId\", groupId);\r\n\r\n\t\t\t\t\tconst prevLen = clusteredDataItem.get(\"value\" as any);\r\n\t\t\t\t\tclusteredDataItem.setRaw(\"value\" as any, len);\r\n\r\n\t\t\t\t\tconst bullet = clusteredDataItem.get(\"bullet\" as any);\r\n\t\t\t\t\tif (bullet) {\r\n\r\n\t\t\t\t\t\tlet geoPoint = this.chart!.invert({ x: averageX, y: averageY });\r\n\t\t\t\t\t\tif (geoPoint) {\r\n\t\t\t\t\t\t\tclusteredDataItem.setAll({\r\n\t\t\t\t\t\t\t\tlongitude: geoPoint.longitude,\r\n\t\t\t\t\t\t\t\tlatitude: geoPoint.latitude,\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tthis._positionBullets(clusteredDataItem)\r\n\r\n\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\t\tsprite.set(\"forceHidden\", false);\r\n\t\t\t\t\t\t\t//sprite.setAll({ x: averageX, y: averageY });\r\n\r\n\t\t\t\t\t\t\tif (prevLen != len) {\r\n\t\t\t\t\t\t\t\tif (sprite instanceof Container) {\r\n\t\t\t\t\t\t\t\t\tsprite.walkChildren((child) => {\r\n\t\t\t\t\t\t\t\t\t\tif (child instanceof Label) {\r\n\t\t\t\t\t\t\t\t\t\t\tchild.text.markDirtyText();\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\ti++;\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\r\n\t\t$array.each(this.clusteredDataItems, (dataItem) => {\r\n\t\t\tlet children = dataItem.get(\"children\");\r\n\t\t\tif (!children || children.length == 0) {\r\n\t\t\t\tconst bullet = dataItem.get(\"bullet\" as any);\r\n\t\t\t\tif (bullet) {\r\n\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\tsprite.set(\"forceHidden\", true);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t}\r\n\r\n\tprotected _onDataClear() {\r\n\t\tsuper._onDataClear();\r\n\r\n\t\t$array.each(this.clusteredDataItems, (dataItem) => {\r\n\t\t\tconst bullet = dataItem.get(\"bullet\" as any);\r\n\t\t\tif (bullet) {\r\n\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\tif (sprite) {\r\n\t\t\t\t\tsprite.dispose();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t})\r\n\t\tthis.clusteredDataItems = [];\r\n\r\n\t}\r\n\r\n\tprotected _clusterDataItem(dataItem: DataItem<IClusteredPointSeriesDataItem>, dataItems: Array<DataItem<IClusteredPointSeriesDataItem>>) {\r\n\t\tconst point = dataItem.get(\"point\");\r\n\t\tif (point) {\r\n\t\t\tlet minDistance = this.get(\"minDistance\", 20);\r\n\t\t\tconst cluster = this._clusters[this._clusterIndex];\r\n\r\n\t\t\tfor (let i = dataItems.length - 1; i >= 0; i--) {\r\n\t\t\t\tconst di = dataItems[i];\r\n\t\t\t\tif (di && !di.get(\"clipped\")) {\r\n\t\t\t\t\tconst diPoint = di.get(\"point\");\r\n\t\t\t\t\tif (diPoint) {\r\n\t\t\t\t\t\tif (Math.hypot(diPoint.x - point.x, diPoint.y - point.y) < minDistance) {\r\n\t\t\t\t\t\t\tcluster.push(di);\r\n\t\t\t\t\t\t\t$array.removeFirst(dataItems, di);\r\n\t\t\t\t\t\t\tthis._clusterDataItem(di, dataItems);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\tprotected _scatterGroup(dataItems: Array<DataItem<IClusteredPointSeriesDataItem>>) {\r\n\t\tconst chart = this.chart;\r\n\t\tif (chart && chart.get(\"zoomLevel\", 1) >= chart.get(\"maxZoomLevel\", 100) * this.get(\"stopClusterZoom\", 0.95)) {\r\n\t\t\twhile (dataItems.length > 0) {\r\n\t\t\t\tthis._scatterIndex++;\r\n\t\t\t\tthis._scatters[this._scatterIndex] = [];\r\n\t\t\t\tconst scatter = this._scatters[this._scatterIndex];\r\n\t\t\t\tconst dataItem = dataItems[0];\r\n\r\n\t\t\t\tscatter.push(dataItem);\r\n\t\t\t\t$array.remove(dataItems, dataItem);\r\n\r\n\t\t\t\tthis._scatterDataItem(dataItem, dataItems);\r\n\t\t\t}\r\n\r\n\t\t\t$array.each(this._scatters, (scatter) => {\r\n\t\t\t\tlet len = scatter.length;\r\n\r\n\t\t\t\tif (len > 1) {\r\n\t\t\t\t\tlet previousCircles: Array<{ x: number, y: number, radius: number }> = [];\r\n\t\t\t\t\tlet s = 0;\r\n\t\t\t\t\tlet radius = this.get(\"scatterRadius\", 8);\r\n\t\t\t\t\t$array.each(scatter, (dataItem) => {\r\n\t\t\t\t\t\tlet spiralPoint = this._spiral[s];\r\n\t\t\t\t\t\tlet intersects = true;\r\n\r\n\t\t\t\t\t\tif (previousCircles.length > 0) {\r\n\t\t\t\t\t\t\twhile (intersects) {\r\n\t\t\t\t\t\t\t\t$array.each(previousCircles, (previousCircle) => {\r\n\t\t\t\t\t\t\t\t\tintersects = false;\r\n\t\t\t\t\t\t\t\t\twhile ($math.circlesOverlap({ x: spiralPoint.x, y: spiralPoint.y, radius: radius }, previousCircle)) {\r\n\t\t\t\t\t\t\t\t\t\ts++;\r\n\r\n\t\t\t\t\t\t\t\t\t\tif (this._spiral[s] == undefined) {\r\n\t\t\t\t\t\t\t\t\t\t\tintersects = false;\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\telse {\r\n\t\t\t\t\t\t\t\t\t\t\tintersects = true;\r\n\t\t\t\t\t\t\t\t\t\t\tspiralPoint = this._spiral[s];\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\tconst dx = spiralPoint.x;\r\n\t\t\t\t\t\tconst dy = spiralPoint.y;\r\n\r\n\t\t\t\t\t\tpreviousCircles.push({ x: dx, y: dy, radius: radius });\r\n\r\n\t\t\t\t\t\tdataItem.set(\"dx\", dx);\r\n\t\t\t\t\t\tdataItem.set(\"dy\", dy);\r\n\r\n\t\t\t\t\t\tconst bullets = dataItem.bullets;\r\n\t\t\t\t\t\tif (bullets) {\r\n\t\t\t\t\t\t\t$array.each(bullets, (bullet) => {\r\n\t\t\t\t\t\t\t\tconst sprite = bullet.get(\"sprite\");\r\n\t\t\t\t\t\t\t\tif (sprite) {\r\n\t\t\t\t\t\t\t\t\tsprite.setAll({ dx: dx, dy: dy });\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n\r\n\tprotected _scatterDataItem(dataItem: DataItem<IClusteredPointSeriesDataItem>, dataItems: Array<DataItem<IClusteredPointSeriesDataItem>>) {\r\n\t\tconst point = dataItem.get(\"point\");\r\n\t\tif (point) {\r\n\t\t\tconst scatterDistance = this.get(\"scatterDistance\", 5)\r\n\t\t\tconst scatter = this._scatters[this._scatterIndex];\r\n\t\t\t$array.each(dataItems, (di) => {\r\n\t\t\t\tif (di && !di.get(\"clipped\")) {\r\n\t\t\t\t\tconst diPoint = di.get(\"point\");\r\n\r\n\t\t\t\t\tif (diPoint) {\r\n\t\t\t\t\t\tif (Math.hypot(diPoint.x - point.x, diPoint.y - point.y) < scatterDistance) {\r\n\t\t\t\t\t\t\tscatter.push(di);\r\n\t\t\t\t\t\t\t$array.removeFirst(dataItems, di);\r\n\t\t\t\t\t\t\tthis._scatterDataItem(di, dataItems);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t}\r\n\t}\r\n}\r\n", "import type { <PERSON><PERSON><PERSON> } from \"./MapChart\";\n\nimport { ZoomTools, IZoomToolsPrivate, IZoomToolsSettings } from \"../../core/render/ZoomTools\";\n\nexport interface IZoomControlSettings extends IZoomToolsSettings {\n}\n\nexport interface IZoomControlPrivate extends IZoomToolsPrivate {\n\t/**\n\t * @ignore\n\t */\n\tchart?: MapChart;\n}\n\n/**\n * A control that displays button for zooming [[MapChart]] in and out.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/map-chart/map-pan-zoom/#Zoom_control} for more information\n * @important\n */\nexport class ZoomControl extends ZoomTools {\n\tpublic static className: string = \"ZoomControl\";\n\tpublic static classNames: Array<string> = ZoomTools.classNames.concat([ZoomControl.className]);\n\n\tdeclare public _settings: IZoomControlSettings;\n\tdeclare public _privateSettings: IZoomControlPrivate;\n\n\tprotected _afterNew() {\n\t\tsuper._afterNew();\n\t\tthis.addTag(\"zoomtools\");\n\t}\n\n\tpublic _prepareChildren() {\n\t\tsuper._prepareChildren();\n\n\t\tif (this.isPrivateDirty(\"chart\")) {\n\t\t\tthis.set(\"target\", this.getPrivate(\"chart\"));\n\t\t}\n\t}\n}\n", "import {asin, cos, epsilon, sin} from \"../math.js\";\nimport {azimuthalInvert} from \"./azimuthal.js\";\nimport projection from \"./index.js\";\n\nexport function orthographicRaw(x, y) {\n  return [cos(y) * sin(x), sin(y)];\n}\n\northographicRaw.invert = azimuthalInvert(asin);\n\nexport default function() {\n  return projection(orthographicRaw)\n      .scale(249.5)\n      .clipAngle(90 + epsilon);\n}\n", "import projection from \"./index.js\";\n\nexport function equirectangularRaw(lambda, phi) {\n  return [lambda, phi];\n}\n\nequirectangularRaw.invert = equirectangularRaw;\n\nexport default function() {\n  return projection(equirectangularRaw)\n      .scale(152.63);\n}\n", "import {abs, asin, atan2, cos, epsilon, pi, sign, sin, sqrt} from \"../math.js\";\nimport {conicProjection} from \"./conic.js\";\nimport {cylindricalEqualAreaRaw} from \"./cylindricalEqualArea.js\";\n\nexport function conicEqualAreaRaw(y0, y1) {\n  var sy0 = sin(y0), n = (sy0 + sin(y1)) / 2;\n\n  // Are the parallels symmetrical around the Equator?\n  if (abs(n) < epsilon) return cylindricalEqualAreaRaw(y0);\n\n  var c = 1 + sy0 * (2 * n - sy0), r0 = sqrt(c) / n;\n\n  function project(x, y) {\n    var r = sqrt(c - 2 * n * sin(y)) / n;\n    return [r * sin(x *= n), r0 - r * cos(x)];\n  }\n\n  project.invert = function(x, y) {\n    var r0y = r0 - y,\n        l = atan2(x, abs(r0y)) * sign(r0y);\n    if (r0y * n < 0)\n      l -= pi * sign(x) * sign(r0y);\n    return [l / n, asin((c - (x * x + r0y * r0y) * n * n) / (2 * n))];\n  };\n\n  return project;\n}\n\nexport default function() {\n  return conicProjection(conicEqualAreaRaw)\n      .scale(155.424)\n      .center([0, 33.6442]);\n}\n", "import {asin, cos, sin} from \"../math.js\";\n\nexport function cylindricalEqualAreaRaw(phi0) {\n  var cosPhi0 = cos(phi0);\n\n  function forward(lambda, phi) {\n    return [lambda * cosPhi0, sin(phi) / cosPhi0];\n  }\n\n  forward.invert = function(x, y) {\n    return [x / cosPhi0, asin(y * cosPhi0)];\n  };\n\n  return forward;\n}\n", "import {degrees, pi, radians} from \"../math.js\";\nimport {projectionMutator} from \"./index.js\";\n\nexport function conicProjection(projectAt) {\n  var phi0 = 0,\n      phi1 = pi / 3,\n      m = projectionMutator(projectAt),\n      p = m(phi0, phi1);\n\n  p.parallels = function(_) {\n    return arguments.length ? m(phi0 = _[0] * radians, phi1 = _[1] * radians) : [phi0 * degrees, phi1 * degrees];\n  };\n\n  return p;\n}\n", "import {epsilon} from \"../math.js\";\nimport albers from \"./albers.js\";\nimport conicEqualArea from \"./conicEqualArea.js\";\nimport {fitExtent, fitSize, fitWidth, fitHeight} from \"./fit.js\";\n\n// The projections must have mutually exclusive clip regions on the sphere,\n// as this will avoid emitting interleaving lines and polygons.\nfunction multiplex(streams) {\n  var n = streams.length;\n  return {\n    point: function(x, y) { var i = -1; while (++i < n) streams[i].point(x, y); },\n    sphere: function() { var i = -1; while (++i < n) streams[i].sphere(); },\n    lineStart: function() { var i = -1; while (++i < n) streams[i].lineStart(); },\n    lineEnd: function() { var i = -1; while (++i < n) streams[i].lineEnd(); },\n    polygonStart: function() { var i = -1; while (++i < n) streams[i].polygonStart(); },\n    polygonEnd: function() { var i = -1; while (++i < n) streams[i].polygonEnd(); }\n  };\n}\n\n// A composite projection for the United States, configured by default for\n// 960×500. The projection also works quite well at 960×600 if you change the\n// scale to 1285 and adjust the translate accordingly. The set of standard\n// parallels for each region comes from USGS, which is published here:\n// http://egsc.usgs.gov/isb/pubs/MapProjections/projections.html#albers\nexport default function() {\n  var cache,\n      cacheStream,\n      lower48 = albers(), lower48Point,\n      alaska = conicEqualArea().rotate([154, 0]).center([-2, 58.5]).parallels([55, 65]), alaskaPoint, // EPSG:3338\n      hawaii = conicEqualArea().rotate([157, 0]).center([-3, 19.9]).parallels([8, 18]), hawaiiPoint, // ESRI:102007\n      point, pointStream = {point: function(x, y) { point = [x, y]; }};\n\n  function albersUsa(coordinates) {\n    var x = coordinates[0], y = coordinates[1];\n    return point = null,\n        (lower48Point.point(x, y), point)\n        || (alaskaPoint.point(x, y), point)\n        || (hawaiiPoint.point(x, y), point);\n  }\n\n  albersUsa.invert = function(coordinates) {\n    var k = lower48.scale(),\n        t = lower48.translate(),\n        x = (coordinates[0] - t[0]) / k,\n        y = (coordinates[1] - t[1]) / k;\n    return (y >= 0.120 && y < 0.234 && x >= -0.425 && x < -0.214 ? alaska\n        : y >= 0.166 && y < 0.234 && x >= -0.214 && x < -0.115 ? hawaii\n        : lower48).invert(coordinates);\n  };\n\n  albersUsa.stream = function(stream) {\n    return cache && cacheStream === stream ? cache : cache = multiplex([lower48.stream(cacheStream = stream), alaska.stream(stream), hawaii.stream(stream)]);\n  };\n\n  albersUsa.precision = function(_) {\n    if (!arguments.length) return lower48.precision();\n    lower48.precision(_), alaska.precision(_), hawaii.precision(_);\n    return reset();\n  };\n\n  albersUsa.scale = function(_) {\n    if (!arguments.length) return lower48.scale();\n    lower48.scale(_), alaska.scale(_ * 0.35), hawaii.scale(_);\n    return albersUsa.translate(lower48.translate());\n  };\n\n  albersUsa.translate = function(_) {\n    if (!arguments.length) return lower48.translate();\n    var k = lower48.scale(), x = +_[0], y = +_[1];\n\n    lower48Point = lower48\n        .translate(_)\n        .clipExtent([[x - 0.455 * k, y - 0.238 * k], [x + 0.455 * k, y + 0.238 * k]])\n        .stream(pointStream);\n\n    alaskaPoint = alaska\n        .translate([x - 0.307 * k, y + 0.201 * k])\n        .clipExtent([[x - 0.425 * k + epsilon, y + 0.120 * k + epsilon], [x - 0.214 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    hawaiiPoint = hawaii\n        .translate([x - 0.205 * k, y + 0.212 * k])\n        .clipExtent([[x - 0.214 * k + epsilon, y + 0.166 * k + epsilon], [x - 0.115 * k - epsilon, y + 0.234 * k - epsilon]])\n        .stream(pointStream);\n\n    return reset();\n  };\n\n  albersUsa.fitExtent = function(extent, object) {\n    return fitExtent(albersUsa, extent, object);\n  };\n\n  albersUsa.fitSize = function(size, object) {\n    return fitSize(albersUsa, size, object);\n  };\n\n  albersUsa.fitWidth = function(width, object) {\n    return fitWidth(albersUsa, width, object);\n  };\n\n  albersUsa.fitHeight = function(height, object) {\n    return fitHeight(albersUsa, height, object);\n  };\n\n  function reset() {\n    cache = cacheStream = null;\n    return albersUsa;\n  }\n\n  return albersUsa.scale(1070);\n}\n", "import conicEqualArea from \"./conicEqualArea.js\";\n\nexport default function() {\n  return conicEqualArea()\n      .parallels([29.5, 45.5])\n      .scale(1070)\n      .translate([480, 250])\n      .rotate([96, 0])\n      .center([-0.6, 38.7]);\n}\n", "import {asin, atan2, cos, sin, sqrt} from \"../math.js\";\n\nexport function azimuthalRaw(scale) {\n  return function(x, y) {\n    var cx = cos(x),\n        cy = cos(y),\n        k = scale(cx * cy);\n        if (k === Infinity) return [2, 0];\n    return [\n      k * cy * sin(x),\n      k * sin(y)\n    ];\n  }\n}\n\nexport function azimuthalInvert(angle) {\n  return function(x, y) {\n    var z = sqrt(x * x + y * y),\n        c = angle(z),\n        sc = sin(c),\n        cc = cos(c);\n    return [\n      atan2(x * sc, z * cc),\n      asin(z && y * sc / z)\n    ];\n  }\n}\n", "import projection from \"./index.js\";\nimport {abs, asin, cos, epsilon2, sin, sqrt} from \"../math.js\";\n\nvar A1 = 1.340264,\n    A2 = -0.081106,\n    A3 = 0.000893,\n    A4 = 0.003796,\n    M = sqrt(3) / 2,\n    iterations = 12;\n\nexport function equalEarthRaw(lambda, phi) {\n  var l = asin(M * sin(phi)), l2 = l * l, l6 = l2 * l2 * l2;\n  return [\n    lambda * cos(l) / (M * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2))),\n    l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2))\n  ];\n}\n\nequalEarthRaw.invert = function(x, y) {\n  var l = y, l2 = l * l, l6 = l2 * l2 * l2;\n  for (var i = 0, delta, fy, fpy; i < iterations; ++i) {\n    fy = l * (A1 + A2 * l2 + l6 * (A3 + A4 * l2)) - y;\n    fpy = A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2);\n    l -= delta = fy / fpy, l2 = l * l, l6 = l2 * l2 * l2;\n    if (abs(delta) < epsilon2) break;\n  }\n  return [\n    M * x * (A1 + 3 * A2 * l2 + l6 * (7 * A3 + 9 * A4 * l2)) / cos(l),\n    asin(sin(l) / M)\n  ];\n};\n\nexport default function() {\n  return projection(equalEarthRaw)\n      .scale(177.158);\n}\n", "import projection from \"./index.js\";\nimport {abs, epsilon} from \"../math.js\";\n\nexport function naturalEarth1Raw(lambda, phi) {\n  var phi2 = phi * phi, phi4 = phi2 * phi2;\n  return [\n    lambda * (0.8707 - 0.131979 * phi2 + phi4 * (-0.013791 + phi4 * (0.003971 * phi2 - 0.001529 * phi4))),\n    phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4)))\n  ];\n}\n\nnaturalEarth1Raw.invert = function(x, y) {\n  var phi = y, i = 25, delta;\n  do {\n    var phi2 = phi * phi, phi4 = phi2 * phi2;\n    phi -= delta = (phi * (1.007226 + phi2 * (0.015085 + phi4 * (-0.044475 + 0.028874 * phi2 - 0.005916 * phi4))) - y) /\n        (1.007226 + phi2 * (0.015085 * 3 + phi4 * (-0.044475 * 7 + 0.028874 * 9 * phi2 - 0.005916 * 11 * phi4)));\n  } while (abs(delta) > epsilon && --i > 0);\n  return [\n    x / (0.8707 + (phi2 = phi * phi) * (-0.131979 + phi2 * (-0.013791 + phi2 * phi2 * phi2 * (0.003971 - 0.001529 * phi2)))),\n    phi\n  ];\n};\n\nexport default function() {\n  return projection(naturalEarth1Raw)\n      .scale(175.295);\n}\n", "'use strict';\n\nvar Queue = require('tinyqueue');\n\nif (Queue.default) Queue = Queue.default; // temporary webpack fix\n\nmodule.exports = polylabel;\nmodule.exports.default = polylabel;\n\nfunction polylabel(polygon, precision, debug) {\n    precision = precision || 1.0;\n\n    // find the bounding box of the outer ring\n    var minX, minY, maxX, maxY;\n    for (var i = 0; i < polygon[0].length; i++) {\n        var p = polygon[0][i];\n        if (!i || p[0] < minX) minX = p[0];\n        if (!i || p[1] < minY) minY = p[1];\n        if (!i || p[0] > maxX) maxX = p[0];\n        if (!i || p[1] > maxY) maxY = p[1];\n    }\n\n    var width = maxX - minX;\n    var height = maxY - minY;\n    var cellSize = Math.min(width, height);\n    var h = cellSize / 2;\n\n    if (cellSize === 0) {\n        var degeneratePoleOfInaccessibility = [minX, minY];\n        degeneratePoleOfInaccessibility.distance = 0;\n        return degeneratePoleOfInaccessibility;\n    }\n\n    // a priority queue of cells in order of their \"potential\" (max distance to polygon)\n    var cellQueue = new Queue(undefined, compareMax);\n\n    // cover polygon with initial cells\n    for (var x = minX; x < maxX; x += cellSize) {\n        for (var y = minY; y < maxY; y += cellSize) {\n            cellQueue.push(new Cell(x + h, y + h, h, polygon));\n        }\n    }\n\n    // take centroid as the first best guess\n    var bestCell = getCentroidCell(polygon);\n\n    // special case for rectangular polygons\n    var bboxCell = new Cell(minX + width / 2, minY + height / 2, 0, polygon);\n    if (bboxCell.d > bestCell.d) bestCell = bboxCell;\n\n    var numProbes = cellQueue.length;\n\n    while (cellQueue.length) {\n        // pick the most promising cell from the queue\n        var cell = cellQueue.pop();\n\n        // update the best cell if we found a better one\n        if (cell.d > bestCell.d) {\n            bestCell = cell;\n            if (debug) console.log('found best %d after %d probes', Math.round(1e4 * cell.d) / 1e4, numProbes);\n        }\n\n        // do not drill down further if there's no chance of a better solution\n        if (cell.max - bestCell.d <= precision) continue;\n\n        // split the cell into four cells\n        h = cell.h / 2;\n        cellQueue.push(new Cell(cell.x - h, cell.y - h, h, polygon));\n        cellQueue.push(new Cell(cell.x + h, cell.y - h, h, polygon));\n        cellQueue.push(new Cell(cell.x - h, cell.y + h, h, polygon));\n        cellQueue.push(new Cell(cell.x + h, cell.y + h, h, polygon));\n        numProbes += 4;\n    }\n\n    if (debug) {\n        console.log('num probes: ' + numProbes);\n        console.log('best distance: ' + bestCell.d);\n    }\n\n    var poleOfInaccessibility = [bestCell.x, bestCell.y];\n    poleOfInaccessibility.distance = bestCell.d;\n    return poleOfInaccessibility;\n}\n\nfunction compareMax(a, b) {\n    return b.max - a.max;\n}\n\nfunction Cell(x, y, h, polygon) {\n    this.x = x; // cell center x\n    this.y = y; // cell center y\n    this.h = h; // half the cell size\n    this.d = pointToPolygonDist(x, y, polygon); // distance from cell center to polygon\n    this.max = this.d + this.h * Math.SQRT2; // max distance to polygon within a cell\n}\n\n// signed distance from point to polygon outline (negative if point is outside)\nfunction pointToPolygonDist(x, y, polygon) {\n    var inside = false;\n    var minDistSq = Infinity;\n\n    for (var k = 0; k < polygon.length; k++) {\n        var ring = polygon[k];\n\n        for (var i = 0, len = ring.length, j = len - 1; i < len; j = i++) {\n            var a = ring[i];\n            var b = ring[j];\n\n            if ((a[1] > y !== b[1] > y) &&\n                (x < (b[0] - a[0]) * (y - a[1]) / (b[1] - a[1]) + a[0])) inside = !inside;\n\n            minDistSq = Math.min(minDistSq, getSegDistSq(x, y, a, b));\n        }\n    }\n\n    return minDistSq === 0 ? 0 : (inside ? 1 : -1) * Math.sqrt(minDistSq);\n}\n\n// get polygon centroid\nfunction getCentroidCell(polygon) {\n    var area = 0;\n    var x = 0;\n    var y = 0;\n    var points = polygon[0];\n\n    for (var i = 0, len = points.length, j = len - 1; i < len; j = i++) {\n        var a = points[i];\n        var b = points[j];\n        var f = a[0] * b[1] - b[0] * a[1];\n        x += (a[0] + b[0]) * f;\n        y += (a[1] + b[1]) * f;\n        area += f * 3;\n    }\n    if (area === 0) return new Cell(points[0][0], points[0][1], 0, polygon);\n    return new Cell(x / area, y / area, 0, polygon);\n}\n\n// get squared distance from a point to a segment\nfunction getSegDistSq(px, py, a, b) {\n\n    var x = a[0];\n    var y = a[1];\n    var dx = b[0] - x;\n    var dy = b[1] - y;\n\n    if (dx !== 0 || dy !== 0) {\n\n        var t = ((px - x) * dx + (py - y) * dy) / (dx * dx + dy * dy);\n\n        if (t > 1) {\n            x = b[0];\n            y = b[1];\n\n        } else if (t > 0) {\n            x += dx * t;\n            y += dy * t;\n        }\n    }\n\n    dx = px - x;\n    dy = py - y;\n\n    return dx * dx + dy * dy;\n}\n", "\nexport default class TinyQueue {\n    constructor(data = [], compare = defaultCompare) {\n        this.data = data;\n        this.length = this.data.length;\n        this.compare = compare;\n\n        if (this.length > 0) {\n            for (let i = (this.length >> 1) - 1; i >= 0; i--) this._down(i);\n        }\n    }\n\n    push(item) {\n        this.data.push(item);\n        this.length++;\n        this._up(this.length - 1);\n    }\n\n    pop() {\n        if (this.length === 0) return undefined;\n\n        const top = this.data[0];\n        const bottom = this.data.pop();\n        this.length--;\n\n        if (this.length > 0) {\n            this.data[0] = bottom;\n            this._down(0);\n        }\n\n        return top;\n    }\n\n    peek() {\n        return this.data[0];\n    }\n\n    _up(pos) {\n        const {data, compare} = this;\n        const item = data[pos];\n\n        while (pos > 0) {\n            const parent = (pos - 1) >> 1;\n            const current = data[parent];\n            if (compare(item, current) >= 0) break;\n            data[pos] = current;\n            pos = parent;\n        }\n\n        data[pos] = item;\n    }\n\n    _down(pos) {\n        const {data, compare} = this;\n        const halfLength = this.length >> 1;\n        const item = data[pos];\n\n        while (pos < halfLength) {\n            let left = (pos << 1) + 1;\n            let best = data[left];\n            const right = left + 1;\n\n            if (right < this.length && compare(data[right], best) < 0) {\n                left = right;\n                best = data[right];\n            }\n            if (compare(best, item) >= 0) break;\n\n            data[pos] = best;\n            pos = left;\n        }\n\n        data[pos] = item;\n    }\n}\n\nfunction defaultCompare(a, b) {\n    return a < b ? -1 : a > b ? 1 : 0;\n}\n", "import * as m from \"./../../dist/es2015/map.js\";\nexport const am5map = m;"], "names": ["MapSeries", "Series", "_afterNew", "this", "fields", "push", "_setRawDefault", "on", "geoJSON", "previous", "_prevSettings", "data", "clear", "super", "_handleDirties", "get", "undefined", "_geoJSONparsed", "_parseGeoJSON", "_prepare<PERSON><PERSON><PERSON><PERSON>", "_valuesDirty", "isDirty", "chart", "exclude", "_centerLocation", "id", "dataItem", "getDataItemById", "_excludeDataItem", "length", "_excluded", "_unexcludeDataItem", "include", "dataItems", "indexOf", "_notIncludeDataItem", "_unNotIncludeDataItem", "_notIncluded", "_removeGeometry", "_addGeometry", "checkInclude", "includes", "excludes", "features", "type", "geometry", "console", "log", "geodataNames", "idField", "i", "len", "feature", "properties", "name", "_types", "dataObject", "value", "dataContext", "geometryType", "set", "processDataItem", "madeFromGeoData", "events", "isEnabled", "dispatch", "target", "_placeBulletsContainer", "_chart", "children", "moveValue", "bulletsContainer", "_removeBulletsContainer", "projection", "geoPath", "getPrivate", "series", "_geometries", "markDirtyGeometries", "_dispose", "removeValue", "_onDataClear", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "classNames", "concat", "className", "<PERSON><PERSON>", "constructor", "_partials", "Float64Array", "_n", "add", "x", "p", "j", "y", "hi", "lo", "Math", "abs", "valueOf", "n", "epsilon", "epsilon2", "pi", "PI", "halfPi", "quarterPi", "tau", "degrees", "radians", "atan", "atan2", "cos", "ceil", "exp", "hypot", "floor", "pow", "sin", "sign", "sqrt", "tan", "asin", "haversin", "noop", "streamGeometry", "stream", "streamGeometryType", "hasOwnProperty", "lengthSum", "lambda0", "sinPhi0", "cosPhi0", "streamObjectType", "Feature", "object", "FeatureCollection", "Sphere", "sphere", "Point", "coordinates", "point", "MultiPoint", "LineString", "streamLine", "MultiLineString", "Polygon", "streamPolygon", "MultiPolygon", "GeometryCollection", "geometries", "closed", "coordinate", "lineStart", "lineEnd", "polygonStart", "polygonEnd", "lengthStream", "lengthPointFirst", "lengthLineEnd", "lambda", "phi", "lengthPoint", "sinPhi", "cosPhi", "delta", "cos<PERSON><PERSON><PERSON>", "z", "a", "b", "MapLine", "Graphics", "_beforeChanged", "_projectionDirty", "clipAngle", "precision", "_clear", "segments", "display", "s", "segment", "gp0", "p0", "convert", "longitude", "latitude", "lineTo", "gp", "pn", "_display", "context", "markDirtyProjection", "<PERSON><PERSON><PERSON><PERSON>", "_clearDirty", "_getTooltipPoint", "tooltipX", "tooltipY", "Percent", "geoPoint", "positionToGeoPoint", "position", "lineType", "distanceAB", "pointA", "pointB", "totalDistance", "currentDistance", "positionA", "positionB", "location", "positionAB", "p1", "invert", "x0", "y0", "x1", "y1", "cy0", "sy0", "cy1", "sy1", "kx0", "ky0", "kx1", "ky1", "d", "k", "interpolate", "t", "B", "A", "distance", "MapLineSeries", "List", "Template", "new", "_new", "_root", "mapLines", "template", "makeMapLine", "mapLine", "make", "_setDataItem", "_handlePointsToConnect", "setPrivate", "pointsToConnect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "coords", "setRaw", "disposeDataItem", "dispose", "range", "start", "stop", "step", "arguments", "max", "Array", "graticuleX", "dy", "map", "graticuleY", "dx", "GraticuleSeries", "makeDataItem", "_dataItem", "_generate", "_update<PERSON><PERSON><PERSON>n", "graticule", "X1", "X0", "Y1", "Y0", "X", "Y", "DX", "DY", "lines", "filter", "outline", "slice", "reverse", "extent", "_", "extent<PERSON><PERSON><PERSON>", "extentMinor", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "geoBounds", "left", "bottom", "right", "top", "compose", "rotationIdentity", "round", "rotateRadians", "deltaLambda", "deltaPhi", "deltaGamma", "rotationLambda", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRotationLambda", "rotation", "cosDeltaPhi", "sinDeltaPhi", "cos<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sin<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "m", "rejoin", "pop", "shift", "result", "Intersection", "points", "other", "entry", "o", "e", "v", "compareIntersection", "startInside", "subject", "clip", "for<PERSON>ach", "pointEqual", "sort", "current", "isSubject", "array", "spherical", "cartesian", "cartesianDot", "cartesianCross", "cartesianAddInPlace", "cartesianScale", "vector", "cartesianNormalizeInPlace", "l", "merge", "arrays", "from", "flatten", "pointVisible", "clipLine", "sink", "polygon", "ring", "<PERSON><PERSON><PERSON><PERSON>", "ringSink", "polygonStarted", "pointRing", "ringStart", "ringEnd", "normal", "angle", "winding", "sum", "point0", "phi0", "lambda1", "sinPhi1", "cosPhi1", "point1", "phi1", "absD<PERSON><PERSON>", "antimeridian", "arc", "intersection", "phiArc", "polygonContains", "pointLine", "clean", "ringSegments", "validSegment", "NaN", "sign0", "sign1", "sinLambda0Lambda1", "clipAntimeridianIntersect", "to", "direction", "circleStream", "radius", "t0", "t1", "cosRadius", "sinRadius", "circleRadius", "acos", "clipMax", "clipMin", "transformer", "methods", "TransformStream", "key", "prototype", "Infinity", "boundsStream", "bounds", "fit", "fitBounds", "clipExtent", "scale", "translate", "fitExtent", "w", "h", "min", "fitSize", "size", "fit<PERSON><PERSON><PERSON>", "width", "fitHeight", "height", "max<PERSON><PERSON><PERSON>", "cosMinDistance", "project", "delta2", "resampleLineTo", "a0", "b0", "c0", "a1", "b1", "c1", "depth", "d2", "c", "phi2", "lambda2", "x2", "y2", "dx2", "dy2", "dz", "lambda00", "x00", "y00", "a00", "b00", "c00", "resampleStream", "linePoint", "ringPoint", "resampleNone", "transformRadians", "scaleTranslateRotate", "sx", "sy", "alpha", "transform", "scaleTranslate", "cosAlpha", "sinAlpha", "ai", "bi", "ci", "fi", "projectionMutator", "projectAt", "rotate", "projectResample", "projectTransform", "projectRotateTransform", "cache", "cacheStream", "theta", "preclip", "postclip", "identity", "recenter", "center", "apply", "resample", "reset", "r", "transformRotate", "cr", "smallRadius", "notHemisphere", "visible", "intersect", "two", "n1", "n2", "n2n2", "n1n2", "determinant", "c2", "n1xn2", "u", "uu", "t2", "q", "polar", "q1", "code", "v0", "v00", "point2", "corner", "comparePoint", "ca", "cb", "x__", "y__", "v__", "x_", "y_", "v_", "first", "activeStream", "bufferStream", "clipStream", "polygonInside", "cleanInside", "ax", "ay", "clipRectangle", "reflectX", "reflectY", "mercatorRaw", "reclip", "forward", "mercatorProjection", "MapChartDefaultTheme", "Theme", "setupDefaultRules", "ic", "interfaceColors", "rule", "bind", "setAll", "panX", "panY", "pinchZoom", "zoomStep", "zoomLevel", "rotationX", "rotationY", "rotationZ", "maxZoomLevel", "minZoomLevel", "wheelY", "wheelX", "animationEasing", "wheelEasing", "wheelDuration", "wheelSensitivity", "maxPanOut", "centerMapOnZoomOut", "role", "affectsBounds", "clipFront", "clipBack", "autoScale", "minDistance", "scatterDistance", "scatterRadius", "stopClusterZoom", "isMeasured", "fillOpacity", "strokeWidth", "strokeOpacity", "areaSum", "areaRingSum", "areaStream", "areaRingStart", "areaRingEnd", "area", "areaPointFirst", "areaPoint", "Z0", "Z1", "X2", "Y2", "Z2", "centroidStream", "centroidPoint", "centroidLineStart", "centroidLineEnd", "centroidRingStart", "centroidRingEnd", "centroid", "centroidPointFirstLine", "centroidPointLine", "centroidPointFirstRing", "centroidPointRing", "PathContext", "_context", "_radius", "pointRadius", "_line", "_point", "closePath", "moveTo", "lengthRing", "cacheDigits", "cacheAppend", "cacheRadius", "cacheCircle", "PathString", "digits", "_append", "append", "RangeError", "strings", "appendRound", "W0", "W1", "phi00", "z0", "centroidPointCartesian", "centroidLinePointFirst", "centroidLinePoint", "centroidRingPointFirst", "centroidRingPoint", "cx", "cy", "cz", "deltaSum", "ranges", "areaRing", "dLambda", "sdLambda", "adLambda", "boundsLineStart", "boundsLineEnd", "boundsRingPoint", "boundsRingStart", "boundsRingEnd", "inflection", "phii", "lambdai", "rangeCompare", "rangeContains", "getGeoCircle", "constant", "circle", "getGeoCentroid", "getGeoArea", "getGeoBounds", "merged", "deltaMax", "getGeoRectangle", "north", "east", "south", "west", "multiPolygon", "<PERSON><PERSON><PERSON>", "stepLat", "ln", "surface", "ll", "lt", "normalizeGeoPoint", "wrapAngleTo180", "latitude180", "MapChart", "<PERSON><PERSON><PERSON><PERSON>", "_makeGeoPath", "path", "projectionStream", "contextStream", "measure", "setPrivateRaw", "seriesContainer", "toGlobal", "geoCentroid", "_geoCentroid", "_geoBounds", "_handleSetWheel", "chartContainer", "_wheelDp", "event", "wheelEvent", "originalEvent", "preventDefault", "toLocal", "_handleWheelZoom", "deltaY", "_handleWheelRotateY", "_handleWheelRotateX", "deltaX", "_disposers", "innerWidth", "innerHeight", "previousGeometries", "_geometryColection", "_fitMap", "prev", "hw", "hh", "centerLocation", "xy", "xx", "yy", "_dirtyGeometries", "each", "_pw", "_ph", "newScale", "_centerX", "_centerY", "_mapBounds", "zoomControl", "_disposeProperty", "isType", "bullets", "bullet", "sprite", "root", "once", "minusButton", "plusButton", "_setUpTouch", "prevGeoBounds", "_prevGeoBounds", "_dispatchBounds", "_mapFitted", "homeGeoPoint", "goHome", "duration", "zoomToGeoPoint", "_afterChanged", "cancelTouch", "_defaultThemes", "_settings", "themeTags", "Rectangle", "fill", "Color", "fromHex", "_handleChartDown", "_handleChartUp", "_handleChartMove", "license", "licenses", "match", "_licenseApplied", "_showBranding", "_downZoomLevel", "downPoints", "_downPoints", "count", "downPoint", "_downTranslateX", "_downTranslateY", "_downRotationX", "_downRotationY", "_downRotationZ", "downId", "_getDownPointId", "movePoint", "_movePoints", "bg", "enableType", "_za", "_txa", "_tya", "_rxa", "_rya", "l0", "l1", "_pLon", "_pLat", "_event", "_handlePinch", "movePoints", "downPoint0", "downPoint1", "movePoint0", "movePoint1", "initialDistance", "level", "moveCenter", "downCenter", "tx", "ty", "_getDownPoint", "downPointId", "pointerId", "local", "disableType", "ww", "downLocation", "easing", "animate", "newZoomLevel", "zoomToPoint", "zoomToGeoBounds", "mapBounds", "zl", "reader<PERSON><PERSON><PERSON>", "_t", "locale", "zoomIn", "zoomOut", "getArea", "MapPointSeries", "_positionBullets", "_makeBullets", "bulletFunction", "_setBulletParent", "_makeBullet", "parent", "_positionBullet", "lineDataItem", "fixed", "lineId", "lineDI", "_lineChangedDp", "polygonDataItem", "polygonId", "polygonDI", "positionOnLine", "visualCentroid", "geoPoint0", "geoPoint1", "_positionBulletReal", "index", "_index", "zoomToDataItem", "zoomToDataItems", "di", "MapPolygon", "isHover", "showTooltip", "biggestArea", "MapPolygonSeries", "mapPolygons", "makeMapPolygon", "mapPolygon", "rx", "ry", "getPolygonByPoint", "found", "displayObject", "_renderer", "getObjectAtPoint", "getPolygonByGeoPoint", "ClusteredPointSeries", "_spiral", "_previousZL", "clusterDelay", "_clusterDP", "setTimeout", "_doTheCluster", "clusteredDataItems", "groups", "groupId", "_scatterIndex", "_scatters", "_clusterIndex", "_clusters", "_key", "group", "_scatterGroup", "_clusterGroup", "zoomToCluster", "cluster", "_clusterDataItem", "bulletMethod", "sumX", "sumY", "clusteredDataItem", "Container", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "child", "Component", "averageX", "averageY", "prevLen", "Label", "text", "markDirtyText", "diPoint", "scatter", "_scatterDataItem", "previousCircles", "spiralPoint", "intersects", "previousCircle", "ZoomControl", "ZoomTools", "addTag", "isPrivateDirty", "orthographicRaw", "equirectangularRaw", "conicEqualAreaRaw", "cylindricalEqualAreaRaw", "r0", "r0y", "parallels", "conicProjection", "lower48Point", "alaskaPoint", "hawaiiPoint", "lower48", "conicEqualArea", "alaska", "hawaii", "pointStream", "albersUsa", "streams", "sc", "cc", "azimuthalInvert", "A1", "A2", "A3", "A4", "M", "equalEarthRaw", "l2", "l6", "naturalEarth1Raw", "phi4", "Queue", "polylabel", "debug", "minX", "minY", "maxX", "maxY", "cellSize", "degeneratePoleOfInaccessibility", "cellQueue", "compareMax", "Cell", "bestCell", "f", "getCentroidCell", "bboxCell", "numProbes", "cell", "poleOfInaccessibility", "inside", "minDistSq", "getSegDistSq", "pointToPolygonDist", "SQRT2", "px", "py", "default", "module", "exports", "TinyQueue", "compare", "defaultCompare", "_down", "item", "_up", "peek", "pos", "<PERSON><PERSON><PERSON><PERSON>", "best", "am5map"], "sourceRoot": ""}