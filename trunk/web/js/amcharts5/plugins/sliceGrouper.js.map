{"version": 3, "file": "plugins/sliceGrouper.js", "mappings": "+LAyEO,MAAMA,UAAqBC,EAAA,GAAlC,c,oBAWC,4C,wDAsND,CAnNW,SAAAC,GACTC,MAAMD,YACNE,KAAKC,eAAe,YAAa,GACjCD,KAAKC,eAAe,YAAa,SACjCD,KAAKC,eAAe,gBAAiB,QACrCD,KAAKE,iBACLF,KAAKG,MAAMC,YAAYJ,MAEvB,MAAMK,EAASL,KAAKM,IAAI,UACxB,GAAID,EAAQ,CACX,MAAME,EAASF,EAAOC,IAAI,UACtBC,IACHP,KAAKQ,WAAW,cAAeD,EAAOE,WAAW,gBACjDT,KAAKQ,WAAW,cAAeD,EAAOE,WAAW,gB,CAGpD,CAEQ,cAAAP,GAEP,GAAsB,SADAF,KAAKM,IAAI,iBACD,CAC7B,MAAMI,EAAYV,KAAKW,KAAKC,iBAC5BZ,KAAKa,cAAgBH,EAAUI,SAASC,KAAKC,EAAA,EAAOC,IAAIjB,KAAKG,MAAO,CACnEe,UAAW,CAAC,QACZC,KAAMC,EAAA,EAASH,IAAIjB,KAAKG,MAAO,CAC9Be,UAAW,CAAC,SAAU,aAGxBlB,KAAKa,cAAcQ,OACnBrB,KAAKa,cAAcS,OAAOC,GAAG,SAAS,KACrCvB,KAAKwB,SAAS,G,CAIjB,CAEQ,UAAAC,GACP,MAAMpB,EAASL,KAAKM,IAAI,UAExB,GAAID,EAAQ,CAGX,IAAIqB,EAAgB1B,KAAKS,WAAW,iBACpC,IAAKiB,EAAe,CAEnB,MAAMC,EAAS3B,KAAKM,IAAI,UAClBsB,EAAgBvB,EAAOC,IAAI,gBAAiB,YAC5CuB,EAAaxB,EAAOC,IAAI,aAAc,SAGtCwB,EAAsB,CAAC,EAC7BA,EAAeF,GAAiB5B,KAAKM,IAAI,YAAa,IACtDwB,EAAeD,GAAc,EAE7B,MAAMtB,EAASF,EAAOC,IAAI,UACtBC,IACHA,EAAOC,WAAW,cAAeR,KAAKS,WAAW,gBACjDF,EAAOC,WAAW,cAAeR,KAAKS,WAAW,iBAElDJ,EAAO0B,KAAKhB,KAAKe,GAEjBJ,EAAgBrB,EAAO2B,UAAU3B,EAAO2B,UAAUC,OAAS,GAE3DP,EAAcpB,IAAI,SAASgB,OAAOC,GAAG,SAAS,KAC7CvB,KAAKkC,aAAa,IAGnBlC,KAAKQ,WAAW,gBAAiBkB,GAG7BC,IACHA,EAAOI,KAAKhB,KAAKW,GAGjBA,EAAcH,GAAG,WAAYY,IACxBA,GACHnC,KAAKwB,S,KAQT,MAAMY,EAAYpC,KAAKM,IAAI,YAAa,GAClC+B,EAAQrC,KAAKM,IAAI,QAAS,KAC1BgC,EAAuB,GACvBC,EAAsB,GAC5B,IAAIC,EAAa,GACbJ,GAAaC,KAEhB,OAAYhC,EAAO2B,WAAW,CAACS,EAAMC,KACpC,MAAMC,EAAiBF,EAAKnC,IAAI,mBAC1BmC,EAAKnC,IAAI,sBAAwB8B,GAAeM,EAASL,EAAQ,IAAQX,IAAkBe,GAChGD,GAAcC,EAAKnC,IAAI,QAAS,GAChCiC,EAAexB,KAAK0B,GACpBA,EAAKpB,KAAK,GACNsB,GACHA,EAAerC,IAAI,iBAAiBe,KAAK,KAI1CiB,EAAgBvB,KAAK0B,GACjBE,GACHA,EAAerC,IAAI,iBAAiBsC,KAAK,G,IAK5C5C,KAAKQ,WAAW,kBAAmB8B,GACnCtC,KAAKQ,WAAW,iBAAkB+B,GAClCvC,KAAK6C,oBAAoBL,G,CAK5B,CAKO,OAAAhB,GACN,MAAME,EAAgB1B,KAAKS,WAAW,iBAMtC,GALIiB,GACHA,EAAckB,OAIM,QADC5C,KAAKM,IAAI,iBACF,CAC5B,MAAMgC,EAAuBtC,KAAKS,WAAW,kBAAmB,IAChE,OAAY6B,GAAiB,CAACG,EAAWK,KACxCL,EAAKG,MAAM,G,CAIb,MAAML,EAAsBvC,KAAKS,WAAW,iBAAkB,IAC9D,OAAY8B,GAAgB,CAACE,EAAWK,KACvCL,EAAKpB,MAAM,IAGRrB,KAAKa,eACRb,KAAKa,cAAcQ,MAErB,CAEQ,mBAAAwB,CAAoBL,GAC3B,MAAMnC,EAASL,KAAKM,IAAI,UACxB,GAAID,EAAQ,CACX,MAAMyB,EAAsB,CAAC,EACvBF,EAAgBvB,EAAOC,IAAI,gBAAiB,YAC5CuB,EAAaxB,EAAOC,IAAI,aAAc,SAC5CwB,EAAeF,GAAiB5B,KAAKM,IAAI,YAAa,IACtDwB,EAAeD,GAAcW,EAC7BnC,EAAO0B,KAAKgB,SAAS1C,EAAO0B,KAAKE,OAAS,EAAGH,GAE7C,MAAMJ,EAAgB1B,KAAKS,WAAW,iBACpB,GAAd+B,EACHd,EAAeL,KAAK,GAEZK,EAAesB,YACvBtB,EAAekB,OAIK,QADC5C,KAAKM,IAAI,kBAE9BoB,EAAepB,IAAI,SAAS2C,IAAI,YAAa,O,CAGhD,CAEQ,WAAAf,GACP,MAAMgB,EAAgBlD,KAAKM,IAAI,iBACzBiC,EAAiBvC,KAAKS,WAAW,kBAEvC,GAAqB,QAAjByC,GAA4BX,GAA2C,GAAzBA,EAAeN,OAChE,OAGD,MAAM5B,EAASL,KAAKM,IAAI,UACFN,KAAKS,WAAW,iBAGvBY,OAGf,OAAYhB,EAAQ2B,WAAYS,KACQ,IAAnCF,EAAgBY,QAAQV,GAC3BA,EAAKG,OAEoB,QAAjBM,GACRT,EAAKpB,M,IAIPrB,KAAKa,cAAe+B,MACrB,CAEO,cAAAQ,GAGN,GAFArD,MAAMqD,iBAEFpD,KAAKqD,QAAQ,UAAW,CAC3B,MAAMhD,EAASL,KAAKM,IAAI,UACpBD,GACHA,EAAOiB,OAAOC,GAAG,iBAAkB+B,IAClCtD,KAAKuD,cAAc,iBACnBvD,KAAKyB,YAAY,G,CAIrB,EA9NA,qC,gDAAkC,iBAClC,sC,gDAA0C5B,EAAA,GAAO2D,WAAWC,OAAO,CAAC7D,EAAa8D,a,oFC1E3E,MAAMC,E", "sources": ["webpack://@amcharts/amcharts5/./src/.internal/plugins/sliceGrouper/SliceGrouper.ts", "webpack://@amcharts/amcharts5/./tmp/webpack/plugins/sliceGrouper.js"], "sourcesContent": ["import type { DataItem } from \"../../core/render/Component\";\nimport type { PercentSeries, IPercentSeriesDataItem } from \"../../charts/percent/PercentSeries\";\nimport type { Legend } from \"../../core/render/Legend\";\n\nimport { Button } from \"../../core/render/Button\";\nimport { Graphics } from \"../../core/render/Graphics\";\nimport { Entity, IEntitySettings, IEntityPrivate, IEntityEvents } from \"../../core/util/Entity\"\n\nimport * as $array from \"../../core/util/Array\";\n\n\nexport interface ISliceGrouperSettings extends IEntitySettings {\n\n\t/**\n\t * A series that will be used to group slices on.\n\t */\n\tseries?: PercentSeries;\n\n\t/**\n\t * If set, plugin will try to manipulate the items in legend, such as\n\t * adding group slice, hiding items for small slices, etc.\n\t */\n\tlegend?: Legend;\n\n\t/**\n\t * Any slice which has percent value less than this setting will be grouped.\n\t * \n\t * @default 5\n\t */\n\tthreshold?: number;\n\n\t/**\n\t * If set, only X first slices will be left as they are. The rest of the\n\t * slices will be grouped.\n\t */\n\tlimit?: number;\n\n\t/**\n\t * Name (category) of the group slice.\n\t *\n\t * @default \"Other\"\n\t */\n\tgroupName?: string;\n\n\t/**\n\t * What happens when group slice is clicked.\n\t *\n\t * * `\"none\"` (default) - nothing.\n\t * * `\"break\"` - underlying small slices are shown.\n\t * * `\"zoom\"` - series shows only small slies (big ones are hidden).\n\t */\n\tclickBehavior?: \"none\" | \"break\" | \"zoom\";\n\n}\n\nexport interface ISliceGrouperPrivate extends IEntityPrivate {\n\tgroupDataItem?: DataItem<IPercentSeriesDataItem>;\n\tnormalDataItems?: DataItem<IPercentSeriesDataItem>[];\n\tsmallDataItems?: DataItem<IPercentSeriesDataItem>[];\n\tcurrentStep?: number;\n\tcurrentPass?: number;\n}\n\nexport interface ISliceGrouperEvents extends IEntityEvents {\n}\n\n\n/**\n * A plugin that can be used to automatically group small slices on percent\n * charts into a single slice.\n *\n * @see {@link https://www.amcharts.com/docs/v5/charts/percent-charts/grouping-slices/} for more info\n */\nexport class SliceGrouper extends Entity {\n\tpublic static className: string = \"SliceGrouper\";\n\tpublic static classNames: Array<string> = Entity.classNames.concat([SliceGrouper.className]);\n\n\tdeclare public _settings: ISliceGrouperSettings;\n\tdeclare public _privateSettings: ISliceGrouperPrivate;\n\tdeclare public _events: ISliceGrouperEvents;\n\n\t/**\n\t * A button that is shown when chart small buttons are visible.\n\t */\n\tpublic zoomOutButton?: Button;\n\n\n\tprotected _afterNew(): void {\n\t\tsuper._afterNew();\n\t\tthis._setRawDefault(\"threshold\", 5);\n\t\tthis._setRawDefault(\"groupName\", \"Other\");\n\t\tthis._setRawDefault(\"clickBehavior\", \"none\");\n\t\tthis.initZoomButton();\n\t\tthis._root.addDisposer(this);\n\n\t\tconst series = this.get(\"series\");\n\t\tif (series) {\n\t\t\tconst colors = series.get(\"colors\");\n\t\t\tif (colors) {\n\t\t\t\tthis.setPrivate(\"currentStep\", colors.getPrivate(\"currentStep\"));\n\t\t\t\tthis.setPrivate(\"currentPass\", colors.getPrivate(\"currentPass\"));\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate initZoomButton(): void {\n\t\tconst clickBehavior = this.get(\"clickBehavior\");\n\t\tif (clickBehavior !== \"none\") {\n\t\t\tconst container = this.root.tooltipContainer;\n\t\t\tthis.zoomOutButton = container.children.push(Button.new(this._root, {\n\t\t\t\tthemeTags: [\"zoom\"],\n\t\t\t\ticon: Graphics.new(this._root, {\n\t\t\t\t\tthemeTags: [\"button\", \"icon\"]\n\t\t\t\t})\n\t\t\t}));\n\t\t\tthis.zoomOutButton.hide();\n\t\t\tthis.zoomOutButton.events.on(\"click\", () => {\n\t\t\t\tthis.zoomOut();\n\t\t\t});\n\t\t}\n\n\t}\n\n\tprivate handleData(): void {\n\t\tconst series = this.get(\"series\");\n\n\t\tif (series) {\n\n\t\t\t// Create group data item if not yet available\n\t\t\tlet groupDataItem = this.getPrivate(\"groupDataItem\");\n\t\t\tif (!groupDataItem) {\n\n\t\t\t\tconst legend = this.get(\"legend\");\n\t\t\t\tconst categoryField = series.get(\"categoryField\", \"category\");\n\t\t\t\tconst valueField = series.get(\"valueField\", \"value\");\n\n\t\t\t\t// Add slice\n\t\t\t\tconst groupSliceData: any = {};\n\t\t\t\tgroupSliceData[categoryField] = this.get(\"groupName\", \"\");\n\t\t\t\tgroupSliceData[valueField] = 0;\n\n\t\t\t\tconst colors = series.get(\"colors\");\n\t\t\t\tif (colors) {\n\t\t\t\t\tcolors.setPrivate(\"currentStep\", this.getPrivate(\"currentStep\"));\n\t\t\t\t\tcolors.setPrivate(\"currentPass\", this.getPrivate(\"currentPass\"));\n\t\t\t\t}\n\t\t\t\tseries.data.push(groupSliceData);\n\n\t\t\t\tgroupDataItem = series.dataItems[series.dataItems.length - 1];\n\n\t\t\t\tgroupDataItem.get(\"slice\").events.on(\"click\", () => {\n\t\t\t\t\tthis.handleClick();\n\t\t\t\t});\n\n\t\t\t\tthis.setPrivate(\"groupDataItem\", groupDataItem);\n\n\t\t\t\t// Add to legend\n\t\t\t\tif (legend) {\n\t\t\t\t\tlegend.data.push(groupDataItem);\n\n\t\t\t\t\t//const legendDataItem = groupDataItem.get(\"legendDataItem\");\n\t\t\t\t\tgroupDataItem.on(\"visible\", (visible) => {\n\t\t\t\t\t\tif (visible) {\n\t\t\t\t\t\t\tthis.zoomOut();\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\t// Recalculate group value and decorate small slices as necessary\n\t\t\tconst threshold = this.get(\"threshold\", 0);\n\t\t\tconst limit = this.get(\"limit\", 1000);\n\t\t\tconst normalDataItems: any = [];\n\t\t\tconst smallDataItems: any = [];\n\t\t\tlet groupValue = 0;\n\t\t\tif (threshold || limit) {\n\n\t\t\t\t$array.each(series.dataItems, (item, index) => {\n\t\t\t\t\tconst legendDataItem = item.get(\"legendDataItem\");\n\t\t\t\t\tif (((item.get(\"valuePercentTotal\") <= threshold) || (index > (limit - 1))) && groupDataItem !== item) {\n\t\t\t\t\t\tgroupValue += item.get(\"value\", 0);\n\t\t\t\t\t\tsmallDataItems.push(item);\n\t\t\t\t\t\titem.hide(0);\n\t\t\t\t\t\tif (legendDataItem) {\n\t\t\t\t\t\t\tlegendDataItem.get(\"itemContainer\").hide(0);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\telse {\n\t\t\t\t\t\tnormalDataItems.push(item);\n\t\t\t\t\t\tif (legendDataItem) {\n\t\t\t\t\t\t\tlegendDataItem.get(\"itemContainer\").show(0);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\n\t\t\t\tthis.setPrivate(\"normalDataItems\", normalDataItems);\n\t\t\t\tthis.setPrivate(\"smallDataItems\", smallDataItems);\n\t\t\t\tthis.updateGroupDataItem(groupValue);\n\n\t\t\t}\n\n\t\t}\n\t}\n\n\t/**\n\t * Resets slice setup to original grouping state.\n\t */\n\tpublic zoomOut(): void {\n\t\tconst groupDataItem = this.getPrivate(\"groupDataItem\");\n\t\tif (groupDataItem) {\n\t\t\tgroupDataItem.show();\n\t\t}\n\n\t\tconst clickBehavior = this.get(\"clickBehavior\");\n\t\tif (clickBehavior == \"zoom\") {\n\t\t\tconst normalDataItems: any = this.getPrivate(\"normalDataItems\", []);\n\t\t\t$array.each(normalDataItems, (item: any, _index) => {\n\t\t\t\titem.show();\n\t\t\t});\n\t\t}\n\n\t\tconst smallDataItems: any = this.getPrivate(\"smallDataItems\", []);\n\t\t$array.each(smallDataItems, (item: any, _index) => {\n\t\t\titem.hide();\n\t\t});\n\n\t\tif (this.zoomOutButton) {\n\t\t\tthis.zoomOutButton.hide();\n\t\t}\n\t}\n\n\tprivate updateGroupDataItem(groupValue: number): void {\n\t\tconst series = this.get(\"series\");\n\t\tif (series) {\n\t\t\tconst groupSliceData: any = {};\n\t\t\tconst categoryField = series.get(\"categoryField\", \"category\");\n\t\t\tconst valueField = series.get(\"valueField\", \"value\");\n\t\t\tgroupSliceData[categoryField] = this.get(\"groupName\", \"\");\n\t\t\tgroupSliceData[valueField] = groupValue;\n\t\t\tseries.data.setIndex(series.data.length - 1, groupSliceData);\n\n\t\t\tconst groupDataItem = this.getPrivate(\"groupDataItem\");\n\t\t\tif (groupValue == 0) {\n\t\t\t\tgroupDataItem!.hide(0);\n\t\t\t}\n\t\t\telse if (groupDataItem!.isHidden()) {\n\t\t\t\tgroupDataItem!.show();\n\t\t\t}\n\n\t\t\tconst clickBehavior = this.get(\"clickBehavior\");\n\t\t\tif (clickBehavior != \"none\") {\n\t\t\t\tgroupDataItem!.get(\"slice\").set(\"toggleKey\", \"none\");\n\t\t\t}\n\t\t}\n\t}\n\n\tprivate handleClick(): void {\n\t\tconst clickBehavior = this.get(\"clickBehavior\");\n\t\tconst smallDataItems = this.getPrivate(\"smallDataItems\");\n\n\t\tif (clickBehavior == \"none\" || (smallDataItems && smallDataItems.length == 0)) {\n\t\t\treturn;\n\t\t}\n\n\t\tconst series = this.get(\"series\");\n\t\tconst groupDataItem = this.getPrivate(\"groupDataItem\");\n\n\t\t// Hide group slice\n\t\tgroupDataItem!.hide();\n\n\t\t// Reveal small slices\n\t\t$array.each(series!.dataItems, (item) => {\n\t\t\tif (smallDataItems!.indexOf(item) !== -1) {\n\t\t\t\titem.show();\n\t\t\t}\n\t\t\telse if (clickBehavior == \"zoom\") {\n\t\t\t\titem.hide();\n\t\t\t}\n\t\t});\n\n\t\tthis.zoomOutButton!.show();\n\t}\n\n\tpublic _beforeChanged() {\n\t\tsuper._beforeChanged();\n\n\t\tif (this.isDirty(\"series\")) {\n\t\t\tconst series = this.get(\"series\");\n\t\t\tif (series) {\n\t\t\t\tseries.events.on(\"datavalidated\", (_ev) => {\n\t\t\t\t\tthis.removePrivate(\"groupDataItem\");\n\t\t\t\t\tthis.handleData();\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\t}\n\n}", "import * as m from \"./../../../dist/es2015/plugins/sliceGrouper.js\";\nexport const am5plugins_sliceGrouper = m;"], "names": ["SliceGrouper", "Entity", "_afterNew", "super", "this", "_setRawDefault", "initZoomButton", "_root", "addDisposer", "series", "get", "colors", "setPrivate", "getPrivate", "container", "root", "tooltipContainer", "zoomOutButton", "children", "push", "<PERSON><PERSON>", "new", "themeTags", "icon", "Graphics", "hide", "events", "on", "zoomOut", "handleData", "groupDataItem", "legend", "categoryField", "valueField", "groupSliceData", "data", "dataItems", "length", "handleClick", "visible", "threshold", "limit", "normalDataItems", "smallDataItems", "groupValue", "item", "index", "legendDataItem", "show", "updateGroupDataItem", "_index", "setIndex", "isHidden", "set", "click<PERSON><PERSON><PERSON><PERSON>", "indexOf", "_beforeChanged", "isDirty", "_ev", "removePrivate", "classNames", "concat", "className", "am5plugins_sliceGrouper"], "sourceRoot": ""}