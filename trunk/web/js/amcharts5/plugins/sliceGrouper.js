"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[476],{1790:function(t,e,s){s.r(e),s.d(e,{SliceGrouper:function(){return n}});var i=s(8054),a=s(1479),o=s(6331),r=s(5071);class n extends o.JH{constructor(){super(...arguments),Object.defineProperty(this,"zoomOutButton",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}_afterNew(){super._afterNew(),this._setRawDefault("threshold",5),this._setRawDefault("groupName","Other"),this._setRawDefault("clickBehavior","none"),this.initZoomButton(),this._root.addDisposer(this);const t=this.get("series");if(t){const e=t.get("colors");e&&(this.setPrivate("currentStep",e.getPrivate("currentStep")),this.setPrivate("currentPass",e.getPrivate("currentPass")))}}initZoomButton(){if("none"!==this.get("clickBehavior")){const t=this.root.tooltipContainer;this.zoomOutButton=t.children.push(i.z.new(this._root,{themeTags:["zoom"],icon:a.T.new(this._root,{themeTags:["button","icon"]})})),this.zoomOutButton.hide(),this.zoomOutButton.events.on("click",(()=>{this.zoomOut()}))}}handleData(){const t=this.get("series");if(t){let e=this.getPrivate("groupDataItem");if(!e){const s=this.get("legend"),i=t.get("categoryField","category"),a=t.get("valueField","value"),o={};o[i]=this.get("groupName",""),o[a]=0;const r=t.get("colors");r&&(r.setPrivate("currentStep",this.getPrivate("currentStep")),r.setPrivate("currentPass",this.getPrivate("currentPass"))),t.data.push(o),e=t.dataItems[t.dataItems.length-1],e.get("slice").events.on("click",(()=>{this.handleClick()})),this.setPrivate("groupDataItem",e),s&&(s.data.push(e),e.on("visible",(t=>{t&&this.zoomOut()})))}const s=this.get("threshold",0),i=this.get("limit",1e3),a=[],o=[];let n=0;(s||i)&&(r.each(t.dataItems,((t,r)=>{const h=t.get("legendDataItem");(t.get("valuePercentTotal")<=s||r>i-1)&&e!==t?(n+=t.get("value",0),o.push(t),t.hide(0),h&&h.get("itemContainer").hide(0)):(a.push(t),h&&h.get("itemContainer").show(0))})),this.setPrivate("normalDataItems",a),this.setPrivate("smallDataItems",o),this.updateGroupDataItem(n))}}zoomOut(){const t=this.getPrivate("groupDataItem");if(t&&t.show(),"zoom"==this.get("clickBehavior")){const t=this.getPrivate("normalDataItems",[]);r.each(t,((t,e)=>{t.show()}))}const e=this.getPrivate("smallDataItems",[]);r.each(e,((t,e)=>{t.hide()})),this.zoomOutButton&&this.zoomOutButton.hide()}updateGroupDataItem(t){const e=this.get("series");if(e){const s={},i=e.get("categoryField","category"),a=e.get("valueField","value");s[i]=this.get("groupName",""),s[a]=t,e.data.setIndex(e.data.length-1,s);const o=this.getPrivate("groupDataItem");0==t?o.hide(0):o.isHidden()&&o.show(),"none"!=this.get("clickBehavior")&&o.get("slice").set("toggleKey","none")}}handleClick(){const t=this.get("clickBehavior"),e=this.getPrivate("smallDataItems");if("none"==t||e&&0==e.length)return;const s=this.get("series");this.getPrivate("groupDataItem").hide(),r.each(s.dataItems,(s=>{-1!==e.indexOf(s)?s.show():"zoom"==t&&s.hide()})),this.zoomOutButton.show()}_beforeChanged(){if(super._beforeChanged(),this.isDirty("series")){const t=this.get("series");t&&t.events.on("datavalidated",(t=>{this.removePrivate("groupDataItem"),this.handleData()}))}}}Object.defineProperty(n,"className",{enumerable:!0,configurable:!0,writable:!0,value:"SliceGrouper"}),Object.defineProperty(n,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:o.JH.classNames.concat([n.className])})},9523:function(t,e,s){s.r(e),s.d(e,{am5plugins_sliceGrouper:function(){return i}});const i=s(1790)}},function(t){var e=(9523,t(t.s=9523)),s=window;for(var i in e)s[i]=e[i];e.__esModule&&Object.defineProperty(s,"__esModule",{value:!0})}]);
//# sourceMappingURL=sliceGrouper.js.map