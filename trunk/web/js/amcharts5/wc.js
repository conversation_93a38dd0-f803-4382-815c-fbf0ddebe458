"use strict";(self.webpackChunk_am5=self.webpackChunk_am5||[]).push([[5547],{6769:function(e,t,s){s.r(t),s.d(t,{DefaultTheme:function(){return n},WordCloud:function(){return p}});var i=s(3409),a=s(6245),o=s(7142),r=s(9395);class n extends i.Q{setupDefaultRules(){super.setupDefaultRules();const e=this._root.interfaceColors,t=this.rule.bind(this);t("WordCloud").setAll({width:a.AQ,height:a.AQ,minFontSize:(0,a.aQ)(2),maxFontSize:(0,a.aQ)(15),excludeWords:[],angles:[0,-90],minWordLength:1,step:15,randomness:0,autoFit:!0,animationEasing:r.out(r.cubic)});{const s=t("Label",["wordcloud"]);s.setAll({text:"{category}",centerX:a.CI,centerY:a.CI,position:"absolute",lineHeight:a.AQ,populateText:!0}),s.setup=t=>{t.set("background",o.A.new(this._root,{fill:e.get("background"),fillOpacity:0}))}}}}var l=s(3399),h=s(5769),u=s(962),d=s(8777),g=s(7144),c=s(1112),f=s(7652),b=s(5071),m=s(751),_=s(5040);class p extends l.F{constructor(){super(...arguments),Object.defineProperty(this,"_currentIndex",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"_timeoutDP",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_ghostContainer",{enumerable:!0,configurable:!0,writable:!0,value:this.children.push(d.W.new(this._root,{layer:99,opacity:.01}))}),Object.defineProperty(this,"_pointSets",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_sets",{enumerable:!0,configurable:!0,writable:!0,value:3}),Object.defineProperty(this,"_process",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"_buffer",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"_boundsToAdd",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"labels",{enumerable:!0,configurable:!0,writable:!0,value:this._makeLabels()})}_afterNew(){this._defaultThemes.push(n.new(this._root)),this.fields.push("category","fill"),this._setDefault("valueField","value"),this._setDefault("categoryField","category"),this._setDefault("fillField","fill"),super._afterNew(),this._root.events.on("frameended",(()=>{this.set("progress",this._currentIndex/this.dataItems.length)}))}makeLabel(e){const t=this.children.push(this.labels.make());t._setDataItem(e);const s=e.get("fill");null!=s&&t.set("fill",s),t.set("x",-999999),e.set("label",t),this.labels.push(t);const i=this._ghostContainer.children.push(this.labels.make());return i._setDataItem(e),i.setAll({fill:c.Il.fromHex(0),fontWeight:"900"}),e.set("ghostLabel",i),this.labels.push(i),t}_makeLabels(){return new g.o(h.YS.new({}),(()=>u._._new(this._root,{themeTags:f.mergeTags(this.labels.template.get("themeTags",[]),["wordcloud","series"])},[this.labels.template])))}processDataItem(e){if(super.processDataItem(e),null==e.get("fill")){let t=this.get("colors");t&&e.setRaw("fill",t.next())}this.makeLabel(e)}_prepareChildren(){super._prepareChildren(),this.isDirty("text")&&(this.data.setAll(this._getWords(this.get("text"))),this._dirty.text=!1)}_updateChildren(){super._updateChildren();const e=this._root._renderer.resolution,t=Math.round(this._root.width()*e);let s=2*this.get("step",1);if(this._valuesDirty||this._sizeDirty||this.isPrivateDirty("adjustedFontSize")){const t=this.getPrivate("adjustedFontSize",1),i=this.innerWidth(),a=this.innerHeight(),o=Math.min(i,a),r=Math.max(i,a);this._buffer=Array(Math.ceil(this._root.width()*this._root.height()*e*e)).fill(0),o<800&&(s/=2),this._ghostContainer._display.clear(),this._pointSets=[];for(let e=0;e<this._sets;e++){const t=s*(this._sets-e),o=m.spiralPoints(i/2,a/2,i,a,0,t*a/r,t*i/r,0,0);for(let e=o.length-1;e>=0;e--){let t=o[e];(t.x<0||t.x>i||t.y<0||t.y>a)&&o.splice(e,1)}this._pointSets.push(o)}let n=0,l=0,h=0,u=1/0,d=0;b.each(this._dataItems,(e=>{const t=e.get("valueWorking",0);n+=t,l+=Math.abs(t)})),this._dataItems.sort(((e,t)=>{let s=e.get("value",0),i=t.get("value",0);return s>i?-1:s<i?1:0})),b.each(this._dataItems,(e=>{const t=e.get("valueWorking",0);t>=l&&(n=e.get("value",0)),t>h&&(h=t),t<u&&(u=t),d++})),this.setPrivateRaw("valueLow",u),this.setPrivateRaw("valueHigh",h),this.setPrivateRaw("valueSum",n),this.setPrivateRaw("valueAverage",n/d),this.setPrivateRaw("valueAbsoluteSum",l);const g=Math.min(i,a),c=f.relativeToValue(this.get("minFontSize",10),g)*t,p=f.relativeToValue(this.get("maxFontSize",100),g)*t,y=this.get("angles",[0]);b.each(this._dataItems,(e=>{const t=e.get("valueWorking",0),s=e.get("ghostLabel");let i=c+(p-c)*(t-u)/(h-u);_.isNaN(i)&&(i=p);const a=this._sets-1-Math.floor((i-c)/(p-c)*(this._sets-1));e.setRaw("set",a),e.setRaw("fontSize",i);let o=y[Math.floor(Math.random()*y.length)];e.setRaw("angle",o),s.setAll({fontSize:i,rotation:o,x:-1e4})})),this._process=!1,this._currentIndex=0,this._root.events.once("frameended",(()=>{this.setTimeout((()=>{this._process=!0,this._markDirtyKey("progress")}),50)}))}const i=this._boundsToAdd;if(i){const e=this._ghostContainer._display.getLayer().context,s=Math.round(i.top),a=Math.round(i.left),o=Math.round(i.right-i.left),r=Math.round(i.bottom-i.top),n=e.getImageData(a,s,o,r).data,l=this._buffer;let h=3;for(let e=s;e<s+r;e++)for(let s=a;s<a+o;s++){let i=(e+1)*t-(t-s);0!=n[h]&&(l[i]=1),h+=4}this._boundsToAdd=void 0}this._process&&this.isDirty("progress")&&this._processItem()}_processItem(){if(this._boundsToAdd=void 0,this._currentIndex<this.dataItems.length){const e=this.dataItems[this._currentIndex],t=e.get("label"),s=e.get("ghostLabel"),i=this._root._renderer.resolution;let a=s.width(),o=s.height();const r=s._display.getLayer().context,n=e.get("set"),l=this._pointSets[n],h=this.innerWidth(),u=this.innerHeight(),d=Math.round(this._root.width()*i),g=this.x(),c=this.y(),f=this.get("angles",[0]);h>u&&a>=h/2&&b.each(f,(t=>{0==t&&0!=e.get("angle")&&(e.setRaw("angle",0),s.set("rotation",0),[a,o]=[o,a])})),u>h&&a>=h/2&&b.each(f,(t=>{90==Math.abs(t)&&0==e.get("angle")&&(e.setRaw("angle",t),s.set("rotation",t),[a,o]=[o,a])}));const m=Math.ceil(a*i),_=Math.ceil(o*i);if(r&&a>0&&o>0){let r=Math.round(Math.random()*l.length*this.get("randomness",0)),n=!0;for(;n;){let f=l[r];if(f){if(n=!1,this._currentIndex>0){let e=Math.round((f.x+g)*i-m/2),t=Math.round((f.y+c)*i-_/2);n=this._hasColor(e,t,m,_,d)}if(f.x-a/2<0||f.x+a/2>h||f.y-o/2<0||f.y+o/2>u)r++,n=!0;else if(n)r+=2;else{const r=e.get("angle",0),n=e.get("fontSize",0);-999999!=t.get("x")?(t.animate({key:"x",to:f.x,duration:this.get("animationDuration",0),easing:this.get("animationEasing")}),t.animate({key:"y",to:f.y,duration:this.get("animationDuration",0),easing:this.get("animationEasing")}),t.animate({key:"rotation",to:r,duration:this.get("animationDuration",0),easing:this.get("animationEasing")}),t.animate({key:"fontSize",to:n,duration:this.get("animationDuration",0),easing:this.get("animationEasing")})):(t.setAll({x:f.x,y:f.y,rotation:r,fontSize:n}),t.appear()),s.setAll({x:f.x,y:f.y});for(let e=l.length-1;e>=0;e--){let t=l[e];t.x>=f.x-a/2&&t.x<=f.x+a/2&&t.y>=f.y-o/2&&t.y<=f.y+o/2&&l.splice(e,1)}this._boundsToAdd={left:(f.x+g-a/2)*i,right:(f.x+g+a/2)*i,top:(f.y+c-o/2)*i,bottom:(f.y+c+o/2)*i}}}else{if(this.get("autoFit"))return void this.setTimeout((()=>{this.setPrivate("adjustedFontSize",.9*this.getPrivate("adjustedFontSize",1))}),50);t.set("x",-999999),n=!1}}}this._currentIndex++}}disposeDataItem(e){super.disposeDataItem(e);const t=e.get("label");t&&(this.labels.removeValue(t),t.dispose());const s=e.get("ghostLabel");s&&(this.labels.removeValue(s),s.dispose())}_getWords(e){let t=[];if(e){const s="A-Za-zªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶ-ͷͺ-ͽΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԣԱ-Ֆՙա-ևא-תװ-ײء-يٮ-ٯٱ-ۓەۥ-ۦۮ-ۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴ-ߵߺऄ-हऽॐक़-ॡॱ-ॲॻ-ॿঅ-ঌএ-ঐও-নপ-রলশ-হঽৎড়-ঢ়য়-ৡৰ-ৱਅ-ਊਏ-ਐਓ-ਨਪ-ਰਲ-ਲ਼ਵ-ਸ਼ਸ-ਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલ-ળવ-હઽૐૠ-ૡଅ-ଌଏ-ଐଓ-ନପ-ରଲ-ଳଵ-ହଽଡ଼-ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கங-சஜஞ-டண-தந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-ళవ-హఽౘ-ౙౠ-ౡಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠ-ೡഅ-ഌഎ-ഐഒ-നപ-ഹഽൠ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะา-ำเ-ๆກ-ຂຄງ-ຈຊຍດ-ທນ-ຟມ-ຣລວສ-ຫອ-ະາ-ຳຽເ-ໄໆໜ-ໝༀཀ-ཇཉ-ཬྈ-ྋက-ဪဿၐ-ၕၚ-ၝၡၥ-ၦၮ-ၰၵ-ႁႎႠ-Ⴥა-ჺჼᄀ-ᅙᅟ-ᆢᆨ-ᇹሀ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏼᐁ-ᙬᙯ-ᙶᚁ-ᚚᚠ-ᛪᛮ-ᛰᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡷᢀ-ᢨᢪᤀ-ᤜᥐ-ᥭᥰ-ᥴᦀ-ᦩᧁ-ᧇᨀ-ᨖᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮ-ᮯᰀ-ᰣᱍ-ᱏᱚ-ᱽᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₔℂℇℊ-ℓℕℙ-ℝℤΩℨK-ℭℯ-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-Ɐⱱ-ⱽⲀ-ⳤⴀ-ⴥⴰ-ⵥⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞⸯ々-〇〡-〩〱-〵〸-〼ぁ-ゖゝ-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎㆠ-ㆷㇰ-ㇿ㐀䶵一鿃ꀀ-ꒌꔀ-ꘌꘐ-ꘟꘪ-ꘫꙀ-ꙟꙢ-ꙮꙿ-ꚗꜗ-ꜟꜢ-ꞈꞋ-ꞌꟻ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꤊ-ꤥꤰ-ꥆꨀ-ꨨꩀ-ꩂꩄ-ꩋ가-힣豈-鶴侮-頻並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּ-סּףּ-פּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ0-9@+",i=new RegExp("(["+s+"]+[-"+s+"]*["+s+"]+)|(["+s+"]+)","ig");let a,o=e.match(i);if(!o)return[];for(;a=o.pop(),a;){let e;for(let s=0;s<t.length;s++)if(t[s].category.toLowerCase()==a.toLowerCase()){e=t[s];break}e?(e.value++,this.isCapitalized(a)||(e.category=a)):t.push({category:a,value:1})}let r=this.get("excludeWords");const n=this.get("minValue",1),l=this.get("minWordLength",1);if(n>1||l>1||r&&r.length>0)for(let e=t.length-1;e>=0;e--){let s=t[e],i=s.category;s.value<n&&t.splice(e,1),i.length<l&&t.splice(e,1),r&&-1!==r.indexOf(i)&&t.splice(e,1)}t.sort((function(e,t){return e.value==t.value?0:e.value>t.value?-1:1}));const h=this.get("maxCount",1/0);t.length>h&&(t=t.slice(0,h))}return t}isCapitalized(e){let t=e.toLowerCase();return e[0]!=t[0]&&e.substr(1)==t.substr(1)&&e!=t}_hasColor(e,t,s,i,a){const o=this._buffer;if(o)for(let r=t;r<t+i;r+=4)for(let t=e;t<e+s;t+=4)if(0!=o[(r+1)*a-(a-t)])return!0;return!1}}Object.defineProperty(p,"className",{enumerable:!0,configurable:!0,writable:!0,value:"WordCloud"}),Object.defineProperty(p,"classNames",{enumerable:!0,configurable:!0,writable:!0,value:l.F.classNames.concat([p.className])})},712:function(e,t,s){s.r(t),s.d(t,{am5wc:function(){return i}});const i=s(6769)}},function(e){var t=(712,e(e.s=712)),s=window;for(var i in t)s[i]=t[i];t.__esModule&&Object.defineProperty(s,"__esModule",{value:!0})}]);
//# sourceMappingURL=wc.js.map