var socket;
function initializeSocket() {
    socket = new WebSocket("ws://" + baseUrl + "/messages?userId=" + socketUserId + "&language=" + userLanguage);

    socket.onopen = function () {
        console.log("Connected to the server");
    };

    socket.onmessage = function (event) {
        let messageObject = JSON.parse(event.data);
        console.log("Received from server: " + event.data);

        if (typeof messageObject !== "undefined") {
            if (notEmpty(messageObject.type)) {
                if (notEmpty(messageObject.content)) {
                    let text = "<h6>" + globalMessages.get("server.message") + "</h6>" + messageObject.content;

                    var found = false;
                    let notyQueue = [...Noty.Queues.global.queue];
                    notyQueue.forEach(function (noty) {
                        if (noty.options.text) {
                            if (noty.options.text === text) {
                                found = true;
                            }
                        }
                    });

                    if (!found) {
                        new Noty({
                            text: text,
                            type: messageObject.type.toLowerCase(),
                            layout: "topRight",
                            visibilityControl: true
                        }).show();
                    }
                }
            }
        }
    };

    socket.onclose = function () {
        console.log("Connection closed");
    };
}

function test() {
    for (var i = 0; i < 5; i++) {
        let text = "<h6>AAAAAAAA</h6>";

        var found = false;
        let notyQueue = [...Noty.Queues.global.queue];
        notyQueue.forEach(function (noty) {
            if (noty.options.text) {
                if (noty.options.text === text) {
                    found = true;
                }
            }
        });

        if (!found) {
            new Noty({
                text: text,
                type: "success",
                layout: "topRight",
                visibilityControl: true
            }).show();
        }
    }
}

function sendMessage(text) {
    var message = text;
    socket.send(message); // Send the message to the server
}