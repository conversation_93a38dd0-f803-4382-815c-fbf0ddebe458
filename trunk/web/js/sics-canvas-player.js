var svgFasce, pitch, scale, scaleSvg, multiplier;
var tip;
var currentContainerId;
var raggio = 15.0;
var basePitchWidth = 27.2, basePitchLength = 42;
var basePitchFasceWidth = (157.5 / 1.9), basePitchFasceLength = (102 / 1.9);
var realWidth = 68, realLength = 105;
var minDistance;

var posizionaleOptions = {
    startPos: "in", // in = le azioni INIZIANO nel quadrante selezionato, out = le azioni FINISCONO nel quadrante selezionato
    orientation: "half"     // vertical, horizontal, grid
};

var lineColors = {
    black: "#2a2d2a",
    blue: "#3672ff",
    red: "#ff4545"
};

var clickColors = {
    base: "#adc274",
    from: "#0040ff44",
    to: "#00ddff44",
    fromTo: "#ea00ff44",
    zoneClickable: "#ffffff00",

    fromAngle: "#5e6fa2",
    toAngle: "#5e99a2",
    fromToAngle: "#9c5ea2"
};

var colors = {
    orange: "#f27e46cc"
};

pitch = {
    width: basePitchWidth,
    length: basePitchLength,
    padding: {
        top: 0.5,
        right: 0.5,
        bottom: 0.5,
        left: 0.5
    },
    paintColor: "#FFF",
    grassColor: "#40b36d",
    isDefault: true,
    id: "basePitch",
    pointsDrawed: 0,
    heatmapPoints: []
};

pitchFasce = {...pitch};
pitchFasce.width = basePitchFasceWidth;
pitchFasce.length = basePitchFasceLength;
pitchFasce.padding = {top: 1, right: 1, bottom: 1, left: 1};
pitchFasce.baseOpacity = 0.6;
pitchFasce.highlightOpacity = 1;
pitchFasce.isDefault = false;
pitchFasce.grassColor = "gray";
pitchFasce.type = 0;
pitchFasce.id = "pitchFasce";

pitchHeatMap = {...pitch};
pitchHeatMap.id = "heatMapPitch";
pitchHeatMap.marginRight = "5px";
pitchHeatMap.isDefault = false;
pitchHeatMap.grassColor = "gray";

function drawField(containerId, pitchElement) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realLength;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentContainerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor)
            .attr("id", pitchElement.id);

    if (typeof pitchElement.marginRight !== "undefined") {
        svg.attr("style", svg.attr("style") + ";margin-right: " + pitchElement.marginRight);
    }

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.length / 8; // disegno sempre 8 fasce, 4 per mezzo campo
    var numStripes = Math.floor(scaleSvg(pitchElement.length) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", 0)
                    .attr("y", scaleSvg(i * stripeHeight))
                    .attr("width", scaleSvg(pitchElement.width))
                    .attr("height", scaleSvg(stripeHeight))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    var pitchElementOutline = baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");

    var centerSpot = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", 2)
            .attr("fill", pitchElement.paintColor);

    var centerCircle = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", baseBezier)
            .attr("fill", 'none')
            .attr("stroke", pitchElement.paintColor);

    var halfwayLine = baseLayer.append("line")
            .attr("y1", scaleSvg(pitchElement.length / 2))
            .attr("y2", scaleSvg(pitchElement.length / 2))
            .attr("x1", 0)
            .attr("x2", scaleSvg(pitchElement.width))
            .attr("stroke", pitchElement.paintColor);

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
//    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    pathData = "M" + tmpScale(14) + ",0L" + tmpScale(14) + "," + tmpScale(16.5) + "L" + tmpScale(54) + "," + tmpScale(16.5) + "L" + tmpScale(54) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
//    pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
    pathData = "M" + tmpScale(24.84) + ",0L" + tmpScale(24.84) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + ",0";
    addPath(pathData, penaltyAreaTop);

    // Top D
//    pathData = "M" + tmpScale(16.5) + "," + tmpScale(42) + "A " + baseBezier + " " + baseBezier + " 5 0 0 " + tmpScale(16.5) + "," + tmpScale(26);
    pathData = "M" + tmpScale(42) + "," + tmpScale(16.5) + "A " + baseBezier + " " + baseBezier + " 5 0 1 " + tmpScale(26) + "," + tmpScale(16.5);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    var penaltySpotTop = penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(34))
            .attr("cy", tmpScale(11))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;
    var pointLayer = svg.append("g")
            .attr("data-index", "1")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");
    pitchElement.pointLayer = pointLayer;

    tip = d3.tip()
            .attr('class', 'd3-tip')
            .offset([-10, 0])
            .html(function (d) {
                return d;
            });
}

function drawPositionalField(containerId, pitchElement, modality) {
    currentContainerId = containerId;

    if (typeof pitchElement === 'undefined' || !pitchElement) {
        pitchElement = pitch;
    }

    var tmpMultiplier = pitchElement.length / realWidth;
    var tmpScale = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500 * tmpMultiplier]);

    if (pitchElement.isDefault) {
        multiplier = tmpMultiplier;
        scale = tmpScale;

        minDistance = scale(2.3); // distanza dipendente dalla dimensione del field
    }

    scaleSvg = d3.scaleLinear()
            .domain([0, 100])
            .range([0, 500]);

    var baseBezier = 50 * tmpMultiplier;

    // rimuovo precedente se esistente
    d3.select("#" + pitchElement.id).remove();
    var svg = d3.select("#" + currentContainerId).append("svg")
            .attr("width", scaleSvg(pitchElement.width + pitchElement.padding.left + pitchElement.padding.right))
            .attr("height", scaleSvg(pitchElement.length + pitchElement.padding.top + pitchElement.padding.bottom))
            .attr("style", "background:" + pitchElement.grassColor)
            .attr("id", pitchElement.id);

    var baseLayer = svg.append("g")
            .attr("data-index", "0")
            .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");

    // fasce alternate verde chiaro e verde scuro
    var stripeHeight = pitchElement.width / 8; // Altezza delle fasce
    var numStripes = Math.floor(scaleSvg(pitchElement.width) / scaleSvg(stripeHeight));

    if (pitchElement.isDefault) {
        for (var i = 0; i < numStripes; i++) {
            // var stripeColor = i % 2 === 0 ? "#4059df" : "#475ee0"; // Alterna tra verde chiaro e scuro
            var stripeColor = i % 2 === 0 ? "#46ac6d" : "#40b36d"; // Alterna tra verde chiaro e scuro

            baseLayer.append("rect")
                    .attr("x", scaleSvg(i * stripeHeight))
                    .attr("y", 0)
                    .attr("width", scaleSvg(stripeHeight))
                    .attr("height", scaleSvg(pitchElement.length))
                    .attr("fill", stripeColor);
        }
    }

    // linee dei campi
    var pitchElementOutline = baseLayer.append("rect")
            .attr("x", 0)
            .attr("y", 0)
            .attr("width", scaleSvg(pitchElement.width))
            .attr("height", scaleSvg(pitchElement.length))
            .attr("stroke", pitchElement.paintColor)
            .attr("fill", "none");

    var centerSpot = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", 2)
            .attr("fill", pitchElement.paintColor);

    var centerCircle = baseLayer.append("circle")
            .attr("cx", scaleSvg(pitchElement.width / 2))
            .attr("cy", scaleSvg(pitchElement.length / 2))
            .attr("r", baseBezier)
            .attr("fill", 'none')
            .attr("stroke", pitchElement.paintColor);

    var halfwayLine = baseLayer.append("line")
            .attr("y1", scaleSvg(pitchElement.length))
            .attr("y2", 0)
            .attr("x1", scaleSvg(pitchElement.width / 2))
            .attr("x2", scaleSvg(pitchElement.width / 2))
            .attr("stroke", pitchElement.paintColor);

    // corners
    function addPath(pathData, parentElement, size) {
        var path = parentElement.append("path")
                .attr("d", pathData)
                .attr("stroke", pitchElement.paintColor)
                .attr("fill", "none");

        if (typeof size !== 'undefined') {
            path.attr("stroke-width", size);
        }

        return path;
    }

    // top left
    var pathData = "M0," + scaleSvg(1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(1) + ",0";
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + ",0 A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 0" + scaleSvg(pitchElement.width) + "," + scaleSvg(1);
    addPath(pathData, baseLayer);

    // bottom left
    pathData = "M0," + scaleSvg(pitchElement.length - 1) + "A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(1) + "," + scaleSvg(pitchElement.length);
    addPath(pathData, baseLayer);

    // top right
    pathData = "M" + scaleSvg(pitchElement.width - 1) + "," + scaleSvg(pitchElement.length) + " A " + scaleSvg(1) + " " + scaleSvg(1) + " 45 0 1" + scaleSvg(pitchElement.width) + "," + scaleSvg(pitchElement.length - 1);
    addPath(pathData, baseLayer);

    // Top Penalty Area
    var penaltyAreaTop = baseLayer.append("g");
    pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Area
    pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
    addPath(pathData, penaltyAreaTop);

    // Top D
    pathData = "M" + tmpScale(16.5) + "," + tmpScale(42) + "A " + baseBezier + " " + baseBezier + " 5 0 0 " + tmpScale(16.5) + "," + tmpScale(26);
    addPath(pathData, penaltyAreaTop);

    // Top Penalty Spot
    var penaltySpotTop = penaltyAreaTop.append("circle")
            .attr("cx", tmpScale(11))
            .attr("cy", tmpScale(34))
            .attr("r", 1)
            .attr("fill", pitchElement.paintColor)
            .attr("stroke", pitchElement.paintColor);

    var penaltyAreaBottom = baseLayer.append("g");
    penaltyAreaBottom.html(penaltyAreaTop.html());
    penaltyAreaBottom.attr("transform", "rotate(180) translate(-" + scaleSvg(pitchElement.width) + ",-" + scaleSvg(pitchElement.length) + ")");

    pitchElement.baseLayer = baseLayer;
    if (pitchElement.isDefault) {
        var pointLayer = svg.append("g")
                .attr("data-index", "1")
                .attr("transform", "translate(" + scaleSvg(pitchElement.padding.left) + "," + scaleSvg(pitchElement.padding.top) + ")");
        pitchElement.pointLayer = pointLayer;

        tip = d3.tip()
                .attr('class', 'd3-tip')
                .offset([-10, 0])
                .html(function (d) {
                    return d;
                });

        //drawMainPitchText("Please choose a filter");
    } else {
        drawBackgroundArrow(baseLayer, pitchFasce);
        delete eventFilter.currentZoneId;

        if (typeof modality === "undefined") {
            modality = pitchFasce.type;
        }

        if (typeof modality === 'undefined' || !modality || modality === 0) { // divisione orizzontale 2 fasce
            // Metà
            for (var i = 1; i < 2; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 2 * i) + ",0L" + scaleSvg(pitchFasce.width / 2 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }

            for (var i = 0; i < 2; i++) {
                baseLayer.append("rect")
                        .attr("x", scaleSvg(pitchFasce.width / 2) * i)
                        .attr("y", 0)
                        .attr("width", scaleSvg(pitchFasce.width / 2))
                        .attr("height", scaleSvg(pitchFasce.length))
                        .attr("fill", "transparent")
                        .attr("data-event", (i + 1))
                        .attr("data-index", i)
                        .style("cursor", "pointer")
                        .on("click", function () {
                            clickEvent(this, 0, 1);
                        })
                        .on("contextmenu", function () {
                            clickEvent(this, 0, 2);
                        });
            }
        } else if (modality === 1) { // divisione verticale 5 fasce
            // Zone
            var baseValue = 2;
            for (var i = 1; i < 3; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 3 * i) + ",0L" + scaleSvg(pitchFasce.width / 3 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }

            for (var i = 0; i < 3; i++) {
                baseLayer.append("rect")
                        .attr("x", scaleSvg(pitchFasce.width / 3) * i)
                        .attr("y", 0)
                        .attr("width", scaleSvg(pitchFasce.width / 3))
                        .attr("height", scaleSvg(pitchFasce.length))
                        .attr("fill", "transparent")
                        .attr("data-event", (baseValue + i + 1))
                        .attr("data-index", i)
                        .style("cursor", "pointer")
                        .on("click", function () {
                            clickEvent(this, 1, 1);
                        })
                        .on("contextmenu", function () {
                            clickEvent(this, 1, 2);
                        });
            }
        } else if (modality === 2) { // divisione verticale 5 fasce
            // Canali
            for (var i = 1; i < 5; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M0," + scaleSvg(pitchFasce.length / 5 * i) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length / 5 * i);
                addPath(pathData, layerFasce, 2);
            }

            var baseValue = 5;
            for (var i = 0; i < 5; i++) {
                baseLayer.append("rect")
                        .attr("x", 0)
                        .attr("y", scaleSvg(pitchFasce.length / 5) * i)
                        .attr("width", scaleSvg(pitchFasce.width))
                        .attr("height", scaleSvg(pitchFasce.length / 5))
                        .attr("fill", "transparent")
                        .attr("data-event", (baseValue + i + 1))
                        .attr("data-index", i)
                        .style("cursor", "pointer")
                        .on("click", function () {
                            clickEvent(this, 2, 1);
                        })
                        .on("contextmenu", function () {
                            clickEvent(this, 2, 2);
                        });
            }
        } else if (modality === 3) { // divisione orizzontale e verticale
            // Zone e Canali
            for (var i = 1; i < 3; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M" + scaleSvg(pitchFasce.width / 3 * i) + ",0L" + scaleSvg(pitchFasce.width / 3 * i) + "," + scaleSvg(pitchFasce.length);
                addPath(pathData, layerFasce, 2);
            }
            for (var i = 1; i < 5; i++) {
                var layerFasce = baseLayer.append("g");
                pathData = "M0," + scaleSvg(pitchFasce.length / 5 * i) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length / 5 * i);
                addPath(pathData, layerFasce, 2);
            }

            var baseValue = 10, counter = 1;
            for (var i = 0; i < 5; i++) {
                for (var j = 0; j < 3; j++) {
                    baseLayer.append("rect")
                            .attr("x", scaleSvg(pitchFasce.width / 3) * j)
                            .attr("y", scaleSvg(pitchFasce.length / 5) * i)
                            .attr("width", scaleSvg(pitchFasce.width / 3))
                            .attr("height", scaleSvg(pitchFasce.length / 5))
                            .attr("fill", "transparent")
                            .attr("data-event", (baseValue + counter))
                            .attr("data-index", i)
                            .attr("data-index-j", j)
                            .style("cursor", "pointer")
                            .on("click", function () {
                                clickEvent(this, 3, 1);
                            })
                            .on("contextmenu", function () {
                                clickEvent(this, 3, 2);
                            });

                    counter++;
                }
            }
        } else if (modality === 4) { // disivione aree (fuori, area, area piccola)
            // PRIMA META' OFFENSIVA
//            pathData = "M0,0L" + scaleSvg(pitchFasce.width / 2) + ",0L" + scaleSvg(pitchFasce.width / 2) + "," + scaleSvg(pitchFasce.length) + "L0," + scaleSvg(pitchFasce.length) + "L0," + tmpScale(54) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L0," + tmpScale(14) + "L0,0";
//            var fuoriArea = addPath(pathData, baseLayer, 2);
//            fuoriArea.attr("fill", "transparent") // altrimenti non è cliccabile tutta
//                    .attr("x", realWidth / 4)
//                    .attr("y", 0)
//                    .attr("width", scaleSvg(pitchFasce.width / 2))
//                    .attr("height", scaleSvg(pitchFasce.length))
//                    .attr("data-event", 24)
//                    .attr("data-index", 0)
//                    .style("cursor", "pointer")
//                    .on("click", function () {
//                        clickEvent(this, 4, 1);
//                    })
//                    .on("contextmenu", function () {
//                        clickEvent(this, 4, 2);
//                    });

            //pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54) + "L0," + tmpScale(43.16) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L0," + tmpScale(24.84) + "L0," + tmpScale(14);
            pathData = "M0," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(14) + "L" + tmpScale(16.5) + "," + tmpScale(54) + "L0," + tmpScale(54) + "L0," + tmpScale(14);
            var areaGrande = addPath(pathData, baseLayer, 2);
            areaGrande.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", 0)
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 26)
                    .attr("data-index", 1)
                    .style("cursor", "pointer")
                    .on("click", function () {
                        clickEvent(this, 4, 1);
                    })
                    .on("contextmenu", function () {
                        clickEvent(this, 4, 2);
                    });

            pathData = "M0," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(24.84) + "L" + tmpScale(5.5) + "," + tmpScale(43.16) + "L0," + tmpScale(43.16);
            var areaPiccola = addPath(pathData, baseLayer, 2);
            areaPiccola.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", 0)
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 27)
                    .attr("data-index", 1)
                    .style("cursor", "pointer")
                    .on("click", function () {
                        clickEvent(this, 4, 1);
                    })
                    .on("contextmenu", function () {
                        clickEvent(this, 4, 2);
                    });

//            baseLayer.append("rect")
//                    .attr("x", 0)
//                    .attr("y", tmpScale(24.84))
//                    .attr("width", tmpScale(5.5))
//                    .attr("height", tmpScale(18.32))
//                    .attr("fill", "transparent")
//                    .attr("data-event", 26)
//                    .attr("data-index", 2)
//                    .attr("stroke", pitchElement.paintColor)
//                    .attr("stroke-width", 2)
//                    .style("cursor", "pointer")
//                    .on("click", function () {
//                        clickEvent(this, 4, 1);
//                    })
//                    .on("contextmenu", function () {
//                        clickEvent(this, 4, 2);
//                    });

            // SECONDA META' OFFENSIVA
//            pathData = "M" + scaleSvg(pitchFasce.width) + ",0L" + scaleSvg(pitchFasce.width / 2) + ",0L" + scaleSvg(pitchFasce.width / 2) + "," + scaleSvg(pitchFasce.length) + "L" + scaleSvg(pitchFasce.width) + "," + scaleSvg(pitchFasce.length) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(54) + "L" + tmpScale(88.5) + "," + tmpScale(54) + "L" + tmpScale(88.5) + "," + tmpScale(14) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(14) + "L" + scaleSvg(pitchFasce.width) + ",0";
//            fuoriArea = addPath(pathData, baseLayer, 2);
//            fuoriArea.attr("fill", "transparent") // altrimenti non è cliccabile tutta
//                    .attr("x", realWidth * 1.5)
//                    .attr("y", 0)
//                    .attr("width", scaleSvg(pitchFasce.width / 2))
//                    .attr("height", scaleSvg(pitchFasce.length))
//                    .attr("data-event", 26)
//                    .attr("data-index", 2)
//                    .style("cursor", "pointer")
//                    .on("click", function () {
//                        clickEvent(this, 4, 1);
//                    })
//                    .on("contextmenu", function () {
//                        clickEvent(this, 4, 2);
//                    });

            pathData = "M" + scaleSvg(pitchFasce.width) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(14);
            areaGrande = addPath(pathData, baseLayer, 2);
            areaGrande.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", tmpScale(88.5))
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 28)
                    .attr("data-index", 3)
                    .style("cursor", "pointer")
                    .on("click", function () {
                        clickEvent(this, 4, 1);
                    })
                    .on("contextmenu", function () {
                        clickEvent(this, 4, 2);
                    });

            // pathData = "M" + scaleSvg(pitchFasce.width) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(14) + "L" + tmpScale(88.5) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(54) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(14);
            pathData = "M" + scaleSvg(pitchFasce.width) + "," + tmpScale(24.84) + "L" + tmpScale(99.5) + "," + tmpScale(24.84) + "L" + tmpScale(99.5) + "," + tmpScale(43.16) + "L" + scaleSvg(pitchFasce.width) + "," + tmpScale(43.16);
            areaGrande = addPath(pathData, baseLayer, 2);
            areaGrande.attr("fill", clickColors.zoneClickable) // altrimenti non è cliccabile tutta
                    .attr("x", tmpScale(88.5))
                    .attr("y", 0)
                    .attr("width", tmpScale(16.5))
                    .attr("height", tmpScale(40.3))
                    .attr("data-event", 29)
                    .attr("data-index", 3)
                    .style("cursor", "pointer")
                    .on("click", function () {
                        clickEvent(this, 4, 1);
                    })
                    .on("contextmenu", function () {
                        clickEvent(this, 4, 2);
                    });
        }

        $("#pitchDiagram").insertAfter($("#pitchFasce"));   // sposto la torta (filtro angolo) dopo filtro fasce
    }
}

function drawBackgroundArrow(layer, pitch) {
    if (d3.select("#arrow" + pitch.id).empty()) {
        // Calcola le dimensioni della freccia in base alle proporzioni specificate
        var arrowWidth = scaleSvg(pitch.width / 2);
        var arrowHeight = scaleSvg(pitch.length / 5);
        var arrowHeadWidth = arrowWidth / 4;
        var arrowHeadHeight = arrowHeight / 3 * 1.5;

        // Calcola le coordinate x e y per posizionare la freccia al centro del SVG
        var centerX = (scaleSvg(pitch.width) - arrowWidth) / 2;
        var centerY = (scaleSvg(pitch.length) / 2) - (arrowHeight / 2);

        // Costruisci il path per la freccia
        var arrowPath = "M" + (centerX) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY - arrowHeadHeight) +
                " L" + (centerX + arrowHeadWidth * 3 + arrowHeadWidth) + "," + (centerY + arrowHeight / 2) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeadHeight + arrowHeight) +
                " L" + (centerX + arrowHeadWidth * 3) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY + arrowHeight) +
                " L" + (centerX) + "," + (centerY);

        layer.append("path")
                .attr("d", arrowPath)
                .attr("stroke", "#ffffff33")
                .attr("stroke-width", 1)
                .attr("fill", "#ffffff25")
                .attr("id", "arrow" + pitch.id);
    }
}

function clickEvent(element, type, eventType) {
    if (eventType === 2) {  // tasto destro
        d3.event.preventDefault();
    }

    // tolgo da tutti
    resetPositionalFilter();

    var d3Element = d3.select(element);
    if (d3Element.attr("fill") === 'transparent') {
        d3Element.attr("fill", clickColors.to);
    }

    var zoneId = parseInt(d3Element.attr("data-event"));
//    var index = parseInt(d3Element.attr("data-index"));
//    var indexJ = "";
//    if (type === 3) {
//        indexJ = parseInt(d3Element.attr("data-index-j"));
//    }

    eventFilter.currentZoneId = zoneId;
}

function resetPositionalFilter() {
    d3.selectAll("#pitchFasce *[data-event]").attr("fill", "transparent");
}

function changeOptions() {
    var type;
    if (posizionaleOptions.orientation === 'half') {
        type = 1;
        posizionaleOptions.orientation = 'vertical';
        $("#modal-change-positional").attr("class", "ph-number-circle-two");
        $("#modal-positional-description").text(globalMessages.get("event.filter.modal.positional.zone"));
    } else if (posizionaleOptions.orientation === 'vertical') {
        type = 2;
        posizionaleOptions.orientation = 'horizontal';
        $("#modal-change-positional").attr("class", "ph-number-circle-three");
        $("#modal-positional-description").text(globalMessages.get("event.filter.modal.positional.channel"));
    } else if (posizionaleOptions.orientation === 'horizontal') {
        type = 3;
        posizionaleOptions.orientation = 'grid';
        $("#modal-change-positional").attr("class", "ph-number-circle-four");
        $("#modal-positional-description").text(globalMessages.get("event.filter.modal.positional.zone.channel"));
    } else if (posizionaleOptions.orientation === 'grid') {
        type = 4;
        posizionaleOptions.orientation = 'aree';
        $("#modal-change-positional").attr("class", "ph-number-circle-five");
        $("#modal-positional-description").text(globalMessages.get("event.filter.modal.positional.area"));
    } else {
        type = 0;
        posizionaleOptions.orientation = 'half';
        $("#modal-change-positional").attr("class", "ph-number-circle-one");
        $("#modal-positional-description").text(globalMessages.get("event.filter.modal.positional.half"));
    }
    drawPositionalField("modal-event-position", pitchFasce, type);
    pitchFasce.type = type;
    if (typeof handleResize === "function") {
        handleResize();
    }
}

function drawPoint(x, y, amount, totalAmount) {
    if (pitch.pointsDrawed < 3) {
        // Define the shadow filter if it doesn't already exist
        if (!document.getElementById("circleShadowFilter")) {
            const defs = pitch.pointLayer.append("defs");
            const filter = defs.append("filter")
                    .attr("id", "circleShadowFilter")
                    .attr("x", "-50%")
                    .attr("y", "-50%")
                    .attr("width", "200%")
                    .attr("height", "200%");

            filter.append("feGaussianBlur")
                    .attr("in", "SourceAlpha")
                    .attr("stdDeviation", "2"); // Adjust the blur radius as needed

            filter.append("feOffset")
                    .attr("dx", "2") // Adjust horizontal shadow offset
                    .attr("dy", "2") // Adjust vertical shadow offset
                    .attr("result", "offsetBlur");

            const feMerge = filter.append("feMerge");
            feMerge.append("feMergeNode").attr("in", "offsetBlur");
            feMerge.append("feMergeNode").attr("in", "SourceGraphic");
        }

        // sistemo i punti
        var firstPoint = getCorrectPoint(x, y);
        var scaledRaggio = raggio * multiplier;
        scaledRaggio += (2 - (pitch.pointsDrawed));

        var color = "gray";
        if (pitch.pointsDrawed === 0) {
            color = "white";
        }

        var percentage = Math.floor(amount / totalAmount * 100);
        var pointCircle = pitch.pointLayer.append("circle")
                .attr("cx", firstPoint.cx + scaledRaggio / 4)
                .attr("cy", firstPoint.cy + scaledRaggio / 4)
                .attr("r", scaledRaggio)
                .attr("percentage", percentage)
                .attr("fill", color)
                .attr("filter", "url(#circleShadowFilter)")
                .call(tip)  // Chiamata per inizializzare d3-tip sul testo
                .on("mouseover", function (d) {
                    tip.show(d3.select(this).attr("percentage") + " %", this);
                })
                .on("mouseout", function (d) {
                    tip.hide();
                });

        if (color === "gray") {
            pointCircle.style("stroke", "white")
                    .style("stroke-width", 1);
        } else {
            pointCircle.style("stroke", "gray")
                    .style("stroke-width", 1);
        }

        pointCircle.lower();
        pitch.pointsDrawed++;
    }
}

function drawPointTeam(x, y, color) {
    // Define the shadow filter if it doesn't already exist
    if (!document.getElementById("circleShadowFilter")) {
        const defs = pitch.pointLayer.append("defs");
        const filter = defs.append("filter")
                .attr("id", "circleShadowFilter")
                .attr("x", "-50%")
                .attr("y", "-50%")
                .attr("width", "200%")
                .attr("height", "200%");

        filter.append("feGaussianBlur")
                .attr("in", "SourceAlpha")
                .attr("stdDeviation", "2"); // Adjust the blur radius as needed

        filter.append("feOffset")
                .attr("dx", "2") // Adjust horizontal shadow offset
                .attr("dy", "2") // Adjust vertical shadow offset
                .attr("result", "offsetBlur");

        const feMerge = filter.append("feMerge");
        feMerge.append("feMergeNode").attr("in", "offsetBlur");
        feMerge.append("feMergeNode").attr("in", "SourceGraphic");
    }

    // sistemo i punti
    var firstPoint = getCorrectPoint(x, y);
    var scaledRaggio = raggio * multiplier;

    pitch.pointLayer.append("circle")
            .attr("cx", firstPoint.cx + scaledRaggio / 4)
            .attr("cy", firstPoint.cy + scaledRaggio / 4)
            .attr("r", scaledRaggio)
            .attr("fill", color)
            .attr("filter", "url(#circleShadowFilter)");

    pitch.pointsDrawed++;
}

function drawPlayerRole(role) {
    if (role === "P") {
        // portiere
        var tmpMultiplier = pitch.length / realLength;
        var tmpScale = d3.scaleLinear()
                .domain([0, 100])
                .range([0, 500 * tmpMultiplier]);

        var pathData = "M" + tmpScale(25.75) + "," + tmpScale(0.75) + "L" + tmpScale(25.75) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(5.5) + "L" + tmpScale(43.16) + "," + tmpScale(0.75);

        var translate = "rotate(180) translate(-" + (scaleSvg(pitch.width) + scaleSvg(pitch.padding.left)) + ",-" + (scaleSvg(pitch.length) + scaleSvg(pitch.padding.left)) + ")";
        var g = pitch.pointLayer.append("g")
                .attr("transform", translate);

        g.append("path")
                .attr("d", pathData)
                .attr("fill", colors.orange);
    } else if (role === "D") {
        // difensore
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", height / 3 * 2)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    } else if (role === "C") {
        // centrocampista
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", height / 3)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    } else if (role === "A") {
        // attaccante
        var width = scaleSvg(pitch.width);
        var height = scaleSvg(pitch.length);

        pitch.pointLayer.append("rect")
                .attr("x", 0)
                .attr("y", 0)
                .attr("width", width)
                .attr("height", height / 3)
                .attr("fill", colors.orange);
    }
}

/*
 * UTILS
 */
function getPointsRectangle(punti) {
    let minX = punti[0].x;
    let maxX = punti[0].x;
    let minY = punti[0].y;
    let maxY = punti[0].y;

    // Trova i valori min e max per x e y
    punti.forEach(punto => {
        if (punto.x < minX)
            minX = punto.x;
        if (punto.x > maxX)
            maxX = punto.x;
        if (punto.y < minY)
            minY = punto.y;
        if (punto.y > maxY)
            maxY = punto.y;
    });

    var raggio = 30;
    // Calcola larghezza e altezza aggiungendo un margine di 20 unità (10 per ogni lato)
    let larghezza = maxX - minX + raggio * 2; // Aggiunge un "raggio" di 10 per lato alla larghezza
    let altezza = maxY - minY + raggio * 2; // Aggiunge un "raggio" di 10 per lato all'altezza

    // Crea il rettangolo con il margine
    if (minX < raggio) {
        minX = raggio;
    }
    if (minY < raggio) {
        minY = raggio;
    }
    let rettangolo = {
        x: minX - 30, // Sposta a sinistra di 10 unità per il margine
        y: minY - 30, // Sposta in alto di 10 unità per il margine
        larghezza: larghezza,
        altezza: altezza
    };

    return rettangolo;
}

function getCorrectPoint(x, y) {
    x = ((8.5 - x) / 8.5 * pitch.length);
    y = ((y) / 8 * pitch.width);

    var scaledRaggio = raggio * multiplier;
    scaledRaggio += (2 - (pitch.pointsDrawed));

    var firstPoint = {
        cx: scaleSvg(y),
        cy: scaleSvg(x),
        r: scaledRaggio
    };

    return firstPoint;
}

function handleResize() {
    var baseWidth = 1920;

    // Aggiorna le dimensioni del tuo SVG in base alle nuove dimensioni della finestra
    var newWidth = window.innerWidth;
//    var newPitchWidth = basePitchWidth / baseWidth * newWidth;
//    var newPitchLength = basePitchLength / basePitchWidth * newPitchWidth;
//
//    // console.log("handleResize()", "old pitch:", pitch.width, "x", pitch.length, "new pitch:", newPitchWidth, "x", newPitchLength);
//    pitch.width = newPitchWidth;
//    pitch.length = newPitchLength;
//    drawField(currentContainerId, pitch);

    // Aggiornamento pitch fasce
    var newPitchFasceWidth = basePitchFasceWidth / baseWidth * newWidth;
    var newPitchFasceLength = basePitchFasceLength / basePitchFasceWidth * newPitchFasceWidth;
    pitchFasce.width = newPitchFasceWidth;
    pitchFasce.length = newPitchFasceLength;

    drawPositionalField("modal-event-position", pitchFasce);
}

// Aggiungi un ascoltatore per l'evento di ridimensionamento della finestra
window.addEventListener("resize", handleResize);