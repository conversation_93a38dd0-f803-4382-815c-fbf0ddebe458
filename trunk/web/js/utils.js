function notEmpty(variable) {
    return (typeof variable !== "undefined" && (variable || variable === 0) && variable.length > 0);
}

function empty(variable) {
    return (typeof variable === "undefined" || !variable || variable.length === 0);
}

function getPageFiltersText() {
    let filters = [];

    let loadEventFromPage = true;
    if ($("#filter-eventtypeid").attr("multiple") === "multiple") {
        // gestione pagine con wizard per parametri
        loadEventFromPage = false;
    }

    $("#filters-containter").find("label").each(function (index, element) {
        let filterName = $(element).text().replace(":", "");
        let selectElements = $(element).parent().find("select");
        if (selectElements && selectElements.length === 1) {
            let selectElement = selectElements[0];
            let selectValue = $(selectElement).val();
            if (selectValue) {
                let selectId = $(selectElement).attr("id");
                // carico gli eventi solo una volta, guardo quindi per eventtypeid
                if (!loadEventFromPage && selectId === "filter-eventtypeid") {
                    if (typeof eventFilter.scatterplot === "undefined") {
                        if (typeof eventFilter.currentFilter !== "undefined") {
                            let events = [];
                            Object.keys(eventFilter.currentFilter).forEach(function (index) {
                                let eventParts = [];
                                let element = eventFilter.currentFilter[index];
                                if (element) {
                                    if (element.type === "2") {
                                        eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else if (element.type === "3") {
                                        eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else {
                                        eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    }
                                    if (typeof element.zoneId !== "undefined") {
                                        let part = " (";
                                        part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                                        part += ")";
                                        eventParts.push(part);
                                    }
                                    events.push(eventParts.join(""));
                                }
                            });
                            filters.push(filterName + " " + events.join(", "));
                        }
                    } else {
                        // scatterplot
                        if (typeof eventFilter.currentFilterX !== "undefined") {
                            let events = [];
                            Object.keys(eventFilter.currentFilterX).forEach(function (index) {
                                let eventParts = [];
                                let element = eventFilter.currentFilterX[index];
                                if (element) {
                                    if (element.type === "2") {
                                        eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else if (element.type === "3") {
                                        eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else {
                                        eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    }
                                    if (typeof element.zoneId !== "undefined") {
                                        let part = " (";
                                        part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                                        part += ")";
                                        eventParts.push(part);
                                    }
                                }
                            });
                            filters.push(filterName + " " + events.join(", "));
                        }
                        if (typeof eventFilter.currentFilterY !== "undefined") {
                            let events = [];
                            Object.keys(eventFilter.currentFilterY).forEach(function (index) {
                                let eventParts = [];
                                let element = eventFilter.currentFilterY[index];
                                if (element) {
                                    if (element.type === "2") {
                                        eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else if (element.type === "3") {
                                        eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    } else {
                                        eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                                        if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                            let part = " (";
                                            let tags = [];
                                            element.tagTypes.forEach(function (value) {
                                                tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                            });
                                            part += tags.join(", ");
                                            part += ")";
                                            eventParts.push(part);
                                        }
                                    }
                                    if (typeof element.zoneId !== "undefined") {
                                        let part = " (";
                                        part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                                        part += ")";
                                        eventParts.push(part);
                                    }
                                }
                            });
                            filters.push(filterName + " " + events.join(", "));
                        }
                    }
                } else {
                    let isEventTypeFilter = false;
                    if (selectId === "filter-eventtypeid" || selectId === "filter-tagtypeid"
                        || selectId === "filter-advancedeventtypeid"
                        || selectId === "filter-tacticaleventtypeid" || selectId === "filter-tacticaltagtypeid") {
                        isEventTypeFilter = true;
                    }
                    if (!isEventTypeFilter || (isEventTypeFilter && $(selectElement).closest(".tab-pane.fade").hasClass("show"))) {
                        if ($.isArray(selectValue)) {
                            if (selectValue.length > 0) {
                                // solo se non sono tutti selezionati
                                if (selectValue.length !== $(selectElement).find("option").length) {
                                    let selectValueTexts = [];
                                    selectValue.forEach(function (value) {
                                        let selectValueText = $(selectElement).find("option[value='" + value + "']").text();
                                        if (selectValueText) {
                                            selectValueText = selectValueText.replace("\n", "");
                                            selectValueTexts.push(selectValueText);
                                        }
                                    });
                                    filters.push(filterName + " " + selectValueTexts.join(", "));
                                }
                                // console.warn(filterName, selectValueTexts);
                            }
                        } else {
                            let selectValueText = $(selectElement).find("option[value='" + selectValue + "']").text();
                            if (selectValueText) {
                                selectValueText = selectValueText.replace("\n", "");
                                filters.push(filterName + " " + selectValueText);
                                // console.warn(filterName, selectValueText);
                            }
                        }
                    }
                }
            }
        }
    });

    return filters.join(" | ");
}

function getPageTitle() {
    let filters = new Map();

    $("#filters-containter").find("label").each(function (index, element) {
        let selectElements = $(element).parent().find("select");
        if (selectElements && selectElements.length === 1) {
            let selectElement = selectElements[0];
            let selectElementId = $(selectElement).attr("id");
            // tolto controllo $(selectElement).hasClass("is-required") per caricamento del tag
            if (!$(selectElement).hasClass("skip-title")) {
                let selectValue = $(selectElement).val();
                if (selectValue) {
                    if ($.isArray(selectValue)) {
                        if (selectValue.length > 0) {
                            // solo se non sono tutti selezionati
                            if (selectValue.length !== $(selectElement).find("option").length) {
                                let selectValueTexts = [];
                                selectValue.forEach(function (value) {
                                    let selectValueText = $(selectElement).find("option[value='" + value + "']").text();
                                    if (selectValueText) {
                                        selectValueText = selectValueText.replace("\n", "");
                                        selectValueTexts.push(selectValueText);
                                    }
                                });
                                filters.set(selectElementId, selectValueTexts.join(", "));
                            }
                        }
                    } else {
                        let selectValueText = $(selectElement).find("option[value='" + selectValue + "']").text();
                        if (selectValueText) {
                            selectValueText = selectValueText.replace("\n", "");
                            filters.set(selectElementId, selectValueText);
                        }
                    }
                }
            }
        }
    });

    let titleParts = [];
    if (filters.has("filter-playerid")) {
        titleParts.push(filters.get("filter-playerid"));
    }
    if (filters.has("filter-teamid")) {
        titleParts.push(filters.get("filter-teamid"));
    }
    let competitionText = filters.get("filter-competitionid");
    if (filters.has("filter-groupid")) {
        competitionText += " | " + filters.get("filter-groupid");
    }
    competitionText += " (" + filters.get("filter-seasonid") + ")";
    titleParts.push(competitionText);

    let eventParts = [];
    if ($("#filter-eventtypeid").attr("multiple") === "multiple") {
        // gestione pagine con wizard per parametri
        if (typeof eventFilter.scatterplot === "undefined") {
            if (typeof eventFilter.currentFilter !== "undefined") {
                Object.keys(eventFilter.currentFilter).forEach(function (index) {
                    let element = eventFilter.currentFilter[index];
                    if (element) {
                        if (element.type === "2") {
                            eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else if (element.type === "3") {
                            eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else {
                            eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        }
                        if (typeof element.zoneId !== "undefined") {
                            let part = " (";
                            part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                            part += ")";
                            eventParts.push(part);
                        }
                    }
                });
            }
        } else {
            // scatterplot
            if (typeof eventFilter.currentFilterX !== "undefined") {
                Object.keys(eventFilter.currentFilterX).forEach(function (index) {
                    let element = eventFilter.currentFilterX[index];
                    if (element) {
                        if (element.type === "2") {
                            eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else if (element.type === "3") {
                            eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else {
                            eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        }
                        if (typeof element.zoneId !== "undefined") {
                            let part = " (";
                            part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                            part += ")";
                            eventParts.push(part);
                        }
                    }
                });
            }
            if (typeof eventFilter.currentFilterY !== "undefined") {
                eventParts.push("/");
                Object.keys(eventFilter.currentFilterY).forEach(function (index) {
                    let element = eventFilter.currentFilterY[index];
                    if (element) {
                        if (element.type === "2") {
                            eventParts.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-advancedeventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else if (element.type === "3") {
                            eventParts.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-tacticaleventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        } else {
                            eventParts.push($("#filter-eventtypeid option[value='" + element.typeId + "']").text().replace("\n", ""));
                            if (typeof element.tagTypes !== "undefined" && element.tagTypes.length > 0) {
                                let part = " (";
                                let tags = [];
                                element.tagTypes.forEach(function (value) {
                                    tags.push($("#filter-eventtypeid option[value='" + element.typeId + "-" + value + "']").text().replace("\n", ""));
                                });
                                part += tags.join(", ");
                                part += ")";
                                eventParts.push(part);
                            }
                        }
                        if (typeof element.zoneId !== "undefined") {
                            let part = " (";
                            part += globalMessages.get(getZoneMessageTag(element.zoneId) + ".abb");
                            part += ")";
                            eventParts.push(part);
                        }
                    }
                });
            }
        }
    } else {
        if ($("#event-filters-event").hasClass("show") && $("#event-filters-event").hasClass("active") && filters.has("filter-eventtypeid")) {
            eventParts.push(filters.get("filter-eventtypeid"));
            if (filters.has("filter-tagtypeid")) {
                eventParts.push("(" + filters.get("filter-tagtypeid") + ")");
            }
            // filtri extra
            for (let [key, value] of filters) {
                if (key.startsWith("filter-eventtypeid") && key !== "filter-eventtypeid") {
                    eventParts.push(" + " + value);
                    // controllo tag
                    let tagKey = key.replace("eventtypeid", "tagtypeid");
                    if (filters.has(tagKey)) {
                        eventParts.push("(" + filters.get(tagKey) + ")");
                    }
                }
            }
        } else if ($("#event-filters-advanced").hasClass("show") && $("#event-filters-advanced").hasClass("active") && filters.has("filter-advancedeventtypeid")) {
            eventParts.push(filters.get("filter-advancedeventtypeid"));
            if (filters.has("filter-advancedtagtypeid")) {
                eventParts.push("(" + filters.get("filter-advancedtagtypeid") + ")");
            }
            // filtri extra
            for (let [key, value] of filters) {
                if (key.startsWith("filter-advancedeventtypeid") && key !== "filter-advancedeventtypeid") {
                    eventParts.push(" + " + value);
                    // controllo tag
                    let tagKey = key.replace("eventtypeid", "tagtypeid");
                    if (filters.has(tagKey)) {
                        eventParts.push("(" + filters.get(tagKey) + ")");
                    }
                }
            }
        } else if ($("#event-filters-tactical").hasClass("show") && $("#event-filters-tactical").hasClass("active") && filters.has("filter-tacticaleventtypeid")) {
            eventParts.push(filters.get("filter-tacticaleventtypeid"));
            if (filters.has("filter-tacticaltagtypeid")) {
                eventParts.push("(" + filters.get("filter-tacticaltagtypeid") + ")");
            }
            // filtri extra
            for (let [key, value] of filters) {
                if (key.startsWith("filter-tacticaleventtypeid") && key !== "filter-tacticaleventtypeid") {
                    eventParts.push(" + " + value);
                    // controllo tag
                    let tagKey = key.replace("eventtypeid", "tagtypeid");
                    if (filters.has(tagKey)) {
                        eventParts.push("(" + filters.get(tagKey) + ")");
                    }
                }
            }
        } else if (filters.has("filter-eventtypeid-x") && filters.has("filter-eventtypeid-y")) {
            let composedText = filters.get("filter-eventtypeid-x");
            if (filters.has("filter-tagtypeid-x")) {
                composedText += " (" + filters.get("filter-tagtypeid-x") + ")";
            }
            composedText += " / " + filters.get("filter-eventtypeid-y");
            if (filters.has("filter-tagtypeid-y")) {
                composedText += " (" + filters.get("filter-tagtypeid-y") + ")";
            }
            eventParts.push(composedText);
        }
    }
    if (eventParts.length > 0) {
        if (filters.has("filter-totaltype") && ($("#filter-totaltype").val() === "p90" || $("#filter-totaltype").val() === "average")) {
            eventParts.push("(" + filters.get("filter-totaltype") + ")");
        }
    }

    let completeTitle = titleParts.join(", ");
    if (eventParts.length > 0) {
        completeTitle += "<br/><span class='text-center text-muted fs-sm'>";
        completeTitle += eventParts.join(" ");
        completeTitle += "</span>";
    }
    if (isPlayerPage()) {
        let pageName = getPageName();
        if (!pageName.includes("-trend") && !pageName.includes("-playtime") && !pageName.includes("-positional")) {
            if (typeof minPlaytimeFilter !== "undefined" && minPlaytimeFilter) {
                completeTitle += "<br/><span class='text-center text-warning fs-sm'>";
                completeTitle += globalMessages.get("messages.playtime.filter.detailed").replace("  ", " " + minPlaytimeFilter + " ");
                completeTitle += "</span>";
            } else {
                completeTitle += "<br/><span class='text-center text-warning fs-sm'>";
                completeTitle += globalMessages.get("messages.playtime.filter");
                completeTitle += "</span>";
            }
        }
    }

    return completeTitle;
}

function getScreenshot(excludeFooter = false, excludeLogo = false, callback = null) {
    if ($("#container").length > 0) {
        if ($("#screenshot-dropdown").hasClass("working")) {
            new Noty({
                text: globalMessages.get("pdf.screenshot.already.saving.warning"),
                type: "warning"
            }).show();
            return;
        }

        // Check if htmlToImage is available
        if (typeof htmlToImage === 'undefined') {
            new Noty({
                text: globalMessages.get("pdf.screenshot.library.not.loaded.error"),
                type: "error"
            }).show();
            return;
        }

        new Noty({
            text: globalMessages.get("pdf.screenshot.saving.wait"),
            type: "success"
        }).show();
        $("#screenshot-dropdown").addClass("working");
        htmlToImage.toPng(document.getElementById("container"), {includeQueryParams: true, cacheBust: true}).then(function (dataUrl) {
            // Step 2: Create a new image element
            const img = new Image();
            img.src = dataUrl;
            img.crossOrigin = "anonymous";
            img.onload = function () {
                // Step 3: Create a canvas and set its dimensions
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const containerHeight = excludeFooter ? 0 : 100; // Height of your custom container
                const logoHeight = 100; // Height of your logo
                const logoWidth = 400; // Width of your logo (adjust as needed)
                canvas.width = img.width;
                canvas.height = img.height + containerHeight;

                // Draw the original image
                ctx.drawImage(img, 0, 0);

                // Step to add repeating watermark
                const watermarkLogo = new Image();
                watermarkLogo.src = '/sicsdataanalytics/images/logo.png'; // Path to your watermark logo
                watermarkLogo.onload = function () {
                    const spacingX = 200; // Horizontal spacing between watermarks
                    const spacingY = 150; // Vertical spacing between watermarks

                    for (let x = 0; x < canvas.width; x += spacingX) {
                        for (let y = 0; y < canvas.height; y += spacingY) {
                            ctx.save(); // Save the current state
                            ctx.translate(x + (logoWidth / 2), y + (logoHeight / 2)); // Move to the position for the watermark
                            ctx.rotate(-Math.PI / 4); // Rotate -45 degrees for oblique effect
                            ctx.globalAlpha = 0.02; // Set transparency for the watermark
                            ctx.drawImage(watermarkLogo, -logoWidth / 2, -logoHeight / 2, logoWidth / 2, logoHeight / 2); // Draw the watermark
                            ctx.restore(); // Restore to previous state
                        }
                    }

                    // Step 4: Draw the custom container
//                    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'; // Background color of the container
//                    ctx.fillRect(0, -containerHeight, canvas.width, containerHeight); // Draw rectangle for container

                    // Load and draw the logo (if not excluded)
                    const logo = new Image();
                    logo.src = '/sicsdataanalytics/images/logo_new.svg'; // Replace with your logo's path
                    logo.onload = function () {
                        if (!excludeLogo) {
                            ctx.drawImage(logo, 15, 15, logoWidth * 0.4, logoHeight * 0.5); // Draw the logo
                        }

                        // Step 5: Draw the custom container (if not excluded)
                        if (!excludeFooter) {
                            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)'; // Background color of the container
                            ctx.fillRect(0, img.height, canvas.width, containerHeight); // Draw rectangle for container

                            // Add custom text
                            ctx.fillStyle = 'black'; // Text color
                            ctx.font = '20px Arial';

                            // Add custom text with wrapping
                            const textToDisplay = getPageFiltersText();
                            wrapText(ctx, textToDisplay, 10, img.height + 30, canvas.width - 20, '20px Arial');
                        }

                        // Export the final image
                        const finalImageUrl = canvas.toDataURL('image/png');

                        if (callback) {
                            // If callback is provided, call it with the image data
                            callback(finalImageUrl);
                        } else {
                            // Default behavior: download the image
                            let splitted = document.URL.split("/");
                            const link = document.createElement('a');
                            link.href = finalImageUrl;
                            link.download = 'sics-' + splitted[splitted.length - 2] + '-' + splitted[splitted.length - 1].replace(".htm", "") + '.png';
                            link.click(); // Trigger download
                        }
                        $("#screenshot-dropdown").removeClass("working");
                    };
                };
            };
        }).catch(function (error) {
            console.error('Oops, something went wrong!', error);
            $("#screenshot-dropdown").removeClass("working");
        });
    } else {
        console.warn("Container not found !");
    }
}

// New functions for screenshot dropdown functionality
function takeInstantScreenshot() {
    getScreenshot(false, false, null);
}

function openScreenshotCollectionModal() {
    // Initialize screenshot collection data if not exists
    if (!localStorage.getItem('sics-screenshot-collection')) {
        localStorage.setItem('sics-screenshot-collection', JSON.stringify({
            screenshots: [],
            created: new Date().toISOString()
        }));
    }

    updateCollectionModalInfo();
    $('#modal-screenshot-collection').modal('show');
}

function addScreenshotToCollection() {
    getScreenshot(true, true, function(imageData) {
        // Get existing collection data
        let collectionData = JSON.parse(localStorage.getItem('sics-screenshot-collection') || '{"screenshots": [], "created": ""}');

        // Add new screenshot
        collectionData.screenshots.push({
            imageData: imageData,
            timestamp: new Date().toISOString(),
            pageUrl: window.location.href,
            pageTitle: document.title
        });

        // Save back to localStorage
        localStorage.setItem('sics-screenshot-collection', JSON.stringify(collectionData));

        // Update modal info
        updateCollectionModalInfo();

        new Noty({
            text: globalMessages.get("pdf.screenshot.added.to.pdf.success"),
            type: "success"
        }).show();
    });
}

function updateCollectionModalInfo() {
    let collectionData = JSON.parse(localStorage.getItem('sics-screenshot-collection') || '{"screenshots": [], "created": ""}');
    let screenshotCount = collectionData.screenshots.length;

    $('#collection-screenshot-count').text(screenshotCount);

    if (screenshotCount === 0) {
        $('#collection-estimated-size').text('0 KB');
        $('#btn-download-collection, #btn-delete-collection').prop('disabled', true);
        return;
    }

    // Calculate more accurate size estimation based on actual image dimensions
    calculateCollectionSize(collectionData.screenshots).then(estimatedSizeBytes => {
        let sizeText;
        if (estimatedSizeBytes < 1024) {
            sizeText = Math.round(estimatedSizeBytes) + ' B';
        } else if (estimatedSizeBytes < 1024 * 1024) {
            sizeText = Math.round(estimatedSizeBytes / 1024) + ' KB';
        } else {
            sizeText = (estimatedSizeBytes / (1024 * 1024)).toFixed(1) + ' MB';
        }
        $('#collection-estimated-size').text(sizeText);
    }).catch(error => {
        console.error('Error calculating collection size:', error);
        // Fallback to simple estimation (more realistic)
        let estimatedSize = screenshotCount * 0.15; // ~150KB per screenshot average
        let sizeText = estimatedSize < 1 ?
            Math.round(estimatedSize * 1024) + ' KB' :
            estimatedSize.toFixed(1) + ' MB';
        $('#collection-estimated-size').text(sizeText);
    });

    // Enable/disable buttons based on screenshot count
    $('#btn-download-collection, #btn-delete-collection').prop('disabled', false);
}

function calculateCollectionSize(screenshots) {
    return new Promise((resolve, reject) => {
        if (screenshots.length === 0) {
            resolve(0);
            return;
        }

        // Load all images to get their actual dimensions
        const imagePromises = screenshots.map((screenshot, index) => {
            return new Promise((resolveImg, rejectImg) => {
                const img = new Image();
                img.onload = function() {
                    // Calculate estimated size based on dimensions
                    // PNG compression varies, but we can estimate based on:
                    // - Image dimensions (width * height)
                    // - Color depth (assume 32-bit RGBA = 4 bytes per pixel)
                    // - PNG compression ratio (typically 10-30% of uncompressed size)

                    const pixelCount = this.width * this.height;
                    const uncompressedSize = pixelCount * 4; // 4 bytes per pixel (RGBA)

                    // Estimate PNG compression ratio based on image characteristics
                    // Screenshots typically compress very well due to large solid color areas
                    // Based on real-world data: 1,360,450 pixels = 340KB (6.4% compression ratio)
                    let compressionRatio = 0.06; // Start with 6% (excellent compression for screenshots)

                    // Adjust compression ratio based on image size
                    // Larger images often have more detail and compress less efficiently
                    if (pixelCount > 3000000) { // > 3MP
                        compressionRatio = 0.10; // 10% for very large images
                    } else if (pixelCount > 2000000) { // > 2MP
                        compressionRatio = 0.08; // 8% for large images
                    } else if (pixelCount > 1000000) { // > 1MP
                        compressionRatio = 0.07; // 7% for medium images
                    }

                    const estimatedSize = Math.round(uncompressedSize * compressionRatio);

                    resolveImg({
                        width: this.width,
                        height: this.height,
                        pixelCount: pixelCount,
                        estimatedSize: estimatedSize
                    });
                };
                img.onerror = function() {
                    console.error('Error loading image for size calculation', index);
                    // Fallback estimation for failed images
                    resolveImg({
                        width: 1920,
                        height: 1080,
                        pixelCount: 1920 * 1080,
                        estimatedSize: 125000 // ~125KB fallback (more realistic for screenshots)
                    });
                };
                img.src = screenshot.imageData;
            });
        });

        Promise.all(imagePromises).then(imageInfos => {
            // Calculate total dimensions for the final vertical image
            const maxWidth = Math.max(...imageInfos.map(info => info.width));
            const totalHeight = imageInfos.reduce((sum, info) => sum + info.height, 0);
            const totalPixelCount = maxWidth * totalHeight;

            // Calculate final image size
            const uncompressedFinalSize = totalPixelCount * 4;
            let finalCompressionRatio = 0.06; // Base compression ratio (same as individual screenshots)

            // Adjust for very large final images
            // Larger vertical images may have slightly less compression due to more varied content
            if (totalPixelCount > 15000000) { // > 15MP
                finalCompressionRatio = 0.12; // 12% for very large vertical images
            } else if (totalPixelCount > 10000000) { // > 10MP
                finalCompressionRatio = 0.10; // 10% for large vertical images
            } else if (totalPixelCount > 5000000) { // > 5MP
                finalCompressionRatio = 0.08; // 8% for medium vertical images
            } else if (totalPixelCount > 2000000) { // > 2MP
                finalCompressionRatio = 0.07; // 7% for small-medium vertical images
            }

            const estimatedFinalSize = Math.round(uncompressedFinalSize * finalCompressionRatio);

            resolve(estimatedFinalSize);
        }).catch(error => {
            reject(error);
        });
    });
}

function downloadCollection() {
    let collectionData = JSON.parse(localStorage.getItem('sics-screenshot-collection') || '{"screenshots": [], "created": ""}');

    if (collectionData.screenshots.length === 0) {
        new Noty({
            text: globalMessages.get("pdf.screenshot.no.pages.to.download.warning"),
            type: "warning"
        }).show();
        return;
    }

    // Generate vertical PNG image from screenshot collection
    generateVerticalPngImage(collectionData);
}

function generateVerticalPngImage(collectionData) {
    try {
        new Noty({
            text: globalMessages.get("pdf.screenshot.generating.pdf.wait"),
            type: "info"
        }).show();

        // First pass: load all images and calculate dimensions
        const imagePromises = collectionData.screenshots.map((screenshot, index) => {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = function() {
                    resolve({
                        imageData: screenshot.imageData,
                        width: this.width,
                        height: this.height,
                        originalScreenshot: screenshot
                    });
                };
                img.onerror = function() {
                    console.error('Error loading image for screenshot', index);
                    reject(new Error(`Failed to load image ${index}`));
                };
                img.src = screenshot.imageData;
            });
        });

        // Wait for all images to load
        Promise.all(imagePromises).then(processedImages => {
            // Filter out any failed images
            const validImages = processedImages.filter(img => img !== null);

            if (validImages.length === 0) {
                new Noty({
                    text: "No valid images to process",
                    type: "error"
                }).show();
                return;
            }

            // Calculate total dimensions
            const maxWidth = Math.max(...validImages.map(img => img.width));
            const totalHeight = validImages.reduce((sum, img) => sum + img.height, 0);

            // Create the final canvas
            const finalCanvas = document.createElement('canvas');
            const finalCtx = finalCanvas.getContext('2d');
            finalCanvas.width = maxWidth;
            finalCanvas.height = totalHeight;

            // Set white background
            finalCtx.fillStyle = 'white';
            finalCtx.fillRect(0, 0, maxWidth, totalHeight);

            // Draw all images vertically
            let currentY = 0;
            const imageDrawPromises = validImages.map((processedImg, index) => {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = function() {
                        // Center the image horizontally if it's smaller than maxWidth
                        const x = (maxWidth - processedImg.width) / 2;
                        finalCtx.drawImage(this, x, currentY);
                        currentY += processedImg.height;
                        resolve();
                    };
                    img.src = processedImg.imageData;
                });
            });

            // Wait for all images to be drawn
            Promise.all(imageDrawPromises).then(() => {
                // Export the final image
                const finalImageUrl = finalCanvas.toDataURL('image/png');

                // Download the image
                const fileName = 'sics-screenshots-vertical-' + new Date().toISOString().split('T')[0] + '.png';
                const link = document.createElement('a');
                link.href = finalImageUrl;
                link.download = fileName;
                link.click();

                new Noty({
                    text: globalMessages.get("pdf.screenshot.pdf.downloaded.success"),
                    type: "success"
                }).show();

                // Ask user if they want to delete the collection data
                /*setTimeout(() => {
                    if (confirm(globalMessages.get("pdf.screenshot.delete.pdf.data.confirm"))) {
                        deleteCollection();
                    }
                }, 1000);*/
            });
        }).catch(error => {
            console.error('Error processing images:', error);
            new Noty({
                text: globalMessages.get("pdf.screenshot.generating.pdf.error"),
                type: "error"
            }).show();
        });

    } catch (error) {
        console.error('Error generating vertical PNG:', error);
        new Noty({
            text: globalMessages.get("pdf.screenshot.generating.pdf.error"),
            type: "error"
        }).show();
    }
}

function deleteCollection() {
    localStorage.removeItem('sics-screenshot-collection');
    updateCollectionModalInfo();
    new Noty({
        text: globalMessages.get("pdf.screenshot.pdf.data.deleted.success"),
        type: "success"
    }).show();
}

// Function to wrap text within a specified width
function wrapText(ctx, text, x, y, maxWidth, lineHeight) {
    const words = text.split(' ');
    let line = '';

    for (let n = 0; n < words.length; n++) {
        const testLine = line + words[n] + ' ';
        const metrics = ctx.measureText(testLine);
        const testWidth = metrics.width;

        if (testWidth > maxWidth && n > 0) {
            ctx.fillText(line, x, y);
            line = words[n] + ' ';
            y += parseInt(lineHeight); // Move down by line height
        } else {
            line = testLine;
        }
    }
    ctx.fillText(line, x, y); // Draw any remaining text in the last line
}

function changeLanguage(language, error) {
    $.ajax({
        type: "GET",
        url: "/sicsdataanalytics/user/changeLanguage.htm",
        cache: false,
        data: encodeURI("language=" + language),
        success: function (result) {
            if (result === "ok") {
                location.reload();
            } else {
                sweetAlert.fire({
                    title: "ERROR, Oops...",
                    text: error,
                    icon: "error",
                    padding: 40
                });
            }
            return false;
        }
    });
}

function getDescriptionInLanguage(object, language) {
    let description = "";
    if (object && language) {
        if (language === "en") {
            if (notEmpty(object['nameEn'])) {
                description = object['nameEn'];
            } else if (notEmpty(object['descEn'])) {
                description = object['descEn'];
            }
        } else if (language === "it") {
            if (notEmpty(object['name'])) {
                description = object['name'];
            } else if (notEmpty(object['desc'])) {
                description = object['desc'];
            }
        }
        if (empty(description) && notEmpty(object['knownName'])) {
            description = object['knownName'];
        }
    }
    return description;
}

function isOpposite(eventTypeId, tagTypeIds) {
    if (notEmpty(tagTypeIds)) {
        let opposite = false;
        if ($.isArray(tagTypeIds)) {
            tagTypeIds.forEach(function (tagTypeId) {
                if (oppositeTagType.has(parseInt(tagTypeId))) {
                    opposite = true;
                }
            });
        } else if (!isNaN(tagTypeIds)) {
            if (oppositeTagType.has(parseInt(tagTypeIds))) {
                opposite = true;
            }
        }

        return opposite;
    } else {
        return oppositeEventType.has(parseInt(eventTypeId));
    }
}

function highlightSearchInput() {
    let input = $("#navbar-search-input").val();
    if (input) {
        $("#navbar-search-result").find(".is-text").each(function (index, element) {
            // Split input on spaces and process each word individually.
            if (input.includes(" ")) {
                input.split(" ").forEach(word => highlightText(element, word));
            } else {
                highlightText(element, input);
            }
        });
    }
}

function highlightSearchPlayerInput() {
    let input = $("#navbar-search-player-input").val();
    if (input) {
        $("#navbar-search-player-result").find(".is-text").each(function (index, element) {
            // Split input on spaces and process each word individually.
            if (input.includes(" ")) {
                input.split(" ").forEach(word => highlightText(element, word));
            } else {
                highlightText(element, input);
            }
        });
    }
}

function highlightSearchTeamPreferredInput() {
    let input = $("#navbar-search-team-preferred-input").val();
    if (input) {
        $("#navbar-search-team-preferred-result").find(".is-text").each(function (index, element) {
            // Split input on spaces and process each word individually.
            if (input.includes(" ")) {
                input.split(" ").forEach(word => highlightText(element, word));
            } else {
                highlightText(element, input);
            }
        });
    }
}

function highlightText(node, searchTerm) {
    // If the node is a text node, replace the matching text.
    if (node.nodeType === Node.TEXT_NODE) {
        const regex = new RegExp(`(${searchTerm})`, "gi");
        const replacedText = node.textContent.replace(regex, '<mark>$1</mark>');
        if (replacedText !== node.textContent) {
            // Create a temporary element to parse the HTML string.
            const tempElem = document.createElement("div");
            tempElem.innerHTML = replacedText;
            while (tempElem.firstChild) {
                node.parentNode.insertBefore(tempElem.firstChild, node);
            }
            node.parentNode.removeChild(node);
        }
    } else if (node.nodeType === Node.ELEMENT_NODE && node.tagName !== "MARK") {
        // Avoid descending into existing <mark> tags.
        Array.from(node.childNodes).forEach(child => highlightText(child, searchTerm));
    }
}

function needToShowFilterMessage() {
    let filters = new Map();
    let invalidFilters = ["seasonid", "competitionid", "groupid", "teamid", "playerid", "totaltype", "eventtypeid", "tagtypeid", "personal-filter"];

    $("#filters-containter").find("label").each(function (index, element) {
        let selectElements = $(element).parent().find("select");
        if (selectElements && selectElements.length === 1) {
            let selectElement = selectElements[0];
            let selectElementId = $(selectElement).attr("id");
            let selectValue = $(selectElement).val();
            if (selectValue) {
                if ($.isArray(selectValue)) {
                    if (selectValue.length > 0) {
                        let selectValueTexts = [];
                        selectValue.forEach(function (value) {
                            let selectValueText = $(selectElement).find("option[value='" + value + "']").text();
                            if (selectValueText) {
                                selectValueText = selectValueText.replace("\n", "");
                                selectValueTexts.push(selectValueText);
                            }
                        });
                        let isValidFilter = true;
                        invalidFilters.forEach(function (filter) {
                            if (selectElementId.includes(filter)) {
                                isValidFilter = false;
                            }
                        });
                        if (isValidFilter) {
                            filters.set(selectElementId, selectValueTexts.join(", "));
                        }
                    }
                } else {
                    let selectValueText = $(selectElement).find("option[value='" + selectValue + "']").text();
                    if (selectValueText) {
                        selectValueText = selectValueText.replace("\n", "");
                        let isValidFilter = true;
                        invalidFilters.forEach(function (filter) {
                            if (selectElementId.includes(filter)) {
                                isValidFilter = false;
                            }
                        });
                        if (isValidFilter) {
                            filters.set(selectElementId, selectValueText);
                        }
                    }
                }
            }
        }
    });

    return filters.size > 0;
}

function getCorrectSeasonId(seasonId, competitionId) {
    return $.ajax({
        type: "GET",
        url: "/sicsdataanalytics/user/getCorrectSeasonId.htm",
        cache: false,
        data: encodeURI("seasonId=" + seasonId + "&competitionId=" + competitionId)
    });
}

function checkIfSoccermentCompetition(competitionId) {
    return soccermentCompetitionMap.has(parseInt(competitionId));
}