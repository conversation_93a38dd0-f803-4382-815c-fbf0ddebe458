/* Colori personalizzati */
:root {
    --orange-color: hsl(25, 100%, 50%);
    --section-bg-color: hsl(0, 0%, 96%);
    --bar-grey-color: hsl(0, 0%, 85%);
    --section-border-color: hsl(0, 0%, 80%);
}

@page {
    size: A4;
}

/*@media screen {
    #report {
        zoom: 2;
    }
}*/

@media print {
    .hide-on-print {
        display: none !important;
        height: 0 !important;
    }

    html, body {
        width: 210mm;
        height: 297mm;
    }
}

#report {
    max-width: 794px;
}

.p-05 {
    padding: .1562rem !important
}

.page {
    height: 1108px;
    padding-top: 15px;
    padding-right: 15px;
    padding-left: 15px;
    background-color: white !important;
}

.red {
    background-color: #EB5A10 !important;
}

.gray {
    background-color: darkgray !important;
}

.light-gray {
    background-color: lightgray !important;
}

.black {
    background-color: #000000 !important;
}

#offensiveIndexColumnChart {
    height: 70%;
}

#offensiveIndexTrendChart {
    height: 300px;
}

.redQuad {
    width: 50px;
    height: 100%;
}

.headerQuad, .mainHeaderQuad {
    width: 100%;
    height: 90px;
}

.headerQuad > div.red.redQuad > div, .mainHeaderQuad > div.red.redQuad > div {
    transform: rotate(-90deg);
    margin-right: -50px;
    margin-left: -50px;
}

.headerQuad > div.black {
    width: 390px;
    height: 5px;
    margin-left: 50px;
    margin-top: -5px;
    background-color: #000000 !important;
}

.headerQuad > div.second-black {
    width: 714px;
    height: 45px;
    margin-left: 50px;
    margin-top: -82px;
    background-color: #000000 !important;
}

.mainHeaderQuad > div.second-black {
    width: 714px;
    height: 45px;
    margin-left: 50px;
    margin-top: -90px;
    background-color: #000000 !important;
}

.headerQuad > div.red.second-red {
    width: 45px;
    height: 5px;
    margin-left: 440px;
    margin-top: -5px;
}

.headerQuad > p.first-p {
    margin-top: -43px;
    margin-left: 90px;
    font-size: 16px;
    font-weight: bold;
}

.headerQuad > img {
    max-width: 100%;
    height: auto;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    border: 0;
    vertical-align: middle;
}

.headerQuad > img.first-img {
    margin-top: -45px;
    margin-left: 50px;
}

.headerQuad > img.second-img, .mainHeaderQuad > img.second-img {
    margin-top: -35px;
    margin-left: 600px;
}

.headerQuad > p.second-p {
    margin-top: 23px;
    margin-left: 90px;
    font-size: 12px;
}

.headerQuad > p.third-p {
    margin-top: -93px;
    margin-left: 90px;
    font-size: 12px;
    font-weight: bold;
    color: white !important;
    max-width: 500px
}

.mainHeaderQuad > p.third-p {
    margin-top: -41px;
    margin-left: 90px;
    font-size: 12px;
    font-weight: bold;
    color: white !important;
    max-width: 500px
}

.table-th {
    border-bottom: 1px lightgray !important;
    border-top: 2px lightgray !important;
    border-right: 2px lightgray !important;
    border-left: 2px lightgray !important;
    font-size: 6px;
}

.table-column, .text-small {
    font-size: 6px;
}

.table-column-black {
    background-color: black !important;
    color: white !important;
}

.subtitle-black {
    background-color: black !important;
    width: 80%;
    height: 3px;
}

.subtitle-red {
    width: 20%;
    height: 3px;
    background-color: #EB5A10 !important;
}

.btn-print {
    position: absolute;
    right: 30%;
}

.py-05 {
    padding-top: .1562rem !important;
    padding-bottom: .1562rem !important
}

.w-65 {
    width: 65% !important;
}

.ms-05 {
    margin-left: 0.1562rem !important;
}

.mt-05 {
    margin-top: 0.1562rem !important;
}

.first-p-for-title {
    margin-top: -38px;
    margin-left: 60px;
    font-size: 16px;
    font-weight: bold;
    height: 20px;
}

.third-p-for-title {
    margin-top: -42px;
    margin-left: 90px;
    font-size: 12px;
    font-weight: bold;
    color: white!important;
    max-width: 500px;
}

.pdf-export .hide-on-print {
    display: none !important;
    height: 0 !important;
}

.vertical-text {
    writing-mode: sideways-lr;
}