@charset "UTF-8";:root,[data-color-theme=light] {
    --dark-success: #04694a;
    --dark-success-rgb: 4, 105, 74;
    --dark-warning: #ac5e31;
    --dark-warning-rgb: 172, 94, 49;
    --dark-danger: #a73030;
    --dark-danger-rgb: 167, 48, 48;
    --dark-yellow: #b39632;
    --dark-yellow-rgb: 179, 150, 50;

    --lime: #5bab56;
    --lime-rgb: 91, 171, 86;
    --light-yellow: #ffb400;
    --light-yellow-rgb: 255, 180, 0;
    --light-grey: #e9ecef;
    --light-grey-rgb: 233, 236, 239;
}

@media (max-width: 576px) {
    .min-vh-50-sm {
        min-height: 50vh !important;
    }
}

@media (max-width: 768px) {
    .min-vh-50-md {
        min-height: 50vh !important;
    }
}

@media (max-width: 992px) {
    .min-vh-50-lg {
        min-height: 50vh !important;
    }
}

@media (max-width: 1200px) {
    .min-vh-50-xl {
        min-height: 50vh !important;
    }
}

.positional-block {
    width: 33.33%;
    height: 20%;
}

.positional-block > span {
    opacity: 0.85;
}

.positional-block:hover {
    cursor: pointer;
    background-color: rgba(255, 255, 255, 0.4);
}

#select2-filter-fixtureid-results > li {
    font-size: var(--body-font-size-sm)!important;
}

.hr {
    margin: .3125rem!important;
    color: inherit;
    border: 0;
    border-top: var(--border-width) solid;
    opacity: .25;
    width: 100%;
}

.is-positional-filter {
    cursor: pointer;
}

.status-indicator-container-image {
    --indicator-size: 0!important;
}

.text-white > .select2-selection__arrow {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e") !important;
}

.vh-80 {
    height: 80vh;
}

.dataTable thead .sorting {
    padding-right: 0!important;
    padding-left: 0!important
}

#container {
    background-color: #f1f4f9!important;
}

.icon-small {
    font-size: 16px!important;
}

.bg-danger.text-white > span > span.select2-selection__placeholder {
    color: white;
}

.animated.pulse {
    animation: pulse 3s infinite 0s!important;
}

.outline-success {
    border: 1px solid transparent!important;
    outline-style: solid;
    outline-color: var(--success);
}

.outline-info {
    border: 1px solid transparent!important;
    outline-style: solid;
    outline-color: var(--info);
}

.outline-warning {
    border: 1px solid transparent!important;
    outline-style: solid;
    outline-color: var(--warning);
}

.outline-danger {
    border: 1px solid transparent!important;
    outline-style: solid;
    outline-color: var(--danger);
}

.ui-blockui {
    position : fixed;
}

.select2-results__option, .multiselect-item.dropdown-item {
    padding-top: 0!important;
    padding-bottom: 0!important;
    font-size: 13px!important;
}

div.multiselect-filter > div.form-control-feedback.form-control-feedback-left.input-group > input {
    padding-right: calc(var(--input-padding-x))!important;
    padding-left: calc(var(--input-padding-x))!important;
}

.select2-results__group {
    margin-bottom: 0!important;
}

.min-vh-20 {
    min-height: 20vh;
}

.min-vh-25 {
    min-height: 25vh;
}

.min-vh-30 {
    min-height: 30vh;
}

.min-vh-35 {
    min-height: 35vh;
}

.min-vh-50 {
    min-height: 50vh;
}

.vh-40 {
    height: 40vh;
}

.image-shadow {
    object-fit: contain;
    filter: drop-shadow(0 0 5px #adadad) drop-shadow(0 0 5px #adadad);
}

.image-shadow-sm {
    object-fit: contain;
    filter: drop-shadow(0 0 2px #adadad) drop-shadow(0 0 2px #adadad);
}

.image-shadow-xs {
    object-fit: contain;
    filter: drop-shadow(0 0 1px #adadad) drop-shadow(0 0 1px #adadad);
}

.bg-dark-success {
    --bg-opacity: 1;
    background-color: rgba(var(--dark-success-rgb),var(--bg-opacity))!important
}

.text-dark-success {
    --text-opacity: 1;
    color: rgba(var(--dark-success-rgb),var(--text-opacity))!important
}

.text-dark-warning {
    --text-opacity: 1;
    color: rgba(var(--dark-warning-rgb),var(--text-opacity))!important
}

.text-dark-danger {
    --text-opacity: 1;
    color: rgba(var(--dark-danger-rgb),var(--text-opacity))!important
}

.text-dark-yellow {
    --text-opacity: 1;
    color: rgba(var(--dark-yellow-rgb),var(--text-opacity))!important
}

.bg-lime {
    --bg-opacity: 1;
    background-color: rgba(var(--lime-rgb),var(--bg-opacity))!important
}

button:not(.border-success) > span.multiselect-selected-text {
    font-size: var(--body-font-size-sm)!important;
    line-height: var(--body-line-height-sm)!important;
    color: var(--gray-600)!important;
}

button.border-success > span.multiselect-selected-text {
    font-size: var(--body-font-size-sm)!important;
    line-height: var(--body-line-height-sm)!important;
    color: var(--s2-color)!important;
}

.multiselect-item.multiselect-group.dropdown-item-text {
    padding-top: 0!important;
    padding-bottom: 0!important;
}

.vh-50 {
    height: 50vh;
}

.vh-55 {
    height: 55vh;
}

.custom-tooltip {
    --bs-tooltip-bg: var(--bd-violet-bg);
    --bs-tooltip-color: var(--bs-white);
}