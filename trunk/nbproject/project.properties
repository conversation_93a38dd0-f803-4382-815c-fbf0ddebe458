annotation.processing.enabled=true
annotation.processing.enabled.in.editor=true
annotation.processing.processors.list=
annotation.processing.run.all.processors=true
annotation.processing.source.output=${build.generated.sources.dir}/ap-source-output
auxiliary.org-netbeans-modules-css-prep.less_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.less_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.less_2e_mappings=/less:/css
auxiliary.org-netbeans-modules-css-prep.sass_2e_compiler_2e_options=
auxiliary.org-netbeans-modules-css-prep.sass_2e_enabled=false
auxiliary.org-netbeans-modules-css-prep.sass_2e_mappings=/scss:/css
auxiliary.org-netbeans-modules-projectapi.jsf_2e_language=JSP
auxiliary.org-netbeans-modules-web-clientproject-api.js_2e_libs_2e_folder=js/i18n
build.classes.dir=${build.web.dir}/WEB-INF/classes
build.classes.excludes=**/*.java,**/*.form
build.dir=build
build.generated.dir=${build.dir}/generated
build.generated.sources.dir=${build.dir}/generated-sources
build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results
build.web.dir=${build.dir}/web
build.web.excludes=${build.classes.excludes}
client.urlPart=
compile.jsps=false
conf.dir=${source.root}/conf
debug.classpath=${build.classes.dir}:${javac.classpath}
debug.test.classpath=\
    ${run.test.classpath}
display.browser=false
dist.dir=dist
dist.ear.war=${dist.dir}/${war.ear.name}
dist.javadoc.dir=${dist.dir}/javadoc
dist.war=${dist.dir}/${war.name}
endorsed.classpath=
excludes=
file.reference.aopalliance.jar=web/WEB-INF/lib/aopalliance.jar
file.reference.apache-mime4j-0.6.jar=web/WEB-INF/lib/apache-mime4j-0.6.jar
file.reference.aws-java-sdk-1.11.48.jar=web/WEB-INF/lib/aws-java-sdk-1.11.48.jar
file.reference.cglib-2.2.jar=web/WEB-INF/lib/cglib-2.2.jar
file.reference.commons-beanutils-1.7.0.jar=web/WEB-INF/lib/commons-beanutils-1.7.0.jar
file.reference.commons-codec-1.6.jar=web/WEB-INF/lib/commons-codec-1.6.jar
file.reference.commons-collections-3.2.jar=web/WEB-INF/lib/commons-collections-3.2.jar
file.reference.commons-dbcp-1.2.2.jar=web/WEB-INF/lib/commons-dbcp-1.2.2.jar
file.reference.commons-fileupload-1.2.1.jar=web/WEB-INF/lib/commons-fileupload-1.2.1.jar
file.reference.commons-io-2.11.0.jar=web/WEB-INF/lib/commons-io-2.11.0.jar
file.reference.commons-lang-2.3.jar=web/WEB-INF/lib/commons-lang-2.3.jar
file.reference.commons-lang3-3.12.0.jar=web\\WEB-INF\\lib\\commons-lang3-3.12.0.jar
file.reference.commons-logging-1.2.jar=web/WEB-INF/lib/commons-logging-1.2.jar
file.reference.commons-pool-1.3.jar=web/WEB-INF/lib/commons-pool-1.3.jar
file.reference.displaytag-1.2.jar=web/WEB-INF/lib/displaytag-1.2.jar
file.reference.fluent-hc-4.3.3.jar=web/WEB-INF/lib/fluent-hc-4.3.3.jar
file.reference.gson-1.5.jar=web/WEB-INF/lib/gson-1.5.jar
file.reference.htmlcompressor-1.5.2.jar=web\\WEB-INF\\lib\\htmlcompressor-1.5.2.jar
file.reference.httpclient-4.5.2.jar=web/WEB-INF/lib/httpclient-4.5.2.jar
file.reference.httpclient-cache-4.3.3.jar=web/WEB-INF/lib/httpclient-cache-4.3.3.jar
file.reference.httpcore-4.4.4.jar=web/WEB-INF/lib/httpcore-4.4.4.jar
file.reference.httpmime-4.5.2.jar=web/WEB-INF/lib/httpmime-4.5.2.jar
file.reference.icu4j-73_2.jar=web/WEB-INF/lib/icu4j-73_2.jar
file.reference.imgscalr-lib-4.2.jar=web\\WEB-INF\\lib\\imgscalr-lib-4.2.jar
file.reference.jackson-annotations-2.8.4.jar=web/WEB-INF/lib/jackson-annotations-2.8.4.jar
file.reference.jackson-annotations-2.9.9.jar=web/WEB-INF/lib/jackson-annotations-2.9.9.jar
file.reference.jackson-core-2.8.4.jar=web/WEB-INF/lib/jackson-core-2.8.4.jar
file.reference.jackson-databind-2.8.4.jar=web/WEB-INF/lib/jackson-databind-2.8.4.jar
file.reference.jcip-annotations.jar=web/WEB-INF/lib/jcip-annotations.jar
file.reference.jdom-2.0.5.jar=web/WEB-INF/lib/jdom-2.0.5.jar
file.reference.jna-4.1.0.jar=web/WEB-INF/lib/jna-4.1.0.jar
file.reference.jna-platform-4.1.0.jar=web/WEB-INF/lib/jna-platform-4.1.0.jar
file.reference.jsch-0.1.55.jar=web/WEB-INF/lib/jsch-0.1.55.jar
file.reference.jstl.jar=web/WEB-INF/lib/jstl.jar
file.reference.log4j-1.2.12.jar=web/WEB-INF/lib/log4j-1.2.12.jar
file.reference.mail.jar=web/WEB-INF/lib/mail.jar
file.reference.mongo-java-driver-3.12.11.jar=web/WEB-INF/lib/mongo-java-driver-3.12.11.jar
file.reference.mybatis-3.0.5.jar=web/WEB-INF/lib/mybatis-3.0.5.jar
file.reference.mybatis-spring-1.0.1.jar=web/WEB-INF/lib/mybatis-spring-1.0.1.jar
file.reference.mysql-connector-java-5.1.8-bin.jar=web/WEB-INF/lib/mysql-connector-java-5.1.8-bin.jar
file.reference.org.springframework.aop-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.aop-3.0.2.RELEASE.jar
file.reference.org.springframework.asm-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.asm-3.0.2.RELEASE.jar
file.reference.org.springframework.aspects-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.aspects-3.0.2.RELEASE.jar
file.reference.org.springframework.beans-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.beans-3.0.2.RELEASE.jar
file.reference.org.springframework.context-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.context-3.0.2.RELEASE.jar
file.reference.org.springframework.context.support-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.context.support-3.0.2.RELEASE.jar
file.reference.org.springframework.core-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.core-3.0.2.RELEASE.jar
file.reference.org.springframework.expression-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.expression-3.0.2.RELEASE.jar
file.reference.org.springframework.instrument-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.instrument-3.0.2.RELEASE.jar
file.reference.org.springframework.instrument.tomcat-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.instrument.tomcat-3.0.2.RELEASE.jar
file.reference.org.springframework.jdbc-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.jdbc-3.0.2.RELEASE.jar
file.reference.org.springframework.jms-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.jms-3.0.2.RELEASE.jar
file.reference.org.springframework.orm-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.orm-3.0.2.RELEASE.jar
file.reference.org.springframework.oxm-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.oxm-3.0.2.RELEASE.jar
file.reference.org.springframework.transaction-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.transaction-3.0.2.RELEASE.jar
file.reference.org.springframework.web-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.web-3.0.2.RELEASE.jar
file.reference.org.springframework.web.servlet-3.0.2.RELEASE.jar=web/WEB-INF/lib/org.springframework.web.servlet-3.0.2.RELEASE.jar
file.reference.signpost-core-1.1.jar=web/WEB-INF/lib/signpost-core-1.1.jar
file.reference.spring-boot-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-2.7.18.jar
file.reference.spring-boot-autoconfigure-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-autoconfigure-2.7.18.jar
file.reference.spring-boot-starter-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-2.7.18.jar
file.reference.spring-boot-starter-json-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-json-2.7.18.jar
file.reference.spring-boot-starter-logging-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-logging-2.7.18.jar
file.reference.spring-boot-starter-tomcat-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-tomcat-2.7.18.jar
file.reference.spring-boot-starter-web-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-web-2.7.18.jar
file.reference.spring-boot-starter-websocket-2.7.18.jar=web/WEB-INF/lib/websocket/spring-boot-starter-websocket-2.7.18.jar
file.reference.spring-jcl-5.3.31.jar=web/WEB-INF/lib/websocket/spring-jcl-5.3.31.jar
file.reference.spring-messaging-5.3.31.jar=web/WEB-INF/lib/websocket/spring-messaging-5.3.31.jar
file.reference.spring-mobile-device-1.0.0.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-mobile-device-1.0.0.RELEASE.jar
file.reference.spring-security-aspects-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-aspects-3.1.3.RELEASE.jar
file.reference.spring-security-cas-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-cas-3.1.3.RELEASE.jar
file.reference.spring-security-config-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-config-3.1.3.RELEASE.jar
file.reference.spring-security-core-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-core-3.1.3.RELEASE.jar
file.reference.spring-security-crypto-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-crypto-3.1.3.RELEASE.jar
file.reference.spring-security-ldap-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-ldap-3.1.3.RELEASE.jar
file.reference.spring-security-openid-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-openid-3.1.3.RELEASE.jar
file.reference.spring-security-remoting-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-remoting-3.1.3.RELEASE.jar
file.reference.spring-security-samples-contacts-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-samples-contacts-3.1.3.RELEASE.jar
file.reference.spring-security-samples-tutorial-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-samples-tutorial-3.1.3.RELEASE.jar
file.reference.spring-security-taglibs-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-taglibs-3.1.3.RELEASE.jar
file.reference.spring-security-web-3.1.3.RELEASE.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\spring-security-web-3.1.3.RELEASE.jar
file.reference.spring-webmvc-5.3.31.jar=web/WEB-INF/lib/websocket/spring-webmvc-5.3.31.jar
file.reference.spring-websocket-5.3.31.jar=web/WEB-INF/lib/websocket/spring-websocket-5.3.31.jar
file.reference.standard.jar=C:\\lavori\\sicsdataaccess2025\\trunk\\web\\WEB-INF\\lib\\standard.jar
file.reference.tomcat-embed-core-9.0.83.jar=web/WEB-INF/lib/websocket/tomcat-embed-core-9.0.83.jar
file.reference.tomcat-embed-el-9.0.83.jar=web/WEB-INF/lib/websocket/tomcat-embed-el-9.0.83.jar
file.reference.tomcat-embed-websocket-9.0.83.jar=web/WEB-INF/lib/websocket/tomcat-embed-websocket-9.0.83.jar
file.reference.xmlrpc-2.0.1.jar=web/WEB-INF/lib/xmlrpc-2.0.1.jar
file.reference.zip4j_1.3.2.jar=web/WEB-INF/lib/zip4j_1.3.2.jar
includes=**
j2ee.compile.on.save=true
j2ee.copy.static.files.on.save=true
j2ee.deploy.on.save=true
j2ee.platform=1.5
j2ee.platform.classpath=${j2ee.server.home}/bin/tomcat-juli.jar:${j2ee.server.home}/lib/annotations-api.jar:${j2ee.server.home}/lib/catalina-ant.jar:${j2ee.server.home}/lib/catalina-ha.jar:${j2ee.server.home}/lib/catalina-storeconfig.jar:${j2ee.server.home}/lib/catalina-tribes.jar:${j2ee.server.home}/lib/catalina.jar:${j2ee.server.home}/lib/ecj-4.6.3.jar:${j2ee.server.home}/lib/el-api.jar:${j2ee.server.home}/lib/jasper-el.jar:${j2ee.server.home}/lib/jasper.jar:${j2ee.server.home}/lib/jaspic-api.jar:${j2ee.server.home}/lib/jsp-api.jar:${j2ee.server.home}/lib/servlet-api.jar:${j2ee.server.home}/lib/tomcat-api.jar:${j2ee.server.home}/lib/tomcat-coyote.jar:${j2ee.server.home}/lib/tomcat-dbcp.jar:${j2ee.server.home}/lib/tomcat-i18n-de.jar:${j2ee.server.home}/lib/tomcat-i18n-es.jar:${j2ee.server.home}/lib/tomcat-i18n-fr.jar:${j2ee.server.home}/lib/tomcat-i18n-ja.jar:${j2ee.server.home}/lib/tomcat-i18n-ko.jar:${j2ee.server.home}/lib/tomcat-i18n-ru.jar:${j2ee.server.home}/lib/tomcat-i18n-zh-CN.jar:${j2ee.server.home}/lib/tomcat-jdbc.jar:${j2ee.server.home}/lib/tomcat-jni.jar:${j2ee.server.home}/lib/tomcat-util-scan.jar:${j2ee.server.home}/lib/tomcat-util.jar:${j2ee.server.home}/lib/tomcat-websocket.jar:${j2ee.server.home}/lib/websocket-api.jar
j2ee.platform.classpath=${j2ee.server.home}/bin/tomcat-juli.jar:${j2ee.server.home}/lib/annotations-api.jar:${j2ee.server.home}/lib/catalina-ant.jar:${j2ee.server.home}/lib/catalina-ha.jar:${j2ee.server.home}/lib/catalina-storeconfig.jar:${j2ee.server.home}/lib/catalina-tribes.jar:${j2ee.server.home}/lib/catalina.jar:${j2ee.server.home}/lib/ecj-4.6.3.jar:${j2ee.server.home}/lib/el-api.jar:${j2ee.server.home}/lib/jasper-el.jar:${j2ee.server.home}/lib/jasper.jar:${j2ee.server.home}/lib/jaspic-api.jar:${j2ee.server.home}/lib/jsp-api.jar:${j2ee.server.home}/lib/servlet-api.jar:${j2ee.server.home}/lib/tomcat-api.jar:${j2ee.server.home}/lib/tomcat-coyote.jar:${j2ee.server.home}/lib/tomcat-dbcp.jar:${j2ee.server.home}/lib/tomcat-i18n-de.jar:${j2ee.server.home}/lib/tomcat-i18n-es.jar:${j2ee.server.home}/lib/tomcat-i18n-fr.jar:${j2ee.server.home}/lib/tomcat-i18n-ja.jar:${j2ee.server.home}/lib/tomcat-i18n-ko.jar:${j2ee.server.home}/lib/tomcat-i18n-ru.jar:${j2ee.server.home}/lib/tomcat-i18n-zh-CN.jar:${j2ee.server.home}/lib/tomcat-jdbc.jar:${j2ee.server.home}/lib/tomcat-jni.jar:${j2ee.server.home}/lib/tomcat-util-scan.jar:${j2ee.server.home}/lib/tomcat-util.jar:${j2ee.server.home}/lib/tomcat-websocket.jar:${j2ee.server.home}/lib/websocket-api.jar
j2ee.server.type=Tomcat
jar.compress=false
javac.classpath=\
    ${file.reference.aopalliance.jar}:\
    ${file.reference.apache-mime4j-0.6.jar}:\
    ${file.reference.cglib-2.2.jar}:\
    ${file.reference.commons-beanutils-1.7.0.jar}:\
    ${file.reference.commons-codec-1.6.jar}:\
    ${file.reference.commons-collections-3.2.jar}:\
    ${file.reference.commons-dbcp-1.2.2.jar}:\
    ${file.reference.commons-fileupload-1.2.1.jar}:\
    ${file.reference.commons-lang-2.3.jar}:\
    ${file.reference.commons-pool-1.3.jar}:\
    ${file.reference.displaytag-1.2.jar}:\
    ${file.reference.fluent-hc-4.3.3.jar}:\
    ${file.reference.gson-1.5.jar}:\
    ${file.reference.httpclient-cache-4.3.3.jar}:\
    ${file.reference.jcip-annotations.jar}:\
    ${file.reference.jdom-2.0.5.jar}:\
    ${file.reference.jstl.jar}:\
    ${file.reference.log4j-1.2.12.jar}:\
    ${file.reference.mail.jar}:\
    ${file.reference.mybatis-3.0.5.jar}:\
    ${file.reference.mybatis-spring-1.0.1.jar}:\
    ${file.reference.mysql-connector-java-5.1.8-bin.jar}:\
    ${file.reference.org.springframework.aop-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.asm-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.aspects-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.beans-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.context-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.context.support-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.core-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.expression-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.instrument-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.instrument.tomcat-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.jdbc-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.jms-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.orm-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.oxm-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.transaction-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.web-3.0.2.RELEASE.jar}:\
    ${file.reference.org.springframework.web.servlet-3.0.2.RELEASE.jar}:\
    ${file.reference.signpost-core-1.1.jar}:\
    ${file.reference.spring-mobile-device-1.0.0.RELEASE.jar}:\
    ${file.reference.spring-security-aspects-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-cas-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-config-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-core-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-crypto-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-ldap-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-openid-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-remoting-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-samples-contacts-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-samples-tutorial-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-taglibs-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-web-3.1.3.RELEASE.jar}:\
    ${file.reference.standard.jar}:\
    ${file.reference.zip4j_1.3.2.jar}:\
    ${file.reference.commons-logging-1.2.jar}:\
    ${file.reference.httpclient-4.5.2.jar}:\
    ${file.reference.httpcore-4.4.4.jar}:\
    ${file.reference.httpmime-4.5.2.jar}:\
    ${file.reference.jackson-annotations-2.8.4.jar}:\
    ${file.reference.jackson-core-2.8.4.jar}:\
    ${file.reference.jackson-databind-2.8.4.jar}:\
    ${file.reference.jna-4.1.0.jar}:\
    ${file.reference.jna-platform-4.1.0.jar}:\
    ${file.reference.aws-java-sdk-1.11.48.jar}:\
    ${file.reference.xmlrpc-2.0.1.jar}:\
    ${file.reference.icu4j-73_2.jar}:\
    ${file.reference.commons-io-2.11.0.jar}:\
    ${file.reference.mongo-java-driver-3.12.11.jar}:\
    ${file.reference.jsch-0.1.55.jar}:\
    ${file.reference.jackson-annotations-2.8.4.jar}:\
    ${file.reference.jackson-annotations-2.9.9.jar}:\
    ${file.reference.jackson-core-2.8.4.jar}:\
    ${file.reference.jackson-databind-2.8.4.jar}:\
    ${file.reference.commons-lang3-3.12.0.jar}:\
    ${file.reference.spring-boot-2.7.18.jar}:\
    ${file.reference.spring-boot-autoconfigure-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-json-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-logging-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-tomcat-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-web-2.7.18.jar}:\
    ${file.reference.spring-boot-starter-websocket-2.7.18.jar}:\
    ${file.reference.spring-jcl-5.3.31.jar}:\
    ${file.reference.spring-messaging-5.3.31.jar}:\
    ${file.reference.spring-webmvc-5.3.31.jar}:\
    ${file.reference.spring-websocket-5.3.31.jar}:\
    ${file.reference.tomcat-embed-core-9.0.83.jar}:\
    ${file.reference.tomcat-embed-el-9.0.83.jar}:\
    ${file.reference.tomcat-embed-websocket-9.0.83.jar}:\
    ${file.reference.htmlcompressor-1.5.2.jar}:\
    ${file.reference.imgscalr-lib-4.2.jar}:\
    ${file.reference.spring-mobile-device-1.0.0.RELEASE.jar}:\
    ${file.reference.spring-security-core-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-web-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-aspects-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-cas-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-config-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-crypto-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-ldap-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-openid-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-remoting-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-samples-contacts-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-samples-tutorial-3.1.3.RELEASE.jar}:\
    ${file.reference.spring-security-taglibs-3.1.3.RELEASE.jar}:\
    ${file.reference.standard.jar}
# Space-separated list of extra javac options
javac.compilerargs=
javac.debug=true
javac.deprecation=false
javac.processorpath=\
    ${javac.classpath}
javac.source=1.7
javac.target=1.7
javac.test.classpath=\
    ${javac.classpath}:\
    ${build.classes.dir}:\
    ${libs.junit.classpath}:\
    ${libs.junit_4.classpath}
javac.test.processorpath=${javac.test.classpath}
javadoc.additionalparam=
javadoc.author=false
javadoc.encoding=${source.encoding}
javadoc.noindex=false
javadoc.nonavbar=false
javadoc.notree=false
javadoc.preview=true
javadoc.private=false
javadoc.splitindex=true
javadoc.use=true
javadoc.version=false
javadoc.windowtitle=
jaxbwiz.endorsed.dirs="${netbeans.home}/../ide12/modules/ext/jaxb/api"
jspcompilation.classpath=${jspc.classpath}:${javac.classpath}
lib.dir=${web.docbase.dir}/WEB-INF/lib
no.dependencies=true
persistence.xml.dir=${conf.dir}
platform.active=default_platform
resource.dir=setup
run.test.classpath=\
    ${javac.test.classpath}:\
    ${build.test.classes.dir}
# Space-separated list of JVM arguments used when running a class with a main method or a unit test
# (you may also define separate properties like run-sys-prop.name=value instead of -Dname=value):
runmain.jvmargs=
source.encoding=UTF-8
source.root=src
src.src.dir=src
war.content.additional=
war.ear.name=sicsdataanalytics.war
war.name=sicsdataanalytics.war
web.docbase.dir=web
webinf.dir=web/WEB-INF
