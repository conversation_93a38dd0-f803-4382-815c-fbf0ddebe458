basic.empty.showtable=false
basic.show.header=true

# page | list
sort.amount=page

export.amount=list
export.decorated=true

css.tr.even=ui-widget-content jqgrow
css.tr.odd=ui-widget-content jqgrow
css.th.sorted=ui-state-default ui-th-column grid_resize mcPagerLink
css.th.ascending=ui-state-default ui-th-column grid_resize mcPagerLink
css.th.descending=ui-state-default ui-th-column grid_resize mcPagerLink
css.th.sortable=ui-state-default ui-th-column grid_resize mcPagerLink
css.table=ui-state-default ui-jqgrid displayTagTable

# factory classes for extensions
factory.requestHelper=org.displaytag.util.DefaultRequestHelperFactory

# locale provider (Struts provider by default)
#locale.provider=org.displaytag.localization.I18nStrutsAdapter
local.provider=org.displaytag.localization.I18nWebworkAdapter

# locale.resolver (nothing by default, simply use locale from request)
locale.resolver=org.displaytag.localization.I18nSpringAdapter

export.banner=<div>esportazione: {0}</div>
export.banner.sepchar=

export.types=excel pdf

export.excel.class=org.displaytag.export.ExcelView
export.pdf.class=org.displaytag.export.PdfView

export.excel=true
export.excel.label=excel
export.excel.include_header=true
export.excel.filename=

export.pdf=true
export.pdf.label=pdf
export.pdf.include_header=true
export.pdf.filename=

# messages

basic.empty.showtable=true
basic.msg.empty_list=
basic.msg.empty_list_row=
error.msg.invalid_page=pagina non valida

paging.banner.group_size=23
paging.banner.placement=bottom

paging.banner.item_name=Elemento
paging.banner.items_name=Elementi

paging.banner.no_items_found=
paging.banner.one_item_found=
paging.banner.all_items_found=
paging.banner.some_items_found=

paging.banner.full=&nbsp;<a href="{1}" class="ui-state-default mcPagerLink"><<</a><a class="ui-state-default mcPagerLink" href="{2}"><</a>{0}<a class="mcPagerLink ui-state-default" href="{3}">></a><a class="mcPagerLink ui-state-default" href="{4}">>></a>
paging.banner.first=&nbsp;<a href="{1}" class="ui-state-default mcPagerLink"><<</a><a class="ui-state-default mcPagerLink" href="{2}"><</a>{0}<a class="mcPagerLink ui-state-default" href="{3}">></a><a class="mcPagerLink ui-state-default" href="{4}">>></a>
paging.banner.last=&nbsp;<a href="{1}" class="ui-state-default mcPagerLink"><<</a><a class="ui-state-default mcPagerLink" href="{2}"><</a>{0}<a class="mcPagerLink ui-state-default" href="{3}">></a><a class="mcPagerLink ui-state-default" href="{4}">>></a>
#paging.banner.first=&nbsp;{0}<a class="mcPagerLink ui-state-default" href="{3}">></a><a class="mcPagerLink ui-state-default" href="{4}">>></a>
#paging.banner.last=&nbsp;<a href="{1}" class="ui-state-default mcPagerLink"><<</a><a class="ui-state-default mcPagerLink" href="{2}"><</a>{0}
paging.banner.page.link=<a class="ui-state-default mcPagerLink" href="{1}">{0}</a>
paging.banner.page.selected=<a class="ui-state-active mcPagerLink" href="{1}">{0}</a>
paging.banner.onepage=
paging.banner.page.separator=

# unused
save.excel.banner=<a href="{0}" rel="external">salvati ({1} bytes)</a>
save.excel.name=export.xls
