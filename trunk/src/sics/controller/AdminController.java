package sics.controller;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.domain.Log;
import sics.domain.User;
import sics.domain.UserLogWrapper;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;

@Controller
@RequestMapping("/admin")
public class AdminController extends BaseController {

    //inserire le location del mapping qui sotto come stringhe
    private final static String PAGE_HOME = "admin/home.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/home")
    public String home(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "agentId", required = false) Long agentId) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_403);
        }

        List<User> agents = mService.getVtigerAgents();
        List<Long> allowedUserIds = null;
        // DEMO SICS | SICS
        if ((curUser.getGroupsetId() != 1L && curUser.getGroupsetId() != 2L) || agentId != null) {
            if (agentId == null) {
                for (User agent : agents) {
                    if (agent.getFirstName().equals(curUser.getFirstName()) && agent.getLastName().equals(curUser.getLastName())) {
                        agentId = agent.getId();
                        break;
                    }
                }
            }

            if (agentId == null) {
                return pageRedirect(PAGE_403);
            }
            allowedUserIds = new ArrayList<>();
            List<User> allowedUsers = mService.getVtigerAgentClients(agentId);
            if (allowedUsers != null && !allowedUsers.isEmpty()) {
                for (User user : allowedUsers) {
                    allowedUserIds.add(user.getId());
                }
            }
        }
        model.addAttribute("mAgents", agents);
        model.addAttribute("mAgentId", agentId);

        List<Document> sessionLogs = MongoHelper.getLogs(allowedUserIds);
        if (sessionLogs != null && !sessionLogs.isEmpty()) {
            Map<Long, UserLogWrapper> userLogMap = new LinkedHashMap<>();
            for (Document sessionLog : sessionLogs) {
                if (sessionLog.containsKey("userId") && sessionLog.containsKey("sessionCreation") && sessionLog.containsKey("sessionDuration") && sessionLog.containsKey("totalCharts")) {
                    Long userId = sessionLog.getLong("userId");
                    userLogMap.putIfAbsent(userId, new UserLogWrapper());

                    UserLogWrapper wrapper = userLogMap.get(userId);
                    Log userSession = new Log();
                    userSession.setSessionCreation(sessionLog.getDate("sessionCreation"));
                    userSession.setSessionDuration(sessionLog.getString("sessionDuration"));
                    userSession.setTotalCharts(sessionLog.getInteger("totalCharts"));
                    userSession.setPageCounterMap(new HashMap<String, Integer>());
                    for (String key : sessionLog.keySet()) {
                        if (StringUtils.startsWith(key, "/")) {
                            if (StringUtils.contains(key, "/user/") || StringUtils.contains(key, "/team/") || StringUtils.contains(key, "/player/")) {
                                if (!StringUtils.endsWith(key, "home_htm")) {
                                    Integer amount = sessionLog.getInteger(key);
                                    if (amount == null) {
                                        amount = 1;
                                    }
                                    String formattedName = key;
                                    List<String> parts = Arrays.asList(StringUtils.split(key, "/"));
                                    if (parts.size() >= 3) {
                                        formattedName = StringUtils.capitalize(parts.get(2).replace("_htm", "")) + " (" + parts.get(1) + ")";
                                    }

                                    userSession.getPageCounterMap().put(formattedName, userSession.getPageCounterMap().getOrDefault(formattedName, 0) + amount);
                                }
                            }
                        }
                    }

                    if (wrapper.getLastLoginDate() == null || wrapper.getLastLoginDate().before(userSession.getSessionCreation())) {
                        wrapper.setLastLoginDate(userSession.getSessionCreation());
                        if (sessionLog.containsKey("groupsetName")) {
                            wrapper.setGroupsetName(sessionLog.getString("groupsetName"));
                        }
                    }
                    wrapper.getSessions().add(userSession);
                    wrapper.addTotalLogins(1);
                    wrapper.addTotalCharts(userSession.getTotalCharts());
                }
            }

            List<User> users = mService.getUsersWithDetails(new ArrayList<>(userLogMap.keySet()));
            Map<Long, User> userMap = new HashMap<>();
            for (User user : users) {
                userMap.put(user.getId(), user);
            }
            for (Long userId : userLogMap.keySet()) {
                UserLogWrapper wrapper = userLogMap.get(userId);
                wrapper.setUser(userMap.get(userId));
            }
            model.addAttribute("mUsersLogs", userLogMap);
        }

        return PAGE_HOME;
    }
}
