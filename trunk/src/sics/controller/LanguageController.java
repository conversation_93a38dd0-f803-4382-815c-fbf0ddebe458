package sics.controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.bson.Document;
import sics.domain.Translation;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;

/**
 *
 * <AUTHOR>
 */
public class LanguageController {

    public static String syncronizeMongodb(Integer type, String baseProjectPath) {
        String result = "ko";
        if (StringUtils.isNotBlank(baseProjectPath)) {
            Map<String, Translation> translations = new HashMap<>();

            File folder = null;
            if (type == 0) {
                // sicstv
                String baseFolder = baseProjectPath + "/trunk/src/pers/sicstv";
                folder = new File(baseFolder);
            } else if (type == 1) {
                // videomatch

            }

            if (folder != null && folder.exists()) {
                for (File file : folder.listFiles()) {
                    if (FilenameUtils.getExtension(file.getName()).equalsIgnoreCase("properties")) {
                        try (InputStream input = new FileInputStream(file.getPath())) {
                            // Carica il file .properties
                            Properties properties = new Properties();
                            properties.load(input);
                            String language = FilenameUtils.getBaseName(file.getName()).replace("ApplicationResources_", "");
                            if (StringUtils.isNotBlank(language) && language.length() == 2) { // it, en, fr, es...
                                for (String key : properties.stringPropertyNames()) {
                                    if (StringUtils.isNotBlank(key)) {
                                        translations.putIfAbsent(key, new Translation());
                                        translations.get(key).setType(type);
                                        translations.get(key).setKey(key);
                                        String value = properties.getProperty(key);
                                        if (StringUtils.isNotBlank(value)) {
                                            if (StringUtils.equalsIgnoreCase(language, "it")) {
                                                translations.get(key).setIt(value);
                                            } else if (StringUtils.equalsIgnoreCase(language, "en")) {
                                                translations.get(key).setEn(value);
                                            } else if (StringUtils.equalsIgnoreCase(language, "fr")) {
                                                translations.get(key).setFr(value);
                                            } else if (StringUtils.equalsIgnoreCase(language, "es")) {
                                                translations.get(key).setEs_ES(value);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (IOException ex) {
                            GlobalHelper.reportError(ex);
                        }
                    }
                }
            }

            if (!translations.isEmpty()) {
                List<Document> documents = new ArrayList<>();
                for (Translation translation : translations.values()) {
                    documents.add(MongoHelper.toDocument(translation));
                }

                if (!documents.isEmpty()) {
                    MongoHelper.deleteAllDocuments("translations");
                    MongoHelper.insertDocuments("translations", documents);
                    result = "ok";
                }
            }
        }

        return result;
    }

    public static String getTableData(Integer type) {
        List<Translation> translations = MongoHelper.getTranslations(type);

//        Gson gson = new Gson();
//        JsonArray jsonArray = new JsonArray();
//        for (Translation translation : translations) {
//            JsonObject jsonObject = new JsonObject();
//            jsonObject.add("data", gson.toJsonTree(translation));
//            jsonArray.add(jsonObject);
//        }
//        return jsonArray.toString();
        StringBuilder builder = new StringBuilder();
        JsonArray jsonArray = new JsonArray();
        for (Translation translation : translations) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("key", translation.getKey());

            if (StringUtils.isNotBlank(translation.getIt())) {
                jsonObject.addProperty("it", translation.getIt());
            }
            if (StringUtils.isNotBlank(translation.getEn())) {
                jsonObject.addProperty("en", translation.getEn());
            }
            if (StringUtils.isNotBlank(translation.getFr())) {
                jsonObject.addProperty("fr", translation.getFr());
            }
            if (StringUtils.isNotBlank(translation.getEs_ES())) {
                jsonObject.addProperty("es", translation.getEs_ES());
            }
            jsonArray.add(jsonObject);

            if (translation.getRowIndex() != null && StringUtils.isNotBlank(translation.getChangedColumnsIndex())) {
                builder.append("[").append(translation.getRowIndex().toString()).append("|").append(translation.getChangedColumnsIndex()).append("]").append("#");
            }
        }
        if (builder.length() > 0) {
            builder.deleteCharAt(builder.length() - 1);
        }

        // se non c'è nulla metto riga vuota altrimenti non posso fare nulla
        if (jsonArray.size() == 0) {
            JsonObject jsonObject = new JsonObject();
            jsonArray.add(jsonObject);
        }
        return jsonArray.toString() + "-_-" + builder.toString();
    }
}
