package sics.controller.team;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.Fixture;
import sics.domain.Point;
import sics.domain.TableRow;
import sics.domain.TeamCareerItem;
import sics.domain.TeamData;
import sics.domain.TeamPlayerData;
import sics.domain.User;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.ModuleHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/overview")
public class TeamOverviewController extends BaseController {

    private final static String PAGE_OVERVIEW_CONTENT = "team-data/overview-content.jsp";
    private final static UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        List<TableRow> rows = new ArrayList<>();
        List<TableRow> sufferedRows = new ArrayList<>();
        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("teamId")) {
                model.addAttribute("mValid", true);
                Long teamId = Long.valueOf(params.get("teamId").toString());

                params.put("type", "team");
                params.remove("teamId");
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_EVENT_TYPE_TAG_TYPE);
                if (!result.isEmpty()) {
                    final DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                    model.addAttribute("mDisplayType", displayType.toString());
                    if (displayType.equals(DisplayType.P90)) {
                        Utils.calculateP90V2(result, params);
                    } else if (displayType.equals(DisplayType.TOUCHES)) {
                        Utils.calculate100Touches(result, params);
                    } else if (displayType.equals(DisplayType.AVERAGE)) {
                        Utils.calculateAverage(result, params);
                    }
                    Map<Long, Map<String, Map<String, List<DocumentRow>>>> groupedByEventTagZone = new HashMap<>();
                    for (DocumentRow row : result) {
                        groupedByEventTagZone.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                        groupedByEventTagZone.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                        groupedByEventTagZone.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                        groupedByEventTagZone.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                    }

//                    Map<Long, Long> teamTotalMinutes = null;
//                    if (!isTotals) {
//                        Map<String, Object> paramsClone = new HashMap<>(params);
//                        paramsClone.remove("eventTypeIds");
//
//                        List<Long> fixtureIds = MongoHelper.getFixtureIdsByParams(paramsClone);
//                        if (fixtureIds != null && !fixtureIds.isEmpty()) {
//                            paramsClone.put("fixtureIds", StringUtils.join(fixtureIds, ","));
//                            teamTotalMinutes = MongoHelper.getTotalMinutes(paramsClone);
//                        } else {
//                            // esco perchè non va bene
//                            model.addAttribute("mValid", false);
//                            return PAGE_OVERVIEW_CONTENT;
//                        }
//                    }
//                    Map<Long, Map<Long, Double>> teamTotals = MongoHelper.getTeamEventTypeTotalsByParams(params);
//                    // se bisogna calcolo i totali dei team per le metriche avanzate
//                    if (MongoHelper.hasAdvancedMetrics(params)) {
//                        // tiro fuori la lista degli id delle metriche avanzate
//                        List<String> advancedEventTypeIds = new ArrayList<>();
//                        for (String eventTypeIdString : StringUtils.split(params.get("eventTypeIds").toString(), "|")) {
//                            Long eventTypeId = Long.valueOf(eventTypeIdString);
//                            if (eventTypeId >= 1000 && eventTypeId <= 1017) {
//                                advancedEventTypeIds.add(eventTypeIdString);
//                            }
//                        }
//
//                        if (!advancedEventTypeIds.isEmpty()) {
//                            params.put("eventTypeIds", StringUtils.join(advancedEventTypeIds, "|"));
//                            List<DocumentRow> advancedMetricsRows = MongoHelper.getDocumentsByParams(params);
//                            if (advancedMetricsRows != null && !advancedMetricsRows.isEmpty()) {
//                                for (DocumentRow row : advancedMetricsRows) {
//                                    teamTotals.putIfAbsent(row.getEventTypeId(), new HashMap<Long, Double>());
//                                    teamTotals.get(row.getEventTypeId()).putIfAbsent(row.getTeamId(), 0D);
//                                    teamTotals.get(row.getEventTypeId()).put(row.getTeamId(), teamTotals.get(row.getEventTypeId()).get(row.getTeamId()) + row.getTotal());
//                                }
//                            }
//                        }
//                    }
                    for (Long eventTypeId : groupedByEventTagZone.keySet()) {
                        for (String tagType : groupedByEventTagZone.get(eventTypeId).keySet()) {
                            for (String zoneId : groupedByEventTagZone.get(eventTypeId).get(tagType).keySet()) {
                                Double min = null, max = null, average = 0D;
                                String minTeamName = null, maxTeamName = null;
                                DocumentRow tmpRow = null;

                                List<DocumentRow> eventTypeTotals = groupedByEventTagZone.get(eventTypeId).get(tagType).get(zoneId);
                                if (eventTypeTotals != null && !eventTypeTotals.isEmpty()) {
                                    for (DocumentRow row : eventTypeTotals) {
                                        Double teamTotal = row.getTotal();
                                        if (displayType.equals(DisplayType.P90)) {
                                            teamTotal = row.getTotalP90();
                                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                                            teamTotal = row.getTotal100Touches();
                                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                                            teamTotal = row.getTotalAverage();
                                        }

                                        if (min == null || teamTotal < min) {
                                            min = teamTotal;
                                            minTeamName = teams.get(row.getTeamId()).getName(curUser.getTvLanguage());
                                        }
                                        if (max == null || teamTotal > max) {
                                            max = teamTotal;
                                            maxTeamName = teams.get(row.getTeamId()).getName(curUser.getTvLanguage());
                                        }

                                        if (tmpRow == null) {
                                            tmpRow = row;
                                        }

                                        average += teamTotal;
                                    }

                                    final boolean opposite = Utils.isOpposite(eventTypeId, tagType);
                                    Collections.sort(eventTypeTotals, new Comparator<DocumentRow>() {
                                        @Override
                                        public int compare(DocumentRow o1, DocumentRow o2) {
                                            switch (displayType) {
                                                case P90:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotalP90(), o2.getTotalP90()) == 0) {
                                                            return o1.getTeamId().compareTo(o2.getTeamId());
                                                        } else {
                                                            return o1.getTotalP90().compareTo(o2.getTotalP90());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotalP90(), o1.getTotalP90()) == 0) {
                                                            return o2.getTeamId().compareTo(o1.getTeamId());
                                                        } else {
                                                            return o2.getTotalP90().compareTo(o1.getTotalP90());
                                                        }
                                                    }
                                                case TOUCHES:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotal100Touches(), o2.getTotal100Touches()) == 0) {
                                                            return o1.getTeamId().compareTo(o2.getTeamId());
                                                        } else {
                                                            return o1.getTotal100Touches().compareTo(o2.getTotal100Touches());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotal100Touches(), o1.getTotal100Touches()) == 0) {
                                                            return o2.getTeamId().compareTo(o1.getTeamId());
                                                        } else {
                                                            return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                                                        }
                                                    }
                                                case AVERAGE:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotalAverage(), o2.getTotalAverage()) == 0) {
                                                            return o1.getTeamId().compareTo(o2.getTeamId());
                                                        } else {
                                                            return o1.getTotalAverage().compareTo(o2.getTotalAverage());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotalAverage(), o1.getTotalAverage()) == 0) {
                                                            return o2.getTeamId().compareTo(o1.getTeamId());
                                                        } else {
                                                            return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                                                        }
                                                    }
                                                // return (opposite ? o1.getTotalAverage().compareTo(o2.getTotalAverage()) : o2.getTotalAverage().compareTo(o1.getTotalAverage()));
                                                default:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotal(), o2.getTotal()) == 0) {
                                                            return o1.getTeamId().compareTo(o2.getTeamId());
                                                        } else {
                                                            return o1.getTotal().compareTo(o2.getTotal());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotal(), o1.getTotal()) == 0) {
                                                            return o2.getTeamId().compareTo(o1.getTeamId());
                                                        } else {
                                                            return o2.getTotal().compareTo(o1.getTotal());
                                                        }
                                                    }
                                                // return (opposite ? o1.getTotal().compareTo(o2.getTotal()) : o2.getTotal().compareTo(o1.getTotal()));
                                            }
                                        }
                                    });
                                    if (opposite) {
                                        double tmp = max;
                                        max = min;
                                        min = tmp;
                                    }

                                    int index = 0;
                                    DocumentRow teamRow = null;
                                    for (DocumentRow row : eventTypeTotals) {
                                        if (teamRow == null) {
                                            if (Long.compare(row.getTeamId(), teamId) == 0) {
                                                teamRow = row;
                                            }
                                            index++;
                                        }
                                    }

                                    // gestione tag
                                    String tagName = Utils.getTagTypeName(tmpRow, curUser.getTvLanguage());
                                    if (StringUtils.isNotBlank(tagName)) {
                                        tagName = "(" + tagName + ")";
                                    }
                                    // gestione posizionale
                                    String zoneAbbName = null, zoneName = null;
                                    if (StringUtils.isNotBlank(zoneId)) {
                                        zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                        if (StringUtils.isNotBlank(zoneAbbName)) {
                                            zoneAbbName = "(" + zoneAbbName + ")";
                                        }
                                        zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                    }

                                    average = Math.round((average / eventTypeTotals.size()) * 100) / 100D;
                                    TableRow tableRow = new TableRow();
                                    tableRow.setEventTypeId(eventTypeId);
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        tableRow.setEventTypeName(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    } else {
                                        // metrica avanzata
                                        tableRow.setEventTypeName(advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    tableRow.setTagTypeName(tagName);
                                    tableRow.setZoneAbbName(zoneAbbName);
                                    tableRow.setZoneName(zoneName);
                                    tableRow.setIndex(index);
                                    tableRow.setTeamId(teamId);
                                    tableRow.setTeamName(teams.get(teamId).getName(curUser.getTvLanguage()));
                                    tableRow.setTeamLogo(teams.get(teamId).getLogo());
                                    Double value = -1D;
                                    if (teamRow != null) {
                                        value = teamRow.getTotal();
                                        if (displayType.equals(DisplayType.P90)) {
                                            value = teamRow.getTotalP90();
                                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                                            value = teamRow.getTotal100Touches();
                                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                                            value = teamRow.getTotalAverage();
                                        }
                                    }
                                    tableRow.setValue(value);
                                    tableRow.setMinValue(Math.round(min * 100) / 100D);
                                    tableRow.setMinValueTeam(minTeamName);
                                    tableRow.setMaxValue(Math.round(max * 100) / 100D);
                                    tableRow.setMaxValueTeam(maxTeamName);
                                    tableRow.setAverage(average);
                                    tableRow.setIsOpposite(opposite);
                                    if (sufferedEventTypes.contains(eventTypeId)) {
                                        sufferedRows.add(tableRow);
                                    } else {
                                        rows.add(tableRow);
                                    }
                                }
                            }
                        }
                    }
                }

                Collections.sort(rows, new Comparator<TableRow>() {
                    @Override
                    public int compare(TableRow o1, TableRow o2) {
                        return o1.getEventTypeName().compareTo(o2.getEventTypeName());
                    }
                });

                Collections.sort(sufferedRows, new Comparator<TableRow>() {
                    @Override
                    public int compare(TableRow o1, TableRow o2) {
                        return o1.getEventTypeName().compareTo(o2.getEventTypeName());
                    }
                });

                String src = "", teamName = "";
                if (!rows.isEmpty()) {
                    src = rows.get(0).getTeamLogo();
                    teamName = rows.get(0).getTeamName();
                } else if (!sufferedRows.isEmpty()) {
                    src = sufferedRows.get(0).getTeamLogo();
                    teamName = sufferedRows.get(0).getTeamName();
                }
                if (StringUtils.isBlank(src)) {
                    src = "unknownxx";
                }
                model.addAttribute("mTeamName", teamName);
                model.addAttribute("mTeamLogo", src);
                model.addAttribute("mRows", rows);
                model.addAttribute("mSufferedRows", sufferedRows);
                model.addAttribute("mTotalRows", (rows.size() + sufferedRows.size()));
                model.addAttribute("mAdvancedMetrics", advancedMetrics);

                // media degli indici
                int offensiveIndexTot = 0, offensiveIndexAmount = 0;
                int defensiveIndexTot = 0, defensiveIndexAmount = 0;
                for (TableRow row : rows) {
                    offensiveIndexAmount++;
                    offensiveIndexTot += (row.getIndex() != null ? row.getIndex() : 0);
                }
                for (TableRow row : sufferedRows) {
                    defensiveIndexAmount++;
                    defensiveIndexTot += (row.getIndex() != null ? row.getIndex() : 0);
                }
                if (offensiveIndexAmount > 0) {
                    int offensiveIndexAverage = Math.round(offensiveIndexTot / offensiveIndexAmount);
                    model.addAttribute("mOffensiveIndexAverage", offensiveIndexAverage);
                }
                if (defensiveIndexAmount > 0) {
                    int defensiveIndexAverage = Math.round(defensiveIndexTot / defensiveIndexAmount);
                    model.addAttribute("mDefensiveIndexAverage", defensiveIndexAverage);
                }

                // dati team
                Long competitionId = Long.valueOf(params.get("competitionId").toString());
                Long seasonId = Long.valueOf(params.get("seasonId").toString());
                seasonId = Utils.getCorrectSeasonId(seasonId, competitionId);
                model.addAttribute("mCompetitionId", competitionId);
                model.addAttribute("mTeamId", teamId);
                TeamData data = mService.getTeamData(teamId, competitionId, seasonId);
                model.addAttribute("mTeamData", data);

                if (StringUtils.isNotBlank(data.getMainModule())) {
                    List<Point> modulePoints = ModuleHelper.getModulePoints(data.getMainModule().replace("-", ""));
                    model.addAttribute("mModulePoints", modulePoints);
                }

                // carriera team
                List<TeamCareerItem> teamCareer = mService.getTeamCareer(teamId);
                if (teamCareer != null) {
                    for (TeamCareerItem item : teamCareer) {
                        if (item.getTeamId() != null) {
                            item.setTeam(teams.get(item.getTeamId()));
                        }
                        if (item.getCompetitionId() != null) {
                            item.setCompetition(competitions.get(item.getCompetitionId()));
                        }
                        if (item.getSeasonId() != null) {
                            item.setSeason(seasons.get(item.getSeasonId()));
                        }
                        if (item.getCompetition() != null && item.getSeason() != null) {
                            if (BooleanUtils.isTrue(item.getSeason().getVisible())) {
                                item.setClickable(curUser.hasAccessToCompetition(item.getCompetitionId()));
                            } else {
                                item.setClickable(false);
                            }
                        } else {
                            item.setClickable(false);
                        }
                    }
                }
                model.addAttribute("mTeamCareer", teamCareer);

                // ultime partite giocate
                List<Fixture> lastFixtures = mService.getTeamLastFixtures(teamId, competitionId, seasonId);
                model.addAttribute("mTeamLastFixtures", lastFixtures);

                // dati giocatori team
                TeamPlayerData playerData = mService.getTeamPlayerData(teamId, competitionId, seasonId);
                playerData.calculateValues();
                model.addAttribute("mTeamPlayerData", playerData);

                model.addAttribute("mTeams", teams);
                model.addAttribute("mCompetitions", competitions);
                model.addAttribute("mCountries", countries);
                model.addAttribute("mUser", curUser);
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_OVERVIEW_CONTENT;
    }
}
