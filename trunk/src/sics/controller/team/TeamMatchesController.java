package sics.controller.team;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.controller.ControllerUtils;
import sics.domain.DocumentRow;
import sics.domain.Fixture;
import sics.domain.User;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/matches")
public class TeamMatchesController extends BaseController {

    private final static String PAGE_MATCHES_CONTENT = "team-data/matches-content.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                model.addAttribute("mValid", true);

                params.put("type", "team");
                List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionId").toString(), "|"));
                Map<Long, List<DocumentRow>> groupedByFixture = new HashMap<>();
                for (String competitionId : competitionIds) {
                    params.put("competitionId", competitionId);

                    List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_FIXTURE);
                    if (!result.isEmpty()) {
                        for (DocumentRow row : result) {
                            if (row.getFixtureId() != null) {
                                groupedByFixture.putIfAbsent(row.getFixtureId(), new ArrayList<DocumentRow>());
                                groupedByFixture.get(row.getFixtureId()).add(row);
                            }
                        }
                    }
                }

                double teamMaxValue = 0;
                Map<Long, List<DocumentRow>> groupedByFixtureTeam = new HashMap<>();
                for (Long fixtureId : groupedByFixture.keySet()) {
                    Map<Long, DocumentRow> groupedByTeam = ControllerUtils.getTotals(true, groupedByFixture.get(fixtureId), params, GroupedField.TEAM);
                    for (DocumentRow row : groupedByTeam.values()) {
                        if (row.getTotal() != null) {
                            if (row.getTotal() > teamMaxValue) {
                                teamMaxValue = row.getTotal();
                            }
                        }
                    }
                    groupedByFixtureTeam.put(fixtureId, new ArrayList<>(groupedByTeam.values()));
                }

                // sistemo le partite che non hanno entrambi i valori
                for (Long fixtureId : groupedByFixtureTeam.keySet()) {
                    if (!groupedByFixtureTeam.get(fixtureId).isEmpty() && groupedByFixtureTeam.get(fixtureId).size() < 2) {
                        DocumentRow firstRow = groupedByFixtureTeam.get(fixtureId).get(0);
                        if (BooleanUtils.isTrue(firstRow.getIsHomeTeam())) {
                            DocumentRow awayRow = new DocumentRow();
                            awayRow.setTotal(0D);
                            awayRow.setTotalP90(0D);
                            groupedByFixtureTeam.get(fixtureId).add(awayRow);
                        } else {
                            DocumentRow homeRow = new DocumentRow();
                            homeRow.setTotal(0D);
                            homeRow.setTotalP90(0D);
                            groupedByFixtureTeam.get(fixtureId).add(0, homeRow);
                        }
                    }
                }

                List<Fixture> fixtures = mService.getFixtureByIds(new ArrayList<>(groupedByFixture.keySet()));
                Map<Long, Fixture> fixtureMap = new HashMap<>();
                if (fixtures != null && !fixtures.isEmpty()) {
                    for (Fixture fixture : fixtures) {
                        fixtureMap.put(fixture.getId(), fixture);
                    }
                }

                int maxValue = 0;
                for (Long fixtureId : groupedByFixtureTeam.keySet()) {
                    int tmpMax = 0;
                    for (DocumentRow row : groupedByFixtureTeam.get(fixtureId)) {
                        tmpMax += row.getTotal();
                    }

                    if (tmpMax > maxValue) {
                        maxValue = tmpMax;
                    }
                }
                model.addAttribute("mFixtures", fixtureMap);
                model.addAttribute("mRows", groupedByFixtureTeam);
                model.addAttribute("mMaxValue", maxValue);
                model.addAttribute("mTeamMaxValue", Math.round(teamMaxValue));
                model.addAttribute("mTeams", teams);
                model.addAttribute("mUser", curUser);
                Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
                String tagTypeIds = null;
                if (params.containsKey("tagTypeId")) {
                    tagTypeIds = params.get("tagTypeId").toString();
                }
                model.addAttribute("mIsOpposite", Utils.isOpposite(eventTypeId, tagTypeIds));
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_MATCHES_CONTENT;
    }
}
