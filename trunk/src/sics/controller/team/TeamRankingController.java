package sics.controller.team;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.User;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/ranking")
public class TeamRankingController extends BaseController {

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonArray chartData = new JsonArray();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                params.put("type", "team");

                Map<String, Object> paramsClone = new HashMap<>(params);

                List<Integer> extraIndex = new ArrayList<>();
                for (String param : params.keySet()) {
                    if (param.startsWith("eventTypeId") && !StringUtils.equalsIgnoreCase(param, "eventTypeId")) {
                        extraIndex.add(Integer.valueOf(StringUtils.replace(param, "eventTypeId", "")));
                    }
                }
                for (Integer index : extraIndex) {
                    params.remove("eventTypeId" + index);
                    params.remove("tagTypeId" + index);
                }

                DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                Map<Long, List<DocumentRow>> finalResult = new LinkedHashMap<>();

                String paramCompetitionId = params.get("competitionId").toString();
                List<String> splittedCompetitionId = Arrays.asList(StringUtils.split(paramCompetitionId, "|"));
                Map<Long, DocumentRow> groupedByTeam = new HashMap<>();
                for (String competitionId : splittedCompetitionId) {
                    params.remove("competitionId");
                    params.put("competitionId", competitionId);
                    List<DocumentRow> tmpResult = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM);
//                    if (!isTotals) {
//                        Utils.calculateP90V2(tmpResult, params);
//                    }
                    for (DocumentRow row : tmpResult) {
                        row.setCompetitionId(Long.valueOf(competitionId));
                        if (groupedByTeam.get(row.getTeamId()) == null) {
                            groupedByTeam.put(row.getTeamId(), row);
                        } else {
                            groupedByTeam.get(row.getTeamId()).setTotal(groupedByTeam.get(row.getTeamId()).getTotal() + row.getTotal());
                        }
                    }
                }

                List<DocumentRow> sortedRows = new ArrayList<>(groupedByTeam.values());
                Collections.sort(sortedRows, new Comparator<DocumentRow>() {
                    @Override
                    public int compare(DocumentRow o1, DocumentRow o2) {
                        return o2.getTotal().compareTo(o1.getTotal());
                    }
                });

                for (DocumentRow row : sortedRows) {
                    finalResult.putIfAbsent(row.getTeamId(), new ArrayList<DocumentRow>());
                    finalResult.get(row.getTeamId()).add(row);
                }

                for (Integer index : extraIndex) {
                    params.remove("eventTypeId");
                    params.remove("tagTypeId");
                    params.put("eventTypeId", paramsClone.get("eventTypeId" + index));
                    if (paramsClone.containsKey("tagTypeId" + index)) {
                        params.put("tagTypeId", paramsClone.get("tagTypeId" + index));
                    }

                    for (String competitionId : splittedCompetitionId) {
                        params.remove("competitionId");
                        params.put("competitionId", competitionId);
                        List<DocumentRow> tmpResult = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM);
                        Map<Long, DocumentRow> tmpGroupedByTeam = new HashMap<>();
//                        if (!isTotals) {
//                            Utils.calculateP90V2(tmpResult, params);
//                        }
                        for (DocumentRow row : tmpResult) {
                            row.setCompetitionId(Long.valueOf(competitionId));
                            if (tmpGroupedByTeam.get(row.getTeamId()) == null) {
                                tmpGroupedByTeam.put(row.getTeamId(), row);
                            } else {
                                tmpGroupedByTeam.get(row.getTeamId()).setTotal(tmpGroupedByTeam.get(row.getTeamId()).getTotal() + row.getTotal());
                            }
                        }

                        for (DocumentRow row : tmpGroupedByTeam.values()) {
                            finalResult.putIfAbsent(row.getTeamId(), new ArrayList<DocumentRow>());
                            finalResult.get(row.getTeamId()).add(row);
                        }
                    }
                }

                if (displayType.equals(DisplayType.P90)) {
                    List<DocumentRow> allRows = new ArrayList<>();
                    List<DocumentRow> rowsForSort = new ArrayList<>();
                    for (List<DocumentRow> rows : finalResult.values()) {
                        rowsForSort.add(rows.get(0));
                        allRows.addAll(rows);
                    }

                    params.put("competitionIds", paramCompetitionId);
                    Utils.calculateP90V2(allRows, params);

                    // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                    Collections.sort(rowsForSort, new Comparator<DocumentRow>() {
                        @Override
                        public int compare(DocumentRow o1, DocumentRow o2) {
                            return o2.getTotalP90().compareTo(o1.getTotalP90());
                        }
                    });
                    Map<Long, List<DocumentRow>> finalResultSorted = new LinkedHashMap<>();
                    for (DocumentRow row : rowsForSort) {
                        finalResultSorted.put(row.getTeamId(), finalResult.get(row.getTeamId()));
                    }
                    if (finalResultSorted.size() != finalResult.size()) {
                        // nel caso in cui un team ha solo il secondo evento e non il primo non verrebbe mai fuori
                        // lo metto quindi alla fine dato che comunque il primo dei 2 valori sarebbe 0
                        for (Long teamId : finalResult.keySet()) {
                            if (!finalResultSorted.containsKey(teamId)) {
                                finalResultSorted.put(teamId, finalResult.get(teamId));
                            }
                        }
                    }
                    finalResult = finalResultSorted;
                } else if (displayType.equals(DisplayType.TOUCHES)) {
                    List<DocumentRow> allRows = new ArrayList<>();
                    List<DocumentRow> rowsForSort = new ArrayList<>();
                    for (List<DocumentRow> rows : finalResult.values()) {
                        rowsForSort.add(rows.get(0));
                        allRows.addAll(rows);
                    }

                    params.put("competitionIds", paramCompetitionId);
                    Utils.calculate100Touches(allRows, params);

                    // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                    Collections.sort(rowsForSort, new Comparator<DocumentRow>() {
                        @Override
                        public int compare(DocumentRow o1, DocumentRow o2) {
                            return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                        }
                    });
                    Map<Long, List<DocumentRow>> finalResultSorted = new LinkedHashMap<>();
                    for (DocumentRow row : rowsForSort) {
                        finalResultSorted.put(row.getTeamId(), finalResult.get(row.getTeamId()));
                    }
                    if (finalResultSorted.size() != finalResult.size()) {
                        // nel caso in cui un team ha solo il secondo evento e non il primo non verrebbe mai fuori
                        // lo metto quindi alla fine dato che comunque il primo dei 2 valori sarebbe 0
                        for (Long teamId : finalResult.keySet()) {
                            if (!finalResultSorted.containsKey(teamId)) {
                                finalResultSorted.put(teamId, finalResult.get(teamId));
                            }
                        }
                    }
                    finalResult = finalResultSorted;
                } else if (displayType.equals(DisplayType.AVERAGE)) {
                    List<DocumentRow> allRows = new ArrayList<>();
                    List<DocumentRow> rowsForSort = new ArrayList<>();
                    for (List<DocumentRow> rows : finalResult.values()) {
                        rowsForSort.add(rows.get(0));
                        allRows.addAll(rows);
                    }

                    params.put("competitionIds", paramCompetitionId);
                    Utils.calculateAverage(allRows, params);

                    // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                    Collections.sort(rowsForSort, new Comparator<DocumentRow>() {
                        @Override
                        public int compare(DocumentRow o1, DocumentRow o2) {
                            return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                        }
                    });
                    Map<Long, List<DocumentRow>> finalResultSorted = new LinkedHashMap<>();
                    for (DocumentRow row : rowsForSort) {
                        finalResultSorted.put(row.getTeamId(), finalResult.get(row.getTeamId()));
                    }
                    if (finalResultSorted.size() != finalResult.size()) {
                        // nel caso in cui un team ha solo il secondo evento e non il primo non verrebbe mai fuori
                        // lo metto quindi alla fine dato che comunque il primo dei 2 valori sarebbe 0
                        for (Long teamId : finalResult.keySet()) {
                            if (!finalResultSorted.containsKey(teamId)) {
                                finalResultSorted.put(teamId, finalResult.get(teamId));
                            }
                        }
                    }
                    finalResult = finalResultSorted;
                }

                int imageSizeToReduce = finalResult.size() / 20 * 6;
                if (imageSizeToReduce >= 36) {
                    imageSizeToReduce = 36;
                }
                switch (displayType) {
                    case AVERAGE:
                        for (Long teamId : finalResult.keySet()) {
                            JsonObject data = new JsonObject();
                            data.addProperty("team", teams.get(teamId).getName(curUser.getTvLanguage()));
                            data.addProperty("teamId", teamId);
                            if (!finalResult.get(teamId).isEmpty()) {
                                data.addProperty("competitionId", finalResult.get(teamId).get(0).getCompetitionId());
                            }
                            for (DocumentRow row : finalResult.get(teamId)) {
                                data.addProperty("total" + row.getEventTypeId(), row.getTotalAverage());
                            }
                            JsonObject logoObject = new JsonObject();
                            String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + teams.get(teamId).getLogo() + ".png";
                            if (StringUtils.isBlank(teams.get(teamId).getLogo())) {
                                src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png";
                            }
                            src += "?" + (new Date().getTime());
                            logoObject.addProperty("src", src);
                            logoObject.addProperty("width", 48 - imageSizeToReduce);
                            logoObject.addProperty("height", 48 - imageSizeToReduce);
                            data.add("logo", logoObject);
                            chartData.add(data);
                        }
                        break;
                    case P90:
                        for (Long teamId : finalResult.keySet()) {
                            JsonObject data = new JsonObject();
                            data.addProperty("team", teams.get(teamId).getName(curUser.getTvLanguage()));
                            data.addProperty("teamId", teamId);
                            if (!finalResult.get(teamId).isEmpty()) {
                                data.addProperty("competitionId", finalResult.get(teamId).get(0).getCompetitionId());
                            }
                            for (DocumentRow row : finalResult.get(teamId)) {
                                data.addProperty("total" + row.getEventTypeId(), row.getTotalP90());
                            }
                            JsonObject logoObject = new JsonObject();
                            String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + teams.get(teamId).getLogo() + ".png";
                            if (StringUtils.isBlank(teams.get(teamId).getLogo())) {
                                src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png";
                            }
                            src += "?" + (new Date().getTime());
                            logoObject.addProperty("src", src);
                            logoObject.addProperty("width", 48 - imageSizeToReduce);
                            logoObject.addProperty("height", 48 - imageSizeToReduce);
                            data.add("logo", logoObject);
                            chartData.add(data);
                        }
                        break;
                    case TOUCHES:
                        for (Long teamId : finalResult.keySet()) {
                            JsonObject data = new JsonObject();
                            data.addProperty("team", teams.get(teamId).getName(curUser.getTvLanguage()));
                            data.addProperty("teamId", teamId);
                            if (!finalResult.get(teamId).isEmpty()) {
                                data.addProperty("competitionId", finalResult.get(teamId).get(0).getCompetitionId());
                            }
                            for (DocumentRow row : finalResult.get(teamId)) {
                                data.addProperty("total" + row.getEventTypeId(), row.getTotal100Touches());
                            }
                            JsonObject logoObject = new JsonObject();
                            String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + teams.get(teamId).getLogo() + ".png";
                            if (StringUtils.isBlank(teams.get(teamId).getLogo())) {
                                src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png";
                            }
                            src += "?" + (new Date().getTime());
                            logoObject.addProperty("src", src);
                            logoObject.addProperty("width", 48 - imageSizeToReduce);
                            logoObject.addProperty("height", 48 - imageSizeToReduce);
                            data.add("logo", logoObject);
                            chartData.add(data);
                        }
                        break;
                    default:
                        for (Long teamId : finalResult.keySet()) {
                            JsonObject data = new JsonObject();
                            data.addProperty("team", teams.get(teamId).getName(curUser.getTvLanguage()));
                            data.addProperty("teamId", teamId);
                            if (!finalResult.get(teamId).isEmpty()) {
                                data.addProperty("competitionId", finalResult.get(teamId).get(0).getCompetitionId());
                            }
                            for (DocumentRow row : finalResult.get(teamId)) {
                                data.addProperty("total" + row.getEventTypeId(), row.getTotal());
                            }
                            JsonObject logoObject = new JsonObject();
                            String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + teams.get(teamId).getLogo() + ".png";
                            if (StringUtils.isBlank(teams.get(teamId).getLogo())) {
                                src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png";
                            }
                            src += "?" + (new Date().getTime());
                            logoObject.addProperty("src", src);
                            logoObject.addProperty("width", 48 - imageSizeToReduce);
                            logoObject.addProperty("height", 48 - imageSizeToReduce);
                            data.add("logo", logoObject);
                            chartData.add(data);
                        }
                        break;
                }
            }
        }

        return chartData.toString();
    }
}
