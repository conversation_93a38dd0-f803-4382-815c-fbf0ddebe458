package sics.controller.team;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.User;
import sics.domain.wrapper.DocumentRowWrapper;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/scatterplot")
public class TeamScatterplotController extends BaseController {

    private final List<Integer> imageSizes = new ArrayList<>(Arrays.asList(48, 36, 27, 15, 10, 7, 5));

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeIdX") && params.containsKey("eventTypeIdY")) {
                params.put("type", "team");

                String eventTypeIdX = params.get("eventTypeIdX").toString();
                Long typeIdX = null;
                String tagIdX = null, zoneIdX = null;
                if (StringUtils.contains(eventTypeIdX, "$") && StringUtils.contains(eventTypeIdX, "@")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "$"));
                    tagIdX = StringUtils.substringBetween(eventTypeIdX, "$", "@");
                    zoneIdX = StringUtils.substringAfterLast(eventTypeIdX, "@");
                } else if (StringUtils.contains(eventTypeIdX, "$")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "$"));
                    tagIdX = StringUtils.substringAfterLast(eventTypeIdX, "$");
                } else if (StringUtils.contains(eventTypeIdX, "@")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "@"));
                    zoneIdX = StringUtils.substringAfterLast(eventTypeIdX, "@");
                } else {
                    typeIdX = Long.valueOf(eventTypeIdX);
                }
                if (StringUtils.isNotBlank(tagIdX)) {
                    List<String> tmpTagTypes = Arrays.asList(StringUtils.split(tagIdX, "-"));
                    Collections.sort(tmpTagTypes);
                    tagIdX = StringUtils.join(tmpTagTypes, "|");
                }
                String eventTypeIdY = params.get("eventTypeIdY").toString();
                Long typeIdY = null;
                String tagIdY = null, zoneIdY = null;
                if (StringUtils.contains(eventTypeIdY, "$") && StringUtils.contains(eventTypeIdY, "@")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "$"));
                    tagIdY = StringUtils.substringBetween(eventTypeIdY, "$", "@");
                    zoneIdY = StringUtils.substringAfterLast(eventTypeIdY, "@");
                } else if (StringUtils.contains(eventTypeIdY, "$")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "$"));
                    tagIdY = StringUtils.substringAfterLast(eventTypeIdY, "$");
                } else if (StringUtils.contains(eventTypeIdY, "@")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "@"));
                    zoneIdY = StringUtils.substringAfterLast(eventTypeIdY, "@");
                } else {
                    typeIdY = Long.valueOf(eventTypeIdY);
                }
                if (StringUtils.isNotBlank(tagIdY)) {
                    List<String> tmpTagTypes = Arrays.asList(StringUtils.split(tagIdY, "-"));
                    Collections.sort(tmpTagTypes);
                    tagIdY = StringUtils.join(tmpTagTypes, "|");
                }

                params.remove("eventTypeIdX");
                params.remove("eventTypeIdY");

                List<Long> teamObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                Map<Long, Map<Long, Map<String, Map<String, Map<Long, DocumentRow>>>>> results = new HashMap<>();
                List<String> seasonIds = Arrays.asList(StringUtils.split(params.get("seasonId").toString(), "/"));
                List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionId").toString(), "|"));
                List<Long> seasonIdList = new ArrayList<>();
                DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                if (seasonIds != null && !seasonIds.isEmpty()) {
                    params.remove("seasonId");
                    params.remove("competitionId");
                    for (String seasonId : seasonIds) {
                        for (String competitionId : competitionIds) {
                            params.remove("tagTypeId");
                            params.put("seasonId", seasonId);
                            params.put("competitionId", competitionId);

                            Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                            Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
                            tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);
                            seasonIdList.add(tmpSeasonId);
                            
                            params.put("eventTypeId", typeIdX);
                            if (StringUtils.isNotBlank(tagIdX)) {
                                params.put("tagTypeId", tagIdX);
                            }
                            MongoHelper.adjustParamsByZoneId(params, zoneIdX);
                            List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM);
                            if (StringUtils.isNotBlank(zoneIdX)) {
                                for (DocumentRow row : result) {
                                    row.setZoneId(zoneIdX);
                                }
                            }
//                            if (!isTotals) {
//                                Utils.calculateP90V2(result, params);
//                            }

                            for (DocumentRow row : result) {
                                row.setSeasonId(tmpSeasonId);
                                
                                if (!teamObjectsAdded.contains(row.getTeamId())) {
                                    JsonObject teamObject = new JsonObject();
                                    teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                    teamObjects.add(teamObject);
                                    teamObjectsAdded.add(row.getTeamId());
                                }

                                results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, DocumentRow>>>>());
                                results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, DocumentRow>>>());
                                results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, DocumentRow>>());
                                results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, DocumentRow>());
                                results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).put(row.getTeamId(), row);
                            }

                            // controllo che non sia lo stesso altrimenti raddoppia
                            if (!StringUtils.equals(eventTypeIdX, eventTypeIdY)) {
                                params.remove("tagTypeId");
                                params.put("eventTypeId", typeIdY);
                                if (StringUtils.isNotBlank(tagIdY)) {
                                    params.put("tagTypeId", tagIdY);
                                }
                                MongoHelper.adjustParamsByZoneId(params, zoneIdY);
                                List<DocumentRow> resultY = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM);
                                if (StringUtils.isNotBlank(zoneIdY)) {
                                    for (DocumentRow row : resultY) {
                                        row.setZoneId(zoneIdY);
                                    }
                                }
//                            if (!isTotals) {
//                                Utils.calculateP90V2(resultY, params);
//                            }

                                for (DocumentRow row : resultY) {
                                    row.setSeasonId(tmpSeasonId);

                                    if (!teamObjectsAdded.contains(row.getTeamId())) {
                                        JsonObject teamObject = new JsonObject();
                                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                        teamObjects.add(teamObject);
                                        teamObjectsAdded.add(row.getTeamId());
                                    }

                                    results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, DocumentRow>>>>());
                                    results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, DocumentRow>>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, DocumentRow>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, DocumentRow>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).put(row.getTeamId(), row);
                                }
                            }
                        }
                    }
                    chartData.add("teams", teamObjects);

//                    // se ci sono calcolo ora i dati dei team / giocatori extra
//                    for (String seasonId : seasonIds) {
//                        seasonIdList.add(Long.valueOf(seasonId));
//                        params.put("seasonId", seasonId);
//
//                        for (Integer index : extraIndex) {
//                            if (paramsClone.containsKey("teamId" + index)) {
//                                params.remove("competitionId");
//                                params.remove("teamId");
//                                params.put("competitionId", paramsClone.get("competitionId" + index));
//                                params.put("teamId", paramsClone.get("teamId" + index));
//
//                                // calcolo evento asse X
//                                params.put("eventTypeId", eventTypeIdX);
//                                List<DocumentRow> tmpResultX = MongoHelper.getDocumentsByParams(params);
//                                Map<Long, DocumentRow> tmpGroupedByTeamX = ControllerUtils.getTotals(true, tmpResultX, params, GroupedField.TEAM);
//                                if (!isTotals) {
//                                    Utils.calculateP90(tmpGroupedByTeamX.values(), tmpResultX, params);
//                                }
//
//                                for (DocumentRow row : tmpGroupedByTeamX.values()) {
//                                    row.setSeasonId(Long.valueOf(seasonId));
//                                }
//                                results.addAll(tmpGroupedByTeamX.values());
//
//                                // calcolo evento asse Y
//                                params.put("eventTypeId", eventTypeIdY);
//                                List<DocumentRow> tmpResultY = MongoHelper.getDocumentsByParams(params);
//                                Map<Long, DocumentRow> tmpGroupedByTeamY = ControllerUtils.getTotals(true, tmpResultY, params, GroupedField.TEAM);
//                                if (!isTotals) {
//                                    Utils.calculateP90(tmpGroupedByTeamY.values(), tmpResultY, params);
//                                }
//
//                                for (DocumentRow row : tmpGroupedByTeamY.values()) {
//                                    row.setSeasonId(Long.valueOf(seasonId));
//                                }
//                                results.addAll(tmpGroupedByTeamY.values());
//                            }
//                        }
//                    }
                }

                if (!results.isEmpty()) {
                    if (displayType.equals(DisplayType.P90) || displayType.equals(DisplayType.TOUCHES) || displayType.equals(DisplayType.AVERAGE)) {
                        params.put("competitionIds", StringUtils.join(competitionIds, "|"));
                        for (Long seasonId : results.keySet()) {
                            params.put("seasonId", seasonId);
                            List<DocumentRow> allRows = new ArrayList<>();
                            for (Long eventTypeId : results.get(seasonId).keySet()) {
                                for (String tagTypeId : results.get(seasonId).get(eventTypeId).keySet()) {
                                    for (String zoneId : results.get(seasonId).get(eventTypeId).get(tagTypeId).keySet()) {
                                        allRows.addAll(results.get(seasonId).get(eventTypeId).get(tagTypeId).get(zoneId).values());
                                    }
                                }
                            }
                            if (displayType.equals(DisplayType.P90)) {
                                Utils.calculateP90V2(allRows, params);
                            } else if (displayType.equals(DisplayType.TOUCHES)) {
                                Utils.calculate100Touches(allRows, params);
                            } else if (displayType.equals(DisplayType.AVERAGE)) {
                                Utils.calculateAverage(allRows, params);
                            }
                        }
                    }

                    Map<Double, List<DocumentRowWrapper>> groupedByEventX = new HashMap<>();
                    for (Long seasonId : results.keySet()) {
                        for (Long eventTypeId : results.get(seasonId).keySet()) {
                            for (String tagTypeId : results.get(seasonId).get(eventTypeId).keySet()) {
                                for (String zoneId : results.get(seasonId).get(eventTypeId).get(tagTypeId).keySet()) {
                                    for (DocumentRow row : results.get(seasonId).get(eventTypeId).get(tagTypeId).get(zoneId).values()) {
                                        if (row.getTeamId() != null && row.getEventTypeId() != null && row.getSeasonId() != null) {
                                            if (Long.compare(row.getEventTypeId(), typeIdX) == 0 && StringUtils.equals(row.getTagTypeId(), tagIdX) && StringUtils.equals(row.getZoneId(), zoneIdX)) {
                                                switch (displayType) {
                                                    case P90:
                                                        groupedByEventX.putIfAbsent(row.getTotalP90(), new ArrayList<DocumentRowWrapper>());
                                                        break;
                                                    case TOUCHES:
                                                        groupedByEventX.putIfAbsent(row.getTotal100Touches(), new ArrayList<DocumentRowWrapper>());
                                                        break;
                                                    case AVERAGE:
                                                        groupedByEventX.putIfAbsent(row.getTotalAverage(), new ArrayList<DocumentRowWrapper>());
                                                        break;
                                                    default:
                                                        groupedByEventX.putIfAbsent(row.getTotal(), new ArrayList<DocumentRowWrapper>());
                                                        break;
                                                }
                                                DocumentRowWrapper wrapper = new DocumentRowWrapper();
                                                wrapper.setxValue(row);
                                                if (results.get(seasonId).containsKey(typeIdY) && results.get(seasonId).get(typeIdY).containsKey(tagIdY) && results.get(seasonId).get(typeIdY).get(tagIdY).containsKey(zoneIdY)) {
                                                    for (DocumentRow tmpRow : results.get(seasonId).get(typeIdY).get(tagIdY).get(zoneIdY).values()) {
                                                        if (tmpRow.getTeamId() != null && tmpRow.getEventTypeId() != null) {
                                                            if (Long.compare(tmpRow.getEventTypeId(), typeIdY) == 0
                                                                    && StringUtils.equals(tmpRow.getTagTypeId(), tagIdY)
                                                                    && Long.compare(tmpRow.getTeamId(), row.getTeamId()) == 0
                                                                    && Long.compare(tmpRow.getSeasonId(), row.getSeasonId()) == 0) {
                                                                wrapper.setyValue(tmpRow);
                                                                break;
                                                            }
                                                        }
                                                    }
                                                } else {
                                                    try {
                                                        DocumentRow clonedRow = row.clone();
                                                        clonedRow.setTotal(0D);
                                                        clonedRow.setTotalP90(0D);
                                                        clonedRow.setTotal100Touches(0D);
                                                        clonedRow.setTotalAverage(0D);
                                                        wrapper.setyValue(clonedRow);
                                                    } catch (CloneNotSupportedException ex) {
                                                        GlobalHelper.reportError(ex);
                                                    }
                                                }
                                                switch (displayType) {
                                                    case P90:
                                                        groupedByEventX.get(row.getTotalP90()).add(wrapper);
                                                        break;
                                                    case TOUCHES:
                                                        groupedByEventX.get(row.getTotal100Touches()).add(wrapper);
                                                        break;
                                                    case AVERAGE:
                                                        groupedByEventX.get(row.getTotalAverage()).add(wrapper);
                                                        break;
                                                    default:
                                                        groupedByEventX.get(row.getTotal()).add(wrapper);
                                                        break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Collections.sort(seasonIdList, Collections.reverseOrder());

                    JsonArray dataArray = new JsonArray();
                    for (Double xValue : groupedByEventX.keySet()) {
                        JsonObject data = new JsonObject();
                        data.addProperty("x", xValue);
                        for (DocumentRowWrapper wrapper : groupedByEventX.get(xValue)) {
                            if (wrapper.getyValue() != null) {
                                switch (displayType) {
                                    case P90:
                                        if (Double.compare(wrapper.getyValue().getTotalP90(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId(), wrapper.getyValue().getTotalP90());
                                        break;
                                    case TOUCHES:
                                        if (Double.compare(wrapper.getyValue().getTotal100Touches(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId(), wrapper.getyValue().getTotal100Touches());
                                        break;
                                    case AVERAGE:
                                        if (Double.compare(wrapper.getyValue().getTotalAverage(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId(), wrapper.getyValue().getTotalAverage());
                                        break;
                                    default:
                                        if (Double.compare(wrapper.getyValue().getTotal(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId(), wrapper.getyValue().getTotal());
                                        break;
                                }
                                data.addProperty("value" + wrapper.getyValue().getTeamId(), 10 + (seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                data.addProperty("name" + wrapper.getyValue().getTeamId(), teams.get(wrapper.getyValue().getTeamId()).getName(curUser.getTvLanguage()));
                                data.addProperty("season" + wrapper.getyValue().getTeamId(), seasons.get(wrapper.getyValue().getSeasonId()).getName());

                                JsonObject logoObject = new JsonObject();
                                String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + teams.get(wrapper.getyValue().getTeamId()).getLogo() + ".png";
                                if (StringUtils.isBlank(teams.get(wrapper.getyValue().getTeamId()).getLogo())) {
                                    src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png";
                                }
                                src += "?" + (new Date().getTime());
                                logoObject.addProperty("src", src);
                                logoObject.addProperty("width", imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                logoObject.addProperty("height", imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                logoObject.addProperty("dx", (-imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())) / 2));
                                logoObject.addProperty("dy", (-imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())) / 2));
                                data.add("logo" + wrapper.getyValue().getTeamId(), logoObject);
                            }
                        }
                        if (data.entrySet().size() > 1) {
                            dataArray.add(data);
                        }
                    }

                    chartData.add("data", dataArray);
                }
            }
        }

        return chartData.toString();
    }
}
