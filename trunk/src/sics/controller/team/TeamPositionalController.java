package sics.controller.team;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.Event;
import sics.domain.wrapper.EventWrapper;
import sics.helper.EventHelper;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/positional")
public class TeamPositionalController extends BaseController {

    private final static String PAGE_POSITIONAL_CONTENT = "team-data/positional-content.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("teamId") && params.containsKey("eventTypeId")) {
                params.put("type", "team");

                List<Event> events = mService.getEventsByParams(params);
                if (events != null && !events.isEmpty()) {
                    Map<String, Integer> heatmapPointsValues = new HashMap<>();
                    Map<String, Integer> heatmapEndPointsValues = new HashMap<>();
                    List<Event> eventsInvalid = new ArrayList<>();
                    for (Event event : events) {
                        EventHelper.setEventDetails(event);

                        // gestione eventi da rimuovere (filtri posizionali)
                        if (params.containsKey("half") && !eventsInvalid.contains(event)) {
                            Integer half = Integer.valueOf(params.get("half").toString());
                            if (event.getHalf() == null || Integer.compare(event.getHalf(), half) == 0) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("zone") && !eventsInvalid.contains(event)) {
                            Integer third = Integer.valueOf(params.get("zone").toString());
                            if (event.getThird() == null || Integer.compare(event.getThird(), third) != 0) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("channel") && !eventsInvalid.contains(event)) {
                            Integer channel = Integer.valueOf(params.get("channel").toString());
                            if (event.getChannel() == null || Integer.compare(event.getChannel(), channel) != 0) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("area") && !eventsInvalid.contains(event)) {
                            Integer area = Integer.valueOf(params.get("area").toString());
                            if (event.getInArea() == null && event.getInSmallArea() == null) {
                                eventsInvalid.add(event);
                            } else if (area == 1 && (BooleanUtils.isNotTrue(event.getInArea()) || event.getHalf() == null || event.getHalf() == 2)) {
                                eventsInvalid.add(event);
                            } else if (area == 2 && (BooleanUtils.isNotTrue(event.getInSmallArea()) || event.getHalf() == null || event.getHalf() == 2)) {
                                eventsInvalid.add(event);
                            } else if (area == 3 && (BooleanUtils.isNotTrue(event.getInArea()) || event.getHalf() == null || event.getHalf() == 1)) {
                                eventsInvalid.add(event);
                            } else if (area == 4 && (BooleanUtils.isNotTrue(event.getInSmallArea()) || event.getHalf() == null || event.getHalf() == 1)) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("angleZone") && !eventsInvalid.contains(event)) {
                            Integer angleZone = Integer.valueOf(params.get("angleZone").toString());
                            if (event.getAngleZone() == null || Integer.compare(event.getAngleZone(), angleZone) != 0) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("verticalChannel") && !eventsInvalid.contains(event)) {
                            Integer verticalChannel = Integer.valueOf(params.get("verticalChannel").toString());
                            if (event.getVerticalChannel() == null || Integer.compare(event.getVerticalChannel(), verticalChannel) != 0) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("timeInterval") && !eventsInvalid.contains(event)) {
                            String timeInterval = params.get("timeInterval").toString();
                            String eventTime = EventHelper.getEventTimeInterval(event);
                            if (StringUtils.isBlank(eventTime) || !StringUtils.equals(eventTime, timeInterval)) {
                                eventsInvalid.add(event);
                            }
                        }
                        if (params.containsKey("distance") && !eventsInvalid.contains(event)) {
                            Integer type = Integer.valueOf(params.get("distance").toString());
                            switch (type) {
                                case 1:
                                    // 10m
                                    if (event.getDistance() != null && event.getDistance() > 0 && event.getDistance() < 10) {
                                        eventsInvalid.add(event);
                                    }
                                    break;
                                case 2:
                                    // 20m
                                    if (event.getDistance() != null && event.getDistance() > 0 && event.getDistance() < 20) {
                                        eventsInvalid.add(event);
                                    }
                                    break;
                                case 3:
                                    // 20m
                                    if (event.getDistance() != null && event.getDistance() > 0 && event.getDistance() < 30) {
                                        eventsInvalid.add(event);
                                    }
                                    break;
                                default:
                                    break;
                            }
                        }

                        if (!eventsInvalid.contains(event)) {
                            if (event.getStartPointNormalized() != null && BooleanUtils.isFalse(event.getStartPointNormalized().getIsDefault())) {
                                String coordinates = Math.round(event.getStartPointNormalized().getX() * 6.67D) + ";" + Math.round(event.getStartPointNormalized().getY() * 6.62D);
                                heatmapPointsValues.putIfAbsent(coordinates, 0);
                                heatmapPointsValues.put(coordinates, heatmapPointsValues.get(coordinates) + 1);
                            }
                            if (event.getEndPointNormalized() != null && BooleanUtils.isFalse(event.getEndPointNormalized().getIsDefault())) {
                                String coordinates = Math.round(event.getEndPointNormalized().getX() * 6.67D) + ";" + Math.round(event.getEndPointNormalized().getY() * 6.62D);
                                heatmapEndPointsValues.putIfAbsent(coordinates, 0);
                                heatmapEndPointsValues.put(coordinates, heatmapEndPointsValues.get(coordinates) + 1);
                            }
                        }
                    }
                    events.removeAll(eventsInvalid);
                    model.addAttribute("mEvents", events);

                    // Heatmap Start Point
                    int max = 0;
                    JsonArray heatmapPoints = new JsonArray();
                    for (String coordinates : heatmapPointsValues.keySet()) {
                        int value = heatmapPointsValues.get(coordinates);
//                        if (value > max) {
//                            max = value;
//                        }

                        JsonObject point = new JsonObject();
                        point.addProperty("x", StringUtils.split(coordinates, ";")[0]);
                        point.addProperty("y", StringUtils.split(coordinates, ";")[1]);
                        point.addProperty("value", value);
                        heatmapPoints.add(point);
                    }
                    model.addAttribute("mHeatmapPoints", heatmapPoints);
                    List<Integer> values = new ArrayList<>(heatmapPointsValues.values());
                    Collections.sort(values, Collections.reverseOrder());
                    if (values.size() >= 3) {
                        max = values.get(2);
                    } else if (values.size() >= 2) {
                        max = values.get(1);
                    } else if (!values.isEmpty()) {
                        max = values.get(0);
                    } else {
                        max = 1;
                    }
                    model.addAttribute("mHeatmapMax", (max * 1.5D));

                    // Heatmap End Point
                    max = 0;
                    JsonArray heatmapEndPoints = new JsonArray();
                    if (!heatmapEndPointsValues.isEmpty()) {
                        for (String coordinates : heatmapEndPointsValues.keySet()) {
                            int value = heatmapEndPointsValues.get(coordinates);
//                        if (value > max) {
//                            max = value;
//                        }

                            JsonObject point = new JsonObject();
                            point.addProperty("x", StringUtils.split(coordinates, ";")[0]);
                            point.addProperty("y", StringUtils.split(coordinates, ";")[1]);
                            point.addProperty("value", value);
                            heatmapEndPoints.add(point);
                        }
                        values = new ArrayList<>(heatmapEndPointsValues.values());
                        Collections.sort(values, Collections.reverseOrder());
                        if (values.size() >= 3) {
                            max = values.get(2);
                        } else if (values.size() >= 2) {
                            max = values.get(1);
                        } else if (!values.isEmpty()) {
                            max = values.get(0);
                        } else {
                            max = 1;
                        }
                    }
                    model.addAttribute("mHeatmapEndPoints", heatmapEndPoints);
                    model.addAttribute("mHeatmapEndMax", (max * 1.5D));

                    Map<String, EventWrapper> groupedChannelThird = EventHelper.groupEventsByChannelAndThird(events);
                    model.addAttribute("mGroupedChannelThird", groupedChannelThird);

                    Map<String, EventWrapper> groupedChannelThirdEnd = EventHelper.groupEventsByChannelAndThird(events, false);
                    model.addAttribute("mGroupedChannelThirdEnd", groupedChannelThirdEnd);

                    Map<String, EventWrapper> groupedChannel = EventHelper.groupEventsByChannel(events);
                    model.addAttribute("mGroupedChannel", groupedChannel);

                    Map<String, EventWrapper> groupedThird = EventHelper.groupEventsByThird(events);
                    model.addAttribute("mGroupedThird", groupedThird);

                    Map<String, EventWrapper> groupedTime = EventHelper.groupEventsByTime(events);
                    model.addAttribute("mGroupedTime", groupedTime);

                    Map<String, EventWrapper> groupedAngleZone = EventHelper.groupEventsByAngleZone(events);
                    model.addAttribute("mGroupedAngleZone", groupedAngleZone);

                    Map<Long, EventWrapper> groupedPlayer = EventHelper.groupEventsByPlayer(events);
                    // Step 1: Convert Map to List of Map Entries
                    List<Map.Entry<Long, EventWrapper>> entryList = new ArrayList<>(groupedPlayer.entrySet());
                    // Step 2: Sort the list using a custom comparator
                    Collections.sort(entryList, new Comparator<Map.Entry<Long, EventWrapper>>() {
                        @Override
                        public int compare(Map.Entry<Long, EventWrapper> o1, Map.Entry<Long, EventWrapper> o2) {
                            return Integer.compare(o2.getValue().getEvents().size(), o1.getValue().getEvents().size());
                        }
                    });
                    // Step 3: Create a new LinkedHashMap to maintain insertion order
                    LinkedHashMap<Long, EventWrapper> sortedMap = new LinkedHashMap<>();
                    for (Map.Entry<Long, EventWrapper> entry : entryList) {
                        sortedMap.put(entry.getKey(), entry.getValue());
                    }
                    model.addAttribute("mGroupedPlayer", sortedMap);

                    model.addAttribute("mPlayers", players);
                    model.addAttribute("mPlayerFiltered", params.containsKey("playerId"));
                }
            }
        }

        return PAGE_POSITIONAL_CONTENT;
    }
}
