package sics.controller.team;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.Fixture;
import sics.domain.FixtureDetails;
import sics.enums.GroupedField;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/team/trend")
public class TeamTrendController extends BaseController {

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId") && params.containsKey("teamId")) {
                params.put("type", "team");

                List<String> teamIds = Arrays.asList(StringUtils.split(params.get("teamId").toString(), "|"));
                params.remove("teamId");

                List<Long> teamObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_FIXTURE);
                Map<Long, List<Integer>> alreadyValidMatchdays = new HashMap<>();
                for (DocumentRow row : result) {
                    if (row.getTeamId() != null && row.getMatchDay() != null) {
                        alreadyValidMatchdays.putIfAbsent(row.getTeamId(), new ArrayList<Integer>());
                        alreadyValidMatchdays.get(row.getTeamId()).add(row.getMatchDay());
                    }
                }

                // gestione dei valori a 0
                Map<String, Object> paramsCloned = new HashMap<>(params);
                paramsCloned.put("eventTypeId", null);
                paramsCloned.remove("tagTypeId");
                List<DocumentRow> allFixtures = MongoHelper.getDocumentsByParams(paramsCloned, GroupedField.TEAM_FIXTURE);
                for (DocumentRow row : allFixtures) {
                    if (row.getTeamId() != null && row.getMatchDay() != null) {
                        if (alreadyValidMatchdays.containsKey(row.getTeamId())) {
                            if (!alreadyValidMatchdays.get(row.getTeamId()).contains(row.getMatchDay())) {
                                row.setTotal(0D);
                                row.setTotalSum(0D);
                                result.add(row);
                            }
                        } else {
                            row.setTotal(0D);
                            row.setTotalSum(0D);
                            result.add(row);
                        }
                    }
                }

                // devo ora dividere i record per team
                Map<Long, List<DocumentRow>> resultGroupedByTeam = new HashMap<>();
                for (DocumentRow row : result) {
                    resultGroupedByTeam.putIfAbsent(row.getTeamId(), new ArrayList<DocumentRow>());
                    resultGroupedByTeam.get(row.getTeamId()).add(row);

                    if (!teamObjectsAdded.contains(row.getTeamId())) {
                        JsonObject teamObject = new JsonObject();
                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                        teamObjects.add(teamObject);
                        teamObjectsAdded.add(row.getTeamId());
                    }
                    // aggiungo tutti i team delle fixture (serve per la tabella)
                    // qua dovrei tenere una lista di quelli che ho già messo ma non credo cambi molto la dimensione della pagina
                    if (row.getFixtureId() != null) {
                        FixtureDetails fixture = fixtureDetails.get(row.getFixtureId());
                        if (fixture != null) {
                            if (fixture.getHomeTeamId() != null && !teamObjectsAdded.contains(fixture.getHomeTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(fixture.getHomeTeamId().toString(), teams.get(fixture.getHomeTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(fixture.getHomeTeamId());
                            }
                            if (fixture.getAwayTeamId() != null && !teamObjectsAdded.contains(fixture.getAwayTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(fixture.getAwayTeamId().toString(), teams.get(fixture.getAwayTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(fixture.getAwayTeamId());
                            }
                        }
                    }
                }
                chartData.add("teams", teamObjects);

                List<Long> fixtureIds = new ArrayList<>();
                for (DocumentRow row : result) {
                    if (row.getFixtureId() != null && !fixtureIds.contains(row.getFixtureId())) {
                        fixtureIds.add(row.getFixtureId());
                    }
                }

                if (fixtureIds.isEmpty()) {
                    return chartData.toString();
                }

                // popolo game date
                List<Fixture> fixtures = mService.getFixtureByIds(fixtureIds);
                Map<Long, Fixture> fixtureMap = new HashMap<>();
                for (Fixture fixture : fixtures) {
                    fixtureMap.put(fixture.getId(), fixture);
                }
                for (DocumentRow row : result) {
                    if (row.getFixtureId() != null && fixtureMap.containsKey(row.getFixtureId())) {
                        row.setGameDate(fixtureMap.get(row.getFixtureId()).getGameDate());
                    }
                }

                Map<Integer, List<Element>> matchdayData = new HashMap<>();
                JsonArray fixtureObjects = new JsonArray();
                for (Long teamId : resultGroupedByTeam.keySet()) {
                    if (teamIds.contains(teamId.toString())) {
                        for (DocumentRow row : resultGroupedByTeam.get(teamId)) {
                            JsonObject fixtureObject = new JsonObject();
                            fixtureObject.addProperty(row.getTeamId() + "-" + row.getMatchDay(), fixtureMap.get(row.getFixtureId()).getJson());
                            fixtureObjects.add(fixtureObject);

                            matchdayData.putIfAbsent(row.getMatchDay(), new ArrayList<Element>());
                            Element element = new Element(row.getTeamId(), row.getTotal(), teams.get(row.getTeamId()).getLogo());
                            matchdayData.get(row.getMatchDay()).add(element);
                        }
                    }
                }
                chartData.add("fixtures", fixtureObjects);

                int imageSizeToReduce = teamIds.size() / 10 * 6;
                JsonArray dataArray = new JsonArray();
                TreeMap<Integer, List<Element>> sortedMatchdayData = new TreeMap<>(matchdayData);
                for (Integer matchDay : sortedMatchdayData.keySet()) {
                    JsonObject data = new JsonObject();
                    data.addProperty("matchDay", matchDay);
                    for (Element element : sortedMatchdayData.get(matchDay)) {
                        data.addProperty("value" + element.getTeamId(), element.getValue());

                        JsonObject logoObject = new JsonObject();
                        String src = element.getLogo();
                        logoObject.addProperty("src", src);
                        logoObject.addProperty("width", 48 - imageSizeToReduce);
                        logoObject.addProperty("height", 48 - imageSizeToReduce);
                        data.add("logo" + element.getTeamId(), logoObject);
                    }
                    dataArray.add(data);
                }
                chartData.add("data", dataArray);

                JsonArray secondChartData = new JsonArray();
                int matchdayDivider = 5, matchdayCounter = 0, matchdayIteration = 1;
                Map<Long, Double> matchdayTotals = new HashMap<>();  // <Contatore, Totale>
                for (Integer matchDay : sortedMatchdayData.keySet()) {
                    if (matchdayCounter >= matchdayDivider) {
                        // dovrei essere arrivato a matchdayDivider quindi scrivo quello che ho
                        JsonObject data = new JsonObject();
                        data.addProperty("matchDay", SpringApplicationContextHelper.getMessage("team.trend.matchdays", RequestContextUtils.getLocale(request)) + " " + ((matchdayIteration - 1) * matchdayDivider + 1) + " - " + (matchdayIteration * matchdayDivider));
                        for (Long teamId : matchdayTotals.keySet()) {
                            data.addProperty("value" + teamId, matchdayTotals.get(teamId));
                        }
                        secondChartData.add(data);
                        matchdayTotals.clear();
                        matchdayIteration++;
                        matchdayCounter = 0;
                    }
                    for (Element element : sortedMatchdayData.get(matchDay)) {
                        matchdayTotals.putIfAbsent(element.getTeamId(), 0D);
                        matchdayTotals.put(element.getTeamId(), matchdayTotals.get(element.getTeamId()) + element.getValue());
                    }
                    matchdayCounter++;
                }
                if (!matchdayTotals.isEmpty()) {
                    JsonObject data = new JsonObject();
                    data.addProperty("matchDay", SpringApplicationContextHelper.getMessage("team.trend.matchdays", RequestContextUtils.getLocale(request)) + " " + ((matchdayIteration - 1) * matchdayDivider + 1) + " - " + (sortedMatchdayData.keySet().toArray()[sortedMatchdayData.keySet().size() - 1]));
                    for (Long teamId : matchdayTotals.keySet()) {
                        data.addProperty("value" + teamId, matchdayTotals.get(teamId));
                    }
                    secondChartData.add(data);
                }
                chartData.add("secondData", secondChartData);

                // media campionato
                params.remove("teamId");
                params.remove("fixtureIds");
                Double competitionAverage = MongoHelper.getCompetitionAverageByParams(params);
                competitionAverage = Math.round(competitionAverage * 10) / 10D;
                chartData.addProperty("competitionAverage", competitionAverage);
            }
        }

        return chartData.toString();
    }

    private class Element {

        private Long teamId;
        private Double value;
        private String logo;
        private String localLogo;

        public Element(Long teamId, Double value, String logo) {
            this.teamId = teamId;
            this.value = value;
            this.logo = logo;

            if (StringUtils.isNotBlank(logo)) {
                this.logo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + logo + ".png?" + (new Date().getTime());
                this.localLogo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/" + logo + ".png?" + (new Date().getTime());
            } else {
                this.logo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?" + (new Date().getTime());
                this.localLogo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/teamlogo/unknownxx.png?" + (new Date().getTime());
            }
        }

        public Long getTeamId() {
            return teamId;
        }

        public void setTeamId(Long teamId) {
            this.teamId = teamId;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getLocalLogo() {
            return localLogo;
        }

        public void setLocalLogo(String localLogo) {
            this.localLogo = localLogo;
        }
    }
}
