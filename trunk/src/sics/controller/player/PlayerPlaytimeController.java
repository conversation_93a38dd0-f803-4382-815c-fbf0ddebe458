package sics.controller.player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.Fixture;
import sics.domain.FixturePlayer;
import sics.domain.User;
import sics.helper.GlobalHelper;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/playtime")
public class PlayerPlaytimeController extends BaseController {

    private final static String PAGE_PLAYTIME_CONTENT = "player-data/playtime-content.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("teamId")) {
                model.addAttribute("mValid", true);

                params.put("type", "player");
                List<FixturePlayer> result = mService.getFixturePlayersByParams(params);
                if (result != null && !result.isEmpty()) {
                    List<Long> fixtureIds = new ArrayList<>();
                    for (FixturePlayer row : result) {
                        if (row.getFixtureId() != null) {
                            if (!fixtureIds.contains(row.getFixtureId())) {
                                fixtureIds.add(row.getFixtureId());
                            }
                        }
                    }

                    if (!fixtureIds.isEmpty()) {
                        List<Fixture> fixtures = mService.getFixtureByIds(fixtureIds);
                        if (fixtures != null && fixtureIds.size() == fixtures.size()) {
                            Map<Long, Fixture> fixtureMap = new HashMap<>();
                            for (Fixture fixture : fixtures) {
                                fixtureMap.put(fixture.getId(), fixture);
                            }

                            // uso linked così dovrei averceli divisi per ruolo
                            Map<Long, Map<Long, FixturePlayer>> groupedByPlayer = new LinkedHashMap<>();
                            Long maxPlaytime = null;
                            for (FixturePlayer row : result) {
                                if (row.getPlayerId() != null) {
                                    groupedByPlayer.putIfAbsent(row.getPlayerId(), new HashMap<Long, FixturePlayer>());
                                    groupedByPlayer.get(row.getPlayerId()).put(row.getFixtureId(), row);
                                }
                            }

                            for (Long playerId : groupedByPlayer.keySet()) {
                                long totalPlaytime = 0L;
                                for (FixturePlayer row : groupedByPlayer.get(playerId).values()) {
                                    if (row.getPlayTime() != null) {
                                        totalPlaytime += row.getPlayTime();
                                    }
                                }
                                for (FixturePlayer row : groupedByPlayer.get(playerId).values()) {
                                    row.setTotalPlaytime(totalPlaytime);
                                    if (maxPlaytime == null || totalPlaytime > maxPlaytime) {
                                        maxPlaytime = totalPlaytime;
                                    }
                                }
                            }

                            // ora devo ordinare per ruolo e totale minuti
                            List<FixturePlayer> goalkeeperRows = new ArrayList<>();
                            List<FixturePlayer> defenderRows = new ArrayList<>();
                            List<FixturePlayer> midfielderRows = new ArrayList<>();
                            List<FixturePlayer> strikersRows = new ArrayList<>();

                            for (Long playerId : groupedByPlayer.keySet()) {
                                for (FixturePlayer row : groupedByPlayer.get(playerId).values()) {
                                    row.setMaxPlaytime(maxPlaytime);

                                    if (row.getPositionId() != null) {
                                        if (Long.compare(row.getPositionId(), 1L) == 0) {
                                            goalkeeperRows.add(row);
                                        } else if (Long.compare(row.getPositionId(), 2L) == 0) {
                                            defenderRows.add(row);
                                        } else if (Long.compare(row.getPositionId(), 3L) == 0) {
                                            midfielderRows.add(row);
                                        } else if (Long.compare(row.getPositionId(), 4L) == 0) {
                                            strikersRows.add(row);
                                        }
                                    }
                                }
                            }

                            Collections.sort(goalkeeperRows, new Comparator<FixturePlayer>() {
                                @Override
                                public int compare(FixturePlayer o1, FixturePlayer o2) {
                                    return o2.getTotalPlaytime().compareTo(o1.getTotalPlaytime());
                                }
                            });
                            Collections.sort(defenderRows, new Comparator<FixturePlayer>() {
                                @Override
                                public int compare(FixturePlayer o1, FixturePlayer o2) {
                                    return o2.getTotalPlaytime().compareTo(o1.getTotalPlaytime());
                                }
                            });
                            Collections.sort(midfielderRows, new Comparator<FixturePlayer>() {
                                @Override
                                public int compare(FixturePlayer o1, FixturePlayer o2) {
                                    return o2.getTotalPlaytime().compareTo(o1.getTotalPlaytime());
                                }
                            });
                            Collections.sort(strikersRows, new Comparator<FixturePlayer>() {
                                @Override
                                public int compare(FixturePlayer o1, FixturePlayer o2) {
                                    return o2.getTotalPlaytime().compareTo(o1.getTotalPlaytime());
                                }
                            });

                            List<Long> playerIdsSorted = new ArrayList<>();
                            for (FixturePlayer row : goalkeeperRows) {
                                if (row.getPlayerId() != null && !playerIdsSorted.contains(row.getPlayerId())) {
                                    playerIdsSorted.add(row.getPlayerId());
                                }
                            }
                            for (FixturePlayer row : defenderRows) {
                                if (row.getPlayerId() != null && !playerIdsSorted.contains(row.getPlayerId())) {
                                    playerIdsSorted.add(row.getPlayerId());
                                }
                            }
                            for (FixturePlayer row : midfielderRows) {
                                if (row.getPlayerId() != null && !playerIdsSorted.contains(row.getPlayerId())) {
                                    playerIdsSorted.add(row.getPlayerId());
                                }
                            }
                            for (FixturePlayer row : strikersRows) {
                                if (row.getPlayerId() != null && !playerIdsSorted.contains(row.getPlayerId())) {
                                    playerIdsSorted.add(row.getPlayerId());
                                }
                            }

                            model.addAttribute("mFixtureIds", fixtureIds);
                            model.addAttribute("mFixtureMap", fixtureMap);
                            model.addAttribute("mSortedPlayerIds", playerIdsSorted);
                            model.addAttribute("mRows", groupedByPlayer);
                            model.addAttribute("mSeasonId", Long.valueOf(params.get("seasonId").toString()));
                            model.addAttribute("mTeamId", Long.valueOf(params.get("teamId").toString()));
                            model.addAttribute("mPlayers", players);
                            model.addAttribute("mCountries", countries);
                            model.addAttribute("mPositions", positions);
                            model.addAttribute("mTeamPlayers", seasonTeamPlayerPositions);
                            model.addAttribute("mTeams", teams);
                            model.addAttribute("mUser", curUser);
                        }
                    }
                }
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_PLAYTIME_CONTENT;
    }
}
