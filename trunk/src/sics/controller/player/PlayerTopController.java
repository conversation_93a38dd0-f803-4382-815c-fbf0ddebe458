package sics.controller.player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/top")
public class PlayerTopController extends BaseController {

    private final static String PAGE_TOP_CONTENT = "player-data/top-content.jsp";

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                model.addAttribute("mValid", true);
                params.put("type", "player");

                double minEventAmount = 0, maxEventAmount = 9999;
                if (params.containsKey("minEventAmount")) {
                    minEventAmount = Long.parseLong(params.get("minEventAmount").toString());
                    maxEventAmount = Long.parseLong(params.get("maxEventAmount").toString());

                    params.remove("minEventAmount");
                    params.remove("maxEventAmount");
                }

                DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());

                double maxTotal = 1;
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER);
                // devo ora dividere i record per player
                List<DocumentRow> rowToRemove = new ArrayList<>();
                for (DocumentRow row : result) {
                    if (row.getTotal() != null) {
                        if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                            rowToRemove.add(row);
                        }

                        if (row.getTotal() > maxTotal) {
                            maxTotal = row.getTotal();
                        }
                    }
                }
                result.removeAll(rowToRemove);
                model.addAttribute("mMinPlaytime", Utils.adjustRowsForPlaytime(result, params, null));
                if (displayType.equals(DisplayType.P90)) {
                    Utils.calculateP90V2(result, params);
                } else if (displayType.equals(DisplayType.TOUCHES)) {
                    Utils.calculate100Touches(result, params);
                } else if (displayType.equals(DisplayType.AVERAGE)) {
                    Utils.calculateAverage(result, params);
                }

                Map<Long, List<DocumentRow>> groupedByTeam = new HashMap<>();
                for (DocumentRow row : result) {
                    if (row.getTeamId() != null) {
                        groupedByTeam.putIfAbsent(row.getTeamId(), new ArrayList<DocumentRow>());
                        groupedByTeam.get(row.getTeamId()).add(row);
                    }
                }

                for (Long teamId : groupedByTeam.keySet()) {
                    List<DocumentRow> rows = groupedByTeam.get(teamId);
                    switch (displayType) {
                        case P90:
                            Collections.sort(rows, new Comparator<DocumentRow>() {
                                @Override
                                public int compare(DocumentRow o1, DocumentRow o2) {
                                    return o2.getTotalP90().compareTo(o1.getTotalP90());
                                }
                            });
                            break;
                        case TOUCHES:
                            Collections.sort(rows, new Comparator<DocumentRow>() {
                                @Override
                                public int compare(DocumentRow o1, DocumentRow o2) {
                                    return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                                }
                            });
                            break;
                        case AVERAGE:
                            Collections.sort(rows, new Comparator<DocumentRow>() {
                                @Override
                                public int compare(DocumentRow o1, DocumentRow o2) {
                                    return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                                }
                            });
                            break;
                        default:
                            Collections.sort(rows, new Comparator<DocumentRow>() {
                                @Override
                                public int compare(DocumentRow o1, DocumentRow o2) {
                                    return o2.getTotal().compareTo(o1.getTotal());
                                }
                            });
                            break;
                    }

                    if (rows.size() > 3) {
                        rows.removeAll(rows.subList(3, rows.size()));
                    } else {
                        int maxIterations = 10; // per sicurezza
                        while (rows.size() < 3 && maxIterations > 0) {
                            maxIterations--;
                            rows.add(new DocumentRow());
                        }
                    }
                }

                Double minValue = null, maxValue = null;
                for (Long teamId : groupedByTeam.keySet()) {
                    List<DocumentRow> rows = groupedByTeam.get(teamId);
                    for (DocumentRow row : rows) {
                        switch (displayType) {
                            case P90:
                                if (row.getTotalP90() != null) {
                                    if (minValue == null || row.getTotalP90() < minValue) {
                                        minValue = row.getTotalP90();
                                    }
                                    if (maxValue == null || row.getTotalP90() > maxValue) {
                                        maxValue = row.getTotalP90();
                                    }
                                }
                                break;
                            case TOUCHES:
                                if (row.getTotal100Touches() != null) {
                                    if (minValue == null || row.getTotal100Touches() < minValue) {
                                        minValue = row.getTotal100Touches();
                                    }
                                    if (maxValue == null || row.getTotal100Touches() > maxValue) {
                                        maxValue = row.getTotal100Touches();
                                    }
                                }
                                break;
                            case AVERAGE:
                                if (row.getTotalAverage() != null) {
                                    if (minValue == null || row.getTotalAverage() < minValue) {
                                        minValue = row.getTotalAverage();
                                    }
                                    if (maxValue == null || row.getTotalAverage() > maxValue) {
                                        maxValue = row.getTotalAverage();
                                    }
                                }
                                break;
                            default:
                                if (row.getTotal() != null) {
                                    if (minValue == null || row.getTotal() < minValue) {
                                        minValue = row.getTotal();
                                    }
                                    if (maxValue == null || row.getTotal() > maxValue) {
                                        maxValue = row.getTotal();
                                    }
                                }
                                break;
                        }
                    }
                }

                Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
                String tagTypeIds = null;
                if (params.containsKey("tagTypeId")) {
                    tagTypeIds = params.get("tagTypeId").toString();
                }
                model.addAttribute("mIsOpposite", Utils.isOpposite(eventTypeId, tagTypeIds));
                model.addAttribute("mRows", groupedByTeam);
                model.addAttribute("mMinValue", minValue);
                model.addAttribute("mMaxValue", maxValue);
                model.addAttribute("mTeams", teams);
                model.addAttribute("mPlayers", players);
                model.addAttribute("mCountries", countries);
                model.addAttribute("mAdvancedMetrics", advancedMetrics);
                model.addAttribute("mDisplayType", displayType.toString());
                model.addAttribute("mMaxTotal", maxTotal);
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_TOP_CONTENT;
    }
}
