package sics.controller.player;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.wrapper.DocumentRowWrapper;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/scatterplot")
public class PlayerScatterplotController extends BaseController {

    private final List<Integer> imageSizes = new ArrayList<>(Arrays.asList(48, 36, 27, 15, 10, 7, 5));

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeIdX") && params.containsKey("eventTypeIdY")) {
                params.put("type", "player");

                double minEventAmount = 0, maxEventAmount = 9999;
                double minEventAmountY = 0, maxEventAmountY = 9999;
                if (params.containsKey("minEventAmount")) {
                    minEventAmount = Long.parseLong(params.get("minEventAmount").toString());
                    maxEventAmount = Long.parseLong(params.get("maxEventAmount").toString());

                    params.remove("minEventAmount");
                    params.remove("maxEventAmount");
                }
                if (params.containsKey("minEventAmountY")) {
                    minEventAmountY = Long.parseLong(params.get("minEventAmountY").toString());
                    maxEventAmountY = Long.parseLong(params.get("maxEventAmountY").toString());

                    params.remove("minEventAmountY");
                    params.remove("maxEventAmountY");
                }

                Map<String, Object> paramsClone = new HashMap<>(params);

                List<Integer> extraIndex = new ArrayList<>();
                for (String param : params.keySet()) {
                    if (param.startsWith("competitionId") && !StringUtils.equalsIgnoreCase(param, "competitionId")) {
                        extraIndex.add(Integer.valueOf(StringUtils.replace(param, "competitionId", "")));
                    }
                }
                for (Integer index : extraIndex) {
                    params.remove("competitionId" + index);
                    params.remove("teamId" + index);
                    params.remove("playerId" + index);
                }

                String eventTypeIdX = params.get("eventTypeIdX").toString();
                Long typeIdX = null;
                String tagIdX = null, zoneIdX = null;
                if (StringUtils.contains(eventTypeIdX, "$") && StringUtils.contains(eventTypeIdX, "@")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "$"));
                    tagIdX = StringUtils.substringBetween(eventTypeIdX, "$", "@");
                    zoneIdX = StringUtils.substringAfterLast(eventTypeIdX, "@");
                } else if (StringUtils.contains(eventTypeIdX, "$")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "$"));
                    tagIdX = StringUtils.substringAfterLast(eventTypeIdX, "$");
                } else if (StringUtils.contains(eventTypeIdX, "@")) {
                    typeIdX = Long.valueOf(StringUtils.substringBefore(eventTypeIdX, "@"));
                    zoneIdX = StringUtils.substringAfterLast(eventTypeIdX, "@");
                } else {
                    typeIdX = Long.valueOf(eventTypeIdX);
                }
                if (StringUtils.isNotBlank(tagIdX)) {
                    List<String> tmpTagTypes = Arrays.asList(StringUtils.split(tagIdX, "-"));
                    Collections.sort(tmpTagTypes);
                    tagIdX = StringUtils.join(tmpTagTypes, "|");
                }
                String eventTypeIdY = params.get("eventTypeIdY").toString();
                Long typeIdY = null;
                String tagIdY = null, zoneIdY = null;
                if (StringUtils.contains(eventTypeIdY, "$") && StringUtils.contains(eventTypeIdY, "@")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "$"));
                    tagIdY = StringUtils.substringBetween(eventTypeIdY, "$", "@");
                    zoneIdY = StringUtils.substringAfterLast(eventTypeIdY, "@");
                } else if (StringUtils.contains(eventTypeIdY, "$")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "$"));
                    tagIdY = StringUtils.substringAfterLast(eventTypeIdY, "$");
                } else if (StringUtils.contains(eventTypeIdY, "@")) {
                    typeIdY = Long.valueOf(StringUtils.substringBefore(eventTypeIdY, "@"));
                    zoneIdY = StringUtils.substringAfterLast(eventTypeIdY, "@");
                } else {
                    typeIdY = Long.valueOf(eventTypeIdY);
                }
                if (StringUtils.isNotBlank(tagIdY)) {
                    List<String> tmpTagTypes = Arrays.asList(StringUtils.split(tagIdY, "-"));
                    Collections.sort(tmpTagTypes);
                    tagIdY = StringUtils.join(tmpTagTypes, "|");
                }

                params.remove("eventTypeIdX");
                params.remove("eventTypeIdY");

                List<Long> playerIds = new ArrayList<>();
                if (params.containsKey("playerId")) {
                    for (String playerId : StringUtils.split(params.get("playerId").toString(), "|")) {
                        playerIds.add(Long.valueOf(playerId));
                    }
                }

                double maxTotal = 0, maxTotalY = 0;
                List<String> extraTeamPlayerIds = new ArrayList<>();
                List<String> teamPlayerIds = new ArrayList<>();
                List<Long> teamObjectsAdded = new ArrayList<>();
                List<Long> playerObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                JsonArray playerObjects = new JsonArray();
                Map<Long, Map<Long, Map<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>>> results = new HashMap<>();
                List<String> seasonIds = Arrays.asList(StringUtils.split(params.get("seasonId").toString(), "/"));
                List<Long> seasonIdList = new ArrayList<>();
                DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                if (seasonIds != null && !seasonIds.isEmpty()) {
                    params.remove("seasonId");
                    for (String seasonId : seasonIds) {
                        params.remove("tagTypeId");
                        params.put("seasonId", seasonId);
                        
                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                        Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);
                        seasonIdList.add(tmpSeasonId);

                        // calcolo evento asse X
                        params.put("eventTypeId", typeIdX);
                        if (StringUtils.isNotBlank(tagIdX)) {
                            params.put("tagTypeId", tagIdX);
                        }
                        MongoHelper.adjustParamsByZoneId(params, zoneIdX);
                        List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                        List<DocumentRow> rowToRemove = new ArrayList<>();
                        for (DocumentRow row : result) {
                            if (row.getTotal() != null) {
                                if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                                    rowToRemove.add(row);
                                }

                                if (row.getTotal() > maxTotal) {
                                    maxTotal = row.getTotal();
                                }
                            }
                        }
                        result.removeAll(rowToRemove);
                        if (StringUtils.isNotBlank(zoneIdX)) {
                            for (DocumentRow row : result) {
                                row.setZoneId(zoneIdX);
                            }
                        }
                        chartData.addProperty("mMinPlaytime", Utils.adjustRowsForPlaytime(result, params, playerIds));
                        if (displayType.equals(DisplayType.P90)) {
                            Utils.calculateP90V2(result, params);
                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                            Utils.calculate100Touches(result, params);
                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                            Utils.calculateAverage(result, params);
                        }

                        for (DocumentRow row : result) {
                            row.setSeasonId(tmpSeasonId);

                            if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(row.getTeamId());
                            }
                            if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                JsonObject playerObject = new JsonObject();
                                playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                playerObjects.add(playerObject);
                                playerObjectsAdded.add(row.getPlayerId());
                            }

                            results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>>());
                            results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, Map<Long, DocumentRow>>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, Map<Long, DocumentRow>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).putIfAbsent(row.getTeamId(), new HashMap<Long, DocumentRow>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).get(row.getTeamId()).put(row.getPlayerId(), row);
                        }

                        // calcolo evento asse Y
                        params.remove("tagTypeId");
                        params.put("eventTypeId", typeIdY);
                        if (StringUtils.isNotBlank(tagIdY)) {
                            params.put("tagTypeId", tagIdY);
                        }
                        MongoHelper.adjustParamsByZoneId(params, zoneIdY);
                        List<DocumentRow> resultY = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                        rowToRemove = new ArrayList<>();
                        for (DocumentRow row : resultY) {
                            if (row.getTotal() != null) {
                                if (row.getTotal() < minEventAmountY || row.getTotal() > maxEventAmountY) {
                                    rowToRemove.add(row);
                                }

                                if (row.getTotal() > maxTotalY) {
                                    maxTotalY = row.getTotal();
                                }
                            }
                        }
                        resultY.removeAll(rowToRemove);
                        if (StringUtils.isNotBlank(zoneIdY)) {
                            for (DocumentRow row : resultY) {
                                row.setZoneId(zoneIdY);
                            }
                        }
                        Utils.adjustRowsForPlaytime(result, params, playerIds);
                        if (displayType.equals(DisplayType.P90)) {
                            Utils.calculateP90V2(resultY, params);
                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                            Utils.calculate100Touches(resultY, params);
                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                            Utils.calculateAverage(resultY, params);
                        }

                        for (DocumentRow row : resultY) {
                            row.setSeasonId(tmpSeasonId);

                            if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(row.getTeamId());
                            }
                            if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                JsonObject playerObject = new JsonObject();
                                playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                playerObjects.add(playerObject);
                                playerObjectsAdded.add(row.getPlayerId());
                            }

                            results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>>());
                            results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, Map<Long, DocumentRow>>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, Map<Long, DocumentRow>>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).putIfAbsent(row.getTeamId(), new HashMap<Long, DocumentRow>());
                            results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).get(row.getTeamId()).put(row.getPlayerId(), row);
                            String teamPlayerId = row.getTeamId() + ":" + row.getPlayerId();
                            if (!teamPlayerIds.contains(teamPlayerId)) {
                                teamPlayerIds.add(teamPlayerId);
                            }
                        }
                    }

                    // se ci sono calcolo ora i dati dei team / giocatori extra
                    for (String seasonId : seasonIds) {
                        seasonIdList.add(Long.valueOf(seasonId));
                        params.put("seasonId", seasonId);

                        for (Integer index : extraIndex) {
                            if (paramsClone.containsKey("competitionId" + index)) {
                                params.remove("competitionId");
                                params.remove("teamId");
                                params.remove("playerId");
                                params.put("competitionId", paramsClone.get("competitionId" + index));
                                if (paramsClone.containsKey("teamId" + index)) {
                                    params.put("teamId", paramsClone.get("teamId" + index));
                                    if (paramsClone.containsKey("playerId" + index)) {
                                        params.put("playerId", paramsClone.get("playerId" + index));
                                    }
                                }

                                // calcolo evento asse X
                                params.remove("tagTypeId");
                                params.put("eventTypeId", typeIdX);
                                if (StringUtils.isNotBlank(tagIdX)) {
                                    params.put("tagTypeId", tagIdX);
                                }
                                MongoHelper.adjustParamsByZoneId(params, zoneIdX);
                                List<DocumentRow> tmpResultX = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                                List<DocumentRow> rowToRemove = new ArrayList<>();
                                for (DocumentRow row : tmpResultX) {
                                    if (row.getTotal() != null) {
                                        if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                                            rowToRemove.add(row);
                                        }

                                        if (row.getTotal() > maxTotal) {
                                            maxTotal = row.getTotal();
                                        }
                                    }
                                }
                                tmpResultX.removeAll(rowToRemove);
                                if (StringUtils.isNotBlank(zoneIdX)) {
                                    for (DocumentRow row : tmpResultX) {
                                        row.setZoneId(zoneIdX);
                                    }
                                }
                                Utils.adjustRowsForPlaytime(tmpResultX, params, playerIds);
                                if (displayType.equals(DisplayType.P90)) {
                                    Utils.calculateP90V2(tmpResultX, params);
                                } else if (displayType.equals(DisplayType.TOUCHES)) {
                                    Utils.calculate100Touches(tmpResultX, params);
                                } else if (displayType.equals(DisplayType.AVERAGE)) {
                                    Utils.calculateAverage(tmpResultX, params);
                                }

                                for (DocumentRow row : tmpResultX) {
                                    Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                    Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
                                    tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                    row.setSeasonId(tmpSeasonId);
                                    if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                        JsonObject teamObject = new JsonObject();
                                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                        teamObjects.add(teamObject);
                                        teamObjectsAdded.add(row.getTeamId());
                                    }
                                    if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                        JsonObject playerObject = new JsonObject();
                                        playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                        playerObjects.add(playerObject);
                                        playerObjectsAdded.add(row.getPlayerId());
                                    }

                                    results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>>());
                                    results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, Map<Long, DocumentRow>>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, Map<Long, DocumentRow>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).putIfAbsent(row.getTeamId(), new HashMap<Long, DocumentRow>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).get(row.getTeamId()).put(row.getPlayerId(), row);
                                }

                                // calcolo evento asse Y
                                params.remove("tagTypeId");
                                params.put("eventTypeId", typeIdY);
                                if (StringUtils.isNotBlank(tagIdY)) {
                                    params.put("tagTypeId", tagIdY);
                                }
                                MongoHelper.adjustParamsByZoneId(params, zoneIdY);
                                List<DocumentRow> tmpResultY = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                                rowToRemove = new ArrayList<>();
                                for (DocumentRow row : tmpResultY) {
                                    if (row.getTotal() != null) {
                                        if (row.getTotal() < minEventAmountY || row.getTotal() > maxEventAmountY) {
                                            rowToRemove.add(row);
                                        }

                                        if (row.getTotal() > maxTotalY) {
                                            maxTotalY = row.getTotal();
                                        }
                                    }
                                }
                                tmpResultY.removeAll(rowToRemove);
                                if (StringUtils.isNotBlank(zoneIdY)) {
                                    for (DocumentRow row : tmpResultY) {
                                        row.setZoneId(zoneIdY);
                                    }
                                }
                                Utils.adjustRowsForPlaytime(tmpResultY, params, playerIds);
                                if (displayType.equals(DisplayType.P90)) {
                                    Utils.calculateP90V2(tmpResultY, params);
                                } else if (displayType.equals(DisplayType.TOUCHES)) {
                                    Utils.calculate100Touches(tmpResultY, params);
                                } else if (displayType.equals(DisplayType.AVERAGE)) {
                                    Utils.calculateAverage(tmpResultY, params);
                                }

                                for (DocumentRow row : tmpResultY) {
                                    Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                    Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
                                    tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                    row.setSeasonId(tmpSeasonId);
                                    if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                        JsonObject teamObject = new JsonObject();
                                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                        teamObjects.add(teamObject);
                                        teamObjectsAdded.add(row.getTeamId());
                                    }
                                    if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                        JsonObject playerObject = new JsonObject();
                                        playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                        playerObjects.add(playerObject);
                                        playerObjectsAdded.add(row.getPlayerId());
                                    }

                                    results.putIfAbsent(row.getSeasonId(), new HashMap<Long, Map<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>>());
                                    results.get(row.getSeasonId()).putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Map<Long, Map<Long, DocumentRow>>>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Map<Long, Map<Long, DocumentRow>>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new HashMap<Long, Map<Long, DocumentRow>>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).putIfAbsent(row.getTeamId(), new HashMap<Long, DocumentRow>());
                                    results.get(row.getSeasonId()).get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).get(row.getTeamId()).put(row.getPlayerId(), row);
                                    String teamPlayerId = row.getTeamId() + ":" + row.getPlayerId();
                                    if (!extraTeamPlayerIds.contains(teamPlayerId)) {
                                        extraTeamPlayerIds.add(teamPlayerId);
                                    }
                                }
                            }
                        }
                    }
                    chartData.add("teams", teamObjects);
                    chartData.add("players", playerObjects);
                }

                if (!results.isEmpty()) {
                    JsonArray dataArray = new JsonArray();

                    JsonObject extraData = new JsonObject();
                    extraData.addProperty("maxEventAmount", maxTotal);
                    extraData.addProperty("maxEventAmountY", maxTotalY);
                    dataArray.add(extraData);

                    Map<Double, List<DocumentRowWrapper>> groupedByEventX = new HashMap<>();
                    for (Long seasonId : results.keySet()) {
                        for (Long eventTypeId : results.get(seasonId).keySet()) {
                            for (String tagTypeId : results.get(seasonId).get(eventTypeId).keySet()) {
                                for (String zoneId : results.get(seasonId).get(eventTypeId).get(tagTypeId).keySet()) {
                                    for (Long teamId : results.get(seasonId).get(eventTypeId).get(tagTypeId).get(zoneId).keySet()) {
                                        for (DocumentRow row : results.get(seasonId).get(eventTypeId).get(tagTypeId).get(zoneId).get(teamId).values()) {
                                            if (row.getPlayerId() != null && row.getEventTypeId() != null && row.getSeasonId() != null) {
                                                if (Long.compare(row.getEventTypeId(), typeIdX) == 0 && StringUtils.equals(row.getTagTypeId(), tagIdX) && StringUtils.equals(row.getZoneId(), zoneIdX)) {
                                                    switch (displayType) {
                                                        case P90:
                                                            groupedByEventX.putIfAbsent(row.getTotalP90(), new ArrayList<DocumentRowWrapper>());
                                                            break;
                                                        case TOUCHES:
                                                            groupedByEventX.putIfAbsent(row.getTotal100Touches(), new ArrayList<DocumentRowWrapper>());
                                                            break;
                                                        case AVERAGE:
                                                            groupedByEventX.putIfAbsent(row.getTotalAverage(), new ArrayList<DocumentRowWrapper>());
                                                            break;
                                                        default:
                                                            groupedByEventX.putIfAbsent(row.getTotal(), new ArrayList<DocumentRowWrapper>());
                                                            break;
                                                    }
                                                    DocumentRowWrapper wrapper = new DocumentRowWrapper();
                                                    wrapper.setxValue(row);
                                                    if (results.get(seasonId).containsKey(typeIdY) && results.get(seasonId).get(typeIdY).containsKey(tagIdY) && results.get(seasonId).get(typeIdY).get(tagIdY).containsKey(zoneIdY) && results.get(seasonId).get(typeIdY).get(tagIdY).get(zoneIdY).containsKey(teamId)) {
                                                        for (DocumentRow tmpRow : results.get(seasonId).get(typeIdY).get(tagIdY).get(zoneIdY).get(teamId).values()) {
                                                            if (tmpRow.getPlayerId() != null && tmpRow.getEventTypeId() != null) {
                                                                if (Long.compare(tmpRow.getEventTypeId(), typeIdY) == 0
                                                                        && Long.compare(tmpRow.getPlayerId(), row.getPlayerId()) == 0
                                                                        && Long.compare(tmpRow.getSeasonId(), row.getSeasonId()) == 0) {
                                                                    wrapper.setyValue(tmpRow);
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        try {
                                                            DocumentRow clonedRow = row.clone();
                                                            clonedRow.setTotal(0D);
                                                            clonedRow.setTotalP90(0D);
                                                            clonedRow.setTotal100Touches(0D);
                                                            clonedRow.setTotalAverage(0D);
                                                            wrapper.setyValue(clonedRow);
                                                        } catch (CloneNotSupportedException ex) {
                                                            GlobalHelper.reportError(ex);
                                                        }
                                                    }
                                                    switch (displayType) {
                                                        case P90:
                                                            groupedByEventX.get(row.getTotalP90()).add(wrapper);
                                                            break;
                                                        case TOUCHES:
                                                            groupedByEventX.get(row.getTotal100Touches()).add(wrapper);
                                                            break;
                                                        case AVERAGE:
                                                            groupedByEventX.get(row.getTotalAverage()).add(wrapper);
                                                            break;
                                                        default:
                                                            groupedByEventX.get(row.getTotal()).add(wrapper);
                                                            break;
                                                    }
                                                }
                                                /*else if (Long.compare(row.getEventTypeId(), typeIdY) == 0 && StringUtils.equals(row.getTagTypeId(), tagIdY) && StringUtils.equals(row.getZoneId(), zoneIdY)) {
                                                    double xValue = 0D;
                                                    DocumentRowWrapper wrapper = new DocumentRowWrapper();
                                                    wrapper.setyValue(row);
                                                    if (results.get(seasonId).containsKey(typeIdX) && results.get(seasonId).get(typeIdX).containsKey(tagIdX) && results.get(seasonId).get(typeIdX).get(tagIdX).containsKey(zoneIdX) && results.get(seasonId).get(typeIdX).get(tagIdX).get(zoneIdX).containsKey(teamId)) {
                                                        for (DocumentRow tmpRow : results.get(seasonId).get(typeIdX).get(tagIdX).get(zoneIdX).get(teamId).values()) {
                                                            if (tmpRow.getPlayerId() != null && tmpRow.getEventTypeId() != null) {
                                                                if (Long.compare(tmpRow.getEventTypeId(), typeIdX) == 0
                                                                        && Long.compare(tmpRow.getPlayerId(), row.getPlayerId()) == 0
                                                                        && Long.compare(tmpRow.getSeasonId(), row.getSeasonId()) == 0) {
                                                                    wrapper.setxValue(tmpRow);
                                                                    switch (displayType) {
                                                                        case P90:
                                                                            xValue = tmpRow.getTotalP90();
                                                                            break;
                                                                        case AVERAGE:
                                                                            xValue = tmpRow.getTotalAverage();
                                                                            break;
                                                                        default:
                                                                            xValue = tmpRow.getTotal();
                                                                            break;
                                                                    }
                                                                    break;
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        try {
                                                            DocumentRow clonedRow = row.clone();
                                                            clonedRow.setTotal(0D);
                                                            clonedRow.setTotalP90(0D);
                                                            clonedRow.setTotalAverage(0D);
                                                            wrapper.setxValue(clonedRow);
                                                        } catch (CloneNotSupportedException ex) {
                                                            GlobalHelper.reportError(ex);
                                                        }
                                                    }
                                                    groupedByEventX.putIfAbsent(xValue, new ArrayList<DocumentRowWrapper>());
                                                    groupedByEventX.get(xValue).add(wrapper);
                                                }*/
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Collections.sort(seasonIdList, Collections.reverseOrder());

                    for (Double xValue : groupedByEventX.keySet()) {
                        JsonObject data = new JsonObject();
                        data.addProperty("x", xValue);
                        for (DocumentRowWrapper wrapper : groupedByEventX.get(xValue)) {
                            if (wrapper.getyValue() != null) {
                                switch (displayType) {
                                    case P90:
                                        if (Double.compare(wrapper.getyValue().getTotalP90(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotalP90());
                                        break;
                                    case TOUCHES:
                                        if (Double.compare(wrapper.getyValue().getTotal100Touches(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotal100Touches());
                                        break;
                                    case AVERAGE:
                                        if (Double.compare(wrapper.getyValue().getTotalAverage(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotalAverage());
                                        break;
                                    default:
                                        if (Double.compare(wrapper.getyValue().getTotal(), 0D) == 0) {
                                            break;
                                        }
                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotal());
                                        break;
                                }
                                data.addProperty("value" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), 10 + (seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                data.addProperty("name" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), players.get(wrapper.getyValue().getPlayerId()).getKnownName());
                                data.addProperty("season" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), seasons.get(wrapper.getyValue().getSeasonId()).getName());

                                JsonObject logoObject = new JsonObject();
                                String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + players.get(wrapper.getyValue().getPlayerId()).getPhoto() + ".png";
                                if (StringUtils.isBlank(players.get(wrapper.getyValue().getPlayerId()).getPhoto())) {
                                    src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png";
                                }
                                src += "?" + (new Date().getTime());
                                logoObject.addProperty("photoSrc", src);
                                String teamPlayerId = wrapper.getyValue().getTeamId() + ":" + wrapper.getyValue().getPlayerId();
                                if (extraTeamPlayerIds.contains(teamPlayerId) && !teamPlayerIds.contains(teamPlayerId)) {
                                    src = "/sicsdataanalytics/images/point.svg";
                                    logoObject.addProperty("width", 18);
                                    logoObject.addProperty("height", 18);
                                    logoObject.addProperty("dx", (-9));
                                    logoObject.addProperty("dy", (-9));
                                } else {
                                    logoObject.addProperty("width", imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                    logoObject.addProperty("height", imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
                                    logoObject.addProperty("dx", (-imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())) / 2));
                                    logoObject.addProperty("dy", (-imageSizes.get(seasonIdList.indexOf(wrapper.getyValue().getSeasonId())) / 2));
                                }
                                logoObject.addProperty("src", src);
                                data.add("logo" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), logoObject);
                            }
                        }
                        if (data.entrySet().size() > 1) {
                            dataArray.add(data);
                        }
                    }

                    chartData.add("data", dataArray);
                }
            }
        }

        return chartData.toString();
    }
}
