package sics.controller.player;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.Player;
import sics.domain.TeamPlayer;
import sics.domain.User;
import sics.enums.DisplayType;
import static sics.enums.DisplayType.AVERAGE;
import static sics.enums.DisplayType.P90;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/distribution")
public class PlayerDistributionController extends BaseController {

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                params.put("type", "player");

                double minEventAmount = 0, maxEventAmount = 9999, minPlaytime = 0, maxPlaytime = 9999;
                if (params.containsKey("minEventAmount")) {
                    minEventAmount = Long.parseLong(params.get("minEventAmount").toString());
                    maxEventAmount = Long.parseLong(params.get("maxEventAmount").toString());

                    params.remove("minEventAmount");
                    params.remove("maxEventAmount");
                }
                if (params.containsKey("minPlaytime")) {
                    minPlaytime = Long.parseLong(params.get("minPlaytime").toString());
                    maxPlaytime = Long.parseLong(params.get("maxPlaytime").toString());

                    params.remove("minPlaytime");
                    params.remove("maxPlaytime");
                }

                Map<String, Object> paramsClone = new HashMap<>(params);

                List<Long> competitionIds = new ArrayList<>();
                List<Integer> extraIndex = new ArrayList<>();
                for (String param : params.keySet()) {
                    if (param.startsWith("competitionId") && !StringUtils.equalsIgnoreCase(param, "competitionId")) {
                        extraIndex.add(Integer.valueOf(StringUtils.replace(param, "competitionId", "")));
                        Long competitionId = Long.valueOf(params.get(param).toString());
                        if (!competitionIds.contains(competitionId)) {
                            competitionIds.add(competitionId);
                        }
                    }
                }
                for (Integer index : extraIndex) {
                    params.remove("competitionId" + index);
                    params.remove("teamId" + index);
                    params.remove("playerId" + index);
                }
                Long competitionId = Long.valueOf(params.get("competitionId").toString());
                if (!competitionIds.contains(competitionId)) {
                    competitionIds.add(competitionId);
                }

                Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
                String tagTypeIds = null;
                if (params.containsKey("tagTypeId")) {
                    tagTypeIds = params.get("tagTypeId").toString();
                }
                chartData.addProperty("isOpposite", Utils.isOpposite(eventTypeId, tagTypeIds));
                double maxTotal = 0, maxPlaytimeValue = 0;
                List<String> extraTeamPlayerIds = new ArrayList<>();
                List<String> teamPlayerIds = new ArrayList<>();
                List<Long> teamObjectsAdded = new ArrayList<>();
                List<Long> playerObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                JsonArray playerObjects = new JsonArray();
                List<DocumentRow> results = new ArrayList<>();
                List<String> seasonIds = Arrays.asList(StringUtils.split(params.get("seasonId").toString(), "/"));
                final DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                if (seasonIds != null && !seasonIds.isEmpty()) {
                    params.remove("seasonId");
                    for (String seasonId : seasonIds) {
                        params.put("seasonId", seasonId);

                        List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER);
                        List<DocumentRow> rowToRemove = new ArrayList<>();
                        for (DocumentRow row : result) {
                            if (row.getTotal() != null) {
                                if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                                    rowToRemove.add(row);
                                }

                                if (row.getTotal() > maxTotal) {
                                    maxTotal = row.getTotal();
                                }
                            }
                        }
                        result.removeAll(rowToRemove);
                        if (displayType.equals(DisplayType.P90)) {
                            Utils.calculateP90V2(result, params);
                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                            Utils.calculate100Touches(result, params);
                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                            Utils.calculateAverage(result, params);
                        }

                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                        Long tmpSeasonId = Long.valueOf(seasonId);
                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);
                        for (DocumentRow row : result) {
                            row.setSeasonId(tmpSeasonId);

                            if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(row.getTeamId());
                            }
                            if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                JsonObject playerObject = new JsonObject();
                                playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                playerObjects.add(playerObject);
                                playerObjectsAdded.add(row.getPlayerId());
                            }

                            String teamPlayerId = row.getTeamId() + ":" + row.getPlayerId();
                            if (!teamPlayerIds.contains(teamPlayerId)) {
                                results.add(row);
                                teamPlayerIds.add(teamPlayerId);
                            }
                        }
                    }

                    // se ci sono calcolo ora i dati dei team / giocatori extra
                    for (String seasonId : seasonIds) {
                        params.put("seasonId", seasonId);

                        for (Integer index : extraIndex) {
                            if (paramsClone.containsKey("competitionId" + index)) {
                                params.remove("competitionId");
                                params.remove("teamId");
                                params.remove("playerId");
                                params.put("competitionId", paramsClone.get("competitionId" + index));
                                if (paramsClone.containsKey("teamId" + index)) {
                                    params.put("teamId", paramsClone.get("teamId" + index));
                                    if (paramsClone.containsKey("playerId" + index)) {
                                        params.put("playerId", paramsClone.get("playerId" + index));
                                    }
                                }

                                List<DocumentRow> tmpResultX = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER);
                                List<DocumentRow> rowToRemove = new ArrayList<>();
                                for (DocumentRow row : tmpResultX) {
                                    if (row.getTotal() != null) {
                                        if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                                            rowToRemove.add(row);
                                        }

                                        if (row.getTotal() > maxTotal) {
                                            maxTotal = row.getTotal();
                                        }
                                    }
                                }
                                tmpResultX.removeAll(rowToRemove);
                                if (displayType.equals(DisplayType.P90)) {
                                    Utils.calculateP90V2(tmpResultX, params);
                                } else if (displayType.equals(DisplayType.TOUCHES)) {
                                    Utils.calculate100Touches(tmpResultX, params);
                                } else if (displayType.equals(DisplayType.AVERAGE)) {
                                    Utils.calculateAverage(tmpResultX, params);
                                }

                                Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                Long tmpSeasonId = Long.valueOf(seasonId);
                                tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);
                                for (DocumentRow row : tmpResultX) {
                                    row.setSeasonId(tmpSeasonId);

                                    if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                        JsonObject teamObject = new JsonObject();
                                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                        teamObjects.add(teamObject);
                                        teamObjectsAdded.add(row.getTeamId());
                                    }
                                    if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                        JsonObject playerObject = new JsonObject();
                                        playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                        playerObjects.add(playerObject);
                                        playerObjectsAdded.add(row.getPlayerId());
                                    }

                                    String teamPlayerId = row.getTeamId() + ":" + row.getPlayerId();
                                    if (!extraTeamPlayerIds.contains(teamPlayerId) && !teamPlayerIds.contains(teamPlayerId)) {
                                        results.add(row);
                                        extraTeamPlayerIds.add(teamPlayerId);
                                    }
                                }
                            }
                        }
                    }
                    chartData.add("teams", teamObjects);
                    chartData.add("players", playerObjects);
                }

                if (!results.isEmpty()) {
                    JsonArray dataArray = new JsonArray();
                    List<Long> teamIds = new ArrayList<>();
                    List<Long> playerIds = new ArrayList<>();
                    for (DocumentRow row : results) {
                        if (row.getTeamId() != null && !teamIds.contains(row.getTeamId())) {
                            teamIds.add(row.getTeamId());
                        }
                        if (row.getPlayerId() != null && !playerIds.contains(row.getPlayerId())) {
                            playerIds.add(row.getPlayerId());
                        }
                    }

                    Map<String, Object> paramsForPlaytime = new HashMap<>(params);
                    paramsForPlaytime.remove("teamIds");
                    paramsForPlaytime.remove("playerIds");
                    if (!teamIds.isEmpty()) {
                        paramsForPlaytime.put("teamIds", StringUtils.join(teamIds, ","));
                    }
                    if (!playerIds.isEmpty()) {
                        paramsForPlaytime.put("playerIds", StringUtils.join(playerIds, ","));
                    }
                    if (!paramsForPlaytime.containsKey("competitionIds")) {
                        paramsForPlaytime.put("competitionIds", StringUtils.join(competitionIds, "|"));
                    }
                    final Map<Long, Map<Long, Long>> totalMinutes = MongoHelper.getTotalMinutes(paramsForPlaytime);

                    // controllo playtime
                    List<DocumentRow> rowsToRemove = new ArrayList<>();
                    for (DocumentRow row : results) {
                        Long minutes = totalMinutes.get(row.getTeamId()).get(row.getPlayerId());
                        if (minutes != null) {
                            if (minutes < minPlaytime || minutes > maxPlaytime) {
                                rowsToRemove.add(row);
                            }
                            if (minutes > maxPlaytimeValue) {
                                maxPlaytimeValue = minutes;
                            }
                        }
                    }
                    results.removeAll(rowsToRemove);
                    // se non è selezionato il minimo dei minuti giocati uso io il 10%
                    boolean minPlaytimeDefault = false;
//                    if (minPlaytime == 0) {
                    rowsToRemove = new ArrayList<>();
                    minPlaytime = Utils.getMinPlaytimeAmount(maxPlaytimeValue);
                    for (DocumentRow row : results) {
                        Long minutes = totalMinutes.get(row.getTeamId()).get(row.getPlayerId());
                        if (minutes != null) {
                            if (minutes < minPlaytime) {
                                rowsToRemove.add(row);
                            }
                        }
                    }
                    results.removeAll(rowsToRemove);
                    minPlaytimeDefault = true;
//                    }

                    JsonObject extraData = new JsonObject();
                    extraData.addProperty("maxEventAmount", maxTotal);
                    extraData.addProperty("maxPlaytime", maxPlaytimeValue);
                    if (minPlaytimeDefault) {
                        extraData.addProperty("minPlaytime", minPlaytime);
                    }
                    dataArray.add(extraData);

                    DocumentRow firstPlayer = null;
                    DocumentRow secondPlayer = null;
                    DocumentRow thirdPlayer = null;
                    Collections.sort(results, new Comparator<DocumentRow>() {
                        @Override
                        public int compare(DocumentRow o1, DocumentRow o2) {
                            switch (displayType) {
                                case P90:
                                    if (Double.compare(o2.getTotalP90(), o1.getTotalP90()) == 0) {
                                        if (totalMinutes.containsKey(o1.getTeamId()) && totalMinutes.get(o1.getTeamId()).containsKey(o1.getPlayerId())
                                                && totalMinutes.containsKey(o2.getTeamId()) && totalMinutes.get(o2.getTeamId()).containsKey(o2.getPlayerId())) {
                                            return totalMinutes.get(o2.getTeamId()).get(o2.getPlayerId()).compareTo(totalMinutes.get(o1.getTeamId()).get(o1.getPlayerId()));
                                        }
                                        return o2.getPlayerId().compareTo(o1.getPlayerId());
                                    } else {
                                        return o2.getTotalP90().compareTo(o1.getTotalP90());
                                    }
                                case TOUCHES:
                                    if (Double.compare(o2.getTotal100Touches(), o1.getTotal100Touches()) == 0) {
                                        if (totalMinutes.containsKey(o1.getTeamId()) && totalMinutes.get(o1.getTeamId()).containsKey(o1.getPlayerId())
                                                && totalMinutes.containsKey(o2.getTeamId()) && totalMinutes.get(o2.getTeamId()).containsKey(o2.getPlayerId())) {
                                            return totalMinutes.get(o2.getTeamId()).get(o2.getPlayerId()).compareTo(totalMinutes.get(o1.getTeamId()).get(o1.getPlayerId()));
                                        }
                                        return o2.getPlayerId().compareTo(o1.getPlayerId());
                                    } else {
                                        return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                                    }
                                case AVERAGE:
                                    if (Double.compare(o2.getTotalAverage(), o1.getTotalAverage()) == 0) {
                                        if (totalMinutes.containsKey(o1.getTeamId()) && totalMinutes.get(o1.getTeamId()).containsKey(o1.getPlayerId())
                                                && totalMinutes.containsKey(o2.getTeamId()) && totalMinutes.get(o2.getTeamId()).containsKey(o2.getPlayerId())) {
                                            return totalMinutes.get(o2.getTeamId()).get(o2.getPlayerId()).compareTo(totalMinutes.get(o1.getTeamId()).get(o1.getPlayerId()));
                                        }
                                        return o2.getPlayerId().compareTo(o1.getPlayerId());
                                    } else {
                                        return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                                    }
                                default:
                                    if (Double.compare(o2.getTotal(), o1.getTotal()) == 0) {
                                        if (totalMinutes.containsKey(o1.getTeamId()) && totalMinutes.get(o1.getTeamId()).containsKey(o1.getPlayerId())
                                                && totalMinutes.containsKey(o2.getTeamId()) && totalMinutes.get(o2.getTeamId()).containsKey(o2.getPlayerId())) {
                                            return totalMinutes.get(o2.getTeamId()).get(o2.getPlayerId()).compareTo(totalMinutes.get(o1.getTeamId()).get(o1.getPlayerId()));
                                        }
                                        return o2.getPlayerId().compareTo(o1.getPlayerId());
                                    } else {
                                        return o2.getTotal().compareTo(o1.getTotal());
                                    }
                            }
                        }
                    });

                    if (!results.isEmpty()) {
                        firstPlayer = results.get(0);
                        if (results.size() > 1) {
                            secondPlayer = results.get(1);
                            if (results.size() > 2) {
                                thirdPlayer = results.get(2);
                            }
                        }
                    }

                    long playtimeMin = 0L, playtimeMax = 0L;
                    for (DocumentRow row : results) {
                        long value = totalMinutes.get(row.getTeamId()).get(row.getPlayerId());
                        if (playtimeMin == 0L || value < playtimeMin) {
                            playtimeMin = value;
                        }
                        if (playtimeMax == 0L || value > playtimeMax) {
                            playtimeMax = value;
                        }
                    }

                    for (DocumentRow row : results) {
                        int index = results.indexOf(row) + 1;
                        JsonObject data = new JsonObject();
                        switch (displayType) {
                            case P90:
                                data.addProperty("x", row.getTotalP90());
                                break;
                            case TOUCHES:
                                data.addProperty("x", row.getTotal100Touches());
                                break;
                            case AVERAGE:
                                data.addProperty("x", row.getTotalAverage());
                                break;
                            default:
                                data.addProperty("x", row.getTotal());
                                break;
                        }
                        data.addProperty("y", 0);
                        long playtime = totalMinutes.get(row.getTeamId()).get(row.getPlayerId());
                        double divider = (playtimeMax - playtimeMin);
                        if (divider == 0D) {
                            divider = 1;
                        }
                        data.addProperty("value", ((1D * (playtime - playtimeMin) / divider)) * 10D + 3);
                        String teamPlayerId = row.getTeamId() + ":" + row.getPlayerId();
                        if (teamPlayerIds.contains(teamPlayerId)) {
                            data.addProperty("bulletColor", "#f58646");
                            data.addProperty("showName", true);
                        }
                        switch (index) {
                            case 1:
                                data.addProperty("strokeColor", "#ffc335");
                                break;
                            case 2:
                                data.addProperty("strokeColor", "#c4caf3");
                                break;
                            case 3:
                                data.addProperty("strokeColor", "#e4624c");
                                break;
                            default:
                                break;
                        }
                        String tooltip = "[bold]" + players.get(row.getPlayerId()).getKnownName() + "[/] (" + index + "°)";
                        tooltip += "\n" + teams.get(row.getTeamId()).getName(curUser.getTvLanguage());
                        if (seasonTeamPlayerPositions.containsKey(row.getSeasonId()) && seasonTeamPlayerPositions.get(row.getSeasonId()).containsKey(row.getTeamId())
                                && seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).containsKey(row.getPlayerId())) {
                            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).get(row.getPlayerId());
                            if (teamPlayer != null) {
                                if (teamPlayer.getPositionId() != null) {
                                    tooltip += "\n" + positions.get(teamPlayer.getPositionId()).getDesc(curUser.getTvLanguage());
                                    if (teamPlayer.getPositionDetailId() != null && teamPlayer.getPositionDetailId() > 0) {
                                        tooltip += " - " + positionDetails.get(teamPlayer.getPositionDetailId()).getDesc(curUser.getTvLanguage());
                                    }
                                }
                            }
                        }
                        tooltip += "\n\n" + SpringApplicationContextHelper.getMessage("menu.player.minutes.played", RequestContextUtils.getLocale(request)) + ": " + totalMinutes.get(row.getTeamId()).get(row.getPlayerId());
                        tooltip += "\n" + SpringApplicationContextHelper.getMessage("menu.player.value", RequestContextUtils.getLocale(request)) + ": {x}";
                        data.addProperty("tooltip", tooltip);
                        data.addProperty("knownName", players.get(row.getPlayerId()).getKnownName());
                        data.addProperty("index", index);
                        dataArray.add(data);
                    }

                    if (firstPlayer != null) {
                        JsonObject bestPlayerItem = new JsonObject();
                        DocumentRow row = firstPlayer;

                        Player player = players.get(row.getPlayerId());
                        String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + player.getPhoto() + ".png";
                        if (StringUtils.isBlank(player.getPhoto())) {
                            src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png";
                        }
                        src += "?" + (new Date().getTime());
                        bestPlayerItem.addProperty("src", src);
                        bestPlayerItem.addProperty("name", player.getKnownName());
                        bestPlayerItem.addProperty("teamName", teams.get(row.getTeamId()).getName(curUser.getTvLanguage()));
                        switch (displayType) {
                            case P90:
                                bestPlayerItem.addProperty("value", row.getTotalP90());
                                break;
                            case TOUCHES:
                                bestPlayerItem.addProperty("value", row.getTotal100Touches());
                                break;
                            case AVERAGE:
                                bestPlayerItem.addProperty("value", row.getTotalAverage());
                                break;
                            default:
                                bestPlayerItem.addProperty("value", row.getTotal());
                                break;
                        }
                        if (seasonTeamPlayerPositions.containsKey(row.getSeasonId()) && seasonTeamPlayerPositions.get(row.getSeasonId()).containsKey(row.getTeamId())
                                && seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).containsKey(row.getPlayerId())) {
                            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).get(row.getPlayerId());
                            if (teamPlayer != null) {
                                if (teamPlayer.getPositionId() != null) {
                                    bestPlayerItem.addProperty("position", positions.get(teamPlayer.getPositionId()).getDesc(curUser.getTvLanguage()));
                                    if (teamPlayer.getPositionDetailId() != null && teamPlayer.getPositionDetailId() > 0) {
                                        bestPlayerItem.addProperty("positionDetail", positionDetails.get(teamPlayer.getPositionDetailId()).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                        }
                        bestPlayerItem.addProperty("playtime", totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));

                        chartData.add("firstBestPlayer", bestPlayerItem);
                    }
                    if (secondPlayer != null) {
                        JsonObject bestPlayerItem = new JsonObject();
                        DocumentRow row = secondPlayer;

                        Player player = players.get(row.getPlayerId());
                        String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + player.getPhoto() + ".png";
                        if (StringUtils.isBlank(player.getPhoto())) {
                            src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png";
                        }
                        src += "?" + (new Date().getTime());
                        bestPlayerItem.addProperty("src", src);
                        bestPlayerItem.addProperty("name", player.getKnownName());
                        bestPlayerItem.addProperty("teamName", teams.get(row.getTeamId()).getName(curUser.getTvLanguage()));
                        switch (displayType) {
                            case P90:
                                bestPlayerItem.addProperty("value", row.getTotalP90());
                                break;
                            case TOUCHES:
                                bestPlayerItem.addProperty("value", row.getTotal100Touches());
                                break;
                            case AVERAGE:
                                bestPlayerItem.addProperty("value", row.getTotalAverage());
                                break;
                            default:
                                bestPlayerItem.addProperty("value", row.getTotal());
                                break;
                        }
                        if (seasonTeamPlayerPositions.containsKey(row.getSeasonId()) && seasonTeamPlayerPositions.get(row.getSeasonId()).containsKey(row.getTeamId())
                                && seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).containsKey(row.getPlayerId())) {
                            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).get(row.getPlayerId());
                            if (teamPlayer != null) {
                                if (teamPlayer.getPositionId() != null) {
                                    bestPlayerItem.addProperty("position", positions.get(teamPlayer.getPositionId()).getDesc(curUser.getTvLanguage()));
                                    if (teamPlayer.getPositionDetailId() != null && teamPlayer.getPositionDetailId() > 0) {
                                        bestPlayerItem.addProperty("positionDetail", positionDetails.get(teamPlayer.getPositionDetailId()).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                        }
                        bestPlayerItem.addProperty("playtime", totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));

                        chartData.add("secondBestPlayer", bestPlayerItem);
                    }
                    if (thirdPlayer != null) {
                        JsonObject bestPlayerItem = new JsonObject();
                        DocumentRow row = thirdPlayer;

                        Player player = players.get(row.getPlayerId());
                        String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + player.getPhoto() + ".png";
                        if (StringUtils.isBlank(player.getPhoto())) {
                            src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png";
                        }
                        src += "?" + (new Date().getTime());
                        bestPlayerItem.addProperty("src", src);
                        bestPlayerItem.addProperty("name", player.getKnownName());
                        bestPlayerItem.addProperty("teamName", teams.get(row.getTeamId()).getName(curUser.getTvLanguage()));
                        switch (displayType) {
                            case P90:
                                bestPlayerItem.addProperty("value", row.getTotalP90());
                                break;
                            case TOUCHES:
                                bestPlayerItem.addProperty("value", row.getTotal100Touches());
                                break;
                            case AVERAGE:
                                bestPlayerItem.addProperty("value", row.getTotalAverage());
                                break;
                            default:
                                bestPlayerItem.addProperty("value", row.getTotal());
                                break;
                        }
                        if (seasonTeamPlayerPositions.containsKey(row.getSeasonId()) && seasonTeamPlayerPositions.get(row.getSeasonId()).containsKey(row.getTeamId())
                                && seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).containsKey(row.getPlayerId())) {
                            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(row.getSeasonId()).get(row.getTeamId()).get(row.getPlayerId());
                            if (teamPlayer != null) {
                                if (teamPlayer.getPositionId() != null) {
                                    bestPlayerItem.addProperty("position", positions.get(teamPlayer.getPositionId()).getDesc(curUser.getTvLanguage()));
                                    if (teamPlayer.getPositionDetailId() != null && teamPlayer.getPositionDetailId() > 0) {
                                        bestPlayerItem.addProperty("positionDetail", positionDetails.get(teamPlayer.getPositionDetailId()).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                        }
                        bestPlayerItem.addProperty("playtime", totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));

                        chartData.add("thirdBestPlayer", bestPlayerItem);
                    }

//                    for (Double xValue : groupedByEventX.keySet()) {
//                        JsonObject data = new JsonObject();
//                        data.addProperty("x", xValue);
//                        for (DocumentRowWrapper wrapper : groupedByEventX.get(xValue)) {
//                            if (wrapper.getyValue() != null) {
//                                switch (displayType) {
//                                    case P90:
//                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotalP90());
//                                        break;
//                                    case AVERAGE:
//                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotalAverage());
//                                        break;
//                                    default:
//                                        data.addProperty("y" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), wrapper.getyValue().getTotal());
//                                        break;
//                                }
//                                data.addProperty("value" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), 10 + (seasonIdList.indexOf(wrapper.getyValue().getSeasonId())));
//                                data.addProperty("name" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), players.get(wrapper.getyValue().getPlayerId()).getKnownName());
//                                data.addProperty("season" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), seasons.get(wrapper.getyValue().getSeasonId()).getName());
//
//                                JsonObject logoObject = new JsonObject();
//                                String src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + players.get(wrapper.getyValue().getPlayerId()).getPhoto() + ".png";
//                                if (StringUtils.isBlank(players.get(wrapper.getyValue().getPlayerId()).getPhoto())) {
//                                    src = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png";
//                                }
//                                src += "?" + (new Date().getTime());
//                                String teamPlayerId = wrapper.getyValue().getTeamId() + ":" + wrapper.getyValue().getPlayerId();
//                                if (extraTeamPlayerIds.contains(teamPlayerId) && !teamPlayerIds.contains(teamPlayerId)) {
//                                    src = "/sicsdataanalytics/images/point.svg";
//                                    logoObject.addProperty("width", 24);
//                                    logoObject.addProperty("height", 24);
//                                    logoObject.addProperty("dx", (-12));
//                                    logoObject.addProperty("dy", (-12));
//                                } else {
//                                    logoObject.addProperty("width", 48);
//                                    logoObject.addProperty("height", 48);
//                                    logoObject.addProperty("dx", (-24));
//                                    logoObject.addProperty("dy", (-24));
//                                }
//                                logoObject.addProperty("src", src);
//                                data.add("logo" + wrapper.getyValue().getTeamId() + "-" + wrapper.getyValue().getPlayerId(), logoObject);
//                            }
//                        }
//                        dataArray.add(data);
//                    }
                    chartData.add("data", dataArray);
                }
            }
        }

        return chartData.toString();
    }
}
