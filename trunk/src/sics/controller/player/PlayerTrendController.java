package sics.controller.player;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.controller.BaseController;
import sics.controller.ControllerUtils;
import sics.domain.DocumentRow;
import sics.domain.Fixture;
import sics.domain.FixtureDetails;
import sics.enums.GroupedField;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/trend")
public class PlayerTrendController extends BaseController {

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                params.put("type", "player");

                List<Long> teamObjectsAdded = new ArrayList<>();
                List<Long> playerObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                JsonArray playerObjects = new JsonArray();
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.PLAYER_FIXTURE);
                Map<Long, List<Integer>> alreadyValidMatchdays = new HashMap<>();
                for (DocumentRow row : result) {
                    if (row.getPlayerId() != null && row.getMatchDay() != null) {
                        alreadyValidMatchdays.putIfAbsent(row.getPlayerId(), new ArrayList<Integer>());
                        alreadyValidMatchdays.get(row.getPlayerId()).add(row.getMatchDay());
                    }
                }

                // gestione dei valori a 0
                Map<String, Object> paramsCloned = new HashMap<>(params);
                paramsCloned.put("eventTypeId", null);
                paramsCloned.remove("tagTypeId");
                List<DocumentRow> allFixtures = MongoHelper.getDocumentsByParams(paramsCloned, GroupedField.PLAYER_FIXTURE);
                for (DocumentRow row : allFixtures) {
                    if (row.getPlayerId() != null && row.getMatchDay() != null) {
                        if (alreadyValidMatchdays.containsKey(row.getPlayerId())) {
                            if (!alreadyValidMatchdays.get(row.getPlayerId()).contains(row.getMatchDay())) {
                                row.setTotal(0D);
                                row.setTotalSum(0D);
                                result.add(row);
                            }
                        } else {
                            row.setTotal(0D);
                            row.setTotalSum(0D);
                            result.add(row);
                        }
                    }
                }

                // devo ora dividere i record per player
                Map<Long, List<DocumentRow>> resultGroupedByPlayer = new HashMap<>();
                for (DocumentRow row : result) {
                    resultGroupedByPlayer.putIfAbsent(row.getPlayerId(), new ArrayList<DocumentRow>());
                    resultGroupedByPlayer.get(row.getPlayerId()).add(row);

                    if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                        JsonObject teamObject = new JsonObject();
                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                        teamObjects.add(teamObject);
                        teamObjectsAdded.add(row.getTeamId());
                    }
                    if (!playerObjectsAdded.contains(row.getPlayerId())) {
                        JsonObject playerObject = new JsonObject();
                        playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                        playerObjects.add(playerObject);
                        playerObjectsAdded.add(row.getPlayerId());
                    }
                    // aggiungo tutti i team delle fixture (serve per la tabella)
                    // qua dovrei tenere una lista di quelli che ho già messo ma non credo cambi molto la dimensione della pagina
                    if (row.getFixtureId() != null) {
                        FixtureDetails fixture = fixtureDetails.get(row.getFixtureId());
                        if (fixture != null) {
                            if (fixture.getHomeTeamId() != null && !teamObjectsAdded.contains(fixture.getHomeTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(fixture.getHomeTeamId().toString(), teams.get(fixture.getHomeTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(fixture.getHomeTeamId());
                            }
                            if (fixture.getAwayTeamId() != null && !teamObjectsAdded.contains(fixture.getAwayTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(fixture.getAwayTeamId().toString(), teams.get(fixture.getAwayTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(fixture.getAwayTeamId());
                            }
                        }
                    }
                }
                chartData.add("teams", teamObjects);
                chartData.add("players", playerObjects);

                List<Long> fixtureIds = new ArrayList<>();
                for (DocumentRow row : result) {
                    if (row.getFixtureId() != null && !fixtureIds.contains(row.getFixtureId())) {
                        fixtureIds.add(row.getFixtureId());
                    }
                }
                if (fixtureIds.isEmpty()) {
                    return chartData.toString();
                }

                // popolo game date
                List<Fixture> fixtures = mService.getFixtureByIds(fixtureIds);
                Map<Long, Fixture> fixtureMap = new HashMap<>();
                for (Fixture fixture : fixtures) {
                    fixtureMap.put(fixture.getId(), fixture);
                }
                for (DocumentRow row : result) {
                    if (row.getFixtureId() != null && fixtureMap.containsKey(row.getFixtureId())) {
                        row.setGameDate(fixtureMap.get(row.getFixtureId()).getGameDate());
                    }
                }

                Map<Integer, List<Element>> matchdayData = new HashMap<>();
                JsonArray fixtureObjects = new JsonArray();
                List<Long> foundedFixtureIds = new ArrayList<>();
                for (Long playerId : resultGroupedByPlayer.keySet()) {
                    Map<Long, DocumentRow> groupedByFixture = ControllerUtils.getTotals(true, resultGroupedByPlayer.get(playerId), params, GroupedField.FIXTURE);

                    for (DocumentRow row : groupedByFixture.values()) {
                        if (!foundedFixtureIds.contains(row.getFixtureId())) {
                            JsonObject fixtureObject = new JsonObject();
                            fixtureObject.addProperty(row.getTeamId() + "-" + row.getMatchDay(), fixtureMap.get(row.getFixtureId()).getJson());
                            fixtureObjects.add(fixtureObject);
                            foundedFixtureIds.add(row.getFixtureId());
                        }

                        matchdayData.putIfAbsent(row.getMatchDay(), new ArrayList<Element>());
                        Element element = new Element(row.getPlayerId(), row.getTotal(), players.get(row.getPlayerId()).getPhoto());
                        matchdayData.get(row.getMatchDay()).add(element);
                    }
                }
                chartData.add("fixtures", fixtureObjects);

                int imageSizeToReduce = resultGroupedByPlayer.size() / 5 * 6;
                JsonArray dataArray = new JsonArray();
                TreeMap<Integer, List<Element>> sortedMatchdayData = new TreeMap<>(matchdayData);
                for (Integer matchDay : sortedMatchdayData.keySet()) {
                    JsonObject data = new JsonObject();
                    data.addProperty("matchDay", matchDay);
                    for (Element element : sortedMatchdayData.get(matchDay)) {
                        data.addProperty("value" + element.getId(), element.getValue());

                        JsonObject logoObject = new JsonObject();
                        String src = element.getLogo();
                        logoObject.addProperty("src", src);
                        logoObject.addProperty("width", 48 - imageSizeToReduce);
                        logoObject.addProperty("height", 48 - imageSizeToReduce);
                        data.add("logo" + element.getId(), logoObject);
                    }
                    dataArray.add(data);
                }
                chartData.add("data", dataArray);

                JsonArray secondChartData = new JsonArray();
                int matchdayDivider = 5, matchdayCounter = 0, matchdayIteration = 1;
                Map<Integer, Double> matchdayTotals = new HashMap<>();  // <Contatore, Totale>
                for (Integer matchDay : sortedMatchdayData.keySet()) {
                    if (matchdayCounter >= matchdayDivider) {
                        // dovrei essere arrivato a matchdayDivider quindi scrivo quello che ho
                        JsonObject data = new JsonObject();
                        data.addProperty("matchDay", SpringApplicationContextHelper.getMessage("player.trend.matchdays", RequestContextUtils.getLocale(request)) + " " + ((matchdayIteration - 1) * matchdayDivider + 1) + " - " + (matchdayIteration * matchdayDivider));
                        for (Integer index : matchdayTotals.keySet()) {
                            data.addProperty("value" + index, matchdayTotals.get(index));
                        }
                        secondChartData.add(data);
                        matchdayTotals.clear();
                        matchdayIteration++;
                        matchdayCounter = 0;
                    }
                    for (Element element : sortedMatchdayData.get(matchDay)) {
                        matchdayTotals.putIfAbsent(element.getId().intValue(), 0D);
                        matchdayTotals.put(element.getId().intValue(), matchdayTotals.get(element.getId().intValue()) + element.getValue());
                    }
                    matchdayCounter++;
                }
                if (!matchdayTotals.isEmpty()) {
                    JsonObject data = new JsonObject();
                    data.addProperty("matchDay", SpringApplicationContextHelper.getMessage("player.trend.matchdays", RequestContextUtils.getLocale(request)) + " " + ((matchdayIteration - 1) * matchdayDivider + 1) + " - " + (sortedMatchdayData.keySet().toArray()[sortedMatchdayData.keySet().size() - 1]));
                    for (Integer index : matchdayTotals.keySet()) {
                        data.addProperty("value" + index, matchdayTotals.get(index));
                    }
                    secondChartData.add(data);
                }
                chartData.add("secondData", secondChartData);

                // media campionato
                params.remove("teamId");
                params.remove("playerId");
                params.remove("fixtureIds");
//                Double competitionAverage = MongoHelper.getCompetitionAverageByParams(params);
//                competitionAverage = Math.round(competitionAverage * 10) / 10D;
//                chartData.addProperty("competitionAverage", competitionAverage);
            }
        }

        return chartData.toString();
    }

    private class Element {

        private Long id;
        private Double value;
        private String logo;
        private String localLogo;

        public Element(Long id, Double value, String logo) {
            this.id = id;
            this.value = value;
            this.logo = logo;

            if (StringUtils.isNotBlank(logo)) {
                this.logo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + logo + ".png?" + (new Date().getTime());
                this.localLogo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/" + logo + ".png?" + (new Date().getTime());
            } else {
                this.logo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?" + (new Date().getTime());
                this.localLogo = "https://s3.eu-west-1.amazonaws.com/it.sics.logo/120/players/unknownxx.png?" + (new Date().getTime());
            }
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Double getValue() {
            return value;
        }

        public void setValue(Double value) {
            this.value = value;
        }

        public String getLogo() {
            return logo;
        }

        public void setLogo(String logo) {
            this.logo = logo;
        }

        public String getLocalLogo() {
            return localLogo;
        }

        public void setLocalLogo(String localLogo) {
            this.localLogo = localLogo;
        }
    }
}
