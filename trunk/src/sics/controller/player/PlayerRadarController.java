package sics.controller.player;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.User;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/radar")
public class PlayerRadarController extends BaseController {

    @RequestMapping("/getChartData")
    public @ResponseBody
    String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject chartData = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeIds")) {
                params.put("type", "player");

                Map<String, Object> paramsClone = new HashMap<>(params);

                List<Integer> extraIndex = new ArrayList<>();
                for (String param : params.keySet()) {
                    if (param.startsWith("playerId") && !StringUtils.equalsIgnoreCase(param, "playerId")) {
                        extraIndex.add(Integer.valueOf(StringUtils.replace(param, "playerId", "")));
                    }
                }
                JsonArray seasonObjects = new JsonArray();
                JsonArray competitionObjects = new JsonArray();
                if (params.containsKey("seasonId")) {
                    Long seasonId = Long.valueOf(params.get("seasonId").toString());
                    JsonObject seasonObject = new JsonObject();
                    seasonObject.addProperty(seasonId.toString(), seasons.get(seasonId).getJson());
                    seasonObjects.add(seasonObject);
                }
                if (params.containsKey("competitionId")) {
                    Long competitionId = Long.valueOf(params.get("competitionId").toString());
                    JsonObject competitionObject = new JsonObject();
                    competitionObject.addProperty(competitionId.toString(), competitions.get(competitionId).getJson());
                    competitionObjects.add(competitionObject);
                }
                for (Integer index : extraIndex) {
                    if (params.containsKey("seasonId" + index)) {
                        Long seasonId = Long.valueOf(params.get("seasonId" + index).toString());
                        JsonObject seasonObject = new JsonObject();
                        seasonObject.addProperty(seasonId.toString(), seasons.get(seasonId).getJson());
                        seasonObjects.add(seasonObject);
                    }
                    if (params.containsKey("competitionId" + index)) {
                        Long competitionId = Long.valueOf(params.get("competitionId" + index).toString());
                        JsonObject competitionObject = new JsonObject();
                        competitionObject.addProperty(competitionId.toString(), competitions.get(competitionId).getJson());
                        competitionObjects.add(competitionObject);
                    }

                    params.remove("seasonId" + index);
                    params.remove("competitionId" + index);
                    params.remove("teamId" + index);
                    params.remove("playerId" + index);
                }
                chartData.add("seasons", seasonObjects);
                chartData.add("competitions", competitionObjects);

                Map<Long, Map<String, Map<String, List<DocumentRow>>>> finalResult = new LinkedHashMap<>();
                Map<Long, Map<String, Map<String, Double>>> eventTypeMinValue = new HashMap<>();
                Map<Long, Map<String, Map<String, Double>>> eventTypeMaxValue = new HashMap<>();
                DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());

                List<Long> playerIds = new ArrayList<>();
                for (String playerId : StringUtils.split(params.get("playerId").toString(), "|")) {
                    playerIds.add(Long.valueOf(playerId));
                }
                Long playerTeamId = Long.valueOf(params.remove("teamId").toString());
                params.remove("teamId");
                params.remove("playerId");

                List<Long> teamObjectsAdded = new ArrayList<>();
                List<Long> playerObjectsAdded = new ArrayList<>();
                JsonArray teamObjects = new JsonArray();
                JsonArray playerObjects = new JsonArray();
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                chartData.addProperty("mMinPlaytime", Utils.adjustRowsForPlaytime(result, params, playerIds));
                if (displayType.equals(DisplayType.P90)) {
                    Utils.calculateP90V2(result, params);
                } else if (displayType.equals(DisplayType.TOUCHES)) {
                    Utils.calculate100Touches(result, params);
                } else if (displayType.equals(DisplayType.AVERAGE)) {
                    Utils.calculateAverage(result, params);
                }
                // devo ora dividere i record per player
                Map<Long, Map<Long, List<DocumentRow>>> resultGroupedByTeamPlayer = new HashMap<>();
                for (DocumentRow row : result) {
                    resultGroupedByTeamPlayer.putIfAbsent(row.getTeamId(), new HashMap<Long, List<DocumentRow>>());
                    resultGroupedByTeamPlayer.get(row.getTeamId()).putIfAbsent(row.getPlayerId(), new ArrayList<DocumentRow>());
                    resultGroupedByTeamPlayer.get(row.getTeamId()).get(row.getPlayerId()).add(row);

                    if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                        JsonObject teamObject = new JsonObject();
                        teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                        teamObjects.add(teamObject);
                        teamObjectsAdded.add(row.getTeamId());
                    }
                    if (!playerObjectsAdded.contains(row.getPlayerId())) {
                        JsonObject playerObject = new JsonObject();
                        playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                        playerObjects.add(playerObject);
                        playerObjectsAdded.add(row.getPlayerId());
                    }
                }

                for (Long teamId : resultGroupedByTeamPlayer.keySet()) {
                    for (Long playerId : resultGroupedByTeamPlayer.get(teamId).keySet()) {
                        switch (displayType) {
                            case P90:
                                for (DocumentRow row : resultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                    if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotalP90() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotalP90() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                        }
                                    } else {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotalP90() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotalP90() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                        }
                                    }

                                    if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                        Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                        row.setSeasonId(tmpSeasonId);
                                        row.setCompetitionId(tmpCompetitionId);
                                        finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                        finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                    }
                                }
                                break;
                            case TOUCHES:
                                for (DocumentRow row : resultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                    if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotal100Touches() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotal100Touches() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                        }
                                    } else {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotal100Touches() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotal100Touches() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                        }
                                    }

                                    if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                        Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                        row.setSeasonId(tmpSeasonId);
                                        row.setCompetitionId(tmpCompetitionId);
                                        finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                        finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                    }
                                }
                                break;
                            case AVERAGE:
                                for (DocumentRow row : resultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                    if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotalAverage() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotalAverage() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                        }
                                    } else {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotalAverage() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotalAverage() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                        }
                                    }

                                    if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                        Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                        row.setSeasonId(tmpSeasonId);
                                        row.setCompetitionId(tmpCompetitionId);
                                        finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                        finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                    }
                                }
                                break;
                            default:
                                for (DocumentRow row : resultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                    if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotal() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotal() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                        }
                                    } else {
                                        eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                        if (row.getTotal() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                        }
                                        eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                        eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                        eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                        if (row.getTotal() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                            eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                        }
                                    }

                                    if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                        Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                        Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                        tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                        row.setSeasonId(tmpSeasonId);
                                        row.setCompetitionId(tmpCompetitionId);
                                        finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                        finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                        finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                    }
                                }
                                break;
                        }
                    }
                }

                // media ruolo solo se non ci sono campi extra
                if (extraIndex.isEmpty()) {
                    // devo controllare i ruoli dei giocatori selezionati
                    Map<String, Object> tmpParamsClone = new HashMap<>();
                    tmpParamsClone.put("seasonId", params.get("seasonId"));
                    tmpParamsClone.put("competitionId", params.get("competitionId"));
                    tmpParamsClone.put("playerId", StringUtils.join(playerIds, "|"));
                    List<Object> playersPosition = MongoHelper.getPlayersPosition(Long.valueOf(tmpParamsClone.get("seasonId").toString()), Long.valueOf(tmpParamsClone.get("competitionId").toString()), tmpParamsClone);
                    String positionId = null;
                    for (Object playerPosition : playersPosition) {
                        String value = playerPosition.toString();
                        if (StringUtils.isNotBlank(value)) {
                            if (value.contains(";")) {
                                String playerPositionId = StringUtils.split(value, ";")[1];
                                if (positionId == null) {
                                    positionId = playerPositionId;
                                } else {
                                    if (!StringUtils.equals(positionId, playerPositionId)) {
                                        positionId = null;
                                        break;
                                    }
                                }
                            } else {
                                positionId = null;
                                break;
                            }
                        } else {
                            positionId = null;
                            break;
                        }
                    }

                    if (positionId != null) {
                        // tutti hanno la stessa posizione
                        List<Long> validPlayers = new ArrayList<>();
                        Map<Long, Map<String, Map<String, DocumentRow>>> competitionAverage = new HashMap<>();
                        for (Long teamId : resultGroupedByTeamPlayer.keySet()) {
                            for (Long playerId : resultGroupedByTeamPlayer.get(teamId).keySet()) {
                                for (DocumentRow row : resultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                    try {
                                        if (StringUtils.equals(row.getPositionId().toString(), positionId)) {
                                            if (!validPlayers.contains(row.getPlayerId())) {
                                                validPlayers.add(row.getPlayerId());
                                            }
                                            if (competitionAverage.get(row.getEventTypeId()) == null || competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()) == null || competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()) == null) {
                                                competitionAverage.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, DocumentRow>>());
                                                competitionAverage.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, DocumentRow>());
                                                competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), row.clone());
                                            } else {
                                                switch (displayType) {
                                                    case P90:
                                                        competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).setTotalP90(competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).getTotalP90() + row.getTotalP90());
                                                        break;
                                                    case TOUCHES:
                                                        competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).setTotal100Touches(competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).getTotal100Touches() + row.getTotal100Touches());
                                                        break;
                                                    case AVERAGE:
                                                        competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).setTotalAverage(competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).getTotalAverage() + row.getTotalAverage());
                                                        break;
                                                    default:
                                                        competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).setTotal(competitionAverage.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).getTotal() + row.getTotal());
                                                        break;
                                                }
                                            }
                                        }
                                    } catch (CloneNotSupportedException ex) {
                                        GlobalHelper.reportError(ex);
                                    }
                                }
                            }
                        }
                        for (Long eventTypeId : competitionAverage.keySet()) {
                            for (String tagTypeId : competitionAverage.get(eventTypeId).keySet()) {
                                for (DocumentRow row : competitionAverage.get(eventTypeId).get(tagTypeId).values()) {
                                    row.setTeamId(0L);
                                    row.setPlayerId(0L);
                                    if (row.getTotal() != null) {
                                        row.setTotal(Math.round(row.getTotal() / validPlayers.size() * 100D) / 100D);
                                    }
                                    if (row.getTotalP90() != null) {
                                        row.setTotalP90(Math.round(row.getTotalP90() / validPlayers.size() * 100D) / 100D);
                                    }
                                    if (row.getTotal100Touches() != null) {
                                        row.setTotal100Touches(Math.round(row.getTotal100Touches() / validPlayers.size() * 100D) / 100D);
                                    }
                                    if (row.getTotalAverage() != null) {
                                        row.setTotalAverage(Math.round(row.getTotalAverage() / validPlayers.size() * 100D) / 100D);
                                    }
                                    Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                    Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                    tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                    row.setSeasonId(tmpSeasonId);
                                    row.setCompetitionId(tmpCompetitionId);
                                    finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                    finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                    finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                    finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                }
                            }
                        }
                        // riga di player per la media ruolo
                        JsonObject playerObject = new JsonObject();
                        playerObject.addProperty("0", players.get(0L).getJson());
                        playerObjects.add(playerObject);
                        // riga di team per la media ruolo
                        JsonObject teamObject = new JsonObject();
                        teamObject.addProperty("0", teams.get(0L).getJson());
                        teamObjects.add(teamObject);

                        chartData.addProperty("containsAverage", true);
                    }
                }

                for (Integer index : extraIndex) {
                    if (paramsClone.containsKey("playerId" + index)) {
                        params.remove("seasonId");
                        params.remove("competitionId");
                        params.remove("teamId");
                        params.remove("playerId");
                        params.put("seasonId", paramsClone.get("seasonId" + index));
                        params.put("competitionId", paramsClone.get("competitionId" + index));
                        params.put("teamId", paramsClone.get("teamId" + index));
                        params.put("playerId", paramsClone.get("playerId" + index));

                        playerIds = new ArrayList<>();
                        for (String playerId : StringUtils.split(params.get("playerId").toString(), "|")) {
                            playerIds.add(Long.valueOf(playerId));
                        }
                        playerTeamId = Long.valueOf(params.remove("teamId").toString());
                        params.remove("teamId");
                        params.remove("playerId");

                        List<DocumentRow> tmpResult = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                        Utils.adjustRowsForPlaytime(tmpResult, params, playerIds);
                        if (displayType.equals(DisplayType.P90)) {
                            Utils.calculateP90V2(tmpResult, params);
                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                            Utils.calculate100Touches(tmpResult, params);
                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                            Utils.calculateAverage(tmpResult, params);
                        }
                        // devo ora dividere i record per player
                        Map<Long, Map<Long, List<DocumentRow>>> tmpResultGroupedByTeamPlayer = new HashMap<>();
                        for (DocumentRow row : tmpResult) {
                            tmpResultGroupedByTeamPlayer.putIfAbsent(row.getTeamId(), new HashMap<Long, List<DocumentRow>>());
                            tmpResultGroupedByTeamPlayer.get(row.getTeamId()).putIfAbsent(row.getPlayerId(), new ArrayList<DocumentRow>());
                            tmpResultGroupedByTeamPlayer.get(row.getTeamId()).get(row.getPlayerId()).add(row);

                            if (row.getTeamId() != null && !teamObjectsAdded.contains(row.getTeamId())) {
                                JsonObject teamObject = new JsonObject();
                                teamObject.addProperty(row.getTeamId().toString(), teams.get(row.getTeamId()).getJson());
                                teamObjects.add(teamObject);
                                teamObjectsAdded.add(row.getTeamId());
                            }
                            if (!playerObjectsAdded.contains(row.getPlayerId())) {
                                JsonObject playerObject = new JsonObject();
                                playerObject.addProperty(row.getPlayerId().toString(), players.get(row.getPlayerId()).getJson());
                                playerObjects.add(playerObject);
                                playerObjectsAdded.add(row.getPlayerId());
                            }
                        }

                        for (Long teamId : tmpResultGroupedByTeamPlayer.keySet()) {
                            for (Long playerId : tmpResultGroupedByTeamPlayer.get(teamId).keySet()) {
                                switch (displayType) {
                                    case P90:
                                        for (DocumentRow row : tmpResultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                            if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotalP90() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotalP90() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                                }
                                            } else {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotalP90() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotalP90() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalP90());
                                                }
                                            }

                                            if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                                Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                                Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                                tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                                row.setSeasonId(tmpSeasonId);
                                                row.setCompetitionId(tmpCompetitionId);
                                                finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                                finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                            }
                                        }
                                        break;
                                    case TOUCHES:
                                        for (DocumentRow row : tmpResultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                            if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotal100Touches() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotal100Touches() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                                }
                                            } else {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotal100Touches() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotal100Touches() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal100Touches());
                                                }
                                            }

                                            if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                                Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                                Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                                tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                                row.setSeasonId(tmpSeasonId);
                                                row.setCompetitionId(tmpCompetitionId);
                                                finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                                finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                            }
                                        }
                                        break;
                                    case AVERAGE:
                                        for (DocumentRow row : tmpResultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                            if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotalAverage() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotalAverage() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                                }
                                            } else {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotalAverage() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotalAverage() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotalAverage());
                                                }
                                            }

                                            if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                                Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                                Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                                tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                                row.setSeasonId(tmpSeasonId);
                                                row.setCompetitionId(tmpCompetitionId);
                                                finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                                finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                            }
                                        }
                                        break;
                                    default:
                                        for (DocumentRow row : tmpResultGroupedByTeamPlayer.get(teamId).get(playerId)) {
                                            if (oppositeEventTypes.containsKey(row.getEventTypeId())) {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotal() < eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotal() > eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                                }
                                            } else {
                                                eventTypeMaxValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), 0D);
                                                if (row.getTotal() > eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMaxValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                                }
                                                eventTypeMinValue.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, Double>>());
                                                eventTypeMinValue.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, Double>());
                                                eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), Double.MAX_VALUE);
                                                if (row.getTotal() < eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId())) {
                                                    eventTypeMinValue.get(row.getEventTypeId()).get(row.getTagTypeId()).put(row.getZoneId(), row.getTotal());
                                                }
                                            }

                                            if (playerIds.contains(playerId) && Long.compare(playerTeamId, teamId) == 0) {
                                                Long tmpCompetitionId = Long.valueOf(params.get("competitionId").toString());
                                                Long tmpSeasonId = Long.valueOf(params.get("seasonId").toString());
//                                                tmpSeasonId = Utils.getCorrectSeasonId(tmpSeasonId, tmpCompetitionId);

                                                row.setSeasonId(tmpSeasonId);
                                                row.setCompetitionId(tmpCompetitionId);
                                                finalResult.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                                                finalResult.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                                                finalResult.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                                            }
                                        }
                                        break;
                                }
                            }
                        }
                    }
                }
                chartData.add("teams", teamObjects);
                chartData.add("players", playerObjects);

                JsonArray dataArray = new JsonArray();
                switch (displayType) {
                    case P90:
                        for (Long eventTypeId : finalResult.keySet()) {
                            for (String tagTypeId : finalResult.get(eventTypeId).keySet()) {
                                for (String zoneId : finalResult.get(eventTypeId).get(tagTypeId).keySet()) {
                                    JsonObject data = new JsonObject();
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        String event = eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                        if (!finalResult.get(eventTypeId).isEmpty()) {
                                            String tagName = Utils.getTagTypeName(finalResult.get(eventTypeId).get(tagTypeId).get(zoneId).get(0), curUser.getTvLanguage());
                                            if (StringUtils.isNotBlank(tagName)) {
                                                tagName = "(" + tagName + ")";
                                                event += "\n" + tagName;
                                            }
                                            String zoneAbbName = null, zoneName = null;
                                            if (StringUtils.isNotBlank(zoneId)) {
                                                zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                                if (StringUtils.isNotBlank(zoneAbbName)) {
                                                    zoneAbbName = "(" + zoneAbbName + ")";
                                                    event += "\n" + zoneAbbName;
                                                }
                                                zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                                data.addProperty("zoneAbbName", zoneAbbName);
                                                data.addProperty("zoneName", zoneName);
                                            }
                                        }
                                        data.addProperty("event", event);
                                    } else {
                                        // metriche avanzate
                                        data.addProperty("event", advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    data.addProperty("eventId", eventTypeId);
                                    for (DocumentRow row : finalResult.get(eventTypeId).get(tagTypeId).get(zoneId)) {
                                        data.addProperty("total" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), Math.round(((row.getTotalP90() - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D)) / (eventTypeMaxValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 100D) - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D))) * 100D * 100D) / 100D);
                                        data.addProperty("value" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), row.getTotalP90());

                                        String src = teams.get(row.getTeamId()).getLogo();
                                        if (StringUtils.isBlank(src)) {
                                            src = "unknownxx";
                                        }
                                        data.addProperty("teamLogo" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), src);
                                    }
                                    dataArray.add(data);
                                }
                            }
                        }
                        break;
                    case TOUCHES:
                        for (Long eventTypeId : finalResult.keySet()) {
                            for (String tagTypeId : finalResult.get(eventTypeId).keySet()) {
                                for (String zoneId : finalResult.get(eventTypeId).get(tagTypeId).keySet()) {
                                    JsonObject data = new JsonObject();
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        String event = eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                        if (!finalResult.get(eventTypeId).isEmpty()) {
                                            String tagName = Utils.getTagTypeName(finalResult.get(eventTypeId).get(tagTypeId).get(zoneId).get(0), curUser.getTvLanguage());
                                            if (StringUtils.isNotBlank(tagName)) {
                                                tagName = "(" + tagName + ")";
                                                event += "\n" + tagName;
                                            }
                                            String zoneAbbName = null, zoneName = null;
                                            if (StringUtils.isNotBlank(zoneId)) {
                                                zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                                if (StringUtils.isNotBlank(zoneAbbName)) {
                                                    zoneAbbName = "(" + zoneAbbName + ")";
                                                    event += "\n" + zoneAbbName;
                                                }
                                                zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                                data.addProperty("zoneAbbName", zoneAbbName);
                                                data.addProperty("zoneName", zoneName);
                                            }
                                        }
                                        data.addProperty("event", event);
                                    } else {
                                        // metriche avanzate
                                        data.addProperty("event", advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    data.addProperty("eventId", eventTypeId);
                                    for (DocumentRow row : finalResult.get(eventTypeId).get(tagTypeId).get(zoneId)) {
                                        data.addProperty("total" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), Math.round(((row.getTotal100Touches() - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D)) / (eventTypeMaxValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 100D) - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D))) * 100D * 100D) / 100D);
                                        data.addProperty("value" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), row.getTotal100Touches());

                                        String src = teams.get(row.getTeamId()).getLogo();
                                        if (StringUtils.isBlank(src)) {
                                            src = "unknownxx";
                                        }
                                        data.addProperty("teamLogo" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), src);
                                    }
                                    dataArray.add(data);
                                }
                            }
                        }
                        break;
                    case AVERAGE:
                        for (Long eventTypeId : finalResult.keySet()) {
                            for (String tagTypeId : finalResult.get(eventTypeId).keySet()) {
                                for (String zoneId : finalResult.get(eventTypeId).get(tagTypeId).keySet()) {
                                    JsonObject data = new JsonObject();
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        String event = eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                        if (!finalResult.get(eventTypeId).isEmpty()) {
                                            String tagName = Utils.getTagTypeName(finalResult.get(eventTypeId).get(tagTypeId).get(zoneId).get(0), curUser.getTvLanguage());
                                            if (StringUtils.isNotBlank(tagName)) {
                                                tagName = "(" + tagName + ")";
                                                event += "\n" + tagName;
                                            }
                                            String zoneAbbName = null, zoneName = null;
                                            if (StringUtils.isNotBlank(zoneId)) {
                                                zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                                if (StringUtils.isNotBlank(zoneAbbName)) {
                                                    zoneAbbName = "(" + zoneAbbName + ")";
                                                    event += "\n" + zoneAbbName;
                                                }
                                                zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                                data.addProperty("zoneAbbName", zoneAbbName);
                                                data.addProperty("zoneName", zoneName);
                                            }
                                        }
                                        data.addProperty("event", event);
                                    } else {
                                        // metriche avanzate
                                        data.addProperty("event", advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    data.addProperty("eventId", eventTypeId);
                                    for (DocumentRow row : finalResult.get(eventTypeId).get(tagTypeId).get(zoneId)) {
                                        data.addProperty("total" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), Math.round(((row.getTotalAverage() - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D)) / (eventTypeMaxValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 100D) - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D))) * 100D * 100D) / 100D);
                                        data.addProperty("value" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), row.getTotalAverage());

                                        String src = teams.get(row.getTeamId()).getLogo();
                                        if (StringUtils.isBlank(src)) {
                                            src = "unknownxx";
                                        }
                                        data.addProperty("teamLogo" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), src);
                                    }
                                    dataArray.add(data);
                                }
                            }
                        }
                        break;
                    default:
                        for (Long eventTypeId : finalResult.keySet()) {
                            for (String tagTypeId : finalResult.get(eventTypeId).keySet()) {
                                for (String zoneId : finalResult.get(eventTypeId).get(tagTypeId).keySet()) {
                                    JsonObject data = new JsonObject();
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        String event = eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                        if (!finalResult.get(eventTypeId).isEmpty()) {
                                            String tagName = Utils.getTagTypeName(finalResult.get(eventTypeId).get(tagTypeId).get(zoneId).get(0), curUser.getTvLanguage());
                                            if (StringUtils.isNotBlank(tagName)) {
                                                tagName = "(" + tagName + ")";
                                                event += "\n" + tagName;
                                            }
                                            String zoneAbbName = null, zoneName = null;
                                            if (StringUtils.isNotBlank(zoneId)) {
                                                zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                                if (StringUtils.isNotBlank(zoneAbbName)) {
                                                    zoneAbbName = "(" + zoneAbbName + ")";
                                                    event += "\n" + zoneAbbName;
                                                }
                                                zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                                data.addProperty("zoneAbbName", zoneAbbName);
                                                data.addProperty("zoneName", zoneName);
                                            }
                                        }
                                        data.addProperty("event", event);
                                    } else {
                                        // metriche avanzate
                                        data.addProperty("event", advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    data.addProperty("eventId", eventTypeId);
                                    for (DocumentRow row : finalResult.get(eventTypeId).get(tagTypeId).get(zoneId)) {
                                        data.addProperty("total" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), Math.round(((row.getTotal() - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D)) / (eventTypeMaxValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 100D) - eventTypeMinValue.get(row.getEventTypeId()).get(tagTypeId).getOrDefault(row.getZoneId(), 0D))) * 100D * 100D) / 100D);
                                        data.addProperty("value" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), row.getTotal());

                                        String src = teams.get(row.getTeamId()).getLogo();
                                        if (StringUtils.isBlank(src)) {
                                            src = "unknownxx";
                                        }
                                        data.addProperty("teamLogo" + row.getPlayerId() + "-" + row.getTeamId() + "-" + row.getCompetitionId() + "-" + row.getSeasonId(), src);
                                    }
                                    dataArray.add(data);
                                }
                            }
                        }
                        break;
                }

                if (dataArray.size() > 0) {
                    // Convert JSONArray to List for sorting
                    List<JsonObject> objectList = new ArrayList<>();
                    for (int i = 0; i < dataArray.size(); i++) {
                        objectList.add((JsonObject) dataArray.get(i));
                    }

                    // Sort the list based on the "event" field
                    Collections.sort(objectList, new Comparator<JsonObject>() {
                        @Override
                        public int compare(JsonObject o1, JsonObject o2) {
                            return o1.get("event").getAsString().compareTo(o2.get("event").getAsString());
                        }
                    });

                    // Convert back to JSONArray
                    JsonArray sortedDataArray = new JsonArray();
                    for (JsonObject jsonObject : objectList) {
                        sortedDataArray.add(jsonObject);
                    }
                    dataArray = sortedDataArray;
                }

                chartData.add("data", dataArray);
            }
        }

        return chartData.toString();
    }
}
