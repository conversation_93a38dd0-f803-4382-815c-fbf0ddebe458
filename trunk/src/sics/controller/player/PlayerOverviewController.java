package sics.controller.player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.Fixture;
import sics.domain.PlayerCareerItem;
import sics.domain.PlayerData;
import sics.domain.Point;
import sics.domain.TableRow;
import sics.domain.User;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.ModuleHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/overview")
public class PlayerOverviewController extends BaseController {

    private final static String PAGE_OVERVIEW_CONTENT = "player-data/overview-content.jsp";
    private final static UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

        List<TableRow> rows = new ArrayList<>();
        List<TableRow> sufferedRows = new ArrayList<>();
        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("playerId")) {
                model.addAttribute("mValid", true);
                Long playerId = Long.valueOf(params.get("playerId").toString());
                Long teamId = Long.valueOf(params.get("teamId").toString());

                params.put("type", "player");
                params.remove("playerId");
                params.remove("teamId");
                List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER_EVENT_TYPE_TAG_TYPE);
                if (!result.isEmpty()) {
                    model.addAttribute("mMinPlaytime", Utils.adjustRowsForPlaytime(result, params, Arrays.asList(playerId)));

                    final DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                    model.addAttribute("mDisplayType", displayType.toString());
                    if (displayType.equals(DisplayType.P90)) {
                        Utils.calculateP90V2(result, params);
                    } else if (displayType.equals(DisplayType.TOUCHES)) {
                        Utils.calculate100Touches(result, params);
                    } else if (displayType.equals(DisplayType.AVERAGE)) {
                        Utils.calculateAverage(result, params);
                    }

                    Map<Long, Map<String, Map<String, List<DocumentRow>>>> groupedByEventTagZone = new HashMap<>();
                    for (DocumentRow row : result) {
                        groupedByEventTagZone.putIfAbsent(row.getEventTypeId(), new HashMap<String, Map<String, List<DocumentRow>>>());
                        groupedByEventTagZone.get(row.getEventTypeId()).putIfAbsent(row.getTagTypeId(), new HashMap<String, List<DocumentRow>>());
                        groupedByEventTagZone.get(row.getEventTypeId()).get(row.getTagTypeId()).putIfAbsent(row.getZoneId(), new ArrayList<DocumentRow>());
                        groupedByEventTagZone.get(row.getEventTypeId()).get(row.getTagTypeId()).get(row.getZoneId()).add(row);
                    }

                    for (Long eventTypeId : groupedByEventTagZone.keySet()) {
                        for (String tagType : groupedByEventTagZone.get(eventTypeId).keySet()) {
                            for (String zoneId : groupedByEventTagZone.get(eventTypeId).get(tagType).keySet()) {
                                Double min = null, max = null, average = 0D;
                                String minPlayerName = null, maxPlayerName = null;
                                DocumentRow tmpRow = null;

                                List<DocumentRow> eventTypeTotals = groupedByEventTagZone.get(eventTypeId).get(tagType).get(zoneId);
                                if (eventTypeTotals != null && !eventTypeTotals.isEmpty()) {
                                    for (DocumentRow row : eventTypeTotals) {
                                        Double playerTotal = row.getTotal();
                                        if (displayType.equals(DisplayType.P90)) {
                                            playerTotal = row.getTotalP90();
                                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                                            playerTotal = row.getTotal100Touches();
                                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                                            playerTotal = row.getTotalAverage();
                                        }

                                        if (min == null || playerTotal < min) {
                                            min = playerTotal;
                                            minPlayerName = players.get(row.getPlayerId()).getKnownName();
                                        }
                                        if (max == null || playerTotal > max) {
                                            max = playerTotal;
                                            maxPlayerName = players.get(row.getPlayerId()).getKnownName();
                                        }

                                        if (tmpRow == null) {
                                            tmpRow = row;
                                        }

                                        average += playerTotal;
                                    }

                                    final boolean opposite = Utils.isOpposite(eventTypeId, tagType);;
                                    Collections.sort(eventTypeTotals, new Comparator<DocumentRow>() {
                                        @Override
                                        public int compare(DocumentRow o1, DocumentRow o2) {
                                            switch (displayType) {
                                                case P90:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotalP90(), o2.getTotalP90()) == 0) {
                                                            return o1.getPlayerId().compareTo(o2.getPlayerId());
                                                        } else {
                                                            return o1.getTotalP90().compareTo(o2.getTotalP90());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotalP90(), o1.getTotalP90()) == 0) {
                                                            return o2.getPlayerId().compareTo(o1.getPlayerId());
                                                        } else {
                                                            return o2.getTotalP90().compareTo(o1.getTotalP90());
                                                        }
                                                    }
                                                case TOUCHES:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotal100Touches(), o2.getTotal100Touches()) == 0) {
                                                            return o1.getPlayerId().compareTo(o2.getPlayerId());
                                                        } else {
                                                            return o1.getTotal100Touches().compareTo(o2.getTotal100Touches());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotal100Touches(), o1.getTotal100Touches()) == 0) {
                                                            return o2.getPlayerId().compareTo(o1.getPlayerId());
                                                        } else {
                                                            return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                                                        }
                                                    }
                                                case AVERAGE:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotalAverage(), o2.getTotalAverage()) == 0) {
                                                            return o1.getPlayerId().compareTo(o2.getPlayerId());
                                                        } else {
                                                            return o1.getTotalAverage().compareTo(o2.getTotalAverage());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotalAverage(), o1.getTotalAverage()) == 0) {
                                                            return o2.getPlayerId().compareTo(o1.getPlayerId());
                                                        } else {
                                                            return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                                                        }
                                                    }
                                                // return (opposite ? o1.getTotalAverage().compareTo(o2.getTotalAverage()) : o2.getTotalAverage().compareTo(o1.getTotalAverage()));
                                                default:
                                                    if (opposite) {
                                                        if (Double.compare(o1.getTotal(), o2.getTotal()) == 0) {
                                                            return o1.getPlayerId().compareTo(o2.getPlayerId());
                                                        } else {
                                                            return o1.getTotal().compareTo(o2.getTotal());
                                                        }
                                                    } else {
                                                        if (Double.compare(o2.getTotal(), o1.getTotal()) == 0) {
                                                            return o2.getPlayerId().compareTo(o1.getPlayerId());
                                                        } else {
                                                            return o2.getTotal().compareTo(o1.getTotal());
                                                        }
                                                    }
                                                // return (opposite ? o1.getTotal().compareTo(o2.getTotal()) : o2.getTotal().compareTo(o1.getTotal()));
                                            }
                                        }
                                    });
                                    if (opposite) {
                                        double tmp = max;
                                        max = min;
                                        min = tmp;
                                    }

                                    int index = 0;
                                    DocumentRow playerRow = null;
                                    for (DocumentRow row : eventTypeTotals) {
                                        if (playerRow == null) {
                                            if (Long.compare(row.getPlayerId(), playerId) == 0 && Long.compare(row.getTeamId(), teamId) == 0) {
                                                playerRow = row;
                                            }
                                            index++;
                                        }
                                    }

                                    // gestione tag
                                    String tagName = Utils.getTagTypeName(tmpRow, curUser.getTvLanguage());
                                    if (StringUtils.isNotBlank(tagName)) {
                                        tagName = "(" + tagName + ")";
                                    }
                                    // gestione posizionale
                                    String zoneAbbName = null, zoneName = null;
                                    if (StringUtils.isNotBlank(zoneId)) {
                                        zoneAbbName = Utils.getZoneIdName(zoneId, true, curUser.getTvLanguage());
                                        if (StringUtils.isNotBlank(zoneAbbName)) {
                                            zoneAbbName = "(" + zoneAbbName + ")";
                                        }
                                        zoneName = Utils.getZoneIdName(zoneId, curUser.getTvLanguage());
                                    }

                                    average = Math.round((average / eventTypeTotals.size()) * 100) / 100D;
                                    TableRow tableRow = new TableRow();
                                    tableRow.setEventTypeId(eventTypeId);
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        tableRow.setEventTypeName(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    } else {
                                        // metriche avanzate
                                        tableRow.setEventTypeName(advancedMetrics.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                    tableRow.setTagTypeName(tagName);
                                    tableRow.setZoneAbbName(zoneAbbName);
                                    tableRow.setZoneName(zoneName);
                                    tableRow.setIndex(index);
                                    tableRow.setPlayerId(playerId);
                                    tableRow.setPlayerName(players.get(playerId).getKnownName());
                                    tableRow.setPlayerPhoto(players.get(playerId).getPhoto());
                                    Double value = -1D;
                                    if (playerRow != null) {
                                        value = playerRow.getTotal();
                                        if (displayType.equals(DisplayType.P90)) {
                                            value = playerRow.getTotalP90();
                                        } else if (displayType.equals(DisplayType.TOUCHES)) {
                                            value = playerRow.getTotal100Touches();
                                        } else if (displayType.equals(DisplayType.AVERAGE)) {
                                            value = playerRow.getTotalAverage();
                                        }
                                    }
                                    tableRow.setValue(value);
                                    tableRow.setMinValue(Math.round(min * 100) / 100D);
                                    tableRow.setMinValuePlayer(minPlayerName);
                                    tableRow.setMaxValue(Math.round(max * 100) / 100D);
                                    tableRow.setMaxValuePlayer(maxPlayerName);
                                    tableRow.setAverage(average);
                                    tableRow.setIsOpposite(opposite);
                                    if (sufferedEventTypes.contains(eventTypeId)) {
                                        sufferedRows.add(tableRow);
                                    } else {
                                        rows.add(tableRow);
                                    }
                                }
                            }
                        }
                    }
                }

                Collections.sort(rows, new Comparator<TableRow>() {
                    @Override
                    public int compare(TableRow o1, TableRow o2) {
                        return o1.getEventTypeName().compareTo(o2.getEventTypeName());
                    }
                });

                Collections.sort(sufferedRows, new Comparator<TableRow>() {
                    @Override
                    public int compare(TableRow o1, TableRow o2) {
                        return o1.getEventTypeName().compareTo(o2.getEventTypeName());
                    }
                });

                String src = "", playerName = "";
                if (!rows.isEmpty()) {
                    src = rows.get(0).getPlayerPhoto();
                    playerName = rows.get(0).getPlayerName();
                } else if (!sufferedRows.isEmpty()) {
                    src = sufferedRows.get(0).getPlayerPhoto();
                    playerName = sufferedRows.get(0).getPlayerName();
                }
                if (StringUtils.isBlank(src)) {
                    src = "unknownxx";
                }
                model.addAttribute("mPlayerName", playerName);
                model.addAttribute("mPlayerPhoto", src);
                model.addAttribute("mRows", rows);
                model.addAttribute("mSufferedRows", sufferedRows);
                model.addAttribute("mTotalRows", (rows.size() + sufferedRows.size()));
                model.addAttribute("mAdvancedMetrics", advancedMetrics);

                // media degli indici
                int offensiveIndexTot = 0, offensiveIndexAmount = 0;
                int defensiveIndexTot = 0, defensiveIndexAmount = 0;
                for (TableRow row : rows) {
                    offensiveIndexAmount++;
                    offensiveIndexTot += (row.getIndex() != null ? row.getIndex() : 0);
                }
                for (TableRow row : sufferedRows) {
                    defensiveIndexAmount++;
                    defensiveIndexTot += (row.getIndex() != null ? row.getIndex() : 0);
                }
                if (offensiveIndexAmount > 0) {
                    int offensiveIndexAverage = Math.round(offensiveIndexTot / offensiveIndexAmount);
                    model.addAttribute("mOffensiveIndexAverage", offensiveIndexAverage);
                }
                if (defensiveIndexAmount > 0) {
                    int defensiveIndexAverage = Math.round(defensiveIndexTot / defensiveIndexAmount);
                    model.addAttribute("mDefensiveIndexAverage", defensiveIndexAverage);
                }

                // dati player
                Long competitionId = Long.valueOf(params.get("competitionId").toString());
                Long seasonId = Long.valueOf(params.get("seasonId").toString());
                seasonId = Utils.getCorrectSeasonId(seasonId, competitionId);
                model.addAttribute("mCompetitionId", competitionId);
                model.addAttribute("mTeamId", teamId);
                model.addAttribute("mPlayerId", playerId);
                model.addAttribute("mPlayer", players.get(playerId));

                PlayerData playerData = mService.getPlayerData(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mPlayerData", playerData);

                PlayerData mostPasses = mService.getPlayerWithMostPasses(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mPlayerMostPasses", mostPasses);
                PlayerData mostPassesReceived = mService.getPlayerWithMostReceivedPasses(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mPlayerMostReceivedPasses", mostPassesReceived);

                PlayerData starterStats = mService.getPlayerStarterStats(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mStarterStats", starterStats);
                PlayerData playtimeStats = mService.getPlayerPlaytimeStats(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mPlaytimeStats", playtimeStats);

                List<PlayerData> playerModules = mService.getPlayerModules(playerId, teamId, competitionId, seasonId);
                int totalAmount = 0;
                Map<Point, Integer> positionAmounts = new LinkedHashMap<>();
                for (PlayerData position : playerModules) {
                    if (StringUtils.isNotBlank(position.getModule()) && position.getModulePosition() != null && position.getTimes() != null) {
                        Point point = ModuleHelper.getPosPlayerByModuleForField(position.getModule().replace("-", ""), position.getModulePosition());
                        for (Point mapPoint : positionAmounts.keySet()) {
                            if (Double.compare(point.getX(), mapPoint.getX()) == 0 && Double.compare(point.getY(), mapPoint.getY()) == 0) {
                                point = mapPoint;
                                break;
                            }
                        }

                        if (positionAmounts.get(point) != null) {
                            positionAmounts.put(point, positionAmounts.get(point) + position.getTimes());
                        } else {
                            positionAmounts.put(point, position.getTimes());
                        }
                        totalAmount += position.getTimes();
                    }
                }
                // BEGIN SORT BY VALUE
                Comparator<Map.Entry<Point, Integer>> valueComparator = new Comparator<Map.Entry<Point, Integer>>() {
                    @Override
                    public int compare(Map.Entry<Point, Integer> e1, Map.Entry<Point, Integer> e2) {
                        Integer v1 = e1.getValue();
                        Integer v2 = e2.getValue();
                        return v2.compareTo(v1);
                    }
                };
                List<Map.Entry<Point, Integer>> listOfEntries = new ArrayList<>(positionAmounts.entrySet());
                Collections.sort(listOfEntries, valueComparator);
                Map<Point, Integer> positionAmountsSorted = new LinkedHashMap<>(listOfEntries.size());
                for (Map.Entry<Point, Integer> entry : listOfEntries) {
                    positionAmountsSorted.put(entry.getKey(), entry.getValue());
                }
                // END SORT BY VALUE
                model.addAttribute("mPlayerPositions", positionAmountsSorted);
                model.addAttribute("mPositionTotal", totalAmount);

                // carriera player
                List<PlayerCareerItem> playerCareer = mService.getPlayerCareer(playerId);
                if (playerCareer != null) {
                    for (PlayerCareerItem item : playerCareer) {
                        if (item.getPlayerId() != null) {
                            item.setPlayer(players.get(item.getPlayerId()));
                        }
                        if (item.getTeamId() != null) {
                            item.setTeam(teams.get(item.getTeamId()));
                        }
                        if (item.getCompetitionId() != null) {
                            item.setCompetition(competitions.get(item.getCompetitionId()));
                        }
                        if (item.getSeasonId() != null) {
                            item.setSeason(seasons.get(item.getSeasonId()));
                        }
                        if (item.getCompetition() != null && item.getSeason() != null) {
                            if (BooleanUtils.isTrue(item.getSeason().getVisible())) {
                                item.setClickable(curUser.hasAccessToCompetition(item.getCompetitionId()));
                            } else {
                                item.setClickable(false);
                            }
                        } else {
                            item.setClickable(false);
                        }
                    }
                }
                model.addAttribute("mPlayerCareer", playerCareer);

                // ultime partite giocate
                List<Fixture> lastFixtures = mService.getPlayerLastFixtures(playerId, teamId, competitionId, seasonId);
                model.addAttribute("mPlayerLastFixtures", lastFixtures);

                model.addAttribute("mPlayers", players);
                model.addAttribute("mTeams", teams);
                model.addAttribute("mCompetitions", competitions);
                model.addAttribute("mCountries", countries);
                model.addAttribute("mPositions", positions);
                model.addAttribute("mPositionDetails", positionDetails);
                model.addAttribute("mFoots", foots);
                model.addAttribute("mAgencies", playerAgencies);
                model.addAttribute("mUser", curUser);
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_OVERVIEW_CONTENT;
    }
}
