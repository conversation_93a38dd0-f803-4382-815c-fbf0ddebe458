package sics.controller.player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.DocumentRow;
import sics.domain.User;
import sics.enums.DisplayType;
import sics.enums.GroupedField;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/ranking")
public class PlayerRankingController extends BaseController {

    private final static String PAGE_RANKING_CONTENT = "player-data/ranking-content.jsp";

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        model.addAttribute("mUser", curUser);

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("seasonId") && params.containsKey("competitionId") && params.containsKey("eventTypeId")) {
                model.addAttribute("mValid", true);
                params.put("type", "player");

                double minEventAmount = 0, maxEventAmount = 9999;
                if (params.containsKey("minEventAmount")) {
                    minEventAmount = Long.parseLong(params.get("minEventAmount").toString());
                    maxEventAmount = Long.parseLong(params.get("maxEventAmount").toString());

                    params.remove("minEventAmount");
                    params.remove("maxEventAmount");
                }

                final DisplayType displayType = DisplayType.valueOf(params.get("totalType").toString().toUpperCase());
                List<DocumentRow> rows = new ArrayList<>();

                double maxTotal = 1;
                String paramCompetitionId = params.get("competitionId").toString();
                List<String> splittedCompetitionId = Arrays.asList(StringUtils.split(paramCompetitionId, "|"));
                Map<Long, Map<Long, DocumentRow>> groupedByTeamPlayer = new HashMap<>();
                for (String competitionId : splittedCompetitionId) {
                    params.remove("competitionId");
                    params.put("competitionId", competitionId);
                    List<DocumentRow> result = MongoHelper.getDocumentsByParams(params, GroupedField.TEAM_PLAYER);
                    List<DocumentRow> rowToRemove = new ArrayList<>();
                    for (DocumentRow row : result) {
                        if (row.getTotal() != null) {
                            if (row.getTotal() < minEventAmount || row.getTotal() > maxEventAmount) {
                                rowToRemove.add(row);
                            }

                            if (row.getTotal() > maxTotal) {
                                maxTotal = row.getTotal();
                            }
                        }
                    }
                    result.removeAll(rowToRemove);
//                    if (!isTotals) {
//                        Utils.calculateP90(result, result, params);
//                    }
                    for (DocumentRow row : result) {
                        groupedByTeamPlayer.putIfAbsent(row.getTeamId(), new HashMap<Long, DocumentRow>());
                        if (groupedByTeamPlayer.get(row.getTeamId()).get(row.getPlayerId()) == null) {
                            groupedByTeamPlayer.get(row.getTeamId()).put(row.getPlayerId(), row);
                        } else {
                            groupedByTeamPlayer.get(row.getTeamId()).get(row.getPlayerId()).setTotal(groupedByTeamPlayer.get(row.getTeamId()).get(row.getPlayerId()).getTotal() + row.getTotal());
                        }
                    }
                }

                List<DocumentRow> sortedRows = new ArrayList<>();
                for (Long teamId : groupedByTeamPlayer.keySet()) {
                    sortedRows.addAll(groupedByTeamPlayer.get(teamId).values());
                }
                params.put("competitionId", paramCompetitionId);
                model.addAttribute("mMinPlaytime", Utils.adjustRowsForPlaytime(sortedRows, params, null));
                switch (displayType) {
                    case P90:
                        params.put("competitionIds", paramCompetitionId);
                        Utils.calculateP90V2(sortedRows, params);
                        // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                        Collections.sort(sortedRows, new Comparator<DocumentRow>() {
                            @Override
                            public int compare(DocumentRow o1, DocumentRow o2) {
                                return o2.getTotalP90().compareTo(o1.getTotalP90());
                            }
                        });
                        break;
                    case TOUCHES:
                        params.put("competitionIds", paramCompetitionId);
                        Utils.calculate100Touches(sortedRows, params);
                        // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                        Collections.sort(sortedRows, new Comparator<DocumentRow>() {
                            @Override
                            public int compare(DocumentRow o1, DocumentRow o2) {
                                return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                            }
                        });
                        break;
                    case AVERAGE:
                        params.put("competitionIds", paramCompetitionId);
                        Utils.calculateAverage(sortedRows, params);
                        // devo ricalcolare il sort, applicato solo al primo evento (nel caso fossero più di uno)
                        Collections.sort(sortedRows, new Comparator<DocumentRow>() {
                            @Override
                            public int compare(DocumentRow o1, DocumentRow o2) {
                                return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                            }
                        });
                        break;
                    default:
                        Collections.sort(sortedRows, new Comparator<DocumentRow>() {
                            @Override
                            public int compare(DocumentRow o1, DocumentRow o2) {
                                return o2.getTotal().compareTo(o1.getTotal());
                            }
                        });
                        break;
                }

                for (DocumentRow row : sortedRows) {
                    rows.add(row);
                }

                model.addAttribute("mRows", rows);
                model.addAttribute("mPlayers", players);
                model.addAttribute("mTeams", teams);
                model.addAttribute("mCountries", countries);
                model.addAttribute("mPositions", positions);
                model.addAttribute("mPositionDetails", positionDetails);
                model.addAttribute("mFoots", foots);
                model.addAttribute("mMaxTotal", maxTotal);

                Double min = null, max = null;
                for (DocumentRow row : rows) {
                    switch (displayType) {
                        case P90:
                            if (min == null || row.getTotalP90() < min) {
                                min = row.getTotalP90();
                            }   if (max == null || row.getTotalP90() > max) {
                                max = row.getTotalP90();
                            }   break;
                        case TOUCHES:
                            if (min == null || row.getTotal100Touches() < min) {
                                min = row.getTotal100Touches();
                            }   if (max == null || row.getTotal100Touches() > max) {
                            max = row.getTotal100Touches();
                        }   break;
                        case AVERAGE:
                            if (min == null || row.getTotalAverage() < min) {
                                min = row.getTotalAverage();
                            }   if (max == null || row.getTotalAverage() > max) {
                                max = row.getTotalAverage();
                            }   break;
                        default:
                            if (min == null || row.getTotal() < min) {
                                min = row.getTotal();
                            }   if (max == null || row.getTotal() > max) {
                                max = row.getTotal();
                            }   break;
                    }
                }
                model.addAttribute("mMin", min);
                model.addAttribute("mMax", max);
                model.addAttribute("mDisplayType", displayType.toString());

                Collections.sort(rows, new Comparator<DocumentRow>() {
                    @Override
                    public int compare(DocumentRow o1, DocumentRow o2) {
                        switch (displayType) {
                            case P90:
                                return o2.getTotalP90().compareTo(o1.getTotalP90());
                            case TOUCHES:
                                return o2.getTotal100Touches().compareTo(o1.getTotal100Touches());
                            case AVERAGE:
                                return o2.getTotalAverage().compareTo(o1.getTotalAverage());
                            default:
                                return o2.getTotal().compareTo(o1.getTotal());
                        }
                    }
                });

//                if (isTotals) {
//                    for (Long playerId : finalResult.keySet()) {
//                        JsonObject data = new JsonObject();
//                        data.addProperty("player", players.get(playerId).getKnownName());
//                        for (DocumentRow row : finalResult.get(playerId)) {
//                            data.addProperty("total" + row.getEventTypeId(), row.getTotal());
//                        }
//                        JsonObject logoObject = new JsonObject();
//                        String src = "/sicsdataanalytics/images/logo/players/" + players.get(playerId).getPhoto() + ".png";
//                        String localPath = "/images/logo/players/" + players.get(playerId).getPhoto() + ".png";
//                        if (!new File(request.getServletContext().getRealPath(localPath)).exists()) {
//                            src = "/sicsdataanalytics/images/logo/players/unknownxx.png";
//                        }
//                        logoObject.addProperty("src", src);
//                        data.add("logo", logoObject);
//                        chartData.add(data);
//                    }
//                } else {
//                    for (Long playerId : finalResult.keySet()) {
//                        JsonObject data = new JsonObject();
//                        data.addProperty("player", players.get(playerId).getKnownName());
//                        for (DocumentRow row : finalResult.get(playerId)) {
//                            data.addProperty("total" + row.getEventTypeId(), row.getTotalP90());
//                        }
//                        JsonObject logoObject = new JsonObject();
//                        String src = "/sicsdataanalytics/images/logo/players/" + players.get(playerId).getPhoto() + ".png";
//                        String localPath = "/images/logo/players/" + players.get(playerId).getPhoto() + ".png";
//                        if (!new File(request.getServletContext().getRealPath(localPath)).exists()) {
//                            src = "/sicsdataanalytics/images/logo/players/unknownxx.png";
//                        }
//                        logoObject.addProperty("src", src);
//                        data.add("logo", logoObject);
//                        chartData.add(data);
//                    }
//                }
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_RANKING_CONTENT;
    }
}
