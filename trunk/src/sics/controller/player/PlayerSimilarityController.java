package sics.controller.player;

import java.time.temporal.ChronoUnit;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.controller.BaseController;
import sics.domain.PlayerData;
import sics.domain.SimilarityDocumentRow;
import sics.domain.User;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.service.UserService;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/player/similarity")
public class PlayerSimilarityController extends BaseController {

    private final static String PAGE_SIMILARITY_CONTENT = "player-data/similarity-content.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/getChartData")
    public String getChartData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters) {
        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            params.put(subParts.get(0), subParts.get(1));
                        }
                    }
                }
            }

            if (params.containsKey("playerId") && params.containsKey("statsTypeIds") && params.containsKey("importances")) {
                User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
                model.addAttribute("mValid", true);
                params.put("type", "player");

                Long playerId = Long.valueOf(params.get("playerId").toString());
                PlayerData basePlayerData = mService.getPlayerLastTeam(playerId);
                String modality = params.get("modality").toString();

                params.put("genere", basePlayerData.getGenere());
                params.put("playerId", playerId);
                params.put("playerIds", StringUtils.join(curUser.getAllowedPlayers(), "|"));
                if (params.containsKey("playtime")) {
                    params.put("playtime", Long.valueOf(params.get("playtime").toString()));
                }
                List<Long> statsTypeIds = new ArrayList<>();
                for (String statsTypeId : StringUtils.split(params.get("statsTypeIds").toString(), "|")) {
                    statsTypeIds.add(Long.valueOf(statsTypeId));
                }
                List<String> importances = Arrays.asList(StringUtils.split(params.get("importances").toString(), "|"));
                if (statsTypeIds.contains(904L)) {
                    statsTypeIds.add(277L);
                    statsTypeIds.add(278L);
                    statsTypeIds.add(279L);
                    statsTypeIds.add(280L);
                    statsTypeIds.add(281L);
                    statsTypeIds.add(282L);
                    statsTypeIds.add(283L);
                    statsTypeIds.add(284L);
                    statsTypeIds.add(285L);
                    statsTypeIds.remove(904L);
                }
                params.put("statsTypeIds", StringUtils.join(statsTypeIds, "|"));

                Date now = new Date();
                List<SimilarityDocumentRow> rows = MongoHelper.getSimilarityDocumentsByParams(params);
                System.out.println("(!) rows loaded in " + Math.abs(ChronoUnit.MILLIS.between(now.toInstant(), new Date().toInstant())) + " milliseconds");

                Map<Long, SimilarityDocumentRow> playerRowsMap = new HashMap<>();
                for (SimilarityDocumentRow row : rows) {
                    playerRowsMap.put(row.getPlayerId(), row);
                }

                // 1. Mappo e calcolo minimo e massimo
                Map<Long, Entry<Double, Double>> statsTypeMinMax = new HashMap<>();
                Map<Long, List<Double>> groupedValuesByStatsType = new HashMap<>();
                Map<Long, Double> playerTotalToucheMap = new HashMap<>();
                for (Long statsTypeId : statsTypeIds) {
                    for (SimilarityDocumentRow row : rows) {
                        groupedValuesByStatsType.putIfAbsent(statsTypeId, new ArrayList<Double>());
                        statsTypeMinMax.putIfAbsent(statsTypeId, new SimpleEntry<>(999D, 0D));
                        Double value = 0D;
                        if (row.getStatsTypeMap() != null && !row.getStatsTypeMap().isEmpty()) {
                            if (row.getStatsTypeMap().containsKey(statsTypeId)) {
                                value = row.getStatsTypeMap().get(statsTypeId);
                            }
                        }
                        groupedValuesByStatsType.get(statsTypeId).add(value);

                        if (value < statsTypeMinMax.get(statsTypeId).getKey()) {
                            statsTypeMinMax.put(statsTypeId, new SimpleEntry<>(value, statsTypeMinMax.get(statsTypeId).getValue()));
                        }
                        if (value > statsTypeMinMax.get(statsTypeId).getValue()) {
                            statsTypeMinMax.put(statsTypeId, new SimpleEntry<>(statsTypeMinMax.get(statsTypeId).getKey(), value));
                        }

                        // metriche tocchi
                        if (statsTypeId >= 277 && statsTypeId <= 285) {
                            playerTotalToucheMap.putIfAbsent(row.getPlayerId(), 0D);
                            playerTotalToucheMap.put(row.getPlayerId(), playerTotalToucheMap.get(row.getPlayerId()) + value);
                        }
                    }
                }

                // 2. Applico minimo e massimo a tutte le metriche
                for (Long statsTypeId : groupedValuesByStatsType.keySet()) {
                    int counter = 0;
                    for (Double statsTypeValue : groupedValuesByStatsType.get(statsTypeId)) {
                        SimilarityDocumentRow row = rows.get(counter);
                        if (row.getStatsTypeMap() == null) {
                            row.setStatsTypeMap(new HashMap<Long, Double>());
                        }
                        if (row.getAdjustedStatsTypeMap() == null) {
                            row.setAdjustedStatsTypeMap(new HashMap<Long, Double>());
                        }
                        // check per divisione per 0
                        if (statsTypeMinMax.get(statsTypeId).getValue() - statsTypeMinMax.get(statsTypeId).getKey() == 0) {
                            row.getAdjustedStatsTypeMap().put(statsTypeId, 0D);
                        } else {
                            if (statsTypeId >= 277 && statsTypeId <= 285) {
                                double divider = 1D;
                                if (playerTotalToucheMap.containsKey(row.getPlayerId())) {
                                    divider = playerTotalToucheMap.get(row.getPlayerId());
                                    if (divider == 0D) {
                                        divider = 1D;
                                    }
                                }
                                row.getAdjustedStatsTypeMap().put(statsTypeId, Math.round(statsTypeValue / divider * 100D) / 100D);
                            } else {
                                row.getAdjustedStatsTypeMap().put(statsTypeId, (statsTypeValue - statsTypeMinMax.get(statsTypeId).getKey()) / (statsTypeMinMax.get(statsTypeId).getValue() - statsTypeMinMax.get(statsTypeId).getKey()));
                            }
                        }
                        counter++;
                    }
                }

                // 3. Calcolo distanza Euclidea
                SimilarityDocumentRow playerRow = playerRowsMap.get(playerId);
                if (playerRow != null) {
                    int statsTypeAmount = 0;
                    String sessionLanguage = StringUtils.defaultIfEmpty(session.getAttribute(GlobalHelper.kBeanLanguage).toString(), "en");
                    Locale locale = Locale.forLanguageTag(sessionLanguage);
                    if (locale != null) {
                        for (String importance : importances) {
                            statsTypeAmount += getImportanceValue(importance, locale);
                        }
                    } else {
                        statsTypeAmount = statsTypeIds.size();
                        if (statsTypeIds.contains(277L)) {
                            statsTypeAmount -= 8;
                        }
                    }
//                    double maxSimilarityDistance = Math.sqrt(statsTypeAmount);

                    List<SimilarityDocumentRow> finalRows = new ArrayList<>();
                    for (SimilarityDocumentRow row : rows) {
                        // non prendo me stesso
                        if (Long.compare(row.getPlayerId(), playerId) != 0) {
                            double similarity = 0;
                            int counter = 0;
                            for (Long statsTypeId : statsTypeIds) {
                                double rowValue = 0, playerValue = 0;
                                if (row.getAdjustedStatsTypeMap() != null && row.getAdjustedStatsTypeMap().containsKey(statsTypeId)) {
                                    rowValue = row.getAdjustedStatsTypeMap().get(statsTypeId);
                                }
                                if (playerRow.getAdjustedStatsTypeMap() != null && playerRow.getAdjustedStatsTypeMap().containsKey(statsTypeId)) {
                                    playerValue = playerRow.getAdjustedStatsTypeMap().get(statsTypeId);
                                }
                                if (statsTypeId >= 277 && statsTypeId <= 285) {
                                    int importance = getImportanceValue(importances.get(counter), locale);
                                    similarity += ((1D * importance / statsTypeAmount) / 9D) * Math.pow((playerValue - rowValue), 2);
                                    counter--;
                                } else {
                                    int importance = getImportanceValue(importances.get(counter), locale);
                                    similarity += (1D * importance / statsTypeAmount) * Math.pow((playerValue - rowValue), 2);
                                }
                                counter++;
                            }
                            similarity = Math.sqrt(similarity);
                            row.setSimilarity(similarity);
                            row.setSimilarityPercentage((1D - similarity) * 100D);
                            finalRows.add(row);
                        }
                    }

                    Collections.sort(finalRows, new Comparator<SimilarityDocumentRow>() {
                        @Override
                        public int compare(SimilarityDocumentRow o1, SimilarityDocumentRow o2) {
                            return o2.getSimilarityPercentage().compareTo(o1.getSimilarityPercentage());
                        }
                    });

                    List<SimilarityDocumentRow> rowsToRemove = new ArrayList<>();
                    if (params.containsKey("ageFrom") && params.containsKey("ageTo")) {
                        Integer from = Integer.valueOf(params.get("ageFrom").toString());
                        Integer to = Integer.valueOf(params.get("ageTo").toString());

                        for (SimilarityDocumentRow row : finalRows) {
                            if (players.containsKey(row.getPlayerId())) {
                                int age = players.get(row.getPlayerId()).getAge();
                                if (age < from || age > to) {
                                    rowsToRemove.add(row);
                                }
                            }
                        }
                    }
                    if (params.containsKey("footId")) {
                        Long footId = Long.valueOf(params.get("footId").toString());

                        for (SimilarityDocumentRow row : finalRows) {
                            if (players.containsKey(row.getPlayerId()) && players.get(row.getPlayerId()).getFootId() != null) {
                                if (Long.compare(footId, players.get(row.getPlayerId()).getFootId()) != 0) {
                                    rowsToRemove.add(row);
                                }
                            } else {
                                rowsToRemove.add(row);
                            }
                        }
                    }
                    if (params.containsKey("countryIds")) {
                        List<String> countryIds = Arrays.asList(StringUtils.split(params.get("countryIds").toString(), "|"));

                        for (SimilarityDocumentRow row : finalRows) {
                            if (players.containsKey(row.getPlayerId()) && players.get(row.getPlayerId()).getCountryId() != null) {
                                boolean valid = false;
                                for (String countryId : countryIds) {
                                    if (StringUtils.contains(countryId, "INT-")) {
                                        Long internationalCompetition = Long.valueOf(countryId.replace("INT-", ""));
                                        if (countries.get(players.get(row.getPlayerId()).getCountryId()).getInternationalCompetitionId() != null) {
                                            if (Long.compare(countries.get(players.get(row.getPlayerId()).getCountryId()).getInternationalCompetitionId(), internationalCompetition) == 0) {
                                                valid = true;
                                                break;
                                            }
                                        }
                                    } else {
                                        Long country = Long.valueOf(countryId);
                                        if (Long.compare(players.get(row.getPlayerId()).getCountryId(), country) == 0) {
                                            valid = true;
                                            break;
                                        }
                                    }
                                }

                                if (!valid) {
                                    rowsToRemove.add(row);
                                }
                            } else {
                                rowsToRemove.add(row);
                            }
                        }
                    }
                    if (params.containsKey("competitionIds")) {
                        List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionIds").toString(), "|"));
                        List<Long> tmpCompetitions = new ArrayList<>();
                        for (String competitionId : competitionIds) {
                            tmpCompetitions.add(Long.valueOf(competitionId));
                        }

                        List<Long> validPlayerIds = new ArrayList<>();
                        for (SimilarityDocumentRow row : finalRows) {
                            validPlayerIds.add(row.getPlayerId());
                        }
                        List<PlayerData> validPlayerDatas = mService.getPlayersCompetitions(validPlayerIds);
                        if (validPlayerDatas != null && !validPlayerDatas.isEmpty()) {
                            Map<Long, PlayerData> validPlayerDataMap = new HashMap<>();
                            for (PlayerData data : validPlayerDatas) {
                                validPlayerDataMap.put(data.getPlayerId(), data);
                            }

                            for (SimilarityDocumentRow row : finalRows) {
                                if (validPlayerDataMap.containsKey(row.getPlayerId()) && validPlayerDataMap.get(row.getPlayerId()).getCompetitionIds() != null) {
                                    boolean valid = false;
                                    for (Long competitionId : tmpCompetitions) {
                                        if (validPlayerDataMap.get(row.getPlayerId()).getCompetitionIds().contains(competitionId)) {
                                            valid = true;
                                            break;
                                        }
                                    }

                                    if (!valid) {
                                        rowsToRemove.add(row);
                                    }
                                } else {
                                    rowsToRemove.add(row);
                                }
                            }
                        }
                    }
                    finalRows.removeAll(rowsToRemove);

                    // solo i primi 10
                    if (StringUtils.equals(modality, "1")) {
                        if (finalRows.size() > 10) {
                            finalRows = finalRows.subList(0, 10);
                        }
                    } else {
                        if (finalRows.size() > 50) {
                            finalRows = finalRows.subList(0, 50);
                        }
                    }
                    finalRows.add(0, playerRow);
                    List<Long> similarPlayerIds = new ArrayList<>();
                    for (SimilarityDocumentRow row : finalRows) {
                        similarPlayerIds.add(row.getPlayerId());
                    }
                    List<PlayerData> playersLastTeam = mService.getPlayersLastTeam(similarPlayerIds);
                    Map<Long, PlayerData> playerLastTeamMap = new HashMap<>();
                    for (PlayerData data : playersLastTeam) {
                        playerLastTeamMap.put(data.getPlayerId(), data);
                    }

                    model.addAttribute("mSimilarPlayers", finalRows);
                    model.addAttribute("mSimilarPlayersData", playerLastTeamMap);
                }

                model.addAttribute("mPlayerRow", playerRow);
                if (statsTypeIds.contains(277L)) {
                    statsTypeIds.remove(277L);
                    statsTypeIds.remove(278L);
                    statsTypeIds.remove(279L);
                    statsTypeIds.remove(280L);
                    statsTypeIds.remove(281L);
                    statsTypeIds.remove(282L);
                    statsTypeIds.remove(283L);
                    statsTypeIds.remove(284L);
                    statsTypeIds.remove(285L);
                    statsTypeIds.add(904L);
                }
                model.addAttribute("mStatsTypeIds", statsTypeIds);
                model.addAttribute("mModality", modality);

                model.addAttribute("mUser", curUser);
                model.addAttribute("mTeams", teams);
                model.addAttribute("mPlayers", players);
                model.addAttribute("mCountries", countries);
                model.addAttribute("mPositions", positions);
                model.addAttribute("mPositionDetails", positionDetails);
                model.addAttribute("mSimilarityMetrics", similarityMetrics);
            } else {
                model.addAttribute("mValid", false);
            }
        }

        return PAGE_SIMILARITY_CONTENT;
    }

    private static int getImportanceValue(String importance, Locale locale) {
        if (StringUtils.equals(importance, SpringApplicationContextHelper.getMessage("metric.importance.low", locale))) {
            return 1;
        } else if (StringUtils.equals(importance, SpringApplicationContextHelper.getMessage("metric.importance.medium", locale))) {
            return 5;
        } else if (StringUtils.equals(importance, SpringApplicationContextHelper.getMessage("metric.importance.high", locale))) {
            return 25;
        } else {
            return 0;
        }
    }
}
