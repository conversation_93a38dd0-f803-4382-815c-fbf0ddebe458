package sics.controller;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.AdvancedMetric;
import sics.domain.DocumentFilter;
import sics.domain.Filter;
import sics.domain.Fixture;
import sics.domain.Group;
import sics.domain.Player;
import sics.domain.PlayerData;
import sics.domain.Season;
import sics.domain.Settings;
import sics.domain.TagType;
import sics.domain.Team;
import sics.domain.TeamPlayer;
import sics.domain.TeamPlayerLast;
import sics.domain.User;
import sics.helper.DynamicReloadableResourceBundleMessageSource;
import sics.helper.GlobalHelper;
import sics.helper.MailHelper;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.listener.SessionListener;
import sics.service.UserService;
import sics.utils.Utils;

@Controller
@RequestMapping("/user")
public class UserController extends BaseController {

    //inserire le location del mapping qui sotto come stringhe
    private final static String PAGE_HOME = "user/home.jsp";
    private final static String PAGE_SEARCH_RESULT = "user/searchResult.jsp";
    private final static String PAGE_AGENCY_DETAILS = "user/agencyDetails.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping(value = "/isValidSession")
    public @ResponseBody
    String isValidSession(HttpServletRequest request, HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (!SessionListener.getSessions().containsKey(curUser.getId()) || !SessionListener.getUserSessions(curUser.getId()).contains(session)) {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionExpired, curUser.getEmail(), curUser.getPassword(), curUser.getId());
                return "expired";
            }
            if (session.getAttribute(GlobalHelper.kLastRequestDate) != null) { // per sicurezza
                Date lastRequest = (Date) session.getAttribute(GlobalHelper.kLastRequestDate);
                int secondsInactivity = session.getMaxInactiveInterval();
                if (DateUtils.addSeconds(lastRequest, secondsInactivity).before(new Date())) {
                    SessionListener.destroySession(session, curUser.getId());
                    return "expired";
                }
            }
            if (SessionListener.getUserMinSessionCreationTime(curUser.getId()) < session.getCreationTime()) {
                session.setMaxInactiveInterval(30); // massimo 30 secondi per dirmi se entrare o uscire
                return "askClearSession";
            }
        } catch (Exception e) {
            GlobalHelper.reportError(e);
            return "expired";
        }
        return "true";
    }

    @RequestMapping(value = "/getIn")
    public @ResponseBody
    String getIn(HttpServletRequest request, HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            List<HttpSession> userSessions = new ArrayList<>(SessionListener.getUserSessions(curUser.getId()));
            for (HttpSession tmpSession : userSessions) {
                if (!tmpSession.getId().equals(session.getId())) {
                    SessionListener.destroySession(tmpSession, curUser.getId(), true);
                }
            }
            if (session.getAttribute(GlobalHelper.kMaxSessionDuration) != null) {
                session.setMaxInactiveInterval((int) session.getAttribute(GlobalHelper.kMaxSessionDuration));
            } else {
                GlobalHelper.sendExceptionMail(request, new Exception("Max Session Duration is null on session"));
            }

            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionUpdated, curUser.getEmail(), curUser.getPassword(), curUser.getId());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "true";
    }

    @RequestMapping(value = "/getOut")
    public @ResponseBody
    String getOut(HttpServletResponse response, ModelMap model, HttpSession session) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            SessionListener.destroySession(session, curUser.getId());
            GlobalHelper.writeLogData(session, GlobalHelper.kActionSessionMaintained, curUser.getEmail(), curUser.getPassword(), curUser.getId());
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return "true";
    }

    @RequestMapping("/home")
    public String home(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response, @RequestParam(value = "country", required = false) Boolean country) {
        String strLocale = request.getLocale().getLanguage().toLowerCase();
        Locale loc;
        if (strLocale.equalsIgnoreCase("it")) {
            loc = new Locale(strLocale);
        } else {
            loc = new Locale("en");
        }
        RequestContextUtils.getLocaleResolver(request).setLocale(request, response, loc);
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }

        return PAGE_HOME;
    }

    @RequestMapping("/getFilters")
    public @ResponseBody
    String getFilters(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters, @RequestParam("filterChanged") String filterChanged) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject filters = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();
            Map<String, Object> extraParams = new HashMap<>();
            List<Integer> extraIndex = new ArrayList<>(Arrays.asList(0));
            boolean loadFixtures = false;

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            String key = subParts.get(0);
                            if (StringUtils.contains(key, "-")) {
                                int index = Integer.parseInt(StringUtils.split(key, "-")[1]);
                                if (!extraIndex.contains(index)) {
                                    extraIndex.add(index);
                                }
                                extraParams.put(key, subParts.get(1));
                            } else {
                                params.put(key, subParts.get(1));
                            }
                        }
                    }
                }
            }

            if (params.containsKey("loadFixtures")) {
                loadFixtures = true;
                params.remove("loadFixtures");
            }

            // per evitare null pointer sul ".startsWith"
            filterChanged = StringUtils.defaultIfEmpty(filterChanged, "");

            if (params.containsKey("seasonId")) {
                Long seasonId = Long.valueOf(params.get("seasonId").toString());
                String paramCompetitionId = null;
                List<Long> competitionIdList = new ArrayList<>();
                if (params.containsKey("competitionId")) {
                    paramCompetitionId = params.get("competitionId").toString();
                    if (StringUtils.isNotBlank(paramCompetitionId)) {
                        if (StringUtils.contains(paramCompetitionId, ",")) {
                            List<String> competitionIds = Arrays.asList(StringUtils.split(paramCompetitionId, ","));
                            for (String competitionIdString : competitionIds) {
                                Long competitionId = Long.valueOf(competitionIdString);
                                if (!competitionIdList.contains(competitionId)) {
                                    competitionIdList.add(competitionId);
                                }
                            }
                        } else {
                            Long competitionId = Long.valueOf(paramCompetitionId);
                            if (!competitionIdList.contains(competitionId)) {
                                competitionIdList.add(competitionId);
                            }
                        }
                    }
                }
                if (competitionIdList.isEmpty() || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
//                    Date start = new Date();
                    // ho solo filtrato per seasonId e voglio sapere quali competizioni ci sono
                    List<Long> competitionIds = MongoHelper.getCompetitionIdsByParams(params);
                    competitionIds = Utils.getSortedCompetitionIds(competitionIds, curUser.getTvLanguage());
                    List<String> competitionList = new ArrayList<>();
                    for (Long tmpCompetitionId : competitionIds) {
                        if (curUser.hasAccessToCompetition(tmpCompetitionId)) {
                            competitionList.add(tmpCompetitionId + "|" + competitions.get(tmpCompetitionId).getCompleteName(curUser.getTvLanguage()));
                        }
                    }
                    filters.addProperty("competitionid", StringUtils.join(competitionList, ","));
//                    System.out.println("Filter competitionid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                }
                if (!competitionIdList.isEmpty()) {
                    boolean isTeam = params.containsKey("type") && StringUtils.equalsIgnoreCase(params.get("type").toString(), "team");
                    Map<String, Object> queryParams = new HashMap<>();
                    Long tmpCompetitionId = competitionIdList.get(0);

                    // carico la lista dei team
                    if ((!params.containsKey("teamId") && !params.containsKey("teamIds"))
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                            || filterChanged.startsWith("competitionId")) {
                        if (filterChanged.startsWith("competitionId") && !StringUtils.equals(filterChanged, "competitionId")) {
                            Integer indexToLoad = Integer.valueOf(StringUtils.replace(filterChanged, "competitionId", ""));
                            if (extraParams.containsKey("seasonId-" + indexToLoad)) {
                                seasonId = Long.valueOf(extraParams.get("seasonId-" + indexToLoad).toString());
                            }
                        }

                        List<Object> teamIds = MongoHelper.getDistinctValues(isTeam, seasonId, tmpCompetitionId, "teamId", queryParams);
                        if (teamIds != null && !teamIds.isEmpty()) {
                            List<String> teamIdList = new ArrayList<>();
                            for (Object tmpTeamId : teamIds) {
                                if (tmpTeamId != null) {
                                    teamIdList.add(tmpTeamId + "|" + teams.get(Long.valueOf(tmpTeamId.toString())).getName(curUser.getTvLanguage()));
                                }
                            }
                            filters.addProperty("teamid", StringUtils.join(teamIdList, ","));
                        }
                    }

                    Date start = null;
                    Map<String, List<Object>> tmpForEventType = new HashMap<>();
                    Map<String, List<Object>> tmp = new HashMap<>();
                    List<AdvancedMetric> advancedMetricsAvailable = new ArrayList<>();
                    List<Boolean> positionalAvailable = new ArrayList<>();

                    for (Long competitionId : competitionIdList) {
                        for (Integer index : extraIndex) {
                            queryParams = new HashMap<>();

                            if (index > 0) {
                                for (String key : extraParams.keySet()) {
                                    if (key.endsWith("-" + index)) {
                                        params.put(key.replace("-" + index, ""), extraParams.get(key));
                                    }
                                }
                            }
                            seasonId = Long.valueOf(params.get("seasonId").toString());

                            // ricarico lista degli eventi
                            start = new Date();
                            if (params.containsKey("eventTypeId")) {
                                queryParams.put("eventTypeId", params.get("eventTypeId"));
                            }
                            if (params.containsKey("tagTypeId")) {
                                queryParams.put("tagTypeId", params.get("tagTypeId"));
                            }
                            if (params.containsKey("isHomeTeam")) {
                                queryParams.put("isHomeTeam", params.get("isHomeTeam"));
                            }
                            if (params.containsKey("matchdayFrom")) {
                                queryParams.put("matchdayFrom", params.get("matchdayFrom"));
                            }
                            if (params.containsKey("matchdayTo")) {
                                queryParams.put("matchdayTo", params.get("matchdayTo"));
                            }
                            if (params.containsKey("homeModule")) {
                                queryParams.put("homeModule", params.get("homeModule"));
                            }
                            if (params.containsKey("awayModule")) {
                                queryParams.put("awayModule", params.get("awayModule"));
                            }
                            if (params.containsKey("countryId")) {
                                queryParams.put("countryId", params.get("countryId"));
                            }
                            if (params.containsKey("positionId")) {
                                queryParams.put("positionId", params.get("positionId"));
                            }
                            if (params.containsKey("positionDetailId")) {
                                queryParams.put("positionDetailId", params.get("positionDetailId"));
                            }
                            if (params.containsKey("bornyearFrom")) {
                                queryParams.put("bornyearFrom", params.get("bornyearFrom"));
                            }
                            if (params.containsKey("bornyearTo")) {
                                queryParams.put("bornyearTo", params.get("bornyearTo"));
                            }
                            if (!isTeam) {
                                if (params.containsKey("teamId")) {
                                    queryParams.put("teamId", params.get("teamId"));
                                }
                            }

                            // se ho cambiato stagione devo aggiornare tutto
                            Map<String, List<Object>> tmpForEventTypeCicle = null;
                            if (StringUtils.equalsIgnoreCase(filterChanged, "seasonId") || filterChanged.startsWith("competitionId")) {
                                queryParams.remove("tagTypeId");

                                if (params.containsKey("eventTypeId")) {
                                    // devo comunque ricaricare la lista degli eventi
                                    start = new Date();
                                    tmpForEventTypeCicle = MongoHelper.getDistinctValues(isTeam, seasonId, competitionId, new HashMap<String, Object>());
                                    System.out.println("tmpForEventTypeCicle: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");

                                    if (tmpForEventType.isEmpty()) {
                                        tmpForEventType = tmpForEventTypeCicle;
                                    } else {
                                        for (String key : tmpForEventTypeCicle.keySet()) {
                                            if (!tmpForEventType.containsKey(key)) {
                                                tmpForEventType.put(key, tmpForEventTypeCicle.get(key));
                                            } else {
                                                for (Object value : tmpForEventTypeCicle.get(key)) {
                                                    if (!tmpForEventType.get(key).contains(value)) {
                                                        tmpForEventType.get(key).add(value);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }

//                            start = new Date();
                            List<AdvancedMetric> advancedMetricsAvailableCicle = MongoHelper.getAdvancedMetricsAvailable(isTeam, seasonId, competitionId, advancedMetrics, queryParams);
//                            System.out.println("advancedMetricsAvailableCicle: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                            if (advancedMetricsAvailable.isEmpty()) {
                                advancedMetricsAvailable = advancedMetricsAvailableCicle;
                            } else {
                                for (AdvancedMetric metric : advancedMetricsAvailableCicle) {
                                    if (!advancedMetricsAvailable.contains(metric)) {
                                        advancedMetricsAvailable.add(metric);
                                    }
                                }
                            }

//                            start = new Date();
                            Map<String, List<Object>> tmpCicle = MongoHelper.getDistinctValues(isTeam, seasonId, competitionId, queryParams);
//                            System.out.println("tmpCicle: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                            if (tmp.isEmpty()) {
                                tmp = tmpCicle;
                            } else {
                                for (String key : tmpCicle.keySet()) {
                                    if (!tmp.containsKey(key)) {
                                        tmp.put(key, tmpCicle.get(key));
                                    } else {
                                        for (Object value : tmpCicle.get(key)) {
                                            if (!tmp.get(key).contains(value)) {
                                                tmp.get(key).add(value);
                                            }
                                        }
                                    }
                                }
                            }
                            // per i giocatori devo controllare a mano se sto prendendo quelli giusti
                            if (tmpCicle.containsKey("playerId")) {
                                if (filterChanged.startsWith("teamId") && !StringUtils.equals(filterChanged, "teamId")) {
                                    Integer indexToLoad = Integer.valueOf(StringUtils.replace(filterChanged, "teamId", ""));
                                    if (extraParams.containsKey("seasonId-" + indexToLoad)) {
                                        Long correctSeasonId = Long.valueOf(extraParams.get("seasonId-" + indexToLoad).toString());
                                        if (Long.compare(seasonId, correctSeasonId) == 0) {
                                            tmp.put("playerId", tmpCicle.get("playerId"));
                                        } else {
                                            tmp.remove("playerId");
                                        }
                                    }
                                }
                            }

                            if (params.containsKey("homeModule")) {
                                queryParams.put("homeModule", params.get("homeModule"));
                            }
                            if (params.containsKey("awayModule")) {
                                queryParams.put("awayModule", params.get("awayModule"));
                            }

//                            start = new Date();
                            List<Boolean> positionalAvailableCicle = MongoHelper.getPositionalAvailability(isTeam, seasonId, competitionId, params);
//                            System.out.println("positionalAvailableCicle: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                            if (positionalAvailable.isEmpty()) {
                                positionalAvailable = positionalAvailableCicle;
                            } else {
                                // hanno sempre la stessa dimensione, non serve verificare il size
                                for (int i = 0; i < positionalAvailableCicle.size(); i++) {
                                    // aggiorno solo quelli a true
                                    if (BooleanUtils.isTrue(positionalAvailableCicle.get(i))) {
                                        positionalAvailable.set(i, true);
                                    }
                                }
                            }
                        }
                    }

//                    System.out.println("Distinct values query: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                    start = new Date();
                    if (tmpForEventType != null && !tmpForEventType.isEmpty()) {
                        tmp.put("eventTypeId", tmpForEventType.get("eventTypeId"));
                    }

                    if (!params.containsKey("eventTypeId")
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                            || filterChanged.startsWith("competitionId")) {
//                        start = new Date();
                        List<Object> eventTypeIds = tmp.get("eventTypeId");
                        if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
                            List<String> eventTypeIdList = new ArrayList<>();
                            for (Object tmpEventTypeId : eventTypeIds) {
                                if (tmpEventTypeId != null) {
                                    Long eventTypeId = Long.valueOf(tmpEventTypeId.toString());
                                    if (!tacticalEventTypes.containsKey(eventTypeId)) {
                                        eventTypeIdList.add(tmpEventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }

                            // metriche avanzate
                            if (advancedMetricsAvailable != null && !advancedMetricsAvailable.isEmpty()) {
                                for (AdvancedMetric metric : advancedMetricsAvailable) {
                                    eventTypeIdList.add(SpringApplicationContextHelper.getMessage("filters.event.advanced.metrics", RequestContextUtils.getLocale(request)) + "|" + metric.getId() + "|" + metric.getDesc(curUser.getTvLanguage()));
                                }
                            }

                            for (Object tmpEventTypeId : eventTypeIds) {
                                if (tmpEventTypeId != null) {
                                    Long eventTypeId = Long.valueOf(tmpEventTypeId.toString());
                                    if (tacticalEventTypes.containsKey(eventTypeId)) {
                                        eventTypeIdList.add(SpringApplicationContextHelper.getMessage("filters.event.tactical", RequestContextUtils.getLocale(request)) + "|" + tmpEventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }

                            filters.addProperty("eventtypeid", StringUtils.join(eventTypeIdList, ","));
//                            System.out.println("Filter eventtypeid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }
                    }

                    if (!params.containsKey("matchdayFrom") || !params.containsKey("matchdayTo")
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId") || StringUtils.equalsIgnoreCase(filterChanged, "competitionId")) {
                        // ricarico i matchday
//                        start = new Date();
                        List<Object> matchDays = tmp.get("matchDay");
                        if (matchDays != null && !matchDays.isEmpty()) {
                            Integer min = null, max = null;

                            List<Integer> matchDayList = new ArrayList<>();
                            for (Object tmpMatchDay : matchDays) {
                                Integer value = (Integer) tmpMatchDay;
                                if (min == null || value < min) {
                                    min = value;
                                }
                                if (max == null || value > max) {
                                    max = value;
                                }
                            }
                            matchDayList.add(min);
                            matchDayList.add(max);
                            filters.addProperty("matchday", StringUtils.join(matchDayList, ","));
//                            System.out.println("Filter matchday: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }
                    }

                    // carico lista partite
//                    start = new Date();
                    if (loadFixtures) {
                        List<Object> fixtureIds = tmp.get("fixtureId");
                        if (fixtureIds != null && !fixtureIds.isEmpty()) {
                            List<Long> fixtureIdsToLoad = new ArrayList<>();
                            for (Object tmpFixtureId : fixtureIds) {
                                fixtureIdsToLoad.add(Long.valueOf(tmpFixtureId.toString()));
                            }
                            List<Fixture> fixtures = mService.getFixtureByIds(fixtureIdsToLoad);
                            if (fixtures != null && !fixtures.isEmpty()) {
                                Map<Long, Fixture> fixtureMap = new HashMap<>();
                                for (Fixture fixture : fixtures) {
                                    fixtureMap.put(fixture.getId(), fixture);
                                }

                                List<String> fixtureIdList = new ArrayList<>();
                                for (Object tmpFixtureId : fixtureIds) {
                                    if (tmpFixtureId != null) {
                                        String tmpFixture = tmpFixtureId.toString();
                                        if (!StringUtils.contains(tmpFixture, "|")) {
                                            Fixture fixture = fixtureMap.get(Long.valueOf(tmpFixture));
                                            if (fixture != null) {
                                                // ho notato che potremmo cancellare le partite quindi bisogna controllare per null
                                                fixtureIdList.add(tmpFixtureId + "|" + teams.get(fixture.getHomeTeamId()).getName(curUser.getTvLanguage()) + " - " + teams.get(fixture.getAwayTeamId()).getName(curUser.getTvLanguage()));
                                            }
                                        }
                                    }
                                }
                                filters.addProperty("fixtureid", StringUtils.join(fixtureIdList, ","));
//                            System.out.println("Filter fixtureid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                            }
                        }
                    }

                    if (params.containsKey("eventTypeId") || params.containsKey("eventTypeIds")) {
                        // carico lista di tag
//                        start = new Date();
                        List<Object> tagTypeIds = tmp.get("tagTypeId");
                        List<String> tagTypeIdList = new ArrayList<>();
                        if (tagTypeIds != null && !tagTypeIds.isEmpty()) {
                            for (Object tmpTagTypeId : tagTypeIds) {
                                if (tmpTagTypeId != null) {
                                    String tmpTagType = tmpTagTypeId.toString();
                                    if (!StringUtils.contains(tmpTagType, "|")) {
                                        tagTypeIdList.add(tmpTagTypeId + "|" + tagTypes.get(Long.valueOf(tmpTagType)).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                        }
                        filters.addProperty("tagtypeid", StringUtils.join(tagTypeIdList, ","));
//                        System.out.println("Filter tagtypeid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");

                        // carico filtro home o away
                        List<String> homeAwayList = new ArrayList<>();
                        homeAwayList.add("true|" + SpringApplicationContextHelper.getMessage("filters.value.home", RequestContextUtils.getLocale(request)));
                        homeAwayList.add("false|" + SpringApplicationContextHelper.getMessage("filters.value.away", RequestContextUtils.getLocale(request)));
                        filters.addProperty("ishometeam", StringUtils.join(homeAwayList, ","));

                        // carico lista dei moduli (home e away)
//                        start = new Date();
//                        if (params.containsKey("tagTypeId")) {
//                            queryParams.put("tagTypeId", params.get("tagTypeId"));
//                        }
//                        if (params.containsKey("isHomeTeam")) {
//                            queryParams.put("isHomeTeam", params.get("isHomeTeam"));
//                        }
//                        if (params.containsKey("matchdayFrom")) {
//                            queryParams.put("matchdayFrom", params.get("matchdayFrom"));
//                        }
//                        if (params.containsKey("matchdayTo")) {
//                            queryParams.put("matchdayTo", params.get("matchdayTo"));
//                        }
//                        if (params.containsKey("homeModule")) {
//                            queryParams.put("homeModule", params.get("homeModule"));
//                        }
//                        if (params.containsKey("awayModule")) {
//                            queryParams.put("awayModule", params.get("awayModule"));
//                        }
                        if (!params.containsKey("homeModule") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<Object> homeModules = tmp.get("homeModule");
                            List<String> homeModuleList = new ArrayList<>();
                            if (homeModules != null && !homeModules.isEmpty()) {
                                for (Object tmpHomeModule : homeModules) {
                                    if (tmpHomeModule != null) {
                                        homeModuleList.add(tmpHomeModule + "|" + tmpHomeModule);
                                    }
                                }
                            }
                            filters.addProperty("homemodule", StringUtils.join(homeModuleList, ","));
//                            System.out.println("Filter homemodule: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        if (!params.containsKey("awayModule") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
//                            start = new Date();
                            List<Object> awayModules = tmp.get("awayModule");
                            List<String> awayModuleList = new ArrayList<>();
                            if (awayModules != null && !awayModules.isEmpty()) {
                                for (Object tmpAwayModule : awayModules) {
                                    if (tmpAwayModule != null) {
                                        awayModuleList.add(tmpAwayModule + "|" + tmpAwayModule);
                                    }
                                }
                            }
                            filters.addProperty("awaymodule", StringUtils.join(awayModuleList, ","));
//                            System.out.println("Filter awaymodule: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        // carico zone per posizionale
//                        start = new Date();
                        if (params.containsKey("homeModule")) {
                            queryParams.put("homeModule", params.get("homeModule"));
                        }
                        if (params.containsKey("awayModule")) {
                            queryParams.put("awayModule", params.get("awayModule"));
                        }

                        List<Boolean> halfAvailable = positionalAvailable.subList(0, 2);
                        List<String> halfAvailableList = new ArrayList<>();
                        int counter = 1;
                        for (Boolean visible : halfAvailable) {
                            if (BooleanUtils.isTrue(visible)) {
                                halfAvailableList.add(counter + "|" + (counter == 1 ? SpringApplicationContextHelper.getMessage("filters.value.offensive", RequestContextUtils.getLocale(request)) : SpringApplicationContextHelper.getMessage("filters.value.defensive", RequestContextUtils.getLocale(request))));
                            }
                            counter++;
                        }
                        filters.addProperty("half", StringUtils.join(halfAvailableList, ","));
//                        System.out.println("Filter half: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");

//                        start = new Date();
                        List<Boolean> zoneAvailable = positionalAvailable.subList(2, 5);
                        List<String> zoneAvailableList = new ArrayList<>();
                        counter = 1;
                        for (Boolean visible : zoneAvailable) {
                            if (BooleanUtils.isTrue(visible)) {
                                zoneAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.zone", RequestContextUtils.getLocale(request)) + " " + counter);
                            }
                            counter++;
                        }
                        filters.addProperty("zone", StringUtils.join(zoneAvailableList, ","));
//                        System.out.println("Filter zone: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");

//                        start = new Date();
                        List<Boolean> channelAvailable = positionalAvailable.subList(5, 10);
                        List<String> channelAvailableList = new ArrayList<>();
                        counter = 1;
                        for (Boolean visible : channelAvailable) {
                            if (BooleanUtils.isTrue(visible)) {
                                channelAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.channel", RequestContextUtils.getLocale(request)) + " " + counter);
                            }
                            counter++;
                        }
                        filters.addProperty("channel", StringUtils.join(channelAvailableList, ","));
//                        System.out.println("Filter channel: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");

//                        start = new Date();
                        List<Boolean> areaAvailable = positionalAvailable.subList(10, 14);
                        List<String> areaAvailableList = new ArrayList<>();
                        counter = 1;
                        for (Boolean visible : areaAvailable) {
                            if (BooleanUtils.isTrue(visible)) {
                                switch (counter) {
                                    case 1:
                                        areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.penalty", RequestContextUtils.getLocale(request)));
                                        break;
                                    case 2:
                                        areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.small", RequestContextUtils.getLocale(request)));
                                        break;
                                    case 3:
                                        areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.penalty.opponent", RequestContextUtils.getLocale(request)));
                                        break;
                                    case 4:
                                        areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.small.opponent", RequestContextUtils.getLocale(request)));
                                        break;
                                }
                            }
                            counter++;
                        }
                        filters.addProperty("area", StringUtils.join(areaAvailableList, ","));
//                        System.out.println("Filter area: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                    }

                    if (!isTeam) {
                        if (!params.containsKey("playerId")
                                || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                                || filterChanged.startsWith("competitionId")
                                || filterChanged.startsWith("teamId")) {
//                            start = new Date();
                            List<Object> playerPositionIds = tmp.get("playerId");
                            List<String> playerIdList = new ArrayList<>();
                            if (playerPositionIds != null && !playerPositionIds.isEmpty()) {
                                for (Object playerPositionId : playerPositionIds) {
                                    if (playerPositionId != null) {
                                        String value = playerPositionId.toString();
                                        if (StringUtils.isNotBlank(value)) {
                                            if (value.contains(";")) {
                                                List<String> splitted = Arrays.asList(StringUtils.split(value, ";"));
                                                if (splitted.size() == 2) {
                                                    Long longPlayerId = Long.valueOf(splitted.get(0));
                                                    Long longPositionId = Long.valueOf(splitted.get(1));
                                                    playerIdList.add(longPlayerId + "|" + players.get(longPlayerId).getKnownName() + " (" + positions.get(longPositionId.intValue()).getDesc(curUser.getTvLanguage()).charAt(0) + ")");
                                                }
                                            } else {
                                                Long longPlayerId = Long.valueOf(value);
                                                playerIdList.add(value + "|" + players.get(longPlayerId).getKnownName());
                                            }
                                        }
                                    }
                                }
                            }
                            filters.addProperty("playerid", StringUtils.join(playerIdList, ","));
//                            System.out.println("Filter playerid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        if (!params.containsKey("countryId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
//                            start = new Date();
                            List<Object> countryIds = tmp.get("countryId");
                            List<String> countryIdList = new ArrayList<>();
                            if (countryIds != null && !countryIds.isEmpty()) {
                                for (Object countryId : countryIds) {
                                    if (countryId != null) {
                                        Long longCountryId = Long.valueOf(countryId.toString());
                                        countryIdList.add(countryId + "|" + countries.get(longCountryId).getName(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("countryid", StringUtils.join(countryIdList, ","));
//                            System.out.println("Filter countryid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        if (!params.containsKey("bornyearFrom") || !params.containsKey("bornyearTo")
                                || StringUtils.equalsIgnoreCase(filterChanged, "seasonId") || StringUtils.equalsIgnoreCase(filterChanged, "competitionId")) {
                            // ricarico i matchday
//                            start = new Date();
                            List<Object> bornYears = tmp.get("bornYear");
                            if (bornYears != null && !bornYears.isEmpty()) {
                                Integer min = null, max = null;

                                List<Integer> bornYearList = new ArrayList<>();
                                for (Object tmpBornYear : bornYears) {
                                    Integer value = (Integer) tmpBornYear;
                                    if (min == null || value < min) {
                                        min = value;
                                    }
                                    if (max == null || value > max) {
                                        max = value;
                                    }
                                }
                                bornYearList.add(min);
                                bornYearList.add(max);
                                filters.addProperty("bornyear", StringUtils.join(bornYearList, ","));
//                                System.out.println("Filter bornyear: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                            }
                        }

                        if (!params.containsKey("footId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
//                            start = new Date();
                            List<Object> footIds = tmp.get("footId");
                            List<String> footIdList = new ArrayList<>();
                            if (footIds != null && !footIds.isEmpty()) {
                                for (Object footId : footIds) {
                                    if (footId != null) {
                                        Integer intFootId = Integer.valueOf(footId.toString());
                                        footIdList.add(footId + "|" + foots.get(intFootId).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("footid", StringUtils.join(footIdList, ","));
//                            System.out.println("Filter footid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        if (!params.containsKey("positionId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
//                            start = new Date();
                            List<Object> positionIds = tmp.get("positionId");
                            List<String> positionIdList = new ArrayList<>();
                            if (positionIds != null && !positionIds.isEmpty()) {
                                for (Object positionId : positionIds) {
                                    if (positionId != null) {
                                        Integer intPositionId = Integer.valueOf(positionId.toString());
                                        positionIdList.add(positionId + "|" + positions.get(intPositionId).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("positionid", StringUtils.join(positionIdList, ","));
//                            System.out.println("Filter positionid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        } else {
                            filters.addProperty("positionid", "");
//                            System.out.println("Filter positionid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }

                        if ((!params.containsKey("positionDetailId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) && params.containsKey("positionId")) {
//                            start = new Date();
                            List<Object> positionDetailIds = tmp.get("positionDetailId");
                            List<String> positionDetailIdList = new ArrayList<>();
                            if (positionDetailIds != null && !positionDetailIds.isEmpty()) {
                                for (Object positionDetailId : positionDetailIds) {
                                    if (positionDetailId != null) {
                                        Integer intPositionDetailId = Integer.valueOf(positionDetailId.toString());
                                        if (intPositionDetailId > 0) {
                                            positionDetailIdList.add(positionDetailId + "|" + positionDetails.get(intPositionDetailId).getDesc(curUser.getTvLanguage()));
                                        }
                                    }
                                }
                            }
                            filters.addProperty("positiondetailid", StringUtils.join(positionDetailIdList, ","));
//                            System.out.println("Filter positiondetailid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        } else {
                            filters.addProperty("positiondetailid", "");
//                            System.out.println("Filter positiondetailid: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                        }
                    }

//                    System.out.println("other: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
                }
            }
        }

        return filters.toString();
    }

    @RequestMapping("/getFiltersV2")
    public @ResponseBody
    String getFiltersV2(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters, @RequestParam("filterChanged") String filterChanged) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject filters = new JsonObject();

        if (StringUtils.isNotBlank(parameters)) {
            Map<String, Object> params = new HashMap<>();
            Map<String, Object> extraParams = new HashMap<>();
            List<Integer> extraIndex = new ArrayList<>(Arrays.asList(0));
            boolean loadFixtures = false;

            List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
            if (parts != null && !parts.isEmpty()) {
                for (String part : parts) {
                    List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                    if (subParts != null && !subParts.isEmpty()) {
                        if (subParts.size() == 2) {
                            String key = subParts.get(0);
                            if (StringUtils.contains(key, "-")) {
                                int index = Integer.parseInt(StringUtils.split(key, "-")[1]);
                                if (!extraIndex.contains(index)) {
                                    extraIndex.add(index);
                                }
                                extraParams.put(key, subParts.get(1));
                            } else {
                                params.put(key, subParts.get(1));
                            }
                        }
                    }
                }
            }

            if (params.containsKey("loadFixtures")) {
                loadFixtures = true;
                params.remove("loadFixtures");
            }

            // per evitare null pointer sul ".startsWith"
            filterChanged = StringUtils.defaultIfEmpty(filterChanged, "");
            boolean isPlayer = false;
            if (params.containsKey("type")) {
                if (StringUtils.equalsIgnoreCase(params.get("type").toString(), "player")) {
                    isPlayer = true;
                }
            }

            if (params.containsKey("seasonId")) {
                Long seasonId = Long.valueOf(params.get("seasonId").toString());
                String paramCompetitionId = null;
                List<Long> competitionIdList = new ArrayList<>();
                if (params.containsKey("competitionId")) {
                    paramCompetitionId = params.get("competitionId").toString();
                    if (StringUtils.isNotBlank(paramCompetitionId)) {
                        if (StringUtils.contains(paramCompetitionId, ",")) {
                            List<String> competitionIds = Arrays.asList(StringUtils.split(paramCompetitionId, ","));
                            for (String competitionIdString : competitionIds) {
                                Long competitionId = Long.valueOf(competitionIdString);
                                if (!competitionIdList.contains(competitionId)) {
                                    competitionIdList.add(competitionId);
                                }
                            }
                        } else {
                            Long competitionId = Long.valueOf(paramCompetitionId);
                            if (!competitionIdList.contains(competitionId)) {
                                competitionIdList.add(competitionId);
                            }
                        }
                    }
                }
                if (competitionIdList.isEmpty() || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                    // ho solo filtrato per seasonId e voglio sapere quali competizioni ci sono
                    List<Long> competitionIds = MongoHelper.getCompetitionIdsByParams(params);
                    competitionIds = Utils.getSortedCompetitionIds(competitionIds, curUser.getTvLanguage());
                    List<String> competitionList = new ArrayList<>();
                    for (Long tmpCompetitionId : competitionIds) {
                        if (curUser.hasAccessToCompetition(tmpCompetitionId)) {
                            if (competitions.containsKey(tmpCompetitionId)) {
                                competitionList.add(tmpCompetitionId + "|" + competitions.get(tmpCompetitionId).getCompleteName(curUser.getTvLanguage()));
                            }
                        }
                    }
                    filters.addProperty("competitionid", StringUtils.join(competitionList, ","));
                }
                if (!competitionIdList.isEmpty()) {
                    boolean isTeam = params.containsKey("type") && StringUtils.equalsIgnoreCase(params.get("type").toString(), "team");
                    Long tmpCompetitionId = competitionIdList.get(0);

                    // carico la lista dei team
                    if ((!params.containsKey("teamId") && !params.containsKey("teamIds"))
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                            || filterChanged.startsWith("competitionId")) {
                        if (filterChanged.startsWith("competitionId") && !StringUtils.equals(filterChanged, "competitionId")) {
                            Integer indexToLoad = Integer.valueOf(StringUtils.replace(filterChanged, "competitionId", ""));
                            if (extraParams.containsKey("seasonId-" + indexToLoad)) {
                                seasonId = Long.valueOf(extraParams.get("seasonId-" + indexToLoad).toString());
                            }
                        }

                        DocumentFilter filter = MongoHelper.getFilter(isTeam, seasonId, tmpCompetitionId, new HashMap<String, Object>());
                        if (filter != null && filter.getTeamIds() != null && !filter.getTeamIds().isEmpty()) {
                            List<String> teamIdList = new ArrayList<>();
                            for (Long teamId : filter.getTeamIds()) {
                                if (teams.containsKey(teamId)) {
                                    teamIdList.add(teamId + "|" + teams.get(teamId).getName(curUser.getTvLanguage()));
                                }
                            }
                            filters.addProperty("teamid", StringUtils.join(teamIdList, ","));
                        }
                    }

                    DocumentFilter documentFilter = new DocumentFilter();
                    for (Long competitionId : competitionIdList) {
                        for (Integer index : extraIndex) {

                            if (index > 0) {
                                for (String key : extraParams.keySet()) {
                                    if (key.endsWith("-" + index)) {
                                        params.put(key.replace("-" + index, ""), extraParams.get(key));
                                    }
                                }
                            }
                            seasonId = Long.valueOf(params.get("seasonId").toString());

                            DocumentFilter filter = MongoHelper.getFilter(isTeam, seasonId, competitionId, params);
                            if (filter != null) {
                                Utils.mergeFilters(documentFilter, filter);
                            }
                        }
                    }
                    documentFilter.calculateExtraFields();
                    if (params.containsKey("loadAllEventTypes")) {
                        if (isPlayer) {
                            documentFilter.setEventTypeIds(new ArrayList<>(playerEventTypes.keySet()));
                            documentFilter.setTagTypeIds(new ArrayList<>(tagTypes.keySet()));
                            documentFilter.setAdvancedMetrics(new ArrayList<>(playerAdvancedMetrics.values()));
                        } else {
                            documentFilter.setEventTypeIds(new ArrayList<>(eventTypes.keySet()));
                            documentFilter.setTagTypeIds(new ArrayList<>(tagTypes.keySet()));
                            documentFilter.setAdvancedMetrics(new ArrayList<>(advancedMetrics.values()));
                        }
                    }

                    // sistemo lista eventi se necessario
                    if (isPlayer) {
                        if (documentFilter.getEventTypeIds() != null && !documentFilter.getEventTypeIds().isEmpty()) {
                            List<Long> eventTypeIdsToRemove = new ArrayList<>();
                            for (Long eventTypeId : documentFilter.getEventTypeIds()) {
                                if (!playerEventTypes.containsKey(eventTypeId)) {
                                    eventTypeIdsToRemove.add(eventTypeId);
                                }
                            }
                            documentFilter.getEventTypeIds().removeAll(eventTypeIdsToRemove);
                        }
                        if (documentFilter.getAdvancedMetrics() != null && !documentFilter.getAdvancedMetrics().isEmpty()) {
                            List<AdvancedMetric> advancedMetricsToRemove = new ArrayList<>();
                            for (AdvancedMetric metric : documentFilter.getAdvancedMetrics()) {
                                if (!playerAdvancedMetrics.containsKey(metric.getId())) {
                                    advancedMetricsToRemove.add(metric);
                                }
                            }
                            documentFilter.getAdvancedMetrics().removeAll(advancedMetricsToRemove);
                        }
                    }

                    if (!params.containsKey("eventTypeId")
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                            || filterChanged.startsWith("competitionId")) {
                        if (documentFilter.getEventTypeIds() != null && !documentFilter.getEventTypeIds().isEmpty()) {
                            List<String> eventTypeIdList = new ArrayList<>();
                            List<String> advancedEventTypeIdList = new ArrayList<>();
                            List<String> tacticalEventTypeIdList = new ArrayList<>();
                            for (Long eventTypeId : documentFilter.getEventTypeIds()) {
                                if (!params.containsKey("multiEventTypeId")) {
                                    if (eventTypes.containsKey(eventTypeId)) {
                                        if (tacticalEventTypes.containsKey(eventTypeId)) {
                                            // metriche tattiche
                                            tacticalEventTypeIdList.add(eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                        } else {
                                            eventTypeIdList.add(eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                        }
                                    }
                                } else {
                                    boolean needOptGroup = false;
                                    if (eventTypeTagList.containsKey(eventTypeId)) {
                                        if (documentFilter.getTagTypeIds() != null && !documentFilter.getTagTypeIds().isEmpty()) {
                                            for (TagType tagType : eventTypeTagList.get(eventTypeId)) {
                                                if (documentFilter.getTagTypeIds().contains(tagType.getId())) {
                                                    needOptGroup = true;
                                                    break;
                                                }
                                            }
                                        }
                                    }

                                    if (eventTypes.containsKey(eventTypeId)) {
                                        if (needOptGroup) {
                                            if (tacticalEventTypes.containsKey(eventTypeId)) {
                                                // metriche tattiche
                                                tacticalEventTypeIdList.add(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()) + "|" + eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                            } else {
                                                eventTypeIdList.add(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()) + "|" + eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                            }
                                            if (documentFilter.getTagTypeIds() != null && !documentFilter.getTagTypeIds().isEmpty()) {
                                                for (Long tagTypeId : documentFilter.getTagTypeIds()) {
                                                    if (tagTypes.containsKey(tagTypeId)) {
                                                        String code = tagTypes.get(tagTypeId).getCode();
                                                        if (StringUtils.isNotBlank(code)) {
                                                            if (code.startsWith(eventTypes.get(eventTypeId).getCode() + "-")) {
                                                                if (tacticalEventTypes.containsKey(eventTypeId)) {
                                                                    // metriche tattiche
                                                                    tacticalEventTypeIdList.add(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()) + "|" + eventTypeId + "-" + tagTypeId + "|" + tagTypes.get(tagTypeId).getDesc(curUser.getTvLanguage()));
                                                                } else {
                                                                    eventTypeIdList.add(eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()) + "|" + eventTypeId + "-" + tagTypeId + "|" + tagTypes.get(tagTypeId).getDesc(curUser.getTvLanguage()));
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        } else {
                                            if (tacticalEventTypes.containsKey(eventTypeId)) {
                                                // metriche tattiche
                                                tacticalEventTypeIdList.add(eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                            } else {
                                                eventTypeIdList.add(eventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage()));
                                            }
                                        }
                                    }
                                }
                            }

                            // metriche avanzate
                            if (documentFilter.getAdvancedMetrics() != null && !documentFilter.getAdvancedMetrics().isEmpty()) {
                                for (AdvancedMetric metric : documentFilter.getAdvancedMetrics()) {
                                    advancedEventTypeIdList.add(metric.getId() + "|" + metric.getDesc(curUser.getTvLanguage()));
                                }
                            }

                            filters.addProperty("eventtypeid", StringUtils.join(eventTypeIdList, ","));
                            filters.addProperty("advancedeventtypeid", StringUtils.join(advancedEventTypeIdList, ","));
                            filters.addProperty("tacticaleventtypeid", StringUtils.join(tacticalEventTypeIdList, ","));
                        }
                    }

                    if (!params.containsKey("matchdayFrom") || !params.containsKey("matchdayTo")
                            || StringUtils.equalsIgnoreCase(filterChanged, "seasonId") || StringUtils.equalsIgnoreCase(filterChanged, "competitionId")) {
                        // ricarico i matchday
                        if (documentFilter.getMatchDays() != null && !documentFilter.getMatchDays().isEmpty()) {
                            Long min = null, max = null;

                            List<Long> matchDayList = new ArrayList<>();
                            for (Long matchDay : documentFilter.getMatchDays()) {
                                if (min == null || matchDay < min) {
                                    min = matchDay;
                                }
                                if (max == null || matchDay > max) {
                                    max = matchDay;
                                }
                            }
                            matchDayList.add(min);
                            matchDayList.add(max);
                            filters.addProperty("matchday", StringUtils.join(matchDayList, ","));
                        }
                    }

                    // carico lista partite
                    if (loadFixtures) {
                        if (documentFilter.getFixtureIds() != null && !documentFilter.getFixtureIds().isEmpty()) {
                            List<Long> fixtureIdsToLoad = new ArrayList<>();
                            for (Long fixtureId : documentFilter.getFixtureIds()) {
                                fixtureIdsToLoad.add(fixtureId);
                            }
                            List<Fixture> fixtures = mService.getFixtureByIds(fixtureIdsToLoad);
                            if (fixtures != null && !fixtures.isEmpty()) {
                                Map<Long, Fixture> fixtureMap = new HashMap<>();
                                for (Fixture fixture : fixtures) {
                                    fixtureMap.put(fixture.getId(), fixture);
                                }

                                List<String> fixtureIdList = new ArrayList<>();
                                for (Long fixtureId : documentFilter.getFixtureIds()) {
                                    String tmpFixture = fixtureId.toString();
                                    if (!StringUtils.contains(tmpFixture, "|")) {
                                        Fixture fixture = fixtureMap.get(Long.valueOf(tmpFixture));
                                        if (fixture != null) {
                                            // ho notato che potremmo cancellare le partite quindi bisogna controllare per null
                                            if (teams.containsKey(fixture.getHomeTeamId()) && teams.containsKey(fixture.getAwayTeamId())) {
                                                fixtureIdList.add(fixtureId + "|" + teams.get(fixture.getHomeTeamId()).getName(curUser.getTvLanguage()) + " - " + teams.get(fixture.getAwayTeamId()).getName(curUser.getTvLanguage()));
                                            }
                                        }
                                    }
                                }
                                filters.addProperty("fixtureid", StringUtils.join(fixtureIdList, ","));
                            }
                        }
                    }

                    if (params.containsKey("eventTypeId") || params.containsKey("eventTypeIds") || params.containsKey("multiEventTypeId")) {
                        // carico lista di tag
                        List<String> tagTypeIdList = new ArrayList<>();
                        if (documentFilter.getTagTypeIds() != null && !documentFilter.getTagTypeIds().isEmpty()) {
                            for (Long tagTypeId : documentFilter.getTagTypeIds()) {
                                String tmpTagType = tagTypeId.toString();
                                if (!StringUtils.contains(tmpTagType, "|")) {
                                    if (tagTypes.containsKey(Long.valueOf(tmpTagType))) {
                                        tagTypeIdList.add(tagTypeId + "|" + tagTypes.get(Long.valueOf(tmpTagType)).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                        }
                        filters.addProperty("tagtypeid", StringUtils.join(tagTypeIdList, ","));

                        // carico filtro home o away
                        List<String> homeAwayList = new ArrayList<>();
                        homeAwayList.add("true|" + SpringApplicationContextHelper.getMessage("filters.value.home", RequestContextUtils.getLocale(request)));
                        homeAwayList.add("false|" + SpringApplicationContextHelper.getMessage("filters.value.away", RequestContextUtils.getLocale(request)));
                        filters.addProperty("ishometeam", StringUtils.join(homeAwayList, ","));

                        // carico lista dei moduli (home e away)
                        if (!params.containsKey("homeModule") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<String> homeModuleList = new ArrayList<>();
                            if (documentFilter.getHomeModules() != null && !documentFilter.getHomeModules().isEmpty()) {
                                for (String homeModule : documentFilter.getHomeModules()) {
                                    homeModuleList.add(homeModule + "|" + homeModule);
                                }
                            }
                            filters.addProperty("homemodule", StringUtils.join(homeModuleList, ","));
                        }

                        if (!params.containsKey("awayModule") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<String> awayModuleList = new ArrayList<>();
                            if (documentFilter.getAwayModules() != null && !documentFilter.getAwayModules().isEmpty()) {
                                for (String awayModule : documentFilter.getAwayModules()) {
                                    awayModuleList.add(awayModule + "|" + awayModule);
                                }
                            }
                            filters.addProperty("awaymodule", StringUtils.join(awayModuleList, ","));
                        }

                        List<Boolean> positionalAvailable = new ArrayList<>();
                        if (documentFilter.getFields() != null && !documentFilter.getFields().isEmpty()) {
                            positionalAvailable.add(documentFilter.getFields().contains("off"));
                            positionalAvailable.add(documentFilter.getFields().contains("dif"));
                            positionalAvailable.add(documentFilter.getFields().contains("zoneOne"));
                            positionalAvailable.add(documentFilter.getFields().contains("zoneTwo"));
                            positionalAvailable.add(documentFilter.getFields().contains("zoneThree"));
                            positionalAvailable.add(documentFilter.getFields().contains("channelOne"));
                            positionalAvailable.add(documentFilter.getFields().contains("channelTwo"));
                            positionalAvailable.add(documentFilter.getFields().contains("channelThree"));
                            positionalAvailable.add(documentFilter.getFields().contains("channelFour"));
                            positionalAvailable.add(documentFilter.getFields().contains("channelFive"));
                            positionalAvailable.add(documentFilter.getFields().contains("difArea"));
                            positionalAvailable.add(documentFilter.getFields().contains("difSmallArea"));
                            positionalAvailable.add(documentFilter.getFields().contains("offArea"));
                            positionalAvailable.add(documentFilter.getFields().contains("offSmallArea"));
                        }

                        if (positionalAvailable.size() == 14) {
                            // carico zone per posizionale
                            List<Boolean> halfAvailable = positionalAvailable.subList(0, 2);
                            List<String> halfAvailableList = new ArrayList<>();
                            int counter = 1;
                            for (Boolean visible : halfAvailable) {
                                if (BooleanUtils.isTrue(visible)) {
                                    halfAvailableList.add(counter + "|" + (counter == 1 ? SpringApplicationContextHelper.getMessage("filters.value.offensive", RequestContextUtils.getLocale(request)) : SpringApplicationContextHelper.getMessage("filters.value.defensive", RequestContextUtils.getLocale(request))));
                                }
                                counter++;
                            }
                            filters.addProperty("half", StringUtils.join(halfAvailableList, ","));

                            List<Boolean> zoneAvailable = positionalAvailable.subList(2, 5);
                            List<String> zoneAvailableList = new ArrayList<>();
                            counter = 1;
                            for (Boolean visible : zoneAvailable) {
                                if (BooleanUtils.isTrue(visible)) {
                                    zoneAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.zone", RequestContextUtils.getLocale(request)) + " " + counter);
                                }
                                counter++;
                            }
                            filters.addProperty("zone", StringUtils.join(zoneAvailableList, ","));

                            List<Boolean> channelAvailable = positionalAvailable.subList(5, 10);
                            List<String> channelAvailableList = new ArrayList<>();
                            counter = 1;
                            for (Boolean visible : channelAvailable) {
                                if (BooleanUtils.isTrue(visible)) {
                                    channelAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.channel", RequestContextUtils.getLocale(request)) + " " + counter);
                                }
                                counter++;
                            }
                            filters.addProperty("channel", StringUtils.join(channelAvailableList, ","));

                            List<Boolean> areaAvailable = positionalAvailable.subList(10, 14);
                            List<String> areaAvailableList = new ArrayList<>();
                            counter = 1;
                            for (Boolean visible : areaAvailable) {
                                if (BooleanUtils.isTrue(visible)) {
                                    switch (counter) {
                                        case 1:
                                            areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.penalty", RequestContextUtils.getLocale(request)));
                                            break;
                                        case 2:
                                            areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.small", RequestContextUtils.getLocale(request)));
                                            break;
                                        case 3:
                                            areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.penalty.opponent", RequestContextUtils.getLocale(request)));
                                            break;
                                        case 4:
                                            areaAvailableList.add(counter + "|" + SpringApplicationContextHelper.getMessage("filters.value.area.small.opponent", RequestContextUtils.getLocale(request)));
                                            break;
                                    }
                                }
                                counter++;
                            }
                            filters.addProperty("area", StringUtils.join(areaAvailableList, ","));
                        }
                    }

                    if (!isTeam) {
                        if (!params.containsKey("playerId")
                                || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")
                                || filterChanged.startsWith("competitionId")
                                || filterChanged.startsWith("teamId")) {
                            List<String> playerIdList = new ArrayList<>();
                            if (documentFilter.getPlayerIds() != null && !documentFilter.getPlayerIds().isEmpty()) {
                                for (Long playerId : documentFilter.getPlayerIds()) {
                                    String value = playerId.toString();
                                    String attributes = "";
                                    if (StringUtils.isNotBlank(value)) {
                                        if (players.containsKey(playerId)) {
                                            String text = players.get(playerId).getKnownName();
                                            if (params.containsKey("competitionId") && params.containsKey("teamId")) {
                                                Long teamId = Long.valueOf(params.get("teamId").toString());
                                                if (seasonTeamPlayerPositions.containsKey(seasonId)) {
                                                    if (seasonTeamPlayerPositions.get(seasonId).containsKey(teamId)) {
                                                        if (seasonTeamPlayerPositions.get(seasonId).get(teamId).containsKey(playerId)) {
                                                            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(seasonId).get(teamId).get(playerId);
                                                            if (teamPlayer.getPositionId() != null) {
                                                                attributes = "positionId=" + teamPlayer.getPositionId();
                                                                text += " (" + positions.get(teamPlayer.getPositionId()).getDesc(curUser.getTvLanguage()).charAt(0) + ")";
                                                            }
                                                        }
                                                    }
                                                }
                                            }

                                            if (StringUtils.isNotBlank(attributes)) {
                                                playerIdList.add(attributes + ";" + value + "|" + text);
                                            } else {
                                                playerIdList.add(value + "|" + text);
                                            }
                                        } else {
                                            // System.out.println("Null player: " + playerId);
                                        }
                                    }
                                }
                            }
                            filters.addProperty("playerid", StringUtils.join(playerIdList, ","));
                        }

                        if (!params.containsKey("countryId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<String> countryIdList = new ArrayList<>();
                            if (documentFilter.getCountryIds() != null && !documentFilter.getCountryIds().isEmpty()) {
                                for (Long countryId : documentFilter.getCountryIds()) {
                                    if (countries.containsKey(countryId)) {
                                        countryIdList.add(countryId + "|" + countries.get(countryId).getName(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("countryid", StringUtils.join(countryIdList, ","));
                        }

                        if (!params.containsKey("bornyearFrom") || !params.containsKey("bornyearTo")
                                || StringUtils.equalsIgnoreCase(filterChanged, "seasonId") || StringUtils.equalsIgnoreCase(filterChanged, "competitionId")) {
                            if (documentFilter.getBornYears() != null && !documentFilter.getBornYears().isEmpty()) {
                                Integer min = null, max = null;

                                List<Integer> bornYearList = new ArrayList<>();
                                for (Integer bornYear : documentFilter.getBornYears()) {
                                    if (min == null || bornYear < min) {
                                        min = bornYear;
                                    }
                                    if (max == null || bornYear > max) {
                                        max = bornYear;
                                    }
                                }
                                bornYearList.add(min);
                                bornYearList.add(max);
                                filters.addProperty("bornyear", StringUtils.join(bornYearList, ","));
                            }
                        }

                        if (!params.containsKey("footId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<String> footIdList = new ArrayList<>();
                            if (documentFilter.getFootIds() != null && !documentFilter.getFootIds().isEmpty()) {
                                for (Long footId : documentFilter.getFootIds()) {
                                    if (foots.containsKey(footId.intValue())) {
                                        footIdList.add(footId + "|" + foots.get(footId.intValue()).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("footid", StringUtils.join(footIdList, ","));
                        }

                        if (!params.containsKey("positionId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) {
                            List<String> positionIdList = new ArrayList<>();
                            if (documentFilter.getPositionIds() != null && !documentFilter.getPositionIds().isEmpty()) {
                                for (Long positionId : documentFilter.getPositionIds()) {
                                    if (positions.containsKey(positionId.intValue())) {
                                        positionIdList.add(positionId + "|" + positions.get(positionId.intValue()).getDesc(curUser.getTvLanguage()));
                                    }
                                }
                            }
                            filters.addProperty("positionid", StringUtils.join(positionIdList, ","));
                        } else {
                            filters.addProperty("positionid", "");
                        }

                        if ((!params.containsKey("positionDetailId") || StringUtils.equalsIgnoreCase(filterChanged, "seasonId")) && params.containsKey("positionId")) {
                            List<String> positionDetailIdList = new ArrayList<>();
                            if (documentFilter.getPositionDetailIds() != null && !documentFilter.getPositionDetailIds().isEmpty()) {
                                for (Long positionDetailId : documentFilter.getPositionDetailIds()) {
                                    if (positionDetailId > 0) {
                                        if (positionDetails.containsKey(positionDetailId.intValue())) {
                                            positionDetailIdList.add(positionDetailId + "|" + positionDetails.get(positionDetailId.intValue()).getDesc(curUser.getTvLanguage()));
                                        }
                                    }
                                }
                            }
                            filters.addProperty("positiondetailid", StringUtils.join(positionDetailIdList, ","));
                        } else {
                            filters.addProperty("positiondetailid", "");
                        }
                    }
                }
            }
        }

        return filters.toString();
    }

    @RequestMapping("/getFiltersAllSeasons")
    public @ResponseBody
    String getFiltersAllSeasons(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("parameters") String parameters, @RequestParam("filterChanged") String filterChanged) {
        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        JsonObject filters = new JsonObject();

        Map<String, Object> params = new HashMap<>();

        List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(parameters, "-_-"));
        if (parts != null && !parts.isEmpty()) {
            for (String part : parts) {
                List<String> subParts = Arrays.asList(StringUtils.split(part, ";"));
                if (subParts != null && !subParts.isEmpty()) {
                    if (subParts.size() == 2) {
                        params.put(subParts.get(0), subParts.get(1));
                    }
                }
            }
        }

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        List<String> competitionList = new ArrayList<>();
        List<String> teamList = new ArrayList<>();
        List<String> eventTypeList = new ArrayList<>();

        List<Long> competitionIds = new ArrayList<>();
        for (Season season : visibleSeasons) {
            params.put("seasonId", season.getId().toString());
            String paramCompetitionId = null;
            Long competitionId = null;
            if (params.containsKey("competitionId")) {
                paramCompetitionId = params.get("competitionId").toString();
                if (StringUtils.isNotBlank(paramCompetitionId)) {
                    if (StringUtils.contains(paramCompetitionId, ",")) {
                        // se multi uso la prima per caricare i filtri
                        competitionId = Long.valueOf(StringUtils.split(paramCompetitionId, ",")[0]);
                    } else {
                        competitionId = Long.valueOf(paramCompetitionId);
                    }
                }
            }
            if (competitionId == null) {
                // ho solo filtrato per seasonId e voglio sapere quali competizioni ci sono
                competitionIds.addAll(MongoHelper.getCompetitionIdsByParams(params));
                // spostato codice fuori dal ciclo per stagione
            }
            if (StringUtils.isNotBlank(filterChanged)) {
                if (filterChanged.startsWith("competitionId")) {
                    boolean isTeam = params.containsKey("type") && StringUtils.equalsIgnoreCase(params.get("type").toString(), "team");

                    Map<String, Object> queryParams = new HashMap<>();
                    // carico la lista dei team se necessario
                    if (!params.containsKey("teamId")) {
                        List<Object> teamIds = MongoHelper.getDistinctValues(isTeam, season.getId(), competitionId, "teamId", queryParams);
                        if (teamIds != null && !teamIds.isEmpty()) {
                            for (Object tmpTeamId : teamIds) {
                                if (tmpTeamId != null) {
                                    String content = tmpTeamId + "|" + teams.get(Long.valueOf(tmpTeamId.toString())).getName(curUser.getTvLanguage());
                                    if (!teamList.contains(content)) {
                                        teamList.add(content);
                                    }
                                }
                            }
                        }
                    }

                    // carico la lista degli eventi se necessario
                    if (params.containsKey("teamId")) {
                        queryParams.put("teamId", params.get("teamId"));
                    }
                    List<Object> eventTypeIds = MongoHelper.getDistinctValues(isTeam, season.getId(), competitionId, "eventTypeId", queryParams);
                    if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
                        for (Object tmpEventTypeId : eventTypeIds) {
                            if (tmpEventTypeId != null) {
                                Long eventTypeId = Long.valueOf(tmpEventTypeId.toString());
                                if (!tacticalEventTypes.containsKey(eventTypeId)) {
                                    String content = tmpEventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                    if (!eventTypeList.contains(content)) {
                                        eventTypeList.add(content);
                                    }
                                }
                            }
                        }
                    }

                    // metriche avanzate
                    List<AdvancedMetric> advancedMetricsAvailable = MongoHelper.getAdvancedMetricsAvailable(isTeam, season.getId(), competitionId, advancedMetrics, queryParams);
                    if (advancedMetricsAvailable != null && !advancedMetricsAvailable.isEmpty()) {
                        for (AdvancedMetric metric : advancedMetricsAvailable) {
                            String content = SpringApplicationContextHelper.getMessage("filters.event.advanced.metrics", RequestContextUtils.getLocale(request)) + "|" + metric.getId() + "|" + metric.getDesc(curUser.getTvLanguage());
                            if (!eventTypeList.contains(content)) {
                                eventTypeList.add(content);
                            }
                        }
                    }

                    if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
                        for (Object tmpEventTypeId : eventTypeIds) {
                            if (tmpEventTypeId != null) {
                                Long eventTypeId = Long.valueOf(tmpEventTypeId.toString());
                                if (tacticalEventTypes.containsKey(eventTypeId)) {
                                    String content = SpringApplicationContextHelper.getMessage("filters.event.tactical", RequestContextUtils.getLocale(request)) + "|" + tmpEventTypeId + "|" + eventTypes.get(eventTypeId).getDesc(curUser.getTvLanguage());
                                    if (!eventTypeList.contains(content)) {
                                        eventTypeList.add(content);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!competitionIds.isEmpty()) {
            competitionIds = Utils.getSortedCompetitionIds(competitionIds, curUser.getTvLanguage());
            for (Long tmpCompetitionId : competitionIds) {
                if (curUser.hasAccessToCompetition(tmpCompetitionId)) {
                    String content = tmpCompetitionId + "|" + competitions.get(tmpCompetitionId).getCompleteName(curUser.getTvLanguage());
                    if (!competitionList.contains(content)) {
                        competitionList.add(tmpCompetitionId + "|" + competitions.get(tmpCompetitionId).getCompleteName(curUser.getTvLanguage()));
                    }
                }
            }
        }
        if (!competitionList.isEmpty()) {
            filters.addProperty("competitionid", StringUtils.join(competitionList, ","));
        }
        if (!teamList.isEmpty()) {
            filters.addProperty("teamid", StringUtils.join(teamList, ","));
        }
        if (!eventTypeList.isEmpty()) {
            filters.addProperty("eventtypeid", StringUtils.join(eventTypeList, ","));
        }

        return filters.toString();
    }

    @RequestMapping(value = "/changeLanguage")
    public @ResponseBody
    String changeLanguage(HttpServletRequest request, ModelMap model, HttpSession session,
            @RequestParam("language") String language) {

        String result = "ko";
        if (StringUtils.isNotBlank(language)) {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (curUser != null) {
                curUser.setTvLanguage(language);
                Settings userSettings = mService.getSettingsByUserId(curUser.getId());
                if (userSettings != null) {
                    // dovrebbe sempre esserlo dato che viene creato dentro al BaseController
                    userSettings.setTvLanguage(language);
                    mService.updateSettings(userSettings);
                }
                session.setAttribute(GlobalHelper.kBeanLanguage, StringUtils.defaultIfEmpty(language, "en"));
                result = "ok";
            }
        }

        return result;
    }

    @RequestMapping(value = "/updateTranslations")
    public @ResponseBody
    String updateTranslations(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (Long.compare(curUser.getGroupsetId(), 2) != 0) {
                return "noPermission";
            }

            DynamicReloadableResourceBundleMessageSource.checkDatabase(true);
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return "ok";
    }

    @RequestMapping(value = "/saveFilter")
    public @ResponseBody
    String saveFilter(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam(value = "id", required = false) Long filterId, @RequestParam("name") String name, @RequestParam("page") String page,
            @RequestParam("filters") String filters) {
        String result = "ko";
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            Filter filter = new Filter();
            filter.setUserId(curUser.getId());
            filter.setGroupsetId(curUser.getGroupsetId());
            filter.setName(GlobalHelper.removeUnwantedCharacter(name, true));
            filter.setPage(page);
            filter.setTmpFilters(filters);
            if (filterId == null) {
                // insert
                filterId = mService.saveFilter(filter);
                if (filterId != null) {
                    filter.setId(filterId);
                    mService.saveFilterDetails(filter);

                    result = "ok";
                }
            } else {
                // update
                filter.setId(filterId);
                mService.updateFilter(filter);
                mService.deleteFilterDetails(filterId);
                mService.saveFilterDetails(filter);

                result = "ok";
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    @RequestMapping(value = "/deleteFilter")
    public @ResponseBody
    String deleteFilter(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("id") Long filterId) {
        String result = "ko";
        try {
            if (filterId != null) {
                mService.deleteFilter(filterId);

                result = "ok";
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    @RequestMapping(value = "/getMatchReport")
    public @ResponseBody
    String getMatchReport(HttpServletResponse response, ModelMap model, HttpSession session, HttpServletRequest request,
            @RequestParam("fixtureId") Long fixtureId) {
        String result = "ko";
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (curUser != null && curUser.getGroupset() != null) {
                if (fixtureId != null) {
                    Long userDownloadAmount = mService.getUserLastWeekMatchDownloadAmount(curUser.getId());
                    if (userDownloadAmount == null || userDownloadAmount < curUser.getGroupset().getMaxDownloadReport()) {
                        GlobalHelper.writeLogData(session, GlobalHelper.kActionMatchReportDownload, curUser.getEmail(), curUser.getPassword(), curUser.getId());

                        String fileProject = mService.getFixtureFileproject(fixtureId);
                        if (StringUtils.isNotBlank(fileProject)) {
                            if (StringUtils.equalsIgnoreCase(curUser.getTvLanguage(), "it")) {
                                result = GlobalHelper.pathMatchReportS3(fileProject.replace(".xml", ".pdf"));
                            } else {
                                result = GlobalHelper.pathMatchReportS3(fileProject.replace(".xml", "_en.pdf"));
                            }
                        }
                        result += "|" + (curUser.getGroupset().getMaxDownloadReport() - userDownloadAmount);
                    } else {
                        result = "limitExceeded";
                    }
                }
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    @RequestMapping("/resetPassword")
    public @ResponseBody
    String resetPassword(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        String result = "ko";
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (curUser != null) {
                mService.resetUserPassword(curUser.getId());
                curUser = mService.getUser(curUser.getId());
                session.setAttribute(GlobalHelper.kBeanUtente, curUser);

                String content = SpringApplicationContextHelper.getMessage("messages.reset.password.email", RequestContextUtils.getLocale(request));
                content += "<br/><br/><strong>Email</strong>: " + curUser.getEmail() + "<br/><strong>Password</strong>: " + curUser.getPassword();
                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.port", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.user", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.pwd", RequestContextUtils.getLocale(request)));
                mail.sendMail(SpringApplicationContextHelper.getMessage("email.from", RequestContextUtils.getLocale(request)), curUser.getEmail(), null, "<EMAIL>", null, null, "SDA - Password Reset", content, null, "");
                result = "ok";
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    @RequestMapping("/updatePassword")
    public @ResponseBody
    String updatePassword(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("password") String password) {
        String result = "ko";
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (curUser != null && StringUtils.isNotBlank(password)) {
                // Update the user's password
                curUser.setPassword(password);
                mService.saveUser(curUser);

                // Update the session with the updated user
                session.setAttribute(GlobalHelper.kBeanUtente, curUser);

                result = "ok";
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    @RequestMapping("/search")
    public String search(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("input") String input, @RequestParam(value = "loadTeams", required = false) Boolean loadTeams,
            @RequestParam(value = "loadPlayers", required = false) Boolean loadPlayers, @RequestParam(value = "loadPlayersLastTeam", required = false) Boolean loadPlayersLastTeam) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (curUser != null && StringUtils.isNotBlank(input)) {
                List<Team> validTeams = new ArrayList<>();
                List<Player> validPlayers = new ArrayList<>();

                if (BooleanUtils.isNotFalse(loadTeams)) {
                    if (curUser.getAllowedTeams() != null && !curUser.getAllowedTeams().isEmpty()) {
                        for (Long teamId : curUser.getAllowedTeams()) {
                            Team team = teams.get(teamId);
                            if (team != null) {
                                if (StringUtils.containsIgnoreCase(team.getName(curUser.getTvLanguage()), input)) {
                                    validTeams.add(team);
                                }
                            }
                        }
                    }
                }

                if (BooleanUtils.isNotFalse(loadPlayers)) {
                    if (curUser.getAllowedPlayers() != null && !curUser.getAllowedPlayers().isEmpty()) {
                        List<Long> validPlayerIds = new ArrayList<>();
                        for (Long playerId : curUser.getAllowedPlayers()) {
                            Player player = players.get(playerId);
                            if (player != null) {
                                boolean found = false;
                                if (StringUtils.containsIgnoreCase(player.getKnownName(), input) || StringUtils.containsIgnoreCase(player.getFirstName(), input) || StringUtils.containsIgnoreCase(player.getLastName(), input)) {
                                    validPlayers.add(player);
                                    if (BooleanUtils.isTrue(loadPlayersLastTeam)) {
                                        validPlayerIds.add(playerId);
                                    }
                                    found = true;
                                }
                                if (input.contains(" ") && !found) {
                                    List<String> splitted = Arrays.asList(StringUtils.split(input, " "));
                                    int wordFoundedAmount = 0;
                                    for (String word : splitted) {
                                        if (StringUtils.containsIgnoreCase(player.getKnownName(), word) || StringUtils.containsIgnoreCase(player.getFirstName(), word) || StringUtils.containsIgnoreCase(player.getLastName(), word)) {
                                            wordFoundedAmount++;
                                        }
                                    }
                                    if (wordFoundedAmount == splitted.size()) {
                                        // devo trovare tutte le parole che ho messo
                                        validPlayers.add(player);
                                        if (BooleanUtils.isTrue(loadPlayersLastTeam)) {
                                            validPlayerIds.add(playerId);
                                        }
                                    }
                                }
                            }
                        }

                        if (!validPlayerIds.isEmpty()) {
                            List<PlayerData> playersLastTeam = mService.getPlayersLastTeam(validPlayerIds);
                            Map<Long, Team> playerLastTeamMap = new HashMap<>();
                            for (PlayerData data : playersLastTeam) {
                                if (data.getLastTeamId() != null) {
                                    if (teams.containsKey(data.getLastTeamId())) {
                                        playerLastTeamMap.put(data.getPlayerId(), teams.get(data.getLastTeamId()));
                                    }
                                }
                            }

                            model.addAttribute("mPlayersLastTeam", playerLastTeamMap);
                        }
                    }
                }

                model.addAttribute("mLoadPlayersLastTeam", loadPlayersLastTeam);
                model.addAttribute("mTeams", validTeams);
                model.addAttribute("mPlayers", validPlayers);
                model.addAttribute("mUser", curUser);
                model.addAttribute("mInput", input);
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return PAGE_SEARCH_RESULT;
    }

    @RequestMapping("/getLastData")
    public @ResponseBody
    String getLastData(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("id") Long id, @RequestParam("isTeam") Boolean isTeam) {
        JsonObject result = new JsonObject();
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (id != null && isTeam != null) {
                TeamPlayerLast data;
                if (BooleanUtils.isTrue(isTeam)) {
                    data = mService.getTeamLastData(id);
                } else {
                    data = mService.getPlayerLastData(id, curUser.getAllowedCompetitions());
                }

                if (data != null) {
                    if (data.getSeasonId() != null) {
                        Long seasonId = data.getSeasonId();
                        if (!groupedSeasons.containsKey(seasonId)) {
                            // devo trovare la seasonId giusta
                            for (Season season : groupedSeasons.values()) {
                                if (season.getSolarId() != null && Long.compare(season.getSolarId(), seasonId) == 0) {
                                    seasonId = season.getId();
                                    break;
                                } else if (season.getNonSolarId() != null && Long.compare(season.getNonSolarId(), seasonId) == 0) {
                                    seasonId = season.getId();
                                    break;
                                }
                            }
                        }
                        result.addProperty("seasonid", seasonId);
                        if (data.getCompetitionId() != null) {
                            result.addProperty("competitionid", data.getCompetitionId());
                            if (data.getTeamId() != null) {
                                result.addProperty("teamid", data.getTeamId());
                                if (data.getPlayerId() != null) {
                                    result.addProperty("playerid", data.getPlayerId());
                                }
                            }
                        }
                    }
                }
            }
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return result.toString();
    }

    @RequestMapping("/getAgencyDetails")
    public String getAgencyDetails(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("id") Long id) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            if (id != null && playerAgencies.containsKey(id)) {
                model.addAttribute("mAgency", playerAgencies.get(id));
                model.addAttribute("mCountries", countries);
            }
            model.addAttribute("mUser", curUser);
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return PAGE_AGENCY_DETAILS;
    }

    @RequestMapping("/savePreferredTeam")
    public @ResponseBody
    String savePreferredTeam(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("id") Long id) {
        try {
            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            curUser.setPreferredTeamId(id);
            Settings settings = mService.getSettingsByUserId(curUser.getId());
            if (settings == null) {
                settings = new Settings();
                settings.setUserId(curUser.getId());
                settings.setPreferredTeamId(id);
                settings.setTvLanguage(curUser.getTvLanguage());

                mService.addSettings(settings);
            } else {
                settings.setPreferredTeamId(id);

                mService.updateSettings(settings);
            }

            return "ok";
        } catch (NumberFormatException ex) {
            GlobalHelper.reportError(ex);
        }
        return "ko";
    }

    @RequestMapping("/getCorrectSeasonId")
    public @ResponseBody
    String getCorrectSeasonId(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("seasonId") Long seasonId, @RequestParam("competitionId") Long competitionId) {
        if (seasonId != null && competitionId != null) {
            try {
                return Utils.getCorrectSeasonId(seasonId, competitionId).toString();
            } catch (NumberFormatException ex) {
                GlobalHelper.reportError(ex);
            }
            return seasonId.toString();
        } else {
            // non dovrebbe mai arrivare qua
            return "";
        }
    }

    @RequestMapping("/getCompetitionGroups")
    public @ResponseBody
    String getCompetitionGroups(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam("seasonId") Long seasonId, @RequestParam("competitionId") Long competitionId) {
        if (seasonId != null && competitionId != null) {
            List<Integer> competitionGroupIds = MongoHelper.getCompetitionGroups(seasonId, competitionId);

            if (competitionGroupIds != null && !competitionGroupIds.isEmpty()) {
                List<Group> competitionGroups = new ArrayList<>();
                for (Integer groupId : competitionGroupIds) {
                    if (groupId != null && Integer.compare(groupId, 0) != 0) {
                        if (groups.containsKey(groupId)) {
                            competitionGroups.add(groups.get(groupId));
                        }
                    }
                }

                Collections.sort(competitionGroups, new Comparator<Group>() {
                    @Override
                    public int compare(Group o1, Group o2) {
                        return o1.getOrder().compareTo(o2.getOrder());
                    }
                });

                Gson gson = new Gson();
                return gson.toJson(competitionGroups);
            }
        }

        return null;
    }
}
