package sics.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import sics.domain.AdvancedMetric;
import sics.domain.Competition;
import sics.domain.Country;
import sics.domain.Filter;
import sics.domain.Player;
import sics.domain.Season;
import sics.domain.TeamPlayer;
import sics.domain.TeamPlayerLast;
import sics.domain.User;
import sics.helper.GlobalHelper;
import sics.helper.MongoHelper;
import sics.service.UserService;

@Controller
@RequestMapping("/player")
public class PlayerController extends BaseController {

    //inserire le location del mapping qui sotto come stringhe
    private final static String PAGE_HOME = "player/home.jsp";

    private final static String PAGE_RANKING = "player/ranking.jsp";
    private final static String PAGE_TREND = "player/trend.jsp";
    private final static String PAGE_OVERVIEW = "player/overview.jsp";
    private final static String PAGE_POSITIONAL = "player/positional.jsp";
    private final static String PAGE_SCATTERPLOT = "player/scatterplot.jsp";
    private final static String PAGE_DISTRIBUTION = "player/distribution.jsp";
    private final static String PAGE_RADAR = "player/radar.jsp";
    private final static String PAGE_PLAYTIME = "player/playtime.jsp";
    private final static String PAGE_TOP = "player/top.jsp";
    private final static String PAGE_SIMILARITY = "player/similarity.jsp";

    //riferimento all'oggetto di servizio per interagire con il DAO
    private final UserService mService = new UserService();

    @RequestMapping("/home")
    public String home(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");
        model.addAttribute("mIsHomePage", true);

        return PAGE_HOME;
    }

    @RequestMapping("/ranking")
    public String ranking(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-ranking");
        model.addAttribute("mFilters", filters);

        return PAGE_RANKING;
    }

    @RequestMapping("/trend")
    public String trend(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-trend");
        model.addAttribute("mFilters", filters);

        return PAGE_TREND;
    }

    @RequestMapping("/overview")
    public String overview(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista delle metriche avanzate
        model.addAttribute("mAdvancedMetrics", advancedMetrics);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-overview");
        model.addAttribute("mFilters", filters);

        return PAGE_OVERVIEW;
    }

    @RequestMapping("/positional")
    public String positional(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-positional");
        model.addAttribute("mFilters", filters);

        return PAGE_POSITIONAL;
    }

    @RequestMapping("/scatterplot")
    public String scatterplot(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-scatterplot");
        model.addAttribute("mFilters", filters);

        return PAGE_SCATTERPLOT;
    }

    @RequestMapping("/distribution")
    public String distribution(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-distribution");
        model.addAttribute("mFilters", filters);

        return PAGE_DISTRIBUTION;
    }

    @RequestMapping("/radar")
    public String radar(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-radar");
        model.addAttribute("mFilters", filters);

        return PAGE_RADAR;
    }

    @RequestMapping("/playtime")
    public String playtime(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        // porto in pagina la lista di eventi opposti
        model.addAttribute("mOppositeEventType", oppositeEventTypes);
        model.addAttribute("mOppositeTagType", oppositeTagTypes);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-playtime");
        model.addAttribute("mFilters", filters);

        return PAGE_PLAYTIME;
    }

    @RequestMapping("/top")
    public String top(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");

        List<Season> visibleSeasons = MongoHelper.getSeasons();
        model.addAttribute("mSeasons", visibleSeasons);

        User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
        List<Filter> filters = mService.getFiltersByPage(curUser.getId(), curUser.getGroupsetId(), "player-top");
        model.addAttribute("mFilters", filters);

        return PAGE_TOP;
    }

    @RequestMapping("/similarity")
    public String similarity(ModelMap model, HttpSession session, HttpServletRequest request, HttpServletResponse response,
            @RequestParam(value = "playerId", required = false) Long playerId) {
        if (!this.initModule(session, model, request, response)) {
            return pageRedirect(PAGE_HOME);
        }
        checkCache();
        model.addAttribute("mPageType", "player");
        model.addAttribute("mPlayerId", playerId);

        if (playerId != null) {
            final User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
            List<AdvancedMetric> sortedMetrics = new ArrayList<>(similarityMetrics.values());
            Collections.sort(sortedMetrics, new Comparator<AdvancedMetric>() {
                @Override
                public int compare(AdvancedMetric o1, AdvancedMetric o2) {
                    return o1.getDesc(curUser.getTvLanguage()).compareTo(o2.getDesc(curUser.getTvLanguage()));
                }
            });

            model.addAttribute("mMetrics", sortedMetrics);
            TeamPlayerLast teamPlayerLast = mService.getPlayerLastData(playerId, curUser.getAllowedCompetitions());
            TeamPlayer teamPlayer = seasonTeamPlayerPositions.get(teamPlayerLast.getSeasonId()).get(teamPlayerLast.getTeamId()).get(teamPlayerLast.getPlayerId());
            model.addAttribute("mTeamPlayer", teamPlayer);

            List<Long> teamCompetitionIds = mService.getTeamCompetitions(teamPlayerLast.getTeamId(), teamPlayerLast.getSeasonId(), curUser.getAllowedCompetitions());
            model.addAttribute("mTeamCompetitions", teamCompetitionIds);

            int minAge = 999, maxAge = 45;
            List<Country> validCountries = new ArrayList<>();
            List<Competition> validInternationalCompetitions = new ArrayList<>();
            for (Long tmpPlayerId : curUser.getAllowedPlayers()) {
                Player player = players.get(tmpPlayerId);
                if (player != null) {
                    if (player.getAge() > 0) {
                        if (player.getAge() < minAge) {
                            minAge = player.getAge();
                        }
                        if (player.getAge() > maxAge) {
                            maxAge = player.getAge();
                        }
                    }

                    if (player.getCountryId() != null && player.getCountryId() > 0L) {
                        Country country = countries.get(player.getCountryId());
                        if (!validCountries.contains(country)) {
                            validCountries.add(country);
                        }
                        if (country.getInternationalCompetitionId() != null) {
                            Competition internationalCompetition = internationalCompetitions.get(country.getInternationalCompetitionId());
                            if (!validInternationalCompetitions.contains(internationalCompetition)) {
                                validInternationalCompetitions.add(internationalCompetition);
                            }
                        }
                    }
                }
            }
            model.addAttribute("mMinAge", minAge);
            model.addAttribute("mMaxAge", maxAge);
            Collections.sort(validInternationalCompetitions, new Comparator<Competition>() {
                @Override
                public int compare(Competition o1, Competition o2) {
                    return o1.getName(curUser.getTvLanguage()).compareTo(o2.getName(curUser.getTvLanguage()));
                }
            });
            Collections.sort(validCountries, new Comparator<Country>() {
                @Override
                public int compare(Country o1, Country o2) {
                    return o1.getName(curUser.getTvLanguage()).compareTo(o2.getName(curUser.getTvLanguage()));
                }
            });
            List<Competition> userAllowedCompetitions = new ArrayList<>();
            for (Competition comp : curUser.getAllowedCompetitions()) {
                if (competitions.containsKey(comp.getId())) {
                    userAllowedCompetitions.add(competitions.get(comp.getId()));
                }
            }
            Collections.sort(userAllowedCompetitions, new Comparator<Competition>() {
                @Override
                public int compare(Competition o1, Competition o2) {
                    return o1.getCompleteName(curUser.getTvLanguage()).compareTo(o2.getCompleteName(curUser.getTvLanguage()));
                }
            });
            model.addAttribute("mInternationalCompetitions", validInternationalCompetitions);
            model.addAttribute("mCountries", validCountries);
            model.addAttribute("mCompetitions", userAllowedCompetitions);

            model.addAttribute("mPositions", positions);
            model.addAttribute("mPositionDetails", positionDetails);
            model.addAttribute("mPositionDetailMap", positionDetailMap);
            model.addAttribute("mFoots", foots);
        }

        return PAGE_SIMILARITY;
    }
}
