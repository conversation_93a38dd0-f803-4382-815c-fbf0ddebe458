package sics.service;

import sics.dao.AppDAO;
import sics.helper.GlobalHelper;
import sics.helper.SpringApplicationContextHelper;

public class BaseService {

    protected AppDAO dao, daoProtezione;

    //Injection del bean a costruttore
    public BaseService() {
        try {
            dao = (AppDAO) SpringApplicationContextHelper.getBean(GlobalHelper.kBeanAppDao);
            daoProtezione = (AppDAO) SpringApplicationContextHelper.getBean(GlobalHelper.kBeanAppDaoProtezione);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }
    
    public void setAppDAO(AppDAO dao) {
        this.dao = dao;
    }

    public void setAppDAOProtezione(AppDAO dao) {
        this.daoProtezione = dao;
    }
}
