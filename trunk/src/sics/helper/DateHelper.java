package sics.helper;

import java.sql.Time;
import java.text.ParseException;
import java.util.Date;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.Locale;
import org.springframework.context.i18n.LocaleContextHolder;

public class DateHelper {

    public static final long DAY_MILLISEC = 1000 * 60 * 60 * 24;
    public static final long ONE_HOUR = 60 * 60 * 1000L;
    public static final String DATE_FORMAT_SHORT = "dd/MM";
    public static final String DATE_FORMAT_USA = "MM/dd/yyyy";
    public static final String DATE_FORMAT_USA_EXT = "yyyyMMdd";
    public static final String DATE_FORMAT_USA_PICKER = "yyyy/MM/dd";
    public static final String DATE_FORMAT_INPUT_DATE = "yyyy-MM-dd";
    public static final String DATE_FORMAT_POINT = "dd.MM.yyyy";
    public static final String DATE_FORMAT = "dd/MM/yyyy";
    public static final String DATE_FORMAT_FULL = "dd/MM/yyyy HH:mm";
    public static final String DATE_FORMAT_ITA = "EEEE dd MMMM";
    public static final String DATE_FORMAT_DATABASE = "dd-MM-yyyy HH:mm";
    public static final String DATE_FORMAT_DATABASE_VTIGER = "yyyy-MM-dd";
    public static final String DATE_FORMAT_STR_TO_DATE = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT_ANALYST_MAIL = "yyyy/MM/dd HH:mm";
    public static final String TIME_FORMAT = "HH:mm";
    public static final String VAL_WEEK = "ABCDEFGHI";

    /**
     * Creates a new instance of DateHelper
     */
    public DateHelper() {
    }

    /**
     * Retrieves the number of days between two dates
     *
     * @param beginDate the Date representing the begin date
     * @param endDate the Date representing the end date
     * @return long the number of days between two dates This method handles the
     * 23 hous Daylight Savings spring forward day
     */
    public static long getDiffDays(Date beginDate, Date endDate) {
        long diffDays = 0;

        if (beginDate != null && endDate != null) {
            // Add one hour in case the duration includes a
            // 23 hour Daylight Savings spring forward day.
            if (endDate.after(beginDate)) {
                diffDays = (endDate.getTime() - beginDate.getTime() + ONE_HOUR) / DAY_MILLISEC;
            } else {
                diffDays = (beginDate.getTime() - endDate.getTime() + ONE_HOUR) / DAY_MILLISEC;
            }
        }

        return diffDays;
    }

    /**
     * Return the date from the input date string in format dd/MM/yyyy
     *
     * @param date the date in string format
     * @return Date the date in Date format
     */
    public static Date toDateUsaFromStringUsaExt(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_USA_EXT);
        Date result = null;
        if (date != null) {
            try {
                result = sdf.parse(date);
            } catch (ParseException pe) {
                result = null;
            }
        }
        return result;
    }

    public static Date toDateUsaFromStringUsa(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_USA_PICKER);
        Date result = null;
        if (date != null) {
            date = date.replace("-", "/");
            try {
                result = sdf.parse(date);
            } catch (ParseException pe) {
                result = null;
            }
        }
        return result;
    }

    /**
     * Return the date from the input date string in format dd/MM/yyyy
     *
     * @param date the date in string format
     * @return Date the date in Date format
     */
    public static Date toDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        Date result = null;
        if (date != null) {
            try {
                result = sdf.parse(date);
            } catch (ParseException pe) {
                result = null;
            }
        }
        return result;
    }

    /**
     * Return the date from the input date string in format dd/MM/yyyy
     *
     * @param date the date in string format
     * @return Date the date in Date format
     */
    public static Date toDate(Date date) {
        Date result = null;
        if (date != null) {
            try {
                result = toDate(toString(date));
            } catch (Exception pe) {
                result = null;
            }
        }

        return result;
    }

    /**
     * Return the date from the input date in format hh:mm
     *
     * @param date the time in string format
     * @return Date the date in Date format
     */
    public static Date toTime(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT);
        Date result = null;
        if (time != null) {
            try {
                result = sdf.parse(time);
            } catch (ParseException pe) {
                result = null;
            }
        }
        return result;
    }

    /**
     * Return the date from the input string in format hh:mm
     *
     * @param date the time in date format
     * @return Date the date in Date format
     */
    public static Date toTime(Date time) {
        Date result = null;

        if (time != null) {
            try {
                result = toTime(toStringTime(time));
            } catch (Exception pe) {
                result = null;
            }
        }

        return result;
    }

    /**
     * Return the date from the input time string in format hh:mm
     *
     * @param date the time in string format
     * @return Date the date in Date format
     */
    public static Date timeToDate(String time) {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT);
        Date result = null;

        if (time != null) {
            try {
                result = sdf.parse(time);
            } catch (ParseException pe) {
                result = null;
            }
        }

        return result;
    }

    /**
     * Return the string in format dd/MM/yyyy from the input date
     *
     * @param date the date in date format
     * @return String the date in string format
     */
    public static String toString(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toStringUsa(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_USA_EXT);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toStringUsaExt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_USA);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * Return the string in format dd/MM/yyyy from the input date
     *
     * @param date the date in date format
     * @return String the date in string format
     */
    public static String toStringDatePicker(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_USA_PICKER);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * yyyy-MM-dd
     *
     * @param date
     * @return
     */
    public static String toStringInputDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_INPUT_DATE);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * Return the string in format dd/MM/yyyy HH:mm from the input date
     *
     * @param date the date in date format
     * @return String the date in string format
     */
    public static String toStringExt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_FULL);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toStringExtDatabase(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_DATABASE);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }
    
    public static String toStringExtDatabaseVtiger(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_DATABASE_VTIGER);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }
    
    public static String toStringStrToDate(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_STR_TO_DATE);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }
    
    public static String toStringAnalystMail(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_ANALYST_MAIL);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * Return the string in time format hh:mm from the input date
     *
     * @param date the date in date format
     * @return String the time in string format
     */
    public static String toStringTime(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(TIME_FORMAT);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * Return the string in time format hh:mm from the input date
     *
     * @param date the date in date format
     * @return String the time in string format
     */
    public static String toStringTime(Time time) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT);
        String result = "";

        if (time != null) {
            result = sdf.format(time);
        }

        return result;
    }

    public static String toStringShort(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_SHORT);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toStringPoint(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FORMAT_POINT);
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    /**
     * Return the date in sql format
     *
     * @param Date the date in java.util.Date format
     * @return java.sql.Date the date in java.sql.Date format
     */
    public static java.sql.Date toSqlDate(Date date) {
        java.sql.Date result = null;

        if (date != null) {
            result = new java.sql.Date(date.getTime());
        }

        return result;
    }

    /**
     * Return the date in sql time format
     *
     * @param Date the date in java.util.Date format
     * @return Time the date in sql Time format
     */
    public static Time toSqlTime(Date date) {
        Time result = null;

        if (date != null) {
            result = new java.sql.Time(date.getTime());
        }

        return result;
    }

    /**
     * Sum date & time to give a resulting date
     *
     * @param Date the date
     * @param Date the time
     * @return Date the summed date
     */
    public static Date sumDateTime(Date date, Date time) {
        Date result = null;
        Calendar calendarTime = new GregorianCalendar();
        Calendar calendarDate = new GregorianCalendar();

        if (date != null && time != null) {
            calendarDate.setTime(date);
            calendarTime.setTime(time);
            calendarTime.set(Calendar.YEAR, calendarDate.get(Calendar.YEAR));
            calendarTime.set(Calendar.MONTH, calendarDate.get(Calendar.MONTH));
            calendarTime.set(Calendar.DAY_OF_MONTH, calendarDate.get(Calendar.DAY_OF_MONTH));
            result = calendarTime.getTime();
        }

        return result;
    }

    public static Date sumDates(Date date, int deltaDays) {

        Calendar calendar = new GregorianCalendar();
        Date currentDate = null;

        if (date != null) {
            calendar.setTime(date);
            calendar.add(Calendar.DAY_OF_MONTH, deltaDays);
            currentDate = calendar.getTime();
        }

        return currentDate;
    }

    public static boolean compareCalendarDates(Calendar cal1, Calendar cal2) {
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH) && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);
    }

    public static String toYearExt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy", LocaleContextHolder.getLocale());
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toMonthExt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("MMM", LocaleContextHolder.getLocale());
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

    public static String toDayExt(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd", LocaleContextHolder.getLocale());
        String result = "";

        if (date != null) {
            result = sdf.format(date);
        }

        return result;
    }

}
