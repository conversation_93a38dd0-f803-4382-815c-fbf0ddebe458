package sics.helper;

import com.amazonaws.AmazonClientException;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.HttpMethod;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Region;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.Date;
import java.util.concurrent.ThreadLocalRandom;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import org.apache.xmlrpc.XmlRpcClient;
import org.springframework.beans.BeansException;
import org.springframework.mobile.device.Device;
import org.springframework.mobile.device.DeviceUtils;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.LogData;
import sics.domain.User;
import sics.service.UserService;

public class GlobalHelper {

    private static final Logger LOGGER = Logger.getLogger(GlobalHelper.class.getName());

    public static final String kBeanAppDao = "appDAO";
    public static final String kBeanAppDaoProtezione = "appDAOProtezione";
    public static final String kBeanUtente = "sicsdataanalyticsUser";
    public static final String kBeanLanguage = "sicsdataanalyticsLang";
    public static final String kBeanS3Client = "AmazonS3Client";
    public static final String kBeanS3ClientPlaylist = "AmazonS3ClientPlaylist";
    public static final String kBeanCountry = "sicsdataanalyticsCountry";
    public static final String kLastRequestDate = "sicsdataanalyticsLastReqestDate";
    public static final String kMaxSessionDuration = "sicsdataanalyticsMaxSessionDuration";
    public static final String kRequestsList = "sicsTvRequestsList";

    public static final String kRoleReferee = "ROLE_REF";
    public static final String kRoleAdmin = "ROLE_ADMIN";

    public static final String kActionLoginCorrect = "LOGIN_CORRECT";
    public static final String kActionSessionUpdated = "SESSION_UPDATED";
    public static final String kActionSessionMaintained = "SESSION_MAINTAINED";
    public static final String kActionSessionExpired = "SESSION_EXPIRED";
    public static final String kActionSessionDestroyed = "SESSION_DESTROYED";
    public static final String kActionSessionInactivity = "SESSION_INACTIVITY";
    public static final String kActionLoginError = "LOGIN_ERROR";
    public static final String kActionLoginNoCompetition = "PERMISSION_ERROR";
    public static final String kActionLogoutSession = "LOGOUT_SESSION";
    public static final String kActionLogoutTimeout = "LOGOUT_TIMEOUT";
    public static final String kActionLicenseExpired = "LICENSE_EXPIRED";
    public static final String kActionDeleteAssignedFixture = "FIXTURE_ANALYST_DELETE";
    public static final String kActionUpdateAssignedFixture = "FIXTURE_ANALYST_UPDATE";
    public static final String kActionAssignFixture = "FIXTURE_ANALYST_INSERT";
    public static final String kActionConfirmTrialTv = "TRIAL_TV_CONFIRMED";
    public static final String kActionDeclineTrialTv = "TRIAL_TV_DECLINED";
    public static final String kActionConfirmTeamRequestTv = "TEAM_REQUEST_TV_CONFIRMED";
    public static final String kActionDeclineTeamRequestTv = "TEAM_REQUEST_TV_DECLINED";
    public static final String kActionGroupsetCreate = "GROUPSET_CREATE";
    public static final String kActionGroupsetUpdate = "GROUPSET_UPDATE";
    public static final String kActionManagerGroupsetCreate = "MANAGER_GROUPSET_CREATE";
    public static final String kActionManagerGroupsetUpdate = "MANAGER_GROUPSET_UPDATE";
    public static final String kActionMatchReportDownload = "MATCH_REPORT_DOWNLOAD";
    public static final String kActionDeviceNormal = "WEB";
    public static final String kActionDeviceMobile = "MOBILE";
    public static final String kActionDeviceTablet = "TABLET";

    public static AmazonS3 s3Client = null;
    public static String kBucketReport = "it.sics.svs.report";

    public static final String kUrlServer = "https://server.sics.it/";
    public static XmlRpcClient serverMail = null;

    static {
        try {
            serverMail = new XmlRpcClient(kUrlServer + "vmrepo/mail.php");
        } catch (MalformedURLException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static void reportError(Exception exception) {
        LOGGER.log(Level.SEVERE, "Unexpected error", exception);
    }

    public static void sendExceptionMail(HttpServletRequest request, Exception ex) {
        reportError(ex);

        StackTraceElement[] st = ex.getStackTrace();
        String StackTrace = "";
        for (StackTraceElement stackTraceElement : st) {
            StackTrace += stackTraceElement.toString() + "\n";
        }

        String contenuto;
        if (request != null) {
            HttpSession session = request.getSession(false);
            User curUser = null;
            if (session != null) {
                Object userToCast = session.getAttribute(GlobalHelper.kBeanUtente);
                if (userToCast != null) {
                    curUser = (User) userToCast;
                }
            }

            contenuto = ex.getClass().toString() + ": " + ex.getMessage() + "\n" + "\n" + StackTrace;
            contenuto += "<br><br>Link: " + request.getRequestURL() + "?" + request.getQueryString();
            if (curUser != null) {
                contenuto += "<br>User: ";
                if (curUser.getFirstName() != null) {
                    contenuto += curUser.getFirstName() + " ";
                }
                if (curUser.getLastName() != null) {
                    contenuto += curUser.getLastName();
                }
            }
        } else {
            contenuto = ex.getClass().toString() + ": " + ex.getMessage() + "\n" + "\n" + StackTrace;
        }
        MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.port", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.user", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.pwd", RequestContextUtils.getLocale(request)));
        mail.sendMail(SpringApplicationContextHelper.getMessage("email.from", RequestContextUtils.getLocale(request)), "<EMAIL>", null, null, null, null, "Errore sics dataanalytics", contenuto, null, "");
    }

    public static void writeLogData(HttpSession session, String action, String username, String password, Long userId) {
        writeLogData(session, null, action, username, password, userId, null);
    }

    public static void writeLogData(UserService service, String action, String username, String password, Long userId) {
        writeLogData(null, service, action, username, password, userId, null);
    }

    public static void writeLogData(HttpSession session, String action, String username, String password, Long userId, String video) {
        writeLogData(session, null, action, username, password, userId, video);
    }

    public static void writeLogData(HttpSession session, UserService service, String action, String username, String password, Long userId, String video) {
        try {
            if (service == null) {
                service = new UserService();
            }

            LogData logData = new LogData();
            logData.setLoginname(username);
            logData.setPassword(password);
            logData.setUserId(userId);
            logData.setAction(action);
            logData.setVideo(video);

            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (sra != null) {
                Device device = DeviceUtils.getCurrentDevice(sra.getRequest());
                if (device != null) {
                    if (device.isTablet()) {
                        logData.setDevice(GlobalHelper.kActionDeviceTablet);
                    } else if (device.isMobile()) {
                        logData.setDevice(GlobalHelper.kActionDeviceMobile);
                    } else if (device.isNormal()) {
                        logData.setDevice(GlobalHelper.kActionDeviceNormal);
                    }
                }
            }

            logData.setApplication(SpringServletContextHelper.getInitParameter("webAppRootKey"));

            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null) {
                WebAuthenticationDetails details = (WebAuthenticationDetails) authentication.getDetails();
                logData.setAddressId(details.getRemoteAddress());
                logData.setSessionId(details.getSessionId());
            }

            service.saveLogData(logData);
        } catch (BeansException ex) {
            reportError(ex);
        }
    }

    public static String replaceString(String input, String find, String replace) {

        final StringBuffer result = new StringBuffer();
        int startIdx = 0;
        int idxOld = 0;
        while ((idxOld = input.indexOf(find, startIdx)) >= 0) {
            result.append(input.substring(startIdx, idxOld));
            result.append(replace);
            startIdx = idxOld + find.length();
        }
        result.append(input.substring(startIdx));

        return result.toString();
    }

    public static boolean isLocalEnvironment() {
        File file = new File("C:/lavori/sicsdataaccess2025");
        return file.exists();
    }

    // Metodo per generare una stringa casuale
    public static String generateRandomString(int length) {
        char[] alfanum = {
            '0', '1', '2', '3', '4',
            '5', '6', '7', '8', '9',
            'A', 'B', 'C', 'D', 'E', 'F', 'G',
            'H', 'I', 'J', 'K', 'L', 'M',
            'N', 'O', 'P', 'Q', 'R', 'S',
            'T', 'U', 'V', 'W', 'X',
            'Y', 'Z'
        };
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < length; i++) {
            int randomIndex = ThreadLocalRandom.current().nextInt(alfanum.length);
            sb.append(alfanum[randomIndex]);
        }

        return sb.toString();
    }

    public static String removeUnwantedCharacter(String text) {
        return removeUnwantedCharacter(text, false);
    }

    public static String removeUnwantedCharacter(String text, boolean keepSpaces) {
        text = text.replaceAll(" ", "_").replaceAll("[^a-zA-Z0-9_]", "");
        if (keepSpaces) {
            text = text.replaceAll("_", " ");
        }
        return text;
    }

    public static void initS3Client() {
        try {
            String accessKey = "********************";
            String secretKey = "EAk+N/aQBAc5frsEzI3YFjCLL98e24BtqL/nRinj";
            AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
            ClientConfiguration clientConfig = new ClientConfiguration();
            clientConfig.setProtocol(Protocol.HTTPS);
            // DAFARE test per problema nel download
            clientConfig.setSocketTimeout(240 * 1000);
            s3Client = new AmazonS3Client(credentials, clientConfig);
            Region usWest2 = Region.getRegion(Regions.EU_WEST_1);
            s3Client.setRegion(usWest2);
        } catch (IllegalArgumentException ex) {
            reportError(ex);
        }
    }

    public static String pathMatchReportS3(String name) {
        if (s3Client == null) {
            initS3Client();
        }
        String value = "";
        try {
            String key = name;
            Date expiration = new Date();
            Long msec = expiration.getTime();

            Integer h24 = 1000 * 60 * 60 * 24;
            msec += h24;

            String bucketName = kBucketReport;
            expiration.setTime(msec);
            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest("/" + bucketName, key);
            generatePresignedUrlRequest.setMethod(HttpMethod.GET); // Default.

            generatePresignedUrlRequest.setExpiration(expiration);
            URL url = s3Client.generatePresignedUrl(generatePresignedUrlRequest);
            value = url.toString();
        } catch (IllegalArgumentException | AmazonClientException ex) {
            reportError(ex);
        }
        return value;
    }

    public static String formatNumber(long number, String thousandsSuffix, String millionsSuffix) {
        // Create a DecimalFormatSymbols instance to set the decimal separator to a comma.
        DecimalFormatSymbols symbols = new DecimalFormatSymbols();
        symbols.setDecimalSeparator('.');

        // Create a DecimalFormat instance that formats to one decimal place.
        DecimalFormat df = new DecimalFormat("#.#", symbols);

        // For numbers less than 1,000,000, format as thousands (k)
        if (number < 1000000) {
            double thousands = number / 1000.0;
            // If the number divides evenly, drop the decimal part.
            if (thousands == (long) thousands) {
                return String.format("%d", (long) thousands) + " " + thousandsSuffix;
            } else {
                return df.format(thousands) + " " + thousandsSuffix;
            }
        } else {
            // Otherwise, format as millions (M)
            double millions = number / 1000000.0;
            if (millions == (long) millions) {
                return String.format("%d", (long) millions) + " " + millionsSuffix;
            } else {
                return df.format(millions) + " " + millionsSuffix;
            }
        }
    }
}
