package sics.helper;

import java.util.Locale;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.NoSuchMessageException;

public class SpringApplication<PERSON>ontextHelper implements ApplicationContextAware {

    private static ApplicationContext mContext;

    /**
     * This method is called from within the ApplicationContext once it is done
     * starting up, it will stick a reference to itself into this bean.
     *
     * @param context a reference to the ApplicationContext.
     */
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        mContext = context;
    }

    public static ApplicationContext getApplicationContext() {
        return mContext;
    }

    /**
     * This is about the same as context.getBean("beanName"), except it has its
     * own static handle to the Spring context, so calling this method
     * statically will give access to the beans by name in the Spring
     * application context. As in the context.getBean("beanName") call, the
     * caller must cast to the appropriate target class. If the bean does not
     * exist, then a Runtime error will be thrown.
     *
     * @param beanName the name of the bean to get.
     * @return an Object reference to the named bean.
     */
    public static Object getBean(String beanName) {
        return mContext.getBean(beanName);
    }

    public static String getMessage(String name) {
        return getMessage(name, "");
    }

    public static String getMessage(String name, String arg) {
        String[] args = {arg};
        return getMessage(name, args);
    }

    public static String getMessage(String name, String arg1, String arg2) {
        String[] args = {arg1, arg2};
        return getMessage(name, args);
    }

    public static String getMessage(String name, String arg1, String arg2, String arg3) {
        String[] args = {arg1, arg2, arg3};
        return getMessage(name, args);
    }

    public static String getMessage(String name, Object[] args) {
        return mContext.getMessage(name, args, Locale.ITALIAN);
    }

    public static String getMessage(String name, Locale language) {
        String[] args = {""};
        try {
            return mContext.getMessage(name, args, language);
        } catch (NoSuchMessageException ex) { // se non trovo il messaggio evito di far esplodere la pagina
            return "Message Code Not Found";
        }
    }

}
