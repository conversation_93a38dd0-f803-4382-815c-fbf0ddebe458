package sics.helper;

import org.bson.codecs.Codec;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;
import sics.domain.DocumentRow;

/**
 *
 * <AUTHOR>
 */
public class DocumentRowCodecProvider implements CodecProvider {
    
    private final CodecProvider pojoCodecProvider;
    
    public DocumentRowCodecProvider(CodecProvider pojoCodecProvider) {
        this.pojoCodecProvider = pojoCodecProvider;
    }
    
    @Override
    public <T> Codec<T> get(Class<T> clazz, CodecRegistry registry) {
        if (clazz.equals(DocumentRow.class)) {
            Codec<DocumentRow> defaultCodec = (Codec<DocumentRow>) pojoCodecProvider.get(clazz, registry);
            return (Codec<T>) new DocumentRowCodec(defaultCodec, registry);
        }
        return null;
    }
}