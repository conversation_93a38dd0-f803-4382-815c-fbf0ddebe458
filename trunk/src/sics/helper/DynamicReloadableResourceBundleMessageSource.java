package sics.helper;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import sics.domain.Translation;

/**
 *
 * <AUTHOR>
 */
public class DynamicReloadableResourceBundleMessageSource extends ReloadableResourceBundleMessageSource {

    private static final Map<String, Map<String, String>> databaseMessages = new HashMap<>();
    private static Boolean databaseLoaded = false;

    @Override
    protected MessageFormat resolveCode(String code, Locale locale) {
        checkDatabase(false);
        String localeKey = locale.toString();
        if (databaseMessages.containsKey(code) && databaseMessages.get(code).containsKey(localeKey)) {
            return new MessageFormat(databaseMessages.get(code).get(localeKey), locale);
        }
        return super.resolveCode(code, locale);
    }

    @Override
    protected String resolveCodeWithoutArguments(String code, Locale locale) {
        checkDatabase(false);
        String localeKey = locale.toString();
        if (databaseMessages.containsKey(code) && databaseMessages.get(code).containsKey(localeKey)) {
            return databaseMessages.get(code).get(localeKey);
        }
        return super.resolveCodeWithoutArguments(code, locale);
    }

    public static void checkDatabase(boolean force) {
        if (!databaseLoaded || BooleanUtils.isTrue(force)) {
            databaseMessages.clear();       // serve solo nel caso in cui togliamo delle traduzioni
            List<Translation> translations = MongoHelper.getTranslations(4);
            if (!translations.isEmpty()) {
                boolean isLocal = GlobalHelper.isLocalEnvironment();
                for (Translation translation : translations) {
                    databaseMessages.put(translation.getKey(), new HashMap<String, String>());
                    databaseMessages.get(translation.getKey()).put("it", StringUtils.defaultIfEmpty(StringUtils.defaultIfEmpty(translation.getIt(), translation.getEn()), ""));
                    databaseMessages.get(translation.getKey()).put("en", StringUtils.defaultIfEmpty(StringUtils.defaultIfEmpty(translation.getEn(), translation.getEn()), ""));
                    databaseMessages.get(translation.getKey()).put("fr", StringUtils.defaultIfEmpty(StringUtils.defaultIfEmpty(translation.getFr(), translation.getEn()), ""));
                    databaseMessages.get(translation.getKey()).put("es_ES", StringUtils.defaultIfEmpty(StringUtils.defaultIfEmpty(translation.getEs_ES(), translation.getEn()), ""));
                    databaseMessages.get(translation.getKey()).put("ru", StringUtils.defaultIfEmpty(StringUtils.defaultIfEmpty(translation.getRu(), translation.getEn()), ""));

                    if (StringUtils.isNotBlank(translation.getChangedColumnsIndex()) && !isLocal) {
                        // non posso permettermi di sminchiare tutto per l'aggiornamento
                        // quindi try catch, nel caso va in errore rimarrà come da aggiornare, pace
                        try {
                            translation.setChangedColumnsIndex(null);
                            MongoHelper.updateDocument("translations", translation);
                        } catch (Exception ex) {
                            GlobalHelper.reportError(ex);
                        }
                    }
                }
            }

            // key 'vuoto' statica
            databaseMessages.put("vuoto", new HashMap<String, String>());
            databaseMessages.get("vuoto").put("it", "");
            databaseMessages.get("vuoto").put("en", "");
            databaseMessages.get("vuoto").put("fr", "");
            databaseMessages.get("vuoto").put("es_ES", "");
            databaseMessages.get("vuoto").put("ru", "");

            databaseLoaded = true;
        }
    }
}
