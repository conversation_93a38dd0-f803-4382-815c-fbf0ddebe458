package sics.helper;

import java.util.HashMap;
import org.apache.commons.lang.StringUtils;
import org.bson.BsonDocumentReader;
import org.bson.BsonReader;
import org.bson.BsonWriter;
import org.bson.Document;
import org.bson.codecs.Codec;
import org.bson.codecs.DecoderContext;
import org.bson.codecs.DocumentCodec;
import org.bson.codecs.EncoderContext;
import org.bson.codecs.configuration.CodecRegistry;
import sics.domain.SimilarityDocumentRow;

/**
 *
 * <AUTHOR>
 */
public class SimilarityDocumentRowCodec implements Codec<SimilarityDocumentRow> {

    private final Codec<SimilarityDocumentRow> defaultCodec;
    private final CodecRegistry codecRegistry;

    public SimilarityDocumentRowCodec(Codec<SimilarityDocumentRow> defaultCodec, CodecRegistry registry) {
        this.defaultCodec = defaultCodec;
        this.codecRegistry = registry;
    }

    @Override
    public void encode(BsonWriter writer, SimilarityDocumentRow value, EncoderContext encoderContext) {
    }

    @Override
    public SimilarityDocumentRow decode(BsonReader reader, DecoderContext decoderContext) {
        // prima parso tutti i campi base
        Document document = new DocumentCodec().decode(reader, decoderContext);
        SimilarityDocumentRow similarityDocumentRow = defaultCodec.decode(new BsonDocumentReader(document.toBsonDocument(SimilarityDocumentRow.class, codecRegistry)), decoderContext);
        for (String fieldName : document.keySet()) {
            if (fieldName.startsWith("type")) {
                if (similarityDocumentRow.getStatsTypeMap() == null) {
                    similarityDocumentRow.setStatsTypeMap(new HashMap<Long, Double>());
                }
                Long statsTypeId = Long.valueOf(StringUtils.replace(fieldName, "type", ""));
                similarityDocumentRow.getStatsTypeMap().put(statsTypeId, document.getDouble(fieldName));
            }
        }

        return similarityDocumentRow;
    }

    // Returns an instance of the v class, since Java cannot infer the class type
    @Override
    public Class<SimilarityDocumentRow> getEncoderClass() {
        return SimilarityDocumentRow.class;
    }
}
