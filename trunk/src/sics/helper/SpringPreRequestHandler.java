package sics.helper;

import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

/**
 *
 * <AUTHOR>
 */
public class SpringPreRequestHandler extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // Questo metodo verrà chiamato prima dell'esecuzione del metodo del controller.
        // Puoi inserire qui la logica globale che desideri eseguire prima di ogni richiesta.

        HttpSession session = request.getSession();
        try {
//            User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);
//            if (curUser != null) {
//                if (BooleanUtils.isNotTrue(curUser.getMulti_login())
//                        && SessionListener.getUserMinSessionCreationTime(curUser.getId()) < session.getCreationTime()
//                        && !request.getRequestURI().contains("multipleSession")
//                        && !request.getRequestURI().contains("isValidSession")) {
//                    response.sendRedirect("/sicsdataanalytics/auth/multipleSession.htm");
//                    return false;
//                }
//            }
            if (!request.getRequestURI().contains("isValidSession") && !request.getRequestURI().contains("isExportRunning") && !request.getRequestURI().contains("isExportRunningLambda")) {
                session.setAttribute(GlobalHelper.kLastRequestDate, new Date());

                Map<String, Integer> sessionRequestList = (Map<String, Integer>) session.getAttribute(GlobalHelper.kRequestsList);
                if (sessionRequestList == null) {
                    sessionRequestList = new LinkedHashMap<>();
                }
                sessionRequestList.putIfAbsent(request.getRequestURI(), 0);
                sessionRequestList.put(request.getRequestURI(), sessionRequestList.get(request.getRequestURI()) + 1);
                session.setAttribute(GlobalHelper.kRequestsList, sessionRequestList);
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        return true;
    }
}
