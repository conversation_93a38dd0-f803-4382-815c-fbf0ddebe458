/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package sics.helper;

import java.util.ArrayList;
import java.util.List;

import sics.domain.Player;
import sics.domain.Point;

/**
 *
 * <AUTHOR>
 */
public class ModuleHelper {

//    public static void setPositionPlayersByModule(List<Player> listPlayers, String module) {
//        for (int i = 0; i < listPlayers.size() && i < 11; i++) {
//            listPlayers.get(i).setmPositionPunto(getPosPlayerByModule(module, i));
//        }
//    }
//
//    public static void setPositionPlayersByModuleReport(List<Player> listPlayers, String module) {
//        for (int i = 0; i < listPlayers.size() && i < 11; i++) {
//            listPlayers.get(i).setmPositionPunto(getPosPlayerByModuleReport(module, i));
//        }
//    }
    public static List<Point> getModulePoints(String module) {
        List<Point> points = new ArrayList<>();
        for (int i = 0; i < 11; i++) {
            points.add(getPosPlayerByModuleReport(module, i));
        }

        return points;
    }

    public static Point getPosPlayerByModule(String module, Integer i) {
        Point point = new Point(-1D, -1D);
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;

                    case 8:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;

                    case 8:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 5:

                        point.setX(4.8D);
                        point.setY(7D);
                        break;
                    case 10:
                        point.setX(5.5D);
                        point.setY(3D);
                        break;
                    case 9:
                        point.setX(5.5D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;

                    case 8:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6.2D);
                        point.setY(2D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(6.2D);
                        point.setY(6D);
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;

                    case 9:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;
                    case 5:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;

                    case 9:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;
                    case 5:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;

                    case 10:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4.8D);
                        point.setY(3D);
                        break;
                    case 8:
                        point.setX(4.8D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "4132":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(4D);
                        point.setY(4D);
                        break;
                    // 3
                    case 9:
                        point.setX(5D);
                        point.setY(2D);
                        break;
                    case 8:
                        point.setX(5.8D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(5D);
                        point.setY(6D);
                        break;
                    // 2
                    case 11:
                        point.setX(7D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(7D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 7:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;

                    case 9:
                        point.setX(5.5D);
                        point.setY(3D);
                        break;
                    case 8:
                        point.setX(5.5D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 7:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;

                    case 10:
                        point.setX(5.5D);
                        point.setY(1.5D);
                        break;
                    case 9:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;
                    case 8:
                        point.setX(5.5D);
                        point.setY(6.5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;
                    case 6:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;

                    case 11:
                        point.setX(6.2D);
                        point.setY(2D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(6.2D);
                        point.setY(6D);
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;

                    case 9:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;

                    case 10:
                        point.setX(5.5D);
                        point.setY(3D);
                        break;
                    case 9:
                        point.setX(5.5D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 3:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;

                    case 11:
                        point.setX(6.5D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.5D);
                        point.setY(5D);
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.7D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.5D);
                        point.setY(2.5D);
                        break;
                    case 4:
                        point.setX(2.2D);
                        point.setY(4D);
                        break;
                    case 3:
                        point.setX(2.5D);
                        point.setY(5.5D);
                        break;
                    case 2:
                        point.setX(2.7D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.8D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4.2D);
                        point.setY(3D);
                        break;
                    case 8:
                        point.setX(4.2D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.8D);
                        point.setY(7D);
                        break;
                    case 11:
                        point.setX(6.5D);
                        point.setY(4D);
                        break;
                }
                break;
            default:
                point = null;
                break;
        }
        return point;
    }

    //restituisce le posizione dei moduli per il campetto dei report che ha i pallini dei giocatori piu grandi e si sovrappongono
    public static Point getPosPlayerByModuleReport(String module, Integer i) {
        Point point = new Point();
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(3D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(2D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(6D);
                        point.setY(6D);
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4D);
                        point.setY(5.5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.2D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4D);
                        point.setY(5.5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(6D);
                        point.setY(3D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;

                    case 10:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(5.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 7:
                        point.setX(3.7D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.7D);
                        point.setY(5D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(2.7D);
                        break;
                    case 8:
                        point.setX(4.3D);
                        point.setY(5.3D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(1.5D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;
                    case 8:
                        point.setX(5.7D);
                        point.setY(6.5D);
                        break;

                    case 11:
                        point.setX(7.5D);
                        point.setY(4D);
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.2D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.2D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(3.8D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(4D);
                        point.setY(5.5D);
                        break;
                    case 6:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(6D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(2D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(6D);
                        point.setY(6D);
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(5.5D);
                        break;

                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5D);
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4.2D);
                        point.setY(2D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(4.2D);
                        point.setY(6D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(2.7D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(5.3D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.1D);
                        point.setY(2.5D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 3:
                        point.setX(2.1D);
                        point.setY(5.5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.2D);
                        point.setY(2D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(6D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(3D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(5D);
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.1D);
                        point.setY(2.5D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(4D);
                        break;
                    case 3:
                        point.setX(2.1D);
                        point.setY(5.5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.2D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.2D);
                        point.setY(7D);
                        break;
                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
        }

        if (!module.equals("4231")) {
            // per tutti gli altri, spazio un pò (+15%)
            if (i != 0) {
                point.setX(point.getX() + (point.getX() * 0.15D));
            }
        }

        return point;
    }

    public static Point getPosPlayerByModuleForField(String module, Integer i) {
        Point point = new Point();
        switch (module) {
            case "3412":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "3421":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(6D);
                        point.setY(2.5D);
                        break;
                    case 9:
                        point.setX(6D);
                        point.setY(5.5D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                }
                break;
            case "343":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 8:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(6D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "3511":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "352":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 4:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 2:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 5:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                }
                break;
            case "4141":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;

                    case 10:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "4222":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 7:
                        point.setX(3.7D);
                        point.setY(3D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(4.3D);
                        point.setY(5.3D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "4231":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(3.7D);
                        point.setY(3D);
                        break;

                    case 10:
                        point.setX(6D);
                        point.setY(2D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;
                    case 8:
                        point.setX(5.7D);
                        point.setY(6.5D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "442":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "4411":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "451":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 6:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "433":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(2D);
                        break;
                    case 10:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(6.5D);
                        break;
                }
                break;
            case "4312":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "4321":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 5:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(1.9D);
                        point.setY(5D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 8:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 7:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 6:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 10:
                        point.setX(6D);
                        point.setY(2.5D);
                        break;
                    case 9:
                        point.setX(5.7D);
                        point.setY(4D);
                        break;

                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
                break;
            case "532":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(2.5D);
                        point.setY(4D);
                        break;
                    case 7:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;

                    case 11:
                        point.setX(6.3D);
                        point.setY(2.5D);
                        break;
                    case 10:
                        point.setX(6.3D);
                        point.setY(5.5D);
                        break;
                }
                break;
            case "541":
                switch (i + 1) {
                    case 1:
                        point.setX(0.5D);
                        point.setY(4D);
                        break;

                    case 6:
                        point.setX(2.3D);
                        point.setY(1D);
                        break;
                    case 5:
                        point.setX(2.2D);
                        point.setY(2D);
                        break;
                    case 4:
                        point.setX(1.9D);
                        point.setY(3D);
                        break;
                    case 3:
                        point.setX(2.2D);
                        point.setY(6D);
                        break;
                    case 2:
                        point.setX(2.3D);
                        point.setY(7D);
                        break;

                    case 10:
                        point.setX(4.3D);
                        point.setY(1D);
                        break;
                    case 9:
                        point.setX(4D);
                        point.setY(2.5D);
                        break;
                    case 8:
                        point.setX(3.9D);
                        point.setY(5D);
                        break;
                    case 7:
                        point.setX(4.3D);
                        point.setY(7D);
                        break;
                    case 11:
                        point.setX(6D);
                        point.setY(4D);
                        break;
                }
        }

//        if (!module.equals("4231")) {
//            // per tutti gli altri, spazio un pò (+15%)
//            if (i != 0) {
//                point.setX((float) (point.x + (point.x * 0.15))D);
//            }
//        }
        return point;
    }

    public static Point getPosPlayerBasket(int i) {
        Point point = new Point();
        switch (i + 1) {
            case 1:
                point.setX(3.2D);
                point.setY(4D);
                break;
            case 2:
                point.setX(2.4D);
                point.setY(1.2D);
                break;
            case 3:
                point.setX(0.9D);
                point.setY(6.7D);
                break;
            case 4:
                point.setX(1.3D);
                point.setY(2.4D);
                break;
            case 5:
                point.setX(1.8D);
                point.setY(5.6D);
                break;

            case 6:
                point.setX(0.23D);
                point.setY(7.7D);
                break;
            case 7:
                point.setX(0.98D);
                point.setY(7.7D);
                break;
            case 8:
                point.setX(1.73D);
                point.setY(7.7D);
                break;
            case 9:
                point.setX(2.48D);
                point.setY(7.7D);
                break;
            case 10:
                point.setX(3.23D);
                point.setY(7.7D);
                break;
            case 11:
                point.setX(3.98D);
                point.setY(7.7D);
                break;
            case 12:
                point.setX(4.73D);
                point.setY(7.7D);
                break;
        }
        return point;
    }

}
