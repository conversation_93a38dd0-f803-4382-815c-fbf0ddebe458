package sics.helper;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import sics.controller.BaseController;
import sics.domain.Event;
import sics.domain.FixtureDetails;
import sics.domain.Point;
import sics.domain.wrapper.EventWrapper;

/**
 *
 * <AUTHOR>
 */
public class EventHelper extends BaseController {

    // Campo
    public static Integer kLatoLungoCampo = 105;
    public static Integer kLatoCortoCampo = 68;

    // Porta
    public static final Double mAltezzaPorta = 2.44D;
    public static final Double mLarghezzaPorta = 7.32D;
    public static final Integer pixelLatiPorta2023 = 100;
    public static final Integer pixelLarghezzaPorta2023 = 1500;
    public static final Integer pixelAltezzaPorta2023 = 600;

    public static void setEventDetails(Event event) {
        if (event.getFixtureId() != null) {
            FixtureDetails details = fixtureDetails.get(event.getFixtureId());
            setEventDetails(event, details);
        }
    }

    public static void setEventDetails(Event event, FixtureDetails details) {
        if (details != null) {
            // Punti Normalizzati
            Map<String, Long> teamOnHalf = new HashMap<>();
            if (details.getTeamSx() != null && details.getHomeTeamId() != null) {
                if (Long.compare(details.getTeamSx(), details.getHomeTeamId()) == 0) {
                    teamOnHalf.put("1", details.getHomeTeamId());
                    teamOnHalf.put("2", details.getAwayTeamId());
                } else {
                    teamOnHalf.put("1", details.getAwayTeamId());
                    teamOnHalf.put("2", details.getHomeTeamId());
                }
            }
            if (details.getTeamSx2() != null && details.getHomeTeamId() != null) {
                if (Long.compare(details.getTeamSx2(), details.getHomeTeamId()) == 0) {
                    teamOnHalf.put("3", details.getHomeTeamId());
                    teamOnHalf.put("4", details.getAwayTeamId());
                } else {
                    teamOnHalf.put("3", details.getAwayTeamId());
                    teamOnHalf.put("4", details.getHomeTeamId());
                }
            }

            List<Point> normalizedPoints = getNormalizedPoints(teamOnHalf, event);
            if (normalizedPoints != null && !normalizedPoints.isEmpty()) {
                if (normalizedPoints.size() >= 2) {
                    event.setStartPointNormalized(normalizedPoints.get(0));
                    event.setEndPointNormalized(normalizedPoints.get(1));
                }
                if (normalizedPoints.size() == 3) {
                    event.setHeightPointNormalized(normalizedPoints.get(2));
                }

                if (event.getStartPointNormalized() != null && BooleanUtils.isFalse(event.getStartPointNormalized().getIsDefault())) {
                    event.setDistanceFromField(event.getStartPointNormalized().getX());
                    if (event.getEndPointNormalized() != null && BooleanUtils.isFalse(event.getEndPointNormalized().getIsDefault())) {
                        event.setEndDistanceFromField(event.getEndPointNormalized().getX());
                    }
                    if (event.getDistanceFromField() != null) {
                        /*
                                1 = metà difensiva
                                2 = metà offensiva
                         */
                        event.setHalf(event.getDistanceFromField() < 52.5 ? 1 : 2);
                        if (event.getEndDistanceFromField() != null) {
                            event.setHalfEnd(event.getEndDistanceFromField() < 52.5 ? 1 : 2);
                        }

                        /*
                                1 = terzo difensivo
                                2 = terzo centrale
                                3 = terzo offensivo
                         */
                        event.setThird((int) Math.round(Math.ceil(event.getDistanceFromField() / 35D)));
                        if (event.getThird() != null) {
                            if (event.getThird() == 0) {
                                event.setThird(1);
                            } else if (event.getThird() == 4) {
                                event.setThird(3);
                            }
                        }
                        if (event.getEndDistanceFromField() != null) {
                            event.setThirdEnd((int) Math.round(Math.ceil(event.getEndDistanceFromField() / 35D)));
                            if (event.getThirdEnd() != null) {
                                if (event.getThirdEnd() == 0) {
                                    event.setThirdEnd(1);
                                } else if (event.getThirdEnd() == 6) {
                                    event.setThirdEnd(5);
                                }
                            }
                        }

                        /*
                                PARTENDO DA SINISTRA
                                1 = canale 1
                                2 = canale 2
                                3 = canale 3
                                4 = canale 4
                                5 = canale 5
                         */
                        event.setVerticalChannel((int) Math.round(Math.ceil(event.getDistanceFromField() / 21D)));
                        if (event.getVerticalChannel() != null) {
                            if (event.getVerticalChannel() == 0) {
                                event.setVerticalChannel(1);
                            } else if (event.getVerticalChannel() == 6) {
                                event.setVerticalChannel(5);
                            }
                        }
                        if (event.getEndDistanceFromField() != null) {
                            event.setVerticalChannelEnd((int) Math.round(Math.ceil(event.getEndDistanceFromField() / 21D)));
                            if (event.getVerticalChannelEnd() != null) {
                                if (event.getVerticalChannelEnd() == 0) {
                                    event.setVerticalChannelEnd(1);
                                } else if (event.getVerticalChannelEnd() == 6) {
                                    event.setVerticalChannelEnd(5);
                                }
                            }
                        }
                    }

                    event.setDistanceFromTop(event.getStartPointNormalized().getY());
                    if (event.getEndPointNormalized() != null && BooleanUtils.isFalse(event.getEndPointNormalized().getIsDefault())) {
                        event.setEndDistanceFromTop(event.getEndPointNormalized().getY());
                    }
                    if (event.getDistanceFromTop() != null) {
                        /*
                                PARTENDO DA SOPRA
                                1 = canale 1
                                2 = canale 2
                                3 = canale 3
                                4 = canale 4
                                5 = canale 5
                         */
                        event.setChannel((int) Math.round(Math.ceil(event.getDistanceFromTop() / 13.6D)));
                        if (event.getChannel() != null) {
                            if (event.getChannel() == 0) {
                                event.setChannel(1);
                            } else if (event.getChannel() == 6) {
                                event.setChannel(5);
                            }
                        }
                        if (event.getEndDistanceFromTop() != null) {
                            event.setChannelEnd((int) Math.round(Math.ceil(event.getEndDistanceFromTop() / 13.6D)));
                            if (event.getChannelEnd() != null) {
                                if (event.getChannelEnd() == 0) {
                                    event.setChannelEnd(1);
                                } else if (event.getChannelEnd() == 6) {
                                    event.setChannelEnd(5);
                                }
                            }
                        }
                    }
                }
            }
        }

        if (event.getStartPointNormalized() != null && BooleanUtils.isFalse(event.getStartPointNormalized().getIsDefault())) {
            if (event.getEndPointNormalized() != null && BooleanUtils.isFalse(event.getEndPointNormalized().getIsDefault())) {
                // Distanza
                event.setDistance(getDistance(event.getStartPointNormalized(), event.getEndPointNormalized()));

                // Angolo
                event.setAngle(getAngle(event.getStartPointNormalized(), event.getEndPointNormalized()));

                // Zona angolo
                if (event.getAngle() != null) {
                    event.setAngleZone(getAngleZone(event.getAngle()));
                }

                // In Area
                event.setInAreaEnd(inArea(event.getEndPointNormalized()));

                // In Area Piccola
                event.setInSmallAreaEnd(inSmallArea(event.getEndPointNormalized()));
            }

            // In Area
            event.setInArea(inArea(event.getStartPointNormalized()));

            // In Area Piccola
            event.setInSmallArea(inSmallArea(event.getStartPointNormalized()));
        }
    }

    private static List<Point> getNormalizedPoints(Map<String, Long> teamOnHalf, Event event) {
        List<Point> points = new ArrayList<>();
        try {
            points.add(event.getStartPoint().clone());
            points.add(event.getEndPoint().clone());
            if (event.getHeightPoint() != null) {
                points.add(event.getHeightPoint().clone());
            }

            Boolean ruota = false;
            // vedi DSFilter normalizzaPosizioneAzioni mMultimatch
            Point pos1 = points.get(0);
            Point pos2 = points.get(1);
            Point pos3 = null;
            if (points.size() == 3) {
                pos3 = points.get(2);
            }
            String half = event.getPeriod().toString();
            if (teamOnHalf.containsKey(half)) {
                // se è multimatch allora devo mettere tutte le azioni come se la squadra attaccasse verso destra
                if (Long.compare(teamOnHalf.get(half), event.getTeamId()) != 0) {
                    ruota = true;
                }
            }
            if (ruota) {
                pos1.setX(kLatoLungoCampo - pos1.getX());
                pos1.setY(kLatoCortoCampo - pos1.getY());
                if (BooleanUtils.isNotTrue(pos2.getIsDefault())) {
                    pos2.setX(kLatoLungoCampo - pos2.getX());
                    pos2.setY(kLatoCortoCampo - pos2.getY());
                }
                if (pos3 != null && BooleanUtils.isNotTrue(pos3.getIsDefault())) {
                    pos3.setX(kLatoLungoCampo - pos3.getX());
                    pos3.setY(kLatoCortoCampo - pos3.getY());
                }
            }
        } catch (CloneNotSupportedException ex) {
            GlobalHelper.reportError(ex);
        }
        return points;
    }

    private static double getDistance(Point A, Point B) {
        if (A != null && BooleanUtils.isNotTrue(A.getIsDefault()) && B != null && BooleanUtils.isNotTrue(B.getIsDefault())) {
            double d1 = A.getX() - B.getX();
            double d2 = A.getY() - B.getY();
            return Math.sqrt(d1 * d1 + d2 * d2);
        } else {
            return 0;
        }
    }

    private static Double getAngle(Point A, Point B) {
        Double angolo = null;
        try {
            double deltaX = B.getX() - A.getX();
            double deltaY = B.getY() - A.getY();
            // Utilizza atan2 per calcolare l'angolo
            angolo = Math.atan2(deltaY, deltaX);
            angolo = Math.toDegrees(angolo);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return angolo;
    }

    private static int getAngleZone(double angle) {
        if (angle <= 30 && angle >= -30) {
            // verticale
            return 3;
        } else if (angle > 30 && angle <= 75) {
            // diagonale avanti dx
            return 4;
        } else if (angle < -30 && angle >= -75) {
            // diagonale avanti sx
            return 2;
        } else if (angle > 75 && angle <= 105) {
            // laterale dx
            return 5;
        } else if (angle < -75 && angle >= -105) {
            // laterale sx
            return 1;
        } else if (angle > 105 || angle < -105) {
            // indietro
            return 6;
        } else {
            return -1;
        }
    }

    private static boolean inArea(Point p) {
        boolean result = false;
        try {
            return (p.getX() >= 0 && p.getX() <= 16.5 && p.getY() >= 13.84 && p.getY() <= 54.16)
                    || (p.getX() >= 88.5 && p.getX() <= kLatoLungoCampo && p.getY() >= 13.84 && p.getY() <= 54.16);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    public static boolean inSmallArea(Point p) {
        boolean result = false;
        try {
            //area sx
            if (p.getX() >= 0 && p.getX() <= 5.5 && p.getY() >= 24.84 && p.getY() <= 43.16) {
                return true;
            } else if (p.getX() >= 99.5 && p.getX() <= 105 && p.getY() >= 24.84 && p.getY() <= 43.16) {
                return true;
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return result;
    }

    public static Map<String, EventWrapper> groupEventsByChannelAndThird(List<Event> events) {
        return groupEventsByChannelAndThird(events, true);
    }

    public static Map<String, EventWrapper> groupEventsByChannelAndThird(List<Event> events, Boolean useStartPoint) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1-1", new EventWrapper());
        map.put("1-2", new EventWrapper());
        map.put("1-3", new EventWrapper());
        map.put("2-1", new EventWrapper());
        map.put("2-2", new EventWrapper());
        map.put("2-3", new EventWrapper());
        map.put("3-1", new EventWrapper());
        map.put("3-2", new EventWrapper());
        map.put("3-3", new EventWrapper());
        map.put("4-1", new EventWrapper());
        map.put("4-2", new EventWrapper());
        map.put("4-3", new EventWrapper());
        map.put("5-1", new EventWrapper());
        map.put("5-2", new EventWrapper());
        map.put("5-3", new EventWrapper());

        if (events != null && !events.isEmpty()) {
            int max = 0, min = Integer.MAX_VALUE;
            for (Event event : events) {
                if (BooleanUtils.isTrue(useStartPoint)) {
                    if (event.getThird() != null && event.getChannel() != null) {
                        String zone = event.getChannel() + "-" + event.getThird();
                        if (StringUtils.isNotBlank(zone)) {
                            if (map.containsKey(zone)) {
                                map.get(zone).getEvents().add(event);
                                map.get(zone).setTotalEvents(events.size());
                            }
                        }
                    }
                } else {
                    if (event.getThirdEnd() != null && event.getChannelEnd() != null) {
                        String zone = event.getChannelEnd() + "-" + event.getThirdEnd();
                        if (StringUtils.isNotBlank(zone)) {
                            if (map.containsKey(zone)) {
                                map.get(zone).getEvents().add(event);
                                map.get(zone).setTotalEvents(events.size());
                            }
                        }
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
                if (wrapper.getEvents().size() < min) {
                    min = wrapper.getEvents().size();
                }
            }
            max -= min;

            for (EventWrapper wrapper : map.values()) {
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / wrapper.getTotalEvents() * 100D));
                wrapper.calculateColor((int) Math.round(1D * (wrapper.getEvents().size() - min) / max * 100D));
            }
        }

        return map;
    }

    public static Map<String, EventWrapper> groupEventsByVerticalChannel(List<Event> events) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1", new EventWrapper());
        map.put("2", new EventWrapper());
        map.put("3", new EventWrapper());
        map.put("4", new EventWrapper());
        map.put("5", new EventWrapper());

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                if (event.getVerticalChannel() != null) {
                    String channel = event.getVerticalChannel().toString();
                    if (StringUtils.isNotBlank(channel)) {
                        if (map.containsKey(channel)) {
                            map.get(channel).getEvents().add(event);
                            map.get(channel).setTotalEvents(events.size());
                        }
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static Map<String, EventWrapper> groupEventsByThird(List<Event> events) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1", new EventWrapper());
        map.put("2", new EventWrapper());
        map.put("3", new EventWrapper());

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                if (event.getThird() != null) {
                    String third = event.getThird().toString();
                    if (StringUtils.isNotBlank(third)) {
                        if (map.containsKey(third)) {
                            map.get(third).getEvents().add(event);
                            map.get(third).setTotalEvents(events.size());
                        }
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static Map<String, EventWrapper> groupEventsByChannel(List<Event> events) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1", new EventWrapper());
        map.put("2", new EventWrapper());
        map.put("3", new EventWrapper());
        map.put("4", new EventWrapper());
        map.put("5", new EventWrapper());

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                if (event.getChannel() != null) {
                    String channel = event.getChannel().toString();
                    if (StringUtils.isNotBlank(channel)) {
                        if (map.containsKey(channel)) {
                            map.get(channel).getEvents().add(event);
                            map.get(channel).setTotalEvents(events.size());
                        }
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static Map<String, EventWrapper> groupEventsByTime(List<Event> events) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1", new EventWrapper());       // 1 - 15
        map.put("2", new EventWrapper());       // 16 - 30
        map.put("3", new EventWrapper());       // 31 - 45
        map.put("4", new EventWrapper());       // 46 - 60
        map.put("5", new EventWrapper());       // 61 - 75
        map.put("6", new EventWrapper());       // 76 - 90
        map.put("7", new EventWrapper());       // 91 - 105
        map.put("8", new EventWrapper());       // 106 - 120
        map.put("9", new EventWrapper());       // 121 - 135

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                String time = getEventTimeInterval(event);
                if (StringUtils.isNotBlank(time)) {
                    if (map.containsKey(time)) {
                        map.get(time).getEvents().add(event);
                        map.get(time).setTotalEvents(events.size());
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static Map<Long, EventWrapper> groupEventsByPlayer(List<Event> events) {
        Map<Long, EventWrapper> map = new LinkedHashMap<>();

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                if (event.getPlayerIds() != null && !event.getPlayerIds().isEmpty()) {
                    for (Long playerId : event.getPlayerIds()) {
                        map.putIfAbsent(playerId, new EventWrapper());
                        map.get(playerId).getEvents().add(event);
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static Map<String, EventWrapper> groupEventsByAngleZone(List<Event> events) {
        Map<String, EventWrapper> map = new LinkedHashMap<>();
        map.put("1", new EventWrapper());
        map.put("2", new EventWrapper());
        map.put("3", new EventWrapper());
        map.put("4", new EventWrapper());
        map.put("5", new EventWrapper());
        map.put("6", new EventWrapper());

        if (events != null && !events.isEmpty()) {
            int max = 0;
            for (Event event : events) {
                if (event.getAngleZone() != null) {
                    String angleZone = event.getAngleZone().toString();
                    if (StringUtils.isNotBlank(angleZone)) {
                        if (map.containsKey(angleZone)) {
                            map.get(angleZone).getEvents().add(event);
                            map.get(angleZone).setTotalEvents(events.size());
                        }
                    }
                }
            }

            for (EventWrapper wrapper : map.values()) {
                if (wrapper.getEvents().size() > max) {
                    max = wrapper.getEvents().size();
                }
            }

            for (EventWrapper wrapper : map.values()) {
                wrapper.setMaxEventsAmount(max);
                wrapper.setPercentage((int) Math.round(1D * wrapper.getEvents().size() / max * 100D));
                // il setPercentage calcola il colore

                // per evitare problemi di visualizzazione aggiungo un 3% ai valori popolati
                if (wrapper.getPercentage() > 0 && wrapper.getPercentage() < 5) {
                    wrapper.setPercentage(wrapper.getPercentage() + 3);
                }
            }
        }

        return map;
    }

    public static String getEventTimeInterval(Event event) {
        String time = "";
        if (event.getStartMinute() != null && event.getPeriod() != null) {
            time = String.valueOf(Math.round(Math.ceil((event.getStartMinute() + ((event.getPeriod() - 1) * 45)) / 15D)));
            // gestione supplementari
            if (event.getPeriod() == 1) {
                if (StringUtils.equals(time, "4")) {
                    time = "3";
                }
            } else if (event.getPeriod() == 2) {
                if (StringUtils.equals(time, "7")) {
                    time = "6";
                }
            }
            // gestione primo minuto di gioco
            if (StringUtils.equals(time, "0")) {
                time = "1";
            }
        }

        return time;
    }
}
