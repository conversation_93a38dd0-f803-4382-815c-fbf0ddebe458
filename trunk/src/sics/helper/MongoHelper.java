package sics.helper;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.mongodb.MongoClient;
import static com.mongodb.MongoClient.getDefaultCodecRegistry;
import com.mongodb.MongoClientOptions;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.MongoIterable;
import com.mongodb.client.model.Accumulators;
import static com.mongodb.client.model.Accumulators.addToSet;
import static com.mongodb.client.model.Accumulators.max;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import com.mongodb.client.model.Aggregates;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import com.mongodb.client.model.BsonField;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.exists;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Filters.lt;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.nin;
import static com.mongodb.client.model.Filters.or;
import static com.mongodb.client.model.Indexes.descending;
import static com.mongodb.client.model.Projections.include;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.time.temporal.ChronoUnit;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import static java.util.Collections.singletonList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.http.HttpSession;
import org.bson.Document;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.codecs.configuration.CodecProvider;
import static org.bson.codecs.configuration.CodecRegistries.fromProviders;
import static org.bson.codecs.configuration.CodecRegistries.fromRegistries;
import org.bson.codecs.configuration.CodecRegistry;
import org.bson.codecs.pojo.ClassModel;
import org.bson.codecs.pojo.PojoCodecProvider;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import sics.controller.BaseController;
import sics.controller.ControllerUtils;
import sics.domain.AdvancedMetric;
import sics.domain.DocumentRow;
import sics.domain.DocumentFilter;
import sics.domain.Season;
import sics.domain.SimilarityDocumentRow;
import sics.domain.Translation;
import sics.domain.User;
import sics.enums.GroupedField;
import static sics.enums.GroupedField.PLAYER_FIXTURE;
import sics.utils.Utils;

/**
 *
 * <AUTHOR>
 */
public class MongoHelper {

    // mongodb
    private static MongoClient mongoClient;
    private static MongoDatabase mongoDatabase;
    private static Session session;
    private static int assignedPort;

    // json
    private static ObjectMapper mapper;

    private static final Logger logger = Logger.getLogger(MongoHelper.class.getName());

    // in questo se accendi mongodb in locale utilizza il tuo server in locale
    // altrimenti si collega al mongodb nel server
    private static void init() {
        try {
            // json
            mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SimpleModule module = new SimpleModule();
            module.addSerializer(Date.class, new DateJsonSerializer());
            module.addDeserializer(Date.class, new DateJsonDeserializer());
            module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
            module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
            mapper.registerModule(module);

            MongoClientOptions options = MongoClientOptions.builder()
                    .serverSelectionTimeout(1000)
                    .connectTimeout(10000)
                    .socketTimeout(60000)
                    .build();
            mongoClient = new MongoClient("localhost", options);
            mongoDatabase = mongoClient.getDatabase("sicsdataaccess");

            // lancio una query per capire se la connessione c'è
            // in questo modo se non c'è acceso mongo in locale, viene fatta la
            // connessione in SSH nel server
            MongoIterable<String> result = mongoDatabase.listCollectionNames();
            result.first();
        } catch (Exception ex) {
//            GlobalHelper.reportError(ex);
            initServer();
        }
    }

    private static void initServer() {
        try {
            // json
            mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            SimpleModule module = new SimpleModule();
            module.addSerializer(Date.class, new DateJsonSerializer());
            module.addDeserializer(Date.class, new DateJsonDeserializer());
            module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
            module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
            mapper.registerModule(module);

            String sshUsername = "admin157278759";  // admin81828046
            String sshPassword = "zWMuP1H'r/fB";      // c613p4b080
            String sshHost = "************";
            int sshPort = 22; // Default SSH port

            String mongoHost = "localhost"; // MongoDB host running on localhost through SSH tunnel
            int mongoPort = 27017; // Default MongoDB port
//            int localPort = 27025; // Local port for tunnel

            JSch jsch = new JSch();
            session = jsch.getSession(sshUsername, sshHost, sshPort);
            session.setPassword(sshPassword);

            // Disable strict host key checking
            java.util.Properties config = new java.util.Properties();
            config.put("StrictHostKeyChecking", "no");
            session.setConfig(config);

            // Connect to SSH server
            session.connect();

            // Create the tunnel
            assignedPort = session.setPortForwardingL(0, mongoHost, mongoPort);
            mongoClient = new MongoClient("localhost", assignedPort);
            mongoDatabase = mongoClient.getDatabase("sicsdataaccess");
        } catch (JSchException ex) {
            GlobalHelper.reportError(ex);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static void closeConnection() {
        // Close the SSH tunnel and session when done
        if (session != null) {
            try {
                System.out.println("Closing Mongo SSH Connection...");
                session.delPortForwardingL(assignedPort);
                session.disconnect();
                System.out.println("Mongo SSH Connection Closed.");
            } catch (JSchException ex) {
                GlobalHelper.reportError(ex);
            }
        }
    }

    public static void deleteAllDocuments(String collection) {
        getMongoDatabase().getCollection(collection).drop();
    }

    public static void deleteDocuments(String baseField, Long id, String collection) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
    }

    public static void deleteAndInsertDocuments(String baseField, Long id, String collection, List<Document> document) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
        getMongoDatabase().getCollection(collection).insertMany(document);
    }

    public static void deleteAndInsertDocument(String baseField, Long id, String collection, Document document) {
        getMongoDatabase().getCollection(collection).deleteMany(eq(baseField, id));
        getMongoDatabase().getCollection(collection).insertOne(document);
    }

    public static void insertDocument(String collection, Document document) {
        getMongoDatabase().getCollection(collection).insertOne(document);
    }

    public static void insertDocuments(String collection, List<Document> documents) {
        getMongoDatabase().getCollection(collection).insertMany(documents);
    }

    public static void removeOldRows(List<Long> playerIds) {
        for (String collection : getMongoDatabase().listCollectionNames()) {
            if (collection.startsWith("player_")) {
                getMongoDatabase().getCollection(collection).deleteMany(and(ne("playerId", null), nin("playerId", playerIds)));
            }
        }
    }

    public static List<Translation> getTranslations(Integer type) {
        ClassModel<Translation> model = ClassModel.builder(Translation.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Translation> collection = getMongoDatabase().getCollection(StringUtils.lowerCase("translations"), Translation.class).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(ne("cancelled", true), eq("type", type)), Translation.class).sort(descending("_id")).into(new ArrayList<Translation>());
    }

    public static Translation getTranslationByKey(String key) {
        ClassModel<Translation> model = ClassModel.builder(Translation.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Translation> collection = getMongoDatabase().getCollection(StringUtils.lowerCase("translations"), Translation.class).withCodecRegistry(pojoCodecRegistry);
        return collection.find(and(ne("cancelled", true), eq("key", key)), Translation.class).first();
    }

    public static void updateDocument(String collectionName, Object objectClass) {
        try {
            MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(StringUtils.defaultIfEmpty(collectionName, objectClass.getClass().getSimpleName())));
            collection.replaceOne(
                    new Document("_id", PropertyUtils.getSimpleProperty(objectClass, "id")),
                    toDocument(objectClass)
            );
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException ex) {
            GlobalHelper.reportError(ex);
        }
    }

    public static Document toDocument(Object object) {
        Document result = null;
        if (object != null) {
            result = Document.parse(serializeToJson(object));
        }
        return result;
    }

    public static String serializeToJson(Object object) {
        initIfNeeded();
        try {
            return mapper.writeValueAsString(object);
        } catch (JsonProcessingException ex) {
            GlobalHelper.reportError(ex);
        }
        return null;
    }

    public static <T> T deserializeFromJson(String json, Class<T> objectClass) {
        try {
            return mapper.readValue(json, objectClass);
        } catch (JsonProcessingException ex) {
            GlobalHelper.reportError(ex);
        } catch (IOException ex) {
            GlobalHelper.reportError(ex);
        }
        return null;
    }

    public static MongoDatabase getMongoDatabase() {
        initIfNeeded();
        return mongoDatabase;
    }

    private static void initIfNeeded() {
        if (mongoDatabase == null || session == null || !session.isConnected()) {
            if (GlobalHelper.isLocalEnvironment()) {
                // init();
                initServer();
            } else {
                initServer();
            }
        }
    }

    /*

        START QUERIES

     */
    public static List<Season> getSeasons() {
        List<Season> seasons = new ArrayList<>();
        for (String collection : getMongoDatabase().listCollectionNames()) {
            if (StringUtils.isNotBlank(collection)) {
                List<String> parts = Arrays.asList(StringUtils.split(collection, "_"));
                if (parts.size() == 3) {
                    Long seasonId = Long.valueOf(parts.get(1));
                    if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
                        Season season = BaseController.getGroupedSeasons().get(seasonId);
                        if (!seasons.contains(season)) {
                            seasons.add(season);
                        }
                    }
                }
            }
        }

        Collections.sort(seasons, new Comparator<Season>() {
            @Override
            public int compare(Season o1, Season o2) {
                return o2.getId().compareTo(o1.getId());
            }
        });

        return seasons;
    }

    public static List<Long> getCompetitionIdsByParams(Map<String, Object> params) {
        Long seasonId = null;
        if (params.containsKey("seasonId")) {
            seasonId = Long.valueOf(params.get("seasonId").toString());
        }

        // nuova gestione stagioni
        List<Long> validSeasonIds = new ArrayList<>();
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (groupedSeason.getNonSolarId() != null) {
                validSeasonIds.add(groupedSeason.getNonSolarId());
            }
            if (groupedSeason.getSolarId() != null) {
                validSeasonIds.add(groupedSeason.getSolarId());
            }
        }

        List<Long> competitionIds = new ArrayList<>();
        for (String collection : getMongoDatabase().listCollectionNames()) {
            if (StringUtils.isNotBlank(collection)) {
                List<String> parts = Arrays.asList(StringUtils.split(collection, "_"));
                if (parts.size() == 3) {
                    boolean valid = false;
                    if (seasonId != null) {
                        if (validSeasonIds.contains(Long.valueOf(parts.get(1)))) {
                            valid = true;
                        }
                    } else {
                        valid = true;
                    }
                    if (valid) {
                        Long competitionId = Long.valueOf(parts.get(2));
                        if (!competitionIds.contains(competitionId)) {
                            competitionIds.add(competitionId);
                        }
                    }
                }
            }
        }

        return competitionIds;
    }

    public static List<DocumentRow> getDocumentsByParams(Map<String, Object> params, GroupedField field) {
        List<Bson> filters = new ArrayList<>();
        int skip = 0, limit = 0;
        Bson sort = null;

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return new ArrayList<>();
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        String originalEventTypeIds = null;
        if (params.containsKey("eventTypeIds")) {
            originalEventTypeIds = params.get("eventTypeIds").toString();
        }
        List<DocumentRow> rows = new ArrayList<>();
        // gestione eventi con filtro posizionale
        if (!params.containsKey("skipEventCheck")) {
            if (params.containsKey("eventTypeIds")) {
                List<String> eventTypeIds = Arrays.asList(StringUtils.split(params.get("eventTypeIds").toString(), "|"));
                Map<String, List<String>> groupedByZoneId = new HashMap<>();
                for (String eventTypeId : eventTypeIds) {
                    String zoneId = null;
                    if (StringUtils.contains(eventTypeId, "@")) {
                        zoneId = StringUtils.substringAfterLast(eventTypeId, "@");
                        eventTypeId = StringUtils.replace(eventTypeId, "@" + zoneId, "");
                    }
                    groupedByZoneId.putIfAbsent(zoneId, new ArrayList<String>());
                    groupedByZoneId.get(zoneId).add(eventTypeId);
                }

                for (String zoneId : groupedByZoneId.keySet()) {
                    if (zoneId != null) {
                        params.put("skipEventCheck", true);
                        adjustParamsByZoneId(params, zoneId);
                        params.put("eventTypeIds", StringUtils.join(groupedByZoneId.get(zoneId), "|"));
                        List<DocumentRow> tmpRows = getDocumentsByParams(params, field);
                        for (DocumentRow row : tmpRows) {
                            row.setZoneId(zoneId);
                        }
                        rows.addAll(tmpRows);
                    }
                }

                params.remove("skipEventCheck");
                // ora se bisogna prendo tutto quello senza filtro posizionale
                String zoneId = null;
                if (groupedByZoneId.containsKey(zoneId)) {
                    adjustParamsByZoneId(params, zoneId);
                    params.put("eventTypeIds", StringUtils.join(groupedByZoneId.get(zoneId), "|"));
                }
            }
        }

        getFilters(filters, params);

        ClassModel<DocumentRow> model = ClassModel.builder(DocumentRow.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(new DocumentRowCodecProvider(pojoCodecProvider)));

        List<String> eventTypeIdList = new ArrayList<>();
        if (params.containsKey("eventTypeId") && params.get("eventTypeId") != null) {
            Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
            if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                eventTypeIdList.add(params.get("eventTypeId").toString());
            }
        } else if (params.containsKey("eventTypeIds")) {
            List<String> tmpEventTypeIdList = Arrays.asList(StringUtils.split(params.get("eventTypeIds").toString(), "|"));
            if (tmpEventTypeIdList != null && !tmpEventTypeIdList.isEmpty()) {
                for (String eventTypeIdString : tmpEventTypeIdList) {
                    List<String> splitted = Arrays.asList(StringUtils.split(eventTypeIdString, "$"));
                    Long eventTypeId = Long.valueOf(splitted.get(0));
                    if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                        eventTypeIdList.add(splitted.get(0));
                    }
                }
            }
        }

        List<String> fieldsToInclude = new ArrayList<>();
        fieldsToInclude.add("competitionId");
        fieldsToInclude.add("fixtureId");
        fieldsToInclude.add("groupId");
        fieldsToInclude.add("matchDay");
        fieldsToInclude.add("eventTypeId");
        if (params.containsKey("tagTypeId") || params.containsKey("tagTypeIds")) {
            fieldsToInclude.add("tagTypeId");
        } else if (params.containsKey("eventTypeIds")) {
            // per profilo e probabilmente anche le altre pagine con multiplo evento devo controllare
            if (StringUtils.contains(params.get("eventTypeIds").toString(), "$")) {
                // il "$" viene usato per indicare che si tratta di un tag
                fieldsToInclude.add("tagTypeId");
            }
        }
        fieldsToInclude.add("isHomeTeam");

        fieldsToInclude.add("teamId");
        fieldsToInclude.add("playerId");
        if (StringUtils.equals(params.get("type").toString(), "player")) {
            fieldsToInclude.add("countryId");
            fieldsToInclude.add("bornYear");
            fieldsToInclude.add("footId");
            fieldsToInclude.add("positionId");
            fieldsToInclude.add("positionDetailId");
        }
        if (eventTypeIdList.contains("999")) {
            fieldsToInclude.add("homePoints");
        }
        for (Long advancedMetricId : BaseController.getAdvancedMetrics().keySet()) {
            if (eventTypeIdList.contains(advancedMetricId.toString())) {
                fieldsToInclude.add(BaseController.getAdvancedMetrics().get(advancedMetricId).getCode());
            }
        }

        List<String> fieldsToSum = ControllerUtils.getFieldsToSum(params);
        fieldsToInclude.addAll(fieldsToSum);
        if (!fieldsToInclude.contains("total")) {
            fieldsToInclude.add("total");
        }
        List<Document> fieldsToSumForQuery = new ArrayList<>();
        for (String fieldToSum : fieldsToSum) {
            fieldsToSumForQuery.add(new Document("$ifNull", Arrays.asList("$" + fieldToSum, 0)));
        }

        MongoCollection<DocumentRow> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), DocumentRow.class).withCodecRegistry(pojoCodecRegistry);
        if (filters.isEmpty()) {
            rows.addAll(collection
                    .find()
                    .projection(include(fieldsToInclude))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<DocumentRow>()));
        } else {
            if (params.containsKey("eventTypeId") || params.containsKey("eventTypeIds")) {
                filters.add(ne("fixtureId", null));
            }

            Document groupBy = new Document();
            switch (field) {
                case TEAM:
                    groupBy.append("teamId", "$teamId");
                    break;
                case PLAYER:
                    groupBy.append("playerId", "$playerId");
                    break;
                case EVENT_TYPE_ID:
                    groupBy.append("eventTypeId", "$eventTypeId");
                    break;
                case FIXTURE:
                    groupBy.append("fixtureId", "$fixtureId");
                    break;
                case TEAM_PLAYER:
                    groupBy.append("teamId", "$teamId").append("playerId", "$playerId");
                    break;
                case TEAM_FIXTURE:
                    groupBy.append("teamId", "$teamId").append("fixtureId", "$fixtureId");
                    break;
                case TEAM_EVENT_TYPE:
                    groupBy.append("teamId", "$teamId").append("eventTypeId", "$eventTypeId");
                    break;
                case TEAM_EVENT_TYPE_TAG_TYPE:
                    groupBy.append("teamId", "$teamId").append("eventTypeId", "$eventTypeId").append("tagTypeId", "$tagTypeId");
                    break;
                case PLAYER_FIXTURE:
                    groupBy.append("playerId", "$playerId").append("fixtureId", "$fixtureId");
                    break;
                case PLAYER_EVENT_TYPE:
                    groupBy.append("playerId", "$playerId").append("eventTypeId", "$eventTypeId");
                    break;
                case TEAM_PLAYER_EVENT_TYPE:
                    groupBy.append("teamId", "$teamId").append("playerId", "$playerId").append("eventTypeId", "$eventTypeId");
                    break;
                case TEAM_PLAYER_EVENT_TYPE_TAG_TYPE:
                    groupBy.append("teamId", "$teamId").append("playerId", "$playerId").append("eventTypeId", "$eventTypeId").append("tagTypeId", "$tagTypeId");
                    break;
            }

            List<BsonField> accumulators = new ArrayList<>();
            Document replacedFields = new Document();

            accumulators.add(Accumulators.sum("totalSum", new Document("$add", fieldsToSumForQuery)));
            accumulators.add(Accumulators.first("existingFields", "$$ROOT"));

            replacedFields.append("totalSum", "$totalSum");
            // per le metriche avanzate devo sommare il campo della metrica
            if (!eventTypeIdList.isEmpty()) {
                for (String eventTypeIdString : eventTypeIdList) {
                    Long eventTypeId = Long.valueOf(eventTypeIdString);
                    AdvancedMetric metric = BaseController.getAdvancedMetrics().get(eventTypeId);
                    if (metric != null) {
                        accumulators.add(Accumulators.sum(metric.getCode(), "$" + metric.getCode()));
                        replacedFields.append(metric.getCode(), new Document("$toDouble", "$" + metric.getCode()));
                    }
                }
            }

//            Date start = new Date();
            rows.addAll(collection.aggregate(
                    Arrays.asList(
                            Aggregates.match(and(filters)),
                            Aggregates.project(include(fieldsToInclude)),
                            Aggregates.group(
                                    groupBy,
                                    accumulators
                            ),
                            Aggregates.replaceRoot(
                                    new Document("$mergeObjects", Arrays.asList("$existingFields", replacedFields))
                            )
                    )
            ).into(new ArrayList<DocumentRow>()));
//            System.out.println("Query: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
        }

        if (!eventTypeIdList.isEmpty()) {
            List<DocumentRow> additionalRows = new ArrayList<>();
            List<DocumentRow> baseRowToDelete = new ArrayList<>();
            int counter = 0;
            for (String eventTypeIdString : eventTypeIdList) {
                Long eventTypeId = Long.valueOf(eventTypeIdString);
                for (DocumentRow row : rows) {
                    if (row.getEventTypeId() == null) {
                        AdvancedMetric metric = BaseController.getAdvancedMetrics().get(eventTypeId);
                        String fieldName = metric.getCode();
                        if (StringUtils.isNotBlank(fieldName)) {
                            try {
                                DocumentRow additionalRow = row.clone();
                                additionalRow.setFieldName(fieldName);
                                additionalRow.setEventTypeId(metric.getId());
                                switch (fieldName) {
                                    case "homePoints":
                                        additionalRow.setTotal(additionalRow.getHomePoints() != null ? additionalRow.getHomePoints().doubleValue() : 0);
                                        break;
                                    case "awayPoints":
                                        additionalRow.setTotal(additionalRow.getAwayPoints() != null ? additionalRow.getAwayPoints().doubleValue() : 0);
                                        break;
                                    case "ipo":
                                        additionalRow.setTotal(additionalRow.getIpo() != null ? additionalRow.getIpo() : 0);
                                        break;
                                    case "ird":
                                        additionalRow.setTotal(additionalRow.getIrd() != null ? additionalRow.getIrd() : 0);
                                        break;
                                    case "possesso":
                                        additionalRow.setTotal(additionalRow.getPossesso() != null ? additionalRow.getPossesso() : 0);
                                        break;
                                    case "xG":
                                        additionalRow.setTotal(additionalRow.getxG() != null ? additionalRow.getxG() : 0);
                                        break;
                                    case "xGot":
                                        additionalRow.setTotal(additionalRow.getxGot() != null ? additionalRow.getxGot() : 0);
                                        break;
                                    case "xA":
                                        additionalRow.setTotal(additionalRow.getxA() != null ? additionalRow.getxA() : 0);
                                        break;
                                    case "xAe":
                                        additionalRow.setTotal(additionalRow.getxAe() != null ? additionalRow.getxAe() : 0);
                                        break;
                                    case "xOvA":
                                        additionalRow.setTotal(additionalRow.getxOvA() != null ? additionalRow.getxOvA() : 0);
                                        break;
                                    case "cxg":
                                        additionalRow.setTotal(additionalRow.getCxg() != null ? additionalRow.getCxg() : 0);
                                        break;
                                    case "shotQuality":
                                        additionalRow.setTotal(additionalRow.getShotQuality() != null ? additionalRow.getShotQuality() : 0);
                                        break;
                                    case "fieldTilt":
                                        additionalRow.setTotal(additionalRow.getFieldTilt() != null ? additionalRow.getFieldTilt() : 0);
                                        break;
                                    case "ppda":
                                        additionalRow.setTotal(additionalRow.getPpda() != null ? additionalRow.getPpda() : 0);
                                        break;
                                    case "axGot":
                                        additionalRow.setTotal(additionalRow.getAxGot() != null ? additionalRow.getAxGot() : 0);
                                        break;
                                    case "fxGot":
                                        additionalRow.setTotal(additionalRow.getFxGot() != null ? additionalRow.getFxGot() : 0);
                                        break;
                                    case "gkGp":
                                        additionalRow.setTotal(additionalRow.getGkGp() != null ? additionalRow.getGkGp() : 0);
                                        break;
                                    case "npxG":
                                        additionalRow.setTotal(additionalRow.getNpxG() != null ? additionalRow.getNpxG() : 0);
                                        break;
                                    case "opxG":
                                        additionalRow.setTotal(additionalRow.getOpxG() != null ? additionalRow.getOpxG() : 0);
                                        break;
                                    case "oidi":
                                        additionalRow.setTotal(additionalRow.getOidi() != null ? additionalRow.getOidi() : 0);
                                        break;
                                    case "matchMinutes":
                                        additionalRow.setTotal((double) (additionalRow.getMatchMinutes() != null ? additionalRow.getMatchMinutes() : 0));
                                        break;
                                    case "touches":
                                        additionalRow.setTotal((double) (additionalRow.getTouches() != null ? additionalRow.getTouches() : 0));
                                        break;
                                    case "xT":
                                        additionalRow.setTotal(additionalRow.getxT() != null ? additionalRow.getxT() : 0);
                                        break;
                                    case "xTPass":
                                        additionalRow.setTotal(additionalRow.getxTPass() != null ? additionalRow.getxTPass() : 0);
                                        break;
                                    case "xTCond":
                                        additionalRow.setTotal(additionalRow.getxTCond() != null ? additionalRow.getxTCond() : 0);
                                        break;
                                    case "iA":
                                        additionalRow.setTotal(additionalRow.getiA() != null ? additionalRow.getiA() : 0);
                                        break;
                                    case "touchesMcd":
                                        additionalRow.setTotal(additionalRow.getTouchesMcd() != null ? additionalRow.getTouchesMcd() : 0);
                                        break;
                                    case "touchesMco":
                                        additionalRow.setTotal(additionalRow.getTouchesMco() != null ? additionalRow.getTouchesMco() : 0);
                                        break;
                                    case "touchesTo":
                                        additionalRow.setTotal(additionalRow.getTouchesTo() != null ? additionalRow.getTouchesTo() : 0);
                                        break;
                                    case "touchesAa":
                                        additionalRow.setTotal(additionalRow.getTouchesAa() != null ? additionalRow.getTouchesAa() : 0);
                                        break;
                                    case "touchesAz":
                                        additionalRow.setTotal(additionalRow.getTouchesAz() != null ? additionalRow.getTouchesAz() : 0);
                                        break;
                                    case "touchesPfa":
                                        additionalRow.setTotal(additionalRow.getTouchesPfa() != null ? additionalRow.getTouchesPfa() : 0);
                                        break;
                                    case "touchesSuff":
                                        additionalRow.setTotal(additionalRow.getTouchesSuff() != null ? additionalRow.getTouchesSuff() : 0);
                                        break;
                                    case "touchesSuffMca":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffMca() != null ? additionalRow.getTouchesSuffMca() : 0);
                                        break;
                                    case "touchesSuffMc":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffMc() != null ? additionalRow.getTouchesSuffMc() : 0);
                                        break;
                                    case "touchesSuffTd":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffTd() != null ? additionalRow.getTouchesSuffTd() : 0);
                                        break;
                                    case "touchesSuffA":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffA() != null ? additionalRow.getTouchesSuffA() : 0);
                                        break;
                                    case "touchesSuffAz":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffAz() != null ? additionalRow.getTouchesSuffAz() : 0);
                                        break;
                                }
                                additionalRow.setTotal(Math.round(additionalRow.getTotal() * 100) / 100D);

                                additionalRows.add(additionalRow);
                                baseRowToDelete.add(row);
                            } catch (CloneNotSupportedException ex) {
                                GlobalHelper.reportError(ex);
                            }
                        }
                    }
                }
                counter++;
            }

            rows.addAll(additionalRows);
            rows.removeAll(baseRowToDelete);
        }

        if (StringUtils.isNotBlank(originalEventTypeIds)) {
            params.put("eventTypeIds", originalEventTypeIds);
        }
        return rows;
    }

    public static List<DocumentRow> getCompetitionTotalsByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        int skip = 0, limit = 0;
        Bson sort = null;

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return new ArrayList<>();
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<DocumentRow> model = ClassModel.builder(DocumentRow.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(new DocumentRowCodecProvider(pojoCodecProvider)));

        List<String> eventTypeIdList = new ArrayList<>();
        if (params.containsKey("eventTypeId")) {
            Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
            if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                eventTypeIdList.add(params.get("eventTypeId").toString());
            }
        } else if (params.containsKey("eventTypeIds")) {
            List<String> tmpEventTypeIdList = Arrays.asList(StringUtils.split(params.get("eventTypeIds").toString(), "|"));
            if (tmpEventTypeIdList != null && !tmpEventTypeIdList.isEmpty()) {
                for (String eventTypeIdString : tmpEventTypeIdList) {
                    Long eventTypeId = Long.valueOf(eventTypeIdString);
                    if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                        eventTypeIdList.add(eventTypeIdString);
                    }
                }
            }
        }

        List<String> fieldsToInclude = new ArrayList<>();
        fieldsToInclude.add("competitionId");
        fieldsToInclude.add("fixtureId");
        fieldsToInclude.add("groupId");
        fieldsToInclude.add("matchDay");
        fieldsToInclude.add("eventTypeId");

        fieldsToInclude.add("teamId");
        fieldsToInclude.add("playerId");
        if (StringUtils.equals(params.get("type").toString(), "player")) {
            fieldsToInclude.add("countryId");
            fieldsToInclude.add("bornYear");
            fieldsToInclude.add("footId");
            fieldsToInclude.add("positionId");
            fieldsToInclude.add("positionDetailId");
        }
        if (eventTypeIdList.contains("999")) {
            fieldsToInclude.add("homePoints");
        }
        for (Long advancedMetricId : BaseController.getAdvancedMetrics().keySet()) {
            if (eventTypeIdList.contains(advancedMetricId.toString())) {
                fieldsToInclude.add(BaseController.getAdvancedMetrics().get(advancedMetricId).getCode());
            }
        }

        List<String> fieldsToSum = ControllerUtils.getFieldsToSum(params);
        fieldsToInclude.addAll(fieldsToSum);
        if (!fieldsToInclude.contains("total")) {
            fieldsToInclude.add("total");
        }

        MongoCollection<DocumentRow> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), DocumentRow.class).withCodecRegistry(pojoCodecRegistry);
        List<DocumentRow> rows;
        if (filters.isEmpty()) {
            rows = collection
                    .find()
                    .projection(include(fieldsToInclude))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<DocumentRow>());
        } else {
            List<String> fieldsToSumForQuery = new ArrayList<>();
            for (String field : fieldsToSum) {
                fieldsToSumForQuery.add("$" + field);
            }

            Date start = new Date();
            rows = collection.aggregate(
                    Arrays.asList(
                            Aggregates.match(and(filters)),
                            Aggregates.project(include(fieldsToInclude)),
                            Aggregates.group(
                                    new Document().append("teamId", "$teamId").append("eventTypeId", "$eventTypeId"),
                                    Accumulators.sum("totalSum", new Document("$add", fieldsToSumForQuery)),
                                    Accumulators.first("existingFields", "$$ROOT") // Capture original document fields
                            ),
                            Aggregates.replaceRoot(
                                    new Document("$mergeObjects", Arrays.asList("$existingFields", new Document("totalSum", "$totalSum")))
                            )
                    )
            ).into(new ArrayList<DocumentRow>());

//            for (DocumentRow row : rows) {
//                System.out.println(row.getEventTypeId() + " - " + row.getTeamId() + " - " + row.getPlayerId() + ": " + row.getTotalSum());
//            }
            System.out.println("Query: " + (ChronoUnit.MILLIS.between(start.toInstant(), new Date().toInstant())) + " msec");
        }

        if (!eventTypeIdList.isEmpty()) {
            List<DocumentRow> additionalRows = new ArrayList<>();
            List<DocumentRow> baseRowToDelete = new ArrayList<>();
            int counter = 0;
            for (String eventTypeIdString : eventTypeIdList) {
                Long eventTypeId = Long.valueOf(eventTypeIdString);
                for (DocumentRow row : rows) {
                    if (row.getEventTypeId() == null) {
                        AdvancedMetric metric = BaseController.getAdvancedMetrics().get(eventTypeId);
                        String fieldName = metric.getCode();
                        if (StringUtils.isNotBlank(fieldName)) {
                            try {
                                DocumentRow additionalRow = row.clone();
                                additionalRow.setFieldName(fieldName);
                                additionalRow.setEventTypeId(metric.getId());
                                switch (fieldName) {
                                    case "homePoints":
                                        additionalRow.setTotal(additionalRow.getHomePoints() != null ? additionalRow.getHomePoints().doubleValue() : 0);
                                        break;
                                    case "awayPoints":
                                        additionalRow.setTotal(additionalRow.getAwayPoints() != null ? additionalRow.getAwayPoints().doubleValue() : 0);
                                        break;
                                    case "ipo":
                                        additionalRow.setTotal(additionalRow.getIpo() != null ? additionalRow.getIpo() : 0);
                                        break;
                                    case "ird":
                                        additionalRow.setTotal(additionalRow.getIrd() != null ? additionalRow.getIrd() : 0);
                                        break;
                                    case "possesso":
                                        additionalRow.setTotal(additionalRow.getPossesso() != null ? additionalRow.getPossesso() : 0);
                                        break;
                                    case "xG":
                                        additionalRow.setTotal(additionalRow.getxG() != null ? additionalRow.getxG() : 0);
                                        break;
                                    case "xGot":
                                        additionalRow.setTotal(additionalRow.getxGot() != null ? additionalRow.getxGot() : 0);
                                        break;
                                    case "xA":
                                        additionalRow.setTotal(additionalRow.getxA() != null ? additionalRow.getxA() : 0);
                                        break;
                                    case "xAe":
                                        additionalRow.setTotal(additionalRow.getxAe() != null ? additionalRow.getxAe() : 0);
                                        break;
                                    case "xOvA":
                                        additionalRow.setTotal(additionalRow.getxOvA() != null ? additionalRow.getxOvA() : 0);
                                        break;
                                    case "cxg":
                                        additionalRow.setTotal(additionalRow.getCxg() != null ? additionalRow.getCxg() : 0);
                                        break;
                                    case "shotQuality":
                                        additionalRow.setTotal(additionalRow.getShotQuality() != null ? additionalRow.getShotQuality() : 0);
                                        break;
                                    case "fieldTilt":
                                        additionalRow.setTotal(additionalRow.getFieldTilt() != null ? additionalRow.getFieldTilt() : 0);
                                        break;
                                    case "ppda":
                                        additionalRow.setTotal(additionalRow.getPpda() != null ? additionalRow.getPpda() : 0);
                                        break;
                                    case "axGot":
                                        additionalRow.setTotal(additionalRow.getAxGot() != null ? additionalRow.getAxGot() : 0);
                                        break;
                                    case "fxGot":
                                        additionalRow.setTotal(additionalRow.getFxGot() != null ? additionalRow.getFxGot() : 0);
                                        break;
                                    case "gkGp":
                                        additionalRow.setTotal(additionalRow.getGkGp() != null ? additionalRow.getGkGp() : 0);
                                        break;
                                    case "npxG":
                                        additionalRow.setTotal(additionalRow.getNpxG() != null ? additionalRow.getNpxG() : 0);
                                        break;
                                    case "opxG":
                                        additionalRow.setTotal(additionalRow.getOpxG() != null ? additionalRow.getOpxG() : 0);
                                        break;
                                    case "oidi":
                                        additionalRow.setTotal(additionalRow.getOidi() != null ? additionalRow.getOidi() : 0);
                                        break;
                                    case "matchMinutes":
                                        additionalRow.setTotal((double) (additionalRow.getMatchMinutes() != null ? additionalRow.getMatchMinutes() : 0));
                                        break;
                                    case "touches":
                                        additionalRow.setTotal((double) (additionalRow.getTouches() != null ? additionalRow.getTouches() : 0));
                                        break;
                                    case "xT":
                                        additionalRow.setTotal(additionalRow.getxT() != null ? additionalRow.getxT() : 0);
                                        break;
                                    case "xTPass":
                                        additionalRow.setTotal(additionalRow.getxTPass() != null ? additionalRow.getxTPass() : 0);
                                        break;
                                    case "xTCond":
                                        additionalRow.setTotal(additionalRow.getxTCond() != null ? additionalRow.getxTCond() : 0);
                                        break;
                                    case "iA":
                                        additionalRow.setTotal(additionalRow.getiA() != null ? additionalRow.getiA() : 0);
                                        break;
                                    case "touchesMcd":
                                        additionalRow.setTotal(additionalRow.getTouchesMcd() != null ? additionalRow.getTouchesMcd() : 0);
                                        break;
                                    case "touchesMco":
                                        additionalRow.setTotal(additionalRow.getTouchesMco() != null ? additionalRow.getTouchesMco() : 0);
                                        break;
                                    case "touchesTo":
                                        additionalRow.setTotal(additionalRow.getTouchesTo() != null ? additionalRow.getTouchesTo() : 0);
                                        break;
                                    case "touchesAa":
                                        additionalRow.setTotal(additionalRow.getTouchesAa() != null ? additionalRow.getTouchesAa() : 0);
                                        break;
                                    case "touchesAz":
                                        additionalRow.setTotal(additionalRow.getTouchesAz() != null ? additionalRow.getTouchesAz() : 0);
                                        break;
                                    case "touchesPfa":
                                        additionalRow.setTotal(additionalRow.getTouchesPfa() != null ? additionalRow.getTouchesPfa() : 0);
                                        break;
                                    case "touchesSuff":
                                        additionalRow.setTotal(additionalRow.getTouchesSuff() != null ? additionalRow.getTouchesSuff() : 0);
                                        break;
                                    case "touchesSuffMca":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffMca() != null ? additionalRow.getTouchesSuffMca() : 0);
                                        break;
                                    case "touchesSuffMc":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffMc() != null ? additionalRow.getTouchesSuffMc() : 0);
                                        break;
                                    case "touchesSuffTd":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffTd() != null ? additionalRow.getTouchesSuffTd() : 0);
                                        break;
                                    case "touchesSuffA":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffA() != null ? additionalRow.getTouchesSuffA() : 0);
                                        break;
                                    case "touchesSuffAz":
                                        additionalRow.setTotal(additionalRow.getTouchesSuffAz() != null ? additionalRow.getTouchesSuffAz() : 0);
                                        break;
                                }
                                additionalRow.setTotal(Math.round(additionalRow.getTotal() * 100) / 100D);

                                additionalRows.add(additionalRow);
                                baseRowToDelete.add(row);
                            } catch (CloneNotSupportedException ex) {
                                GlobalHelper.reportError(ex);
                            }
                        }
                    }
                }
                counter++;
            }

            rows.addAll(additionalRows);
            rows.removeAll(baseRowToDelete);
        }

        return rows;
    }

    public static List<Object> getDistinctValues(Boolean isTeam, Long seasonId, Long competitionId, String field) {
        return getDistinctValues(isTeam, seasonId, competitionId, field, null);
    }

    public static List<Object> getDistinctValues(Boolean isTeam, Long seasonId, Long competitionId, String field, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        boolean isLong = true;
        if (StringUtils.equals(field, "homeModule") || StringUtils.equals(field, "awayModule") || StringUtils.equals(field, "tagTypeId")) {
            isLong = false;
        }

        if (StringUtils.equals(field, "tagTypeId")) {
            filters.add(eq("eventTypeId", Long.valueOf(params.get("eventTypeId").toString())));
        }

        ClassModel<DocumentRow> model = ClassModel.builder(DocumentRow.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(new DocumentRowCodecProvider(pojoCodecProvider)));

        MongoCollection<DocumentRow> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), DocumentRow.class).withCodecRegistry(pojoCodecRegistry);
        if (isLong) {
            if (filters.isEmpty()) {
                return collection.distinct(field, Long.class).into(new ArrayList<>());
            } else {
                return collection.distinct(field, and(filters), Long.class).into(new ArrayList<>());
            }
        } else {
            if (filters.isEmpty()) {
                return collection.distinct(field, String.class).into(new ArrayList<>());
            } else {
                return collection.distinct(field, and(filters), String.class).into(new ArrayList<>());
            }
        }
    }

    public static Map<String, List<Object>> getDistinctValues(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new HashMap<>();
        }

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

//        if (params.containsKey("eventTypeId")) {
//            filters.add(eq("eventTypeId", Long.valueOf(params.get("eventTypeId").toString())));
//        }
        getFilters(filters, params);
        filters.remove(eq("tagTypeId", null));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        List<BsonField> fields = new ArrayList<>();
        fields.add(addToSet("fixtureId", "$fixtureId"));
        fields.add(addToSet("matchDay", "$matchDay"));
        fields.add(addToSet("homeModule", "$homeModule"));
        fields.add(addToSet("awayModule", "$awayModule"));
        fields.add(addToSet("eventTypeId", "$eventTypeId"));
        if (params.containsKey("eventTypeId")) {
            fields.add(addToSet("tagTypeId", "$tagTypeId"));
        }
        if (BooleanUtils.isFalse(isTeam) && params.containsKey("teamId")) {
            fields.add(addToSet("playerId", new Document("playerId", "$playerId").append("positionId", new Document("$ifNull", Arrays.asList("$positionId", -1L)))));
            fields.add(addToSet("countryId", "$countryId"));
            fields.add(addToSet("bornYear", "$bornYear"));
            fields.add(addToSet("footId", "$footId"));
            fields.add(addToSet("positionId", "$positionId"));
            fields.add(addToSet("positionDetailId", "$positionDetailId"));
        }
        AggregateIterable<Document> result;
        if (filters.isEmpty()) {
            result = collection
                    .aggregate(singletonList(
                            group(null,
                                    fields
                            )
                    ));
        } else {
            result = collection
                    .aggregate(Arrays.asList(
                            match(and(filters)),
                            group(null,
                                    fields
                            )
                    ));
        }

        if (result != null) {
            Map<String, List<Object>> distinctValuesMap = new HashMap<>();
            for (Document doc : result) {
                // fixtureId
                if (doc.containsKey("fixtureId")) {
                    List<Object> fixtureIds = doc.getList("fixtureId", Object.class);
                    distinctValuesMap.put("fixtureId", fixtureIds);
                }
                // matchDay
                if (doc.containsKey("matchDay")) {
                    List<Object> matchDays = doc.getList("matchDay", Object.class);
                    distinctValuesMap.put("matchDay", matchDays);
                }
                // homeModule
                if (doc.containsKey("homeModule")) {
                    List<Object> homeModules = doc.getList("homeModule", Object.class);
                    distinctValuesMap.put("homeModule", homeModules);
                }
                // awayModule
                if (doc.containsKey("awayModule")) {
                    List<Object> awayModules = doc.getList("awayModule", Object.class);
                    distinctValuesMap.put("awayModule", awayModules);
                }
                // eventTypeId
                if (doc.containsKey("eventTypeId")) {
                    List<Object> eventTypeIds = doc.getList("eventTypeId", Object.class);
                    distinctValuesMap.put("eventTypeId", eventTypeIds);
                }
                // tagTypeId
                if (doc.containsKey("tagTypeId")) {
                    List<Object> tagTypeIds = doc.getList("tagTypeId", Object.class);
                    distinctValuesMap.put("tagTypeId", tagTypeIds);
                }
                if (BooleanUtils.isFalse(isTeam)) {
                    // playerId
                    if (doc.containsKey("playerId")) {
                        List<Object> playerIds = new ArrayList<>();
                        if (doc.get("playerId") instanceof ArrayList) {
                            List<Document> playerPositions = doc.get("playerId", ArrayList.class);
                            for (Document playerPosition : playerPositions) {
                                String content = playerPosition.getLong("playerId").toString();
                                if (playerPosition.containsKey("positionId")) {
                                    if (playerPosition.getLong("positionId") > -1L) {
                                        content += ";" + playerPosition.getLong("positionId");
                                    }
                                }
                                playerIds.add(content);
                            }
                        }
                        distinctValuesMap.put("playerId", playerIds);
                    }
                    // countryId
                    if (doc.containsKey("countryId")) {
                        List<Object> countryIds = doc.getList("countryId", Object.class);
                        distinctValuesMap.put("countryId", countryIds);
                    }
                    // bornYear
                    if (doc.containsKey("bornYear")) {
                        List<Object> bornYears = doc.getList("bornYear", Object.class);
                        distinctValuesMap.put("bornYear", bornYears);
                    }
                    // footId
                    if (doc.containsKey("footId")) {
                        List<Object> footIds = doc.getList("footId", Object.class);
                        distinctValuesMap.put("footId", footIds);
                    }
                    // positionId
                    if (doc.containsKey("positionId")) {
                        List<Object> positionIds = doc.getList("positionId", Object.class);
                        distinctValuesMap.put("positionId", positionIds);
                    }
                    // positionDetailId
                    if (doc.containsKey("positionDetailId")) {
                        List<Object> positionDetailIds = doc.getList("positionDetailId", Object.class);
                        distinctValuesMap.put("positionDetailId", positionDetailIds);
                    }
                }
            }

            return distinctValuesMap;
        }

        return new HashMap<>();
    }

    public static List<Boolean> getHalfAvailability(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        Document row = collection.aggregate(
                Arrays.asList(
                        match(
                                and(
                                        or(
                                                gte("off", 0),
                                                gte("dif", 0)
                                        ),
                                        and(filters)
                                )
                        ),
                        group(
                                null,
                                sum("halfOffCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$off", 0)), 1, 0))),
                                sum("halfDifCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$dif", 0)), 1, 0)))
                        )
                )
        ).first();

        if (row != null) {
            return Arrays.asList(row.getInteger("halfOffCount") != null && row.getInteger("halfOffCount") > 0,
                    row.getInteger("halfDifCount") != null && row.getInteger("halfDifCount") > 0);
        } else {
            return Arrays.asList(false, false);
        }
    }

    public static List<Boolean> getZoneAvailability(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        Document row = collection.aggregate(
                Arrays.asList(
                        match(
                                and(
                                        or(
                                                gte("zoneOne", 0),
                                                gte("zoneTwo", 0),
                                                gte("zoneThree", 0)
                                        ),
                                        and(filters)
                                )
                        ),
                        group(
                                null,
                                sum("zoneOneCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneOne", 0)), 1, 0))),
                                sum("zoneTwoCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneTwo", 0)), 1, 0))),
                                sum("zoneThreeCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneThree", 0)), 1, 0)))
                        )
                )
        ).first();

        if (row != null) {
            return Arrays.asList(row.getInteger("zoneOneCount") != null && row.getInteger("zoneOneCount") > 0,
                    row.getInteger("zoneTwoCount") != null && row.getInteger("zoneTwoCount") > 0,
                    row.getInteger("zoneThreeCount") != null && row.getInteger("zoneThreeCount") > 0);
        } else {
            return Arrays.asList(false, false, false);
        }
    }

    public static List<Boolean> getChannelAvailability(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        Document row = collection.aggregate(
                Arrays.asList(
                        match(
                                and(
                                        or(
                                                gte("channelOne", 0),
                                                gte("channelTwo", 0),
                                                gte("channelThree", 0),
                                                gte("channelFour", 0),
                                                gte("channelFive", 0)
                                        ),
                                        and(filters)
                                )
                        ),
                        group(
                                null,
                                sum("channelOneCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelOne", 0)), 1, 0))),
                                sum("channelTwoCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelTwo", 0)), 1, 0))),
                                sum("channelThreeCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelThree", 0)), 1, 0))),
                                sum("channelFourCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFour", 0)), 1, 0))),
                                sum("channelFiveCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFive", 0)), 1, 0)))
                        )
                )
        ).first();

        if (row != null) {
            return Arrays.asList(row.getInteger("channelOneCount") != null && row.getInteger("channelOneCount") > 0,
                    row.getInteger("channelTwoCount") != null && row.getInteger("channelTwoCount") > 0,
                    row.getInteger("channelThreeCount") != null && row.getInteger("channelThreeCount") > 0,
                    row.getInteger("channelFourCount") != null && row.getInteger("channelFourCount") > 0,
                    row.getInteger("channelFiveCount") != null && row.getInteger("channelFiveCount") > 0);
        } else {
            return Arrays.asList(false, false, false, false, false);
        }
    }

    public static List<Boolean> getAreaAvailability(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        Document row = collection.aggregate(
                Arrays.asList(
                        match(
                                and(
                                        or(
                                                gte("difArea", 0),
                                                gte("difSmallArea", 0),
                                                gte("offArea", 0),
                                                gte("offSmallArea", 0)
                                        ),
                                        and(filters)
                                )
                        ),
                        group(
                                null,
                                sum("difAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelOne", 0)), 1, 0))),
                                sum("difSmallAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelTwo", 0)), 1, 0))),
                                sum("offAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelThree", 0)), 1, 0))),
                                sum("offSmallAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFour", 0)), 1, 0)))
                        )
                )
        ).first();

        if (row != null) {
            return Arrays.asList(row.getInteger("difAreaCount") != null && row.getInteger("difAreaCount") > 0,
                    row.getInteger("difSmallAreaCount") != null && row.getInteger("difSmallAreaCount") > 0,
                    row.getInteger("offAreaCount") != null && row.getInteger("offAreaCount") > 0,
                    row.getInteger("offSmallAreaCount") != null && row.getInteger("offSmallAreaCount") > 0);
        } else {
            return Arrays.asList(false, false, false, false);
        }
    }

    public static List<Boolean> getPositionalAvailability(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        Document row = collection.aggregate(
                Arrays.asList(
                        match(
                                and(
                                        or(
                                                gte("off", 0),
                                                gte("dif", 0),
                                                gte("zoneOne", 0),
                                                gte("zoneTwo", 0),
                                                gte("zoneThree", 0),
                                                gte("channelOne", 0),
                                                gte("channelTwo", 0),
                                                gte("channelThree", 0),
                                                gte("channelFour", 0),
                                                gte("channelFive", 0),
                                                gte("difArea", 0),
                                                gte("difSmallArea", 0),
                                                gte("offArea", 0),
                                                gte("offSmallArea", 0)
                                        ),
                                        and(filters)
                                )
                        ),
                        group(
                                null,
                                sum("halfOffCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$off", 0)), 1, 0))),
                                sum("halfDifCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$dif", 0)), 1, 0))),
                                sum("zoneOneCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneOne", 0)), 1, 0))),
                                sum("zoneTwoCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneTwo", 0)), 1, 0))),
                                sum("zoneThreeCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$zoneThree", 0)), 1, 0))),
                                sum("channelOneCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelOne", 0)), 1, 0))),
                                sum("channelTwoCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelTwo", 0)), 1, 0))),
                                sum("channelThreeCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelThree", 0)), 1, 0))),
                                sum("channelFourCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFour", 0)), 1, 0))),
                                sum("channelFiveCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFive", 0)), 1, 0))),
                                sum("difAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelOne", 0)), 1, 0))),
                                sum("difSmallAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelTwo", 0)), 1, 0))),
                                sum("offAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelThree", 0)), 1, 0))),
                                sum("offSmallAreaCount", new Document("$cond", Arrays.asList(new Document("$gt", Arrays.asList("$channelFour", 0)), 1, 0)))
                        )
                )
        ).first();

        if (row != null) {
            return Arrays.asList(row.getInteger("halfOffCount") != null && row.getInteger("halfOffCount") > 0,
                    row.getInteger("halfDifCount") != null && row.getInteger("halfDifCount") > 0,
                    row.getInteger("zoneOneCount") != null && row.getInteger("zoneOneCount") > 0,
                    row.getInteger("zoneTwoCount") != null && row.getInteger("zoneTwoCount") > 0,
                    row.getInteger("zoneThreeCount") != null && row.getInteger("zoneThreeCount") > 0,
                    row.getInteger("channelOneCount") != null && row.getInteger("channelOneCount") > 0,
                    row.getInteger("channelTwoCount") != null && row.getInteger("channelTwoCount") > 0,
                    row.getInteger("channelThreeCount") != null && row.getInteger("channelThreeCount") > 0,
                    row.getInteger("channelFourCount") != null && row.getInteger("channelFourCount") > 0,
                    row.getInteger("channelFiveCount") != null && row.getInteger("channelFiveCount") > 0,
                    row.getInteger("difAreaCount") != null && row.getInteger("difAreaCount") > 0,
                    row.getInteger("difSmallAreaCount") != null && row.getInteger("difSmallAreaCount") > 0,
                    row.getInteger("offAreaCount") != null && row.getInteger("offAreaCount") > 0,
                    row.getInteger("offSmallAreaCount") != null && row.getInteger("offSmallAreaCount") > 0);
        } else {
            return Arrays.asList(false, false, false, false, false, false, false, false, false, false, false, false, false, false);
        }
    }

    public static List<AdvancedMetric> getAdvancedMetricsAvailable(Boolean isTeam, Long seasonId, Long competitionId, Map<Long, AdvancedMetric> metrics, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        List<AdvancedMetric> availableMetrics = new ArrayList<>();
        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        List<Document> fieldExists = collection.find(and(eq("eventTypeId", null), exists("xG")), Document.class).limit(1).into(new ArrayList<Document>());
        if (fieldExists != null && !fieldExists.isEmpty()) {
            Document document = fieldExists.get(0);
            for (AdvancedMetric metric : metrics.values()) {
                String fieldName = metric.getCode();
                if (StringUtils.isNotBlank(fieldName)) {
                    if (document.containsKey(fieldName)) {
                        availableMetrics.add(metric);
                    }
                }
            }
        }

        return availableMetrics;
    }

    public static List<Long> getFixtureIdsByParams(Map<String, Object> params) {
        List<Long> result = new ArrayList<>();
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return result;
        }

        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        getFilters(filters, params);

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        MongoCursor<Document> cursor = null;

        if (filters.isEmpty()) {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(ne("matchMinutes", null)),
                            group("$fixtureId")
                    )
            ).iterator();
        } else {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(and(and(filters), ne("matchMinutes", null))),
                            group("$fixtureId")
                    )
            ).iterator();
        }

        if (cursor != null) {
            while (cursor.hasNext()) {
                Document row = cursor.next();
                if (row != null) {
                    result.add(row.getLong("_id"));
                }
            }
        }

        return result;
    }

    public static Map<Long, Map<Long, Long>> getTotalMinutes(Map<String, Object> params) {
        Map<Long, Map<Long, Long>> result = new HashMap<>();
        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionIds")) {
            return result;
        }

        List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionIds").toString(), "|"));
        params.remove("competitionIds");
        for (String competitionId : competitionIds) {
            params.put("competitionId", competitionId);
            String type = params.get("type").toString();

            // nuova gestione stagioni
            boolean validRequest = false;
            Long seasonId = Long.valueOf(params.get("seasonId").toString());
            if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
                Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
                Long tmpCompetitionId = Long.valueOf(competitionId);
                if (BaseController.getCompetitions().containsKey(tmpCompetitionId)) {
                    boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(tmpCompetitionId).getSolarSeason());
                    if (isSolar) {
                        if (groupedSeason.getSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    } else {
                        if (groupedSeason.getNonSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getNonSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    }
                }
            } else if (BaseController.getSeasons().containsKey(seasonId)) {
                // probabilmente ho già passato la stagione giusta
                collectionName = type;
                collectionName += "_" + seasonId;
                collectionName += "_" + competitionId;
                validRequest = true;
            }
            if (!validRequest) {
                continue;
            }

            ClassModel<Document> model = ClassModel.builder(Document.class).build();
            CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
            CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

            List<Bson> filters = new ArrayList<>();
            filters.add(ne("matchMinutes", null));
            if (params.containsKey("teamIds")) {
                List<String> stringTeamIds = Arrays.asList(StringUtils.split(params.get("teamIds").toString(), ","));
                List<Long> teamIds = new ArrayList<>();
                for (String teamId : stringTeamIds) {
                    teamIds.add(Long.valueOf(teamId));
                }
                filters.add(in("teamId", teamIds));
            }
            if (params.containsKey("playerIds")) {
                List<String> stringPlayerIds = Arrays.asList(StringUtils.split(params.get("playerIds").toString(), ","));
                List<Long> playerIds = new ArrayList<>();
                for (String playerId : stringPlayerIds) {
                    playerIds.add(Long.valueOf(playerId));
                }
                filters.add(in("playerId", playerIds));
            }
            if (params.containsKey("isHomeTeam")) {
                filters.add(eq("isHomeTeam", Boolean.valueOf(params.get("isHomeTeam").toString())));
            }
            if (params.containsKey("matchdayFrom") && params.containsKey("matchdayTo")) {
                filters.add(gte("matchDay", Integer.valueOf(params.get("matchdayFrom").toString())));
                filters.add(lte("matchDay", Integer.valueOf(params.get("matchdayTo").toString())));
            }
            if (params.containsKey("homeModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("homeModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("homeModule", modules)), and(eq("isHomeTeam", false), in("awayModule", modules)))));
            }
            if (params.containsKey("awayModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("awayModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("awayModule", modules)), and(eq("isHomeTeam", false), in("homeModule", modules)))));
            }

            MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
            MongoCursor<Document> cursor = null;
            if (StringUtils.equalsIgnoreCase(type, "team")) {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group("$teamId", sum("totalMinutes", "$matchMinutes"))
                        )
                ).iterator();
            } else {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group(new Document("teamId", "$teamId").append("playerId", "$playerId"), sum("totalMinutes", "$matchMinutes"))
                        )
                ).iterator();
            }

            if (cursor != null) {
                while (cursor.hasNext()) {
                    Document row = cursor.next();
                    if (row != null) {
                        if (row.get("_id") instanceof Document) {
                            Document groupBy = row.get("_id", Document.class);
                            result.putIfAbsent(groupBy.getLong("teamId"), new HashMap<Long, Long>());
                            result.get(groupBy.getLong("teamId")).putIfAbsent(groupBy.getLong("playerId"), 0L);
                            result.get(groupBy.getLong("teamId")).put(groupBy.getLong("playerId"), result.get(groupBy.getLong("teamId")).get(groupBy.getLong("playerId")) + row.getLong("totalMinutes"));
                        } else {
                            result.putIfAbsent(row.getLong("_id"), new HashMap<Long, Long>());
                            result.get(row.getLong("_id")).putIfAbsent(null, 0L);
                            result.get(row.getLong("_id")).put(null, result.get(row.getLong("_id")).get(null) + row.getLong("totalMinutes"));
                        }
                    }
                }
            }
        }

        return result;
    }

    public static Map<Long, Map<Long, Long>> getTotalTouches(Map<String, Object> params) {
        Map<Long, Map<Long, Long>> result = new HashMap<>();
        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionIds")) {
            return result;
        }

        List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionIds").toString(), "|"));
        params.remove("competitionIds");
        for (String competitionId : competitionIds) {
            params.put("competitionId", competitionId);
            String type = params.get("type").toString();

            // nuova gestione stagioni
            boolean validRequest = false;
            Long seasonId = Long.valueOf(params.get("seasonId").toString());
            if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
                Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
                Long tmpCompetitionId = Long.valueOf(competitionId);
                if (BaseController.getCompetitions().containsKey(tmpCompetitionId)) {
                    boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(tmpCompetitionId).getSolarSeason());
                    if (isSolar) {
                        if (groupedSeason.getSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    } else {
                        if (groupedSeason.getNonSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getNonSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    }
                }
            } else if (BaseController.getSeasons().containsKey(seasonId)) {
                // probabilmente ho già passato la stagione giusta
                collectionName = type;
                collectionName += "_" + seasonId;
                collectionName += "_" + competitionId;
                validRequest = true;
            }
            if (!validRequest) {
                continue;
            }

            ClassModel<Document> model = ClassModel.builder(Document.class).build();
            CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
            CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

            List<Bson> filters = new ArrayList<>();
            filters.add(ne("touches", null));
            if (params.containsKey("teamIds")) {
                List<String> stringTeamIds = Arrays.asList(StringUtils.split(params.get("teamIds").toString(), ","));
                List<Long> teamIds = new ArrayList<>();
                for (String teamId : stringTeamIds) {
                    teamIds.add(Long.valueOf(teamId));
                }
                filters.add(in("teamId", teamIds));
            }
            if (params.containsKey("playerIds")) {
                List<String> stringPlayerIds = Arrays.asList(StringUtils.split(params.get("playerIds").toString(), ","));
                List<Long> playerIds = new ArrayList<>();
                for (String playerId : stringPlayerIds) {
                    playerIds.add(Long.valueOf(playerId));
                }
                filters.add(in("playerId", playerIds));
            }
            if (params.containsKey("isHomeTeam")) {
                filters.add(eq("isHomeTeam", Boolean.valueOf(params.get("isHomeTeam").toString())));
            }
            if (params.containsKey("matchdayFrom") && params.containsKey("matchdayTo")) {
                filters.add(gte("matchDay", Integer.valueOf(params.get("matchdayFrom").toString())));
                filters.add(lte("matchDay", Integer.valueOf(params.get("matchdayTo").toString())));
            }
            if (params.containsKey("homeModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("homeModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("homeModule", modules)), and(eq("isHomeTeam", false), in("awayModule", modules)))));
            }
            if (params.containsKey("awayModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("awayModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("awayModule", modules)), and(eq("isHomeTeam", false), in("homeModule", modules)))));
            }

            MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
            MongoCursor<Document> cursor = null;
            if (StringUtils.equalsIgnoreCase(type, "team")) {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group("$teamId", sum("totalTouches", "$touches"))
                        )
                ).iterator();
            } else {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group(new Document("teamId", "$teamId").append("playerId", "$playerId"), sum("totalTouches", "$touches"))
                        )
                ).iterator();
            }

            if (cursor != null) {
                while (cursor.hasNext()) {
                    Document row = cursor.next();
                    if (row != null) {
                        if (row.get("_id") instanceof Document) {
                            Document groupBy = row.get("_id", Document.class);
                            result.putIfAbsent(groupBy.getLong("teamId"), new HashMap<Long, Long>());
                            result.get(groupBy.getLong("teamId")).putIfAbsent(groupBy.getLong("playerId"), 0L);
                            result.get(groupBy.getLong("teamId")).put(groupBy.getLong("playerId"), result.get(groupBy.getLong("teamId")).get(groupBy.getLong("playerId")) + row.getLong("totalTouches"));
                        } else {
                            result.putIfAbsent(row.getLong("_id"), new HashMap<Long, Long>());
                            result.get(row.getLong("_id")).putIfAbsent(null, 0L);
                            result.get(row.getLong("_id")).put(null, result.get(row.getLong("_id")).get(null) + row.getLong("totalTouches"));
                        }
                    }
                }
            }
        }

        return result;
    }

    public static Map<Long, Map<Long, Long>> getTotalFixture(Map<String, Object> params) {
        Map<Long, Map<Long, Long>> result = new HashMap<>();
        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionIds")) {
            return result;
        }

        List<String> competitionIds = Arrays.asList(StringUtils.split(params.get("competitionIds").toString(), "|"));
        params.remove("competitionIds");
        for (String competitionId : competitionIds) {
            params.put("competitionId", competitionId);
            String type = params.get("type").toString();

            // nuova gestione stagioni
            boolean validRequest = false;
            Long seasonId = Long.valueOf(params.get("seasonId").toString());
            if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
                Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
                Long tmpCompetitionId = Long.valueOf(competitionId);
                if (BaseController.getCompetitions().containsKey(tmpCompetitionId)) {
                    boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(tmpCompetitionId).getSolarSeason());
                    if (isSolar) {
                        if (groupedSeason.getSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    } else {
                        if (groupedSeason.getNonSolarId() != null) {
                            collectionName = type;
                            collectionName += "_" + groupedSeason.getNonSolarId();
                            collectionName += "_" + tmpCompetitionId;
                            validRequest = true;
                        }
                    }
                }
            } else if (BaseController.getSeasons().containsKey(seasonId)) {
                // probabilmente ho già passato la stagione giusta
                collectionName = type;
                collectionName += "_" + seasonId;
                collectionName += "_" + competitionId;
                validRequest = true;
            }
            if (!validRequest) {
                continue;
            }

            ClassModel<Document> model = ClassModel.builder(Document.class).build();
            CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
            CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

            List<Bson> filters = new ArrayList<>();
            filters.add(ne("matchMinutes", null));
            if (params.containsKey("teamIds")) {
                List<String> stringTeamIds = Arrays.asList(StringUtils.split(params.get("teamIds").toString(), ","));
                List<Long> teamIds = new ArrayList<>();
                for (String teamId : stringTeamIds) {
                    teamIds.add(Long.valueOf(teamId));
                }
                filters.add(in("teamId", teamIds));
            }
            if (params.containsKey("playerIds")) {
                List<String> stringPlayerIds = Arrays.asList(StringUtils.split(params.get("playerIds").toString(), ","));
                List<Long> playerIds = new ArrayList<>();
                for (String playerId : stringPlayerIds) {
                    playerIds.add(Long.valueOf(playerId));
                }
                filters.add(in("playerId", playerIds));
            }
            if (params.containsKey("isHomeTeam")) {
                filters.add(eq("isHomeTeam", Boolean.valueOf(params.get("isHomeTeam").toString())));
            }
            if (params.containsKey("matchdayFrom") && params.containsKey("matchdayTo")) {
                filters.add(gte("matchDay", Integer.valueOf(params.get("matchdayFrom").toString())));
                filters.add(lte("matchDay", Integer.valueOf(params.get("matchdayTo").toString())));
            }
            if (params.containsKey("homeModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("homeModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("homeModule", modules)), and(eq("isHomeTeam", false), in("awayModule", modules)))));
            }
            if (params.containsKey("awayModule")) {
                List<String> modules = Arrays.asList(StringUtils.split(params.get("awayModule").toString(), "|"));
                filters.add(and(or(and(eq("isHomeTeam", true), in("awayModule", modules)), and(eq("isHomeTeam", false), in("homeModule", modules)))));
            }

            MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
            MongoCursor<Document> cursor = null;
            if (StringUtils.equalsIgnoreCase(type, "team")) {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group("$teamId",
                                        Accumulators.addToSet("distinctFixtureIds", "$fixtureId")
                                ),
                                project(new Document("teamId", "$_id")
                                        .append("totalFixtures", new Document("$size", "$distinctFixtureIds"))
                                        .append("_id", 0))
                        )
                ).iterator();
            } else {
                cursor = collection.aggregate(
                        Arrays.asList(
                                match(and(filters)),
                                group(new Document("teamId", "$teamId").append("playerId", "$playerId"),
                                        Accumulators.addToSet("distinctFixtureIds", "$fixtureId")
                                ),
                                project(new Document("teamId", "$_id.teamId")
                                        .append("playerId", "$_id.playerId")
                                        .append("totalFixtures", new Document("$size", "$distinctFixtureIds"))
                                        .append("_id", 0))
                        )
                ).iterator();
            }

            if (cursor != null) {
                while (cursor.hasNext()) {
                    Document row = cursor.next();
                    if (row != null) {
                        result.putIfAbsent(row.getLong("teamId"), new HashMap<Long, Long>());
                        result.get(row.getLong("teamId")).putIfAbsent(row.getLong("playerId"), 0L);
                        result.get(row.getLong("teamId")).put(row.getLong("playerId"), result.get(row.getLong("teamId")).get(row.getLong("playerId")) + row.getInteger("totalFixtures"));
                    }
                }
            }
        }

        return result;
    }

    public static Double getCompetitionAverageByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return 0D;
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = "team";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = "team";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = "team";
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return 0D;
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        MongoCursor<Document> cursor = null;
        String totalField = "total";
        Long eventTypeId = Long.valueOf(params.get("eventTypeId").toString());
        if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
            totalField = BaseController.getAdvancedMetrics().get(eventTypeId).getCode();
        }

        if (filters.isEmpty()) {
            cursor = collection.aggregate(
                    singletonList(
                            group("$teamId", sum(totalField, "$" + totalField))
                    )
            ).iterator();
        } else {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(and(filters)),
                            group("$teamId", sum(totalField, "$" + totalField), addToSet("uniqueFixtures", "$fixtureId")),
                            project(new Document(totalField, "$" + totalField).append("count", new Document("$size", "$uniqueFixtures")))
                    )
            ).iterator();
        }

        Map<Long, Map<Long, Long>> totalMinutes = new HashMap<>();
        if (params.containsKey("totalType")) {
            boolean isTotals = !StringUtils.equals(params.get("totalType").toString(), "p90");
            if (!isTotals) {
                List<Long> fixtureIds;
                if (filters.isEmpty()) {
                    fixtureIds = collection.distinct("fixtureId", Long.class).into(new ArrayList<Long>());
                } else {
                    fixtureIds = collection.distinct("fixtureId", and(filters), Long.class).into(new ArrayList<Long>());
                }

                if (fixtureIds != null && !fixtureIds.isEmpty()) {
                    params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
                    totalMinutes = getTotalMinutes(params);
                }
            }
        }

        double totals = 0;
        int rows = 0;
        if (cursor != null) {
            while (cursor.hasNext()) {
                Document row = cursor.next();
                if (row != null) {
                    Long teamId = row.getLong("_id");
                    double teamTotal = 0;
                    if (row.get(totalField) instanceof Integer) {
                        teamTotal = row.getInteger(totalField);
                    } else if (row.get(totalField) instanceof Double) {
                        teamTotal = row.getDouble(totalField);
                    }
                    if (totalMinutes.containsKey(teamId)) {
                        teamTotal = 90D * teamTotal / totalMinutes.get(teamId).get(null);
                    }

                    totals += teamTotal;
                    rows++;
                }
            }
        }

        if (rows > 0) {
            return totals / rows / Double.parseDouble(params.get("matchdayTo").toString());
        } else {
            return 0D;
        }
    }

    public static Map<Long, Map<Long, Double>> getTeamEventTypeTotalsByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return new HashMap<>();
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new HashMap<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        List<String> fieldsToSum = ControllerUtils.getFieldsToSum(params);
        List<String> finalFieldsToSum = new ArrayList<>();
        for (String field : fieldsToSum) {
            finalFieldsToSum.add("$" + field);
        }

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        MongoCursor<Document> cursor = null;
        if (filters.isEmpty()) {
            cursor = collection.aggregate(
                    Arrays.asList(
                            group(
                                    new Document("teamId", "$teamId").append("eventTypeId", "$eventTypeId"),
                                    sum("total", new Document("$add", finalFieldsToSum))
                            )
                    )
            ).iterator();
        } else {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(and(filters)),
                            group(
                                    new Document("teamId", "$teamId").append("eventTypeId", "$eventTypeId"),
                                    sum("total", new Document("$add", finalFieldsToSum))
                            )
                    )
            ).iterator();
        }

        Map<Long, Map<Long, Double>> teamTotalMap = new HashMap<>();
        if (cursor != null) {
            while (cursor.hasNext()) {
                Document row = cursor.next();
                if (row != null) {
                    Document doc = row.get("_id", Document.class);
                    Long teamId = doc.getLong("teamId");
                    Long eventTypeId = doc.getLong("eventTypeId");
                    teamTotalMap.putIfAbsent(eventTypeId, new HashMap<Long, Double>());
                    teamTotalMap.get(eventTypeId).put(teamId, row.getInteger("total").doubleValue());
                }
            }
        }

        return teamTotalMap;
    }

    public static Map<Long, Map<Long, Double>> getPlayerEventTypeTotalsByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return new HashMap<>();
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new HashMap<>();
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        List<String> fieldsToSum = ControllerUtils.getFieldsToSum(params);
        List<String> finalFieldsToSum = new ArrayList<>();
        for (String field : fieldsToSum) {
            finalFieldsToSum.add("$" + field);
        }

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        MongoCursor<Document> cursor = null;
        if (filters.isEmpty()) {
            cursor = collection.aggregate(
                    Arrays.asList(
                            group(
                                    new Document("playerId", "$playerId").append("eventTypeId", "$eventTypeId"),
                                    sum("total", new Document("$add", finalFieldsToSum))
                            )
                    )
            ).iterator();
        } else {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(and(filters)),
                            group(
                                    new Document("playerId", "$playerId").append("eventTypeId", "$eventTypeId"),
                                    sum("total", new Document("$add", finalFieldsToSum))
                            )
                    )
            ).iterator();
        }

        Map<Long, Map<Long, Double>> playerTotalMap = new HashMap<>();
        if (cursor != null) {
            while (cursor.hasNext()) {
                Document row = cursor.next();
                if (row != null) {
                    Document doc = row.get("_id", Document.class);
                    Long playerId = doc.getLong("playerId");
                    Long eventTypeId = doc.getLong("eventTypeId");
                    playerTotalMap.putIfAbsent(eventTypeId, new HashMap<Long, Double>());
                    playerTotalMap.get(eventTypeId).put(playerId, row.getInteger("total").doubleValue());
                }
            }
        }

        return playerTotalMap;
    }

    public static Entry<Integer, Integer> getMinMaxByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();

        String collectionName = "";
        if (!params.containsKey("type") || !params.containsKey("seasonId") || !params.containsKey("competitionId")) {
            return new SimpleEntry<>(0, 0);
        }
        // nuova gestione stagioni
        boolean validRequest = false;
        Long seasonId = Long.valueOf(params.get("seasonId").toString());
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            Long competitionId = Long.valueOf(params.get("competitionId").toString());
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = params.get("type").toString();
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = params.get("type").toString();
            collectionName += "_" + seasonId;
            collectionName += "_" + params.get("competitionId").toString();
            validRequest = true;
        }
        if (!validRequest) {
            return new SimpleEntry<>(0, 0);
        }

        getFilters(filters, params);

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        MongoCursor<Document> cursor = null;
        if (filters.isEmpty()) {
            cursor = collection.aggregate(
                    singletonList(
                            group(null, min("min", "$total"), max("max", "$total"))
                    )
            ).iterator();
        } else {
            cursor = collection.aggregate(
                    Arrays.asList(
                            match(and(filters)),
                            group(null, min("min", "$total"), max("max", "$total"))
                    )
            ).iterator();
        }

        Entry<Integer, Integer> minMaxValues = new SimpleEntry<>(0, 0);
        if (cursor != null) {
            while (cursor.hasNext()) {
                Document row = cursor.next();
                if (row != null) {
                    minMaxValues = new SimpleEntry<>(row.getInteger("min"), row.getInteger("max"));
                }
            }
        }

        return minMaxValues;
    }

    public static List<Object> getPlayersPosition(Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();

        String collectionName = "";
        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return new ArrayList<>();
        }

        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        getFilters(filters, params);
        filters.remove(eq("tagTypeId", null));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        List<BsonField> fields = new ArrayList<>();
        fields.add(addToSet("playerId", new Document("playerId", "$playerId").append("positionId", new Document("$ifNull", Arrays.asList("$positionId", -1L)))));
        AggregateIterable<Document> result;
        if (filters.isEmpty()) {
            result = collection
                    .aggregate(singletonList(
                            group(null,
                                    fields
                            )
                    ));
        } else {
            result = collection
                    .aggregate(Arrays.asList(
                            match(and(filters)),
                            group(null,
                                    fields
                            )
                    ));
        }

        if (result != null) {
            List<Object> distinctValuesMap = new ArrayList<>();
            for (Document doc : result) {
                // playerId
                if (doc.containsKey("playerId")) {
                    List<Object> playerIds = new ArrayList<>();
                    if (doc.get("playerId") instanceof ArrayList) {
                        List<Document> playerPositions = doc.get("playerId", ArrayList.class);
                        for (Document playerPosition : playerPositions) {
                            String content = playerPosition.getLong("playerId").toString();
                            if (playerPosition.containsKey("positionId")) {
                                if (playerPosition.getLong("positionId") > -1L) {
                                    content += ";" + playerPosition.getLong("positionId");
                                }
                            }
                            playerIds.add(content);
                        }
                    }
                    distinctValuesMap.addAll(playerIds);
                }
            }

            return distinctValuesMap;
        }

        return new ArrayList<>();
    }

    public static List<SimilarityDocumentRow> getSimilarityDocumentsByParams(Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        int skip = 0, limit = 0;
        Bson sort = null;

        String collectionName = "similar_player";
        if (!params.containsKey("playerId") || !params.containsKey("playerIds") || !params.containsKey("statsTypeIds")) {
            return new ArrayList<>();
        }

        ClassModel<SimilarityDocumentRow> model = ClassModel.builder(SimilarityDocumentRow.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(new SimilarityDocumentRowCodecProvider(pojoCodecProvider)));

        if (params.containsKey("positionIds")) {
            List<String> values = Arrays.asList(StringUtils.split(params.get("positionIds").toString(), "|"));
            List<String> validValues = new ArrayList<>();
            for (String value : values) {
                if (StringUtils.contains(value, "-")) {
                    List<String> splitted = Arrays.asList(StringUtils.split(value, "-"));
                    if (splitted.size() == 2) {
                        if (validValues.contains(splitted.get(0))) {
                            validValues.remove(splitted.get(0));
                        }
                        validValues.add(value);
                    }
                } else {
                    validValues.add(value);
                }
            }
            List<Bson> positionFilters = new ArrayList<>();
            for (String positionId : validValues) {
                if (StringUtils.contains(positionId, "-")) {
                    List<String> splitted = Arrays.asList(StringUtils.split(positionId, "-"));
                    if (splitted.size() == 2) {
                        positionFilters.add(and(eq("positionId", Long.valueOf(splitted.get(0))), eq("positionDetailId", Long.valueOf(splitted.get(1)))));
                    }
                } else {
                    positionFilters.add(eq("positionId", Long.valueOf(positionId)));
                }
            }
            positionFilters.add(eq("playerId", Long.valueOf(params.get("playerId").toString())));
            filters.add(or(positionFilters));
        }
        if (params.containsKey("playerIds")) {
            List<Long> validPlayerIds = new ArrayList<>();
            for (String playerId : StringUtils.split(params.get("playerIds").toString(), "|")) {
                validPlayerIds.add(Long.valueOf(playerId));
            }
            filters.add(in("playerId", validPlayerIds));
        }
        if (params.containsKey("genere")) {
            filters.add(eq("genere", Integer.valueOf(params.get("genere").toString())));
        }
        if (params.containsKey("playtime")) {
            filters.add(gte("type1", Long.valueOf(params.get("playtime").toString())));
        }

        List<String> fieldsToInclude = new ArrayList<>();
        fieldsToInclude.add("playerId");
        fieldsToInclude.add("positionId");
        if (params.containsKey("statsTypeIds")) {
            for (String statsTypeId : StringUtils.split(params.get("statsTypeIds").toString(), "|")) {
                fieldsToInclude.add("type" + statsTypeId);
            }
        }

        MongoCollection<SimilarityDocumentRow> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), SimilarityDocumentRow.class).withCodecRegistry(pojoCodecRegistry);
        List<SimilarityDocumentRow> rows;
        if (filters.isEmpty()) {
            rows = collection
                    .find()
                    .projection(include(fieldsToInclude))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<SimilarityDocumentRow>());
        } else {
            rows = collection
                    .find(and(filters))
                    .projection(include(fieldsToInclude))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<SimilarityDocumentRow>());
        }

        return rows;
    }

    public static DocumentFilter getFilter(Boolean isTeam, Long seasonId, Long competitionId, Map<String, Object> params) {
        List<Bson> filters = new ArrayList<>();
        String collectionName = "";

        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        collectionName += "_filters";
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        collectionName += "_filters";
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = BooleanUtils.isTrue(isTeam) ? "team" : "player";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            collectionName += "_filters";
            validRequest = true;
        }
        if (!validRequest) {
            return null;
        }

        if (params.containsKey("teamId")) {
            String teamIds = params.get("teamId").toString();
            if (teamIds.contains("|")) {
                List<Long> validTeamIds = new ArrayList<>();
                for (String teamId : StringUtils.split(teamIds, "|")) {
                    validTeamIds.add(Long.valueOf(teamId));
                }
                filters.add(in("teamId", validTeamIds));
            } else {
                filters.add(eq("teamId", Long.valueOf(teamIds)));
            }
        }
        // se non contiene il teamId devo fare il merge di tutti i documenti dei team
        if (params.containsKey("playerId")) {
            String playerIds = params.get("playerId").toString();
            if (playerIds.contains("|")) {
                List<Long> validPlayerIds = new ArrayList<>();
                for (String playerId : StringUtils.split(playerIds, "|")) {
                    validPlayerIds.add(Long.valueOf(playerId));
                }
                filters.add(in("playerId", validPlayerIds));
            } else {
                filters.add(eq("playerId", Long.valueOf(playerIds)));
            }
        } else {
            if (collectionName.startsWith("player_")) {
                if (!params.containsKey("eventTypeId")) {
                    filters.add(eq("playerId", null));
                }
            } else {
                filters.add(eq("playerId", null));
            }
        }
        if (params.containsKey("eventTypeId")) {
            // per i filtri dei giocatori non c'è il raggruppato per team e evento
            // quindi nel caso non ho il giocatore ignoro il filtro per evento
            if (collectionName.startsWith("player_")) {
                if (params.containsKey("playerId") || !params.containsKey("teamId")) {
                    filters.add(eq("eventTypeId", Long.valueOf(params.get("eventTypeId").toString())));
                }
            } else {
                filters.add(eq("eventTypeId", Long.valueOf(params.get("eventTypeId").toString())));
            }
        } else {
            // in questo modo per le pagine con selezione multipla degli eventi carico tutti i tag
            // non posso mettere ne("eventTypeId", null) perchè altrimenti non carica le metriche avanzate
            if (!params.containsKey("multiEventTypeId")) {
                filters.add(eq("eventTypeId", null));
            }
        }

        ClassModel<DocumentFilter> model = ClassModel.builder(DocumentFilter.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<DocumentFilter> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), DocumentFilter.class).withCodecRegistry(pojoCodecRegistry);
        List<DocumentFilter> documentFilters = collection.find(and(filters), DocumentFilter.class).into(new ArrayList<DocumentFilter>());
        DocumentFilter filter = new DocumentFilter();
        if (documentFilters != null && !documentFilters.isEmpty()) {
            for (DocumentFilter documentFilter : documentFilters) {
                Utils.mergeFilters(filter, documentFilter);
            }
        }

        return filter;
    }

    private static void getFilters(List<Bson> filters, Map<String, Object> params) {
        if (params.containsKey("half")) {
            if (params.containsKey("zone")) {
                if (params.containsKey("channel")) {
                    String half = params.get("half").toString();
                    String zone = params.get("zone").toString();
                    String channel = params.get("channel").toString();
                    switch (half) {
                        case "1":
                            switch (zone) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("offZoneOneChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("offZoneOneChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("offZoneOneChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("offZoneOneChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("offZoneOneChannelFive"));
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("offZoneTwoChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("offZoneTwoChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("offZoneTwoChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("offZoneTwoChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("offZoneTwoChannelFive"));
                                            break;
                                    }
                                    break;
                                case "3":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("offZoneThreeChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("offZoneThreeChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("offZoneThreeChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("offZoneThreeChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("offZoneThreeChannelFive"));
                                            break;
                                    }
                                    break;
                            }
                            break;
                        case "2":
                            switch (zone) {
                                case "1":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("difZoneOneChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("difZoneOneChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("difZoneOneChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("difZoneOneChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("difZoneOneChannelFive"));
                                            break;
                                    }
                                    break;
                                case "2":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("difZoneTwoChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("difZoneTwoChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("difZoneTwoChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("difZoneTwoChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("difZoneTwoChannelFive"));
                                            break;
                                    }
                                    break;
                                case "3":
                                    switch (channel) {
                                        case "1":
                                            filters.add(exists("difZoneThreeChannelOne"));
                                            break;
                                        case "2":
                                            filters.add(exists("difZoneThreeChannelTwo"));
                                            break;
                                        case "3":
                                            filters.add(exists("difZoneThreeChannelThree"));
                                            break;
                                        case "4":
                                            filters.add(exists("difZoneThreeChannelFour"));
                                            break;
                                        case "5":
                                            filters.add(exists("difZoneThreeChannelFive"));
                                            break;
                                    }
                                    break;
                            }
                    }
                } else {
                    String half = params.get("half").toString();
                    String zone = params.get("zone").toString();
                    switch (half) {
                        case "1":
                            switch (zone) {
                                case "1":
                                    filters.add(or(exists("offZoneOneChannelOne"), exists("offZoneOneChannelTwo"), exists("offZoneOneChannelThree"), exists("offZoneOneChannelFour"), exists("offZoneOneChannelFive")));
                                    break;
                                case "2":
                                    filters.add(or(exists("offZoneTwoChannelOne"), exists("offZoneTwoChannelTwo"), exists("offZoneTwoChannelThree"), exists("offZoneTwoChannelFour"), exists("offZoneTwoChannelFive")));
                                    break;
                                case "3":
                                    filters.add(or(exists("offZoneThreeChannelOne"), exists("offZoneThreeChannelTwo"), exists("offZoneThreeChannelThree"), exists("offZoneTwoChannelFour"), exists("offZoneThreeChannelFive")));
                                    break;
                            }
                            break;
                        case "2":
                            switch (zone) {
                                case "1":
                                    filters.add(or(exists("difZoneOneChannelOne"), exists("difZoneOneChannelTwo"), exists("difZoneOneChannelThree"), exists("difZoneOneChannelFour"), exists("difZoneOneChannelFive")));
                                    break;
                                case "2":
                                    filters.add(or(exists("difZoneTwoChannelOne"), exists("difZoneTwoChannelTwo"), exists("difZoneTwoChannelThree"), exists("difZoneTwoChannelFour"), exists("difZoneTwoChannelFive")));
                                    break;
                                case "3":
                                    filters.add(or(exists("difZoneThreeChannelOne"), exists("difZoneThreeChannelTwo"), exists("difZoneThreeChannelThree"), exists("difZoneTwoChannelFour"), exists("difZoneThreeChannelFive")));
                                    break;
                            }
                            break;
                    }
                }
            } else {
                if (params.containsKey("channel")) {
                    String half = params.get("half").toString();
                    String channel = params.get("channel").toString();
                    switch (half) {
                        case "1":
                            switch (channel) {
                                case "1":
                                    filters.add(or(exists("offZoneTwoChannelOne"), exists("offZoneThreeChannelOne")));
                                    break;
                                case "2":
                                    filters.add(or(exists("offZoneTwoChannelTwo"), exists("offZoneThreeChannelTwo")));
                                    break;
                                case "3":
                                    filters.add(or(exists("offZoneTwoChannelThree"), exists("offZoneThreeChannelThree")));
                                    break;
                                case "4":
                                    filters.add(or(exists("offZoneTwoChannelFour"), exists("offZoneThreeChannelFour")));
                                    break;
                                case "5":
                                    filters.add(or(exists("offZoneTwoChannelFive"), exists("offZoneThreeChannelFive")));
                                    break;
                            }
                            break;
                        case "2":
                            switch (channel) {
                                case "1":
                                    filters.add(or(exists("difZoneOneChannelOne"), exists("difZoneTwoChannelOne")));
                                    break;
                                case "2":
                                    filters.add(or(exists("difZoneOneChannelTwo"), exists("difZoneTwoChannelTwo")));
                                    break;
                                case "3":
                                    filters.add(or(exists("difZoneOneChannelThree"), exists("difZoneTwoChannelThree")));
                                    break;
                                case "4":
                                    filters.add(or(exists("difZoneOneChannelFour"), exists("difZoneTwoChannelFour")));
                                    break;
                                case "5":
                                    filters.add(or(exists("difZoneOneChannelFive"), exists("difZoneTwoChannelFive")));
                                    break;
                            }
                            break;
                    }
                } else {
                    String half = params.get("half").toString();
                    switch (half) {
                        case "1":
                            filters.add(exists("off"));
                            break;
                        case "2":
                            filters.add(exists("dif"));
                            break;
                    }
                }
            }
        } else if (params.containsKey("zone")) {
            if (params.containsKey("channel")) {
                String zone = params.get("zone").toString();
                String channel = params.get("channel").toString();
                switch (zone) {
                    case "1":
                        switch (channel) {
                            case "1":
                                filters.add(exists("difZoneOneChannelOne"));
                                break;
                            case "2":
                                filters.add(exists("difZoneOneChannelTwo"));
                                break;
                            case "3":
                                filters.add(exists("difZoneOneChannelThree"));
                                break;
                            case "4":
                                filters.add(exists("difZoneOneChannelFour"));
                                break;
                            case "5":
                                filters.add(exists("difZoneOneChannelFive"));
                                break;
                        }
                        break;
                    case "2":
                        switch (channel) {
                            case "1":
                                filters.add(or(exists("offZoneTwoChannelOne"), exists("difZoneTwoChannelOne")));
                                break;
                            case "2":
                                filters.add(or(exists("offZoneTwoChannelTwo"), exists("difZoneTwoChannelTwo")));
                                break;
                            case "3":
                                filters.add(or(exists("offZoneTwoChannelTwo"), exists("difZoneTwoChannelTwo")));
                                break;
                            case "4":
                                filters.add(or(exists("offZoneTwoChannelTwo"), exists("difZoneTwoChannelTwo")));
                                break;
                            case "5":
                                filters.add(or(exists("offZoneTwoChannelTwo"), exists("difZoneTwoChannelTwo")));
                                break;
                        }
                        break;
                    case "3":
                        switch (channel) {
                            case "1":
                                filters.add(exists("offZoneThreeChannelOne"));
                                break;
                            case "2":
                                filters.add(exists("offZoneThreeChannelTwo"));
                                break;
                            case "3":
                                filters.add(exists("offZoneThreeChannelThree"));
                                break;
                            case "4":
                                filters.add(exists("offZoneThreeChannelFour"));
                                break;
                            case "5":
                                filters.add(exists("offZoneThreeChannelFive"));
                                break;
                        }
                        break;
                }
            } else {
                String zone = params.get("zone").toString();
                switch (zone) {
                    case "1":
                        filters.add(exists("zoneOne"));
                        break;
                    case "2":
                        filters.add(exists("zoneTwo"));
                        break;
                    case "3":
                        filters.add(exists("zoneThree"));
                        break;
                }
            }
        } else if (params.containsKey("channel")) {
            String channel = params.get("channel").toString();
            switch (channel) {
                case "1":
                    filters.add(exists("channelOne"));
                    break;
                case "2":
                    filters.add(exists("channelTwo"));
                    break;
                case "3":
                    filters.add(exists("channelThree"));
                    break;
                case "4":
                    filters.add(exists("channelFour"));
                    break;
                case "5":
                    filters.add(exists("channelFive"));
                    break;
            }
        }

        if (params.containsKey("area")) {
            String area = params.get("area").toString();
            switch (area) {
                case "1":
                    filters.add(exists("difArea"));
                    break;
                case "2":
                    filters.add(exists("difSmallArea"));
                    break;
                case "3":
                    filters.add(exists("offArea"));
                    break;
                case "4":
                    filters.add(exists("offSmallArea"));
                    break;
            }
        }

        for (String param : params.keySet()) {
            if (!StringUtils.equals(param, "type") && !StringUtils.equals(param, "seasonId") && !StringUtils.equals(param, "competitionId")
                    && !StringUtils.equals(param, "half") && !StringUtils.equals(param, "zone") && !StringUtils.equals(param, "channel") && !StringUtils.equals(param, "area")
                    && !StringUtils.equals(param, "totalType") && !StringUtils.equals(param, "skipEventCheck")) {
                boolean validParameter = true;
                if (params.containsKey("type")) {
                    if (StringUtils.equalsIgnoreCase(params.get("type").toString(), "team")) {
                        if (StringUtils.equals(param, "playerId") || StringUtils.equals(param, "playerIds")) {
                            validParameter = false;
                        }
                    }
                }

                if (validParameter) {
                    if (StringUtils.equals(param, "matchdayFrom")) {
                        filters.add(gte("matchDay", Integer.valueOf(params.get(param).toString())));
                    } else if (StringUtils.equals(param, "matchdayTo")) {
                        filters.add(lte("matchDay", Integer.valueOf(params.get(param).toString())));
                    } else if (StringUtils.equals(param, "bornyearFrom")) {
                        filters.add(gte("bornYear", Integer.valueOf(params.get(param).toString())));
                    } else if (StringUtils.equals(param, "bornyearTo")) {
                        filters.add(lte("bornYear", Integer.valueOf(params.get(param).toString())));
                    } else {
                        if (StringUtils.equals(param, "homeModule") || StringUtils.equals(param, "awayModule") || StringUtils.equals(param, "tagTypeId")) {
                            String value = params.get(param).toString().replaceAll("/", "|");
                            if (StringUtils.equals(param, "homeModule")) {
                                List<String> modules = Arrays.asList(StringUtils.split(value, "|"));
                                filters.add(and(or(and(eq("isHomeTeam", true), in("homeModule", modules)), and(eq("isHomeTeam", false), in("awayModule", modules)))));
                            } else if (StringUtils.equals(param, "awayModule")) {
                                List<String> modules = Arrays.asList(StringUtils.split(value, "|"));
                                filters.add(and(or(and(eq("isHomeTeam", true), in("awayModule", modules)), and(eq("isHomeTeam", false), in("homeModule", modules)))));
                            } else {
                                filters.add(eq(param, value));
                            }
                        } else if (StringUtils.equals(param, "isHomeTeam")) {
                            filters.add(eq(param, Boolean.valueOf(params.get(param).toString())));
                        } else if (StringUtils.equals(param, "eventTypeIds")) {
                            List<String> eventTypeIds = Arrays.asList(StringUtils.split(params.get(param).toString(), "|"));
                            if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
                                List<Bson> validEventType = new ArrayList<>();

//                                Map<Long, List<Long>> composedFilter = new HashMap<>();
                                for (String eventTypeIdString : eventTypeIds) {
                                    List<String> splitted = Arrays.asList(StringUtils.split(eventTypeIdString, "$"));
                                    // gestione metriche avanzate
                                    Long eventTypeId = Long.valueOf(splitted.get(0));
                                    if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                                        validEventType.add(exists(BaseController.getAdvancedMetrics().get(eventTypeId).getCode()));
                                    } else {
                                        // gestione evento multiplo con tag
                                        if (splitted.size() > 1) {
                                            List<Long> tagTypeIds = new ArrayList<>();
                                            for (String tagTypeId : StringUtils.split(splitted.get(1), "-")) {
                                                tagTypeIds.add(Long.valueOf(tagTypeId));
                                            }
                                            Collections.sort(tagTypeIds);
                                            validEventType.add(and(eq("eventTypeId", eventTypeId), eq("tagTypeId", StringUtils.join(tagTypeIds, "|"))));
                                        } else {
//                                            boolean hasTags = false;
//                                            for (String tmpEventTypeIdString : eventTypeIds) {
//                                                if (tmpEventTypeIdString.startsWith(eventTypeIdString + "-")) {
//                                                    hasTags = true;
//                                                    break;
//                                                }
//                                            }
//                                            if (!hasTags) {
//                                                validEventType.add(and(eq("eventTypeId", eventTypeId), eq("tagTypeId", null)));
//                                            }
                                            validEventType.add(and(eq("eventTypeId", eventTypeId), eq("tagTypeId", null)));
                                        }
                                    }
                                }

//                                for (Long eventTypeId : composedFilter.keySet()) {
//                                    List<Long> tagTypeIds = composedFilter.get(eventTypeId);
//                                    Collections.sort(tagTypeIds);
//                                    validEventType.add(and(eq("eventTypeId", eventTypeId), eq("tagTypeId", StringUtils.join(tagTypeIds, "|"))));
//                                }
                                filters.add(and(or(validEventType)));
                            }
                        } else {
                            if (params.get(param) == null) {
                                filters.add(eq(param, null));
                            } else {
                                String value = params.get(param).toString();
                                if (StringUtils.contains(value, "/")) {
                                    // array di valori in and
                                    List<String> values = Arrays.asList(StringUtils.split(value, "/"));
                                    for (String tmpValue : values) {
                                        filters.add(eq(param, Long.valueOf(tmpValue)));
                                    }
                                } else if (StringUtils.contains(value, "|")) {
                                    // array di valori in or
                                    List<String> values = Arrays.asList(StringUtils.split(value, "|"));
                                    List<Long> longValues = new ArrayList<>();
                                    for (String tmpValue : values) {
                                        longValues.add(Long.valueOf(tmpValue));
                                    }
                                    filters.add(in(param, longValues));
                                } else {
                                    if (StringUtils.equals(param, "eventTypeId")) {
                                        // gestione metriche avanzate
                                        Long eventTypeId = Long.valueOf(value);
                                        if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                                            filters.add(exists(BaseController.getAdvancedMetrics().get(eventTypeId).getCode()));
                                        } else {
                                            filters.add(eq(param, Long.valueOf(value)));
                                        }
                                    } else {
                                        filters.add(eq(param, Long.valueOf(value)));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        if (!params.containsKey("tagTypeId") && !params.containsKey("eventTypeIds")) {
            filters.add(eq("tagTypeId", null));
        }
    }

    public static boolean hasAdvancedMetrics(Map<String, Object> params) {
        for (String param : params.keySet()) {
            if (!StringUtils.equals(param, "type") && !StringUtils.equals(param, "seasonId") && !StringUtils.equals(param, "competitionId")
                    && !StringUtils.equals(param, "half") && !StringUtils.equals(param, "zone") && !StringUtils.equals(param, "channel") && !StringUtils.equals(param, "area")
                    && !StringUtils.equals(param, "totalType")) {
                if (StringUtils.equals(param, "eventTypeId")) {
                    // gestione metriche avanzate
                    String value = params.get(param).toString();
                    Long eventTypeId = Long.valueOf(value);
                    if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                        return true;
                    }
                } else if (StringUtils.equals(param, "eventTypeIds")) {
                    List<String> eventTypeIds = Arrays.asList(StringUtils.split(params.get(param).toString(), "|"));
                    if (eventTypeIds != null && !eventTypeIds.isEmpty()) {

                        for (String eventTypeIdString : eventTypeIds) {
                            // gestione metriche avanzate
                            Long eventTypeId = Long.valueOf(eventTypeIdString);
                            if (BaseController.getAdvancedMetrics().containsKey(eventTypeId)) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    public static void adjustParamsByZoneId(Map<String, Object> params, String zoneId) {
        params.remove("half");
        params.remove("zone");
        params.remove("channel");
        params.remove("area");
        if (StringUtils.isNotBlank(zoneId)) {
            switch (zoneId) {
                case "1":
                    params.put("half", "2");
                    break;
                case "2":
                    params.put("half", "1");
                    break;
                case "3":
                    params.put("zone", "1");
                    break;
                case "4":
                    params.put("zone", "2");
                    break;
                case "5":
                    params.put("zone", "3");
                    break;
                case "6":
                    params.put("channel", "1");
                    break;
                case "7":
                    params.put("channel", "2");
                    break;
                case "8":
                    params.put("channel", "3");
                    break;
                case "9":
                    params.put("channel", "4");
                    break;
                case "10":
                    params.put("channel", "5");
                    break;
                case "11":
                    params.put("zone", "1");
                    params.put("channel", "1");
                    break;
                case "12":
                    params.put("zone", "2");
                    params.put("channel", "1");
                    break;
                case "13":
                    params.put("zone", "3");
                    params.put("channel", "1");
                    break;
                case "14":
                    params.put("zone", "1");
                    params.put("channel", "2");
                    break;
                case "15":
                    params.put("zone", "2");
                    params.put("channel", "2");
                    break;
                case "16":
                    params.put("zone", "3");
                    params.put("channel", "2");
                    break;
                case "17":
                    params.put("zone", "1");
                    params.put("channel", "3");
                    break;
                case "18":
                    params.put("zone", "2");
                    params.put("channel", "3");
                    break;
                case "19":
                    params.put("zone", "3");
                    params.put("channel", "3");
                    break;
                case "20":
                    params.put("zone", "1");
                    params.put("channel", "4");
                    break;
                case "21":
                    params.put("zone", "2");
                    params.put("channel", "4");
                    break;
                case "22":
                    params.put("zone", "3");
                    params.put("channel", "4");
                    break;
                case "23":
                    params.put("zone", "1");
                    params.put("channel", "5");
                    break;
                case "24":
                    params.put("zone", "2");
                    params.put("channel", "5");
                    break;
                case "25":
                    params.put("zone", "3");
                    params.put("channel", "5");
                    break;
                case "26":
                    params.put("area", "1");
                    break;
                case "27":
                    params.put("area", "2");
                    break;
                case "28":
                    params.put("area", "3");
                    break;
                case "29":
                    params.put("area", "4");
                    break;
            }
        }
    }

    public static void saveSession(HttpSession session) {
        List<String> requestToExclude = new ArrayList<>();
        String collection = "user_sessions";

        try {
            if (session.getAttribute(GlobalHelper.kBeanUtente) != null) {
                User curUser = (User) session.getAttribute(GlobalHelper.kBeanUtente);

                if (curUser != null) {
                    Map<String, Integer> sessionRequestList = (Map<String, Integer>) session.getAttribute(GlobalHelper.kRequestsList);
                    if (sessionRequestList != null && !sessionRequestList.isEmpty()) {
                        // in teoria se non è null di base non è mai vuota, ma non si sa mai...
                        Document document = new Document();
                        document.append("userId", curUser.getId());
                        document.append("groupsetId", curUser.getGroupsetId());
                        if (curUser.getGroupset() != null && StringUtils.isNotBlank(curUser.getGroupset().getName())) {
                            document.append("groupsetName", curUser.getGroupset().getName());
                        }
                        document.append("creation", new Date());

                        // informazioni sulla sessione
                        document.append("sessionCreation", new Date(session.getCreationTime()));
                        long durationInMillis = new Date().getTime() - new Date(session.getCreationTime()).getTime();
                        long second = (durationInMillis / 1000) % 60;
                        long minute = (durationInMillis / (1000 * 60)) % 60;
                        long hour = (durationInMillis / (1000 * 60 * 60)) % 24;
                        document.append("sessionDuration", String.format("%02d:%02d:%02d", hour, minute, second));

                        int totalCharts = 0;
                        for (String requestLink : sessionRequestList.keySet()) {
                            // correggo tutti quei link tipo
                            // /sicstv/user/home_htm;jsessionid=377DECB4E455ABD8F7BAF491B8757C7D
                            String correctRequestLink = StringUtils.substringBefore(requestLink, ";");
                            if (!requestToExclude.contains(correctRequestLink) && !correctRequestLink.contains("/getFilters") && !correctRequestLink.contains("/getChartData")) {
                                document.append(correctRequestLink.replaceAll("\\.", "_"), sessionRequestList.get(correctRequestLink));
                            }
                            if (correctRequestLink.contains("/getChartData")) {
                                totalCharts += sessionRequestList.get(correctRequestLink);
                            }
                        }
                        if (totalCharts > 0) {
                            document.append("totalCharts", totalCharts);
                        }

                        insertDocument(collection, document);
                        // evito che inserisca 2 volte lo stesso documento, quindi per sicurezza
                        // butto via il log così se anche dovesse rientrare qua non inserisce nulla
                        session.removeAttribute(GlobalHelper.kRequestsList);
                    } else {
                        logger.log(Level.WARNING, "MongoHelper.saveSession: skipping session because sessionRequestList is empty ({0})", curUser.getId());
                    }

                    // tengo solo i dati degli ultimi 6 mesi (per evitare che diventi logdata 2.0)
                    getMongoDatabase().getCollection(collection).deleteMany(lt("creation", DateUtils.addDays(new Date(), -180)));
                } else {
                    logger.log(Level.WARNING, "MongoHelper.saveSession: curUser is null");
                }
            }
        } catch (Exception ex) {
            logger.log(Level.WARNING, "MongoHelper.saveSession: exception occured. {0}", ex.getLocalizedMessage());
        }
    }

    public static List<Document> getLogs(List<Long> userIds) {
        List<Bson> filters = new ArrayList<>();
        int skip = 0, limit = 0;
        Bson sort = descending("sessionCreation");

        String collectionName = "user_sessions";
        ClassModel<Document> model = ClassModel.builder(Document.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        if (userIds != null) {
            filters.add(in("userId", userIds));
        }
        Date sixMonthsAgo = new Date();
        sixMonthsAgo = DateUtils.addMonths(sixMonthsAgo, -6);
        filters.add(gte("sessionCreation", sixMonthsAgo));

        MongoCollection<Document> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), Document.class).withCodecRegistry(pojoCodecRegistry);
        List<Document> rows;
        if (filters.isEmpty()) {
            rows = collection
                    .find()
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<Document>());
        } else {
            rows = collection
                    .find(and(filters))
                    .skip(skip)
                    .limit(limit)
                    .sort(sort)
                    .into(new ArrayList<Document>());
        }

        return rows;
    }

    public static List<Integer> getCompetitionGroups(Long seasonId, Long competitionId) {
        String collectionName = "";

        // nuova gestione stagioni
        boolean validRequest = false;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        collectionName = "team";
                        collectionName += "_" + groupedSeason.getSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        collectionName = "team";
                        collectionName += "_" + groupedSeason.getNonSolarId();
                        collectionName += "_" + competitionId;
                        validRequest = true;
                    }
                }
            }
        } else if (BaseController.getSeasons().containsKey(seasonId)) {
            // probabilmente ho già passato la stagione giusta
            collectionName = "team";
            collectionName += "_" + seasonId;
            collectionName += "_" + competitionId;
            validRequest = true;
        }
        if (!validRequest) {
            return null;
        }

        ClassModel<DocumentFilter> model = ClassModel.builder(DocumentFilter.class).build();
        CodecProvider pojoCodecProvider = PojoCodecProvider.builder().automatic(true).register(model).build();
        CodecRegistry pojoCodecRegistry = fromRegistries(getDefaultCodecRegistry(), fromProviders(pojoCodecProvider));

        MongoCollection<DocumentFilter> collection = getMongoDatabase().getCollection(StringUtils.lowerCase(collectionName), DocumentFilter.class).withCodecRegistry(pojoCodecRegistry);
        return collection.distinct("groupId", Integer.class).into(new ArrayList<Integer>());
    }

    /*

        END QUERIES

     */
}
