package sics.helper;

import org.bson.codecs.Codec;
import org.bson.codecs.configuration.CodecProvider;
import org.bson.codecs.configuration.CodecRegistry;
import sics.domain.SimilarityDocumentRow;

/**
 *
 * <AUTHOR>
 */
public class SimilarityDocumentRowCodecProvider implements CodecProvider {
    
    private final CodecProvider pojoCodecProvider;
    
    public SimilarityDocumentRowCodecProvider(CodecProvider pojoCodecProvider) {
        this.pojoCodecProvider = pojoCodecProvider;
    }
    
    @Override
    public <T> Codec<T> get(Class<T> clazz, CodecRegistry registry) {
        if (clazz.equals(SimilarityDocumentRow.class)) {
            Codec<SimilarityDocumentRow> defaultCodec = (Codec<SimilarityDocumentRow>) pojoCodecProvider.get(clazz, registry);
            return (Codec<T>) new SimilarityDocumentRowCodec(defaultCodec, registry);
        }
        return null;
    }
}