package sics.helper;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.catalina.connector.ClientAbortException;
import org.springframework.web.bind.MissingServletRequestParameterException;

import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.SimpleMappingExceptionResolver;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.User;

public class ExceptionHandlerHelper extends SimpleMappingExceptionResolver {

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler, Exception exception) {

        if (!(exception instanceof MissingServletRequestParameterException) && !(exception instanceof ClientAbortException)) {
            this.logException(exception, request);
            exception.printStackTrace();
            StackTraceElement[] st = exception.getStackTrace();
            String StackTrace = "";
            for (int i = 0; i < st.length; i++) {
                StackTraceElement stackTraceElement = st[i];
                StackTrace += stackTraceElement.toString() + "\n";
            }
            
            HttpSession session = request.getSession(false);
            User curUser = null;
            if (session != null) {
                Object userToCast = session.getAttribute(GlobalHelper.kBeanUtente);
                if (userToCast != null) {
                    curUser = (User) userToCast;
                }
            }
            
            String contenuto = exception.getClass().toString() + ": " + exception.getMessage() + "\n" + "\n" + StackTrace;
            contenuto += "<br><br>Link: " + request.getRequestURL() + "?" + request.getQueryString();
            if (curUser != null) {
                contenuto += "<br>User: ";
                if (curUser.getFirstName() != null) {
                    contenuto += curUser.getFirstName() + " ";
                }
                if (curUser.getLastName() != null) {
                    contenuto += curUser.getLastName();
                }
            }
            MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.port", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.user", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.pwd", RequestContextUtils.getLocale(request)));
            mail.sendMail(SpringApplicationContextHelper.getMessage("email.from", RequestContextUtils.getLocale(request)), "<EMAIL>", null, null, null, null, "Errore sics dataanalytics", contenuto, null, "");
            
        }
        return new ModelAndView("redirect:/auth/404.htm");
//        return super.resolveException(request, response, handler, exception);
    }
}
