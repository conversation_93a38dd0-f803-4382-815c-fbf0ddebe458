package sics.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import sics.controller.BaseController;
import sics.domain.Competition;
import sics.domain.Country;
import sics.domain.DocumentRow;
import sics.domain.DocumentFilter;
import sics.domain.Season;
import sics.helper.MongoHelper;
import sics.helper.SpringApplicationContextHelper;

/**
 *
 * <AUTHOR>
 */
public class Utils {

    public static void calculateP90(Collection<DocumentRow> result, List<DocumentRow> baseRows, Map<String, Object> params) {
        List<Long> fixtureIds = new ArrayList<>();
        for (DocumentRow row : baseRows) {
            if (row.getFixtureId() != null && !fixtureIds.contains(row.getFixtureId())) {
                fixtureIds.add(row.getFixtureId());
            }
        }
        params.put("fixtureIds", StringUtils.join(fixtureIds, ","));
        Map<Long, Map<Long, Long>> totalMinutes = MongoHelper.getTotalMinutes(params);
        for (DocumentRow row : result) {
//            if (row.getFixtureId() != null && totalMinutes.containsKey(row.getFixtureId()) && totalMinutes.get(row.getFixtureId()) > 0) {
//                row.setTotalP90(90D * row.getTotal() / (totalMinutes.get(row.getFixtureId()) / 2));
//            } else {
//                row.setTotalP90(0D);
//            }

//            if (row.getTeamId() != null && totalMinutes.containsKey(row.getTeamId()) && totalMinutes.get(row.getTeamId()) > 0) {
//                row.setTotalP90(90D * row.getTotal() / totalMinutes.get(row.getTeamId()));
//            } else if (row.getPlayerId() != null && totalMinutes.containsKey(row.getPlayerId()) && totalMinutes.get(row.getPlayerId()) > 0) {
//                row.setTotalP90(90D * row.getTotal() / totalMinutes.get(row.getPlayerId()));
//            } else {
//                row.setTotalP90(0D);
//            }
            row.setTotalP90(0D);
            row.setTotalP90(Math.round(row.getTotalP90() * 100) / 100D);
        }
        params.remove("fixtureIds");
    }

    public static void calculateP90V2(Collection<DocumentRow> rows, Map<String, Object> params) {
        Map<Long, Map<Long, Long>> totalMinutes = getTotalMinutes(rows, params);
        for (DocumentRow row : rows) {
            if ((totalMinutes.containsKey(row.getTeamId()) && totalMinutes.get(row.getTeamId()).containsKey(row.getPlayerId())) || row.getPlaytime() != null) {
                if (row.getPlaytime() != null) {
                    row.setTotalP90(90D * row.getTotal() / row.getPlaytime());
                } else {
                    if (totalMinutes.get(row.getTeamId()).get(row.getPlayerId()) > 0) {
                        row.setPlaytime(totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));
                        row.setTotalP90(90D * row.getTotal() / totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));
                    } else {
                        row.setTotalP90(0D);
                    }
                }
            } else {
                row.setTotalP90(0D);
            }
            row.setTotalP90(Math.round(row.getTotalP90() * 100) / 100D);
        }
    }

    public static void calculate100Touches(Collection<DocumentRow> rows, Map<String, Object> params) {
        Map<Long, Map<Long, Long>> totalTouches = getTotalTouches(rows, params);
        for (DocumentRow row : rows) {
            if ((totalTouches.containsKey(row.getTeamId()) && totalTouches.get(row.getTeamId()).containsKey(row.getPlayerId())) || row.getTouches() != null) {
                if (row.getTouches() != null) {
                    row.setTotal100Touches(100D * row.getTotal() / row.getTouches());
                } else {
                    if (totalTouches.get(row.getTeamId()).get(row.getPlayerId()) > 0) {
                        row.setTouches(totalTouches.get(row.getTeamId()).get(row.getPlayerId()));
                        row.setTotal100Touches(100D * row.getTotal() / totalTouches.get(row.getTeamId()).get(row.getPlayerId()));
                    } else {
                        row.setTotal100Touches(0D);
                    }
                }
            } else {
                row.setTotal100Touches(0D);
            }
            row.setTotal100Touches(Math.round(row.getTotal100Touches() * 100) / 100D);
        }
    }

    private static Map<Long, Map<Long, Long>> getTotalMinutes(Collection<DocumentRow> rows, Map<String, Object> params) {
        List<Long> teamIds = new ArrayList<>();
        List<Long> playerIds = new ArrayList<>();
        for (DocumentRow row : rows) {
            if (row.getPlaytime() == null) { // altrimenti significa che ho già calcolato il playtime da altre parti
                if (row.getTeamId() != null && !teamIds.contains(row.getTeamId())) {
                    teamIds.add(row.getTeamId());
                }
                if (row.getPlayerId() != null && !playerIds.contains(row.getPlayerId())) {
                    playerIds.add(row.getPlayerId());
                }
            }
        }

        Map<String, Object> paramsClone = new HashMap<>(params);
        paramsClone.remove("teamIds");
        paramsClone.remove("playerIds");
        if (!teamIds.isEmpty()) {
            paramsClone.put("teamIds", StringUtils.join(teamIds, ","));
        }
        if (!playerIds.isEmpty()) {
            paramsClone.put("playerIds", StringUtils.join(playerIds, ","));
        }
        if (!paramsClone.containsKey("competitionIds")) {
            paramsClone.put("competitionIds", paramsClone.get("competitionId").toString());
        }
        return MongoHelper.getTotalMinutes(paramsClone);
    }

    private static Map<Long, Map<Long, Long>> getTotalTouches(Collection<DocumentRow> rows, Map<String, Object> params) {
        List<Long> teamIds = new ArrayList<>();
        List<Long> playerIds = new ArrayList<>();
        for (DocumentRow row : rows) {
            if (row.getTouches() == null) { // altrimenti significa che ho già calcolato il playtime da altre parti
                if (row.getTeamId() != null && !teamIds.contains(row.getTeamId())) {
                    teamIds.add(row.getTeamId());
                }
                if (row.getPlayerId() != null && !playerIds.contains(row.getPlayerId())) {
                    playerIds.add(row.getPlayerId());
                }
            }
        }

        Map<String, Object> paramsClone = new HashMap<>(params);
        paramsClone.remove("teamIds");
        paramsClone.remove("playerIds");
        if (!teamIds.isEmpty()) {
            paramsClone.put("teamIds", StringUtils.join(teamIds, ","));
        }
        if (!playerIds.isEmpty()) {
            paramsClone.put("playerIds", StringUtils.join(playerIds, ","));
        }
        if (!paramsClone.containsKey("competitionIds")) {
            paramsClone.put("competitionIds", paramsClone.get("competitionId").toString());
        }
        return MongoHelper.getTotalTouches(paramsClone);
    }

    public static void calculateAverage(Collection<DocumentRow> rows, Map<String, Object> params) {
        List<Long> teamIds = new ArrayList<>();
        List<Long> playerIds = new ArrayList<>();
        for (DocumentRow row : rows) {
            if (row.getTeamId() != null && !teamIds.contains(row.getTeamId())) {
                teamIds.add(row.getTeamId());
            }
            if (row.getPlayerId() != null && !playerIds.contains(row.getPlayerId())) {
                playerIds.add(row.getPlayerId());
            }
        }

        Map<String, Object> paramsClone = new HashMap<>(params);
        paramsClone.remove("teamIds");
        paramsClone.remove("playerIds");
        if (!teamIds.isEmpty()) {
            paramsClone.put("teamIds", StringUtils.join(teamIds, ","));
        }
        if (!playerIds.isEmpty()) {
            paramsClone.put("playerIds", StringUtils.join(playerIds, ","));
        }
        if (!paramsClone.containsKey("competitionIds")) {
            paramsClone.put("competitionIds", paramsClone.get("competitionId").toString());
        }
        Map<Long, Map<Long, Long>> totalFixtures = MongoHelper.getTotalFixture(paramsClone);
        for (DocumentRow row : rows) {
            if (totalFixtures.containsKey(row.getTeamId()) && totalFixtures.get(row.getTeamId()).containsKey(row.getPlayerId())) {
                if (totalFixtures.get(row.getTeamId()).get(row.getPlayerId()) > 0) {
                    row.setTotalAverage(row.getTotal() / totalFixtures.get(row.getTeamId()).get(row.getPlayerId()));
                } else {
                    row.setTotalAverage(0D);
                }
            } else {
                row.setTotalAverage(0D);
            }
            row.setTotalAverage(Math.round(row.getTotalAverage() * 100) / 100D);
        }
    }

    public static List<Long> getSortedCompetitionIds(List<Long> competitionIds, final String language) {
        List<Long> sortedCompetitionIds = new ArrayList<>();

        Map<Country, List<Competition>> groupedByCountry = new HashMap<>();
        for (Long competitionId : competitionIds) {
            Competition competition = BaseController.getCompetitions().get(competitionId);
            if (competition != null) {
                Country country = BaseController.getCountries().get(competition.getCountryId());
                groupedByCountry.putIfAbsent(country, new ArrayList<Competition>());
                groupedByCountry.get(country).add(competition);
            }
        }
        // ordino le competizioni
        for (Country country : groupedByCountry.keySet()) {
            Collections.sort(groupedByCountry.get(country), new Comparator<Competition>() {
                @Override
                public int compare(Competition o1, Competition o2) {
                    return o1.getOrder().compareTo(o2.getOrder());
                }
            });
        }
        // ordino i country
        List<Country> sortedCountries = new ArrayList<>(groupedByCountry.keySet());
        Collections.sort(sortedCountries, new Comparator<Country>() {
            @Override
            public int compare(Country o1, Country o2) {
                return o1.getName(language).compareTo(o2.getName(language));
            }
        });

        for (Country country : sortedCountries) {
            for (Country tmpCountry : groupedByCountry.keySet()) {
                if (country.equals(tmpCountry)) {
                    for (Competition competition : groupedByCountry.get(tmpCountry)) {
                        sortedCompetitionIds.add(competition.getId());
                    }
                    break;
                }
            }
        }

        if (sortedCompetitionIds.size() != competitionIds.size()) {
            // evito di passare meno competizioni delle iniziali
            return competitionIds;
        }

        return sortedCompetitionIds;
    }

    public static void mergeFilters(DocumentFilter origin, DocumentFilter child) {
        if (origin.getTeamIds() == null) {
            origin.setTeamIds(new ArrayList<Long>());
        }
        if (child.getTeamId() != null) {
            if (!origin.getTeamIds().contains(child.getTeamId())) {
                origin.getTeamIds().add(child.getTeamId());
            }
        }
        if (child.getTeamIds() != null && !child.getTeamIds().isEmpty()) {
            for (Long element : child.getTeamIds()) {
                if (!origin.getTeamIds().contains(element)) {
                    origin.getTeamIds().add(element);
                }
            }
        }

        if (origin.getPlayerIds() == null) {
            origin.setPlayerIds(new ArrayList<Long>());
        }
        if (child.getPlayerId() != null) {
            if (!origin.getPlayerIds().contains(child.getPlayerId())) {
                origin.getPlayerIds().add(child.getPlayerId());
            }
        }
        if (child.getPlayerIds() != null && !child.getPlayerIds().isEmpty()) {
            for (Long element : child.getPlayerIds()) {
                if (!origin.getPlayerIds().contains(element)) {
                    origin.getPlayerIds().add(element);
                }
            }
        }

        if (origin.getEventTypeIds() == null) {
            origin.setEventTypeIds(new ArrayList<Long>());
        }
        if (child.getEventTypeId() != null) {
            if (!origin.getEventTypeIds().contains(child.getEventTypeId())) {
                origin.getEventTypeIds().add(child.getEventTypeId());
            }
        }
        if (child.getEventTypeIds() != null && !child.getEventTypeIds().isEmpty()) {
            for (Long element : child.getEventTypeIds()) {
                if (!origin.getEventTypeIds().contains(element)) {
                    origin.getEventTypeIds().add(element);
                }
            }
        }

        if (origin.getFixtureIds() == null) {
            origin.setFixtureIds(new ArrayList<Long>());
        }
        if (child.getFixtureIds() != null && !child.getFixtureIds().isEmpty()) {
            for (Long element : child.getFixtureIds()) {
                if (!origin.getFixtureIds().contains(element)) {
                    origin.getFixtureIds().add(element);
                }
            }
        }

        if (origin.getTagTypeIds() == null) {
            origin.setTagTypeIds(new ArrayList<Long>());
        }
        if (child.getTagTypeIds() != null && !child.getTagTypeIds().isEmpty()) {
            for (Long element : child.getTagTypeIds()) {
                if (!origin.getTagTypeIds().contains(element)) {
                    origin.getTagTypeIds().add(element);
                }
            }
        }

        if (origin.getMatchDays() == null) {
            origin.setMatchDays(new ArrayList<Long>());
        }
        if (child.getMatchDays() != null && !child.getMatchDays().isEmpty()) {
            for (Long element : child.getMatchDays()) {
                if (!origin.getMatchDays().contains(element)) {
                    origin.getMatchDays().add(element);
                }
            }
        }

        if (origin.getCountryIds() == null) {
            origin.setCountryIds(new ArrayList<Long>());
        }
        if (child.getCountryIds() != null && !child.getCountryIds().isEmpty()) {
            for (Long element : child.getCountryIds()) {
                if (!origin.getCountryIds().contains(element)) {
                    origin.getCountryIds().add(element);
                }
            }
        }

        if (origin.getPositionIds() == null) {
            origin.setPositionIds(new ArrayList<Long>());
        }
        if (child.getPositionIds() != null && !child.getPositionIds().isEmpty()) {
            for (Long element : child.getPositionIds()) {
                if (!origin.getPositionIds().contains(element)) {
                    origin.getPositionIds().add(element);
                }
            }
        }

        if (origin.getPositionDetailIds() == null) {
            origin.setPositionDetailIds(new ArrayList<Long>());
        }
        if (child.getPositionDetailIds() != null && !child.getPositionDetailIds().isEmpty()) {
            for (Long element : child.getPositionDetailIds()) {
                if (!origin.getPositionDetailIds().contains(element)) {
                    origin.getPositionDetailIds().add(element);
                }
            }
        }

        if (origin.getFootIds() == null) {
            origin.setFootIds(new ArrayList<Long>());
        }
        if (child.getFootIds() != null && !child.getFootIds().isEmpty()) {
            for (Long element : child.getFootIds()) {
                if (!origin.getFootIds().contains(element)) {
                    origin.getFootIds().add(element);
                }
            }
        }

        if (origin.getBornYears() == null) {
            origin.setBornYears(new ArrayList<Integer>());
        }
        if (child.getBornYears() != null && !child.getBornYears().isEmpty()) {
            for (Integer element : child.getBornYears()) {
                if (!origin.getBornYears().contains(element)) {
                    origin.getBornYears().add(element);
                }
            }
        }

        if (origin.getHomeModules() == null) {
            origin.setHomeModules(new ArrayList<String>());
        }
        if (child.getHomeModules() != null && !child.getHomeModules().isEmpty()) {
            for (String element : child.getHomeModules()) {
                if (!origin.getHomeModules().contains(element)) {
                    origin.getHomeModules().add(element);
                }
            }
        }

        if (origin.getAwayModules() == null) {
            origin.setAwayModules(new ArrayList<String>());
        }
        if (child.getAwayModules() != null && !child.getAwayModules().isEmpty()) {
            for (String element : child.getAwayModules()) {
                if (!origin.getAwayModules().contains(element)) {
                    origin.getAwayModules().add(element);
                }
            }
        }

        if (origin.getFields() == null) {
            origin.setFields(new ArrayList<String>());
        }
        if (child.getFields() != null && !child.getFields().isEmpty()) {
            for (String element : child.getFields()) {
                if (!origin.getFields().contains(element)) {
                    origin.getFields().add(element);
                }
            }
        }
    }

    public static boolean isOpposite(Long eventTypeId, String tagTypeIds) {
        boolean isOpposite = false;
        if (StringUtils.isNotBlank(tagTypeIds)) {
            if (StringUtils.contains(tagTypeIds, "/")) {
                for (String tmpTagTypeId : StringUtils.split(tagTypeIds, "/")) {
                    if (NumberUtils.isParsable(tagTypeIds)) {
                        Long tagTypeId = Long.valueOf(tmpTagTypeId);
                        if (BaseController.getOppositeTagTypes().containsKey(tagTypeId)) {
                            return true;
                        }
                    }
                }
            } else if (StringUtils.contains(tagTypeIds, "|")) {
                for (String tmpTagTypeId : StringUtils.split(tagTypeIds, "|")) {
                    if (NumberUtils.isParsable(tagTypeIds)) {
                        Long tagTypeId = Long.valueOf(tmpTagTypeId);
                        if (BaseController.getOppositeTagTypes().containsKey(tagTypeId)) {
                            return true;
                        }
                    }
                }
            } else if (NumberUtils.isParsable(tagTypeIds)) {
                Long tagTypeId = Long.valueOf(tagTypeIds);
                if (BaseController.getOppositeTagTypes().containsKey(tagTypeId)) {
                    return true;
                }
            }
        } else if (eventTypeId != null) {
            isOpposite = BaseController.getOppositeEventTypes().containsKey(eventTypeId);
        }

        return isOpposite;
    }

    public static int getMinPlaytimeAmount(Double maxPlaytime) {
        if (maxPlaytime == null) {
            return 70;
        } else {
            if (maxPlaytime >= 1000D) {
                return 300;
            } else if (maxPlaytime >= 500D) {
                return 150;
            } else {
                return 70;
            }
        }
    }

    public static String getTagTypeName(DocumentRow row, String language) {
        String name = null;

        if (StringUtils.isNotBlank(row.getTagTypeId())) {
            List<String> tagTypeIds = Arrays.asList(StringUtils.split(row.getTagTypeId(), "|"));
            List<String> names = new ArrayList<>();
            for (String tagType : tagTypeIds) {
                Long tagTypeId = Long.valueOf(tagType);
                if (BaseController.getTagTypes().containsKey(tagTypeId)) {
                    names.add(BaseController.getTagTypes().get(tagTypeId).getDesc(language));
                }
            }
            name = StringUtils.join(names, ", ");
        }

        return name;
    }

    public static String getZoneIdName(String zoneId, String language) {
        return getZoneIdName(zoneId, false, language);
    }

    public static String getZoneIdName(String zoneId, Boolean abbreviation, String language) {
        String name = null;

        Locale locale = Locale.forLanguageTag(language);

        if (StringUtils.isNotBlank(zoneId)) {
            switch (zoneId) {
                case "1":
                    name = SpringApplicationContextHelper.getMessage("positional.defensive.half" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "2":
                    name = SpringApplicationContextHelper.getMessage("positional.offensive.half" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "3":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "4":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "5":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "6":
                    name = SpringApplicationContextHelper.getMessage("positional.channel.one" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "7":
                    name = SpringApplicationContextHelper.getMessage("positional.channel.two" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "8":
                    name = SpringApplicationContextHelper.getMessage("positional.channel.three" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "9":
                    name = SpringApplicationContextHelper.getMessage("positional.channel.four" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "10":
                    name = SpringApplicationContextHelper.getMessage("positional.channel.five" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "11":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one.channel.one" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "12":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two.channel.one" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "13":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three.channel.one" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "14":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one.channel.two" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "15":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two.channel.two" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "16":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three.channel.two" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "17":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one.channel.three" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "18":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two.channel.three" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "19":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three.channel.three" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "20":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one.channel.four" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "21":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two.channel.four" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "22":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three.channel.four" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "23":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.one.channel.five" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "24":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.two.channel.five" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "25":
                    name = SpringApplicationContextHelper.getMessage("positional.zone.three.channel.five" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "26":
                    name = SpringApplicationContextHelper.getMessage("positional.penalty.area" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "27":
                    name = SpringApplicationContextHelper.getMessage("positional.small.area" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "28":
                    name = SpringApplicationContextHelper.getMessage("positional.opponent.penalty.area" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
                case "29":
                    name = SpringApplicationContextHelper.getMessage("positional.opponent.small.area" + (BooleanUtils.isTrue(abbreviation) ? ".abb" : ""), locale);
                    break;
            }
        }

        return name;
    }

    public static long adjustRowsForPlaytime(List<DocumentRow> rows, Map<String, Object> params, List<Long> playersSelected) {
        // Team, Player, Minutaggio
        Map<Long, Map<Long, Long>> totalMinutes = getTotalMinutes(rows, params);

        // trovo il massimo
        Long maxMinutes = null;
        for (Long teamId : totalMinutes.keySet()) {
            for (Long playtime : totalMinutes.get(teamId).values()) {
                if (playtime != null && (maxMinutes == null || playtime > maxMinutes)) {
                    maxMinutes = playtime;
                }
            }
        }

        long minMinutes = 0;
        if (maxMinutes != null) {
            // verifico valore massimo
            if (maxMinutes > 3000) {
                maxMinutes = 3000L;
            }

            // calcolo i minuti minimi
            minMinutes = Math.round((maxMinutes / 5D) - (Math.pow(maxMinutes, 2) / 30000D));

            List<DocumentRow> excludedRows = new ArrayList<>();
            for (DocumentRow row : rows) {
                if ((totalMinutes.containsKey(row.getTeamId()) && totalMinutes.get(row.getTeamId()).containsKey(row.getPlayerId())) || row.getPlaytime() != null) {
                    if (row.getPlaytime() != null) {
                        if (row.getPlaytime() < minMinutes) {
                            if (playersSelected == null || !playersSelected.contains(row.getPlayerId())) {
                                excludedRows.add(row);
                            }
                        }
                    } else {
                        if (totalMinutes.get(row.getTeamId()).get(row.getPlayerId()) < minMinutes) {
                            // setto il valore di playtime per il calcolo del P90
                            row.setPlaytime(totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));
                            if (playersSelected == null || !playersSelected.contains(row.getPlayerId())) {
                                excludedRows.add(row);
                            }
                        } else {
                            // riga valida, setto il valore di playtime per il calcolo del P90
                            row.setPlaytime(totalMinutes.get(row.getTeamId()).get(row.getPlayerId()));
                        }
                    }
                } else if (playersSelected == null || !playersSelected.contains(row.getPlayerId())) {
                    excludedRows.add(row);
                }
            }

            rows.removeAll(excludedRows);
        }

        return minMinutes;
    }

    public static Long getCorrectSeasonId(Long seasonId, Long competitionId) {
        Long correctSeasonId = seasonId;
        if (BaseController.getGroupedSeasons().containsKey(seasonId)) {
            Season groupedSeason = BaseController.getGroupedSeasons().get(seasonId);
            if (BaseController.getCompetitions().containsKey(competitionId)) {
                boolean isSolar = org.apache.commons.lang3.BooleanUtils.isTrue(BaseController.getCompetitions().get(competitionId).getSolarSeason());
                if (isSolar) {
                    if (groupedSeason.getSolarId() != null) {
                        correctSeasonId = groupedSeason.getSolarId();
                    }
                } else {
                    if (groupedSeason.getNonSolarId() != null) {
                        correctSeasonId = groupedSeason.getNonSolarId();
                    }
                }
            }
        }

        return correctSeasonId;
    }
}
