package sics.websocket;

import com.google.gson.JsonObject;
import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.OnClose;
import javax.websocket.OnError;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.ServerEndpoint;
import org.apache.commons.lang.StringUtils;
import sics.helper.GlobalHelper;
import sics.helper.MailHelper;
import sics.helper.SpringApplicationContextHelper;

/**
 *
 * <AUTHOR>
 */
@ServerEndpoint("/messages") // Define the WebSocket endpoint
public class WebSocketEndpoint {

    // Set to hold all active sessions
    private static final Map<Long, Session> clients = new ConcurrentHashMap<>();

    @OnOpen
    public void onOpen(Session session) {
        boolean valid = false;
        // Extract query parameters
        Map<String, List<String>> params = session.getRequestParameterMap();
        if (params.containsKey("userId") && !params.get("userId").isEmpty()) {
            String userId = params.get("userId").get(0);
            if (StringUtils.isNotBlank(userId)) {
                session.getUserProperties().put("userId", Long.valueOf(userId));
                clients.put(Long.valueOf(userId), session);
                session.setMaxIdleTimeout(0); // Disable idle timeout for this session
                System.out.println("New connection: " + session.getId());
                valid = true;
            }
        }
        Locale locale = Locale.ENGLISH;
        if (params.containsKey("language") && !params.get("language").isEmpty()) {
            String language = params.get("language").get(0);
            if (StringUtils.isNotBlank(language)) {
                session.getUserProperties().put("language", language);
                locale = Locale.forLanguageTag(language);
            }
        }

        if (!valid) {
            send(session, MessageType.WARNING, SpringApplicationContextHelper.getMessage("websocket.invalid.session", locale));
            try {
                session.close();
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) {
        if (StringUtils.isNotBlank(message)) {
            if (message.startsWith("0x00A")) {
                // Errore dalla pagina, mando mail
                message = message.replace("0x00A", "");
                String emailFrom = SpringApplicationContextHelper.getMessage("email.from", Locale.ENGLISH);
                String strHello = SpringApplicationContextHelper.getMessage("email.hello", Locale.ENGLISH);

                String strSubject = "SICS Data Analytics - Page Error";
                String strContent = message;
                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.port", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.user", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.pwd", Locale.ENGLISH));
                mail.sendMail(emailFrom, "<EMAIL>", null, "<EMAIL>", "SICS Data Analytics", strHello, strSubject, strContent, null, "");
            }
        }
        System.out.println("Received message: " + message);
    }

    @OnClose
    public void onClose(Session session) {
        boolean manualCheck = true;
        try {
            if (session.getUserProperties() != null && !session.getUserProperties().isEmpty()) {
                Object userIdObject = session.getUserProperties().get("userId");
                if (userIdObject != null) {
                    Long userId = (Long) userIdObject;
                    clients.remove(userId);
                    manualCheck = false;
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }

        try {
            if (manualCheck) {
                Long userId = null;
                for (Entry<Long, Session> entry : clients.entrySet()) {
                    if (entry.getValue().equals(session)) {
                        userId = entry.getKey();
                        break;
                    }
                }
                if (userId != null) {
                    clients.remove(userId); // Remove session from the set
                } else if (!clients.isEmpty()) {
                    String emailFrom = SpringApplicationContextHelper.getMessage("email.from", Locale.ENGLISH);
                    String strHello = SpringApplicationContextHelper.getMessage("email.hello", Locale.ENGLISH);

                    String strSubject = "SICS Data Analytics - WebSocket";
                    String strContent = "Unable to remove the session from the map. Error is: 404 Not Found (" + session.getId() + ")";
                    MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.port", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.user", Locale.ENGLISH), SpringApplicationContextHelper.getMessage("email.smtp.pwd", Locale.ENGLISH));
                    mail.sendMail(emailFrom, "<EMAIL>", null, "<EMAIL>", "SICS Data Analytics", strHello, strSubject, strContent, null, "");
                }
            }
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        System.out.println("Connection closed: " + session.getId());
    }

    @OnError
    public void onError(Session session, Throwable throwable) {
        System.err.println("Error on connection: " + session.getId() + " - " + throwable.getMessage());
    }

    // Method to broadcast messages to all connected clients
    public static void broadcast(MessageType type, String content) {
        for (Session client : clients.values()) {
            send(client, type, content);
        }
    }

    public static void send(Session client, MessageType type, String content) {
        if (client.isOpen()) {
            try {
                JsonObject message = new JsonObject();
                message.addProperty("type", type.toString());
                message.addProperty("content", content);

                client.getBasicRemote().sendText(message.toString()); // Send message to client
            } catch (IOException ex) {
                GlobalHelper.reportError(ex);
            }
        }
    }

    public static Map<Long, Session> getClients() {
        return clients;
    }
}
