package sics.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Level;
import java.util.logging.Logger;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.servlet.support.RequestContextUtils;
import sics.domain.Settings;

import sics.helper.GlobalHelper;
import sics.domain.User;
import sics.domain.UserPermission;
import sics.helper.MailHelper;
import sics.helper.SpringApplicationContextHelper;
import sics.listener.SessionListener;
import sics.service.UserService;

public class UserAuthenticationFilter extends UsernamePasswordAuthenticationFilter {

    @Override
    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (!request.getMethod().equals("POST")) {
            throw new AuthenticationServiceException("Authentication method not supported: " + request.getMethod());
        }

        String username = obtainUsername(request);
        if (username == null) {
            username = "";
        }
        String password = obtainPassword(request);
        if (password == null) {
            password = "";
        }

        username = username.trim();
        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(username, password);
        setDetails(request, authRequest);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);
        UserService uService = new UserService();
        User usr = uService.getUserByMail(username);
        if (usr != null && usr.isExpired()) {
            throw new AuthenticationServiceException(GlobalHelper.kActionLicenseExpired + ": " + usr.getExpirationDateString());
        } else if (usr != null) {
            initializeSession(session, req, usr);
            //SessionListener.addSession(usr, session);
        } else {
            throw new AuthenticationServiceException(GlobalHelper.kActionLoginError);
        }

        return this.getAuthenticationManager().authenticate(authRequest);
        // DOPO QUESTA FUNZIONE SOLITAMENTE VENIVA FATTO IL "DISPOSE" DELLA SESSIONE ATTUALE
        // QUESTO ERA DOVUTO AL FATTO CHE VENIVA APPLICATO UNA DETERMINATA STRATEGIA DI AUTENTICAZIONE
        // CHE PER IMPEDIRE ATTACCHI DI TIPO "SESSION FIXATION" CREAVA UNA NUOVA SESSIONE
        // QUESTO ERA CONFIGURATO NEL FILE "applicationContext-Security.xml" CON LA SEGUENTE CLASSE
        // org.springframework.security.web.authentication.session.SessionFixationProtectionStrategy
        // IL CODICE E' ANCORA COMMENTATO (10/01/2024)
    }

    @Override
    protected void successfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, Authentication authResult)
            throws IOException, ServletException {

        super.successfulAuthentication(request, response, authResult);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);
        if (session.getAttribute(GlobalHelper.kBeanUtente) != null) {
            sics.domain.User curUser = (sics.domain.User) session.getAttribute(GlobalHelper.kBeanUtente);
            SessionListener.addSession(curUser, session);
            GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginCorrect, curUser.getEmail(), curUser.getPassword(), curUser.getId());

            if (session.getAttribute(GlobalHelper.kMaxSessionDuration) == null) {
                session.setAttribute(GlobalHelper.kMaxSessionDuration, session.getMaxInactiveInterval());
            }
            UserService uService = new UserService();
            curUser.setUserPermissions(uService.getUserPermissions(curUser.getId()));
            if (curUser.getUserPermissions() == null || curUser.getUserPermissions().isEmpty()) {
                // scrivo la riga su da_user_permission solo quando hanno accesso solo ad alcune tab
                UserPermission permission = new UserPermission();
                permission.setUserId(curUser.getId());
                permission.setTab(999);
                permission.setRole(0);
                List<UserPermission> permissions = Arrays.asList(permission);
                curUser.setUserPermissions(permissions);
            }
        } else if (session.getAttribute(GlobalHelper.kBeanUtente) == null) {
            UserService uService = new UserService();
            User userAuth = (User) authResult.getPrincipal();
            if (userAuth != null && userAuth.getEmail() != null) {
                User user = uService.getUserByMail(userAuth.getEmail());

                GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginCorrect, userAuth.getEmail(), userAuth.getPassword(), user.getId());
                Logger.getLogger(UserAuthenticationFilter.class.getName()).log(Level.INFO, "INGRESSO OK {0}", userAuth.getEmail());
                initializeSession(session, req, user);
                SessionListener.addSession(user, session);
            }
        }

    }

    @Override
    protected void unsuccessfulAuthentication(HttpServletRequest request,
            HttpServletResponse response, AuthenticationException failed)
            throws IOException, ServletException {

        super.unsuccessfulAuthentication(request, response, failed);

        HttpServletRequest req = (HttpServletRequest) request;
        HttpSession session = req.getSession(true);

        try {
            String[] splitExceptionMsg = failed.getMessage().split(":");
            if (splitExceptionMsg[0].trim().equals(GlobalHelper.kActionLicenseExpired)) {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionLicenseExpired, obtainUsername(request), obtainPassword(request), null);
            } else {
                GlobalHelper.writeLogData(session, GlobalHelper.kActionLoginError, obtainUsername(request), obtainPassword(request), null);
            }
        } catch (Exception e) {
            System.out.println("Exception during unsuccessfulAuthentication: " + e.getMessage());
            session.invalidate();
        }
    }

    public static void initializeSession(HttpSession session, HttpServletRequest request, User user) {
        UserService uService = new UserService();

        session.setAttribute(GlobalHelper.kBeanUtente, user);
//        if (session.getAttribute(GlobalHelper.kBeanLanguage) == null) {
//            String strLocale = request.getLocale().getLanguage().toLowerCase();
//            if (strLocale.equalsIgnoreCase("it")) {
//                session.setAttribute(GlobalHelper.kBeanLanguage, "it");
//            } else {
//                session.setAttribute(GlobalHelper.kBeanLanguage, "en");
//            }
//        }

        String language = RequestContextUtils.getLocale(request).getLanguage().toLowerCase();
        if (language.equalsIgnoreCase("it")) {
            language = "";
        } else {
            language = "_en";
        }

        user.setGroupset(uService.getGroupset(user.getGroupsetId()));
        user.setAllowedCompetitions(uService.getUserCompetitions(user.getGroupsetId()));
        if (user.getAllowedCompetitions() != null && !user.getAllowedCompetitions().isEmpty()) {
            user.setAllowedTeams(uService.getTeamByGroupsetId(user.getAllowedCompetitions()));
            user.setAllowedPlayers(uService.getPlayersByGroupsetId(user.getAllowedCompetitions()));
        } else {
            try {
                String contenuto = "Groupset #" + user.getGroupsetId() + " doesn't have access to any competition. (" + StringUtils.defaultIfEmpty(user.getEmail(), "N.A.") + ")";
                MailHelper mail = new MailHelper(SpringApplicationContextHelper.getMessage("email.smtp", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.port", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.user", RequestContextUtils.getLocale(request)), SpringApplicationContextHelper.getMessage("email.smtp.pwd", RequestContextUtils.getLocale(request)));
                mail.sendMail(SpringApplicationContextHelper.getMessage("email.from", RequestContextUtils.getLocale(request)), "<EMAIL>", null, null, null, null, "SDA - Groupset Configuration", contenuto, null, "");
            } catch (Exception ex) {
                GlobalHelper.reportError(ex);
            }

            // ritorno messaggio di errore e chiudo la sessione
            SessionListener.destroySession(session, user.getId());
            throw new AuthenticationServiceException(GlobalHelper.kActionLoginNoCompetition);
        }
        // nuova gestione prodotto "Soccerment Team Report"
        user.setSoccermentTeamReportExpirationDate(uService.getUserSoccermentTeamReport(user.getId()));

        if (StringUtils.isBlank(user.getTvLanguage())) {
            // in teoria sempre
            Settings userSettings = uService.getSettingsByUserId(user.getId());
            if (userSettings != null) {
                user.setTvLanguage(userSettings.getTvLanguage());
                if (userSettings.getPreferredTeamId() != null) {
                    user.setPreferredTeamId(userSettings.getPreferredTeamId());
                }
            } else {
                user.setTvLanguage(StringUtils.defaultIfEmpty(user.getTvLanguage(), "en"));
            }
            session.setAttribute(GlobalHelper.kBeanLanguage, StringUtils.defaultIfEmpty(user.getTvLanguage(), "en"));
        }
    }
}
