package sics.filter;

import java.io.IOException;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;

/**
 *
 * <AUTHOR>
 */

public class CustomAuthenticationSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    private final String HOME_PAGE = "/user/home.htm";
    
    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, org.springframework.security.core.Authentication authentication) throws IOException, ServletException {
        SavedRequest savedRequest = new HttpSessionRequestCache().getRequest(request, response);
        
        if (savedRequest != null && savedRequest.getRedirectUrl().contains("isValidSession")) {
            getRedirectStrategy().sendRedirect(request, response, HOME_PAGE);
        } else {
            super.onAuthenticationSuccess(request, response, authentication);
        }
    }
}
