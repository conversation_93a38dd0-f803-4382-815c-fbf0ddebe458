package sics.filter;

import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import org.apache.commons.lang.StringUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import sics.helper.GlobalHelper;

/**
 *
 * <AUTHOR>
 */
public class CustomAuthenticationFailureHandler implements AuthenticationFailureHandler {

    private String defaultFailureUrl;

    @Override
    public void onAuthenticationFailure(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) throws IOException, ServletException {
        try {
            String errorMessage = exception.getMessage();
            if (errorMessage != null) {
                HttpSession session = request.getSession(true);

                List<String> splitted = Arrays.asList(StringUtils.split(errorMessage, ":"));
                if (!splitted.isEmpty()) {
                    if (splitted.get(0).equalsIgnoreCase(GlobalHelper.kActionLicenseExpired)) {
                        session.setAttribute("expiredAccess", splitted.get(1).trim());

                        if (defaultFailureUrl == null) {
                            defaultFailureUrl = "/sicsdataanalytics/auth/login.htm";
                        }
                        response.sendRedirect(defaultFailureUrl);
                    } else if (splitted.get(0).equalsIgnoreCase(GlobalHelper.kActionLoginNoCompetition)) {
                        session.setAttribute("permissionError", "true");

                        if (defaultFailureUrl == null) {
                            defaultFailureUrl = "/sicsdataanalytics/auth/login.htm";
                        }
                        response.sendRedirect(defaultFailureUrl);
                    } else {
                        response.sendRedirect(defaultFailureUrl + "?login_error=true");
                    }
                }
            }
        } catch (IOException ex) {
            System.out.println("Exception during onAuthenticationFailure: " + ex.getMessage());
            response.sendRedirect(defaultFailureUrl + "?login_error=true");
        }
    }

    public String getDefaultFailureUrl() {
        return defaultFailureUrl;
    }

    public void setDefaultFailureUrl(String defaultFailureUrl) {
        this.defaultFailureUrl = defaultFailureUrl;
    }
}
