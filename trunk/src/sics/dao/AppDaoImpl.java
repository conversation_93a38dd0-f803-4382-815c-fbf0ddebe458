package sics.dao;

import sics.domain.*;
import java.sql.SQLException;
import java.util.*;

import org.apache.commons.lang.BooleanUtils;
import org.mybatis.spring.support.SqlSessionDaoSupport;
import org.apache.commons.lang.StringUtils;
import sics.utils.Utils;

public class AppDaoImpl extends SqlSessionDaoSupport implements AppDAO {

    @Override
    public User getUser(Long id) throws SQLException {
        List<User> items = getSqlSession().selectList("User.getUser", id);
        return (items != null && !items.isEmpty()) ? items.get(0) : null;
    }

    @Override
    public List<User> getUsers() throws SQLException {
        return getSqlSession().selectList("User.getUsers");
    }

    @Override
    public User getUserByMail(String mail) throws SQLException {
        List<User> items = getSqlSession().selectList("User.getUserByMail", mail);
        User user = (items != null && !items.isEmpty()) ? items.get(0) : null;
        return user;
    }

    @Override
    public List<User> getUsers(List<Long> ids) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", StringUtils.join(ids, ","));
        return getSqlSession().selectList("User.getUsers", map);
    }
    
    @Override
    public List<User> getUsersWithDetails(List<Long> ids) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("ids", StringUtils.join(ids, ","));
        return getSqlSession().selectList("User.getUsersWithDetails", map);
    }

    @Override
    public List<User> getUsersByGroupset(Long groupsetId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("groupsetId", groupsetId);
        return getSqlSession().selectList("User.getUsersByGroupset", map);
    }

    @Override
    public List<UserPermission> getUserPermissions(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        return getSqlSession().selectList("User.getUserPermissions", map);
    }

    @Override
    public List<Competition> getUserCompetitions(Long groupsetId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("groupsetId", groupsetId);
        return getSqlSession().selectList("Competition.getUserCompetitions", map);
    }

    @Override
    public void saveUser(User item) throws SQLException {
        if (item.getId() == null) {
            getSqlSession().insert("User.addUser", item);
        } else {
            getSqlSession().update("User.updateUser", item);
        }
    }

    @Override
    public void saveLogData(LogData item) throws SQLException {
        getSqlSession().insert("LogData.addLogData", item);
    }

    @Override
    public List<Team> getTeams() throws SQLException {
        return getSqlSession().selectList("Team.getTeams");
    }

    @Override
    public List<Team> getTeams(List<Long> teamIds) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamIds", StringUtils.join(teamIds, ","));
        return getSqlSession().selectList("Team.getTeams", map);
    }

    @Override
    public List<Season> getSeasons() throws SQLException {
        return getSqlSession().selectList("Season.getSeasons");
    }

    @Override
    public List<Competition> getCompetitions() throws SQLException {
        return getSqlSession().selectList("Competition.getCompetitions");
    }

    @Override
    public List<Competition> getCompetitions(List<Long> competitionIds) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("competitionIds", StringUtils.join(competitionIds, ","));
        return getSqlSession().selectList("Competition.getCompetitions", map);
    }

    @Override
    public Competition getCompetition(Long competitionId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("competitionIds", competitionId);
        return (Competition) getSqlSession().selectOne("Competition.getCompetitions", map);
    }

    @Override
    public List<Competition> getInternationalCompetitions() throws SQLException {
        return getSqlSession().selectList("Competition.getInternationalCompetitions");
    }

    @Override
    public Settings getSettingsByUserId(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);

        return (Settings) getSqlSession().selectOne("Settings.getSettingsByUserId", map);
    }

    @Override
    public List<Long> getTeamByGroupsetId(List<Competition> allowedCompetitions) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        return getSqlSession().selectList("Team.getTeamByGroupsetId", map);
    }

    @Override
    public List<Long> getPlayersByGroupsetId(List<Competition> allowedCompetitions) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        return getSqlSession().selectList("Player.getPlayersByGroupsetId", map);
    }

    @Override
    public List<EventType> getEventTypes() throws SQLException {
        return getSqlSession().selectList("EventType.getEventTypes");
    }

    @Override
    public List<EventType> getOppositeEventTypes() throws SQLException {
        return getSqlSession().selectList("EventType.getOppositeEventTypes");
    }

    @Override
    public List<TagType> getTagTypes() throws SQLException {
        return getSqlSession().selectList("TagType.getTagTypes");
    }

    @Override
    public List<Player> getPlayers() throws SQLException {
        return getSqlSession().selectList("Player.getAllPlayers");
    }

    @Override
    public Player getPlayer(Long playerId, Boolean isMatchStudioQuery) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("isMatchStudioQuery", BooleanUtils.isTrue(isMatchStudioQuery));
        return (Player) getSqlSession().selectOne("Player.getPlayer", map);
    }

    @Override
    public List<FixtureDetails> getFixtureDetails() throws SQLException {
        return getSqlSession().selectList("Fixture.getFixtureDetails");
    }

    @Override
    public List<Country> getCountries() throws SQLException {
        return getSqlSession().selectList("Country.getCountries");
    }

    @Override
    public List<Foot> getFoots() throws SQLException {
        return getSqlSession().selectList("Foot.getFoots");
    }

    @Override
    public List<Position> getPositions() throws SQLException {
        return getSqlSession().selectList("Position.getPositions");
    }

    @Override
    public List<Position> getPositionDetails() throws SQLException {
        return getSqlSession().selectList("Position.getPositionDetails");
    }

    @Override
    public List<TeamPlayer> getTeamPlayers() throws SQLException {
        List<Competition> groupsetCompetitions = getUserCompetitions(2L);
        if (groupsetCompetitions != null && !groupsetCompetitions.isEmpty()) {
            List<Long> teamIds = getTeamByGroupsetId(groupsetCompetitions);
            if (teamIds != null && !teamIds.isEmpty()) {
                TreeMap map = new TreeMap();
                map.put("teamIds", StringUtils.join(teamIds, ","));
                return getSqlSession().selectList("TeamPlayer.getTeamPlayers", map);
            }
        }

        return new ArrayList<>();
    }

    @Override
    public List<AdvancedMetric> getAdvancedMetrics() throws SQLException {
        List<AdvancedMetric> metrics = getSqlSession().selectList("StatsType.getAdvancedMetrics");

        for (AdvancedMetric metric : metrics) {
            if (Long.compare(metric.getId(), 1000L) == 0) {
                metric.setCode("ipo");
            } else if (Long.compare(metric.getId(), 1001L) == 0) {
                metric.setCode("ird");
            } else if (Long.compare(metric.getId(), 1002L) == 0) {
                metric.setCode("possesso");
            } else if (Long.compare(metric.getId(), 1003L) == 0) {
                metric.setCode("xG");
            } else if (Long.compare(metric.getId(), 1004L) == 0) {
                metric.setCode("xGot");
            } else if (Long.compare(metric.getId(), 1005L) == 0) {
                metric.setCode("xA");
            } else if (Long.compare(metric.getId(), 1006L) == 0) {
                metric.setCode("xAe");
            } else if (Long.compare(metric.getId(), 1007L) == 0) {
                metric.setCode("xOvA");
            } else if (Long.compare(metric.getId(), 1008L) == 0) {
                metric.setCode("cxg");
            } else if (Long.compare(metric.getId(), 1009L) == 0) {
                metric.setCode("shotQuality");
            } else if (Long.compare(metric.getId(), 1010L) == 0) {
                metric.setCode("fieldTilt");
            } else if (Long.compare(metric.getId(), 1011L) == 0) {
                metric.setCode("ppda");
            } else if (Long.compare(metric.getId(), 1012L) == 0) {
                metric.setCode("axGot");
            } else if (Long.compare(metric.getId(), 1013L) == 0) {
                metric.setCode("fxGot");
            } else if (Long.compare(metric.getId(), 1014L) == 0) {
                metric.setCode("gkGp");
            } else if (Long.compare(metric.getId(), 1015L) == 0) {
                metric.setCode("npxG");
            } else if (Long.compare(metric.getId(), 1016L) == 0) {
                metric.setCode("opxG");
            } else if (Long.compare(metric.getId(), 1017L) == 0) {
                metric.setCode("oidi");
            } else if (Long.compare(metric.getId(), 1L) == 0) {
                metric.setCode("matchMinutes");
            } else if (Long.compare(metric.getId(), 268L) == 0) {
                metric.setCode("touches");
            } else if (Long.compare(metric.getId(), 1018L) == 0) {
                metric.setCode("xT");
            } else if (Long.compare(metric.getId(), 1019L) == 0) {
                metric.setCode("xTPass");
            } else if (Long.compare(metric.getId(), 1020L) == 0) {
                metric.setCode("xTCond");
            } else if (Long.compare(metric.getId(), 1021L) == 0) {
                metric.setCode("iA");
            } else if (Long.compare(metric.getId(), 269L) == 0) {
                metric.setCode("touchesMcd");
            } else if (Long.compare(metric.getId(), 270L) == 0) {
                metric.setCode("touchesMco");
            } else if (Long.compare(metric.getId(), 273L) == 0) {
                metric.setCode("touchesTo");
            } else if (Long.compare(metric.getId(), 275L) == 0) {
                metric.setCode("touchesAa");
            } else if (Long.compare(metric.getId(), 286L) == 0) {
                metric.setCode("touchesAz");
            } else if (Long.compare(metric.getId(), 276L) == 0) {
                metric.setCode("touchesPfa");
            } else if (Long.compare(metric.getId(), 295L) == 0) {
                metric.setCode("touchesSuff");
            } else if (Long.compare(metric.getId(), 296L) == 0) {
                metric.setCode("touchesSuffMca");
            } else if (Long.compare(metric.getId(), 297L) == 0) {
                metric.setCode("touchesSuffMc");
            } else if (Long.compare(metric.getId(), 300L) == 0) {
                metric.setCode("touchesSuffTd");
            } else if (Long.compare(metric.getId(), 302L) == 0) {
                metric.setCode("touchesSuffA");
            } else if (Long.compare(metric.getId(), 304L) == 0) {
                metric.setCode("touchesSuffAz");
            }
        }

        return metrics;
    }

    @Override
    public Fixture getFixtureById(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);

        return (Fixture) getSqlSession().selectOne("Fixture.getFixtureById", map);
    }

    @Override
    public List<Fixture> getFixtureByIds(List<Long> fixtureIds) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureIds", StringUtils.join(fixtureIds, ","));

        return getSqlSession().selectList("Fixture.getFixtureByIds", map);
    }

    @Override
    public List<Event> getEventsByParams(Map<String, Object> params) throws SQLException {
        TreeMap map = new TreeMap();

        List<String> from = new ArrayList<>();
        List<String> where = new ArrayList<>();

        if (params != null && !params.isEmpty()) {
            String tagTypeIdParam = null;
            if (params.containsKey("seasonId")) {
                Long seasonId = Long.valueOf(params.get("seasonId").toString());
                if (params.containsKey("competitionId")) {
                    Long competitionId = Long.valueOf(params.get("competitionId").toString());
                    seasonId = Utils.getCorrectSeasonId(seasonId, competitionId);
                }
                where.add("fixture.season_id = " + seasonId);
            }
            if (params.containsKey("competitionId")) {
                where.add("fixture.competition_id = " + params.get("competitionId"));
            }
            if (params.containsKey("teamId")) {
                where.add("event.team_id = " + params.get("teamId"));
            }
            if (params.containsKey("playerId")) {
                where.add("event_player.player_id IN(" + StringUtils.replace(params.get("playerId").toString(), "/", ",") + ")");
            }
            if (params.containsKey("eventTypeId")) {
                where.add("event.event_type_id = " + params.get("eventTypeId"));
            }
            if (params.containsKey("tagTypeId")) {
                tagTypeIdParam = StringUtils.replace(params.get("tagTypeId").toString(), "|", ",");
                where.add("event_tag.tag_type_id IN(" + tagTypeIdParam + ")");
            }
            if (params.containsKey("fixtureId")) {
                where.add("event.fixture_id IN(" + StringUtils.replace(params.get("fixtureId").toString(), "/", ",") + ")");
            }
            if (params.containsKey("isHomeTeam")) {
                boolean isHomeTeam = BooleanUtils.toBoolean(params.get("isHomeTeam").toString());
                where.add("event.team_id = " + (isHomeTeam ? "fixture.home_team_id" : "fixture.away_team_id"));
            }
            if (params.containsKey("matchdayFrom")) {
                where.add("fixture.matchday >= " + params.get("matchdayFrom"));
            }
            if (params.containsKey("matchdayTo")) {
                where.add("fixture.matchday <= " + params.get("matchdayTo"));
            }
            if (params.containsKey("homeModule")) {
                where.add("fixture.home_module = " + params.get("homeModule"));
            }
            if (params.containsKey("awayModule")) {
                where.add("fixture.away_module = " + params.get("awayModule"));
            }

            String query = StringUtils.join(from, " ");
            query += " WHERE " + StringUtils.join(where, " AND ");
            query += " GROUP BY event.id";
            map.put("query", query);

            if (!from.isEmpty() || !where.isEmpty()) {
                List<Event> events = getSqlSession().selectList("Event.getEventsByParams", map);

                // nel caso in cui ho più di 1 tag devo controllare a mano (per farlo da query dovrei fare select della select)
                // quindi preferisco controllare qua
                if (events != null && !events.isEmpty()) {
                    if (params.containsKey("tagTypeId")) {
                        if (StringUtils.contains(tagTypeIdParam, ",")) {
                            List<String> queryTagTypes = Arrays.asList(StringUtils.split(tagTypeIdParam, ","));
                            List<Event> eventsToRemove = new ArrayList<>();
                            for (Event event : events) {
                                int tagFoundAmount = 0;
                                if (StringUtils.isNotBlank(event.getTagTypeList())) {
                                    // devo controllare a mano perchè non è detto che l'ordinamento sia lo stesso che ho io
                                    List<String> eventTagTypes = Arrays.asList(StringUtils.split(event.getTagTypeList(), ","));
                                    for (String tagType : queryTagTypes) {
                                        for (String eventTagType : eventTagTypes) {
                                            if (StringUtils.equals(tagType, eventTagType)) {
                                                tagFoundAmount++;
                                                break;
                                            }
                                        }
                                    }
                                }
                                if (tagFoundAmount != queryTagTypes.size()) {
                                    eventsToRemove.add(event);
                                }
                            }

                            events.removeAll(eventsToRemove);
                        }
                    }
                }

                return events;
            } else {
                return null;
            }
        } else {
            return null;
        }
    }

    @Override
    public List<FixturePlayer> getFixturePlayersByParams(Map<String, Object> params) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("seasonId", params.get("seasonId"));
        map.put("competitionId", params.get("competitionId"));
        map.put("teamId", params.get("teamId"));
        map.put("positionId", params.get("positionId"));
        map.put("fixtureId", params.get("fixtureId"));
        map.put("matchdayFrom", params.get("matchdayFrom"));
        map.put("matchdayTo", params.get("matchdayTo"));

        return getSqlSession().selectList("FixturePlayer.getFixturePlayersByParams", map);
    }

    @Override
    public void addSettings(Settings settings) throws SQLException {
        if (StringUtils.isBlank(settings.getTvLanguage())) {
            // per sicurezza
            settings.setTvLanguage("en");
        }
        getSqlSession().insert("Settings.addSettings", settings);
    }

    @Override
    public void updateSettings(Settings settings) throws SQLException {
        if (StringUtils.isBlank(settings.getTvLanguage())) {
            // per sicurezza
            settings.setTvLanguage("en");
        }
        getSqlSession().update("Settings.updateSettings", settings);
    }

    @Override
    public List<Filter> getFiltersByPage(Long userId, Long groupsetId, String page) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        map.put("groupsetId", groupsetId);
        map.put("page", page);

        return getSqlSession().selectList("Filter.getFiltersByPage", map);
    }

    @Override
    public Long saveFilter(Filter filter) throws SQLException {
        return (Long) getSqlSession().selectOne("Filter.saveFilter", filter);
    }

    @Override
    public void updateFilter(Filter filter) throws SQLException {
        getSqlSession().update("Filter.updateFilter", filter);
    }

    @Override
    public void deleteFilter(Long filterId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("id", filterId);
        getSqlSession().delete("Filter.deleteFilter", map);
    }

    @Override
    public void saveFilterDetails(Filter filter) throws SQLException {
        TreeMap map = new TreeMap();

        if (filter.getFilters() != null && !filter.getFilters().isEmpty()) {
            List<String> values = new ArrayList<>();
            for (String key : filter.getFilters().keySet()) {
                values.add("(" + filter.getId() + ", '" + key + "', '" + filter.getFilters().get(key) + "')");
            }

            map.put("values", StringUtils.join(values, ","));
            getSqlSession().insert("Filter.saveFilterDetails", map);
        }
    }

    @Override
    public void deleteFilterDetails(Long filterId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("filterId", filterId);
        getSqlSession().delete("Filter.deleteFilterDetails", map);
    }

    @Override
    public List<TeamCareerItem> getTeamCareer(Long teamId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamId", teamId);
        return getSqlSession().selectList("Team.getTeamCareer", map);
    }

    @Override
    public List<Fixture> getTeamLastFixtures(Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return getSqlSession().selectList("Fixture.getTeamLastFixtures", map);
    }

    @Override
    public TeamData getTeamData(Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (TeamData) getSqlSession().selectOne("Team.getTeamData", map);
    }

    @Override
    public TeamPlayerData getTeamPlayerData(Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (TeamPlayerData) getSqlSession().selectOne("Team.getTeamPlayerData", map);
    }

    @Override
    public PlayerData getPlayerData(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerData", map);
    }

    @Override
    public PlayerData getPlayerWithMostPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerWithMostPasses", map);
    }

    @Override
    public PlayerData getPlayerWithMostReceivedPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerWithMostReceivedPasses", map);
    }

    @Override
    public List<PlayerData> getPlayerModules(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return getSqlSession().selectList("Player.getPlayerModules", map);
    }

    @Override
    public PlayerData getPlayerPlaytimeStats(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerPlaytimeStats", map);
    }

    @Override
    public PlayerData getPlayerStarterStats(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerStarterStats", map);
    }

    @Override
    public List<PlayerCareerItem> getPlayerCareer(Long playerId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        return getSqlSession().selectList("Player.getPlayerCareer", map);
    }

    @Override
    public List<Fixture> getPlayerLastFixtures(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        map.put("teamId", teamId);
        map.put("competitionId", competitionId);
        map.put("seasonId", seasonId);
        return getSqlSession().selectList("Fixture.getPlayerLastFixtures", map);
    }

    @Override
    public String getFixtureFileproject(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        return (String) getSqlSession().selectOne("Fixture.getFixtureFileproject", map);
    }

    @Override
    public Groupset getGroupset(Long groupsetId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("groupsetId", groupsetId);
        return (Groupset) getSqlSession().selectOne("Groupset.getGroupset", map);
    }

    @Override
    public Long getUserLastWeekMatchDownloadAmount(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        return (Long) getSqlSession().selectOne("LogData.getUserLastWeekMatchDownloadAmount", map);
    }

    @Override
    public void resetUserPassword(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        getSqlSession().update("User.resetUserPassword", map);
    }

    @Override
    public TeamPlayerLast getTeamLastData(Long teamId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("teamId", teamId);
        return (TeamPlayerLast) getSqlSession().selectOne("TeamPlayer.getTeamLastData", map);
    }

    @Override
    public TeamPlayerLast getPlayerLastData(Long playerId, List<Competition> allowedCompetitions) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        List<Long> competitionIds = new ArrayList<>();
        for (Competition competition : allowedCompetitions) {
            competitionIds.add(competition.getId());
        }
        map.put("competitionIds", StringUtils.join(competitionIds, ","));
        return (TeamPlayerLast) getSqlSession().selectOne("TeamPlayer.getPlayerLastData", map);
    }

    @Override
    public List<AdvancedMetric> getSimilarityMetrics() throws SQLException {
        return getSqlSession().selectList("StatsType.getSimilarityMetrics");
    }

    @Override
    public List<PlayerAgency> getPlayerAgencies() throws SQLException {
        return getSqlSession().selectList("PlayerAgency.getPlayerAgencies");
    }

    @Override
    public List<Group> getGroups() throws SQLException {
        return getSqlSession().selectList("Group.getGroups");
    }

    @Override
    public PlayerData getPlayerLastTeam(Long playerId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerId", playerId);
        return (PlayerData) getSqlSession().selectOne("Player.getPlayerLastTeam", map);
    }

    @Override
    public List<PlayerData> getPlayersLastTeam(List<Long> playerIds) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerIds", StringUtils.join(playerIds, ","));
        return getSqlSession().selectList("Player.getPlayersLastTeam", map);
    }

    @Override
    public List<PlayerData> getPlayersCompetitions(List<Long> playerIds) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("playerIds", StringUtils.join(playerIds, ","));
        return getSqlSession().selectList("Player.getPlayersCompetitions", map);
    }

    @Override
    public List<Long> getTeamCompetitions(Long teamId, Long seasonId, List<Competition> allowedCompetitions) throws SQLException {
        TreeMap map = new TreeMap();
        List<String> competitionIdList = new ArrayList<>();
        for (Competition comp : allowedCompetitions) {
            competitionIdList.add(comp.getId().toString());
        }

        map.put("allowedCompetitions", StringUtils.join(competitionIdList, ","));
        map.put("teamId", teamId);
        map.put("seasonId", seasonId);
        return getSqlSession().selectList("Competition.getTeamCompetitions", map);
    }

    @Override
    public FixtureDetails getFixtureDetails(Long fixtureId, Boolean isMatchStudioQuery) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        map.put("isMatchStudioQuery", BooleanUtils.isTrue(isMatchStudioQuery));
        return (FixtureDetails) getSqlSession().selectOne("Fixture.getFixtureDetailsByFixtureId", map);
    }

    @Override
    public List<User> getVtigerAgents() throws SQLException {
        return getSqlSession().selectList("User.getVtigerAgents");
    }

    @Override
    public List<User> getVtigerAgentClients(Long agentId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("agentId", agentId);
        return getSqlSession().selectList("User.getVtigerAgentClients", map);
    }

    @Override
    public List<AdvancedMetricValue> getMatchStudioTeamAdvancedMetrics(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        return getSqlSession().selectList("StatsType.getMatchStudioTeamAdvancedMetrics", map);
    }

    @Override
    public List<AdvancedMetricValue> getMatchStudioPlayerAdvancedMetrics(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        return getSqlSession().selectList("StatsType.getMatchStudioPlayerAdvancedMetrics", map);
    }

    @Override
    public List<IndexEvent> getFixtureIndexEvent(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        return getSqlSession().selectList("Index.getFixtureIndexEvent", map);
    }

    @Override
    public List<IndexEventType> getIndexEventType() throws SQLException {
        return getSqlSession().selectList("Index.getIndexEventType");
    }

    @Override
    public List<IndexTrend> getFixtureIndexTrend(Long fixtureId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("fixtureId", fixtureId);
        return getSqlSession().selectList("Index.getFixtureIndexTrend", map);
    }

    @Override
    public Date getUserSoccermentTeamReport(Long userId) throws SQLException {
        TreeMap map = new TreeMap();
        map.put("userId", userId);
        return (Date) getSqlSession().selectOne("User.getUserSoccermentTeamReport", map);
    }
}
