<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Group">

    <resultMap id="resultMapGroup"              type="Group">
        <result property="id"                   column="id"/>
        <result property="name"                 column="name"/>
        <result property="nameEn"               column="name_en"/>
        <result property="nameFr"               column="name_fr"/>
        <result property="nameEs"               column="name_es"/>
        <result property="nameRu"               column="name_ru"/>
        <result property="order"                column="order"/>
    </resultMap>
	
    <select id="getGroups" resultMap="resultMapGroup" parameterType="java.util.TreeMap">
        SELECT group.id, group.name, group.name_en, group.name_fr, group.name_es, group.name_ru, group.order
        FROM `group`
    </select>

</mapper>
