<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="PlayerAgency">

    <resultMap id="resultMapPlayerAgency"       type="PlayerAgency">
        <result property="id"                   column="id"/>
        <result property="name"                 column="name"/>
        <result property="street"               column="street"/>
        <result property="location"             column="location"/>
        <result property="countryId"            column="country_id"/>
        <result property="phone"                column="phone"/>
        <result property="email"                column="email"/>
        <result property="website"              column="website"/>
        <result property="photo"                column="photo"/>
    </resultMap>
	
    <select id="getPlayerAgencies" resultMap="resultMapPlayerAgency" parameterType="java.util.TreeMap">
        SELECT id, name, street, location, country_id, phone, email, website, photo
        FROM player_agent
    </select>

</mapper>
