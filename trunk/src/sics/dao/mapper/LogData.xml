<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="LogData">
    
    <resultMap id="resultMapLogData"    type="LogData">
        <id property="id"		column="id"/>
        <result property="loginname"	column="loginname"/>
        <result property="password"	column="password"/>
        <result property="userId"	column="user_id"/>
        <result property="date"		column="date"/>
        <result property="action"	column="action"/>
        <result property="video"	column="video_id"/>
        <result property="addressId"	column="address_id"/>
        <result property="sessionId"	column="session_id"/>
        <result property="application"	column="application"/>
        <result property="device"	column="device"/>
        
        <result property="amount"	column="amount"/>
    </resultMap>
        
    <select id="getActiveUsersByWeek" resultMap="resultMapLogData" parameterType="java.util.TreeMap">
        SELECT DATE(logdata.date) DATE, COUNT(DISTINCT user_id) amount
        FROM logdata
        WHERE application = 'sicstv' AND ACTION = 'LOGIN_CORRECT'
        GROUP BY YEAR(DATE(logdata.date)), WEEK(DATE(logdata.date))
        ORDER BY DATE(logdata.date)
    </select>
    
    <select id="getUserActionsByMonth" resultMap="resultMapLogData" parameterType="java.util.TreeMap">
        SELECT DATE(logdata.date) DATE, action, COUNT(logdata.id) amount
        FROM logdata
        WHERE application = 'sicstv' AND action IN ('LOGIN_CORRECT', 'NEW_PLAYLIST_TV', 'NEW_WATCHLIST', 'VIDEO_DOWNLOAD', 'VIDEO_STREAM', 'XML_JSON_DOWNLOAD')
        GROUP BY YEAR(DATE(logdata.date)), MONTH(DATE(logdata.date)), action
        ORDER BY DATE(logdata.date)
    </select>
    
    <select id="getUserActionsByParams" resultMap="resultMapLogData" parameterType="java.util.TreeMap">
        SELECT DATE(logdata.date) date, action, COUNT(logdata.id) amount
        FROM logdata
        <if test="groupsetId != null">
            INNER JOIN user ON user.id = logdata.user_id
        </if>
        WHERE application = 'sicstv'
        AND action IN ('LOGIN_CORRECT', 'NEW_PLAYLIST_TV', 'NEW_WATCHLIST', 'VIDEO_DOWNLOAD', 'VIDEO_STREAM', 'XML_JSON_DOWNLOAD')
        <if test="userId != null">
            AND user_id = ${userId}
        </if>
        <if test="groupsetId != null">
            AND user.groupset_id = ${groupsetId}
        </if>
        <![CDATA[
        AND DATE(logdata.date) >= STR_TO_DATE('${from}', '%Y-%m-%d %H:%i:%s')
        AND DATE(logdata.date) <= STR_TO_DATE('${to}', '%Y-%m-%d %H:%i:%s')
        ]]>
        <if test="groupType == 1">
            GROUP BY YEAR(DATE(logdata.date)), MONTH(DATE(logdata.date)), WEEK(DATE(logdata.date)), action
        </if>
        <if test="groupType == 2">
            GROUP BY YEAR(DATE(logdata.date)), MONTH(DATE(logdata.date)), action
        </if>
        <if test="groupType == 3">
            GROUP BY action
        </if>
        UNION 
        SELECT DATE(start_time) date, 'EXPORT' action, COUNT(export.id) amount
        FROM export
        <if test="groupsetId != null">
            INNER JOIN user ON user.id = export.user_id
        </if>
        WHERE 
        <if test="userId != null">
            user_id = ${userId}
        </if>
        <if test="groupsetId != null">
            user.groupset_id = ${groupsetId}
        </if>
        <![CDATA[
        AND DATE(start_time) >= STR_TO_DATE('${from}', '%Y-%m-%d %H:%i:%s')
        AND DATE(start_time) <= STR_TO_DATE('${to}', '%Y-%m-%d %H:%i:%s')
        ]]>
        <if test="groupType == 1">
            GROUP BY YEAR(DATE(start_time)), MONTH(DATE(start_time)), WEEK(DATE(start_time))
        </if>
        <if test="groupType == 2">
            GROUP BY YEAR(DATE(start_time)), MONTH(DATE(start_time))
        </if>
        <if test="groupType == 3">
            GROUP BY action
        </if>
        
        ORDER BY date
    </select>
    
    <select id="getLogdataUsers" resultMap="User.resultMapUser" parameterType="java.util.TreeMap">
        SELECT user.id, user.first_name, user.last_name, user.email, groupset.id groupset_id, groupset.name groupset_name
        FROM logdata
        INNER JOIN user ON user.id = logdata.user_id
        INNER JOIN groupset ON groupset.id = user.groupset_id
        WHERE logdata.application = 'sicstv'
        GROUP BY logdata.user_id
        ORDER BY user.first_name, user.last_name
    </select>
    
    <select id="getUserLastWeekMatchDownloadAmount" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT COUNT(logdata.id) amount
        FROM logdata
        WHERE application = 'sicsdataanalytics' AND action IN ('MATCH_REPORT_DOWNLOAD')
        AND user_id = ${userId}
        <![CDATA[
        AND date >= NOW() - INTERVAL 1 WEEK
        ]]>
    </select>
    
    <insert id="addLogData" parameterType="LogData">
        insert into logdata
        (loginname,password,user_id,video_id,action,address_id, session_id, application, device)
        values
        (#{loginname},#{password},#{userId},#{video},#{action},#{addressId},#{sessionId},#{application},#{device})
    </insert>
	
</mapper>
