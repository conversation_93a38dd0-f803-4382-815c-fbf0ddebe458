<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="User">

    <resultMap id="resultMapUser"           type="User">
        <id property="id"                   column="id"/>
        <result property="firstName"        column="first_name"/>
        <result property="lastName"         column="last_name"/>
        <result property="email"            column="email"/>
        <result property="password"         column="password"/>
        <result property="saveSeasonId"     column="last_season_id"/>
        <result property="groupsetId"       column="groupset_id"/>
        <result property="expirationDate"   column="expiration_date"/>
        <result property="language"         column="language"/>

        <result property="oraganizationName" column="organization_name"/>
    </resultMap>

    <resultMap id="resultMapUserPermission" type="UserPermission">
        <id property="id"                   column="id"/>
        <result property="userId"           column="user_id"/>
        <result property="tab"              column="tab"/>
        <result property="role"             column="role"/>
        <result property="comment"          column="comment"/>
    </resultMap>

    <sql id="select">
        SELECT user.id,
        user.first_name,
        user.last_name,
        user.email,
        user.webpassword password,
        user.last_season_id,
        user.groupset_id,
        pdata.datascadenza expiration_date,
        user.language
        FROM user
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.loginname
    </sql>

    <select id="getUser" resultMap="resultMapUser" parameterType="java.lang.Long">
        <include refid="select"/>
        WHERE user.id = #{value}
    </select>

    <select id="getUsers" resultMap="resultMapUser">
        <include refid="select"/>
        ORDER BY user.first_name, user.last_name
    </select>

    <select id="getUsersWithDetails" resultMap="resultMapUser">
        SELECT user.id,
        user.first_name,
        user.last_name,
        user.email,
        user.webpassword password,
        user.last_season_id,
        user.groupset_id,
        pdata.datascadenza expiration_date,
        user.language,
        vtiger_account.accountname organization_name
        FROM user
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.loginname
        INNER JOIN vtiger.vtiger_assets ON vtiger_assets.serialnumber = pdata.sn
        INNER JOIN vtiger.vtiger_contactdetails vtiger_contactdetails ON vtiger_contactdetails.contactid = vtiger_assets.contact
        LEFT JOIN vtiger.vtiger_account vtiger_account ON vtiger_account.accountid = vtiger_contactdetails.accountid
        ORDER BY user.first_name, user.last_name
    </select>

    <select id="getUsersByGroupset" resultMap="resultMapUser">
        <include refid="select"/>
        WHERE user.groupset_id = ${groupsetId}
    </select>

    <select id="getUserByMail" resultMap="resultMapUser" parameterType="java.lang.String">
        <include refid="select"/>
        WHERE user.webusername = #{value} AND user.loginname LIKE '%610'
    </select>

    <select id="getUserPermissions" resultMap="resultMapUserPermission" parameterType="java.lang.Long">
        SELECT id, user_id, tab, role, comment
        FROM da_user_permission
        WHERE user_id = ${userId}
    </select>

    <select id="getGroupsetUserList" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT user.id, user.first_name, user.last_name, user.email, groupset.id groupset_id, groupset.name groupset_name
        FROM user
        INNER JOIN groupset ON groupset.id = user.groupset_id
        WHERE user.groupset_id = ${groupsetId}
    </select>

    <select id="resetUserPassword" parameterType="java.util.TreeMap">
        CALL RESET_USER_PASSWORD(${userId})
    </select>

    <update id="updateUser" parameterType="User">
        UPDATE user
        SET webpassword = #{password}
        WHERE id = #{id}
    </update>

    <select id="getVtigerAgents" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT DISTINCT vtiger.vtiger_users.id, vtiger.vtiger_users.first_name, vtiger.vtiger_users.last_name
        FROM vtiger.vtiger_assets
        INNER JOIN vtiger.vtiger_crmentity ON vtiger.vtiger_crmentity.crmid = vtiger.vtiger_assets.assetsid
        INNER JOIN vtiger.vtiger_users ON vtiger.vtiger_users.id = vtiger.vtiger_crmentity.smownerid
        INNER JOIN vtiger.vtiger_user2role ON vtiger.vtiger_user2role.userid = vtiger.vtiger_users.id
        WHERE vtiger.vtiger_assets.product = 22321 AND (vtiger.vtiger_user2role.roleid = "H12" OR vtiger.vtiger_users.id IN (6, 20))
        ORDER BY vtiger.vtiger_users.first_name, vtiger.vtiger_users.last_name
    </select>

    <select id="getVtigerAgentClients" resultMap="resultMapUser" parameterType="java.util.TreeMap">
        SELECT DISTINCT user.id, user.first_name, user.last_name, user.email, user.webpassword password, user.last_season_id,
        user.groupset_id, pdata.datascadenza expiration_date, user.language
        FROM vtiger.vtiger_assets
        INNER JOIN vtiger.vtiger_crmentity ON vtiger.vtiger_crmentity.crmid = vtiger.vtiger_assets.assetsid
        INNER JOIN videomatch.user ON videomatch.user.loginname = vtiger.vtiger_assets.serialnumber
        INNER JOIN sics_protezione.pdata ON pdata.sn = vtiger.vtiger_assets.serialnumber
        WHERE vtiger.vtiger_assets.product = 22321
        <if test="agentId != null">
            AND vtiger.vtiger_crmentity.smownerid = ${agentId}
        </if>
    </select>

    <select id="getUserSoccermentTeamReport" resultType="java.util.Date" parameterType="java.util.TreeMap">
        SELECT MAX(vtiger_assetscf.cf_868) expiration_date
        FROM user
        INNER JOIN sics_protezione.pdata pdata ON pdata.sn = user.loginname
        INNER JOIN vtiger.vtiger_assets ON vtiger_assets.serialnumber = pdata.sn
        INNER JOIN vtiger.vtiger_assets team_report_assets ON team_report_assets.contact = vtiger_assets.contact AND team_report_assets.product = 65867
        INNER JOIN vtiger.vtiger_assetscf ON vtiger_assetscf.assetsid = team_report_assets.assetsid
        WHERE user.id = ${userId}
    </select>

</mapper>
