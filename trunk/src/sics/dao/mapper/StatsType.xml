<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="StatsType">

    <resultMap id="resultMapAdvancedMetric"     type="AdvancedMetric">
        <result property="desc"                 column="desc_it"/>
        <result property="descEn"               column="desc_en"/>
        <result property="descFr"               column="desc_fr"/>
        <result property="descEs"               column="desc_es"/>
        <result property="extendedDesc"         column="extended_description_en"/>
        <result property="isOpposite"           column="is_opposite"/>
    </resultMap>

    <resultMap id="resultMapAdvancedMetricValue" type="AdvancedMetricValue">
        <result property="teamId"               column="team_id"/>
        <result property="playerId"             column="player_id"/>
        <result property="statsTypeId"          column="type_id"/>
        <result property="value"                column="value"/>
    </resultMap>
	
    <select id="getAdvancedMetrics" resultMap="resultMapAdvancedMetric" parameterType="java.util.TreeMap">
        SELECT id, desc_it, desc_en, desc_fr, desc_es, extended_description_en, is_opposite
        FROM stats_type
        WHERE id IN(1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1, 268, 1018, 1019, 1020, 1021, 268, 269, 270, 273, 275, 286, 276, 295, 296, 297, 300, 302, 304)
    </select>
    
    <select id="getSimilarityMetrics" resultMap="resultMapAdvancedMetric" parameterType="java.util.TreeMap">
        SELECT id, desc_it, desc_en, desc_fr, desc_es, extended_description_en, is_opposite
        FROM stats_type
        WHERE id IN(12, 8, 14, 11, 17, 50, 41, 43, 36, 2, 63, 20, 275, 110, 72, 71, 3, 2, 1003, 1005, 26, 81, 80, 59, 60, 276, 83, 6, 54, 56, 287, 288, 289, 290, 291, 292)
    </select>

    <select id="getMatchStudioTeamAdvancedMetrics" resultMap="resultMapAdvancedMetricValue" parameterType="java.util.TreeMap">
        SELECT team_id, type_id, SUM(value) value
        FROM team_stats
        WHERE type_id IN(1003, 1004, 1005, 1006, 1012, 1016)
        AND team_stats.fixture_id = ${fixtureId}
        GROUP BY team_id, type_id
    </select>

    <select id="getMatchStudioPlayerAdvancedMetrics" resultMap="resultMapAdvancedMetricValue" parameterType="java.util.TreeMap">
        SELECT team_id, player_id, type_id, SUM(value) value
        FROM player_stats
        WHERE type_id IN(1003, 1004, 1005, 1006)
        AND player_stats.fixture_id = ${fixtureId}
        GROUP BY team_id, player_id, type_id
    </select>

</mapper>
