<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="TagType">

    <resultMap id="resultMapTagType"                type="TagType">
        <id     property="id"                       column="id"/>
        <result property="desc"                     column="desc_it"/>
        <result property="descEn"                   column="desc_en"/>
        <result property="descFr"                   column="desc_fr"/>
        <result property="descEs"                   column="desc_es"/>
        <result property="isOpposite"               column="opposite"/>
    </resultMap>

    <select id="getTagTypes" resultMap="resultMapTagType" parameterType="java.util.TreeMap">
        SELECT tag_type.id, tag_type.code, tag_type.desc_it, tag_type.desc_en, IFNULL(tag_type.desc_fr, tag_type.desc_en) desc_fr, IFNULL(tag_type.desc_es, tag_type.desc_en) desc_es, tag_type.opposite
        FROM tag_type
        INNER JOIN event_type ON event_type.id = tag_type.event_type_id AND event_type.panel_id = 1 AND event_type.groupset_id = -1 AND event_type.id NOT IN(740, 284, 29, 30, 739, 737, 33) AND event_type.visible = 1
        WHERE tag_type.groupset_id = -1 AND tag_type.visible = 1
    </select>
        
</mapper>