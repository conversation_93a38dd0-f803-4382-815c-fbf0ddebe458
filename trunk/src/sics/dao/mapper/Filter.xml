<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Filter">

    <resultMap id="resultMapFilter"                 type="Filter">
        <id     property="id"                       column="id"/>
        <result property="userId"                   column="user_id"/>
        <result property="groupsetId"               column="groupset_id"/>
        <result property="name"                     column="name"/>
        <result property="page"                     column="page"/>
        
        <result property="tmpFilters"               column="filters"/>
    </resultMap>

    <select id="getFiltersByPage" resultMap="resultMapFilter" parameterType="java.util.TreeMap">
        SELECT da_filter.id, da_filter.user_id, da_filter.groupset_id, da_filter.name, da_filter.page,
        GROUP_CONCAT(da_filter_detail.filter_name, '||', da_filter_detail.filter_value SEPARATOR '-_-') filters
        FROM da_filter
        INNER JOIN da_filter_detail ON da_filter_detail.filter_id = da_filter.id
        WHERE da_filter.user_id = ${userId} AND da_filter.groupset_id = ${groupsetId}
        AND da_filter.page = "${page}"
        GROUP BY da_filter.id
        ORDER BY da_filter.creation
    </select>
    
    <select id="saveFilter" parameterType="Filter" resultType="java.lang.Long">
        SELECT addFilterDataAccess(#{userId}, #{groupsetId}, #{name}, #{page}) last_insert_id
    </select>
    
    <insert id="saveFilterDetails" parameterType="java.lang.String">
        INSERT IGNORE INTO da_filter_detail (filter_id, filter_name, filter_value) 
        VALUES ${values}
    </insert>
    
    <update id="updateFilter" parameterType="Filter">
        UPDATE da_filter SET name = #{name}
        WHERE id = #{id}
    </update>
    
    <delete id = "deleteFilter" parameterType="java.util.TreeMap">
        DELETE FROM da_filter WHERE id = ${id}
    </delete>
    
    <delete id = "deleteFilterDetails" parameterType="java.util.TreeMap">
        DELETE FROM da_filter_detail WHERE filter_id = ${filterId}
    </delete>
    
</mapper>