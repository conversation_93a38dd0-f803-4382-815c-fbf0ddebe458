<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Player">

    <resultMap id="resultMapPlayer"         type="Player">
        <id     property="id"               column="id"/>
        <result property="firstName"        column="first_name"/>
        <result property="lastName"         column="last_name"/>
        <result property="knownName"        column="known_name"/>
        <result property="photo"            column="photo"/>
        <result property="bornDate"         column="born_date"/>
        <result property="footId"           column="foot_id"/>
        <result property="countryId"        column="country_id"/>
        <result property="playerAgencyId"   column="player_agent_id"/>
        <result property="marketValue"      column="market_value"/>
    </resultMap>
    
    <resultMap id="resultMapPlayerData"     type="PlayerData">
        <result property="lastTeamId"       column="last_team_id"/>
        <result property="height"           column="height"/>
        <result property="genere"           column="genere"/>
        <result property="footId"           column="foot_id"/>
        <result property="positionId"       column="position_id"/>
        <result property="positionDetailId" column="position_detail_id"/>
        <result property="jerseyNumber"     column="jersey_number"/>
        <result property="bornDate"         column="born_date"/>
        <result property="contractExpires"  column="contract_expires"/>
        <result property="eventAmount"      column="event_amount"/>
        <result property="playerId"         column="player_id"/>
        <result property="module"           column="module"/>
        <result property="modulePosition"   column="module_position"/>
        <result property="times"            column="times"/>
        <result property="min"              column="min"/>
        <result property="max"              column="max"/>
        <result property="average"          column="average"/>
        <result property="totalMinutes"     column="total_minutes"/>
        <result property="starterCompleted" column="starter_completed"/>
        <result property="starterNotCompleted" column="starter_not_completed"/>
        <result property="substituted"      column="substituted"/>
        <result property="redCards"         column="red_cards"/>
        <result property="totals"           column="totals"/>
        <result property="competitions"     column="competitions"/>
    </resultMap>
    
    <resultMap id="resultMapPlayerCareer"   type="PlayerCareerItem">
        <result property="playerId"         column="player_id"/>
        <result property="teamId"           column="team_id"/>
        <result property="competitionId"    column="competition_id"/>
        <result property="seasonId"         column="season_id"/>
        <result property="fixtureAmount"    column="fixture_amount"/>
    </resultMap>

    <sql id="select">
        SELECT player.id, player.first_name, player.last_name, player.known_name, player.photo, player.born_date, player.foot_id, player.country_id, player.player_agent_id, player.market_value
        FROM player
        <if test="isMatchStudioQuery == null or !isMatchStudioQuery">
        INNER JOIN team_player_competition ON team_player_competition.player_id = player.id AND team_player_competition.competition_id IN (SELECT competition_id FROM competition_da)
        </if>
    </sql>

    <select id="getPlayer" resultMap="resultMapPlayer">
        <include refid="select"/>
        WHERE player.id = ${playerId} AND groupset_id = -1 AND sport_id = 0
        GROUP BY player.id
    </select>
    
    <select id="getAllPlayers" resultMap="resultMapPlayer">
        <include refid="select"/>
        WHERE groupset_id = -1 AND sport_id = 0 AND player.visible = 1
        GROUP BY player.id
    </select>
    
    <select id="getPlayers" resultMap="resultMapPlayer">
        <include refid="select"/>
        WHERE player.id IN(${playerIds}) AND groupset_id = -1 AND sport_id = 0
        GROUP BY player.id
    </select>
    
    <select id="getPlayersByGroupsetId" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT player_id
        FROM team_player_competition
        WHERE team_player_competition.competition_id IN (${allowedCompetitions})
    </select>
    
    <select id="getPlayerData" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT team_player_last.team_id last_team_id, height, team.genere, foot_id, born_date, team_player.position_id, team_player.position_detail_id,
        team_player.jersey_num jersey_number, team_player.contract_expires
        FROM player
        INNER JOIN team_player ON team_player.player_id = player.id AND team_player.team_id = ${teamId} AND team_player.season_id = ${seasonId} AND team_player.groupset_id = -1
        INNER JOIN team_player_last ON team_player_last.player_id = team_player.player_id
        INNER JOIN team ON team.id = ${teamId}
        WHERE player.id = ${playerId}
    </select>
    
    <select id="getPlayerLastTeam" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT player.id player_id, team_player_last.team_id last_team_id, height, team.genere, foot_id, born_date, team_player.position_id, team_player.position_detail_id,
        team_player.jersey_num jersey_number, team_player.contract_expires
        FROM player
        INNER JOIN team_player_last ON team_player_last.player_id = player.id
        INNER JOIN fixture ON fixture.id = team_player_last.fixture_id
        INNER JOIN team_player ON team_player.player_id = player.id AND team_player.team_id = team_player_last.team_id AND team_player.season_id = fixture.season_id AND team_player.groupset_id = -1
        INNER JOIN team ON team.id = team_player.team_id
        WHERE player.id = ${playerId}
        GROUP BY player.id
    </select>
    
    <select id="getPlayersLastTeam" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT player.id player_id, team_player_last.team_id last_team_id, height, team.genere, foot_id, born_date, team_player.position_id, team_player.position_detail_id,
        team_player.jersey_num jersey_number, team_player.contract_expires
        FROM player
        INNER JOIN team_player_last ON team_player_last.player_id = player.id
        INNER JOIN fixture ON fixture.id = team_player_last.fixture_id
        INNER JOIN team_player ON team_player.player_id = player.id AND team_player.team_id = team_player_last.team_id AND team_player.season_id = fixture.season_id AND team_player.groupset_id = -1
        INNER JOIN team ON team.id = team_player.team_id
        WHERE player.id IN(${playerIds})
        GROUP BY player.id
    </select>
    
    <select id="getPlayersCompetitions" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT DISTINCT team_player_last.player_id, GROUP_CONCAT(DISTINCT team_player_competition.competition_id) competitions
        FROM team_player_last
        INNER JOIN fixture ON fixture.id = team_player_last.fixture_id
        INNER JOIN team_player_competition ON team_player_competition.player_id = team_player_last.player_id AND team_player_competition.team_id = team_player_last.team_id AND team_player_competition.season_id = fixture.season_id
        WHERE team_player_last.player_id IN(${playerIds})
        GROUP BY team_player_last.player_id
    </select>
    
    <!-- Giocatore a cui fa più passaggi -->
    <select id="getPlayerWithMostPasses" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT COUNT(DISTINCT event.id) event_amount, event_player.playerto_id player_id, team_player.position_id, team_player.position_detail_id
        FROM event
        INNER JOIN event_player ON event_player.event_id = event.id AND event_player.player_id = ${playerId} AND event_player.playerto_id IS NOT NULL
        INNER JOIN fixture ON fixture.id = event.fixture_id AND fixture.season_id = ${seasonId} AND fixture.competition_id = ${competitionId}
        AND fixture.owner_user_id = 1 AND fixture.provider_id != 3
        INNER JOIN team_player ON team_player.player_id = event_player.playerto_id AND team_player.team_id = event.team_id AND team_player.season_id = fixture.season_id
        WHERE event.event_type_id = 34 AND event.team_id = ${teamId} AND event.groupset_id = -1
        GROUP BY event_player.playerto_id
        ORDER BY event_amount DESC, event_player.playerto_id DESC
        LIMIT 1
    </select>
    
    <!-- Giocatore da cui riceve più passaggi -->
    <select id="getPlayerWithMostReceivedPasses" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT COUNT(DISTINCT event.id) event_amount, event_player.player_id player_id, team_player.position_id, team_player.position_detail_id
        FROM event
        INNER JOIN event_player ON event_player.event_id = event.id AND event_player.playerto_id = ${playerId}
        INNER JOIN fixture ON fixture.id = event.fixture_id AND fixture.season_id = ${seasonId} AND fixture.competition_id = ${competitionId}
        AND fixture.owner_user_id = 1 AND fixture.provider_id != 3
        INNER JOIN team_player ON team_player.player_id = event_player.player_id AND team_player.team_id = event.team_id AND team_player.season_id = fixture.season_id
        WHERE event.event_type_id = 34 AND event.team_id = ${teamId} AND event.groupset_id = -1
        GROUP BY event_player.player_id
        ORDER BY event_amount DESC, event_player.player_id DESC
        LIMIT 1
    </select>
    
    <!-- Posizioni in cui ha giocato (con il numero di volte) -->
    <select id="getPlayerModules" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT CASE WHEN fixture.home_team_id = fixture_player.team_id THEN fixture.home_module ELSE fixture.away_module END module,
        (fixture_player.module_position - 1) module_position, COUNT(DISTINCT fixture.id) times
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id AND fixture.owner_user_id = 1 AND fixture.provider_id != 3
        WHERE fixture.season_id = ${seasonId} AND fixture.competition_id = ${competitionId}
        AND fixture_player.player_id = ${playerId} AND fixture_player.team_id = ${teamId} AND fixture_player.groupset_id = -1
        <![CDATA[
        AND fixture_player.module_position <= 11
        ]]>
        GROUP BY module, fixture_player.module_position
        ORDER BY times DESC
    </select>
    
    <!-- Minimo, massimo e media di minuti giocati -->
    <select id="getPlayerPlaytimeStats" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        SELECT MIN(ROUND(IFNULL(value, 0) / 60)) min, MAX(ROUND(IFNULL(value, 0) / 60)) max, ROUND(AVG(IFNULL(value, 0) / 60)) average, SUM(IFNULL(value, 0) / 60) total_minutes
        FROM player_stats
        INNER JOIN fixture ON fixture.id = player_stats.fixture_id AND fixture.owner_user_id = 1 AND fixture.provider_id != 3
        WHERE fixture.season_id = ${seasonId} AND fixture.competition_id = ${competitionId}
        AND player_stats.player_id = ${playerId} AND player_stats.team_id = ${teamId} AND player_stats.type_id = 1
    </select>
    
    <!-- Partite come titolare e totali -->
    <select id="getPlayerStarterStats" resultMap="resultMapPlayerData" parameterType="java.util.TreeMap">
        <![CDATA[
        SELECT SUM(CASE WHEN module_position <= 11 AND IFNULL(substitute, -1) = -1 AND IFNULL(red, -1) = -1 THEN 1 ELSE 0 END) starter_completed,
        SUM(CASE WHEN module_position <= 11 AND IFNULL(substitute, -1) > -1 THEN 1 ELSE 0 END) starter_not_completed,
        SUM(CASE WHEN module_position > 11 AND IFNULL(red, -1) = -1 THEN 1 ELSE 0 END) substituted,
        SUM(CASE WHEN IFNULL(red, -1) > -1 THEN 1 ELSE 0 END) red_cards,
        COUNT(DISTINCT fixture.id) totals
        ]]>
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id AND fixture.owner_user_id = 1 AND fixture.provider_id != 3
        WHERE fixture.season_id = ${seasonId} AND fixture.competition_id = ${competitionId}
        AND fixture_player.player_id = ${playerId} AND fixture_player.team_id = ${teamId} AND fixture_player.groupset_id = -1
    </select>
    
    <select id="getPlayerCareer" resultMap="resultMapPlayerCareer" parameterType="java.util.TreeMap">
        SELECT DISTINCT ${playerId} player_id, fixture_player.team_id team_id, competition_id, season_id, COUNT(DISTINCT fixture.id) fixture_amount
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        WHERE fixture_player.player_id = ${playerId}
        AND provider_id != 3 AND fixture.owner_user_id = 1 AND fixture_player.groupset_id = -1
        AND fixture.season_id IN (SELECT id FROM season WHERE visible = 1)
        AND fixture.competition_id NOT IN(SELECT id FROM competition WHERE name_en LIKE '%friendly%' AND groupset_id = -1 AND sport_id = 0)
        AND fixture.competition_id NOT IN(708, 272)
        GROUP BY fixture.season_id, fixture.competition_id, fixture_player.team_id
        ORDER BY season_id DESC, competition_id
    </select>
</mapper>