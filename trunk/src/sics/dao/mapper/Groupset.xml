<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Groupset">

    <resultMap id="resultMapGroupset"                   type="Groupset">
        <id property="id"                               column="id"/>
        <result property="name"                         column="name"/>
        <result property="distribute"                   column="distribute"/>
        <result property="type"                         column="type"/>
        <result property="pers"                         column="pers"/>
        <result property="maxDownload"                  column="max_download"/>
        <result property="maxDownloadReport"            column="max_download_report"/>
        <result property="cloudSize"                    column="cloud_size"/>
        <result property="exportTimeLimit"              column="export_time_limit"/>
        <result property="trackingVideo"                column="tracking_video"/>
        <result property="trackingMinute"               column="tracking_minute"/>
        <result property="upload"                       column="upload"/>
        <result property="teamId"                       column="team_id"/>
        <result property="sportId"                      column="sport_id"/>
        <result property="genere"                       column="genere"/>
        <result property="guest"                        column="guest"/>
        
        <result property="competitions"                 column="competitions"/>
        <result property="competitionsVm"               column="competitions_vm"/>
        <result property="competitionsDa"               column="competitions_da"/>
        <result property="tactCompetitions"             column="tact_competitions"/>
        <result property="users"                        column="users"/>
    </resultMap>

    <sql id="select">
        SELECT groupset.id, groupset.name, groupset.distribute, groupset.type, pers, max_download, max_download_report,
        groupset.cloud_size, groupset.export_time_limit, groupset.tracking_video, groupset.tracking_minute
        FROM groupset
    </sql>

    <select id="getGroupsets" resultMap="resultMapGroupset" parameterType="java.util.TreeMap">
        <include refid="select"/>
        GROUP BY groupset.id
        ORDER BY groupset.name
    </select>
    
    <select id="getGroupset" resultMap="resultMapGroupset" parameterType="java.util.TreeMap">
        <include refid="select"/>
        WHERE groupset.id = ${groupsetId}
        GROUP BY groupset.id
    </select>
    
    <select id="getGroupsetByName" resultMap="resultMapGroupset" parameterType="java.util.TreeMap">
        <include refid="select"/>
        WHERE groupset.name = '${name}'
        GROUP BY groupset.id
    </select>

</mapper>
