<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="TeamPlayer">

    <resultMap id="resultMapTeamPlayer"         type="TeamPlayer">
        <result property="seasonId"             column="season_id"/>
        <result property="teamId"               column="team_id"/>
        <result property="playerId"             column="player_id"/>
        <result property="jerseyNumber"         column="jersey_num"/>
        <result property="positionId"           column="position_id"/>
        <result property="positionDetailId"     column="position_detail_id"/>
        <result property="contractExpires"      column="contract_expires"/>
    </resultMap>
    
    <resultMap id="resultMapTeamPlayerLast"     type="TeamPlayerLast">
        <result property="teamId"               column="team_id"/>
        <result property="playerId"             column="player_id"/>
        <result property="competitionId"        column="competition_id"/>
        <result property="seasonId"             column="season_id"/>
    </resultMap>
	
    <select id="getTeamPlayers" resultMap="resultMapTeamPlayer" parameterType="java.util.TreeMap">
        SELECT team_player.season_id, team_player.team_id, team_player.player_id,
        team_player.position_id, team_player.jersey_num, team_player.position_detail_id, team_player.contract_expires
        FROM team_player
        WHERE team_player.groupset_id = -1 AND team_player.season_id IN (SELECT id FROM season WHERE visible = 1)
        AND team_player.team_id IN(${teamIds})
    </select>
    
    <select id="getTeamLastData" resultMap="resultMapTeamPlayerLast" parameterType="java.util.TreeMap">
        SELECT fixture_player.team_id, fixture.competition_id, fixture.season_id
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN competition_da ON competition_da.competition_id = fixture.competition_id
        WHERE fixture_player.team_id = ${teamId} AND fixture.owner_user_id = 1 AND fixture_player.groupset_id = -1
        ORDER BY fixture.game_date DESC
        LIMIT 1
    </select>
    
    <select id="getPlayerLastData" resultMap="resultMapTeamPlayerLast" parameterType="java.util.TreeMap">
        SELECT fixture_player.team_id, fixture_player.player_id, fixture.competition_id, fixture.season_id
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        INNER JOIN competition_da ON competition_da.competition_id = fixture.competition_id
        WHERE fixture_player.player_id = ${playerId} AND fixture.owner_user_id = 1 AND fixture_player.groupset_id = -1
        AND competition_da.competition_id IN(${competitionIds})
        ORDER BY fixture.game_date DESC
        LIMIT 1
    </select>

</mapper>
