<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Settings">

    <resultMap id="resultMapSettings"               type="Settings">
        <id     property="id"                       column="user_settings_id"/>
        <result property="userId"                   column="user_id"/>
        <result property="application"              column="application"/>
        <result property="eventAdditionalStart"     column="event_additional_start"/>
        <result property="eventAdditionalEnd"       column="event_additional_end"/>
        <result property="preferredTeamId"          column="preferred_team_id"/>
        <result property="tvLanguage"               column="tv_language"/>
    </resultMap>

    <sql id="select">
        SELECT user_settings.id user_settings_id, user_settings.user_id, user_settings.application, user_settings.event_additional_start, user_settings.event_additional_end,
        user_settings.preferred_team_id, user_settings.tv_language
        FROM user_settings
    </sql>

    <select id="getSettingsById" resultMap="resultMapSettings" parameterType="java.lang.Long">
        <include refid="select"/>
        WHERE user_settings.id = ${id}
        ORDER BY user_settings.id
    </select>

    <select id="getSettingsByUserId" resultMap="resultMapSettings" parameterType="java.util.TreeMap">
        <include refid="select"/>
        WHERE user_settings.user_id = ${userId}
    </select>
    
    <insert id="addSettings" parameterType="Settings">
        INSERT IGNORE INTO user_settings (user_settings.user_id, user_settings.preferred_team_id, user_settings.tv_language)
        VALUES (#{userId}, #{preferredTeamId}, #{tvLanguage})
    </insert>
    
    <update id="updateSettings" parameterType="Settings">
        UPDATE user_settings SET event_additional_start = CASE WHEN #{eventAdditionalStart} IS NULL THEN 0 ELSE #{eventAdditionalStart} END,
        event_additional_end = CASE WHEN #{eventAdditionalEnd} IS NULL THEN 0 ELSE #{eventAdditionalEnd} END, preferred_team_id = #{preferredTeamId},
        tv_language = #{tvLanguage}
        WHERE id = #{id}
    </update>
        
</mapper>