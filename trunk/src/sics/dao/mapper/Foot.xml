<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Foot">

    <resultMap id="resultMapFoot"               type="Foot">
        <result property="id"                   column="id"/>
        <result property="desc"                 column="desc_it"/>
        <result property="descEn"               column="desc_en"/>
        <result property="descFr"               column="desc_fr"/>
        <result property="descEs"               column="desc_es"/>
    </resultMap>
	
    <select id="getFoots" resultMap="resultMapFoot" parameterType="java.util.TreeMap">
        SELECT id, desc_it, IFNULL(desc_en, desc_it) desc_en, IFNULL(desc_fr, desc_it) desc_fr, IFNULL(desc_es, desc_it) desc_es
        FROM foot
        WHERE id > 0
    </select>

</mapper>
