<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Event">

    <resultMap id="resultMapEvent"                  type="Event">
        <id     property="id"                       column="id"/>
        <result property="eventTypeId"              column="event_type_id"/>
        <result property="tagTypeList"              column="tag_type_list"/>
        <result property="fixtureId"                column="fixture_id"/>
        <result property="teamId"                   column="team_id"/>
        <result property="playerIdList"             column="player_id_list"/>
        <result property="playerToIdList"           column="player_to_id_list"/>
        <result property="period"                   column="period_id"/>
        <result property="startSecond"              column="start_second"/>
        <result property="startMinute"              column="start_minute"/>
        <result property="endSecond"                column="end_second"/>
        <result property="endMinute"                column="end_minute"/>
        <result property="startMillis"              column="start_millis"/>
        <result property="endMillis"                column="end_millis"/>
        <result property="coordinateStart"          column="coordinate_start"/>
        <result property="coordinateEnd"            column="coordinate_end"/>
        <result property="coordinateHeight"         column="coordinate_height"/>
    </resultMap>

    <select id="getEventsByParams" resultMap="resultMapEvent" parameterType="java.util.TreeMap">
        SELECT event.id, event.event_type_id, GROUP_CONCAT(DISTINCT IFNULL(event_tag.tag_type_id, '')) tag_type_list,
        event.fixture_id, event.team_id, GROUP_CONCAT(DISTINCT IFNULL(event_player.player_id, '')) player_id_list,
        GROUP_CONCAT(DISTINCT IFNULL(event_player.playerto_id, '')) player_to_id_list,
        event.period_id, event.period_start_second start_second, event.period_start_minute start_minute,
        event.period_end_second end_second, event.period_end_minute end_minute, event.period_start_msec start_millis,
        event.period_end_msec end_millis, event.coordinate_start, event.coordinate_end, event.coordinate_height
        FROM event
        INNER JOIN fixture ON fixture.id = event.fixture_id
        LEFT JOIN event_tag ON event_tag.event_id = event.id
        LEFT JOIN event_player ON event_player.event_id = event.id
        ${query}
    </select>
        
</mapper>