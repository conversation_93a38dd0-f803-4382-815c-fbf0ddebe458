<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Season">

    <resultMap id="resultMapSeason"             type="Season">
        <result property="id"                   column="id"/>
        <result property="name"                 column="name"/>
        <result property="visible"              column="visible"/>
    </resultMap>
	
    <select id="getSeasons" resultMap="resultMapSeason" parameterType="java.util.TreeMap">
        SELECT id, name, visible
        FROM season
        ORDER BY name DESC
    </select>

</mapper>
