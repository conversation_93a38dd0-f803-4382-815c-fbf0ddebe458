<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Fixture">

    <resultMap id="resultMapFixture"                type="Fixture">
        <id     property="id"                       column="id"/>
        <result property="gameDate"                 column="game_date"/>
        <result property="competitionId"            column="competition_id"/>
        <result property="homeTeamId"               column="home_team_id"/>
        <result property="awayTeamId"               column="away_team_id"/>
        <result property="teamSx"                   column="team_sx"/>
        <result property="teamSx2"                  column="team_sx_2"/>
        <result property="homeScore"                column="home_score"/>
        <result property="awayScore"                column="away_score"/>
        <result property="homeModule"               column="home_module"/>
        <result property="awayModule"               column="away_module"/>
        <result property="homeOi"                   column="home_oi"/>
        <result property="awayOi"                   column="away_oi"/>
        <result property="homePossesso"             column="home_possesso"/>
        <result property="awayPossesso"             column="away_possesso"/>
        <result property="duration"                 column="duration"/>
        <result property="matchDay"                 column="matchday"/>
        <result property="groupId"                  column="group_id"/>
        <result property="seasonId"                 column="season_id"/>
        <result property="referee"                  column="referee"/>
        <result property="firstAssistant"           column="first_assistant"/>
        <result property="secondAssistant"          column="second_assistant"/>
        <result property="fourthReferee"            column="fourth_referee"/>
        <result property="homexG"                   column="home_xg"/>
        <result property="awayxG"                   column="away_xg"/>
        <result property="minutesPlayed"            column="minutes_played"/>
    </resultMap>
    
    <resultMap id="resultMapFixtureDetails" type="FixtureDetails">
        <result property="id"               column="id"/>
        <result property="fixtureId"        column="fixture_id"/>
        <result property="teamSx"           column="teamSx"/>
        <result property="teamSx2"          column="teamSx2"/>
        <result property="homeTeamColor"    column="home_team_color"/>
        <result property="awayTeamColor"    column="away_team_color"/>
        <result property="startTime1"       column="starttime1"/>
        <result property="endTime1"         column="endtime1"/>
        <result property="startTime2"       column="starttime2"/>
        <result property="endTime2"         column="endtime2"/>
        <result property="startTime3"       column="starttime3"/>
        <result property="endTime3"         column="endtime3"/>
        <result property="startTime4"       column="starttime4"/>
        <result property="endTime4"         column="endtime4"/>
        <result property="homeTeamId"       column="home_team_id"/>
        <result property="awayTeamId"       column="away_team_id"/>
    </resultMap>

    <select id="getFixtureById" resultMap="resultMapFixture">
        SELECT fixture.id, competition_id, game_date, home_team_id, away_team_id,
        home_score, away_score, home_module, away_module, home_oi, away_oi,
        possesso1 home_possesso, possesso2 away_possesso, matchday, group_id, season_id,
        referee.last_name referee, firstAssistant.last_name first_assistant, secondAssistant.last_name second_assistant, fourthReferee.last_name fourth_referee
        FROM fixture
        LEFT JOIN referee ON referee.id = fixture.referee_id
        LEFT JOIN referee firstAssistant ON firstAssistant.id = fixture.assistant1_id
        LEFT JOIN referee secondAssistant ON secondAssistant.id = fixture.assistant2_id
        LEFT JOIN referee fourthReferee ON fourthReferee.id = fixture.assistant4_id
        WHERE fixture.id = ${fixtureId}
        AND season_id IN (SELECT id FROM season WHERE visible = 1)
        AND provider_id != 3 AND owner_user_id = 1
        ORDER BY game_date
    </select>
    
    <select id="getFixtureByIds" resultMap="resultMapFixture">
        SELECT fixture.id, competition_id, game_date, home_team_id, away_team_id,
        home_score, away_score, home_module, away_module, home_oi, away_oi,
        possesso1 home_possesso, possesso2 away_possesso, matchday, group_id, season_id
        FROM fixture
        WHERE fixture.id IN(${fixtureIds})
        AND season_id IN (SELECT id FROM season WHERE visible = 1)
        AND provider_id != 3 AND owner_user_id = 1
        ORDER BY game_date
    </select>
    
    <select id="getFixtureDetails" resultMap="resultMapFixtureDetails" parameterType="java.util.TreeMap">
        SELECT fixture_details.id, fixture_details.fixture_id, fixture_details.teamSx, fixture_details.teamSx2,
        fixture_details.home_team_color, fixture_details.away_team_color,
        fixture_details.starttime1, fixture_details.endtime1, fixture_details.starttime2, fixture_details.endtime2,
        fixture_details.starttime3, fixture_details.endtime3, fixture_details.starttime4, fixture_details.endtime4,
        fixture.home_team_id, fixture.away_team_id
        FROM fixture_details
        INNER JOIN fixture ON fixture.id = fixture_details.fixture_id
        INNER JOIN competition_da ON competition_da.competition_id = fixture.competition_id
    </select>
    
    <select id="getFixtureDetailsByFixtureId" resultMap="resultMapFixtureDetails" parameterType="java.util.TreeMap">
        SELECT fixture_details.id, fixture_details.fixture_id, fixture_details.teamSx, fixture_details.teamSx2,
        fixture_details.home_team_color, fixture_details.away_team_color,
        fixture_details.starttime1, fixture_details.endtime1, fixture_details.starttime2, fixture_details.endtime2,
        fixture_details.starttime3, fixture_details.endtime3, fixture_details.starttime4, fixture_details.endtime4,
        fixture.home_team_id, fixture.away_team_id
        FROM fixture_details
        INNER JOIN fixture ON fixture.id = fixture_details.fixture_id
        <if test="isMatchStudioQuery == null or !isMatchStudioQuery">
        INNER JOIN competition_da ON competition_da.competition_id = fixture.competition_id
        </if>
        WHERE fixture.id = ${fixtureId}
    </select>
    
    <select id="getTeamLastFixtures" resultMap="resultMapFixture" parameterType="java.util.TreeMap">
        SELECT fixture.id, fixture.home_team_id, fixture.away_team_id, fixture.game_date, fixture.competition_id, fixture.home_score, fixture.away_score,
        fixture.possesso1 home_possesso, fixture.possesso2 away_possesso, fixture.home_module, fixture.away_module, (IFNULL(xGMade.value, 0) / 1000) home_xg, (IFNULL(xGSuffered.value, 0) / 1000) away_xg
        FROM fixture
        LEFT JOIN team_stats xGMade ON xGMade.fixture_id = fixture.id AND xGMade.team_id = ${teamId} AND xGMade.type_id = 1003
        LEFT JOIN team_stats xGSuffered ON xGSuffered.fixture_id = fixture.id AND xGSuffered.team_id = CASE WHEN fixture.home_team_id = ${teamId} THEN fixture.away_team_id ELSE fixture.home_team_id END AND xGSuffered.type_id = 1003
        WHERE (home_team_id = ${teamId} OR away_team_id = ${teamId})
        AND fixture.competition_id = ${competitionId}
        AND fixture.season_id = ${seasonId}
        AND fixture.owner_user_id = 1
        <![CDATA[
        AND fixture.game_date < NOW()
        ]]>
        ORDER BY fixture.game_date DESC
        LIMIT 10
    </select>
    
    <select id="getPlayerLastFixtures" resultMap="resultMapFixture" parameterType="java.util.TreeMap">
        SELECT fixture.id, fixture.home_team_id, fixture.away_team_id, fixture.game_date, fixture.competition_id, fixture.home_score, fixture.away_score,
        fixture.possesso1 home_possesso, fixture.possesso2 away_possesso, fixture.home_module, fixture.away_module, ROUND(fixture_player.play_time / 60) minutes_played
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        WHERE fixture_player.player_id = ${playerId}
        AND fixture_player.team_id = ${teamId}
        AND fixture.competition_id = ${competitionId}
        AND fixture.season_id = ${seasonId}
        AND fixture.owner_user_id = 1
        AND fixture_player.groupset_id = -1
        <![CDATA[
        AND fixture.game_date < NOW()
        ]]>
        ORDER BY fixture.game_date DESC
        LIMIT 10
    </select>
    
    <select id="getFixtureFileproject" resultType="java.lang.String" parameterType="java.util.TreeMap">
        SELECT fileproject
        FROM game
        WHERE fixture_id = ${fixtureId} AND groupset_id = -1
    </select>
    
</mapper>