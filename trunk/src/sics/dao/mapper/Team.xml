<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="Team">

    <resultMap id="resultMapTeam"               type="Team">
        <result property="id"                   column="id"/>
        <result property="countryId"            column="country_id"/>
        <result property="name"                 column="name"/>
        <result property="nameEn"               column="name_en"/>
        <result property="logo"                 column="logo"/>
        <result property="color"                column="color"/>
    </resultMap>
    
    <resultMap id="resultMapTeamCareer"         type="TeamCareerItem">
        <result property="teamId"               column="team_id"/>
        <result property="competitionId"        column="competition_id"/>
        <result property="seasonId"             column="season_id"/>
    </resultMap>
    
    <resultMap id="resultMapTeamData"           type="TeamData">
        <result property="fixtureAmount"        column="fixture_amount"/>
        <result property="homeScore"            column="home_score"/>
        <result property="awayScore"            column="away_score"/>
        <result property="wins"                 column="wins"/>
        <result property="draws"                column="draws"/>
        <result property="losses"               column="losses"/>
        <result property="mainModule"           column="main_module"/>
    </resultMap>
    
    <resultMap id="resultMapTeamPlayerData"     type="TeamPlayerData">
        <result property="totalPlayers"         column="total_players"/>
        <result property="totalNationals"       column="total_nationals"/>
        <result property="minYear"              column="min_year"/>
        <result property="maxYear"              column="max_year"/>
        <result property="yearAverage"          column="year_average"/>
    </resultMap>
	
    <select id="getTeams" resultMap="resultMapTeam" parameterType="java.util.TreeMap">
        SELECT team.id, team.name, IFNULL(team.name_en, team.name) name_en, team.logo, country.id country_id, team.color
        FROM team
        LEFT JOIN country ON country.id = team.country_id
        WHERE groupset_id = -1 AND sport_id = 0
        <if test="teamIds != null">
            AND team.id IN(${teamIds})
        </if>
    </select>
    
    <select id="getTeamByGroupsetId" resultType="java.lang.Long" parameterType="java.util.TreeMap">
        SELECT DISTINCT team_id
        FROM team_player_competition
        WHERE team_player_competition.competition_id IN (${allowedCompetitions})
    </select>
    
    <select id="getTeamCareer" resultMap="resultMapTeamCareer" parameterType="java.util.TreeMap">
        SELECT DISTINCT ${teamId} team_id, competition_id, season_id
        FROM fixture
        WHERE (home_team_id = ${teamId} OR home_team_id = ${teamId})
        AND provider_id != 3 AND owner_user_id = 1
        AND fixture.season_id IN (SELECT id FROM season WHERE visible = 1)
        AND fixture.competition_id NOT IN(SELECT id FROM competition WHERE name_en LIKE '%friendly%' AND groupset_id = -1 AND sport_id = 0)
        AND fixture.competition_id NOT IN(708, 272)
        ORDER BY season_id DESC, competition_id
    </select>
    
    <select id="getTeamData" resultMap="resultMapTeamData" parameterType="java.util.TreeMap">
        SELECT COUNT(id) fixture_amount,
        SUM(IFNULL(CASE WHEN home_team_id = ${teamId} THEN home_score ELSE away_score END, 0)) home_score,
        SUM(IFNULL(CASE WHEN home_team_id = ${teamId} THEN away_score ELSE home_score END, 0)) away_score,
        SUM(CASE WHEN (home_team_id = ${teamId} AND home_score > away_score) OR (away_team_id = ${teamId} AND away_score > home_score) THEN 1 ELSE 0 END) wins,
        SUM(CASE WHEN home_score = away_score THEN 1 ELSE 0 END) draws,
        SUM(CASE WHEN (away_team_id = ${teamId} AND home_score > away_score) OR (home_team_id = ${teamId} AND away_score > home_score) THEN 1 ELSE 0 END) losses,
        (SELECT CASE WHEN home_team_id = ${teamId} THEN home_module ELSE away_module END module
        FROM fixture
        WHERE (home_team_id = ${teamId} OR away_team_id = ${teamId})
        AND competition_id = ${competitionId} AND season_id = ${seasonId}
        AND provider_id != 3 AND owner_user_id = 1 AND fixture.analysis_level_id IN(2, 4)
                <![CDATA[
                AND game_date < NOW()
                ]]>
        GROUP BY module
        ORDER BY COUNT(id) DESC
        LIMIT 1
        ) main_module
        FROM fixture
        WHERE (home_team_id = ${teamId} OR away_team_id = ${teamId})
        AND competition_id = ${competitionId} AND season_id = ${seasonId}
        AND provider_id != 3 AND owner_user_id = 1 AND fixture.analysis_level_id IN(2, 4)
        <![CDATA[
        AND game_date < NOW()
        ]]>
    </select>
    
    <select id="getTeamPlayerData" resultMap="resultMapTeamPlayerData" parameterType="java.util.TreeMap">
        SELECT COUNT(DISTINCT player.id) total_players,
        SUM(CASE WHEN player.country_id = tbl.country_id THEN 1 ELSE 0 END) total_nationals,
        MIN(YEAR(player.born_date)) min_year,
        MAX(YEAR(player.born_date)) max_year,
        AVG(DISTINCT YEAR(player.born_date)) year_average
        FROM (
        SELECT DISTINCT player.id player_id, team.country_id
        FROM team
        INNER JOIN fixture ON fixture.home_team_id = team.id OR fixture.away_team_id = team.id
        INNER JOIN fixture_player ON fixture_player.fixture_id = fixture.id AND fixture_player.team_id = team.id
        INNER JOIN player ON player.id = fixture_player.player_id
        WHERE fixture.season_id = ${seasonId} AND team.id = ${teamId} AND fixture.competition_id = ${competitionId}
        AND provider_id != 3 AND fixture.owner_user_id = 1 AND fixture_player.groupset_id = -1
            <![CDATA[
            AND game_date < NOW()
            ]]>
        AND player.groupset_id = -1 AND player.sport_id = 0
        ) tbl
        INNER JOIN player ON player.id = tbl.player_id
    </select>

</mapper>
