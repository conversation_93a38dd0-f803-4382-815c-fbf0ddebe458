<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN" "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">
<mapper namespace="FixturePlayer">

    <resultMap id="resultMapFixturePlayer"      type="FixturePlayer">
        <result property="fixtureId"            column="fixture_id"/>
        <result property="teamId"               column="team_id"/>
        <result property="playerId"             column="player_id"/>
        <result property="positionId"           column="position_id"/>
        <result property="jerseyNumber"         column="jersey_num"/>
        <result property="modulePosition"       column="module_position"/>
        
        <result property="starter"              column="starter"/>
        <result property="playerOut"            column="player_out"/>
        <result property="playerIn"             column="player_in"/>
        <result property="redCard"              column="red_card"/>
        <result property="redCardTime"          column="red_card_time"/>
        <result property="substituteTime"       column="substitute_time"/>
        <result property="coordinate"           column="coordinate"/>
        <result property="playTime"             column="play_time"/>
        <result property="fromPeriodId"         column="from_period_id"/>
        <result property="fromPeriodMinute"     column="from_period_minute"/>
        <result property="toPeriodId"           column="to_period_id"/>
        <result property="toPeriodMinute"       column="to_period_minute"/>
        <result property="fixtureTimeDuration"  column="fixture_time_duration"/>
        <result property="fixtureExtraTimeDuration"  column="fixture_extra_time_duration"/>
    </resultMap>
    
    <sql id="select">
        SELECT fixture_player.fixture_id, fixture_player.team_id, fixture_player.player_id,
        fixture_player.position_id, fixture_player.jersey_num, fixture_player.module_position,
        CASE WHEN fixture_player.module_position BETWEEN 1 AND 11 THEN 1 ELSE 0 END starter,
        <![CDATA[
        CASE WHEN fixture_player.substitute > -1 THEN 1 ELSE 0 END player_out,
        CASE WHEN fixture_player.module_position > 11 AND fixture_player.from_period_id > 0 THEN 1 ELSE 0 END player_in,
        CASE WHEN fixture_player.red > -1 THEN 1 ELSE 0 END red_card,
        ]]>
        fixture_player.from_period_id, fixture_player.from_period_minute,
        fixture_player.to_period_id, fixture_player.to_period_minute,
        fixture_player.red red_card_time, fixture_player.substitute substitute_time,
        fixture_player.coordinate,
        fixture_player.play_time,
        (IFNULL(fixture_details.endtime1, 0) - IFNULL(fixture_details.starttime1, 0)) fixture_time_duration,
        (IFNULL(fixture_details.endtime3, 0) - IFNULL(fixture_details.starttime3, 0)) fixture_extra_time_duration
        FROM fixture_player
        INNER JOIN fixture ON fixture.id = fixture_player.fixture_id
        LEFT JOIN fixture_details ON fixture_details.fixture_id = fixture.id
    </sql>
        
    <select id="getFixturePlayersByParams" resultMap="resultMapFixturePlayer" parameterType="java.util.TreeMap">
        <include refid="select"/>
        WHERE fixture_player.groupset_id = -1
        <if test="seasonId != null">
            AND fixture.season_id = ${seasonId}
        </if>
        <if test="competitionId != null">
            AND fixture.competition_id = ${competitionId}
        </if>
        <if test="teamId != null">
            AND fixture_player.team_id = ${teamId}
        </if>
        <if test="positionId != null">
            AND fixture_player.position_id = ${positionId}
        </if>
        <if test="fixtureId != null">
            AND fixture_player.fixture_id IN(${fixtureId})
        </if>
        <if test="matchdayFrom != null">
            <![CDATA[
            AND fixture.matchday >= ${matchdayFrom}
            ]]>
        </if>
        <if test="matchdayTo != null">
            <![CDATA[
            AND fixture.matchday <= ${matchdayTo}
            ]]>
        </if>
        ORDER BY fixture.game_date, fixture_player.position_id
    </select>

</mapper>
