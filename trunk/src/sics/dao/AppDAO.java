package sics.dao;

import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import sics.domain.*;

public interface AppDAO {

    public User getUser(Long id) throws SQLException;

    public List<User> getUsers() throws SQLException;
    
    public User getUserByMail(String mail) throws SQLException;
    
    public List<User> getUsers(List<Long> ids) throws SQLException;

    public List<User> getUsersWithDetails(List<Long> ids) throws SQLException;

    public List<User> getUsersByGroupset(Long groupsetId) throws SQLException;
    
    public List<UserPermission> getUserPermissions(Long userId) throws SQLException;

    public List<Competition> getUserCompetitions(Long groupsetId) throws SQLException;
    
    public void saveUser(User item) throws SQLException;
    
    public void saveLogData(LogData item) throws SQLException;

    public List<Team> getTeams() throws SQLException;

    public List<Team> getTeams(List<Long> teamIds) throws SQLException;

    public List<Season> getSeasons() throws SQLException;

    public List<Competition> getCompetitions() throws SQLException;

    public List<Competition> getCompetitions(List<Long> competitionIds) throws SQLException;

    public Competition getCompetition(Long competitionId) throws SQLException;

    public List<Competition> getInternationalCompetitions() throws SQLException;

    public Settings getSettingsByUserId(Long userId) throws SQLException;

    public List<Long> getTeamByGroupsetId(List<Competition> allowedCompetitions) throws SQLException;

    public List<Long> getPlayersByGroupsetId(List<Competition> allowedCompetitions) throws SQLException;

    public List<EventType> getEventTypes() throws SQLException;

    public List<EventType> getOppositeEventTypes() throws SQLException;

    public List<TagType> getTagTypes() throws SQLException;

    public List<Player> getPlayers() throws SQLException;

    public Player getPlayer(Long playerId, Boolean isMatchStudioQuery) throws SQLException;

    public List<FixtureDetails> getFixtureDetails() throws SQLException;

    public List<Country> getCountries() throws SQLException;

    public List<Foot> getFoots() throws SQLException;

    public List<Position> getPositions() throws SQLException;

    public List<Position> getPositionDetails() throws SQLException;

    public List<TeamPlayer> getTeamPlayers() throws SQLException;

    public List<AdvancedMetric> getAdvancedMetrics() throws SQLException;

    public Fixture getFixtureById(Long fixtureId) throws SQLException;

    public List<Fixture> getFixtureByIds(List<Long> fixtureIds) throws SQLException;

    public List<Event> getEventsByParams(Map<String, Object> params) throws SQLException;

    public List<FixturePlayer> getFixturePlayersByParams(Map<String, Object> params) throws SQLException;

    public void addSettings(Settings settings) throws SQLException;

    public void updateSettings(Settings settings) throws SQLException;

    public List<Filter> getFiltersByPage(Long userId, Long groupsetId, String page) throws SQLException;

    public Long saveFilter(Filter filter) throws SQLException;

    public void updateFilter(Filter filter) throws SQLException;

    public void deleteFilter(Long filterId) throws SQLException;

    public void saveFilterDetails(Filter filter) throws SQLException;

    public void deleteFilterDetails(Long filterId) throws SQLException;

    public List<TeamCareerItem> getTeamCareer(Long teamId) throws SQLException;

    public List<Fixture> getTeamLastFixtures(Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public TeamData getTeamData(Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public TeamPlayerData getTeamPlayerData(Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public PlayerData getPlayerData(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public PlayerData getPlayerWithMostPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public PlayerData getPlayerWithMostReceivedPasses(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public List<PlayerData> getPlayerModules(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public PlayerData getPlayerPlaytimeStats(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public PlayerData getPlayerStarterStats(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public List<PlayerCareerItem> getPlayerCareer(Long playerId) throws SQLException;

    public List<Fixture> getPlayerLastFixtures(Long playerId, Long teamId, Long competitionId, Long seasonId) throws SQLException;

    public String getFixtureFileproject(Long fixtureId) throws SQLException;

    public Groupset getGroupset(Long groupsetId) throws SQLException;

    public Long getUserLastWeekMatchDownloadAmount(Long userId) throws SQLException;

    public void resetUserPassword(Long userId) throws SQLException;

    public TeamPlayerLast getTeamLastData(Long teamId) throws SQLException;

    public TeamPlayerLast getPlayerLastData(Long playerId, List<Competition> allowedCompetitions) throws SQLException;

    public List<AdvancedMetric> getSimilarityMetrics() throws SQLException;

    public List<PlayerAgency> getPlayerAgencies() throws SQLException;

    public List<Group> getGroups() throws SQLException;

    public PlayerData getPlayerLastTeam(Long playerId) throws SQLException;

    public List<PlayerData> getPlayersLastTeam(List<Long> playerIds) throws SQLException;

    public List<PlayerData> getPlayersCompetitions(List<Long> playerIds) throws SQLException;

    public List<Long> getTeamCompetitions(Long teamId, Long seasonId, List<Competition> allowedCompetitions) throws SQLException;

    public FixtureDetails getFixtureDetails(Long fixtureId, Boolean isMatchStudioQuery) throws SQLException;

    public List<User> getVtigerAgents() throws SQLException;

    public List<User> getVtigerAgentClients(Long agentId) throws SQLException;

    public List<AdvancedMetricValue> getMatchStudioTeamAdvancedMetrics(Long fixtureId) throws SQLException;

    public List<AdvancedMetricValue> getMatchStudioPlayerAdvancedMetrics(Long fixtureId) throws SQLException;

    public List<IndexEvent> getFixtureIndexEvent(Long fixtureId) throws SQLException;

    public List<IndexEventType> getIndexEventType() throws SQLException;

    public List<IndexTrend> getFixtureIndexTrend(Long fixtureId) throws SQLException;

    public Date getUserSoccermentTeamReport(Long userId) throws SQLException;
}
