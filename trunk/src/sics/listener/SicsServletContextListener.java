package sics.listener;

import javax.servlet.ServletContext;
import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import sics.controller.BaseController;
import sics.helper.MongoHelper;

/**
 *
 * <AUTHOR>
 */
public class SicsServletContextListener implements ServletContextListener {

    private static ServletContext context;

    @Override
    public void contextInitialized(ServletContextEvent sce) {
        context = sce.getServletContext();
    }

    @Override
    public void contextDestroyed(ServletContextEvent sce) {
        BaseController.killTimer();
        SessionListener.killTimer();
        MongoHelper.closeConnection();
        context = null;
    }

    public static ServletContext getContext() {
        return context;
    }

}
