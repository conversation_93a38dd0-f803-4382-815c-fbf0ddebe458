package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class Point {

    private Double x, y, z;
    private Boolean isDefault;

    public Point() {
        this.x = -1D;
        this.y = -1D;
        this.z = -1D;

        isDefault = isDefault();
    }

    public Point(Double x, Double y) {
        this.x = x;
        this.y = y;
        this.z = -1D;

        isDefault = isDefault();
    }

    public Point(Double x, Double y, Double z) {
        this.x = x;
        this.y = y;
        this.z = z;

        isDefault = isDefault();
    }

    @Override
    public Point clone() throws CloneNotSupportedException {
        return new Point(this.x, this.y, this.z);
    }

    public Double getX() {
        return x;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public Double getY() {
        return y;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public Double getZ() {
        return z;
    }

    public void setZ(Double z) {
        this.z = z;
    }

    public Boolean getIsDefault() {
        return isDefault;
    }
    
    private boolean isDefault() {
        return (this.x == -1D && this.y == -1D);
    }

    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
}
