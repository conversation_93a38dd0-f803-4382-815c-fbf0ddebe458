package sics.domain;

import org.apache.commons.lang.BooleanUtils;

/**
 *
 * <AUTHOR>
 */
public class TableRow {

    private Long eventTypeId;
    private String eventTypeName, tagTypeName, zoneAbbName, zoneName;
    private Integer index;
    private Long teamId, playerId;
    private String teamName, teamLogo, playerName, playerPhoto;
    private Double minValue, value, maxValue, average;
    private String minValueTeam, maxValueTeam, minValuePlayer, maxValuePlayer;
    private Long progressPercentage;

    private Boolean isOpposite;                         // per metriche "subite" bisogna applicare il contrario

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getEventTypeName() {
        return eventTypeName;
    }

    public void setEventTypeName(String eventTypeName) {
        this.eventTypeName = eventTypeName;
    }

    public String getTagTypeName() {
        return tagTypeName;
    }

    public void setTagTypeName(String tagTypeName) {
        this.tagTypeName = tagTypeName;
    }

    public String getZoneAbbName() {
        return zoneAbbName;
    }

    public void setZoneAbbName(String zoneAbbName) {
        this.zoneAbbName = zoneAbbName;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamLogo() {
        return teamLogo;
    }

    public void setTeamLogo(String teamLogo) {
        this.teamLogo = teamLogo;
    }

    public String getPlayerName() {
        return playerName;
    }

    public void setPlayerName(String playerName) {
        this.playerName = playerName;
    }

    public String getPlayerPhoto() {
        return playerPhoto;
    }

    public void setPlayerPhoto(String playerPhoto) {
        this.playerPhoto = playerPhoto;
    }

    public Double getMinValue() {
        return minValue;
    }

    public void setMinValue(Double minValue) {
        this.minValue = minValue;
        calculateProgressPercentage(false);
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
        calculateProgressPercentage(false);
    }

    public Double getMaxValue() {
        return maxValue;
    }

    public void setMaxValue(Double maxValue) {
        this.maxValue = maxValue;
        calculateProgressPercentage(false);
    }

    public Double getAverage() {
        return average;
    }

    public void setAverage(Double average) {
        this.average = average;
    }

    public String getMinValueTeam() {
        return minValueTeam;
    }

    public void setMinValueTeam(String minValueTeam) {
        this.minValueTeam = minValueTeam;
    }

    public String getMaxValueTeam() {
        return maxValueTeam;
    }

    public void setMaxValueTeam(String maxValueTeam) {
        this.maxValueTeam = maxValueTeam;
    }

    public String getMinValuePlayer() {
        return minValuePlayer;
    }

    public void setMinValuePlayer(String minValuePlayer) {
        this.minValuePlayer = minValuePlayer;
    }

    public String getMaxValuePlayer() {
        return maxValuePlayer;
    }

    public void setMaxValuePlayer(String maxValuePlayer) {
        this.maxValuePlayer = maxValuePlayer;
    }

    public Long getProgressPercentage() {
        return progressPercentage;
    }

    public void setProgressPercentage(Long progressPercentage) {
        this.progressPercentage = progressPercentage;
        if (this.progressPercentage != null && this.progressPercentage < 5) {
            this.progressPercentage = 5L;
        }
    }

    private void calculateProgressPercentage(boolean bypass) {
        if (this.progressPercentage == null || bypass) {
            if (this.minValue != null && this.maxValue != null && this.value != null && this.average != null) {
                setProgressPercentage(Math.round((this.value - this.minValue) / (this.maxValue - this.minValue) * 100));
                if (BooleanUtils.isTrue(this.isOpposite)) {
                    setProgressPercentage(100 - this.progressPercentage);
                }
                if (Double.compare(this.value, -1D) == 0) {
                    setProgressPercentage(0L);
                }

//                if (this.value <= this.average) {
//                    // Calculate position in the first segment (0% to 50%)
//                    if (BooleanUtils.isTrue(this.isOpposite)) {
//                        this.progressPercentage = Math.round(50.0 * (this.value - this.maxValue) / (this.average - this.maxValue));
//                    } else {
//                        this.progressPercentage = Math.round(50.0 * (this.value - this.minValue) / (this.average - this.minValue));
//                    }
//                } else {
//                    // Calculate position in the second segment (50% to 100%)
//                    if (BooleanUtils.isTrue(this.isOpposite)) {
//                        this.progressPercentage = Math.round(50.0 + (50.0 * (this.value - this.average) / (this.minValue - this.average)));
//                    } else {
//                        this.progressPercentage = Math.round(50.0 + (50.0 * (this.value - this.average) / (this.maxValue - this.average)));
//                    }
//                }
//
//                if (BooleanUtils.isTrue(this.isOpposite)) {
//                    this.progressPercentage = 100 - this.progressPercentage;
//                }
            }
        }
    }

    public String getProgressClass() {
        String color = BooleanUtils.isTrue(this.isOpposite) ? "success" : "danger";

        Double tmpMinValue = this.minValue, tmpMaxValue = this.maxValue;
        if (BooleanUtils.isTrue(this.isOpposite)) {
            tmpMinValue = this.maxValue;
            tmpMaxValue = this.minValue;
        }

        if (this.value != null && tmpMaxValue != null && this.average != null) {
            if (this.value >= this.average) {
                double halfMax = (tmpMaxValue + this.average) / 2;
                if (this.value >= halfMax) {
                    color = BooleanUtils.isTrue(this.isOpposite) ? "danger" : "success";
//                    color = "success";
                } else {
                    color = BooleanUtils.isTrue(this.isOpposite) ? "yellow" : "lime";
//                    color = "lime";
                }
            } else {
                double halfMin = (tmpMinValue + this.average) / 2;
                if (this.value >= halfMin) {
                    color = BooleanUtils.isTrue(this.isOpposite) ? "lime" : "yellow";
//                    color = "yellow";
                } else {
                    color = BooleanUtils.isTrue(this.isOpposite) ? "success" : "danger";
//                    color = "danger";
                }
            }
        }
//        if (this.value != null && this.maxValue != null && this.average != null) {
//            if (this.value >= this.average) {
//                color = BooleanUtils.isTrue(this.isOpposite) ? "danger" : "success";
//            } else {
//                color = BooleanUtils.isTrue(this.isOpposite) ? "success" : "danger";
//            }
//        }

//        if (this.progressPercentage <= 25) {
//            color = "danger";
//        } else if (this.progressPercentage <= 50) {
//            color = "yellow";
//        } else if (this.progressPercentage <= 75) {
//            color = "lime";
//        } else {
//            color = "success";
//        }
        return color;
    }

    public String getProgressClassRBG() {
        switch (getProgressClass()) {
            case "danger":
                return "#EF4444";
            case "yellow":
                return "#ffb400";
            case "lime":
                return "#5bab56";
            case "success":
                return "#059669";
            default:
                return "#EF4444";
        }
    }

    public Boolean getIsOpposite() {
        return isOpposite;
    }

    public void setIsOpposite(Boolean isOpposite) {
        this.isOpposite = isOpposite;
        calculateProgressPercentage(true);
    }
}
