package sics.domain;

import java.util.Calendar;

/**
 *
 * <AUTHOR>
 */
public class TeamPlayerData {

    private Integer totalPlayers, totalNationals, minYear, maxYear;
    private Double yearAverage;

    // extra fields
    private Integer min, max, average, barIndex;

    public Integer getTotalPlayers() {
        return totalPlayers;
    }

    public void setTotalPlayers(Integer totalPlayers) {
        this.totalPlayers = totalPlayers;
    }

    public Integer getTotalNationals() {
        return totalNationals;
    }

    public void setTotalNationals(Integer totalNationals) {
        this.totalNationals = totalNationals;
    }

    public Integer getMinYear() {
        return minYear;
    }

    public void setMinYear(Integer minYear) {
        this.minYear = minYear;
    }

    public Integer getMaxYear() {
        return maxYear;
    }

    public void setMaxYear(Integer maxYear) {
        this.maxYear = maxYear;
    }

    public Double getYearAverage() {
        return yearAverage;
    }

    public void setYearAverage(Double yearAverage) {
        this.yearAverage = yearAverage;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getAverage() {
        return average;
    }

    public void setAverage(Integer average) {
        this.average = average;
    }

    public Integer getBarIndex() {
        return barIndex;
    }

    public void setBarIndex(Integer barIndex) {
        this.barIndex = barIndex;
    }

    public void calculateValues() {
        if (this.minYear != null) {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, minYear);
            this.max = Calendar.getInstance().get(Calendar.YEAR) - cal.get(Calendar.YEAR);
        } else {
            this.max = 18;
        }
        if (this.maxYear != null) {
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, maxYear);
            this.min = Calendar.getInstance().get(Calendar.YEAR) - cal.get(Calendar.YEAR);
        } else {
            this.min = 35;
        }

        if (this.yearAverage != null) {
            Long tmpYear = Math.round(this.yearAverage);
            Calendar cal = Calendar.getInstance();
            cal.set(Calendar.YEAR, tmpYear.intValue());
            this.average = Calendar.getInstance().get(Calendar.YEAR) - cal.get(Calendar.YEAR);
        } else {
            this.average = 18;
        }
        
        this.barIndex = (int) Math.round(1D * (this.average - this.min) / (this.max - this.min) * 10);
    }
}
