package sics.domain;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import java.util.Date;
import sics.helper.DateHelper;

/**
 *
 * <AUTHOR>
 */
public class Fixture {

    private Long id;
    private Date gameDate;
    private Long competitionId;
    private Long homeTeamId, awayTeamId;
    private Long teamSx, teamSx2;
    private Integer homeScore, awayScore;
    private String homeModule, awayModule;
    private Integer homeOi, awayOi;             // IPO
    private Integer homeIrd, awayIrd;           // IRD del homeTeam è l'IPO dell'awayTeam
    private Integer homePossesso, awayPossesso; // POSSESSO
    private Long duration;
    private Integer matchDay;
    private Integer groupId;
    private Long seasonId;

    private String referee, firstAssistant, secondAssistant, fourthReferee;

    // extra fields
    private String gameDateString;              // messo come campo altrimenti non lo ho in pagina
    private Double homexG, awayxG;              // usati su overview team
    private Long minutesPlayed;                 // usato su overview player

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGameDate() {
        return gameDate;
    }

    public void setGameDate(Date gameDate) {
        this.gameDate = gameDate;
        if (gameDate != null) {
            this.gameDateString = DateHelper.toStringExt(gameDate);
        } else {
            this.gameDateString = "";
        }
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public Long getHomeTeamId() {
        return homeTeamId;
    }

    public void setHomeTeamId(Long homeTeamId) {
        this.homeTeamId = homeTeamId;
    }

    public Long getAwayTeamId() {
        return awayTeamId;
    }

    public void setAwayTeamId(Long awayTeamId) {
        this.awayTeamId = awayTeamId;
    }

    public Long getTeamSx() {
        return teamSx;
    }

    public void setTeamSx(Long teamSx) {
        this.teamSx = teamSx;
    }

    public Long getTeamSx2() {
        return teamSx2;
    }

    public void setTeamSx2(Long teamSx2) {
        this.teamSx2 = teamSx2;
    }

    public Integer getHomeScore() {
        return homeScore;
    }

    public void setHomeScore(Integer homeScore) {
        this.homeScore = homeScore;
    }

    public Integer getAwayScore() {
        return awayScore;
    }

    public void setAwayScore(Integer awayScore) {
        this.awayScore = awayScore;
    }

    public String getHomeModule() {
        return homeModule;
    }

    public void setHomeModule(String homeModule) {
        this.homeModule = homeModule;
    }

    public String getAwayModule() {
        return awayModule;
    }

    public void setAwayModule(String awayModule) {
        this.awayModule = awayModule;
    }

    public Integer getHomeOi() {
        return homeOi;
    }

    public void setHomeOi(Integer homeOi) {
        this.homeOi = homeOi;
        this.awayIrd = homeOi;
    }

    public Integer getAwayOi() {
        return awayOi;
    }

    public void setAwayOi(Integer awayOi) {
        this.awayOi = awayOi;
        this.homeIrd = awayOi;
    }

    public Integer getHomeIrd() {
        return homeIrd;
    }

    public void setHomeIrd(Integer homeIrd) {
        this.homeIrd = homeIrd;
    }

    public Integer getAwayIrd() {
        return awayIrd;
    }

    public void setAwayIrd(Integer awayIrd) {
        this.awayIrd = awayIrd;
    }

    public Integer getHomePossesso() {
        return homePossesso;
    }

    public void setHomePossesso(Integer homePossesso) {
        this.homePossesso = homePossesso;
    }

    public Integer getAwayPossesso() {
        return awayPossesso;
    }

    public void setAwayPossesso(Integer awayPossesso) {
        this.awayPossesso = awayPossesso;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Integer getMatchDay() {
        return matchDay;
    }

    public void setMatchDay(Integer matchDay) {
        this.matchDay = matchDay;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public String getReferee() {
        return referee;
    }

    public void setReferee(String referee) {
        this.referee = referee;
    }

    public String getFirstAssistant() {
        return firstAssistant;
    }

    public void setFirstAssistant(String firstAssistant) {
        this.firstAssistant = firstAssistant;
    }

    public String getSecondAssistant() {
        return secondAssistant;
    }

    public void setSecondAssistant(String secondAssistant) {
        this.secondAssistant = secondAssistant;
    }

    public String getFourthReferee() {
        return fourthReferee;
    }

    public void setFourthReferee(String fourthReferee) {
        this.fourthReferee = fourthReferee;
    }

    public String getGameDateString() {
        return gameDateString;
    }

    public void setGameDateString(String gameDateString) {
        this.gameDateString = gameDateString;
    }

    public Double getHomexG() {
        return homexG;
    }

    public void setHomexG(Double homexG) {
        this.homexG = homexG;
    }

    public Double getAwayxG() {
        return awayxG;
    }

    public void setAwayxG(Double awayxG) {
        this.awayxG = awayxG;
    }

    public Long getMinutesPlayed() {
        return minutesPlayed;
    }

    public void setMinutesPlayed(Long minutesPlayed) {
        this.minutesPlayed = minutesPlayed;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }

    public JsonObject toJsonObject() {
        Gson gson = new Gson();
        String jsonString = gson.toJson(this);
        return gson.fromJson(jsonString, JsonObject.class);
    }
}
