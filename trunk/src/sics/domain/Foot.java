package sics.domain;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;

/**
 *
 * <AUTHOR>
 */
public class Foot {

    private Long id;
    private String desc, descEn, descFr, descEs;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDesc() {
        return desc;
    }

    public String getDesc(String language) {
        language = StringUtils.defaultIfEmpty(language, "en");
        switch (language) {
            case "it":
                return WordUtils.capitalize(desc);
            case "fr":
                return WordUtils.capitalize(descFr);
            case "es":
                return WordUtils.capitalize(descEs);
            default:
                return WordUtils.capitalize(descEn);
        }
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getDescEn() {
        return descEn;
    }

    public void setDescEn(String descEn) {
        this.descEn = descEn;
    }

    public String getDescFr() {
        return descFr;
    }

    public void setDescFr(String descFr) {
        this.descFr = descFr;
    }

    public String getDescEs() {
        return descEs;
    }

    public void setDescEs(String descEs) {
        this.descEs = descEs;
    }
}
