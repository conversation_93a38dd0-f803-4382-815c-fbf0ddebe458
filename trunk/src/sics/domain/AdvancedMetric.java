package sics.domain;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class AdvancedMetric {

    private Long id;
    private String code;
    private Object type;        // Long, Integer...

    private String desc, descEn, descFr, descEs;
    private String extendedDesc;

    private Boolean isOpposite;
    private Boolean isPercentage;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Object getType() {
        return type;
    }

    public void setType(Object type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
        if (StringUtils.isNotBlank(this.desc)) {
            this.desc = this.desc.replace("\n", " ");
        }
    }

    public String getDescEn() {
        return descEn;
    }

    public void setDescEn(String descEn) {
        this.descEn = descEn;
        if (StringUtils.isNotBlank(this.descEn)) {
            this.descEn = this.descEn.replace("\n", " ");
        }
    }

    public String getDescFr() {
        return descFr;
    }

    public void setDescFr(String descFr) {
        this.descFr = descFr;
        if (StringUtils.isNotBlank(this.descFr)) {
            this.descFr = this.descFr.replace("\n", " ");
        }
    }

    public String getDescEs() {
        return descEs;
    }

    public void setDescEs(String descEs) {
        this.descEs = descEs;
        if (StringUtils.isNotBlank(this.descEs)) {
            this.descEs = this.descEs.replace("\n", " ");
        }
    }

    public String getDesc(String language) {
        language = StringUtils.defaultIfEmpty(language, "en");
        switch (language) {
            case "it":
                return desc;
            case "fr":
                return descFr;
            case "es":
                return descEs;
            default:
                return descEn;
        }
    }

    public String getExtendedDesc() {
        return extendedDesc;
    }

    public void setExtendedDesc(String extendedDesc) {
        this.extendedDesc = extendedDesc;
    }

    public Boolean getIsOpposite() {
        return isOpposite;
    }

    public void setIsOpposite(Boolean isOpposite) {
        this.isOpposite = isOpposite;
    }

    public Boolean getIsPercentage() {
        return isPercentage;
    }

    public void setIsPercentage(Boolean isPercentage) {
        this.isPercentage = isPercentage;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
