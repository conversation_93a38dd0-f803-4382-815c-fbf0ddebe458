package sics.domain;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
public class DocumentRow implements Cloneable {

    private Long competitionId;
    private Long fixtureId;
    private Integer matchDay;
    private Long duration;
    private Integer homeScore, awayScore;
    private Integer homePoints, awayPoints;
    private String homeModule, awayModule;
    private Long matchMinutes, touches;
    private Long teamId, playerId, lastTeamId;
    private Long countryId, positionId, positionDetailId;
    private Integer bornYear, footId;
    private Boolean isHomeTeam;

    private Long eventTypeId;
    private String tagTypeId;

    private Double total, totalSum;
    private Double totalP90, total100Touches, totalAverage;
    // metà campo
    private Double dif, off;
    // zone
    private Double zoneOne, zoneTwo, zoneThree;
    // canali
    private Double channelOne, channelTwo, channelThree, channelFour, channelFive;
    // area
    private Double difArea, offArea;
    // area piccola
    private Double difSmallArea, offSmallArea;
    // intersezioni
    private Double difZoneOneChannelOne, difZoneOneChannelTwo, difZoneOneChannelThree, difZoneOneChannelFour, difZoneOneChannelFive;
    private Double difZoneTwoChannelOne, difZoneTwoChannelTwo, difZoneTwoChannelThree, difZoneTwoChannelFour, difZoneTwoChannelFive;
    private Double difZoneThreeChannelOne, difZoneThreeChannelTwo, difZoneThreeChannelThree, difZoneThreeChannelFour, difZoneThreeChannelFive;
    private Double offZoneOneChannelOne, offZoneOneChannelTwo, offZoneOneChannelThree, offZoneOneChannelFour, offZoneOneChannelFive;
    private Double offZoneTwoChannelOne, offZoneTwoChannelTwo, offZoneTwoChannelThree, offZoneTwoChannelFour, offZoneTwoChannelFive;
    private Double offZoneThreeChannelOne, offZoneThreeChannelTwo, offZoneThreeChannelThree, offZoneThreeChannelFour, offZoneThreeChannelFive;

    // metriche avanzate
    private Double ipo, ird, possesso;
    private Double xG, xGot, xA, xAe, xOvA, cxg, shotQuality, fieldTilt, ppda, axGot, fxGot, gkGp, npxG, opxG, oidi;
    private Double xT, xTPass, xTCond, iA, touchesMcd, touchesMco, touchesTo, touchesAa, touchesAz, touchesPfa, touchesSuff, touchesSuffMca, touchesSuffMc, touchesSuffTd, touchesSuffA, touchesSuffAz;

    // CAMPI EXTRA
    private Date gameDate;
    private Long seasonId;
    private String fieldName;
    private String zoneId;
    private Long playtime;

    @Override
    public DocumentRow clone() throws CloneNotSupportedException {
        DocumentRow clone = (DocumentRow) super.clone();
        return clone;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Integer getMatchDay() {
        return matchDay;
    }

    public void setMatchDay(Integer matchDay) {
        this.matchDay = matchDay;
    }

    public Long getDuration() {
        return duration;
    }

    public void setDuration(Long duration) {
        this.duration = duration;
    }

    public Integer getHomeScore() {
        return homeScore;
    }

    public void setHomeScore(Integer homeScore) {
        this.homeScore = homeScore;
    }

    public Integer getAwayScore() {
        return awayScore;
    }

    public void setAwayScore(Integer awayScore) {
        this.awayScore = awayScore;
    }

    public Integer getHomePoints() {
        return homePoints;
    }

    public void setHomePoints(Integer homePoints) {
        this.homePoints = homePoints;
    }

    public Integer getAwayPoints() {
        return awayPoints;
    }

    public void setAwayPoints(Integer awayPoints) {
        this.awayPoints = awayPoints;
    }

    public String getHomeModule() {
        return homeModule;
    }

    public void setHomeModule(String homeModule) {
        this.homeModule = homeModule;
    }

    public String getAwayModule() {
        return awayModule;
    }

    public void setAwayModule(String awayModule) {
        this.awayModule = awayModule;
    }

    public Long getMatchMinutes() {
        return matchMinutes;
    }

    public void setMatchMinutes(Long matchMinutes) {
        this.matchMinutes = matchMinutes;
    }

    public Long getTouches() {
        return touches;
    }

    public void setTouches(Long touches) {
        this.touches = touches;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Long getLastTeamId() {
        return lastTeamId;
    }

    public void setLastTeamId(Long lastTeamId) {
        this.lastTeamId = lastTeamId;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Long getPositionDetailId() {
        return positionDetailId;
    }

    public void setPositionDetailId(Long positionDetailId) {
        this.positionDetailId = positionDetailId;
    }

    public Integer getBornYear() {
        return bornYear;
    }

    public void setBornYear(Integer bornYear) {
        this.bornYear = bornYear;
    }

    public Integer getFootId() {
        return footId;
    }

    public void setFootId(Integer footId) {
        this.footId = footId;
    }

    public Boolean getIsHomeTeam() {
        return isHomeTeam;
    }

    public void setIsHomeTeam(Boolean isHomeTeam) {
        this.isHomeTeam = isHomeTeam;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getTagTypeId() {
        return tagTypeId;
    }

    public void setTagTypeId(String tagTypeId) {
        this.tagTypeId = tagTypeId;
    }

    public Double getTotal() {
        return total;
    }

    public void setTotal(Double total) {
        this.total = total;
    }

    public Double getTotalSum() {
        return totalSum;
    }

    public void setTotalSum(Double totalSum) {
        this.totalSum = totalSum;
    }

    public Double getTotalP90() {
        return totalP90;
    }

    public void setTotalP90(Double totalP90) {
        this.totalP90 = totalP90;
    }

    public Double getTotal100Touches() {
        return total100Touches;
    }

    public void setTotal100Touches(Double total100Touches) {
        this.total100Touches = total100Touches;
    }

    public Double getTotalAverage() {
        return totalAverage;
    }

    public void setTotalAverage(Double totalAverage) {
        this.totalAverage = totalAverage;
    }

    public Double getDif() {
        return dif;
    }

    public void setDif(Double dif) {
        this.dif = dif;
    }

    public Double getOff() {
        return off;
    }

    public void setOff(Double off) {
        this.off = off;
    }

    public Double getZoneOne() {
        return zoneOne;
    }

    public void setZoneOne(Double zoneOne) {
        this.zoneOne = zoneOne;
    }

    public Double getZoneTwo() {
        return zoneTwo;
    }

    public void setZoneTwo(Double zoneTwo) {
        this.zoneTwo = zoneTwo;
    }

    public Double getZoneThree() {
        return zoneThree;
    }

    public void setZoneThree(Double zoneThree) {
        this.zoneThree = zoneThree;
    }

    public Double getChannelOne() {
        return channelOne;
    }

    public void setChannelOne(Double channelOne) {
        this.channelOne = channelOne;
    }

    public Double getChannelTwo() {
        return channelTwo;
    }

    public void setChannelTwo(Double channelTwo) {
        this.channelTwo = channelTwo;
    }

    public Double getChannelThree() {
        return channelThree;
    }

    public void setChannelThree(Double channelThree) {
        this.channelThree = channelThree;
    }

    public Double getChannelFour() {
        return channelFour;
    }

    public void setChannelFour(Double channelFour) {
        this.channelFour = channelFour;
    }

    public Double getChannelFive() {
        return channelFive;
    }

    public void setChannelFive(Double channelFive) {
        this.channelFive = channelFive;
    }

    public Double getDifArea() {
        return difArea;
    }

    public void setDifArea(Double difArea) {
        this.difArea = difArea;
    }

    public Double getOffArea() {
        return offArea;
    }

    public void setOffArea(Double offArea) {
        this.offArea = offArea;
    }

    public Double getDifSmallArea() {
        return difSmallArea;
    }

    public void setDifSmallArea(Double difSmallArea) {
        this.difSmallArea = difSmallArea;
    }

    public Double getOffSmallArea() {
        return offSmallArea;
    }

    public void setOffSmallArea(Double offSmallArea) {
        this.offSmallArea = offSmallArea;
    }

    public Double getDifZoneOneChannelOne() {
        return difZoneOneChannelOne != null ? difZoneOneChannelOne : 0;
    }

    public void setDifZoneOneChannelOne(Double difZoneOneChannelOne) {
        this.difZoneOneChannelOne = difZoneOneChannelOne;
    }

    public Double getDifZoneOneChannelTwo() {
        return difZoneOneChannelTwo != null ? difZoneOneChannelTwo : 0;
    }

    public void setDifZoneOneChannelTwo(Double difZoneOneChannelTwo) {
        this.difZoneOneChannelTwo = difZoneOneChannelTwo;
    }

    public Double getDifZoneOneChannelThree() {
        return difZoneOneChannelThree != null ? difZoneOneChannelThree : 0;
    }

    public void setDifZoneOneChannelThree(Double difZoneOneChannelThree) {
        this.difZoneOneChannelThree = difZoneOneChannelThree;
    }

    public Double getDifZoneOneChannelFour() {
        return difZoneOneChannelFour != null ? difZoneOneChannelFour : 0;
    }

    public void setDifZoneOneChannelFour(Double difZoneOneChannelFour) {
        this.difZoneOneChannelFour = difZoneOneChannelFour;
    }

    public Double getDifZoneOneChannelFive() {
        return difZoneOneChannelFive != null ? difZoneOneChannelFive : 0;
    }

    public void setDifZoneOneChannelFive(Double difZoneOneChannelFive) {
        this.difZoneOneChannelFive = difZoneOneChannelFive;
    }

    public Double getDifZoneTwoChannelOne() {
        return difZoneTwoChannelOne != null ? difZoneTwoChannelOne : 0;
    }

    public void setDifZoneTwoChannelOne(Double difZoneTwoChannelOne) {
        this.difZoneTwoChannelOne = difZoneTwoChannelOne;
    }

    public Double getDifZoneTwoChannelTwo() {
        return difZoneTwoChannelTwo != null ? difZoneTwoChannelTwo : 0;
    }

    public void setDifZoneTwoChannelTwo(Double difZoneTwoChannelTwo) {
        this.difZoneTwoChannelTwo = difZoneTwoChannelTwo;
    }

    public Double getDifZoneTwoChannelThree() {
        return difZoneTwoChannelThree != null ? difZoneTwoChannelThree : 0;
    }

    public void setDifZoneTwoChannelThree(Double difZoneTwoChannelThree) {
        this.difZoneTwoChannelThree = difZoneTwoChannelThree;
    }

    public Double getDifZoneTwoChannelFour() {
        return difZoneTwoChannelFour != null ? difZoneTwoChannelFour : 0;
    }

    public void setDifZoneTwoChannelFour(Double difZoneTwoChannelFour) {
        this.difZoneTwoChannelFour = difZoneTwoChannelFour;
    }

    public Double getDifZoneTwoChannelFive() {
        return difZoneTwoChannelFive != null ? difZoneTwoChannelFive : 0;
    }

    public void setDifZoneTwoChannelFive(Double difZoneTwoChannelFive) {
        this.difZoneTwoChannelFive = difZoneTwoChannelFive;
    }

    public Double getDifZoneThreeChannelOne() {
        return difZoneThreeChannelOne != null ? difZoneThreeChannelOne : 0;
    }

    public void setDifZoneThreeChannelOne(Double difZoneThreeChannelOne) {
        this.difZoneThreeChannelOne = difZoneThreeChannelOne;
    }

    public Double getDifZoneThreeChannelTwo() {
        return difZoneThreeChannelTwo != null ? difZoneThreeChannelTwo : 0;
    }

    public void setDifZoneThreeChannelTwo(Double difZoneThreeChannelTwo) {
        this.difZoneThreeChannelTwo = difZoneThreeChannelTwo;
    }

    public Double getDifZoneThreeChannelThree() {
        return difZoneThreeChannelThree != null ? difZoneThreeChannelThree : 0;
    }

    public void setDifZoneThreeChannelThree(Double difZoneThreeChannelThree) {
        this.difZoneThreeChannelThree = difZoneThreeChannelThree;
    }

    public Double getDifZoneThreeChannelFour() {
        return difZoneThreeChannelFour != null ? difZoneThreeChannelFour : 0;
    }

    public void setDifZoneThreeChannelFour(Double difZoneThreeChannelFour) {
        this.difZoneThreeChannelFour = difZoneThreeChannelFour;
    }

    public Double getDifZoneThreeChannelFive() {
        return difZoneThreeChannelFive != null ? difZoneThreeChannelFive : 0;
    }

    public void setDifZoneThreeChannelFive(Double difZoneThreeChannelFive) {
        this.difZoneThreeChannelFive = difZoneThreeChannelFive;
    }

    public Double getOffZoneOneChannelOne() {
        return offZoneOneChannelOne != null ? offZoneOneChannelOne : 0;
    }

    public void setOffZoneOneChannelOne(Double offZoneOneChannelOne) {
        this.offZoneOneChannelOne = offZoneOneChannelOne;
    }

    public Double getOffZoneOneChannelTwo() {
        return offZoneOneChannelTwo != null ? offZoneOneChannelTwo : 0;
    }

    public void setOffZoneOneChannelTwo(Double offZoneOneChannelTwo) {
        this.offZoneOneChannelTwo = offZoneOneChannelTwo;
    }

    public Double getOffZoneOneChannelThree() {
        return offZoneOneChannelThree != null ? offZoneOneChannelThree : 0;
    }

    public void setOffZoneOneChannelThree(Double offZoneOneChannelThree) {
        this.offZoneOneChannelThree = offZoneOneChannelThree;
    }

    public Double getOffZoneOneChannelFour() {
        return offZoneOneChannelFour != null ? offZoneOneChannelFour : 0;
    }

    public void setOffZoneOneChannelFour(Double offZoneOneChannelFour) {
        this.offZoneOneChannelFour = offZoneOneChannelFour;
    }

    public Double getOffZoneOneChannelFive() {
        return offZoneOneChannelFive != null ? offZoneOneChannelFive : 0;
    }

    public void setOffZoneOneChannelFive(Double offZoneOneChannelFive) {
        this.offZoneOneChannelFive = offZoneOneChannelFive;
    }

    public Double getOffZoneTwoChannelOne() {
        return offZoneTwoChannelOne != null ? offZoneTwoChannelOne : 0;
    }

    public void setOffZoneTwoChannelOne(Double offZoneTwoChannelOne) {
        this.offZoneTwoChannelOne = offZoneTwoChannelOne;
    }

    public Double getOffZoneTwoChannelTwo() {
        return offZoneTwoChannelTwo != null ? offZoneTwoChannelTwo : 0;
    }

    public void setOffZoneTwoChannelTwo(Double offZoneTwoChannelTwo) {
        this.offZoneTwoChannelTwo = offZoneTwoChannelTwo;
    }

    public Double getOffZoneTwoChannelThree() {
        return offZoneTwoChannelThree != null ? offZoneTwoChannelThree : 0;
    }

    public void setOffZoneTwoChannelThree(Double offZoneTwoChannelThree) {
        this.offZoneTwoChannelThree = offZoneTwoChannelThree;
    }

    public Double getOffZoneTwoChannelFour() {
        return offZoneTwoChannelFour != null ? offZoneTwoChannelFour : 0;
    }

    public void setOffZoneTwoChannelFour(Double offZoneTwoChannelFour) {
        this.offZoneTwoChannelFour = offZoneTwoChannelFour;
    }

    public Double getOffZoneTwoChannelFive() {
        return offZoneTwoChannelFive != null ? offZoneTwoChannelFive : 0;
    }

    public void setOffZoneTwoChannelFive(Double offZoneTwoChannelFive) {
        this.offZoneTwoChannelFive = offZoneTwoChannelFive;
    }

    public Double getOffZoneThreeChannelOne() {
        return offZoneThreeChannelOne != null ? offZoneThreeChannelOne : 0;
    }

    public void setOffZoneThreeChannelOne(Double offZoneThreeChannelOne) {
        this.offZoneThreeChannelOne = offZoneThreeChannelOne;
    }

    public Double getOffZoneThreeChannelTwo() {
        return offZoneThreeChannelTwo != null ? offZoneThreeChannelTwo : 0;
    }

    public void setOffZoneThreeChannelTwo(Double offZoneThreeChannelTwo) {
        this.offZoneThreeChannelTwo = offZoneThreeChannelTwo;
    }

    public Double getOffZoneThreeChannelThree() {
        return offZoneThreeChannelThree != null ? offZoneThreeChannelThree : 0;
    }

    public void setOffZoneThreeChannelThree(Double offZoneThreeChannelThree) {
        this.offZoneThreeChannelThree = offZoneThreeChannelThree;
    }

    public Double getOffZoneThreeChannelFour() {
        return offZoneThreeChannelFour != null ? offZoneThreeChannelFour : 0;
    }

    public void setOffZoneThreeChannelFour(Double offZoneThreeChannelFour) {
        this.offZoneThreeChannelFour = offZoneThreeChannelFour;
    }

    public Double getOffZoneThreeChannelFive() {
        return offZoneThreeChannelFive != null ? offZoneThreeChannelFive : 0;
    }

    public void setOffZoneThreeChannelFive(Double offZoneThreeChannelFive) {
        this.offZoneThreeChannelFive = offZoneThreeChannelFive;
    }

    public Double getIpo() {
        return ipo;
    }

    public void setIpo(Double ipo) {
        this.ipo = ipo;
    }

    public Double getIrd() {
        return ird;
    }

    public void setIrd(Double ird) {
        this.ird = ird;
    }

    public Double getPossesso() {
        return possesso;
    }

    public void setPossesso(Double possesso) {
        this.possesso = possesso;
    }

    public Double getxG() {
        return xG;
    }

    public void setxG(Double xG) {
        this.xG = xG;
    }

    public Double getxGot() {
        return xGot;
    }

    public void setxGot(Double xGot) {
        this.xGot = xGot;
    }

    public Double getxA() {
        return xA;
    }

    public void setxA(Double xA) {
        this.xA = xA;
    }

    public Double getxAe() {
        return xAe;
    }

    public void setxAe(Double xAe) {
        this.xAe = xAe;
    }

    public Double getxOvA() {
        return xOvA;
    }

    public void setxOvA(Double xOvA) {
        this.xOvA = xOvA;
    }

    public Double getCxg() {
        return cxg;
    }

    public void setCxg(Double cxg) {
        this.cxg = cxg;
    }

    public Double getShotQuality() {
        return shotQuality;
    }

    public void setShotQuality(Double shotQuality) {
        this.shotQuality = shotQuality;
    }

    public Double getFieldTilt() {
        return fieldTilt;
    }

    public void setFieldTilt(Double fieldTilt) {
        this.fieldTilt = fieldTilt;
    }

    public Double getPpda() {
        return ppda;
    }

    public void setPpda(Double ppda) {
        this.ppda = ppda;
    }

    public Double getAxGot() {
        return axGot;
    }

    public void setAxGot(Double axGot) {
        this.axGot = axGot;
    }

    public Double getFxGot() {
        return fxGot;
    }

    public void setFxGot(Double fxGot) {
        this.fxGot = fxGot;
    }

    public Double getGkGp() {
        return gkGp;
    }

    public void setGkGp(Double gkGp) {
        this.gkGp = gkGp;
    }

    public Double getNpxG() {
        return npxG;
    }

    public void setNpxG(Double npxG) {
        this.npxG = npxG;
    }

    public Double getOpxG() {
        return opxG;
    }

    public void setOpxG(Double opxG) {
        this.opxG = opxG;
    }

    public Double getOidi() {
        return oidi;
    }

    public void setOidi(Double oidi) {
        this.oidi = oidi;
    }

    public Double getxT() {
        return xT;
    }

    public void setxT(Double xT) {
        this.xT = xT;
    }

    public Double getxTPass() {
        return xTPass;
    }

    public void setxTPass(Double xTPass) {
        this.xTPass = xTPass;
    }

    public Double getxTCond() {
        return xTCond;
    }

    public void setxTCond(Double xTCond) {
        this.xTCond = xTCond;
    }

    public Double getiA() {
        return iA;
    }

    public void setiA(Double iA) {
        this.iA = iA;
    }

    public Double getTouchesMcd() {
        return touchesMcd;
    }

    public void setTouchesMcd(Double touchesMcd) {
        this.touchesMcd = touchesMcd;
    }

    public Double getTouchesMco() {
        return touchesMco;
    }

    public void setTouchesMco(Double touchesMco) {
        this.touchesMco = touchesMco;
    }

    public Double getTouchesTo() {
        return touchesTo;
    }

    public void setTouchesTo(Double touchesTo) {
        this.touchesTo = touchesTo;
    }

    public Double getTouchesAa() {
        return touchesAa;
    }

    public void setTouchesAa(Double touchesAa) {
        this.touchesAa = touchesAa;
    }

    public Double getTouchesAz() {
        return touchesAz;
    }

    public void setTouchesAz(Double touchesAz) {
        this.touchesAz = touchesAz;
    }

    public Double getTouchesPfa() {
        return touchesPfa;
    }

    public void setTouchesPfa(Double touchesPfa) {
        this.touchesPfa = touchesPfa;
    }

    public Double getTouchesSuff() {
        return touchesSuff;
    }

    public void setTouchesSuff(Double touchesSuff) {
        this.touchesSuff = touchesSuff;
    }

    public Double getTouchesSuffMca() {
        return touchesSuffMca;
    }

    public void setTouchesSuffMca(Double touchesSuffMca) {
        this.touchesSuffMca = touchesSuffMca;
    }

    public Double getTouchesSuffMc() {
        return touchesSuffMc;
    }

    public void setTouchesSuffMc(Double touchesSuffMc) {
        this.touchesSuffMc = touchesSuffMc;
    }

    public Double getTouchesSuffTd() {
        return touchesSuffTd;
    }

    public void setTouchesSuffTd(Double touchesSuffTd) {
        this.touchesSuffTd = touchesSuffTd;
    }

    public Double getTouchesSuffA() {
        return touchesSuffA;
    }

    public void setTouchesSuffA(Double touchesSuffA) {
        this.touchesSuffA = touchesSuffA;
    }

    public Double getTouchesSuffAz() {
        return touchesSuffAz;
    }

    public void setTouchesSuffAz(Double touchesSuffAz) {
        this.touchesSuffAz = touchesSuffAz;
    }

    public Date getGameDate() {
        return gameDate;
    }

    public void setGameDate(Date gameDate) {
        this.gameDate = gameDate;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getZoneId() {
        return zoneId;
    }

    public void setZoneId(String zoneId) {
        this.zoneId = zoneId;
    }

    public Long getPlaytime() {
        return playtime;
    }

    public void setPlaytime(Long playtime) {
        this.playtime = playtime;
    }
}
