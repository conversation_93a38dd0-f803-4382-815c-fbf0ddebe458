package sics.domain;

import com.google.gson.Gson;

/**
 *
 * <AUTHOR>
 */
public class Season {

    private Long id;
    private String name;
    private Boolean visible;

    // extra fields
    private Long nonSolarId, solarId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public Long getNonSolarId() {
        return nonSolarId;
    }

    public void setNonSolarId(Long nonSolarId) {
        this.nonSolarId = nonSolarId;
    }

    public Long getSolarId() {
        return solarId;
    }

    public void setSolarId(Long solarId) {
        this.solarId = solarId;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
