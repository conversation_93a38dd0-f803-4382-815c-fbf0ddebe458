package sics.domain;

import java.awt.*;

/**
 *
 * <AUTHOR>
 */
public class FixtureDetails {
    
    private Long id;
    private Long fixtureId;
    private Long teamSx;                // team a sinistra primo tempo
    private Long teamSx2;               // team a sinistra primo tempo supplementare
    private String homeTeamColor;       // colore maglie partita
    private String awayTeamColor;       // colore maglie partita
    private Long startTime1, endTime1, startTime2, endTime2, startTime3, endTime3, startTime4, endTime4;

    private Long homeTeamId, awayTeamId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Long getTeamSx() {
        return teamSx;
    }

    public void setTeamSx(Long teamSx) {
        this.teamSx = teamSx;
    }

    public Long getTeamSx2() {
        return teamSx2;
    }

    public void setTeamSx2(Long teamSx2) {
        this.teamSx2 = teamSx2;
    }

    public String getHomeTeamColor() {
        return homeTeamColor;
    }

    public Color getHomeColor() {
        Color colorTeam;
        if (homeTeamColor != null && homeTeamColor.contains("x")) {
            String[] colorString = homeTeamColor.split("x");
            if (colorString.length == 4) {
                colorTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
            } else {
                colorTeam = Color.BLUE;
            }
        } else if (homeTeamColor != null && homeTeamColor.length() == 6) {
            colorTeam = Color.decode("#" + homeTeamColor);
        } else {
            colorTeam = Color.BLUE;
        }

        return colorTeam;
    }

    public void setHomeTeamColor(String homeTeamColor) {
        this.homeTeamColor = homeTeamColor;
    }

    public String getAwayTeamColor() {
        return awayTeamColor;
    }

    public Color getAwayColor() {
        Color colorTeam;
        if (awayTeamColor != null && awayTeamColor.contains("x")) {
            String[] colorString = awayTeamColor.split("x");
            if (colorString.length == 4) {
                colorTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
            } else {
                colorTeam = Color.BLUE;
            }
        } else if (awayTeamColor != null && awayTeamColor.length() == 6) {
            colorTeam = Color.decode("#" + awayTeamColor);
        } else {
            colorTeam = Color.BLUE;
        }

        return colorTeam;
    }

    public void setAwayTeamColor(String awayTeamColor) {
        this.awayTeamColor = awayTeamColor;
    }

    public Long getStartTime1() {
        return startTime1;
    }

    public void setStartTime1(Long startTime1) {
        this.startTime1 = startTime1;
    }

    public Long getEndTime1() {
        return endTime1;
    }

    public void setEndTime1(Long endTime1) {
        this.endTime1 = endTime1;
    }

    public Long getStartTime2() {
        return startTime2;
    }

    public void setStartTime2(Long startTime2) {
        this.startTime2 = startTime2;
    }

    public Long getEndTime2() {
        return endTime2;
    }

    public void setEndTime2(Long endTime2) {
        this.endTime2 = endTime2;
    }

    public Long getStartTime3() {
        return startTime3;
    }

    public void setStartTime3(Long startTime3) {
        this.startTime3 = startTime3;
    }

    public Long getEndTime3() {
        return endTime3;
    }

    public void setEndTime3(Long endTime3) {
        this.endTime3 = endTime3;
    }

    public Long getStartTime4() {
        return startTime4;
    }

    public void setStartTime4(Long startTime4) {
        this.startTime4 = startTime4;
    }

    public Long getEndTime4() {
        return endTime4;
    }

    public void setEndTime4(Long endTime4) {
        this.endTime4 = endTime4;
    }

    public long getMatchMinutes() {
        long minutes = 0;
        if (startTime1 != null) {
            if (endTime1 != null) {
                minutes += endTime1 - startTime1;
            }
        }
        if (startTime2 != null) {
            if (endTime2 != null) {
                minutes += endTime2 - startTime2;
            }
        }
        if (startTime3 != null) {
            if (endTime3 != null) {
                minutes += endTime3 - startTime3;
            }
        }
        if (startTime4 != null) {
            if (endTime4 != null) {
                minutes += endTime4 - startTime4;
            }
        }

        // millis to minutes
        minutes = minutes / 1000 / 60;
        return minutes;
    }

    public Long getHomeTeamId() {
        return homeTeamId;
    }

    public void setHomeTeamId(Long homeTeamId) {
        this.homeTeamId = homeTeamId;
    }

    public Long getAwayTeamId() {
        return awayTeamId;
    }

    public void setAwayTeamId(Long awayTeamId) {
        this.awayTeamId = awayTeamId;
    }
}
