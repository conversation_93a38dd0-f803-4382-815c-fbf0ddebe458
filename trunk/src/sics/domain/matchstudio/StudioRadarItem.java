package sics.domain.matchstudio;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import sics.domain.FixturePlayer;

/**
 *
 * <AUTHOR>
 */
public class StudioRadarItem {

    private String text;
    private Integer totalPlayers;
    private Map<Long, Integer> playersValueMap;
    private Double averageValue;

    public StudioRadarItem(String text) {
        this.text = text;
        this.totalPlayers = 0;
        this.playersValueMap = new HashMap<>();
        this.averageValue = 0D;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getTotalPlayers() {
        return totalPlayers;
    }

    public void setTotalPlayers(Integer totalPlayers) {
        this.totalPlayers = totalPlayers;
    }

    public void setTotalPlayers(List<FixturePlayer> fixturePlayersMap, Long teamId) {
        int tmpTotalPlayers = 0;
        if (fixturePlayersMap != null && !fixturePlayersMap.isEmpty()) {
            for (FixturePlayer fixturePlayer : fixturePlayersMap) {
                if (fixturePlayer.getTeamId() != null && Long.compare(fixturePlayer.getTeamId(), teamId) == 0) {
                    if (fixturePlayer.getPlayTime() != null && fixturePlayer.getPlayTime() > 0) {
                        if (fixturePlayer.getPositionId() != null && fixturePlayer.getPositionId() != 1) {
                            // portieri esclusi
                            tmpTotalPlayers++;
                        }
                    }
                }
            }
        }

        this.totalPlayers = tmpTotalPlayers;
    }

    public Map<Long, Integer> getPlayersValueMap() {
        return playersValueMap;
    }

    public void setPlayersValueMap(Map<Long, Integer> playersValueMap) {
        this.playersValueMap = playersValueMap;
    }

    public Double getAverageValue() {
        return averageValue;
    }

    public void setAverageValue(Double averageValue) {
        this.averageValue = averageValue;
    }

    public Integer getMinPlayerValue() {
        Integer minValue = null;

        if (!playersValueMap.isEmpty()) {
            for (Integer value : playersValueMap.values()) {
                if (minValue == null || value < minValue) {
                    minValue = value;
                }
            }
        }

        if (minValue == null) {
            minValue = 0;
        }
        return minValue;
    }

    public Integer getMaxPlayerValue() {
        Integer maxValue = null;

        if (!playersValueMap.isEmpty()) {
            for (Integer value : playersValueMap.values()) {
                if (maxValue == null || value > maxValue) {
                    maxValue = value;
                }
            }
        }

        if (maxValue == null) {
            maxValue = 0;
        }
        return maxValue;
    }

    public Integer getTotalPlayerValue() {
        int totalValue = 0;

        if (!playersValueMap.isEmpty()) {
            for (Integer value : playersValueMap.values()) {
                totalValue += value;
            }
        }

        return totalValue;
    }
}
