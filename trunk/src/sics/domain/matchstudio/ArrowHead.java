package sics.domain.matchstudio;

import java.awt.geom.Path2D;

public class ArrowHead extends Path2D.Double {

    public ArrowHead() {
        int size = 10;
        moveTo(0, size);
        lineTo(size / 2, 0);
        lineTo(size, size);
        closePath();
    }

    public ArrowHead(int size) {
        moveTo(0, size);
        lineTo(size / 2, 0);
        lineTo(size, size);
        closePath();
    }

}
