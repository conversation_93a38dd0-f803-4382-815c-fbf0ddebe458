package sics.domain.matchstudio;

import sics.domain.Player;

public class PlayerRanking extends Player {

    private Long teamId;
    private Integer counter;

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Integer getCounter() {
        return counter;
    }

    public void setCounter(Integer counter) {
        this.counter = counter;
    }
}
