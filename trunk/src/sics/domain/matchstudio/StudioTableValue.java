package sics.domain.matchstudio;

/**
 *
 * <AUTHOR>
 */
public class StudioTableValue {

    private Integer total, firstHalf, secondHalf;
    private Double tmpValue, tmpValueFirstHalf, tmpValueSecondHalf;

    public StudioTableValue() {
        this.total = 0;
        this.firstHalf = 0;
        this.secondHalf = 0;
        this.tmpValue = 0D;
        this.tmpValueFirstHalf = 0D;
        this.tmpValueSecondHalf = 0D;
    }

    public Integer getTotal() {
        return total;
    }

    public void increaseTotal() {
        this.total++;
    }

    public void increaseTotal(Integer amount) {
        this.total += amount;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public Integer getFirstHalf() {
        return firstHalf;
    }

    public void increaseFirstHalf() {
        this.firstHalf++;
    }

    public void increaseFirstHalf(Integer amount) {
        this.firstHalf += amount;
    }

    public void setFirstHalf(Integer firstHalf) {
        this.firstHalf = firstHalf;
    }

    public Integer getSecondHalf() {
        return secondHalf;
    }
    
    public void increaseSecondHalf() {
        this.secondHalf++;
    }

    public void increaseSecondHalf(Integer amount) {
        this.secondHalf += amount;
    }

    public void setSecondHalf(Integer secondHalf) {
        this.secondHalf = secondHalf;
    }

    public Double getTmpValue() {
        return tmpValue;
    }

    public void increaseTmpValue(Double amount) {
        this.tmpValue += amount;
    }

    public void setTmpValue(Double tmpValue) {
        this.tmpValue = tmpValue;
    }

    public Double getTmpValueFirstHalf() {
        return tmpValueFirstHalf;
    }

    public void increaseTmpValueFirstHalf(Double amount) {
        this.tmpValueFirstHalf += amount;
    }

    public void setTmpValueFirstHalf(Double tmpValueFirstHalf) {
        this.tmpValueFirstHalf = tmpValueFirstHalf;
    }

    public Double getTmpValueSecondHalf() {
        return tmpValueSecondHalf;
    }
    
    public void increaseTmpValueSecondHalf(Double amount) {
        this.tmpValueSecondHalf += amount;
    }

    public void setTmpValueSecondHalf(Double tmpValueSecondHalf) {
        this.tmpValueSecondHalf = tmpValueSecondHalf;
    }
}
