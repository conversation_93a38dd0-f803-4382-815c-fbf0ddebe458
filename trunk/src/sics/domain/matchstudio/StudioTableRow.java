package sics.domain.matchstudio;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class StudioTableRow {

    private Integer number;
    private String text;
    private List<StudioTableValue> values;
    private Integer type;       // 1=standard, 2=percentuale

    public StudioTableRow(String text) {
        this.text = text;
        this.values = new ArrayList();
        this.type = 1;
    }

    public Integer getNumber() {
        return number;
    }

    public void setNumber(Integer number) {
        this.number = number;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public List<StudioTableValue> getValues() {
        return values;
    }

    public String getValuesText(int index) {
        List<Integer> tmpValues = new ArrayList<>();
        for (StudioTableValue value : values) {
            switch (index) {
                case 0:
                    tmpValues.add(value.getTotal());
                    break;
                case 1:
                    tmpValues.add(value.getFirstHalf());
                    break;
                case 2:
                    tmpValues.add(value.getSecondHalf());
                    break;
                default:
                    break;
            }
        }

        if (type == 2) {
            return StringUtils.join(tmpValues, "%/") + "%";
        } else {
            return StringUtils.join(tmpValues, "/");
        }
    }

    public void setValues(List<StudioTableValue> values) {
        this.values = values;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
