package sics.domain;

import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import sics.controller.BaseController;

/**
 *
 * <AUTHOR>
 */
public class DocumentFilter {

    private Long teamId, playerId, eventTypeId;

    private List<Long> eventTypeIds, playerIds, fixtureIds, tagTypeIds, matchDays, countryIds, positionIds, positionDetailIds, footIds;
    private List<Integer> bornYears;
    private List<String> tmpTagTypeIds;
    private List<String> homeModules, awayModules;
    private List<String> fields;

    // extra fields
    private List<Long> teamIds;
    private List<AdvancedMetric> advancedMetrics;

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public List<Long> getEventTypeIds() {
        return eventTypeIds;
    }

    public void setEventTypeIds(List<Long> eventTypeIds) {
        this.eventTypeIds = eventTypeIds;
    }

    public List<Long> getPlayerIds() {
        return playerIds;
    }

    public void setPlayerIds(List<Long> playerIds) {
        this.playerIds = playerIds;
    }

    public List<Long> getFixtureIds() {
        return fixtureIds;
    }

    public void setFixtureIds(List<Long> fixtureIds) {
        this.fixtureIds = fixtureIds;
    }

    public List<Long> getTagTypeIds() {
        return tagTypeIds;
    }

    public void setTagTypeIds(List<Long> tagTypeIds) {
        this.tagTypeIds = tagTypeIds;
    }

    public List<Long> getMatchDays() {
        return matchDays;
    }

    public void setMatchDays(List<Long> matchDays) {
        this.matchDays = matchDays;
    }

    public List<Long> getCountryIds() {
        return countryIds;
    }

    public void setCountryIds(List<Long> countryIds) {
        this.countryIds = countryIds;
    }

    public List<Long> getPositionIds() {
        return positionIds;
    }

    public void setPositionIds(List<Long> positionIds) {
        this.positionIds = positionIds;
    }

    public List<Long> getPositionDetailIds() {
        return positionDetailIds;
    }

    public void setPositionDetailIds(List<Long> positionDetailIds) {
        this.positionDetailIds = positionDetailIds;
    }

    public List<Long> getFootIds() {
        return footIds;
    }

    public void setFootIds(List<Long> footIds) {
        this.footIds = footIds;
    }

    public List<Integer> getBornYears() {
        return bornYears;
    }

    public void setBornYears(List<Integer> bornYears) {
        this.bornYears = bornYears;
    }

    public List<String> getTmpTagTypeIds() {
        return tmpTagTypeIds;
    }

    public void setTmpTagTypeIds(List<String> tmpTagTypeIds) {
        this.tmpTagTypeIds = tmpTagTypeIds;
        if (tmpTagTypeIds != null && !tmpTagTypeIds.isEmpty()) {
            this.tagTypeIds = new ArrayList<>();
            for (String tmpTagTypeId : tmpTagTypeIds) {
                for (String tagTypeId : StringUtils.split(tmpTagTypeId, "|")) {
                    Long tagType = Long.valueOf(tagTypeId);
                    if (!this.tagTypeIds.contains(tagType)) {
                        this.tagTypeIds.add(tagType);
                    }
                }
            }
        }
    }

    public List<String> getHomeModules() {
        return homeModules;
    }

    public void setHomeModules(List<String> homeModules) {
        this.homeModules = homeModules;
    }

    public List<String> getAwayModules() {
        return awayModules;
    }

    public void setAwayModules(List<String> awayModules) {
        this.awayModules = awayModules;
    }

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public List<Long> getTeamIds() {
        return teamIds;
    }

    public void setTeamIds(List<Long> teamIds) {
        this.teamIds = teamIds;
    }

    public List<AdvancedMetric> getAdvancedMetrics() {
        return advancedMetrics;
    }

    public void setAdvancedMetrics(List<AdvancedMetric> advancedMetrics) {
        this.advancedMetrics = advancedMetrics;
    }

    public void calculateExtraFields() {
        this.advancedMetrics = new ArrayList<>();
        if (fields != null && !fields.isEmpty()) {
            for (AdvancedMetric metric : BaseController.getAdvancedMetrics().values()) {
                String fieldName = metric.getCode();
                if (StringUtils.isNotBlank(fieldName)) {
                    if (fields.contains(fieldName)) {
                        advancedMetrics.add(metric);
                    }
                }
            }
        }
    }

    public Document getMongoDocument() {
        Document row = new Document();

        row.append("teamId", teamId);
        if (playerId != null) {
            row.append("playerId", playerId);
        }
        if (eventTypeId != null) {
            row.append("eventTypeId", eventTypeId);
        }
        if (eventTypeIds != null && !eventTypeIds.isEmpty()) {
            row.append("eventTypeIds", eventTypeIds);
        }
        if (tagTypeIds != null && !tagTypeIds.isEmpty()) {
            if (eventTypeId != null) {
                // tutti i tag di un team / player non mi servono a nulla
                row.append("tagTypeIds", tagTypeIds);
            }
        }
        if (fixtureIds != null && !fixtureIds.isEmpty()) {
            row.append("fixtureIds", fixtureIds);
        }
        if (homeModules != null && !homeModules.isEmpty()) {
            row.append("homeModules", homeModules);
        }
        if (awayModules != null && !awayModules.isEmpty()) {
            row.append("awayModules", awayModules);
        }
        if (matchDays != null && !matchDays.isEmpty()) {
            row.append("matchDays", matchDays);
        }
        if (playerIds != null && !playerIds.isEmpty()) {
            row.append("playerIds", playerIds);
        }
        if (countryIds != null && !countryIds.isEmpty()) {
            row.append("countryIds", countryIds);
        }
        if (positionIds != null && !positionIds.isEmpty()) {
            row.append("positionIds", positionIds);
        }
        if (positionDetailIds != null && !positionDetailIds.isEmpty()) {
            row.append("positionDetailIds", positionDetailIds);
        }
        if (footIds != null && !footIds.isEmpty()) {
            row.append("footIds", footIds);
        }
        if (bornYears != null && !bornYears.isEmpty()) {
            row.append("bornYears", bornYears);
        }
        if (fields != null && !fields.isEmpty()) {
            row.append("fields", fields);
        }

        return row;
    }
}
