package sics.domain;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SimilarityDocumentRow implements Cloneable {

    private Long playerId, positionId;
    private Map<Long, Double> statsTypeMap;
    
    // extra fields
    private Double similarity, similarityPercentage;
    private Map<Long, Double> adjustedStatsTypeMap;

    @Override
    public SimilarityDocumentRow clone() throws CloneNotSupportedException {
        SimilarityDocumentRow clone = (SimilarityDocumentRow) super.clone();
        return clone;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Long getPositionId() {
        return positionId;
    }

    public void setPositionId(Long positionId) {
        this.positionId = positionId;
    }

    public Map<Long, Double> getStatsTypeMap() {
        return statsTypeMap;
    }

    public void setStatsTypeMap(Map<Long, Double> statsTypeMap) {
        this.statsTypeMap = statsTypeMap;
    }

    public Double getSimilarity() {
        return similarity;
    }

    public void setSimilarity(Double similarity) {
        this.similarity = similarity;
    }

    public Double getSimilarityPercentage() {
        return similarityPercentage;
    }

    public void setSimilarityPercentage(Double similarityPercentage) {
        this.similarityPercentage = similarityPercentage;
    }

    public Map<Long, Double> getAdjustedStatsTypeMap() {
        return adjustedStatsTypeMap;
    }

    public void setAdjustedStatsTypeMap(Map<Long, Double> adjustedStatsTypeMap) {
        this.adjustedStatsTypeMap = adjustedStatsTypeMap;
    }
}
