package sics.domain;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
public class UserPermission implements Serializable {
    
    private Long id;
    private Long userId;
    private Integer tab;        // se 999 allora ho accesso a tutto
    private Integer role;       // 0 = visualizzazione + modifica, 1 = solo visualizzazione
    private String comment;     // usato per definire quante partite possono fare al giorno

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getTab() {
        return tab;
    }

    public void setTab(Integer tab) {
        this.tab = tab;
    }

    public Integer getRole() {
        return role;
    }

    public void setRole(Integer role) {
        this.role = role;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
