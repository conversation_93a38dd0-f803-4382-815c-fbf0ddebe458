package sics.domain;

import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Groupset {

    private Long id;
    private String name;
    private Integer distribute;
    private Integer type;                   // 0 = tutto, 1 = sicstv, 2 = vm
    private String pers;
    private Integer maxDownload;            // numero massimo download
    private Integer maxDownloadReport;      // numero massimo download match studio
    private Integer cloudSize;              // GB di spazio S3 per upload di partite e clip
    private Long exportTimeLimit;           // Limite settimanale minuti esportazioni
    private Integer trackingVideo;          // massimo numero di video/clip caricabili
    private Integer trackingMinute;         // minuti di tracking AWS
    private Integer upload;
    private Long teamId;
    private Long sportId;
    private Integer genere;
    private Integer guest;

    private List<String> competitionList;
    private String competitions;            // lista di competizioni a cui ha accesso, separato dalla virgola
    private List<User> userList;
    private String users;                   // lista di utenti per ogni gruppo
    private Boolean isEditable;             // usato per mandare in pagina il valore

    public Groupset() {
        this.maxDownload = 14;
        this.maxDownloadReport = 28;
        this.cloudSize = 100;
        this.exportTimeLimit = 100L;
        this.competitionList = new ArrayList<>();
        this.userList = new ArrayList<>();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDistribute() {
        return distribute;
    }

    public void setDistribute(Integer distribute) {
        this.distribute = distribute;
        setIsEditable(type != null && Integer.compare(type, 1) == 0 && distribute != null && Integer.compare(distribute, 0) == 0);
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
        setIsEditable(type != null && Integer.compare(type, 1) == 0 && distribute != null && Integer.compare(distribute, 0) == 0);
    }

    public String getPers() {
        return pers;
    }

    public void setPers(String pers) {
        this.pers = pers;
    }

    public Integer getMaxDownload() {
        return maxDownload;
    }

    public void setMaxDownload(Integer maxDownload) {
        this.maxDownload = maxDownload;
    }

    public Integer getMaxDownloadReport() {
        return maxDownloadReport;
    }

    public void setMaxDownloadReport(Integer maxDownloadReport) {
        this.maxDownloadReport = maxDownloadReport;
    }

    public Integer getCloudSize() {
        return cloudSize;
    }

    public void setCloudSize(Integer cloudSize) {
        this.cloudSize = cloudSize;
    }

    public Long getExportTimeLimit() {
        return exportTimeLimit;
    }

    public void setExportTimeLimit(Long exportTimeLimit) {
        this.exportTimeLimit = exportTimeLimit;
    }

    public Integer getTrackingVideo() {
        return trackingVideo;
    }

    public void setTrackingVideo(Integer trackingVideo) {
        this.trackingVideo = trackingVideo;
    }

    public Integer getTrackingMinute() {
        return trackingMinute;
    }

    public void setTrackingMinute(Integer trackingMinute) {
        this.trackingMinute = trackingMinute;
    }

    public Integer getUpload() {
        return upload;
    }

    public void setUpload(Integer upload) {
        this.upload = upload;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getSportId() {
        return sportId;
    }

    public void setSportId(Long sportId) {
        this.sportId = sportId;
    }

    public Integer getGenere() {
        return genere;
    }

    public void setGenere(Integer genere) {
        this.genere = genere;
    }

    public Integer getGuest() {
        return guest;
    }

    public void setGuest(Integer guest) {
        this.guest = guest;
    }

    public List<String> getCompetitionList() {
        return competitionList;
    }

    public void setCompetitionList(List<String> competitionList) {
        this.competitionList = competitionList;
    }

    public int getCompetitionListAmount() {
        if (this.competitionList == null) {
            return 0;
        }
        return this.competitionList.size();
    }

    public String getCompetitions() {
        return competitions;
    }

    public void setCompetitions(String competitions) {
        this.competitions = competitions;

        if (StringUtils.isNotBlank(this.competitions)) {
            setCompetitionList(new ArrayList<>(Arrays.asList(StringUtils.split(this.competitions, ","))));
        }
    }

    public List<User> getUserList() {
        return userList;
    }

    public void setUserList(List<User> userList) {
        this.userList = userList;
    }

    public String getUsers() {
        return users;
    }

    public void setUsers(String users) {
        this.users = users;

        if (StringUtils.isNotBlank(this.users)) {
            List<String> splitted = new ArrayList<>(Arrays.asList(StringUtils.splitByWholeSeparator(this.users, "-_-")));
            if (!splitted.isEmpty()) {
                for (String split : splitted) {
                    List<String> splitParts = new ArrayList<>(Arrays.asList(StringUtils.splitByWholeSeparator(split, "||")));
                    if (splitParts.size() == 3) {
                        User tmpUser = new User();
                        tmpUser.setFirstName(splitParts.get(0));
                        tmpUser.setLastName(splitParts.get(1));
                        tmpUser.setEmail(splitParts.get(2));
                        this.userList.add(tmpUser);
                    }
                }
            }
        }
    }

    public Boolean getIsEditable() {
        return isEditable;
    }

    public void setIsEditable(Boolean isEditable) {
        this.isEditable = isEditable;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
