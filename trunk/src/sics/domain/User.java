package sics.domain;

import com.google.gson.Gson;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import sics.helper.GlobalHelper;

public class User implements Serializable {

    private Long id;
    private String firstName;
    private String lastName;
    private String email;
    private String password;
    private Long saveSeasonId;
    private Long groupsetId;
    private Date expirationDate;
    private String language;

    private Groupset groupset;
    private String tvLanguage;                      // questa arriva da user_settings
    private Long preferredTeamId;                   // team selezionato che fa highlight del team in alcune pagine
    private String oraganizationName;               // organizzazione da CRM

    // permessi
    private List<UserPermission> userPermissions;
    private List<Integer> userAllowedTabs;
    private List<Competition> allowedCompetitions;
    private Map<Long, Competition> allowedCompetitionsMap;
    private List<Long> allowedTeams, allowedPlayers;

    // extra
    private Date soccermentTeamReportExpirationDate;

    public User() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.YEAR, 1970);

        expirationDate = c.getTime();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Long getSaveSeasonId() {
        return saveSeasonId;
    }

    public void setSaveSeasonId(Long saveSeasonId) {
        this.saveSeasonId = saveSeasonId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public Date getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Date expirationDate) {
        this.expirationDate = expirationDate;
    }
    
    public boolean isExpired() {
        try {
            Date today = new Date();
            return expirationDate.before(today);
        } catch (Exception ex) {
            GlobalHelper.reportError(ex);
        }
        return false;
    }
    
    public String getExpirationDateString() {
        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(expirationDate);
    }
    
    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public Groupset getGroupset() {
        return groupset;
    }

    public void setGroupset(Groupset groupset) {
        this.groupset = groupset;
    }

    public String getTvLanguage() {
        return tvLanguage;
    }

    public void setTvLanguage(String tvLanguage) {
        this.tvLanguage = tvLanguage;
    }

    public Long getPreferredTeamId() {
        return preferredTeamId;
    }

    public void setPreferredTeamId(Long preferredTeamId) {
        this.preferredTeamId = preferredTeamId;
    }

    public String getOraganizationName() {
        return oraganizationName;
    }

    public void setOraganizationName(String oraganizationName) {
        this.oraganizationName = oraganizationName;
    }

    public List<UserPermission> getUserPermissions() {
        return userPermissions;
    }

    public void setUserPermissions(List<UserPermission> userPermissions) {
        this.userPermissions = userPermissions;
        if (userPermissions != null) {
            userAllowedTabs = new ArrayList<>();    // resetto o inizializzo
            for (UserPermission permission : userPermissions) {
                if (permission.getTab() != null && !userAllowedTabs.contains(permission.getTab())) {
                    userAllowedTabs.add(permission.getTab());
                }
            }
        }
    }

    public List<Integer> getUserAllowedTabs() {
        return userAllowedTabs;
    }

    public void setUserAllowedTabs(List<Integer> userAllowedTabs) {
        this.userAllowedTabs = userAllowedTabs;
    }

    public List<Competition> getAllowedCompetitions() {
        return allowedCompetitions;
    }

    public void setAllowedCompetitions(List<Competition> allowedCompetitions) {
        this.allowedCompetitions = allowedCompetitions;
        
        if (this.allowedCompetitions != null && !this.allowedCompetitions.isEmpty()) {
            this.allowedCompetitionsMap = new HashMap<>();
            for (Competition competition : this.allowedCompetitions) {
                this.allowedCompetitionsMap.put(competition.getId(), competition);
            }
        }
    }

    public Map<Long, Competition> getAllowedCompetitionsMap() {
        return allowedCompetitionsMap;
    }
    
    public boolean hasAccessToCompetition(Long competitionId) {
        if (competitionId == null) return true;

        boolean hasAccess = false;
        if (this.allowedCompetitionsMap != null) {
            hasAccess = this.allowedCompetitionsMap.containsKey(competitionId);
        }

        return hasAccess;
    }

    public void setAllowedCompetitionsMap(Map<Long, Competition> allowedCompetitionsMap) {
        this.allowedCompetitionsMap = allowedCompetitionsMap;
    }

    public List<Long> getAllowedTeams() {
        return allowedTeams;
    }

    public void setAllowedTeams(List<Long> allowedTeams) {
        this.allowedTeams = allowedTeams;
    }

    public List<Long> getAllowedPlayers() {
        return allowedPlayers;
    }

    public void setAllowedPlayers(List<Long> allowedPlayers) {
        this.allowedPlayers = allowedPlayers;
    }

    public Date getSoccermentTeamReportExpirationDate() {
        return soccermentTeamReportExpirationDate;
    }

    public String getSoccermentTeamReportExpirationDateString() {
        if (soccermentTeamReportExpirationDate == null) return "";

        SimpleDateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");
        return formatter.format(soccermentTeamReportExpirationDate);
    }

    public void setSoccermentTeamReportExpirationDate(Date soccermentTeamReportExpirationDate) {
        this.soccermentTeamReportExpirationDate = soccermentTeamReportExpirationDate;
    }

    public boolean hasAccessToFixture(Fixture fixture) {
        if (fixture == null) return true;
        
        boolean hasAccess = false;
        if (fixture.getCompetitionId() != null) {
            if (this.allowedCompetitions != null && !this.allowedCompetitions.isEmpty()) {
                for (Competition competition : this.allowedCompetitions) {
                    if (Long.compare(competition.getId(), fixture.getCompetitionId()) == 0) {
                        hasAccess = true;
                        break;
                    }
                }
            }
        }

        return hasAccess;
    }
    
    public boolean hasAccess(Integer tab) {
        if (userAllowedTabs == null) return false;
        
        return userAllowedTabs.contains(tab) || userAllowedTabs.contains(999);
    }

    public boolean isTabAdmin(Integer tab) {
        if (userAllowedTabs == null) return false;
        
        for (UserPermission permission : userPermissions) {
            if (permission.getTab() != null && (Integer.compare(permission.getTab(), tab) == 0 || Integer.compare(permission.getTab(), 999) == 0)) {
                if (permission.getRole() != null && Integer.compare(permission.getRole(), 0) == 0) {
                    return true;
                }
            }
        }
        return false;
    }

    public Integer tabRole(Integer tab) {
        if (userAllowedTabs == null) return -1;
        
        for (UserPermission permission : userPermissions) {
            if (permission.getTab() != null && (Integer.compare(permission.getTab(), tab) == 0 || Integer.compare(permission.getTab(), 999) == 0)) {
                if (permission.getRole() != null) {
                    return permission.getRole();
                }
            }
        }
        return -1;
    }

    public String tabComment(Integer tab) {
        if (userAllowedTabs == null) return null;

        for (UserPermission permission : userPermissions) {
            if (permission.getTab() != null && (Integer.compare(permission.getTab(), tab) == 0 || Integer.compare(permission.getTab(), 999) == 0)) {
                if (StringUtils.isNotBlank(permission.getComment())) {
                    return permission.getComment();
                }
            }
        }
        return null;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
