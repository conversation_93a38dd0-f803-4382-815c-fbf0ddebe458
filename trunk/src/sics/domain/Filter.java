package sics.domain;

import com.google.gson.Gson;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Filter {

    private Long id;
    private Long userId, groupsetId;
    private String name, page;

    private String tmpFilters;
    private Map<String, String> filters;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPage() {
        return page;
    }

    public void setPage(String page) {
        this.page = page;
    }

    public String getTmpFilters() {
        return tmpFilters;
    }

    public void setTmpFilters(String tmpFilters) {
        this.tmpFilters = tmpFilters;
        this.filters = new LinkedHashMap<>();

        if (StringUtils.isNotBlank(tmpFilters)) {
            List<String> filterList = Arrays.asList(StringUtils.splitByWholeSeparator(this.tmpFilters, "-_-"));
            if (filterList != null && !filterList.isEmpty()) {
                for (String filter : filterList) {
                    List<String> parts = Arrays.asList(StringUtils.splitByWholeSeparator(filter, "||"));
                    if (parts.size() == 2) {
                        this.filters.put(parts.get(0), parts.get(1));
                    }
                }
            }
        }
    }

    public Map<String, String> getFilters() {
        return filters;
    }

    public void setFilters(Map<String, String> filters) {
        this.filters = filters;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
