package sics.domain;

/**
 *
 * <AUTHOR>
 */
public class PlayerCareerItem {

    private Long playerId, teamId, competitionId, seasonId;
    private Player player;
    private Team team;
    private Competition competition;
    private Season season;

    private Long fixtureAmount, minutesAmount;

    private Boolean clickable;                      // campo usato per sapere se posso cliccare per vedere i dati

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getCompetitionId() {
        return competitionId;
    }

    public void setCompetitionId(Long competitionId) {
        this.competitionId = competitionId;
    }

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public Player getPlayer() {
        return player;
    }

    public void setPlayer(Player player) {
        this.player = player;
    }

    public Team getTeam() {
        return team;
    }

    public void setTeam(Team team) {
        this.team = team;
    }

    public Competition getCompetition() {
        return competition;
    }

    public void setCompetition(Competition competition) {
        this.competition = competition;
    }

    public Season getSeason() {
        return season;
    }

    public void setSeason(Season season) {
        this.season = season;
    }

    public Long getFixtureAmount() {
        return fixtureAmount;
    }

    public void setFixtureAmount(Long fixtureAmount) {
        this.fixtureAmount = fixtureAmount;
    }

    public Long getMinutesAmount() {
        return minutesAmount;
    }

    public void setMinutesAmount(Long minutesAmount) {
        this.minutesAmount = minutesAmount;
    }

    public Boolean getClickable() {
        return clickable;
    }

    public void setClickable(Boolean clickable) {
        this.clickable = clickable;
    }
}
