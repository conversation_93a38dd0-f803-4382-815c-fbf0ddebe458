package sics.domain;

import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import sics.helper.DateHelper;

/**
 *
 * <AUTHOR>
 */
public class PlayerData {

    private Long lastTeamId, height, jerseyNumber;
    private Integer genere, footId, positionId, positionDetailId;
    private Date bornDate, contractExpires;

    private Long eventAmount, playerId;

    private String module;
    private Integer modulePosition, times;

    private Long min, max, average, totalMinutes;

    private Integer starterCompleted, starterNotCompleted, substituted, redCards, totals;

    private String competitions;
    private List<Long> competitionIds;

    public Long getLastTeamId() {
        return lastTeamId;
    }

    public void setLastTeamId(Long lastTeamId) {
        this.lastTeamId = lastTeamId;
    }

    public Long getHeight() {
        return height;
    }

    public void setHeight(Long height) {
        this.height = height;
    }

    public Integer getFootId() {
        return footId;
    }

    public void setFootId(Integer footId) {
        this.footId = footId;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getPositionDetailId() {
        return positionDetailId;
    }

    public void setPositionDetailId(Integer positionDetailId) {
        this.positionDetailId = positionDetailId;
    }

    public Long getJerseyNumber() {
        return jerseyNumber;
    }

    public void setJerseyNumber(Long jerseyNumber) {
        this.jerseyNumber = jerseyNumber;
    }

    public Integer getGenere() {
        return genere;
    }

    public void setGenere(Integer genere) {
        this.genere = genere;
    }

    public Date getBornDate() {
        return bornDate;
    }

    public String getBornDateString() {
        if (bornDate != null) {
            return DateHelper.toString(bornDate);
        } else {
            return "";
        }
    }

    public String getBornYearString() {
        if (bornDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(this.bornDate);
            Integer year = calendar.get(Calendar.YEAR);
            return "'" + year.toString().substring(2);
        } else {
            return "";
        }
    }

    public Integer getAge() {
        if (this.bornDate != null) {
            return Period.between(
                    this.bornDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                    new Date().toInstant().atZone(ZoneId.systemDefault()).toLocalDate()
            ).getYears();
        }

        return 0;
    }

    public void setBornDate(Date bornDate) {
        this.bornDate = bornDate;
    }

    public Date getContractExpires() {
        return contractExpires;
    }

    public String getContractExpiresString() {
        return (contractExpires != null ? DateHelper.toString(contractExpires) : "");
    }

    public void setContractExpires(Date contractExpires) {
        this.contractExpires = contractExpires;
    }

    public Long getEventAmount() {
        return eventAmount;
    }

    public void setEventAmount(Long eventAmount) {
        this.eventAmount = eventAmount;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public Integer getModulePosition() {
        return modulePosition;
    }

    public void setModulePosition(Integer modulePosition) {
        this.modulePosition = modulePosition;
    }

    public Integer getTimes() {
        return times;
    }

    public void setTimes(Integer times) {
        this.times = times;
    }

    public Long getMin() {
        return min;
    }

    public void setMin(Long min) {
        this.min = min;
    }

    public Long getMax() {
        return max;
    }

    public void setMax(Long max) {
        this.max = max;
    }

    public Long getAverage() {
        return average;
    }

    public void setAverage(Long average) {
        this.average = average;
    }

    public Long getTotalMinutes() {
        return totalMinutes;
    }

    public void setTotalMinutes(Long totalMinutes) {
        this.totalMinutes = totalMinutes;
    }

    public Integer getStarterCompleted() {
        return starterCompleted;
    }

    public void setStarterCompleted(Integer starterCompleted) {
        this.starterCompleted = starterCompleted;
    }

    public Integer getStarterNotCompleted() {
        return starterNotCompleted;
    }

    public void setStarterNotCompleted(Integer starterNotCompleted) {
        this.starterNotCompleted = starterNotCompleted;
    }

    public Integer getSubstituted() {
        return substituted;
    }

    public void setSubstituted(Integer substituted) {
        this.substituted = substituted;
    }

    public Integer getRedCards() {
        return redCards;
    }

    public void setRedCards(Integer redCards) {
        this.redCards = redCards;
    }

    public Integer getTotals() {
        return totals;
    }

    public void setTotals(Integer totals) {
        this.totals = totals;
    }

    public String getCompetitions() {
        return competitions;
    }

    public void setCompetitions(String competitions) {
        this.competitions = competitions;
        if (StringUtils.isNotBlank(competitions)) {
            if (this.competitionIds == null) {
                this.competitionIds = new ArrayList<>();
            }
            for (String competitionId : StringUtils.split(competitions, ",")) {
                this.competitionIds.add(Long.valueOf(competitionId));
            }
        }
    }

    public List<Long> getCompetitionIds() {
        return competitionIds;
    }

    public void setCompetitionIds(List<Long> competitionIds) {
        this.competitionIds = competitionIds;
    }
}
