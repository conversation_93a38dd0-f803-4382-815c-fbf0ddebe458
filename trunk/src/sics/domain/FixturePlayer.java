package sics.domain;

import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class FixturePlayer {

    private Long fixtureId, teamId, playerId;
    private Integer positionId;
    private Integer jerseyNumber, modulePosition;

    private Boolean starter, playerOut, playerIn, redCard;
    private Long redCardTime, substituteTime;
    private String coordinate;
    private Long playTime;      // in secondi
    private Integer fromPeriodId, fromPeriodMinute, toPeriodId, toPeriodMinute;

    private Long fixtureTimeDuration, fixtureExtraTimeDuration;

    // extra fields
    private Point point;
    private Long totalPlaytime;
    private Long maxPlaytime;

    private static final long[] VALID_DURATIONS = {10L, 15L, 30L, 45L, 60L};

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getJerseyNumber() {
        return jerseyNumber;
    }

    public void setJerseyNumber(Integer jerseyNumber) {
        this.jerseyNumber = jerseyNumber;
    }

    public Integer getModulePosition() {
        return modulePosition;
    }

    public void setModulePosition(Integer modulePosition) {
        this.modulePosition = modulePosition;
    }

    public Boolean getStarter() {
        return starter;
    }

    public void setStarter(Boolean starter) {
        this.starter = starter;
    }

    public Boolean getPlayerOut() {
        return playerOut;
    }

    public void setPlayerOut(Boolean playerOut) {
        this.playerOut = playerOut;
    }

    public Boolean getPlayerIn() {
        return playerIn;
    }

    public void setPlayerIn(Boolean playerIn) {
        this.playerIn = playerIn;
    }

    public Boolean getRedCard() {
        return redCard;
    }

    public void setRedCard(Boolean redCard) {
        this.redCard = redCard;
    }

    public Long getRedCardTime() {
        return redCardTime;
    }

    public void setRedCardTime(Long redCardTime) {
        this.redCardTime = redCardTime;
    }

    public Long getSubstituteTime() {
        return substituteTime;
    }

    public void setSubstituteTime(Long substituteTime) {
        this.substituteTime = substituteTime;
    }

    public String getCoordinate() {
        return coordinate;
    }

    public void setCoordinate(String coordinate) {
        this.coordinate = coordinate;

        if (StringUtils.isNotBlank(coordinate)) {
            if (StringUtils.contains(coordinate, ";")) {
                List<String> parts = Arrays.asList(StringUtils.split(StringUtils.replace(coordinate, ",", "."), ";"));
                if (parts.size() == 2) {
                    this.point = new Point(Double.valueOf(parts.get(0)), Double.valueOf(parts.get(1)));
                } else if (parts.size() == 3) {
                    this.point = new Point(Double.valueOf(parts.get(0)), Double.valueOf(parts.get(1)), Double.valueOf(parts.get(2)));
                }
            }
        }
    }

    public Long getPlayTime() {
        return playTime;
    }

    public void setPlayTime(Long playTime) {
        this.playTime = playTime;
    }

    public Integer getFromPeriodId() {
        return fromPeriodId;
    }

    public void setFromPeriodId(Integer fromPeriodId) {
        this.fromPeriodId = fromPeriodId;
    }

    public Integer getFromPeriodMinute() {
        return fromPeriodMinute;
    }

    public void setFromPeriodMinute(Integer fromPeriodMinute) {
        this.fromPeriodMinute = fromPeriodMinute;
    }

    public Integer getToPeriodId() {
        return toPeriodId;
    }

    public void setToPeriodId(Integer toPeriodId) {
        this.toPeriodId = toPeriodId;
    }

    public Integer getToPeriodMinute() {
        return toPeriodMinute;
    }

    public void setToPeriodMinute(Integer toPeriodMinute) {
        this.toPeriodMinute = toPeriodMinute;
    }

    public Long getFixtureTimeDuration() {
        return fixtureTimeDuration;
    }

    public void setFixtureTimeDuration(Long fixtureTimeDuration) {
        if (fixtureTimeDuration != null) {
            long durationInMinutes = fixtureTimeDuration / (60 * 1000);
            long closest = VALID_DURATIONS[0];
            long minDiff = Math.abs(durationInMinutes - VALID_DURATIONS[0]);

            for (long duration : VALID_DURATIONS) {
                long diff = Math.abs(durationInMinutes - duration);
                if (diff < minDiff) {
                    minDiff = diff;
                    closest = duration;
                }
            }
            this.fixtureTimeDuration = closest;
        } else {
            this.fixtureTimeDuration = null;
        }
    }

    public Long getFixtureExtraTimeDuration() {
        return fixtureExtraTimeDuration;
    }

    public void setFixtureExtraTimeDuration(Long fixtureExtraTimeDuration) {
        if (fixtureExtraTimeDuration != null) {
            long durationInMinutes = fixtureExtraTimeDuration / (60 * 1000);
            long closest = VALID_DURATIONS[0];
            long minDiff = Math.abs(durationInMinutes - VALID_DURATIONS[0]);

            for (long duration : VALID_DURATIONS) {
                long diff = Math.abs(durationInMinutes - duration);
                if (diff < minDiff) {
                    minDiff = diff;
                    closest = duration;
                }
            }
            this.fixtureExtraTimeDuration = closest;
        } else {
            this.fixtureExtraTimeDuration = null;
        }
    }

    public Point getPoint() {
        return point;
    }

    public void setPoint(Point point) {
        this.point = point;
    }

    public Long getTotalPlaytime() {
        return totalPlaytime;
    }

    public void setTotalPlaytime(Long totalPlaytime) {
        this.totalPlaytime = totalPlaytime;
    }

    public Long getMaxPlaytime() {
        return maxPlaytime;
    }

    public void setMaxPlaytime(Long maxPlaytime) {
        this.maxPlaytime = maxPlaytime;
    }

    public String getExitTime() {
        if (this.substituteTime != null) {
            if (this.toPeriodId != null && this.toPeriodId > 0) {
                if (this.toPeriodMinute != null && this.toPeriodMinute > 0) {
                    if (this.toPeriodId < 3) {
                        long timeDuration = 45;
                        if (this.fixtureTimeDuration != null) {
                            timeDuration = this.fixtureTimeDuration;
                        }

                        return (timeDuration * (this.toPeriodId - 1)) + this.toPeriodMinute + "'";
                    } else {
                        long timeDuration = 45;
                        if (this.fixtureTimeDuration != null) {
                            timeDuration = this.fixtureTimeDuration;
                        }
                        long extraTimeDuration = 45;
                        if (this.fixtureExtraTimeDuration != null) {
                            extraTimeDuration = this.fixtureExtraTimeDuration;
                        }

                        return (timeDuration * (this.toPeriodId - 3) + extraTimeDuration * (this.toPeriodId - 3)) + this.toPeriodMinute + "'";
                    }
                }
            }
        }

        return null;
    }

    public String getJoinTime() {
        if (this.fromPeriodId != null && this.fromPeriodId > 0) {
            if (this.fromPeriodMinute != null && this.fromPeriodMinute > 0) {
                if (this.fromPeriodId < 3) {
                    long timeDuration = 45;
                    if (this.fixtureTimeDuration != null) {
                        timeDuration = this.fixtureTimeDuration;
                    }

                    return (timeDuration * (this.fromPeriodId - 1)) + this.fromPeriodMinute + "'";
                } else {
                    long timeDuration = 45;
                    if (this.fixtureTimeDuration != null) {
                        timeDuration = this.fixtureTimeDuration;
                    }
                    long extraTimeDuration = 45;
                    if (this.fixtureExtraTimeDuration != null) {
                        extraTimeDuration = this.fixtureExtraTimeDuration;
                    }

                    return (timeDuration * (this.fromPeriodId - 3) + extraTimeDuration * (this.fromPeriodId - 3)) + this.fromPeriodMinute + "'";
                }
            }
        }

        return null;
    }
}
