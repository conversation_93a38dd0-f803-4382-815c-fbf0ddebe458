package sics.domain;

import java.util.Date;
import sics.helper.DateHelper;

/**
 *
 * <AUTHOR>
 */
public class TeamPlayer {

    private Long seasonId, teamId, playerId;
    private Integer jerseyNumber, positionId, positionDetailId;
    private Date contractExpires;

    public Long getSeasonId() {
        return seasonId;
    }

    public void setSeasonId(Long seasonId) {
        this.seasonId = seasonId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getPlayerId() {
        return playerId;
    }

    public void setPlayerId(Long playerId) {
        this.playerId = playerId;
    }

    public Integer getJerseyNumber() {
        return jerseyNumber;
    }

    public void setJerseyNumber(Integer jerseyNumber) {
        this.jerseyNumber = jerseyNumber;
    }

    public Integer getPositionId() {
        return positionId;
    }

    public void setPositionId(Integer positionId) {
        this.positionId = positionId;
    }

    public Integer getPositionDetailId() {
        return positionDetailId;
    }

    public void setPositionDetailId(Integer positionDetailId) {
        this.positionDetailId = positionDetailId;
    }

    public Date getContractExpires() {
        return contractExpires;
    }

    public String getContractExpiresString() {
        return (contractExpires != null ? DateHelper.toString(contractExpires) : "");
    }

    public void setContractExpires(Date contractExpires) {
        this.contractExpires = contractExpires;
    }
}
