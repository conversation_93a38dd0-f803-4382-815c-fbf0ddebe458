package sics.domain;

import com.google.gson.Gson;
import java.awt.Color;
import org.apache.commons.lang.StringUtils;

public class Team implements Cloneable {

    private Long id, countryId;
    private String name;
    private String nameEn;
    private String logo;
    private String color;
    private String htmlColor;

    @Override
    public Team clone() throws CloneNotSupportedException {
        Team clone = (Team) super.clone();
        return clone;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public String getName() {
        return name;
    }

    public String getName(String language) {
        if (StringUtils.equalsIgnoreCase(language, "it")) {
            return name;
        } else {
            return nameEn;
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;

        if (color == null) {
            setHtmlColor("red");
        } else {
            String[] colorString = color.split("x");
            Color colorTeam;
            if (colorString.length == 4) {
                colorTeam = new Color(Integer.parseInt(colorString[1]), Integer.parseInt(colorString[2]), Integer.parseInt(colorString[3]), Integer.parseInt(colorString[0]));
            } else {
                colorTeam = Color.BLUE;
            }

//            if (colorTeam.getRed() >= 230 && colorTeam.getGreen() >= 230 && colorTeam.getBlue() >= 230) {
//                colorTeam = colorTeam.darker();
//            }
            colorTeam = colorTeam.darker();
            setHtmlColor("#" + Integer.toHexString(colorTeam.getRGB()).substring(2));
        }
    }

    public String getHtmlColor() {
        return htmlColor;
    }

    public void setHtmlColor(String htmlColor) {
        this.htmlColor = htmlColor;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
