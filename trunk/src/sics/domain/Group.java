package sics.domain;

import com.google.gson.Gson;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;

/**
 *
 * <AUTHOR>
 */
public class Group {

    private Integer id;
    private String name, nameEn, nameFr, nameEs, nameRu;
    private Integer order;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public String getName(String language) {
        language = StringUtils.defaultIfEmpty(language, "en");
        switch (language) {
            case "it":
                return WordUtils.capitalize(name);
            case "fr":
                return WordUtils.capitalize(nameFr);
            case "es":
                return WordUtils.capitalize(nameEs);
            case "ru":
                return WordUtils.capitalize(nameRu);
            default:
                return WordUtils.capitalize(nameEn);
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameFr() {
        return nameFr;
    }

    public void setNameFr(String nameFr) {
        this.nameFr = nameFr;
    }

    public String getNameEs() {
        return nameEs;
    }

    public void setNameEs(String nameEs) {
        this.nameEs = nameEs;
    }

    public String getNameRu() {
        return nameRu;
    }

    public void setNameRu(String nameRu) {
        this.nameRu = nameRu;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
