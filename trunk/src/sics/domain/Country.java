package sics.domain;

import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Country {

    private Long id, internationalCompetitionId;
    private String name, nameEn;
    private String logo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInternationalCompetitionId() {
        return internationalCompetitionId;
    }

    public void setInternationalCompetitionId(Long internationalCompetitionId) {
        this.internationalCompetitionId = internationalCompetitionId;
    }

    public String getName() {
        return name;
    }

    public String getName(String language) {
        if (StringUtils.equalsIgnoreCase(language, "it")) {
            return name;
        } else {
            return nameEn;
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }
}
