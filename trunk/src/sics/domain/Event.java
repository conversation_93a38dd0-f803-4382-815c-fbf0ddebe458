package sics.domain;

import com.google.gson.Gson;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.apache.commons.lang.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class Event {

    private Long id;
    private Long eventTypeId;
    private String tagTypeList;
    private List<Long> tagTypeIds;              // impostato al set di tagTypeList

    private Long fixtureId;
    private Long teamId;
    private String playerIdList, playerToIdList;
    private List<Long> playerIds, playerToIds;

    private Integer period;
    private Integer startSecond, startMinute, endSecond, endMinute;
    private Long startMillis, endMillis;

    private String coordinateStart, coordinateEnd, coordinateHeight;
    private Point startPoint = new Point(), endPoint = new Point(), heightPoint;

    // extra fields
    private Point startPointNormalized, endPointNormalized, heightPointNormalized;
    private Double distance, distanceFromField, distanceFromTop, angle;
    private Double endDistanceFromField, endDistanceFromTop;
    private Integer angleZone;
    private Integer half, third, channel, verticalChannel;
    private Boolean inArea, inSmallArea;
    private Integer halfEnd, thirdEnd, channelEnd, verticalChannelEnd;
    private Boolean inAreaEnd, inSmallAreaEnd;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEventTypeId() {
        return eventTypeId;
    }

    public void setEventTypeId(Long eventTypeId) {
        this.eventTypeId = eventTypeId;
    }

    public String getTagTypeList() {
        return tagTypeList;
    }

    public void setTagTypeList(String tagTypeList) {
        this.tagTypeList = tagTypeList;

        this.tagTypeIds = new ArrayList<>();
        if (StringUtils.isNotBlank(this.tagTypeList)) {
            for (String tagTypeId : StringUtils.split(this.tagTypeList, ",")) {
                this.tagTypeIds.add(Long.valueOf(tagTypeId));
            }
        }
    }

    public List<Long> getTagTypeIds() {
        return tagTypeIds;
    }

    public void setTagTypeIds(List<Long> tagTypeIds) {
        this.tagTypeIds = tagTypeIds;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getPlayerIdList() {
        return playerIdList;
    }

    public void setPlayerIdList(String playerIdList) {
        this.playerIdList = playerIdList;

        this.playerIds = new ArrayList<>();
        if (StringUtils.isNotBlank(this.playerIdList)) {
            for (String playerId : StringUtils.split(this.playerIdList, ",")) {
                this.playerIds.add(Long.valueOf(playerId));
            }
        }
    }

    public String getPlayerToIdList() {
        return playerToIdList;
    }

    public void setPlayerToIdList(String playerToIdList) {
        this.playerToIdList = playerToIdList;

        this.playerToIds = new ArrayList<>();
        if (StringUtils.isNotBlank(this.playerToIdList)) {
            for (String playerId : StringUtils.split(this.playerToIdList, ",")) {
                this.playerToIds.add(Long.valueOf(playerId));
            }
        }
    }

    public List<Long> getPlayerIds() {
        return playerIds;
    }

    public void setPlayerIds(List<Long> playerIds) {
        this.playerIds = playerIds;
    }

    public List<Long> getPlayerToIds() {
        return playerToIds;
    }

    public void setPlayerToIds(List<Long> playerToIds) {
        this.playerToIds = playerToIds;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getStartSecond() {
        return startSecond;
    }

    public void setStartSecond(Integer startSecond) {
        this.startSecond = startSecond;
    }

    public Integer getStartMinute() {
        return startMinute;
    }

    public void setStartMinute(Integer startMinute) {
        this.startMinute = startMinute;
    }

    public Integer getEndSecond() {
        return endSecond;
    }

    public void setEndSecond(Integer endSecond) {
        this.endSecond = endSecond;
    }

    public Integer getEndMinute() {
        return endMinute;
    }

    public void setEndMinute(Integer endMinute) {
        this.endMinute = endMinute;
    }

    public Long getStartMillis() {
        return startMillis;
    }

    public void setStartMillis(Long startMillis) {
        this.startMillis = startMillis;
    }

    public Long getEndMillis() {
        return endMillis;
    }

    public void setEndMillis(Long endMillis) {
        this.endMillis = endMillis;
    }

    public String getCoordinateStart() {
        return coordinateStart;
    }

    public void setCoordinateStart(String coordinateStart) {
        this.coordinateStart = coordinateStart;

        this.startPoint = new Point();
        if (StringUtils.isNotBlank(this.coordinateStart)) {
            List<String> splitted = Arrays.asList(StringUtils.split(this.coordinateStart, ";"));
            if (splitted != null && !splitted.isEmpty() && splitted.size() == 2) {
                this.startPoint.setX(Double.valueOf(splitted.get(0)));
                this.startPoint.setY(Double.valueOf(splitted.get(1)));
                this.startPoint.setIsDefault(false);
            }
        }
    }

    public String getCoordinateEnd() {
        return coordinateEnd;
    }

    public void setCoordinateEnd(String coordinateEnd) {
        this.coordinateEnd = coordinateEnd;

        this.endPoint = new Point();
        if (StringUtils.isNotBlank(this.coordinateEnd)) {
            List<String> splitted = Arrays.asList(StringUtils.split(this.coordinateEnd, ";"));
            if (splitted != null && !splitted.isEmpty() && splitted.size() == 2) {
                this.endPoint.setX(Double.valueOf(splitted.get(0)));
                this.endPoint.setY(Double.valueOf(splitted.get(1)));
                this.endPoint.setIsDefault(false);
            }
        }
    }

    public String getCoordinateHeight() {
        return coordinateHeight;
    }

    public void setCoordinateHeight(String coordinateHeight) {
        this.coordinateHeight = coordinateHeight;

        this.heightPoint = new Point();
        if (StringUtils.isNotBlank(this.coordinateHeight)) {
            List<String> splitted = Arrays.asList(StringUtils.split(this.coordinateHeight, ";"));
            if (splitted != null && !splitted.isEmpty() && splitted.size() == 3) {
                this.heightPoint.setX(Double.valueOf(splitted.get(0)));
                this.heightPoint.setY(Double.valueOf(splitted.get(1)));
                this.heightPoint.setZ(Double.valueOf(splitted.get(2)));
                this.heightPoint.setIsDefault(false);
            }
        }
    }

    public Point getStartPoint() {
        return startPoint;
    }

    public void setStartPoint(Point startPoint) {
        this.startPoint = startPoint;
    }

    public Point getEndPoint() {
        return endPoint;
    }

    public void setEndPoint(Point endPoint) {
        this.endPoint = endPoint;
    }

    public Point getHeightPoint() {
        return heightPoint;
    }

    public void setHeightPoint(Point heightPoint) {
        this.heightPoint = heightPoint;
    }

    public Point getStartPointNormalized() {
        return startPointNormalized;
    }

    public void setStartPointNormalized(Point startPointNormalized) {
        this.startPointNormalized = startPointNormalized;

        if (this.startPointNormalized != null) {
            if (this.startPointNormalized.getX() != -1 || this.startPointNormalized.getY() != -1) {
                this.startPointNormalized.setIsDefault(false);
            }
        }
    }

    public Point getEndPointNormalized() {
        return endPointNormalized;
    }

    public void setEndPointNormalized(Point endPointNormalized) {
        this.endPointNormalized = endPointNormalized;
        
        if (this.endPointNormalized != null) {
            if (this.endPointNormalized.getX() != -1 || this.endPointNormalized.getY() != -1) {
                this.endPointNormalized.setIsDefault(false);
            }
        }
    }

    public Point getHeightPointNormalized() {
        return heightPointNormalized;
    }

    public void setHeightPointNormalized(Point heightPointNormalized) {
        this.heightPointNormalized = heightPointNormalized;

        if (this.heightPointNormalized != null) {
            if (this.heightPointNormalized.getX() != -1 || this.heightPointNormalized.getY() != -1 || this.heightPointNormalized.getZ() != -1) {
                this.heightPointNormalized.setIsDefault(false);
            }
        }
    }

    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }

    public Double getDistanceFromField() {
        return distanceFromField;
    }

    public void setDistanceFromField(Double distanceFromField) {
        this.distanceFromField = distanceFromField;
    }

    public Double getDistanceFromTop() {
        return distanceFromTop;
    }

    public void setDistanceFromTop(Double distanceFromTop) {
        this.distanceFromTop = distanceFromTop;
    }

    public Double getAngle() {
        return angle;
    }

    public void setAngle(Double angle) {
        this.angle = angle;
    }

    public Double getEndDistanceFromField() {
        return endDistanceFromField;
    }

    public void setEndDistanceFromField(Double endDistanceFromField) {
        this.endDistanceFromField = endDistanceFromField;
    }

    public Double getEndDistanceFromTop() {
        return endDistanceFromTop;
    }

    public void setEndDistanceFromTop(Double endDistanceFromTop) {
        this.endDistanceFromTop = endDistanceFromTop;
    }

    public Integer getAngleZone() {
        return angleZone;
    }

    public void setAngleZone(Integer angleZone) {
        this.angleZone = angleZone;
    }

    public Integer getHalf() {
        return half;
    }

    public void setHalf(Integer half) {
        this.half = half;
    }

    public Integer getThird() {
        return third;
    }

    public void setThird(Integer third) {
        this.third = third;
    }

    public Integer getChannel() {
        return channel;
    }

    public void setChannel(Integer channel) {
        this.channel = channel;
    }

    public Integer getVerticalChannel() {
        return verticalChannel;
    }

    public void setVerticalChannel(Integer verticalChannel) {
        this.verticalChannel = verticalChannel;
    }

    public Boolean getInArea() {
        return inArea;
    }

    public void setInArea(Boolean inArea) {
        this.inArea = inArea;
    }

    public Boolean getInSmallArea() {
        return inSmallArea;
    }

    public void setInSmallArea(Boolean inSmallArea) {
        this.inSmallArea = inSmallArea;
    }

    public Integer getHalfEnd() {
        return halfEnd;
    }

    public void setHalfEnd(Integer halfEnd) {
        this.halfEnd = halfEnd;
    }

    public Integer getThirdEnd() {
        return thirdEnd;
    }

    public void setThirdEnd(Integer thirdEnd) {
        this.thirdEnd = thirdEnd;
    }

    public Integer getChannelEnd() {
        return channelEnd;
    }

    public void setChannelEnd(Integer channelEnd) {
        this.channelEnd = channelEnd;
    }

    public Integer getVerticalChannelEnd() {
        return verticalChannelEnd;
    }

    public void setVerticalChannelEnd(Integer verticalChannelEnd) {
        this.verticalChannelEnd = verticalChannelEnd;
    }

    public Boolean getInAreaEnd() {
        return inAreaEnd;
    }

    public void setInAreaEnd(Boolean inAreaEnd) {
        this.inAreaEnd = inAreaEnd;
    }

    public Boolean getInSmallAreaEnd() {
        return inSmallAreaEnd;
    }

    public void setInSmallAreaEnd(Boolean inSmallAreaEnd) {
        this.inSmallAreaEnd = inSmallAreaEnd;
    }
    
    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
