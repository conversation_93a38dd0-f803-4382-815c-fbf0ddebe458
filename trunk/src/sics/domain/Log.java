package sics.domain;

import java.util.Date;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class Log {

    private Long userId, groupsetId;
    private String groupsetName;
    private Date creation, sessionCreation;
    private String sessionDuration;
    private Integer totalCharts;

    private Map<String, Integer> pageCounterMap;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getGroupsetId() {
        return groupsetId;
    }

    public void setGroupsetId(Long groupsetId) {
        this.groupsetId = groupsetId;
    }

    public String getGroupsetName() {
        return groupsetName;
    }

    public void setGroupsetName(String groupsetName) {
        this.groupsetName = groupsetName;
    }

    public Date getCreation() {
        return creation;
    }

    public void setCreation(Date creation) {
        this.creation = creation;
    }

    public Date getSessionCreation() {
        return sessionCreation;
    }

    public void setSessionCreation(Date sessionCreation) {
        this.sessionCreation = sessionCreation;
    }

    public String getSessionDuration() {
        return sessionDuration;
    }

    public void setSessionDuration(String sessionDuration) {
        this.sessionDuration = sessionDuration;
    }

    public Integer getTotalCharts() {
        return totalCharts;
    }

    public void setTotalCharts(Integer totalCharts) {
        this.totalCharts = totalCharts;
    }

    public Map<String, Integer> getPageCounterMap() {
        return pageCounterMap;
    }

    public void setPageCounterMap(Map<String, Integer> pageCounterMap) {
        this.pageCounterMap = pageCounterMap;
    }
}
