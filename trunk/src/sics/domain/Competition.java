package sics.domain;

import com.google.gson.Gson;
import java.io.*;
import org.apache.commons.lang.StringUtils;

public class Competition implements Serializable {

    private Long id;
    private String name;
    private String nameEn;
    private String logo;
    private Long panelId;
    private Long sportId;
    private boolean visible;
    private Long countryId;
    private Long internationalCompetitionId;
    private Boolean ranking, solarSeason;
    private Integer order;
    private Boolean isSoccerment;

    private String countryName;
    private String countryNameEn;
    private String countryLogo;
    private String internationalCompetitionName;
    private String internationalCompetitionNameEn;
    private String internationalCompetitionLogo;

    public Competition() {
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public String getName(String language) {
        if (StringUtils.equalsIgnoreCase(language, "it")) {
            return name;
        } else {
            return nameEn;
        }
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getCompleteName() {
        return getCompleteName("en");
    }

    public String getCompleteName(String language) {
        if (StringUtils.equalsIgnoreCase(language, "it")) {
            if (StringUtils.isNotBlank(this.internationalCompetitionName)) {
                return this.internationalCompetitionName + " - " + this.name;
            } else if (StringUtils.isNotBlank(this.countryName)) {
                return this.countryName + " - " + this.name;
            } else {
                return this.name;
            }
        } else {
            if (StringUtils.isNotBlank(this.internationalCompetitionNameEn)) {
                return this.internationalCompetitionNameEn + " - " + this.nameEn;
            } else if (StringUtils.isNotBlank(this.countryNameEn)) {
                return this.countryNameEn + " - " + this.nameEn;
            } else {
                return this.nameEn;
            }
        }
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getLogo() {
        return logo;
    }

    public Long getPanelId() {
        return panelId;
    }

    public void setPanelId(Long panelId) {
        this.panelId = panelId;
    }

    public Long getSportId() {
        return sportId;
    }

    public void setSportId(Long sportId) {
        this.sportId = sportId;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public Long getCountryId() {
        return countryId;
    }

    public void setCountryId(Long countryId) {
        this.countryId = countryId;
    }

    public Long getInternationalCompetitionId() {
        return internationalCompetitionId;
    }

    public void setInternationalCompetitionId(Long internationalCompetitionId) {
        this.internationalCompetitionId = internationalCompetitionId;
    }

    public Boolean getRanking() {
        return ranking;
    }

    public void setRanking(Boolean ranking) {
        this.ranking = ranking;
    }

    public Boolean getSolarSeason() {
        return solarSeason;
    }

    public void setSolarSeason(Boolean solarSeason) {
        this.solarSeason = solarSeason;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public Boolean getSoccerment() {
        return isSoccerment;
    }

    public void setSoccerment(Boolean soccerment) {
        isSoccerment = soccerment;
    }

    public String getCountryName() {
        return countryName;
    }

    public void setCountryName(String countryName) {
        this.countryName = countryName;
    }

    public String getCountryNameEn() {
        return countryNameEn;
    }

    public void setCountryNameEn(String countryNameEn) {
        this.countryNameEn = countryNameEn;
    }

    public String getCountryLogo() {
        return countryLogo;
    }

    public void setCountryLogo(String countryLogo) {
        this.countryLogo = countryLogo;
    }

    public String getInternationalCompetitionName() {
        return internationalCompetitionName;
    }

    public void setInternationalCompetitionName(String internationalCompetitionName) {
        this.internationalCompetitionName = internationalCompetitionName;
    }

    public String getInternationalCompetitionNameEn() {
        return internationalCompetitionNameEn;
    }

    public void setInternationalCompetitionNameEn(String internationalCompetitionNameEn) {
        this.internationalCompetitionNameEn = internationalCompetitionNameEn;
    }

    public String getInternationalCompetitionLogo() {
        return internationalCompetitionLogo;
    }

    public void setInternationalCompetitionLogo(String internationalCompetitionLogo) {
        this.internationalCompetitionLogo = internationalCompetitionLogo;
    }

    public String getJson() {
        Gson gson = new Gson();
        return gson.toJson(this);
    }
}
