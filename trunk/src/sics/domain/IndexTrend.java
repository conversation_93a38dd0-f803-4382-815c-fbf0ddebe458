package sics.domain;

public class IndexTrend {

    private Long id, fixtureId, teamId, indexEventTypeId;
    private Integer periodId, periodMinute, periodSecond, score;
    private Double oi, delta;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFixtureId() {
        return fixtureId;
    }

    public void setFixtureId(Long fixtureId) {
        this.fixtureId = fixtureId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public Long getIndexEventTypeId() {
        return indexEventTypeId;
    }

    public void setIndexEventTypeId(Long indexEventTypeId) {
        this.indexEventTypeId = indexEventTypeId;
    }

    public Integer getPeriodId() {
        return periodId;
    }

    public void setPeriodId(Integer periodId) {
        this.periodId = periodId;
    }

    public Integer getPeriodMinute() {
        return periodMinute;
    }

    public void setPeriodMinute(Integer periodMinute) {
        this.periodMinute = periodMinute;
    }

    public Integer getPeriodSecond() {
        return periodSecond;
    }

    public void setPeriodSecond(Integer periodSecond) {
        this.periodSecond = periodSecond;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Double getOi() {
        return oi;
    }

    public void setOi(Double oi) {
        this.oi = oi;
    }

    public Double getDelta() {
        return delta;
    }

    public void setDelta(Double delta) {
        this.delta = delta;
    }
}
