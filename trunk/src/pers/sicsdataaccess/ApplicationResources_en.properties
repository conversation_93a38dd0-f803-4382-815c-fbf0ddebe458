email.smtp.port=25
email.smtp=localhost
email.smtp.user=
email.smtp.pwd=
email.from=<EMAIL>
email.hello=Hi {0},
email.header=<html><head><title>SICS</title><meta content="text/html; charset=" http-equiv="Content-Type"><meta content="width=device-width" name="viewport"><link href="https://server.sics.it/download/css/mail.min.css" rel="stylesheet"></head>
email.title=
email.content=<body><table class="body-wrap"><tbody><tr><td class="container"><table><tbody><tr><td class="masthead" align="center"> <img alt="SICS"src="http://www.sics.cloud/download/icon/sics-logo-03-50px_w.png" border="0"> </td></tr><tr class="testo" style="border: none;"><td style="border: none;"> {0} <br><br><p style="font-weight: bold; font-size: 16px; color: #F26522;">SICS Staff</p></td></tr><tr><td class="container"><table><tbody><tr><td class="footer" align="center"><p>Sent from <a href="http://www.sics.it/en/">SICS srl</a>, Largo Perlasca 3/51, 36061,Bassano del Grappa (VI), Italy</p><p align="center" style="font-size:12px;">(Do not reply to this email. Email automatically generated)</p></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table></body>
email.footer=</html>
email.app.title=SICS Data Analytics
theme.title=SICS Data Analytics
auth.login=Login to your account
auth.login.description=Enter your credentials below
auth.username=Username
auth.password=Password
auth.sign.in=Sign in
auth.forgot.password=Forgot password?
auth.session.expired=Session expired. Sign In again.
auth.login.expired=Access expired on
auth.login.failed=Wrong username or password
auth.login.other.device=Signed in from another device.
menu.multi.session.header=Multiple access attempt
menu.multi.session.content=Your account is already in use on another device.<br>Force login to access, all other open sessions will be closed.
menu.multi.session.force=Force Login
menu.multi.session.cancel=Cancel
menu.user.logout=Logout
menu.home=Home
menu.assign.game=Assign Game
404.title=PAGE NOT FOUND
404.description=This page doesn't seem to exist or is temporarily unavailable
menu.assign.game.description=Here you can view the list of the matches available and, for each one, you can assign it to an analyst
menu.assign.game.action=Assign
